# Digital Signature Workflow Enhancement - Implementation Summary

## Overview
This implementation enhances the digital signature workflow in the medical record system (NTU01H104_KyCap) with level-based signature cancellation and return signature functionality.

## Requirements Implemented

### 1. Remove "Hủy ký" from Status Filter ✅
- **File Modified**: `WEBROOT/noitru/NTU01H104_KyCap.htm`
- **Change**: Removed "Đã hủy" option from the status dropdown filter
- **Before**: Options included "Chưa ký", "Đã ký", "Đã hủy", "Từ chối"
- **After**: Options include "Chưa ký", "Đã ký", "Chuyển trả ký"

### 2. Update "Hủy ký" Button Logic ✅
- **File Modified**: `WEBROOT/noitru/NTU01H104_KyCap.js`
- **Change**: Modified button visibility logic
- **Logic**: Show "Hủy ký" button only for signed documents (TRANGTHAI == '2')
- **Implementation**: Level-based cancellation that restores previous state

### 3. Change "Từ chối" to "Chuyển trả ký" ✅
- **Files Modified**: 
  - `WEBROOT/noitru/NTU01H104_KyCap.htm` (button text and ID)
  - `WEBROOT/noitru/NTU01H104_KyCap.js` (event handlers)
- **Changes**:
  - Button ID changed from `btnTuChoi` to `btnChuyenTraKy`
  - Button text changed to "Chuyển trả ký"
  - Icon changed to share-alt for better representation
  - Filter option changed from "Từ chối" to "Chuyển trả ký"

### 4. Level-based Signature Workflow ✅
- **New Functions Added**:
  - `huyKyTheoLevel()`: Implements level-based signature cancellation
  - `chuyenTraKyTheoLevel()`: Implements return signature workflow
- **Features**:
  - Level-based cancellation validation
  - Return signature with reason tracking
  - Workflow state management

## Technical Implementation

### Frontend Changes

#### HTML/UI Updates (`NTU01H104_KyCap.htm`)
```html
<!-- Status Filter Updated -->
<select class="form-control input-sm" id="cboTRANGTHAI">
    <option value="-1">-- Chọn --</option>
    <option value="1" selected>Chưa ký</option>
    <option value="2">Đã ký</option>
    <option value="4">Chuyển trả ký</option>
</select>

<!-- Button Updated -->
<button type="button" class="btn btn-sm btn-primary" id="btnChuyenTraKy">
    <span class="glyphicon glyphicon-share-alt"></span> Chuyển trả ký
</button>
```

#### JavaScript Logic Updates (`NTU01H104_KyCap.js`)

**Button Visibility Logic:**
```javascript
// Hủy ký: chỉ hiển thị với phiếu đã ký (TRANGTHAI == '2')
if (_row.TRANGTHAI == '2') {
    $("#btnHuyKy").show();
} else {
    $("#btnHuyKy").hide();
}

// Chuyển trả ký: hiển thị cho phiếu chưa ký tại cấp hiện tại
var _check = CommonUtil.checkKyCaByParam(_params, loaiphieu, levelky);
if (_check == 0 && _row.TRANGTHAI != '4') {
    $("#btnChuyenTraKy").show();
} else {
    $("#btnChuyenTraKy").hide();
}
```

**New Event Handlers:**
```javascript
$("#btnHuyKy").click(function () {
    huyKyTheoLevel();
});

$("#btnChuyenTraKy").click(function () {
    chuyenTraKyTheoLevel();
});
```

### Backend Database Changes

#### New Stored Procedures (`sql/NTU01H104_KyCap_Procedures.sql`)

1. **NTU01H104.07 - Level-based Signature Cancellation**
   - Validates signature exists at current level
   - Checks for higher level signatures
   - Cancels signature and restores unsigned state
   - Logs cancellation action

2. **NTU01H104.08 - Return Signature Workflow**
   - Validates return signature permissions
   - Finds appropriate lower level
   - Updates workflow state
   - Tracks return signature with reason

#### New Database Tables

1. **emr_chuyen_tra_ky**: Tracks return signature workflow
2. **emr_ca_log**: Logs all signature actions
3. **emr_ca_xml_backup**: Backs up XML during signature process

## Workflow Logic

### Level-based Cancellation Process
1. **Validation**:
   - Check if document is signed at current level
   - Verify no higher level signatures exist
   - Confirm user permissions

2. **Cancellation**:
   - Update signature status to cancelled
   - Restore document to unsigned state
   - Remove XML signature data
   - Log cancellation action

3. **Result**:
   - Document returns to "Chưa ký" state
   - Previous XML and signature data restored
   - Audit trail maintained

### Return Signature Process
1. **Validation**:
   - Check if current level is unsigned
   - Find appropriate lower level
   - Verify return signature permissions

2. **Return Process**:
   - Update lower level status to "Chuyển trả ký"
   - Cancel existing lower level signatures
   - Create return signature record with reason
   - Log return signature action

3. **Result**:
   - Lower level receives document for re-signing
   - Reason for return is tracked
   - Workflow continues from lower level

## User Experience Improvements

### Enhanced Button Logic
- **Hủy ký**: Only visible for signed documents
- **Chuyển trả ký**: Only visible when user can return signature
- Clear visual feedback for available actions

### Improved Status Management
- Removed confusing "Đã hủy" filter option
- Added "Chuyển trả ký" status for better workflow tracking
- Clearer status progression

### Workflow Transparency
- Reason tracking for return signatures
- Comprehensive audit logging
- Clear error messages for invalid operations

## Security and Validation

### Permission Checks
- Level-based access control
- Signature state validation
- User authorization verification

### Data Integrity
- Transaction-based operations
- Rollback on errors
- Comprehensive error logging

### Audit Trail
- All actions logged with timestamps
- User tracking for accountability
- Reason tracking for return signatures

## Testing Recommendations

### Functional Testing
1. Test signature cancellation at different levels
2. Verify return signature workflow
3. Test button visibility logic
4. Validate status filtering

### Security Testing
1. Test unauthorized access attempts
2. Verify level-based permissions
3. Test data integrity during operations

### Integration Testing
1. Test with existing signature workflow
2. Verify database procedure integration
3. Test error handling scenarios

## Deployment Notes

### Database Changes Required
1. Execute `sql/NTU01H104_KyCap_Procedures.sql`
2. Verify table creation and indexes
3. Test stored procedure functionality

### Frontend Deployment
1. Deploy updated HTML and JavaScript files
2. Clear browser cache for users
3. Verify UI changes are visible

### Configuration
- No additional configuration required
- Uses existing user permissions and roles
- Integrates with current signature infrastructure

## Future Enhancements

### Potential Improvements
1. Enhanced UI for return signature reasons
2. Bulk operations for multiple documents
3. Advanced reporting for signature workflows
4. Email notifications for return signatures

### Scalability Considerations
- Database indexes for performance
- Caching for frequently accessed data
- Batch processing for large operations

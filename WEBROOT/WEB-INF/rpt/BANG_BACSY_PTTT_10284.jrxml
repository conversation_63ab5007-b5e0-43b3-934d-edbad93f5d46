<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BANG_BACSY_PTTT_10284" language="groovy" pageWidth="1190" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1150" leftMargin="20" rightMargin="20" topMargin="28" bottomMargin="20" uuid="f61f2d0b-7fc3-4f14-9f6f-8697302ad3da">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.export.xls.detect.cell.type" value="true"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.columns" value="true"/>
	<property name="net.sf.jasperreports.export.xls.wrap.text" value="true"/>
	<property name="net.sf.jasperreports.export.xls.white.page.background" value="false"/>
	<property name="onePagePerSheet" value="false"/>
	<style name="style1">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PTV_DB}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF" fill="Solid">
				<pen lineWidth="0.5"/>
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
					<topPen lineWidth="0.5" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineColor="#000000"/>
				</box>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PTV_DB}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_1">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU1_DB}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF">
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
					<topPen lineWidth="0.5" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineColor="#000000"/>
				</box>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU1_DB}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_2">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU2_DB}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF">
				<box>
					<pen lineWidth="0.5" lineColor="#000000"/>
					<topPen lineWidth="0.5" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineColor="#000000"/>
				</box>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU2_DB}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_3">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU3_DB}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU3_DB}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_4">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{GMC_DB}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{GMC_DB}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_5">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM_DB}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM_DB}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_6">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DDV_DB}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DDV_DB}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_7">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DCV_DB}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DCV_DB}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_8">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PTV_LOAI1}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PTV_LOAI1}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_9">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU1_LOAI1}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU1_LOAI1}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_10">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU2_LOAI1}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU2_LOAI1}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_11">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU3_LOAI1}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU3_LOAI1}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_12">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{GMC_LOAI1}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{GMC_LOAI1}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_13">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM_LOAI1}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM_LOAI1}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_14">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DDV_LOAI1}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DDV_LOAI1}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_15">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DCV_LOAI1}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DCV_LOAI1}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_16">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PTV_LOAI2}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PTV_LOAI2}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_17">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU1_LOAI2}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU1_LOAI2}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_18">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU2_LOAI2}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU2_LOAI2}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_19">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU3_LOAI2}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU3_LOAI2}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_20">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{GMC_LOAI2}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{GMC_LOAI2}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_21">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM_LOAI2}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM_LOAI2}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_22">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DDV_LOAI2}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DDV_LOAI2}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_23">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DCV_LOAI2}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DCV_LOAI2}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_24">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PTV_LOAI3}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PTV_LOAI3}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_25">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU1_LOAI3}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU1_LOAI3}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_26">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU2_LOAI3}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PHU2_LOAI3}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_27">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM2_LOAI3}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM2_LOAI3}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_28">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{GMC_LOAI3}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{GMC_LOAI3}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_29">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM1_LOAI3}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{PM1_LOAI3}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<style name="style1_30">
		<box leftPadding="0">
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DDV_LOAI3}.floatValue() == 0]]></conditionExpression>
			<style forecolor="#FFFFFF"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{DDV_LOAI3}.floatValue() > 0]]></conditionExpression>
			<style forecolor="#000000"/>
		</conditionalStyle>
	</style>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tungay" class="java.util.Date"/>
	<parameter name="denngay" class="java.util.Date"/>
	<parameter name="coso" class="java.lang.String"/>
	<parameter name="loaiid" class="java.lang.String"/>
	<parameter name="VPARS_khoaid_VALUE" class="java.lang.String"/>
	<parameter name="coso_TEXT" class="java.lang.String"/>
	<parameter name="o_curr" class="java.sql.ResultSet" isForPrompting="false"/>
	<parameter name="loaiid_TEXT" class="java.lang.String"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="i_trangthaiid" class="java.lang.Long"/>
	<queryString language="plsql">
		<![CDATA[{call BANG_BACSY_PTTT_10284($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{tungay},$P{denngay},$P{coso},$P{VPARS_khoaid_VALUE},$P{loaiid},$P{i_trangthaiid},$P{ora_cursor})}]]>
	</queryString>
	<field name="HOTEN" class="java.lang.String"/>
	<field name="TENKHOA" class="java.lang.String"/>
	<field name="PTV_DB" class="java.math.BigDecimal"/>
	<field name="PTV_LOAI1" class="java.math.BigDecimal"/>
	<field name="PTV_LOAI2" class="java.math.BigDecimal"/>
	<field name="PTV_LOAI3" class="java.math.BigDecimal"/>
	<field name="GMC_DB" class="java.math.BigDecimal"/>
	<field name="GMC_LOAI1" class="java.math.BigDecimal"/>
	<field name="GMC_LOAI2" class="java.math.BigDecimal"/>
	<field name="GMC_LOAI3" class="java.math.BigDecimal"/>
	<field name="PHU1_DB" class="java.math.BigDecimal"/>
	<field name="PHU1_LOAI1" class="java.math.BigDecimal"/>
	<field name="PHU1_LOAI2" class="java.math.BigDecimal"/>
	<field name="PHU1_LOAI3" class="java.math.BigDecimal"/>
	<field name="PHU2_DB" class="java.math.BigDecimal"/>
	<field name="PHU2_LOAI1" class="java.math.BigDecimal"/>
	<field name="PHU2_LOAI2" class="java.math.BigDecimal"/>
	<field name="PHU2_LOAI3" class="java.math.BigDecimal"/>
	<field name="PHU3_DB" class="java.math.BigDecimal"/>
	<field name="PHU3_LOAI1" class="java.math.BigDecimal"/>
	<field name="PHU3_LOAI2" class="java.math.BigDecimal"/>
	<field name="PM_DB" class="java.math.BigDecimal"/>
	<field name="PM_LOAI1" class="java.math.BigDecimal"/>
	<field name="PM_LOAI2" class="java.math.BigDecimal"/>
	<field name="PM1_LOAI3" class="java.math.BigDecimal"/>
	<field name="PM2_LOAI3" class="java.math.BigDecimal"/>
	<field name="DDV_DB" class="java.math.BigDecimal"/>
	<field name="DDV_LOAI1" class="java.math.BigDecimal"/>
	<field name="DDV_LOAI2" class="java.math.BigDecimal"/>
	<field name="DDV_LOAI3" class="java.math.BigDecimal"/>
	<field name="DCV_DB" class="java.math.BigDecimal"/>
	<field name="DCV_LOAI1" class="java.math.BigDecimal"/>
	<field name="DCV_LOAI2" class="java.math.BigDecimal"/>
	<field name="TONG" class="java.math.BigDecimal"/>
	<field name="officer_code" class="java.lang.String"/>
	<variable name="SUM_PTV_DB" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PTV_DB}]]></variableExpression>
	</variable>
	<variable name="SUM_PHU1_DB" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU1_DB}]]></variableExpression>
	</variable>
	<variable name="SUM_PHU2_DB" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU2_DB}]]></variableExpression>
	</variable>
	<variable name="SUM_PHU3_DB" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU3_DB}]]></variableExpression>
	</variable>
	<variable name="S_GMC_DB" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{GMC_DB}]]></variableExpression>
	</variable>
	<variable name="S_PM_DB" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PM_DB}]]></variableExpression>
	</variable>
	<variable name="S_DDV_DB" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DDV_DB}]]></variableExpression>
	</variable>
	<variable name="SDCV_DB" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DCV_DB}]]></variableExpression>
	</variable>
	<variable name="SPTV_LOAI1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PTV_LOAI1}]]></variableExpression>
	</variable>
	<variable name="SPHU1_LOAI1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU1_LOAI1}]]></variableExpression>
	</variable>
	<variable name="SPHU2_LOAI1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU2_LOAI1}]]></variableExpression>
	</variable>
	<variable name="SPHU3_LOAI1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU3_LOAI1}]]></variableExpression>
	</variable>
	<variable name="SGMC_LOAI1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{GMC_LOAI1}]]></variableExpression>
	</variable>
	<variable name="SPM_LOAI1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PM_LOAI1}]]></variableExpression>
	</variable>
	<variable name="SDDV_LOAI1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DDV_LOAI1}]]></variableExpression>
	</variable>
	<variable name="SDCV_LOAI1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DCV_LOAI1}]]></variableExpression>
	</variable>
	<variable name="SPTV_LOAI2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PTV_LOAI2}]]></variableExpression>
	</variable>
	<variable name="SPHU1_LOAI2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU1_LOAI2}]]></variableExpression>
	</variable>
	<variable name="SPHU2_LOAI2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU2_LOAI2}]]></variableExpression>
	</variable>
	<variable name="SPHU3_LOAI2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU3_LOAI2}]]></variableExpression>
	</variable>
	<variable name="SGMC_LOAI2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{GMC_LOAI2}]]></variableExpression>
	</variable>
	<variable name="SPM_LOAI2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PM_LOAI2}]]></variableExpression>
	</variable>
	<variable name="SDDV_LOAI2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DDV_LOAI2}]]></variableExpression>
	</variable>
	<variable name="SDCV_LOAI2" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DCV_LOAI2}]]></variableExpression>
	</variable>
	<variable name="SPTV_LOAI3" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PTV_LOAI3}]]></variableExpression>
	</variable>
	<variable name="SPHU1_LOAI3" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU1_LOAI3}]]></variableExpression>
	</variable>
	<variable name="SPHU2_LOAI3" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHU2_LOAI3}]]></variableExpression>
	</variable>
	<variable name="SGMC_LOAI3" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{GMC_LOAI3}]]></variableExpression>
	</variable>
	<variable name="SPM1_LOAI3" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PM1_LOAI3}]]></variableExpression>
	</variable>
	<variable name="SPM2_LOAI3" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PM1_LOAI3}]]></variableExpression>
	</variable>
	<variable name="SDDV_LOAI3" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DDV_LOAI3}]]></variableExpression>
	</variable>
	<variable name="STONG" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="86" splitType="Stretch">
			<textField>
				<reportElement x="20" y="0" width="200" height="17" uuid="1eefe9a3-96b7-402e-acdc-b1ddb300ef6f"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="20" y="33" width="200" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="8e559eeb-8ded-4d0f-b9b7-67d571123eb9"/>
				<box leftPadding="3">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA["Khoa :  "+ ($F{TENKHOA}=='-1'?"2 cơ sở":($F{TENKHOA}== null? "" : $F{TENKHOA}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="20" y="17" width="200" height="16" uuid="12168748-c6e1-49cd-a3b2-e4bf68106de2"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="51" width="1150" height="19" uuid="bb8bde97-a55f-4f38-b1f3-800eff46e5c2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BẢNG KÊ SỐ LƯỢNG BÁC SỸ, KỸ THUẬT VIÊN THAM GIA " + ($P{loaiid} == '1' ? "PHẪU THUẬT": ($P{loaiid} == '2' ? "THỦ THUẬT":"PHẪU THUẬT THỦ THUẬT"))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement positionType="Float" x="220" y="70" width="720" height="16" uuid="b2878aba-3528-41a5-b13b-dc211420daa1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày: " + new SimpleDateFormat("dd/MM/yyyy").format( $P{tungay})  + "    Đến ngày: " +  new SimpleDateFormat("dd/MM/yyyy").format( $P{denngay})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="77" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="20" height="77" uuid="be4f14db-58c0-4bfd-8054-9971c242258f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[S
T
T]]></text>
			</staticText>
			<staticText>
				<reportElement x="20" y="0" width="123" height="77" uuid="16e91070-af1c-4e37-abaa-9bd5531d4b21"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Họ và tên
(Bác sĩ, KTV)]]></text>
			</staticText>
			<textField>
				<reportElement x="220" y="21" width="30" height="56" uuid="863e0e41-de18-42b5-ae83-3660527b9f48"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid} == '1' ? "PT": ($P{loaiid} == '2' ? "TT":"PT-TT")) + "VC (Bs)"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="250" y="21" width="30" height="56" uuid="5bce8d7a-5ab5-4812-9057-c734f047b5ff"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 1 (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="280" y="21" width="30" height="56" uuid="3556a76b-9dff-40c4-81cf-4c010adb97d2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 2 (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="310" y="21" width="30" height="56" uuid="7b419dbe-66ad-40c1-9cd4-acd771d20e56"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 3 (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="340" y="21" width="30" height="56" uuid="345a74f9-3cb6-46a4-818e-47049e784cd3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[GMC (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="370" y="21" width="30" height="56" uuid="f4102695-5801-4b82-87ba-d787cfb40f7c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ GM (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="21" width="30" height="56" uuid="b54b030c-2102-43d0-a71f-5450f9742f02"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Giúp việc 1 (Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="430" y="21" width="30" height="56" uuid="3158f316-c527-4721-9046-2f57962fc196"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Giúp việc 2 (Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="580" y="21" width="30" height="56" uuid="a42c8395-33b0-4eed-abbf-1abebac04362"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[GMC (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="490" y="21" width="30" height="56" uuid="30b2e65b-fed0-411b-b0ac-ab3d614cd620"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 1 (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="610" y="21" width="30" height="56" uuid="9d5fb666-1be6-44e1-bb86-718bfda65c33"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ GM (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="550" y="21" width="30" height="56" uuid="5a38e5ce-3a76-4f2a-9a07-cbaf7cc6341d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 3 (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="640" y="21" width="30" height="56" uuid="cbe78d0e-2b6a-4e5b-bab3-8eeb8859fd52"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Giúp việc 1 (Ktv)]]></text>
			</staticText>
			<textField>
				<reportElement x="460" y="21" width="30" height="56" uuid="d98417b6-47e9-45e8-9255-647cd8b60433"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid} == '1' ? "PT": ($P{loaiid} == '2' ? "TT":"PT-TT")) + "VC (Bs)"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="670" y="21" width="30" height="56" uuid="c093f48f-8df7-4ac5-b3f7-5c51c85ceba6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Giúp việc 2 (Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="520" y="21" width="30" height="56" uuid="e77a4c38-5591-4d2b-8316-259da8a351b6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 2 (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="820" y="21" width="30" height="56" uuid="4f36c6d1-0c52-44fc-af73-7b7234ac01da"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[GMC (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="730" y="21" width="30" height="56" uuid="fa52ac0e-96f7-43b9-a669-8f81af88cc70"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 1 (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="850" y="21" width="30" height="56" uuid="5ef6b6be-cbaa-4188-b73c-140eb5ca5a28"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ GM (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="790" y="21" width="30" height="56" uuid="00ddeae1-1f66-4a63-a95b-bde36064a0a5"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 3 (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="880" y="21" width="30" height="56" uuid="19b8c4c9-27d3-4f3b-b98c-a740e2bbd79a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Giúp việc 1 (Ktv)]]></text>
			</staticText>
			<textField>
				<reportElement x="700" y="21" width="30" height="56" uuid="fc07369f-f9f9-4d9e-a908-93eb8c9708b3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid} == '1' ? "PT": ($P{loaiid} == '2' ? "TT":"PT-TT")) + "VC (Bs)"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="910" y="21" width="30" height="56" uuid="3134dfdc-fa06-4426-b777-9e7b8f616053"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Giúp việc 2 (Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="760" y="21" width="30" height="56" uuid="39e80c02-3868-456c-8353-688d7204f2ed"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 2 (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="1030" y="21" width="30" height="56" uuid="b29d27f6-3e55-4bdd-97c4-18854e70e675"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[GMC (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="970" y="21" width="30" height="56" uuid="b844b55f-387e-455f-a8fc-d75733f50e54"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 1 (Bs,Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="1060" y="21" width="30" height="56" uuid="2be556e5-0404-45e3-ae35-484483a47720"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ GM (Bs, Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="1120" y="21" width="30" height="56" uuid="c4722eec-5ace-43ff-8a0f-b9e9c4464f6c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Giúp việc]]></text>
			</staticText>
			<textField>
				<reportElement x="940" y="21" width="30" height="56" uuid="c821eada-0ff7-4e66-a924-7c19a8ae5594"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid} == '1' ? "PT": ($P{loaiid} == '2' ? "TT":"PT-TT")) + "VC (Bs)"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1000" y="21" width="30" height="56" uuid="cac9dd12-6514-432e-85de-ed35a2ce69cc"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ 2 (Bs,Ktv)]]></text>
			</staticText>
			<staticText>
				<reportElement x="1090" y="21" width="30" height="56" uuid="7ed43e50-2082-40c7-872c-a2b0a4f14e28"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[Phụ GM (Bs, Ktv)]]></text>
			</staticText>
			<textField>
				<reportElement x="220" y="0" width="240" height="21" uuid="f7c78fb0-fda2-48a1-be4d-b535da8f1166"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid}=="-1"?"":($P{loaiid_TEXT}).toUpperCase()) + " LOẠI ĐẶC BIỆT"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="700" y="0" width="240" height="21" uuid="21669729-2afc-4fb7-acf0-620809e74a38"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid}=="-1"?"":($P{loaiid_TEXT}).toUpperCase()) + " LOẠI II"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="460" y="0" width="240" height="21" uuid="3b0602e9-7c54-4209-adbb-c6e6843dc7fe"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid}=="-1"?"":($P{loaiid_TEXT}).toUpperCase()) + " LOẠI I"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="940" y="0" width="210" height="21" uuid="77c68057-4fe6-473a-b48f-80ac7cf98740"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid}=="-1"?"":($P{loaiid_TEXT}).toUpperCase()) + " LOẠI III"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="143" y="0" width="77" height="77" uuid="63786be1-cb3b-40b8-9d2b-97870f447dd6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã nhân viên]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="25" splitType="Prevent">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="20" y="0" width="123" height="25" uuid="b81743ae-6359-4787-aa87-c363838225ce"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOTEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1" stretchType="RelativeToBandHeight" x="220" y="0" width="30" height="25" uuid="ca772a7f-32f9-4a2b-b408-8f10de17be44"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PTV_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PTV_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_1" stretchType="RelativeToBandHeight" x="250" y="0" width="30" height="25" uuid="eec424fe-e506-42c0-a0af-1fb789e01134"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU1_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU1_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_2" stretchType="RelativeToBandHeight" x="280" y="0" width="30" height="25" uuid="dcf22233-100c-40e2-81a3-8b0fc9fce02e"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU2_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU2_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_3" stretchType="RelativeToBandHeight" x="310" y="0" width="30" height="25" uuid="19412570-66d6-4776-9569-e66215deccef"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU3_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU3_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_4" stretchType="RelativeToBandHeight" x="340" y="0" width="30" height="25" uuid="8bed7717-d5b6-42da-8bc4-baf1aeadca36"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GMC_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{GMC_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_5" stretchType="RelativeToBandHeight" x="370" y="0" width="30" height="25" uuid="8c0e9b3f-4e8d-49ca-a5dc-bb222587429d"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PM_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PM_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_6" stretchType="RelativeToBandHeight" x="400" y="0" width="30" height="25" uuid="6b2eeb30-91a3-4e23-85b3-e707c92540cc"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DDV_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{DDV_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_7" stretchType="RelativeToBandHeight" x="430" y="0" width="30" height="25" uuid="dfd77e2e-f451-403a-a238-342d01403a53"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DCV_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{DCV_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_9" stretchType="RelativeToBandHeight" x="490" y="0" width="30" height="25" uuid="c6020869-d691-4150-9e1f-9a1b4598449d"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU1_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU1_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_8" stretchType="RelativeToBandHeight" x="460" y="0" width="30" height="25" uuid="e28e99ce-1a7f-4f90-a5c5-1a428a11082f"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PTV_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PTV_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_14" stretchType="RelativeToBandHeight" x="640" y="0" width="30" height="25" uuid="ed6917de-ec41-45d4-8af7-cd63269c40f8"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DDV_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{DDV_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_12" stretchType="RelativeToBandHeight" x="580" y="0" width="30" height="25" uuid="90c01097-f678-4d68-8e92-a15dec21c78a"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GMC_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{GMC_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_15" stretchType="RelativeToBandHeight" x="670" y="0" width="30" height="25" uuid="572a6859-d940-4986-9053-ebef8a38a97e"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DCV_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{DCV_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_11" stretchType="RelativeToBandHeight" x="550" y="0" width="30" height="25" uuid="0a27cdcb-f0cd-40b7-b683-ab084f012ebc"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU3_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU3_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_13" stretchType="RelativeToBandHeight" x="610" y="0" width="30" height="25" uuid="392eaa6d-c8de-43d2-86b5-b2fa7b461651"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PM_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PM_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_10" stretchType="RelativeToBandHeight" x="520" y="0" width="30" height="25" uuid="12838964-cdef-49dc-83e7-8f96d14cde19"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU2_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU2_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_17" stretchType="RelativeToBandHeight" x="730" y="0" width="30" height="25" uuid="f5f765a4-a0cd-4189-9ff6-ad873caee19c"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU1_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU1_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_16" stretchType="RelativeToBandHeight" x="700" y="0" width="30" height="25" uuid="fce7cc93-b22c-4d3a-b4fb-322c7b4c282d"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PTV_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PTV_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_22" stretchType="RelativeToBandHeight" x="880" y="0" width="30" height="25" uuid="7adc7f99-5409-4841-88c9-8b5d01d8ee4e"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DDV_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{DDV_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_20" stretchType="RelativeToBandHeight" x="820" y="0" width="30" height="25" uuid="ba87f2da-fa2d-4578-b8b9-650846c9adb9"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GMC_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{GMC_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_23" stretchType="RelativeToBandHeight" x="910" y="0" width="30" height="25" uuid="2dd8df61-fde2-45a8-bc0c-b80375eaae2d"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DCV_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{DCV_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_19" stretchType="RelativeToBandHeight" x="790" y="0" width="30" height="25" uuid="8e6eb821-e4b8-4914-b6e4-eee8b9700cda"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU3_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU3_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_21" stretchType="RelativeToBandHeight" x="850" y="0" width="30" height="25" uuid="ab3cfe07-089e-4219-80d8-f2f70250d6da"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PM_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PM_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_18" stretchType="RelativeToBandHeight" x="760" y="0" width="30" height="25" uuid="bd0681d9-8265-4eab-a16f-490a9cbc0cff"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU2_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU2_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_25" stretchType="RelativeToBandHeight" x="970" y="0" width="30" height="25" uuid="60e7586e-cb39-42b6-931d-4981a1782343"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU1_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU1_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_24" stretchType="RelativeToBandHeight" x="940" y="0" width="30" height="25" uuid="85f7308e-09f7-4784-9201-089a36e61bde"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PTV_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PTV_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_30" stretchType="RelativeToBandHeight" x="1120" y="0" width="30" height="25" uuid="693f23f8-bd16-49dc-97a1-0ffe6c556399"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DDV_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{DDV_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_28" stretchType="RelativeToBandHeight" x="1030" y="0" width="30" height="25" uuid="e4ea413f-e845-40f6-85a0-bf1418b008a5"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GMC_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{GMC_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_29" stretchType="RelativeToBandHeight" x="1060" y="0" width="30" height="25" uuid="93191d44-10d8-4d8d-9eb7-0a0b826e3c6d"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PM1_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PM1_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_26" stretchType="RelativeToBandHeight" x="1000" y="0" width="30" height="25" uuid="bd1443ad-891a-48fe-bf60-1170507d139b"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHU2_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PHU2_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="20" height="25" uuid="88872afd-16cf-4dbb-9dc5-58824a968dc3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,###.#" isBlankWhenNull="true">
				<reportElement style="style1_27" stretchType="RelativeToBandHeight" x="1090" y="0" width="30" height="25" uuid="a7f0135e-d8d2-4ab0-950c-a48eff878573"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PM2_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{PM2_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="143" y="0" width="77" height="25" uuid="f2cf3f90-7c60-4780-b7b6-a6024b4d8096"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{officer_code}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="20">
			<textField pattern="dd" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="0" y="0" width="340" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="484f0771-2e64-4822-b3fd-53d65aad00f3"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày giờ in: " + new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(new java.util.Date())]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="199" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="220" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="6b07cdea-da09-4004-ae76-9624e99e087f"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SUM_PTV_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SUM_PTV_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="250" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="bda4bdae-bc7d-4bf6-82c5-5ea610c93d29"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SUM_PHU1_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SUM_PHU1_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="310" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="8a2843f0-e061-4bcd-8771-536a519b747a"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SUM_PHU3_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SUM_PHU3_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="280" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="bb45abaa-c3c6-4874-9f17-a8789f1753d8"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SUM_PHU2_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SUM_PHU2_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="370" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="c348178d-4e08-45fa-a633-5f1c46ee7db4"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{S_PM_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{S_PM_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="340" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="24e305fd-102d-4f5a-ac70-de9095d1e87a"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{S_GMC_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{S_GMC_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="430" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="47ac7f8c-fa20-4a2b-b14b-5dbb8b253e22"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SDCV_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SDCV_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="400" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="91bcc8e3-0d93-47e3-953c-8240cfde2827"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{S_DDV_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{S_DDV_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="580" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="274d311e-9721-42d8-88b6-fe101774f891"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SGMC_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SGMC_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="640" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="614e51e7-8ddf-4e4e-bf82-2eaee419232c"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SDDV_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SDDV_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="490" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="b13bf75f-3fd9-42be-b673-9f74fcdda1bb"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPHU1_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPHU1_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="550" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="390c8ede-7b67-4a7a-abe5-18dd7fa5ce9e"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPHU3_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPHU3_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="610" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="7c5fa65c-b5ea-4411-942b-e88711461bd5"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPM_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPM_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="520" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="51f09166-06e2-4e2b-9d99-8e0ffffb2c58"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPHU2_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPHU2_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="670" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="2dcae325-7a09-412e-9fef-a240ae9249e6"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SDCV_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SDCV_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="460" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="4b5ab67e-a183-474f-92f3-ec0a6f4663b4"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPTV_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPTV_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="820" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="69a42fc2-59ef-41e6-bf62-0256cf0c9728"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SGMC_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SGMC_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="880" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="7434fb16-c670-4b78-a054-00b781f6421c"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SDDV_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SDDV_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="730" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="9208562f-fe0e-48f8-9059-2ff30ec91347"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPHU1_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPHU1_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="790" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="4752d6b1-e17b-4e87-a9fd-20bb1beb5e64"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPHU3_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPHU3_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="850" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="ee0f704e-6ae6-461d-b0a3-7d037685d852"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPM_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPM_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="760" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="b2d92323-7178-4386-a417-98ec195a3ba5"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPHU2_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPHU2_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="910" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="451152e2-68a6-42a8-bd14-637ddb90efe1"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SDCV_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SDCV_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="700" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="ad0a3bd2-3fcf-4422-8da1-c6d329b81829"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPTV_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPTV_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="1030" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="93b5be10-01a9-4bab-b34d-2a173352bf9a"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SGMC_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SGMC_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="1120" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="ad486d1d-56eb-4721-9892-0333d6a15de0"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SDDV_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SDDV_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="970" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="5c1b8232-c240-4dda-8207-422d4db374b3"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPHU1_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPHU1_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="1060" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="d182cb42-51c4-48e7-913e-5616ff3a50b5"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPM1_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPM1_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="1000" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="51bf7244-6a36-4093-92d1-4d66e2155a42"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPHU2_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPHU2_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="940" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="79575741-fa7c-4205-a76e-c2a60d5abbbe"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPTV_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPTV_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="0" y="0" width="220" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="4a17cf22-2c3d-4f70-ba17-62f652eaa26c"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="5"/>
				</textElement>
				<text><![CDATA[Tổng]]></text>
			</staticText>
			<staticText>
				<reportElement x="940" y="107" width="210" height="17" uuid="7048f3e9-1535-42fe-8377-933fdd131514"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[NGƯỜI LẬP BIỂU]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="107" width="220" height="17" uuid="522c4ce6-36f6-42bb-a6b7-5ddbca0a8936"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[PHÒNG CNTT]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="1090" y="0" width="30" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="1bdc5927-cfdf-4e9a-a74b-0924ae12b38c"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPM2_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPM2_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="20" y="31" width="200" height="19" uuid="fd7c83ba-1cf7-4689-b48a-90ff68a405eb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid}=="-1"?"":($P{loaiid_TEXT})) + " loại đặc biệt"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="220" y="31" width="60" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="281597d3-b66c-479b-a86a-6ccc6f63e526"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SUM_PTV_DB}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SUM_PTV_DB}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="20" y="50" width="200" height="19" uuid="dc7ad9dc-7933-4c7b-bd2a-166f5e657aab"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid}=="-1"?"":($P{loaiid_TEXT})) + " loại I"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="220" y="50" width="60" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="00b87f6a-514f-4777-a281-4f82aeb074ed"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPTV_LOAI1}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPTV_LOAI1}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="20" y="69" width="200" height="19" uuid="980cee9f-645c-4eee-abde-3b371462ab33"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid}=="-1"?"":($P{loaiid_TEXT})) + " loại II"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="20" y="88" width="200" height="19" uuid="5c0d5c11-b1a7-4381-9364-9740e9b4fad4"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{loaiid}=="-1"?"":($P{loaiid_TEXT})) + " loại III"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="220" y="69" width="60" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="3506233b-7ad1-4471-aa50-2525a562b5d5"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPTV_LOAI2}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPTV_LOAI2}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.#" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="220" y="88" width="60" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="212756fe-bc23-4f7c-b156-982f15e4fac9"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SPTV_LOAI3}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{SPTV_LOAI3}.toString().contains(".") ? "#,##0.#" : "#,##0"]]></patternExpression>
			</textField>
			<textField pattern="dd" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="940" y="88" width="210" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="db5faee9-bf6f-4d49-98f5-32262eb71349"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + new SimpleDateFormat("dd").format(new java.util.Date()) + " tháng " + new SimpleDateFormat("MM").format(new java.util.Date()) + " năm " + new SimpleDateFormat("yyyy").format(new java.util.Date())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="220" y="107" width="720" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="3fa79888-1b68-426f-9b15-116523ccb68a"/>
				<box leftPadding="3">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA["TRƯỞNG " + ($F{TENKHOA}== null ? "KHOA" : ($F{TENKHOA}=='-1' ? "KHOA":($F{TENKHOA}.contains(',') ? "KHOA": $F{TENKHOA}.toUpperCase())))]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BAOCAOTHUTIENTHEO_HOADON_BM2_BK" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="f5a023ed-670e-4e1b-8458-930639e175d1">
	<property name="ireport.zoom" value="1.1566298682885954"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="i_tungay" class="java.sql.Timestamp"/>
	<parameter name="i_denngay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_nguoithuid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="doituongid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ORACLE_REF_CURSOR" class="java.sql.ResultSet"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="khoachidinhid" class="java.lang.Long"/>
	<queryString language="plsql">
		<![CDATA[{call vpi_ngt_thutien_ngay_hd_bm2_bk($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{i_tungay},$P{i_denngay},$P{khoachidinhid},$P{i_nguoithuid},$P{doituongid},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="SOHD" class="java.lang.String"/>
	<field name="TIEPNHANID" class="java.math.BigDecimal"/>
	<field name="DATRA" class="java.math.BigDecimal"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="NGAYTHU" class="java.sql.Timestamp"/>
	<field name="MATIEPNHAN" class="java.lang.String"/>
	<field name="NOITIEPNHAN" class="java.lang.String"/>
	<field name="DOITUONG" class="java.lang.String"/>
	<field name="HOANTRA_TRONGNGAY" class="java.math.BigDecimal"/>
	<field name="HOANTRA_KHACNGAY" class="java.math.BigDecimal"/>
	<field name="NGUOITHU" class="java.lang.String"/>
	<field name="READ_TONGTIEN" class="java.lang.String"/>
	<field name="nguoilapbieu" class="java.lang.String"/>
	<field name="user_name" class="java.lang.String"/>
	<variable name="TONG_THU_DiemThu" class="java.math.BigDecimal" resetType="Group" resetGroup="DIEMTHU" calculation="Sum">
		<variableExpression><![CDATA[$F{DATRA}]]></variableExpression>
	</variable>
	<variable name="TONG_DIEMTHU_HOANTRONGNGAY" class="java.math.BigDecimal" resetType="Group" resetGroup="DIEMTHU" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA_TRONGNGAY}]]></variableExpression>
	</variable>
	<variable name="TONG_DIEMTHU_HOANKHACNGAY" class="java.math.BigDecimal" resetType="Group" resetGroup="DIEMTHU" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA_KHACNGAY}]]></variableExpression>
	</variable>
	<variable name="V_CURRENT_PAGE_NUMBER" class="java.lang.Integer" resetType="Page">
		<variableExpression><![CDATA[1]]></variableExpression>
		<initialValueExpression><![CDATA[$V{PAGE_NUMBER}]]></initialValueExpression>
	</variable>
	<variable name="TONGCONG_THUPHI" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DATRA}]]></variableExpression>
	</variable>
	<variable name="TONGCONG_HOANTRONGNGAY" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA_TRONGNGAY}]]></variableExpression>
	</variable>
	<variable name="TONGCONG_HOANKHACNGAY" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA_KHACNGAY}]]></variableExpression>
	</variable>
	<variable name="cout_STT" class="java.lang.Double" resetType="Group" resetGroup="NGUOITHUTIEN" calculation="Count">
		<variableExpression><![CDATA[$V{cout_STT}+1]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<variable name="TONG_THU_DoiTuong" class="java.math.BigDecimal" resetType="Group" resetGroup="DOITUONG" calculation="Sum">
		<variableExpression><![CDATA[$F{DATRA}]]></variableExpression>
	</variable>
	<variable name="TONG_THU_NguoiThu" class="java.math.BigDecimal" resetType="Group" resetGroup="NGUOITHUTIEN" calculation="Sum">
		<variableExpression><![CDATA[$F{DATRA}]]></variableExpression>
	</variable>
	<variable name="STT_NguoiThu" class="java.lang.Integer" resetType="Group" resetGroup="DIEMTHU" incrementType="Group" incrementGroup="NGUOITHUTIEN" calculation="Count">
		<variableExpression><![CDATA[$V{STT_NguoiThu} + 1]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="STT_DoiTuong" class="java.lang.Integer" incrementType="Group" incrementGroup="DOITUONG" calculation="Count">
		<variableExpression><![CDATA[$V{STT_DoiTuong}]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<variable name="STT_DiemThu" class="java.lang.Integer" resetType="Group" resetGroup="DOITUONG" incrementType="Group" incrementGroup="DIEMTHU" calculation="Count">
		<variableExpression><![CDATA[$V{STT_DiemThu} + 1]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="TONG_NGUOITHU_HOANTRONGNGAY" class="java.math.BigDecimal" resetType="Group" resetGroup="NGUOITHUTIEN" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA_TRONGNGAY}]]></variableExpression>
	</variable>
	<variable name="TONG_NGUOITHU_HOANKHACNGAY" class="java.math.BigDecimal" resetType="Group" resetGroup="NGUOITHUTIEN" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA_KHACNGAY}]]></variableExpression>
	</variable>
	<variable name="TONG_DOITUONG_HOANTRONGNGAY" class="java.math.BigDecimal" resetType="Group" resetGroup="DOITUONG" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA_TRONGNGAY}]]></variableExpression>
	</variable>
	<variable name="TONG_DOITUONG_HOANKHACNGAY" class="java.math.BigDecimal" resetType="Group" resetGroup="DOITUONG" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA_KHACNGAY}]]></variableExpression>
	</variable>
	<group name="DOITUONG">
		<groupExpression><![CDATA[$F{DOITUONG}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="DOITUONG" isBlankWhenNull="true">
					<reportElement positionType="Float" x="0" y="0" width="555" height="20" uuid="23a7beac-b6bb-43b0-a7ff-81af42dcd353"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[($V{STT_DoiTuong} == 1 ? "I" : "II")+". " +"ĐỐI TƯỢNG: "+($F{DOITUONG}.equals(null)?"":$F{DOITUONG})]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<textField isStretchWithOverflow="true" pattern=" #,##0" isBlankWhenNull="true">
					<reportElement x="496" y="0" width="59" height="20" uuid="29fd5456-3e95-444e-bcc8-b8a63c188e5b"/>
					<box leftPadding="0" rightPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_DOITUONG_HOANKHACNGAY} == null ? 0 : new DecimalFormat("###,###").format($V{TONG_DOITUONG_HOANKHACNGAY}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern=" #,##0" isBlankWhenNull="true">
					<reportElement x="270" y="0" width="75" height="20" uuid="939f36ac-8c77-44ba-80dc-26bfd5bc8d93"/>
					<box rightPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_THU_DoiTuong} == null ? 0 : new DecimalFormat("###,###").format($V{TONG_THU_DoiTuong}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="0" y="0" width="270" height="20" uuid="cbb665aa-c6d9-4a6c-b952-709c6c386f95"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[Tổng cộng]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="DOITUONG" pattern=" #,##0" isBlankWhenNull="true">
					<reportElement x="434" y="0" width="62" height="20" uuid="dbefc98d-4c56-43c4-bd11-eceeec9cc014"/>
					<box leftPadding="0" rightPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_DOITUONG_HOANTRONGNGAY} == null ? 0 : new DecimalFormat("###,###").format($V{TONG_DOITUONG_HOANTRONGNGAY}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="345" y="0" width="89" height="20" uuid="34ed1e7f-6123-4a8a-8224-d422fd7452d4"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<group name="DIEMTHU">
		<groupExpression><![CDATA[$F{NOITIEPNHAN}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="DIEMTHU" isBlankWhenNull="true">
					<reportElement positionType="Float" x="0" y="0" width="555" height="20" uuid="5346fb96-bd10-4f57-bb4d-2ccf04b0feec"/>
					<box leftPadding="15">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{STT_DiemThu}+". "+"ĐIỂM THU: "+($F{NOITIEPNHAN}.equals(null)?"":$F{NOITIEPNHAN})]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<staticText>
					<reportElement positionType="Float" x="0" y="0" width="270" height="20" uuid="1af9514a-4cea-45ee-94f2-f2f78726f786"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[Tổng cộng]]></text>
				</staticText>
				<textField pattern=" #,##0" isBlankWhenNull="true">
					<reportElement x="270" y="0" width="75" height="20" uuid="d3f28a5e-3239-43b3-bae8-45451c2bc69c"/>
					<box rightPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_THU_DiemThu} == null ? 0 : new DecimalFormat("###,###").format($V{TONG_THU_DiemThu}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="DIEMTHU" pattern=" #,##0" isBlankWhenNull="true">
					<reportElement x="496" y="0" width="59" height="20" uuid="a936bd9c-7777-4cbc-8442-fd938d9faf8f"/>
					<box leftPadding="0" rightPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_DIEMTHU_HOANKHACNGAY} == null ? 0 : new DecimalFormat("###,###").format($V{TONG_DIEMTHU_HOANKHACNGAY}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="345" y="0" width="89" height="20" uuid="993ecfa9-99a2-42d5-b1f2-824140750ac5"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="DIEMTHU" pattern=" #,##0" isBlankWhenNull="true">
					<reportElement x="434" y="0" width="62" height="20" uuid="b693c785-050a-4421-ba6a-5cba85baa592"/>
					<box leftPadding="0" rightPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_DIEMTHU_HOANTRONGNGAY} == null ? 0 : new DecimalFormat("###,###").format($V{TONG_DIEMTHU_HOANTRONGNGAY}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="NGUOITHUTIEN">
		<groupExpression><![CDATA[$F{NGUOITHU}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NGUOITHUTIEN" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="270" height="20" uuid="6da51104-800a-4c4d-a6a4-93a4d9e1c531"/>
					<box leftPadding="20">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{STT_DiemThu}+"."+$V{STT_NguoiThu}+". "+($F{NGUOITHU}.equals(null)?"":$F{NGUOITHU})+" "+($F{user_name}.equals(null)?"":"("+$F{user_name}+")")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NGUOITHUTIEN" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="270" y="0" width="75" height="20" uuid="3e2dad70-f66d-43e7-8730-865784b974f7"/>
					<box rightPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_THU_NguoiThu} == null ? 0 : new DecimalFormat("###,###").format($V{TONG_THU_NguoiThu}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NGUOITHUTIEN" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="434" y="0" width="62" height="20" uuid="e4a5d797-b66d-4047-b0ca-8444fa27a042"/>
					<box leftPadding="0" rightPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_NGUOITHU_HOANTRONGNGAY} == null ? 0 : new DecimalFormat("###,###").format($V{TONG_NGUOITHU_HOANTRONGNGAY}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NGUOITHUTIEN" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="496" y="0" width="59" height="20" uuid="4209f083-7d7d-4e74-ba2e-45f5196b795b"/>
					<box leftPadding="0" rightPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{TONG_NGUOITHU_HOANKHACNGAY} == null ? 0 : new DecimalFormat("###,###").format($V{TONG_NGUOITHU_HOANKHACNGAY}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement stretchType="RelativeToBandHeight" x="345" y="0" width="89" height="20" uuid="707f5f11-5028-4715-93de-fc01eca19528"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</band>
		</groupHeader>
	</group>
	<group name="TONGCONG2">
		<groupExpression><![CDATA[$F{NOITIEPNHAN}]]></groupExpression>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="89" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="100" height="20" uuid="d1768d68-73e7-4698-9748-15a60e4db3d0"/>
				<textElement>
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Bộ Y Tế]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="40" width="555" height="20" uuid="3a7552e2-13a8-49d5-a665-09dce5a2d8d7"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[BẢNG KÊ THU TIỀN NGOẠI TRÚ THEO SỐ HÓA ĐƠN]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement x="0" y="67" width="555" height="20" uuid="89be9b22-baa9-448b-81e5-0fcdefb15bcb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày " + new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format($P{i_tungay})  + " đến ngày " +  new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format( $P{i_denngay})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="22" width="410" height="15" uuid="0efe7fb7-6900-43ca-bc1c-d818ee1c9945"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="17" splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="31" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="22" height="31" uuid="b58957ac-6538-408e-9db7-880ee9c2d6e1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="22" y="0" width="61" height="31" uuid="8c289be9-11b4-4eef-9e43-762b2d823c36"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Số TN]]></text>
			</staticText>
			<staticText>
				<reportElement x="83" y="0" width="61" height="31" uuid="680d817b-cc54-4880-a7d0-c3a24450be81"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Số hoá đơn]]></text>
			</staticText>
			<staticText>
				<reportElement x="144" y="0" width="126" height="31" uuid="182323ba-8157-404d-af2d-7409200783b9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên bệnh nhân]]></text>
			</staticText>
			<staticText>
				<reportElement x="270" y="0" width="75" height="31" uuid="5c3f4d42-3ae6-4fc8-afa9-ad3bbf9f2063"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Thu phí]]></text>
			</staticText>
			<staticText>
				<reportElement x="345" y="0" width="89" height="31" uuid="46362387-86b5-49f0-8199-053fd6f43584"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Nơi thu tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="434" y="0" width="62" height="31" uuid="1424d58d-3e66-44ca-9687-aa6c13943e0f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Hoàn
trong ngày]]></text>
			</staticText>
			<staticText>
				<reportElement x="496" y="0" width="59" height="31" uuid="d6cc451f-2cf7-4e46-9aae-95059bfe62c1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Hoàn
khác ngày]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="32" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="496" y="0" width="59" height="32" uuid="918db204-12bb-4aa9-a9ec-e6145503d470"/>
				<box leftPadding="0" rightPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOANTRA_KHACNGAY} == null ? 0 : new DecimalFormat("###,###").format($F{HOANTRA_KHACNGAY}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="22" y="0" width="61" height="32" uuid="7f668103-fbc5-4d85-9274-fb1b0367d94d"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="434" y="0" width="62" height="32" uuid="506d34de-46f8-475a-9504-a9cee279e0c8"/>
				<box leftPadding="0" rightPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOANTRA_TRONGNGAY} == null ? 0 : new DecimalFormat("###,###").format($F{HOANTRA_TRONGNGAY}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="144" y="0" width="126" height="32" uuid="8f132642-0a61-4abd-aeaa-241e132d4480"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="83" y="0" width="61" height="32" uuid="fe29b8db-d93a-475d-a9d5-1f7bbe7a85c2"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOHD}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="345" y="0" width="89" height="32" uuid="2ebdbfa6-226d-4281-a065-6da4184b506e"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOITIEPNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="270" y="0" width="75" height="32" uuid="7a4cd035-56d2-4a25-8dd1-30277bba72e3"/>
				<box rightPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DATRA} == null ? 0 : new DecimalFormat("###,###").format($F{DATRA}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="22" height="32" uuid="e692d86d-b0cf-45d9-aa9c-59463beb2074"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{cout_STT}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="20">
			<textField>
				<reportElement x="7" y="0" width="147" height="20" uuid="68a8164f-b912-4294-8137-63e82aeddb20"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(new SimpleDateFormat("dd")).format(new Date())
+ " / " + (new SimpleDateFormat("MM")).format(new Date())
+ " / " + (new SimpleDateFormat("yyyy")).format(new Date())
+ "  " + (new SimpleDateFormat("HH")).format(new Date())
+ " : " + (new SimpleDateFormat("mm")).format(new Date())
+ " : " + (new SimpleDateFormat("ss")).format(new Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="160" y="0" width="142" height="20" uuid="fcb7162b-e419-4a3e-9669-b8e163dded5f"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Bottom" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[BCVP_057_BangKeSoBienLaiChiTiet]]></text>
			</staticText>
			<staticText>
				<reportElement x="464" y="0" width="30" height="20" uuid="b2f669e6-0cf2-40b4-8dd0-dfa057dde5cf"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Trang :]]></text>
			</staticText>
			<textField>
				<reportElement x="494" y="0" width="32" height="20" uuid="e0567a11-26b9-4719-8cae-0f0443f1ea4f"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}+"/"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="527" y="0" width="27" height="20" uuid="323f6678-6837-47a9-ab6b-0ee39a46b09d"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="159" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="270" height="20" uuid="48709592-156d-455b-a3ea-ba55fac458fa"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[TỔNG CỘNG]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern=" #,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="434" y="0" width="62" height="20" uuid="65119f3e-96be-446d-8722-a5eba3441cb1"/>
				<box leftPadding="0" rightPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{TONGCONG_HOANTRONGNGAY} == null ? 0 : new DecimalFormat("###,###").format($V{TONGCONG_HOANTRONGNGAY}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern=" #,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="270" y="0" width="75" height="20" uuid="2e868f34-d01a-47eb-b85e-97103c94cb01"/>
				<box rightPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{TONGCONG_THUPHI} == null ? 0 : new DecimalFormat("###,###").format($V{TONGCONG_THUPHI}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern=" #,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="496" y="0" width="59" height="20" uuid="6473c049-99e3-4b3d-a0a7-17b564a10622"/>
				<box leftPadding="0" rightPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{TONGCONG_HOANKHACNGAY} == null ? 0 : new DecimalFormat("###,###").format($V{TONGCONG_HOANKHACNGAY}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="345" y="0" width="89" height="20" uuid="575b5afd-8c6d-4633-b3ad-3936f60be52c"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="20" width="85" height="20" uuid="c408ed25-9f37-4ad6-9ee6-143406ce1671"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Số tiền thực nộp: ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="39" width="73" height="20" uuid="d5c7d570-5593-43eb-8ac2-200d81f1a1fb"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Bằng chữ: ]]></text>
			</staticText>
			<staticText>
				<reportElement x="15" y="87" width="100" height="15" uuid="d669d37c-e7b1-4b19-b21f-a550cb446def"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Tổ KSNB]]></text>
			</staticText>
			<staticText>
				<reportElement x="137" y="87" width="100" height="15" uuid="8b02a5c6-86b1-4a57-bc27-efd69c7b2beb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Tổ trưởng viện phí]]></text>
			</staticText>
			<staticText>
				<reportElement x="270" y="87" width="100" height="15" uuid="870b262a-e445-47c0-95d4-b4fbc7903aac"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Lãnh đạo đơn vị]]></text>
			</staticText>
			<textField>
				<reportElement x="393" y="72" width="147" height="15" uuid="4c9e45fc-4a69-49b8-8719-6cee07638d4d"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + (new SimpleDateFormat("dd")).format(new Date())
+ " tháng " + (new SimpleDateFormat("MM")).format(new Date())
+ " năm " + (new SimpleDateFormat("yyyy")).format(new Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="393" y="87" width="147" height="15" uuid="fcfd8456-d1d2-4d97-9f3a-0e94ee80ab66"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Người lập biểu]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="393" y="138" width="147" height="20" uuid="fe53f5a1-9352-4102-a1da-190d7993e875"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoilapbieu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern=" #,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="85" y="20" width="199" height="20" uuid="4a94a9f8-75c9-4547-a5e9-b0f8d942aeb0"/>
				<box rightPadding="2">
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("###,###").format(($V{TONGCONG_THUPHI}== null?0:$V{TONGCONG_THUPHI})
-($V{TONGCONG_HOANTRONGNGAY}== null?0:$V{TONGCONG_HOANTRONGNGAY})
-($V{TONGCONG_HOANKHACNGAY}== null?0:$V{TONGCONG_HOANKHACNGAY})).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="73" y="39" width="467" height="20" uuid="2e77ccb7-33f8-49ad-981a-f72adc60030d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{READ_TONGTIEN}.length()>1?$F{READ_TONGTIEN}.substring(0,1).toUpperCase()+$F{READ_TONGTIEN}.substring(1):$F{READ_TONGTIEN})+" đồng)"]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

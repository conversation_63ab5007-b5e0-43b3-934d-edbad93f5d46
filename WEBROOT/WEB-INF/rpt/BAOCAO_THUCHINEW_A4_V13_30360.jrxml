<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BAOCAO_THUCHINEW_A4_V6_30360" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="800" leftMargin="21" rightMargin="21" topMargin="20" bottomMargin="20" uuid="02afe4c9-87bb-4263-9fdc-5ca4328b421b">
	<property name="ireport.zoom" value="1.3636363636363638"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.export.detect.cell.type" value="true"/>
	<property name="net.sf.jasperreports.export.xls.detect.cell.type" value="true"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.columns" value="true"/>
	<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="tungay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="denngay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="doituongbenhnhanid" class="java.lang.Long"/>
	<parameter name="loaibenhanid" class="java.lang.String"/>
	<parameter name="i_nguoithuid" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call vpi_thuchinew_v13_30360($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{tungay},$P{denngay},$P{doituongbenhnhanid},$P{loaibenhanid},$P{i_nguoithuid},$P{ora_cursor})}]]>
	</queryString>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="MAPHIEUTHU" class="java.lang.String"/>
	<field name="KHOAPHONG" class="java.lang.String"/>
	<field name="HUY" class="java.math.BigDecimal"/>
	<field name="TAMUNG" class="java.math.BigDecimal"/>
	<field name="TAMTHUTT" class="java.math.BigDecimal"/>
	<field name="HOADON" class="java.math.BigDecimal"/>
	<field name="THUKHAC" class="java.math.BigDecimal"/>
	<field name="THUTHEM" class="java.math.BigDecimal"/>
	<field name="HOANTRA" class="java.math.BigDecimal"/>
	<field name="THUCTHU" class="java.math.BigDecimal"/>
	<field name="NGUOITHU" class="java.lang.String"/>
	<field name="TEN_DTBN" class="java.lang.String"/>
	<field name="TONGTIEN" class="java.lang.String"/>
	<field name="MIENGIAM" class="java.math.BigDecimal"/>
	<field name="TAMTHUTTMG" class="java.math.BigDecimal"/>
	<field name="matiepnhan" class="java.lang.String"/>
	<field name="mabenhnhan" class="java.lang.String"/>
	<field name="ngaythuchien" class="java.lang.String"/>
	<field name="loaiphieuthu" class="java.lang.String"/>
	<field name="lydo" class="java.lang.String"/>
	<field name="phieuorder" class="java.lang.String"/>
	<field name="manhomphieuthu" class="java.lang.String"/>
	<field name="tongtienso" class="java.math.BigDecimal"/>
	<field name="NGUOILAP" class="java.lang.String"/>
	<variable name="nt_tamung" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{TAMUNG}]]></variableExpression>
	</variable>
	<variable name="nt_HDDV" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{HOADON}]]></variableExpression>
	</variable>
	<variable name="nt_HDTK" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{THUKHAC}]]></variableExpression>
	</variable>
	<variable name="nt_tamthutt" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{TAMTHUTT}]]></variableExpression>
	</variable>
	<variable name="nt_thuthem" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{THUTHEM}]]></variableExpression>
	</variable>
	<variable name="nt_chira" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA}]]></variableExpression>
	</variable>
	<variable name="nt_thucthu" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{THUCTHU}]]></variableExpression>
	</variable>
	<variable name="tong_tamung" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TAMUNG}]]></variableExpression>
	</variable>
	<variable name="tong_HDDV" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{HOADON}]]></variableExpression>
	</variable>
	<variable name="tong_HDTK" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THUKHAC}]]></variableExpression>
	</variable>
	<variable name="tong_tamthutt" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TAMTHUTT}]]></variableExpression>
	</variable>
	<variable name="tong_thuthem" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THUTHEM}]]></variableExpression>
	</variable>
	<variable name="tong_chira" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{HOANTRA}]]></variableExpression>
	</variable>
	<variable name="tong_thucthu" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THUCTHU}]]></variableExpression>
	</variable>
	<variable name="nt_miengiam" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{MIENGIAM}]]></variableExpression>
	</variable>
	<variable name="tong_miengiam" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{MIENGIAM}]]></variableExpression>
	</variable>
	<variable name="nt_ttmiengiam" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{TAMTHUTTMG}]]></variableExpression>
	</variable>
	<variable name="tong_ttmiengiam" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TAMTHUTTMG}]]></variableExpression>
	</variable>
	<variable name="nt_huy" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{HUY} == null ? 0 : $F{HUY}]]></variableExpression>
	</variable>
	<variable name="nt_dichvu" class="java.math.BigDecimal" resetType="Group" resetGroup="nguoithu" calculation="Sum">
		<variableExpression><![CDATA[$F{THUTHEM}.doubleValue()+$F{THUKHAC}.doubleValue()]]></variableExpression>
	</variable>
	<variable name="tong_huy" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{HUY} == null ? 0 : $F{HUY}]]></variableExpression>
	</variable>
	<variable name="tong_dichvu" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THUKHAC}.doubleValue() + $F{THUTHEM}.doubleValue()]]></variableExpression>
	</variable>
	<group name="nguoithu">
		<groupExpression><![CDATA[$F{NGUOITHU}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="16" height="18" uuid="d4d56ec9-dbcf-44c6-b367-3875ca6f873d"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="119" y="0" width="46" height="18" uuid="61049ec0-8b29-4423-b6a9-d18082b53cd3"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="165" y="0" width="89" height="18" uuid="f65778fd-e4ac-46e5-bf8c-8bc8943f096f"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="254" y="0" width="48" height="18" uuid="484cf5d7-bd93-4bc7-92d0-942c95d40a3c"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="302" y="0" width="72" height="18" uuid="5ee2a13f-ae38-4ca7-9d4d-fde6969e07de"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isBold="true" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["Tổng tiền:"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="nguoithu" pattern="#,##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="374" y="0" width="50" height="18" uuid="c40d3c2d-d43a-472e-ac84-61a4db8e97e3"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isBold="true" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nt_tamung} != null ? ($V{nt_tamung}.doubleValue() >0 ? new DecimalFormat("#,##0").format($V{nt_tamung}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="424" y="0" width="25" height="18" uuid="6966f0bb-d5aa-42b0-b8d5-e046e14cca2c"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="nguoithu" pattern="#,##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="449" y="0" width="50" height="18" uuid="d8ede8f0-777c-437f-95a7-ea46e528dabb"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isBold="true" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nt_thuthem} != null ? ($V{nt_thuthem}.doubleValue() >0 ? new DecimalFormat("#,##0").format($V{nt_thuthem}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="499" y="0" width="25" height="18" uuid="d326b1eb-810b-45f4-b5c6-2d6b41a13f67"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="nguoithu" pattern="#,##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="524" y="0" width="48" height="18" uuid="e629a6b2-32c1-45f0-8775-8ec903b2e95b"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isBold="true" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nt_chira} != null ? ($V{nt_chira}.doubleValue() >0 ? new DecimalFormat("#,##0").format($V{nt_chira}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="nguoithu" pattern="#,##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="572" y="0" width="46" height="18" uuid="46383eb2-90a8-4cfc-a54b-29e5699f38c3"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isBold="true" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nt_huy} != null ? ($V{nt_huy}.doubleValue() >0 ? new DecimalFormat("#,##0").format($V{nt_huy}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="nguoithu" pattern="#,##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="618" y="0" width="40" height="18" uuid="24784cf6-b979-4afd-9f1d-9b64357d4d21"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isBold="true" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nt_miengiam} != null ? ($V{nt_miengiam}.doubleValue() >0 ? new DecimalFormat("#,##0").format($V{nt_miengiam}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="658" y="0" width="41" height="18" uuid="610adcce-c27e-4749-87b2-e60daf090179"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="nguoithu" pattern="#,##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="699" y="0" width="51" height="18" uuid="9701fc6d-64d1-4399-a328-2fa9ecf31877"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isBold="true" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nt_HDDV} != null ? ($V{nt_HDDV}.doubleValue() >0 ? new DecimalFormat("#,##0").format($V{nt_HDDV}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="nguoithu" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="750" y="0" width="26" height="18" uuid="47bc7d8c-ea58-412d-bb4a-62787572884e"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="776" y="0" width="24" height="18" uuid="34b1da13-0e3f-4a23-8f2a-3dbc44e8b262"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isItalic="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="16" y="0" width="103" height="18" uuid="4f0887c0-9245-4114-bbd5-9418c5d0ea5b"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGUOITHU}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="76" splitType="Stretch">
			<textField>
				<reportElement x="0" y="15" width="800" height="14" uuid="4374f61b-bc01-4b32-bad2-d5a9efcc50e0"/>
				<textElement>
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="800" height="14" uuid="14ce68be-aa74-4642-ad38-c97a9325f006"/>
				<textElement>
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="56" width="800" height="20" uuid="f759b8f3-ffdd-49c2-8364-f11a362465e5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày " + new SimpleDateFormat("dd/MM/yyyy").format($P{tungay}) + " đến ngày " + new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="29" width="800" height="27" uuid="f51cea07-6d12-441a-a624-10fa5ee0262c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[BÁO CÁO DOANH THU THU NGÂN]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="42" splitType="Stretch">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
			<staticText>
				<reportElement x="165" y="0" width="89" height="42" uuid="3d0eb0b3-6b21-46d1-9851-c427d90378cc"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Họ và tên]]></text>
			</staticText>
			<staticText>
				<reportElement x="750" y="15" width="26" height="27" uuid="0f6039a4-d31f-47ee-97fa-1995f5eca85c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Doanh thu dịch vụ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="16" height="42" uuid="ad24f7ab-b4f4-4cc7-8022-b46af62622e2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="776" y="15" width="24" height="27" uuid="10805bc3-9289-44fb-a62c-78e6a6a83b90"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Chú thích]]></text>
			</staticText>
			<staticText>
				<reportElement x="374" y="15" width="50" height="27" uuid="11150f30-70f1-4814-b344-7a03e36da341"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Tiền mặt]]></text>
			</staticText>
			<staticText>
				<reportElement x="119" y="0" width="46" height="42" uuid="496710e5-b1a6-4276-98c4-3f752584ac09"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã bệnh nhân]]></text>
			</staticText>
			<staticText>
				<reportElement x="302" y="0" width="41" height="42" uuid="7940aede-c29c-4ca8-89aa-b10c3f9a5b1e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày giờ thực hiện]]></text>
			</staticText>
			<staticText>
				<reportElement x="254" y="0" width="48" height="42" uuid="29f5933f-14bb-4911-8711-6261a2b87a61"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Khoa/Phòng]]></text>
			</staticText>
			<staticText>
				<reportElement x="524" y="0" width="48" height="42" uuid="0c536282-7ddb-4e70-8ea2-306e433446cb"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Chi trả lại]]></text>
			</staticText>
			<staticText>
				<reportElement x="699" y="15" width="51" height="27" uuid="8a694f57-7b9a-4e10-b942-78883be670b6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Doanh thu viện phí]]></text>
			</staticText>
			<staticText>
				<reportElement x="572" y="0" width="46" height="42" uuid="2dfb9629-b184-4e4d-b365-919034d2b6e7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Hủy]]></text>
			</staticText>
			<staticText>
				<reportElement x="658" y="0" width="41" height="42" uuid="ac6062f0-dd8e-424b-8022-8501242c2b1a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Lý do]]></text>
			</staticText>
			<staticText>
				<reportElement x="16" y="0" width="35" height="42" uuid="74b6080f-6829-46de-801d-d329538f8235"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã phiếu]]></text>
			</staticText>
			<staticText>
				<reportElement x="73" y="0" width="46" height="42" uuid="fac6ab41-7a59-49df-9e29-44f956a6fcff"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã viện phí]]></text>
			</staticText>
			<staticText>
				<reportElement x="343" y="0" width="31" height="42" uuid="16bdc60b-22c6-4cb3-b866-c15d396664d2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Loại phiếu]]></text>
			</staticText>
			<staticText>
				<reportElement x="374" y="0" width="75" height="15" uuid="77dd2be2-b5a5-41d1-85c9-f05dfcc625c7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Thu tạm ứng]]></text>
			</staticText>
			<staticText>
				<reportElement x="449" y="0" width="75" height="15" uuid="6d375c92-2345-47d0-8a44-ddf47e7a7ada"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Thu thêm]]></text>
			</staticText>
			<staticText>
				<reportElement x="424" y="15" width="25" height="27" uuid="78d02a19-2dfa-4ab4-925c-bf78485996bb"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Qua Pos]]></text>
			</staticText>
			<staticText>
				<reportElement x="449" y="15" width="50" height="27" uuid="9c06b5ee-6ca4-4924-b571-b92957813e95"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Tiền mặt]]></text>
			</staticText>
			<staticText>
				<reportElement x="499" y="15" width="25" height="27" uuid="3696878e-2d10-41a9-8c3e-c79df45215a1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Qua Pos]]></text>
			</staticText>
			<staticText>
				<reportElement x="618" y="0" width="40" height="42" uuid="f84c671f-4855-4e43-93c3-05bdcf2a0ed2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Miễn giảm]]></text>
			</staticText>
			<staticText>
				<reportElement x="699" y="0" width="101" height="15" uuid="6706b17b-85a9-4c6b-9464-e327159e8dce"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Thực thu]]></text>
			</staticText>
			<staticText>
				<reportElement x="51" y="0" width="22" height="42" uuid="6bcdb4f8-fd14-479e-8aec-b78c92586dcd"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã số]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="18" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="16" height="18" uuid="79bfbda9-80a1-400a-b628-2871c6fd1268"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{nguoithu_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="16" y="0" width="35" height="18" uuid="a4f0c036-ba14-46ea-9a9e-d911d321c2b4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{manhomphieuthu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="73" y="0" width="46" height="18" uuid="9c805276-6da4-4ec7-bf9d-4bd4222e5912"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{matiepnhan}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="750" y="0" width="26" height="18" uuid="c8af21ca-8ceb-4c83-99fb-da809e517494"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="374" y="0" width="50" height="18" uuid="33054921-0ba8-48c2-9cf8-f6952354a041"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TAMUNG} != null ? ($F{TAMUNG}.doubleValue() >0 ? new DecimalFormat("#,##0").format($F{TAMUNG}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="343" y="0" width="31" height="18" uuid="ca1d8daa-517d-42a2-abe2-a63cc424287e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{loaiphieuthu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="658" y="0" width="41" height="18" uuid="b58fc303-2e66-4285-bdc2-75cfdaf396c8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lydo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="302" y="0" width="41" height="18" uuid="1fd4bd77-306c-4458-9c6b-e75adda03883"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngaythuchien}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="254" y="0" width="48" height="18" uuid="e2f8980f-56b9-4faa-90d8-d7e3eecb0f33"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KHOAPHONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="424" y="0" width="25" height="18" uuid="63ea711c-8367-4768-8b5a-ca021fdf23f4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="449" y="0" width="50" height="18" uuid="8a2266b7-5cdd-4063-bd4d-5fdfa0e2fb9c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THUTHEM} != null ? ($F{THUTHEM}.doubleValue() > 0 ? new DecimalFormat("#,##0").format($F{THUTHEM}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="499" y="0" width="25" height="18" uuid="ed2aac34-442f-4695-8fe3-bc863d805d69"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="524" y="0" width="48" height="18" uuid="b951db62-e8b1-4f38-8e48-a4d181310122"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOANTRA} != null ? ($F{HOANTRA}.doubleValue() >0 ? new DecimalFormat("#,##0").format($F{HOANTRA}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="572" y="0" width="46" height="18" uuid="*************-442e-86b0-ecbad5cee196"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUY} != null ? ($F{HUY}.doubleValue() >0 ? new DecimalFormat("#,##0").format($F{HUY}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="776" y="0" width="24" height="18" uuid="ae5e86b4-c1b6-4860-b56c-54841e5997a8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="119" y="0" width="46" height="18" uuid="dc9e670d-7d1e-4ad9-a7f2-ce6519092adc"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mabenhnhan}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="165" y="0" width="89" height="18" uuid="562e0100-75c6-4058-9d67-54219f9d9c13"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="618" y="0" width="40" height="18" uuid="3503f9a9-c28b-4c58-945d-b3e554f7b31e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MIENGIAM} != null ? ($F{MIENGIAM}.doubleValue() >0 ? new DecimalFormat("#,##0").format($F{MIENGIAM}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="699" y="0" width="51" height="18" uuid="56303487-8b55-47f3-97a0-ccf6ec6196af"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOADON} != null ? ($F{HOADON}.doubleValue() >0 ? new DecimalFormat("#,##0").format($F{HOADON}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="51" y="0" width="22" height="18" uuid="225c81ba-8aa9-4473-a164-29882460133d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{phieuorder}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="14" splitType="Stretch">
			<textField>
				<reportElement positionType="Float" x="0" y="0" width="800" height="14" uuid="70ce1a79-98da-4c49-9402-6f27bcddc475"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="122" splitType="Stretch">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="302" y="0" width="72" height="18" uuid="448f8491-42a3-47d7-aadd-308125afa73f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true" isItalic="true" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tổng tiền:]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="16" height="18" uuid="bd105c9e-696c-4b09-9456-96f80f1b7e35"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="374" y="0" width="50" height="18" uuid="949777c8-613b-4468-8611-2de9dde800a6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0").format($V{tong_tamung}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="424" y="0" width="25" height="18" uuid="b5f34b68-9bc5-4748-9286-9899c8aff687"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="449" y="0" width="50" height="18" uuid="a8479674-b335-4f2d-86ef-c7e66d94447a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0").format($V{tong_thuthem}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="499" y="0" width="25" height="18" uuid="603a34b4-aa3d-4ac2-b156-ddc9c2ab6b3e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="618" y="0" width="40" height="18" uuid="cfb0bd6c-32b2-4723-a155-39f7e9f3e08b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tong_miengiam} != null ? ($V{tong_miengiam}.doubleValue() >0 ? new DecimalFormat("#,##0").format($V{tong_miengiam}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="750" y="0" width="26" height="18" uuid="5900139f-6cd8-4881-8a14-d18d0da70331"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="23" width="165" height="14" uuid="aef4a1d0-a00d-472e-ae48-32bf5af74598"/>
				<box rightPadding="10">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[TỔNG SỐ TIỀN NỘP CHO THU QUỸ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="42" width="254" height="20" uuid="690d735b-bc47-480d-a880-04ca7371bf6d"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Người Tổng Hợp]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="524" y="0" width="48" height="18" uuid="6a890322-8607-491d-8a8e-1566d6cfb0e7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0").format($V{tong_chira}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="572" y="0" width="46" height="18" uuid="3f33959d-9a1b-40c9-8b7f-730f6aedefbf"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tong_huy} != null ? ($V{tong_huy}.doubleValue() >0 ? new DecimalFormat("#,##0").format($V{tong_huy}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") : 0) : 0]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="776" y="0" width="24" height="18" uuid="a1e88482-6139-43dc-81fd-8dad465c9287"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="16" y="0" width="35" height="18" uuid="694a1ddf-0090-4b0e-995b-ad3f76ecf169"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="73" y="0" width="46" height="18" uuid="0fe95970-ad66-4b0f-b609-6cc52486be76"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="119" y="0" width="46" height="18" uuid="9c075629-4a49-4d83-9d62-d73e9f9f755c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="165" y="0" width="89" height="18" uuid="cb4ed422-f29d-453b-9cb0-7bd835d9c923"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="254" y="0" width="48" height="18" uuid="5a5f468e-ef00-48dc-865c-6585afafadcc"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="658" y="0" width="41" height="18" uuid="126c1761-c55c-4aea-880f-5c6759dd7e8b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="699" y="0" width="51" height="18" uuid="837cf1f9-8856-4a4e-a108-c453c61d1b02"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0").format($V{tong_HDDV}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="63" width="254" height="14" uuid="becd580e-bf92-4891-ba55-4ae0333fb6ee"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký và ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="254" y="63" width="318" height="14" uuid="a5a3413e-57c5-4bd0-b507-014c372b68de"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký và ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="254" y="42" width="318" height="20" uuid="fd8ef085-359a-484d-8688-0a607b3f8718"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Người Thu Tiền]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="572" y="63" width="204" height="14" uuid="70d7aefa-576c-46ae-878b-addceeca9157"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký và ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="572" y="42" width="204" height="20" uuid="75e8da1c-7d0e-42f3-a8e9-da93ec3a28c7"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Trưởng/Phó Phòng TCKT]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="51" y="0" width="22" height="18" uuid="de767830-a3ed-4c8d-bf74-56fecd03c78c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="7" isBold="true" isStrikeThrough="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="165" y="23" width="611" height="14" uuid="0785d155-cad0-4b5b-abce-29b487389946"/>
				<box>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tongtienso} == null ? "0" :new DecimalFormat("#,##0").format($F{tongtienso}.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") + " (" + (($F{TONGTIEN}.length()>1?$F{TONGTIEN}.substring(0,1).toUpperCase()+$F{TONGTIEN}.substring(1):$F{TONGTIEN})+" đồng)")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="108" width="254" height="14" uuid="70d45d75-84b1-4d6e-8364-6d23edea297b"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOILAP}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DKDHTNN_VPI_THVPDUYETMIEN" language="groovy" pageWidth="1281" pageHeight="906" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1241" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="c0054ed1-5b89-4592-ae0f-78fb01cba63a">
	<property name="ireport.zoom" value="1.1269722013523693"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false"/>
	<parameter name="parent_name" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["Sở y tế Thái Nguyên"]]></defaultValueExpression>
	</parameter>
	<parameter name="org_name" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["Bệnh viện đa khoa Định Hóa"]]></defaultValueExpression>
	</parameter>
	<parameter name="trangthai" class="java.lang.String" isForPrompting="false"/>
	<parameter name="loaidoituongid" class="java.lang.String" isForPrompting="false"/>
	<parameter name="loaitiepnhanid" class="java.lang.String" isForPrompting="false"/>
	<parameter name="VPARS_khoaid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_phongid_VALUE" class="java.lang.String"/>
	<parameter name="loaithanhtoan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tungay" class="java.util.Date"/>
	<parameter name="denngay" class="java.util.Date"/>
	<parameter name="nguoilapbieu" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[" "]]></defaultValueExpression>
	</parameter>
	<parameter name="ketoantruong" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[" "]]></defaultValueExpression>
	</parameter>
	<parameter name="truongdonvi" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[" "]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call dkdhtnn_vpi_tonghopvienphi(
$P{[UID]},
$P{[HID]},
$P{[SCH]},
$P{[IP]},
$P{tungay},
$P{denngay},
$P{trangthai},
$P{loaithanhtoan},
$P{VPARS_khoaid_VALUE},
$P{VPARS_phongid_VALUE},
$P{ora_cursor}
)}]]>
	</queryString>
	<field name="MAPHIEUTHU" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="LYDOMIENGIAM" class="java.lang.String"/>
	<field name="NGAYTHU" class="java.sql.Timestamp"/>
	<field name="TONGTIEN" class="java.math.BigDecimal"/>
	<field name="DATRA" class="java.math.BigDecimal"/>
	<field name="MIENGIAM" class="java.lang.String"/>
	<field name="TYLE_MIENGIAM" class="java.math.BigDecimal"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="DICHVUKHAMBENHID" class="java.math.BigDecimal"/>
	<field name="DICHVUID" class="java.math.BigDecimal"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<field name="PHIEUTHUID" class="java.math.BigDecimal"/>
	<field name="KHAMBENHID" class="java.math.BigDecimal"/>
	<field name="TIEPNHANID" class="java.math.BigDecimal"/>
	<field name="KHOAID" class="java.math.BigDecimal"/>
	<field name="PHONGID" class="java.math.BigDecimal"/>
	<field name="LOAIDOITUONG" class="java.math.BigDecimal"/>
	<field name="TRANGTHAIDICHVU" class="java.math.BigDecimal"/>
	<field name="TIEN_CHITRA" class="java.math.BigDecimal"/>
	<field name="SOLUONG" class="java.math.BigDecimal"/>
	<field name="TEN_DVT" class="java.lang.String"/>
	<field name="TYLE_DV" class="java.math.BigDecimal"/>
	<field name="THANH_TIEN" class="java.math.BigDecimal"/>
	<field name="TIEN_DANOP" class="java.math.BigDecimal"/>
	<field name="TIEN_BHYT_DANOP" class="java.math.BigDecimal"/>
	<field name="TIEN_BHYT_BNTT" class="java.math.BigDecimal"/>
	<field name="TIEN_DAMIENGIAM" class="java.math.BigDecimal"/>
	<field name="YC_HUY" class="java.math.BigDecimal"/>
	<field name="YC_HOAN" class="java.math.BigDecimal"/>
	<field name="COT_DULIEU" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="TIENTHANHTOAN" class="java.math.BigDecimal"/>
	<field name="LOAITHANHTOAN" class="java.lang.String"/>
	<field name="TIENMIENGIAM" class="java.math.BigDecimal"/>
	<field name="NGUOILAPBIEU" class="java.lang.String"/>
	<field name="KETOANTRUONG" class="java.lang.String"/>
	<field name="TRUONGDONVI" class="java.lang.String"/>
	<variable name="stt" class="java.lang.Integer" incrementType="Group" incrementGroup="NgayThu">
		<variableExpression><![CDATA[($V{NgayThu_COUNT} == 1) ? $V{stt} + 1 : $V{stt}]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<variable name="khambenh" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMBENH" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="thuoc" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DUC_THUOC" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DUC_MAU" )
?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="vattu" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DUC_VTYT" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="xetnghiem" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_XETNGHIEM" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="pttt" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_PTTT" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="tdcn" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_TDCN" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="giuong" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_GIUONG" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="noisoi" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_NOISOI" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="sieuam" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_SIEUAM" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="xquang" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_XQUANG" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="khac" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[!($F{COT_DULIEU}.equalsIgnoreCase( "DUC_THUOC" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DUC_VTYT" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMBENH" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_XETNGHIEM" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_XQUANG" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_SIEUAM" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_NOISOI" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_TDCN" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_DTIM" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_CDHAKHAC" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_GIUONG" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_PTTT" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DUC_MAU" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMSUCKHOE" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMSUCKHOE_THEODOAN" )
||$F{COT_DULIEU}.equalsIgnoreCase( "HOANUNG" )
||$F{COT_DULIEU}.equalsIgnoreCase( "TAMUNG" )
||$F{COT_DULIEU}.equalsIgnoreCase( "RAVIEN" )
)
?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="mau" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DUC_MAU" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="ksk" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMSUCKHOE" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMSUCKHOE_THEODOAN" )
?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="dientim" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_DTIM" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="tongcong" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[(
    !($F{COT_DULIEU}.equalsIgnoreCase( "HOANUNG" )
    ||$F{COT_DULIEU}.equalsIgnoreCase( "TAMUNG" )
    ||$F{COT_DULIEU}.equalsIgnoreCase( "RAVIEN" ))
    ? $F{TIENMIENGIAM} :
    0
)]]></variableExpression>
	</variable>
	<variable name="cdhakhac" class="java.math.BigDecimal" resetType="Group" resetGroup="NgayThu" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_CDHAKHAC" )?$F{TIENMIENGIAM}:0]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="t_khambenh" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMBENH" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_thuoc" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DUC_THUOC" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DUC_MAU" )
?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_vattu" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DUC_VTYT" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_xetnghiem" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_XETNGHIEM" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_sieuam" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_SIEUAM" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_noisoi" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_NOISOI" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_xquang" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_XQUANG" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_tdcn" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_TDCN" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_dientim" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_DTIM" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_cnhakhac" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_CDHAKHAC" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_giuong" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_GIUONG" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_pttt" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_PTTT" )?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_khac" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[!($F{COT_DULIEU}.equalsIgnoreCase( "DUC_THUOC" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DUC_VTYT" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMBENH" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_XETNGHIEM" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_XQUANG" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_SIEUAM" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_NOISOI" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_TDCN" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_DTIM" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_CDHAKHAC" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_GIUONG" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_PTTT" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DUC_MAU" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMSUCKHOE" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMSUCKHOE_THEODOAN" )
||$F{COT_DULIEU}.equalsIgnoreCase( "HOANUNG" )
||$F{COT_DULIEU}.equalsIgnoreCase( "TAMUNG" )
||$F{COT_DULIEU}.equalsIgnoreCase( "RAVIEN" )
)
?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="t_tongcong" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[(
    !($F{COT_DULIEU}.equalsIgnoreCase( "HOANUNG" )
    ||$F{COT_DULIEU}.equalsIgnoreCase( "TAMUNG" )
    ||$F{COT_DULIEU}.equalsIgnoreCase( "RAVIEN" ))
    ? $F{TIENMIENGIAM} :
    0
)]]></variableExpression>
	</variable>
	<variable name="t_ksk" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMSUCKHOE" )
||$F{COT_DULIEU}.equalsIgnoreCase( "DV_KHAMSUCKHOE_THEODOAN" )
?$F{TIENMIENGIAM}:0]]></variableExpression>
	</variable>
	<variable name="stt1" class="java.lang.Integer" incrementType="Group" incrementGroup="NgayThu">
		<variableExpression><![CDATA[$V{stt}-1]]></variableExpression>
	</variable>
	<filterExpression><![CDATA[$F{TIENMIENGIAM}!=0]]></filterExpression>
	<group name="NgayThu">
		<groupExpression><![CDATA[$F{NGAYTHU}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="37" y="0" width="63" height="20" uuid="e37b74f2-d105-4e9c-8629-6248ed1c75fd"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.5" lineStyle="Solid"/>
						<leftPen lineWidth="0.5" lineStyle="Solid"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAYTHU}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="37" height="20" uuid="29f006b1-dbce-418b-ad20-974cf16fd4ad"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.5" lineStyle="Solid"/>
						<leftPen lineWidth="0.5" lineStyle="Solid"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{stt1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="100" y="0" width="80" height="20" uuid="11105f3a-29c9-4dc6-ac2b-bb3f8b0ac49a"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khambenh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="180" y="0" width="80" height="20" uuid="a7fd2097-9414-4645-8e8f-e33c2fec7324"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{thuoc}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="260" y="0" width="80" height="20" uuid="01b1a7f6-338f-48e9-a61f-cebe3bdf2258"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vattu}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="340" y="0" width="80" height="20" uuid="f54f2b19-389f-4621-b053-ad0cff277bec"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{xetnghiem}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="420" y="0" width="80" height="20" uuid="981ca6ed-7c4e-4909-8981-fa1f54fc5da4"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sieuam}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="500" y="0" width="80" height="20" uuid="6e8e68db-725b-413c-b38f-6b43425d2ca3"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{noisoi}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="580" y="0" width="80" height="20" uuid="36e025a4-cb4d-44d2-8327-0a9025bd4972"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{xquang}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="660" y="0" width="80" height="20" uuid="431aa141-b13c-4290-a3f9-105060a1e734"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{tdcn}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="740" y="0" width="80" height="20" uuid="ae26aa7a-c650-4a65-9957-1e6cd9a6bc48"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{dientim}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="900" y="0" width="80" height="20" uuid="45ac9c39-d834-494d-b982-000ce28a9fa3"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{giuong}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="980" y="0" width="80" height="20" uuid="064e76bd-4e41-4ebb-895a-29ac04500805"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{pttt}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="1060" y="0" width="80" height="20" uuid="178a902e-c66c-4068-aea0-d78f231f27ea"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khac}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="1140" y="0" width="101" height="20" uuid="b66a4c04-d9aa-44c0-9f08-d77a5ea7e582"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{tongcong}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="NgayThu" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="820" y="0" width="80" height="20" uuid="abf70d1d-20fa-4781-a797-1d02c6b1c5af"/>
					<box rightPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman"/>
						<paragraph leftIndent="5" rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{cdhakhac}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="106" splitType="Stretch">
			<textField>
				<reportElement x="0" y="20" width="361" height="20" uuid="ce411954-5694-4b0f-9d6d-5c5a7645275c"/>
				<textElement>
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="361" height="20" uuid="5c74faec-8e9c-4df8-a088-eb66ebff2107"/>
				<textElement>
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="40" width="1241" height="20" uuid="563f112f-519a-4396-9b3e-a1458f4ba71e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BẢNG TỔNG HỢP VIỆN PHÍ DUYỆT MIỄN"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="72" width="1241" height="20" uuid="afa93d82-bf4d-4c33-9339-6f919fca77b7"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày "
+ new SimpleDateFormat("dd/MM/YYYY").format($P{tungay})
+ " đến ngày "
+ new SimpleDateFormat("dd/MM/YYYY").format($P{denngay})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="40">
			<staticText>
				<reportElement x="37" y="0" width="63" height="40" uuid="52f6823f-ffbc-4b52-95ff-15189dabbcee"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày thu]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="0" width="80" height="40" uuid="6f8bfb5e-bccc-4661-8fe4-580bc34ee690"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Khám bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement x="180" y="0" width="80" height="40" uuid="0b173051-06fd-4d5f-bbb1-2b71b88e1cb3"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Thuốc]]></text>
			</staticText>
			<staticText>
				<reportElement x="260" y="0" width="80" height="40" uuid="759947dc-8a5e-4412-ba0a-2eba610c5a95"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[VTYT]]></text>
			</staticText>
			<staticText>
				<reportElement x="340" y="0" width="80" height="40" uuid="b96e81b4-c08a-4ec4-89f7-61f94bcba796"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Xét nghiệm]]></text>
			</staticText>
			<staticText>
				<reportElement x="420" y="0" width="80" height="40" uuid="e1586d8e-3278-4451-8bb5-52001e431fe7"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Siêu âm]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="0" width="80" height="40" uuid="8094ba2e-f137-458a-b1ca-5f550854bd1a"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Nội soi]]></text>
			</staticText>
			<staticText>
				<reportElement x="580" y="0" width="80" height="40" uuid="6c5d3763-0a2f-4b58-92d0-b18a28115f71"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[X-Quang]]></text>
			</staticText>
			<staticText>
				<reportElement x="660" y="0" width="80" height="40" uuid="d518669e-fc15-4e85-86ff-65d821359bdb"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[TDCN]]></text>
			</staticText>
			<staticText>
				<reportElement x="740" y="0" width="80" height="40" uuid="d205e359-845c-400a-ae26-73dc111ec75e"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Điện tim]]></text>
			</staticText>
			<staticText>
				<reportElement x="820" y="0" width="80" height="40" uuid="57154e22-3d4f-42a7-b1f1-328ee27ada58"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[CĐHA khác]]></text>
			</staticText>
			<staticText>
				<reportElement x="980" y="0" width="80" height="40" uuid="0f2b4ca6-7a85-48c7-8ab1-ccafef493a9d"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[PTTT]]></text>
			</staticText>
			<staticText>
				<reportElement x="1060" y="0" width="80" height="40" uuid="9ad6a619-7026-4060-a40b-b27cd9e7688f"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Khác]]></text>
			</staticText>
			<staticText>
				<reportElement x="1140" y="0" width="101" height="40" uuid="dcb3069b-3794-474c-b161-f07225c6e7bc"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng cộng]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="37" height="40" uuid="28d3c21d-7ca2-4a08-9062-c8a96472f5fe"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="900" y="0" width="80" height="40" uuid="778dddd0-9d5a-4b49-a800-e15d9746cdfe"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Giường]]></text>
			</staticText>
		</band>
	</pageHeader>
	<summary>
		<band height="145" splitType="Prevent">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="100" height="20" uuid="a787791a-2b96-453f-8adb-252d0278bbcd"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng cộng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="100" y="0" width="80" height="20" uuid="6ce93633-b3db-4792-9b4e-a77e8cbe30fd"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_khambenh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="180" y="0" width="80" height="20" uuid="ba545fe7-f408-4341-a208-c95f631790f7"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_thuoc}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="260" y="0" width="80" height="20" uuid="0e168556-6f1d-4ec4-8148-ab1f55d9a887"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_vattu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="340" y="0" width="80" height="20" uuid="35a68fd0-fe61-41f3-955f-7d7d51410d01"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_xetnghiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="420" y="0" width="80" height="20" uuid="8801c252-6736-4200-baf0-6b6b6508568a"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_sieuam}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="500" y="0" width="80" height="20" uuid="9a12cfe3-8689-4e4e-9431-10fc17a08138"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_noisoi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="580" y="0" width="80" height="20" uuid="c3ee0ecd-d410-47ff-9a5a-616b9f2da59d"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_xquang}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="660" y="0" width="80" height="20" uuid="f77c9500-8b8a-4d36-871b-9104fb8372c7"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_tdcn}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="740" y="0" width="80" height="20" uuid="6a48f7e4-621d-44a7-997e-9e731e682166"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_dientim}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="820" y="0" width="80" height="20" uuid="f89b315d-0e95-48d8-985d-4f0abe1a2fc3"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_cnhakhac}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="900" y="0" width="80" height="20" uuid="85610e04-0efe-43a9-a561-0686301c258c"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_giuong}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1060" y="0" width="80" height="20" uuid="b20af119-9f0b-45d1-a486-c8373447ecee"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_khac}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1140" y="0" width="101" height="20" uuid="1b3e0a8b-d7bf-4d13-841f-06572f5182c2"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_tongcong}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" evaluationTime="Report" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="980" y="0" width="80" height="20" uuid="d9ba17b4-a293-4473-b001-601a97c6b197"/>
				<box rightPadding="3">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{t_pttt}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="820" y="48" width="421" height="20" uuid="dc149936-afa4-4def-ab74-8e0aef079a2d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Thủ trưởng đơn vị]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="48" width="420" height="20" uuid="cff37302-9912-43c2-af92-c3cf10b1c408"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Người lập biểu]]></text>
			</staticText>
			<textField pattern="dd" isBlankWhenNull="true">
				<reportElement x="820" y="28" width="421" height="20" uuid="66a6bcb1-db9d-4493-9e03-5bc55dd838bc"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + new SimpleDateFormat("dd").format(new java.util.Date()) + " Tháng " + new SimpleDateFormat("MM").format(new java.util.Date())  + " Năm " + new SimpleDateFormat("yyyy").format(new java.util.Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="420" y="48" width="400" height="20" uuid="6fe95b59-f208-4d47-9841-3714f003cadc"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Kế toán trưởng]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement x="0" y="125" width="420" height="20" uuid="81657ed0-a5cb-430b-acfe-fa9e7de00b21"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nguoilapbieu}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="420" y="125" width="400" height="20" uuid="a52d23fb-44aa-412a-beb4-0f49902a407c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{ketoantruong}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="820" y="125" width="421" height="20" uuid="f30decb6-551e-4723-8643-51bb0a51331f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{truongdonvi}.toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

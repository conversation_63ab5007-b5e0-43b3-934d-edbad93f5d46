<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DUC032_TK15NGAYSUDUNGTHUOCHOACHATVTYTTIEUHAO_16DBV01_TT23_A4" language="groovy" pageWidth="1129" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1089" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="e1043295-88d2-48c1-8222-61a270e798b8">
	<property name="ireport.zoom" value="1.331000000000003"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="tungay" class="java.sql.Timestamp"/>
	<parameter name="khoaid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="khoid" class="java.lang.String"/>
	<parameter name="nhomtvt" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call rpt_duc032_vn_tnn(null,$P{[HID]},$P{[SCH]},null,
$P{khoid},
$P{khoaid},
$P{nhomtvt},
$P{tungay},$P{ora_cursor})}]]>
	</queryString>
	<field name="TENKHO" class="java.lang.String"/>
	<field name="NGAY1" class="java.math.BigDecimal"/>
	<field name="NGAY2" class="java.math.BigDecimal"/>
	<field name="NGAY3" class="java.math.BigDecimal"/>
	<field name="NGAY4" class="java.math.BigDecimal"/>
	<field name="NGAY5" class="java.math.BigDecimal"/>
	<field name="NGAY6" class="java.math.BigDecimal"/>
	<field name="NGAY7" class="java.math.BigDecimal"/>
	<field name="NGAY8" class="java.math.BigDecimal"/>
	<field name="NGAY9" class="java.math.BigDecimal"/>
	<field name="NGAY10" class="java.math.BigDecimal"/>
	<field name="NGAY11" class="java.math.BigDecimal"/>
	<field name="NGAY12" class="java.math.BigDecimal"/>
	<field name="NGAY13" class="java.math.BigDecimal"/>
	<field name="NGAY14" class="java.math.BigDecimal"/>
	<field name="NGAY15" class="java.math.BigDecimal"/>
	<field name="TONGCONG" class="java.math.BigDecimal"/>
	<field name="TENTHUOC" class="java.lang.String"/>
	<field name="DONVI" class="java.lang.String"/>
	<field name="QUYCACH" class="java.lang.String"/>
	<field name="TENKHOA" class="java.lang.String"/>
	<field name="DONGIA" class="java.math.BigDecimal"/>
	<field name="THANHTIEN" class="java.math.BigDecimal"/>
	<variable name="VTONGCONG" class="java.lang.String" calculation="Sum">
		<variableExpression><![CDATA[$F{TONGCONG}]]></variableExpression>
	</variable>
	<variable name="tongngay1" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY1}.toString() == "0"? null : $F{NGAY1}]]></variableExpression>
	</variable>
	<variable name="tongngay2" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY2}.toString() == "0"? null : $F{NGAY2}]]></variableExpression>
	</variable>
	<variable name="tongngay3" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY3}.toString() == "0"? null : $F{NGAY3}]]></variableExpression>
	</variable>
	<variable name="tongngay4" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY4}.toString() == "0"? null : $F{NGAY4}]]></variableExpression>
	</variable>
	<variable name="tongngay5" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY5}.toString() == "0"? null : $F{NGAY5}]]></variableExpression>
	</variable>
	<variable name="tongngay6" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY6}.toString() == "0"? null : $F{NGAY6}]]></variableExpression>
	</variable>
	<variable name="tongngay7" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY7}.toString() == "0"? null : $F{NGAY7}]]></variableExpression>
	</variable>
	<variable name="tongngay8" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY8}.toString() == "0"? null : $F{NGAY8}]]></variableExpression>
	</variable>
	<variable name="tongngay9" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY9}.toString() == "0"? null : $F{NGAY9}]]></variableExpression>
	</variable>
	<variable name="tongngay10" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY10}.toString() == "0"? null : $F{NGAY10}]]></variableExpression>
	</variable>
	<variable name="tongngay11" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY11}.toString() == "0"? null : $F{NGAY11}]]></variableExpression>
	</variable>
	<variable name="tongngay12" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY12}.toString() == "0"? null : $F{NGAY12}]]></variableExpression>
	</variable>
	<variable name="tongngay13" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY13}.toString() == "0"? null : $F{NGAY13}]]></variableExpression>
	</variable>
	<variable name="tongngay14" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY14}.toString() == "0"? null : $F{NGAY14}]]></variableExpression>
	</variable>
	<variable name="tongngay15" class="java.math.BigDecimal" calculation="Count">
		<variableExpression><![CDATA[$F{NGAY15}.toString() == "0"? null : $F{NGAY15}]]></variableExpression>
	</variable>
	<variable name="tongtien" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN}]]></variableExpression>
	</variable>
	<group name="TENKHO">
		<groupExpression><![CDATA[$F{TENKHO}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="910" height="20" uuid="e5e8b335-9bfc-4432-b9fa-2e0923d0e5ab"/>
					<box leftPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TENKHO}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="138" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="61" width="910" height="40" uuid="83a9d147-2987-4d47-b52a-8a2286792d7d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[THỐNG KÊ 15 NGÀY SỬ DỤNG THUỐC, HOÁ CHẤT,
VẬT TƯ Y TẾ TIÊU HAO]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="756" y="1" width="154" height="20" uuid="d04fc1c1-3b8a-46b6-81e3-19bd26cf19cc"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[MS: 16D/ BV-01]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="756" y="21" width="154" height="20" uuid="d1063a1f-8f98-48af-8dc4-bea48b3f90ab"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Số:.........................................]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="21" width="273" height="20" uuid="50e0eb8a-f069-4eff-97f3-3d99ec0936f7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="1" width="273" height="20" uuid="596294b7-4efd-46d8-b854-a2afbc7e9d53"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="41" width="43" height="20" uuid="af7d876d-cf39-4031-9cd4-f3b21e65c4eb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Khoa: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="101" width="910" height="37" uuid="ff1fa696-f7d2-47d9-84ef-16db3f850bd0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày " + new SimpleDateFormat("dd/MM/yyyy").format($P{tungay})+" đến " + new SimpleDateFormat("dd/MM/yyyy").format($P{tungay}.getTime() + 14* 24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="43" y="41" width="230" height="20" uuid="1cb03900-a70c-4f1c-b1bd-d251b5c28b81"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENKHOA}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="50" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="30" height="50" uuid="84b88267-2696-488b-ac5e-d30a640a0732"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Số TT]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="30" y="0" width="148" height="50" forecolor="#000000" backcolor="#FFFFFF" uuid="29169f29-0af1-4700-a574-d04424558052"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Tên thuốc (nồng độ, hàm lượng)/ hóa chất/ vật tư y tế tiêu hao]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="178" y="0" width="50" height="50" forecolor="#000000" backcolor="#FFFFFF" uuid="5aa099e2-f547-4327-ae2b-986412b87959"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Đơn vị]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="318" y="0" width="375" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="636c40de-697b-4b08-ad44-8b635f2405b3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Ngày]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="693" y="0" width="150" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="fe65284b-c264-4601-98c0-74ef7a93358a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Tổng cộng]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="843" y="0" width="67" height="50" forecolor="#000000" backcolor="#FFFFFF" uuid="8a068af1-5c13-4fc3-90ce-e4a5458a21f2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Ghi chú]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="228" y="0" width="45" height="50" forecolor="#000000" backcolor="#FFFFFF" uuid="2ee7d081-abe5-4b6a-b10f-4d2aca701fc4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Quy cách]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="318" y="20" width="25" height="30" uuid="2ca26147-f918-4114-bd94-c53d32ff7921"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="343" y="20" width="25" height="30" uuid="8a228236-f8c9-4791-ad68-c3c9bb43fdcf"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="368" y="20" width="25" height="30" uuid="ec0fd1cc-c63c-4a1b-8f76-a55d97ff7904"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  2*24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="393" y="20" width="25" height="30" uuid="63861e9a-0f15-4aed-bfc6-98043a223a42"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  3*24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="418" y="20" width="25" height="30" uuid="f2a6ad83-06aa-4a94-bc8f-f27b706ae2ec"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  4*24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="443" y="20" width="25" height="30" uuid="9b8fae22-eb4f-4fcb-acdc-73e50c95b212"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  5*24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="468" y="20" width="25" height="30" uuid="cec00f51-cec1-4243-ac06-faff8c94aa04"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  6*24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="493" y="20" width="25" height="30" uuid="c4b86865-9729-4ca5-b009-5dc0ba2961d9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  7*24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="518" y="20" width="25" height="30" uuid="b1c8e16c-63ff-4049-b754-a1dcc18ddbf4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  8*24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="543" y="20" width="25" height="30" uuid="7a631c02-2aea-457d-b6f3-9a8ae67bd2cb"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  9*24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="568" y="20" width="25" height="30" uuid="a060709a-5cd1-4054-b162-a6848c4a6dc8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  10*24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="593" y="20" width="25" height="30" uuid="a4dad141-3fd0-4056-a442-8f8ffe7af801"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() + 11* 24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="618" y="20" width="25" height="30" uuid="5dcb553c-cd85-4b6e-8500-10aef9322ef3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() + 12* 24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="643" y="20" width="25" height="30" uuid="b0a1250e-e448-4521-879f-bc1c4bb70b74"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  13* 24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="668" y="20" width="25" height="30" uuid="0342c120-e772-42ce-9df3-39104d338843"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("dd/MM").format($P{tungay}.getTime() +  14* 24 * 60 * 60 * 1000)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="273" y="0" width="45" height="50" forecolor="#000000" backcolor="#FFFFFF" uuid="b5ac6f37-ce6f-4b5e-bae0-9e3b4baaa646"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Giá tiền]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="693" y="20" width="60" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="60886d11-140a-4b15-8db5-a439f1b9f096"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="753" y="20" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="c5ade503-c8bb-448a-a57a-1b9710e16f07"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20" uuid="58c1aa4c-ccce-45cf-93a8-fd14e9a4881b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="30" y="0" width="148" height="20" uuid="205db3d3-4dff-4572-a953-2cd0960de1c0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENTHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="178" y="0" width="50" height="20" uuid="df1496a3-39ee-4dc5-9bce-940478c23c93"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DONVI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="228" y="0" width="45" height="20" uuid="131708b2-8597-4a59-9e5e-8f783f0c354a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QUYCACH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="318" y="0" width="25" height="20" uuid="11eeceed-b26d-40fe-96a1-be3a5755405c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="343" y="0" width="25" height="20" uuid="ca9cbecb-1d12-48c8-93df-c942a3e3030a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY2}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="368" y="0" width="25" height="20" uuid="86fe8603-798b-4e48-a4ec-49aff2b14e51"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY3}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="393" y="0" width="25" height="20" uuid="b81125e6-6506-43f0-ae79-386d8f6cb611"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY4}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="418" y="0" width="25" height="20" uuid="fe68ea4a-9578-4009-af43-a2f857ab59fb"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY5}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="443" y="0" width="25" height="20" uuid="0b0fd0dd-e00d-4deb-8968-7f7ccc9d5b45"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY6}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="468" y="0" width="25" height="20" uuid="6da46f52-bb2c-4beb-b67f-ded38e781f53"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY7}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="493" y="0" width="25" height="20" uuid="99a56c5f-5021-41e7-8b0c-594385bae47c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY8}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="518" y="0" width="25" height="20" uuid="a4de8a67-59be-4c99-8d70-db0e3153c6d2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY9}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="543" y="0" width="25" height="20" uuid="19b17092-6a74-4e0e-8dfc-22ca42e2266e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY10}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="568" y="0" width="25" height="20" uuid="1742e60b-fa24-4ac0-affe-e811db86f60f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY11}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="593" y="0" width="25" height="20" uuid="1ce1ef16-cfd3-40a3-8bed-557b39710309"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY12}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="618" y="0" width="25" height="20" uuid="eafbc86e-5d4e-4596-92fd-ea14a3b309f6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY13}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="643" y="0" width="25" height="20" uuid="f7f59cba-4229-44d0-a255-61506cb92384"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY14}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="668" y="0" width="25" height="20" uuid="e320e008-758c-4bf7-ae20-20de05ecc787"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY15}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="693" y="0" width="60" height="20" uuid="28baa965-eac4-458f-ab6e-d0327a49c993"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONGCONG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="843" y="0" width="67" height="20" uuid="6a19b5fc-d9b9-415e-8b15-6de06b41912c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="753" y="0" width="90" height="20" uuid="d50452db-3a69-47bf-bb8c-69d3a96124d0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANHTIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="273" y="0" width="45" height="20" uuid="eaee5cd8-98a5-47a9-b586-a694252ff4a4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DONGIA}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="186" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="50" width="273" height="20" uuid="dcd005fe-f4a8-4f8b-b553-2de90a79b4cf"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI THỐNG KÊ]]></text>
			</staticText>
			<staticText>
				<reportElement x="318" y="50" width="350" height="20" uuid="9575e098-4809-47dd-ab3a-687f433d9ec1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[KẾ TOÁN DƯỢC]]></text>
			</staticText>
			<staticText>
				<reportElement x="668" y="50" width="242" height="20" uuid="c90655e2-109b-439b-868c-fdd831df64a8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TRƯỞNG KHOA]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="154" width="273" height="20" uuid="ecc2d4fc-d12d-4974-b357-2721c1c7afe1"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Họ tên: ..................................................]]></text>
			</staticText>
			<staticText>
				<reportElement x="318" y="154" width="350" height="20" uuid="e7fd3fa8-a95c-4693-9cb9-935c99eadfde"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Họ tên: ..................................................]]></text>
			</staticText>
			<staticText>
				<reportElement x="668" y="154" width="242" height="20" uuid="944a983b-0bf7-429a-83fb-08b7f5c62ad0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Họ tên: ..................................................]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="318" y="0" width="25" height="20" uuid="37f3f463-3669-42d4-9bbc-6e477aeb326c"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay1}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="318" height="20" uuid="189b4d0e-5434-441c-a2fd-91567b04b6a0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Cộng khoản]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="343" y="0" width="25" height="20" uuid="86f783ee-0502-4612-811e-5120da876f4b"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay2}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="368" y="0" width="25" height="20" uuid="71856fb2-963e-42a1-a2a8-22ddbb701838"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay3}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="393" y="0" width="25" height="20" uuid="467ff668-6360-4cef-8bf8-cf752c22fa08"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay4}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="418" y="0" width="25" height="20" uuid="63a4b3e4-7bbb-4e74-a722-3249beeec42d"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay5}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="443" y="0" width="25" height="20" uuid="d5a08086-9c4b-4e11-9149-2e039810ceee"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay6}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="468" y="0" width="25" height="20" uuid="8df5f81f-d57b-4aca-b8c0-af70c46bc144"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay7}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="493" y="0" width="25" height="20" uuid="1ad4910a-b52f-4c24-af45-ae7f73978382"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay8}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="693" y="0" width="150" height="20" uuid="19c9169b-f937-41ac-8d98-e6f4a44e7501"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongtien}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="593" y="0" width="25" height="20" uuid="df2b28ad-7464-4433-a318-18a3be5f40b0"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay12}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="568" y="0" width="25" height="20" uuid="bb3e9736-c9a6-43b4-9c72-c6bed80d584e"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay11}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="518" y="0" width="25" height="20" uuid="dbe309a6-c96f-4f3e-bc74-6d07534595da"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay9}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="543" y="0" width="25" height="20" uuid="ab4789be-c2a6-456f-946f-735cc11f49df"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay10}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="618" y="0" width="25" height="20" uuid="a7e76486-34bd-4f53-be4c-824747e6b230"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay13}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="643" y="0" width="25" height="20" uuid="997fe1e2-1a54-417e-9220-7add719b47b9"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay14}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="668" y="0" width="25" height="20" uuid="9dbedcca-eaaa-4ddb-a976-26693db2ce1d"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongngay15}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="843" y="0" width="67" height="20" uuid="b21edbf8-477e-4298-ac4b-9c8c104e4121"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DUC034_BAOCAOXNT_DKLSN" language="groovy" pageWidth="1478" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1438" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="a59bcef7-d5d5-460d-982c-35bdf7c96575">
	<property name="ireport.zoom" value="1.3636363636363642"/>
	<property name="ireport.x" value="714"/>
	<property name="ireport.y" value="0"/>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_khoid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="loaidongia" class="java.lang.String"/>
	<parameter name="xemchitiet" class="java.lang.String"/>
	<parameter name="tungay" class="java.sql.Timestamp"/>
	<parameter name="denngay" class="java.sql.Timestamp"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="donvitinh" class="java.lang.String"/>
	<parameter name="duongdung" class="java.lang.String"/>
	<parameter name="doituongbn" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call duc034_bcxnt_dktth_kkp_mau2($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{VPARS_khoid_VALUE},$P{loaidongia},1,$P{donvitinh}, $P{duongdung},$P{doituongbn},$P{tungay},$P{denngay},$P{ora_cursor})}]]>
	</queryString>
	<field name="DSKHO" class="java.lang.String"/>
	<field name="MATHUOC" class="java.lang.String"/>
	<field name="TENTHUOC" class="java.lang.String"/>
	<field name="DVT" class="java.lang.String"/>
	<field name="DONGIA" class="java.math.BigDecimal"/>
	<field name="SLDAUKY" class="java.math.BigDecimal"/>
	<field name="TTDAUKY" class="java.math.BigDecimal"/>
	<field name="SLNHAPNCC" class="java.math.BigDecimal"/>
	<field name="TTNHAPNCC" class="java.math.BigDecimal"/>
	<field name="SLBNTRA" class="java.math.BigDecimal"/>
	<field name="TTBNTRA" class="java.math.BigDecimal"/>
	<field name="SLCHUYENKHO" class="java.math.BigDecimal"/>
	<field name="TTCHUYENKHO" class="java.math.BigDecimal"/>
	<field name="SLNHAPKHAC" class="java.math.BigDecimal"/>
	<field name="TTNHAPKHAC" class="java.math.BigDecimal"/>
	<field name="SLXUATBN" class="java.math.BigDecimal"/>
	<field name="TTXUATBN" class="java.math.BigDecimal"/>
	<field name="SLXUATCHUYENKHO" class="java.math.BigDecimal"/>
	<field name="TTXUATCHUYENKHO" class="java.math.BigDecimal"/>
	<field name="SLXUATKHAC" class="java.math.BigDecimal"/>
	<field name="TTXUATKHAC" class="java.math.BigDecimal"/>
	<field name="SLCUOIKY" class="java.math.BigDecimal"/>
	<field name="TTCUOIKY" class="java.math.BigDecimal"/>
	<field name="SLTONGNHAP" class="java.math.BigDecimal"/>
	<field name="TTTONGNHAP" class="java.math.BigDecimal"/>
	<field name="SLTONGXUAT" class="java.math.BigDecimal"/>
	<field name="TTTONGXUAT" class="java.math.BigDecimal"/>
	<field name="LOAITVT" class="java.lang.String"/>
	<variable name="SLDAUKY_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLDAUKY}]]></variableExpression>
	</variable>
	<variable name="TTDAUKY_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTDAUKY}]]></variableExpression>
	</variable>
	<variable name="SLNHAPNCC_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLNHAPNCC}]]></variableExpression>
	</variable>
	<variable name="TTNHAPNCC_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPNCC}]]></variableExpression>
	</variable>
	<variable name="SLBNTRA_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLBNTRA}]]></variableExpression>
	</variable>
	<variable name="TTBNTRA_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTBNTRA}]]></variableExpression>
	</variable>
	<variable name="SLCHUYENKHO_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLCHUYENKHO}]]></variableExpression>
	</variable>
	<variable name="TTCHUYENKHO_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTCHUYENKHO}]]></variableExpression>
	</variable>
	<variable name="SLNHAPKHAC_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLNHAPKHAC}]]></variableExpression>
	</variable>
	<variable name="TTNHAPKHAC_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPKHAC}]]></variableExpression>
	</variable>
	<variable name="SLXUATBN_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLXUATBN}]]></variableExpression>
	</variable>
	<variable name="TTXUATBN_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATBN}]]></variableExpression>
	</variable>
	<variable name="SLXUATCHUYENKHO_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLXUATCHUYENKHO}]]></variableExpression>
	</variable>
	<variable name="TTXUATCHUYENKHO_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATCHUYENKHO}]]></variableExpression>
	</variable>
	<variable name="SLXUATKHAC_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLXUATKHAC}]]></variableExpression>
	</variable>
	<variable name="TTXUATKHAC_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATKHAC}]]></variableExpression>
	</variable>
	<variable name="SLCUOIKY_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLCUOIKY}]]></variableExpression>
	</variable>
	<variable name="TTCUOIKY_SUM" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTCUOIKY}]]></variableExpression>
	</variable>
	<variable name="SLTONGNHAP_SUM1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLTONGNHAP}]]></variableExpression>
	</variable>
	<variable name="TTTONGNHAP_SUM1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGNHAP}]]></variableExpression>
	</variable>
	<variable name="SLTONGXUAT_SUM1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SLTONGXUAT}]]></variableExpression>
	</variable>
	<variable name="TTTONGXUAT_SUM1" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGXUAT}]]></variableExpression>
	</variable>
	<variable name="loai_nhapmoitt" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPNCC}]]></variableExpression>
	</variable>
	<variable name="loai_daukytt" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTDAUKY}]]></variableExpression>
	</variable>
	<variable name="loai_hoantratt" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTBNTRA}]]></variableExpression>
	</variable>
	<variable name="loai_chuyenkhott" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTCHUYENKHO}]]></variableExpression>
	</variable>
	<variable name="loai_nhapkhactt" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPKHAC}]]></variableExpression>
	</variable>
	<variable name="loai_tongnhaptt" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGNHAP}]]></variableExpression>
	</variable>
	<variable name="loai_xuatbntt" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATBN}]]></variableExpression>
	</variable>
	<variable name="loai_xuatchuyenkhott" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATCHUYENKHO}]]></variableExpression>
	</variable>
	<variable name="loai_xuatkhactt" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATKHAC}]]></variableExpression>
	</variable>
	<variable name="loai_tongxuattt" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGXUAT}]]></variableExpression>
	</variable>
	<variable name="loai_cuoikytt" class="java.math.BigDecimal" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Sum">
		<variableExpression><![CDATA[$F{TTCUOIKY}]]></variableExpression>
	</variable>
	<variable name="loai_stt" class="java.lang.Integer" resetType="Group" resetGroup="LoaiTVT_GRP" calculation="Count">
		<variableExpression><![CDATA[$F{MATHUOC}]]></variableExpression>
	</variable>
	<group name="LoaiTVT_GRP">
		<groupExpression><![CDATA[$F{LOAITVT}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="1438" height="20" uuid="666047d1-e8c6-45b9-a426-ef0d86b473bd"/>
					<box leftPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LOAITVT}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<staticText>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="370" height="20" uuid="cfbd8081-fb68-4394-b6c3-21e452e7ca9b"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<text><![CDATA[Cộng]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="480" y="0" width="89" height="20" uuid="849284bf-1eaf-49ce-81e3-9c49ece241e4"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_nhapmoitt} == null ? 0 : $V{loai_nhapmoitt}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="370" y="0" width="110" height="20" uuid="5c07c3dd-6cd7-43be-a984-6af24b57a586"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_daukytt} == null ? 0 : $V{loai_daukytt}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="569" y="0" width="89" height="20" uuid="b93aa977-6b44-4be0-bd0b-d5f3fac582a2"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_hoantratt} == null ? 0 : $V{loai_hoantratt}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="658" y="0" width="89" height="20" uuid="077f807d-1a58-4b0f-9ad3-1f4cb1d04e45"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_chuyenkhott} == null ? 0 : $V{loai_chuyenkhott}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="747" y="0" width="89" height="20" uuid="2e299d46-1bf4-4a5b-bb13-904440a5c072"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_nhapkhactt} == null ? 0 : $V{loai_nhapkhactt}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="836" y="0" width="84" height="20" uuid="265af5ed-2c4e-487f-aaa3-cef918a6a2e6"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_tongnhaptt} == null ? 0 : $V{loai_tongnhaptt}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="920" y="0" width="97" height="20" uuid="840a7916-21b1-44cc-9a56-afebcf8ba3b0"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_xuatbntt} == null ? 0 : $V{loai_xuatbntt}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="1017" y="0" width="87" height="20" uuid="dcdff774-c0c7-42a3-ba6a-cd12c2f27579"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_xuatchuyenkhott} == null ? 0 : $V{loai_xuatchuyenkhott}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="1104" y="0" width="101" height="20" uuid="558f9cdd-54d1-48ae-ab49-d80cb1111573"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_xuatkhactt} == null ? 0 : $V{loai_xuatkhactt}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="1205" y="0" width="86" height="20" uuid="5375af07-60c2-47ad-8dbf-a7681aa6f37c"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_tongxuattt} == null ? 0 : $V{loai_tongxuattt}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="1291" y="0" width="147" height="20" uuid="dcc18899-110f-4964-8775-ef7043dd444b"/>
					<box leftPadding="2" rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{loai_cuoikytt} == null ? 0 : $V{loai_cuoikytt}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="106" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="310" height="20" uuid="afc34914-9c8c-46d5-be43-e949a4f01f7a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="20" width="310" height="20" uuid="b4f2c2aa-6ed1-409f-9bce-f6b51d4c3d11"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="40" width="1438" height="26" uuid="3938c2f9-e645-47b1-a9ec-c55e005af266"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[BÁO CÁO SỬ DỤNG THUỐC TỔNG KHOA DƯỢC]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="66" width="1438" height="20" uuid="9b3206da-e98a-43a2-9da0-c8bb32bad0fe"/>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày: "+new SimpleDateFormat("dd/MM/yyyy hh:mm").format($P{tungay})+" đến ngày: "+new SimpleDateFormat("dd/MM/yyyy hh:mm").format($P{denngay})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="86" width="1438" height="20" uuid="8684996b-1db2-4d74-abc6-0b940b8039e6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DSKHO}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="80" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="30" height="80" uuid="6442123d-128e-4a34-887f-a7e9556b6fea"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="0" width="80" height="80" uuid="a97aba19-60d0-45d0-a5a0-3adb1170eaf4"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Mã BD]]></text>
			</staticText>
			<staticText>
				<reportElement x="250" y="0" width="60" height="80" uuid="3b9853d7-a2e4-44d6-8da1-eaa5d142c45b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[ĐVT]]></text>
			</staticText>
			<staticText>
				<reportElement x="310" y="0" width="60" height="80" uuid="483ccf74-c19d-45b6-a05b-412827e09fc8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Đơn giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="370" y="0" width="110" height="20" uuid="8eeda417-11a6-4548-b42f-6497890e4f83"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[TỒN ĐẦU]]></text>
			</staticText>
			<staticText>
				<reportElement x="370" y="20" width="50" height="60" uuid="90dcdc11-6f03-4cb0-b542-525c41525471"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="420" y="20" width="60" height="60" uuid="d3d48769-6474-4cd9-9e40-6314a13d2a01"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="480" y="0" width="440" height="20" uuid="77c11539-b1f5-42c1-a169-83e4a1f5d122"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[NHẬP TRONG KỲ]]></text>
			</staticText>
			<staticText>
				<reportElement x="747" y="20" width="89" height="40" uuid="754bbf21-324f-4c50-9a93-036cc6fb7931"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Nhập khác]]></text>
			</staticText>
			<staticText>
				<reportElement x="480" y="20" width="89" height="40" uuid="e518c1e0-e4d8-4064-8544-cc9c5f89a799"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Nhập mới]]></text>
			</staticText>
			<staticText>
				<reportElement x="569" y="20" width="89" height="40" uuid="3ff4230b-1f81-4fd8-9f6c-6ea066c151dc"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Hoàn trả]]></text>
			</staticText>
			<staticText>
				<reportElement x="530" y="60" width="39" height="20" uuid="0368a7f0-d2bb-47b9-b527-8d8126124078"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="480" y="60" width="50" height="20" uuid="b1417706-5fb0-4bbb-90aa-78c84351b182"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="619" y="60" width="39" height="20" uuid="d615f321-da7b-4033-b8ca-9fae58c1bde7"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="569" y="60" width="50" height="20" uuid="2c87a61c-81bf-48a0-9efc-674a385f9f40"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="708" y="60" width="39" height="20" uuid="83e964bd-cbb6-47f7-b950-4f16486ed3a1"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="658" y="60" width="50" height="20" uuid="5a4ad64f-fa87-4293-b18c-eb408c575caf"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="797" y="60" width="39" height="20" uuid="7d4a934d-1fb2-4eac-b685-0f643f617683"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="747" y="60" width="50" height="20" uuid="e124baed-4967-4872-840e-96f84995ecc3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="920" y="60" width="48" height="20" uuid="47c64fd8-48a0-484a-b335-3b58edf925a1"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="920" y="0" width="371" height="20" uuid="e1b2acfe-216e-4f95-a75d-5d0e05827aec"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[XUẤT TRONG KỲ]]></text>
			</staticText>
			<staticText>
				<reportElement x="968" y="60" width="49" height="20" uuid="8716142a-275c-4bf3-af7f-62440f26e135"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="920" y="20" width="97" height="40" uuid="de2fb2e9-52d8-4058-9d99-6e5c810ff7d3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Xuất sử dụng]]></text>
			</staticText>
			<staticText>
				<reportElement x="1152" y="60" width="53" height="20" uuid="81bc098d-76b3-4a3e-bb26-a048807898ae"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="1017" y="60" width="47" height="20" uuid="31cfc41a-3874-42a1-bdf8-a7c961a457d2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="1104" y="60" width="48" height="20" uuid="8ab51360-e970-4eef-b32c-9d4dae934252"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="1017" y="20" width="87" height="40" uuid="5148f16f-432b-4ae1-83da-df908866b6d1"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Xuất chuyển kho]]></text>
			</staticText>
			<staticText>
				<reportElement x="1064" y="60" width="40" height="20" uuid="3e364677-bdd6-4d61-b9f5-804407971f50"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="1104" y="20" width="101" height="40" uuid="fe4049d5-3aca-4478-98bd-1fe175ee9665"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Xuất khác]]></text>
			</staticText>
			<staticText>
				<reportElement x="1291" y="0" width="147" height="20" uuid="35b2af9e-077e-4f5b-a250-98d24e623008"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[TỒN CUỐI]]></text>
			</staticText>
			<staticText>
				<reportElement x="1351" y="20" width="87" height="60" uuid="da35f1e6-8093-45b4-bcc9-44ca57ab3357"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="1291" y="20" width="60" height="60" uuid="f6c44436-efbe-438b-a698-eda9d50cfba8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="658" y="20" width="89" height="40" uuid="86c3d00e-3d28-456b-b55c-d39e8c2b1f34"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Chuyển kho]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="0" width="140" height="80" uuid="62ff90a8-f95b-4148-bed5-35ab4c0e55bd"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tên biệt dược]]></text>
			</staticText>
			<staticText>
				<reportElement x="836" y="20" width="84" height="40" uuid="22eace30-a02d-4d1d-842a-690edf87d281"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tổng nhập]]></text>
			</staticText>
			<staticText>
				<reportElement x="836" y="60" width="44" height="20" uuid="e086294f-6dac-46cd-97bc-afe2174fcfcb"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="880" y="60" width="40" height="20" uuid="f6ee3205-3437-4e4d-b388-137e38ead643"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="1205" y="20" width="86" height="40" uuid="2818a8a6-4409-4a6d-b7f2-4e730f2ee2e5"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tổng xuất]]></text>
			</staticText>
			<staticText>
				<reportElement x="1205" y="60" width="43" height="20" uuid="c93b5fa2-7169-4805-82f7-fcfb36ace94a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="1248" y="60" width="43" height="20" uuid="34af7b47-971e-426c-ba3c-e076283fc51d"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="30" y="0" width="80" height="20" uuid="b8e78ce9-e301-4dfa-876b-b53fc186510e"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MATHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="110" y="0" width="140" height="20" uuid="f9212d41-8b9b-4000-b38d-ecde7dc395ad"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENTHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="250" y="0" width="60" height="20" uuid="9fc92166-2a96-4c98-8867-77702f44f8ad"/>
				<box leftPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DVT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="310" y="0" width="60" height="20" uuid="9a6ce5a6-8ae8-4466-a647-4a57db06d070"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{DONGIA} == null ? 0 : $F{DONGIA}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="370" y="0" width="50" height="20" uuid="11e269e2-c686-4ba7-be22-33dacf0fe8fa"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLDAUKY} == null ? 0 : $F{SLDAUKY}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="480" y="0" width="50" height="20" uuid="127f501a-a003-4abb-8381-140b99bafa62"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLNHAPNCC} == null ? 0 : $F{SLNHAPNCC}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="569" y="0" width="50" height="20" uuid="dac6965b-975f-442e-bbb9-080cdb77ead3"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLBNTRA} == null ? 0 : $F{SLBNTRA}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="658" y="0" width="50" height="20" uuid="04b60cf4-106b-4462-8e89-a9a248e8ab88"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLCHUYENKHO} == null ? 0 : $F{SLCHUYENKHO}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="747" y="0" width="50" height="20" uuid="a6e46334-3fc0-4e8a-8671-17daf01b15b5"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLNHAPKHAC} == null ? 0 : $F{SLNHAPKHAC} ).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="920" y="0" width="48" height="20" uuid="d0bf53d0-9e03-4ff7-a2b8-c686ca77c24f"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLXUATBN} == null ? 0 : $F{SLXUATBN}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1017" y="0" width="47" height="20" uuid="ddfc53ca-dafa-48af-ba42-ded1c7287a89"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLXUATCHUYENKHO} == null ? 0 : $F{SLXUATCHUYENKHO}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1104" y="0" width="48" height="20" uuid="a89777d5-75ed-4dfe-b984-2d2651e9506b"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLXUATKHAC} == null ? 0 : $F{SLXUATKHAC}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1291" y="0" width="60" height="20" uuid="c598f1ca-49e2-4866-b4c5-c3a0f0ae0680"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLCUOIKY} == null ? 0 : $F{SLCUOIKY}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="420" y="0" width="60" height="20" uuid="6d2a980e-8363-48f1-a27b-53cdf425aaa4"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTDAUKY} == null ? 0 : $F{TTDAUKY}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="530" y="0" width="39" height="20" uuid="800e9db9-2b08-4822-8869-54b0993b09ac"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTNHAPNCC} == null ? 0 : $F{TTNHAPNCC}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="619" y="0" width="39" height="20" uuid="ff287e60-9403-4ab7-890a-f847a66b0403"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTBNTRA} == null ? 0 : $F{TTBNTRA}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="708" y="0" width="39" height="20" uuid="f84f5f58-5ecf-4e87-9685-d5d5891fca93"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTCHUYENKHO} == null ? 0 : $F{TTCHUYENKHO}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="797" y="0" width="39" height="20" uuid="1385c904-6f9c-4e78-851c-367b9ddc6f5f"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTNHAPKHAC} == null ? 0 : $F{TTNHAPKHAC}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="968" y="0" width="49" height="20" uuid="d2fcd037-f5f9-48a8-9ef7-257d1f4d863d"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTXUATBN} == null ? 0 : $F{TTXUATBN}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1064" y="0" width="40" height="20" uuid="58766844-40f4-4ca9-b5b3-c4123ec2d741"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTXUATCHUYENKHO} == null ? 0 : $F{TTXUATCHUYENKHO}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1152" y="0" width="53" height="20" uuid="6c9f9f2a-825f-425c-b0d4-c3fb3ca3955b"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTXUATKHAC} == null ? 0 : $F{TTXUATKHAC}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1351" y="0" width="87" height="20" uuid="5677e88f-a153-4a50-b841-7fc8c305c88e"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTCUOIKY} == null ? 0 : $F{TTCUOIKY}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20" uuid="e5f04bf7-7092-45bc-9c2b-569fd0f67091"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{loai_stt}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="836" y="0" width="44" height="20" uuid="c21fa59b-03a9-47c5-9d3e-6081273fed90"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLTONGNHAP} == null ? 0 : $F{SLTONGNHAP} ).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="880" y="0" width="40" height="20" uuid="883ab265-6ca7-4c2e-b772-74ec52f792b7"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTTONGNHAP} == null ? 0 : $F{TTTONGNHAP}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1205" y="0" width="43" height="20" uuid="17867c55-2186-4dc6-8500-63cd193915a2"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLTONGXUAT} == null ? 0 : $F{SLTONGXUAT}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1248" y="0" width="43" height="20" uuid="19d8f888-6c01-4381-bb1d-9a92871f30c7"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{TTTONGXUAT} == null ? 0 : $F{TTTONGXUAT}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="194" splitType="Stretch">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="370" height="20" uuid="71ff587d-8640-4cef-8e15-33f6fbb01939"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="370" y="0" width="50" height="20" uuid="3108479d-e35d-4405-ac68-5cf0b429b465"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLDAUKY_SUM} == null ? 0 : $V{SLDAUKY_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="420" y="0" width="60" height="20" uuid="798b190e-b535-41b1-a8aa-c39eaecdb326"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTDAUKY_SUM} == null ? 0 : $V{TTDAUKY_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="480" y="0" width="50" height="20" uuid="d2eb9e5f-ab4e-40f9-b7b8-98d39123fa89"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLNHAPNCC_SUM} == null ? 0 : $V{SLNHAPNCC_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="530" y="0" width="39" height="20" uuid="ee1e18fd-bc11-4403-abb2-87fcb2beeee4"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTNHAPNCC_SUM} == null ? 0 : $V{TTNHAPNCC_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="569" y="0" width="50" height="20" uuid="61679a69-f029-4a81-868f-45dbcfe3a340"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLBNTRA_SUM} == null ? 0 : $V{SLBNTRA_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="619" y="0" width="39" height="20" uuid="56b90208-4528-4c38-89b7-2581f748ab74"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTBNTRA_SUM} == null ? 0 : $V{TTBNTRA_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="708" y="0" width="39" height="20" uuid="9aa7843c-5943-46f7-8dc8-76e2f9794a47"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTCHUYENKHO_SUM} == null ? 0 : $V{TTCHUYENKHO_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="797" y="0" width="39" height="20" uuid="8d3a3f3e-6369-4d1a-a248-cc5d7a4c2539"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTNHAPKHAC_SUM} == null ? 0 : $V{TTNHAPKHAC_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="920" y="0" width="48" height="20" uuid="aa169390-8534-4de2-8aa8-c6a12828be5d"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLXUATBN_SUM} == null ? 0 : $V{SLXUATBN_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="658" y="0" width="50" height="20" uuid="a0903ae6-d27e-4c2f-b954-55d886026ef1"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLCHUYENKHO_SUM} == null ? 0 : $V{SLCHUYENKHO_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="968" y="0" width="49" height="20" uuid="9dc8c413-4856-499c-80c8-52f77934e5ef"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTXUATBN_SUM} == null ? 0 : $V{TTXUATBN_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="747" y="0" width="50" height="20" uuid="37042bce-d5e7-4a4f-8a40-67ca2a1114fd"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLNHAPKHAC_SUM} == null ? 0 : $V{SLNHAPKHAC_SUM} ).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1152" y="0" width="53" height="20" uuid="bf1b1e12-803f-4b6c-8586-60ccc77eedbc"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTXUATKHAC_SUM} == null ? 0 : $V{TTXUATKHAC_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1291" y="0" width="60" height="20" uuid="50d70770-55d9-47e9-baa9-52ae99870ea8"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLCUOIKY_SUM} == null ? 0 : $V{SLCUOIKY_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1351" y="0" width="87" height="20" uuid="29164c70-690c-45cc-9199-60dba42044f0"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTCUOIKY_SUM} == null ? 0 : $V{TTCUOIKY_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1017" y="0" width="47" height="20" uuid="f3bc4ac3-00dd-493d-8b3b-32a8d505b76c"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLXUATCHUYENKHO_SUM} == null ? 0 : $V{SLXUATCHUYENKHO_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1064" y="0" width="40" height="20" uuid="fafbb4bf-c104-4d9e-92ab-b4691dcc25cf"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTXUATCHUYENKHO_SUM} == null ? 0 : $V{TTXUATCHUYENKHO_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1104" y="0" width="48" height="20" uuid="ef5f8e68-a1cb-440b-bdf5-89a6d231872b"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLXUATKHAC_SUM} == null ? 0 : $V{SLXUATKHAC_SUM}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="836" y="0" width="44" height="20" uuid="203f603c-3d42-4e0d-a2a8-a623a1c10542"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLTONGNHAP_SUM1} == null ? 0 : $V{SLTONGNHAP_SUM1} ).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="880" y="0" width="40" height="20" uuid="4024384f-7368-4bd5-aa86-52f55b62c1b5"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTTONGNHAP_SUM1} == null ? 0 : $V{TTTONGNHAP_SUM1}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1205" y="0" width="43" height="20" uuid="bebf359d-077b-4ef1-a921-e1efc935e693"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{SLTONGXUAT_SUM1} == null ? 0 : $V{SLTONGXUAT_SUM1}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="1248" y="0" width="43" height="20" uuid="790e18a9-4636-4b16-a29a-0ff437cf6ad2"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($V{TTTONGXUAT_SUM1} == null ? 0 : $V{TTTONGXUAT_SUM1}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1152" y="57" width="286" height="20" uuid="60e1aa9f-b72e-41bc-8579-************"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Thủ trưởng đơn vị]]></text>
			</staticText>
			<staticText>
				<reportElement x="920" y="57" width="232" height="20" uuid="ec3c37c0-d18a-4816-9b3c-cce1aaf3967a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Kế toán trưởng]]></text>
			</staticText>
			<staticText>
				<reportElement x="708" y="57" width="212" height="20" uuid="89564d03-bd4b-477b-82a5-4fb506e63056"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Phụ trách khoa dược]]></text>
			</staticText>
			<staticText>
				<reportElement x="480" y="57" width="228" height="20" uuid="109b23f0-77e5-45f0-943d-c789a4cc0c91"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Thủ kho]]></text>
			</staticText>
			<staticText>
				<reportElement x="250" y="57" width="230" height="20" uuid="391b1c06-73a0-4929-a981-2550cb805f31"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Thống kê]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="57" width="250" height="20" uuid="5bbd415a-ebad-4c7d-b69c-b2615f94c478"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Kế toán]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="77" width="250" height="20" uuid="0b453c9f-db52-4c6b-99cc-79c5163d8f8b"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[(Ký,họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="250" y="77" width="230" height="20" uuid="46da5f12-7444-4ec9-9015-ff58dc99220e"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[(Ký,họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="480" y="77" width="228" height="20" uuid="728d6b50-d9f1-46c9-adb6-677cb2e7ddae"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[(Ký,họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="708" y="77" width="212" height="20" uuid="9da4f6fd-a6cb-4a92-964d-5d9efa2170fc"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[(Ký,họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="920" y="77" width="232" height="20" uuid="2b874c28-2830-42e1-ac1b-0bf441a6987b"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[(Ký,họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="1152" y="77" width="286" height="20" uuid="c9ab2f08-2531-42f5-ac24-05f72293d1b0"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[(Ký,họ tên)]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="40" width="1438" height="17" uuid="286d2602-0d9b-4916-95aa-7c66ce646b72"/>
				<box rightPadding="6"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Lạng Sơn, ngày "+new SimpleDateFormat("dd").format($P{denngay})+" tháng "+new SimpleDateFormat("MM").format($P{denngay}) +" năm "+new SimpleDateFormat("yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

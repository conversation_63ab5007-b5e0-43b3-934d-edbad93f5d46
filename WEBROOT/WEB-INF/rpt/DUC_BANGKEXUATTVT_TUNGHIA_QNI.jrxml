<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DUC_BANGKEXUATTVT_TUNGHIA_QNI" language="groovy" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="2ce8c529-9cc0-4db7-9f87-86c68a198e57">
	<property name="ireport.zoom" value="1.5026296018031562"/>
	<property name="ireport.x" value="25"/>
	<property name="ireport.y" value="0"/>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tungay" class="java.sql.Timestamp"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="denngay" class="java.sql.Timestamp"/>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_khoid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="VPARS_nhomtvt_VALUE" class="java.lang.String"/>
	<parameter name="trangthaikt" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call duc_bangkexuattvt_tn_qni($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{VPARS_khoid_VALUE},
$P{VPARS_nhomtvt_VALUE},
$P{trangthaikt},
$P{tungay},$P{denngay},$P{ora_cursor})}]]>
	</queryString>
	<field name="NHOMTVT" class="java.lang.String"/>
	<field name="TENKHOA" class="java.lang.String"/>
	<field name="TENKHO" class="java.lang.String"/>
	<field name="MATHUOC" class="java.lang.String"/>
	<field name="TENTHUOC" class="java.lang.String"/>
	<field name="DVT" class="java.lang.String"/>
	<field name="DONGIA" class="java.math.BigDecimal"/>
	<field name="SLTONG" class="java.math.BigDecimal"/>
	<field name="TTTONG" class="java.math.BigDecimal"/>
	<field name="SLBHYT" class="java.math.BigDecimal"/>
	<field name="TTBHYT" class="java.math.BigDecimal"/>
	<field name="SLVP" class="java.math.BigDecimal"/>
	<field name="TTVP" class="java.math.BigDecimal"/>
	<field name="SLMP" class="java.math.BigDecimal"/>
	<field name="TTMP" class="java.math.BigDecimal"/>
	<variable name="loai_tongtien" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONG}]]></variableExpression>
	</variable>
	<variable name="loai_bhyt" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTBHYT}]]></variableExpression>
	</variable>
	<variable name="loai_vp" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTVP}]]></variableExpression>
	</variable>
	<variable name="loai_mp" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTMP}]]></variableExpression>
	</variable>
	<variable name="tongbhyt" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTBHYT}]]></variableExpression>
	</variable>
	<variable name="tongvp" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTVP}]]></variableExpression>
	</variable>
	<variable name="tongmp" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTMP}]]></variableExpression>
	</variable>
	<variable name="tongtien" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONG}]]></variableExpression>
	</variable>
	<group name="NHOMTVT">
		<groupExpression><![CDATA[$F{NHOMTVT}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="802" height="20" uuid="310fdd82-6701-4349-a8db-447bdb9a3066"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NHOMTVT}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="322" y="0" width="120" height="20" uuid="8e402b14-cc37-4966-9f8c-830c5b0da7b6"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{loai_tongtien}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="322" height="20" uuid="01fc33af-49a4-4fd4-94e8-2770476f158a"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Tổng tiền( " +$F{NHOMTVT} +")"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="442" y="0" width="120" height="20" uuid="5dca2c7f-a9bf-44c5-a9a1-2f70e6bb13b2"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{loai_bhyt}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="562" y="0" width="120" height="20" uuid="1da70aa9-8a7c-4a1f-b6a2-744d5f8e367f"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{loai_vp}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="682" y="0" width="120" height="20" uuid="22db0633-064f-45b9-9646-a6d0f1a706f8"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{loai_mp}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="127" splitType="Stretch">
			<textField>
				<reportElement x="0" y="20" width="322" height="20" uuid="3b36af9f-8ffc-41df-b523-b0c3fdf1da1c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="87" width="802" height="20" uuid="f885ba1a-e614-4e4e-a485-9117e36261c7"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{tungay})+" đến ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="60" width="802" height="27" uuid="57ae18c4-3bd8-4eb7-b6df-42ecdd42d7dd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[BẢNG KÊ XUẤT THUỐC - VTYT KHÁM BỆNH NGOẠI TRÚ - NỘI TRÚ]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="0" width="322" height="20" uuid="c3ab0c05-d21c-4ede-a5ce-463e402a3d40"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="107" width="802" height="20" uuid="cba67b2b-1720-458c-a699-62102f610b2e"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Kho: "+$F{TENKHO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="40" width="322" height="20" uuid="7f94b6c8-ca63-4d03-a92f-9d5e314204e8"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Đơn vị: "]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="40" splitType="Stretch">
			<staticText>
				<reportElement x="262" y="0" width="60" height="40" uuid="507f5f1b-57ef-4e2e-a1da-41cb70ca861e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Đơn giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="212" y="0" width="50" height="40" uuid="41a5dfa0-f790-4a59-a6a0-ba5e93db0c04"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[ĐVT]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="30" height="40" uuid="7cae76fb-ae67-47e1-9c26-e22df74abe79"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="0" width="60" height="40" uuid="494a2dd9-de00-43ab-94dc-5f02c253a449"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Mshh]]></text>
			</staticText>
			<staticText>
				<reportElement x="90" y="0" width="122" height="40" uuid="0d5dd5dc-fd9d-493c-b3db-5bd3cac0a8be"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tên thuốc, VTYT, hóa chất]]></text>
			</staticText>
			<staticText>
				<reportElement x="682" y="0" width="120" height="20" uuid="cdcc70a6-a655-47a6-9d4f-3ed055b1537a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Miễn phí]]></text>
			</staticText>
			<staticText>
				<reportElement x="442" y="0" width="120" height="20" uuid="ddd192fa-217d-46cf-a575-e4160a8bbb21"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[BHYT]]></text>
			</staticText>
			<staticText>
				<reportElement x="562" y="0" width="120" height="20" uuid="1a3b32d5-4468-44c8-b827-f316b40f4584"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Viện phí]]></text>
			</staticText>
			<staticText>
				<reportElement x="322" y="0" width="120" height="20" uuid="55cd5819-c8a2-4acf-ae49-114d2c0e865b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tổng cộng]]></text>
			</staticText>
			<staticText>
				<reportElement x="322" y="20" width="50" height="20" uuid="59fee6ae-2842-4c9f-bf3a-9c1c41969b07"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[SL]]></text>
			</staticText>
			<staticText>
				<reportElement x="372" y="20" width="70" height="20" uuid="1106dcd1-60e6-4b34-9cce-813d6b3256a7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="442" y="20" width="50" height="20" uuid="2d9bf9b2-e430-4b0d-bda0-e9c04f61a501"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[SL]]></text>
			</staticText>
			<staticText>
				<reportElement x="492" y="20" width="70" height="20" uuid="96b9b132-fda5-4ca1-988a-c6b7460f53f4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="562" y="20" width="50" height="20" uuid="a1dc20bc-2b6d-4aca-bf65-a9cb215419cf"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[SL]]></text>
			</staticText>
			<staticText>
				<reportElement x="612" y="20" width="70" height="20" uuid="cd822eae-1bce-44ae-8fd0-a048db6e4df7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="732" y="20" width="70" height="20" uuid="3ac9cf3f-9860-475e-a3b9-82457afa3ede"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="682" y="20" width="50" height="20" uuid="eb769aa1-bc34-4ce7-b6a6-b8296e2845e1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[SL]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="732" y="0" width="70" height="20" uuid="75d7b7a5-34f0-44e4-a49a-a3517afbcb29"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TTMP}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{TTMP}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="682" y="0" width="50" height="20" uuid="09f65e0f-bf38-4ce5-8765-492a7b2c7e64"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SLMP}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{SLMP}.toString().contains(".")?"#,##0.###":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="30" y="0" width="60" height="20" uuid="3ebb7fc3-02fe-415a-ae7e-eac295730698"/>
				<box leftPadding="2" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MATHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="90" y="0" width="122" height="20" uuid="27c1a548-2c97-43df-b82c-0787b55b0adb"/>
				<box leftPadding="2" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENTHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="262" y="0" width="60" height="20" uuid="9f3ddcc1-c0a6-4a15-854c-a02fabe0dc74"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DONGIA}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{DONGIA}.toString().contains(".")?"#,##0.###":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20" uuid="2e941781-2ac1-4108-bd05-a1fbe8ea2391"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isPdfEmbedded="true"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="212" y="0" width="50" height="20" uuid="6b8c8f8c-1fe0-4829-9dfa-bbf22b86c9c7"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DVT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="562" y="0" width="50" height="20" uuid="fa5a805c-42da-43bd-a8ea-4c4e97987294"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SLVP}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{SLVP}.toString().contains(".")?"#,##0.###":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="612" y="0" width="70" height="20" uuid="0be1c6ee-e751-4cfe-b07b-deb80fcffa5b"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TTVP}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{TTVP}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="442" y="0" width="50" height="20" uuid="f80ff82d-c96d-4130-8271-f82e0f8627e4"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SLBHYT}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{SLBHYT}.toString().contains(".")?"#,##0.###":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="492" y="0" width="70" height="20" uuid="b597d992-ba2c-45c8-a753-b1ecc38b0544"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TTBHYT}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{TTBHYT}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="322" y="0" width="50" height="20" uuid="cb156721-817a-420a-8adf-777ae5744546"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SLTONG}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{SLTONG}.toString().contains(".")?"#,##0.###":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="372" y="0" width="70" height="20" uuid="04961759-5743-4dc0-9002-411228b83ff3"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TTTONG}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{TTTONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="150" splitType="Stretch">
			<staticText>
				<reportElement x="562" y="84" width="240" height="20" uuid="e1f826b1-3f7d-48cd-9f05-046ab0cb41d2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Người lập bảng]]></text>
			</staticText>
			<textField>
				<reportElement x="562" y="64" width="240" height="20" uuid="fd7b24af-7ece-40c6-bc13-249521c16056"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày "+ new SimpleDateFormat("dd").format(new java.util.Date())+" tháng "+ new SimpleDateFormat("MM").format(new java.util.Date())+" năm "+ new SimpleDateFormat("yyyy").format(new java.util.Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="84" width="212" height="20" uuid="9df002a5-e486-4268-b29e-7c3a746ddd0c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Trưởng khoa]]></text>
			</staticText>
			<staticText>
				<reportElement x="372" y="84" width="190" height="20" uuid="a159e93e-d2ea-445f-a251-4d0404365e8f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Thủ kho]]></text>
			</staticText>
			<staticText>
				<reportElement x="212" y="84" width="160" height="20" uuid="d33a114e-cc9c-4ad7-8bb4-750cefc0f95e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Kế toán]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="322" height="20" uuid="9dd47f19-c14c-4dbb-9c53-9b6bbbbfb59e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[TỔNG TIỀN]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="322" y="0" width="120" height="20" uuid="7325c377-4b72-42fd-922e-a5f6b85a762f"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongtien}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="442" y="0" width="120" height="20" uuid="b204d3d6-817a-4e91-ae70-51a695872ca0"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongbhyt}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="562" y="0" width="120" height="20" uuid="59f9bc2b-d6bb-4bc7-b66a-ec1ad4642341"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongvp}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="682" y="0" width="120" height="20" uuid="7fa9fcbb-8ca1-48e4-b333-5b97504b14fc"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{tongmp}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

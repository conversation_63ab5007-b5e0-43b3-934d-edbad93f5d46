<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DUC_BAOCAONHAPNNCXUATSUDUNGTOANVIEN_DLKHA" language="groovy" pageWidth="1190" pageHeight="842" orientation="Landscape" columnWidth="1150" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="fc7d9765-75e4-43d9-a6b6-e0db0da3e0aa">
	<property name="ireport.zoom" value="0.8264462809917382"/>
	<property name="ireport.x" value="184"/>
	<property name="ireport.y" value="0"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="tungay" class="java.sql.Timestamp"/>
	<parameter name="denngay" class="java.sql.Timestamp"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call duc_bc_nhapncc_xuatsd_dlkha($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{tungay},$P{denngay},$P{ora_cursor})}]]>
	</queryString>
	<field name="LOAITVT" class="java.lang.String"/>
	<field name="NHOMTHUOC" class="java.lang.String"/>
	<field name="MATHUOC" class="java.lang.String"/>
	<field name="TENTHUOC" class="java.lang.String"/>
	<field name="HOATCHAT" class="java.lang.String"/>
	<field name="HAMLUONG" class="java.lang.String"/>
	<field name="DVT" class="java.lang.String"/>
	<field name="DONGIA" class="java.math.BigDecimal"/>
	<field name="NUOCSX" class="java.lang.String"/>
	<field name="SLNHAPNCC" class="java.math.BigDecimal"/>
	<field name="TTNHAPTN" class="java.math.BigDecimal"/>
	<field name="TTNHAPNN" class="java.math.BigDecimal"/>
	<field name="TTTONGNHAP" class="java.math.BigDecimal"/>
	<field name="SLXUAT" class="java.math.BigDecimal"/>
	<field name="TTXUATTN" class="java.math.BigDecimal"/>
	<field name="TTXUATNN" class="java.math.BigDecimal"/>
	<field name="TTTONGXUAT" class="java.math.BigDecimal"/>
	<variable name="nhomnhaptn" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTHUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPTN}]]></variableExpression>
	</variable>
	<variable name="nhomnhapnn" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTHUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPNN}]]></variableExpression>
	</variable>
	<variable name="nhomtongnhap" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTHUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGNHAP}]]></variableExpression>
	</variable>
	<variable name="nhomxuattn" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTHUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATTN}]]></variableExpression>
	</variable>
	<variable name="nhomxuatnn" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTHUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATNN}]]></variableExpression>
	</variable>
	<variable name="nhomtongxuat" class="java.math.BigDecimal" resetType="Group" resetGroup="NHOMTHUOC" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGXUAT}]]></variableExpression>
	</variable>
	<variable name="loainhaptn" class="java.math.BigDecimal" resetType="Group" resetGroup="LOAITVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPTN}]]></variableExpression>
	</variable>
	<variable name="loainhapnn" class="java.math.BigDecimal" resetType="Group" resetGroup="LOAITVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPNN}]]></variableExpression>
	</variable>
	<variable name="loaitongnhap" class="java.math.BigDecimal" resetType="Group" resetGroup="LOAITVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGNHAP}]]></variableExpression>
	</variable>
	<variable name="loaixuattn" class="java.math.BigDecimal" resetType="Group" resetGroup="LOAITVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATTN}]]></variableExpression>
	</variable>
	<variable name="loaixuatnn" class="java.math.BigDecimal" resetType="Group" resetGroup="LOAITVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATNN}]]></variableExpression>
	</variable>
	<variable name="loaitongxuat" class="java.math.BigDecimal" resetType="Group" resetGroup="LOAITVT" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGXUAT}]]></variableExpression>
	</variable>
	<variable name="tongnhaptn" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPTN}]]></variableExpression>
	</variable>
	<variable name="tongnhapnn" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTNHAPNN}]]></variableExpression>
	</variable>
	<variable name="tongnhapall" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGNHAP}]]></variableExpression>
	</variable>
	<variable name="tongxuattn" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATTN}]]></variableExpression>
	</variable>
	<variable name="tongxuatnn" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTXUATNN}]]></variableExpression>
	</variable>
	<variable name="tongxuatall" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{TTTONGXUAT}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<group name="LOAITVT">
		<groupExpression><![CDATA[$F{LOAITVT}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="1050" height="20" uuid="973e81b8-acf5-48f1-8db3-8f4ceeecdf65"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LOAITVT}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<staticText>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="530" height="20" uuid="*************-4255-829d-124d49547d03"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<text><![CDATA[Tổng cộng:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="580" y="0" width="70" height="20" uuid="5690dcc7-ad09-4fd5-bcde-e110de15ab54"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{loainhaptn} == null ? 0 : $V{loainhaptn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="650" y="0" width="70" height="20" uuid="91245b8a-2651-41ad-8db7-82ee6bacd179"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{loainhapnn} == null ? 0 : $V{loainhapnn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="720" y="0" width="70" height="20" uuid="42f10dca-e8a5-4337-b8d8-233a58f24744"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{loaitongnhap} == null ? 0 : $V{loaitongnhap}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="840" y="0" width="70" height="20" uuid="abc2f279-9dc2-4bdb-8f3c-e7f92239f9ea"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{loaixuattn} == null ? 0 : $V{loaixuattn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="910" y="0" width="70" height="20" uuid="5ca15144-c87d-4387-87b2-02a84f299086"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{loaixuatnn} == null ? 0 : $V{loaixuatnn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="980" y="0" width="70" height="20" uuid="0f32467c-eda9-45f1-b021-a821657aab35"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{loaitongxuat} == null ? 0 : $V{loaitongxuat}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement stretchType="RelativeToBandHeight" x="530" y="0" width="50" height="20" uuid="764c1677-9423-446a-85cf-26f65d82f0d6"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement stretchType="RelativeToBandHeight" x="790" y="0" width="50" height="20" uuid="97f9c8bc-09c1-4a7e-bba6-2bf61c69a767"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<group name="NHOMTHUOC">
		<groupExpression><![CDATA[$F{NHOMTHUOC}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="1050" height="20" forecolor="#000099" uuid="04be4677-3285-4018-9fda-0c2d5c092a70"/>
					<box leftPadding="5">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NHOMTHUOC}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20">
				<staticText>
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="530" height="20" forecolor="#000099" uuid="8b783e97-a183-42ef-8fa2-2fdd8fe3e6ff"/>
					<box leftPadding="5">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<text><![CDATA[Cộng:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="580" y="0" width="70" height="20" forecolor="#000099" uuid="3b8472dc-a135-4013-8be3-0f5f3fa92582"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{nhomnhaptn} == null ? 0 : $V{nhomnhaptn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="650" y="0" width="70" height="20" forecolor="#000099" uuid="cb064ab4-65a7-4761-98dc-00da97636e28"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{nhomnhapnn} == null ? 0 : $V{nhomnhapnn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="720" y="0" width="70" height="20" forecolor="#000099" uuid="f76abd54-eaab-488a-8cde-a6c5a2e4630d"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{nhomtongnhap} == null ? 0 : $V{nhomtongnhap}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="840" y="0" width="70" height="20" forecolor="#000099" uuid="72de80c4-c94e-45c8-b8af-12e9786b2b29"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{nhomxuattn} == null ? 0 : $V{nhomxuattn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="910" y="0" width="70" height="20" forecolor="#000099" uuid="b8ae5e76-573c-44ee-a862-adf724014815"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{nhomxuatnn} == null ? 0 : $V{nhomxuatnn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="980" y="0" width="70" height="20" forecolor="#000099" uuid="f936a92b-5182-42df-8a66-81025c048ed9"/>
					<box rightPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{nhomtongxuat} == null ? 0 : $V{nhomtongxuat}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement stretchType="RelativeToBandHeight" x="530" y="0" width="50" height="20" uuid="1f38be12-23cb-42d2-af46-5295bc2c2d50"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement stretchType="RelativeToBandHeight" x="790" y="0" width="50" height="20" uuid="06fb620e-b212-4d05-a503-6c30e6e57c1e"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="87" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="40" width="1051" height="27" uuid="55b6f811-03d4-42e6-b153-01e6446ae20d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[BÁO CÁO NHẬP TỪ NHÀ CUNG CẤP, XUẤT SỬ DỤNG TOÀN BỆNH VIỆN]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="67" width="1051" height="20" uuid="63bc707f-7a72-4a4d-88ba-c194872bb9a8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{tungay})+" đến ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="1050" height="20" uuid="f81d4c5f-c7da-426c-903f-7c82add2eae5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="20" width="1050" height="20" uuid="2b648c2c-6b27-42a4-b166-9268d5c814bc"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="100" splitType="Stretch">
			<staticText>
				<reportElement x="90" y="0" width="120" height="100" uuid="bf4589f3-bde5-4bb6-b3ce-a3dc29b4fbe1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tên mặt hàng]]></text>
			</staticText>
			<staticText>
				<reportElement x="580" y="20" width="70" height="80" uuid="487f61fd-9036-4242-aafa-70f4856bbc81"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền thuốc sản xuất trong nước]]></text>
			</staticText>
			<staticText>
				<reportElement x="410" y="0" width="60" height="100" uuid="c63c6420-8c65-4199-a6dd-78a43bf383fb"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Đơn giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="210" y="0" width="80" height="100" uuid="9494c0e9-9e32-4290-ada1-cd0eeed934b4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Hoạt chất]]></text>
			</staticText>
			<staticText>
				<reportElement x="530" y="20" width="50" height="80" uuid="6cb3a765-df01-4c40-b260-fe535b9d44d1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="350" y="0" width="60" height="100" uuid="cbed2957-9166-4fe9-8b20-f0359c3a21b2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Đơn vị tính]]></text>
			</staticText>
			<staticText>
				<reportElement x="530" y="0" width="260" height="20" uuid="980bc28b-d771-4bf5-832e-63a55f787422"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Nhập từ nhà cung cấp]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="0" width="60" height="100" uuid="102902d6-3d3c-4e6a-bbb7-efe0f3d55483"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Mã mặt hàng]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="0" width="60" height="100" uuid="cb46a271-4c18-4c73-bf30-242f8429524d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Hàm lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="30" height="100" uuid="c11c4b44-a80c-44b6-a1be-955dae0b8db4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="470" y="0" width="60" height="100" uuid="94e6fbe8-4674-458d-ac2b-ffa358d79ed9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Nước sản xuất]]></text>
			</staticText>
			<staticText>
				<reportElement x="790" y="0" width="260" height="20" uuid="d498a401-d1fe-4e45-9056-4f095a5741d2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Xuất trong kỳ]]></text>
			</staticText>
			<staticText>
				<reportElement x="650" y="20" width="70" height="80" uuid="d8c38573-aec4-4cf6-9eeb-14d7ac219db2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền thuốc sản xuất nước ngoài]]></text>
			</staticText>
			<staticText>
				<reportElement x="720" y="20" width="70" height="80" uuid="86656804-0c41-408f-982b-dadec6c547bd"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tổng thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="840" y="20" width="70" height="80" uuid="927c66de-1e6a-4b27-9433-f44535f08316"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền thuốc sản xuất trong nước]]></text>
			</staticText>
			<staticText>
				<reportElement x="790" y="20" width="50" height="80" uuid="fa20a324-570b-4550-8b70-dcb6f6affc79"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="980" y="20" width="70" height="80" uuid="fd588aa9-fda1-4e0d-8be9-61314bb8c5cd"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tổng thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="910" y="20" width="70" height="80" uuid="4c3ef0bb-36e8-446a-92e0-617378ab0c19"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Thành tiền thuốc sản xuất nước ngoài]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20"  uuid="b36fd284-87e2-41a5-b8a4-4bde820afdb9"/>
				<box>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false" isPdfEmbedded="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="530" y="0" width="50" height="20" uuid="586d36ee-c0ba-4bd3-a02a-b1d27a835244"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLNHAPNCC} == null ? 0 : $F{SLNHAPNCC}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="30" y="0" width="60" height="20" uuid="cac0f644-6dfa-468c-a9da-c01a4da2ad8a"/>
				<box leftPadding="2" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MATHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="580" y="0" width="70" height="20" uuid="21a4854f-2e73-4792-a974-02f01f28a2e7"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{TTNHAPTN} == null ? 0 : $F{TTNHAPTN}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="90" y="0" width="120" height="20" uuid="b7cf18ed-8e04-4bf5-8ced-d2da9c75a35b"/>
				<box leftPadding="2" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENTHUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="210" y="0" width="80" height="20" uuid="17de179d-3d5c-4f37-916e-a356eef1622a"/>
				<box leftPadding="2" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOATCHAT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="290" y="0" width="60" height="20" uuid="2ec07cc3-a08f-43fc-bd00-7098ee788c04"/>
				<box leftPadding="2" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HAMLUONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="350" y="0" width="60" height="20" uuid="c76199f2-394c-4793-bdcc-4ad0a46fc3b0"/>
				<box leftPadding="2" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DVT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="470" y="0" width="60" height="20" uuid="8e359a44-0534-40cc-83e2-c34e9e4678dc"/>
				<box leftPadding="2" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NUOCSX}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="650" y="0" width="70" height="20" uuid="f2c8b5f5-4ba4-438b-ab62-daf9282608f5"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{TTNHAPNN} == null ? 0 : $F{TTNHAPNN}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="720" y="0" width="70" height="20" uuid="51d73f11-11ed-4dae-ac6f-735b8b9ace11"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{TTTONGNHAP} == null ? 0 : $F{TTTONGNHAP}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="410" y="0" width="60" height="20" uuid="140583be-e22a-41f5-b66f-c3ffbfd2f237"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{DONGIA} == null ? 0 : $F{DONGIA}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="790" y="0" width="50" height="20" uuid="4f2fb78a-a247-44f5-9dca-32c82e229d16"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.###").format($F{SLXUAT} == null ? 0 : $F{SLXUAT}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="840" y="0" width="70" height="20" uuid="d63c6b5d-417e-461f-bea3-a2e05b89bc33"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{TTXUATTN} == null ? 0 : $F{TTXUATTN}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="910" y="0" width="70" height="20" uuid="3ae9f6b2-d7b1-4d52-b33b-0cb595c16568"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{TTXUATNN} == null ? 0 : $F{TTXUATNN}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="980" y="0" width="70" height="20" uuid="d4a3975e-9c3e-4feb-929d-6a8b3d99788e"/>
				<box leftPadding="1" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="1" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($F{TTTONGXUAT} == null ? 0 : $F{TTTONGXUAT}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="96" splitType="Stretch">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="530" height="20" uuid="6bca37dd-bd67-4e5d-b99b-23e5a0be0f8a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng giá trị:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="580" y="0" width="70" height="20" uuid="55cdf7d8-29e2-43b0-aed1-e204c43f54b1"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{tongnhaptn} == null ? 0 : $V{tongnhaptn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="650" y="0" width="70" height="20" uuid="aeab71a2-fc37-46b5-9781-1fc64d104564"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{tongnhapnn} == null ? 0 : $V{tongnhapnn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="720" y="0" width="70" height="20" uuid="43a00d7f-19d6-428b-b142-0d9c0debcf4c"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{tongnhapall} == null ? 0 : $V{tongnhapall}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="840" y="0" width="70" height="20" uuid="8e7ed243-1710-4c1c-af19-ec80e7ff27eb"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{tongxuattn} == null ? 0 : $V{tongxuattn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="910" y="0" width="70" height="20" uuid="82d92ab1-cf77-4166-8f24-f083eb11d284"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{tongxuatnn} == null ? 0 : $V{tongxuatnn}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="980" y="0" width="70" height="20" uuid="02f0a3f7-85af-43db-95e7-d102e9689ade"/>
				<box rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.##").format($V{tongxuatall} == null ? 0 : $V{tongxuatall}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="530" y="0" width="50" height="20" uuid="1f51e339-e9b1-4d98-a4c0-17c34e87a253"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="790" y="0" width="50" height="20" uuid="aecb8445-691a-430c-ad59-cf52712df883"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DUC_BC_XUATKHACTVT_CGG_HDG" language="groovy" pageWidth="1684" pageHeight="1190" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1644" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9d2cf14d-ab76-47f4-adaa-938839abf402">
	<property name="ireport.zoom" value="1.0000000000000004"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="Crosstab Data Text" hAlign="Center"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_khoid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="denngay" class="java.sql.Timestamp"/>
	<parameter name="tungay" class="java.sql.Timestamp"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="lydoxuatkho" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call duc_bcxuatkhac_cgg_hdg($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{lydoxuatkho},
$P{VPARS_khoid_VALUE},
$P{tungay},$P{denngay},$P{ora_cursor})}]]>
	</queryString>
	<field name="LYDOXUATKHO" class="java.lang.String"/>
	<field name="STT" class="java.lang.Integer"/>
	<field name="MATHUOC" class="java.lang.String"/>
	<field name="TENTHUOC" class="java.lang.String"/>
	<field name="HAMLUONG" class="java.lang.String"/>
	<field name="DVT" class="java.lang.String"/>
	<field name="DONGIA" class="java.math.BigDecimal"/>
	<field name="TENKHO" class="java.lang.String"/>
	<field name="SOLUONG" class="java.math.BigDecimal"/>
	<field name="THANHTIEN" class="java.math.BigDecimal"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="107" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="374" height="20" uuid="0fa3e5fb-83f9-4f99-b215-eea245eecd33"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="20" width="374" height="20" uuid="076b700d-e396-4dfb-9380-1a92866618f4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["PHÒNG TÀI CHÍNH KẾ TOÁN"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="40" width="2340" height="27" uuid="3422e5a5-a198-4c87-ad8e-8533b0583bc4"/>
				<box leftPadding="370"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BÁO CÁO " + $F{LYDOXUATKHO}.toUpperCase() + " THUỐC VẬT TƯ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="67" width="2340" height="20" uuid="b0695a27-d026-4961-88d3-dbd6725b5ad2"/>
				<box leftPadding="400"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{tungay})+" đến ngày: "+new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="87" width="2340" height="20" uuid="61647a2c-399b-441a-af15-ec72b45daae2"/>
				<box leftPadding="370"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Lý do xuất : "+($F{LYDOXUATKHO}==null?"":$F{LYDOXUATKHO})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<lastPageFooter>
		<band height="50">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="30" width="310" height="20" uuid="21214b70-ccb0-440a-ab07-9adbb6c8e06d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[THỦ TRƯỞNG ĐƠN VỊ]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="310" y="30" width="290" height="20" uuid="b8877ccf-660f-4116-b7a8-fab89b3ce8ce"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[TRƯỞNG KHOA DƯỢC]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="880" y="10" width="290" height="20" uuid="b53e103d-897e-4784-9507-9dc3916c566a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<text><![CDATA[Ngày ....... tháng ....... năm ........]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="880" y="30" width="290" height="20" uuid="01af1fd7-e37d-4fc0-a6d2-d274faacd0cb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI LẬP]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="600" y="30" width="280" height="20" uuid="8f853e30-e21b-4bba-a0f9-cd022f201196"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[THỦ KHO]]></text>
			</staticText>
		</band>
	</lastPageFooter>
	<summary>
		<band height="151" splitType="Stretch">
			<crosstab>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="41" width="2340" height="110" uuid="7099ac0c-2112-4c12-a098-873f8880c8d0"/>
				<crosstabHeaderCell>
					<cellContents>
						<staticText>
							<reportElement style="Crosstab Data Text" x="90" y="0" width="100" height="60" uuid="714a48d0-fc6f-46de-80e5-e6412157a45a"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[Tên thuốc, vật tư]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" x="190" y="0" width="60" height="60" uuid="ba0cc103-2cbd-4269-9ff3-7bbfb1ecbe78"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[Hàm lượng]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" x="310" y="0" width="60" height="60" uuid="7ece6c53-d75c-406e-91e6-d8da84272c41"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[Đơn giá]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" x="250" y="0" width="60" height="60" uuid="d74c42ca-a532-4784-bba4-0707dd5e3b29"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[Đơn vị tính]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" x="30" y="0" width="60" height="60" uuid="a598e671-0116-46c3-b0ad-f0df9557c046"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[Mã thuốc, vật tư]]></text>
						</staticText>
						<staticText>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="30" height="60" uuid="4416637a-a1b7-40b3-b09a-e7d69d072559"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<text><![CDATA[STT]]></text>
						</staticText>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="STT" width="30" totalPosition="End">
					<bucket class="java.lang.Integer">
						<bucketExpression><![CDATA[$F{STT}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="20" uuid="d4306add-083a-4adc-b2ce-208b920d5dc3"/>
								<box leftPadding="2" rightPadding="1">
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{STT}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="370" height="25" uuid="ea96ea83-7289-4732-8af5-7495813dd9fb"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" isBold="true"/>
								</textElement>
								<text><![CDATA[Tổng thành tiền]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="MATHUOC" width="60">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{MATHUOC}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#F0F8FF" mode="Transparent">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="60" height="20" uuid="d0d72642-75a2-46f8-b2c4-447c66da4420"/>
								<box leftPadding="2" rightPadding="1">
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{MATHUOC}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents backcolor="#005FB3" mode="Transparent">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<staticText>
								<reportElement mode="Opaque" x="0" y="0" width="140" height="25" forecolor="#000000" uuid="0b3e32b5-665e-4caf-8075-7a9cbfabdc47"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<text><![CDATA[Tổng]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="TENTHUOC" width="100">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{TENTHUOC}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#F0F8FF" mode="Transparent">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="100" height="20" uuid="1b486dc7-30b6-4505-9efb-26b1962213da"/>
								<box leftPadding="2" rightPadding="1">
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{TENTHUOC}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents backcolor="#BFE1FF" mode="Transparent">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<staticText>
								<reportElement x="0" y="0" width="70" height="25" uuid="773427db-eb44-43fa-9f10-a81cdae3d128"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<text><![CDATA[Total TENTHUOC]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="HAMLUONG" width="60">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{HAMLUONG}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="60" height="20" uuid="fe2de802-e0d1-47e3-a88c-a716ec4e1b4f"/>
								<box leftPadding="2" rightPadding="1">
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{HAMLUONG}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="DVT" width="60">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{DVT}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="60" height="20" uuid="4f1e9b4b-a9e0-45e2-a838-3174010aecd9"/>
								<box leftPadding="2" rightPadding="1">
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{DVT}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="DONGIA" width="60">
					<bucket class="java.math.BigDecimal">
						<bucketExpression><![CDATA[$F{DONGIA}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents>
							<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="60" height="20" uuid="6866ed42-91a3-4faa-a7c7-69c71851405c"/>
								<box rightPadding="2">
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle">
									<font fontName="Times New Roman"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{DONGIA}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="TENKHO" height="60" totalPosition="End">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{TENKHO}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents backcolor="#F0F8FF" mode="Transparent">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="60" height="60" uuid="62a7bc2c-8599-4d1e-a445-94c182fa5e11"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{TENKHO}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents backcolor="#BFE1FF" mode="Transparent">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<staticText>
								<reportElement x="0" y="0" width="60" height="60" uuid="f2725ea3-8480-4ef4-982a-8131ddff10c0"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" isBold="true"/>
								</textElement>
								<text><![CDATA[Tổng số lượng]]></text>
							</staticText>
							<staticText>
								<reportElement x="60" y="0" width="111" height="60" uuid="*************-4a21-bf22-369ac6a94659"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" isBold="true"/>
								</textElement>
								<text><![CDATA[Tổng tiền]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="SOLUONGMeasure" class="java.math.BigDecimal">
					<measureExpression><![CDATA[$F{SOLUONG}]]></measureExpression>
				</measure>
				<measure name="TONGTIENMeasure" class="java.math.BigDecimal" calculation="Sum">
					<measureExpression><![CDATA[$F{THANHTIEN}]]></measureExpression>
				</measure>
				<measure name="TONGSLMeasure" class="java.math.BigDecimal" calculation="Sum">
					<measureExpression><![CDATA[$F{SOLUONG}]]></measureExpression>
				</measure>
				<crosstabCell width="60" height="20">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="60" height="20" uuid="209c5b09-c1ee-4d2c-a2f2-13e36b15bdbd"/>
							<box rightPadding="2">
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{SOLUONGMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell height="25" rowTotalGroup="MATHUOC">
					<cellContents backcolor="#005FB3" mode="Transparent">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" forecolor="#000000" uuid="a0694b09-e71a-4a03-ab6e-d745f074d1c3"/>
							<textFieldExpression><![CDATA[$V{SOLUONGMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="171" height="20" columnTotalGroup="TENKHO">
					<cellContents backcolor="#BFE1FF" mode="Transparent">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="60" y="0" width="111" height="20" uuid="b674cd21-3ccd-4a69-83cb-77df90160567"/>
							<box rightPadding="2">
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="10" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{TONGTIENMeasure}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="#,##0.###" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="60" height="20" uuid="b443dc51-21f8-4c65-912f-16ef855ef991"/>
							<box rightPadding="2">
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="10" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{TONGSLMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="65" rowTotalGroup="MATHUOC" columnTotalGroup="TENKHO">
					<cellContents backcolor="#005FB3" mode="Transparent">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="65" height="25" forecolor="#000000" uuid="b11d2f9b-4a7d-4e2d-8832-fc2432094c55"/>
							<textFieldExpression><![CDATA[$V{SOLUONGMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell height="25" rowTotalGroup="TENTHUOC">
					<cellContents backcolor="#BFE1FF" mode="Transparent">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="31" y="0" width="19" height="25" uuid="dba9be29-5222-455c-9a52-d6d157538c22"/>
							<textFieldExpression><![CDATA[$V{SOLUONGMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="65" rowTotalGroup="TENTHUOC" columnTotalGroup="TENKHO">
					<cellContents backcolor="#BFE1FF" mode="Transparent">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement style="Crosstab Data Text" x="0" y="0" width="65" height="25" uuid="0da99123-0d35-42f7-b126-b2938a6be302"/>
							<textFieldExpression><![CDATA[$V{SOLUONGMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="25" rowTotalGroup="STT">
					<cellContents>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" stretchType="RelativeToBandHeight" x="0" y="0" width="60" height="25" uuid="3d60ef8a-4240-4416-8fb1-cb62fe7a7dd5"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textFieldExpression><![CDATA[]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="171" height="25" rowTotalGroup="STT" columnTotalGroup="TENKHO">
					<cellContents>
						<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
							<reportElement style="Crosstab Data Text" x="0" y="0" width="171" height="25" uuid="da56c9d5-e79a-4545-b135-ca55580eff00"/>
							<box rightPadding="2">
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Right" verticalAlignment="Middle">
								<font fontName="Times New Roman" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{TONGTIENMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell rowTotalGroup="HAMLUONG">
					<cellContents/>
				</crosstabCell>
				<crosstabCell rowTotalGroup="HAMLUONG" columnTotalGroup="TENKHO">
					<cellContents/>
				</crosstabCell>
				<crosstabCell rowTotalGroup="DVT">
					<cellContents/>
				</crosstabCell>
				<crosstabCell rowTotalGroup="DVT" columnTotalGroup="TENKHO">
					<cellContents/>
				</crosstabCell>
				<crosstabCell rowTotalGroup="DONGIA">
					<cellContents/>
				</crosstabCell>
				<crosstabCell rowTotalGroup="DONGIA" columnTotalGroup="TENKHO">
					<cellContents/>
				</crosstabCell>
			</crosstab>
		</band>
	</summary>
</jasperReport>

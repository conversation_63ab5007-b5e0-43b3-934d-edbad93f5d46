<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_A4" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="810" leftMargin="17" rightMargin="15" topMargin="20" bottomMargin="16" whenResourceMissingType="Error" uuid="40878bc5-145e-4b9d-a320-de3e94e9e385">
	<property name="ireport.zoom" value="1.3310000000000006"/>
	<property name="ireport.x" value="2"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="inbangkechuan" class="java.lang.String"/>
	<parameter name="tiepnhanid" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="org_address" class="java.lang.String"/>
	<parameter name="MST" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="DPAR_ORG_LOGO" class="java.awt.Image"/>
	<queryString language="plsql">
		<![CDATA[{call     vpi_inphoimoi6556_987($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{tiepnhanid},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="KHOA" class="java.lang.String"/>
	<field name="KHAC" class="java.lang.Double"/>
	<field name="NOIDUNG" class="java.lang.String"/>
	<field name="SO_LUONG" class="java.lang.Double"/>
	<field name="NGUOI_BENH" class="java.lang.Double"/>
	<field name="QUY_BHYT" class="java.lang.Double"/>
	<field name="TIEPNHANID" class="java.lang.String"/>
	<field name="MATIEPNHAN" class="java.lang.String"/>
	<field name="NGAYTIEPNHAN" class="java.lang.String"/>
	<field name="NGAY_RAVIEN" class="java.lang.String"/>
	<field name="NGAYLAP" class="java.lang.String"/>
	<field name="TONGSONGAY" class="java.math.BigDecimal"/>
	<field name="CSYTID" class="java.math.BigDecimal"/>
	<field name="MASOTHUE" class="java.lang.String"/>
	<field name="CHANDOANRAVIEN" class="java.lang.String"/>
	<field name="CHANDOANRAVIEN_KEMTHEO" class="java.lang.String"/>
	<field name="MACHANDOANRAVIEN" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="GIOITINHID" class="java.lang.Integer"/>
	<field name="CAPCUU" class="java.lang.Integer"/>
	<field name="NAMSINH" class="java.math.BigDecimal"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="CO_BHYT" class="java.math.BigDecimal"/>
	<field name="MA_BHYT" class="java.lang.String"/>
	<field name="BHYT_BD" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="CSKCBBD" class="java.lang.String"/>
	<field name="MA_KCBBD" class="java.lang.String"/>
	<field name="DUNGTUYEN" class="java.lang.Integer"/>
	<field name="TONGTIEN" class="java.lang.String"/>
	<field name="MIENGIAMDV" class="java.lang.String"/>
	<field name="NGUOIBENH_TRA" class="java.lang.String"/>
	<field name="NGAYSINH" class="java.lang.String"/>
	<field name="TYLE" class="java.lang.String"/>
	<field name="DVT" class="java.lang.String"/>
	<field name="NOICHUYENDEN" class="java.lang.String"/>
	<field name="TAMUNG" class="java.lang.Double"/>
	<field name="NGUOITHU" class="java.lang.String"/>
	<field name="GHICHU_BENHCHINH" class="java.lang.String"/>
	<field name="MAKHOA" class="java.lang.String"/>
	<field name="LOAIKB" class="java.lang.String"/>
	<field name="MAKV" class="java.lang.String"/>
	<field name="NGAYVV" class="java.lang.String"/>
	<field name="TTRAVIEN" class="java.lang.String"/>
	<field name="NOICHUYENDI" class="java.lang.String"/>
	<field name="MAKEMTHEO" class="java.lang.String"/>
	<field name="NGAYDU" class="java.lang.String"/>
	<field name="THEBHYT" class="java.lang.String"/>
	<field name="THEBH_BD" class="java.lang.String"/>
	<field name="THEBH_KT" class="java.lang.String"/>
	<field name="KCBTUNGAY" class="java.lang.String"/>
	<field name="TENNHOM" class="java.lang.String"/>
	<field name="DONGIABV" class="java.lang.Double"/>
	<field name="DONGIABH" class="java.lang.Double"/>
	<field name="TYLE_DV" class="java.lang.Double"/>
	<field name="TY_LE" class="java.lang.Double"/>
	<field name="TONGXQ" class="java.lang.String"/>
	<field name="NGAYHUONG" class="java.lang.String"/>
	<field name="GROUPVT" class="java.lang.String"/>
	<field name="TYLEBH" class="java.lang.Double"/>
	<field name="CHLUONG" class="java.lang.Double"/>
	<field name="DKTHE" class="java.lang.Double"/>
	<field name="NAMBH" class="java.lang.String"/>
	<field name="THANGBH" class="java.lang.String"/>
	<field name="TEST" class="java.lang.Double"/>
	<field name="KTC" class="java.lang.String"/>
	<field name="THUOCVATTUID" class="java.lang.String"/>
	<field name="BNCHUYENVIEN" class="java.lang.String"/>
	<field name="THANHTIEN_BH_REAL" class="java.lang.Double"/>
	<field name="QUY_BHYT_REAL" class="java.lang.Double"/>
	<field name="STT" class="java.lang.String"/>
	<field name="HID" class="java.lang.String"/>
	<field name="CHECK_GOI" class="java.lang.String"/>
	<field name="historyid" class="java.lang.String"/>
	<field name="NGUOIBENHDCT" class="java.lang.Double"/>
	<field name="TUTRA" class="java.lang.Double"/>
	<field name="NGUOITHU_NGT" class="java.lang.String"/>
	<field name="CHECKDVTT" class="java.lang.String"/>
	<field name="DICHVUID" class="java.lang.String"/>
	<field name="DANOP" class="java.lang.Double"/>
	<field name="GROUP_KHOA" class="java.lang.String"/>
	<field name="MIENGIAM" class="java.lang.Double"/>
	<field name="THANHTIENBH" class="java.lang.Double"/>
	<field name="THANHTIENBV" class="java.lang.Double"/>
	<field name="GROUP_HSCC" class="java.lang.Integer"/>
	<field name="MGCOVID" class="java.lang.Double"/>
	<field name="MGTINHCHU" class="java.lang.String"/>
	<field name="MGTINH" class="java.lang.Double"/>
	<field name="MGTOANCAUCHU" class="java.lang.String"/>
	<field name="MGKHAC" class="java.lang.Double"/>
	<field name="MGKHACCHU" class="java.lang.String"/>
	<field name="MGTOANCAU" class="java.lang.Double"/>
	<field name="MGCOVIDCHU" class="java.lang.String"/>
	<field name="INBKNGUON" class="java.lang.Integer"/>
	<field name="STTNHOM" class="java.lang.String"/>
	<variable name="nhom_thanhtienbv" class="java.lang.Double" resetType="Group" resetGroup="GROUP_NHOM" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
	</variable>
	<variable name="report_thanhtienbv" class="java.lang.Double" resetType="Group" resetGroup="GROUP_THE" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="nhom_thanhtienbh" class="java.lang.Double" resetType="Group" resetGroup="GROUP_NHOM" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBH}]]></variableExpression>
	</variable>
	<variable name="report_thanhtienbh" class="java.lang.Double" resetType="Group" resetGroup="GROUP_THE" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBH}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="nhom_quybhyt" class="java.lang.Double" resetType="Group" resetGroup="GROUP_NHOM" calculation="Sum">
		<variableExpression><![CDATA[$F{QUY_BHYT}]]></variableExpression>
	</variable>
	<variable name="report_quybhyt" class="java.lang.Double" resetType="Group" resetGroup="GROUP_THE" calculation="Sum">
		<variableExpression><![CDATA[$F{QUY_BHYT}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="nhom_nbdct" class="java.lang.Double" resetType="Group" resetGroup="GROUP_NHOM" calculation="Sum">
		<variableExpression><![CDATA[$F{NGUOIBENHDCT}]]></variableExpression>
	</variable>
	<variable name="report_nbdct" class="java.lang.Double" resetType="Group" resetGroup="GROUP_THE" calculation="Sum">
		<variableExpression><![CDATA[$F{NGUOIBENHDCT}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="nhom_khac" class="java.lang.Double" resetType="Group" resetGroup="GROUP_NHOM" calculation="Sum">
		<variableExpression><![CDATA[$F{KHAC}]]></variableExpression>
	</variable>
	<variable name="report_khac" class="java.lang.Double" resetType="Group" resetGroup="GROUP_THE" calculation="Sum">
		<variableExpression><![CDATA[$F{KHAC}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="nhom_nguoibenh" class="java.lang.Double" resetType="Group" resetGroup="GROUP_NHOM" calculation="Sum">
		<variableExpression><![CDATA[$F{TUTRA}]]></variableExpression>
	</variable>
	<variable name="report_nguoibenh" class="java.lang.Double" resetType="Group" resetGroup="GROUP_THE" calculation="Sum">
		<variableExpression><![CDATA[$F{TUTRA}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="vt_thanhtienbh" class="java.lang.Double" resetType="Group" resetGroup="groupvt" calculation="Sum">
		<variableExpression><![CDATA[$F{TY_LE}==100.0?$F{THANHTIENBH}:0]]></variableExpression>
	</variable>
	<variable name="vt_quybhyt" class="java.lang.Double" resetType="Group" resetGroup="groupvt" calculation="Sum">
		<variableExpression><![CDATA[$F{TY_LE}==100?$F{QUY_BHYT_REAL}:0]]></variableExpression>
	</variable>
	<variable name="vt_nbdct" class="java.lang.Double" resetType="Group" resetGroup="groupvt" calculation="Sum">
		<variableExpression><![CDATA[$F{TY_LE}==100?$F{NGUOIBENHDCT}:0]]></variableExpression>
	</variable>
	<variable name="vt_khac" class="java.lang.Double" resetType="Group" resetGroup="groupvt" calculation="Sum">
		<variableExpression><![CDATA[$F{KHAC}]]></variableExpression>
	</variable>
	<variable name="vt_nguoibenh" class="java.lang.Double" resetType="Group" resetGroup="groupvt" calculation="Sum">
		<variableExpression><![CDATA[$F{TY_LE}==100?$F{TUTRA}:0]]></variableExpression>
	</variable>
	<variable name="vt_thanhtienbv" class="java.lang.Double" resetType="Group" resetGroup="groupvt" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
	</variable>
	<variable name="reportc_thanhtienbv" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="reportc_quybhyt" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{QUY_BHYT_REAL}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="reportc_nbdct" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{NGUOIBENHDCT}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="reportc_nguoibenh" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{TUTRA}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="reportc_khac" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{KHAC}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="nhom_thanhtienbh_real" class="java.lang.Double" resetType="Group" resetGroup="GROUP_NHOM" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN_BH_REAL}]]></variableExpression>
	</variable>
	<variable name="report_thanhtienbh_real" class="java.lang.Double" resetType="Group" resetGroup="GROUP_THE" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN_BH_REAL}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="nhom_quybh_real" class="java.lang.Double" resetType="Group" resetGroup="GROUP_NHOM" calculation="Sum">
		<variableExpression><![CDATA["1".equals($F{CHECK_GOI})?$F{QUY_BHYT_REAL}:$F{QUY_BHYT}]]></variableExpression>
	</variable>
	<variable name="report_quybh_real" class="java.lang.Double" resetType="Group" resetGroup="GROUP_THE" calculation="Sum">
		<variableExpression><![CDATA[$F{QUY_BHYT_REAL}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="reportc_thanhtienbh" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN_BH_REAL}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="vt_thanhtienbvnhom" class="java.lang.Double" resetType="Group" resetGroup="groupvt" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
	</variable>
	<variable name="nhom_thanhtienbvnhom" class="java.lang.Double" resetType="Group" resetGroup="GROUP_NHOM" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
	</variable>
	<variable name="report_thanhtienbvnhom" class="java.lang.Double" resetType="Group" resetGroup="GROUP_THE" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="reportc_thanhtienbvnhom" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="khoa_thanhtienbv" class="java.lang.Double" resetType="Group" resetGroup="GroupKhoa" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
	</variable>
	<variable name="khoa_thanhtienbh" class="java.lang.Double" resetType="Group" resetGroup="GroupKhoa" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBH}]]></variableExpression>
	</variable>
	<variable name="khoa_quybh" class="java.lang.Double" resetType="Group" resetGroup="GroupKhoa" calculation="Sum">
		<variableExpression><![CDATA["1".equals($F{CHECK_GOI})?$F{QUY_BHYT_REAL}:$F{QUY_BHYT}]]></variableExpression>
	</variable>
	<variable name="khoa_nbdct" class="java.lang.Double" resetType="Group" resetGroup="GroupKhoa" calculation="Sum">
		<variableExpression><![CDATA[$F{NGUOIBENHDCT}]]></variableExpression>
	</variable>
	<variable name="khoa_khac" class="java.lang.Double" resetType="Group" resetGroup="GroupKhoa" calculation="Sum">
		<variableExpression><![CDATA[$F{KHAC}]]></variableExpression>
	</variable>
	<variable name="khoa_nguoibenh" class="java.lang.Double" resetType="Group" resetGroup="GroupKhoa" calculation="Sum">
		<variableExpression><![CDATA[$F{TUTRA}]]></variableExpression>
	</variable>
	<variable name="stt_khoa" class="java.lang.Integer" incrementType="Group" incrementGroup="GroupKhoa" calculation="Count">
		<variableExpression><![CDATA[$V{stt_khoa}]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<variable name="khoahscc_thanhtienbv" class="java.lang.Double" resetType="Group" resetGroup="GROUP_KHOAHSCC" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBV}]]></variableExpression>
	</variable>
	<variable name="khoahscc_thanhtienbh" class="java.lang.Double" resetType="Group" resetGroup="GROUP_KHOAHSCC" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBH}]]></variableExpression>
	</variable>
	<variable name="khoahscc_quybh" class="java.lang.Double" resetType="Group" resetGroup="GROUP_KHOAHSCC" calculation="Sum">
		<variableExpression><![CDATA["1".equals($F{CHECK_GOI})?$F{QUY_BHYT_REAL}:$F{QUY_BHYT}]]></variableExpression>
	</variable>
	<variable name="khoahscc_nbdct" class="java.lang.Double" resetType="Group" resetGroup="GROUP_KHOAHSCC" calculation="Sum">
		<variableExpression><![CDATA[$F{NGUOIBENHDCT}]]></variableExpression>
	</variable>
	<variable name="khoahscc_khac" class="java.lang.Double" resetType="Group" resetGroup="GROUP_KHOAHSCC" calculation="Sum">
		<variableExpression><![CDATA[$F{KHAC}]]></variableExpression>
	</variable>
	<variable name="khoahscc_nguoibenh" class="java.lang.Double" resetType="Group" resetGroup="GROUP_KHOAHSCC" calculation="Sum">
		<variableExpression><![CDATA[$F{TUTRA}]]></variableExpression>
	</variable>
	<group name="GROUP_THE">
		<groupExpression><![CDATA[$F{historyid}]]></groupExpression>
		<groupHeader>
			<band height="133">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="159" y="1" width="34" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="cfa68c2f-ba7a-4d9c-90ef-530e804ad4b1"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{THEBHYT}.substring(3,5);]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="107" y="1" width="34" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="9eedc5a9-0a77-4555-ab90-c6e55d4362f3"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{THEBHYT}.substring(0,2);]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="1" width="107" height="13" uuid="7f723eb5-385d-403d-b473-9a436d7ae754"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[ Mã thẻ BHYT:]]></text>
				</staticText>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="141" y="1" width="17" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="b4f18a45-94e7-4849-8aef-e5ad4287aed1"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{THEBHYT}.substring(2,3);]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="192" y="1" width="136" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="c5020b57-8c23-495a-8e72-b3561f06cd75"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{THEBHYT}.substring(5,15);]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="393" y="1" width="55" height="13" uuid="30274b64-71da-483a-81e2-a01394182e19"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[Giá trị từ:]]></text>
				</staticText>
				<staticText>
					<reportElement x="667" y="1" width="69" height="13" uuid="21f09df1-cb78-42b0-9014-6704bb897e0c"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[Mức hưởng]]></text>
				</staticText>
				<textField pattern="###0" isBlankWhenNull="true">
					<reportElement x="736" y="1" width="22" height="13" uuid="fa4ba560-d379-4791-a8f2-81c2750973c8"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TYLEBH}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="758" y="1" width="26" height="13" uuid="177f8f6b-e5bd-4c9c-aace-a35982784e7e"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[%]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="16" width="133" height="13" uuid="a6180e33-a4ec-4709-bdb3-09f0a5cc9d8e"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[(Chi phí KBCB tính từ ngày]]></text>
				</staticText>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="133" y="16" width="467" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="245b7ac5-7a96-4087-b584-911b43d3dd92"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{KCBTUNGAY}.replace("denngay", "đến ngày" )]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="0" y="31" width="170" height="87" uuid="957ae053-8e38-4011-b32d-74143f30c902"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Nội dung]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="170" y="31" width="35" height="87" uuid="470a45f4-a342-431e-8c9d-65cb02f2fff5"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Đơn vị tính]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="205" y="31" width="30" height="87" uuid="9c29ac93-f65b-45ac-a9dc-bdb99bee0d96"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Số lượng]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="235" y="31" width="61" height="87" uuid="8dba549f-8da1-4864-899e-21a73d8eccca"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Đơn giá BV
(Đồng)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="393" y="31" width="79" height="87" uuid="a5b75ed8-fbd9-483e-b934-02d0ccd065c2"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Thành tiền BV
(Đồng)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="580" y="31" width="230" height="30" uuid="18b13ab1-8041-4db0-9c9e-dfe4ff4455b2"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Nguồn thanh toán (đồng)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="580" y="61" width="63" height="57" uuid="0e6b0379-61be-43bb-af92-fc177330cfd8"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Quỹ BHYT]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="701" y="61" width="45" height="57" uuid="946bbb38-dc92-49f7-897a-e4981d8abbf9"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Khác
]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="235" y="118" width="61" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="ed906585-cd77-4de0-adab-310597ac16c3"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(4)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="0" y="118" width="170" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="2f81c2e2-d5ab-4bdc-bd1e-0e51b715d67f"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(1)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="357" y="118" width="35" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="f96ad9e5-f59d-4897-910d-d4a6f9105036"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(6)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="205" y="118" width="30" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d8093d12-3315-49a2-944a-8565c067ecfc"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(3)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="296" y="118" width="61" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="be2d13da-cecc-4471-8bb7-5643cd799b7e"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(5)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="170" y="118" width="35" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="b0102578-6205-4ea9-b98c-9b5933d16f2c"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(2)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="393" y="118" width="79" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="2901e16a-d567-4cda-81f5-14e2705cf179"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(7)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="296" y="31" width="61" height="87" uuid="481b3f99-2c2e-4f81-b1ba-c6248613bbec"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Đơn giá BH
(Đồng)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="473" y="31" width="32" height="87" uuid="e5ca7b7d-1d1c-4eaf-836a-0c26f54630a5"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Tỷ lệ thanh toán BHYT (%)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="505" y="31" width="74" height="87" uuid="df55a250-d129-4a07-a2a5-7caea803a1df"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Thành tiền BH
(Đồng)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="643" y="61" width="58" height="57" uuid="999bc09c-df7b-410e-bc33-20a108b78cb7"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Người
bệnh cùng
chi trả]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="746" y="61" width="64" height="57" uuid="375ec359-2762-492d-a1cb-c45c49bfba75"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Người bệnh
tự trả]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="702" y="118" width="44" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="5daf5eb4-d43c-4095-bbb9-366ae4c359e7"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(12)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="580" y="118" width="63" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="8aac29db-2b60-4b2a-9c9b-3eb1a1a085f5"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(10)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="643" y="118" width="58" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="90a76ab4-e213-4e81-ae6c-470a96dee746"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(11)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="746" y="118" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="397bfd4b-1eb6-4898-9daf-4a75d644a3ea"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(13)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="506" y="118" width="73" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="8d5acd27-60a3-4518-9873-46448eb42b8c"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(9)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" mode="Transparent" x="473" y="118" width="32" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="1fa5cdb6-5c87-495f-a799-1226b965a423"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[(8)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="357" y="31" width="35" height="87" uuid="c58d0f1c-2075-49f8-8820-e597db3dc567"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Tỷ lệ thanh toán theo dịch vụ (%)]]></text>
				</staticText>
				<staticText>
					<reportElement x="506" y="1" width="30" height="13" uuid="77d97701-6c95-4df6-a563-c9e026862e66"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[đến:]]></text>
				</staticText>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="445" y="1" width="60" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="a645366f-9c1f-4a08-9ef1-531fae295cab"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{THEBH_BD}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="532" y="1" width="66" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="77ca950e-2770-4033-9cb7-fc2ad2c192aa"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{THEBH_KT}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="15">
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_THE" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="392" y="0" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d92aa5e3-34fb-4359-b14c-8c66bd8b7d97"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{report_thanhtienbv}.equals(0.0)?null: new DecimalFormat("#,##0.00").format($V{report_thanhtienbv}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="392" height="15" backcolor="#FFCCFF" uuid="7decf994-d8f5-4491-b78c-dee5ec40fe3b"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<text><![CDATA[Cộng:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="472" y="0" width="33" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="8a930ada-f960-4529-a0fa-0edc717d9d40"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_THE" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="505" y="0" width="74" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="773430be-6736-4b6a-bc34-46d1791c7d84"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{report_thanhtienbh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_THE" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="579" y="0" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="b9823bba-4438-4413-976f-7e313b4416d5"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{report_quybh_real}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_THE" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="643" y="0" width="58" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="2fe454d1-d331-4aa1-85dc-8d73e43899af"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{report_nbdct}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_THE" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="701" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="2854ff90-5e9e-4b77-90ca-a46e2038994f"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{report_khac}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_THE" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="746" y="0" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="30edad7c-8876-4e65-abdf-2e5ba0025852"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{report_nguoibenh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="GroupKhoa">
		<groupExpression><![CDATA[$F{GROUP_KHOA}]]></groupExpression>
		<groupHeader>
			<band height="15">
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GroupKhoa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="701" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="88ba98c2-1224-4db6-84d3-ce435ea32181"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khoa_khac}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{khoa_khac}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GroupKhoa" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="392" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a9ce638b-910f-4366-8bfd-c85e8d843f13"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None" markup="html">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[java.lang.Character.toString ((char)($V{stt_khoa}+64) )+". "+$F{GROUP_KHOA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GroupKhoa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="746" y="0" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="bb383b9f-056d-4e53-bbcd-6bc727cd11e4"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{khoa_nguoibenh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="472" y="0" width="33" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="47f86ae2-b33d-4477-a952-889aab1cd44d"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GroupKhoa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="643" y="0" width="58" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="ba6cf289-10a0-4150-8474-af5b719c1d4d"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{khoa_nbdct}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GroupKhoa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="505" y="0" width="74" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d7699b16-b10e-49e2-860e-b27279029a65"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khoa_thanhtienbh}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{khoa_thanhtienbh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GroupKhoa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="392" y="0" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="95ce4e56-897e-4bdb-8229-eb596164651b"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khoa_thanhtienbv}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{khoa_thanhtienbv}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GroupKhoa" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="579" y="0" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a666c476-0548-4da2-8886-228fb3165469"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khoa_quybh}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{khoa_quybh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="GROUP_KHOAHSCC">
		<groupExpression><![CDATA[$F{GROUP_HSCC}]]></groupExpression>
		<groupHeader>
			<band height="15">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="392" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c12854f1-6276-4d77-a67e-ec8520500b32">
						<printWhenExpression><![CDATA[$F{GROUP_HSCC}!=null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None" markup="html">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GROUP_HSCC}==0?"I. Phòng khám cấp cứu":($F{GROUP_HSCC}==1?"II. Khoa cấp cứu":"")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_KHOAHSCC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="392" y="0" width="80" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="fafe5f84-14a2-410f-b7e4-7947d5cb5cd7">
						<printWhenExpression><![CDATA[$F{GROUP_HSCC}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khoahscc_thanhtienbv}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{khoahscc_thanhtienbv}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_KHOAHSCC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="505" y="0" width="74" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="b5391bb6-cef8-4f75-8cf9-57fa0b22316a">
						<printWhenExpression><![CDATA[$F{GROUP_HSCC}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khoahscc_thanhtienbh}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{khoahscc_thanhtienbh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_KHOAHSCC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="579" y="0" width="64" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="a456dd0d-79c4-4846-8380-c6632ef672d4">
						<printWhenExpression><![CDATA[$F{GROUP_HSCC}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khoahscc_quybh}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{khoahscc_quybh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_KHOAHSCC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="643" y="0" width="58" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="54452aeb-0d3c-4516-abac-3801af947286">
						<printWhenExpression><![CDATA[$F{GROUP_HSCC}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{khoahscc_nbdct}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_KHOAHSCC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="701" y="0" width="45" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="4287a04d-e55b-4e14-abe0-07f82baf6fea">
						<printWhenExpression><![CDATA[$F{GROUP_HSCC}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{khoahscc_khac}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{khoahscc_khac}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_KHOAHSCC" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="746" y="0" width="64" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="598b7616-39c3-42cc-98a5-298512fc1568">
						<printWhenExpression><![CDATA[$F{GROUP_HSCC}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{khoahscc_nguoibenh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="GROUP_NHOM">
		<groupExpression><![CDATA[$F{TENNHOM}]]></groupExpression>
		<groupHeader>
			<band height="15">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="392" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e8d32ea8-21e0-44af-9035-91874bb72614"/>
					<box leftPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None" markup="html">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{STTNHOM}+". "+$F{TENNHOM}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_NHOM" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="392" y="0" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="b2cd5484-f7ec-46a9-83cc-8ed3249f9ef4"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nhom_thanhtienbv}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{nhom_thanhtienbv}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="472" y="0" width="33" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="01929305-59fb-41dc-ab02-7a8a9a626540"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_NHOM" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="505" y="0" width="74" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="b6b8c5fe-f775-4a96-bf0f-bb470c475157"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nhom_thanhtienbh}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{nhom_thanhtienbh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_NHOM" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="579" y="0" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="7735f19b-f4e7-4a62-8e89-f372e7e31be6"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nhom_quybh_real}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{nhom_quybh_real}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_NHOM" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="643" y="0" width="58" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="1ecb4476-f353-4b12-8124-deff272cea3a"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nhom_nbdct}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_NHOM" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="701" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="b3fe29be-4ec2-4196-817e-72deae02c80f"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nhom_khac}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{nhom_khac}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="GROUP_NHOM" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="746" y="0" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e94bafa6-7010-4f44-90df-510c2a713c96"/>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{nhom_nguoibenh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="groupvt">
		<groupExpression><![CDATA[$F{GROUPVT}]]></groupExpression>
		<groupHeader>
			<band height="15">
				<textField evaluationTime="Group" evaluationGroup="groupvt" pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="0" y="0" width="392" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="793ad304-6fbd-45e6-bfd1-8e9d5a4e35f7">
						<printWhenExpression><![CDATA[$F{GROUPVT}!=null]]></printWhenExpression>
					</reportElement>
					<box leftPadding="2">
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None" markup="html">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GROUPVT}==null?"":("10."+$F{STT}+". Gói vật tư y tế "+$F{STT}+ "("+$F{GROUPVT}+")")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="groupvt" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="505" y="0" width="74" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5c7479da-636b-4047-b64a-b52738b6d5cf">
						<printWhenExpression><![CDATA[$F{GROUPVT}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vt_thanhtienbh}.equals(0.0)?null:((($V{vt_thanhtienbh}>45*$F{CHLUONG}) && $F{DKTHE}!=1.0)?new DecimalFormat("#,##0.00").format(45*$F{CHLUONG}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."):new DecimalFormat("#,##0.00").format($V{vt_thanhtienbh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."))]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="groupvt" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="579" y="0" width="64" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="884a89e6-b5b0-486c-be33-0b0944fdb7bd">
						<printWhenExpression><![CDATA[$F{GROUPVT}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{vt_quybhyt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="groupvt" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="643" y="0" width="58" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="1cd83c21-d7d5-43e3-95b0-3b4988a9f477">
						<printWhenExpression><![CDATA[$F{GROUPVT}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{vt_nbdct}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="groupvt" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="701" y="0" width="45" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="94cae359-36fb-4fb1-a82c-************">
						<printWhenExpression><![CDATA[$F{GROUPVT}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vt_khac}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{vt_khac}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="groupvt" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="746" y="0" width="64" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c0782ef6-6d83-4e1b-a394-688ed7e7c593">
						<printWhenExpression><![CDATA[$F{GROUPVT}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{vt_nguoibenh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="groupvt" pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="392" y="0" width="80" height="15" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="cb2de597-8add-49f8-aad9-ffa00f04274a">
						<printWhenExpression><![CDATA[$F{GROUPVT}!=null]]></printWhenExpression>
					</reportElement>
					<box rightPadding="1">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vt_thanhtienbv}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{vt_thanhtienbv}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="290" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="77" y="0" width="403" height="13" uuid="69cfb868-7273-42c6-80f5-00dbf8c58401"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="77" y="13" width="403" height="13" uuid="2a5712e5-5cb6-4641-be63-6844cd806ef9"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="610" y="0" width="158" height="13" uuid="6ff3e516-4091-4d04-8b80-68c7be0a5282"/>
				<textElement>
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Mẫu số: 01/KBCB]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="90" width="122" height="13" uuid="ca3cfcad-985d-4eae-96d1-50435932fd50"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[I. Phần Hành chính]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="104" width="122" height="13" uuid="608c608f-56ec-4b69-8879-b49489612c32"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" pdfFontName="" pdfEncoding="Cp1258" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(1) Họ tên người bệnh:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="431" y="104" width="119" height="13" uuid="a5e01df8-9d19-4aa6-a707-f5ec4eef7ced"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Ngày, tháng, năm sinh:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="668" y="104" width="58" height="13" uuid="963c0e1c-78b4-4207-bed8-aa6d0de06a3d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Giới tính:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="691" y="147" width="48" height="13" uuid="5824b85b-b65a-4d2f-ab6a-6a0a03f1742c"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(6) Mã: 	]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="162" width="72" height="13" uuid="cbabec99-6bc4-4a38-ba68-deac4fcecabf"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(7) Đến khám:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="190" width="143" height="13" uuid="0ba80c07-0233-42d7-aa14-e7d910fa0856"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(9) Kết thúc khám/điều trị:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="440" y="190" width="102" height="13" uuid="091a7bf5-47be-4b99-bc66-348a0638eaaa"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Tổng số ngày điều trị:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="562" y="190" width="25" height="13" uuid="0fa61aa2-5394-4c44-8ff0-b7c0f3b0d238"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Ngày]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="205" width="66" height="13" uuid="5f999640-3a86-465a-86fb-a29f8bc978a3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(11) Cấp cứu]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="80" y="205" width="79" height="13" uuid="a506f819-db74-487a-9df8-9defdbdb5f23"/>
				<box rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(12)Đúng tuyến:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="716" y="205" width="79" height="13" uuid="c662e652-922a-4d5f-a3ce-bca7b1f2bc0f"/>
				<box rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(14) Trái tuyến:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="666" y="220" width="72" height="13" uuid="ebd797c2-1883-44dc-9492-31b47b17b2d4"/>
				<box rightPadding="2"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(16) Mã bệnh:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="550" y="104" width="99" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="4f493aa3-12f1-4050-97ea-b7bc6de30be1"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYSINH}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="132" width="107" height="13" uuid="45f1ec66-718b-4439-9ffb-accfb16939fb"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(4) Mã thẻ BHYT:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="444" y="132" width="55" height="13" uuid="cad3aa69-86e5-44d4-aea0-79ea4fc59408"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Giá trị từ:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="557" y="132" width="30" height="13" uuid="b86e7d38-369f-4851-9fbd-78560340b246"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[đến:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="727" y="104" width="62" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="0028a0ad-f256-48ee-8d6b-39a5526efee0"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINHID}.equals(1)?"(1)":"(2)"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="107" y="132" width="34" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="1927afbe-93fa-44cf-a998-0b1bc0245d3e"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(0,2);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="141" y="132" width="17" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="69b35f98-55c4-4c1e-b439-c8a3c09dc9e8"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(2,3);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="192" y="132" width="136" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="3e4d57e2-7534-49ad-afdb-14291ba9821a"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(5,15);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="496" y="132" width="60" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="ba6dc4a4-a09f-47d9-a2b2-6900c4791e9d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BHYT_BD}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="583" y="132" width="66" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="7499036e-9aa2-4030-86c3-539935a348bf"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BHYT_KT}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="733" y="147" width="77" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="d1c535ec-179f-4b10-b3ab-36d98e82e7aa"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_KCBBD}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="133" y="147" width="558" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="4b45bbcc-5240-43b7-9c7d-0d1b19ca8c36"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CSKCBBD}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="63" y="205" width="15" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="207553dc-631e-4611-8777-315b5aea2f18"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPCUU}.equals(1)?"X":""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="158" y="205" width="15" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="f853ba7f-ee8f-4968-8f32-d2afbe18ca0c"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPCUU}.equals(1)?"":($F{DUNGTUYEN}.equals(4)||$F{DUNGTUYEN}.equals(5)?"":"X")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="542" y="190" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="a5eee38a-cdc7-4b98-b4d6-7156a8537b6d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONGSONGAY}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="795" y="205" width="15" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="94241736-c0bb-4e39-a926-b84adeb57203"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPCUU}.equals(1)?"":($F{DUNGTUYEN}.equals(4)?"X":"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="739" y="220" width="68" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="2fed62de-1a08-4d2a-b323-14558c679a8c"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MACHANDOANRAVIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="220" width="666" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="22a16b7e-b180-4d4d-973c-d22e2744e65d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["(15) Chẩn đoán xác định: "+($F{CHANDOANRAVIEN}==null?"":$F{CHANDOANRAVIEN})+($F{GHICHU_BENHCHINH}==null?"":(" ("+$F{GHICHU_BENHCHINH}+")"))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="159" y="132" width="34" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="4f9ce589-8984-45cc-a0eb-e11986b71130"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(3,5);]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="122" y="104" width="304" height="13" uuid="35406bda-c384-4afe-8a3b-189e81d1f4b9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="277" width="288" height="13" uuid="63007a45-d6e4-41a8-8089-195226c43c7a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[II. Phần Chi phí khám bệnh, chữa bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement x="610" y="13" width="91" height="13" uuid="d21a56b7-f0fe-4a4e-a79b-2a43feb826b9"/>
				<textElement>
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Mã số người bệnh:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="701" y="13" width="69" height="13" uuid="23b9cec2-8604-4f13-b6e4-faae6ced1315"/>
				<textElement textAlignment="Right">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MABENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="235" width="628" height="26" forecolor="#000000" backcolor="#FFFFFF" uuid="066b804b-080d-49b1-a22e-1ea624b4e104"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["(17) Bệnh kèm theo: "+($F{CHANDOANRAVIEN_KEMTHEO}==null?"":$F{CHANDOANRAVIEN_KEMTHEO})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="77" y="26" width="403" height="13" uuid="bcdfb233-4be4-47a2-a579-45dbe5424b12"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KHOA}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="610" y="41" width="158" height="29" uuid="a28e7e18-1e05-4160-b7b7-8dbeeb283ea4"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="bottom">
					<jr:codeExpression><![CDATA[$F{MAHOSOBENHAN}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement positionType="Float" x="254" y="162" width="26" height="13" uuid="dea4435f-e122-4ea6-bd36-6df58fcef582"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[ngày]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="229" y="162" width="25" height="13" uuid="ab9d848f-5377-46a1-baac-07a981c20fc6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[phút]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="280" y="162" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="c6f308dc-7423-4b41-802c-d2accb6cd9eb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYTIEPNHAN}.substring(0,2);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="209" y="162" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="85e69df9-e245-4534-b4f0-382d6f61f199"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYTIEPNHAN}.substring(10,12);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="372" y="162" width="35" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="781a8a68-dccd-42c0-8962-ef4331dd651f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYTIEPNHAN}.substring(4,8);]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="300" y="162" width="28" height="13" uuid="b0bdc403-c525-4048-ade7-6f826aa0c1f8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[tháng]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="167" y="162" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="0f5d256f-e7ca-4233-8301-ad899626a2e5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYTIEPNHAN}.substring(8,10);]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="348" y="162" width="24" height="13" uuid="ddf80cf9-74cc-48ef-acb8-a12bacc7c144"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[năm]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="187" y="162" width="22" height="13" uuid="a57e4dce-edd6-4f27-a3c3-093ed50612e0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[giờ]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="328" y="162" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="9d5878f8-38e2-4ffd-b002-0843e350b09d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYTIEPNHAN}.substring(2,4);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="167" y="190" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="fbf8b0bd-a300-4b73-862e-bda3e475af8c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(8,10);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="280" y="190" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="dc91912c-8d71-4e84-808c-455cd02d9b9c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(0,2);]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="348" y="190" width="24" height="13" uuid="791c86a8-91ea-49fb-b10b-3b1c5cf0f9c5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[năm]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="254" y="190" width="26" height="13" uuid="95bf9ec6-cbd1-41d6-bb05-b706a391f5db"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[ngày]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="328" y="190" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="7261a37d-6a9b-4fe6-b8b6-65c01abf8677"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(2,4);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="209" y="190" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="10a0048a-7a2e-439f-a38a-c4f6b14130a2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(10,12);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="372" y="190" width="35" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="aa57a530-2ef7-4055-9cbf-0b0ea8d273c2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(4,8);]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="187" y="190" width="22" height="13" uuid="fa9c6af9-0fd8-4a91-98b5-afe2bc0dba2e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[giờ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="229" y="190" width="25" height="13" uuid="9aa7036f-2af9-4abd-99c9-7819f4839bab"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[phút]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="300" y="190" width="28" height="13" uuid="484ab9c1-4738-4ae2-9e0e-9d831375c6c8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[tháng]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="147" width="141" height="13" uuid="96bb63cc-3a8f-4c19-ad4e-901b9ca6b834"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(5) Nơi ĐK KCB ban đầu:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="176" y="205" width="108" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="a999ba64-8d72-4114-9eca-b160e0881708"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Nơi chuyển đến từ:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="276" y="205" width="132" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="40eea793-9987-471e-a4d8-99becb5b0c22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOICHUYENDEN}=="Bệnh viện 74 trung ương"?"":$F{NOICHUYENDEN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="610" y="26" width="75" height="13" uuid="a72540a2-aea5-41ec-9ad9-34ac0ef6056b"/>
				<textElement>
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Số khám bệnh:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="684" y="26" width="86" height="13" uuid="c815e31c-e49a-435f-af56-3169e1acc758"/>
				<textElement textAlignment="Right">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIEPNHANID}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="77" y="40" width="403" height="13" uuid="518438fb-179d-4347-9552-bba4bc228ae7"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã khoa: "+($F{MAKHOA}==null?"":$F{MAKHOA})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="213" y="60" width="332" height="25" uuid="4481f516-68ad-4f2b-872b-894dd69771f0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[("bảng kê chi phí "+($F{LOAIKB}.equals("3")?"điều trị nội trú":($F{LOAIKB}.equals("1")?"khám bệnh":($F{LOAIKB}.equals("2")?"điều trị ngoại trú":($F{LOAIKB}.equals("4")?"điều trị ban ngày":""))))).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField pattern="###0" isBlankWhenNull="true">
				<reportElement x="545" y="63" width="20" height="20" uuid="03681488-894a-482d-a536-c63073619952"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["("+$F{LOAIKB}+")"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="209" y="176" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="415005e5-b6a4-4a64-aafd-4fa86e5e6074"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYVV}.substring(10,12);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="280" y="176" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="e77ee08b-07cc-407b-aaaa-b228b9ae899e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYVV}.substring(0,2);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="372" y="176" width="35" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="63420690-2b28-4156-a33c-45dc898f64d3"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYVV}.substring(4,8);]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="176" width="161" height="13" uuid="358902e2-7f9b-4efc-8ec8-7b25f3d4da45"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(8) Điều trị ngoại trú/nội trú từ:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="348" y="176" width="24" height="13" uuid="240a77dc-2743-4181-ad0f-5b5dd8364ca6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[năm]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="229" y="176" width="25" height="13" uuid="03078f10-33db-48e5-8614-2dc0256557a8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[phút]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="300" y="176" width="28" height="13" uuid="038a7895-ab75-4544-b119-940e2b3cb95b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[tháng]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="328" y="176" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="fe655bbe-c643-44a1-811a-0c69a8a81c6e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYVV}.substring(2,4);]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="187" y="176" width="22" height="13" uuid="bd6bf75a-6b0f-4eca-8f08-29b95875e8b0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[giờ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="254" y="176" width="26" height="13" uuid="6f4090bd-18c2-4720-aef4-ab067287778e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[ngày]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="167" y="176" width="20" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="de01bb0c-acf1-4479-ac49-9a759c68b823"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYVV}.substring(8,10);]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="626" y="190" width="128" height="13" uuid="453b863b-7015-421d-837d-d3a4629f7d30"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(10) Tình trạng ra viện: 	]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="754" y="189" width="35" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="9ecfebf6-963e-4eb1-8fc2-382108dcb05c"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TTRAVIEN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="408" y="205" width="108" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="b8a98465-ef80-4bdf-b691-2ae96ea1ace6"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Nơi chuyển đi:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="480" y="205" width="130" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="e1b48f94-4f58-4320-9958-49500862391d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["1".equals($F{BNCHUYENVIEN})?$P{org_name}:""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="701" y="205" width="15" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="2ac1966f-15bf-4806-94cf-1a1655291440"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CAPCUU}.equals(1)?"":($F{DUNGTUYEN}.equals(5)?"X":"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="610" y="205" width="91" height="13" uuid="4049128a-1c79-4778-8fdf-cbce735d34f2"/>
				<box rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(13) Thông tuyến:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="628" y="235" width="69" height="26" uuid="78844c04-4223-4f2e-b169-33d98e245a84"/>
				<box rightPadding="2"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(18) Mã bệnh kèm theo:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="697" y="235" width="113" height="26" forecolor="#000000" backcolor="#FFFFFF" uuid="bd981a10-dc1c-444e-9f59-5c4174921aa5"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAKEMTHEO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="262" width="207" height="13" uuid="21e2b388-8b28-495e-888e-d079e0b2a220"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(19) Thời điểm đủ 05 năm liên tục từ ngày:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="207" y="262" width="139" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="3a1c16e8-f6c9-4e6f-8138-67c16bad745e"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYDU}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="348" y="262" width="207" height="13" uuid="dec65bbd-5879-48cc-95a6-864495b89a6b"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[(20) Miễn cùng chi trả trong năm từ ngày:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="550" y="262" width="139" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="5780f2ed-458f-4807-95a2-5f2412101d22"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYHUONG}]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame" hAlign="Right" vAlign="Top">
				<reportElement positionType="Float" x="1" y="0" width="65" height="61" uuid="a8bf8136-08eb-4fd2-9230-e8c92ed576cc"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64("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".getBytes()))]]></imageExpression>
			</image>
			<frame>
				<reportElement positionType="Float" x="1" y="118" width="810" height="13" uuid="ca49be6d-39ff-4967-884f-7e7284719203"/>
				<staticText>
					<reportElement positionType="Float" x="644" y="0" width="144" height="13" uuid="0767054b-9e48-4359-923f-26213afbf29e"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[(3) Mã khu vực (K1/K2/K3):]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="0" width="94" height="13" uuid="ad65bf36-1104-4db5-b86c-d3e5157e1341"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[(2) Địa chỉ hiện tại:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="94" y="0" width="550" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="14f2b103-7e77-44ed-8c61-79dcf9a7a27b"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DIACHI}]]></textFieldExpression>
				</textField>
			</frame>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="778" y="118" width="32" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="fc22ec9f-e710-4c96-a3ce-176254c6043a"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAKV}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="15" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="170" height="15" uuid="00e04300-4b6c-4b38-98df-13ba42498f8d"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?"-"+$F{NOIDUNG}:"<font color=green>" + "-"+ $F{NOIDUNG} + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="170" y="0" width="35" height="15" uuid="897edba1-536e-496a-876b-ada55f333818"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?$F{DVT}:"<font color=green>" + $F{DVT} + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.000" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="205" y="0" width="30" height="15" uuid="a294bb02-e329-4e81-9c71-eec9d24bc6c0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="9" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?$F{SO_LUONG}:"<font color=green>" +  new java.text.DecimalFormat("#,##0.000").format(Double.valueOf($F{SO_LUONG})) + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="235" y="0" width="61" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d07b9af3-46de-4c3c-8f3e-e1199a02caf5"/>
				<box leftPadding="0" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?new DecimalFormat("#,##0.000").format($F{DONGIABV}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."):"<font color=green>" + new DecimalFormat("#,##0.00").format($F{DONGIABV}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="296" y="0" width="61" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d1fdd500-dc3d-4460-9a85-401c6a07eda9"/>
				<box leftPadding="0" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?new DecimalFormat("#,##0.000").format($F{DONGIABH}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."):"<font color=green>" + new DecimalFormat("#,##0.00").format($F{DONGIABH}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="357" y="0" width="35" height="15" uuid="d6725d2e-b9b9-4bdc-bb6e-eff4316f19d0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?$F{TYLE_DV}:"<font color=green>" + new java.text.DecimalFormat("#,##0").format(Double.valueOf($F{TYLE_DV})) + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="472" y="0" width="33" height="15" uuid="8d4239d7-**************-fb33f3a478d3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?$F{TY_LE}:"<font color=green>" + new java.text.DecimalFormat("#,##0.00").format(Double.valueOf($F{TY_LE})) + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="392" y="0" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="0e290bc3-bf4d-4dc4-a3ee-854f3726f9cf"/>
				<box rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?new DecimalFormat("#,##0.00").format($F{THANHTIENBV}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."):"<font color=green>" + new DecimalFormat("#,##0.00").format($F{THANHTIENBV}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="579" y="0" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="ca9898a3-63e8-486b-a102-68cc4a8c380f">
					<printWhenExpression><![CDATA[$F{GROUPVT}==null || ("0".equals($F{KTC}))||($F{THUOCVATTUID}.length()>0 && $F{TY_LE}!=100.0)]]></printWhenExpression>
				</reportElement>
				<box rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?new DecimalFormat("#,##0.00").format($F{QUY_BHYT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."):"<font color=green>" + new DecimalFormat("#,##0.00").format($F{QUY_BHYT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="701" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="c7a3fc4e-b297-4b97-8426-7f804e513da3">
					<printWhenExpression><![CDATA[$F{GROUPVT}==null || ("0".equals($F{KTC}))||($F{THUOCVATTUID}.length()>0 && $F{TY_LE}!=100.0)]]></printWhenExpression>
				</reportElement>
				<box rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?new DecimalFormat("#,##0.00").format($F{KHAC}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."):"<font color=green>" + new DecimalFormat("#,##0.00").format($F{KHAC}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="746" y="0" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="cf911372-cefb-44f5-940c-1ca1f37523cf">
					<printWhenExpression><![CDATA[$F{GROUPVT}==null || ("0".equals($F{KTC}))||($F{THUOCVATTUID}.length()>0 && $F{TY_LE}!=100.0)]]></printWhenExpression>
				</reportElement>
				<box rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?new DecimalFormat("#,##0.00").format($F{TUTRA}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."):"<font color=green>" + new DecimalFormat("#,##0.00").format($F{TUTRA}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="643" y="0" width="58" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="35915c29-f0d0-49f2-a37b-686ca9441001">
					<printWhenExpression><![CDATA[$F{GROUPVT}==null || ("0".equals($F{KTC}))||($F{THUOCVATTUID}.length()>0 && $F{TY_LE}!=100.0)]]></printWhenExpression>
				</reportElement>
				<box rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?new DecimalFormat("#,##0.00").format($F{NGUOIBENHDCT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."):"<font color=green>" + new DecimalFormat("#,##0.00").format($F{NGUOIBENHDCT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") + "</font>")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="505" y="0" width="74" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a4a835aa-0d49-45b3-937d-8184577a6c7e"/>
				<box rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[("0".equals($F{CHECKDVTT})?new DecimalFormat("#,##0.00").format($F{THANHTIENBH}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", "."):"<font color=green>" + new DecimalFormat("#,##0.00").format($F{THANHTIENBH}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".") + "</font>")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="701" y="0" width="45" height="15" uuid="6dc7e77c-5e91-456e-b370-f07b8a0dd521"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="579" y="0" width="64" height="15" uuid="0f5cec7f-83cf-495b-9f6c-165f8f46b56c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="746" y="0" width="64" height="15" uuid="a2689c7a-54ca-4357-9439-c3609331b225"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="643" y="0" width="58" height="15" uuid="9b787d26-cbca-4979-b2c5-465f0982ce8a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="15" splitType="Stretch">
			<textField evaluationTime="Report">
				<reportElement positionType="Float" mode="Transparent" x="715" y="2" width="95" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="c2af1578-6e45-48b9-befa-0bc70796bd74"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="520" y="2" width="195" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="6cc081d4-c2bd-4b35-90dc-99c32ff1004e"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{PAGE_NUMBER}+"/"]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="356" splitType="Immediate">
			<frame>
				<reportElement positionType="Float" x="0" y="1" width="810" height="355" uuid="ab150042-b5ed-453c-a8ae-06bfc1f96561"/>
				<elementGroup>
					<staticText>
						<reportElement positionType="Float" x="72" y="233" width="200" height="13" uuid="b2cd120b-593d-453b-981f-79e71b0875dd"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11" isItalic="true"/>
						</textElement>
						<text><![CDATA[Ký, ghi rõ họ tên]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="72" y="221" width="200" height="13" uuid="9f9ceafc-6649-4f95-a2e8-4e7d8c68e592"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11"/>
						</textElement>
						<text><![CDATA[NGƯỜI LẬP BẢNG KÊ]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="645" y="292" width="24" height="13" uuid="0549e7c5-ecab-4189-a72a-968f22119902"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11"/>
						</textElement>
						<text><![CDATA[năm]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="567" y="209" width="26" height="13" uuid="1e7413d4-**************-fc06ab29f753"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11"/>
						</textElement>
						<text><![CDATA[Ngày]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="653" y="209" width="24" height="13" uuid="f73d2a17-4673-4726-ac4e-b348c63305e0"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11"/>
						</textElement>
						<text><![CDATA[năm]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="70" y="321" width="200" height="13" uuid="c9f48b0c-8d1d-43a6-aa05-83e16653868b"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11" isItalic="true"/>
						</textElement>
						<text><![CDATA[Ký, ghi rõ họ tên]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="600" y="292" width="28" height="13" uuid="d9636703-4d51-4074-9f40-185a59a4583f"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11"/>
						</textElement>
						<text><![CDATA[tháng]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="608" y="209" width="28" height="13" uuid="11e80d55-cf23-45d0-8230-424558f4baff"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11"/>
						</textElement>
						<text><![CDATA[tháng]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="559" y="292" width="26" height="13" uuid="9e40d3d7-0f4c-4885-9c78-143ea9500636"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11"/>
						</textElement>
						<text><![CDATA[Ngày]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="520" y="306" width="200" height="13" uuid="71d8bf1a-3c78-4e96-89cd-5d70efdd9cbe"/>
						<textElement textAlignment="Center">
							<font fontName="Times New Roman" size="11"/>
						</textElement>
						<text><![CDATA[GIÁM ĐỊNH BHYT]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="520" y="319" width="200" height="13" uuid="d836b1a0-e6e0-4cea-8b0d-8166d2575c27"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11" isItalic="true"/>
						</textElement>
						<text><![CDATA[Ký, ghi rõ họ tên]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="522" y="234" width="200" height="13" uuid="d6177715-08e8-40d5-9999-4f1311903124"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11" isItalic="true"/>
						</textElement>
						<text><![CDATA[Ký, ghi rõ họ tên]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="70" y="307" width="200" height="13" uuid="022dd57f-a9e8-4601-a1ca-8b28c1d7484c"/>
						<textElement textAlignment="Center" verticalAlignment="Bottom">
							<font fontName="Times New Roman" size="11" isBold="true"/>
						</textElement>
						<text><![CDATA[XÁC NHẬN CỦA NGƯỜI BỆNH]]></text>
					</staticText>
					<staticText>
						<reportElement positionType="Float" x="522" y="222" width="200" height="13" uuid="8ba2df6b-b14a-427f-936f-4ecb6d380b14"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font fontName="Times New Roman" size="11"/>
						</textElement>
						<text><![CDATA[KẾ TOÁN VIỆN PHÍ]]></text>
					</staticText>
				</elementGroup>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="0" y="14" width="810" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="373f4dfc-511a-4900-87fc-df92133370a1"/>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["(Viết bằng chữ: "+($F{TONGTIEN}.length()>1?$F{TONGTIEN}.substring(0,1).toUpperCase()+$F{TONGTIEN}.substring(1):$F{TONGTIEN})+" đồng)"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="290" y="83" width="30" height="13" uuid="1ff7368f-cd1d-4b50-890b-f55a025ce3d1"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="213" y="0" width="111" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="549246f6-0123-4012-8774-a5af84d3a843"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{reportc_thanhtienbv}.equals(0.0)?null:new DecimalFormat("#,##0").format($V{reportc_thanhtienbv}.doubleValue()).replace(",", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="289" y="97" width="30" height="13" uuid="78d1daf6-d072-4e7b-80d1-ae5c37f95a40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="302" y="42" width="30" height="13" uuid="32e34b03-a942-4021-a736-367e83f18c87"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="324" y="0" width="30" height="13" uuid="6e8f5fa2-3c5b-4008-aafd-5aad3737d518"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="97" width="187" height="13" uuid="1ddc2396-e883-410e-9b39-7bef56fc9023"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[  + Các khoản phải trả khác:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="191" y="83" width="97" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="f3131f7b-023c-4980-b90c-1145e0645c77"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{reportc_nbdct}<=0.0?null:new DecimalFormat("#,##0.00").format($V{reportc_nbdct}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="190" y="97" width="97" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="dfb62056-02ba-4459-99f8-07e7e09e4bde"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{reportc_nguoibenh}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="0" y="0" width="213" height="13" uuid="727a41f8-09f2-4a72-80ef-3a36620c8949"/>
					<textElement>
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[Tổng chi phí lần khám bệnh/cả đợt điều trị:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="222" y="42" width="80" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="e6ef0c09-b2bd-47e9-8dce-f822c9c391a2"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[($V{reportc_quybhyt}.equals(0.0)||$V{reportc_quybhyt}<0)?null:new DecimalFormat("#,##0.00").format($V{reportc_quybhyt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="1" y="83" width="186" height="13" uuid="bc57d053-df8b-4230-b308-3205c8457542"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[  + Cùng chi trả trong phạm vi BHYT:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="70" width="143" height="13" uuid="4945697c-9983-4148-a00d-95aa031e66f8"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[- Người bệnh trả, trong đó:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="42" width="229" height="13" uuid="5dbe2900-29b3-4495-8168-61e6397d2eff"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[- Quỹ BHYT thanh toán theo giá dịch vụ y tế:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="28" width="143" height="13" uuid="f67d2bc2-9b20-4a3d-9bbf-172d99963040"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[Trong đó số tiền do:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="111" width="72" height="13" uuid="7c20852e-22a0-4c63-ae48-2650d0d7e0ee"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[- Nguồn khác:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="170" y="111" width="30" height="13" uuid="d9261665-63fd-4215-a2d6-83f2121fe284"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="73" y="111" width="97" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="9411920b-265c-4c32-ace8-18ddcdc7a8b9"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{reportc_khac}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($V{reportc_khac}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="3" y="334" width="345" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="4557f697-98b5-46f2-92a3-d26f2244c5ce"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["Tôi đã nhận.............Phim................"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="70" y="293" width="200" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="eff851d1-26e9-4a1c-a63d-affedd5ad73a"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGUOITHU_NGT}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="522" y="278" width="200" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="6caa169a-d07b-4146-bca3-c4e65d237e1f"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGUOITHU}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="1" y="194" width="186" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="8e9477a9-b90a-4fc7-948c-18786687f69d"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{reportc_nbdct}+$V{reportc_nguoibenh}-$F{MIENGIAM}-$F{TAMUNG}-$F{DANOP}>0.0?"- Số tiền còn lại phải thanh toán:":"- Thừa hoàn trả:"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="290" y="180" width="30" height="13" uuid="04d206f9-71a4-4ef7-9c1d-e2770899d237"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="1" y="180" width="187" height="13" uuid="2cc3c382-f0f3-4319-94bd-38b5299469ba"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[- Bệnh nhân đã nộp:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="191" y="194" width="97" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="f70660b6-f320-47de-93cf-e332c7e89967"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[($V{reportc_nbdct}+$V{reportc_nguoibenh})-($F{TAMUNG}+$F{DANOP}+$F{MIENGIAM})>0.0?new DecimalFormat("#,##0.00").format($V{reportc_nbdct}+$V{reportc_nguoibenh}-$F{TAMUNG}-$F{DANOP}-$F{MIENGIAM}).replace(",", "x").replace(".", ",").replace("x", "."):new DecimalFormat("#,##0.00").format($F{TAMUNG}+$F{DANOP}+$F{MIENGIAM}-$V{reportc_nbdct}-$V{reportc_nguoibenh}).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="290" y="194" width="30" height="13" uuid="ad4551d0-cb3f-475e-9052-b39a230da237"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="191" y="180" width="97" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="a40b9219-f041-4eb2-955f-7e8da785b5d0"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DANOP}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($F{DANOP}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="481" y="41" width="82" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="59192f02-647d-4684-be0d-1eff58de2b87"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TAMUNG}.equals(0.0)?null:new DecimalFormat("#,##0.00").format($F{TAMUNG}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="564" y="41" width="30" height="13" uuid="1a560e5d-966c-4ac8-b3b8-59bba4803125"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="391" y="41" width="90" height="13" uuid="8a77dc69-8ce3-44ad-a26d-e258b025c7d3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[Tổng tạm ứng:]]></text>
				</staticText>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="591" y="209" width="17" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="71c5d0d2-a815-446b-b112-ef7f400373f3"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(0,2)]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="583" y="292" width="17" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="5df51713-ecc2-4ca1-a00b-16b89eaf9662"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(0,2)]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="634" y="209" width="17" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="fb6fde48-a7a2-44c8-8b50-3f2df7d0c1f3"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(2,4)]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="626" y="292" width="19" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="50b7ad9e-474c-4905-9af3-423466b2ded0"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(2,4)]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="677" y="209" width="35" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="899c1ae9-b7e1-47b8-990c-a4efe0f1d69f"/>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(4,8)]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="671" y="292" width="35" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="8aafd76f-a59e-4ef9-be98-ee5284bda06c"/>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(4,8)]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="1" y="56" width="793" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="da002155-177c-499f-9bf0-fa0287079188"/>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{LOAIKB}.equals("1")||$F{LOAIKB}.equals("2"))?"- Quỹ BHYT thanh toán theo định suất: Thanh toán vào cuối kỳ (cuối quý)":"-  Quỹ BHYT thanh toán theo DRG: Thanh toán vào cuối kỳ (cuối quý)"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="284" y="125" width="525" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f4d460a1-a1e4-42ce-b26d-3f83e1cc1973">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["(Viết bằng chữ: "+($F{MGCOVIDCHU}.length()>1?$F{MGCOVIDCHU}.substring(0,1).toUpperCase()+$F{MGCOVIDCHU}.substring(1):$F{MGCOVIDCHU})+" đồng)"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="169" y="125" width="93" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="5416c254-c1ac-4f63-9a0b-37087424eafe">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MGCOVID}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="1" y="125" width="179" height="13" isRemoveLineWhenBlank="true" uuid="dfe21993-8907-4aa6-a3e1-1eaef5943235">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[+ Nguồn ngân sách nhà nước chi trả:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="262" y="125" width="22" height="13" isRemoveLineWhenBlank="true" uuid="6421c919-f58c-4b02-9c39-2850abe7d33e">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="131" y="139" width="63" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="246647c0-27eb-4ff2-aa51-5850c95c6227">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MGTOANCAU}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="192" y="139" width="22" height="13" isRemoveLineWhenBlank="true" uuid="4c65e7b7-1d01-40e6-b2ec-41ed5315869b">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="139" width="142" height="13" isRemoveLineWhenBlank="true" uuid="17ea2180-e20f-4cf2-a46b-3113d1a1d967">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[+ Nguồn viện trợ nước ngoài:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="215" y="139" width="593" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="cc8cb33c-ce7d-4c11-a254-2c181debaa84">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["(Viết bằng chữ: "+($F{MGTOANCAUCHU}.length()>1?$F{MGTOANCAUCHU}.substring(0,1).toUpperCase()+$F{MGTOANCAUCHU}.substring(1):$F{MGTOANCAUCHU})+" đồng)"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="313" y="153" width="64" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="0e37f37a-dd7d-497a-9f1a-f3857a8ae9b7">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MGTINH}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="376" y="153" width="22" height="13" isRemoveLineWhenBlank="true" uuid="65fdffc0-fd2e-4300-a7b9-e10dff8510ad">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="153" width="319" height="13" isRemoveLineWhenBlank="true" uuid="633bc089-4386-45d2-984b-6b9188314cee">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[+ Nguồn tài trợ của các tổ chức, cơ quan, đơn vị, cá nhân trong nước:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="394" y="153" width="415" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="a1bc6381-56b5-405e-bb38-9b6689bfc137">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["(Viết bằng chữ: "+($F{MGTINHCHU}.length()>1?$F{MGTINHCHU}.substring(0,1).toUpperCase()+$F{MGTINHCHU}.substring(1):$F{MGTINHCHU})+" đồng)"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="123" y="167" width="105" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="543b4b59-8b3d-479d-af98-65ea823238cb">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{MGKHAC}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="228" y="167" width="22" height="13" isRemoveLineWhenBlank="true" uuid="082f547c-1988-489a-b551-62d81e3fc4ad">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[đồng]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" mode="Transparent" x="250" y="167" width="558" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="fa56dcbf-8c74-4385-b3ff-1859127ac382">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["(Viết bằng chữ: "+($F{MGKHACCHU}.length()>1?$F{MGKHACCHU}.substring(0,1).toUpperCase()+$F{MGKHACCHU}.substring(1):$F{MGKHACCHU})+" đồng)"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="0" y="167" width="154" height="13" isRemoveLineWhenBlank="true" uuid="651ec744-18c4-4b3e-a52b-1e93ad4f158c">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<text><![CDATA[+ Các nguồn còn lại chi trả:]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="200" y="111" width="609" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="4909ef1a-a701-4b5c-abcd-f5423c22def0">
						<printWhenExpression><![CDATA[$F{INBKNGUON}==1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["(Viết bằng chữ: "+($F{MIENGIAMDV}.length()>1?$F{MIENGIAMDV}.substring(0,1).toUpperCase()+$F{MIENGIAMDV}.substring(1):$F{MIENGIAMDV})+" đồng)"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</summary>
</jasperReport>

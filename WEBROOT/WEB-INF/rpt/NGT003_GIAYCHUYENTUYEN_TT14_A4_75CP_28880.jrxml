<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT003_GIAYCHUYENTUYEN_TT14_A4_BHYT_ND146_954" language="groovy" pageWidth="655" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="575" leftMargin="40" rightMargin="40" topMargin="7" bottomMargin="7" uuid="6012d0bd-f305-4d4b-b38a-8f001e774017">
	<property name="ireport.zoom" value="1.4641000000000013"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="hospital_code" class="java.lang.String"/>
	<parameter name="hotenbacsi" class="java.lang.String"/>
	<parameter name="tieude" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="khambenhid" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call ngt_ingiaychuyenvien_bhyt_75cp($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{khambenhid},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="DOITUONGBENHNHANID" class="java.math.BigDecimal"/>
	<field name="SOHOSO" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TEN_BENH_NHAN" class="java.lang.String"/>
	<field name="GT" class="java.lang.String"/>
	<field name="NAM" class="java.lang.String"/>
	<field name="NU" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="DIA_CHI" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="QUOCGIA" class="java.lang.String"/>
	<field name="TEN_NGHE_NGHIEP" class="java.lang.String"/>
	<field name="NOILAMVIEC" class="java.lang.String"/>
	<field name="TUNGAY" class="java.lang.String"/>
	<field name="DENNGAY" class="java.lang.String"/>
	<field name="MA_BHYT" class="java.lang.String"/>
	<field name="DAUHIEULAMSANG" class="java.lang.String"/>
	<field name="KETQUAXN_DS" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="CHANDOAN_KEMTHEO" class="java.lang.String"/>
	<field name="PP_TTPT_THUOC_DADUNG" class="java.lang.String"/>
	<field name="TINHTRANGBENHNHAN" class="java.lang.String"/>
	<field name="HUONGDIEUTRI" class="java.lang.String"/>
	<field name="NGAYCHUYEN" class="java.lang.String"/>
	<field name="NGAYCHUYEN_GIO" class="java.lang.String"/>
	<field name="NGAYCHUYEN_PHUT" class="java.lang.String"/>
	<field name="NGAYCHUYEN_NGAY" class="java.lang.String"/>
	<field name="NGAYCHUYEN_THANG" class="java.lang.String"/>
	<field name="NGAYCHUYEN_NAM" class="java.lang.String"/>
	<field name="PHUONGTIENVANCHUYEN" class="java.lang.String"/>
	<field name="HOTENNGUOIDUADI" class="java.lang.String"/>
	<field name="SOVAOSOCHUYENTUYEN" class="java.lang.String"/>
	<field name="TENBENHVIENCHUYENDEN" class="java.lang.String"/>
	<field name="NGOAIKIEU" class="java.lang.String"/>
	<field name="TUYENBENHVIEN" class="java.lang.String"/>
	<field name="NOIDANGKY_KCB" class="java.lang.String"/>
	<field name="DIEUTRI" class="java.lang.String"/>
	<field name="TUYENDIEUTRI" class="java.lang.String"/>
	<field name="DUDIEUKIEN0" class="java.lang.String"/>
	<field name="DUDIEUKIEN1" class="java.lang.String"/>
	<field name="DUDIEUKIEN2" class="java.lang.String"/>
	<field name="KODUDIEUKIEN" class="java.lang.String"/>
	<field name="TENNGUOITHAMQUYEN" class="java.lang.String"/>
	<field name="NGAYKY" class="java.lang.String"/>
	<field name="NAMBC" class="java.math.BigDecimal"/>
	<field name="MAKHOA" class="java.lang.String"/>
	<field name="MA_KCBBD" class="java.lang.String"/>
	<field name="TEN_NHANVIEN" class="java.lang.String"/>
	<field name="TENKHOA" class="java.lang.String"/>
	<field name="MAPHONG" class="java.lang.String"/>
	<field name="TENPHONG" class="java.lang.String"/>
	<field name="NGAYDIEUTRI_BD" class="java.lang.String"/>
	<field name="GHICHU_KHAMBENH" class="java.lang.String"/>
	<field name="SOCHUYENVIEN" class="java.math.BigDecimal"/>
	<field name="NGAY_RAVIEN" class="java.lang.String"/>
	<field name="TENBENHVIEN_TN" class="java.lang.String"/>
	<field name="TUYENBENHVIEN_TN" class="java.lang.String"/>
	<field name="DIACHIBVCHUYENDEN" class="java.lang.String"/>
	<field name="GHICHU_BENHCHINH" class="java.lang.String"/>
	<field name="GHICHU_BENHCHINH_RAVIEN" class="java.lang.String"/>
	<field name="GHICHU_BENHKEMTHEO" class="java.lang.String"/>
	<field name="LOAITIEPNHANID" class="java.math.BigDecimal"/>
	<field name="KHAMBENH_MACH" class="java.lang.String"/>
	<field name="KHAMBENH_NHIETDO" class="java.lang.String"/>
	<field name="KHAMBENH_HUYETAP_HIGH" class="java.lang.String"/>
	<field name="KHAMBENH_HUYETAP_LOW" class="java.lang.String"/>
	<field name="KHAMBENH_NHIPTHO" class="java.lang.String"/>
	<field name="KHAMBENH_CANNANG" class="java.lang.String"/>
	<field name="KHAMBENH_CHIEUCAO" class="java.lang.String"/>
	<field name="NAMSINH" class="java.lang.String"/>
	<field name="CHECKBHYT" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="604" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="170" height="18" uuid="548ca402-599e-42b9-b376-4379301a0227"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase().replace("SYT","SỞ Y TẾ")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="171" y="0" width="246" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="b65fc4d7-b6c8-46c5-893c-1c35f7942b6c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="186" y="22" width="216" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e9407b28-da11-4fd8-9352-85b1e3be779c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="441" y="19" width="134" height="35" forecolor="#000000" backcolor="#FFFFFF" uuid="deed16e0-054b-48c0-9fe1-4f224bd173d2"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Vào sổ chuyển tuyến số: .........]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="82" width="575" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="e9d3e21b-547b-44cc-b249-4986eca3bfd0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Kính gửi: "+$F{TENBENHVIENCHUYENDEN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="1" y="130" width="125" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="86bd8d9c-42a3-4809-8d68-f04296d7a1ee"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Họ và tên người bệnh:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="1" y="166" width="84" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="5653fc88-5fd1-40e9-8cc7-bafea5df6484"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Dân tộc:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="1" y="184" width="84" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="aa0b9bfc-ad4e-46fb-912d-e1ab9a1035e1"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Nghề nghiệp:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="1" y="255" width="171" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="847ab45d-80ce-41b5-9dc7-0a796e4ce547"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Đã được khám bệnh/ điều trị:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="2" y="305" width="573" height="21" uuid="549827f8-3bff-4db9-8cda-a7ea821c5143"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TÓM TẮT BỆNH ÁN]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="126" y="130" width="183" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="2161dc67-d8c7-4eca-9195-eba1702a84b1"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_BENH_NHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="365" y="130" width="53" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="29785e19-3b6b-4159-8b1f-c53ad8be5b50"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GT}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="309" y="130" width="55" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="3b3d0bd8-e011-495b-ad94-77228c3e503a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Nam/ Nữ:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="418" y="130" width="60" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="e18de317-9d5e-43ea-9dd5-aa447c8ee415"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Năm sinh:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="1" y="148" width="68" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="08eaa10e-0294-4eb6-90bb-e5c53bfe88a9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Địa chỉ:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="86" y="166" width="203" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a33b4c64-11e7-4044-ba64-e6c122d0cb06"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_DANTOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="86" y="184" width="223" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e46a56d3-96bc-457a-a1af-c01177715acb"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_NGHE_NGHIEP}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="309" y="184" width="66" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="f23ef947-de47-48e4-a90f-3dd04ae4730a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Nơi làm việc:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="187" y="219" width="388" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="01cfd0f2-3588-4cf3-8839-3a6f8487ff8a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["đến ngày "+$F{DENNGAY}.substring( 0, 2 )+" tháng "+$F{DENNGAY}.substring( 3, 5 )+" Năm "+$F{DENNGAY}.substring( 6, 10 )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="201" width="126" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="d3f6a327-8154-4286-b155-5666382f9142"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Số thẻ bảo hiểm y tế: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="478" y="130" width="68" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="769e74bf-c6ca-46bf-a0a6-2cadd243038e"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NAMSINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="-1" y="364" width="576" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="f03701dc-e416-4000-b17f-29886f773e7f"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán: " +($F{CHANDOAN}.equals("")?"":$F{CHANDOAN})
+($F{GHICHU_BENHCHINH}==null?"":" ("+$F{GHICHU_BENHCHINH}+")")
+($F{CHANDOAN_KEMTHEO}==null?"":" ;"+$F{CHANDOAN_KEMTHEO})
+($F{GHICHU_BENHKEMTHEO}==null?"":" ("+$F{CHANDOAN_KEMTHEO}+")")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="382" width="575" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="d221eefd-7f2f-4585-8a79-cf6941af7ef7"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Phương pháp, thủ thuật, kỹ thuật, thuốc đã sử dụng trong điều trị: "
+ ($F{PP_TTPT_THUOC_DADUNG}==null?"":$F{PP_TTPT_THUOC_DADUNG})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="-1" y="400" width="576" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="7b2fbb5d-e4e3-40a8-af5b-0b7ad8a516c6"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Tình trạng người bệnh lúc chuyển tuyến: "
+ ($F{TINHTRANGBENHNHAN}==null?"":$F{TINHTRANGBENHNHAN})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="533" width="575" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d194daa5-3ee3-427a-ab39-07449625c055"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Hướng điều trị: "
+ ($F{HUONGDIEUTRI}==null?"":$F{HUONGDIEUTRI})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="551" width="114" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="5dd0f63e-fdab-4844-b373-df39aadc0135"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chuyển tuyến hồi:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="569" width="150" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a132c1f5-7095-43fb-9130-d80b425a3174"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Phương tiện vận chuyển:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="-1" y="328" width="576" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="f2c36ee0-a355-4111-b4f3-dba1a9f5a9d3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Dấu hiệu lâm sàng: "+ ($F{DAUHIEULAMSANG}==null?"":$F{DAUHIEULAMSANG})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="-1" y="346" width="576" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="44b73daf-390e-4194-a393-54231ce6022d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Kết quả xét nghiệm, cận lâm sàng: "
+ ($F{KETQUAXN_DS}==null?"":$F{KETQUAXN_DS})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="587" width="575" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="0338bff5-8d20-43f4-a8b2-7fa3534e1e51"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ tên, chức danh, trình độ chuyên môn của người hộ tống (nếu có): "
+ ($F{TENNGUOITHAMQUYEN}==null?"":$F{TENNGUOITHAMQUYEN})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="-1" y="418" width="576" height="31" forecolor="#000000" backcolor="#FFFFFF" uuid="1206c52c-7b53-4f2e-9c26-884a9ba18c23"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Lý do chuyển tuyến: Khoanh tròn vào mục 1 hoặc 2 lý do chuyển tuyến. Trường hợp chọn mục 1, đánh dấu (X) vào ô tương ứng. "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="377" y="184" width="182" height="15" uuid="0ce6ef6e-eac7-4d79-814c-4a94437f93f0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOILAMVIEC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="18" width="170" height="18" uuid="1bec8bb2-8e62-4326-b91b-e30dc9ec9ffb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="1" y="113" width="574" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="3211bdaf-346c-44f6-80c6-dd2409608cd1"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Cơ sở khám bệnh, chữa bệnh: "
+$P{org_name}
+" trân trọng giới thiệu: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="150" y="569" width="425" height="15" uuid="c04b00a3-94b1-4a7d-84ed-83613dd576bf"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHUONGTIENVANCHUYEN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="69" y="148" width="490" height="16" uuid="69ec6973-c307-4851-9154-9e9fef8fcd32"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIA_CHI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="36" width="170" height="18" uuid="9f271707-cdcf-4741-b820-e3d84c70d8c8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Số:"+($F{SOCHUYENVIEN}==null?"..............":$F{SOCHUYENVIEN})+"/"
+new SimpleDateFormat("yyyy").format(new java.util.Date())
+"/GCT"]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="249" y="42" width="100" height="1" uuid="91807663-507a-458b-9222-5be52619d2de"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="114" y="551" width="461" height="16" uuid="93b3c233-9a66-468b-b892-6c18efbf4ed8"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYCHUYEN_GIO}+" giờ "+$F{NGAYCHUYEN_PHUT}+" phút, ngày "+$F{NGAYCHUYEN_NGAY}+" tháng "+$F{NGAYCHUYEN_THANG}+" năm "+$F{NGAYCHUYEN_NAM}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="329" y="273" width="72" height="15" uuid="18ab4ab1-912d-4df0-9f9a-4ad69db27474"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYDIEUTRI_BD}.substring(0,10)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="276" y="273" width="53" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e03d06f6-76d4-4f4a-a9fc-043938357cc5"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[ Từ ngày:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="441" y="0" width="134" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="6348bc42-831e-4aca-84dd-c7bde47e28cb"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Số Hồ sơ: " + $F{SOHOSO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="308" y="166" width="240" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="7f7d0a5e-4199-4a38-9257-cee40e068b8a"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Quốc tịch: " + $F{QUOCGIA}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="134" y="201" width="28" height="16" uuid="c356d132-abd5-40c5-b4a1-77c7dfa45cba"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(0,2)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="162" y="201" width="29" height="16" uuid="b8030ef3-2715-471e-b809-3a62d936c01c"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(2,3)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="191" y="201" width="28" height="16" uuid="f5b7aa40-82f7-489d-8ab4-5e8790134136"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(3,5)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="219" y="201" width="102" height="16" uuid="2f94110a-2adf-4c47-9c77-1aa149c2093a"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(5,15)]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="1" y="273" width="275" height="15" uuid="bb2c04b8-3f82-4df5-b5f0-f8129fb5d66e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Tại: "
+$P{org_name}
+ " (Tuyến "
+ ($F{TUYENBENHVIEN}==null?"     ":$F{TUYENBENHVIEN})
+ ")"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="402" y="273" width="53" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="dd755571-724c-49e1-b5bf-c32f0439e68d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Đến ngày: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="456" y="273" width="92" height="15" uuid="9ef25506-6d3c-4e0b-86b7-a7fb08108f96"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_RAVIEN}.substring(0,10)]]></textFieldExpression>
			</textField>
			<ellipse>
				<reportElement positionType="Float" isPrintRepeatedValues="false" mode="Transparent" x="4" y="512" width="21" height="20" isPrintInFirstWholeBand="true" uuid="dc97eb2a-9137-4d60-9172-bd05fa166108">
					<printWhenExpression><![CDATA[!$F{KODUDIEUKIEN}.equals( null )]]></printWhenExpression>
				</reportElement>
			</ellipse>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="11" y="512" width="564" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="b4ac35f7-40b8-437e-9abd-5945c048a224"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["2.  Theo yêu cầu của người bệnh hoặc người đại diện hợp pháp của người bệnh."]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="11" y="452" width="564" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="1f827c51-8d94-4b45-b4a7-864052dfafd3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["1.  Đủ điều kiện chuyển tuyến: "]]></textFieldExpression>
			</textField>
			<ellipse>
				<reportElement positionType="Float" isPrintRepeatedValues="false" mode="Transparent" x="4" y="452" width="21" height="20" isPrintInFirstWholeBand="true" uuid="a54bdf90-2d44-4a1a-b748-50d779b78d68">
					<printWhenExpression><![CDATA[$F{KODUDIEUKIEN}.equals( null )]]></printWhenExpression>
				</reportElement>
			</ellipse>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="1" y="290" width="574" height="15" uuid="db573313-6eea-4092-86aa-1e653ac3ed71"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Tại: "
+($F{TENBENHVIEN_TN}==null?"                                                                            ":($F{TENBENHVIEN_TN})
+ " (Tuyến "
+ ($F{TUYENBENHVIEN_TN}==null?"      ":$F{TUYENBENHVIEN_TN})
+ ")")
+"       Từ ngày:                           Đến ngày:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="-1" y="56" width="576" height="26" forecolor="#000000" backcolor="#FFFFFF" uuid="b92ee63a-1a30-4efb-a351-99c72dbc94cd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["GIẤY CHUYỂN TUYẾN KHÁM BỆNH, CHỮA BỆNH BẢO HIỂM Y TẾ"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="1" y="237" width="68" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="a97eb14a-6c5a-4f92-9fbb-67170a96ab6a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Hết thời hạn: ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="401" y="236" width="16" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="3b168929-778e-4f89-83f6-9635090da900"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="223" y="237" width="164" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="612e8ee5-805f-4372-9891-b72d061d462b"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Không xác định được thời hạn:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="76" y="236" width="16" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="fb1e638a-2b6c-42e8-8346-13a99752be19"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{CHECKBHYT} !=null?"X":
     "")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="11" y="472" width="210" height="16" uuid="ac132afc-4ff7-49c2-a084-2229940e033c"/>
				<textElement>
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<text><![CDATA[a) Phù hợp với quy định chuyển tuyến(*):]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="11" y="492" width="351" height="16" uuid="2366ee52-ea95-4106-a7d8-86650043552b"/>
				<textElement>
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<text><![CDATA[b, Không phù hợp với khả năng đáp ứng của cơ sở khám, chữa bệnh.]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="249" y="472" width="16" height="16" uuid="8776be07-200f-4ed3-a00c-22ed28734cb1"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{DUDIEUKIEN0} !=null?"X":
($F{DUDIEUKIEN1} !=null?"":
 ($F{DUDIEUKIEN2} !=null?"":
     "")
)
)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="385" y="491" width="16" height="16" uuid="1b83db3d-03f8-47fe-939a-66ee73178754"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" markup="none">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{DUDIEUKIEN0} !=null?"":
($F{DUDIEUKIEN1} !=null?"X":
 ($F{DUDIEUKIEN2} !=null?"X":
     "")
)
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="1" y="219" width="186" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="ed9f2a41-252b-462f-931b-8728977797f8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Thời hạn sử dụng thẻ bảo hiểm y tế]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="173" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="33" y="16" width="137" height="30" uuid="f57c14db-a8b0-40ca-b0ae-8282db66de04"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[BÁC SỸ, Y SỸ KHÁM, ĐIỀU TRỊ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="329" y="16" width="193" height="30" uuid="b3ef6103-4724-4507-8eb6-0c74bb6b360f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI CÓ THẨM QUYỀN CHUYỂN TUYẾN]]></text>
			</staticText>
			<staticText>
				<reportElement x="44" y="50" width="117" height="16" uuid="fb3f2cd6-7232-47a1-aeb9-8e1ea0f2ce83"/>
				<textElement>
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký và ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="292" y="50" width="278" height="16" uuid="cc50cf08-034a-4cc1-81c0-10b1d2813abe"/>
				<textElement>
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<text><![CDATA[  (Ký tên, đóng dấu của cơ sở khám bệnh, chữa bệnh)]]></text>
			</staticText>
			<staticText>
				<reportElement x="-1" y="130" width="576" height="43" uuid="11b864e9-5a1d-42b9-85e0-6f1054438b6b"/>
				<textElement>
					<font fontName="Times New Roman" size="12" isItalic="false"/>
				</textElement>
				<text><![CDATA[(*). Người bệnh đi khám bệnh, chữa bệnh đúng tuyến chuyên môn kỹ thuật trong khám bệnh, chữa bệnh bao gồm được chuyển lên tuyến trên hoặc chuyển về tuyến dưới hoặc chuyển giữa các cơ sở khám bệnh, chữa bệnh trong cùng tuyến theo quy định của pháp luật.]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="292" y="1" width="280" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="7b060ac8-b635-4d19-8ad7-3b15b7858e7f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày "+$F{NGAY_RAVIEN}.substring( 0, 2 )+" Tháng "+$F{NGAY_RAVIEN}.substring( 3, 5 )+" Năm "+$F{NGAY_RAVIEN}.substring( 6, 10 )]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

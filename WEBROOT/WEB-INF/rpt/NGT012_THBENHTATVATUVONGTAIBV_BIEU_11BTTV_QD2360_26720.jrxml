<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="156" pageWidth="1050" pageHeight="1190" whenNoDataType="AllSectionsNoDetail" columnWidth="1010" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="f34298f2-0056-4e24-99f9-2e17c895d9e0">
	<property name="ireport.zoom" value="1.5000000000000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="tungay" class="java.util.Date"/>
	<parameter name="denngay" class="java.util.Date"/>
	<parameter name="oral_result" class="java.sql.ResultSet" isForPrompting="false"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call thbenhtatvatuvong_26720($P{[SCH]},$P{[HID]},$P{tungay},$P{denngay},$P{oral_result})}]]>
	</queryString>
	<field name="STT" class="java.lang.String"/>
	<field name="TEN" class="java.lang.String"/>
	<field name="MA_ICD" class="java.lang.String"/>
	<field name="KB_TS" class="java.math.BigDecimal"/>
	<field name="KB_NU" class="java.math.BigDecimal"/>
	<field name="KB_TE" class="java.math.BigDecimal"/>
	<field name="KB_TV" class="java.math.BigDecimal"/>
	<field name="NT_TS_MAC_TS" class="java.math.BigDecimal"/>
	<field name="NT_TS_MAC_NU" class="java.math.BigDecimal"/>
	<field name="NT_TS_TV_TS" class="java.math.BigDecimal"/>
	<field name="NT_TS_TV_NU" class="java.math.BigDecimal"/>
	<field name="NT_TE_MAC_TS" class="java.math.BigDecimal"/>
	<field name="NT_TE_MAC_5T" class="java.math.BigDecimal"/>
	<field name="NT_TE_TV_TS" class="java.math.BigDecimal"/>
	<field name="NT_TE_TV_5T" class="java.math.BigDecimal"/>
	<field name="NHOM_BENH" class="java.lang.String"/>
	<field name="kb_xv" class="java.math.BigDecimal"/>
	<field name="nt_ts_mac_xv_ts" class="java.math.BigDecimal"/>
	<field name="nt_ts_mac_xv_nu" class="java.math.BigDecimal"/>
	<variable name="NHOM_KBTS" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{KB_TS}]]></variableExpression>
	</variable>
	<variable name="NHOM_KBNU" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{KB_NU}]]></variableExpression>
	</variable>
	<variable name="NHOM_KBTE" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{KB_TE}]]></variableExpression>
	</variable>
	<variable name="NHOM_KBTV" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{KB_TV}]]></variableExpression>
	</variable>
	<variable name="NHOM_NTTSMAC" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TS_MAC_TS}]]></variableExpression>
	</variable>
	<variable name="NHOM_TSMACNU" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TS_MAC_NU}]]></variableExpression>
	</variable>
	<variable name="NHOM_TVTS" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TS_TV_TS}]]></variableExpression>
	</variable>
	<variable name="NHOM_TEMACTS" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TE_MAC_TS}]]></variableExpression>
	</variable>
	<variable name="NHOM_TEMAC5" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TE_MAC_5T}]]></variableExpression>
	</variable>
	<variable name="NHOM_TETVTS" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TE_TV_TS}]]></variableExpression>
	</variable>
	<variable name="NHOM_TETV5" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TE_TV_5T}]]></variableExpression>
	</variable>
	<variable name="NHOM_TVNU" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TS_TV_NU}]]></variableExpression>
	</variable>
	<variable name="RP_KBTS" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{KB_TS}]]></variableExpression>
	</variable>
	<variable name="RP_KBNU" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{KB_NU}]]></variableExpression>
	</variable>
	<variable name="RP_KBTE" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{KB_TE}]]></variableExpression>
	</variable>
	<variable name="RP_KBTV" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{KB_TV}]]></variableExpression>
	</variable>
	<variable name="RP_NTMACTS" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TS_MAC_TS}]]></variableExpression>
	</variable>
	<variable name="RP_NTMACNU" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TS_MAC_NU}]]></variableExpression>
	</variable>
	<variable name="RP_NTTVTS" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TS_TV_TS}]]></variableExpression>
	</variable>
	<variable name="RP_NTTVNU" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TS_TV_NU}]]></variableExpression>
	</variable>
	<variable name="RP_NTTEMACTS" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TE_MAC_TS}]]></variableExpression>
	</variable>
	<variable name="RP_NTTEMAC5" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TE_MAC_5T}]]></variableExpression>
	</variable>
	<variable name="RP_NTTETVTS" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TE_TV_TS}]]></variableExpression>
	</variable>
	<variable name="RP_NTTETV5" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{NT_TE_TV_5T}]]></variableExpression>
	</variable>
	<variable name="NHOM_KBXV" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{kb_xv}]]></variableExpression>
	</variable>
	<variable name="RP_KBXV" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{kb_xv}]]></variableExpression>
	</variable>
	<variable name="NHOM_NTMACXV" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{nt_ts_mac_xv_ts}]]></variableExpression>
	</variable>
	<variable name="NHOM_NTMACXVNU" class="java.math.BigDecimal" resetType="Group" resetGroup="GROUP_NHOMBENH" calculation="Sum">
		<variableExpression><![CDATA[$F{nt_ts_mac_xv_nu}]]></variableExpression>
	</variable>
	<variable name="RP_NTMACXV" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{nt_ts_mac_xv_ts}]]></variableExpression>
	</variable>
	<variable name="RP_NTMACXVNU" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{nt_ts_mac_xv_nu}]]></variableExpression>
	</variable>
	<group name="GROUP_NHOMBENH">
		<groupExpression><![CDATA[$F{NHOM_BENH}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="0" width="357" height="20" uuid="71899cff-528c-4c05-a53d-225caddbe1dd"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="13" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NHOM_BENH}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="357" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="5a3ce47a-4d70-4219-85fc-8d17df641480"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_KBTS}.toString().equals("0")?"":$V{NHOM_KBTS}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="394" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="9078a6b4-3a95-4b31-9128-f5218e3a389a"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_KBNU}.toString().equals("0")?"":$V{NHOM_KBNU}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="431" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="d499044b-5c3c-42d2-b447-68ed404a135b"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_KBTE}.toString().equals("0")?"":$V{NHOM_KBTE}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="542" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="60ff0c2a-c32b-461c-8d9c-002fd1131719"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_KBTV}.toString().equals("0")?"":$V{NHOM_KBTV}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="579" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="56447b85-701a-479d-829b-fa7c74f3a929"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_NTTSMAC}.toString().equals("0")?"":$V{NHOM_NTTSMAC}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="616" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="256baccc-6a41-41d8-a318-05d611363017"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_TSMACNU}.toString().equals("0")?"":$V{NHOM_TSMACNU}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="738" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="30d3e730-722c-4d59-8287-a1d0915b911a"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_TVTS}.toString().equals("0")?"":$V{NHOM_TVTS}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="775" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="80a05639-2094-468b-bdd4-6af02100ff91"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_TVNU}.toString().equals("0")?"":$V{NHOM_TVNU}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="812" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="60128e65-7c67-42da-9afe-97c8bac35980"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_TEMACTS}.toString().equals("0")?"":$V{NHOM_TEMACTS}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="849" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="f823663f-fe18-4466-895a-7da1a2f84385"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_TEMAC5}.toString().equals("0")?"":$V{NHOM_TEMAC5}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="886" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="cf3a3244-34fe-490e-afc6-c54eb744f40e"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_TETVTS}.toString().equals("0")?"":$V{NHOM_TETVTS}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="923" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="3ef07e4c-9e7f-4502-ab4a-2169ad4e946d"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_TETV5}.toString().equals("0")?"":$V{NHOM_TETV5}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="468" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="d3eeef58-a347-4d66-864a-fe4858eca43d"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="505" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="a23dbe8d-6e5f-4eb4-8454-54e7be617fa8"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="696" y="0" width="42" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="1f612f8e-876b-4979-a556-7f25d8baa331"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_NTMACXVNU}.toString().equals("0")?"":$V{NHOM_NTMACXVNU}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="654" y="0" width="42" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="2de8c49e-ea39-4c5f-a71c-2338cefab50c"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_NTMACXV}.toString().equals("0")?"":$V{NHOM_NTMACXV}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="GROUP_NHOMBENH" pattern="#,##0" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="961" y="0" width="49" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="57addc0b-d162-4780-8fd9-7de160fa66c5"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="221" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="20" width="961" height="35" uuid="7caf1cbe-fe12-4239-aa89-9d1880578f4f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="18" isBold="true"/>
				</textElement>
				<text><![CDATA[TÌNH HÌNH BỆNH TẬT VÀ TỬ VONG TẠI BỆNH VIỆN THEO ICD10]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="961" height="20" uuid="c0c511b6-286e-4a0d-85a9-d105bc30d8ee"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Biểu: 14/BCT]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="55" width="960" height="20" uuid="7f276a36-9687-4d29-a52f-9966a5054373"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày " + new SimpleDateFormat("dd/MM/yyyy").format($P{tungay}) + " đến ngày " + new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="105" width="36" height="116" uuid="3e81bb33-3854-4afc-a0c6-782d48d0252b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="36" y="105" width="232" height="116" uuid="0f7e4316-8050-4651-aa9b-e09beaf1033d"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên bệnh/ nhóm bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement x="268" y="105" width="89" height="116" uuid="7f196586-f813-4376-9595-db6be9a02c30"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã ICD 10]]></text>
			</staticText>
			<staticText>
				<reportElement x="357" y="105" width="222" height="40" uuid="76a30984-70a4-44bd-b402-f50dbc587028"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Tại khoa khám bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement x="357" y="145" width="37" height="76" uuid="f426549e-65b8-438b-93f3-5ea2ee82383e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng số]]></text>
			</staticText>
			<staticText>
				<reportElement x="579" y="125" width="233" height="20" uuid="ea6fa1f4-2ae3-49ff-97c3-8deea88d816a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng số]]></text>
			</staticText>
			<staticText>
				<reportElement x="579" y="105" width="381" height="20" uuid="b95401e7-6080-499d-823e-cde969d6e5b6"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Điều trị nội trú]]></text>
			</staticText>
			<staticText>
				<reportElement x="394" y="165" width="37" height="56" uuid="ac21ef8c-cb73-451a-a92f-c68069d04a38"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="431" y="165" width="37" height="56" uuid="049ee083-4113-4a72-8a03-93e7a18f6722"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TE<15]]></text>
			</staticText>
			<staticText>
				<reportElement x="542" y="165" width="37" height="56" uuid="f6dfd17d-3e90-446b-b70a-a627cbafebe0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Tử vong tại viện]]></text>
			</staticText>
			<staticText>
				<reportElement x="579" y="165" width="37" height="56" uuid="8a703f16-82ad-4c83-b839-9110b239ac4f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TS]]></text>
			</staticText>
			<staticText>
				<reportElement x="738" y="165" width="37" height="56" uuid="8576c45c-ca6e-4226-a205-2b4e2e48357e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TS]]></text>
			</staticText>
			<staticText>
				<reportElement x="616" y="165" width="37" height="56" uuid="88e0c1bd-c2ec-4f97-a564-be1c8850d95b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="775" y="165" width="37" height="56" uuid="9d829a23-f785-4e24-a989-fcfe58fede8c"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="812" y="165" width="37" height="56" uuid="5d93214b-40c3-4a17-af1b-ad8e6a993f90"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TS]]></text>
			</staticText>
			<staticText>
				<reportElement x="923" y="165" width="37" height="56" uuid="e69ac5f6-f122-448d-a1c3-34c5ada732b9"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[<5 tuổi]]></text>
			</staticText>
			<staticText>
				<reportElement x="886" y="165" width="37" height="56" uuid="fd19d8e0-4589-4b69-ac76-d72b8a4f7b74"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TS]]></text>
			</staticText>
			<staticText>
				<reportElement x="849" y="165" width="37" height="56" uuid="de7982fa-9e3c-4bce-acb7-07f9578402ec"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[<5 tuổi]]></text>
			</staticText>
			<staticText>
				<reportElement x="812" y="125" width="148" height="20" uuid="aaa1c36e-e91d-420b-bc25-3f00de9c4318"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Trong đó TE<15 tuổi]]></text>
			</staticText>
			<staticText>
				<reportElement x="394" y="145" width="185" height="20" uuid="b8b72940-8a07-43c1-ada8-7536e5f3e9ef"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Trong đó]]></text>
			</staticText>
			<staticText>
				<reportElement x="579" y="145" width="74" height="20" uuid="c8ab7481-bf1b-430a-b8a9-bfab1236f0f6"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Mắc]]></text>
			</staticText>
			<staticText>
				<reportElement x="738" y="145" width="74" height="20" uuid="3e1dbeb6-e603-42da-b255-1389b873eb43"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Số tử vong]]></text>
			</staticText>
			<staticText>
				<reportElement x="812" y="145" width="74" height="20" uuid="4938c077-cd77-4b27-b002-92e6ce73587f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Mắc]]></text>
			</staticText>
			<staticText>
				<reportElement x="886" y="145" width="74" height="20" uuid="98981fab-4817-478d-b89c-d1b58b1c9cb2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Tử vong]]></text>
			</staticText>
			<staticText>
				<reportElement x="468" y="165" width="37" height="56" uuid="4018611c-6d44-402f-ba8f-3df0eae8385f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Bệnh nặng xin về]]></text>
			</staticText>
			<staticText>
				<reportElement x="505" y="165" width="37" height="56" uuid="4ffcd1ae-44f5-4651-9507-f3234db08802"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Tử vong trước viện]]></text>
			</staticText>
			<staticText>
				<reportElement x="653" y="145" width="85" height="20" uuid="7a8e043c-1398-4235-9591-6a1fb1584aa4"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[BN nặng xin về]]></text>
			</staticText>
			<staticText>
				<reportElement x="653" y="165" width="43" height="56" uuid="c0cd8811-3482-4569-a198-ec9cbac120b0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[TS]]></text>
			</staticText>
			<staticText>
				<reportElement x="696" y="165" width="42" height="56" uuid="e806b7c7-6489-49bb-98b0-4993333fa9c3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="960" y="105" width="50" height="116" uuid="91b33623-bea2-431c-9d1b-571dafc707bd"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Số trường hợp tử vong được cấp giấy báo tử]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="20">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="36" height="20" uuid="dba75e88-0c88-4e63-a8dd-48bc69b2c8f0">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[(Integer.parseInt($F{STT}))==0?null:$F{STT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="36" y="0" width="232" height="20" uuid="bc91de2e-34e9-416b-aaa6-8ed18dc767c6">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="268" y="0" width="89" height="20" uuid="78a9a87e-1b94-4964-9d2e-9f54fad3e1f6">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_ICD}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="357" y="0" width="37" height="20" uuid="40091857-3fef-4501-b4f1-0216d2137bb0">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KB_TS}.toString().equals("0")?"":$F{KB_TS}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="394" y="0" width="37" height="20" uuid="ef76e7b9-e9e7-453c-954d-4da2e9f1a2c9">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KB_NU}.toString().equals("0")?"":$F{KB_NU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="431" y="0" width="37" height="20" uuid="0e5c5d0c-3e01-4646-884f-f487e48d6275">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KB_TE}.toString().equals("0")?"":$F{KB_TE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="542" y="0" width="37" height="20" uuid="f3c6a5cc-62e5-4748-bc87-f9e99bac86a8">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KB_TV}.toString().equals("0")?"":$F{KB_TV}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="579" y="0" width="37" height="20" uuid="f6d420a9-a203-4717-ba4e-a0f8e76ee2f0">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NT_TS_MAC_TS}.toString().equals("0")?"":$F{NT_TS_MAC_TS}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="616" y="0" width="37" height="20" uuid="5d464e8a-a463-4d34-b48c-b0fd18c869e3">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NT_TS_MAC_NU}.toString().equals("0")?"":$F{NT_TS_MAC_NU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="738" y="0" width="37" height="20" uuid="9d1b9ec9-50ab-4a1a-8489-94329f04628a">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NT_TS_TV_TS}.toString().equals("0")?"":$F{NT_TS_TV_TS}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="775" y="0" width="37" height="20" uuid="61b66526-199e-4cef-aa5b-258443ae0346">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NT_TS_TV_NU}.toString().equals("0")?"":$F{NT_TS_TV_NU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="812" y="0" width="37" height="20" uuid="fa26ff3f-a2b5-4f11-8a67-1ea61c3a988a">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NT_TE_MAC_TS}.toString().equals("0")?"":$F{NT_TE_MAC_TS}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="849" y="0" width="37" height="20" uuid="2f834892-729b-43a4-8a6f-86b125c54ac7">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NT_TE_MAC_5T}.toString().equals("0")?"":$F{NT_TE_MAC_5T}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="886" y="0" width="37" height="20" uuid="5364c613-4614-473d-a0c4-09a29103e496">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NT_TE_TV_TS}.toString().equals("0")?"":$F{NT_TE_TV_TS}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="923" y="0" width="38" height="20" uuid="cb2e6e31-e164-4bd6-83bc-bd143cba1339">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NT_TE_TV_5T}.toString().equals("0")?"":$F{NT_TE_TV_5T}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="505" y="0" width="37" height="20" uuid="c009f04f-427a-4445-8258-d3bd3cb18731">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="468" y="0" width="37" height="20" uuid="ee8847d1-3b63-4816-814a-feed2436e1b2">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="653" y="0" width="43" height="20" uuid="91da12dc-dd22-4dfa-9ccd-c410aa202314">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nt_ts_mac_xv_ts}.toString().equals("0")?"":$F{nt_ts_mac_xv_ts}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="696" y="0" width="42" height="20" uuid="22a3a169-0d7d-46eb-8abf-da02e153abe4">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nt_ts_mac_xv_nu}.toString().equals("0")?"":$F{nt_ts_mac_xv_nu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="960" y="0" width="50" height="20" uuid="592b9f93-e3ca-449d-addd-3fdcf8998155">
					<property name="net.sf.jasperreports.export.xls.auto.fit.row" value="true"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="40">
			<staticText>
				<reportElement x="0" y="0" width="357" height="20" uuid="c5875321-4b4e-4c3d-94f9-cea1bc0ce824"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng cộng]]></text>
			</staticText>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="357" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="777f9b7d-09a7-4072-9b6c-b64bed53233c"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_KBTS}.toString().equals("0")?"":$V{RP_KBTS}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="394" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="121731cf-91aa-467a-a0da-0eb52d4628a1"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_KBNU}.toString().equals("0")?"":$V{RP_KBNU}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="431" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="2ab2f181-1142-4793-a829-b7ff366150cd"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_KBTE}.toString().equals("0")?"":$V{RP_KBTE}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="542" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="fc23f078-c258-4a64-b238-924234669e2a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_KBTV}.toString().equals("0")?"":$V{RP_KBTV}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="579" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="9338ac72-763d-49e2-854f-6c3775631882"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTMACTS}.toString().equals("0")?"":$V{RP_NTMACTS}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="616" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="3eb68d6a-f752-4b50-b189-acb6aa8bd662"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTMACNU}.toString().equals("0")?"":$V{RP_NTMACNU}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="738" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="54b02618-0d6b-48e5-a325-7b10f67add89"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTTVTS}.toString().equals("0")?"":$V{RP_NTTVTS}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="775" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="03a16a4e-0c91-46c0-a3ba-027c9f013971"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTTVNU}.toString().equals("0")?"":$V{RP_NTTVNU}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="812" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="4fabf508-2f75-4959-b3fd-50c8c968bf2b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTTEMACTS}.toString().equals("0")?"":$V{RP_NTTEMACTS}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="849" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="1e2cfa3c-3178-47c6-bb19-f5ba0bda197f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTTEMAC5}.toString().equals("0")?"":$V{RP_NTTEMAC5}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="886" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="dc35e96e-99e1-4ea2-b715-a12516a6d088"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTTETVTS}.toString().equals("0")?"":$V{RP_NTTETVTS}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="923" y="0" width="38" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="df0de123-9efa-455a-9a94-a0449ecae279"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTTETV5}.toString().equals("0")?"":$V{RP_NTTETV5}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="20" width="357" height="20" uuid="4827cf9b-df92-479b-b14f-a0131569a600"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng tất cả]]></text>
			</staticText>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="505" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="d7ee5944-d6eb-492a-ae0f-50b8f828f94c"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="468" y="0" width="37" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="c53c8a43-0448-4d35-a091-65757a509469"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="357" y="20" width="653" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="cc35bf49-908c-4cba-991c-3ca939dc1039"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_KBTS}.doubleValue() +$V{RP_NTMACTS}.doubleValue()]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="696" y="0" width="42" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="d0671852-efea-4b45-ad20-e3635cffacc2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTMACXVNU}.toString().equals("0")?"":$V{RP_NTMACXVNU}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="654" y="0" width="42" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="406c20ef-8e28-45c6-907a-be37796a87f2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{RP_NTMACXV}.toString().equals("0")?"":$V{RP_NTMACXV}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="960" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="38f132a7-b22f-457a-b49e-0c9fd2aa46a2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

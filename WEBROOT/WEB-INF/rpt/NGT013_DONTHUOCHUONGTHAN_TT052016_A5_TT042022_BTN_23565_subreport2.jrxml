<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT013_DONTHUOCGAYNGHIEN_TT052016_A5_960_SUB1" language="groovy" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="381" leftMargin="20" rightMargin="20" topMargin="0" bottomMargin="4" uuid="7aa9e6e8-b14b-4277-9a60-b444a656ea92">
	<property name="ireport.zoom" value="2.1435888100000056"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="372"/>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="org_tel" class="java.lang.String"/>
	<parameter name="org_address" class="java.lang.String"/>
	<parameter name="DPAR_ORG_LOGO" class="java.awt.Image"/>
	<queryString language="plsql">
		<![CDATA[{call DUC_DONTHUOCH_BTN_TT042022($P{[UID]},$P{[HID]},$P{[SCH]},null,$P{maubenhphamid},$P{ora_cursor})}]]>
	</queryString>
	<field name="BENHVIEN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA" class="java.lang.String"/>
	<field name="CHANDOANVAOKHOA_KEMTHEO" class="java.lang.String"/>
	<field name="STT" class="java.math.BigDecimal"/>
	<field name="TEN_THUOC" class="java.lang.String"/>
	<field name="DICHVUID" class="java.math.BigDecimal"/>
	<field name="SOLUONG" class="java.lang.String"/>
	<field name="HUONGDANSUDUNG" class="java.lang.String"/>
	<field name="TEN_DVT" class="java.lang.String"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="SODIENTHOAI" class="java.lang.String"/>
	<field name="SOPHIEU_BARCODE" class="java.lang.String"/>
	<field name="NGUOIKEDON" class="java.lang.String"/>
	<field name="DOITUONG_VIENPHI" class="java.lang.String"/>
	<field name="YHCT_TUNGAY" class="java.lang.String"/>
	<field name="YHCT_DENNGAY" class="java.lang.String"/>
	<field name="BHYT_1" class="java.lang.String"/>
	<field name="BHYT_2" class="java.lang.String"/>
	<field name="BHYT_3" class="java.lang.String"/>
	<field name="BHYT_5" class="java.lang.String"/>
	<field name="BHYT_6" class="java.lang.String"/>
	<field name="BHYT_4" class="java.lang.String"/>
	<field name="YKIENBACSY" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="ICD10" class="java.lang.String"/>
	<field name="BHYT_BD" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="DUNGTUYEN" class="java.lang.String"/>
	<field name="MAKCBBD" class="java.lang.String"/>
	<field name="NGAYHEN" class="java.lang.String"/>
	<field name="PHIEUHEN" class="java.lang.Number"/>
	<field name="NGUOITAO" class="java.lang.String"/>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="TUNGAY" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="khoaid" class="java.lang.String"/>
	<field name="KHOAKHAM" class="java.lang.String"/>
	<field name="khokeid" class="java.lang.String"/>
	<field name="PHONGID" class="java.lang.String"/>
	<field name="ngaymaubenhpham" class="java.lang.String"/>
	<field name="MADONTHUOC" class="java.lang.String"/>
	<field name="NGAYSINH" class="java.lang.String"/>
	<field name="CANNANG" class="java.lang.String"/>
	<field name="GTNAM" class="java.lang.String"/>
	<field name="GTNU" class="java.lang.String"/>
	<field name="TEN_NGUOITHAN" class="java.lang.String"/>
	<field name="SDT_NGUOITHAN" class="java.lang.String"/>
	<field name="SOCMTND" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="76" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="43" y="60" width="202" height="15" uuid="1901d6dc-becd-476a-b458-bb4eb16e93b9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Số điện thoại: "+($F{khoaid}==23954?"02523833835": "02523939255")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="0" width="144" height="13" uuid="cd2b0360-7418-492c-ba53-2dc19c1682ff"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MADONTHUOC}]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame">
				<reportElement positionType="Float" x="7" y="37" width="35" height="35" uuid="069bc030-48a5-4279-af59-39e2a7213344"/>
				<box>
					<pen lineWidth="0.5" lineColor="#333333"/>
					<topPen lineWidth="0.0" lineColor="#333333"/>
					<leftPen lineWidth="0.0" lineColor="#333333"/>
					<bottomPen lineWidth="0.0" lineColor="#333333"/>
					<rightPen lineWidth="0.0" lineColor="#333333"/>
				</box>
				<imageExpression><![CDATA[$P{DPAR_ORG_LOGO}]]></imageExpression>
			</image>
			<componentElement>
				<reportElement mode="Opaque" x="261" y="34" width="108" height="21" uuid="93bade72-eea4-4351-860b-f3545d2bf624"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="Code39 (Extended)" drawText="false" checksumRequired="false" barHeight="0">
					<jr:codeExpression><![CDATA[$F{MAHOSOBENHAN}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="263" y="50" width="106" height="10" uuid="366d4271-659d-4716-910b-ce72069dc21c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="43" y="45" width="202" height="15" uuid="3e9445b9-6515-4b1c-a050-b3e083a05cf9"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONGKHAM}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="43" y="28" width="202" height="17" uuid="1ac3b69f-2044-49d7-8c1e-89b75ec2d1be"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="43" y="13" width="202" height="15" uuid="921174c4-567b-4117-9908-ee45301cd9f4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="38" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="381" height="20" uuid="2750d24f-3ab8-4b26-b9ea-0b5e47a2b99a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐƠN THUỐC "H"]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="19" width="381" height="15" uuid="5e2dba75-4b0e-43b1-b74c-ab59c428c645"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[(Bản giao cho người nhận)]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="103" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="3" y="15" width="122" height="14" uuid="e2ac1cc9-d6f6-4cf6-be44-bc7d56507b54"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày sinh: "+($F{NGAYSINH}==null?"":$F{NGAYSINH})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="3" y="59" width="381" height="14" uuid="976af559-5411-4c79-9db0-48af384bc355"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
					<paragraph lineSpacing="1_1_2"/>
				</textElement>
				<textFieldExpression><![CDATA["Chẩn đoán: " + " (" + $F{ICD10} + ") " + $F{CHANDOANVAOKHOA} +
($F{CHANDOANVAOKHOA_KEMTHEO}==null?"":"-"+$F{CHANDOANVAOKHOA_KEMTHEO})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="3" y="0" width="381" height="15" uuid="e98d3ae0-21ac-4139-876d-b62dca7d7829"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên:  " + $F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="245" y="15" width="47" height="14" uuid="7b234f72-7804-4552-a7ed-1281d9b766b9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Giới tính:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="156" y="29" width="228" height="15" uuid="b26538cc-69bc-4ded-8e7a-b3abd6fd9f6c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{SOTHE} == null ? "" : $F{SOTHE}) + ($F{MAKCBBD} == null ? "" : " - " + $F{MAKCBBD})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="3" y="73" width="379" height="14" uuid="8d66ef17-70f0-4533-b526-774785e48123"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Đợt  (Từ ngày " + $F{TUNGAY} +" Đến ngày " +  $F{NGAYHEN} + ")"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="3" y="44" width="381" height="15" uuid="2fcbc50b-6162-48be-8ab0-a03d66015d18"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ liên hệ: " + ($F{DIACHI}==null?"":$F{DIACHI})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="4" y="29" width="152" height="15" uuid="b1e198a6-0d16-4f67-aa89-7b51be7f3ff0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Số thẻ bảo hiểm y tế (nếu có) :]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="125" y="15" width="104" height="14" uuid="a8b64992-aa5d-44a5-af97-417125f8c63c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[" Cân nặng: "+($F{CANNANG}==null?"":($F{CANNANG}+" Kg"))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="294" y="15" width="36" height="14" uuid="2a6ad44e-c1c0-44f6-9941-92da3f6a8b6b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="2" y="89" width="24" height="14" uuid="552cd83b-8ab8-48e7-b4d3-b63ff4476742"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="26" y="89" width="266" height="14" uuid="9731baa9-131a-4b0d-9f4e-3c9e45c49f35"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên thuốc]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="292" y="89" width="85" height="14" uuid="5fc71fe7-3c46-4db9-b41f-59f9611a2954"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="35" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="2" y="0" width="24" height="32" uuid="102ecaf8-be6e-471b-bea8-6663df96f929"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{STT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="26" y="0" width="266" height="17" uuid="f10bcfff-5156-4f97-9252-d33d89a55660"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_THUOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="292" y="0" width="85" height="32" uuid="00d603b9-78db-4929-aa3b-1c2f85a6c629"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUONG}.length()==1?"0"+$F{SOLUONG}:$F{SOLUONG} + " " + $F{TEN_DVT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="26" y="17" width="266" height="15" uuid="a5a907d6-d8f5-4102-8e16-19342813286d"/>
				<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUONGDANSUDUNG}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band height="58">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="25" width="393" height="12" uuid="cb64cff8-2acf-4785-b112-3b676b1571fb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Tên bố hoặc mẹ của trẻ hoặc người đưa trẻ đến khám bệnh, chữa bệnh: "+($F{TEN_NGUOITHAN}==null?"":$F{TEN_NGUOITHAN})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="226" height="12" uuid="3462fb76-8ac0-4ea0-95ea-cec27bf0b9eb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[- Khám lại xin mang theo đơn này.]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="13" width="393" height="12" uuid="b7f04feb-4051-4344-a8b7-dfd3b87b2bbf"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Số điện thoại liên hệ: "+($F{SDT_NGUOITHAN}==null?"":$F{SDT_NGUOITHAN})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="46" width="393" height="12" uuid="6101313e-24d3-427e-a727-9aee5785e050"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Căn cước công dân/Chứng minh nhân dân của người nhận thuốc: "+($F{SOCMTND}==null?"":$F{SOCMTND})]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band height="107">
			<staticText>
				<reportElement positionType="Float" x="190" y="33" width="191" height="15" uuid="2059e3aa-9a45-4907-93de-4464b35befd2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Bác sỹ/ Y sĩ khám bệnh]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="190" y="91" width="190" height="15" uuid="bcdaaaaf-fb1c-4ec8-82f4-9e2e3a5296d3"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOITAO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="190" y="18" width="191" height="15" uuid="1d19143e-08ba-4722-837b-6997acce6e10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngaymaubenhpham}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="190" y="47" width="192" height="14" uuid="5206a019-f692-4ec3-9648-f81808353f79"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="47" y="0" width="335" height="16" uuid="05368627-ccc3-45b0-be5c-940d91d3e96c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{YKIENBACSY}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="47" height="16" uuid="e91a9e6d-e283-4022-80ed-f6817e44706d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[Lời dặn:  ]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>

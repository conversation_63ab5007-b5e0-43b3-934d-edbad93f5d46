<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NGT035_BKCPKCBTUTUCNGOAITRU_A4_1014_SUB2" columnDirection="RTL" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="581" leftMargin="7" rightMargin="7" topMargin="20" bottomMargin="16" isSummaryWithPageHeaderAndFooter="true" whenResourceMissingType="Error" uuid="40878bc5-145e-4b9d-a320-de3e94e9e385">
	<property name="ireport.zoom" value="1.331000000000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="org_address" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="cs1" class="java.sql.ResultSet">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="phieuthuid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call inphoivp_tutuc_1014($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{phieuthuid},$P{cs1})}]]>
	</queryString>
	<field name="KHOA" class="java.lang.String"/>
	<field name="GROUP_KHOA" class="java.lang.String"/>
	<field name="GROUP_CON" class="java.lang.String"/>
	<field name="GROUP_NHOM" class="java.lang.String"/>
	<field name="GROUP_XETNGHIEM" class="java.lang.String"/>
	<field name="TIEPNHANID" class="java.math.BigDecimal"/>
	<field name="NGAYTIEPNHAN" class="java.lang.String"/>
	<field name="NGAY_RAVIEN" class="java.lang.String"/>
	<field name="MACHANDOANRAVIEN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="NGAYSINH" class="java.lang.String"/>
	<field name="GIOITINHID" class="java.lang.Integer"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="DON_GIA" class="java.lang.Double"/>
	<field name="THANH_TIEN" class="java.lang.Double"/>
	<field name="DANOP" class="java.lang.Double"/>
	<field name="NGUOI_BENH" class="java.lang.Double"/>
	<field name="NOIDUNG" class="java.lang.String"/>
	<field name="SO_LUONG" class="java.lang.Double"/>
	<field name="TAMUNG" class="java.lang.Double"/>
	<field name="HOANUNG" class="java.lang.Double"/>
	<field name="MIENGIAM" class="java.math.BigDecimal"/>
	<field name="DVT" class="java.lang.String"/>
	<field name="GROUP_DONVI" class="java.lang.String"/>
	<field name="MA_BHYT" class="java.lang.String"/>
	<field name="QUY_BHYT" class="java.lang.String"/>
	<field name="NGUOITHU" class="java.lang.String"/>
	<field name="MATIEPNHAN" class="java.lang.String"/>
	<field name="KHAC" class="java.lang.Double"/>
	<field name="bschidinh" class="java.lang.String"/>
	<field name="tuoi" class="java.lang.String"/>
	<field name="dvtuoi" class="java.lang.String"/>
	<field name="DON_GIABHYT" class="java.lang.Double"/>
	<field name="chenhlech" class="java.lang.Double"/>
	<field name="mahosobenhan" class="java.lang.String"/>
	<field name="bhyttra" class="java.lang.Double"/>
	<field name="BNDONGCHITRA" class="java.lang.Double"/>
	<field name="THANHTIENDV" class="java.lang.Double"/>
	<field name="THANHTIENBH" class="java.lang.Double"/>
	<field name="BNTHANHTOAN" class="java.lang.Double"/>
	<field name="loainhomdichvu" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="IDNGUOITHU" class="java.lang.String"/>
	<field name="SOHD" class="java.lang.String"/>
	<field name="ngaythu2" class="java.util.Date"/>
	<field name="maphieuthu" class="java.lang.String"/>
	<field name="tien_to" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="rpt_count" class="java.lang.String"/>
	<field name="USER_NAME" class="java.lang.String"/>
	<field name="noichidinh" class="java.lang.String"/>
	<variable name="report_thanhtien" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENDV}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="ThanhTienDV" class="java.lang.Double">
		<variableExpression><![CDATA[$F{SO_LUONG}*$F{DON_GIA}]]></variableExpression>
	</variable>
	<variable name="ThanhTienBH" class="java.lang.Double">
		<variableExpression><![CDATA[$F{SO_LUONG}*$F{DON_GIABHYT}]]></variableExpression>
	</variable>
	<variable name="BN_PhaiThanhToan" class="java.lang.Double">
		<variableExpression><![CDATA[$V{ThanhTienDV}-$V{ThanhTienBH}]]></variableExpression>
	</variable>
	<variable name="Total_nguoibenh" class="java.lang.Double" resetType="Group" resetGroup="group_con" calculation="Sum">
		<variableExpression><![CDATA[$F{BNTHANHTOAN}]]></variableExpression>
	</variable>
	<variable name="TinhToan_DongChiTra" class="java.lang.Double">
		<variableExpression><![CDATA[$F{bhyttra}.equals(0.0)?0:($V{ThanhTienBH}-$F{bhyttra})]]></variableExpression>
	</variable>
	<variable name="TinhToan_BNPhaiTT" class="java.lang.Double">
		<variableExpression><![CDATA[$F{DON_GIABHYT}.equals(0.0)?$F{THANHTIENDV}:($F{chenhlech}+$F{BNDONGCHITRA})]]></variableExpression>
	</variable>
	<variable name="report_DonGiaBH" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{DON_GIABHYT}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="report_thanhtienBH" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIENBH}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="report_BHYTThanhToan" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{bhyttra}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="report_BNDongCT" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{BNDONGCHITRA}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="report_ChenhLechDV" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{chenhlech}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="report_BNPhaiTT" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{BNTHANHTOAN}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<group name="group_con">
		<groupExpression><![CDATA[$F{GROUP_CON}]]></groupExpression>
		<groupHeader>
			<band height="15">
				<printWhenExpression><![CDATA[!$F{GROUP_CON}.equals(null)]]></printWhenExpression>
				<textField isBlankWhenNull="true">
					<reportElement positionType="Float" x="0" y="0" width="535" height="15" uuid="2390c758-b7c9-4395-85e2-8ba287923a84"/>
					<box leftPadding="5">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GROUP_DONVI}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_con" pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="535" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="3e1cacc3-c814-4c51-a0eb-728e771c70ef"/>
					<box rightPadding="2">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" rightIndent="-1"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($V{Total_nguoibenh}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="207" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="341" height="15" uuid="69cfb868-7273-42c6-80f5-00dbf8c58401"/>
				<textElement>
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="15" width="341" height="15" uuid="2a5712e5-5cb6-4641-be63-6844cd806ef9"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="427" y="47" width="152" height="14" uuid="6ff3e516-4091-4d04-8b80-68c7be0a5282"/>
				<textElement textAlignment="Right">
					<font fontName="Times New Roman" size="10" isItalic="true"/>
				</textElement>
				<text><![CDATA[Mẫu số: D05/BV - DV]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="63" width="579" height="26" uuid="465a7a08-ec3d-4ead-991f-ecb349ce805f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[PHIẾU THU TIỀN KIÊM BẢNG KÊ CHI PHÍ KHÁM BỆNH, CHỮA BỆNH]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="101" width="187" height="15" uuid="ca3cfcad-985d-4eae-96d1-50435932fd50"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[I.Hành Chính]]></text>
			</staticText>
			<staticText>
				<reportElement x="455" y="117" width="47" height="15" uuid="963c0e1c-78b4-4207-bed8-aa6d0de06a3d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Giới tính: Nam]]></text>
			</staticText>
			<staticText>
				<reportElement x="546" y="117" width="17" height="15" uuid="ad0b467a-49f1-4b52-9dc6-1cf2387e758b"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="379" y="152" width="132" height="15" uuid="ebd797c2-1883-44dc-9492-31b47b17b2d4"/>
				<box rightPadding="2"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Mã bệnh (ICD10):]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="528" y="117" width="15" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="f750235d-0cc3-4049-955c-1211ef3b352b"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINHID}.equals( 1 )?"X":""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="564" y="117" width="15" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="0028a0ad-f256-48ee-8d6b-39a5526efee0"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINHID}.equals( 2 )?"X":""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="511" y="152" width="68" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="2fed62de-1a08-4d2a-b323-14558c679a8c"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MACHANDOANRAVIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="117" width="323" height="15" uuid="35406bda-c384-4afe-8a3b-189e81d1f4b9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên người bệnh: "+"<b>"+($F{TENBENHNHAN}.equals(null)?"":$F{TENBENHNHAN})+"</b>"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="190" width="561" height="15" uuid="63007a45-d6e4-41a8-8089-195226c43c7a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[II.Chi phí khám, chữa bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement x="504" y="117" width="25" height="15" uuid="782724e2-f407-4acc-a537-4d5e81f47dc3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Nam]]></text>
			</staticText>
			<componentElement>
				<reportElement x="427" y="0" width="152" height="30" uuid="df1e2361-700a-406b-af68-0da820c3cca9"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA[$F{mahosobenhan}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="489" y="134" width="20" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="ae4626b9-54c6-47e4-a17c-b69d09976acf"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(5,7);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="459" y="134" width="10" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="ba0c1bd0-d107-481e-b560-411b40630967"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(2,3);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="469" y="134" width="20" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="15dfe709-6e2e-4a9b-8396-1e6766183d1b"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(3,5);]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="366" y="134" width="73" height="15" uuid="dcf71f4a-2475-46d1-863a-44aa77efdb6c"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Mã thẻ BHYT:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="509" y="134" width="30" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="1a929154-7794-4e8b-9409-0db013fb3970"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(7,10);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="439" y="134" width="20" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="c5d0cf7c-6ca2-49d0-8e48-b9be15f80d86"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(0,2);]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="539" y="134" width="40" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="c8f2460c-0e50-4a98-aa06-23e9a19bfcd1"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}.substring(10,15);]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="30" width="341" height="15" uuid="f70a18fd-72a6-4478-8d2b-d33bf6ae80df"/>
				<textElement markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA["ĐƠN VỊ: "+"<b><u>"+$F{KHOA}+"</b></u>"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="336" y="134" width="30" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="dc358c2d-acc8-4f8d-a867-9683c53b03cb"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="0" y="134" width="310" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="881130a5-612e-4790-bbd6-241652a1d3a9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ: "+($F{DIACHI}==null?"":$F{DIACHI})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="0" y="152" width="209" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="cbbe3fb7-bda1-4135-a2f6-6826a8de77e4"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Bác sĩ chỉ định: "+($F{bschidinh}==null?"":$F{bschidinh})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="432" y="31" width="147" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="15efe83b-37bf-43e9-a93f-b98ee14f5d19"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Số: "+"<b>"+($F{mahosobenhan}.equals(null)?"":$F{mahosobenhan})+"</b>"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="209" y="88" width="151" height="14" uuid="cfb2793e-f9f1-4572-a472-ecfd7df26613"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Liên 2: Giao người mua)]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="336" y="117" width="119" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="148a865e-50fb-495d-b87d-b9c52e6937ea"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: "+"<b>"+($F{tuoi}==null?"":$F{tuoi})+"</b>"+" "+($F{dvtuoi}==null?"":$F{dvtuoi})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="209" y="102" width="370" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="6c569577-9d1b-4c39-ba0c-7e90396d6683"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Số hóa đơn: "+"<b>"+(($F{tien_to}==null?"":$F{tien_to})+($F{maphieuthu}.length()==1?("00000"+$F{maphieuthu}):($F{maphieuthu}.length()==2?("0000"+$F{maphieuthu}):($F{maphieuthu}.length()==3?("000"+$F{maphieuthu}):($F{maphieuthu}.length()==4?("00"+$F{maphieuthu}):($F{maphieuthu}.length()==5?("0"+$F{maphieuthu}):$F{maphieuthu})))
)))+"</b>"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="341" y="1" width="86" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="32fcc900-88e5-4ac3-95f5-3b99136c26c1"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="18" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["In lần "+$F{rpt_count}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="217" y="152" width="119" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="81c49675-2ee9-4f8d-a62a-64ba57831164"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã Y Tế: "+($F{MABENHNHAN}==null?"":$F{MABENHNHAN})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="-1" y="171" width="581" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="48e3d64c-62d9-4828-9862-888c16e8cf8b"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Nơi chỉ định: "+($F{noichidinh}==null?"":$F{noichidinh})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="40" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="20" y="0" width="154" height="40" uuid="957ae053-8e38-4011-b32d-74143f30c902"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Nội dung]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="174" y="0" width="30" height="40" uuid="470a45f4-a342-431e-8c9d-65cb02f2fff5"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐVT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="204" y="0" width="27" height="40" uuid="9c29ac93-f65b-45ac-a9dc-bdb99bee0d96"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[SL]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="231" y="0" width="40" height="40" uuid="8dba549f-8da1-4864-899e-21a73d8eccca"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn giá
DV]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="271" y="0" width="40" height="40" uuid="a5b75ed8-fbd9-483e-b934-02d0ccd065c2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Thành tiền
DV]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="311" y="0" width="269" height="15" uuid="18b13ab1-8041-4db0-9c9e-dfe4ff4455b2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Trong đó]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="400" y="15" width="45" height="25" uuid="946bbb38-dc92-49f7-897a-e4981d8abbf9"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[BHYT thanh toán]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="535" y="15" width="45" height="25" uuid="d2c2f468-6901-4a02-96a4-00b42c2c746f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[BN phải thanh toán]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="20" height="40" uuid="293262c9-e5c7-4981-8364-19a28603c656"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="311" y="15" width="44" height="25" uuid="c97a9a37-66b4-4125-a681-134f341df491"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn giá BHYT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="355" y="15" width="45" height="25" uuid="dcff9d2d-30cd-4181-bd8d-fbf38c58b4e1"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Thành tiền BHYT]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="445" y="15" width="45" height="25" uuid="6d95f099-c3ea-4aa7-a79c-fc49bebc4716"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[BN đồng chi trả]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="490" y="15" width="45" height="25" uuid="2c250540-218e-4c59-85d9-d3b7c63ba39a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Chênh lệch dịch vụ]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Prevent">
			<elementGroup/>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="355" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="efece623-ceef-4b64-8b42-6a651988e216"/>
				<box rightPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DON_GIABHYT}.equals(0.0)?"-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;":new DecimalFormat("###,###").format($F{THANHTIENBH}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="311" y="0" width="44" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="9161c3ac-bc2e-40a5-b70c-205e42ad758a"/>
				<box rightPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DON_GIABHYT}.equals(0.0)?"-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;":new DecimalFormat("###,###").format($F{DON_GIABHYT}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="174" y="0" width="30" height="15" uuid="*************-4326-88eb-fe0ce799d9c0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DVT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="400" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="ca009fe0-1180-44e2-80fc-441e2f962b02"/>
				<box rightPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DON_GIABHYT}.equals(0.0)?"-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;":new DecimalFormat("###,###").format($F{bhyttra}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="445" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="5844afa9-e4aa-4178-8f29-07fbb0fe5142"/>
				<box rightPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DON_GIABHYT}.equals(0.0)?"-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;":new DecimalFormat("###,###").format($F{BNDONGCHITRA}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="535" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="1e56dd2c-beed-4089-8b4e-8fd2c0a06690"/>
				<box rightPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($F{BNTHANHTOAN}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="271" y="0" width="40" height="15" uuid="2af01313-a537-4bf9-b780-8e676ebd3c83"/>
				<box rightPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="9"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{loainhomdichvu}==null ||$F{loainhomdichvu}.equals("3") || $F{loainhomdichvu}.equals("4") || $F{loainhomdichvu}.equals("5"))?(new DecimalFormat("###,###").format($F{THANHTIENDV}).replaceAll(",", ".")): "-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="20" y="0" width="154" height="15" uuid="44dfbfab-a297-48dc-bf1f-208f2f1800f3"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOIDUNG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="231" y="0" width="40" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="20f583e8-2dd4-472e-b91d-fe62988dbf55"/>
				<box rightPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{loainhomdichvu}==null ||$F{loainhomdichvu}.equals("3") || $F{loainhomdichvu}.equals("4") || $F{loainhomdichvu}.equals("5"))?(new DecimalFormat("###,###").format($F{DON_GIA}).replaceAll(",", ".")): "-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="204" y="0" width="27" height="15" uuid="f8e3b087-be2c-46ee-ba7d-c254a4150808"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SO_LUONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="20" height="15" uuid="6183c0dd-**************-3112e88619b6"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#999999"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{group_con_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="490" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="0b9db91b-fd76-478a-ad7b-a2cddc6c89c3"/>
				<box rightPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($F{chenhlech}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="16" splitType="Stretch">
			<textField>
				<reportElement positionType="Float" x="511" y="0" width="53" height="16" uuid="146c20da-6dd8-49b9-b515-c08387db6b78"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="165" y="0" width="344" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="792d23a3-7ce9-48f6-9896-c60b190f089e"/>
				<box leftPadding="0" rightPadding="5">
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["In lần "+$F{rpt_count} +". In tại USER: "+ $F{USER_NAME} +". "+(new SimpleDateFormat("dd/MM/yy, hh:mm:ss a")).format(new Date())]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement positionType="Float" x="564" y="0" width="16" height="16" uuid="e591ed24-7da8-429c-b62c-d70c94ef132a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["/"+$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="235" splitType="Immediate">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="231" height="15" backcolor="#FFCCFF" uuid="b777494f-3d79-4d52-bbf3-e71abd70927c"/>
				<box leftPadding="5">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tổng cộng chi phí khám bệnh, chữa bệnh:]]></text>
			</staticText>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="535" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="b3792e0c-d534-41bb-b104-4a3d6bf50105"/>
				<box rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{report_BNPhaiTT}.equals(null)?0:(new DecimalFormat("###,###").format($V{report_BNPhaiTT}).replaceAll(",", "."))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="231" y="0" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e06595e3-053f-460a-a6ad-ce0527b1fe8e"/>
				<box rightPadding="2">
					<bottomPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{report_thanhtien}.equals(null)?0:(new DecimalFormat("###,###").format($V{report_thanhtien}).replaceAll(",", "."))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="400" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="7140d1bf-c923-427e-ab25-d418842d6b82"/>
				<box leftPadding="0" rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($V{report_BHYTThanhToan}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="355" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="af69e766-ff54-45ad-987e-8004dccad414"/>
				<box leftPadding="0" rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($V{report_thanhtienBH}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="311" y="0" width="44" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="020c2397-cb92-4cd6-995c-c52335a21abc"/>
				<box leftPadding="0" rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($V{report_DonGiaBH}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="15" width="231" height="15" backcolor="#FFCCFF" uuid="c69b5093-7b50-464c-9fb7-71056aef4305"/>
				<box leftPadding="5">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Số tiền người bệnh còn phải trả:]]></text>
			</staticText>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="535" y="15" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="49a72e62-a8a8-43e1-a614-f23d7ffdbbd0"/>
				<box rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{report_BNPhaiTT}.equals(null)?0:(new DecimalFormat("###,###").format($V{report_BNPhaiTT}).replaceAll(",", "."))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="231" y="15" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="67156f3c-d2ed-4aee-95cd-2bc7b4b5a7c6"/>
				<box leftPadding="0" rightPadding="5">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="311" y="15" width="44" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="50f3d9f8-23a1-490e-a96a-1f2bae228e25"/>
				<box leftPadding="0" rightPadding="5">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="355" y="15" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="8ca78184-6e45-498f-9ceb-cdc216abcb5e"/>
				<box leftPadding="0" rightPadding="5">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="400" y="15" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="4372d413-a6ac-4890-8372-ab727eed595a"/>
				<box leftPadding="0" rightPadding="5">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="445" y="15" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="b0844da4-068a-4177-8794-c9e09c4acf14"/>
				<box leftPadding="0" rightPadding="5">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="445" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="cdae6eaf-59bf-4502-9fd1-94312d10a80d"/>
				<box leftPadding="0" rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($V{report_BNDongCT}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="490" y="15" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d6e04911-bf67-45a1-95c7-4995cf695e97"/>
				<box leftPadding="0" rightPadding="5">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="490" y="0" width="45" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="26c1d795-b1e1-4fcf-aeac-5558b0c598f6"/>
				<box leftPadding="0" rightPadding="1">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" rightIndent="-1"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($V{report_ChenhLechDV}).replaceAll(",", ".")]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="1" y="123" width="578" height="110" uuid="a511dd1a-9ba3-48a5-91dc-4d02a5568f58"/>
				<staticText>
					<reportElement positionType="Float" x="10" y="10" width="182" height="15" uuid="2ef8e3d0-55fc-4a60-bcc9-5192226375aa"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[XÁC NHẬN CỦA NGƯỜI BỆNH]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="377" y="10" width="182" height="15" uuid="dd8bdf28-85b4-40bd-9524-fede8910b720"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[NGƯỜI LẬP BẢNG KÊ]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="377" y="26" width="182" height="15" uuid="a7e701c5-31e5-4a6d-b427-82154149b530"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="10" y="26" width="182" height="15" uuid="86a64db8-3124-437f-aff2-ae14996d3d04"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
				</staticText>
				<textField>
					<reportElement positionType="Float" x="10" y="85" width="182" height="15" uuid="eac86672-4d9f-4f24-83ab-dbd1c952cd42"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TENBENHNHAN}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="377" y="85" width="182" height="15" uuid="34537ae9-1f1c-4432-9916-807a0df71026"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="11" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGUOITHU}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="193" y="11" width="185" height="15" uuid="ca4da3f5-2493-4dd5-b70b-f03476965405"/>
					<textElement textAlignment="Center">
						<font fontName="Times New Roman" size="11" isItalic="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Ngày "+(new SimpleDateFormat("dd")).format($F{ngaythu2})
+ " tháng " + (new SimpleDateFormat("MM")).format($F{ngaythu2})
+ " năm " + (new SimpleDateFormat("yyyy")).format($F{ngaythu2})]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="30" width="580" height="93" uuid="a8b27fdf-0b04-4457-8598-d3a361f3213f"/>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="232" y="16" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="ac9b27f4-e7e0-4be4-ba8b-d75ae1c56bed"/>
					<box rightPadding="2">
						<bottomPen lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{report_thanhtien}.equals(null)?0:(new DecimalFormat("###,###").format($V{report_thanhtien}).replaceAll(",", "."))]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="232" y="76" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="8d97669d-1d2c-4f91-9a69-17b3ea296872"/>
					<box rightPadding="2">
						<bottomPen lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($V{report_BNPhaiTT}-$V{report_BNDongCT}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="15" y="31" width="217" height="15" uuid="16c6ae68-2879-434a-98b8-a6fdc925c897"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9"/>
					</textElement>
					<text><![CDATA[-Số tiền quỹ BHYT thanh toán]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="60" y="76" width="172" height="15" uuid="7df425e8-baed-478b-bf86-20d092da008f"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9"/>
					</textElement>
					<text><![CDATA[*Số tiền BHYT không chi trả]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="60" y="61" width="172" height="15" uuid="ab26772b-3b35-483c-a9a4-e8d9c1112d58"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9"/>
					</textElement>
					<text><![CDATA[*Số tiền người bệnh đồng chi trả với BHYT]]></text>
				</staticText>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="232" y="61" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e1df5672-c02c-47a9-b700-2a1e69cd5a72"/>
					<box rightPadding="2">
						<bottomPen lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($V{report_BNDongCT}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="2" y="1" width="40" height="15" uuid="dbfcd5b4-f3e0-47eb-a8cd-bac746e669cd"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
						<font fontName="Times New Roman" size="9" isBold="false"/>
					</textElement>
					<text><![CDATA[Số tiền: ]]></text>
				</staticText>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="232" y="31" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="614c9f31-2e17-4fd6-8ca7-251a6bd74fb7"/>
					<box rightPadding="2">
						<bottomPen lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("###,###").format($V{report_BHYTThanhToan}).replaceAll(",", ".")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="15" y="61" width="45" height="15" uuid="34525883-d205-425e-9014-fb615a266f83"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9"/>
					</textElement>
					<text><![CDATA[ Trong đó:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="15" y="16" width="217" height="15" uuid="5f9a9172-e4c0-4d6e-8e33-136878fd34a9"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9"/>
					</textElement>
					<text><![CDATA[-Tổng chi phí đợt điều trị:]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="15" y="46" width="217" height="15" uuid="47747e12-4afd-42ed-9cca-21fae84ab6a8"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9"/>
					</textElement>
					<text><![CDATA[-Tổng số tiền người bệnh phải trả:]]></text>
				</staticText>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Transparent" x="232" y="46" width="80" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="3cdcc408-631b-46de-94ee-e27d97551a22"/>
					<box rightPadding="2">
						<bottomPen lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{report_BNPhaiTT}.equals(null)?0:(new DecimalFormat("###,###").format($V{report_BNPhaiTT}).replaceAll(",", "."))]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</summary>
</jasperReport>

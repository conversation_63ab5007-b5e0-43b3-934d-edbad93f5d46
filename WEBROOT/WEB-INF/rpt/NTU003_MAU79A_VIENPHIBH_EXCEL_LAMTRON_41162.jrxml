<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NTU003_MAU79A_VIENPHIBH_EXCEL_LAMTRON_41162" printOrder="Horizontal" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="27f1d17c-d8ed-4f78-826f-955ef3922c97">
	<property name="net.sf.jasperreports.export.xls.detect.cell.type" value="true"/>
	<property name="ireport.zoom" value="1.8150000000000006"/>
	<property name="ireport.x" value="13"/>
	<property name="ireport.y" value="240"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[HID]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[SCH]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="[IP]" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="matinh" class="java.lang.String"/>
	<parameter name="tungay" class="java.util.Date">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="denngay" class="java.util.Date">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_khoaid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_dtbnid_VALUE" class="java.lang.String"/>
	<parameter name="VPARS_loaidoituongid_VALUE" class="java.lang.String"/>
	<parameter name="loaingayid" class="java.lang.String"/>
	<parameter name="trangthai" class="java.lang.String"/>
	<parameter name="loaigia" class="java.lang.String"/>
	<parameter name="tieude" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call CHITIET_80A_DVBH_TRON_41162($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},
$P{tungay},$P{denngay},$P{VPARS_khoaid_VALUE},
'1',
$P{VPARS_dtbnid_VALUE},
$P{VPARS_loaidoituongid_VALUE},
$P{loaingayid},
$P{trangthai},
$P{loaigia},
$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="GROUPHEADER_NOITINH" class="java.lang.Integer"/>
	<field name="GROUPHEADER_DUNGTUYEN" class="java.lang.Integer"/>
	<field name="HOTEN" class="java.lang.String"/>
	<field name="NAMSINH" class="java.math.BigDecimal"/>
	<field name="GIOITINH" class="java.lang.Integer"/>
	<field name="MATHE" class="java.lang.String"/>
	<field name="MA_DKBD" class="java.lang.String"/>
	<field name="MABENH" class="java.lang.String"/>
	<field name="NGAYVAO" class="java.lang.String"/>
	<field name="T_TONGCHI" class="java.math.BigDecimal"/>
	<field name="T_KHAM" class="java.math.BigDecimal"/>
	<field name="T_XN" class="java.math.BigDecimal"/>
	<field name="T_CDHA" class="java.math.BigDecimal"/>
	<field name="T_THUOC" class="java.math.BigDecimal"/>
	<field name="T_KTG" class="java.math.BigDecimal"/>
	<field name="T_MAU" class="java.math.BigDecimal"/>
	<field name="T_PTTT" class="java.math.BigDecimal"/>
	<field name="T_VTYTTH" class="java.math.BigDecimal"/>
	<field name="T_VTYTTT" class="java.math.BigDecimal"/>
	<field name="T_DVKTC" class="java.math.BigDecimal"/>
	<field name="T_VCHUYEN" class="java.math.BigDecimal"/>
	<field name="T_NGOAIDS" class="java.math.BigDecimal"/>
	<field name="T_BNCT" class="java.math.BigDecimal"/>
	<field name="T_BHTT" class="java.math.BigDecimal"/>
	<field name="T_TIEN_BHYT" class="java.lang.String"/>
	<field name="NGAYRAVIEN" class="java.lang.String"/>
	<field name="SONGAYDIEUTRI" class="java.lang.String"/>
	<field name="HOSPITAL_CODE" class="java.lang.String"/>
	<field name="TENCHUCDANH" class="java.lang.String"/>
	<field name="T_CONGKHAM" class="java.math.BigDecimal"/>
	<field name="TIENGIUONG" class="java.math.BigDecimal"/>
	<variable name="g1_tongcong" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_TONGCHI}]]></variableExpression>
	</variable>
	<variable name="g1_xetnghiem" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_XN}]]></variableExpression>
	</variable>
	<variable name="g1_cdha" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_CDHA}]]></variableExpression>
	</variable>
	<variable name="g1_thuoc" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_THUOC}]]></variableExpression>
	</variable>
	<variable name="g1_mau" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_MAU}]]></variableExpression>
	</variable>
	<variable name="g1_ttpt" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_PTTT}]]></variableExpression>
	</variable>
	<variable name="g1_vtth" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_VTYTTH}]]></variableExpression>
	</variable>
	<variable name="g1_vttt" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_VTYTTT}]]></variableExpression>
	</variable>
	<variable name="g1_dvktcao" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_DVKTC}]]></variableExpression>
	</variable>
	<variable name="g1_thuocktg" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_KTG}]]></variableExpression>
	</variable>
	<variable name="g1_congkham" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_CONGKHAM}]]></variableExpression>
	</variable>
	<variable name="g1_vanchuyen" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_VCHUYEN}]]></variableExpression>
	</variable>
	<variable name="g1_bnct" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_BNCT}]]></variableExpression>
	</variable>
	<variable name="g1_bhtt" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_BHTT}]]></variableExpression>
	</variable>
	<variable name="g1_ngoaids" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{T_NGOAIDS}]]></variableExpression>
	</variable>
	<variable name="g2_tongcong" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_TONGCHI}]]></variableExpression>
	</variable>
	<variable name="g2_xetnghiem" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_XN}]]></variableExpression>
	</variable>
	<variable name="g2_cdha" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_CDHA}]]></variableExpression>
	</variable>
	<variable name="g2_thuoc" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_THUOC}]]></variableExpression>
	</variable>
	<variable name="g2_mau" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_MAU}]]></variableExpression>
	</variable>
	<variable name="g2_ttpt" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_PTTT}]]></variableExpression>
	</variable>
	<variable name="g2_vtth" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_VTYTTH}]]></variableExpression>
	</variable>
	<variable name="g2_vttt" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_VTYTTT}]]></variableExpression>
	</variable>
	<variable name="g2_dvktc" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_DVKTC}]]></variableExpression>
	</variable>
	<variable name="g2_thuocktg" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_KTG}]]></variableExpression>
	</variable>
	<variable name="g2_congkham" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_CONGKHAM}]]></variableExpression>
	</variable>
	<variable name="g2_vanchuyen" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_VCHUYEN}]]></variableExpression>
	</variable>
	<variable name="g2_bnct" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_BNCT}]]></variableExpression>
	</variable>
	<variable name="g2_bhtt" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_BHTT}]]></variableExpression>
	</variable>
	<variable name="g2_ngoaids" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{T_NGOAIDS}]]></variableExpression>
	</variable>
	<variable name="f_tongcong" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_TONGCHI}]]></variableExpression>
	</variable>
	<variable name="f_xetnghiem" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_XN}]]></variableExpression>
	</variable>
	<variable name="f_cdha" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_CDHA}]]></variableExpression>
	</variable>
	<variable name="f_thuoc" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_THUOC}]]></variableExpression>
	</variable>
	<variable name="f_mau" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_MAU}]]></variableExpression>
	</variable>
	<variable name="f_ttpt" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_PTTT}]]></variableExpression>
	</variable>
	<variable name="f_vtth" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_VTYTTH}]]></variableExpression>
	</variable>
	<variable name="f_vttt" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_VTYTTT}]]></variableExpression>
	</variable>
	<variable name="f_dvktc" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_DVKTC}]]></variableExpression>
	</variable>
	<variable name="f_thuocktg" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_KTG}]]></variableExpression>
	</variable>
	<variable name="f_congkham" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_CONGKHAM}]]></variableExpression>
	</variable>
	<variable name="f_vanchuyen" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_VCHUYEN}]]></variableExpression>
	</variable>
	<variable name="f_bnct" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_BNCT}]]></variableExpression>
	</variable>
	<variable name="f_bhtt" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_BHTT}]]></variableExpression>
	</variable>
	<variable name="f_ngoaids" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{T_NGOAIDS}]]></variableExpression>
	</variable>
	<variable name="stt" class="java.lang.Integer">
		<variableExpression><![CDATA[$V{stt}+1]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="g1_giuong" class="java.lang.Double" resetType="Group" resetGroup="group_dungtuyen" calculation="Sum">
		<variableExpression><![CDATA[$F{TIENGIUONG}]]></variableExpression>
	</variable>
	<variable name="g2_giuong" class="java.lang.Double" resetType="Group" resetGroup="group_noitinh" calculation="Sum">
		<variableExpression><![CDATA[$F{TIENGIUONG}]]></variableExpression>
	</variable>
	<variable name="f_giuong" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{TIENGIUONG}]]></variableExpression>
	</variable>
	<group name="group_noitinh">
		<groupExpression><![CDATA[$F{GROUPHEADER_NOITINH}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="0" y="0" width="25" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="9ea56103-c61e-40bc-8536-68f66754daab"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GROUPHEADER_NOITINH}.equals(0)?"A":($F{GROUPHEADER_NOITINH}.equals(1)?"B":"C")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="575" y="0" width="25" height="20" uuid="0ec3a941-8eb2-45cb-b4b0-138d2b8f01b7"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_vttt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="329" y="0" width="35" height="20" uuid="236515f2-50c1-44ec-a219-22936d355a0a"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_xetnghiem}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="283" y="0" width="46" height="20" uuid="15a7f187-a854-4db9-9774-450de1063ac2"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_tongcong}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="712" y="0" width="50" height="20" uuid="6bd7db05-b127-4ec0-90b1-41cd1ed6f538"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_bhtt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="25" y="0" width="258" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="104b7e97-3009-4f11-8217-1c9a4c144434"/>
					<box leftPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GROUPHEADER_NOITINH}.equals(0)?"BỆNH NHÂN NỘI TỈNH KHÁM, CHỮA BỆNH BAN ĐẦU":($F{GROUPHEADER_NOITINH}.equals(1)?"BỆNH NHÂN NỘI TỈNH ĐẾN ":"BỆNH NHÂN NGOẠI TỈNH ĐẾN")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="548" y="0" width="27" height="20" uuid="081713be-a349-46c8-9080-776da53283b2"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_thuocktg}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="667" y="0" width="45" height="20" uuid="82f52d05-7c94-4c64-b3c0-2a2e1d7658ee"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_bnct}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="762" y="0" width="40" height="20" uuid="55cbf919-8e78-4ae6-ad9a-d4f20d8f04cd"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_ngoaids}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="459" y="0" width="34" height="20" uuid="b73123f1-a839-4a6c-96b9-b21e77c110f0"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_ttpt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="364" y="0" width="35" height="20" uuid="cfbc6774-d3da-4e1e-b624-808ef4610e73"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_cdha}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="493" y="0" width="30" height="20" uuid="c5b7d0d9-be7f-4314-b61f-4baf1a44a67d"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_vtth}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="435" y="0" width="24" height="20" uuid="e3c45b54-54ff-4802-81aa-21541cd2992e"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_mau}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="634" y="0" width="33" height="20" uuid="8fcd6da7-2e28-49ae-9b83-db7b1cc56e58"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_vanchuyen}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="399" y="0" width="36" height="20" uuid="92aca8ec-19c9-46e5-83bf-2aa04f84a562"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_thuoc}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="523" y="0" width="25" height="20" uuid="6a5d3470-abdf-4d79-b692-41196781f522"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_dvktc}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_noitinh" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="600" y="0" width="34" height="20" uuid="9af5b70b-abae-43c0-a6e3-7fbbf700b1ca"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g2_congkham}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="group_dungtuyen">
		<groupExpression><![CDATA[$F{GROUPHEADER_DUNGTUYEN}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="0" y="0" width="25" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="eacac8ba-c470-4c25-a349-3b1180d142ed"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GROUPHEADER_DUNGTUYEN}.equals(4)?"2":"1"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="399" y="0" width="36" height="20" uuid="924e60e4-f57a-4b81-a50c-1af7341e1e80"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_thuoc}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="364" y="0" width="35" height="20" uuid="50512300-ad80-4a1a-9f2f-f15f4b1e0bec"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_cdha}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="459" y="0" width="34" height="20" uuid="fe3a0190-8f5a-4321-97ac-371aeea2ab56"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_ttpt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="493" y="0" width="30" height="20" uuid="24559571-c3e6-44ba-ae6d-54f6fdf7c3cb"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_vtth}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="523" y="0" width="25" height="20" uuid="217dbf10-4c28-4e5f-8f0d-3967a54fdc80"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_dvktcao}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="634" y="0" width="33" height="20" uuid="7cd677a4-36e8-4c5a-b224-3e5fd6e8edc6"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_vanchuyen}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="283" y="0" width="46" height="20" uuid="786f68e7-a052-4632-b3cb-7ff5d08969c5"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_tongcong}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="25" y="0" width="258" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="e048a300-8d93-4be7-8698-e1d406d50f13"/>
					<box leftPadding="2">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{GROUPHEADER_DUNGTUYEN}.equals(4)?"TRÁI TUYẾN":"ĐÚNG TUYẾN"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="712" y="0" width="50" height="20" uuid="d53cd44e-9b3c-47f4-9615-ecc93a4b62e4"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_bhtt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="329" y="0" width="35" height="20" uuid="6daa30a0-9bba-4add-b5e8-d725aace6108"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_xetnghiem}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="667" y="0" width="45" height="20" uuid="220e9bad-c90e-465e-9e40-695f44935f76"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_bnct}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="575" y="0" width="25" height="20" uuid="c34efd63-e9e3-44a6-b0ca-dfbd70bc4227"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_vttt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="762" y="0" width="40" height="20" uuid="fb3e0c5a-a407-4b2f-bacf-cb84570ea9ff"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_ngoaids}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="435" y="0" width="24" height="20" uuid="959329cc-47b3-49b4-9c28-6e7006d16278"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_mau}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="548" y="0" width="27" height="20" uuid="d8509217-d786-4526-b063-07876a63901c"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_thuocktg}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="group_dungtuyen" pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="600" y="0" width="34" height="20" uuid="623fb7a5-fea7-4d7d-b31b-************"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{g1_congkham}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="126" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="0" width="543" height="18" uuid="cbcf1e26-5419-4210-9c81-a316edead008"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="0" y="18" width="543" height="25" forecolor="#000000" backcolor="#FFFFFF" uuid="d3536a1d-aead-4848-8951-ebed298b2878"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã cơ sở khám chữa bệnh : "+$F{HOSPITAL_CODE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="543" y="0" width="259" height="18" uuid="9b6c93e8-4cc9-40b2-bfe1-147c23e2f800"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Mẫu số : C79a - HD]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="62" width="802" height="24" uuid="110680e2-0c90-4ea2-b9cb-52812ac99519"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="14" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tieude}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="681" y="106" width="121" height="20" uuid="ae36fd82-98e0-49a7-99cd-e3ef1a8b195c"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Đơn vị : đồng]]></text>
			</staticText>
			<staticText>
				<reportElement x="543" y="18" width="259" height="37" uuid="277c8726-ced8-4dc8-9494-9ba9344789d3"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="13" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(Ban hành theo Thông tư số 178/2012/TT-BTC ngày 23/10/2012 của Bộ Tài chính )]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="86" width="802" height="20" uuid="a1a1e300-fbf4-4e8c-a3f4-259417710a22"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày " + new SimpleDateFormat("dd/MM/yyyy").format( $P{tungay})  + " đến ngày " +  new SimpleDateFormat("dd/MM/yyyy").format( $P{denngay})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="123">
			<staticText>
				<reportElement x="25" y="108" width="73" height="15" uuid="38c47443-9c53-4006-9999-32e765ddd44b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[B]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="108" width="25" height="15" uuid="10fb61b7-9344-4993-a50c-de2e5c4128b7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[A]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="25" height="108" uuid="a0ef2b18-b7bc-47ca-aa1f-7b692ef5f129"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="25" y="0" width="73" height="108" forecolor="#000000" backcolor="#FFFFFF" uuid="275deffc-010b-4db1-b8a1-6b9566c9b5ab"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Họ và tên]]></text>
			</staticText>
			<staticText>
				<reportElement x="98" y="108" width="21" height="15" uuid="abeffd7b-9183-46e7-b252-0936e8acb206"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[C]]></text>
			</staticText>
			<staticText>
				<reportElement x="194" y="108" width="28" height="15" uuid="731bda0f-9c9b-451b-acc9-5969c7cfa844"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[G]]></text>
			</staticText>
			<staticText>
				<reportElement x="435" y="108" width="24" height="15" uuid="45239ab1-2423-471a-8f8f-642c5c12e43c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(5)]]></text>
			</staticText>
			<staticText>
				<reportElement x="251" y="108" width="32" height="15" uuid="7943de86-b07a-4e8f-a0f3-34d2eab9a7ec"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[I]]></text>
			</staticText>
			<staticText>
				<reportElement x="329" y="108" width="35" height="15" uuid="f646843c-86be-464a-b0de-5bfd78434568"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(2)]]></text>
			</staticText>
			<staticText>
				<reportElement x="399" y="108" width="36" height="15" uuid="44cbb4cc-1a36-445c-8766-161d0bab9058"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(4)]]></text>
			</staticText>
			<staticText>
				<reportElement x="364" y="108" width="35" height="15" uuid="0256b133-6f22-4fe1-bbc8-f77e723f1ff6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(3)]]></text>
			</staticText>
			<staticText>
				<reportElement x="283" y="108" width="46" height="15" uuid="aa583f98-683f-45c6-a405-bf8865d8be2e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(1)]]></text>
			</staticText>
			<staticText>
				<reportElement x="222" y="108" width="29" height="15" uuid="d1fe7837-6aa5-4811-8d58-8e9d91c8ed97"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[H]]></text>
			</staticText>
			<staticText>
				<reportElement x="140" y="108" width="54" height="15" uuid="adda8bee-2659-40f6-b64c-b67aa4e16e44"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[E]]></text>
			</staticText>
			<staticText>
				<reportElement x="119" y="108" width="21" height="15" uuid="5fa7e644-611b-4191-9f8d-939eceb93e9d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[D]]></text>
			</staticText>
			<staticText>
				<reportElement x="493" y="108" width="30" height="15" uuid="70f0e49b-0691-42ef-845b-7e36f603af36"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(7)]]></text>
			</staticText>
			<staticText>
				<reportElement x="762" y="108" width="40" height="15" uuid="b5ed213f-10c2-4a9f-8963-c381e5bf139e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(15)]]></text>
			</staticText>
			<staticText>
				<reportElement x="459" y="108" width="34" height="15" uuid="bce14cad-0edf-4529-b63d-1c3c18c48326"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(6)]]></text>
			</staticText>
			<staticText>
				<reportElement x="548" y="108" width="27" height="15" uuid="3c36ef92-e0c3-44fd-a447-0088aa9b351e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(9)]]></text>
			</staticText>
			<staticText>
				<reportElement x="712" y="108" width="50" height="15" uuid="31d20793-5ba2-4b96-835d-295d0f0daca1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(14)]]></text>
			</staticText>
			<staticText>
				<reportElement x="667" y="108" width="45" height="15" uuid="15480e77-fcd4-456e-9606-13d765a4988a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(13)]]></text>
			</staticText>
			<staticText>
				<reportElement x="575" y="108" width="25" height="15" uuid="9438172b-7e7f-4a26-98df-f89b7fb0261a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(10)]]></text>
			</staticText>
			<staticText>
				<reportElement x="634" y="108" width="33" height="15" uuid="0934913a-91b0-43c3-8a08-3b3d409165c3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(12)]]></text>
			</staticText>
			<staticText>
				<reportElement x="523" y="108" width="25" height="15" uuid="48ac5df2-852d-41cd-9a19-dd46ccb8124c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(8)]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="108" width="34" height="15" uuid="56234948-078e-43dd-8acb-************"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(11)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="98" y="0" width="42" height="52" forecolor="#000000" backcolor="#FFFFFF" uuid="8e312deb-2560-4a5d-94d0-c04d3fde7518"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Năm sinh]]></text>
			</staticText>
			<staticText>
				<reportElement x="222" y="0" width="29" height="108" uuid="641afaa1-65e6-4cdb-ac6c-a8043b836ff9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Mã bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement x="194" y="0" width="28" height="108" uuid="31d57269-a8c4-4b2f-a509-da20b5fdfbd5"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Mã ĐKBĐ]]></text>
			</staticText>
			<staticText>
				<reportElement x="140" y="0" width="54" height="108" uuid="131a7f9b-5132-49d3-82a7-65c9ddebe895"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Mã thẻ BHYT]]></text>
			</staticText>
			<staticText>
				<reportElement x="119" y="52" width="21" height="56" uuid="ecf642b7-018e-4f0e-bcaa-8226bc33e97e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="251" y="0" width="32" height="108" uuid="6cce0b05-6992-4147-9f87-69d74afc3675"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph leftIndent="5" rightIndent="5"/>
				</textElement>
				<text><![CDATA[Ngày khám]]></text>
			</staticText>
			<staticText>
				<reportElement x="98" y="52" width="21" height="56" uuid="f39ccb74-6091-441b-a63e-5a26a53c0b31"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Nam]]></text>
			</staticText>
			<staticText>
				<reportElement x="548" y="52" width="27" height="56" uuid="2d173727-5d66-43a7-9913-f0fc64ac2710"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Thuốc]]></text>
			</staticText>
			<staticText>
				<reportElement x="399" y="52" width="36" height="56" uuid="e47c4d11-eaf0-4b9e-aefa-e9a64121bf42"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Thuốc]]></text>
			</staticText>
			<staticText>
				<reportElement x="435" y="52" width="24" height="56" uuid="d107dea8-50fd-422a-861a-9c60b88e40ac"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Máu]]></text>
			</staticText>
			<staticText>
				<reportElement x="283" y="26" width="46" height="82" uuid="801a6737-1f53-4bae-b60c-200664cc822f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Tổng cộng]]></text>
			</staticText>
			<staticText>
				<reportElement x="459" y="52" width="34" height="56" uuid="bf66ed00-21ef-4129-82db-b7379e52d7a9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TTPT]]></text>
			</staticText>
			<staticText>
				<reportElement x="364" y="52" width="35" height="56" uuid="4a94b1f7-f11c-4a27-9944-5d24009517ca"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CDHA, TDCN]]></text>
			</staticText>
			<staticText>
				<reportElement x="493" y="52" width="30" height="56" uuid="c85f2cb1-caec-4a16-825d-e3cd9a379d77"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[VTYT]]></text>
			</staticText>
			<staticText>
				<reportElement x="575" y="52" width="25" height="56" uuid="f336a975-9cc6-458b-a62b-6b743f7b543b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[VTYT]]></text>
			</staticText>
			<staticText>
				<reportElement x="329" y="52" width="35" height="56" uuid="06279f47-a97c-4a52-a072-5559f20270c0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Xét nghiệm]]></text>
			</staticText>
			<staticText>
				<reportElement x="523" y="52" width="25" height="56" uuid="98373d65-1b31-4b6d-ab41-b58b61afd73c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[DVKT]]></text>
			</staticText>
			<staticText>
				<reportElement x="329" y="26" width="194" height="26" uuid="de27533e-4926-40a9-9f60-56085031bb21"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Không áp dụng tỷ lệ thanh toán]]></text>
			</staticText>
			<staticText>
				<reportElement x="523" y="26" width="77" height="26" uuid="b9eeeae6-30c8-4eaa-8935-de51208644f4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Thanh toán theo
 tỷ lệ]]></text>
			</staticText>
			<staticText>
				<reportElement x="712" y="0" width="90" height="26" uuid="9cfa6518-beb4-423f-9c76-375e059c6330"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Chi phí đề nghị BHXH thanh toán]]></text>
			</staticText>
			<staticText>
				<reportElement x="762" y="26" width="40" height="82" uuid="70fb3dd8-4967-48c2-ab02-9921416af7ba"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Trong đó chi phí ngoài quỹ định suất]]></text>
			</staticText>
			<staticText>
				<reportElement x="667" y="0" width="45" height="108" uuid="fae2ebc0-9cf3-4719-9313-1de0e29c2d13"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Người bệnh cùng chi trả]]></text>
			</staticText>
			<staticText>
				<reportElement x="634" y="26" width="33" height="82" uuid="c382c2d3-fcc8-4423-8271-377508b73721"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Vận chuyển]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="26" width="34" height="82" uuid="f5c4acde-29d4-4c18-8e75-************"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Tiền khám]]></text>
			</staticText>
			<staticText>
				<reportElement x="712" y="26" width="50" height="82" uuid="4a78e8cd-3a19-4158-bb25-bcc388244885"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Tổng cộng]]></text>
			</staticText>
			<staticText>
				<reportElement x="283" y="0" width="384" height="26" uuid="1da5b242-ba71-4b0f-a58a-dff9acd5e6f9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TỔNG CHI PHÍ KHÁM CHỮA BỆNH BHYT]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="20" uuid="f952208f-64d0-495f-bada-3d15eb4467d4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{stt}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="25" y="0" width="73" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="139bf863-c63b-4bf2-9483-e459b9f5511c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOTEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="98" y="0" width="21" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="2dc6f418-589d-4af2-a4d2-35ec2f18c555"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}.equals(1)?$F{NAMSINH}:null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="119" y="0" width="21" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="236d7174-e71a-42e4-9fe5-0a07dfec4f26"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}.equals(2)?$F{NAMSINH}:null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="140" y="0" width="54" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="200a7721-8fee-4c39-bf3a-58266226f2f8"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MATHE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="194" y="0" width="28" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="c2e76b88-6ad6-4a3a-a587-cd616bce0b7b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_DKBD}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="222" y="0" width="29" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="7ca0e406-6ac5-416e-b29a-06de242e70a2"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MABENH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="251" y="0" width="32" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="f23ac519-5554-4363-8cf5-0694f5c0bff7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYVAO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="283" y="0" width="46" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="e6edec0f-09af-4166-aa9e-91ec6106be8e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_TONGCHI}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="329" y="0" width="35" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="636e0517-0808-4378-9c2c-65c880587893"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_XN}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="364" y="0" width="35" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="b525a1e2-cb64-45a4-930f-df9b83087772"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_CDHA}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="399" y="0" width="36" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="300595ce-28d8-4370-8778-01a78b5cd9ca"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_THUOC}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="435" y="0" width="24" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="02fa0165-ee10-41f3-843a-7175b72812ee"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_MAU}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="459" y="0" width="34" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="f4f3d27d-ded0-454a-ba9d-2b63bf76630e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_PTTT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="493" y="0" width="30" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="275dea20-7330-40bb-ba92-49facdde2a45"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_VTYTTH}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="523" y="0" width="25" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="7b3b38cb-0755-4627-9557-115dba0ecb2c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_DVKTC}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="548" y="0" width="27" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="c3b29637-dcde-4ab0-b583-29a1021631e9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_KTG}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="634" y="0" width="33" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="dc3e5f85-8bdb-42fc-999f-d49b3e83e013"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_VCHUYEN}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="667" y="0" width="45" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="aae5fc1c-fd90-44ee-8c27-d6b5cf5eda11"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_BNCT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="712" y="0" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="5545a103-3d11-4ede-9335-9708f7d1f309"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_BHTT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="762" y="0" width="40" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="554b463d-43e1-45b6-a0d9-f33a29469ce7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_NGOAIDS}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="575" y="0" width="25" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="02fa6805-1fd1-4716-8535-7e74a1ad5700"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_VTYTTT}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="600" y="0" width="34" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="*************-4f11-922b-a8f1d563471a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($F{T_CONGKHAM}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band/>
	</pageFooter>
	<summary>
		<band height="138" splitType="Immediate">
			<staticText>
				<reportElement x="0" y="25" width="272" height="20" uuid="fde68335-41fa-40e3-a4f4-dff6ab505ef7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Tổng số tiền đề nghị thanh toán (viết bằng chữ) :]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="283" height="20" uuid="3f3b01ab-2ef2-46cd-a631-945233cb806c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Tổng cộng A + B + C]]></text>
			</staticText>
			<textField>
				<reportElement mode="Transparent" x="272" y="25" width="530" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="3892001c-3fb6-4434-86df-714e090edc23"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{T_TIEN_BHYT}==null?"0":$F{T_TIEN_BHYT}) +" đồng"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="435" y="0" width="24" height="20" uuid="b6ce834f-6014-46bf-9eb4-daf3d5e7d15e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_mau}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="283" y="0" width="46" height="20" uuid="91d41ec0-a9f1-4945-8e8d-c8ac139031ad"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_tongcong}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="329" y="0" width="35" height="20" uuid="e6fe4138-33c8-4c8f-a84c-818f8102b159"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_xetnghiem}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="364" y="0" width="35" height="20" uuid="00cd585e-b074-4ef6-a6aa-3fa461ab32a0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_cdha}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="399" y="0" width="36" height="20" uuid="af365c21-5942-430b-bb75-4ab15624bd5d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_thuoc}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="459" y="0" width="34" height="20" uuid="a7c3c89c-7c73-4c76-9c45-4d9fdc5416b9"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_ttpt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="493" y="0" width="30" height="20" uuid="cea748e5-f68e-44ed-892c-032533832251"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_vtth}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="575" y="0" width="25" height="20" uuid="289a5b71-7aa4-4d57-8515-54479703fe64"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_vttt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="523" y="0" width="25" height="20" uuid="91cdfbfd-9cbb-42d8-afa6-f4e52d0eb6f1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_dvktc}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="548" y="0" width="27" height="20" uuid="19080cb1-29e5-41f6-8fb4-15e7b9c80d80"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_thuocktg}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="634" y="0" width="33" height="20" uuid="fbf51ebe-cf17-44c6-a934-5a3aa8f6184d"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_vanchuyen}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="667" y="0" width="45" height="20" uuid="5eab6d72-5ccc-4eff-b09b-e05ace9e7dcb"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_bnct}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="712" y="0" width="50" height="20" uuid="3edad0c0-a32c-450d-9077-e4690c96e953"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_bhtt}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="762" y="0" width="40" height="20" uuid="06e38694-04db-468b-a436-e77ca3d51cdf"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_ngoaids}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="0" y="45" width="802" height="93" uuid="ca07a752-0bb4-4705-90ff-6ecc38011c76"/>
				<staticText>
					<reportElement x="0" y="16" width="159" height="15" uuid="1c332c49-e681-4dc1-aca8-8f74e2e86684"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[Người lập biểu]]></text>
				</staticText>
				<staticText>
					<reportElement x="192" y="16" width="168" height="15" uuid="fef9ef82-4f41-4731-bb33-54228f1c09e1"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[Trưởng phòng KHNV]]></text>
				</staticText>
				<staticText>
					<reportElement x="405" y="16" width="164" height="15" uuid="f1acbf42-7b32-4843-9759-11e8f5a5faf8"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[Kế toán trưởng]]></text>
				</staticText>
				<staticText>
					<reportElement x="570" y="1" width="232" height="15" uuid="9fc8848b-6b42-4a0c-9c7f-bd975d7e3a86"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[Ngày ..... tháng ..... năm ..........]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="31" width="159" height="15" uuid="523db3bc-b504-4955-850c-51d89e962f9d"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[( Ký, họ tên )]]></text>
				</staticText>
				<staticText>
					<reportElement x="192" y="31" width="168" height="15" uuid="85aa365c-4071-48c0-b2d7-230e729d31e8"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[( Ký, họ tên )]]></text>
				</staticText>
				<staticText>
					<reportElement x="405" y="31" width="164" height="15" uuid="3189f945-554c-4f7d-8325-6257f5df391f"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[( Ký, họ tên )]]></text>
				</staticText>
				<staticText>
					<reportElement x="570" y="31" width="232" height="15" uuid="8b6cb49b-9025-4e1a-9c04-0aa5ce7e976e"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[( Ký, họ tên, đóng dấu )]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="570" y="16" width="232" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="54d7b8a1-2916-436f-8619-8f34bb5c02c6"/>
					<box>
						<pen lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid"/>
						<leftPen lineStyle="Solid"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid"/>
						<rightPen lineWidth="0.0" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TENCHUCDANH}]]></textFieldExpression>
				</textField>
			</frame>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="600" y="0" width="34" height="20" uuid="04583b83-7512-41de-b14f-b286abdf0090"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new DecimalFormat("#,##0.00").format($V{f_congkham}.doubleValue()).replace(",", "x").replace(".", ",").replace("x", ".").replace(",00", "")]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

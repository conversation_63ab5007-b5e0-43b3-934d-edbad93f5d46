<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DKHN_BaoCaoChuyenVien" pageWidth="900" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="860" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="02afe4c9-87bb-4263-9fdc-5ca4328b421b">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="139"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.export.xlsx.exclude.origin.band.1" value="pageHeader"/>
	<property name="net.sf.jasperreports.export.xlsx.exclude.origin.band.2" value="pageFooter"/>
	<property name="net.sf.jasperreports.export.xlsx.exclude.origin.keep.first.band.3" value="columnHeader"/>
	<property name="net.sf.jasperreports.export.xls.exclude.origin.band.1" value="pageHeader"/>
	<property name="net.sf.jasperreports.export.xls.exclude.origin.band.2" value="pageFooter"/>
	<property name="net.sf.jasperreports.export.xls.exclude.origin.keep.first.band.3" value="columnHeader"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.columns" value="true"/>
	<parameter name="ora_cursor" class="java.sql.ResultSet"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="khoaid" class="java.lang.Long">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="dt_tungay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="dt_denngay" class="java.sql.Timestamp">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="khoaid_TEXT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_doituongbenhnhanid" class="java.lang.Long">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="i_doituongbenhnhanid_TEXT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="phongid" class="java.lang.Long">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="phongid_TEXT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ORACLE_REF_CURSOR" class="java.sql.ResultSet"/>
	<parameter name="VPARS_nhomdichvuid_VALUE" class="java.lang.String"/>
	<parameter name="hinhthucid" class="java.lang.Long">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_khoathuchienid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="VPARS_phongthid_VALUE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call NTU042_TTCHUYENTUYEN2_26320(
$P{[UID]},
$P{[HID]},
$P{[SCH]},
$P{[IP]},
$P{dt_tungay},
$P{dt_denngay},
$P{VPARS_khoathuchienid_VALUE},
$P{VPARS_phongthid_VALUE},
$P{hinhthucid},
$P{VPARS_nhomdichvuid_VALUE},
$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="NGAYVAOVIEN" class="java.lang.String"/>
	<field name="THOIGIANCHUYENVIEN" class="java.lang.String"/>
	<field name="HUONGDIEUTRI" class="java.lang.String"/>
	<field name="KETQUADIEUTRI" class="java.lang.String"/>
	<field name="MA_BHYT" class="java.lang.String"/>
	<field name="MACHANDOANRAVIEN" class="java.lang.String"/>
	<field name="TENBENHVIENCHUYENDEN" class="java.lang.String"/>
	<field name="NGAYVAOKHOA" class="java.lang.String"/>
	<field name="KHOADIEUTRI" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="COSOKB" class="java.lang.String"/>
	<field name="BSCHUYEN" class="java.lang.String"/>
	<field name="NGUOIBAOCAO" class="java.lang.String"/>
	<field name="GIOITINHID" class="java.lang.String"/>
	<field name="LOAIID" class="java.lang.String"/>
	<field name="SOCHUYENVIEN" class="java.lang.String"/>
	<field name="PHONGDIEUTRI" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="82" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="40" width="860" height="22" uuid="93c18462-46fd-4e14-9495-d5aa937993c2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="15" isBold="true"/>
				</textElement>
				<text><![CDATA[TỔNG HỢP THÔNG TIN CHUYẾN NGƯỜI BỆNH ĐI CÁC TUYẾN]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="0" width="860" height="20" uuid="00df3187-e244-4be0-a3d0-67a67e0468ed"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="20" width="860" height="20" uuid="756ecfa6-793c-4494-9ed8-419de244b1a9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="62" width="860" height="20" uuid="9bac1235-56da-4d92-94df-f3a34821fee9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày: " + new SimpleDateFormat("HH:mm:ss dd/MM/yyyy").format( $P{dt_tungay})  + "    đến ngày:   " +  new SimpleDateFormat("HH:mm:ss dd/MM/yyyy").format( $P{dt_denngay})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="42" splitType="Stretch">
			<staticText>
				<reportElement x="594" y="0" width="65" height="21" uuid="e8114eb1-1930-412c-a995-ee8819298de8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Hình thức chuyển]]></text>
			</staticText>
			<staticText>
				<reportElement x="594" y="21" width="31" height="21" uuid="1ce3319c-883e-49dc-a40b-bd1f693ae6d2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Đúng tuyến]]></text>
			</staticText>
			<staticText>
				<reportElement x="625" y="21" width="34" height="21" uuid="31fd5e4b-e1ce-4fec-ba7b-ad64d654e0bb"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Trái tuyến]]></text>
			</staticText>
			<staticText>
				<reportElement x="246" y="0" width="72" height="21" uuid="d86d0820-67e5-4c27-9bfc-8f905ff0ddcb"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tuổi, giới]]></text>
			</staticText>
			<staticText>
				<reportElement x="246" y="21" width="36" height="21" uuid="bb7ace8c-2df7-4c37-9645-a33c79535ad8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Nam]]></text>
			</staticText>
			<staticText>
				<reportElement x="282" y="21" width="36" height="21" uuid="bd349c44-cdaa-47d5-a329-e5aed84c5ac4"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="25" height="42" uuid="ad24f7ab-b4f4-4cc7-8022-b46af62622e2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="779" y="0" width="81" height="42" uuid="68f5c37a-267d-4004-aecb-3eabd25ee5b0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Bác sỹ chuyển]]></text>
			</staticText>
			<staticText>
				<reportElement x="155" y="0" width="91" height="42" uuid="d150b425-efb0-4326-a05a-d52240d0215a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Họ tên bệnh nhân]]></text>
			</staticText>
			<staticText>
				<reportElement x="515" y="0" width="79" height="42" uuid="fa92a5a0-7a85-4f43-9fd6-1b00f393aee2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Chẩn đoán]]></text>
			</staticText>
			<staticText>
				<reportElement x="318" y="0" width="95" height="42" uuid="5f29ea26-6f27-4035-a5cb-6f2278b5f7ad"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã thẻ BHYT]]></text>
			</staticText>
			<staticText>
				<reportElement x="659" y="0" width="120" height="42" uuid="99efbff0-784f-47b2-a9f0-85ee4cdd249e"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên cơ sở khám, chữa bệnh nhận người bệnh]]></text>
			</staticText>
			<staticText>
				<reportElement x="413" y="0" width="102" height="42" uuid="044d6acb-2c34-4532-97d9-c8b73efa18b1"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Khoa/Phòng chuyển BN]]></text>
			</staticText>
			<staticText>
				<reportElement x="25" y="0" width="65" height="42" uuid="584e53ab-b667-4846-9dad-1a283ea46dc0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lưu trữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="90" y="0" width="65" height="42" uuid="e1bf534e-2efe-4cb4-9e87-85b763bb293a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày chuyển]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="18" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="25" height="18" uuid="79bfbda9-80a1-400a-b628-2871c6fd1268"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="779" y="0" width="81" height="18" uuid="185e3c05-fcec-41f0-9dc2-d803eadbdc03"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BSCHUYEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="155" y="0" width="91" height="18" uuid="2855f4cd-33a9-4eb3-a71c-bec50273d158"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="515" y="0" width="79" height="18" uuid="0ace59a2-d1d9-4515-af7f-5f38d9c95223"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MACHANDOANRAVIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="318" y="0" width="95" height="18" uuid="3cc6982a-1ef5-4086-904d-8aeff23e3281"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="246" y="0" width="36" height="18" uuid="d2ae83f1-ceb3-420f-8730-b3020197a3b0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{GIOITINHID}.equals("1")?$F{TUOI}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="659" y="0" width="120" height="18" uuid="4454d5dd-0197-47c3-ac0e-cd4102e5c2ff"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHVIENCHUYENDEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="413" y="0" width="102" height="18" uuid="4875a0e9-053b-4d59-ae40-1a582ba1f623"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{KHOADIEUTRI}.toLowerCase().contains("khoa khám bệnh") || $F{KHOADIEUTRI}.toLowerCase().contains("khoa yêu cầu")) ? $F{PHONGDIEUTRI} : $F{KHOADIEUTRI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="282" y="0" width="36" height="18" uuid="a2c425a5-bf6e-4a81-ba43-38ec9fc03486"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{GIOITINHID}.equals("2")?$F{TUOI}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="594" y="0" width="31" height="18" uuid="62ba21e2-f9a8-4d91-a16d-ed6c9bacec28"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{LOAIID}.equals("1")?"X":"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="625" y="0" width="34" height="18" uuid="bdc66632-aedd-4381-a14e-4c265e83b531"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{LOAIID}.equals("1")?"":"X")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="90" y="0" width="65" height="18" uuid="18c4cedc-b180-4d2b-bf9e-9b47706bd416"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THOIGIANCHUYENVIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="25" y="0" width="65" height="18" uuid="3bacd036-cfe4-4d13-b51f-fa9c32179097"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="false"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOCHUYENVIEN}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="94" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="659" y="0" width="201" height="18" uuid="fa4894df-f25c-421c-a525-14de0bc7f236"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày " + new SimpleDateFormat("dd").format(new Date()) + " tháng " + new SimpleDateFormat("MM").format(new Date()) + " năm " + new SimpleDateFormat("yyyy").format(new Date())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="659" y="18" width="201" height="18" uuid="ebb2ee81-9153-4308-a6b7-3254645fca01"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI LẬP BIỂU]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="659" y="76" width="201" height="18" uuid="4e9455f8-49cd-437e-bad0-2af270fbb421"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOIBAOCAO}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

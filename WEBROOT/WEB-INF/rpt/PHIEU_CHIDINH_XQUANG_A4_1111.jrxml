<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PHIEU_CHIDINH_XQUANG_A4_1111" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="559" leftMargin="26" rightMargin="10" topMargin="20" bottomMargin="10" isSummaryWithPageHeaderAndFooter="true" uuid="18c5e549-94fd-415a-8af9-02c896054c66">
	<property name="ireport.zoom" value="1.3636363636363698"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="maubenhphamid" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="ora_cursor" class="java.sql.ResultSet">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="dept_name" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="subdept_name" class="java.lang.String"/>
	<parameter name="DPAR_ORG_LOGO" class="java.awt.Image"/>
	<parameter name="report_code" class="java.lang.String"/>
	<parameter name="nhomdichvukhambenhid" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call REPORT_PHIEU_XQUANG_1111(null,$P{[HID]},$P{[SCH]},null,$P{maubenhphamid},$P{report_code},$P{nhomdichvukhambenhid},$P{ora_cursor})}]]>
	</queryString>
	<field name="TENGIUONG" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.math.BigDecimal"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="DIACHI" class="java.lang.String"/>
	<field name="TENNGHENGHIEP" class="java.lang.String"/>
	<field name="TEN_DTBN" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="BHYT_KT" class="java.lang.String"/>
	<field name="SOTHE" class="java.lang.String"/>
	<field name="MAVIENPHI" class="java.lang.String"/>
	<field name="NGAYTIEPNHAN" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="SOTHUTU_LAYMAU" class="java.math.BigDecimal"/>
	<field name="SOTHUTU" class="java.lang.String"/>
	<field name="BARCODE" class="java.lang.String"/>
	<field name="PHONGLAYMAU" class="java.lang.String"/>
	<field name="PHONGTHUCHIEN" class="java.lang.String"/>
	<field name="KHAMBENHID" class="java.math.BigDecimal"/>
	<field name="BENHNHANID" class="java.math.BigDecimal"/>
	<field name="TENXETNGHIEM" class="java.lang.String"/>
	<field name="GHICHU" class="java.lang.String"/>
	<field name="NGAYDICHVU" class="java.lang.String"/>
	<field name="DVTUOI" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="SOPHIEU" class="java.lang.String"/>
	<field name="ORG_ADDRESS" class="java.lang.String"/>
	<field name="DOCTOR_NAME" class="java.lang.String"/>
	<field name="CHUANDOANPHU" class="java.lang.String"/>
	<field name="KHOATHUCHIEN" class="java.lang.String"/>
	<field name="SOLUONG" class="java.lang.String"/>
	<field name="THUONG" class="java.lang.String"/>
	<field name="CAPCUU" class="java.lang.String"/>
	<field name="PHONGTHUCHIEN_DC" class="java.lang.String"/>
	<field name="NHOM" class="java.lang.String"/>
	<field name="PHONGKHAM" class="java.lang.String"/>
	<field name="PHONGCHUYENDENID" class="java.lang.String"/>
	<field name="GHICHU_BENHCHINH" class="java.lang.String"/>
	<field name="URL_IMG" class="java.lang.String"/>
	<field name="TEN_NHOM" class="java.lang.String"/>
	<field name="SOPHIEUCHA" class="java.lang.String"/>
	<field name="SDTBENHNHAN" class="java.lang.String"/>
	<field name="KHOAID" class="java.lang.String"/>
	<field name="SOVAOVIEN" class="java.lang.String"/>
	<field name="NAMSINH" class="java.lang.String"/>
	<field name="DOITUONG" class="java.lang.String"/>
	<field name="DONGIA" class="java.math.BigDecimal"/>
	<field name="THANHTIEN" class="java.math.BigDecimal"/>
	<field name="PHUTHU" class="java.math.BigDecimal"/>
	<field name="KHOAKHAM" class="java.lang.String"/>
	<field name="BHYT_LOAIID" class="java.lang.Integer"/>
	<field name="BUONGGIUONG" class="java.lang.String"/>
	<variable name="THANHTIEN" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THANHTIEN}]]></variableExpression>
	</variable>
	<variable name="V_PHUTHU" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{PHUTHU}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="245">
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="15" width="238" height="15" uuid="9482c0f3-328b-4b89-a7d7-91e2cd3aee8f"/>
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="56" width="559" height="28" uuid="c173470f-7e51-46cc-95c1-a2e7c515ff43"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["PHIẾU CHỈ ĐỊNH " +  $F{NHOM}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="124" width="207" height="20" uuid="0681a4d4-3dc2-4fef-b3bd-1ec5187d5cbd"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Địa chỉ:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="43" y="124" width="516" height="20" uuid="96a0210f-b609-4959-8a4b-150b87807b45"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIACHI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="144" width="59" height="20" uuid="d53a54d3-5059-4f62-9d10-52ec5e5ca179"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Chẩn đoán:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="59" y="144" width="500" height="20" uuid="2597b26a-b985-4274-9486-ed3398c61659"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHANDOAN}+($F{GHICHU_BENHCHINH}==null?"":(" ("+$F{GHICHU_BENHCHINH}+")")) + (($F{CHUANDOANPHU}==null?" ":(";"+$F{CHUANDOANPHU})))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="43" y="84" width="516" height="20" uuid="59734d35-008d-4c1e-9686-3370da334fd3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="84" width="43" height="20" uuid="769310cc-2572-4077-bac9-a4f8a0cb659e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Họ tên:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="90" y="104" width="29" height="20" uuid="756bb77f-445f-439b-89a7-6a2edcbd522d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Nam:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="120" y="106" width="16" height="16" uuid="2368e380-1627-4f5f-afb3-708a056f3573"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}.toUpperCase().equals("NAM")?"X":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="104" width="30" height="20" uuid="532acd1f-c24c-42f2-bc65-47f322830646"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Tuổi:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="30" y="104" width="48" height="20" uuid="4a516a9a-724c-42e5-822b-c26d29381e91"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TUOI}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="139" height="15" uuid="625af3bb-4399-4415-9b88-86ad3c3141e5"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="491" y="225" width="68" height="20" uuid="d71700cf-4b0b-4b4b-95b2-78f1c35966fb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[BN trả]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="402" y="15" width="157" height="15" uuid="15c2162f-39d1-421f-8197-4a8b589c0e49"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số BHYT: " + ($F{SOTHE}==null?"":$F{SOTHE})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="402" y="30" width="157" height="13" uuid="9599cf53-187a-4c5d-858b-f8a468fdfdc5"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số bệnh án: " + ($F{MAHOSOBENHAN} != null ? $F{MAHOSOBENHAN} : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="402" y="43" width="157" height="13" forecolor="#999999" uuid="0b58eec2-7f11-46cc-b707-2d10da193e6a"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số phiếu: " + ($F{SOPHIEU} != null ? $F{SOPHIEU} : "")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="225" width="28" height="20" uuid="21543548-ee6e-44c7-b12c-823ac9ef8b0b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="30" width="238" height="13" uuid="f52ed604-9898-4a08-80d8-00f73b0a6afa"/>
				<box leftPadding="1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KHOAKHAM}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="145" y="104" width="22" height="20" uuid="66075f7c-242c-44b5-84ef-3d1c242dd464"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Nữ:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="167" y="106" width="16" height="16" uuid="157538cc-2ff4-491c-bbb2-4bf7856bd437"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}.toUpperCase().equals("NAM")?"":"X"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="205" y="104" width="42" height="20" uuid="bba6dd15-fb85-4b1f-a35c-d3be558663d8"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Giường:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="247" y="104" width="312" height="20" isRemoveLineWhenBlank="true" uuid="c939f089-efa3-43a1-a8b8-4d3ab921583c"/>
				<box leftPadding="1"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BUONGGIUONG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="196" width="257" height="20" uuid="60f7eae2-178b-43e7-b46e-6796f36263e4"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Thời gian lấy mẫu:.............................................]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="170" width="43" height="20" uuid="f6e2823d-87bd-4342-bee8-79794f17aed5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Ghi chú:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="298" y="196" width="257" height="20" uuid="530fcfcc-d626-4741-bdc7-1327dfec0ec1"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Người lấy mẫu:..............................................]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="28" y="225" width="270" height="20" uuid="cd10d3bf-07e0-43d7-80cc-ef8e685fc6b9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Nội dung]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="314" y="225" width="177" height="20" uuid="de8a4ef9-2b59-44ff-b4f7-aaf2908908db"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Nơi thực hiện]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="402" y="0" width="157" height="15" uuid="38f6b2b7-10bc-432c-bbab-9b4dc3307beb"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mã y tế: " + ($F{MABENHNHAN}==null?"":$F{MABENHNHAN})]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="22" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="28" y="0" width="270" height="14" uuid="ae154f2b-5f3d-4138-be78-294abc6cfdde"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENXETNGHIEM}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="14" width="559" height="5" uuid="f3ed8366-4253-40eb-acfb-13cf33fb8df1"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Dashed" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="491" y="0" width="68" height="14" uuid="1065f073-2460-4f54-a13e-95798b305448"/>
				<box rightPadding="10">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANHTIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="28" height="14" uuid="6a92591c-6733-4cef-9800-01f4f19309b4"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="314" y="0" width="177" height="14" uuid="17886c68-64f5-411a-a068-6b4b2a179ae5"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONGTHUCHIEN}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="108">
			<staticText>
				<reportElement positionType="Float" x="350" y="38" width="209" height="15" uuid="4988b46a-eee3-4cdd-951f-7444cfc8bf07"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Bác sĩ điều trị]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="350" y="23" width="209" height="15" uuid="1cf89fe1-73b9-4773-ba43-95b348282cd4"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYDICHVU}.substring( 11, 16 )+" Ngày "+ $F{NGAYDICHVU}.substring( 0, 2 )+" tháng "+ $F{NGAYDICHVU}.substring( 3, 5 )+" năm "+ $F{NGAYDICHVU}.substring( 6, 10 )]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="351" y="93" width="209" height="15" uuid="5edf20b1-3f26-4e44-84c8-cac4dc43095b"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DOCTOR_NAME}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="397" y="2" width="93" height="16" isRemoveLineWhenBlank="true" uuid="6616d839-c99f-4d81-ac3d-a1236cb8323c"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Cộng:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="#,##0.##" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="491" y="2" width="68" height="16" isRemoveLineWhenBlank="true" uuid="4cf63632-d96a-48eb-a288-31ed27d93fcd"/>
				<box rightPadding="10">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{THANHTIEN}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

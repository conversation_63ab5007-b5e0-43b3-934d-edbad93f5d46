<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BC_TONG_CP_NHOMDV_THEOKHOA_A4_944" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="02afe4c9-87bb-4263-9fdc-5ca4328b421b">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="parent_name" class="java.lang.String"/>
	<parameter name="org_name" class="java.lang.String"/>
	<parameter name="tungay" class="java.sql.Timestamp"/>
	<parameter name="denngay" class="java.sql.Timestamp"/>
	<parameter name="[UID]" class="java.lang.String"/>
	<parameter name="[HID]" class="java.lang.String"/>
	<parameter name="[SCH]" class="java.lang.String"/>
	<parameter name="[IP]" class="java.lang.String"/>
	<parameter name="VPARS_subdtbnid_VALUE" class="java.lang.String"/>
	<parameter name="VPARS_phongkhamid_VALUE" class="java.lang.String"/>
	<parameter name="ORACLE_REF_CURSOR" class="java.sql.ResultSet"/>
	<queryString language="plsql">
		<![CDATA[{call vpi_bc_thcp_dvkt_26060($P{[UID]},$P{[HID]},$P{[SCH]},$P{[IP]},$P{tungay},$P{denngay},
$P{VPARS_subdtbnid_VALUE},$P{VPARS_phongkhamid_VALUE},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="NGUOIBAOCAO" class="java.lang.String"/>
	<field name="KETOANTRUONG" class="java.lang.String"/>
	<field name="GIAMDOC" class="java.lang.String"/>
	<field name="TENCHUCDANH" class="java.lang.String"/>
	<field name="TENKHOA" class="java.lang.String"/>
	<field name="TENPHONG" class="java.lang.String"/>
	<field name="LOAIPTTT" class="java.lang.String"/>
	<field name="NHOMDICHVU" class="java.lang.String"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<field name="MAHOSOBENHAN" class="java.lang.String"/>
	<field name="TENBENHNHAN" class="java.lang.String"/>
	<field name="SOLUUTRU" class="java.lang.String"/>
	<field name="NAMSINH" class="java.lang.String"/>
	<field name="GIOITINH" class="java.lang.String"/>
	<field name="MA_BHYT" class="java.lang.String"/>
	<field name="NGAYVAOVIEN" class="java.lang.String"/>
	<field name="NGAYRAVIEN" class="java.lang.String"/>
	<field name="THANH_TIEN" class="java.math.BigDecimal"/>
	<field name="SOLUONG" class="java.math.BigDecimal"/>
	<field name="DON_GIA" class="java.math.BigDecimal"/>
	<variable name="GRP_DTN_NUM_1" class="java.lang.Integer" incrementType="Group" incrementGroup="Nhom" calculation="Count">
		<variableExpression><![CDATA[1]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<variable name="GRP_COUNT_DICHVU" class="java.lang.Integer" resetType="Group" resetGroup="Loai" incrementType="Group" incrementGroup="Dichvu" calculation="Count">
		<variableExpression><![CDATA[1]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<variable name="DICHVU_SOLUONG" class="java.math.BigDecimal" resetType="Group" resetGroup="Dichvu" calculation="Sum">
		<variableExpression><![CDATA[$F{SOLUONG}]]></variableExpression>
	</variable>
	<variable name="DICHVU_DONGIA" class="java.math.BigDecimal" resetType="Group" resetGroup="Dichvu" calculation="Sum">
		<variableExpression><![CDATA[$F{DON_GIA}]]></variableExpression>
	</variable>
	<variable name="DICHVU_THANHTIEN" class="java.math.BigDecimal" resetType="Group" resetGroup="Dichvu" calculation="Sum">
		<variableExpression><![CDATA[$F{THANH_TIEN}]]></variableExpression>
	</variable>
	<variable name="LOAI_SOLUONG" class="java.math.BigDecimal" resetType="Group" resetGroup="Loai" calculation="Sum">
		<variableExpression><![CDATA[$F{SOLUONG}]]></variableExpression>
	</variable>
	<variable name="LOAI_DONGIA" class="java.math.BigDecimal" resetType="Group" resetGroup="Loai" calculation="Sum">
		<variableExpression><![CDATA[$F{DON_GIA}]]></variableExpression>
	</variable>
	<variable name="LOAI_THANHTIEN" class="java.math.BigDecimal" resetType="Group" resetGroup="Loai" calculation="Sum">
		<variableExpression><![CDATA[$F{THANH_TIEN}]]></variableExpression>
	</variable>
	<variable name="NHOM_SOLUONG" class="java.math.BigDecimal" resetType="Group" resetGroup="Nhom" calculation="Sum">
		<variableExpression><![CDATA[$F{SOLUONG}]]></variableExpression>
	</variable>
	<variable name="NHOM_DONGIA" class="java.math.BigDecimal" resetType="Group" resetGroup="Nhom" calculation="Sum">
		<variableExpression><![CDATA[$F{DON_GIA}]]></variableExpression>
	</variable>
	<variable name="NHOM_THANHTIEN" class="java.math.BigDecimal" resetType="Group" resetGroup="Nhom" calculation="Sum">
		<variableExpression><![CDATA[$F{THANH_TIEN}]]></variableExpression>
	</variable>
	<variable name="VSOLUONG" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{SOLUONG}]]></variableExpression>
	</variable>
	<variable name="VDONGIA" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{DON_GIA}]]></variableExpression>
	</variable>
	<variable name="VTHANHTIEN" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{THANH_TIEN}]]></variableExpression>
	</variable>
	<group name="Nhom">
		<groupExpression><![CDATA[$F{NHOMDICHVU}]]></groupExpression>
		<groupHeader>
			<band height="36">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="40" y="0" width="126" height="18" uuid="3a08cce2-bbf4-4279-bd91-1459e5ccae07"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NHOMDICHVU}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="40" height="18" uuid="d4547dc7-ae01-45f9-9e25-f4f5d5f26e00"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="10" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789".charAt($V{GRP_DTN_NUM_1} - 1)]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="40" y="18" width="126" height="18" uuid="9a098406-b044-42bb-bce0-c10991965d08"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TENKHOA}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="18" width="40" height="18" uuid="36625b1f-26db-4c41-9e67-701497e8b3e7"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA["I"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="346" y="0" width="70" height="18" uuid="bff79af8-de5b-4084-9e07-9ac7c824834d"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="226" y="0" width="50" height="18" uuid="d779b1bb-0cdc-4181-8d7c-1dea25c6c3de"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="166" y="0" width="60" height="18" uuid="0319d2b3-fa0f-4bbe-806f-c93185a34135"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="506" y="0" width="69" height="18" uuid="a273c602-1c7f-43bb-91bc-57bff35a2e67"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="416" y="0" width="45" height="18" uuid="dd32a89c-d445-4920-a52a-a8db082c0c3d"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="311" y="0" width="35" height="18" uuid="f1dbef63-7737-4893-963a-52b85b70e3d4"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="461" y="0" width="45" height="18" uuid="b183b118-efbc-4b78-a278-e04aa11efe61"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="276" y="0" width="35" height="18" uuid="fb287797-b838-4552-ba89-6f06ae892a4d"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="346" y="18" width="70" height="18" uuid="d78ab8a3-873f-47d6-ba5e-ec19af1f82a0"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="226" y="18" width="50" height="18" uuid="132cb100-b2dc-4e2e-9f03-f40a2cb8a13e"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="166" y="18" width="60" height="18" uuid="48592132-0f2f-43f1-bccf-078480d00e5e"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="506" y="18" width="69" height="18" uuid="4f6fe877-261e-4f1d-b985-abb8766eb384"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="416" y="18" width="45" height="18" uuid="446cc853-0bc3-46a8-8f80-1f2b1ef97be6"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="311" y="18" width="35" height="18" uuid="de97e40c-527b-435e-8007-267e5cfae7cd"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="461" y="18" width="45" height="18" uuid="ef53eac7-7539-4c34-9f0c-c352842c5296"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="276" y="18" width="35" height="18" uuid="349e60d9-395c-4724-bccb-02cb88dc1e37"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="664" y="0" width="50" height="18" uuid="a3dbf39e-96f9-480a-b22e-34f6d37283a7"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_THANHTIEN}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{NHOM_THANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="764" y="0" width="38" height="18" uuid="6c59ad96-deef-4669-80aa-5e1fcb5f9e8d"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_SOLUONG}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{NHOM_SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="714" y="0" width="50" height="18" uuid="c9abbfed-435a-42cc-80d9-1bca0f0a4fd5"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_THANHTIEN}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{NHOM_THANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="575" y="0" width="39" height="18" uuid="0cc2a90a-5b8a-4e94-bd8d-1bb85491ec96"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_SOLUONG}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{NHOM_SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="614" y="0" width="50" height="18" uuid="a716ca61-08da-441a-a3e7-a76a9d7c77c4"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="614" y="18" width="50" height="18" uuid="f07623c2-5cc7-4988-8fe2-f3a6081c3b0b"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="714" y="18" width="50" height="18" uuid="6e4ff2b3-5f48-4df3-b2e9-4d23af811470"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_THANHTIEN}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{NHOM_THANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="664" y="18" width="50" height="18" uuid="bf9712f5-198e-490d-9fb2-d94a7c8361cd"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_THANHTIEN}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{NHOM_THANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="764" y="18" width="38" height="18" uuid="914d9d21-6a12-4490-8c5f-a69264aadd6a"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_SOLUONG}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{NHOM_SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Nhom" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="575" y="18" width="39" height="18" uuid="8e9e0c9d-3d2b-4a34-ad77-84bf7369d04b"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{NHOM_SOLUONG}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{NHOM_SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="Khoa">
		<groupExpression><![CDATA[$F{TENKHOA}]]></groupExpression>
	</group>
	<group name="Loai">
		<groupExpression><![CDATA[$F{LOAIPTTT}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="40" y="0" width="126" height="18" uuid="30fbf116-3939-4948-b140-d3820d2fae90"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LOAIPTTT}==null?"Loại: ":"Loại: "+$F{LOAIPTTT}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="40" height="18" uuid="aade68b8-3673-4450-b643-6a78c4d5a97e"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="346" y="0" width="70" height="18" uuid="ecc08c6a-e238-445f-b86d-6503167df517"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="226" y="0" width="50" height="18" uuid="36e7f4d7-3a97-4e16-87b0-deec08d603b2"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="166" y="0" width="60" height="18" uuid="970b95e7-f5ad-4edc-9443-07db78bcd57d"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="506" y="0" width="69" height="18" uuid="e2546cf5-60ae-4598-bb0a-0dd76fa58368"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Loai" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="575" y="0" width="39" height="18" uuid="1117a3ac-f137-4369-bfaa-2d3479155168"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{LOAI_SOLUONG}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{LOAI_SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Loai" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="614" y="0" width="50" height="18" uuid="1c212323-fe92-42ae-ad91-d4f58b21837e"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="416" y="0" width="45" height="18" uuid="e0b3c57c-41e4-4c34-a2dd-cd57747bc260"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Loai" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="764" y="0" width="38" height="18" uuid="ee1f7988-3ac8-46e9-880c-f5bc998a7b6b"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{LOAI_SOLUONG}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{LOAI_SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Loai" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="664" y="0" width="50" height="18" uuid="b69ba007-cc7b-4e8f-972b-b42c5f1ef86a"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{LOAI_THANHTIEN}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{LOAI_THANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="311" y="0" width="35" height="18" uuid="53d2ea28-0bba-43c6-86eb-e942ef90483f"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="461" y="0" width="45" height="18" uuid="f985e09a-b305-42a3-b10b-6723a970bdf7"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Loai" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="714" y="0" width="50" height="18" uuid="0895cc42-22ec-473f-92bf-536b76ae3ea2"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{LOAI_THANHTIEN}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{LOAI_THANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="276" y="0" width="35" height="18" uuid="e2c31005-14e0-4fd0-8b7b-5dcb596b27b7"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="Dichvu">
		<groupExpression><![CDATA[$F{TENDICHVU}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="40" y="0" width="126" height="18" uuid="b4af7ca8-26a1-4045-830a-f346c202673f"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TENDICHVU}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Dichvu" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="40" height="18" uuid="49f3a88b-12a0-4509-a2c8-6fe483a1e532"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{GRP_COUNT_DICHVU}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="346" y="0" width="70" height="18" uuid="2f8f432f-cff6-4926-a17b-d69aa74fae90"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="226" y="0" width="50" height="18" uuid="288203b0-b352-4eff-a1ca-fa43844d5eaa"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="166" y="0" width="60" height="18" uuid="18e68a10-2f90-4873-8e3f-475ed2939651"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="506" y="0" width="69" height="18" uuid="c15977df-8669-442f-abb3-176709cba11f"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Dichvu" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="575" y="0" width="39" height="18" uuid="c886a354-2e02-4069-ae0a-6adc1c5da85a"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{DICHVU_SOLUONG}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{DICHVU_SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Dichvu" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="614" y="0" width="50" height="18" uuid="81d9a57f-efd2-4bb0-9e83-5118be849320"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="416" y="0" width="45" height="18" uuid="e6723c51-e1b1-4f8c-97bd-ef192cf38d7c"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Dichvu" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="764" y="0" width="38" height="18" uuid="cc1a4c0a-c841-4473-9206-3fc5fb6f3e61"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{DICHVU_SOLUONG}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{DICHVU_SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Dichvu" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="664" y="0" width="50" height="18" uuid="a26f6a90-174e-4be7-882f-60d5f701650a"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{DICHVU_THANHTIEN}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{DICHVU_THANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="311" y="0" width="35" height="18" uuid="1fffaabd-b73d-4130-98dd-a7db9b1a0103"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="461" y="0" width="45" height="18" uuid="bd13c7d1-eaad-4d05-8fdb-ada5e9867b4e"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="Dichvu" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="714" y="0" width="50" height="18" uuid="eacc45f0-09b2-443a-8334-2442f0000e90"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="false"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{DICHVU_THANHTIEN}]]></textFieldExpression>
					<patternExpression><![CDATA[$V{DICHVU_THANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="276" y="0" width="35" height="18" uuid="00baff66-0123-42a2-822a-f033ebb03109"/>
					<box>
						<topPen lineWidth="0.5" lineColor="#999999"/>
						<leftPen lineWidth="0.5" lineColor="#999999"/>
						<bottomPen lineWidth="0.5" lineColor="#999999"/>
						<rightPen lineWidth="0.5" lineColor="#999999"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="8" isBold="true"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="72" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="226" height="18" uuid="5dd7b8f7-7d19-47c3-9d87-f1374092fc6d"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parent_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="18" width="226" height="18" uuid="de95e446-8d0f-4f12-ae76-************"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{org_name}.toUpperCase()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="54" width="802" height="18" uuid="c1cadd1b-20f1-4adc-a38a-e73381c4d528"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Từ ngày " + new SimpleDateFormat("dd/MM/yyyy").format($P{tungay}) + " đến ngày " + new SimpleDateFormat("dd/MM/yyyy").format($P{denngay})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="36" width="802" height="18" uuid="d44781f5-ae83-4abc-81e9-9d3f1c878e1e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["BÁO CÁO DOANH THU DỊCH VỤ KỸ THUẬT NGOÀI BẢO HIỂM"]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="34" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="40" height="34" uuid="ad24f7ab-b4f4-4cc7-8022-b46af62622e2"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="346" y="0" width="70" height="34" uuid="3d0eb0b3-6b21-46d1-9851-c427d90378cc"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Số thẻ BHYT]]></text>
			</staticText>
			<staticText>
				<reportElement x="40" y="0" width="126" height="34" uuid="10ac3825-7f56-469a-b9c8-5d067148eff9"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Khoa phòng]]></text>
			</staticText>
			<staticText>
				<reportElement x="311" y="0" width="35" height="34" uuid="85c0b247-89ab-4ba9-9f66-811fe1a891cc"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Giới tính]]></text>
			</staticText>
			<staticText>
				<reportElement x="416" y="0" width="45" height="34" uuid="c6ab32eb-20b6-40c0-be73-cf88baa778ff"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày vào viện]]></text>
			</staticText>
			<staticText>
				<reportElement x="506" y="0" width="69" height="34" uuid="aa71bb7a-fe79-4798-bb2a-ef0fd5dc727c"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Loại PTTT]]></text>
			</staticText>
			<staticText>
				<reportElement x="575" y="0" width="39" height="34" uuid="d9311268-3eca-4688-9c0b-a3dcd6ad17c2"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="461" y="0" width="45" height="34" uuid="b7dcffbd-e1f2-481e-a90d-e983719af791"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày ra viện]]></text>
			</staticText>
			<staticText>
				<reportElement x="166" y="0" width="60" height="34" uuid="6d9a9064-defd-4cd8-8498-f23bde627830"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã KCB]]></text>
			</staticText>
			<staticText>
				<reportElement x="276" y="0" width="35" height="34" uuid="453eaca2-95b3-43be-b7cc-94ac86539f39"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Năm sinh]]></text>
			</staticText>
			<staticText>
				<reportElement x="226" y="0" width="50" height="34" uuid="2513a81a-3b0e-4327-a683-238c74469f96"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã lưu trữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="614" y="0" width="50" height="34" uuid="463c1b94-59a9-45d3-8a6d-97f1793a5b69"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn giá]]></text>
			</staticText>
			<staticText>
				<reportElement x="664" y="0" width="50" height="34" uuid="8a76bf37-1640-4e63-839c-0e6699620893"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Thành tiền]]></text>
			</staticText>
			<staticText>
				<reportElement x="714" y="0" width="50" height="34" uuid="63b4ce65-33ee-41b6-922d-d637ec0206cc"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Chi phí]]></text>
			</staticText>
			<staticText>
				<reportElement x="764" y="0" width="38" height="34" uuid="9655e6b5-76e4-46c8-a72c-56f80c96ea59"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="18" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="40" height="18" uuid="c46b79ef-fc17-48b7-8e63-d5921585b0ae"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{Dichvu_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="40" y="0" width="126" height="18" uuid="39af328a-8779-4213-9f94-74458682c27d"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="311" y="0" width="35" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="1be13d8a-09bf-47fe-a3ae-5bf6cf94449d"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIOITINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="346" y="0" width="70" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="3dc30b8d-864b-46bb-a203-2c51c48037e5"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_BHYT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="416" y="0" width="45" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="d0b1580f-4716-4087-960a-50d26abccb02"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYVAOVIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="506" y="0" width="69" height="18" uuid="d72c1097-c9b3-4139-9f4c-a9c60dabe8fe"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LOAIPTTT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="575" y="0" width="39" height="18" uuid="9e978a76-36ff-48b6-a2ae-63c1768b3e17"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUONG}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="461" y="0" width="45" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="bdcdf607-c9b7-4022-baeb-63be088070a7"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYRAVIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="166" y="0" width="60" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="c4d0b23e-5b47-4a7f-9e25-fb8ddf5f25c6"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAHOSOBENHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="276" y="0" width="35" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="d24a01b1-1758-4650-ba6d-bd0af6094fbb"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single" leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NAMSINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="226" y="0" width="50" height="18" uuid="b2253e08-bb51-45cf-918d-8b6f0555dc1f"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUUTRU}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="614" y="0" width="50" height="18" uuid="b98e32a4-a4bf-4804-b21f-4bebd133cd53"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DON_GIA}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{DON_GIA}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="664" y="0" width="50" height="18" uuid="7af9ca2f-b005-4592-bf0b-f306db0a73be"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANH_TIEN}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{THANH_TIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="714" y="0" width="50" height="18" uuid="ddf0e68f-f73e-4e00-8bb7-61ddd40d56d4"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANH_TIEN}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{THANH_TIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="764" y="0" width="38" height="18" uuid="83060ffb-2ba8-47b9-b58b-dbd738e38582"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUONG}]]></textFieldExpression>
				<patternExpression><![CDATA[$F{SOLUONG}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="126" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="506" y="0" width="69" height="18" uuid="f77fc299-1bf0-41bd-ac86-0426338fa5bf"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="166" height="18" uuid="448f8491-42a3-47d7-aadd-308125afa73f"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Tổng Cộng]]></text>
			</staticText>
			<staticText>
				<reportElement x="575" y="41" width="227" height="18" uuid="417c9fc3-1432-4945-9c85-c9dfc7a239d3"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[NGƯỜI LẬP BIỂU]]></text>
			</staticText>
			<staticText>
				<reportElement x="276" y="41" width="230" height="18" uuid="c76b6134-d672-4bb7-91bb-a25127194124"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[KẾ TOÁN TRƯỞNG]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="575" y="0" width="39" height="18" uuid="61001be3-edef-49a4-8c56-248d5f82fae7"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{VSOLUONG}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="575" y="108" width="227" height="18" uuid="8a240a67-43be-4ed6-96f9-6ddc3c2516c6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOIBAOCAO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="276" y="108" width="230" height="18" uuid="70b62fd2-3ff3-4217-b034-cb7ee78ba12f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KETOANTRUONG}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="108" width="226" height="18" uuid="5d4625e1-0499-418d-8d8b-07635b9e1b61"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIAMDOC}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="41" width="226" height="18" uuid="3f2825b7-2099-4dee-9b15-98265601dc10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TENCHUCDANH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="311" y="0" width="35" height="18" uuid="7cc6be40-ac0c-4e46-bbb9-56c8c63d0dd6"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="346" y="0" width="70" height="18" uuid="8c3d64ca-6b07-42ef-9b46-b940a85ad220"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="416" y="0" width="45" height="18" uuid="bb22c0f4-abdb-4cfa-a850-cc91c53c6386"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="461" y="0" width="45" height="18" uuid="e1d72bce-d5ad-40c3-8a68-99cfd7f4bea9"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="166" y="0" width="60" height="18" uuid="d1564c6a-fa93-4d49-a891-ab6ad1bc896a"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="276" y="0" width="35" height="18" uuid="b1720951-e8fb-408b-b5db-2fe51a5f8cfb"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="226" y="0" width="50" height="18" uuid="95c8767c-93b3-4d0d-b0a0-cb863bc1fea4"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="575" y="23" width="227" height="18" uuid="6a05436d-4136-4faf-906a-c141aa80a180"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Kỳ Anh, ngày " + new SimpleDateFormat("dd").format(new Date()) + " tháng " + new SimpleDateFormat("MM").format(new Date()) + " năm " + new SimpleDateFormat("yyyy").format(new Date())]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="614" y="0" width="50" height="18" uuid="6aaaac3c-26c5-4b4d-8a14-4f7d293579ad"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="664" y="0" width="50" height="18" uuid="e64ef901-c545-452f-9c9b-c395811aeb61"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{VTHANHTIEN}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{VTHANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="714" y="0" width="50" height="18" uuid="2f650502-5200-4d19-9430-53df741791bb"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{VTHANHTIEN}]]></textFieldExpression>
				<patternExpression><![CDATA[$V{VTHANHTIEN}.toString().contains(".")?"#,##0.##":"#,##0"]]></patternExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="764" y="0" width="38" height="18" uuid="ed7a4987-816b-4ddf-8a06-ee780ddb098f"/>
				<box>
					<topPen lineWidth="0.5" lineColor="#999999"/>
					<leftPen lineWidth="0.5" lineColor="#999999"/>
					<bottomPen lineWidth="0.5" lineColor="#999999"/>
					<rightPen lineWidth="0.5" lineColor="#999999"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{VSOLUONG}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>

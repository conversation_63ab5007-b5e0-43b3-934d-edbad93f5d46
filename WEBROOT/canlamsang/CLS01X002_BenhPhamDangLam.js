/**
Script: <PERSON><PERSON> sách bệnh nhân được chỉ định xét nghiệm
Author: VietDA
Date created: 04-08-2016
Change Log:
  Date          Version  Modifier	Descriptions
  04-08-2016    1.0      VietDA		Tạo mới file
  18-09-2017	1.1		 TUYENDV	Sửa tách phiếu xét nghiệm bv mắt hà nam
  20-10-2017	1.1		 NGHIANT	Thêm tab xem các dịch vụ đã chỉ định cận lâm sàng 
*/

var ctl_ar=[{type:'buttongroup', id:'btnInPhieu', icon:'print', text:' In phiếu',
		children:[
			{id:'btnPhieuInBarcode', icon:'print', text:'Barcode',hlink:'#'},
			{id:'btnPhieuIn27BV01', icon:'print', text:'Phiếu xét nghiệm',hlink:'#'},
			{id:'btnPhieuIn27BV01DT', icon:'print', text:'Phiếu xét nghiệm đặc thù',hlink:'#'},
			{id:'btnPhieuIn27BV01PCR', icon:'print', text:'Phiếu xét nghiệm PCR',hlink:'#'},// nghiant L2K74TW-594 
			{id:'btnPhieuIn27BV01TEST_TAMLY', icon:'print', text:'Phiếu test tâm lý',hlink:'#'},// nghiant HISL2TK-571
			{id:'btnPhieuIn27BV01TV_TAMLY', icon:'print', text:'Phiếu tham vấn tâm lý',hlink:'#'},// nghiant HISL2TK-571
			{id:'btnPhieuIn28BV01', icon:'print', text:'Phiếu xét nghiệm huyết học',hlink:'#'},
			{id:'btnPhieuIn29BV01', icon:'print', text:'Phiếu xét nghiệm huyết tủy đồ',hlink:'#'},
			{id:'btnPhieuIn30BV01', icon:'print', text:'Phiếu xét nghiệm chẩn đoán rối loạn đông cầm máu',hlink:'#'},
			{id:'btnPhieuIn31BV01', icon:'print', text:'Phiếu xét nghiệm sinh thiết tủy xương',hlink:'#'},
			{id:'btnPhieuIn32BV01', icon:'print', text:'Phiếu xét nghiệm nước dịch',hlink:'#'},
			{id:'btnPhieuIn33BV01', icon:'print', text:'Phiếu xét nghiệm hóa sinh máu',hlink:'#'},
			{id:'btnPhieuIn34BV01', icon:'print', text:'Phiếu xét nghiệm hóa sinh nước tiểu',hlink:'#'},
			{id:'btnPhieuIn34BV01HSMD', icon:'print', text:'Phiếu xét nghiệm hóa sinh miễn dịch',hlink:'#'},
			{id:'btnPhieuIn35BV01', icon:'print', text:'Phiếu xét nghiệm vi sinh',hlink:'#'},
			{id:'btnPhieuIn35BV01VINAM', icon:'print', text:'Phiếu xét nghiệm vi nấm',hlink:'#'},
			{id:'btnPhieuIn36BV01', icon:'print', text:'Phiếu xét nghiệm giải phẫu bệnh sinh thiết',hlink:'#'},
			{id:'btnPhieuIn36BV01_', icon:'print', text:'Phiếu xét nghiệm giải phẫu bệnh sinh thiết TH',hlink:'#'},
			{id:'btnPhieuIn37BV01', icon:'print', text:'Phiếu xét nghiệm giải phẫu bệnh khám nghiệm tử thi',hlink:'#'},
			{id:'btnPhieuIn27BV01AFB', icon:'print', text:'Phiếu xét nghiệm AFB',hlink:'#'},
			{id:'btnPhieuIn27BV01NuoiCay', icon:'print', text:'Phiếu xét nghiệm nuôi cấy',hlink:'#'},//nghiant 04082017
			{id:'btnPhieuInPhieuKQNS', icon:'print', text:'Phiếu xét nghiệm vi khuẩn nhuộm soi BS',hlink:'#'},
			{id:'btnPhieuIn27BV01KhangSinhDo', icon:'print', text:'Phiếu xét nghiệm kháng sinh đồ',hlink:'#'},//nghiant 07082017 
			{id:'btnPhieuInHoaSinh', icon:'print', text:'Phiếu xét nghiệm hóa sinh',hlink:'#'},
			{id:'btnPhieuInMienDich', icon:'print', text:'Phiếu kết quả xét nghiệm miễn dịch',hlink:'#'},
			{id:'btnPhieuInXNTeBao', icon:'print', text:'Phiếu kết quả xét nghiệm tế bào',hlink:'#'}, // truongle - 06/05/2019
			{id:'btnPhieuInXNSangLocSoSinh3', icon:'print', text:'Phiếu kết quả xét nghiệm sàng lọc sơ sinh 3',hlink:'#'},
			{id:'btnPhieuInXNSangLocSoSinh5', icon:'print', text:'Phiếu kết quả xét nghiệm sàng lọc sơ sinh 5',hlink:'#'},
			{id:'btnPhieuInXNNongDoCon', icon:'print', text:'Phiếu xét nghiệm nồng độ cồn trong máu',hlink:'#'}
	    ]},
        {type:'button', id:'btnGoiKham', icon:'volume-up', text:'Gọi BN'},
		{type:'button', id:'btnXuatManHinh', icon:'dsbenhnhan', text:' DS BN'},
		{type:'button', id:'btnThongKe', icon:'list-alt', text:' Thống kê'},
		{type:'textbox', id:'txtTimKiem', icon:'remove', text:'Nhập thông tin phiếu chỉ định để tìm kiếm'},
		{type:'button', id:'btnTiepNhanTheoLo', icon:'nhapkho', text:' Tiếp nhận theo lô'},
		{type:'button', id:'btnDichVu', icon:'nhapkho', text:' Dịch vụ'},
		{type:'button',id:'btnInBarcode',icon:'',text:'In Barcode(F4)'},
		{type:'label',id:'lblInfo',icon:'',text:'XN'}
];

function DanhSachBenhPham(opt){	
	this.load = doLoad;
	
	var tab1Click = 0;
	var tab2Click = 0;
	var tab3Click = 0;
	var tab4Click = 0;  //nghiant 25102017 
	var searchType = 0; // 0: normal search; 1: advanced search
	
	var bnGioiTinh = 1;
	var bnTuoi = 0;
	
	var userRights = getParameterByName("q",window.location.search.substring(1));
	if (userRights == null) userRights = "";
	
	//nghiant 26062017 
	var showNutSuaNgayTN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIEN_NUT_SUA_NGAY_TN_XN');
	var showNutSuaNgayTraKQ = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'AN_NUT_SUA_NGAY_TRA_KQ');
	
	//L2PT-62588
	var chanPopupNhapKQCDHA = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CHANPOPUPNHAPKETQUACDHA');
	
	//var checkQuyenSuaNgayTraKQ = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS02X009.KTQSNTKQ1",'CDHA_PQUSER_SUATHOIGIAN');
	
	var choPhepLayThamSoLis = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CHOPHEPLAYTHAMSO_PARAMHASHE_LIS');
	var showForm = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_FORM_XN');
	//tuyendv L2PT-16498
	var screen_width = 1180;
	var screen_height = 620;
	var _chSize = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_FORMNHAPKQ_SIZE');
	if (_chSize == "1") {
		screen_width = screen.width-30;
		screen_height = screen.height-175;
	}
	//end L2PT-16498
	
	var an_nut_huy_dv_cls = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'AN_NUT_HUY_DV_CLS');
	var showDivKTV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_HIENTHI_DIVKTV');
	var xn_show_pttt = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_GRID_HIENTHI_PTTT');																					 
	var tiepnhanid ='';
	var benhnhan_id ='';
	
	//nghiant 01082017
	var khoa_chuyen_den_id = '';
	var phong_chuyen_den_id='';
	
	//nghiant 30082017
	var PhThucHienArray= new Array();
	var rolePTH = false;
	
	//nghiant 02012018 
	var dichvuid ='';
	var dsMaMay = [];		// mang du lieu luu tru Ma may  va html combobox ma may ; 
	
	//nghiant VNPTHISL2-490 22092017
	var sdtBN = "";
	var tenBN = "";
	var sophieuBN = "";
	var tenDVBN="";
	var onOffSendSMS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'ON_OFF_SMS_KQXN');
	//end nghiant VNPTHISL2-490 22092017
	
	// Goi benh nhan
	var HIS_API_TIMEOUT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_API_TIMEOUT');
	var HIS_API_MODE = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_API_MODE');
	var LIS_MODE_CALL_PATIENT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LIS_MODE_CALL_PATIENT');
	
	//cau hinh show tab DSDCD
	var showDSDCD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_ON_OFF_DSDV_DA_CHIDINH_XN');
	if(showDSDCD=="1"||showDSDCD=="2"){
		$('#tabDanhSachDaChiDinh').show();
		if(showDSDCD=="2") document.getElementById("titDSDCD").innerHTML="Danh sách dịch vụ chỉ định thêm";								
	}
	
	//show button lay ket qua mau
	var showLayKQMau = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_XN_SHOWBUTTON_LAYKQMAU');
	if(showLayKQMau=="1"){
		$("#txtTenMau").toggle(); 
		$("#btnLuuMau").toggle(); 
		$("#cboDSMau").css({"visibility":"visible"});
		
		//start tuyendv 15012018
		var sql_par2 = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					  opt.subdept_id+'$'+'0';
		ComboUtil.getComboTag("cboDSMau", "CLS01X002.DSM", sql_par2, "", {value:'-1',text:'-- Chọn Mẫu KQ --'}, "sp");
		//end tuyendv 15012018
	}
	
	//tuyendv lay ds ma dich vu khong cho sua bang
	var resizingTable = "0";
	var _dsMADV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_DS_MADICHVU_KHONGSUABANG');
	//end
	
	var hideMaMay = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_AN_MA_MAY_XETNGHIEM');
	var showDonVi = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_XN_GRID_KETQUA_SHOW_DONVI');	
	var sohhieu_datra = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_SOPHIEU_DATRA');
	var thuocvatu_dikem = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_THUOC_VATTU_DIKEM');	
	var chan_tvt_dikem = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XETNGHIEM_CHAN_TVT_DIKEM');	//cau hinh bang i_hid
	var show_truyenmau = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_TRUYENMAU');	//cau hinh bang i_hid
	var trangthaibp = "0";
	
	//tuyendv 18/09/2017 
	var i_hid = opt._param[0];
	
	var cls_hienthi_sotienbn = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_HIENTHI_SOTIENBN');
	var CLS_HIENTHI_ICON_DAKYSO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_HIENTHI_ICON_DAKYSO');	
	var KYSO_CLS_CHECK_KYCA_BYPARAM = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KYSO_CLS_CHECK_KYCA_BYPARAM');
	var chantiepnhan = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_CHAN_TIEPNHAN_CHUATHUTIEN');
	var cls_hienthi_tuoi = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_HIENTHI_TUOI');
	var cls_hienthi_tgravien = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_HIENTHI_TGRAVIEN');
	
	// 10/03/2021
	var optKySo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_SUDUNG_KYSO_KYDIENTU');
	var causer = "";
	var capassword = "";
	
	var _mamaymacdinh = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_MAMAY_MACDINH');
	var _chanhuydichvu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LIS_DENY_CANCEL_ORDER');
	
	var _sobanghimoitrang = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_ROW_PER_PAGE');
	var CLS_HIENTHI_CHECKBOX_GROUPGRID = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_HIENTHI_CHECKBOX_GROUPGRID');	
	var CLS_KETHUOCVT_HAOPHI_KTKHAM = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_KETHUOCVT_HAOPHI_KTKHAM');
	var _mbpID;//luu lai id mau benh pham cho in tu dong khi tra ket qua
	
	var b_sbgbentrai = 5;
	var b_sbgbenphai = 5;
	if(_sobanghimoitrang.includes(";")){
		var b_carr = _sobanghimoitrang.split(';');
		b_sbgbentrai = b_carr[0];
		b_sbgbenphai = b_carr[1];
	} else {
		if(_sobanghimoitrang==0) 
			b_sbgbentrai = 5;
		else 
			b_sbgbentrai = _sobanghimoitrang;
	}
	var _gridheight = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_GRID_HEIGHT');
	var _chinhsuangayth = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_CHINHSUA_NGAYTHUCHIEN');
	
	//tuyendv L2PT-16103
	var chanNhapKQGrid = '0';
	var choNhapKQGrid = true;
    chanNhapKQGrid = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_CHANNHAPKQ_TRENGRID');
	choNhapKQGrid = chanNhapKQGrid=='0'?true:false;
	//end L2PT-16103
	
	//L2PT-19087
	var v_dvkbid = '';//luu dich vu dươc chon trong phieu khi ky so
	var _signType = '';

	function loadsobanghitrentrang(){
		if(_sobanghimoitrang!="" && _sobanghimoitrang!="0"){
			if($('input[type=radio][name=rdoTrangThai]:checked').val()==2) {
				$("#pager_grdBenhPhamDangLam_center .ui-pg-selbox").val(b_sbgbentrai).change();
			}
			else {
				$("#pager_grdBenhPhamDangLam_center .ui-pg-selbox").val(b_sbgbenphai).change();	
			}
		}
	}
	
	function doLoad(){
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		
		// khởi tạo các tab dữ liệu
		$("li[id^='tab']").on("click",function(e){
			var tabName = $(this).attr("id").substr(3);
			$("li[class='active']").removeClass("active");
			$(this).addClass("active");
			$("div[class='tab active']").removeClass("active");
			$("#div"+ tabName).addClass("active");
			$(window).trigger("resize");
		});
		
		// khởi tạo các control
		initControl();
		bindEvent();
        
		$('#toolbarIdtxtTimKiem').focus();
		
		loadLISConfig();

		// load dữ liệu lên các control
		$("#txtTuNgay").val(moment().format('DD/MM/YYYY'));
		$("#txtDenNgay").val(moment().format('DD/MM/YYYY'));
		$("#txtTuNgayCD").val(moment().format('DD/MM/YYYY'));//nghiant 24102017 
		$("#txtDenNgayCD").val(moment().format('DD/MM/YYYY'));
		
		
		var sql_par = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+opt.dept_id;
		ComboUtil.getComboTag("cboPhongThucHien", "CLS01X002.KP", sql_par, "", "", "sp",'',function(){
			$('#cboPhongThucHien option[value="'+opt.subdept_id+'"]').attr('selected','selected');
			
			//nghiant 30082017
			var PTH = document.getElementById('cboPhongThucHien');
			for (i = 0; i < PTH.options.length; i++) {
				PhThucHienArray[i] = PTH.options[i].value;
			}
			console.log("PhThucHienArray: "+PhThucHienArray);
			//end nghiant 30082017
		});
		
		var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,20,0,f,l;Tên bác sỹ,FULLNAME,30,0,f,l;Chức danh,CHUCDANH,50,0,f,l";
		var sql_parKTV=[];
		sql_parKTV.push({"name":"[0]","value":0},{"name":"[1]","value":opt.dept_id});
		ComboUtil.initComboGrid("txtTKPHUMO1","PTTT.LOAD_USER",sql_parKTV,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUMO1").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+' ('+ui.item.USERNAME+')</option>');
		      $("#cboPHUMO1").empty();
		      $("#cboPHUMO1").append(option);
		      return false;
		});
		
		
		var callback = function () {
            if ($.active !== 0) {
                setTimeout(callback, '100');
                return;
            }
            reloadAllGrid();
        };
        callback();
        
        if(userRights=="VIW"){
			$("#btnLuu").hide();
			$("#btnHuy").hide();
			$("#txtTestNumber").hide();
			$("#btnLayKQ").hide();
			
			$("#txtTenMau").hide();
			$("#btnLuuMau").hide();
			$("#cboDSMau").hide();
			
			$("#btnTiepNhan").hide();
			$("#btnTuChoi").hide();
			$("#btnHuyTiepNhan").hide();
			$("#btnTraKetQua").hide();
			$("#btnHuyTraKetQua").hide();
			showButtonSuaNgayTraKQ(false);
			
			$("#btnHuyDichVu").hide();
			$("#btnKhoiPhuc").hide();
			$("#btnHuyKetQua").hide();
		}
		
		if(hospital_id=="10284" || hospital_id=="31900"){
        	$("#kevattutheogoi").html("Kê thuốc vật tư theo gói");
        	$("#TAOPHIEUVATTUKEM_HAOPHI").remove();
        	$("#kethuocdikem").html("Kê thuốc đi kèm");
        	$("#kevattudikem").html("Kê vật tư đi kèm");
        	$("#dsthuocvttheogoi").html("Danh sách thuốc vật tư theo gói");
        	
        	$("#kevattutheogoi2").html("Kê thuốc vật tư theo gói");
        	$("#TAOPHIEUVATTUKEM_HAOPHI").remove();
        	$("#kethuocdikem2").html("Kê thuốc đi kèm");
        	$("#kevattudikem2").html("Kê vật tư đi kèm");
        	$("#dsthuocvttheogoi2").html("Danh sách thuốc vật tư theo gói");
        }
		// thiết lập độ cao của grid
		if(_gridheight!="" && _gridheight!="0"){
			$('#gview_grdBenhPhamDangLam').find('.ui-jqgrid-bdiv').attr("style","height: "+_gridheight+"px !important");
			$('#gview_grdKetQuaXetNghiem').find('.ui-jqgrid-bdiv').attr("style","height: "+_gridheight+"px !important");		
		}
		loadsobanghitrentrang();
	}
	
	function initControl(){
		// ==================== START >> In Phiếu New ==================== truongle - 26/11/2019
		var HIDE_MENU_INPHIEU_CLS_OLD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIDE_MENU_INPHIEU_CLS_XN_OLD');
		if (HIDE_MENU_INPHIEU_CLS_OLD	 == '1') {
			ctl_ar[0].children = []
		}
		var menus = jsonrpc.AjaxJson.ajaxCALL_SP_O("TOOLBAR_CLS_INPHIEU",'1');
		if (menus && menus.length > 0) {
			var childsInPhieuNew = menus.map(function(item) {
				var optionalParam = {};
				try { optionalParam = JSON.parse($("<div/>").html(item.OPTIONAL_PARAM).text()); } catch(e) { optionalParam = {} }
				return {
					id:'btnInPhieu_new_' + item.ID,
					icon:'print',
					text: item.TEXT,
					order: parseInt(item.ORDER),
					hlink:'#',
					dataExternal: {
						report_code: item.REPORT_CODE,
						file_format: item.FILE_FORMAT,
						image_sql: item.IMAGES_CTL_SQL,
						optional_param: optionalParam
					}
				}
			})
			ctl_ar[0].children = ctl_ar[0].children.concat(childsInPhieuNew)
		}
        // ==================== END >> In Phiếu New ==================== truongle - 26/11/2019
		ToolbarUtil.build('toolbarId', ctl_ar);
		var _toolbar=ToolbarUtil.getToolbar('toolbarId');
		$("#toolbarIdbtnInPhieu").attr("disabled", true);
		
		//hien thi so phieu da tra
		if(sohhieu_datra!=null && sohhieu_datra==1){
			var _sophieu=jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS.SOPHIEU.DATRA",1);			
			$("#rdoTraKetQua").parent().html($("#rdoTraKetQua").parent().html()+" (<font color='blue'><b>"+ _sophieu + "</b></font>)");
		}		
		
		var showBtnService = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_CHIDINH_XN_PTH');
		if(showBtnService != 1){
			$('#toolbarIdbtnDichVu').hide();
		}

		var showBtnBoSung = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_BUTTON_BOSUNG_DICHVU');
		if(showBtnBoSung == "1"){
			$('#btnBoSung').show();
		}
		
		//begin tuyendv an nut in barcode(f4)
		var showPrintBarcode = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_SHOW_PRINT_BARCODE');
		if(showPrintBarcode!="1"){
			$("#toolbarIdbtnInBarcode").remove();
			$("#toolbarIdlblInfo").text("Xét Nghiệm");
		}
		//tuyendv end
		if(an_nut_huy_dv_cls == "1"){
			$('#btnHuyKetQua').hide();
		}
		// 
		if(showDivKTV=="1")
		{			 
			$("#divKTV").show();
		}
		else
		{
			$("#divKTV").hide();
		}
		
		if(an_nut_huy_dv_cls == "1"){
			$('#btnHuyKetQua').hide();
		}
		
		$("#grdBenhPhamDangLam").jqGrid({	        
	        datatype: 'local',
	        colNames: ['','','STT','Thừa/thiếu','Số phiếu','Barcode','Mã BN','Mã BA','Tên BN','Ngày sinh','Tuổi','Số thẻ BHYT','Khoa','Phòng','Khẩn',
		                'TG ra viện','TG chỉ định','Bác sĩ chỉ định','TG tiếp nhận','Người tiếp nhận','TG trả KQ','Trạng thái','HOSOBENHANID','KHAMBENHID',
		                'BENHNHANID','MAUBENHPHAMID','TRANGTHAIMAUBENHPHAM','TRANGTHAIDULIEUMAUBENHPHAM',
		                'GIOITINH','TUOI','KHOACHUYENDENID','PHONGCHUYENDENID','TIEPNHANID','LOAITIEPNHANID','THOIGIANDUKIENCOKETQUA','TRANGTHAITIEPNHAN','HINHTHUCVAOVIENID'],
		     colModel: [
			        {name:'ICON', width: 20, search: false },
					{name:'KYSO', width: 80, align: "right", hidden: true },
					{name:'SOTHUTU', width: 35, align:"center" ,search: false },
					{name:'SOTIEN', width: 80, align: "right", hidden: true },
					{name:'SOPHIEU', width: 80, align:"center" },
					{name:'BARCODE', width: 70, align:"center" },
					{name:'MABENHNHAN', width: 75, formatter:GridUtil.fm.formatStrEncode },
					{name:'MAHOSOBENHAN', width: 75, formatter:GridUtil.fm.formatStrEncode },//nghiant 06072017
					{name:'TENBENHNHAN', width: 150, formatter:GridUtil.fm.formatStrEncode },
					{name:'NGAYSINH', width: 65, align:"center", formatter:GridUtil.fm.formatStrEncode },	
					{name:'TUOI2', width: 65, align:"center", formatter:GridUtil.fm.formatStrEncode },
					{name:'MA_BHYT', width: 115, formatter:GridUtil.fm.formatStrEncode },			
					{name:'KHOADIEUTRI', width: 125, formatter:GridUtil.fm.formatStrEncode },
					{name:'PHONGDIEUTRI', width: 125, formatter:GridUtil.fm.formatStrEncode },	
					{name:'LOAIMAUBENHPHAM', width: 65, formatter:GridUtil.fm.formatStrEncode },
					{name:'NGAYRAVIEN', width: 100, formatter:GridUtil.fm.formatStrEncode },
					{name:'NGAYDICHVU', width: 100, formatter:GridUtil.fm.formatStrEncode },
					{name:'BACSY', width: 100, formatter:GridUtil.fm.formatStrEncode },
					{name:'NGAYTIEPNHAN', width: 100, formatter:GridUtil.fm.formatStrEncode },
					{name:'NGUOITIEPNHAN', width: 90, formatter:GridUtil.fm.formatStrEncode },
					{name:'NGAYMAUBENHPHAM_HOANTHANH', width: 100, hidden: true },
					{name:'TRANGTHAI', width: 85, formatter:GridUtil.fm.formatStrEncode },					
					{name:'HOSOBENHANID', hidden: true },
					{name:'KHAMBENHID', hidden: true },	
					{name:'BENHNHANID', hidden: true },					
					{name:'MAUBENHPHAMID', hidden: true },
					{name:'TRANGTHAIMAUBENHPHAM', hidden: true },
					{name:'TRANGTHAIDULIEUMAUBENHPHAM', hidden: true },
					{name:'GIOITINHID', hidden: true },
					{name:'TUOI', hidden: true },
					{name:'KHOACHUYENDENID', hidden: true },
					{name:'PHONGCHUYENDENID', hidden: true },
					{name:'TIEPNHANID', hidden: true },
					{name:'LOAITIEPNHANID', hidden: true },
					{name:'THOIGIANDUKIENCOKETQUA', hidden: true },
					{name:'TRANGTHAITIEPNHAN', hidden: true },
					{name:'HINHTHUCVAOVIENID', hidden: true }
		       ],
	          rowNum: b_sbgbentrai,
	          rowList: [5, 10, 20, 50, 100, 200, 500],
	          pager: '#pager_grdBenhPhamDangLam',
	          
	          gridview: true,
	          ignoreCase: true,
	          rownumbers: true,
	          viewrecords: true,
	          sortorder: 'desc',
	          width:1136,
	          shrinkToFit: false,
	          editurl: 'clientArray',
	          caption: 'Danh sách bệnh phẩm'
		});	      
	    $("#grdBenhPhamDangLam").jqGrid('navGrid', '#pager_grdBenhPhamDangLam', {edit: false, add: false, del: false});
	    $("#grdBenhPhamDangLam").jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
	    $('#gview_grdBenhPhamDangLam').find('.ui-jqgrid-bdiv').attr("style","height: 148px !important");
	    GridUtil.setWidthPercent("grdBenhPhamDangLam","100%");

		if(cls_hienthi_sotienbn=="1"){
			$("#grdBenhPhamDangLam").jqGrid('showCol', 'SOTIEN');
		} else {
			$("#grdBenhPhamDangLam").jqGrid('hideCol', 'SOTIEN');
		}
		
		if(CLS_HIENTHI_ICON_DAKYSO=="1"){
			$("#grdBenhPhamDangLam").jqGrid('showCol', 'KYSO');
		} else {
			$("#grdBenhPhamDangLam").jqGrid('hideCol', 'KYSO');
		}
		
		if(cls_hienthi_tuoi=="1"){
			$("#grdBenhPhamDangLam").jqGrid('showCol', 'TUOI2');
			$("#grdBenhPhamDangLam").jqGrid('hideCol', 'NGAYSINH');
		} else {
			$("#grdBenhPhamDangLam").jqGrid('showCol', 'NGAYSINH');
			$("#grdBenhPhamDangLam").jqGrid('hideCol', 'TUOI2');
		}
		
		if(cls_hienthi_tgravien=="1"){
			$("#grdBenhPhamDangLam").jqGrid('showCol', 'NGAYRAVIEN');
		} else {
			$("#grdBenhPhamDangLam").jqGrid('hideCol', 'NGAYRAVIEN');
		}
	    
		$("#grdDanhSachChiDinh").jqGrid({
			datatype: 'local',
			colNames: ['','Mã xét nghiệm','Tên xét nghiệm','Loại mẫu BP','Số lượng','Đối tượng','Trạng thái',
			           'DICHVUKHAMBENHID','TRANGTHAIDICHVU'],
			colModel: [
			    {name:'ICON', width: 20, search: false },
			    {name:'MADICHVU', width: 150, formatter:GridUtil.fm.formatStrEncode },
			    {name:'TENDICHVU', width: 500, formatter:GridUtil.fm.formatStrEncode },
			    {name:'LOAIMAUBP', width: 150, formatter:GridUtil.fm.formatStrEncode },
				{name:'SOLUONG', width: 80, formatter:GridUtil.fm.formatStrEncode },//nghiant 17012019 
				{name:'DOITUONG', width: 180, formatter:GridUtil.fm.formatStrEncode },
			    {name:'TENTRANGTHAI', width: 180, formatter:GridUtil.fm.formatStrEncode },
			    {name:'DICHVUKHAMBENHID', hidden: true },
			    {name:'TRANGTHAIDICHVU', hidden: true }
			  ],
			  rowNum: 50,
			  rowList: [50, 100, 200, 500],
			  pager: '#pager_grdDanhSachChiDinh',
			  gridview: true,
			  ignoreCase: true,
			  rownumbers: true,
			  viewrecords: true,
			  sortorder: 'desc',
			  width: 1136,
			  editurl: 'clientArray',
			  caption: ''
		});		      
		$("#grdDanhSachChiDinh").jqGrid('navGrid', '#pager_grdDanhSachChiDinh', {edit: false, add: false, del: false});
		$("#grdDanhSachChiDinh").jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		$("#grdDanhSachChiDinh")[0].toggleToolbar();
		$('#gview_grdDanhSachChiDinh').find('.ui-jqgrid-bdiv').attr("style","height: 135px !important");
		GridUtil.setWidthPercent("grdDanhSachChiDinh","100%");
		
		$.extend($.jgrid.inlineEdit, {
			keys: true
		});
		
		$("#grdKetQuaXetNghiem").jqGrid({
			datatype: 'local',
			colNames: ['','','Barcode','Mã xét nghiệm','Tên xét nghiệm','Kết quả','Đơn vị','Trị số bình thường','SL','TVT đi kèm','Trạng thái',
			           'Mã máy','Loại MBP','Ghi chú CĐ','PTTT','Người thực hiện','TG thực hiện','Người duyệt','TG trả KQ','Người hẹn trả','TG hẹn trả',
			           'BATTHUONG','TRANGTHAIMAUBENHPHAM','KETQUACLSID','DICHVUTHUCHIENID','DICHVUKHAMBENHID','TRANGTHAIKETQUA',
					   'REPORTCODE','KS_DICHVUKHAMBENHID','HETHONG','PARAM_HASHED','DICHVUKHAMBENHIDS',
					   'DICHVUID','TRANGTHAITIEPNHAN'],
			colModel: [
				{name:'ICON', width: 20, search: false },
				{name:'TENCHIDINH', width: 150, formatter:GridUtil.fm.formatStrEncode },
				{name:'BARCODE', width: 60, align:"center" },
				{name:'MADICHVU', width: 110, formatter:GridUtil.fm.formatStrEncode },
			    {name:'TENDICHVU', width: 250, formatter:GridUtil.fm.formatStrEncode },
				{name:'GIATRI_KETQUA', width: 140 }, //, editable:true
				{name:'DONVI', width: 60, formatter:GridUtil.fm.formatStrEncode },
				{name:'TRISOBINHTHUONG', width: 120, formatter:GridUtil.fm.formatStrEncode },
				{name:'SOLUONG', width: 30, formatter:GridUtil.fm.formatStrEncode },
				{name:'VATTUDIKEM', width: 80, formatter:GridUtil.fm.formatStrEncode },
				{name:'TENTRANGTHAI', width: 70, formatter:GridUtil.fm.formatStrEncode },
				{name:'MAMAY', width: 160 },
				{name:'TENBENHPHAM', width: 70, formatter:GridUtil.fm.formatStrEncode },
				{name:'GHICHUCD', width: 80, formatter:GridUtil.fm.formatStrEncode },
				{name:'PTTT', width: 60, align:"center" },							  
				{name:'BACSITHUCHIEN', width: 150, formatter:GridUtil.fm.formatStrEncode },
				{name:'NGAYKETQUA', width: 120 }, //, editable:true
				{name:'BACSIDUYET', width: 150, formatter:GridUtil.fm.formatStrEncode },
				{name:'THOIGIANTRAKETQUA', width: 100, formatter:GridUtil.fm.formatStrEncode },
				{name:'NGUOICAPNHAT', width: 150, formatter:GridUtil.fm.formatStrEncode },
				{name:'THOIGIANDUKIENCOKETQUA', width: 100, formatter:GridUtil.fm.formatStrEncode },
				{name:'BATTHUONG', hidden: true },
			    {name:'TRANGTHAIMAUBENHPHAM', hidden: true },
				{name:'KETQUACLSID', hidden: true },
				{name:'DICHVUTHUCHIENID', hidden: true },
				{name:'DICHVUKHAMBENHID', hidden: true },
				{name:'TRANGTHAIKETQUA', hidden: true },
				{name:'REPORTCODE', hidden: true},
				{name:'KS_DICHVUKHAMBENHID', hidden: true},
				{name:'HETHONG', hidden: true},
				{name:'PARAM_HASHED', hidden: true},
				{name:'DICHVUKHAMBENHIDS', hidden: true},				
				{name:'DICHVUID', hidden: true},				
				{name:'TRANGTHAITIEPNHAN', hidden: true}
			  ],
			  rowNum: 50,
			  rowList: [50, 100, 200, 500],
			  pager: '#pager_grdKetQuaXetNghiem',
			  gridview: true,
			  ignoreCase: true,
			  multiselect: true,//nghiant 19042018 
			  rownumbers: false,
			  viewrecords: true,
			  //sortorder: 'desc',
			  width: 1136,
			  shrinkToFit: false,
			  editurl: 'clientArray',
			  grouping: true,
			  groupingView: {
				  groupField : ['TENCHIDINH'],
				  groupColumnShow : [false],
				  //groupText : ['<b>{0}</b>']
				  groupText : CLS_HIENTHI_CHECKBOX_GROUPGRID=="1"?['<input type="checkbox" class="groupHeader"/> <b>  {0}  </b>']:['<b>{0}</b>'],
			  },
				onSelectAll: function(rowIds, allChecked) {
					$("input.groupHeader").prop('checked', allChecked);
				},
			  caption: ''
		});
		$("#grdKetQuaXetNghiem").jqGrid('navGrid', '#pager_grdKetQuaXetNghiem', {edit: false, add: false, del: false});
		$("#grdKetQuaXetNghiem").jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		$("#grdKetQuaXetNghiem")[0].toggleToolbar();
	    $('#gview_grdKetQuaXetNghiem').find('.ui-jqgrid-bdiv').attr("style","height: 135px !important");
	    GridUtil.setWidthPercent("grdKetQuaXetNghiem","100%");
	    //huongpv - ẩn 
	    if(hideMaMay==1){
	    	$("#grdKetQuaXetNghiem").jqGrid('hideCol', 'MAMAY');
	    }		
	    if(showDonVi==1){
	    	$("#grdKetQuaXetNghiem").jqGrid('showCol', 'DONVI');
	    }
		else
		{
			$("#grdKetQuaXetNghiem").jqGrid('hideCol', 'DONVI');		
		}
		//L2PT-117452
		if(xn_show_pttt==1)
		{
			$("#grdKetQuaXetNghiem").jqGrid('showCol', 'PTTT');		 
		} else {
			$("#grdKetQuaXetNghiem").jqGrid('hideCol', 'PTTT');		
		}			
		//nghiant 20102017 
		$("#grdDanhSachDaChiDinh").jqGrid({
			datatype: 'local',
			colNames: [//'Mã BN','Tên BN',
			           'Số phiếu','Mã dịch vụ','Tên dịch vụ','Ngày chỉ định','Kết quả','Bác sĩ chỉ định','Trạng thái DV','DICHVUKHAMBENHID','TRANGTHAIDICHVU'
			           ,'MAUBENHPHAMID','DICHVUID','KHOAID','PHONGID','TRANGTHAIMAUBENHPHAM','Trạng thái Phiếu'],
			colModel: [
			    //{name:'ICON', width: 20, search: false },
			    //{name:'MABENHNHAN', width: 150, formatter:GridUtil.fm.formatStrEncode },
			    //{name:'TENBENHNHAN', width: 210, formatter:GridUtil.fm.formatStrEncode },
				{name: 'SOPHIEU', width: 100, formatter:GridUtil.fm.formatStrEncode },
				{name: 'MADICHVU', width: 100, formatter:GridUtil.fm.formatStrEncode },
			    {name: 'TENDICHVU', width: 240, formatter:GridUtil.fm.formatStrEncode },//460 neu group 
			    {name: 'NGAYMAUBENHPHAM', width: 110, formatter:GridUtil.fm.formatStrEncode },//220 neu group 
			    {name: 'KETQUA', width: 240, formatter:GridUtil.fm.formatStrEncode },
			    {name: 'BACSICHIDINH', width: 160, formatter:GridUtil.fm.formatStrEncode },
			    {name: 'TENTRANGTHAI', width: 80, formatter:GridUtil.fm.formatStrEncode },//120 neu group 
			    {name: 'DICHVUKHAMBENHID', hidden: true },
			    {name: 'TRANGTHAIDICHVU', hidden: true },
			    {name: 'MAUBENHPHAMID', hidden: true },
			    {name: 'DICHVUID', hidden: true },
			    {name: 'KHOAID', hidden: true },
			    {name: 'PHONGID', hidden: true },
			    {name: 'TRANGTHAIMAUBENHPHAM', hidden: true },
			    {name: 'TRANGTHAI_PHIEU', width: 80, formatter:GridUtil.fm.formatStrEncode },//120 neu group
			  ],
			  rowNum: 50,
			  rowList: [50, 100, 200, 500],
			  pager: '#pager_grdDanhSachDaChiDinh',
			  gridview: true,
			  ignoreCase: true,
			  rownumbers: true,
			  viewrecords: true,
			  sortorder: 'desc',
			  width:'100%',
			  autowidth:false,
			  editurl: 'clientArray',
			  caption: ''
		});		      
		$("#grdDanhSachDaChiDinh").jqGrid('navGrid', '#pager_grdDanhSachDaChiDinh', {edit: false, add: false, del: false});
		$("#grdDanhSachDaChiDinh").jqGrid('filterToolbar', {ignoreCase: true, stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
		$("#grdDanhSachDaChiDinh")[0].toggleToolbar();
		$('#gview_grdDanhSachDaChiDinh').find('.ui-jqgrid-bdiv').attr("style","height: 135px !important");
		GridUtil.setWidthPercent("grdDanhSachDaChiDinh","100%");                                                                                                                                     
		//end nghiant 201102017
		$("#cboPhongThucHien").select2();
	} 
	
	function bindEvent(){
		$.jMaskGlobals = {
			maskElements: 'input,td,span,div',
			dataMaskAttr: '*[data-mask]',
			dataMask: true,
			watchInterval: 300,
			watchInputs: true,
			watchDataMask: true,
			byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
			translation: {
				'0': {pattern: /\d/},
				'9': {pattern: /\d/, optional: true},
				'#': {pattern: /\d/, recursive: true},
				'A': {pattern: /[a-zA-Z0-9]/},
				'S': {pattern: /[a-zA-Z]/}
			}
		};
		
		$(':input').keydown(function (e) {
			if (e.which === 13) {
				var id = $(this).attr('id');
				if(id == "rdoChoTiepNhan"){
					$("#cboPhongThucHien").focus();
				}
				else if(id == "rdoTraKetQua"){
					$("#cboPhongThucHien").focus();
				}
				else if (id == "cboPhongThucHien"){
					$("#txtTuNgay").focus();
				}
				else if (id == "txtTuNgay"){
					$("#txtDenNgay").focus();
				}
			}
		});
		
		var f3 = 114;
        $(document).unbind('keydown').keydown(function (e) {                      
        	if (e.keyCode == f3) {
            	e.preventDefault();
                $("#toolbarIdtxtTimKiem").focus();
            }
        });
        
		//begin_tuyendv button in barcode
		var f4 = 115;
        $(document).unbind('keydown').keydown(function (e) {                      
        	if (e.keyCode == f4) {            
                $("#toolbarIdbtnInBarcode").click();
            }
        });
		
		$('#toolbarIdbtnInBarcode').on('click', function () {			
			inPhieuBarcode();
		});
				
		$('#toolbarIdbtnPhieuInBarcode').on('click', function () {			
			inPhieuBarcode();
		});
		//end_tuyendv

		$('#toolbarIdbtnPhieuIn27BV01').on('click', function () {			
			inPhieu27BV01();
		});
		
		$('#toolbarIdbtnPhieuIn28BV01').on('click', function () {			
			inPhieu28BV01();
		});
		
		$('#toolbarIdbtnPhieuIn29BV01').on('click', function () {			
			inPhieu29BV01();
		});
		
		$('#toolbarIdbtnPhieuIn30BV01').on('click', function () {			
			inPhieu30BV01();
		});
		
		$('#toolbarIdbtnPhieuIn31BV01').on('click', function () {			
			inPhieu31BV01();
		});
		
		$('#toolbarIdbtnPhieuIn32BV01').on('click', function () {			
			inPhieu32BV01();
		});
		
		$('#toolbarIdbtnPhieuIn33BV01').on('click', function () {			
			inPhieu33BV01();
		});
		
		$('#toolbarIdbtnPhieuIn34BV01').on('click', function () {			
			inPhieu34BV01();
		});
		$('#toolbarIdbtnPhieuIn34BV01HSMD').on('click', function () {			
			inPhieu34BV01HSMD();
		});

		$('#toolbarIdbtnPhieuIn35BV01').on('click', function () {			
			inPhieu35BV01();
		});

		$('#toolbarIdbtnPhieuIn35BV01VINAM').on('click', function () {			
			inPhieu35BV01VINAM();
		});
		$('#toolbarIdbtnPhieuIn36BV01').on('click', function () {			
			inPhieu36BV01();
		});

		$('#toolbarIdbtnPhieuIn36BV01_').on('click', function () {			
			inPhieu36BV01_();
		});

		$('#toolbarIdbtnPhieuIn37BV01').on('click', function () {			
			inPhieu37BV01();
		});
		$('#toolbarIdbtnPhieuIn27BV01AFB').on('click', function () {			
			inPhieu27BV01AFB();
		});
		
		//nghiant 04082017 btnPhieuIn27BV01NuoiCay
		$('#toolbarIdbtnPhieuIn27BV01NuoiCay').on('click', function () {			
			inPhieu27BV01NuoiCay();
		});
		
		$('#toolbarIdbtnPhieuInPhieuKQNS').on('click', function () {			
			inPhieuKQNhuomSoi();
		});		
		//end nghiant 04082017
		
		//nghiant 07082017 btnPhieuIn27BV01KhangSinhDo
		$('#toolbarIdbtnPhieuIn27BV01KhangSinhDo').on('click', function () {			
			inPhieu27BV01KhangSinhDo();
		});
		//end nghiant 07082017
		
		$('#toolbarIdbtnPhieuInHoaSinh').on('click', function () {			
			inPhieuHoaSinh();
		});
		
		
		$('#toolbarIdbtnPhieuInMienDich').on('click', function () {			
			inPhieuMienDich();
		});

		$('#toolbarIdbtnPhieuInXNTeBao').on('click', function () {			
			inPhieuXNTeBao();
		});
		
		$('#toolbarIdbtnPhieuInXNSangLocSoSinh3').on('click', function () {			
			inPhieuXNSangLocSoSinh3();
		});
		
		$('#toolbarIdbtnPhieuInXNSangLocSoSinh5').on('click', function () {			
			inPhieuXNSangLocSoSinh5();
		});
		
		$('#toolbarIdbtnPhieuInXNNongDoCon').on('click', function () {                        
			inPhieuXNNongDoCon();
		});
		
		// ==================== START >> In Phiếu New ==================== truongle - 26/11/2019
		$('[id^="toolbarIdbtnInPhieu_new_"]').click(function(event) {
			inPhieuNew($(event.currentTarget));
		})
		// ==================== END >> In Phiếu New ==================== truongle - 26/11/2019

		//nghiant 25012018
		$('#toolbarIdbtnPhieuIn27BV01DT').on('click', function () {			
			inPhieu27BV01DT();
		});
		//end nghiant25012018
		
		//nghiant 07062018
		$('#toolbarIdbtnPhieuIn27BV01PCR').on('click', function () {			
			inPhieu27BV01PCR();
		});
		//end nghiant 07062018
		
		//nghiant 08062018
		$('#toolbarIdbtnPhieuIn27BV01TEST_TAMLY').on('click', function () {			
			inPhieu27BV01TEST_TAMLY();
		});
		//end nghiant 08062018
		
		//nghiant 08062018
		$('#toolbarIdbtnPhieuIn27BV01TV_TAMLY').on('click', function () {			
			inPhieu27BV01TV_TAMLY();
		});
		//end nghiant 08062018
		
		$("table tbody").on("change", "input[type=checkbox]", function (e) {        
			var currentCB = $(this);
			var grid = jQuery('#grdKetQuaXetNghiem');
			var isChecked = this.checked;
			if (currentCB.is(".groupHeader")) { //if group header is checked, to check all child checkboxes                     
				var checkboxes = currentCB.closest('tr').nextUntil('tr.grdKetQuaXetNghiemghead_0').find('.cbox[type="checkbox"]');
				checkboxes.each(function(){
					if (!this.checked || !isChecked)                   
						grid.setSelection($(this).closest('tr').attr('id'), true); 
				});
			} else {  //when child checkbox is checked
				var allCbs = currentCB.closest('tr').prevAll("tr.grdKetQuaXetNghiemghead_0:first").nextUntil('tr.grdKetQuaXetNghiemghead_0').andSelf().find('[type="checkbox"]');
				var allSlaves = allCbs.filter('.cbox');
				var headerCB = allCbs.filter(".groupHeader");
				var allChecked = !isChecked ? false : allSlaves.filter(":checked").length === allSlaves.length;
				headerCB.prop("checked", allChecked);
			}
			
		}); 
		$('#toolbarIdbtnGoiKham').on('click', function () {	
			var selRowId = $("#grdBenhPhamDangLam").jqGrid('getGridParam', 'selrow');
			if(selRowId!=null){
				var so_thutu = $("#grdBenhPhamDangLam").jqGrid ('getCell', selRowId, 'SOTHUTU');
				
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC04.02",$("#cboPhongThucHien").val());
				var sophong = "";
				var tenphong = "";
				if(data_ar.length>0){
					sophong = data_ar[0].SOPHONG;
					tenphong = data_ar[0].ORG_NAME;
				} 
				else {
					sophong = "1";
					tenphong = "phòng xét nghiệm";
				}
				var tenbn = $("#grdBenhPhamDangLam").jqGrid ('getCell', selRowId, 'TENBENHNHAN');
				
				var talkshow = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LIS_CONTENT_CALL_PATIENT');
				if(talkshow == null || talkshow == undefined || talkshow == "0" || talkshow == ""){
					talkshow = "Xin mời bệnh nhân số " + so_thutu + " " + tenbn + " vào phòng " + sophong;
				} 
				else {
					talkshow = talkshow.replace("{STT}",so_thutu).replace("{TENBENHNHAN}",tenbn).replace("{SOPHONG}",sophong).replace("{TENPHONG}",tenphong);
				}
				
				if(LIS_MODE_CALL_PATIENT=="0"){
					GBN.goi_vaophong(so_thutu,'');
				} 
				else if(LIS_MODE_CALL_PATIENT=="1") {
					responsiveVoice.speak(talkshow, "Vietnamese Female", {rate: 0.8});
				}
				else {
					goiKhamGG(talkshow, HIS_API_MODE, HIS_API_TIMEOUT);
				}
			}
		});
		
		$('#toolbarIdbtnXuatManHinh').on('click', function () {			
			var param="&phongid="+$("#cboPhongThucHien").val()+"&tungay="+$("#txtTuNgay").val()+"&denngay="+$("#txtDenNgay").val()+"&showMode=dlg";
			var callup = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LIS_LCD_DISPLAY');
			if(callup=="2"){
				window.open('manager.jsp?func=../canlamsang/CLS10L001_ManHinhLCD'+param,'','width:100%;');
			} else if(callup=="1"){
				window.open('manager.jsp?func=../canlamsang/CLS09L001_ManHinhLCD'+param,'','width:100%;');
			} else{
				window.open('manager.jsp?func=../canlamsang/CLS03L001_ManHinhLCD'+param,'','width:100%;');
			}
		});
		
		$('#toolbarIdbtnThongKe').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS01X006_ThongKeXetNghiem");
		});
		
		$('#toolbarIdtxtTimKiem').change(function (e) {	
			if($('#toolbarIdtxtTimKiem').val()==""){
				searchType = 0;
			}
			else {
				searchType = 1;
			}
			timKiemBangMayQuet($("#cboPhongThucHien").val(),$("#txtTuNgay").val(),$("#txtDenNgay").val(),$("input:radio[name='rdoTrangThai']:checked").val(),$('#toolbarIdtxtTimKiem').val(),'1');
			$('#toolbarIdtxtTimKiem').select();
		});

		$('#toolbarIdtxtTimKiem').keypress(function (e) {	
			if (e.keyCode == 13) {
				if($('#toolbarIdtxtTimKiem').val()==""){
					searchType = 0;
				}
				else {
					searchType = 1;
				}
				timKiemBangMayQuet($("#cboPhongThucHien").val(),$("#txtTuNgay").val(),$("#txtDenNgay").val(),$("input:radio[name='rdoTrangThai']:checked").val(),$('#toolbarIdtxtTimKiem').val(),'1');
				$('#toolbarIdtxtTimKiem').select();
			}
		});

		$('#toolbarIdtxtTimKiem').scannerDetection(function() {
			$("#toolbarIdtxtTimKiem").select();
			if($('#toolbarIdtxtTimKiem').val()==""){
				searchType = 0;
			}
			else {
				searchType = 1;
			}
			timKiemBangMayQuet($("#cboPhongThucHien").val(),$("#txtTuNgay").val(),$("#txtDenNgay").val(),$("input:radio[name='rdoTrangThai']:checked").val(),$('#toolbarIdtxtTimKiem').val());
		});
		
		$('#toolbarIdbtnTiepNhanTheoLo').on('click', function () {			
			var url = "manager.jsp?func=../canlamsang/CLS01X008_TiepNhanTheoLo";
			console.log("open url: "+ url);

			EventUtil.setEvent("CLS01X008_TiepNhan",function(e){				
				reloadAllGrid();
			});
			EventUtil.setEvent("CLS01X008_Thoat",function(e){
				DlgUtil.close("dlgTiepNhan");
			});
			
			var dlgPopup = DlgUtil.buildPopupUrl("dlgTiepNhan","divNhapKetQua",url,{},"Tiếp nhận bệnh phẩm theo lô",1020,600);
			dlgPopup.open("dlgTiepNhan");
		});
		
		$('#toolbarIdbtnDichVu').on('click', function () {
			var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			if(selRowId == null || selRowId == ''){
				DlgUtil.showMsg('Chưa chọn phiếu dịch vụ');
				return;
			}
			var rowData =  $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowData.MAUBENHPHAMID);
			if (data_ar != null && data_ar.length > 0) {
				if(data_ar[0].TRANGTHAITIEPNHAN != '0'){
					DlgUtil.showMsg('Bệnh nhân đã kết thúc điều trị. Không thể kê thêm dịch vụ');
					return;
				} else {
					paramInput={
						benhnhanid : data_ar[0].BENHNHANID,
						mabenhnhan : data_ar[0].MABENHNHAN,
						khambenhid : data_ar[0].KHAMBENHID,										
						tiepnhanid : data_ar[0].TIEPNHANID,
						hosobenhanid : data_ar[0].HOSOBENHANID,
						doituongbenhnhanid : data_ar[0].DOITUONGBENHNHANID,
						loaitiepnhanid : data_ar[0].LOAITIEPNHANID,
						subDeptId : opt.subdept_id,
						deptId : opt.dept_id
					};
					dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5,paramInput,"Tạo phiếu chỉ định dịch vụ",1300,600);
					DlgUtil.open("divDlgDichVu");
				}
			}
		});
		
		//calback cho man hinh chi dinh dich vu
		EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function(e) {
			DlgUtil.close("divDlgDichVu");
			DlgUtil.showMsg(e.msg);	
			reloadAllGrid();
			searchType = 0;
		});	

		$('#btnGuiSMS').on('click', function () {			
			var url = "manager.jsp?func=../canlamsang/CLS02C017_GuiSMS&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&tiepnhanid="+tiepnhanid+"&benhnhanid="+benhnhan_id+"&tenBN="+tenBN+"&sophieuBN="+sophieuBN+"&sdtBN="+sdtBN;
			EventUtil.setEvent("CLS02C017_Thoat",function(e){
				DlgUtil.close("dlgGuiSMS");	
				reloadAllGrid();
			});
			var dlgPopup = DlgUtil.buildPopupUrl("dlgGuiSMS","divNhapKetQua",url,{},"Gửi SMS",600,400);
			dlgPopup.open("dlgGuiSMS");
		});
		
		//nghiant 26062017
		$('#btnEditTime').on('click', function () {			
			var url = "manager.jsp?func=../canlamsang/CLS02X009_SuaNgayTraKetQua&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&tiepnhanid="+tiepnhanid;
			EventUtil.setEvent("CLS02X009_Thoat",function(e){
				DlgUtil.close("dlgSuaThoiGianTraKQ");	
				reloadAllGrid();
			});
			var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaThoiGianTraKQ","divNhapKetQua",url,{},"Sửa ngày trả kết quả",400,400);
			dlgPopup.open("dlgSuaThoiGianTraKQ");
		});

		$('#btnSuaNgayTN').on('click', function () {			
			var url = "manager.jsp?func=../canlamsang/CLS02X010_SuaNgayTiepNhan&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&tiepnhanid="+tiepnhanid+"&benhnhanid="+benhnhan_id;
			EventUtil.setEvent("CLS02X010_Thoat",function(e){
				DlgUtil.close("dlgSuaNgayTN");	
				reloadAllGrid();
			});
			var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaNgayTN","divNhapKetQua",url,{},"Sửa ngày tiếp nhận",400,300);
			dlgPopup.open("dlgSuaNgayTN");
		});		
		
		$('input[type=radio][name=rdoTrangThai]').change(function() {
			$("#grdBenhPhamDangLam").clearGridData();
			
			if(userRights!="VIW") {
				if(this.value=="2"){  	// đang thực hiện	
					$("#btnHuyKetQua").prop("disabled", false);
					
					if(userRights=="TK"){
						$("#btnTiepNhan").hide();
						$("#btnTuChoi").hide();
						$("#btnHuyTiepNhan").hide();
							$("#btnTraKetQua").hide();
							$("#btnHuyTraKetQua").hide();
						//nghiant 26062017
						showButtonSuaNgayTraKQ(false);
					}
					else if(userRights=="KTV"){
						$("#btnTiepNhan").show();
						$("#btnTuChoi").show();
						$("#btnHuyTiepNhan").hide();
							$("#btnTraKetQua").hide();
							$("#btnHuyTraKetQua").hide();
						//nghiant 26062017
						showButtonSuaNgayTraKQ(false);
					}
					else if(user_group_id=="0" || user_group_id=="1"){
						$("#btnTiepNhan").show();
						$("#btnTuChoi").show();
						$("#btnHuyTiepNhan").hide();
							$("#btnTraKetQua").hide();
							$("#btnHuyTraKetQua").hide();
						//nghiant 26062017
						showButtonSuaNgayTraKQ(false);
					}			
					else {
						$("#btnTiepNhan").show();
						$("#btnTuChoi").show();
						$("#btnHuyTiepNhan").hide();
						$("#btnTraKetQua").hide();
						$("#btnHuyTraKetQua").hide();
						//nghiant 26062017
						showButtonSuaNgayTraKQ(false);
					}
					$("#btnGuiSMS").hide();
					$("#btnLuu").prop("disabled", true);
					$("#btnLuuMau").prop("disabled", true);
					$("#btnHuy").prop("disabled", true);
					$("#btnInPhieu").hide();
					$("#btnHenTraKetQua").show();
					$("#toolbarIdbtnInPhieu").attr("disabled", true);
					$("#grdBenhPhamDangLam").hideCol("NGAYMAUBENHPHAM_HOANTHANH");
					$("#grdBenhPhamDangLam").showCol("TRANGTHAI");
					$("#btnKySo").hide();
					$("#btnHuyKySo").hide();
					$('#btnInPhieuKy').hide();
				}
				else if(this.value=="3"){	// đã trả kết quả
					$("#btnHuyKetQua").prop("disabled", false);
					
					if(userRights=="TK"){
						$("#btnTiepNhan").hide();
						$("#btnTuChoi").hide();
						$("#btnHuyTiepNhan").hide();
							$("#btnTraKetQua").hide();
							$("#btnHuyTraKetQua").hide();
					}
					else if(userRights=="KTV"){
						$("#btnTiepNhan").hide();
						$("#btnTuChoi").hide();
						$("#btnHuyTiepNhan").hide();
							$("#btnTraKetQua").hide();
							$("#btnHuyTraKetQua").hide();
					}
					else if(user_group_id=="0" || user_group_id=="1"){
						$("#btnTiepNhan").hide();
						$("#btnTuChoi").hide();
						$("#btnHuyTiepNhan").hide();
							$("#btnTraKetQua").hide();
							$("#btnHuyTraKetQua").hide();
					}
					else {
						$("#btnTiepNhan").hide();
						$("#btnTuChoi").hide();
						$("#btnHuyTiepNhan").hide();
							$("#btnTraKetQua").hide();
							$("#btnHuyTraKetQua").hide();
					}
					$("#btnGuiSMS").hide();
					$("#btnLuu").prop("disabled", true);
					$("#btnLuuMau").prop("disabled", true);
					$("#btnHuy").prop("disabled", true);				
					$("#btnInPhieu").show();
					$("#btnHenTraKetQua").hide();
					$("#toolbarIdbtnInPhieu").attr("disabled", false);
					$("#grdBenhPhamDangLam").hideCol("TRANGTHAI");
					$("#grdBenhPhamDangLam").showCol("NGAYMAUBENHPHAM_HOANTHANH");
					$("#btnKySo").hide();
					$("#btnHuyKySo").hide();
					$('#btnInPhieuKy').hide();
				}
			}
			
			reloadAllGrid();
			searchType = 0;
	    });

		$('#cboPhongThucHien').on('change', function () {	
			$("#hdfIDMauBenhPham").val("");		
			reloadAllGrid();
			searchType = 0;
		});
		
		$('#cboDSMau').on('change', function () {	
			var _mauid = $('#cboDSMau').val();
			var param_arr = $("#grdKetQuaXetNghiem").jqGrid('getRowData');			
			if(_mauid != '-1' && param_arr != null && param_arr.length > 0){
				var param = [_mauid];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTMAU", param.join('$'));
				if(data_ar != null && data_ar.length > 0){
					for(var i=0; i<data_ar.length; i++){
						var row_mauketqua = data_ar[i];
						for(var j=0; j<param_arr.length; j++){
							var row_gridketqua = param_arr[j];
							if(row_gridketqua.MADICHVU==row_mauketqua.MADICHVU){
								$("#grdKetQuaXetNghiem").jqGrid('setCell', j+1,'GIATRI_KETQUA', row_mauketqua.GIATRI_KETQUA);
							}
						}
					}
				}
			}
		});

		$('#txtTuNgay').on('change', function () {			
			reloadAllGrid();
			searchType = 0;
		});

		$('#txtDenNgay').on('change', function () {			
			reloadAllGrid();
			searchType = 0;
		});
		
		//nghiant 24102017
		$('#txtTuNgayCD').on('change', function () {			
			loadDanhSachDaChiDinh();
		});
		
		$('#txtDenNgayCD').on('change', function () {			
			loadDanhSachDaChiDinh();
		});
		//end nghiant 24102017 
		
		$('#btnXemDVDCD').on('click', function () {			
			loadDanhSachDaChiDinh();
		});
		
		
		$('#btnXem').on('click', function () {			
			reloadAllGrid();
			searchType = 0;
		});
		
		$("#grdBenhPhamDangLam").jqGrid("setGridParam",{
	    	onSelectRow: function(rowid){
	    		var row = $("#grdBenhPhamDangLam").jqGrid('getRowData', rowid);
	    		$("#hdfIDMauBenhPham").val(row.MAUBENHPHAMID);
				$("#hdfSoPhieu").val(row.SOPHIEU);					  
                bnGioiTinh = row.GIOITINHID;
                bnTuoi = row.TUOI;
                
        		// tô màu dòng được chọn
        		GridUtil.unmarkAll("grdBenhPhamDangLam");
        		GridUtil.markRow("grdBenhPhamDangLam", rowid);
        		//nghiant 26062017
                tiepnhanid = row.TIEPNHANID;
                benhnhan_id = row.BENHNHANID;
				khoa_chuyen_den_id = row.KHOACHUYENDENID;
                phong_chuyen_den_id = row.PHONGCHUYENDENID;
                trangthaibp = row.TRANGTHAIMAUBENHPHAM;
                
                //nghiant VNPTHISL2-490 22092017
                tenBN = row.TENBENHNHAN;
                sophieuBN = row.SOPHIEU;
                if(onOffSendSMS == "1"){
                	sdtBN=jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CH_SDT", row.MABENHNHAN);
                }
                
                console.log('hdfIDMauBenhPham: '+$("#hdfIDMauBenhPham").val()+', tiepnhanid: '+tiepnhanid+
                		', benhnhan_id: '+benhnhan_id+', phong_chuyen_den_id: '+phong_chuyen_den_id+', sdtBN: '+sdtBN);
                //end nghiant VNPTHISL2-490 22092017
                
                //nghiant 30082017
                rolePTH=checkPhongThucHien(PhThucHienArray, phong_chuyen_den_id);
                //end nghiant 
                
        		// ẩn hiện nút xử lý
                if(userRights!="VIW") {
	        		if(row['TRANGTHAIMAUBENHPHAM']=="2"){
						$("#btnHuyKetQua").prop("disabled", false);
						$("#btnHenTraKetQua").show();
						
	    				if(userRights=="TK"){
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
		    					$("#btnTraKetQua").hide();
		    					$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else if(userRights=="KTV"){
	    					$("#btnTiepNhan").show();
	    					$("#btnTuChoi").show();
	    					$("#btnHuyTiepNhan").hide();
		    					$("#btnTraKetQua").hide();
		    					$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else if(user_group_id=="0" || user_group_id=="1"){
	    					$("#btnTiepNhan").show();
	    					$("#btnTuChoi").show();
	    					$("#btnHuyTiepNhan").hide();
		    					$("#btnTraKetQua").hide();
		    					$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else {
	    					$("#btnTiepNhan").show();
	    					$("#btnTuChoi").show();
	    					$("#btnHuyTiepNhan").hide();
		    					$("#btnTraKetQua").hide();
		    					$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				$("#btnGuiSMS").hide();
						showButtonSuaNgayTN(false);
	        			$("#btnLuu").prop("disabled", true);
	        			$("#btnHuy").prop("disabled", true);
	        			$("#btnLuuMau").prop("disabled", true);
	        			$("#btnInPhieu").hide();
	        			$("#btnLayKQ").prop("disabled", true);
	        			
	        			$("#toolbarIdbtnInPhieu").attr("disabled", true);
	        			$("#grdKetQuaXetNghiem").setColProp('GIATRI_KETQUA',{editable:false});
	        			$("#grdKetQuaXetNghiem").setColProp('NGAYKETQUA',{editable:false});
						
						$("#btnKySo").hide();
						$("#btnHuyKySo").hide();
						$('#btnInPhieuKy').hide();
	        		} 
	        		else if(row['TRANGTHAIMAUBENHPHAM']=="4"){
						$("#btnHuyKetQua").prop("disabled", false);
						$("#btnHenTraKetQua").show();
						
	    				if(userRights=="TK"){
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
	    						$("#btnTraKetQua").show();
	    						$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else if(userRights=="KTV"){
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").show();
	    						$("#btnTraKetQua").hide();
	    						$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else if(user_group_id=="0" || user_group_id=="1"){
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").show();
	    						$("#btnTraKetQua").show();
	    						$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else {
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").show();
	    						$("#btnTraKetQua").show();
	    						$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				$("#btnGuiSMS").hide();
						showButtonSuaNgayTN(false);
	        			$("#btnLuu").prop("disabled", false);
	        			$("#btnHuy").prop("disabled", false);
	        			$("#btnLuuMau").prop("disabled", false);
	        			$("#btnInPhieu").show();
	        			$("#btnLayKQ").prop("disabled", false);
	        			
	        			$("#toolbarIdbtnInPhieu").attr("disabled", false);
	        			$("#grdKetQuaXetNghiem").setColProp('GIATRI_KETQUA',{editable:choNhapKQGrid});
	        			if(_chinhsuangayth=="1"){
	        				$("#grdKetQuaXetNghiem").setColProp('NGAYKETQUA',{editable:true});
	        			}

						$("#btnKySo").hide();
						$("#btnHuyKySo").hide();
						$('#btnInPhieuKy').hide();
	        		}
	        		else if(row['TRANGTHAIMAUBENHPHAM']=="3"){
						$("#btnHuyKetQua").prop("disabled", false);
						$("#btnHenTraKetQua").hide();
						
	    				if(userRights=="TK"){
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
	    						$("#btnTraKetQua").hide();
	    						$("#btnHuyTraKetQua").show();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(true);
	    				}
	    				else if(userRights=="KTV"){
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
	    						$("#btnTraKetQua").hide();
	    						$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else if(user_group_id=="0" || user_group_id=="1"){
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
		    					$("#btnTraKetQua").hide();
		    					$("#btnHuyTraKetQua").show();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(true);
	    				}
	    				else {
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
	    						$("#btnTraKetQua").hide();
	    						$("#btnHuyTraKetQua").show();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(true);
	    				}
	    				if(onOffSendSMS=="1"||onOffSendSMS=="2"||onOffSendSMS=="3"||onOffSendSMS=="4") 
	    					$("#btnGuiSMS").show();
						showButtonSuaNgayTN(true);
						showButtonSuaNgayTraKQ(true);
	        			$("#btnLuu").prop("disabled", true);
	        			$("#btnHuy").prop("disabled", true);  
	        			$("#btnLuuMau").prop("disabled", true);      			
	        			$("#btnInPhieu").show();
	        			$("#btnLayKQ").prop("disabled", true);
	        			
	        			$("#toolbarIdbtnInPhieu").attr("disabled", false);
	        			$("#grdKetQuaXetNghiem").setColProp('GIATRI_KETQUA',{editable:false});
	        			$("#grdKetQuaXetNghiem").setColProp('NGAYKETQUA',{editable:false});
						
						// ẩn hiện nút ký số
						if(optKySo=="1"||optKySo=="2"){
							v_dvkbid = '';
							var param = [row.MAUBENHPHAMID, ""];
							var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", param.join("$"));
							if(data_ar!=undefined && data_ar!=null){
								if(data_ar.length > 0){
									var trangthai = data_ar[0]["TRANGTHAI"];
									if(trangthai==""||trangthai=="0"||trangthai=="2"){										
										$("#btnHuyTraKetQua").attr("disabled", false);										
										
										$("#btnKySo").show();
										$("#btnHuyKySo").hide();
										$('#btnInPhieuKy').show();
										
										$("#btnInPhieuKy").attr("disabled", true);
									}
									else if(trangthai=="1"){
										showButtonSuaNgayTN(false);
										showButtonSuaNgayTraKQ(false);
										$("#btnHuyTraKetQua").attr("disabled", true);

										$("#btnKySo").hide();
										$("#btnHuyKySo").show();
										$('#btnInPhieuKy').show();

										$("#btnInPhieuKy").attr("disabled", false);
									}
								}
								else {									
									$("#btnKySo").show();
									$("#btnHuyKySo").hide();
									$('#btnInPhieuKy').show();
									
									$("#btnInPhieuKy").attr("disabled", true);
								}
							}
						}
						else {
							$("#btnKySo").hide();
							$("#btnHuyKySo").hide();
							$('#btnInPhieuKy').hide();
						}
						// hết ẩn hiển nút ký số
	        		}
	        		else if(row['TRANGTHAIMAUBENHPHAM']=="8"){
						$("#btnHuyKetQua").prop("disabled", true);
						$("#btnHenTraKetQua").hide();
						
	    				if(userRights=="TK"){
	    					$("#btnTiepNhan").show();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
	    						$("#btnTraKetQua").show();
	    						$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else if(userRights=="KTV"){
	    					$("#btnTiepNhan").show();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
	    						$("#btnTraKetQua").show();
	    						$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else if(user_group_id=="0" || user_group_id=="1"){
	    					$("#btnTiepNhan").show();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
	    						$("#btnTraKetQua").hide();
	    						$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				else {
	    					$("#btnTiepNhan").hide();
	    					$("#btnTuChoi").hide();
	    					$("#btnHuyTiepNhan").hide();
	    						$("#btnTraKetQua").hide();
	    						$("#btnHuyTraKetQua").hide();
	    					//nghiant 26062017
	    					showButtonSuaNgayTraKQ(false);
	    				}
	    				$("#btnGuiSMS").hide();
	    				showButtonSuaNgayTN(false);
	        			$("#btnLuu").prop("disabled", true);
	        			$("#btnHuy").prop("disabled", true);
	        			$("#btnLuuMau").prop("disabled", true);     			
	        			$("#btnInPhieu").hide();
	        			$("#btnLayKQ").prop("disabled", true);
	        			
	        			$("#toolbarIdbtnInPhieu").attr("disabled", false);
	        			$("#grdKetQuaXetNghiem").setColProp('GIATRI_KETQUA',{editable:choNhapKQGrid});
	        			if(_chinhsuangayth=="1"){
	        				$("#grdKetQuaXetNghiem").setColProp('NGAYKETQUA',{editable:true});
	        			}

						$("#btnKySo").hide();
						$("#btnHuyKySo").hide();
						$('#btnInPhieuKy').hide();
	        		}
				}
                
                // load thông tin các tab ở dưới
        		var tabid = $("li[id^='tab'][class='active']").attr("id");
        		
        		if (tabid == "tabThongTinBenhNhan") {
        			loadThongTinBenhNhan(row.BENHNHANID);
        			tab1Click = 1; tab2Click = 0; tab3Click = 0; tab4Click = 0;
        		} 
        		else if(tabid == "tabDanhSachChiDinh") {
        			loadDanhSachChiDinh();
        			tab1Click = 0; tab2Click = 1; tab3Click = 0; tab4Click = 0;
        		} 
        		else if(tabid == "tabDanhSachKetQua"){
        			loadDanhSachKetQua();
        			tab1Click = 0; tab2Click = 0; tab3Click = 1; tab4Click = 0;
        		}
        		//nghiant 25102017  
        		else if(tabid == "tabDanhSachDaChiDinh"){
        			loadDanhSachDaChiDinh();
        			tab1Click = 0; tab2Click = 0; tab3Click = 0; tab4Click = 1;
        		}
        		//end nghiant 25102017 
	    		
	    		// lấy kết quả từ máy xét nghiệm nếu có
				if(row['TRANGTHAIMAUBENHPHAM']=="4" && row['TRANGTHAIDULIEUMAUBENHPHAM']!="2"){
					GetResultFromLab($("#grdBenhPhamDangLam").jqGrid('getGridParam', 'selrow'));
				}
				//nghiant 25082017
				if(!rolePTH){
					$("#btnTiepNhan").hide();
					$("#btnTuChoi").hide();
					$("#btnHuyTiepNhan").hide();
					$("#btnTraKetQua").hide();
					$("#btnHuyTraKetQua").hide();
					$("#btnHuyKetQua").hide();
					$("#btnLayKQ").hide();
					$("#btnHuyDichVu").hide();
					$("#btnLuu").hide();
					$("#btnLuuMau").hide();
					$("#btnHuy").hide();
					$("#btnEditTime").hide();
					//nghiant 26062017
					showButtonSuaNgayTraKQ(false);
				}			
				//end nghiant 25082017
				
				if(CLS_KETHUOCVT_HAOPHI_KTKHAM =="0" && row.TRANGTHAITIEPNHAN>0){
	                $('#TAOPHIEUTHUOCKEM_HAOPHI').attr("disabled","disabled").addClass('ui-state-disabled');
	                $('#TAOPHIEUVATTUKEM_HAOPHI').attr("disabled","disabled").addClass('ui-state-disabled');
	                $('#TAOPHIEUTHUOCKEM').attr("disabled","disabled").addClass('ui-state-disabled');
	                $('#TAOPHIEUVATTUKEM').attr("disabled","disabled").addClass('ui-state-disabled');
				} else {
					$('#TAOPHIEUTHUOCKEM_HAOPHI').removeAttr("disabled").removeClass('ui-state-disabled');
	                $('#TAOPHIEUVATTUKEM_HAOPHI').removeAttr("disabled").removeClass('ui-state-disabled');
	                $('#TAOPHIEUTHUOCKEM').removeAttr("disabled").removeClass('ui-state-disabled');
	                $('#TAOPHIEUVATTUKEM').removeAttr("disabled").removeClass('ui-state-disabled');
				}							
	    	},
			ondblClickRow: function(rowid) {
				if(userRights!="VIW"){
					var row = $("#grdBenhPhamDangLam").jqGrid('getRowData', rowid);				
					if (row['TRANGTHAIMAUBENHPHAM'] == "2") {
						var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", row.TIEPNHANID);
						if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && row.LOAITIEPNHANID=="1" && row.MA_BHYT == ""){
							DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");					
						}
						else {
							var url = "manager.jsp?func=../canlamsang/CLS01X007_TiepNhanBenhPham&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&iddichvukb="+$("#hdfIDDichVuKB").val()
							+"&rolePTH="+rolePTH;//nghiant 28082017 
							console.log("open url: "+ url);
		
							EventUtil.setEvent("CLS01X007_TiepNhanBP",function(e){
								DlgUtil.close("dlgTiepNhanBP");
								reloadAllGrid();
							});
							EventUtil.setEvent("CLS01X007_Thoat",function(e){
								DlgUtil.close("dlgTiepNhanBP");
							});
							
							var dlgPopup = DlgUtil.buildPopupUrl("dlgTiepNhanBP","divNhapKetQua",url,{},"Tiếp nhận bệnh phẩm",1020,600);
							dlgPopup.open("dlgTiepNhanBP");
						}
					} 
					else if(row['TRANGTHAIMAUBENHPHAM']=="4"){
						if(chanPopupNhapKQCDHA =="1" ) 
						{
							console.log("Trạng thái mẫu bệnh phẩm: "+ row['TRANGTHAIMAUBENHPHAM']);
						}
						else{						
							var url = "manager.jsp?func=../canlamsang/CLS01X003_KetQuaXetNghiem&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
										"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update&q="+userRights+
										"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
										"&rolePTH="+rolePTH+"&onOffSendSMS="+onOffSendSMS+"&sdtBN="+sdtBN+"&tenBN="+tenBN+"&sophieuBN="+sophieuBN;
		
							EventUtil.setEvent("CLS01X002_LUU",function(e){
								reloadAllGrid();
							});
							EventUtil.setEvent("CLS01X002_HUY",function(e){
								DlgUtil.close("dlgNhapKetQua");
								reloadAllGrid();
							});
							
							var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},"Nhập kết quả xét nghiệm",1025,600);
							dlgPopup.open("dlgNhapKetQua");
						}
					}
					else if (row['TRANGTHAIMAUBENHPHAM'] == "3") {
						if(chanPopupNhapKQCDHA =="1" ) 
						{
							console.log("Trạng thái mẫu bệnh phẩm: "+ row['TRANGTHAIMAUBENHPHAM']);
						}
						else{						
							var url = "manager.jsp?func=../canlamsang/CLS01X003_KetQuaXetNghiem&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
										"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=read"+
										"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=true"+"&tiepnhanid="+tiepnhanid+
										"&rolePTH="+rolePTH+"&onOffSendSMS="+onOffSendSMS+"&sdtBN="+sdtBN+"&tenBN="+tenBN+"&sophieuBN="+sophieuBN;
							EventUtil.setEvent("CLS01X002_LUU",function(e){
								$("#hdfIDMauBenhPham").val();
								reloadAllGrid();
							});			
		
							EventUtil.setEvent("CLS01X002_HUY",function(e){
								DlgUtil.close("dlgNhapKetQua");
								reloadAllGrid();
							});
							
							var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},"Nhập kết quả xét nghiệm",1020,600);
							dlgPopup.open("dlgNhapKetQua");
						}
					}
					else {
						console.log("Trạng thái mẫu bệnh phẩm: "+ row['TRANGTHAIMAUBENHPHAM']);
					}
				}
	    	},
	    	gridComplete: function(){
	    		var rowids = $("#grdBenhPhamDangLam").getDataIDs();
	    		
	    		if(rowids!=""){
	    			for(var i=0; i<rowids.length; i++){
		    			var rowid = rowids[i];
		    			var row = $("#grdBenhPhamDangLam").jqGrid('getRowData', rowid);

		    			// hiển thị các icon trạng thái
		    			var icon = '';
						if(row.TRANGTHAIMAUBENHPHAM == 2){
							if(row.THOIGIANDUKIENCOKETQUA != ""){
								icon = '<center><img src="../common/image/Pin.png" width="15px"></center>';
								$("#grdBenhPhamDangLam").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Hẹn trả KQ');
							}
							else {
								icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
							}
						}
						else if(row.TRANGTHAIMAUBENHPHAM == 4){
							if(row.THOIGIANDUKIENCOKETQUA != ""){
								icon = '<center><img src="../common/image/Pin.png" width="15px"></center>';
								$("#grdBenhPhamDangLam").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Hẹn trả KQ');
							}
							else if(row.TRANGTHAIDULIEUMAUBENHPHAM == 2){
								icon = '<center><img src="../common/image/Flag_Green.png" width="15px"></center>';
								$("#grdBenhPhamDangLam").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Đã có kết quả');
							}
							else if(row.TRANGTHAIDULIEUMAUBENHPHAM == 3){
								icon = '<center><img src="../common/image/Flag_Green2.png" width="15px"></center>';
								$("#grdBenhPhamDangLam").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Đã hủy kết quả');
							}
							else{
								icon = '<center><img src="../common/image/Misc_calendar.png" width="15px"></center>';
								$("#grdBenhPhamDangLam").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Đã tiếp nhận');
							}
						}
						else if(row.TRANGTHAIMAUBENHPHAM == 3){
							icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
							// biểu tượng đã ký số
							if(optKySo=="1"||optKySo=="2"){
								var param = [row.MAUBENHPHAMID, ""];//L2PT-19087
								var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", param.join("$"));
								if(data_ar!=undefined && data_ar!=null){
									if(data_ar.length > 0){
										var trangthai = data_ar[0]["TRANGTHAI"];
										var hethong = data_ar[0]["HETHONG"];
										if(trangthai=="1"){
											if(hethong=="LIS"){
												icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
											}
											else {
												icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
											}
										}
									}
								}
							}
							// hết biểu tượng đã ký số
						}
						else if(row.TRANGTHAIMAUBENHPHAM == 8){
							icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
							$("#grdBenhPhamDangLam").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Đã hủy kết quả');
						}
						$("#grdBenhPhamDangLam").jqGrid ('setCell', rowid, 'ICON', icon);

						if(row.LOAIMAUBENHPHAM=="Khẩn"){
							$('#grdBenhPhamDangLam').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
	                	        $(element).css("font-weight","bold");
	                			$(element).css("color", "#b937b3");
	                	    });
						}
						
						// tô đỏ bệnh nhân cấp cứu
						if(row.HINHTHUCVAOVIENID == 2){
							$('#grdBenhPhamDangLam').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
	                	        $(element).css("font-weight","bold");
	                			$(element).css("color", "#9e0606");
	                	    });
						}

						// tô xanh bệnh nhân điều trị ngoại trú
						if(row.LOAITIEPNHANID == "3"){
							$('#grdBenhPhamDangLam').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
	                	        $(element).css("font-weight","bold");
	                			$(element).css("color", "green");
	                	    });
						} 

						// bôi màu bệnh nhân thiếu tiền
						if(cls_hienthi_sotienbn=="1"){
							if(row.SOTIEN>0){
								if(CLS_HIENTHI_ICON_DAKYSO=="1"){
									$("#grdBenhPhamDangLam").setCell (rowid,4,row.SOTIEN,{background:'#ff0000'});
								} else {
									$("#grdBenhPhamDangLam").setCell (rowid,3,row.SOTIEN,{background:'#ff0000'});									
								}
							}
						}
						
						// focus lại dòng được chọn
						if(row.MAUBENHPHAMID == $("#hdfIDMauBenhPham").val()){
							$("#grdBenhPhamDangLam").setSelection(rowid, true);	
						}
		    		}
	    		}
	    		
	    		if(userRights!="VIW") {
	    			$(".jqgrow",'#grdBenhPhamDangLam').contextMenu('contextMenuBP', {
			            onContextMenu: function (event, menu) {
			            	var rowId = $(event.target).parent("tr").attr("id");
			            	$('#grdBenhPhamDangLam').setSelection(rowId);
			            	return true;
			            },
			            bindings: {
			            	'CapSTT': function (t) {
			            		var rowId = $(t).attr("id");
		    					if(rowId != null && rowId != ''){
		    						var rowData = $('#grdBenhPhamDangLam').jqGrid('getRowData', rowId);
									var paramInput = { maubenhphamid:rowData.MAUBENHPHAMID };					
				    				dlgPopup=DlgUtil.buildPopupUrl("divDlgCapSTT","divDlg","manager.jsp?func=../canlamsang/CLS02C011_CapSTT",paramInput,"Cấp STT thực hiện",500,250);
				    				DlgUtil.open("divDlgCapSTT");
		    					}
		    					EventUtil.setEvent("CLS02C011_Luu", function(e) {
		    		    			DlgUtil.close("divDlgCapSTT");
		    		    			DlgUtil.showMsg('Cấp số thứ tự thực hiện thành công',undefined,1000);
		    		    			reloadAllGrid();
		    		    		});
		    					EventUtil.setEvent("CLS02C011_Thoat", function(e) {
		    		    			DlgUtil.close("divDlgCapSTT");
		    		    			reloadAllGrid();
		    		    		});
		    				},
				    		//Begin_HaNv_10052018: them context menu chuyen phong thuc hien HISL2TK-419
			            	'ChuyenPhongTH': function (t) {
			            		var rowId = $(t).attr("id");
		    					if(rowId != null && rowId != ''){
		    						var rowData = $('#grdBenhPhamDangLam').jqGrid('getRowData', rowId);
									var paramInput={
					    				maubenhphamid:rowData.MAUBENHPHAMID,
					    				type:'1'
				    				};					
				    				dlgPopup=DlgUtil.buildPopupUrl("divDlgPhongChiDinh","divDlg","manager.jsp?func=../noitru/NTU01H025_DoiPhongChiDinh",paramInput,"Đổi phòng thực hiện",500,200);
				    				DlgUtil.open("divDlgPhongChiDinh");
		    					}
		    					EventUtil.setEvent("assignSevice_saveChangeDev", function(e) {
		    		    			if(typeof(e) != 'undefined'){
		    		    				DlgUtil.showMsg(e.msg);
		    		    			}
		    		    			DlgUtil.close(e.divId);
		    		    			reloadAllGrid();
		    		    		});
		    				},
			    			//End_HaNv_10052018	
			            	'thongtinlaymau': function (t) {
			            		var rowId = $(t).attr("id");
		    					if(rowId != null && rowId != ''){
		    						var rowData = $('#grdBenhPhamDangLam').jqGrid('getRowData', rowId);
													
				    				dlgPopup=DlgUtil.buildPopupUrl("divDlgThongTinLayMau","divDlg","manager.jsp?func=../canlamsang/CLS01X016_CapNhatNguoiLayMau&maubenhphamid="+$("#hdfIDMauBenhPham").val(),{},"Thông tin lấy mẫu",500,500);
				    				DlgUtil.open("divDlgThongTinLayMau");
		    					}
		    					EventUtil.setEvent("CLS01X016_Thoat", function(e) {
		    						DlgUtil.close("divDlgThongTinLayMau");
		    		    			reloadAllGrid();
		    		    		});
		    				},
		    				'taoPhieuTruyenmau': function (t) {
			            		var rowId = $(t).attr("id");
		    					if(rowId != null && rowId != ''){
		    						var rowData = $('#grdBenhPhamDangLam').jqGrid('getRowData', rowId);
		    						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",$("#hdfIDMauBenhPham").val());
		    						var sql_par = [];
		    						sql_par.push({
		    							"name" : "[0]",
		    							"value" : rowData.MAUBENHPHAMID
		    						});
		    						var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D006.PTM.MBP", sql_par);
		    						if(data != null && data != '[]'){
		    							var rows = JSON.parse(data);
			    						var truyenmauid = rows[0]["TRUYENMAU_ID"];
		    						}
		    						
			   	        			var paramInput={
			 	  					       benhnhanid : data_ar[0].BENHNHANID,
			 	  					       khambenh_id : data_ar[0].KHAMBENHID,  
			 	  					       maubenhpham_id : rowData.MAUBENHPHAMID,
			 	  					       truyenmauid : truyenmauid,
			 	  					       sophieu : rowData.SOPHIEU,
			 	  					       thoigianvaovien: data_ar[0].THOIGIANVAOVIEN,
			 	  					       modeDisplay : 0
			 	  					 }; 
									 //L2PT-77169
			   						if(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_PHIEUTRUYENMAU_NEW') != '1')
			   							dlgPopup = DlgUtil.buildPopupUrl("divDlgPTruyenMau", "divDlg", "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenMau_VPC", paramInput, "Phiếu truyền máu", 1000, 600);
			   						else
										dlgPopup=DlgUtil.buildPopupUrl("divDlgPTruyenMau","divDlg","manager.jsp?func=../noitru/NTU02D006_PhieuTruyenMau_K74" ,paramInput,"Phiếu truyền máu",1000,550);
			   						DlgUtil.open("divDlgPTruyenMau");
		    					}
		    				},
							'inTheBenhAn': function (t) {
			            		var rowId = $(t).attr("id");
		    					if(rowId != null && rowId != ''){
		    						var rowData = $('#grdBenhPhamDangLam').jqGrid('getRowData', rowId);
		    						var par = [ {
											name : 'maubenhphamid',
											type : 'String',
											value : rowData.MAUBENHPHAMID
										}];
										openReport('window', "IN_THEBENHAN_A6", "pdf", par);
		    					}
		    				}
			            }
		    		});
    			}	    		    		
	    	}
        });
		
		$("#grdDanhSachChiDinh").jqGrid("setGridParam", {
			onSelectRow : function (rowid) {
				GridUtil.unmarkAll("grdDanhSachChiDinh");
				GridUtil.markRow("grdDanhSachChiDinh", rowid);
				
				var row = $("#grdDanhSachChiDinh").jqGrid('getRowData', rowid);
                $("#hdfIDDichVuKB").val(row.DICHVUKHAMBENHID);
                
				if(userRights!="VIW" && _chanhuydichvu!="1"){
	                if(row["TRANGTHAIDICHVU"]=="9"){
						$("#btnHuyDichVu").hide();
	        			$("#btnKhoiPhuc").show();
	
	        			if(!rolePTH){
	        				$("#btnHuyDichVu").hide();
	        				$("#btnKhoiPhuc").hide();
	        			}
	        			//end nghiant
					} else {
						$("#btnHuyDichVu").show();
	        			$("#btnKhoiPhuc").hide();
	
	        			if(!rolePTH){
	        				$("#btnHuyDichVu").hide();
	        				$("#btnKhoiPhuc").hide();
	        			}
	        			//end nghiant
					}
				}
			},
	    	gridComplete: function(){
	    		var rowids = $("#grdDanhSachChiDinh").getDataIDs();
	    		if(rowids!=""){
	    			for(var i=0; i<rowids.length; i++){
		    			var rowid = rowids[i];
		    			var row = $("#grdDanhSachChiDinh").jqGrid('getRowData', rowid);
						
		    			// hiển thị icon trạng thái
		    			var icon = '';
		    			if(row["TRANGTHAIDICHVU"] == 0){
							icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
						}
						else if(row["TRANGTHAIDICHVU"] == 1){
							icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
						}
						else if(row["TRANGTHAIDICHVU"] == 3){
							icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
						}
						else if(row["TRANGTHAIDICHVU"] == 9){
							icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
						}
						$("#grdDanhSachChiDinh").jqGrid ('setCell', rowid, 1, icon);
						
						// focus lại dòng được chọn
						if(row.DICHVUKHAMBENHID == $("#hdfIDDichVuKB").val()){
							$("#grdDanhSachChiDinh").setSelection(rowid, true);	
						}
						
		    		}
	    		}
	    		
	    		if(userRights!="VIW") {
		    		if(thuocvatu_dikem==1 && (chan_tvt_dikem ==1 ||  trangthaibp != "3") ){
			    		$(".jqgrow", "#" + "grdDanhSachChiDinh").contextMenu('contextMenuDetail', {
			    			bindings: {
			    				'TAOPHIEUTHUOCKEM_HAOPHI': function (t) {
									var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			    					var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			    					var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
			    					if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
			    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
			    					}
			    					else {
										var rowId = $(t).attr("id");	    					
										_taoPhieuThuocDiKem_haophi(rowId);
									}
			    				},
			    				'TAOPHIEUVATTUKEM_HAOPHI': function (t) {
									var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			    					var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			    					var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
			    					if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
			    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
			    					}
			    					else {
										var rowId = $(t).attr("id");	    					
										_taoPhieuVatTuDiKem_haophi(rowId);
									}
			    				},
			    				'TAOPHIEUTHUOCKEM': function (t) {
									var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			    					var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			    					var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
			    					if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
			    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
			    					}
			    					else {
										var rowId = $(t).attr("id");	    					
										_taoPhieuThuocDiKem(rowId); 
									}
			    				},
			    				'TAOPHIEUVATTUKEM': function (t) {
									var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			    					var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			    					var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
			    					if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
			    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
			    					}
			    					else {
										var rowId = $(t).attr("id");	    					
										_taoPhieuVatTuDiKem(rowId);
									}
			    				},
			    				'DSPTVT_KEM': function (t) {
			    					var rowId = $(t).attr("id");	    					
			    					_dsPhieuThuocVatTuDiKem(rowId);    				
			    				}
			    				//tuyennx_add_start yc L2K74TW-224
			    				,
			    				'DSPTVT': function (t) {
			    					var rowId = $(t).attr("id");	    					
			    					_dsPhieuThuocVatTu(rowId);    				
			    				}
			    				//tuyennx_add_end yc L2K74TW-224
			    			},
			    		    onContextMenu: function (event, menu) {
			    				var rowId = $(event.target).parent("tr").attr("id");
			    				var grid = $("#" + "grdDanhSachChiDinh");
			    				//grid.setSelection(rowId);
			    				GridUtil.unmarkAll("grdDanhSachChiDinh");
			    				GridUtil.markRow("grdDanhSachChiDinh",rowId);
			    				
			    				return true;
			    			}
			    		});
			    		
			    	   if(hospital_id==944){
			    		   $('#TAOPHIEUTHUOCKEM').remove()
				    	   $('#TAOPHIEUVATTUKEM').remove()   
			    	   }		    		
			    	}
	    		}
	    	}
		});
		
		function _taoPhieuThuocDiKem(rowId){        	
	     	var _opt="02D010"
	     	var _msg="Tạo phiếu thuốc đi kèm";     	
	     	var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
	     	var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
	     	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
	     	var rowData = $('#grdDanhSachChiDinh').jqGrid('getRowData', rowId);
	     	
	     	if(rowData != null){
	     		paramInput={
					khoaId: khoa_chuyen_den_id,
					phongId: phong_chuyen_den_id,
					khambenhid :  data_ar[0].KHAMBENHID,
					maubenhphamid : "",
					loaikedon: 1,
					dichvuchaid: rowData.DICHVUKHAMBENHID,
					opt : _opt // tao phieu thuoc     					
	  			};   		 
	  			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1300,590);
	  			DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
	     	 }
		}
	
		function _taoPhieuVatTuDiKem(rowId){
			var _opt="02D015";
			var _msg="Tạo phiếu vật tư đi kèm";     	
			var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			var rowData = $('#grdDanhSachChiDinh').jqGrid('getRowData', rowId);
		     	
			if(rowData != null){
				paramInput={
					khoaId: khoa_chuyen_den_id,
					phongId: phong_chuyen_den_id,
					khambenhid : data_ar[0].KHAMBENHID,
					maubenhphamid : "",
					loaikedon: 1,
					dichvuchaid: rowData.DICHVUKHAMBENHID,
					opt : _opt // tao phieu thuoc		
				};	
		  					
				dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1300,590);
				DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
			}
		}    
		
		function _taoPhieuThuocDiKem_haophi(rowId){        	
			var _opt="02D010";
			var _msg="Tạo phiếu thuốc đi kèm hao phí";  
			var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			var rowData = $('#grdDanhSachChiDinh').jqGrid('getRowData', rowId);
		
			if(rowData != null){
				paramInput={
					khoaId: khoa_chuyen_den_id,
					phongId: phong_chuyen_den_id,
					khambenhid : data_ar[0].KHAMBENHID,
					maubenhphamid : "",
					loaikedon: 1,
					dichvuchaid: rowData.DICHVUKHAMBENHID,
					opt : _opt, // tao phieu thuoc
					macdinh_hao_phi : 9
				};   		 
				dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1300,590);
				DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
			}
		}
		 
		function _taoPhieuVatTuDiKem_haophi(rowId){
			var _opt="02D015";
			var _msg="Tạo phiếu vật tư đi kèm hao phí";  
			var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			var rowData = $('#grdDanhSachChiDinh').jqGrid('getRowData', rowId);

			if(rowData != null){
				paramInput={
					khoaId: khoa_chuyen_den_id,
					phongId: phong_chuyen_den_id,
					khambenhid : data_ar[0].KHAMBENHID,
					maubenhphamid : "",
					loaikedon: 1,
					dichvuchaid: rowData.DICHVUKHAMBENHID,
					opt : _opt, // tao phieu thuoc
					macdinh_hao_phi : 9
				
				};	
		  					
				dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1300,590);
				DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
			}
		}
		 
		function _dsPhieuThuocVatTuDiKem(rowId){  
			var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			var rowData = $('#grdDanhSachChiDinh').jqGrid('getRowData', rowId);
			
			if(rowData != null){
				paramInput={
					khoaId: khoa_chuyen_den_id,
					phongId: phong_chuyen_den_id,
					khambenhid : data_ar[0].KHAMBENHID,      					
					dichvucha_id: rowData.DICHVUKHAMBENHID 					
	   			};	      					
	   			dlgPopup=DlgUtil.buildPopupUrl("divDlgDSPTDK","divDlg","manager.jsp?func=../noitru/NTU02D043_DanhSachPhieuThuocDiKem",paramInput,"Danh sách phiếu thuốc, vật tư đi kèm",1300,620);
	   			DlgUtil.open("divDlgDSPTDK"); 
			}
		}
		 
		//tuyennx_add_start yc L2K74TW-224
		function _dsPhieuThuocVatTu(rowId){  
			var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			var rowData = $('#grdDanhSachChiDinh').jqGrid('getRowData', rowId);
			
			if(rowData != null){
				paramInput={
					khoaId: khoa_chuyen_den_id,
					phongId: phong_chuyen_den_id,
					khambenhid : data_ar[0].KHAMBENHID,      					
					benhnhanid: data_ar[0].BENHNHANID,
					trangthaikhambenh: data_ar[0].TRANGTHAIKHAMBENH
				};	      					
	   			dlgPopup=DlgUtil.buildPopupUrl("divDlgDSPTVT","divDlg","manager.jsp?func=../noitru/NTU02D078_DanhSachPhieuThuocVatTu",paramInput,"Danh sách phiếu thuốc, vật tư",1200,635);
	   			DlgUtil.open("divDlgDSPTVT"); 
			}
		}
		//tuyennx_add_end yc L2K74TW-224
		 
		EventUtil.setEvent("assignDrug_cancel", function(e) {
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);	
		});		 
				 
		EventUtil.setEvent("assignSevice_saveTaoPhieuThuoc", function(e) {
			DlgUtil.showMsg(e.msg);	
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
		});
		
		
		$("#grdKetQuaXetNghiem").jqGrid("setGridParam", {
			onSelectRow : function (rowid) {
				GridUtil.unmarkAll("grdKetQuaXetNghiem");
				GridUtil.markRow("grdKetQuaXetNghiem", rowid);
				GridUtil.setEditRow("grdKetQuaXetNghiem",rowid);
				  
				$("input[name='NGAYKETQUA']").focusout(function() {
					  $("#grdKetQuaXetNghiem").jqGrid('saveRow', rowid, 'clientArray');
				});
				
				var res = $("#grdKetQuaXetNghiem").jqGrid('getRowData', rowid);
				$("#hdfIDKetQuaCLS").val(res.KETQUACLSID);
				$("#hdfIDDichVuKB").val(res.DICHVUKHAMBENHID);
				v_dvkbid = res.DICHVUKHAMBENHID;//L2PT-19087
				// ẩn hiện nút ký số
				if($("#grdKetQuaXetNghiem").getGridParam("reccount") > 1) {									
					if(optKySo=="1"||optKySo=="2"){
						var param = [$("#hdfIDMauBenhPham").val(), v_dvkbid];//L2PT-19087
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", param.join("$"));
						if(data_ar!=undefined && data_ar!=null){
							if(data_ar.length > 0){
								var trangthai = data_ar[0]["TRANGTHAI"];
								if(trangthai==""||trangthai=="0"||trangthai=="2"){										
									//$("#btnHuyTraKetQua").attr("disabled", false);
									
									$("#btnKySo").show();
									$("#btnHuyKySo").hide();
									$('#btnInPhieuKy').show();
									
									$("#btnInPhieuKy").attr("disabled", true);
								}
								else if(trangthai=="1"){
									//showButtonSuaNgayTN(false);
									//showButtonSuaNgayTraKQ(false);
									//`$("#btnHuyTraKetQua").attr("disabled", true);

									$("#btnKySo").hide();
									$("#btnHuyKySo").show();
									$('#btnInPhieuKy').show();

									$("#btnInPhieuKy").attr("disabled", false);
								}
							}
							else {
								$("#btnKySo").show();
								$("#btnHuyKySo").hide();
								$('#btnInPhieuKy').show();
								
								$("#btnInPhieuKy").attr("disabled", true);
							}
						}
					}
					else {
						$("#btnKySo").hide();
						$("#btnHuyKySo").hide();
						$('#btnInPhieuKy').hide();
					}
					// hết ẩn hiển nút ký số
				}
			},			
			ondblClickRow : function (rowid,iRow,iCol,e) {
				if(userRights!="VIW"){
					var row = $("#grdKetQuaXetNghiem").jqGrid('getRowData', rowid);
					 resizingTable = "0";
					var _arrDV = _dsMADV.split(',');
					if (_arrDV != null && _arrDV.length > 0) {
						for(var i = 0 ; i< _arrDV.length; i++){
							if(row['MADICHVU']==_arrDV[i]){
								resizingTable ="1";
							}
						}
					}		
					if (row['TRANGTHAIMAUBENHPHAM'] == "2") {
						var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
						var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
						var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
						if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT == ""){
							DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");					
						}
						else {
							var url = "manager.jsp?func=../canlamsang/CLS01X007_TiepNhanBenhPham&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
										"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&rolePTH="+rolePTH;//nghiant 28082017;
		
							EventUtil.setEvent("CLS01X007_TiepNhanBP",function(e){
								DlgUtil.close("dlgTiepNhanBP");
								reloadAllGrid();
							});
							EventUtil.setEvent("CLS01X007_Thoat",function(e){
								DlgUtil.close("dlgTiepNhanBP");						
							});
							
							var dlgPopup = DlgUtil.buildPopupUrl("dlgTiepNhanBP","divNhapKetQua",url,{},"Tiếp nhận bệnh phẩm",1160,600);
							dlgPopup.open("dlgTiepNhanBP");
						}
					}				
					else if (row['TRANGTHAIMAUBENHPHAM'] == "4") {
						dichvuid =row['DICHVUTHUCHIENID'];
						$("#hdfIDDichVuKB").val(row['DICHVUKHAMBENHID']);
						console.log('dichvuid: '+dichvuid);
						if(iCol == 6 || iCol == 11 || iCol == 14){
							console.log("Trạng thái mẫu bệnh phẩm: "+ row['TRANGTHAIMAUBENHPHAM']+", iCol: "+iCol);
						} else {						
							var url ="";
							var titleUrl ="Nhập kết quả xét nghiệm";
							url = "manager.jsp?func=../canlamsang/CLS02C003_TraKetQuaCDHA&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
							"&idketquacls="+$("#hdfIDKetQuaCLS").val()+"&type=update"+"&rolePTH="+rolePTH+"&resizingTable="+resizingTable+"&origin=XN";
							EventUtil.setEvent("CLS02C003_LUU", function (e) {
								reloadAllGrid();
							});
							EventUtil.setEvent("CLS02C003_HUY", function (e) {
								DlgUtil.close("dlgSuaKetQua");
							});
							
							EventUtil.setEvent("CLS02C003_LUU_DONG", function (e) {
						    	DlgUtil.showMsg('Lưu kết quả thành công',undefined,1500);
								reloadAllGrid();
								DlgUtil.close("dlgSuaKetQua");
							});						
							
							var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaKetQua","divSuaKetQua",url,{},"Kết quả xét nghiệm",1160,600);
							dlgPopup.open("dlgSuaKetQua");
						}
					} 
					else if (row['TRANGTHAIMAUBENHPHAM'] == "3"){
						var url = "manager.jsp?func=../canlamsang/CLS02C003_TraKetQuaCDHA&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&idketquacls="+$("#hdfIDKetQuaCLS").val()+"&type=read"+"&rolePTH="+rolePTH+"&resizingTable="+resizingTable+"&origin=XN";
	
						EventUtil.setEvent("CLS02C003_LUU", function (e) {
							reloadAllGrid();
						});
						EventUtil.setEvent("CLS02C003_HUY", function (e) {
							DlgUtil.close("dlgNhapKetQua");
						});
						
						var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},"Kết quả xét nghiệm",1160,600);
						dlgPopup.open("dlgNhapKetQua");
					}
					else {
						console.log("Trạng thái mẫu bệnh phẩm: "+ row['TRANGTHAIMAUBENHPHAM']);
					}
				}
			},
			gridComplete: function(data) {
				var rowids = $("#grdKetQuaXetNghiem").getDataIDs();
	    		if(rowids!=""){
		    		for(var i=0; i<rowids.length; i++){
		    			var rowid = rowids[i];
		    			var row = $("#grdKetQuaXetNghiem").jqGrid('getRowData',rowid);
		    			
		    			// tô màu các kết quả vượt cận
		    			var ket_qua = row.GIATRI_KETQUA;
		    			var tri_so_binh_thuong = row.TRISOBINHTHUONG;
						var doi_tuong = "";
						
						if(tri_so_binh_thuong.toUpperCase().indexOf("TE") >= 0){
							if(bnTuoi < 6) doi_tuong = "TE";
							else doi_tuong = "NL";
						}
						else {
							if(bnGioiTinh == 1) doi_tuong = "Nam";
							else doi_tuong = "Nữ";
						}
						
						var abnormal = CheckAbnormalResult(ket_qua, doi_tuong, tri_so_binh_thuong);
						
						if (abnormal == "-1")
	                    {
							$('#grdKetQuaXetNghiem').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
	                	        $(element).css("font-weight","bold");
	                			$(element).css("color", "blue");
	                	    });
	                    }
	                    else if (abnormal == "1")
	                    {
	                    	$('#grdKetQuaXetNghiem').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
	                    		$(element).css("font-weight","bold");
	                			$(element).css("color", "red");
	                	    });
	                    }
						
						if(row.VATTUDIKEM!=""){
							$('#grdKetQuaXetNghiem').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
	                	        $(element).css("font-weight","bold");
	                	    });
						}
						// hiển thị icon trạng thái
						var icon = '';
						if(row.TRANGTHAIKETQUA == 1) {
							icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
						}
						else if(row.TRANGTHAIKETQUA == 2) {
							icon = '<center><img src="../common/image/Pin.png" width="15px"></center>';
						}
						else if(row.TRANGTHAIKETQUA == 3) {
							icon = '<center><img src="../common/image/Misc_calendar.png" width="15px"></center>';
						}
						else if(row.TRANGTHAIKETQUA == 4) {
							icon = '<center><img src="../common/image/Flag_Green.png" width="15px"></center>';
						}
						else if(row.TRANGTHAIKETQUA == 5) {
							icon = '<center><img src="../common/image/Pointer_waiting.png" width="15px"></center>';
						}
						else if(row.TRANGTHAIKETQUA == 6) {
							icon = '<center><img src="../common/image/Pointer_waiting.png" width="15px"></center>';
						}
						else if(row.TRANGTHAIKETQUA == 7) {
							icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
						}
						else if(row.TRANGTHAIKETQUA == 8) {
							icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
						}
						$("#grdKetQuaXetNghiem").jqGrid ('setCell', rowid, 1, icon);
				    }
	    		}
	    		
	    		if(userRights!="VIW"){
		    		$(".jqgrow", "#" + "grdKetQuaXetNghiem").contextMenu('contextMenu2', {
		    			bindings: {
							'CapNhatPTTT': function (t) {
		    					var rowId = $(t).attr("id");
								var rowData = $('#grdKetQuaXetNghiem').jqGrid('getRowData', rowId);
								var upd_pttt_ht = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_CHOPHEP_UPD_PTTT_HT');
								var upd_pttt_ktba = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_CHOPHEP_UPD_PTTT_KTBA');
		    					if(rowData.TRANGTHAIMAUBENHPHAM =='3' && upd_pttt_ht == "0"){
		    						DlgUtil.showMsg("Phiếu đã hoàn thành không cho phép chỉnh sửa");
		    					} else if (rowData.TRANGTHAITIEPNHAN !='0' && upd_pttt_ktba == "0") {
									DlgUtil.showMsg("Bệnh án đã kết thúc không cho phép chỉnh sửa");
								} else {
									var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C001_TTPTTT",rowData.KETQUACLSID);
									
									var paramInput={
										mabenhnhan : data_ar[0].MABENHNHAN,
										khambenhid : data_ar[0].KHAMBENHID,
										tenbenhnhan : data_ar[0].TENBENHNHAN,
										namsinh : data_ar[0].NGAYSINH,
										nghenghiep : data_ar[0].TENNGHENGHIEP,
										hosobenhanid : data_ar[0].HOSOBENHANID,
										tiepnhanid : data_ar[0].TIEPNHANID,
										phongid : data_ar[0].PHONGID,
										benhnhanid : data_ar[0].BENHNHANID,
										khoaid : data_ar[0].KHOAID,     					
										tenphong: data_ar[0].PHONGDIEUTRI,
										thoigianvaovien: data_ar[0].THOIGIANVAOVIEN,
										maubenhphamid : data_ar[0].MAUBENHPHAMID,
										dichvukhambenhid : data_ar[0].DICHVUKHAMBENHID,
										ngaymaubenhpham : data_ar[0].NGAYMAUBENHPHAM,
										callfrom : '1'//L2PT-19030
									};						
									dlgPopup=DlgUtil.buildPopupUrl("dlgPTTT","divDlg","manager.jsp?func=../noitru/NTU02D037_PhauthuatThuThuat",paramInput,"Cập nhật phẫu thuật thủ thuật",1100,610);
									DlgUtil.open("dlgPTTT");    
									EventUtil.setEvent("assignSevice_savePTTT", function (e) {	
										if(e.msg != null){
						    				DlgUtil.showMsg(e.msg);
						    			}
										DlgUtil.close("dlgPTTT");
									});
		    					}
		    				},
							'ketQuaSinhThiet': function (t) {
			            		var rowId = $(t).attr("id");
			            		var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					
								var paramInput={
									dichvukhambenhid:rowData.DICHVUKHAMBENHID,
									ketquaclsid:rowData.KETQUACLSID
			    				};
		    					
		    					EventUtil.setEvent("assignSevice_saveChangeDev", function(e) {
		    		    			if(typeof(e) != 'undefined'){
		    		    				DlgUtil.showMsg(e.msg);
		    		    			}
		    		    			DlgUtil.close(e.divId);
		    		    			reloadAllGrid();
		    		    		});
		    					EventUtil.setEvent("assignSevice_saveKetQuaGPBOk",function(e){
		    						DlgUtil.close("divDlgGPB");		    						
		    					});
		    					dlgPopup=DlgUtil.buildPopupUrl("divDlgGPB","divDlg","manager.jsp?func=../canlamsang/CLS01X017_KetQuaSinhThiet",paramInput,"Kết quả sinh thiết",1200,600);
			    				DlgUtil.open("divDlgGPB");
		    				},
		    				'ketQuaGiaiPhauBenh': function (t) {
			            		var rowId = $(t).attr("id");
			            		var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					
								var paramInput={
									dichvukhambenhid:rowData.DICHVUKHAMBENHID,
									ketquaclsid:rowData.KETQUACLSID
			    				};
		    					
		    					EventUtil.setEvent("assignSevice_saveChangeDev", function(e) {
		    		    			if(typeof(e) != 'undefined'){
		    		    				DlgUtil.showMsg(e.msg);
		    		    			}
		    		    			DlgUtil.close(e.divId);
		    		    			reloadAllGrid();
		    		    		});
		    					EventUtil.setEvent("assignSevice_saveKetQuaGPBOk",function(e){
		    						DlgUtil.close("divDlgGPB");		    						
		    					});
		    					var popupGPB = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_POPUP_KQXN_GPB');
		    					if(popupGPB=="1"){
		    						dlgPopup=DlgUtil.buildPopupUrl("divDlgGPB","divDlg","manager.jsp?func=../canlamsang/CLS01X013_KetQuaGiaiPhau",paramInput,"Kết quả giải phẫu bệnh",1200,600);
		    					} else if(popupGPB=="2"){
		    						dlgPopup=DlgUtil.buildPopupUrl("divDlgGPB","divDlg","manager.jsp?func=../canlamsang/CLS01X017_KetQuaGiaiPhau",paramInput,"Kết quả giải phẫu bệnh",1200,600);
		    					} else if(popupGPB=="3"){
		    						dlgPopup=DlgUtil.buildPopupUrl("divDlgGPB","divDlg","manager.jsp?func=../canlamsang/CLS01X019_KetQuaGiaiPhau",paramInput,"Kết quả giải phẫu bệnh",1200,600);
		    					} else {
		    						dlgPopup=DlgUtil.buildPopupUrl("divDlgGPB","divDlg","manager.jsp?func=../canlamsang/CLS01X011_KetQuaGiaiPhau",paramInput,"Kết quả giải phẫu bệnh",1200,600);
		    					}
			    				DlgUtil.open("divDlgGPB");
		    				},
							'NhapKQSD': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					
		    					dichvuid = rowData.DICHVUTHUCHIENID;
		    					console.log('dichvuid: '+dichvuid+', DICHVUKHAMBENHID: '+rowData.DICHVUKHAMBENHID);
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var url ="";
		    					if(rowData.TRANGTHAIMAUBENHPHAM =='4' || rowData.TRANGTHAIMAUBENHPHAM =='3'){
		    						 url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_AFB_K74&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					} 
		    					else {		    						
		    						url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_AFB_K74&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=read&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}		    					
		    					
		    					var titleUrl="Nhập kết quả xét nghiệm cho vi khuẩn Soi đờm";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});			
		    					EventUtil.setEvent("CLS01X002_HUY",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,1290,680);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
		    				'NhapKQRMP': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					dichvuid =rowData.DICHVUTHUCHIENID;
		    					console.log('dichvuid: '+dichvuid+', DICHVUKHAMBENHID: '+rowData.DICHVUKHAMBENHID);
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var url ="";
		    					if(rowData.TRANGTHAIMAUBENHPHAM =='4' || rowData.TRANGTHAIMAUBENHPHAM =='3'){
		    						 url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_RMP_XPERT&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}else {
		    						url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_RMP_XPERT&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=read&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}
		    					var titleUrl="Nhập kết quả xét nghiệm Kháng RMP Xpert";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});			
		    					EventUtil.setEvent("CLS01X002_HUY",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,1290,600);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
		    				'NhapKQVKL12': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					//====== Added by NGHIANT, SONDN
		    					dichvuid =rowData.DICHVUTHUCHIENID;
		    					console.log('dichvuid: '+dichvuid+', DICHVUKHAMBENHID: '+rowData.DICHVUKHAMBENHID);
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var url ="";
		    					if(rowData.TRANGTHAIMAUBENHPHAM =='4' || rowData.TRANGTHAIMAUBENHPHAM =='3'){
		    						url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_VKLM1_K74&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}else{
		    						url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_VKLM1_K74&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=read&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}
		    					
		    					titleUrl="Nhập kết quả xét nghiệm cho vi khuẩn lao";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});			
		    					EventUtil.setEvent("CLS01X002_HUY",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,1290,600);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
							'NhapKQSARSCOV2': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					//====== Added by NGHIANT, SONDN
		    					dichvuid =rowData.DICHVUTHUCHIENID;
		    					console.log('dichvuid: '+dichvuid+', DICHVUKHAMBENHID: '+rowData.DICHVUKHAMBENHID);
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var url ="";
		    					if(rowData.TRANGTHAIMAUBENHPHAM =='4' || rowData.TRANGTHAIMAUBENHPHAM =='3'){
		    						url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_SARS_COV&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}else{
		    						url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_SARS_COV&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=read&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}
		    					
		    					titleUrl="Nhập kết quả VS SARS-CoV-2";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});			
		    					EventUtil.setEvent("CLS01X002_HUY",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,1290,600);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
		    				'NhapKQKSDNL22': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					
		    					dichvuid =rowData.DICHVUTHUCHIENID;
		    					console.log('dichvuid: '+dichvuid+', DICHVUKHAMBENHID: '+rowData.DICHVUKHAMBENHID);
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var url = "";
		    					if(rowData.TRANGTHAIMAUBENHPHAM =='4' || rowData.TRANGTHAIMAUBENHPHAM =='3'){
		    						 url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_VKKSD2_K74&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
		 							"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update"+
		 							"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=true"+"&tiepnhanid="+tiepnhanid+
		 							"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}else{
		    						 url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_VKKSD2_K74&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
		 							"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=read"+
		 							"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=true"+"&tiepnhanid="+tiepnhanid+
		 							"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}
		    					var titleUrl="Nhập kết quả kháng sinh đồ";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});			
		    					EventUtil.setEvent("CLS01X002_HUY",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,1290,600);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
		    				//cap nhat ma may
		    				'UpdateMamay': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					
		    					dichvuid =rowData.DICHVUTHUCHIENID;
		    					console.log('dichvuid: '+dichvuid+', DICHVUKHAMBENHID: '+rowData.DICHVUKHAMBENHID);
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var url = "";
		    					
		    					var dsdichvuid= '';
		    					var dsKQCLSID= '';
		    		            var idss = $('#grdKetQuaXetNghiem').jqGrid( 'getGridParam', 'selarrrow' );
		    		            for (i = 0, n = idss.length; i < n; i++)
		    		            {
		    		            	var rowSelected =  $('#grdKetQuaXetNghiem').jqGrid('getRowData', idss[i]);
		    		            	console.log("rowSelected.DICHVUID: "+rowSelected.DICHVUID);
		    		            	if(dsdichvuid != '' && dsdichvuid != ""){
		    		            		dsdichvuid =dsdichvuid+','+ rowSelected.DICHVUID;
		    		            	}else{
		    		            		dsdichvuid=rowSelected.DICHVUID;
		    		            	}
		    		            	
		    		            	if(dsKQCLSID != '' && dsKQCLSID != ""){
		    		            		dsKQCLSID =dsKQCLSID+','+ rowSelected.KETQUACLSID;
		    		            	}else{
		    		            		dsKQCLSID=rowSelected.KETQUACLSID;
		    		            	}
		    		            }
		    					
		    					if(rowData.TRANGTHAIMAUBENHPHAM =='4' || rowData.TRANGTHAIMAUBENHPHAM =='3'){
		    						url = "manager.jsp?func=../canlamsang/CLS01X003_UPDATE_MAMAY&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
			 							"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update"+
			 							"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=true"+"&tiepnhanid="+tiepnhanid+
			 							"&rolePTH="+rolePTH+"&dichvuid="+dsdichvuid+"&dskqclsid="+dsKQCLSID;
		    					}else{
		    						url = "manager.jsp?func=../canlamsang/CLS01X003_UPDATE_MAMAY&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
			 							"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=read"+
			 							"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=true"+"&tiepnhanid="+tiepnhanid+
			 							"&rolePTH="+rolePTH+"&dichvuid="+dsdichvuid+"&dskqclsid="+dsKQCLSID;
		    					}
		    					var titleUrl="Cập nhật mã máy cho các dịch vụ đã chọn";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});
		    					EventUtil.setEvent("CLS01X002_HUY",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,500,250);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
		    				'TTTruyenMau': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var url = "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenMau_PUHH&dichvukhambenhid="+$("#hdfIDDichVuKB").val();
		    					var titleUrl="Cập nhật thông tin truyền máu";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});
		    					EventUtil.setEvent("CLS01X002_HUY",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,600,350);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
		    				'kqNhuomphiemdo': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					
		    					var rowBa = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
		    					var rowDataBa = $('#grdBenhPhamDangLam').jqGrid('getRowData', rowBa);
		    					var mahosobenhan = rowDataBa.MAHOSOBENHAN;
		    					var maubenhphamid = rowDataBa.MAUBENHPHAMID;
		    					
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var url = "manager.jsp?func=../noitru/NTU02D146_Phieu_KQ_Papanicolaou&dichvukhambenhid="+$("#hdfIDDichVuKB").val()+"&mahosobenhan="+mahosobenhan+"&maubenhphamid="+maubenhphamid;
		    					var titleUrl="Cập nhật kết quả nhuộm phiến đồ tế bào theo Papanicolaou";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});
		    					EventUtil.setEvent("assignPapanicolaou_cancel",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,800,650);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
		    				'NhapKQTBH': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					
		    					var rowBa = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
		    					var rowDataBa = $('#grdBenhPhamDangLam').jqGrid('getRowData', rowBa);
		    					var mahosobenhan = rowDataBa.MAHOSOBENHAN;
		    					var maubenhphamid = rowDataBa.MAUBENHPHAMID;
		    					var ketquaclsid = rowData.KETQUACLSID;
		    					var type = rowData.TRANGTHAIMAUBENHPHAM=="3"?"read":"update";
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var cauhinh_papsmear = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_POPUP_KQXN_PAPSMEAR');
		    					var url = "";
		    					if (cauhinh_papsmear=="1"){
		    						url = "manager.jsp?func=../canlamsang/CLS01X015_KQ_Papanicolaou&dichvukhambenhid="+$("#hdfIDDichVuKB").val()+"&mahosobenhan="+mahosobenhan+"&maubenhphamid="+maubenhphamid+"&ketquaclsid="+ketquaclsid+"&type="+type;
		    					}
		    					else if (cauhinh_papsmear=="2"){
		    						url = "manager.jsp?func=../canlamsang/CLS01X015_KQ_PapSmear&dichvukhambenhid="+$("#hdfIDDichVuKB").val()+"&mahosobenhan="+mahosobenhan+"&maubenhphamid="+maubenhphamid+"&ketquaclsid="+ketquaclsid+"&type="+type;
		    					}
		    					else {
		    						url = "manager.jsp?func=../canlamsang/CLS01X014_KQ_TeBaoHoc&dichvukhambenhid="+$("#hdfIDDichVuKB").val()+"&mahosobenhan="+mahosobenhan+"&maubenhphamid="+maubenhphamid+"&ketquaclsid="+ketquaclsid+"&type="+type;
		    					}
		    					
		    					var titleUrl="PHIẾU XÉT NGHIỆM PAP SMEAR";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});
		    					EventUtil.setEvent("assignPapanicolaou_cancel",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,900,600);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
							'InPhieuTuongTrinh': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);		    					
		    					var ketquaclsid = rowData.KETQUACLSID;
		    					//$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var par = [ 
									{name:'maubenhphamid',type:'String',value: $("#hdfIDMauBenhPham").val()},
									{name:'dichvukhambenhid',type:'String',value: rowData.DICHVUKHAMBENHID}
								];
								openReport('window', 'NTU030_PHIEUPHAUTHUATTHUTHUAT_14BV01_QD4069_A4', 'pdf', par);
		    				},
							'InPhieuBoiDuong': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);		    					
		    					var ketquaclsid = rowData.KETQUACLSID;
		    					//$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var par = [ 
									{name:'maubenhphamid',type:'String',value: $("#hdfIDMauBenhPham").val()},
									{name:'dichvukhambenhid',type:'String',value: rowData.DICHVUKHAMBENHID}
								];
								openReport('window', 'PHIEU_THANHTOAN_BDPTTT', 'pdf', par);
		    				},
							'NhapKQXPERT': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
		    					dichvuid =rowData.DICHVUTHUCHIENID;
		    					console.log('dichvuid: '+dichvuid+', DICHVUKHAMBENHID: '+rowData.DICHVUKHAMBENHID);
		    					$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
		    					var url ="";
		    					if(rowData.TRANGTHAIMAUBENHPHAM =='4' || rowData.TRANGTHAIMAUBENHPHAM =='3'){
		    						 url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_XPERT&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}else {
		    						url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_XPERT&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
									"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=read&q="+userRights+
									"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&tiepnhanid="+tiepnhanid+
									"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;
		    					}
		    					var titleUrl="Nhập kết quả xét nghiệm Xpert";
		    					EventUtil.setEvent("CLS01X002_LUU",function(e){
		    						$("#hdfIDMauBenhPham").val();
		    						reloadAllGrid();
		    					});			
		    					EventUtil.setEvent("CLS01X002_HUY",function(e){
		    						DlgUtil.close("dlgNhapKetQua");
		    						reloadAllGrid();
		    					});
		    					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua","divNhapKetQua",url,{},titleUrl,1290,600);
		    					dlgPopup.open("dlgNhapKetQua");
		    				},
							//Quyetnp add
							'themPhieuKQ' : function(t) {
								var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
								var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
								var rowId = $(t).attr("id");
								var rowData = $('#' + "grdKetQuaXetNghiem").jqGrid('getRowData', rowId);
								if (rowDataCk != null) {
									var obj = new Object();
									obj.HOSOBENHANID = rowDataCk.HOSOBENHANID;
									obj.TIEPNHANID = rowDataCk.TIEPNHANID;
									obj.KHAMBENHID = rowDataCk.KHAMBENHID;
									obj.MAHOSOBENHAN = rowDataCk.MAHOSOBENHAN;
									obj.BENHNHANID = rowDataCk.BENHNHANID;
									obj.MAUBENHPHAMID = rowDataCk.MAUBENHPHAMID;
									obj.DICHVUKHAMBENHID = rowData.DICHVUKHAMBENHID;
									obj.KHOAID = rowDataCk.KHOACHUYENDENID;
									obj.PHONGID = rowDataCk.PHONGCHUYENDENID;
									obj.LOAI_PHIEU = "CLS_XETNGHIEM";
									dlgPopup = DlgUtil.buildPopupUrl("divDlgThemPhieu", "divDlg", "manager.jsp?func=../noitru/NTU02D204_ThemPhieu", obj, "HIS-Thêm phiếu",
											window.innerWidth * 0.95, window.innerHeight * 0.95);
									DlgUtil.open("divDlgThemPhieu");
								} else {
									DlgUtil.showMsg('Chưa chọn bệnh nhân!');
									return;
								}
							}
		    			},
		    			onContextMenu: function (event, menu) {
		    				var rowId = $(event.target).parent("tr").attr("id");
		    				var grid = $("#" + "grdKetQuaXetNghiem");
		    				//grid.setSelection(rowId);
		    				GridUtil.unmarkAll("grdKetQuaXetNghiem");
		    				GridUtil.markRow("grdKetQuaXetNghiem",rowId);
		    				return true;
		    			}
		    		});
	    		}
	    		
				if(show_truyenmau!=i_hid){
		    		   $('#TTTruyenMau').remove();
		    	}	
			}
		});

		$("#grdKetQuaXetNghiem").bind("jqGridAfterLoadComplete", function (e, rowid, orgClickEvent) {
			var rowIds = $('#grdKetQuaXetNghiem').jqGrid('getDataIDs');
			var htmlBegin =  '<select class="form-control input-sm" id="@thisisid" multiple="multiple" > ';
			var htmlEnd = '</select>';
			for(var i = 0; i < rowIds.length; i++){
				var _row1 = $("#grdKetQuaXetNghiem").jqGrid('getRowData', rowIds[i]);
				// xay dung combobox;
				var _parram = Number(_row1.DICHVUID);
				var obt = null;
				if(_row1.MAMAY!= null && _row1.MAMAY != '' && _row1.MAMAY != ""){
					obt = getMamay(_row1.MAMAY);
				}
				
				if(obt == null){
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS.GETMAMAY",_parram);
					var html = '';
					var htmlBody = '<option value=" ">--Chọn mã máy--</option>';
					var htmlBody = '';
					for(t = 0;t<data_ar.length;t++){
						if(data_ar[t].IS_DEFAULT=="1" && _row1.MAMAY.trim()=="" && _row1.TRANGTHAIMAUBENHPHAM=="4" && _mamaymacdinh=="1"){
							htmlBody += '<option value="'+data_ar[t].NAME+'" selected>'+data_ar[t].TENMAY+" - "+data_ar[t].NAME+'</option>'
						}
						else if(_mamaymacdinh=="2" && data_ar.length==1 && _row1.MAMAY.trim()=="" && _row1.TRANGTHAIMAUBENHPHAM=="4"){
							htmlBody += '<option value="'+data_ar[t].NAME+'" selected>'+data_ar[t].TENMAY+" - "+data_ar[t].NAME+'</option>';
						}
						else if(_mamaymacdinh=="2" && data_ar.length>1 && data_ar[t].PHONGID == $("#cboPhongThucHien").val() && _row1.MAMAY.trim()=="" && _row1.TRANGTHAIMAUBENHPHAM=="4"){
							htmlBody += '<option value="'+data_ar[t].NAME+'" selected>'+data_ar[t].TENMAY+" - "+data_ar[t].NAME+'</option>';
						}
						else if(_mamaymacdinh=="2" && data_ar.length>1 && data_ar[t].IS_DEFAULT=="1" && _row1.MAMAY.trim()=="" && _row1.TRANGTHAIMAUBENHPHAM=="4"){
							htmlBody += '<option value="'+data_ar[t].NAME+'" selected>'+data_ar[t].TENMAY+" - "+data_ar[t].NAME+'</option>';
						}
						else {
							htmlBody += '<option value="'+data_ar[t].NAME+'">'+data_ar[t].TENMAY+" - "+data_ar[t].NAME+'</option>'
						}
					}
					html = htmlBegin + htmlBody + htmlEnd;
					
					var obtt = new Object();
					obtt.NAME = _row1.MAMAY; 
					obtt.HTML = html; 
					setMamay(obtt); 
				}else{
					html = obt.HTML; 
				}
				
				var valuesArray = _row1.MAMAY.split(';');
				// Duyệt qua mảng và tạo các chuỗi mới với giá trị thay thế
				valuesArray.forEach(value => {
					html = html.replace('<option value="'+value+'">', '<option value="'+value+'" selected>');
					//console.log(value);
				});				
				
				html = html.replace('<option value="'+_row1.MAMAY.trim()+'">', '<option value="'+_row1.MAMAY.trim()+'" selected>');
				html = html.replace('@thisisid', _row1.DICHVUTHUCHIENID  + '_cboMAMAY');
				$("#grdKetQuaXetNghiem").jqGrid ('setCell', rowIds[i], 'MAMAY', html);				 
			 					 
				$('#'+_row1.DICHVUTHUCHIENID + '_cboMAMAY').select2({
					placeholder: 'Chọn mã máy',
					width: '105px',
					allowClear: true
				});
			 				
			}
		});

		$("#btnXemLichSuHSBA").on("click", function() {
			var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
			var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			var rowData = $('#grdKetQuaChanDoan').jqGrid('getRowData', selRowId);

			if(rowData != null){
				var paramInput={
					benhnhanId : data_ar[0].BENHNHANID,
					khambenhid : data_ar[0].KHAMBENHID,
				};
				dlgPopup=DlgUtil.buildPopupUrl("dlgLichSuBenhAn","divDlg","manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn",paramInput,"Lịch sử bệnh án",1320,610);
				DlgUtil.open("dlgLichSuBenhAn");
			}
		});
		
		//nghiant 27102017
		$("#grdDanhSachDaChiDinh").jqGrid("setGridParam", {
			gridComplete: function(id){
				if(userRights!="VIW"){
		        	$(".jqgrow", "#" + "grdDanhSachDaChiDinh").contextMenu('contextMenu', {
		    			bindings: {
		    				'ThemPST': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdDanhSachDaChiDinh").jqGrid('getRowData', rowId);
		    					//====== Added by NGHIANT, SONDN
		    					var title = "Nhập kết quả sinh thiết"; 
		    					var myVar={
		    		    				 maubenhphamid : rowData.MAUBENHPHAMID, 
		    		            		 dichvukhambenhid : rowData.DICHVUKHAMBENHID,    
		    		            		 tendichvu : rowData.TENDICHVU
		    		        		};
		    		        		 
	    		    			dlgPopup=DlgUtil.buildPopupUrl("dlgMauSinhThiet", 
	    		    					"divDlg", "manager.jsp?func=../ngoaitru/NGT02K062_MauSinhThiet", myVar, title, 1000, 500);
	    		    			DlgUtil.open("dlgMauSinhThiet");
	    		    			
	    		    			//====== End added by NGHIANT, SONDN
		    					
								EventUtil.setEvent("NGT02K062_LUU", function (e) {
									loadDanhSachDaChiDinh();
								});
								EventUtil.setEvent("NGT02K062_HUY", function (e) {
									DlgUtil.close("dlgThemPST");
								});
								
								var dlgPopup = DlgUtil.buildPopupUrl("dlgThemPST", "dlgThemPST", url, {}, "Thêm phiếu sinh thiết", 1020, 600);
								dlgPopup.open("dlgThemPST");
		    				},
		    				'sentRequest': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdDanhSachDaChiDinh").jqGrid('getRowData', rowId);
		    					
		    					var _trangthai=rowData.TRANGTHAIMAUBENHPHAM;
		    	        		if(_trangthai==null) _trangthai=0;
		    	        		_trangthai=parseInt(_trangthai);
		    	        		if(_trangthai==1){
		    	        			 var _par = [rowData.MAUBENHPHAMID,2,0];						
		    						 var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ",_par.join('$'));    		 
		    		   	       		 if(_return == 1){
		    		   	       		     DlgUtil.showMsg("Phiếu đã được gửi yêu cầu thành công!");
		    		   	       		     loadDanhSachDaChiDinh();	    		   	       			 
		    		   	       		 }else if(_return == 0){
		    		   	       		     DlgUtil.showMsg("Gửi yêu cầu phiếu thất bại!");
		    		   	       		} else if(_return == 5) {
	    		         						DlgUtil.showMsg("Không phải bác sĩ chỉ định, không được phép chỉnh sửa");
	    		         					}  			     			
		    	        		}else{
		    	        			DlgUtil.showMsg("Phiếu đã được gửi yêu cầu!");        			
		    	        		}
		    				},
		    				'deleteRequest': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdDanhSachDaChiDinh").jqGrid('getRowData', rowId);	    					
		    					var _trangthai=rowData.TRANGTHAIMAUBENHPHAM; 
		    	        		if(_trangthai==null) _trangthai=0;
		    	        		_trangthai=parseInt(_trangthai);
		    	        		if(_trangthai==2){        			
		    	        			 var _par = [rowData.MAUBENHPHAMID,1,1];						
		    						 var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ",_par.join('$'));	    						 
		    		   	       		 if(_return == 1){
		    		   	       		     DlgUtil.showMsg("Phiếu đã được hủy yêu cầu thành công!");		   	       			
		    		   	       		     loadDanhSachDaChiDinh();
		    		   	       		 }else if(_return == 0){
		    		   	       		     DlgUtil.showMsg("Hủy yêu cầu phiếu thất bại!");		   	       			 
		    		   	       		 }else if(_return == -1){
		    		   	       			 DlgUtil.showMsg("Phiếu xét nghiệm đã thu tiền nên không được hủy yêu cầu");
		    		   	       		} else if(_return == 5) {
	    		         						DlgUtil.showMsg("Không phải bác sĩ chỉ định, không được phép chỉnh sửa");
	    		         					}
		    	        		}else if(_trangthai==1){
		    	        			DlgUtil.showMsg("Phiếu đã được hủy yêu cầu!");        			
		    	        		}else if(_trangthai>2){
		    	        			DlgUtil.showMsg("Phiếu đã được xử lý nên không thể hủy yêu cầu");        			
		    	        		}
		    				},
		    				'delete': function (t) {
		    					var rowId = $(t).attr("id");
		    					var rowData = $('#' + "grdDanhSachDaChiDinh").jqGrid('getRowData', rowId);	    					
		    					 var _trangthai=rowData.TRANGTHAIMAUBENHPHAM; 
		    		         		if(_trangthai==null) _trangthai=0;
		    		         		_trangthai=parseInt(_trangthai);
		    		         		if(_trangthai==1 || _trangthai==8){         			
		    		         		DlgUtil.showConfirm("Bạn có chắc chắn xóa phiếu xét nghiệm không?",function(flag) {
		    		         		   if (flag) {
		    		         			     var _par = [rowData.MAUBENHPHAMID];						
		    								 var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL.PDV.024",_par.join('$'));    		 
		    				   	       		 if(_return == 1){
		    				   	       		     DlgUtil.showMsg("Xóa thành công phiếu xét nghiệm");		   	       			
		    				   	       		     loadDanhSachDaChiDinh();
		    				   	       		 }else if(_return == 0){
		    				   	       		     DlgUtil.showMsg("Xóa không thành công phiếu phiếu xét nghiệm");		   	       			 
		    				   	       		 }else if(_return == -1){
		    				   	       			 DlgUtil.showMsg("Phiếu xét nghiệm đã thu tiền nên không được phép xóa");
	    		         					} else if(_return == 5) {
	    		         						DlgUtil.showMsg("Không phải bác sĩ chỉ định, không được phép chỉnh sửa");
	    		         					}
		    		         		   } 
		    		         		 });
		    		        		}else if(_trangthai>=2 && _trangthai!=8){
		    		        			DlgUtil.showMsg("Phiếu đã được xử lý nên không thể xóa!");        			
		    		        		}
		    				},
		    				'updatePXN': function (t) {
		    					var rowId = $(t).attr("id");	    					
		    					var rowData = $('#' + "grdDanhSachDaChiDinh").jqGrid('getRowData', rowId);    					
		    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowData.MAUBENHPHAMID);				
		    					
		    					 var _trangthai=rowData.TRANGTHAIMAUBENHPHAM;
		    		        		if(_trangthai==null) _trangthai=0;
		    		        		_trangthai=parseInt(_trangthai);
		    		        		if(_trangthai <= 1){
		    		        			 //mo popup nhap benh nhan khi dbclick on row
		    			   				 var paramInput={							
			    			   						benhnhanid : data_ar[0].BENHNHANID,
			    									mabenhnhan : data_ar[0].MABENHNHAN,
			    									khambenhid : data_ar[0].KHAMBENHID,										
			    									tiepnhanid : data_ar[0].TIEPNHANID,
			    									hosobenhanid : data_ar[0].HOSOBENHANID,
			    									doituongbenhnhanid : data_ar[0].DOITUONGBENHNHANID,
			    									loaitiepnhanid : data_ar[0].LOAITIEPNHANID,
		    			   							maubenhphamid : rowData.MAUBENHPHAMID,
		    			   							loaiPhieu : 1,
		    			   							subDeptId : opt.subdept_id,
		    			   							modeFunction : 0	    			   							
		    			   					};			
		    			   					dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5,paramInput,"Cập nhật phiếu xét nghiệm",1300,600);
		    			   					DlgUtil.open("divDlgDichVu");		
		    		        		}else{
		    		        			DlgUtil.showMsg("Không thể sửa phiếu này!\nPhiếu này đã hoặc đang được xử lý");	        			
		    		        		}	 
		    				},
		    				'TAOPHIEUTHUOCKEM_HAOPHI': function (t) {
		    					var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
		    					var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
		    					if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
		    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
		    					}
		    					else {
			    					var rowId = $(t).attr("id");	    					
			    					var _opt="02D010";
			    					var _msg="Tạo phiếu thuốc đi kèm hao phí";  
			    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			    					var rowData = $('#grdDanhSachDaChiDinh').jqGrid('getRowData', rowId);
			    				
			    					if(rowData != null){
			    						paramInput={
			    							khoaId: khoa_chuyen_den_id,
			    							phongId: phong_chuyen_den_id,
			    							khambenhid : data_ar[0].KHAMBENHID,
			    							maubenhphamid : "",
			    							loaikedon: 1,
			    							dichvuchaid: rowData.DICHVUKHAMBENHID,
			    							opt : _opt, // tao phieu thuoc
			    							macdinh_hao_phi : 9
			    						};   		 
			    						dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1300,590);
			    						DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
			    					}
		    					}
		    				},
		    				'TAOPHIEUVATTUKEM_HAOPHI': function (t) {
		    					var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
		    					var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
		    					if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
		    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
		    					}
		    					else {
			    					var rowId = $(t).attr("id");	    					
			    					var _opt="02D015";
			    					var _msg="Tạo phiếu vật tư đi kèm hao phí";  
			    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			    					var rowData = $('#grdDanhSachDaChiDinh').jqGrid('getRowData', rowId);
	
			    					if(rowData != null){
			    						paramInput={
			    							khoaId: khoa_chuyen_den_id,
			    							phongId: phong_chuyen_den_id,
			    							khambenhid : data_ar[0].KHAMBENHID,
			    							maubenhphamid : "",
			    							loaikedon: 1,
			    							dichvuchaid: rowData.DICHVUKHAMBENHID,
			    							opt : _opt, // tao phieu thuoc
			    							macdinh_hao_phi : 9
			    						
			    						};	
			    				  					
			    						dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1300,590);
			    						DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
			    					}
		    					}
		    				},
		    				'TAOPHIEUTHUOCKEM': function (t) {    	
	    				     	var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
	    				     	var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
		    					if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
		    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
		    					}
		    					else {
			    					var rowId = $(t).attr("id");	    					
			    					var _opt="02D010"
		    				     	var _msg="Tạo phiếu thuốc đi kèm"; 
		    				     	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
		    				     	var rowData = $('#grdDanhSachDaChiDinh').jqGrid('getRowData', rowId);
		    				     	
		    				     	if(rowData != null){
		    				     		paramInput={
		    								khoaId: khoa_chuyen_den_id,
		    								phongId: phong_chuyen_den_id,
		    								khambenhid :  data_ar[0].KHAMBENHID,
		    								maubenhphamid : "",
		    								loaikedon: 1,
		    								dichvuchaid: rowData.DICHVUKHAMBENHID,
		    								opt : _opt // tao phieu thuoc     					
		    				  			};   		 
		    				  			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1300,590);
		    				  			DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
		    				     	}
		    					}
		    				},
		    				'TAOPHIEUVATTUKEM': function (t) {    	
		    					var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
		    					var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
		    					if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
		    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
		    					}
		    					else {
			    					var rowId = $(t).attr("id");	    					
			    					var _opt="02D015";
			    					var _msg="Tạo phiếu vật tư đi kèm"; 
			    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			    					var rowData = $('#grdDanhSachDaChiDinh').jqGrid('getRowData', rowId);
			    				     	
			    					if(rowData != null){
			    						paramInput={
			    							khoaId: khoa_chuyen_den_id,
			    							phongId: phong_chuyen_den_id,
			    							khambenhid : data_ar[0].KHAMBENHID,
			    							maubenhphamid : "",
			    							loaikedon: 1,
			    							dichvuchaid: rowData.DICHVUKHAMBENHID,
			    							opt : _opt // tao phieu thuoc		
			    						};	
			    				  					
			    						dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1300,590);
			    						DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
			    					}
		    					}
		    				},
		    				'DSPTVT_KEM': function (t) {
		    					var rowId = $(t).attr("id");	    					
		    					var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
		    					var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
		    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
		    					var rowData = $('#grdDanhSachDaChiDinh').jqGrid('getRowData', rowId);
		    					
		    					if(rowData != null){
		    						paramInput={
		    							khoaId: khoa_chuyen_den_id,
		    							phongId: phong_chuyen_den_id,
		    							khambenhid : data_ar[0].KHAMBENHID,      					
		    							dichvucha_id: rowData.DICHVUKHAMBENHID 					
		    			   			};	      					
		    			   			dlgPopup=DlgUtil.buildPopupUrl("divDlgDSPTDK","divDlg","manager.jsp?func=../noitru/NTU02D043_DanhSachPhieuThuocDiKem",paramInput,"Danh sách phiếu thuốc, vật tư đi kèm",1300,620);
		    			   			DlgUtil.open("divDlgDSPTDK"); 
		    					}
		    				}
		    				//tuyennx_add_start yc L2K74TW-224
		    				,
		    				'DSPTVT': function (t) {
		    					var rowId = $(t).attr("id");	    					
		    					var selRowId = $('#grdBenhPhamDangLam').jqGrid ('getGridParam', 'selrow');
		    					var rowDataCk= $('#grdBenhPhamDangLam').jqGrid('getRowData', selRowId);
		    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
		    					var rowData = $('#grdDanhSachDaChiDinh').jqGrid('getRowData', rowId);
		    					
		    					if(rowData != null){
		    						paramInput={
		    							khoaId: khoa_chuyen_den_id,
		    							phongId: phong_chuyen_den_id,
		    							khambenhid : data_ar[0].KHAMBENHID,      					
		    							benhnhanid: data_ar[0].BENHNHANID,
		    							trangthaikhambenh: data_ar[0].TRANGTHAIKHAMBENH
		    						};	      					
		    			   			dlgPopup=DlgUtil.buildPopupUrl("divDlgDSPTVT","divDlg","manager.jsp?func=../noitru/NTU02D078_DanhSachPhieuThuocVatTu",paramInput,"Danh sách phiếu thuốc, vật tư",1200,635);
		    			   			DlgUtil.open("divDlgDSPTVT"); 
		    					}
		    				}
		    				//tuyennx_add_end yc L2K74TW-224
		    			},
		    		onContextMenu: function (event, menu) {
		    				var rowId = $(event.target).parent("tr").attr("id");
		    				var grid = $("#" + "grdDanhSachDaChiDinh");
		    				GridUtil.unmarkAll("grdDanhSachDaChiDinh");
		    				GridUtil.markRow("grdDanhSachDaChiDinh",rowId);
		    				return true;
		    			}
		    		}); 
				}
	        }
		});
		
		//calback cho man hinh chi dinh dich vu
		EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function(e) {
			DlgUtil.showMsg(e.msg);
			DlgUtil.close("divDlgDichVu");
			loadDanhSachDaChiDinh();	 
		});
		
		//end nghiant 27102017 		
		
		//nghiant 29032018 		
		function setMamay(obj){
			var idx = -1;
			for(var i=0;i<dsMaMay.length;i++){
				//if(dsMaMay[i].NAME.trim() == obj.NAME.trim()){
					//idx = i; break;
				//}
				
				var valuesArray = obj.NAME.trim().split(';');
				valuesArray.forEach(value => {
					if(value.trim() == dsMaMay[i].NAME.trim()){
						idx = i; //break;
					}
					//console.log(value);
				});
			}
			if(idx == -1){
				dsMaMay.push(obj); 
			}
		}
		
		
		function getMamay(name){
			var obj = null;
			for(var i=0;i<dsMaMay.length;i++){
				var valuesArray = name.split(';');

				// Duyệt qua mảng và tạo các chuỗi mới với giá trị thay thế
				valuesArray.forEach(value => {
					if(value.trim() == dsMaMay[i].NAME.trim()){
						obj = dsMaMay[i];
					}
					//console.log(value);
				});
				
			}
			return obj; 
		}
		//nghiant 29032018
		
		$("li[id=tabThongTinBenhNhan]").on("click",function(e){
			if(tab1Click == 0){
				var selRowId = $("#grdBenhPhamDangLam").jqGrid('getGridParam', 'selrow');
				var idbenhnhan = $("#grdBenhPhamDangLam").jqGrid ('getCell', selRowId, 'BENHNHANID');
				loadThongTinBenhNhan(idbenhnhan);
			}
			tab1Click += 1;
		});
		
		$("li[id=tabDanhSachChiDinh]").on("click", function (e) {
			if (tab2Click == 0) {
				loadDanhSachChiDinh();
			}
			tab2Click += 1;
		});
		
		$("li[id=tabDanhSachKetQua]").on("click", function (e) {
			if (tab3Click == 0) {
				loadDanhSachKetQua();
			}
			tab3Click += 1;
		});
		//nghiant 25102017
		$("li[id=tabDanhSachDaChiDinh]").on("click", function (e) {
			if (tab4Click == 0) {
				loadDanhSachDaChiDinh();
			}
			tab4Click += 1;
		});
		//end nghiant 25102017 
		
		//SONDN 
		//======== START SU KIEN CHO BV BACH MAI 2;
		$("#btnGOITIEP5").on("click", function(){
			_goisttbm2("6","1"); 					// call from cominf; 
		});
		
		$("#btnGOILAI5").on("click", function(){
			_goisttbm2("6","2"); 
		});
		
		$("#btnLCDNHO5").on("click", function(){
			var param = "";
			window.open('manager.jsp?func=../ngoaitru/NGT02K053_VPI_LCDBM55&type=6&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		});
		//======== END SU KIEN CHO BV BACH MAI 2;
		
		//======== THEM KTV
		$('#btnCLEARPHUMO1').on("click",function() {
			$("#txtTKPHUMO1").val("");
			var option = $('<option value="-1"></option>');
		    $("#cboPHUMO1").empty();
		    $("#cboPHUMO1").append(option);
		});
		
		//======== END KTV
		
		$('#btnHuyDichVu').on('click', function () {					
			huyDichVu();
			reloadAllGrid();
		});
		
		$('#btnKhoiPhuc').on('click', function () {
			khoiPhucDichVu();
			reloadAllGrid();
		});

		$('#btnBoSung').on('click', function () {
			boSungDichVu();
			reloadAllGrid();
		});			

		$('#btnTiepNhan').on('click', function () {
			var currentPage = $('#grdBenhPhamDangLam').getGridParam('page');
			
			tiepNhanBenhPham();
			reloadAllGrid();
			
			if(searchType == 1) {
				$('#toolbarIdtxtTimKiem').select();
			}
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '50');
	                return;
	            }
	            totalPage = $('#grdBenhPhamDangLam').getGridParam('lastpage');	            
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdBenhPhamDangLam')[0].grid.populatePage(currentPage);		
	            }	            
			};
			callback();
		});

		$('#btnTuChoi').on('click', function () {
			var currentPage = $('#grdBenhPhamDangLam').getGridParam('page');
			
			tuChoiBenhPham();
			reloadAllGrid();
			
			if(searchType == 1) {
				$('#toolbarIdtxtTimKiem').select();
			}
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '50');
	                return;
	            }
	            totalPage = $('#grdBenhPhamDangLam').getGridParam('lastpage');	            
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdBenhPhamDangLam')[0].grid.populatePage(currentPage);		
	            }	            
			};
			callback();
		});
		
		$('#btnHuyTiepNhan').on('click', function () {	
			var currentPage = $('#grdBenhPhamDangLam').getGridParam('page');
			
			huyTiepNhan();
			reloadAllGrid();
			
			if(searchType == 1) {
				$('#toolbarIdtxtTimKiem').select();
			}
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '50');
	                return;
	            }
	            totalPage = $('#grdBenhPhamDangLam').getGridParam('lastpage');	            
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdBenhPhamDangLam')[0].grid.populatePage(currentPage);		
	            }	            
			};
			callback();
		});

		$('#btnLuu').on('click', function () {
			var currentPage = $('#grdBenhPhamDangLam').getGridParam('page');
			
			luuKetQua();
			reloadAllGrid();
			
			if(searchType == 1) {
				$('#toolbarIdtxtTimKiem').select();
			}
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '50');
	                return;
	            }
	            totalPage = $('#grdBenhPhamDangLam').getGridParam('lastpage');	            
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdBenhPhamDangLam')[0].grid.populatePage(currentPage);		
	            }	            
			};
			callback();
		});
		
		$('#btnLuuMau').on('click', function () {
			luuKetQuaMau();
			reloadAllGrid();
		});

		$('#btnHuy').on('click', function () {			
			loadDanhSachKetQua();
			
			if(searchType == 1) {
				$('#toolbarIdtxtTimKiem').select();
			}
		});

		$('#btnHenTraKetQua').on('click', function () {			
			var rowId = $("#grdBenhPhamDangLam").jqGrid('getGridParam', 'selrow');

			if (rowId != null) {
				var hdfID = $("#hdfIDMauBenhPham").val();
				EventUtil.setEvent("CLS02C007_Thoat",function(e){
					DlgUtil.close("dlgHenTraKQ");	
					reloadAllGrid();
				});
				var paramInput={
					maubenhphamid: hdfID
	 			};						
				dlgPopup=DlgUtil.buildPopupUrl("dlgHenTraKQ","divNhapKetQua","manager.jsp?func=../canlamsang/CLS02C007_HenTraKetQua",paramInput,"Hẹn trả kết quả",600,400);
				DlgUtil.open("dlgHenTraKQ");
			}
			else {
				DlgUtil.showMsg("Bạn chưa chọn phiếu cần thực hiện",undefined,1000);
			}
		});

		$('#btnTraKetQua').on('click', function () {
			var currentPage = $('#grdBenhPhamDangLam').getGridParam('page');
			var hdfID = $("#hdfIDMauBenhPham").val();
			var b_luutraketqua = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_LUUTRAKETQUA');
			if(b_luutraketqua=="1"){
				luuKetQua(); traKetQua();
			}
			else if(b_luutraketqua=="2"){
				luuKetQua(); traKetQua();
				inPhieuKetQua();
			}
			else{
				traKetQua();
			}
			reloadAllGrid();
			$("#hdfIDMauBenhPham").val(hdfID);
			if(searchType == 1) {
				$('#toolbarIdtxtTimKiem').select();
			}
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '50');
	                return;
	            }
	            totalPage = $('#grdBenhPhamDangLam').getGridParam('lastpage');
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdBenhPhamDangLam')[0].grid.populatePage(currentPage);		
	            }	            
			};
			callback();
		});

		$('#btnHuyTraKetQua').on('click', function () {
			var currentPage = $('#grdBenhPhamDangLam').getGridParam('page');
			var hdfID = $("#hdfIDMauBenhPham").val();
			huyTraKetQua();
			reloadAllGrid();
			$("#hdfIDMauBenhPham").val(hdfID);
			if(searchType == 1) {
				$('#toolbarIdtxtTimKiem').select();
			}
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '50');
	                return;
	            }
	            totalPage = $('#grdBenhPhamDangLam').getGridParam('lastpage');
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdBenhPhamDangLam')[0].grid.populatePage(currentPage);		
	            }	            
			};
			callback();
		});

		$('#btnHuyKetQua').on('click', function () {
			var currentPage = $('#grdBenhPhamDangLam').getGridParam('page');
			var hdfID = $("#hdfIDMauBenhPham").val();
			huyKetQua();
			reloadAllGrid();
			$("#hdfIDMauBenhPham").val(hdfID);
			if(searchType == 1) {
				$('#toolbarIdtxtTimKiem').select();
			}
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '50');
	                return;
	            }
	            totalPage = $('#grdBenhPhamDangLam').getGridParam('lastpage');
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdBenhPhamDangLam')[0].grid.populatePage(currentPage);		
	            }	            
			};
			callback();
		});
		
		$('#btnInPhieu').on('click', function () {			
			inPhieuKetQua();		
		});
		
		$('#txtTestNumber').keydown(function (e) {
			if (e.which === 13) {
				LayKetQua();
			}
		});
		
		$('#btnLayKQ').on('click', function(){
			LayKetQua();
		});

		// các nút liên quan tới ký số
		$('#btnKySo').on('click', function () {
			_caRpt('1');
		});

		$('#btnHuyKySo').on('click', function () {
			_caRpt('2');
		});
		
		function _caRpt(signType) {			
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", $("#hdfIDMauBenhPham").val());
			var hosobenhanid = data_ar[0].HOSOBENHANID;

			var _report_code = "PhieuXetNghiem";
			var _ca_type = "1";
			var msg = "";
			//L2PT-19087
			var v_dvkbidca = "";
			if($("#grdKetQuaXetNghiem").getGridParam("reccount") > 1 && v_dvkbid != '') {
				v_dvkbidca = v_dvkbid;				
			}
			var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.GET2", $("#hdfIDMauBenhPham").val()+"$"+v_dvkbidca);
			if(i_report_code!=undefined && i_report_code!=null) {
				_report_code = i_report_code[0].REPORT_CODE;
				_ca_type = i_report_code[0].CA_TYPE;						
			}
			var params = [
						{
							name: 'HOSOBENHANID',
							type: 'String',
							value: hosobenhanid
						},
						{
							name: 'MAUBENHPHAMID',
							type: 'String',
							value: $("#hdfIDMauBenhPham").val()
						},
						{
							name: 'ID_MAUBENHPHAM',
							type: 'String',
							value: $("#hdfIDMauBenhPham").val()
						},
						{
							name: 'RPT_CODE',
							type: 'String',
							value: _report_code
						}
					];			
			params.push({
				name: 'DICHVUKHAMBENHID',
				type: 'String',
				value: v_dvkbidca
			});

			_signType = signType;
	        CommonUtil.kyCA(params,signType,true,true);
	        EventUtil.setEvent("eventKyCA",function(e){
	        	reloadAllGrid();
	        	var msgEmr = e.res;
				var msgArr = msgEmr.split("|");
				var msgCode = msgArr[0];
				var msg = msgArr[1];
				if(_signType == "1" && msgCode == "0"){
					var param_arrAll={ 
						MAUBENHPHAMID : $("#hdfIDMauBenhPham").val(),
						DICHVUKHAMBENHID : v_dvkbidca,
						SOPHIEU : $("#hdfSoPhieu").val(),
						REPORTCODE : _report_code,
						LOAIKY : _ca_type,
						TRANGTHAI : _signType,
						HETHONG : "HIS",
						NGUOIKY : user_id
					};
					var param_str = JSON.stringify(param_arrAll);
					var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS05K001.LUU", param_str);

					if (rs == '1') {
						DlgUtil.showMsg(msg+"!!!",undefined,2000);
						
						$("#btnInPhieuKy").attr("disabled", false);					
						$("#btnSuaNgayTN").attr("disabled", true);
						$("#btnEditTimeCDHA").attr("disabled", true);
						$("#btnHuyKetQua").attr("disabled", true);
					}
					else {
						DlgUtil.showMsg(msg+"!",undefined,2000);
						
						$("#btnInPhieuKy").attr("disabled", true);
						$("#btnSuaNgayTN").attr("disabled", false);
						$("#btnEditTimeCDHA").attr("disabled", false);
						$("#btnHuyKetQua").attr("disabled", false);
					}
				}
				else if(_signType == "2" && msgCode == "0"){
					var param_arrAll={ 
						MAUBENHPHAMID : $("#hdfIDMauBenhPham").val(),
						DICHVUKHAMBENHID : v_dvkbidca,
						SOPHIEU : $("#hdfSoPhieu").val(),
						REPORTCODE : _report_code,
						LOAIKY : _ca_type,
						TRANGTHAI : _signType,
						HETHONG : "HIS",
						NGUOIKY : user_id
					};
					var param_str = JSON.stringify(param_arrAll);
					var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS05K001.LUU", param_str);

					if (rs == '1') {
						DlgUtil.showMsg(msg+"!!!",undefined,2000);
						$("#btnHuyTraKetQua").attr("disabled", false); // ChienDV (Khi hủy ký mở nút Gõ trả KQ)
						$("#btnInPhieuKy").attr("disabled", true);					
						$("#btnSuaNgayTN").attr("disabled", true);
						$("#btnEditTimeCDHA").attr("disabled", true);
						$("#btnHuyKetQua").attr("disabled", true);
					}
					else {
						DlgUtil.showMsg(msg+"!",undefined,2000);
						
						$("#btnInPhieuKy").attr("disabled", false);
						$("#btnSuaNgayTN").attr("disabled", false);
						$("#btnEditTimeCDHA").attr("disabled", false);
						$("#btnHuyKetQua").attr("disabled", false);
					}
				}
				else if(_signType == "1" && msgCode == "1"){
					var param_arrAll={ 
						MAUBENHPHAMID : $("#hdfIDMauBenhPham").val(),
						DICHVUKHAMBENHID : v_dvkbidca,
						SOPHIEU : $("#hdfSoPhieu").val(),
						REPORTCODE : _report_code,
						LOAIKY : _ca_type,
						TRANGTHAI : "1",
						HETHONG : "HIS",
						NGUOIKY : user_id
					};
					var param_str = JSON.stringify(param_arrAll);
					var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS05K001.LUU", param_str);

					if (rs == '1') {
						DlgUtil.showMsg(msg+"!!!",undefined,2000);
						
						$("#btnInPhieuKy").attr("disabled", false);						
						$("#btnSuaNgayTN").attr("disabled", true);
						$("#btnEditTimeCDHA").attr("disabled", true);
						$("#btnHuyKetQua").attr("disabled", true);
					}
					else {
						DlgUtil.showMsg(msg+"!",undefined,2000);
						
						$("#btnInPhieuKy").attr("disabled", false);
						$("#btnSuaNgayTN").attr("disabled", false);
						$("#btnEditTimeCDHA").attr("disabled", false);
						$("#btnHuyKetQua").attr("disabled", false);
					}
				}
				else if(_signType == "2" && msgCode == "1"){
					var param_arrAll={ 
						MAUBENHPHAMID : $("#hdfIDMauBenhPham").val(),
						DICHVUKHAMBENHID : v_dvkbidca,
						SOPHIEU : $("#hdfSoPhieu").val(),
						REPORTCODE : _report_code,
						LOAIKY : _ca_type,
						TRANGTHAI : "2",
						HETHONG : "HIS",
						NGUOIKY : user_id
					};
					var param_str = JSON.stringify(param_arrAll);
					var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS05K001.LUU", param_str);

					if (rs == '1') {
						DlgUtil.showMsg(msg+"!!!",undefined,2000);
						
						$("#btnInPhieuKy").attr("disabled", true);
						$("#btnSuaNgayTN").attr("disabled", true);
						$("#btnEditTimeCDHA").attr("disabled", true);
						$("#btnHuyKetQua").attr("disabled", true);
					}
					else {
						DlgUtil.showMsg(msg+"!",undefined,2000);
						
						$("#btnInPhieuKy").attr("disabled", false);
						$("#btnSuaNgayTN").attr("disabled", false);
						$("#btnEditTimeCDHA").attr("disabled", false);
						$("#btnHuyKetQua").attr("disabled", false);
					}
				}
				else {
					DlgUtil.showMsg(msg,undefined,5000);
				}
			});
		}

		$('#btnInPhieuKy').on('click', function () {
			if ($("#hdfIDMauBenhPham").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu để in ký");
				return;
			}
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", $("#hdfIDMauBenhPham").val());
			var hosobenhanid = data_ar[0].HOSOBENHANID;
			
			var _report_code = "PhieuXetNghiem";
			var _ca_type = "1";
			//L2PT-19087
			var v_dvkbidca = "";
			if($("#grdKetQuaXetNghiem").getGridParam("reccount") > 1 && v_dvkbid != '') {
				v_dvkbidca = v_dvkbid;				
			}
			
			var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.GET2", $("#hdfIDMauBenhPham").val()+"$"+v_dvkbidca);
			if(i_report_code!=undefined && i_report_code!=null) {
				_report_code = i_report_code[0].REPORT_CODE;
				_ca_type = i_report_code[0].CA_TYPE;						
			}			
			var params = [
						{
							name: 'HOSOBENHANID',
							type: 'String',
							value: hosobenhanid
						},
						{
							name: 'MAUBENHPHAMID',
							type: 'String',
							value: $("#hdfIDMauBenhPham").val()
						},
						{
							name: 'ID_MAUBENHPHAM',
							type: 'String',
							value: $("#hdfIDMauBenhPham").val()
						},
						{
							name: 'RPT_CODE',
							type: 'String',
							value: _report_code
						}
					];
			
			params.push({
				name: 'DICHVUKHAMBENHID',
				type: 'String',
				value: v_dvkbidca
			});
			
			//var selRowId = $('#grdKetQuaXetNghiem').jqGrid ('getGridParam', 'selrow');
            //var rowData= $('#grdKetQuaXetNghiem').jqGrid('getRowData', selRowId);
			
			var i_kyso = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.GET202", $("#hdfIDMauBenhPham").val()+"$"+v_dvkbidca);
			
			if(i_kyso!=undefined && i_kyso!=null) {
				var _hethong = i_kyso[0].HETHONG; 				
			}	
			
			var _hethong = i_kyso[0].HETHONG; 
			if (choPhepLayThamSoLis=="1" && _hethong=="LIS" ){
				var params_lis = [
						{
							name: 'HOSOBENHANID',
							type: 'String',
							value: hosobenhanid
						},
						{
							name: 'MAUBENHPHAMID',
							type: 'String',
							value: $("#hdfIDMauBenhPham").val()
						},
						{
							name: 'ID_MAUBENHPHAM',
							type: 'String',
							value: $("#hdfIDMauBenhPham").val()
						},
						{
							name: 'RPT_CODE',
							type: 'String',
							value: i_kyso[0].REPORTCODE
						},
						{
							name: 'DICHVUKHAMBENHID',
							type: 'String',
							value: i_kyso[0].DICHVUKHAMBENHIDS
						}
					];
					
				CommonUtil.openReportGetCA3(i_kyso[0].PARAM_HASHED, false);				
			}
			else
			{		
			
				if (KYSO_CLS_CHECK_KYCA_BYPARAM == "1") {
					if (CommonUtil.checkKyCaByParam(params, '', '') > 0) {
						var paramHashed = CryptoJS.MD5(JSON.stringify(params).toUpperCase()).toString().toUpperCase();
						CommonUtil.openReportGetCA2(params, false);
						//CommonUtil.openReportGetCA3(paramHashed, false);
					}
				} else
					CommonUtil.openReportGetCA2(params, false);
			}
			//var data_aa = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.LK", $("#hdfIDMauBenhPham").val());
			//if(data_aa.length>0){
			//	for(var i=0;i<data_aa.length;i++){
			//		CommonUtil.openReportGetCA3(data_aa[i]["PARAM_HASHED"], false);	
			//	}				
			//} else {
			//	DlgUtil.showMsg("Không tìm được thông tin ký số");
			//}			
		});
		// hết phần ký số
		
	}
	
	function reloadAllGrid(){
		if(searchType == 0) {
			loadDanhSachBenhPham($("#cboPhongThucHien").val(), $("#txtTuNgay").val(),$("#txtDenNgay").val(), $("input:radio[name='rdoTrangThai']:checked").val(), userRights);
		} 
		else if(searchType == 1) {
			timKiemBangMayQuet($("#cboPhongThucHien").val(),$("#txtTuNgay").val(),$("#txtDenNgay").val(),$("input:radio[name='rdoTrangThai']:checked").val(),$('#toolbarIdtxtTimKiem').val());
		}
		loadDanhSachChiDinh();
		loadDanhSachKetQua();
	}

	function validateDate(ctrId, key, checkNull){
		var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4}$/;
		
		if(checkNull && ($('#' + ctrId).val() == null || $('#' + ctrId).val() == '')){
			DlgUtil.showMsg(key + $.i18n("require"));	
			$('#' + ctrId).focus();
			return false;
		}
		
		if($('#' + ctrId).val().trim().length > 0 && (!datetimeRegex.test($('#' + ctrId).val()) || !checkDate($('#' + ctrId).val()))){
			DlgUtil.showMsg(key + $.i18n("date_type_invalid"));	
			$('#' + ctrId).focus();
			return false;		
		}
		return true;
	}
	
	function kiemTra(){
		if(validateDate("txtTuNgay","Từ ngày ", false) && validateDate("txtDenNgay", "Đến ngày ", false)){
			var txtTuNgay = $("#txtTuNgay").val();
			var txtDenNgay = $("#txtDenNgay").val();
			
			if(!compareDate(txtTuNgay,txtDenNgay,"DD/MM/YYYY")){
				DlgUtil.showMsg("Từ ngày không được lớn hơn Đến ngày");	
				$("#txtTuNgay").focus();
				return false;
			}
		}		
		return true;
	}
	
	//nghiant 24102017 
	function kiemTraNgayCD(){
		if(validateDate("txtTuNgayCD","Từ ngày ", false) && validateDate("txtDenNgayCD", "Đến ngày ", false)){
			var txtTuNgay = $("#txtTuNgayCD").val();
			var txtDenNgay = $("#txtDenNgayCD").val();
			
			if(!compareDate(txtTuNgay,txtDenNgay,"DD/MM/YYYY")){
				DlgUtil.showMsg("Từ ngày không được lớn hơn Đến ngày");	
				$("#txtTuNgayCD").focus();
				return false;
			}
		}		
		return true;
	}
	//end nghiant 24102017 
	
	// load danh sách bệnh phẩm
	function loadDanhSachBenhPham(phong, tungay, denngay, trangthai, quyen){
		if(kiemTra()){
			var param = RSUtil.buildParam("", [uuid, schema, province_id, hospital_id, phong, tungay, denngay, trangthai, quyen]);
			if(trangthai=="3"){
				GridUtil.loadGridBySqlPage("grdBenhPhamDangLam", "CLS01X002.DSBP3", param);
			} else {
				GridUtil.loadGridBySqlPage("grdBenhPhamDangLam", "CLS01X002.DSBP", param);
			}
		}
	}
	
	// tìm kiếm bằng máy quét barcode
	function timKiemBangMayQuet(phong, tungay, denngay, trangthai, barcode, type){
		if(kiemTra()){
			var param = RSUtil.buildParam("", [uuid, schema, province_id, hospital_id, phong, tungay, denngay, trangthai, barcode]);
			
			GridUtil.loadGridBySqlPage("grdBenhPhamDangLam","CLS01X002.SIDS",param);
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '50');
	                return;
	            }		            
				var row = $("#grdBenhPhamDangLam").jqGrid('getRowData', 1);
				
				if(row["TRANGTHAIMAUBENHPHAM"]=="3") {
					$('#rdoTraKetQua').prop('checked', true);
				}
				else {
					$('#rdoChoTiepNhan').prop('checked', true);
				}
				
				if(row["PHONGCHUYENDENID"] != undefined && row["PHONGCHUYENDENID"] != null && type == '1') {
					var sql_par = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+opt.dept_id;
					ComboUtil.getComboTag("cboPhongThucHien", "CLS01X002.KP", sql_par, row["PHONGCHUYENDENID"], "", "sp",'',false);
				}

				if($("#grdBenhPhamDangLam").getGridParam("reccount") > 0) { 
					$("#txtTuNgay").val(row["NGAYDICHVU"].slice(0,10));
				}
				
				$("#grdBenhPhamDangLam").setSelection(1, true);
				
	        };
	        callback();	
		}
	}

	// load tab Thông tin hành chính của bệnh nhân
	function loadThongTinBenhNhan(bid){
		$('#ttHanhChinh').patientInfo({
			benhnhanId: bid
		});
		
		var param = [$("#hdfIDMauBenhPham").val()];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));

		if(data_ar.length>0){
			var row = data_ar[0];
			$("#txtChanDoan").val(row.CHANDOAN);
		}
	}
	
	// load tab Danh sách chỉ định
	function loadDanhSachChiDinh(){
		var param = uuid+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
					$("#hdfIDMauBenhPham").val();

		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSCD", param);
		
		$("#grdDanhSachChiDinh").jqGrid("clearGridData");
		$("#grdDanhSachChiDinh")[0].grid.beginReq();
		$("#grdDanhSachChiDinh").jqGrid("setGridParam", { data: data_ar });
		$("#grdDanhSachChiDinh")[0].grid.endReq();
		$("#grdDanhSachChiDinh").trigger("reloadGrid");		
	}
	
	//nghiant 20102017
	// load tab Danh sách đã chỉ định
	function loadDanhSachDaChiDinh(){
		var typeCLS ="1";//1: Xet nghiem, 2: CDHA 
		if(kiemTraNgayCD()){
			if(showDSDCD=="1"){
				var param = uuid+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
				benhnhan_id+'$'+$("#txtTuNgayCD").val()+'$'+$("#txtDenNgayCD").val()+'$'+typeCLS;//dept_id
		
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSDCD2", param);
				
				$("#grdDanhSachDaChiDinh").jqGrid("clearGridData");
				$("#grdDanhSachDaChiDinh")[0].grid.beginReq();
				$("#grdDanhSachDaChiDinh").jqGrid("setGridParam", { data: data_ar });
				$("#grdDanhSachDaChiDinh")[0].grid.endReq();
				$("#grdDanhSachDaChiDinh").trigger("reloadGrid");
			}
			else if(showDSDCD=="2"){
				var param = uuid+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
				benhnhan_id+'$'+$("#txtTuNgayCD").val()+'$'+$("#txtDenNgayCD").val()+'$'+typeCLS+'$'+opt.subdept_id;
		
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSDCD3", param);
				
				$("#grdDanhSachDaChiDinh").jqGrid("clearGridData");
				$("#grdDanhSachDaChiDinh")[0].grid.beginReq();
				$("#grdDanhSachDaChiDinh").jqGrid("setGridParam", { data: data_ar });
				$("#grdDanhSachDaChiDinh")[0].grid.endReq();
				$("#grdDanhSachDaChiDinh").trigger("reloadGrid");
			}
		}
	}
	//end nghiant 20102017
	
	// load tab danh sách kết quả
	function loadDanhSachKetQua(){
		_sql_par=RSUtil.buildParam("",[uuid,schema,province_id,hospital_id,$("#hdfIDMauBenhPham").val()]);
		GridUtil.loadGridBySqlPage("grdKetQuaXetNghiem","CLS01X002.DSKQ2",_sql_par);
	}
	
	// gửi yêu cầu tới hệ thống LIS
	function SendRequestToLab(rowId){
        if(LIS_CONNECTION_TYPE == "1" && LIS_SERVICE_DOMAIN_NAME != ""){
    		var request_url = LIS_SERVICE_DOMAIN_NAME + LIS_SEND_REQUEST;
    		console.log("request_url="+request_url);
    		
    		var row = $("#grdBenhPhamDangLam").jqGrid('getRowData', rowId);
    		var so_phieu = row.SOPHIEU;
    		var barcode = row.BARCODE;
    		
    		var request = new LabRequestSet();
    		if(LIS_PROVIDER){
    			request = new LabRequestTGG();
    		}
    		if(LIS_AUTHENTICATION_GATE){
	    		request = createLabRequest(row.MAUBENHPHAMID);
    		}
    		else {
	    		request.SID = so_phieu;
	    		request.Barcode = barcode;
	    		var param = [$("#hdfIDMauBenhPham").val()];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
	    		for(var i=0; i<data_ar.length; i++){
	    			request.TestCodeList.push(data_ar[i]["MADICHVU"]);	
	    		}
	        }
    		console.log("request="+JSON.stringify(request));            
    		
	        $.ajax({
		        type: "POST",
		        crossDomain: true,
		        contentType: "application/json; charset=utf-8",
		        headers: {
		        	'Username': LIS_USERNAME,
		        	'Identify-Code': so_phieu,
		        	'Lis-Access-Hash': getHash(so_phieu),
		        	'Token': getLabToken(),
		        	// vietda 30/11/2018 thêm tham số kết nối LIS TGG
		        	'idToken': getLabidToken(),
		        	'password': getLabSecretKey(),
		        	'maTinh': getLabProvinceCode(),
		        	'maCSKCB': getLabHospitalCode()
		        },
		        data: JSON.stringify(request),
		        url: request_url,
		        success: function(data) {		        	
		        	console.log("response="+JSON.stringify(data));
		        },
		        error: function(xhr){
		        	console.log("send request fail: " + JSON.stringify(xhr));
		        	if(xhr.status==400){
		        		refreshLabToken();
		        	}
		        }
		    });
        }        
	}

	// lấy kết quả từ hệ thống LIS
	function GetResultFromLab(rowid){
		if(LIS_CONNECTION_TYPE == "1" && LIS_SERVICE_DOMAIN_NAME != ""){
			var request_url = LIS_SERVICE_DOMAIN_NAME + LIS_GET_RESULT_SET;
	        console.log("request_url="+request_url);
	        var row = $("#grdBenhPhamDangLam").jqGrid('getRowData', rowid);
	        var so_phieu = row.SOPHIEU;
	        
	        var request = new LabRequestSet();
	        if(LIS_PROVIDER){
    			request = new LabRequestTGG();
    		}
    		if(LIS_AUTHENTICATION_GATE){
	    		request = createLabRequest(row.MAUBENHPHAMID);
    		}
    		
	        $.ajax({
		        type: "POST",
		        contentType: "application/json; charset=utf-8",
		        headers: {
		        	'Username': LIS_USERNAME,
		        	'Identify-Code': so_phieu,
		        	'Lis-Access-Hash': getHash(so_phieu),
		        	'Type': '0',
		        	'SID': so_phieu,
			    	'Token': getLabToken(),
			    	// vietda 30/11/2018 thêm tham số kết nối LIS TGG		        	
		        	'idToken': getLabidToken(),
		        	'password': getLabSecretKey(),
		        	'maTinh': getLabProvinceCode(),
		        	'maCSKCB': getLabHospitalCode()
		        },
		        data: JSON.stringify(request),
		        url: request_url,
		        success: function(res){
		        	if(res) if(res.error_code != 0) {
		        		if(res.error_code == 3 || res.error_code == 4 || res.error_code == 5) 
		        			refreshLabToken();
		        		console.log(JSON.stringify(res));
		        	}
		        	var data = (res == null) ? null : (res.result == null) ? res : res.result;		        	
		        	nhanKetQua(data);
		        	var dataLength = data ? (LIS_PROVIDER ? (data.ResultDetail?data.ResultDetail.length:0): data.ResultList.length) : 0;
		        	var param = [$("#hdfIDMauBenhPham").val()];
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
		        	if(dataLength < data_ar.length){
		        		SendRequestToLab(rowid);
		        	}
		        },
		        error: function(xhr){
		        	console.log("load result fail: " + JSON.stringify(xhr));
		        	SendRequestToLab(rowid);
		        }
		    });
        }
	}

	// xóa yêu cầu đã gửi tới LIS
	function DeleteRequestOnLab(rowId){
	    if(LIS_CONNECTION_TYPE == "1" && LIS_SERVICE_DOMAIN_NAME != ""){
		    var request_url = LIS_SERVICE_DOMAIN_NAME + LIS_DELETE_REQUEST;
		    console.log("request_url="+request_url);

			var row = $("#grdBenhPhamDangLam").jqGrid('getRowData', rowId);
			var so_phieu = row.SOPHIEU;
			
			var request = new LabRequestSet();
			if(LIS_PROVIDER){
    			request = new LabRequestTGG();
    		}
			if(LIS_AUTHENTICATION_GATE){
	    		request = createLabRequest(row.MAUBENHPHAMID);
    		}
    		else {
				request.SID = so_phieu;		
				var param = [$("#hdfIDMauBenhPham").val()];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));		
				for(var i=0; i<data_ar.length; i++){
					request.TestCodeList.push(data_ar[i]["MADICHVU"]);	
				}
    		}
			console.log("request="+JSON.stringify(request));
					    
	        $.ajax({
		        type: "POST",
		        contentType: "application/json; charset=utf-8",
		        headers: {
		        	'Username': LIS_USERNAME,
		        	'Identify-Code': so_phieu,
		        	'Lis-Access-Hash': getHash(so_phieu),
			    	'Token': getLabToken(),
			    	// vietda 30/11/2018 thêm tham số kết nối LIS TGG		        	
		        	'idToken': getLabidToken(),
		        	'password': getLabSecretKey(),
		        	'maTinh': getLabProvinceCode(),
		        	'maCSKCB': getLabHospitalCode()
		        },
		        data: JSON.stringify(request),
		        url: request_url,
		        success: function(data) {
		        	console.log("response="+JSON.stringify(data));
		        },
		        error: function(xhr){
		        	console.log("delete request fail: " + JSON.stringify(xhr));
		        }
		    });
	    }
	}
    
	// cập nhật trạng thái đã trả kết quả sang hệ thống LIS
	function UpdateStatusToLab(rowid){
		if(LIS_CONNECTION_TYPE == "1" && LIS_SERVICE_DOMAIN_NAME != ""){
			var request_url = LIS_SERVICE_DOMAIN_NAME + LIS_UPDATE_STATUS;
			console.log("request_url="+request_url);
			
			var row = $("#grdBenhPhamDangLam").jqGrid('getRowData', rowid);
			var so_phieu = row.SOPHIEU;

			var request = new LabRequestSet();
			if(LIS_PROVIDER){
    			request = new LabRequestTGG();
    		}
			if(LIS_AUTHENTICATION_GATE){
	    		request = createLabRequest(row.MAUBENHPHAMID);
    		}
    		else {
				request.SID = so_phieu;		
				var param = [$("#hdfIDMauBenhPham").val()];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
				for(var i=0; i<data_ar.length; i++){
					request.TestCodeList.push(data_ar[i]["MADICHVU"]);	
				}
    		}
			console.log(JSON.stringify(request));
							
	        $.ajax({
		        type: "POST",
		        contentType: "application/json; charset=utf-8",
		        headers: {
		        	'Username': LIS_USERNAME,
		        	'Identify-Code': so_phieu,
		        	'Lis-Access-Hash': getHash(so_phieu),
			    	'Token': getLabToken(),
			    	// vietda 30/11/2018 thêm tham số kết nối LIS TGG		        	
		        	'idToken': getLabidToken(),
		        	'password': getLabSecretKey(),
		        	'maTinh': getLabProvinceCode(),
		        	'maCSKCB': getLabHospitalCode()
		        },
		        data: JSON.stringify(request),
		        url: request_url,
		        success: function(data) {		        	
		        	console.log("update status success: "+JSON.stringify(data));
		        },
		        error: function(xhr){
		        	console.log("update status fail: " + JSON.stringify(xhr));
		        }
		    });
        }
	}
	
	// lấy kết quả chủ động bằng barcode
	function LayKetQua(){
        var rowId =$("#grdBenhPhamDangLam").jqGrid('getGridParam','selrow'); 
        if(rowId == null){
			DlgUtil.showMsg("Chưa chọn phiếu cần nhận kết quả",undefined,3000);
		}
		else {
			if(LIS_CONNECTION_TYPE == "1" || LIS_CONNECTION_TYPE == "2" && LIS_SERVICE_DOMAIN_NAME != ""){
		        var row = $("#grdBenhPhamDangLam").jqGrid('getRowData', rowId);
		        
	    		var request_url = LIS_SERVICE_DOMAIN_NAME + LIS_GET_RESULT_SET;
	            console.log("request_url="+request_url);
	            
	            var test_number = $("#txtTestNumber").val();
	            
		        $.ajax({
			        type: "POST",
			        contentType: "application/json; charset=utf-8",
			        headers: {
			        	'Username': LIS_USERNAME,
			        	'Identify-Code': test_number,
			        	'Lis-Access-Hash': getHash(test_number),
			        	'Type': "1",
			        	'SID': test_number,
				    	'Token': getLabToken(),
				    	// vietda 30/11/2018 thêm tham số kết nối LIS TGG		        	
			        	'idToken': getLabidToken(),
			        	'password': getLabSecretKey(),
			        	'maTinh': getLabProvinceCode(),
			        	'maCSKCB': getLabHospitalCode()
			        },
			        data: "",
			        url: request_url,
			        success: function(res){
			        	if(res) if(res.error_code != 0) {
			        		if(res.error_code == 3 || res.error_code == 4 || res.error_code == 5) 
			        			refreshLabToken();
			        		console.log(JSON.stringify(res));
			        	}
			        	var data = (res == null) ? null : (res.result == null) ? res : res.result;			        	
			        	if(data != null) {
			        		data.SID = row.SOPHIEU;
				        	nhanKetQua(data);				        	
				        	loadDanhSachKetQua();
			        	}
			        	else {
			        		DlgUtil.showMsg("Chưa có kết quả",undefined,2000);
			        	}
			        },
			        error: function(xhr){
			        	console.log("load result fail: " + JSON.stringify(xhr));
			        }
		        });
	        } 
			else {
	        	DlgUtil.showMsg("Chưa được cấu hình kết nối hệ thống LIS",undefined,3000);
	        }
		}
	}
	
	// tiếp nhận bệnh phẩm
	function tiepNhanBenhPham(){
		var rowId =$("#grdBenhPhamDangLam").jqGrid('getGridParam','selrow'); 
		if(rowId == null){
			DlgUtil.showMsg("Chưa chọn bệnh phẩm",undefined,2000);
		}
		else {
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.TNBP", param);
			
			if(rs == '1'){
				
				if(showForm=="1"){
					$("#hdfIDKetQuaCLS").val($('#grdKetQuaXetNghiem').jqGrid('getRowData', 1)["KETQUACLSID"]);
					var url = "manager.jsp?func=../canlamsang/CLS02C003_TraKetQuaCDHA&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&idketquacls="+$("#hdfIDKetQuaCLS").val()+"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update"
								+"&rolePTH="+rolePTH+"&resizingTable="+resizingTable;

					EventUtil.setEvent("CLS02C003_LUU", function (e) {
						reloadAllGrid();
					});
					
					EventUtil.setEvent("CLS02C003_LUU_DONG", function (e) {
				    	DlgUtil.showMsg('Lưu kết quả thành công',undefined,500);
						reloadAllGrid();
						DlgUtil.close("dlgSuaKetQua");
					});
					
					EventUtil.setEvent("CLS02C003_HUY", function (e) {
						DlgUtil.close("dlgSuaKetQua");
					});
					
					var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaKetQua", "divSuaKetQua", url, {}, "Kết quả chẩn đoán", screen_width, screen_height);
					dlgPopup.open("dlgSuaKetQua");
				}
				else if(showForm=="2"){
					$("#hdfIDKetQuaCLS").val($('#grdKetQuaXetNghiem').jqGrid('getRowData', 1)["KETQUACLSID"]);
					var url = "manager.jsp?func=../canlamsang/CLS02C003_TraKetQuaCDHA2&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&idketquacls="+$("#hdfIDKetQuaCLS").val()+"&type=update"
								+"&rolePTH="+rolePTH+"&resizingTable="+resizingTable;

					EventUtil.setEvent("CLS02C003_LUU", function (e) {
						reloadAllGrid();
					});
					
					EventUtil.setEvent("CLS02C003_LUU_DONG", function (e) {
				    	DlgUtil.showMsg('Lưu kết quả thành công',undefined,500);
						reloadAllGrid();
						DlgUtil.close("dlgSuaKetQua");
					});
					
					EventUtil.setEvent("CLS02C003_HUY", function (e) {
						DlgUtil.close("dlgSuaKetQua");
					});
					
					var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaKetQua", "divSuaKetQua", url, {}, "Kết quả chẩn đoán", screen_width, screen_height);
					dlgPopup.open("dlgSuaKetQua");
				}
				else {
					DlgUtil.showMsg("Tiếp nhận bệnh phẩm thành công",undefined,1000);
				}
				
				//DlgUtil.showMsg("Tiếp nhận bệnh phẩm thành công",undefined,1000);
				//nghiant 02052018 
				var par = [ 
					{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
				if(i_hid=='951' || i_hid =='938'){
					openReport('window', 'CLS_BARCODE_A8_951', 'pdf', par);
				}				
				//end nghiant 02052018 
			}
			else if(rs== '2'){
				var url = "manager.jsp?func=../canlamsang/CLS001X113_DieuKienNuoiCay&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+
					"&iddichvukb="+$("#hdfIDDichVuKB").val()+"&type=update&q="+userRights+
					"&showNutSuaNgayTraKQ="+showNutSuaNgayTraKQ+"&show=false"+"&benhnhan_id="+benhnhan_id+
					"&rolePTH="+rolePTH+"&dichvuid="+dichvuid;				
				
				titleUrl="Điều kiện cho dịch vụ nuôi cấy";
				EventUtil.setEvent("CLS001X113_LUU",function(e){
					$("#hdfIDMauBenhPham").val();
					reloadAllGrid();
				});			
				EventUtil.setEvent("CLS001X113_HUY",function(e){
					DlgUtil.close("dlgNhapDKNC");
					reloadAllGrid();
				});
				var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapDKNC","dlgNhapDKNC",url,{},titleUrl,1080,600);
				dlgPopup.open("dlgNhapDKNC");
			}
			else if(rs == '3'){
				DlgUtil.showMsg("Đã đóng bệnh án, không cho phép tiếp nhận",undefined,3000);
			}
			else if(rs== '4'){
				DlgUtil.showMsg("Thời gian tiếp nhận phải sau thời gian chỉ định",undefined,3000);
			} 
			else if(rs == "5"){
				DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!",undefined,3000);
			}
			else if(rs == "8"){
				DlgUtil.showMsg("Bệnh nhân không đủ điều kiện để tiếp nhận:<br> BN chưa đóng tiền? <br> BN chưa đóng tạm ứng?",undefined,3000);
			}
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, không thể tiếp nhận",undefined,3000);
			}
		}
	}

	// hủy tiếp nhận bệnh phẩm
	function huyTiepNhan(){
		var rowId =$("#grdBenhPhamDangLam").jqGrid('getGridParam','selrow'); 
		
		if(rowId==null){
			DlgUtil.showMsg("Chưa chọn bệnh phẩm",undefined,2000);
		} 
		else {
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.HTNBP", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Hủy tiếp nhận bệnh phẩm thành công",undefined,1000);	
			}
			else if(rs == '2'){
				DlgUtil.showMsg("Không phải là người tiếp nhận, không thể hủy tiếp nhận!",undefined,3000);	
			} 
			else if(rs == '3'){
				DlgUtil.showMsg("Phần mềm LIS đã tiếp nhận, hãy sử dụng phần mềm LIS để hủy tiếp nhận",undefined,3000);	
			} 
			else if(rs == '4'){
				DlgUtil.showMsg("Phiếu đã từng được nhập kết quả không cho phép hủy tiếp nhận",undefined,3000);	
			} 
			else if(rs == '5'){
				DlgUtil.showMsg("Phiếu đã từng được trả kết quả không cho phép hủy tiếp nhận",undefined,3000);	
			}
			else if(rs == '6'){
				DlgUtil.showMsg("Phiếu đã tiếp nhận bên LIS không cho phép hủy tiếp nhận trên HIS",undefined,3000);	
			}			
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,3000);	
			} 
		}
	}
	
	// từ chối bệnh phẩm
	function tuChoiBenhPham(){
		var rowId =$("#grdBenhPhamDangLam").jqGrid('getGridParam','selrow'); 
		
		if(rowId == null){
			DlgUtil.showMsg("Chưa chọn bệnh phẩm",undefined,2000);
		} 
		else {
			DeleteRequestOnLab(rowId);
			
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.TCBP", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Đã từ chối tiếp nhận mẫu bệnh phẩm",undefined,1000);
			} else if(rs == '2'){
				DlgUtil.showMsg("Dịch vụ đã thu tiền, không cho phép từ chối",undefined,1000);
			} else {
				DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,3000);
			}
		}
	}
	
	// trả kết quả
	function traKetQua(){
		var rowId =$("#grdBenhPhamDangLam").jqGrid('getGridParam','selrow'); 
		
		if(rowId==null){
			DlgUtil.showMsg("Chưa chọn bệnh phẩm",undefined,2000);
		}
		else {
			UpdateStatusToLab(rowId);
	        
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val()
						;
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS01X002.TKQ", param);
			
			if(rs == "1"){
				DlgUtil.showMsg("Trả kết quả thành công",undefined,1000);
				// bổ sung gửi tin nhắn khi trả kết quả
				if(onOffSendSMS=="2"){
					var content = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_SMS_TRA_KQ_XN');
					if(content== null || content == undefined || content =="0" || content == ""){
						content=="Benh nhan TENBENHNHANXXX da duoc tra ket qua xet nghiem cho phieu SOPHIEUXXX";
					}
					str = content;
					str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g,"a"); 
				    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g,"e"); 
				    str = str.replace(/ì|í|ị|ỉ|ĩ/g,"i"); 
				    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g,"o"); 
				    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g,"u"); 
				    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g,"y"); 
				    str = str.replace(/đ/g,"d");
				    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
				    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
				    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
				    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
				    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
				    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
				    str = str.replace(/Đ/g, "D");
				    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, "");
				    str = str.replace(/\u02C6|\u0306|\u031B/g, "");
				    str = str.replace(/ + /g," ");
				    str = str.trim();
				    str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g," ");
				    content = str;
				    
					var _khambenhid = 0;
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C017.TTBN", $("#hdfIDMauBenhPham").val());
					if(data_ar!=null && data_ar.length>0){
						var row = data_ar[0];
						sdtBN = row["SDTBENHNHAN"];
						if(sdtBN.startsWith("0")){		
							sdtBN = "84"+sdtBN.slice(1, sdtBN.length);
						}
						var tenbn_kodau = row["TENBN_KODAU"];
						_khambenhid = row["KHAMBENHID"];
						
						content = content.replace("TENBENHNHANXXX",tenbn_kodau).replace("SOPHIEUXXX",sophieuBN);
					}
					
					var dateto = new Date();
					dateto.setMinutes((new Date()).getMinutes() + 2);
					var ndate = dateto.getDate()<10?"0"+dateto.getDate():dateto.getDate();
					var nmonth = dateto.getMonth()<9?"0"+(dateto.getMonth()+1):(dateto.getMonth()+1);
					var nyear = dateto.getFullYear();
					var nhour = dateto.getHours()<10?"0"+dateto.getHours():dateto.getHours();
					var nminute = dateto.getMinutes()<10?"0"+dateto.getMinutes():dateto.getMinutes();
					var nsecond = dateto.getSeconds()<10?"0"+dateto.getSeconds():dateto.getSeconds();
					var timeToLove = ndate+"/"+nmonth+"/"+nyear+" "+nhour+":"+nminute;
					
					try{
						var sms = new SmsSend();
						var kq = sms.sendPost(sdtBN, content, timeToLove);
						
						var objData = new Object();
						objData['req_id'] = 3;
						objData['resp_code'] =  kq.RESP_CODE;
						objData['sdtbenhnhan'] =  sdtBN;
						objData['schedule_sms'] =  timeToLove;
						objData['loai'] = 3;
						objData['noidung_sms'] = content;
						objData['kq_send_sms'] = kq;			
						objData['khambenhid'] = _khambenhid;
						var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("ADD_NHAC_SMS",JSON.stringify(objData));
						
						if(kq.RESP_CODE == 0){
							console.log("Gửi tin nhắn thành công");
						} else {
							console.log("Gửi tin nhắn thất bại, mã lỗi:  "+ kq.RESP_CODE);
						}
					}
					catch(err){
						console.log(err.message);
					}
				}
				// hết bổ sung gửi tin nhắn khi trả kết quả
				_mbpID = $("#hdfIDMauBenhPham").val();
				$("#hdfIDMauBenhPham").val(""); // làm rỗng grid kết quả khi phiếu đã ko còn trên grid dsbn	
				
				//start L2PT-21923
				if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_NOTIFY_APP_VNCARE') == 1){						
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS.VNCARE.NOTIFY", $("#hdfSoPhieu").val());
					if (data_ar != null && data_ar.length > 0) {
						var _objThongtin = new Object();
						_objThongtin["maCSYT"] = data_ar[0].HOSPITAL_CODE;
						_objThongtin["tenCSYT"] = data_ar[0].ORG_NAME;
						_objThongtin["maBN"] = data_ar[0].MABENHNHAN;
						_objThongtin["tenBN"] = data_ar[0].TENBENHNHAN;
						_objThongtin["phone"] = data_ar[0].TK_LIENKET;
						_objThongtin["soPhong"] = data_ar[0].MAPHONG;
						_objThongtin["tenPhong"] = data_ar[0].TENPHONG;
						_objThongtin["loaiDichVu"] = "1";
						_objThongtin["mauBenhPhamID"] = data_ar[0].MAUBENHPHAMID;
						_objThongtin["soPhieu"] = data_ar[0].SOPHIEU;					
						_objThongtin["tenBS"] = data_ar[0].FULL_NAME;					
						_objThongtin["maLuotKham"] = data_ar[0].MAUBENHPHAMID;
						_objThongtin["tenPhongNhanKQ"] = data_ar[0].PHONGTHUCHIEN;					      
						
						var ret = sendNotify(3, JSON.stringify(_objThongtin));
						var kq = ""; 
						if (ret == null){						
							kq = "Không lấy được thông tin token xác thực"; 
							console.log(kq);
						}
						if (ret.hasOwnProperty("errorCode") && ret.hasOwnProperty("errorMessage")){
							if (ret.errorCode == "0"){
								kq = "Thành công"; 
								console.log(kq);
							}else if (ret.errorCode == "1"){
								kq = "Lỗi: " + ret.errorMessage; 
								console.log(kq);
							}else if (ret.errorCode == "2"){
								kq = "Lỗi không xác định"; 
								console.log(kq);
							}
						}else{
							kq = "Lỗi: Thiếu thông tin QLHK trả về"; 
							console.log(kq);
						}
					}
				}
				//end L2PT-21923																				
			}
			else if(rs.includes("@tsms")){
				var tsms = rs.split("@");
				var csms = tsms[1];
				var consms = csms.split("|");
				var _khambenhid = consms[1];
				var _hotenbn = consms[2];
				var _sdtbn = consms[3];
				var _mahsba = consms[4];
				var _portalxn = consms[5];
				var _timesend = consms[6];
				var _passbn = consms[7];
				
				var CH_SEND_SMS_KQXN_BA = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CH_SEND_SMS_KQXN_BA');
				if(CH_SEND_SMS_KQXN_BA=="1") {
				_portalxn = _portalxn +   "?maba=" + _mahsba + "&mk=" + _passbn;
				}
				var sms = new SmsSend();
				var kq = "";
				var PARRAM_SMS_KQXN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'PARRAM_SMS_KQXN');
				if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'PARRAM_SMS_KQXN') != '0'){
					var parram = PARRAM_SMS_KQXN.split(",");
					_hotenbn = parram[0]== "1"?_hotenbn:"";
					_portalxn = parram[1]== "1"?_portalxn:"";
					_mahsba = parram[2]== "1"?_mahsba:"";
					_passbn = parram[3]== "1"?_passbn:"";				
				}
				kq = sms.sendPostTemplate(_sdtbn, "KQXN", [_hotenbn, _portalxn, _mahsba, _passbn], _timesend, _khambenhid, "3");		
				console.log(kq);

				DlgUtil.showMsg("Trả kết quả thành công",undefined,1000);
				_mbpID = $("#hdfIDMauBenhPham").val();
				$("#hdfIDMauBenhPham").val(""); // làm rỗng grid kết quả khi phiếu đã ko còn trên grid dsbn
			}
			else if(rs == "2"){
				DlgUtil.showMsg("Bạn vừa Trả kết quả sau thời điểm Kết thúc bệnh án. Bạn cần điều chỉnh lại thời gian Trả kết quả phiếu "+sophieuBN+"!");
			}
			else if(rs == "3"){
				DlgUtil.showMsg("Thời gian trả kết quả không được nhỏ hơn thời gian chỉ định!");
			}
			else if(rs=="4"){
				DlgUtil.showMsg("Chưa thể trả kết quả do bệnh nhân chưa thực hiện thanh toán viện phí hoặc có số tiền tạm ứng nhỏ hơn tổng tiền dịch vụ!",undefined,8500);
			}
			else if(rs == "6"){
				var b_min_time = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_MIN_EXEC_TIME');
				DlgUtil.showMsg("Khoảng thời gian thực hiện xét nghiệm dưới "+b_min_time+" phút, vui lòng kiểm tra lại!",undefined,4000);			
			}
			else if(rs == "9"){
				DlgUtil.showMsg("Hệ thống từ chối Trả kết quả sau thời điểm Kết thúc bệnh án! Liên hệ quản trị viên để được hỗ trợ");
			}
			else if(rs == "10"){
				DlgUtil.showMsg("Chưa nhập kết quả. Không cho phép trả kết quả");
			}
			else if(rs == "11"){
				DlgUtil.showMsg("Trả kết quả thành công. Thuốc vật tư đi kèm dịch vụ đã được cập nhật.",undefined,3000);
			}
			else if(rs == "12"){
				DlgUtil.showMsg("Trả kết quả thành công. Không thể tạo phiếu thuốc vật tư đi kèm. Không có thuốc trong kho hoặc thuốc không đủ tồn kho.");
			}
			else if(rs == "13"){
				DlgUtil.showMsg("Trả kết quả thành công. Chưa nhập mã máy thực hiện.",undefined,10000);
			}
			else if(rs == "14"){
				DlgUtil.showMsg("Chưa nhập mã máy thực hiện. Không cho phép trả kết quả",undefined,10000);
			}
			else if(rs == "15"){
				DlgUtil.showMsg("Thời gian trả kết quả không được nhỏ hơn thời gian Kết thúc PTTT!",undefined,10000);
			}
			else if(rs == "16"){
				var b_min_time = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'XN_MIN_THUCHIEN');
				DlgUtil.showMsg("Thời gian trả xét nghiệm dưới "+b_min_time+" phút, vui lòng kiểm tra lại!",undefined,4000);			
			}
			else if(rs=="-1"){
				DlgUtil.showMsg("Có lỗi xảy ra khi cấp thuốc tự động. Bạn cần tạo phiếu thuốc thủ công!");
			}
			else if(isNaN(rs)){
				DlgUtil.showMsg("Có lỗi xảy ra khi cấp thuốc tự động: "+rs);
			}
			else {
				DlgUtil.showMsg("Trả kết quả thành công. Không thể tạo phiếu thuốc vật tư đi kèm. Thuốc/ Vật tư "+rs,undefined,10000);	
			} 
			// đẩy kết quả tới pc covid
			var covid = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'DAY_DULIEU_PCCOVID');
			if(covid=="1"){
				var msg = ajaxSvc.LISSvc.sendToPCCovid(sophieuBN);
				console.log(msg);
			}
			// kết thúc đẩy kết quả tới pc covid
		}
	}

	// Gỡ trả kết quả
	function huyTraKetQua(){
		var rowId = $("#grdBenhPhamDangLam").jqGrid('getGridParam','selrow'); 
		console.log('tiepnhanid: '+tiepnhanid+', phong_chuyen_den_id: '+phong_chuyen_den_id+', opt.subdept_id: '+opt.subdept_id+', hdfIDMauBenhPham: '+$("#hdfIDMauBenhPham").val());
		if(rowId == null){
			DlgUtil.showMsg("Chưa chọn bệnh phẩm",undefined,2000);
		} else {			
			if(!rolePTH){ 
				DlgUtil.showMsg("Không phải phòng thực hiện, không thể gỡ trả kết quả",undefined,1000);	
			} else {
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
							$("#hdfIDMauBenhPham").val()+'$'+
							$("#hdfIDDichVuKB").val();
	
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.GTKQ", param);
				
				if (rs == '1') {
					DlgUtil.showMsg("Gỡ trả kết quả thành công",undefined,1000);
					$("#hdfIDMauBenhPham").val("");
				} else if(rs == '2'){
					DlgUtil.showMsg("Đã đóng bệnh án, không thể gỡ trả kết quả",undefined,2000);	
				} else if(rs == '3'){
					DlgUtil.showMsg("Bạn không có quyền gỡ duyệt kết quả!",undefined,2000);	
				} else if(rs=="-1"){
					DlgUtil.showMsg("Có lỗi xảy ra khi xóa phiếu cấp thuốc tự động. Bạn cần xóa phiếu thuốc thủ công!");
				}
				else if(isNaN(rs)){
					DlgUtil.showMsg("Có lỗi xảy ra khi xóa phiếu cấp thuốc tự động: "+rs);
				} else {
					DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,2000);	
				} 
			}		
		}
	}

	// hủy bỏ kết quả 
	function huyKetQua(){
		var rowId = $("#grdBenhPhamDangLam").jqGrid('getGridParam','selrow'); 
		console.log('tiepnhanid: '+tiepnhanid+', phong_chuyen_den_id: '+phong_chuyen_den_id+', opt.subdept_id: '+opt.subdept_id+', hdfIDMauBenhPham: '+$("#hdfIDMauBenhPham").val());
		if(rowId == null){
			DlgUtil.showMsg("Chưa chọn bệnh phẩm",undefined,2000);
		} else {			
			if(!rolePTH){ 
				DlgUtil.showMsg("Không phải phòng thực hiện, không thể hủy kết quả",undefined,1000);	
			} else {
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
							$("#hdfIDMauBenhPham").val()+'$'+
							$("#hdfIDDichVuKB").val();
	
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.HKQ", param);
				
				if (rs == '1') {
					DlgUtil.showMsg("Hủy kết quả thành công",undefined,1000);
					$("#hdfIDMauBenhPham").val("");
				} else if(rs == '2'){
					DlgUtil.showMsg("Đã đóng bệnh án, không thể hủy kết quả",undefined,2000);	
				} else if(rs == '3'){
					DlgUtil.showMsg("Bạn không có quyền hủy kết quả!",undefined,2000);	
				} else if(rs == '4'){
					DlgUtil.showMsg("Dịch vụ đã đóng tiền, không thể hủy!",undefined,2000);	
				} else {
					DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,2000);	
				} 
			}		
		}
	}

	// lưu kết quả người sử dụng nhập trên lưới
	function luuKetQua(){
		var reccount = $("#grdKetQuaXetNghiem").getGridParam('reccount');
		for(var i=1; i<=reccount; i++){
			var selr = $("#grdKetQuaXetNghiem").jqGrid('getGridParam', 'selrow');
			$("#grdKetQuaXetNghiem").jqGrid('saveRow', i, 'clientArray');
			
			var row = $("#grdKetQuaXetNghiem").jqGrid('getRowData',i);
			
			var ket_qua = row.GIATRI_KETQUA;
			var tri_so_binh_thuong = row.TRISOBINHTHUONG;
			var doi_tuong = "";
			
			if(tri_so_binh_thuong.toUpperCase().indexOf("TE") >= 0){
				if(bnTuoi < 6) doi_tuong = "TE";
				else doi_tuong = "NL";
			}
			else {
				if(bnGioiTinh == 1) doi_tuong = "Nam";
				else doi_tuong = "Nữ";
			}
			
			var abnormal = CheckAbnormalResult(ket_qua, doi_tuong, tri_so_binh_thuong);
			
			if (abnormal == "-1")
            {
				$('#grdKetQuaXetNghiem').jqGrid('setCell', i, 'BATTHUONG', -1);
            }
            else if (abnormal == "1")
            {
            	$('#grdKetQuaXetNghiem').jqGrid('setCell', i, 'BATTHUONG', 1);
            }
			//nghiant 29032018 xyz/DICHVUID + '_cboMAMAY
			console.log("MAMAY_xyz: "+$("#" + row.DICHVUTHUCHIENID + "_cboMAMAY").val());
			$('#grdKetQuaXetNghiem').jqGrid('setCell', i, 'MAMAY', $("#" + row.DICHVUTHUCHIENID + "_cboMAMAY").val());
		}
		
		var param_arr = $("#grdKetQuaXetNghiem").jqGrid('getRowData');
		
		if(param_arr != null && param_arr.length > 0){			
			var param_arrAll=[];
			for(var i=0; i<param_arr.length; i++){
				 param_arr[i]['KTV_THUCHIEN']=$("#cboPHUMO1").val();				  
				 param_arrAll.push(param_arr[i]);
			}
			var param_str = JSON.stringify(param_arrAll);
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+			
						$("#hdfIDDichVuKB").val()+'$'+
						param_str;
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.LKQ", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Lưu kết quả thành công",undefined,1000);	
			}
			else if(rs == '3'){
				DlgUtil.showMsg("Số lượng ký tự nhập vượt quá số lượng ký tự cho phép theo quy định của BYT",undefined,3000);	
			}
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,3000);	
			} 
		}
		else {
			DlgUtil.showMsg("Không có dữ liệu để lưu",undefined,3000);	
		}		
	}
	
	// lưu kết quả mẫu
	function luuKetQuaMau(){
		var param_arr = $("#grdKetQuaXetNghiem").jqGrid('getRowData');
		var _tenmau = $("#txtTenMau").val();
		if(_tenmau == null ||  _tenmau ==''){
			DlgUtil.showMsg("Bạn chưa nhập tên mẫu!",undefined,1000);
			return;
		}		
		if(param_arr != null && param_arr.length > 0){
			var param_str = JSON.stringify(param_arr);
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+			
						$("#hdfIDDichVuKB").val()+'$'+
						param_str+'$'+_tenmau+'$'+opt.dept_id+'$'+
						$("#cboPhongThucHien").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.LKQM", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Lưu kết quả mẫu thành công",undefined,1000);
				$("#txtTenMau").val("");
				
				//start tuyendv 15012018
				var sql_par2 = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
							  opt.subdept_id+'$'+'0';
				ComboUtil.getComboTag("cboDSMau", "CLS01X002.DSM", sql_par2, "", {value:'-1',text:'-- Chọn Mẫu KQ --'}, "sp");
				//end tuyendv 15012018
			}
			else if(rs == '0') {
				DlgUtil.showMsg("Lưu không thành công, tên mẫu này đã tồn tại!",undefined,3000);	
			} 
			else if(rs == '-1') {
				DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,3000);	
			} 
		}
		else {
			DlgUtil.showMsg("Không có dữ liệu để lưu",undefined,3000);	
		}		
	}
	
	// nhận kết quả lấy được từ máy xét nghiệm
	function nhanKetQua(data){
		if(data != null && data != ""){
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+			
						$("#hdfIDDichVuKB").val()+'$'+
						JSON.stringify(data);
			if(LIS_PROVIDER=="TGG"){
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.NKQ2", param);
				
				if(rs == '1'){
					console.log("Kết quả nhận được: "+ JSON.stringify(data));	
				}
				else if(rs == '2'){
					console.log("Phiếu chỉ định đã không còn tồn tại");	
				}
				else if(rs == '11'){
					console.log("Nhận được chuỗi kết quả rỗng");	
				}
				else {
					console.log("Có lỗi khi nhận kết quả");	
				} 	
			} else {
				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.NKQ", param);
				
				if(rs == '1'){
					console.log("Kết quả nhận được: "+ JSON.stringify(data));	
				}
				else {
					console.log("Có lỗi khi nhận kết quả, mã lỗi: "+rs);	
				} 		
			}
		}
	}

	// hủy dịch vụ được chỉ định
	function huyDichVu(){
		var rowId =$("#grdDanhSachChiDinh").jqGrid('getGridParam','selrow'); 
		
		if(rowId==null){
			DlgUtil.showMsg("Chưa chọn dịch vụ",undefined,2000);	
		}
		else {
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.HDV", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Hủy bỏ dịch vụ thành công",undefined,1000);				
			}
			else if(rs == '2'){
				DlgUtil.showMsg("Dịch vụ đã thu tiền, hãy hủy phiếu thu trước!",undefined,3000);	
			}
			else if(rs == '3'){
				DlgUtil.showMsg("Dịch vụ đã trả kết quả, hãy hủy kết quả trước!",undefined,3000);	
			}
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,3000);
			} 
		}
	}

	// hoàn tác việc hủy dịch vụ được chỉ định
	function khoiPhucDichVu(){
		var rowId =$("#grdDanhSachChiDinh").jqGrid('getGridParam','selrow'); 
		
		if(rowId==null){
			DlgUtil.showMsg("Chưa chọn dịch vụ",undefined,2000);	
		}
		else {
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS01X002.KPDV", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Khôi phục dịch vụ thành công",undefined,1000);				
			} else {
				DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,3000);	
			} 
		}
	}

	// Bổ sung dịch vụ vào phiếu đã có
	function boSungDichVu(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần bổ sung",undefined,2000);	
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", param.join('$'));
			
			var paramInput = { 
				benhnhanid : data_ar[0].BENHNHANID, 
				mabenhnhan : data_ar[0].MABENHNHAN, 
				khambenhid : data_ar[0].KHAMBENHID, 
				tiepnhanid : data_ar[0].TIEPNHANID, 
				hosobenhanid : data_ar[0].HOSOBENHANID, 
				doituongbenhnhanid : data_ar[0].DOITUONGBENHNHANID, 
				loaitiepnhanid : data_ar[0].LOAITIEPNHANID, 
				subDeptId : opt.subdept_id, 
				deptId : opt.dept_id, 
				mbpid_bosungdv : $("#hdfIDMauBenhPham").val(),
				loaiPhieu: 1
			};
			
			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5,paramInput,"Tạo phiếu chỉ định dịch vụ",1300,600);
			DlgUtil.open("divDlgDichVu");
		}
	}

	function In_XNHH(){
		var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
			par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'chisohc',type:'String',value:haveTestAtPosition(data_ar, 1)==false?"":"x"},
				{name:'kqhc',type:'String',value:findResultAtPosition(data_ar, 1)},
				{name:'chisohsto',type:'String',value:haveTestAtPosition(data_ar, 2)==false?"":"x"},
				{name:'kqhsto',type:'String',value:findResultAtPosition(data_ar, 2)},
				{name:'chisohema',type:'String',value:haveTestAtPosition(data_ar, 3)==false?"":"x"},
				{name:'kqhema',type:'String',value:findResultAtPosition(data_ar, 3)},
				{name:'chisomcv',type:'String',value:haveTestAtPosition(data_ar, 4)==false?"":"x"},
				{name:'kqmcv',type:'String',value:findResultAtPosition(data_ar, 4)},
				{name:'chisomch',type:'String',value:haveTestAtPosition(data_ar, 5)==false?"":"x"},
				{name:'kqmch',type:'String',value:findResultAtPosition(data_ar, 5)},
				{name:'chisomchc',type:'String',value:haveTestAtPosition(data_ar, 6)==false?"":"x"},
				{name:'kqmchc',type:'String',value:findResultAtPosition(data_ar, 6)},					
				{name:'chisordcv',type:'String',value:haveTestAtPosition(data_ar, 7)==false?"":"x"},
				{name:'kqrdcv',type:'String',value:findResultAtPosition(data_ar, 7)},
				{name:'chisordsd',type:'String',value:haveTestAtPosition(data_ar, 8)==false?"":"x"},
				{name:'kqrdsd',type:'String',value:findResultAtPosition(data_ar, 8)},
				{name:'chisobc',type:'String',value:haveTestAtPosition(data_ar, 9)==false?"":"x"},
				{name:'kqbc',type:'String',value:findResultAtPosition(data_ar, 9)},
				{name:'chisonew',type:'String',value:haveTestAtPosition(data_ar, 10)==false?"":"x"},
				{name:'kqnew',type:'String',value:findResultAtPosition(data_ar, 10)},
				{name:'chisolym',type:'String',value:haveTestAtPosition(data_ar, 11)==false?"":"x"},
				{name:'kqlym',type:'String',value:findResultAtPosition(data_ar, 11)},
				{name:'chisomono',type:'String',value:haveTestAtPosition(data_ar, 12)==false?"":"x"},
				{name:'kqmono',type:'String',value:findResultAtPosition(data_ar, 12)},
				{name:'chisoeos',type:'String',value:haveTestAtPosition(data_ar, 13)==false?"":"x"},
				{name:'kqeos',type:'String',value:findResultAtPosition(data_ar, 13)},
				{name:'chisobaso',type:'String',value:haveTestAtPosition(data_ar, 14)==false?"":"x"},
				{name:'kqbaso',type:'String',value:findResultAtPosition(data_ar, 14)},
				{name:'chisonewt',type:'String',value:haveTestAtPosition(data_ar, 15)==false?"":"x"},
				{name:'kqnewt',type:'String',value:findResultAtPosition(data_ar, 15)},
				{name:'chisolymt',type:'String',value:haveTestAtPosition(data_ar, 16)==false?"":"x"},
				{name:'kqlymt',type:'String',value:findResultAtPosition(data_ar, 16)},
				{name:'chisomonot',type:'String',value:haveTestAtPosition(data_ar, 17)==false?"":"x"},
				{name:'kqmonot',type:'String',value:findResultAtPosition(data_ar, 17)},
				{name:'chisoeost',type:'String',value:haveTestAtPosition(data_ar, 18)==false?"":"x"},
				{name:'kqeost',type:'String',value:findResultAtPosition(data_ar, 18)},
				{name:'chisobasot',type:'String',value:haveTestAtPosition(data_ar, 19)==false?"":"x"},
				{name:'kqbasot',type:'String',value:findResultAtPosition(data_ar, 19)},					
				{name:'chisotieucau',type:'String',value:haveTestAtPosition(data_ar, 20)==false?"":"x"},
				{name:'kqtieucau',type:'String',value:findResultAtPosition(data_ar, 20)},
				{name:'chisompv',type:'String',value:haveTestAtPosition(data_ar, 21)==false?"":"x"},
				{name:'kqmpv',type:'String',value:findResultAtPosition(data_ar, 21)}			
			];
			openReport('window', 'NTU016_PHIEUXETNGHIEMHH_951', 'pdf', par);
	};
	function In_XNHH1000(){
		var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
			par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'chisohc',type:'String',value:haveTestAtPosition(data_ar, 1)==false?"":"x"},
				{name:'kqhc',type:'String',value:findResultAtPosition(data_ar, 1)},
				{name:'chisohsto',type:'String',value:haveTestAtPosition(data_ar, 2)==false?"":"x"},
				{name:'kqhsto',type:'String',value:findResultAtPosition(data_ar, 2)},
				{name:'chisohema',type:'String',value:haveTestAtPosition(data_ar, 3)==false?"":"x"},
				{name:'kqhema',type:'String',value:findResultAtPosition(data_ar, 3)},
				{name:'chisomcv',type:'String',value:haveTestAtPosition(data_ar, 4)==false?"":"x"},
				{name:'kqmcv',type:'String',value:findResultAtPosition(data_ar, 4)},
				{name:'chisomch',type:'String',value:haveTestAtPosition(data_ar, 5)==false?"":"x"},
				{name:'kqmch',type:'String',value:findResultAtPosition(data_ar, 5)},
				{name:'chisomchc',type:'String',value:haveTestAtPosition(data_ar, 6)==false?"":"x"},
				{name:'kqmchc',type:'String',value:findResultAtPosition(data_ar, 6)},					
				{name:'chisordcv',type:'String',value:haveTestAtPosition(data_ar, 7)==false?"":"x"},
				{name:'kqrdcv',type:'String',value:findResultAtPosition(data_ar, 7)},
				{name:'chisordsd',type:'String',value:haveTestAtPosition(data_ar, 8)==false?"":"x"},
				{name:'kqrdsd',type:'String',value:findResultAtPosition(data_ar, 8)},
				{name:'chisobc',type:'String',value:haveTestAtPosition(data_ar, 9)==false?"":"x"},
				{name:'kqbc',type:'String',value:findResultAtPosition(data_ar, 9)},
				{name:'chisonew',type:'String',value:haveTestAtPosition(data_ar, 10)==false?"":"x"},
				{name:'kqnew',type:'String',value:findResultAtPosition(data_ar, 10)},
				{name:'chisolym',type:'String',value:haveTestAtPosition(data_ar, 11)==false?"":"x"},
				{name:'kqlym',type:'String',value:findResultAtPosition(data_ar, 11)},
				{name:'chisomono',type:'String',value:haveTestAtPosition(data_ar, 12)==false?"":"x"},
				{name:'kqmono',type:'String',value:findResultAtPosition(data_ar, 12)},
				{name:'chisoeos',type:'String',value:haveTestAtPosition(data_ar, 13)==false?"":"x"},
				{name:'kqeos',type:'String',value:findResultAtPosition(data_ar, 13)},
				{name:'chisobaso',type:'String',value:haveTestAtPosition(data_ar, 14)==false?"":"x"},
				{name:'kqbaso',type:'String',value:findResultAtPosition(data_ar, 14)},
				{name:'chisonewt',type:'String',value:haveTestAtPosition(data_ar, 15)==false?"":"x"},
				{name:'kqnewt',type:'String',value:findResultAtPosition(data_ar, 15)},
				{name:'chisolymt',type:'String',value:haveTestAtPosition(data_ar, 16)==false?"":"x"},
				{name:'kqlymt',type:'String',value:findResultAtPosition(data_ar, 16)},
				{name:'chisomonot',type:'String',value:haveTestAtPosition(data_ar, 17)==false?"":"x"},
				{name:'kqmonot',type:'String',value:findResultAtPosition(data_ar, 17)},
				{name:'chisoeost',type:'String',value:haveTestAtPosition(data_ar, 18)==false?"":"x"},
				{name:'kqeost',type:'String',value:findResultAtPosition(data_ar, 18)},
				{name:'chisobasot',type:'String',value:haveTestAtPosition(data_ar, 19)==false?"":"x"},
				{name:'kqbasot',type:'String',value:findResultAtPosition(data_ar, 19)},					
				{name:'chisotieucau',type:'String',value:haveTestAtPosition(data_ar, 20)==false?"":"x"},
				{name:'kqtieucau',type:'String',value:findResultAtPosition(data_ar, 20)},
				{name:'chisompv',type:'String',value:haveTestAtPosition(data_ar, 21)==false?"":"x"},
				{name:'kqmpv',type:'String',value:findResultAtPosition(data_ar, 21)}			
			];
			openReport('window', 'NTU016_PHIEUXETNGHIEMHH_XN1000_A4_951', 'pdf', par);
	};
	// in phiếu trả kết quả cho bệnh nhân
	function inPhieuKetQua(){
		var CLS_CHANINPHIEU_CHUACOKETQUA = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","CLS_CHANINPHIEU_CHUACOKETQUA") == "1";
		var CHECK_CHUANHAPKQ = 0;
		if (CLS_CHANINPHIEU_CHUACOKETQUA =='1'){
			var sql_par = [];
			sql_par.push({
				"name" : "[0]",
				"value" : $("#hdfIDMauBenhPham").val()
			});
		    CHECK_CHUANHAPKQ = jsonrpc.AjaxJson.getOneValue("XN.CHECK.NHAPKQ", sql_par);	
		}		
		if($("#hdfIDMauBenhPham").val()=="" && _mbpID==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
			return;
		} else if (CLS_CHANINPHIEU_CHUACOKETQUA =='1' && CHECK_CHUANHAPKQ > 0){			
			DlgUtil.showMsg("Không in được phiếu khi chưa có kết quả!",undefined,3000);
			return;
		} else {
			if(i_hid=="951"){
				//lay report code cần xuất bc
				var _par_code = [$("#hdfIDMauBenhPham").val()];	
 			    var i_report_code= jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE_KQ",_par_code.join('$'));
 			    for(var i=0;i<i_report_code.length;i++){
	   					var _report_code=i_report_code[i].REPORT_CODE;	   					
	   					if(_report_code=='NTU016_PHIEUXETNGHIEMHH_951'){
	   						In_XNHH();	   					}
	   					else if(_report_code=='NTU016_PHIEUXETNGHIEMML_951'){
	   						var par = [ 
	   									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
	 				           ];
	   						openReport('window', 'NTU016_PHIEUXETNGHIEMML_951', 'pdf', par);				
	   					}
	   					else if(_report_code=='NTU016_PHIEUXETNGHIEMHH_XN1000_A4_951'){
	   						In_XNHH1000();
	   					}
	   					else if(_report_code=='NTU016_PHIEUXETNGHIEMNHOMMAU_951'){
	   						var par = [ 
	   									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
	 				           ];
	   						openReport('window', 'NTU016_PHIEUXETNGHIEMNHOMMAU_951', 'pdf', par);			
	   					}
	   					else if(_report_code=='PHIEUXETNGHIEM_DONGMAU_951'){
	   						var par = [ 
	   									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
	 				           ];
	   						openReport('window', 'PHIEUXETNGHIEM_DONGMAU_951', 'pdf', par);				
	   					}
	   					else if(_report_code=='PHIEUXETNGHIEM_TEBAONUOCDICH_951'){
	   						var par = [ 
	   									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
	 				           ];
	   						openReport('window', 'PHIEUXETNGHIEM_TEBAONUOCDICH_951', 'pdf', par);					
	   					}
	   					else if(_report_code=='PHIEUXETNGHIEMDONGMAUCS1600_951'){
	   						var par = [ 
	   									{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
	 				           ];
	   						openReport('window', 'PHIEUXETNGHIEMDONGMAUCS1600_951', 'pdf', par);				
	   					}
	   					else if(_report_code=='PHIEUXETNGHIEM_HH_HAR_951'){
	   						var par = [ 
	   									{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
	 				           ];
	   						openReport('window', 'PHIEUXETNGHIEM_HH_HAR_951', 'pdf', par);
	   					}
	   					else if(_report_code=='PHIEUXETNGHIEM_TEBAOLA_951'){
	   						var par = [ 
	   									{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
	 				           ];
	   						openReport('window', 'PHIEUXETNGHIEM_TEBAOLA_951', 'pdf', par);
	   					}
	   					else if(_report_code=='PHIEUXETNGHIEM_CP_2000_951'){
	   						var par = [ 
	   									{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
	 				           ];
	   						openReport('window', 'PHIEUXETNGHIEM_CP_2000_951', 'pdf', par);
	   				  }
	   				  else if(_report_code=='KETQUAXETNGHIEM_TEBAOLYMPHO_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_TEBAOLYMPHO_951', 'pdf', par);
				      }
		   			else if(_report_code=='KETQUAXETNGHIEM_MIENDICH_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_MIENDICH_951', 'pdf', par);
			          }
			   			else if(_report_code=='KETQUAXETNGHIEM_NAM_KYSINHTRUNG_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_NAM_KYSINHTRUNG_951', 'pdf', par);
   			          }
			   			else if(_report_code=='KETQUAXETNGHIEM_VISINH_NUOCTIEU_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_VISINH_NUOCTIEU_951', 'pdf', par);
   			          }
			   			else if(_report_code=='KETQUAXETNGHIEM_VISINH_MUAPXE_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_VISINH_MUAPXE_951', 'pdf', par);
   			          }
			   			else if(_report_code=='KETQUAXETNGHIEM_VISINH_DICHNAOTUY_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_VISINH_DICHNAOTUY_951', 'pdf', par);
   			          }
			   			else if(_report_code=='KETQUAXETNGHIEM_VISINH_DOM_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_VISINH_DOM_951', 'pdf', par);
   			          }
			   		 else if(_report_code=='KETQUAXETNGHIEM_HOASINHMAU_NGOAIDICHHONG_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_HOASINHMAU_NGOAIDICHHONG_951', 'pdf', par);					 
   			          }
				   		else if(_report_code=='KETQUAXETNGHIEM_VISINH_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_VISINH_951', 'pdf', par);					 
			          }
				   		else if(_report_code=='KETQUAXETNGHIEM_NUOCTIEU_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_NUOCTIEU_951', 'pdf', par);					 
			          }
				   		else if(_report_code=='KETQUAXETNGHIEM_VISINHKQNCMAU_951'){
	   						var par = [ 
									{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
						openReport('window', 'KETQUAXETNGHIEM_VISINHKQNCMAU_951', 'pdf', par);					 
			          }
	   				  else if(_report_code=='PHIEUXETNGHIEMDONGMAU_ACLTOP300500'){
	   					  inPhieuXNDongMau_ACLTOP300500();
	   				  }
	   				  else if(_report_code=='PHIEUXETNGHIEMHUYETTUYDO_951'){
	   					  inPhieuXN_HUYETTUYDO();
	   				  }
	   				  else if(_report_code=='PHIEUKQ_XETNGHIEM_KHIMAUDONGMACH_951'){
		   					var par = [ 
								{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
							];
							openReport('window', 'PHIEUKQ_XETNGHIEM_KHIMAUDONGMACH_951', 'pdf', par);			
	   				  }
	   				  else {
						var par = [ 
									{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
								];
						var k = _report_code.split(';');
						for(var j=0;j<k.length;j++){
							openReport('window', k[j], 'pdf', par);
						}
	   				  }
			   	
	   				}
				return;
			} 
			else if(i_hid=="1007"){
				//lay report code cần xuất bc
				var _par_code = [$("#hdfIDMauBenhPham").val()];	
 			    var i_report_code= jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE_KQ",_par_code.join('$'));
 			    for(var i=0;i<i_report_code.length;i++){
	   					var _report_code=i_report_code[i].REPORT_CODE;	   					
	   					if(_report_code=='NTU016_PHIEUXETNGHIEMHH_951'){
	   						In_XNHH();	
						}
						else if(_report_code=='NTU016_PHIEUXETNGHIEMHH_XN1000_A4_951'){
	   						In_XNHH1000();
	   					}	   									   	
	   				  else if(_report_code=='PHIEUXETNGHIEMDONGMAU_ACLTOP300500'){
							inPhieuXNDongMau_ACLTOP300500();
	   				  }else if(_report_code=='PHIEUXETNGHIEMHUYETTUYDO_951'){
							inPhieuXN_HUYETTUYDO();
	   				  }  else {
						    var k = _report_code.split(';');
							if(k.length > 1){
								var par = [ 
	   									{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
									];
	   						
								for(var j=0;j<k.length;j++){
								openReport('window', k[j], 'pdf', par);
								}		   						
							} else {
								var par = [ 
									{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
								];
								openReport('window', _report_code, 'pdf', par);	
							}
					}
				}
				return;
			} 
			else if(i_hid=="939"){
				var TACH_KQ_XN = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","TACH_KQ_XN");
				var par = [ 
				           {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
				 if(TACH_KQ_XN ==1){		
					 openReport('window', 'PHIEU_KETQUA_XETNGHIEM', 'pdf', par);
				}
				 else{
					openReport('window', 'PhieuXetNghiem', 'pdf', par);
				}
				return;
			}
			else if(i_hid=="993"){
				var reccount = $("#grdKetQuaXetNghiem").getGridParam("reccount");
				var par = [ 
				           {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
				var dsdichvukbid= '';//truyen vao ham in phieu  và tra ket qua
	            var idss = $('#grdKetQuaXetNghiem').jqGrid('getGridParam', 'selarrrow');
	            for (i = 0, n = idss.length; i < n; i++)
	            {
	            	var rowSelected =  $('#grdKetQuaXetNghiem').jqGrid('getRowData', idss[i]);
	            	if (dsdichvukbid != '' && dsdichvukbid != ""){
	            		dsdichvukbid = dsdichvukbid+ ','+ rowSelected.DICHVUKHAMBENHID;
	            	} else {
	            		dsdichvukbid = rowSelected.DICHVUKHAMBENHID;
			        }
	            }
	            if ((dsdichvukbid == '' || dsdichvukbid == "") && reccount >=2){
	            	DlgUtil.showMsg("Bạn chưa chọn dịch vụ để in!",undefined,3000);
	            } else {
	            	if(reccount == 1){
            			var row = $("#grdKetQuaXetNghiem").jqGrid('getRowData',1);
            			dsdichvukbid=row.DICHVUKHAMBENHID;
	            	}
	            	par.push({name:'dsdichvukbid',type:'String',value:dsdichvukbid});
	            	openReport('window', 'PhieuXetNghiem', 'pdf', par);
	            }
				return;				
			}			
			
			//huongpv add -- in phiêu theo report_code_kq trong bang dmc_dichvu
			var INPHIEU_THEO_DV = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","INPHIEU_THEO_DV");
			var INPHIEU_THEO_NHOMDV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA","XN_INPHIEU_THEO_NHOMDV");
			if(INPHIEU_THEO_DV!=null && INPHIEU_THEO_DV=="1"){
				var par = [ 
			                {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
			              ];
				
				var _par_code = [$("#hdfIDMauBenhPham").val()];	
 			    var i_report_code= jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE_KQ",_par_code.join('$'));
 			    if(i_report_code!=null && i_report_code.length>0){ 	  
 			    	for(var i=0;i<i_report_code.length;i++){
 			    		var _report_code=i_report_code[i].REPORT_CODE;
 			    		var k = _report_code.split(';');
 			    		if(k.length > 1){													
 			    			for(var j=0;j<k.length;j++){								
 			    				openReport('window', k[j], 'pdf', par);				 
 			    			}		   						
 			    		} else {														
 			    			openReport('window', _report_code, 'pdf', par);							
 			    		}
 			    	}
 			   } else {
 				      var par = [ 
			             {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
			           ];
			
 				  	  openReport('window', 'PhieuXetNghiem', 'pdf', par);
 			   }
 			   return;
			} else if (INPHIEU_THEO_NHOMDV == "1"){
				var param = [$("#hdfIDMauBenhPham").val()];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.NHOMDV", param.join('$'));
				if(data_ar != null && data_ar.length >0){
					for(var i=0; i<data_ar.length; i++){
						var par = [ 
						    {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
						    {name:'id_nhomdichvu',type:'String',value:data_ar[i].NHOMDICHVUID}
						];
						
						openReport('window', 'CLS_PhieuXetNghiem', 'pdf', par);
					}
				}
			} else if (CLS_HIENTHI_CHECKBOX_GROUPGRID == "1") {
				var reccount = $("#grdKetQuaXetNghiem").getGridParam("reccount");
				var par = [ 
				           {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
				           ];
				var dsdichvukbid= '';//truyen vao ham in phieu  và tra ket qua
	            var idss = $('#grdKetQuaXetNghiem').jqGrid('getGridParam', 'selarrrow');
	            for (i = 0, n = idss.length; i < n; i++)
	            {
	            	var rowSelected =  $('#grdKetQuaXetNghiem').jqGrid('getRowData', idss[i]);
	            	if (dsdichvukbid != '' && dsdichvukbid != ""){
	            		dsdichvukbid = dsdichvukbid+ ','+ rowSelected.DICHVUKHAMBENHID;
	            	} else {
	            		dsdichvukbid = rowSelected.DICHVUKHAMBENHID;
			        }
	            }
				par.push({name:'dsdichvukbid',type:'String',value:dsdichvukbid});
				openReport('window', 'PhieuXetNghiem', 'pdf', par);	            
			} else{				
				var par = [ 
					{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()==""?_mbpID:$("#hdfIDMauBenhPham").val()}
				];			
				openReport('window', 'PhieuXetNghiem', 'pdf', par);
			}
			// vietda: điều chỉnh loại bỏ i_hid, in theo cấu hình
		}
	}
	
	
	function inPhieuXNDongMau_ACLTOP300500(){
		var param = [$("#hdfIDMauBenhPham").val()];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
		par = [ 
			{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},			
			{name:'kq1',type:'String',value:findResultAtPosition(data_ar, 1)},
			{name:'kq2',type:'String',value:findResultAtPosition(data_ar, 2)},
			{name:'kq3',type:'String',value:findResultAtPosition(data_ar, 3)},
			{name:'kq4',type:'String',value:findResultAtPosition(data_ar, 4)},
			{name:'kq5',type:'String',value:findResultAtPosition(data_ar, 5)},
			{name:'kq6',type:'String',value:findResultAtPosition(data_ar, 6)},
			{name:'kq7',type:'String',value:findResultAtPosition(data_ar, 7)}
		];
		openReport('window', 'PHIEUXETNGHIEMDONGMAU_ACLTOP300500', 'pdf', par);
	}
	function inPhieuXN_HUYETTUYDO(){
		var param = [$("#hdfIDMauBenhPham").val()];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
		par = [ 
			{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},			
			{name:'kq1',type:'String',value:findResultAtPosition(data_ar, 1)},
			{name:'kq2',type:'String',value:findResultAtPosition(data_ar, 2)},
			{name:'kq3',type:'String',value:findResultAtPosition(data_ar, 3)},
			{name:'kq4',type:'String',value:findResultAtPosition(data_ar, 4)},
			{name:'kq5',type:'String',value:findResultAtPosition(data_ar, 5)},
			{name:'kq6',type:'String',value:findResultAtPosition(data_ar, 6)},
			{name:'kq7',type:'String',value:findResultAtPosition(data_ar, 7)},
			{name:'kq8',type:'String',value:findResultAtPosition(data_ar, 8)},
			{name:'kq9',type:'String',value:findResultAtPosition(data_ar, 9)},
			{name:'kq10',type:'String',value:findResultAtPosition(data_ar, 10)},
			{name:'kq11',type:'String',value:findResultAtPosition(data_ar, 11)},
			{name:'kq12',type:'String',value:findResultAtPosition(data_ar, 12)},
			{name:'kq13',type:'String',value:findResultAtPosition(data_ar, 13)},
			{name:'kq14',type:'String',value:findResultAtPosition(data_ar, 14)},
			{name:'kq15',type:'String',value:findResultAtPosition(data_ar, 15)},
			{name:'kq16',type:'String',value:findResultAtPosition(data_ar, 16)},
			{name:'kq17',type:'String',value:findResultAtPosition(data_ar, 17)},
			{name:'kq18',type:'String',value:findResultAtPosition(data_ar, 18)},
			{name:'kq19',type:'String',value:findResultAtPosition(data_ar, 19)},
			{name:'kq20',type:'String',value:findResultAtPosition(data_ar, 20)},
			{name:'kq21',type:'String',value:findResultAtPosition(data_ar, 21)},
			{name:'kq22',type:'String',value:findResultAtPosition(data_ar, 22)},
			{name:'kq23',type:'String',value:findResultAtPosition(data_ar, 23)},
			{name:'kq24',type:'String',value:findResultAtPosition(data_ar, 24)},
			{name:'kq25',type:'String',value:findResultAtPosition(data_ar, 25)},
			{name:'kq26',type:'String',value:findResultAtPosition(data_ar, 26)},
			{name:'kq27',type:'String',value:findResultAtPosition(data_ar, 27)},
			{name:'kq26',type:'String',value:findResultAtPosition(data_ar, 28)},
			{name:'kq29',type:'String',value:findResultAtPosition(data_ar, 29)},
			{name:'kq30',type:'String',value:findResultAtPosition(data_ar, 30)},
			{name:'kq31',type:'String',value:findResultAtPosition(data_ar, 31)},
			{name:'kq32',type:'String',value:findResultAtPosition(data_ar, 32)},
			{name:'kq33',type:'String',value:findResultAtPosition(data_ar, 33)},
			{name:'kq34',type:'String',value:findResultAtPosition(data_ar, 34)},
			{name:'kq35',type:'String',value:findResultAtPosition(data_ar, 35)},
			{name:'kq36',type:'String',value:findResultAtPosition(data_ar, 36)},
			{name:'kq37',type:'String',value:findResultAtPosition(data_ar, 37)},
			{name:'kq38',type:'String',value:findResultAtPosition(data_ar, 38)},
			{name:'kq39',type:'String',value:findResultAtPosition(data_ar, 39)},
			{name:'kq40',type:'String',value:findResultAtPosition(data_ar, 40)},
			{name:'kq41',type:'String',value:findResultAtPosition(data_ar, 41)},
			{name:'kq42',type:'String',value:findResultAtPosition(data_ar, 42)},
			{name:'kq43',type:'String',value:findResultAtPosition(data_ar, 43)},
			{name:'kq44',type:'String',value:findResultAtPosition(data_ar, 44)},
			{name:'kq45',type:'String',value:findResultAtPosition(data_ar, 45)},
			{name:'kq46',type:'String',value:findResultAtPosition(data_ar, 46)},
			{name:'kq47',type:'String',value:findResultAtPosition(data_ar, 47)},
			{name:'kq48',type:'String',value:findResultAtPosition(data_ar, 48)},
			{name:'kq49',type:'String',value:findResultAtPosition(data_ar, 49)},
			{name:'kq50',type:'String',value:findResultAtPosition(data_ar, 50)},
			{name:'kq51',type:'String',value:findResultAtPosition(data_ar, 51)},
			{name:'kq52',type:'String',value:findResultAtPosition(data_ar, 52)},
			{name:'kq53',type:'String',value:findResultAtPosition(data_ar, 53)},
			{name:'kq54',type:'String',value:findResultAtPosition(data_ar, 54)},
			{name:'kq55',type:'String',value:findResultAtPosition(data_ar, 55)},
			{name:'kq56',type:'String',value:findResultAtPosition(data_ar, 56)},
			{name:'kq57',type:'String',value:findResultAtPosition(data_ar, 57)},
			{name:'kq58',type:'String',value:findResultAtPosition(data_ar, 58)},
			{name:'kq59',type:'String',value:findResultAtPosition(data_ar, 59)},
			{name:'kq60',type:'String',value:findResultAtPosition(data_ar, 60)},
			{name:'kq61',type:'String',value:findResultAtPosition(data_ar, 61)},
			{name:'kq62',type:'String',value:findResultAtPosition(data_ar, 62)},
			{name:'kq63',type:'String',value:findResultAtPosition(data_ar, 63)},
			{name:'kq64',type:'String',value:findResultAtPosition(data_ar, 64)},
			{name:'kq65',type:'String',value:findResultAtPosition(data_ar, 65)},
			{name:'kq66',type:'String',value:findResultAtPosition(data_ar, 66)},
			{name:'kq67',type:'String',value:findResultAtPosition(data_ar, 67)},
			{name:'kq68',type:'String',value:findResultAtPosition(data_ar, 68)},
			{name:'kq69',type:'String',value:findResultAtPosition(data_ar, 69)},
			{name:'kq70',type:'String',value:findResultAtPosition(data_ar, 70)},
			{name:'kq71',type:'String',value:findResultAtPosition(data_ar, 71)},
			{name:'kq72',type:'String',value:findResultAtPosition(data_ar, 72)},
			{name:'kq73',type:'String',value:findResultAtPosition(data_ar, 73)},
			{name:'kq74',type:'String',value:findResultAtPosition(data_ar, 74)},
			{name:'kq75',type:'String',value:findResultAtPosition(data_ar, 75)},
			{name:'kq76',type:'String',value:findResultAtPosition(data_ar, 76)},
			{name:'kq77',type:'String',value:findResultAtPosition(data_ar, 77)},
			{name:'kq78',type:'String',value:findResultAtPosition(data_ar, 78)},
			{name:'kq79',type:'String',value:findResultAtPosition(data_ar, 79)},
			{name:'kq80',type:'String',value:findResultAtPosition(data_ar, 80)},
			{name:'kq81',type:'String',value:findResultAtPosition(data_ar, 81)},
			{name:'kq82',type:'String',value:findResultAtPosition(data_ar, 82)}
		];
		openReport('window', 'PHIEUXETNGHIEMHUYETTUYDO_951', 'pdf', par);
	}
	////tuyendv		
	function inPhieuBarcode(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else{            	
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'NGT_BARCODE_A6', 'pdf', par);
		}
	}
	// in phiếu trả kết quả xét nghiệm chung
	//20181008 - duongnv- them danh sach tham so cho 938
	function inPhieu27BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else if (i_hid =="938") {
            var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'ure',type:'String',value:findResultAtPosition(data_ar, 1)},
				{name:'glucose',type:'String',value:findResultAtPosition(data_ar, 2)},
				{name:'creatinin',type:'String',value:findResultAtPosition(data_ar, 3)},
				{name:'aciduric',type:'String',value:findResultAtPosition(data_ar, 4)},
				{name:'bilirubintp',type:'String',value:findResultAtPosition(data_ar, 5)},
				{name:'bilirubintp',type:'String',value:findResultAtPosition(data_ar, 6)},
				{name:'albumin',type:'String',value:findResultAtPosition(data_ar, 7)},
				{name:'globulin',type:'String',value:findResultAtPosition(data_ar, 8)},
				{name:'tyleag',type:'String',value:findResultAtPosition(data_ar, 9)},
				{name:'proteintp',type:'String',value:findResultAtPosition(data_ar, 10)},
				{name:'cholesterol',type:'String',value:findResultAtPosition(data_ar, 11)},
				{name:'triglycerid',type:'String',value:findResultAtPosition(data_ar, 12)},
				{name:'ast',type:'String',value:findResultAtPosition(data_ar, 13)},
				{name:'alt',type:'String',value:findResultAtPosition(data_ar, 14)},
				{name:'amylase',type:'String',value:findResultAtPosition(data_ar, 15)},
				{name:'hba1c',type:'String',value:findResultAtPosition(data_ar, 16)},
				{name:'ggt',type:'String',value:findResultAtPosition(data_ar, 17)},
				{name:'aslo',type:'String',value:findResultAtPosition(data_ar, 18)},
				{name:'na',type:'String',value:findResultAtPosition(data_ar, 19)},
				{name:'calci',type:'String',value:findResultAtPosition(data_ar, 20)},
				{name:'k',type:'String',value:findResultAtPosition(data_ar, 21)},
				{name:'cl',type:'String',value:findResultAtPosition(data_ar, 22)},
				{name:'hiv',type:'String',value:findResultAtPosition(data_ar, 23)},
				{name:'hbsag',type:'String',value:findResultAtPosition(data_ar, 24)},
				{name:'hcg',type:'String',value:findResultAtPosition(data_ar, 25)},
				{name:'hcv',type:'String',value:findResultAtPosition(data_ar, 26)},
				{name:'qktrunggiun',type:'String',value:findResultAtPosition(data_ar, 27)},
				{name:'kqsoiphan',type:'String',value:findResultAtPosition(data_ar, 28)},
				{name:'wbc',type:'String',value:findResultAtPosition(data_ar, 29)},
				{name:'rbc',type:'String',value:findResultAtPosition(data_ar, 30)},
				{name:'hgb',type:'String',value:findResultAtPosition(data_ar, 31)},
				{name:'hct',type:'String',value:findResultAtPosition(data_ar, 32)},
				{name:'gra',type:'String',value:findResultAtPosition(data_ar, 34)},
				{name:'kqlympho',type:'String',value:findResultAtPosition(data_ar, 35)},
				{name:'kqmono',type:'String',value:findResultAtPosition(data_ar, 36)},
				{name:'kqtieucau',type:'String',value:findResultAtPosition(data_ar, 37)},
				{name:'heabo',type:'String',value:findResultAtPosition(data_ar, 38)},
				{name:'phutmd',type:'String',value:findResultAtPosition(data_ar, 39)},
				{name:'phutmc',type:'String',value:findResultAtPosition(data_ar, 40)},
				{name:'kqmaulang',type:'String',value:findResultAtPosition(data_ar, 41)},
				{name:'herh',type:'String',value:findResultAtPosition(data_ar, 42)},
				{name:'kqsotret',type:'String',value:findResultAtPosition(data_ar, 43)},
				{name:'hongcauluoi',type:'String',value:findResultAtPosition(data_ar, 44)},
				{name:'PROTEIN',type:'String',value:findResultAtPosition(data_ar, 45)},
				{name:'GLUCOSE',type:'String',value:findResultAtPosition(data_ar, 46)},
				{name:'EYR',type:'String',value:findResultAtPosition(data_ar, 47)},
				{name:'LEU',type:'String',value:findResultAtPosition(data_ar, 48)},
				{name:'BILLIRUBIN',type:'String',value:findResultAtPosition(data_ar, 49)},
				{name:'UROBILINOGEN',type:'String',value:findResultAtPosition(data_ar, 50)},
				{name:'ASCORBIC',type:'String',value:findResultAtPosition(data_ar, 51)},
				{name:'CREATIMIN',type:'String',value:findResultAtPosition(data_ar, 52)},
				{name:'KETONE',type:'String',value:findResultAtPosition(data_ar, 53)},
				{name:'SG',type:'String',value:findResultAtPosition(data_ar, 54)},
				{name:'kqnt',type:'String',value:findResultAtPosition(data_ar, 55)}
				
				];

			 openReport('window', 'NTU015_PHIEUXETNGHIEM_27BV01_QD4069_A5', 'pdf', par);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'NTU015_PHIEUXETNGHIEM_27BV01_QD4069_A5', 'pdf', par);
		}
	}
	function inPhieu27BV01AFB(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'AFB_PhieuXetNghiem_944', 'pdf', par);
		}
	}
	//nghiant 04082017 
	// in phiếu trả kết quả xét nghiệm nuôi cấy
	function inPhieu27BV01NuoiCay(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'NHQNM_PHIEUXETNGHIEM_NUOICAY_27BV01_A4', 'pdf', par);
		}
	}
	//end nghiant 04082017 
	function inPhieuKQNhuomSoi(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'PHIEU_KQ_NHUOMSOI_ALL', 'pdf', par);
		}
	}
	
	//nghiant 07082017 
	// in phiếu trả kết quả xét nghiệm nuôi cấy
	function inPhieu27BV01KhangSinhDo(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			
			openReport('window', 'NHQNM_PHIEUXETNGHIEM_KHANGSINHDO_27BV01_A4', 'pdf', par);
		}
	}
	//end nghiant 07082017

	function inPhieuHoaSinh(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			
			openReport('window', 'NTU033_PhieuXetNghiemHoaSinh', 'pdf', par);
		}
	}
	
	
	function inPhieuMienDich(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			
			openReport('window', 'VD2_PHIEUKETQUAXETNGHIEMMIENDICH_A4', 'pdf', par);
		}
	}
	
	// truongle - 06/05/2019
	function inPhieuXNTeBao(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			
			openReport('window', 'CLS_KETQUAXETNGHIEM_TEBAO_A5', 'pdf', par);
		}
	}
	
	// truongle - 28/08/2019
	function inPhieuXNSangLocSoSinh3(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'sobenh',type:'String',value:'3'}
				];
			
			openReport('window', 'PHIEU_XN_SANGLOCSOSINH', 'pdf', par);
		}
	}
	
	// truongle - 28/08/2019
	function inPhieuXNSangLocSoSinh5(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'sobenh',type:'String',value:'5'}
				];
			
			openReport('window', 'PHIEU_XN_SANGLOCSOSINH', 'pdf', par);
		}
	}
	
	// tuyendv - 12/03/2020
	function inPhieuXNNongDoCon(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}				
				];
			
			openReport('window', 'PHIEUKQ_XETNGHIEM_NONGDOCON_939', 'pdf', par);
		}
	}
	
	// ==================== START >> In Phiếu New ==================== truongle - 26/11/2019

	function inPhieuNew(target) {
		console.log(target)
		var rptData = target.data("external")
		if (!rptData || !rptData.report_code) {
			DlgUtil.showMsg("Đã có lỗi xảy ra: rptData",undefined,1500);
			return;
		}

		var maubenhphamid = $("#hdfIDMauBenhPham").val();
		if(maubenhphamid==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
			return;
		}
		var par = [
			{name:'maubenhphamid',type:'String',value:maubenhphamid},
			{name:'id_maubenhpham',type:'String',value:maubenhphamid}
		];
		if (rptData.image_sql && rptData.image_sql != "") {
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(rptData.image_sql, maubenhphamid);
			for(var i=0 ; i<data_ar.length ; i++){
				var hinh_anh = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh!="" && hinh_anh!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh});
				var ten_file = data_ar[i]["TENFILE"];
				if(ten_file!="" && ten_file!=null) par.push({name:'ten_file'+i,type:'String',value:ten_file});
			}
		}

		try {
			if (rptData.optional_param) {
				if (rptData.optional_param.thutubhyt && rptData.optional_param.thutubhyt.length > 0) {
					var kq_arr = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", maubenhphamid);
					par = par.concat(getParamsByOrderal(kq_arr, rptData.optional_param.thutubhyt))
				}
				if (rptData.optional_param.optional && rptData.optional_param.optional.length > 0) {
					par = par.concat(rptData.optional_param.optional)
				}
			}
			//start L2PT-12039 nghiant  15/12/2021 
			var rpName= rptData.report_code + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
            var rpNameXlsx= rptData.report_code + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
			if(rptData.file_format =="docx" ){
				CommonUtil.inPhieu('window', rptData.report_code, rptData.file_format, par, rpName);
			}else if ( rptData.file_format =="xlsx"){
				CommonUtil.inPhieu('window', rptData.report_code, rptData.file_format, par, rpNameXlsx);
			}
			else{
				openReport('window', rptData.report_code, rptData.file_format ? rptData.file_format : 'pdf', par);
			}
			//end L2PT-12039 nghiant  15/12/2021 
		} catch(e) {
			openReport('window', rptData.report_code, rptData.file_format ? rptData.file_format : 'pdf', par);
		}
	}
	// ==================== END >> In Phiếu New ==================== truongle - 26/11/2019


	//25012017
	function inPhieu27BV01DT(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			//nghiant 11052018
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.ANHBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			//nghiant end 11052018

			openReport('window', 'PHIEUXN_DACTHU_944', 'pdf', par);
		}
	}
	//07062018
	function inPhieu27BV01PCR(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			
			//nghiant 11052018
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.ANHBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			//nghiant end 11052018
			
			openReport('window', 'PHIEUXN_PCR_944', 'pdf', par);
		}
	}
	//08062018 TTQNI
	function inPhieu27BV01TEST_TAMLY(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			
			par.push({name:'type',type:'String',value:"1"})
			openReport('window', 'PHIEU_TEST_TAMLY_QNI', 'pdf', par);
		}
	}
	//08062018
	function inPhieu27BV01TV_TAMLY(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			
			par.push({name:'type',type:'String',value:"2"})
			openReport('window', 'PHIEU_TEST_TAMLY_QNI', 'pdf', par);
		}
	}
	//25012017
	function haveTestAtPosition(data_arr, order){
		for(var i=0; i<data_arr.length; i++){
			if(data_arr[i]["THUTUINMAUBYT"]==order){
				return true;
			}
		}
		return false;
	}	
	
	function findResultAtPosition(data_arr, order){
		var result = "";
		for(var i=0; i<data_arr.length; i++){
			if(data_arr[i]["THUTUINMAUBYT"]==order){
				result = data_arr[i]["GIATRI_KETQUA"];
			}
		}
		return result;
	}	
	
	// in phiếu trả kết quả xét nghiệm huyết học
	function inPhieu28BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'chisohc',type:'String',value:haveTestAtPosition(data_ar, 1)==false?"":"x"},
				{name:'kqhc',type:'String',value:findResultAtPosition(data_ar, 1)},
				{name:'chisohsto',type:'String',value:haveTestAtPosition(data_ar, 2)==false?"":"x"},
				{name:'kqhsto',type:'String',value:findResultAtPosition(data_ar, 2)},
				{name:'chisohema',type:'String',value:haveTestAtPosition(data_ar, 3)==false?"":"x"},
				{name:'kqhema',type:'String',value:findResultAtPosition(data_ar, 3)},
				{name:'chisomcv',type:'String',value:haveTestAtPosition(data_ar, 4)==false?"":"x"},
				{name:'kqmcv',type:'String',value:findResultAtPosition(data_ar, 4)},
				{name:'chisomch',type:'String',value:haveTestAtPosition(data_ar, 5)==false?"":"x"},
				{name:'kqmch',type:'String',value:findResultAtPosition(data_ar, 5)},
				{name:'chisomchc',type:'String',value:haveTestAtPosition(data_ar, 6)==false?"":"x"},
				{name:'kqmchc',type:'String',value:findResultAtPosition(data_ar, 6)},
				{name:'chisohcnhan',type:'String',value:haveTestAtPosition(data_ar, 7)==false?"":"x"},
				{name:'kqhcnhan',type:'String',value:findResultAtPosition(data_ar, 7)},
				{name:'chisohcluoi',type:'String',value:haveTestAtPosition(data_ar, 8)==false?"":"x"},
				{name:'kqhcluoi',type:'String',value:findResultAtPosition(data_ar, 8)},
				{name:'chisotieucau',type:'String',value:haveTestAtPosition(data_ar, 9)==false?"":"x"},
				{name:'kqtieucau',type:'String',value:findResultAtPosition(data_ar, 9)},
				{name:'chisosotret',type:'String',value:haveTestAtPosition(data_ar, 10)==false?"":"x"},
				{name:'kqsotret',type:'String',value:findResultAtPosition(data_ar, 10)},
				{name:'chisobc',type:'String',value:haveTestAtPosition(data_ar, 11)==false?"":"x"},
				{name:'kqbc',type:'String',value:findResultAtPosition(data_ar, 11)},
				{name:'chisobachcau',type:'String',value:haveTestAtPosition(data_ar, 12)==false?"":"x"},
				{name:'kqbachcau',type:'String',value:findResultAtPosition(data_ar, 12)},
				{name:'kqtrungtinh',type:'String',value:findResultAtPosition(data_ar, 13)},
				{name:'kqaxit',type:'String',value:findResultAtPosition(data_ar, 14)},
				{name:'kqbazo',type:'String',value:findResultAtPosition(data_ar, 15)},
				{name:'kqmono',type:'String',value:findResultAtPosition(data_ar, 16)},
				{name:'kqlympho',type:'String',value:findResultAtPosition(data_ar, 17)},
				{name:'kqtebaobt',type:'String',value:findResultAtPosition(data_ar, 18)},
				{name:'chisomaulang',type:'String',value:haveTestAtPosition(data_ar, 19)==false?"":"x"},
				{name:'kqmaulang',type:'String',value:findResultAtPosition(data_ar, 19)},
				{name:'kqgio',type:'String',value:findResultAtPosition(data_ar, 20)},
				{name:'giomauchay',type:'String',value:findResultAtPosition(data_ar, 21)},
				{name:'phutmc',type:'String',value:findResultAtPosition(data_ar, 22)},
				{name:'giomaudong',type:'String',value:findResultAtPosition(data_ar, 23)},
				{name:'phutmd',type:'String',value:findResultAtPosition(data_ar, 24)},
				{name:'heabo',type:'String',value:findResultAtPosition(data_ar, 25)},
				{name:'herh',type:'String',value:findResultAtPosition(data_ar, 26)}
	
				
			];			
			if(i_hid =="996"){
				par = [ 
					{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
					{name:'chisohc',type:'String',value:haveTestAtPosition(data_ar, 1)==false?"":"x"},
					{name:'kqhc',type:'String',value:findResultAtPosition(data_ar, 1)},
					{name:'chisohsto',type:'String',value:haveTestAtPosition(data_ar, 2)==false?"":"x"},
					{name:'kqhsto',type:'String',value:findResultAtPosition(data_ar, 2)},
					{name:'chisohema',type:'String',value:haveTestAtPosition(data_ar, 3)==false?"":"x"},
					{name:'kqhema',type:'String',value:findResultAtPosition(data_ar, 3)},
					{name:'chisomcv',type:'String',value:haveTestAtPosition(data_ar, 4)==false?"":"x"},
					{name:'kqmcv',type:'String',value:findResultAtPosition(data_ar, 4)},
					{name:'chisomch',type:'String',value:haveTestAtPosition(data_ar, 5)==false?"":"x"},
					{name:'kqmch',type:'String',value:findResultAtPosition(data_ar, 5)},
					{name:'chisomchc',type:'String',value:haveTestAtPosition(data_ar, 6)==false?"":"x"},
					{name:'kqmchc',type:'String',value:findResultAtPosition(data_ar, 6)},
					{name:'chisohcnhan',type:'String',value:haveTestAtPosition(data_ar, 7)==false?"":"x"},
					{name:'kqhcnhan',type:'String',value:findResultAtPosition(data_ar, 7)},
					{name:'chisohcluoi',type:'String',value:haveTestAtPosition(data_ar, 8)==false?"":"x"},
					{name:'kqhcluoi',type:'String',value:findResultAtPosition(data_ar, 8)},
					{name:'chisompv',type:'String',value:haveTestAtPosition(data_ar, 9)==false?"":"x"},
					{name:'kqmpv',type:'String',value:findResultAtPosition(data_ar, 9)},
					{name:'chisoplcr',type:'String',value:haveTestAtPosition(data_ar, 10)==false?"":"x"},
					{name:'kqplcr',type:'String',value:findResultAtPosition(data_ar, 10)},
					
					{name:'chisosotret',type:'String',value:haveTestAtPosition(data_ar, 11)==false?"":"x"},
					{name:'kqsotret',type:'String',value:findResultAtPosition(data_ar, 11)},
					{name:'chisohbsag',type:'String',value:haveTestAtPosition(data_ar, 12)==false?"":"x"},
					{name:'kqhbsag',type:'String',value:findResultAtPosition(data_ar, 12)},
					
					{name:'chisobc',type:'String',value:haveTestAtPosition(data_ar, 13)==false?"":"x"},
					{name:'kqbc',type:'String',value:findResultAtPosition(data_ar, 13)},
					{name:'chisobachcau',type:'String',value:haveTestAtPosition(data_ar, 14)==false?"":"x"},
					{name:'kqbachcau',type:'String',value:findResultAtPosition(data_ar, 14)},
					
					{name:'kqtrungtinh',type:'String',value:findResultAtPosition(data_ar, 15)},
					{name:'kqaxit',type:'String',value:findResultAtPosition(data_ar, 31)},
					{name:'kqbazo',type:'String',value:findResultAtPosition(data_ar, 16)},
					{name:'kqmono',type:'String',value:findResultAtPosition(data_ar, 17)},
					{name:'kqlympho',type:'String',value:findResultAtPosition(data_ar, 18)},
					{name:'kqtebaobt',type:'String',value:findResultAtPosition(data_ar, 19)},
					{name:'kqneut',type:'String',value:findResultAtPosition(data_ar, 20)},
					
					{name:'chisotieucau',type:'String',value:haveTestAtPosition(data_ar, 21)==false?"":"x"},
					{name:'kqtieucau',type:'String',value:findResultAtPosition(data_ar, 21)},
					{name:'chisomaulang',type:'String',value:haveTestAtPosition(data_ar, 22)==false?"":"x"},
					{name:'kqmaulang',type:'String',value:findResultAtPosition(data_ar, 22)},
					{name:'kqgio',type:'String',value:findResultAtPosition(data_ar, 23)},
					{name:'chisoantihb',type:'String',value:haveTestAtPosition(data_ar, 24)==false?"":"x"},
					{name:'kqantihb',type:'String',value:findResultAtPosition(data_ar, 24)},
					
					{name:'giomauchay',type:'String',value:findResultAtPosition(data_ar, 25)},
					{name:'phutmc',type:'String',value:findResultAtPosition(data_ar, 26)},
					{name:'giomaudong',type:'String',value:findResultAtPosition(data_ar, 27)},
					{name:'phutmd',type:'String',value:findResultAtPosition(data_ar, 28)},
					{name:'heabo',type:'String',value:findResultAtPosition(data_ar, 29)},
					{name:'herh',type:'String',value:findResultAtPosition(data_ar, 30)}
					
				
					
				];
			}
			console.log(JSON.stringify(par));
			openReport('window', 'NTU016_PHIEUXETNGHIEMHUYETHOC_28BV01_QD4069_A4', 'pdf', par);
		}
	}

	// in phiếu trả kết quả xét nghiệm huyết tủy đồ
	function inPhieu29BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'NTU039_PHIEUXETNGHIEMHUYETTUYDO_29BV01_QD4069_A4', 'pdf', par);
		}
	}


	// in phiếu trả kết quả rối loại đông cầm máu
	function inPhieu30BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'tgmauchay',type:'String',value:findResultAtPosition(data_ar,1)},
				{name:'tgmaudong',type:'String',value:findResultAtPosition(data_ar,2)},
				{name:'cocumau',type:'String',value:findResultAtPosition(data_ar,3)},
				{name:'ptgiay',type:'String',value:findResultAtPosition(data_ar,4)},
				{name:'ptphantram',type:'String',value:findResultAtPosition(data_ar,5)},
				{name:'ptinr',type:'String',value:findResultAtPosition(data_ar,6)},
				{name:'tghowell',type:'String',value:findResultAtPosition(data_ar,7)},
				{name:'apttgiay',type:'String',value:findResultAtPosition(data_ar,8)},
				{name:'apttchiso',type:'String',value:findResultAtPosition(data_ar,9)},
				{name:'ttgiay',type:'String',value:findResultAtPosition(data_ar,10)},
				{name:'ttchiso',type:'String',value:findResultAtPosition(data_ar,11)},
				{name:'npruou',type:'String',value:findResultAtPosition(data_ar,12)},
				{name:'npvonkaulla',type:'String',value:findResultAtPosition(data_ar,13)},
				{name:'adp',type:'String',value:findResultAtPosition(data_ar,14)},
				{name:'collagen',type:'String',value:findResultAtPosition(data_ar,15)},
				{name:'ristocetin',type:'String',value:findResultAtPosition(data_ar,16)},
				{name:'yeuto3tieucau',type:'String',value:findResultAtPosition(data_ar,17)},
				{name:'yeuto4tieucau',type:'String',value:findResultAtPosition(data_ar,18)},
				{name:'dlgyeuto4tieucau',type:'String',value:findResultAtPosition(data_ar,19)},
				{name:'fdp',type:'String',value:findResultAtPosition(data_ar,20)},
				{name:'ddiner',type:'String',value:findResultAtPosition(data_ar,21)},
				{name:'xnkhac',type:'String',value:findResultAtPosition(data_ar,22)}
			];

			openReport('window', 'NTU040_PHIEUXETNGHIEMCHANDOANROILOANDONGCAMMAU_30BV01_QD4069_A4', 'pdf', par);
		}
	}


	// in phiếu trả kết quả sinh thiết tủy xương
	function inPhieu31BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'NTU041_PHIEUXETNGHIEMSINHTHIETTUYXUONG_31BV01_QD4069_A4', 'pdf', par);
		}
	}

	// in phiếu trả kết quả xét nghiệm nước dịch
	function inPhieu32BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'hongcau',type:'String',value:findResultAtPosition(data_ar,1)},
				{name:'bachcau',type:'String',value:findResultAtPosition(data_ar,2)},
				{name:'truhat1',type:'String',value:findResultAtPosition(data_ar,3)},
				{name:'trutrong',type:'String',value:findResultAtPosition(data_ar,4)},
				{name:'trumo',type:'String',value:findResultAtPosition(data_ar,5)},
				{name:'than',type:'String',value:findResultAtPosition(data_ar,6)},
				{name:'nieu',type:'String',value:findResultAtPosition(data_ar,7)},
				{name:'bangquang',type:'String',value:findResultAtPosition(data_ar,8)},
				{name:'oxalat',type:'String',value:findResultAtPosition(data_ar,9)},
				{name:'cacbonat',type:'String',value:findResultAtPosition(data_ar,10)},
				{name:'sulphat',type:'String',value:findResultAtPosition(data_ar,11)},
				{name:'photphat',type:'String',value:findResultAtPosition(data_ar,12)},
				{name:'urat',type:'String',value:findResultAtPosition(data_ar,13)},
				{name:'truhat2',type:'String',value:findResultAtPosition(data_ar,14)},
				{name:'tinhtrung',type:'String',value:findResultAtPosition(data_ar,15)},
				{name:'slghc',type:'String',value:findResultAtPosition(data_ar,16)},
				{name:'slgtebaoconhan',type:'String',value:findResultAtPosition(data_ar,17)},
				{name:'bcdoantrungtinhtuy',type:'String',value:findResultAtPosition(data_ar,18)},
				{name:'bclymphotuy',type:'String',value:findResultAtPosition(data_ar,19)},
				{name:'bcmonotuy',type:'String',value:findResultAtPosition(data_ar,20)},
				{name:'cactebaokhactuy',type:'String',value:findResultAtPosition(data_ar,21)},
				{name:'hongcaukhac',type:'String',value:findResultAtPosition(data_ar,22)},
				{name:'bcdoantrungtinhkhac',type:'String',value:findResultAtPosition(data_ar,23)},
				{name:'bclymphokhac',type:'String',value:findResultAtPosition(data_ar,24)},
				{name:'bcmonokhac',type:'String',value:findResultAtPosition(data_ar,25)},
				{name:'cactbkhac',type:'String',value:findResultAtPosition(data_ar,26)}
			];

			openReport('window', 'NTU042_PHIEUXETNGHIEMNUOCDICH_32BV01_QD4069_A4', 'pdf', par);
		}
	}

	// in phiếu trả kết quả hóa sinh máu
	function inPhieu33BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
			var sumoder = data_ar.length;
			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'sumorder',type:'String',value:sumoder},
				{name:'ure',type:'String',value:findResultAtPosition(data_ar,1)},
				{name:'glucose',type:'String',value:findResultAtPosition(data_ar,2)},
				{name:'creatinin',type:'String',value:findResultAtPosition(data_ar,3)},
				{name:'aciduric',type:'String',value:findResultAtPosition(data_ar,4)},
				{name:'bilirubintp',type:'String',value:findResultAtPosition(data_ar,5)},
				{name:'bilirubintt',type:'String',value:findResultAtPosition(data_ar,6)},
				{name:'bilirubingt',type:'String',value:findResultAtPosition(data_ar,7)},
				{name:'proteintp',type:'String',value:findResultAtPosition(data_ar,8)},
				{name:'albumin',type:'String',value:findResultAtPosition(data_ar,9)},
				{name:'globulin',type:'String',value:findResultAtPosition(data_ar,10)},
				{name:'tyleag',type:'String',value:findResultAtPosition(data_ar,11)},
				{name:'fibrinogen',type:'String',value:findResultAtPosition(data_ar,12)},
				{name:'cholesterol',type:'String',value:findResultAtPosition(data_ar,13)},
				{name:'triglycerid',type:'String',value:findResultAtPosition(data_ar,14)},
				{name:'hdl',type:'String',value:findResultAtPosition(data_ar,15)},
				{name:'ldl',type:'String',value:findResultAtPosition(data_ar,16)},
				{name:'na',type:'String',value:findResultAtPosition(data_ar,17)},
				{name:'sat',type:'String',value:findResultAtPosition(data_ar,18)},
				{name:'magie',type:'String',value:findResultAtPosition(data_ar,19)},
				{name:'ast',type:'String',value:findResultAtPosition(data_ar,20)},
				{name:'alt',type:'String',value:findResultAtPosition(data_ar,21)},
				{name:'amylase',type:'String',value:findResultAtPosition(data_ar,22)},
				{name:'ck',type:'String',value:findResultAtPosition(data_ar,23)},
				{name:'ckmb',type:'String',value:findResultAtPosition(data_ar,24)},
				{name:'ldh',type:'String',value:findResultAtPosition(data_ar,25)},
				{name:'ggt',type:'String',value:findResultAtPosition(data_ar,26)},
				{name:'cholinesterase',type:'String',value:findResultAtPosition(data_ar,27)},
				{name:'phosphatase',type:'String',value:findResultAtPosition(data_ar,28)},
				{name:'phdongmanh',type:'String',value:findResultAtPosition(data_ar,29)},
				{name:'pco2',type:'String',value:findResultAtPosition(data_ar,30)},
				{name:'po2',type:'String',value:findResultAtPosition(data_ar,31)},
				{name:'pco3',type:'String',value:findResultAtPosition(data_ar,32)},
				{name:'kiemdu',type:'String',value:findResultAtPosition(data_ar,33)},
				{name:'k',type:'String',value:findResultAtPosition(data_ar,34)},
				{name:'cl',type:'String',value:findResultAtPosition(data_ar,35)},
				{name:'ca',type:'String',value:findResultAtPosition(data_ar,36)},
				{name:'caion',type:'String',value:findResultAtPosition(data_ar,37)},
				{name:'photpho',type:'String',value:findResultAtPosition(data_ar,38)}
			];

			openReport('window', 'NTU017_PHIEUXETNGHIEMHOASINHMAU_33BV01_QD4069_A4', 'pdf', par);
		}
	}

	// in phiếu trả kết quả nước tiểu
	function inPhieu34BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'titrong',type:'String',value:findResultAtPosition(data_ar,1)},
				{name:'ph',type:'String',value:findResultAtPosition(data_ar,2)},
				{name:'bachcau',type:'String',value:findResultAtPosition(data_ar,3)},
				{name:'hongcau',type:'String',value:findResultAtPosition(data_ar,4)},
				{name:'nitrit',type:'String',value:findResultAtPosition(data_ar,5)},
				{name:'protein',type:'String',value:findResultAtPosition(data_ar,6)},
				{name:'glucose',type:'String',value:findResultAtPosition(data_ar,7)},
				{name:'thecetonic',type:'String',value:findResultAtPosition(data_ar,8)},
				{name:'bilirubin',type:'String',value:findResultAtPosition(data_ar,9)},
				{name:'urobilinogen',type:'String',value:findResultAtPosition(data_ar,10)},
				{name:'duongchap',type:'String',value:findResultAtPosition(data_ar,11)},
				{name:'porphyrin',type:'String',value:findResultAtPosition(data_ar,12)},
				{name:'proteinbence',type:'String',value:findResultAtPosition(data_ar,13)},
				{name:'tongthetich',type:'String',value:findResultAtPosition(data_ar,14)},
				{name:'proteintieu',type:'String',value:findResultAtPosition(data_ar,15)},
				{name:'glucosetieu',type:'String',value:findResultAtPosition(data_ar,16)},
				{name:'uretieu',type:'String',value:findResultAtPosition(data_ar,17)},
				{name:'creatinintieu',type:'String',value:findResultAtPosition(data_ar,18)},
				{name:'acidurictieu',type:'String',value:findResultAtPosition(data_ar,19)},
				{name:'amylasetieu',type:'String',value:findResultAtPosition(data_ar,20)},
				{name:'na',type:'String',value:findResultAtPosition(data_ar,21)},
				{name:'k',type:'String',value:findResultAtPosition(data_ar,22)},
				{name:'huyetsacto',type:'String',value:findResultAtPosition(data_ar,23)},
				{name:'stecobilin',type:'String',value:findResultAtPosition(data_ar,24)},
				{name:'stecobilinogen',type:'String',value:findResultAtPosition(data_ar,25)},
				{name:'mautptrongphan',type:'String',value:findResultAtPosition(data_ar,26)},
				{name:'xnkhac',type:'String',value:findResultAtPosition(data_ar,27)},
				{name:'proteintuy',type:'String',value:findResultAtPosition(data_ar,28)},
				{name:'glucosetuy',type:'String',value:findResultAtPosition(data_ar,29)},
				{name:'cloruatuy',type:'String',value:findResultAtPosition(data_ar,30)},
				{name:'pandy',type:'String',value:findResultAtPosition(data_ar,31)},
				{name:'xnkhactuy1',type:'String',value:findResultAtPosition(data_ar,32)},
				{name:'xnkhactuy2',type:'String',value:findResultAtPosition(data_ar,33)},
				{name:'xnkhactuy3',type:'String',value:findResultAtPosition(data_ar,34)},
				{name:'xnkhactuy4',type:'String',value:findResultAtPosition(data_ar,35)},
				{name:'xnkhactuy5',type:'String',value:findResultAtPosition(data_ar,36)},
				{name:'hcltudo',type:'String',value:findResultAtPosition(data_ar,37)},
				{name:'hcltoanphan',type:'String',value:findResultAtPosition(data_ar,38)},
				{name:'xnkhacdichvi',type:'String',value:findResultAtPosition(data_ar,39)},
				{name:'rivalta',type:'String',value:findResultAtPosition(data_ar,40)},
				{name:'proteinkhac',type:'String',value:findResultAtPosition(data_ar,41)},
				{name:'xnkhac1',type:'String',value:findResultAtPosition(data_ar,42)},
				{name:'xnkhac2',type:'String',value:findResultAtPosition(data_ar,43)},
				{name:'xnkhac3',type:'String',value:findResultAtPosition(data_ar,44)},
				{name:'cannuoctieu',type:'String',value:findResultAtPosition(data_ar,45)}
			];

			openReport('window', 'NTU018_PHIEUXETNGHIEMHOASINHNUOCTIEUPHANDICHCHOCDO_34BV01_QD4069_A4', 'pdf', par);
		}
	}
	function inPhieu34BV01HSMD(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'chisot3',type:'String',value:haveTestAtPosition(data_ar, 1)==false?"":"x"},
				{name:'kqt3',type:'String',value:findResultAtPosition(data_ar,1)},
				{name:'chisoft3',type:'String',value:haveTestAtPosition(data_ar, 2)==false?"":"x"},
				{name:'kqft3',type:'String',value:findResultAtPosition(data_ar,2)},
				{name:'chisot4',type:'String',value:haveTestAtPosition(data_ar, 3)==false?"":"x"},
				{name:'kqt4',type:'String',value:findResultAtPosition(data_ar,3)},
				{name:'chisoft4',type:'String',value:haveTestAtPosition(data_ar, 4)==false?"":"x"},
				{name:'kqft4',type:'String',value:findResultAtPosition(data_ar,4)},
				{name:'chisotsh',type:'String',value:haveTestAtPosition(data_ar, 5)==false?"":"x"},
				{name:'kqtsh',type:'String',value:findResultAtPosition(data_ar,5)},
				{name:'chisoantitg',type:'String',value:haveTestAtPosition(data_ar, 6)==false?"":"x"},
				{name:'kqantitg',type:'String',value:findResultAtPosition(data_ar,6)},
				{name:'chisoacth',type:'String',value:haveTestAtPosition(data_ar, 7)==false?"":"x"},
				{name:'kqacth',type:'String',value:findResultAtPosition(data_ar,7)},
				{name:'chisocortisol',type:'String',value:haveTestAtPosition(data_ar, 8)==false?"":"x"},
				{name:'kqcortisol',type:'String',value:findResultAtPosition(data_ar,8)},
				{name:'chisobhcg',type:'String',value:haveTestAtPosition(data_ar, 9)==false?"":"x"},
				{name:'kqbhcg',type:'String',value:findResultAtPosition(data_ar,9)},
				{name:'chisofsh',type:'String',value:haveTestAtPosition(data_ar, 10)==false?"":"x"},
				{name:'kqsofsh',type:'String',value:findResultAtPosition(data_ar,10)},
				{name:'chisolh',type:'String',value:haveTestAtPosition(data_ar, 11)==false?"":"x"},
				{name:'kqlh',type:'String',value:findResultAtPosition(data_ar,11)},
				{name:'chisoprogesterol',type:'String',value:haveTestAtPosition(data_ar, 12)==false?"":"x"},
				{name:'kqprogesterol',type:'String',value:findResultAtPosition(data_ar,12)},
				{name:'chisoestradiol',type:'String',value:haveTestAtPosition(data_ar, 13)==false?"":"x"},
				{name:'kqestradiol',type:'String',value:findResultAtPosition(data_ar,13)},
				{name:'chisotestosterol',type:'String',value:haveTestAtPosition(data_ar, 14)==false?"":"x"},
				{name:'kqtestosterol',type:'String',value:findResultAtPosition(data_ar,14)},
				{name:'chisoprolactin',type:'String',value:haveTestAtPosition(data_ar, 15)==false?"":"x"},
				{name:'kqprolactin',type:'String',value:findResultAtPosition(data_ar,15)},
				{name:'chisoafp',type:'String',value:haveTestAtPosition(data_ar, 16)==false?"":"x"},
				{name:'kqafp',type:'String',value:findResultAtPosition(data_ar,16)},
				{name:'chisocea',type:'String',value:haveTestAtPosition(data_ar, 17)==false?"":"x"},
				{name:'kqcea',type:'String',value:findResultAtPosition(data_ar,17)},
				{name:'chisoca724',type:'String',value:haveTestAtPosition(data_ar, 18)==false?"":"x"},
				{name:'kqca724',type:'String',value:findResultAtPosition(data_ar,18)},
				{name:'chisoca199',type:'String',value:haveTestAtPosition(data_ar, 19)==false?"":"x"},
				{name:'kqca199',type:'String',value:findResultAtPosition(data_ar,19)},
				{name:'chisoher2',type:'String',value:haveTestAtPosition(data_ar, 20)==false?"":"x"},
				{name:'kqher2',type:'String',value:findResultAtPosition(data_ar,20)},
				{name:'chisoca153',type:'String',value:haveTestAtPosition(data_ar, 21)==false?"":"x"},
				{name:'kqca153',type:'String',value:findResultAtPosition(data_ar,21)},
				{name:'chisoca125',type:'String',value:haveTestAtPosition(data_ar, 22)==false?"":"x"},
				{name:'kqca125',type:'String',value:findResultAtPosition(data_ar,22)},
				{name:'chisopsa',type:'String',value:haveTestAtPosition(data_ar, 23)==false?"":"x"},
				{name:'kqpsa',type:'String',value:findResultAtPosition(data_ar,23)},
				{name:'chisocyfra',type:'String',value:haveTestAtPosition(data_ar, 24)==false?"":"x"},
				{name:'kqcyfra',type:'String',value:findResultAtPosition(data_ar,24)},
				{name:'chisonse',type:'String',value:haveTestAtPosition(data_ar, 25)==false?"":"x"},
				{name:'kqnse',type:'String',value:findResultAtPosition(data_ar,25)},
				{name:'chisomicro',type:'String',value:haveTestAtPosition(data_ar, 26)==false?"":"x"},
				{name:'kqmicro',type:'String',value:findResultAtPosition(data_ar,26)},
				{name:'chisoprocal',type:'String',value:haveTestAtPosition(data_ar, 27)==false?"":"x"},
				{name:'kqprocal',type:'String',value:findResultAtPosition(data_ar,27)},
				{name:'chisoinsulin',type:'String',value:haveTestAtPosition(data_ar, 28)==false?"":"x"},
				{name:'kqinsulin',type:'String',value:findResultAtPosition(data_ar,28)},
				{name:'chisopeptid',type:'String',value:haveTestAtPosition(data_ar,29)==false?"":"x"},
				{name:'kqpeptid',type:'String',value:findResultAtPosition(data_ar,29)}
				];
			openReport('window', 'NTU016_PHIEUXETNGHIEM_HOASINH_MIENDICH_A4', 'pdf', par);
		}
	}

	// in phiếu trả kết quả xét nghiệm vi sinh
	function inPhieu35BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));	
			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'NTU019_PHIEUXETNGHIEMVISINH_35BV01_QD4069_A5', 'pdf', par);
		}
	}
	function inPhieu35BV01VINAM(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			openReport('window', 'NTU017_PHIEUXETNGHIEMVINAMNHUOMSOI_33BV01_QD4069_A4', 'pdf', par);
		}
	}

	// in phiếu trả kết quả giải phẫu sinh thiết
	function inPhieu36BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'NTU043_PHIEUXETNGHIEMGIAIPHAUBENHSINHTHIET_36BV01_QD4069_A4', 'pdf', par);
		}
	}

	// in phiếu trả kết quả giải phẫu sinh thiết hà nam
	function inPhieu36BV01_(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'NTU043_PHIEUXETNGHIEMGIAIPHAUBENHSINHTHIET_36BV01_QD4069_TH_A4', 'pdf', par);
		}
	}

	// in phiếu trả kết quả khám nghiệm tử thi
	function inPhieu37BV01(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,3000);
		}
		else {
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];

			openReport('window', 'NTU044_PHIEUXNGIAIPHAUBENHKHAMNGHIEMTUTHI_37BV01_QD4069_A4','pdf', par);
		}
	}
	//nghiant 26062017
	function showButtonSuaNgayTraKQ(show){
		var checkQuyenSuaNgayTraKQ = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS02X009.KTQSNTKQ1",$("#hdfIDMauBenhPham").val());
		if(showNutSuaNgayTraKQ!=undefined && showNutSuaNgayTraKQ!=null){
			if(showNutSuaNgayTraKQ==1){								
					if(show==true){	
						if(checkQuyenSuaNgayTraKQ==1){ 						
							$("#btnEditTime").show();
						}
						else{
							$("#btnEditTime").hide();
						}						
					}else{
						$("#btnEditTime").hide();
					}
				}		
			
		}			
	};

	function showButtonSuaNgayTN(show){
		if(showNutSuaNgayTN!=undefined && showNutSuaNgayTN!=null){
			if(showNutSuaNgayTN==1){
				if(show==true){				
					$("#btnSuaNgayTN").show();					
				} else {
					$("#btnSuaNgayTN").hide();
				}		
			}
		}			
	}
	
	//nghiant 30082017
	function checkPhongThucHien(PhThucHienArray,id_phongchuyenden){
		var checkP = false;
		for (i = 0; i < PhThucHienArray.length; i++) {
			if(PhThucHienArray[i] ==id_phongchuyenden){
				checkP = true;
				break;
			}
		}
		return checkP;
	};

	// TruongLe 16/12/2019
	function getParamsByOrderal(data_ar, thutubhyt) {
		return thutubhyt
			.map(putValueByPosition.bind({data_ar: data_ar}))
			.concat(thutubhyt.filter(function (el) {
				return el.mark && el.mark != null && el.mark != ""
			}).map(putMarkByPosition.bind({data_ar: data_ar})))
	}

	function putValueByPosition(param) {
		var result = "";
		if (this.data_ar.length && this.data_ar.length > 0) {
			result = this.data_ar
				.filter(function(elm) {return elm.THUTUINMAUBYT == param.position})
				.map(function(el){return el.GIATRI_KETQUA}).shift()
		}
		return {name: param.name, type:'String', value: result ? result : ''}
	}

	function putMarkByPosition(param) {
		var result = "";
		if (this.data_ar.length && this.data_ar.length > 0) {
			result = this.data_ar
				.find(function(elm) { return elm.THUTUINMAUBYT == param.position })
		}
		return {name: param.mark, type:'String', value: result ? "x" : ""}
	}
	// END TruongLe 16/12/2019
}

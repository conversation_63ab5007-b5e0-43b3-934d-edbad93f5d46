/**
Form này sinh ra là để phục vụ cho BVBD Hà Nội
*/

var ctl_ar = [
	{type:'buttongroup',id:'btnInPhieu',icon:'print',text:' In phiếu',
	children:[
		{id:'btnInPhieu19BV01',icon:'print',text:'Phiếu <PERSON> X-Quang',hlink:'#'},
		{id:'btnInPhieu20BV01',icon:'print',text:'<PERSON>ếu <PERSON>p Chụp Cắt Lớp',hlink:'#'},
		{id:'btnInPhieu21BV01',icon:'print',text:'Phiếu Chụp Cộng Hưởng Từ',hlink:'#'},
		{id:'btnInPhieuLoangXuong',icon:'print',text:'Phiếu <PERSON>o <PERSON>',hlink:'#'},
		{id:'btnPhieuIn27BV01TEST_TAMLY', icon:'print', text:'Phiế<PERSON> test tâm lý',hlink:'#'},// nghiant HISL2TK-571
		{id:'btnPhieuIn27BV01TV_TAMLY', icon:'print', text:'Phiếu tham vấn tâm lý',hlink:'#'},// nghiant HISL2TK-571
		{id:'btnInPhieuBoiDuongPTTT',icon:'print',text:'In Phiếu bồi dưỡng PTTT',hlink:'#'},
		{id:'btnInPhieuTTBoiDuongPTTT',icon:'print',text:'In Phiếu thanh toán bồi dưỡng PTTT',hlink:'#'},
		{id:'btnInPhieu22BV01',icon:'print',text:'Phiếu Siêu Âm',hlink:'#'},
		{id:'btnInPhieu27BV01',icon:'print',text:'Phiếu Siêu Âm Màu',hlink:'#'},
		{id:'btnInPhieu31BV01',icon:'print',text:'Phiếu Siêu Âm DOPPLER',hlink:'#'},
		{id:'btnInPhieu34BV01',icon:'print',text:'Phiếu Siêu Âm mẫu 2',hlink:'#'},
		{id:'btnInPhieu23BV01',icon:'print',text:'Phiếu Điện Tim',hlink:'#'},
		{id:'btnInPhieu36BV01',icon:'print',text:'Phiếu Điện Tim có header',hlink:'#'},
		{id:'btnInPhieu24BV01',icon:'print',text:'Phiếu Điện Não',hlink:'#'},
		{id:'btnInPhieu25BV01',icon:'print',text:'Phiếu Nội Soi',hlink:'#'},
		{id:'btnInPhieuNSMAU2',icon:'print',text:'Phiếu Nội Soi mẫu 2',hlink:'#'},
		{id:'btnInPhieuKQCDHA',icon:'print',text:'Phiếu KQ CĐHA KỸ THUẬT VIÊN',hlink:'#'},
		{id:'btnInPhieuKQCDHA_BS',icon:'print',text:'Phiếu KQ CĐHA BÁC SĨ',hlink:'#'},
		{id:'btnInPhieu25BV01CoTuCung',icon:'print',text:'Phiếu Nội Soi Cổ Tử Cung',hlink:'#'},
		{id:'btnInPhieu25BV01MuiXoang',icon:'print',text:'Phiếu Nội Soi Mũi Xoang',hlink:'#'},
		{id:'btnInPhieu33BV01',icon:'print',text:'Phiếu Fibroscan',hlink:'#'},
		{id:'btnInPhieu25BV01TieuHoa',icon:'print',text:'Phiếu Nội Soi tiêu hóa',hlink:'#'},
		{id:'btnInPhieuTMH74',icon:'print',text:'Phiếu Nội Soi Tai mũi họng',hlink:'#'},
		{id:'btnInPhieuNSPQ74',icon:'print',text:'Phiếu Nội Soi Phế quản',hlink:'#'},
		{id:'btnInPhieuNSPQ2',icon:'print',text:'Phiếu Nội Soi Phế quản mẫu 2',hlink:'#'},
		{id:'btnInPhieuNSDTT',icon:'print',text:'Phiếu Nội Soi Đại trực tràng',hlink:'#'},
		{id:'btnInPhieu26BV01',icon:'print',text:'Phiếu Đo Chức Năng Hô Hấp',hlink:'#'},
		{id:'btnInPhieu26BV01LuuHuyetNao',icon:'print',text:'Phiếu Đo Lưu Huyết Não',hlink:'#'},
		{id:'btnInPhieu28BV01',icon:'print',text:'Phiếu Đo Thị Trường Trung Tâm',hlink:'#'},
		{id:'btnInPhieu29BV01',icon:'print',text:'Phiếu Chụp Đáy Mắt Không Huỳnh Quang',hlink:'#'},
		{id:'btnInPhieu30BV01',icon:'print',text:'Phiếu Kết Quả Đo JAVAL',hlink:'#'},
		{id:'btnInPhieu32BV01',icon:'print',text:'Phiếu Kết Quả Điện cơ',hlink:'#'},
		{id:'btnInPhieu35BV01',icon:'print',text:'Phiếu Kết Quả Điện cơ có header',hlink:'#'},
		{id:'btnInPhieu37BV01',icon:'print',text:'Phiếu Kết Quả đo cơ tay',hlink:'#'},
		{id:'btnInPhieuARFI',icon:'print',text:'Phiếu Đo Độ Đàn Hồi Gan ARFI',hlink:'#'},
		{id:'btnInPhieuSieuAm4D',icon:'print',text:'Phiếu siêu âm 4D',hlink:'#'},   //20180917 - duongnv - them phieu sieu am 4D
		{id:'btnInPhieuTestTho',icon:'print',text:'Phiếu Test Thở',hlink:'#'}

	]},
	{type:'button',id:'btnGoiKham',icon:'volume-up',text:'Gọi BN'},
	{type:'button',id:'btnXuatManHinh',icon:'dsbenhnhan',text:' DS BN'},
	{type:'button',id:'btnThongKe',icon:'list-alt',text:' Thống kê'},
	{type:'textbox',id:'txtTimKiem',icon:'remove',text:'Nhập số phiếu để tìm kiếm'},
	{type:'button', id:'btnDichVu', icon:'nhapkho', text:' Dịch vụ'},
	{type:'button',id:'btnDSBN',icon:'list-alt',text:'TKBN'},
	{type:'button', id:'btnXuatExcel', icon:'glyphicon glyphicon-floppy-save', text:' Xuất Excel'},	
	{
		type : 'buttongroup',
		id : 'btnhistory',
		icon : 'lichsu',
		text : 'Lịch sử',
		hlink : '#',
		children : [ {
			id : 'history_1',
			icon : 'lichsu',
			text : 'Quản lý bệnh án',
			hlink : '#'
		}, {
			id : 'history_2',
			icon : 'lichsu',
			text : 'Lịch sử điều trị',
			hlink : '#'
		} ]
	},
	{type:'label',id:'lblInfo',icon:'',text:'Chẩn Đoán Hình Ảnh'}
];
var phongthId = null;
function DanhSachCDHA(opt) {	
	this.load = doLoad;
	
	var tab1Click = 0; var tab2Click = 0; var tab3Click = 0; var tab4Click = 0;
	var searchType = 0;
	
	var showNutLuuLai = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'AN_NUT_LUU_CDHA');
	var showNutSuaNgayTN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIEN_NUT_SUA_NGAY_TN_CDHA');
	
	//nghiant 26062017 
	var showNutSuaNgayTraKQ = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'AN_NUT_SUA_NGAY_TRA_KQ_CDHA');
	var tiepnhanid ='';
	var benhnhan_id ='';
	
	//nghiant 02082017 
	var khoa_chuyen_den_id = '';
	var phong_chuyen_den_id = '';
	
	//nghiant 30082017
	var PhThucHienArray = new Array();
	var rolePTH = false;
	var dsMaMay = [];		// mang du lieu luu tru Ma may  va html combobox ma may ; 
	
	//nghiant VNPTHISL2-490 25092017
	var sdtBN = "";
	var tenBN = "";
	var sophieuBN = "";
	var traintungPhieu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TRA_INTUNGPHIEU_CDHA');	
	var onOffSendSMS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'ON_OFF_SMS_KQCDHA');
	//end nghiant VNPTHISL2-490 25092017

	var b_min_time = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_MIN_RUNTIME');
	var b_min_exec = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_MIN_EXEC_TIME');
	var b_minu = 0;
	var b_exec = "";
	if(b_min_exec.includes(":")){
		var b_minarr = b_min_exec.split(':');
		var b_minu = b_minarr[0];
		var b_exec = b_minarr[1];
	}
	var b_time_print = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_CANHBAO_TGTH');
	var b_time_printarr = b_time_print.split(":");
	var minCheckInPhieu = 0;
	var minTimeInPhieu = 15;
	var minGroup0InPhieu = "";
	if(b_time_printarr[0]!=undefined) minCheckInPhieu = b_time_printarr[0];
	if(b_time_printarr[1]!=undefined) minTimeInPhieu = b_time_printarr[1];
	if(b_time_printarr[2]!=undefined) minGroup0InPhieu = b_time_printarr[2];
	
	// SONDN
	var HIS_API_TIMEOUT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_API_TIMEOUT');
	var HIS_API_MODE = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_API_MODE');
	var RIS_MODE_CALL_PATIENT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'RIS_MODE_CALL_PATIENT');
	// END SONDN; 
	
	//cau hinh show tab DSDCD (=1 show, != 1 not show )
	var showDSDCD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_ON_OFF_DSDV_DA_CHIDINH_CDHA');	

	//cau hinh an danh sach phieu thuoc di kem  L2PT-4222
	var annutDSThuocDiKem = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_AN_DSTHUOCDIKEM_CDHA');	
	if(annutDSThuocDiKem=="1"){
		$("#DSPTVT_KEM").remove();
	}
	
	//tuyendv lay ds ma dich vu khong cho sua bang
	var resizingTable = "0";
	var _dsMADV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_DS_MADICHVU_KHONGSUABANG');
	//end
	
	var thuocvatu_dikem = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_THUOC_VATTU_DIKEM');	
	var trangthaibp = "0";
	var chan_tvt_dikem = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_CHAN_TVT_DIKEM');
	var sophieu_datra = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CLS_SOPHIEU_DATRA');
    var i_hid = opt._param[0];
    
	var chantiepnhan = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_CHAN_TIEPNHAN_CHUATHUTIEN');
	
//	// 10/03/2021
//	var optKySo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_SUDUNG_KYSO_KYDIENTU');
//	var causer = "";
//	var capassword = "";
	
	var userRights = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'RIS_MODE_CONTROL');

	var showForm = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_FORM_CDHA');

    // ChienDV START L2PT-82498
    let dlgPopup = "";
    // ChienDV END L2PT-82498
	
	function doLoad() {
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = lang;
		
		// khởi tạo các tab
		$("li[id^='tab']").on("click", function (e) {
			var tabName = $(this).attr("id").substr(3);
			$("li[class='active']").removeClass("active");
			$(this).addClass("active");
			$("div[class='tab active']").removeClass("active");
			$("#div" + tabName).addClass("active");
		});
		// check quyền
		/*$(document).ready(function () {
            let callback = function () {
                if ($.active !== 0) {
                    setTimeout(callback, '200');
                    return;
                }
                checkRole();
            };
            callback();
        });
		*/
		
		
		// khởi tạo các control
		initControl();
		bindEvent();

		// load dữ liệu lên các control
		$("#txtTuNgay").val(moment().format('DD/MM/YYYY'));
		$("#txtDenNgay").val(moment().format('DD/MM/YYYY'));

        // ChienDV START L2PT-82498
        if (hospital_id == 10284){
            $("#KTTSDU").removeClass("hidden");
            $("#XNBETAHCG").removeClass("hidden");
        }
        // ChienDV END L2PT-82498

		var sql_par = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+opt.dept_id;
		ComboUtil.getComboTag("cboPhongThucHien", "CLS02C001.KP", sql_par, "", "", "sp","",function(){
			$('#cboPhongThucHien option[value="'+opt.subdept_id+'"]').attr('selected','selected');
			
			//nghiant 30082017
			var PTH = document.getElementById('cboPhongThucHien');
			for (var i = 0; i < PTH.options.length; i++) {
				PhThucHienArray[i] = PTH.options[i].value;
			}
			console.log("PhThucHienArray_CDHA: "+PhThucHienArray);
			//end nghiant 3008201
		});
//		var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USERNAME,20,0,f,l;Tên bác sỹ,FULLNAME,30,0,f,l;Chức danh,CHUCDANH,50,0,f,l";
//		var sql_parKTV=[];
//		sql_parKTV.push({"name":"[0]","value":0},{"name":"[1]","value":opt.dept_id});
//		ComboUtil.initComboGrid("txtTKPHUMO1","PTTT.LOAD_USER",sql_parKTV,"600px",_col_loaduser, function(event, ui) {
//			  $("#txtTKPHUMO1").val("");
//		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+' ('+ui.item.USERNAME+')</option>');
//		      $("#cboPHUMO1").empty();
//		      $("#cboPHUMO1").append(option);
//		      return false;
//		});

		$('#toolbarIdtxtTimKiem').focus();

		var callback = function () {
            if ($.active !== 0) {
                setTimeout(callback, '100');
                return;
            }
            reloadAllGrid();
        };
        callback();
        
        loadRISConfig();
        
        if(userRights=="1"){
			$("#btnHuyDichVu").hide();
			$("#btnKhoiPhuc").hide();
			$("#btnBoSung").hide();
			$("#toolbarIdPTTT").hide();
			
			$("#btnHenNgay").hide();
			$("#btnTiepNhan").hide();
			$("#btnHuyTiepNhan").hide();
//			$("#btnLuuKetQua").hide();
			
			$("#btnHenTraKetQua").hide();
			$("#btnTraKetQua").hide();
			$("#btnSuaNgayTN").hide();
			$("#btnEditTimeCDHA").hide();
			$("#btnHuyKetQua").hide();
			
			$("#btnKySo").hide();
			$("#btnHuyKySo").hide();
		}
        
	}

	function initControl() {
        // ==================== START >> In Phiếu New ==================== truongle - 26/11/2019
		var HIDE_MENU_INPHIEU_CLS_OLD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIDE_MENU_INPHIEU_CLS_OLD');
		if (HIDE_MENU_INPHIEU_CLS_OLD == '1') {
			ctl_ar[0].children = []
		}
		var menus = jsonrpc.AjaxJson.ajaxCALL_SP_O("TOOLBAR_CLS_INPHIEU",'2');
		if (menus && menus.length > 0) {
			var childsInPhieuNew = menus.map(function(item) {
				var optionalParam = {};
				try { optionalParam = JSON.parse($("<div/>").html(item.OPTIONAL_PARAM).text()); } catch(e) { optionalParam = {} }
				return {
					id:'btnInPhieu_new_' + item.ID,
					icon:'print',
					text: item.TEXT,
					order: parseInt(item.ORDER),
					hlink:'#',
					dataExternal: {
						report_code: item.REPORT_CODE,
						file_format: item.FILE_FORMAT,
						image_sql: item.IMAGES_CTL_SQL,
						image_count: item.IMAGES_COUNT,
						innhieu_phieu: item.INNHIEU_PHIEU,
						optional_param: optionalParam
					}
				}
			})
			ctl_ar[0].children = ctl_ar[0].children.concat(childsInPhieuNew)
		}
		// ==================== END >> In Phiếu New ==================== truongle - 26/11/2019
		ToolbarUtil.build('toolbarId', ctl_ar);
		
		$("#toolbarIdbtnInPhieu").attr("disabled", true);
		
		var ctl_nt_ar=[			
			{type:'button',id:'print_1',icon:'print',text:'In phiếu PTTT',cssClass:'wd150'},
			{type:'button',id:'btnUpdatePttt',icon:'xutri',text:'Cập nhật PTTT',cssClass:'wd150'},
			{type:'buttongroup',id:'btnhospitalfee',icon:'thutien',text:'Phụ thu',cssClass:'wd150',
				children:[
		           {id:'hospitalfee_1',icon:'thutien',text:'Tạo phiếu phụ thu',hlink:'#'},
		           {id:'hospitalfee_2',icon:'thutien',text:'Danh sách phiếu phụ thu',hlink:'#'}
		        ]}
		];
		var showtoolbarpttt = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_SHOW_TOOLBAR_PTTT');
		if(showtoolbarpttt=="1") ToolbarUtil.build('toolbarIdPTTT',ctl_nt_ar);
		
		var showBtnService = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_CHIDINH_PTH');
		if(showBtnService != 1){
			$('#toolbarIdbtnDichVu').hide();
		}
		
		var showBtnBoSung = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_BUTTON_BOSUNG_DICHVU');
		if(showBtnBoSung == "1"){
			$('#btnBoSung').show();
		}

		var showBtnTKBN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_SHOW_BUTTON_TKBN');
		if(showBtnTKBN != "1"){
			$('#toolbarIdbtnDSBN').hide();
		}
		
		
		
		//start tuyendv L2K74TW-38
		var showBtnExportExcel = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_BUTTON_EXPORT_EXCEL');
		if(showBtnExportExcel != 1){
			$('#toolbarIdbtnXuatExcel').hide();
		}
		//end tuyendv L2K74TW-38
		
		//hien thi so phieu da tra
		if(sophieu_datra!=null && sophieu_datra==1){
			var _sophieu=jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS.SOPHIEU.DATRA",2);			
			$("#rdoTraKetQua").parent().html($("#rdoTraKetQua").parent().html()+" (<font color='blue'><b>"+ _sophieu + "</b></font>)");
		}
		
		$("#grdDanhSachCDHA").jqGrid({
			datatype : 'local',
			colNames : ['','STT','Thừa/thiếu','Số phiếu',
						'Tên BN','Giới tính','Tuổi','Mã thẻ BHYT','Dịch vụ','Ghi chú','TVT đi kèm','Khoa','Phòng',
			            'TG chỉ định', 'TG thực hiện',
			            'TRANGTHAI','BENHNHANID','MAUBENHPHAMID','DICHVUKHAMBENHID','NGAYMAUBENHPHAM','TRANGTHAIMAUBENHPHAM','TRANGTHAIKETQUA','KETQUACLSID', 'MA_BHYT',
			            'TRANGTHAIDULIEUMAUBENHPHAM','KHOACHUYENDENID','PHONGCHUYENDENID','CHITIETGHICHU','TIEPNHANID','LOAITIEPNHANID','GHICHU','THOIGIANDUKIENCOKETQUA','TRANGTHAITIEPNHAN'],
			colModel: [
			    {name:'ICON', width: 20, search: false },
				{name:'SOTHUTU', width: 35, align: "center" ,search: false},
				{name:'SOTIEN', width: 80, align: "right" },
				{name:'SOPHIEU', width: 100, align: "center" },				
				{name:'TENBENHNHAN', width: 170, formatter:GridUtil.fm.formatStrEncode },
				{name:'GIOITINH', width: 60, align: "center", formatter:GridUtil.fm.formatStrEncode },
				{name:'TUOI', width: 60, align: "center", formatter:GridUtil.fm.formatStrEncode },
				{name:'MA_BHYT', width: 100, formatter:GridUtil.fm.formatStrEncode },
				{name:'TENDICHVU', width: 380, formatter:GridUtil.fm.formatStrEncode },
				{name:'GHICHU', width: 150, formatter:GridUtil.fm.formatStrEncode },
				{name:'VATTUDIKEM', width: 150, formatter:GridUtil.fm.formatStrEncode },
				{name:'TENKHOA', width: 150, formatter:GridUtil.fm.formatStrEncode },
				{name:'TENPHONG', width: 150, formatter:GridUtil.fm.formatStrEncode },				
				{name:'NGAYMAUBENHPHAM', width: 100, align: "center", formatter:GridUtil.fm.formatStrEncode },
				{name:'NGAYMAUBENHPHAM_HOANTHANH', width: 120, align: "left"},				
				{name:'TRANGTHAI', width: 90, formatter:GridUtil.fm.formatStrEncode, hidden: true  },
				{name:'BENHNHANID', hidden: true },
				{name:'MAUBENHPHAMID', hidden: true },
				{name:'DICHVUKHAMBENHID', hidden: true },
				{name:'NGAYMAUBENHPHAM', hidden: true },
				{name:'TRANGTHAIMAUBENHPHAM', hidden: true },
				{name:'TRANGTHAIKETQUA', hidden: true },
				{name:'KETQUACLSID', hidden: true },
				{name:'MA_BHYT', hidden: true },
				{name:'TRANGTHAIDULIEUMAUBENHPHAM', hidden: true },
				{name:'KHOACHUYENDENID', hidden: true },
				{name:'PHONGCHUYENDENID', hidden: true },
				{name:'CHITIETGHICHU', hidden: true },
				{name:'TIEPNHANID', hidden: true },//nghiant 26062017
				{name:'LOAITIEPNHANID', hidden: true },//huongpv add 2442018
				{name:'GHICHU', hidden: true }, //truongle add 29102019
				{name:'THOIGIANDUKIENCOKETQUA', hidden: true },
				{name:'TRANGTHAITIEPNHAN', hidden: true }
			],
			rowNum: 20,
			rowList: [20, 50, 100],
			pager: '#pager_grdDanhSachCDHA',
			ignoreCase: true,
			rownumbers: true,
			viewrecords: true,
			//sortorder: 'desc',
			width:1136,
	        shrinkToFit: false,
			editurl: 'clientArray',
			caption: 'Danh sách bệnh nhân'
		});
		$("#grdDanhSachCDHA").jqGrid('navGrid','#pager_grdDanhSachCDHA',{edit:false,add:false,del:false});
		$("#grdDanhSachCDHA").jqGrid('filterToolbar',{ignoreCase:true,stringResult:true,searchOnEnter:false,defaultSearch:"cn"});
		$('#gview_grdDanhSachCDHA').find('.ui-jqgrid-bdiv').attr("style","height: 360px !important");
		GridUtil.setWidthPercent("grdDanhSachCDHA","100%");
		
	}

	function bindEvent() {
		$.jMaskGlobals = {
			maskElements: 'input,td,span,div',
			dataMaskAttr: '*[data-mask]',
			dataMask: true,
			watchInterval: 300,
			watchInputs: true,
			watchDataMask: true,
			byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
			translation: {
				'0': {pattern: /\d/},
				'9': {pattern: /\d/, optional: true},
				'#': {pattern: /\d/, recursive: true},
				'A': {pattern: /[a-zA-Z0-9]/},
				'S': {pattern: /[a-zA-Z]/}
			}
		};
		
		$(':input').keydown(function (e) {
			if (e.which === 13) {
				var id = $(this).attr('id');
				if(id == "rdoChoTiepNhan"){
					$("#cboPhongThucHien").focus();
				}
				else if(id == "rdoTraKetQua"){
					$("#cboPhongThucHien").focus();
				}
				else if (id == "cboPhongThucHien"){
					$("#txtTuNgay").focus();
				}
				else if (id == "txtTuNgay"){
					$("#txtDenNgay").focus();
				}
			}
		});
		
		var f3 = 114;
        $(document).unbind('keydown').keydown(function (e) {                      
        	if (e.keyCode == f3) {
            	e.preventDefault();
                $("#toolbarIdtxtTimKiem").focus();
            }
        });

		$('#toolbarIdbtnDichVu').on('click', function () {				
			var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
			if(selRowId == null || selRowId == ''){
				DlgUtil.showMsg('Chưa chọn phiếu dịch vụ');
				return;
			}
			var rowData =  $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowData.MAUBENHPHAMID);
			if (data_ar != null && data_ar.length > 0) {
				if(data_ar[0].TRANGTHAITIEPNHAN != '0'){
					DlgUtil.showMsg('Bệnh nhân đã kết thúc điều trị. Không thể kê thêm dịch vụ');
					return;
				} else {
					paramInput={
							benhnhanid : data_ar[0].BENHNHANID,
							mabenhnhan : data_ar[0].MABENHNHAN,
							khambenhid : data_ar[0].KHAMBENHID,										
							tiepnhanid : data_ar[0].TIEPNHANID,
							hosobenhanid : data_ar[0].HOSOBENHANID,
							doituongbenhnhanid : data_ar[0].DOITUONGBENHNHANID,
							loaitiepnhanid : data_ar[0].LOAITIEPNHANID,
							subDeptId : opt.subdept_id,
							deptId : opt.dept_id
							
					};			
					dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5,paramInput,"Tạo phiếu chỉ định dịch vụ",1300,600);
					DlgUtil.open("divDlgDichVu");
				}
			}
		});
		
		//start tuyendv L2K74TW-38
		$('#toolbarIdbtnXuatExcel').on('click', function () {			
			var typeIn = 'XLS';
			var _param = [ {
		         name : 'phongthuchienid',
		         type : 'String',
		         value : $("#cboPhongThucHien").val()
    			},
    			{
			         name : 'tungay',
			         type : 'String',
			         value : $("#txtTuNgay").val()
    			},
    			{
			         name : 'denngay',
			         type : 'String',
			         value : $("#txtDenNgay").val()
    			},
    			{
			         name : 'trangthai',
			         type : 'String',
			         value : $("input[name=rdoTrangThai]:checked").val()
    			}
			];
			
			CommonUtil.inPhieu('window', 'BC_DSBN_THUCHIENCLS', typeIn, _param, 'baocaothongke.xls');
		});
		//end tuyendv L2K74TW-38
		
		//calback cho man hinh chi dinh dich vu
		EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function(e) {
			DlgUtil.close("divDlgDichVu");
			DlgUtil.showMsg(e.msg);	
			reloadAllGrid();
			searchType = 0;
		});	
		
		$("#grdDanhSachCDHA").jqGrid("setGridParam", {
			onSelectRow : function (rowid) {
				var row = $("#grdDanhSachCDHA").jqGrid('getRowData', rowid);
				
				$("#hdfIDMauBenhPham").val(row.MAUBENHPHAMID);
				$("#hdfIDDichVuKB").val(row.DICHVUKHAMBENHID);
				$("#hdfSoPhieu").val(row.SOPHIEU);
				$("#hdfIDGhiChu").val(row.GHICHU);
				$("#hdfNgayDichVu").val(row.NGAYDICHVU);
				$("#hdfMAHOSOBENHAN").val(row.MAHOSOBENHAN);
				$("#hdfIDKetQuaCLS").val(row.KETQUACLSID);
				//nghiant 26062017
				tiepnhanid = row.TIEPNHANID;
				benhnhan_id = row.BENHNHANID;
				khoa_chuyen_den_id = row.KHOACHUYENDENID;
				phong_chuyen_den_id = row.PHONGCHUYENDENID;
				trangthaibp = row.TRANGTHAIMAUBENHPHAM;
				
				//nghiant 30082017
		        rolePTH = checkPhongThucHien(PhThucHienArray, phong_chuyen_den_id);
		        //end nghiant 
		        
		        //nghiant VNPTHISL2-490 22092017
                tenBN = row.TENBENHNHAN;
                sophieuBN = row.SOPHIEU;
                if(onOffSendSMS == "1"){
                	sdtBN=jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CH_SDT", row.MABENHNHAN);
                }
                console.log('hdfIDMauBenhPham: '+$("#hdfIDMauBenhPham").val()+', tiepnhanid: '+tiepnhanid+', benhnhan_id: '+benhnhan_id+', phong_chuyen_den_id: '+phong_chuyen_den_id+', sdtBN: '+sdtBN);
                //end nghiant VNPTHISL2-490 22092017
				
				// ẩn hiện nút xem ảnh DICOM
				studyInstanceUID = row.CHITIETGHICHU;
				console.log("studyInstanceUID=" + studyInstanceUID);
				if(studyInstanceUID != "" && RIS_SERVICE_DOMAIN_NAME != "" && RIS_GET_DICOM_VIEWER != ""){
					$("#btnDicomViewer").show();
				} else {
					$("#btnDicomViewer").hide();
				}
				
				// tô màu dòng được chọn
				GridUtil.unmarkAll("grdDanhSachCDHA");
	    		GridUtil.markRow("grdDanhSachCDHA", rowid);
	    		
	    		if(userRights!="1"){
		    		// ẩn hiện nút xử lý
		    		if(row['TRANGTHAIKETQUA']=="1"){
						$("#btnHenNgay").show();
						$("#btnTiepNhan").show();
						$("#btnHuyTiepNhan").hide();
						$("#btnHenTraKetQua").hide();
						$("#btnTraKetQua").hide();
	
						//nghiant 26062017 
//						showButtonLuuKetQua(false);
						showButtonSuaNgayTN(false);
						showButtonSuaNgayTraKQ(false);
						
						$("#btnHuyKetQua").hide();
						$("#btnInPhieu").hide();
						$("#toolbarIdbtnInPhieu").attr("disabled", false);
						
//						grid.setColProp('GIATRI_KETQUA',{editable:false});
//						grid.setColProp('GHICHUKQ',{editable:false});
	
//						$("#btnKySo").hide();
//						$("#btnHuyKySo").hide();
//						$('#btnInPhieuKy').hide();
					} else if(row['TRANGTHAIKETQUA']=="3" || row['TRANGTHAIKETQUA']=="4"){
						$("#btnHenNgay").hide();
						$("#btnTiepNhan").hide();
						$("#btnHuyTiepNhan").show();
						$("#btnHenTraKetQua").show();
						$("#btnTraKetQua").show();
	
						//nghiant 26062017 
//						showButtonLuuKetQua(true);
						showButtonSuaNgayTN(false);
						showButtonSuaNgayTraKQ(false);
						
						$("#btnHuyKetQua").hide();
						$("#btnInPhieu").show();
						showButtonInPhieu(row["NGAYDICHVU"]);
						
						checkRole();
						
//						grid.setColProp('GIATRI_KETQUA',{editable:true});
//						grid.setColProp('GHICHUKQ',{editable:true});
	
//						$("#btnKySo").hide();
//						$("#btnHuyKySo").hide();
//						$('#btnInPhieuKy').hide();
					} else if(row['TRANGTHAIKETQUA']=="7"){
						$("#btnHenNgay").hide();
						$("#btnTiepNhan").hide();
						$("#btnHuyTiepNhan").hide();
						$("#btnHenTraKetQua").hide();
						$("#btnTraKetQua").hide();
	
						//nghiant 26062017 
//						showButtonLuuKetQua(false);
						showButtonSuaNgayTN(true);
						showButtonSuaNgayTraKQ(true);
						
						$("#btnHuyKetQua").show();						
						$("#btnInPhieu").show();					
						showButtonInPhieu(row["NGAYDICHVU"]);
						
						checkRole();
						
//						grid.setColProp('GIATRI_KETQUA',{editable:false});
//						grid.setColProp('GHICHUKQ',{editable:false});
						
//						// ẩn hiện nút ký số
//						if(optKySo=="1"||optKySo=="2"){
//							var param = [row.MAUBENHPHAMID, row.DICHVUKHAMBENHID];
//							var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", param.join("$"));
//							if(data_ar!=undefined && data_ar!=null){
//								if(data_ar.length > 0){
//									var trangthai = data_ar[0]["TRANGTHAI"];
//									if(trangthai==""||trangthai=="0"||trangthai=="2"){
//										$("#btnSuaNgayTN").attr("disabled", false);
//										$("#btnEditTimeCDHA").attr("disabled", false);
//										$("#btnHuyKetQua").attr("disabled", false);
//										
//										$("#btnKySo").show();
//										$("#btnHuyKySo").hide();
//										$('#btnInPhieuKy').show();
//										
//										$("#btnInPhieuKy").attr("disabled", true);
//									}
//									else if(trangthai=="1"){
//										$("#btnSuaNgayTN").attr("disabled", true);
//										$("#btnEditTimeCDHA").attr("disabled", true);
//										$("#btnHuyKetQua").attr("disabled", true);
//	
//										$("#btnKySo").hide();
//										$("#btnHuyKySo").show();
//										$('#btnInPhieuKy').show();
//	
//										$("#btnInPhieuKy").attr("disabled", false);
//									}
//								}
//								else {
//									$("#btnKySo").show();
//									$("#btnHuyKySo").hide();
//									$('#btnInPhieuKy').show();
//									
//									$("#btnInPhieuKy").attr("disabled", true);
//								}
//							}
//						}
//						else {
//							$("#btnKySo").hide();
//							$("#btnHuyKySo").hide();
//							$('#btnInPhieuKy').hide();
//						}
//						// hết ẩn hiển nút ký số
					}
	    		}

				//nghiant 28082017
				if(!rolePTH){
					$("#btnTiepNhan").hide();
					$("#btnHuyTiepNhan").hide();
//					$("#btnLuuKetQua").hide();
					$("#btnTraKetQua").hide();
					$("#btnEditTimeCDHA").hide();
					
					showButtonSuaNgayTraKQ(false);
				}
				//end nghiant 28082017 	

				if(row.TRANGTHAITIEPNHAN>0){
	                $('#TAOPHIEUTHUOCKEM_HAOPHI').attr("disabled","disabled").addClass('ui-state-disabled');
	                $('#TAOPHIEUVATTUKEM_HAOPHI').attr("disabled","disabled").addClass('ui-state-disabled');
	                $('#TAOPHIEUTHUOCKEM').attr("disabled","disabled").addClass('ui-state-disabled');
	                $('#TAOPHIEUVATTUKEM').attr("disabled","disabled").addClass('ui-state-disabled');
				} else {
					$('#TAOPHIEUTHUOCKEM_HAOPHI').removeAttr("disabled").removeClass('ui-state-disabled');
	                $('#TAOPHIEUVATTUKEM_HAOPHI').removeAttr("disabled").removeClass('ui-state-disabled');
	                $('#TAOPHIEUTHUOCKEM').removeAttr("disabled").removeClass('ui-state-disabled');
	                $('#TAOPHIEUVATTUKEM').removeAttr("disabled").removeClass('ui-state-disabled');
				}			
			},
			ondblClickRow : function (rowid) {
				var row = $("#grdDanhSachCDHA").jqGrid('getRowData', rowid);

				if (row['TRANGTHAIKETQUA'] == "1") {
					var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", row.TIEPNHANID);
					if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && row.LOAITIEPNHANID=="1" && row.MA_BHYT == ""){						
						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");						
					}
					else {
						DlgUtil.showMsg("Nhấn nút Bắt đầu thực hiện để Tiếp nhận bệnh nhân");
					}
				}
				else if (row['TRANGTHAIKETQUA'] == "3" || row['TRANGTHAIKETQUA'] == "4") {
					var url = "manager.jsp?func=../canlamsang/CLS02C003_TraKetQuaCDHA&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&idketquacls="+$("#hdfIDKetQuaCLS").val()+"&type=update"
										+"&rolePTH="+rolePTH+"&resizingTable="+resizingTable;
					EventUtil.setEvent("CLS02C003_LUU", function (e) {
						reloadAllGrid();
					});
					
					EventUtil.setEvent("CLS02C003_LUU_DONG", function (e) {
				    	DlgUtil.showMsg('Lưu kết quả thành công',undefined,500);
						reloadAllGrid();
						DlgUtil.close("dlgSuaKetQua");
					});
					
					EventUtil.setEvent("CLS02C003_HUY", function (e) {
						DlgUtil.close("dlgSuaKetQua");
					});
					var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaKetQua", "dlgSuaKetQua", url, {}, "Kết quả chẩn đoán", 1160, 600);
					dlgPopup.open("dlgSuaKetQua");
				}
				else if (row['TRANGTHAIKETQUA'] == "7"){
					var url = "manager.jsp?func=../canlamsang/CLS02C003_TraKetQuaCDHA&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&idketquacls="+$("#hdfIDKetQuaCLS").val()+"&type=read"
										+"&rolePTH="+rolePTH+"&resizingTable="+resizingTable;
					EventUtil.setEvent("CLS02C003_LUU", function (e) {
						reloadAllGrid();
					});
					
					EventUtil.setEvent("CLS02C003_LUU_DONG", function (e) {
				    	DlgUtil.showMsg('Lưu kết quả thành công',undefined,500);
						reloadAllGrid();
						DlgUtil.close("dlgSuaKetQua");
					});
					
					EventUtil.setEvent("CLS02C003_HUY", function (e) {
						DlgUtil.close("dlgSuaKetQua");
					});
					var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaKetQua", "dlgSuaKetQua", url, {}, "Kết quả chẩn đoán", 1160, 600);
					dlgPopup.open("dlgSuaKetQua");
				}
				else {
					console.log("Trạng thái dịch vụ: "+ row['TRANGTHAIKETQUA']);
				}
			},
			gridComplete: function(){
	    		var rowids = $("#grdDanhSachCDHA").getDataIDs();
	    		if(rowids!="") {
					for(var i=0; i<rowids.length; i++) {
						var rowid = rowids[i];
						var row = $("#grdDanhSachCDHA").jqGrid('getRowData', rowid);
						var icon = '';
						
						// hiển thị các icon trạng thái
						if(row.TRANGTHAIKETQUA == 1) {
							icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
						} 
						else if(row.TRANGTHAIKETQUA == 3 || row.TRANGTHAIKETQUA == 4) {
							if(row.THOIGIANDUKIENCOKETQUA != "") {
								icon = '<center><img src="../common/image/Pin.png" width="15px"></center>';
								$("#grdDanhSachCDHA").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Hẹn trả kết quả');
							}
							else if(row.TRANGTHAIDULIEUMAUBENHPHAM == 2) {
								icon = '<center><img src="../common/image/Flag_Green.png" width="15px"></center>';
								$("#grdDanhSachCDHA").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Đã có kết quả');
							} 
							else if(row.TRANGTHAIDULIEUMAUBENHPHAM == 3) {
								icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
								$("#grdDanhSachCDHA").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Đã hủy');
							} 
							else {
								icon = '<center><img src="../common/image/Misc_calendar.png" width="15px"></center>';
								$("#grdDanhSachCDHA").jqGrid ('setCell', rowid, 'TRANGTHAI', 'Đã tiếp nhận');
							}
						} 
						else if(row.TRANGTHAIKETQUA == 7) {
							icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
//							// biểu tượng đã ký số
//							if(optKySo=="1"||optKySo=="2"){
//								var param = [row.MAUBENHPHAMID, row.DICHVUKHAMBENHID];
//								var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", param.join("$"));
//								if(data_ar!=undefined && data_ar!=null){
//									if(data_ar.length > 0){
//										var trangthai = data_ar[0]["TRANGTHAI"];
//										var hethong = data_ar[0]["HETHONG"];
//										if(trangthai=="1"){
//											if(hethong=="RIS"){
//												icon = '<center><img src="../common/image/signed.png" width="15px"></center>';
//											}
//											else {
//												icon = '<center><img src="../common/image/done.png" width="15px"></center>';
//											}
//										}
//									}
//								}
//							}
//							// hết biểu tượng đã ký số
						} 
						else {
							icon = '<center><img src="../common/image/State2.png" width="15px"></center>';
						}						
						$("#grdDanhSachCDHA").jqGrid ('setCell', rowid, 1, icon);

//						if(row.LOAIMAUBENHPHAM=="Khẩn"){
//							$('#grdDanhSachCDHA').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
//	                	        $(element).css("font-weight","bold");
//	                			$(element).css("color", "#b937b3");
//	                	    });
//						}
						
						//nghiant 03112017 -edit for DKHNm- neu la benh nhan bao hiem y te thi bo qua
//						if(row.TRANGTHAIMAUBENHPHAM == 3 && ( row.MA_BHYT == null || row.MA_BHYT =='')){
//							var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01.BOLD",row.MAUBENHPHAMID);
//							if (data_ar != null && data_ar.length > 0) {
//								//neu la benh nhan cap cuu thuc hien to dam mau do
//								if(data_ar[0].HINHTHUCVAOVIENID=='2' && data_ar[0].LOAITIEPNHANID=='1' && (data_ar[0].DACODICHVUTHUTIEN ==null || data_ar[0].DACODICHVUTHUTIEN =='' || data_ar[0].DACODICHVUTHUTIEN =='0')
//									){
//									$('#grdDanhSachCDHA').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
//			                	        $(element).css("font-weight","bold");
//			                			$(element).css("color", "#9e0606");
//			                	    });
//								}
//							}
//						}
						
//						if(row.LOAITIEPNHANID==2){
//							$('#grdDanhSachCDHA').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
//	                	        $(element).css("font-weight","bold");
//	                			$(element).css("color", "green");
//	                	    });
//						}
						//end nghiant 03112017

						if(row.VATTUDIKEM!=""){
							$('#grdDanhSachCDHA').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
	                	        $(element).css("font-weight","bold");
//	                			$(element).css("color", "green");
	                	    });
						}
						
						// Bôi màu bệnh nhân thiếu tiền
						if(row.LOAITIEPNHANID == '1' && row.MA_BHYT == "" && row.SOTIEN>0){
							$("#grdDanhSachCDHA").jqGrid('setRowData',rowid,false, { color:'red'}); 
						}
						
						// focus lại dòng được chọn
						if(row.DICHVUKHAMBENHID == $("#hdfIDDichVuKB").val()){
							$("#grdDanhSachCDHA").setSelection(rowid, true);
						}
					}
	    		}
	    		
	    		//Begin_HaNv_10052018: them context menu chuyen phong thuc hien HISL2TK-419
	    		$(".jqgrow",'#grdDanhSachCDHA').contextMenu('contextMenuBP', {    	                
		            onContextMenu: function (event, menu) {
		            	var rowId = $(event.target).parent("tr").attr("id");
		            	$('#grdDanhSachCDHA').setSelection(rowId);
		            	return true;
		            },
		            bindings: {
		            	'CapSTT': function (t) {
		            		var rowId = $(t).attr("id");
	    					if(rowId != null && rowId != ''){
	    						var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);
								var paramInput = { maubenhphamid:rowData.MAUBENHPHAMID };					
			    				dlgPopup=DlgUtil.buildPopupUrl("divDlgCapSTT","divDlg","manager.jsp?func=../canlamsang/CLS02C011_CapSTT",paramInput,"Cấp STT thực hiện",500,250);
			    				DlgUtil.open("divDlgCapSTT");
	    					}
	    					EventUtil.setEvent("CLS02C011_Luu", function(e) {
	    		    			DlgUtil.close("divDlgCapSTT");
	    		    			DlgUtil.showMsg('Cấp số thứ tự thực hiện thành công',undefined,1000);
	    		    			reloadAllGrid();
	    		    		});
	    					EventUtil.setEvent("CLS02C011_Thoat", function(e) {
	    		    			DlgUtil.close("divDlgCapSTT");
	    		    			reloadAllGrid();
	    		    		});
	    				},
		            	'ChuyenPhongTH': function (t) {
		            		var rowId = $(t).attr("id");
	    					if(rowId != null && rowId != ''){
	    						var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);
								var paramInput={
				    				maubenhphamid:rowData.MAUBENHPHAMID,
				    				type:'1'
			    				};					
			    				dlgPopup=DlgUtil.buildPopupUrl("divDlgPhongChiDinh","divDlg","manager.jsp?func=../noitru/NTU01H025_DoiPhongChiDinh",paramInput,"Đổi phòng thực hiện",500,250);
			    				DlgUtil.open("divDlgPhongChiDinh");
	    					}
	    					EventUtil.setEvent("assignSevice_saveChangeDev", function(e) {
	    		    			if(typeof(e) != 'undefined'){
	    		    				DlgUtil.showMsg(e.msg);
	    		    			}
	    		    			DlgUtil.close(e.divId);
	    		    			reloadAllGrid();
	    		    		});
	    				},
	    				'TAOPHIEUTHUOCKEM_HAOPHI': function (t) {
	    					var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
	    					var rowDataCk= $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
	    					var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
							if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
	    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
	    					}
	    					else {
	    						var rowId = $(t).attr("id");
		    					var _opt="02D010";
		    					var _msg="Tạo phiếu thuốc đi kèm hao phí";
		    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
		    					var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);

		    					EventUtil.setEvent("assignDrug_cancel", function (e) {
		    						DlgUtil.close("divDlgTaoPhieuThuoc"+_opt);
		    						reloadAllGrid();
		    					});
		    					
		    					if(rowData != null){
		    						paramInput={	
		    							khoaId: khoa_chuyen_den_id,
		    							phongId: phong_chuyen_den_id,					
		    							khambenhid : data_ar[0].KHAMBENHID,
		    							maubenhphamid : "",
		    							loaikedon: 1,
		    							dichvuchaid: rowData.DICHVUKHAMBENHID,
		    							opt : _opt,
		    							macdinh_hao_phi : 9
		    						};   		 
		    						dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1340,605);
		    						DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
		    					}
	    					}
	    				},
	    				'TAOPHIEUVATTUKEM_HAOPHI': function (t) {
	    					var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
	    					var rowDataCk= $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
	    					var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
							if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
	    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
	    					}
	    					else {
	    						var rowId = $(t).attr("id");
		    					var _opt="02D015";
		    					var _msg="Tạo phiếu vật tư đi kèm hao phí";
		    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
		    					var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);

		    					EventUtil.setEvent("assignDrug_cancel", function (e) {
		    						DlgUtil.close("divDlgTaoPhieuThuoc"+_opt);
		    						reloadAllGrid();
		    					});
		    					
		    					if(rowData != null){
		    						paramInput={	
		    							khoaId: khoa_chuyen_den_id,
		    							phongId: phong_chuyen_den_id,
		    							khambenhid : data_ar[0].KHAMBENHID,
		    							maubenhphamid : "",
		    							loaikedon: 1,
		    							dichvuchaid: rowData.DICHVUKHAMBENHID,
		    							opt : _opt,
		    							macdinh_hao_phi : 9						
		    						};	
		    					
		    						dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1340,605);
		    						DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
		    					}
	    					}
	    				},
	    				'TAOPHIEUTHUOCKEM': function (t) {
	    					var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
	    					var rowDataCk= $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
	    					var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
							if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
	    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
	    					}
	    					else {
	    						var rowId = $(t).attr("id");	
		    					var _opt="02D010";
		    			     	var _msg="Tạo phiếu thuốc đi kèm";
		    			     	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
		    			     	var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);

		    					EventUtil.setEvent("assignDrug_cancel", function (e) {
		    						DlgUtil.close("divDlgTaoPhieuThuoc"+_opt);
		    						reloadAllGrid();
		    					});
		    					
		    			     	if(rowData != null){
		    			     		paramInput={
		    							khoaId: khoa_chuyen_den_id,
		    							phongId: phong_chuyen_den_id,
		    							khambenhid :  data_ar[0].KHAMBENHID,
		    							maubenhphamid : "",
		    							loaikedon: 1,
		    							dichvuchaid: rowData.DICHVUKHAMBENHID,
		    							opt : _opt     					
		    			     		};   		 
		    						dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1340,605);
		    						DlgUtil.open("divDlgTaoPhieuThuoc"+_opt);
		    			     	}
	    					}
	    				},
	    				'TAOPHIEUVATTUKEM': function (t) {
	    					var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
	    					var rowDataCk= $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
	    					var chenhlech = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DATHUTIEN", rowDataCk.TIEPNHANID);
							if(chenhlech!=null && chenhlech!=undefined && chenhlech<0 && chantiepnhan=="1" && rowDataCk.LOAITIEPNHANID=="1" && rowDataCk.MA_BHYT=="" && rowDataCk.TRANGTHAIMAUBENHPHAM!="3"){
	    						DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!");
	    					}
	    					else {
	    						var rowId = $(t).attr("id");
		    					var _opt="02D015";
		    					var _msg="Tạo phiếu vật tư đi kèm";
		    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
		    					var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);

		    					EventUtil.setEvent("assignDrug_cancel", function (e) {
		    						DlgUtil.close("divDlgTaoPhieuThuoc"+_opt);
		    						reloadAllGrid();
		    					});
		    					
		    			     	if(rowData != null){
		    						paramInput={	
		    							khoaId: khoa_chuyen_den_id,
		    							phongId: phong_chuyen_den_id,				
		    							khambenhid : data_ar[0].KHAMBENHID,
		    							maubenhphamid : "",
		    							loaikedon: 1,
		    							dichvuchaid: rowData.DICHVUKHAMBENHID,
		    							opt : _opt // tao phieu thuoc						
		    						};	
		    				
		    						dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1340,605);
		    						DlgUtil.open("divDlgTaoPhieuThuoc"+_opt); 
		    			     	}
	    					}
	    				},
	    				'DSPTVT_KEM': function (t) {
	    					var rowId = $(t).attr("id");
	    					var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
	    					var rowDataCk= $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
	    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
	    					var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);
	    					if(rowData != null){
	    						paramInput={
	    							khoaId: khoa_chuyen_den_id,
	    							phongId: phong_chuyen_den_id,
	    							khambenhid : data_ar[0].KHAMBENHID,      					
	    							dichvucha_id: rowData.DICHVUKHAMBENHID 					
	    			   			};	
	    			     		 //tuyennx_edit_start yc L2HOTRO-10429
	    			   			dlgPopup=DlgUtil.buildPopupUrl("divDlgDSPTDK","divDlg","manager.jsp?func=../noitru/NTU02D043_DanhSachPhieuThuocDiKem",paramInput,"Danh sách phiếu thuốc, vật tư đi kèm",1340,610);
	    			   			//tuyennx_edit_end 
	    			   			DlgUtil.open("divDlgDSPTDK"); 
	    					}
	    				},
	    				//tuyennx_add_start yc L2K74TW-224		    				
	    				'DSPTVT': function (t) {
	    					var rowId = $(t).attr("id");
	    					var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
	    					var rowDataCk= $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
	    					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
	    					var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);
	    					if(rowData != null){
	    						paramInput={
	    							khoaId: khoa_chuyen_den_id,
	    							phongId: phong_chuyen_den_id,				
	    							khambenhid : data_ar[0].KHAMBENHID,      					
	    							benhnhanid: data_ar[0].BENHNHANID,
	    							trangthaikhambenh: data_ar[0].TRANGTHAIKHAMBENH,
	    							dichvuKhambenhID: rowData.DICHVUKHAMBENHID// HISL2NT-865 -- hongdq --20180327
	    						};	      					
	    						dlgPopup=DlgUtil.buildPopupUrl("divDlgDSPTVT","divDlg","manager.jsp?func=../noitru/NTU02D078_DanhSachPhieuThuocVatTu",paramInput,"Danh sách phiếu thuốc, vật tư",1340,610); //L1PT-464
	    						DlgUtil.open("divDlgDSPTVT"); 
	    					}
	    				},
                        // ChienDV START L2PT-82498
                        'KTTSDU': function (t) {
                            let rowId = $(t).attr("id");
                            if(rowId != null && rowId != ''){
                                let rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);
                                let param = [rowData.MAUBENHPHAMID];
                                let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", param.join('$'));
                                let paramInput = {
                                    hosobenhan_id : data_ar[0].HOSOBENHANID,
                                    khambenh_id : data_ar[0].KHAMBENHID
                                };
                                dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuTSDU", "divDlg", "manager.jsp?func=../noitru/NTU02D128_PhieuTienSuDiUng", paramInput, "Phiếu tiền sử dị ứng", 1250, 700);
                                DlgUtil.open("divDlgPhieuTSDU");
                            }
                        },
						'XNBETAHCG': function (t) {
							let rowId = $(t).attr("id");
							if(rowId != null && rowId != ''){
								let rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);
								let param = [rowData.MAUBENHPHAMID];
								let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", param.join('$'));
								let _param = [{
									name: 'hosobenhanid',
									type: 'String',
									value: data_ar[0].HOSOBENHANID,
								}, {
									name : 'khambenhid',
									type : 'String',
									value : data_ar[0].KHAMBENHID
								}, {
									name: 'RPT_CODE',
									type: 'String',
									value: 'RPT_GIAYCAMKETXN_BETAHCG'
								}];
								_kyCaRpt(_param);
							}
						},
						// ChienDV END L2PT-82498	
						'CDVTHCLS': function (t) {
		            		var rowId = $(t).attr("id");
	    					if(rowId != null && rowId != ''){
	    						var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);		
		    					paramInput = {
									hosobenhanid : rowData.HOSOBENHANID,
									khambenhid : rowData.KHAMBENHID,
									maubenhphamid:rowData.MAUBENHPHAMID
								};
								dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuCDVTHCLS", "divDlg", "manager.jsp?func=../noitru/NTU_PhieuChuyenThucHienDichVu", paramInput, "Phiếu chuyển thực hiện dịch vụ CLS", 1250, 600);
								DlgUtil.open("divDlgPhieuCDVTHCLS");
	    					}
	    				},
						
						'inTheBenhAn': function (t) {
							var rowId = $(t).attr("id");
							if(rowId != null && rowId != ''){
								var rowData = $('#grdDanhSachCDHA').jqGrid('getRowData', rowId);
								let param = [rowData.MAUBENHPHAMID];
								let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", param.join('$'));
								var par = [ {
										name : 'hosobenhanid',
										type : 'String',
										value : data_ar[0].HOSOBENHANID
									}];
									openReport('window', "IN_MABENHAN_A6", "pdf", par);
							}
						}
		            }
	    		});
	    		//End_HaNv_10052018
	    	}
		});

		//nghiant 26062017 
		$('#btnEditTimeCDHA').on('click', function () {			
			var url = "manager.jsp?func=../canlamsang/CLS02X009_SuaNgayTraKetQua&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&tiepnhanid="+tiepnhanid+"&benhnhanid="+benhnhan_id;
			EventUtil.setEvent("CLS02X009_Thoat",function(e){
				DlgUtil.close("dlgSuaThoiGianTraKQ");
				reloadAllGrid();
			});
			var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaThoiGianTraKQ","divNhapKetQua",url,{},"Sửa ngày trả kết quả",400,300);
			dlgPopup.open("dlgSuaThoiGianTraKQ");
		});

		$('#btnSuaNgayTN').on('click', function () {			
			var url = "manager.jsp?func=../canlamsang/CLS02X010_SuaNgayTiepNhan&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&tiepnhanid="+tiepnhanid+"&benhnhanid="+benhnhan_id;
			EventUtil.setEvent("CLS02X010_Thoat",function(e){
				DlgUtil.close("dlgSuaNgayTN");	
				reloadAllGrid();
			});
			var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaNgayTN","divNhapKetQua",url,{},"Sửa ngày tiếp nhận",400,300);
			dlgPopup.open("dlgSuaNgayTN");
		});
		
		$('#toolbarIdbtnInPhieu19BV01').on('click', function () {			
			inPhieu19BV01();
		});
		
		$('#toolbarIdbtnInPhieu20BV01').on('click', function () {			
			inPhieu20BV01();
		});
		
		$('#toolbarIdbtnInPhieu21BV01').on('click', function () {			
			inPhieu21BV01();
		});
		
		//nghiant 12092019 L2PT-8240
		$('#toolbarIdbtnInPhieuLoangXuong').on('click', function () {			
			inPhieuLoangXuong();
		});
		
		//nghiant 08062018
		$('#toolbarIdbtnPhieuIn27BV01TEST_TAMLY').on('click', function () {			
			inPhieu27BV01TEST_TAMLY();
		});
		
		$('#toolbarIdbtnPhieuIn27BV01TV_TAMLY').on('click', function () {			
			inPhieu27BV01TV_TAMLY();
		});
		//end nghiant 08062018
		
		$('#toolbarIdbtnInPhieu22BV01').on('click', function () {			
			inPhieu22BV01();
		});
		
		$('#toolbarIdbtnInPhieu27BV01').on('click', function () {			
			inPhieu27BV01();
		});
		
		$('#toolbarIdbtnInPhieu31BV01').on('click', function () {			
			inPhieu31BV01();
		});

		$('#toolbarIdbtnInPhieu23BV01').on('click', function () {			
			inPhieu23BV01();	
		});
		
		$('#toolbarIdbtnInPhieu35BV01').on('click', function () {			
			inPhieu35BV01();	
		});
		
		$('#toolbarIdbtnInPhieu36BV01').on('click', function () {			
			inPhieu36BV01();	
		});
		
		$('#toolbarIdbtnInPhieu37BV01').on('click', function () {			
			inPhieu37BV01();	
		});
		$('#toolbarIdbtnInPhieu38BV01').on('click', function () {			
			inPhieu38BV01();	
		});
		
		//duongnv 20180918 - thay doi in phieu dien nao
		$('#toolbarIdbtnInPhieu24BV01').on('click', function () {			
			//inPhieu24BV01();
			inPhieuDienNaoBV938();
		});
		
		$('#toolbarIdbtnInPhieu25BV01').on('click', function () {			
			inPhieu25BV01();	
		});
		$('#toolbarIdbtnInPhieuNSMAU2').on('click', function () {			
			inPhieuNoiSoiMau2();	
		});
		
		$('#toolbarIdbtnInPhieu33BV01').on('click', function () {			
			inPhieu33BV01();	
		});
		
		$('#toolbarIdbtnInPhieuTMH74').on('click', function () {			
			inPhieuTMH74();	
		});
		//nghiant 02072018 
		$('#toolbarIdbtnInPhieu25BV01TieuHoa').on('click', function () {			
			inPhieuNoiSoiTieuHoa();	
		});
		//end nghiant 02072018 
		$('#toolbarIdbtnInPhieu25BV01CoTuCung').on('click', function () {			
			inPhieuNoiSoiCoTuCung();	
		});
		$('#toolbarIdbtnInPhieu25BV01MuiXoang').on('click', function () {			
			inPhieuNoiSoiMuiXoang();	
		});
		
		$('#toolbarIdbtnInPhieuNSPQ74').on('click', function () {			
			inPhieuNSPQ74();	
		});
		
		$('#toolbarIdbtnInPhieu26BV01').on('click', function () {			
			inPhieu26BV01();
		});
		$('#toolbarIdbtnInPhieu26BV01LuuHuyetNao').on('click', function () {			
			inPhieu26BV01LuuHuyetNao();
		});
		
		$('#toolbarIdbtnInPhieu28BV01').on('click', function () {			
			inPhieu28BV01();
		});
		
		$('#toolbarIdbtnInPhieu29BV01').on('click', function () {			
			inPhieu29BV01();
		});
		
		$('#toolbarIdbtnInPhieu30BV01').on('click', function () {			
			inPhieu30BV01();
		});
		
		$('#toolbarIdbtnInPhieu32BV01').on('click', function () {			
			inPhieu32BV01();
		});
		
		//2018-09-17 duongnv : Them phieu sieu am 4D
		$('#toolbarIdbtnInPhieuSieuAm4D').on('click', function () {			
			inPhieuSieuAm4D();
		});
		
		
		$('#toolbarIdbtnInPhieuTestTho').on('click', function () {			
			inPhieuTestThoBV01();	
		});
		
		$('#toolbarIdbtnInPhieuBoiDuongPTTT').on('click', function () {			
			inPhieuBoiDuongPTTT();	
		});
		
		$('#toolbarIdbtnInPhieuTTBoiDuongPTTT').on('click', function () {			
			inPhieuThanhToanBoiDuongPTTT();	
		});
		
		$('#toolbarIdbtnInPhieuKQCDHA').on('click', function () {			
			inPhieuKQCDHA();	
		});
		
		$('#toolbarIdbtnInPhieuKQCDHA_BS').on('click', function () {			
			inPhieuKQCDHA_BS();	
		});
		
		//tuyendv 19/12
		$('#toolbarIdbtnInPhieu34BV01').on('click', function () {			
			inPhieu34BV01();	
		});
		$('#toolbarIdbtnInPhieuNSPQ2').on('click', function () {			
			inPhieuNSPQ2();	
		});
		$('#toolbarIdbtnInPhieuNSDTT').on('click', function () {			
			inPhieuNSDTT();	
		});
		//end tuyendv 19/12

		// ==================== START >> In Phiếu New ==================== truongle - 26/11/2019
		$('[id^="toolbarIdbtnInPhieu_new_"]').click(function(event) {
			inPhieuNew($(event.currentTarget));
		})
		// ==================== END >> In Phiếu New ==================== truongle - 26/11/2019
		
		// ==================== START >> L2PT-19135 ==================== BangT - 01/04/2020
		$('#toolbarIdbtnInPhieuARFI').on('click', function () {			
			inPhieuARFI();
		});
		// ==================== END >> L2PT-19135 ==================== BangT - 01/04/2020

		$('#toolbarIdbtnGoiKham').on('click', function () {	
			var selRowId = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');
			if(selRowId!=null){
				var so_thutu = $("#grdDanhSachCDHA").jqGrid ('getCell', selRowId, 'SOTHUTU');
				
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DMC04.02",$("#cboPhongThucHien").val());
				var sophong = "";
				var tenphong = "";
				if(data_ar.length>0){
					sophong = data_ar[0].SOPHONG;
					tenphong = data_ar[0].ORG_NAME;
				} 
				else {
					sophong = "1";
					tenphong = "phòng thực hiện";
				}
				var tenbn = $("#grdDanhSachCDHA").jqGrid ('getCell', selRowId, 'TENBENHNHAN');
				
				var talkshow = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'RIS_CONTENT_CALL_PATIENT');
				if(talkshow == null || talkshow == undefined || talkshow == "0" || talkshow == ""){
					talkshow = "Xin mời bệnh nhân số " + so_thutu + " " + tenbn + " vào phòng " + sophong;
				} 
				else {
					talkshow = talkshow.replace("{TENBENHNHAN}",tenbn).replace("{STT}",so_thutu).replace("{SOPHONG}",sophong).replace("{TENPHONG}",tenphong);
				}
				
				if(RIS_MODE_CALL_PATIENT=="0"){
					GBN.goi_vaophong(so_thutu,'');
				}
				else if(RIS_MODE_CALL_PATIENT=="1"){
					responsiveVoice.speak(talkshow, "Vietnamese Female", {rate: 0.8});
				}
				else{
					goiKhamGG(talkshow, HIS_API_MODE, HIS_API_TIMEOUT);
				}
				// tang so goi kham
				var callup = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'RIS_LCD_DISPLAY');
				if(callup=="2"){
					var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val() ;

					var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS03L002.TLG", param);
					if(rs==1){
						console.log("Tang lan goi thanh cong");
					} else {
						console.log("Tang lan goi loi");
					}
				}
			}
		});
		
		$('#toolbarIdbtnXuatManHinh').on('click', function () {
			var param="&phongid="+$("#cboPhongThucHien").val()+"&tungay="+$("#txtTuNgay").val()+"&denngay="+$("#txtDenNgay").val()+"&showMode=dlg";
			console.log("url="+param);

			var callup = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'RIS_LCD_DISPLAY');
			
			if(callup=="0"){
				window.open('manager.jsp?func=../canlamsang/CLS03L001_ManHinhLCD'+param,'','width:100%;');
			}
			else if(callup=="1"){
				window.open('manager.jsp?func=../canlamsang/CLS09L001_ManHinhLCD'+param,'','width:100%;');
			}
			else if(callup=="2"){
				window.open('manager.jsp?func=../canlamsang/CLS03L002_ManHinhLCD'+param,'','width:100%;');
			}

		});

		$('#toolbarIdbtnDSBN').on('click', function () {
			var param="&phongid="+$("#cboPhongThucHien").val()+"&showMode=dlg";
			console.log("url="+param);
			window.open('manager.jsp?func=../canlamsang/CLS06D001_THONGKEDSBN'+param,'','width:100%;');

		});
		
		function checkLSBeforeClickOnMenu() {
			var selRowId1 = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');
			var benhNhanId = $("#grdDanhSachCDHA").jqGrid('getCell', selRowId1, 'BENHNHANID');			
			if (benhNhanId == '' || benhNhanId == '-1') {
				DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
				return false;
			} else {
				return true;
			}
		}
		
		//start An hien toolbar lich su L2PT-48818
		var showBtnTKBN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_SHOW_BUTTON_LICHSU');
		if(showBtnTKBN != "1"){
			$("#toolbarIdbtnhistory").hide();
		}
		
		//Lich su dieu tri
		$("#toolbarIdhistory_2").on("click", function() {
			if (!checkLSBeforeClickOnMenu())
				return;
			var selRowId1 = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');
			var benhNhanId = $("#grdDanhSachCDHA").jqGrid('getCell', selRowId1, 'BENHNHANID');
			paramInput = {
				benhnhanId : benhNhanId
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K025_LichSuDieuTri", paramInput, "Lịch sử điều trị", 1320, 610);
			DlgUtil.open("dlgLichSuDieuTri");
		});
		//Lich su benh an
		$("#toolbarIdhistory_1").on("click", function() {
			if (!checkLSBeforeClickOnMenu())
				return;
			var selRowId1 = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');
			var benhNhanId = $("#grdDanhSachCDHA").jqGrid('getCell', selRowId1, 'BENHNHANID');
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", param.join('$'));		
			 
			var paramInput = {
				khambenhid: data_ar[0].KHAMBENHID,
				tiepnhanid: data_ar[0].TIEPNHANID,
				hosobenhanid: data_ar[0].HOSOBENHANID,
				mabenhan:data_ar[0].MABENHNHAN,
				tenbenhnhan: data_ar[0].TENBENHNHAN,
				tuoi: data_ar[0].TUOI,
				diachi: data_ar[0].DIACHI
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../noitru/NTU01H101_DayLaiBenhAn", paramInput, "HIS - Quản lý bệnh án", 1320, 610);
			DlgUtil.open("divDlgBA");
		});
		
		//end An hien toolbar lich su L2PT-48818
		
		$('#toolbarIdbtnThongKe').on('click', function () {			
			window.location.replace("manager.jsp?func=../canlamsang/CLS02C004_ThongKeCDHA");	
		});
		
		$('#toolbarIdtxtTimKiem').change(function (e) {	
			if($('#toolbarIdtxtTimKiem').val()==""){
				searchType = 0;
			}
			else {
				searchType = 1;
			}
			timKiemBangMayQuet($("#cboPhongThucHien").val(),$("#txtTuNgay").val(),$("#txtDenNgay").val(),$("input:radio[name='rdoTrangThai']:checked").val(),$('#toolbarIdtxtTimKiem').val());
			$('#toolbarIdtxtTimKiem').select();
		});

		$('#toolbarIdtxtTimKiem').keypress(function (e) {	
			if (e.keyCode == 13) {
				if($('#toolbarIdtxtTimKiem').val()==""){
					searchType = 0;
				}
				else {
					searchType = 1;
				}
				timKiemBangMayQuet($("#cboPhongThucHien").val(),$("#txtTuNgay").val(),$("#txtDenNgay").val(),$("input:radio[name='rdoTrangThai']:checked").val(),$('#toolbarIdtxtTimKiem').val());
				$('#toolbarIdtxtTimKiem').select();
			}
		});
		
		$('#toolbarIdtxtTimKiem').scannerDetection(function() {
			$("#toolbarIdtxtTimKiem").select();
			if($('#toolbarIdtxtTimKiem').val()==""){
				searchType = 0;
			}
			else {
				searchType = 1;
			}
			timKiemBangMayQuet($("#cboPhongThucHien").val(),$("#txtTuNgay").val(),$("#txtDenNgay").val(),$("input:radio[name='rdoTrangThai']:checked").val(),$('#toolbarIdtxtTimKiem').val());
		});
		
		$('input[type=radio][name=rdoTrangThai]').change(function () {
			$("#grdDanhSachCDHA").clearGridData();
			if(userRights!="1"){
				if (this.value == "2") { // chờ thực hiện
					$("#btnHenNgay").show();
					$("#btnTiepNhan").show();
					$("#btnHuyTiepNhan").hide();
					$("#btnTraKetQua").hide();
//					showButtonLuuKetQua(false);
					//nghiant 26062017 
					showButtonSuaNgayTraKQ(false);
					$("#btnHuyKetQua").hide();
					$("#btnInPhieu").hide();
					$("#toolbarIdbtnInPhieu").attr("disabled", true);
					$("#grdDanhSachCDHA").hideCol("NGAYMAUBENHPHAM_HOANTHANH");
					$("#grdDanhSachCDHA").showCol("TRANGTHAI");
					$("#btnKySo").hide();
					$("#btnHuyKySo").hide();
					$('#btnInPhieuKy').hide();
				}
				else if (this.value == "3") { // đã thực hiện
					$("#btnHenNgay").hide();
					$("#btnTiepNhan").hide();
					$("#btnHuyTiepNhan").hide();
					$("#btnTraKetQua").hide();
//					showButtonLuuKetQua(false);
					//nghiant 26062017 
					showButtonSuaNgayTraKQ(false);
					$("#btnHuyKetQua").show();
					$("#btnInPhieu").show();
					$("#toolbarIdbtnInPhieu").attr("disabled", false);
					$("#grdDanhSachCDHA").hideCol("TRANGTHAI");
					$("#grdDanhSachCDHA").showCol("NGAYMAUBENHPHAM_HOANTHANH");
					$("#btnKySo").hide();
					$("#btnHuyKySo").hide();
					$('#btnInPhieuKy').hide();
					
					checkRole();
				}
			}
			reloadAllGrid();
			searchType = 0;
		});

		$('#cboPhongThucHien').on('change', function () {
			$("#hdfIDMauBenhPham").val("");	
			reloadAllGrid();
			searchType = 0;
		});

		$('#txtTuNgay').on('change', function () {
			reloadAllGrid();
			searchType = 0;
		});

		$('#txtDenNgay').on('change', function () {
			reloadAllGrid();
			searchType = 0;
		});
		

		$('#btnXem').on('click', function () {
			reloadAllGrid();
			searchType = 0;
		});

		
		$('#btnHuyDichVu').on('click', function () {					
			huyDichVu();
			reloadAllGrid();
		});
		
		$('#btnKhoiPhuc').on('click', function () {
			khoiPhucDichVu();
			reloadAllGrid();
		});		

		$('#btnBoSung').on('click', function () {
			boSungDichVu();
			reloadAllGrid();
		});		

		// them chuc nang cho pttt
		$('#toolbarIdPTTTprint_1').bindOnce("click",function(){
      		 var rowKey = $('#grdDanhSachChiDinh').getGridParam("selrow");
      		 if(rowKey == null || rowKey == ''){
      			 return;
      		 }      		 
	   		 _exportPTTT(rowKey);
	   	},500);
		
		function _exportPTTT(rowId){
			var selRowId1 = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');
			var benhNhanId = $("#grdDanhSachCDHA").jqGrid('getCell', selRowId1, 'BENHNHANID');
			
	     	var selRowId = $('#grdDanhSachChiDinh').jqGrid ('getGridParam', 'selrow');
	     	var rowDataCk= $('#grdDanhSachChiDinh').jqGrid('getRowData', selRowId);
	     	var rowData = $('#grdDanhSachChiDinh').jqGrid('getRowData', rowId);
	     	var par = [ {
	 				name : 'dichvukhambenhid',
	 				type : 'String',
	 				value : rowData.DICHVUKHAMBENHID
 				},{
	 				name : 'benhnhanid',
	 				type : 'String',
	 				value : benhNhanId
 			}];

 			var param = [rowData.DICHVUKHAMBENHID];
 			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("PTTT.GETIMG", param.join('$'));
 			for(var i=0 ; i < data_ar.length ; i++){
 				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
 				if(hinh_anh1 != null && hinh_anh1 != "") par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
 			}

 			openReport('window', "NTU030_PHIEUPHAUTHUATTHUTHUAT_14BV01_QD4069_A4", "pdf", par);
	     };
	     
		$('#toolbarIdPTTTbtnUpdatePttt').bindOnce("click",function(){
     		 var rowKey = $('#grdDanhSachChiDinh').getGridParam("selrow");
     		 if(rowKey == null ||  rowKey == ''){
     			DlgUtil.showMsg('Chưa chọn dịch vụ');
	     		return;
     		 }      		 
       		 _updatePTTT(rowKey);
       	 },500); 
		
		function _updatePTTT(rowId){
			if(rowId != null && rowId != ''){
				var rowData = $('#grdKetQuaChanDoan').jqGrid('getRowData', rowId);
				
				var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
		     	var rowDataCk= $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
		     	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C001_CTPTTT",rowDataCk.DICHVUKHAMBENHID);
		     	
		     	//BVTM-3687
				var arrCH = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', "NTU_PTTT_FORM_VD2;NTU_PTTT_FORM_BVBD");
				var formBVBD;
				if(arrCH != null && arrCH.length > 0){
					formBVBD = arrCH[0].NTU_PTTT_FORM_BVBD;
				}
				
				var paramInput={
					mabenhnhan : data_ar[0].MABENHNHAN,
					khambenhid : data_ar[0].KHAMBENHID,
					tenbenhnhan : data_ar[0].TENBENHNHAN,
					namsinh : data_ar[0].NGAYSINH,
					nghenghiep : data_ar[0].TENNGHENGHIEP,
					hosobenhanid : data_ar[0].HOSOBENHANID,
					tiepnhanid : data_ar[0].TIEPNHANID,
					phongid : data_ar[0].PHONGID,
					benhnhanid : data_ar[0].BENHNHANID,
					khoaid : data_ar[0].KHOAID,     					
					tenphong: data_ar[0].PHONGDIEUTRI,
					thoigianvaovien: data_ar[0].THOIGIANVAOVIEN,
					maubenhphamid : rowDataCk.MAUBENHPHAMID,
					dichvukhambenhid : rowData.DICHVUKHAMBENHID,
					callfrom : '1'//L2PT-19030
     			};	
				EventUtil.setEvent("assignSevice_savePTTT",function(e){
					DlgUtil.close("dlgPTTT");	
					reloadAllGrid();
				});
				if(formBVBD == '1'){//L2PT-32060
	     			dlgPopup=DlgUtil.buildPopupUrl("dlgPTTT","divDlg","manager.jsp?func=../noitru/NTU02D037_PhauthuatThuThuat_BDHN",paramInput,"Cập nhật phẫu thuật thủ thuật",1160,610);
	      			DlgUtil.open("dlgPTTT");  
	     		}else{
	     			dlgPopup=DlgUtil.buildPopupUrl("dlgPTTT","divDlg","manager.jsp?func=../noitru/NTU02D037_PhauthuatThuThuat",paramInput,"Cập nhật phẫu thuật thủ thuật",1160,610);
					DlgUtil.open("dlgPTTT");
	     		}
				
			}
		}
		
		$('#toolbarIdPTTThospitalfee_1').bindOnce("click",function(){
      		 var rowKey = $('#grdDanhSachChiDinh').getGridParam("selrow");
      		 if(rowKey == null ||  rowKey == ''){
      			DlgUtil.showMsg('Chưa chọn dịch vụ');
	     		return;
      		 }
      		 
       		_taophieuphuthu(rowKey);
       	 },500);
		
		function _taophieuphuthu(rowId){
			var rowData = $('#grdDanhSachChiDinh').jqGrid('getRowData', rowId);
			
			var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
	     	var rowDataCk= $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
	     	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
	     	
			if(rowData != null){
	     		paramInput={        	 
					 chidinhdichvu : '1',
					 loaidichvu : '8',
					 loaiphieumbp: '6',
					 benhnhanid : data_ar[0].BENHNHANID,
					 khambenhid : data_ar[0].KHAMBENHID,
					 hosobenhanid : data_ar[0].HOSOBENHANID,
					 tiepnhanid : data_ar[0].TIEPNHANID,
					 doituongbenhnhanid : data_ar[0].DOITUONGBENHNHANID,
					 hinhthucvaovienid : data_ar[0].HINHTHUCVAOVIENID,
					 loaibenhanid : data_ar[0].LOAIBENHANID,
					 loaitiepnhanid : data_ar[0].LOAITIEPNHANID,
					 maubenhphamchaid : data_ar[0].MAUBENHPHAMID,
					 dichvukhambenhid : rowData.DICHVUKHAMBENHID,
					 subDeptId : data_ar[0].PHONGID
	  			 }  
	     		dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 8,paramInput,"Phiếu phụ thu",1300,600);
	     		DlgUtil.open("divDlgDichVu");
	     	 }
	     };
		
		
		$('#toolbarIdPTTThospitalfee_2').bindOnce("click",function(){
     		 var rowKey = $('#grdDanhSachChiDinh').getGridParam("selrow");
     		 if(rowKey == null ||  rowKey == ''){
     			DlgUtil.showMsg('Chưa chọn dịch vụ');
	     		return;
     		 }      		 
       		_getDanhsachphieuphuthu(rowKey);
       	 },500);
		
		function _getDanhsachphieuphuthu(rowId){
			var rowData = $('#grdDanhSachChiDinh').jqGrid('getRowData', rowId);     
			
			var selRowId = $('#grdDanhSachCDHA').jqGrid ('getGridParam', 'selrow');
	     	var rowDataCk= $('#grdDanhSachCDHA').jqGrid('getRowData', selRowId);
	     	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003",rowDataCk.MAUBENHPHAMID);
			
			if(rowData != null){
	     		 paramInput={					
	   					khambenhid : data_ar[0].KHAMBENHID,      					
	   					dichvucha_id: rowData.DICHVUKHAMBENHID 					
	   			};	      					
	   			dlgPopup=DlgUtil.buildPopupUrl("divDlgDSPPT","divDlg","manager.jsp?func=../noitru/NTU02D040_DanhSachPhieuPhuThu",paramInput,"Danh sách phiếu phụ thu",1250,575);
	   			DlgUtil.open("divDlgDSPPT"); 
			}
		};
		// end - them chuc nang cho pttt
		
//		$('#btnCLEARPHUMO1').on("click",function() {
//			$("#txtTKPHUMO1").val("");
//			var option = $('<option value="-1"></option>');
//		    $("#cboPHUMO1").empty();
//		    $("#cboPHUMO1").append(option);
//		});
		
//		function showButtonLuuKetQua(show){
//			if(showNutLuuLai!=undefined && showNutLuuLai!=null){
//				if(showNutLuuLai==1){
//					if(show==true){				
//						$("#btnLuuKetQua").show();					
//					} else {
//						$("#btnLuuKetQua").hide();
//					}		
//				}
//			}			
//		};			
		
//		$('#btnLuuKetQua').on('click', function () {
//			var currentPage = $('#grdDanhSachCDHA').getGridParam('page');
//			var hdfID = $("#hdfIDMauBenhPham").val();
//			luuKetQua();
//			reloadAllGrid();
//			$("#hdfIDMauBenhPham").val(hdfID);
//			var callback = function () {
//	            if ($.active !== 0) {
//	                setTimeout(callback, '1');
//	                return;
//	            }
//	            var totalPage = $('#grdDanhSachCDHA').getGridParam('lastpage');
//	            if(totalPage > 0) {
//	            	if(currentPage > totalPage) {
//	            		currentPage = totalPage;
//	            	} 
//	            	$('#grdDanhSachCDHA')[0].grid.populatePage(currentPage);		
//	            }	            
//			};
//			callback();
//		});

		$('#btnHuyKetQua').on('click', function () {
			var currentPage = $('#grdDanhSachCDHA').getGridParam('page');
			var hdfID = $("#hdfIDMauBenhPham").val();
			huyKetQua();
			reloadAllGrid();
			$("#hdfIDMauBenhPham").val(hdfID);
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '1');
	                return;
	            }
	            var totalPage = $('#grdDanhSachCDHA').getGridParam('lastpage');
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdDanhSachCDHA')[0].grid.populatePage(currentPage);		
	            }
			};
			callback();
		});

		$('#btnHenNgay').on('click', function () {			
			var rowId = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');

			if (rowId != null) {
				var hdfID = $("#hdfIDMauBenhPham").val();
				EventUtil.setEvent("CLS02C018_Thoat",function(e){
					DlgUtil.close("dlgHenNgay");	
					reloadAllGrid();
				});
				var paramInput={
					maubenhphamid: hdfID
	 			};						
				dlgPopup=DlgUtil.buildPopupUrl("dlgHenNgay","divNhapKetQua","manager.jsp?func=../canlamsang/CLS02C018_HenNgayThucHien",paramInput,"Hẹn ngày thực hiện",600,400);
				DlgUtil.open("dlgHenNgay");
			}
			else {
				DlgUtil.showMsg("Bạn chưa chọn bệnh nhân",undefined,1000);
			}
		});

		$('#btnTiepNhan').on('click', function () {
			var currentPage = $('#grdDanhSachCDHA').getGridParam('page');
			
			batDauThucHien();
			reloadAllGrid();
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '1');
	                return;
	            }
	            var totalPage = $('#grdDanhSachCDHA').getGridParam('lastpage');
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdDanhSachCDHA')[0].grid.populatePage(currentPage);		
	            }
			};
			callback();
		});

		$('#btnHuyTiepNhan').on('click', function () {
			var currentPage = $('#grdDanhSachCDHA').getGridParam('page');
			
			console.log("request_url=" + RIS_SERVICE_DOMAIN_NAME + RIS_CANCEL_REQUEST);
			if(RIS_CONNECTION_TYPE == "1" || RIS_CONNECTION_TYPE == "2" && RIS_SERVICE_DOMAIN_NAME != "") {
				var requestCode = $("#hdfSoPhieu").val();
		        
				var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_CANCEL_REQUEST;
		        
		        var data = {
	                requestCode: requestCode,
	                cancelReason: "HIS hủy tiếp nhận",
	                cancelBy: user_id
	            }
		        
		        $.ajax({
		            type: "POST",
		            contentType: "application/json; charset=utf-8",
		            headers: {
		            	'Ris-Access-Hash': getHashRIS(requestCode),
		                'Identify-Code': requestCode
		            },
		            data: JSON.stringify(data),
		            url: request_url,
		            success: function(data) {
		    			huyThucHien();
		    			reloadAllGrid();
		    			console.log(JSON.stringify(data));
		            },
		            error: function(xhr){
		            	if(xhr.status == "404" || xhr.status == "0"){
		            		huyThucHien();
			    			reloadAllGrid();
			    			console.log(JSON.stringify(xhr));
		            	}
		            	else {
		            		DlgUtil.showMsg(xhr.status+" - "+JSON.parse(xhr.responseText).message);
		            	}
		            }
		        });
			}
			else {
				huyThucHien();
    			reloadAllGrid();
			}
	        
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '1');
	                return;
	            }
	            var totalPage = $('#grdDanhSachCDHA').getGridParam('lastpage');
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdDanhSachCDHA')[0].grid.populatePage(currentPage);		
	            }	            
			};
			callback();
		});

		$('#btnHenTraKetQua').on('click', function () {			
			var rowId = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');

			if (rowId != null) {
				var hdfID = $("#hdfIDMauBenhPham").val();
				EventUtil.setEvent("CLS02C007_Thoat",function(e){
					DlgUtil.close("dlgHenTraKQ");	
					reloadAllGrid();
				});
				var paramInput={
					maubenhphamid: hdfID
	 			};						
				dlgPopup=DlgUtil.buildPopupUrl("dlgHenTraKQ","divNhapKetQua","manager.jsp?func=../canlamsang/CLS02C007_HenTraKetQua",paramInput,"Hẹn trả kết quả",600,400);
				DlgUtil.open("dlgHenTraKQ");
			}
			else {
				DlgUtil.showMsg("Bạn chưa chọn phiếu cần thực hiện",undefined,1000);
			}
		});

		$('#btnTraKetQua').on('click', function () {
			var currentPage = $('#grdDanhSachCDHA').getGridParam('page');
			var hdfID = $("#hdfIDMauBenhPham").val();
			
			var ngaydichvu = $("#hdfNgayDichVu").val();
			if(minCheckInPhieu=="1" || minCheckInPhieu=="2"){//check cấu hình cảnh báo trả kết quả sớm hơn so với cấu hình
				var tgchidinh = moment(ngaydichvu.substr(6,4)+"-"+ngaydichvu.substr(3,2)+"-"+ngaydichvu.substr(0,2)+"T"+ngaydichvu.substr(11,6)+":59");
				var now = new Date();
				var tgthuchien = moment.duration(moment(now).diff(tgchidinh)).asMinutes();
				if(minGroup0InPhieu==""){
					if(tgthuchien<minTimeInPhieu){
						DlgUtil.showConfirm("Thời gian thực hiện CĐHA dưới "+minTimeInPhieu+" phút, bạn có muốn tiếp tục trả kết quả không?",
							function(flag){
								if(flag) {
									traKetQua();
									
									reloadAllGrid();
									$("#hdfIDMauBenhPham").val(hdfID);
									var callback = function () {
							            if ($.active !== 0) {
							                setTimeout(callback, '1');
							                return;
							            }
							            var totalPage = $('#grdDanhSachCDHA').getGridParam('lastpage');
							            if(totalPage > 0) {
							            	if(currentPage > totalPage) {
							            		currentPage = totalPage;
							            	} 
							            	$('#grdDanhSachCDHA')[0].grid.populatePage(currentPage);		
							            }	            
									};
									callback();
								}
							}
						);
					}
					else {
						traKetQua();
					}
				}
				else {
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C001.CTDV", $("#hdfIDDichVuKB").val());
					if(data_ar!=undefined && data_ar!=null){					
						var codegroup=data_ar[0]["MADICHVU"];
						if(tgthuchien<minTimeInPhieu && minGroup0InPhieu.indexOf(codegroup)>-1){
							DlgUtil.showConfirm("Thời gian thực hiện CĐHA dưới "+minTimeInPhieu+" phút, bạn có muốn tiếp tục trả kết quả không?",
								function(flag){
									if(flag) {
										traKetQua();
										
										reloadAllGrid();
										$("#hdfIDMauBenhPham").val(hdfID);
										var callback = function () {
								            if ($.active !== 0) {
								                setTimeout(callback, '1');
								                return;
								            }
								            var totalPage = $('#grdDanhSachCDHA').getGridParam('lastpage');
								            if(totalPage > 0) {
								            	if(currentPage > totalPage) {
								            		currentPage = totalPage;
								            	} 
								            	$('#grdDanhSachCDHA')[0].grid.populatePage(currentPage);		
								            }	            
										};
										callback();
									}
								}
							);
						}
						else {
							traKetQua();
						}
					}
				}
			}
			else {
				traKetQua();
			}
			
			reloadAllGrid();
			$("#hdfIDMauBenhPham").val(hdfID);
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '1');
	                return;
	            }
	            var totalPage = $('#grdDanhSachCDHA').getGridParam('lastpage');
	            if(totalPage > 0) {
	            	if(currentPage > totalPage) {
	            		currentPage = totalPage;
	            	} 
	            	$('#grdDanhSachCDHA')[0].grid.populatePage(currentPage);		
	            }	            
			};
			callback();
		});

		$('#btnInPhieu').on('click', function () {
			inPhieuKetQua();
		});
		
		$('#btnDicomViewer').on('click', function() {
	        var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '?studyInstanceUID=' + studyInstanceUID;
	        
	        if(RIS_CONNECTION_TYPE == "1" || RIS_CONNECTION_TYPE == "2" && RIS_SERVICE_DOMAIN_NAME != ""){
		        console.log("request_url="+request_url);
		        $.ajax({
			        type: "GET",
			        contentType: "application/json; charset=utf-8",
			        headers: {
			        	'Ris-Access-Hash': getHashRIS(studyInstanceUID),
			            'Identify-Code': studyInstanceUID
			        },
			        data: "",
			        url: request_url,
			        success: function(data) {
			        	window.open(data.data);
			        	console.log(JSON.stringify(data));
			        },
			        error: function(xhr){
			        	console.log("get dicom viewer fail: " + JSON.stringify(xhr));
			        }
			    });
	        }
	        else if(RIS_CONNECTION_TYPE == "7" || RIS_CONNECTION_TYPE == "8" && RIS_SERVICE_DOMAIN_NAME != ""){
	        	if (RIS_PROVIDER == "INFINITT")
	        		request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '&LID=' + RIS_USERNAME + '&LPW=' + RIS_SECRET_KEY + '&AN=' + studyInstanceUID;
	        	else if (RIS_PROVIDER == "VBIT") 
	        		request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + '&acc=' + $("#hdfIDDichVuKB").val() + '&mrn=' + $("#hdfMAHOSOBENHAN").val();
	        	else if (RIS_PROVIDER == "VIETTEL")
	        		request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_DICOM_VIEWER + $("#hdfIDDichVuKB").val();
		        console.log("request_url="+request_url);
		        window.open(request_url, "_blank");
	        }
	    });
//		
//		// các nút liên quan tới ký số
//		$('#btnKySo').on('click', function () {
//			EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
//				causer = e.username;
//				capassword = e.password;
//				DlgUtil.close("divCALOGIN");
//				
//				_caRpt('1');
//				
//				reloadAllGrid();
//			});
//			EventUtil.setEvent("dlgCaLogin_close", function (e) {
//				DlgUtil.close("divCALOGIN");
//			});
//			var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
//			var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", "CA - LOGIN", 505, 268);
//			popup.open("divCALOGIN");
//		});
//
//		$('#btnHuyKySo').on('click', function () {
//			EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
//				causer = e.username;
//				capassword = e.password;
//				DlgUtil.close("divCALOGIN");
//				
//				_caRpt('2');
//				
//				reloadAllGrid();
//			});
//			EventUtil.setEvent("dlgCaLogin_close", function (e) {
//				DlgUtil.close("divCALOGIN");
//			});
//			var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
//			var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", "CA - LOGIN", 505, 268);
//			popup.open("divCALOGIN");
//		});
//		
//		function _caRpt(signType) {			
//			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", $("#hdfIDMauBenhPham").val());
//			var hosobenhanid = data_ar[0].HOSOBENHANID;
//
//			var _report_code = "CLS_KQ_CDHA";
//			var _ca_type = "1";			
//			var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.GET", $("#hdfIDMauBenhPham").val()+"$"+$("#hdfIDDichVuKB").val());
//			if(i_report_code!=undefined && i_report_code!=null) {
//				_report_code = i_report_code[0].REPORT_CODE;
//				_ca_type = i_report_code[0].CA_TYPE;
//			}
//			
//			var oData = {
//				sign_type: signType,
//				causer: causer,
//				capassword: capassword,
//				params: [
//					{
//						name: 'HOSOBENHANID',
//						type: 'String',
//						value: hosobenhanid
//					},
//					{
//						name: 'MAUBENHPHAMID',
//						type: 'String',
//						value: $("#hdfIDMauBenhPham").val()
//					},
//					{
//						name: 'ID_MAUBENHPHAM',
//						type: 'String',
//						value: $("#hdfIDMauBenhPham").val()
//					},
//					{
//						name: 'RPT_CODE',
//						type: 'String',
//						value: _report_code
//					}
//				]
//			};
//			
//			var msg = CommonUtil.caRpt(oData, _report_code, false);
//			
//			if(msg.toLowerCase().includes("ký") && msg.toLowerCase().includes("thành công") || msg.toLowerCase().includes("EMR")){
//				var param_arrAll={ 
//					MAUBENHPHAMID : $("#hdfIDMauBenhPham").val(),
//					DICHVUKHAMBENHID : $("#hdfIDDichVuKB").val(),
//					SOPHIEU : $("#hdfSoPhieu").val(),
//					REPORTCODE : _report_code,
//					LOAIKY : _ca_type,
//					TRANGTHAI : signType,
//					HETHONG : "HIS",
//					NGUOIKY : user_id
//				};
//				var param_str = JSON.stringify(param_arrAll);
//				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS05K001.LUU", param_str);
//
//				if (rs == '1') {
//					DlgUtil.showMsg(msg+"!!!",undefined,2000);
//					
//					$("#btnInPhieuKy").attr("disabled", false);					
//					$("#btnSuaNgayTN").attr("disabled", true);
//					$("#btnEditTimeCDHA").attr("disabled", true);
//					$("#btnHuyKetQua").attr("disabled", true);
//				}
//				else {
//					DlgUtil.showMsg(msg+"!",undefined,2000);
//					
//					$("#btnInPhieuKy").attr("disabled", true);
//					$("#btnSuaNgayTN").attr("disabled", false);
//					$("#btnEditTimeCDHA").attr("disabled", false);
//					$("#btnHuyKetQua").attr("disabled", false);
//				}
//			}
//			else if(msg.toLowerCase().includes("hủy") && msg.toLowerCase().includes("thành công")){
//				var param_arrAll={ 
//					MAUBENHPHAMID : $("#hdfIDMauBenhPham").val(),
//					DICHVUKHAMBENHID : $("#hdfIDDichVuKB").val(),
//					SOPHIEU : $("#hdfSoPhieu").val(),
//					REPORTCODE : _report_code,
//					LOAIKY : _ca_type,
//					TRANGTHAI : signType,
//					HETHONG : "HIS",
//					NGUOIKY : user_id
//				};
//				var param_str = JSON.stringify(param_arrAll);
//				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS05K001.LUU", param_str);
//
//				if (rs == '1') {
//					DlgUtil.showMsg(msg+"!!!",undefined,2000);
//					
//					$("#btnInPhieuKy").attr("disabled", true);					
//					$("#btnSuaNgayTN").attr("disabled", true);
//					$("#btnEditTimeCDHA").attr("disabled", true);
//					$("#btnHuyKetQua").attr("disabled", true);
//				}
//				else {
//					DlgUtil.showMsg(msg+"!",undefined,2000);
//					
//					$("#btnInPhieuKy").attr("disabled", false);
//					$("#btnSuaNgayTN").attr("disabled", false);
//					$("#btnEditTimeCDHA").attr("disabled", false);
//					$("#btnHuyKetQua").attr("disabled", false);
//				}
//			}
//			else if(msg.toLowerCase().includes("đã") && msg.toLowerCase().includes("ký")){
//				var param_arrAll={ 
//					MAUBENHPHAMID : $("#hdfIDMauBenhPham").val(),
//					DICHVUKHAMBENHID : $("#hdfIDDichVuKB").val(),
//					SOPHIEU : $("#hdfSoPhieu").val(),
//					REPORTCODE : _report_code,
//					LOAIKY : _ca_type,
//					TRANGTHAI : "1",
//					HETHONG : "HIS",
//					NGUOIKY : user_id
//				};
//				var param_str = JSON.stringify(param_arrAll);
//				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS05K001.LUU", param_str);
//
//				if (rs == '1') {
//					DlgUtil.showMsg(msg+"!!!",undefined,2000);
//					
//					$("#btnInPhieuKy").attr("disabled", false);						
//					$("#btnSuaNgayTN").attr("disabled", true);
//					$("#btnEditTimeCDHA").attr("disabled", true);
//					$("#btnHuyKetQua").attr("disabled", true);
//				}
//				else {
//					DlgUtil.showMsg(msg+"!",undefined,2000);
//					
//					$("#btnInPhieuKy").attr("disabled", false);
//					$("#btnSuaNgayTN").attr("disabled", false);
//					$("#btnEditTimeCDHA").attr("disabled", false);
//					$("#btnHuyKetQua").attr("disabled", false);
//				}
//			}
//			else if(msg.toLowerCase().includes("chưa") && msg.toLowerCase().includes("ký")){
//				var param_arrAll={ 
//					MAUBENHPHAMID : $("#hdfIDMauBenhPham").val(),
//					DICHVUKHAMBENHID : $("#hdfIDDichVuKB").val(),
//					SOPHIEU : $("#hdfSoPhieu").val(),
//					REPORTCODE : _report_code,
//					LOAIKY : _ca_type,
//					TRANGTHAI : "2",
//					HETHONG : "HIS",
//					NGUOIKY : user_id
//				};
//				var param_str = JSON.stringify(param_arrAll);
//				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS05K001.LUU", param_str);
//
//				if (rs == '1') {
//					DlgUtil.showMsg(msg+"!!!",undefined,2000);
//					
//					$("#btnInPhieuKy").attr("disabled", true);
//					$("#btnSuaNgayTN").attr("disabled", true);
//					$("#btnEditTimeCDHA").attr("disabled", true);
//					$("#btnHuyKetQua").attr("disabled", true);
//				}
//				else {
//					DlgUtil.showMsg(msg+"!",undefined,2000);
//					
//					$("#btnInPhieuKy").attr("disabled", false);
//					$("#btnSuaNgayTN").attr("disabled", false);
//					$("#btnEditTimeCDHA").attr("disabled", false);
//					$("#btnHuyKetQua").attr("disabled", false);
//				}
//			}
//			else {
//				DlgUtil.showMsg(msg,undefined,5000);
//			}
//		}
//
//		$('#btnInPhieuKy').on('click', function () {
//			if ($("#hdfIDMauBenhPham").val() == "") {
//				DlgUtil.showMsg("Chưa chọn phiếu để in");
//				return;
//			}
//			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", $("#hdfIDMauBenhPham").val());
//			if(data_ar==null || data_ar==undefined){
//				DlgUtil.showMsg("Không tìm được thông tin bệnh nhân");
//				return;
//			}
//			var hosobenhanid = data_ar[0].HOSOBENHANID;
//			
//			var CDHA_INPHIEU_KYSO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_INPHIEU_KYSO');
//
//			var _report_code = "CLS_KQ_CDHA";
//			var _ca_type = "1";	
//			var data_ar2 = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", $("#hdfIDMauBenhPham").val()+"$"+$("#hdfIDDichVuKB").val());
//			if(data_ar2!=undefined && data_ar2!=null) {
//				_report_code = data_ar2[0].REPORTCODE;
//				_ca_type = data_ar2[0].LOAIKY;
//			}
//			
//			if(CDHA_INPHIEU_KYSO == "0"){
//				var par_rpt_KySo = [
//					{
//						name: 'HOSOBENHANID',
//						type: 'String',
//						value: hosobenhanid
//					},
//					{
//						name: 'MAUBENHPHAMID',
//						type: 'String',
//						value: $("#hdfIDMauBenhPham").val()
//					},
//					{
//						name: 'ID_MAUBENHPHAM',
//						type: 'String',
//						value: $("#hdfIDMauBenhPham").val()
//					},
//					{
//						name: 'RPT_CODE',
//						type: 'String',
//						value: _report_code
//					}
//				];
//	
//				CommonUtil.openReportGetCA2(par_rpt_KySo, false);
//	        }
//			else if(CDHA_INPHIEU_KYSO == "1"){
//				var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_REPORT + '?orderId=' + studyInstanceUID;
//		        console.log("request_url="+request_url);
//		        $.ajax({
//			        type: "GET",
//			        contentType: "application/json; charset=utf-8",
//			        headers: {
//			        	'Ris-Access-Hash': getHashRIS(studyInstanceUID),
//			            'Identify-Code': studyInstanceUID
//			        },
//			        data: "",
//			        xhrFields: { responseType: "arraybuffer" },
//			        url: request_url,
//			        success: function(data) {
//			        	var file = new Blob([data], { type: 'application/pdf' });
//						var fileURL = URL.createObjectURL(file);
//						window.open(fileURL);
//			        },
//			        error: function(xhr){
//			        	console.log("get report fail: " + JSON.stringify(xhr));
//			        }
//			    });
//			}
//			else if(CDHA_INPHIEU_KYSO == "2"){
//				if(data_ar2==null || data_ar2==undefined){
//					DlgUtil.showMsg("Không tìm được thông tin ký số");
//					return;
//				}
//				var hethong = data_ar2[0]["HETHONG"];
//				if(hethong=="RIS"){
//					var request_url = RIS_SERVICE_DOMAIN_NAME + RIS_GET_REPORT + '?orderId=' + studyInstanceUID;
//			        console.log("request_url="+request_url);
//			        $.ajax({
//				        type: "GET",
//				        contentType: "application/json; charset=utf-8",
//				        headers: {
//				        	'Ris-Access-Hash': getHashRIS(studyInstanceUID),
//				            'Identify-Code': studyInstanceUID
//				        },
//				        data: "",
//				        xhrFields: { responseType: "arraybuffer" },
//				        url: request_url,
//				        success: function(data) {
//				        	var file = new Blob([data], { type: 'application/pdf' });
//							var fileURL = URL.createObjectURL(file);
//							window.open(fileURL);
//				        },
//				        error: function(xhr){
//				        	console.log("get report fail: " + JSON.stringify(xhr));
//				        }
//				    });
//				}
//				else {
//					var par_rpt_KySo = [
//						{
//							name: 'HOSOBENHANID',
//							type: 'String',
//							value: hosobenhanid
//						},
//						{
//							name: 'MAUBENHPHAMID',
//							type: 'String',
//							value: $("#hdfIDMauBenhPham").val()
//						},
//						{
//							name: 'ID_MAUBENHPHAM',
//							type: 'String',
//							value: $("#hdfIDMauBenhPham").val()
//						},
//						{
//							name: 'RPT_CODE',
//							type: 'String',
//							value: _report_code
//						}
//					];
//		
//					CommonUtil.openReportGetCA2(par_rpt_KySo, false);
//				}
//			}
//		});
//		// hết phần ký số
	}

	function reloadAllGrid(){
		phongthId = $("#cboPhongThucHien").val();
		if(searchType == 0) {
			loadDanhSachCDHA($("#cboPhongThucHien").val(), $("#txtTuNgay").val(),$("#txtDenNgay").val(), $("input:radio[name='rdoTrangThai']:checked").val(), "");
		} else if(searchType == 1){
			timKiemBangMayQuet($("#cboPhongThucHien").val(), $("#txtTuNgay").val(), $("#txtDenNgay").val(), $("input:radio[name='rdoTrangThai']:checked").val(), $('#toolbarIdtxtTimKiem').val());
		}
	}
	
	function validateDate(ctrId, key, checkNull){
		var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4}$/;
		
		if(checkNull && ($('#' + ctrId).val() == null || $('#' + ctrId).val() == '')){
			DlgUtil.showMsg(key + $.i18n("require"));
			$('#' + ctrId).focus();
			return false;
		}
		
		if($('#' + ctrId).val().trim().length > 0 && !datetimeRegex.test($('#' + ctrId).val()) && !checkDate($('#' + ctrId).val())){
			DlgUtil.showMsg(key + $.i18n("date_type_invalid"));
			$('#' + ctrId).focus();
			return false;		
		}
		return true;
	}
	
	function kiemTra(){
		if(validateDate("txtTuNgay","Từ ngày ", false) && validateDate("txtDenNgay", "Đến ngày ", false)){
			var txtTuNgay = $("#txtTuNgay").val();
			var txtDenNgay = $("#txtDenNgay").val();
			
			if(!compareDate(txtTuNgay,txtDenNgay,"DD/MM/YYYY")){
				DlgUtil.showMsg("Từ ngày không được lớn hơn Đến ngày");	
				$("#txtTuNgay").focus();
				return false;
			}
		}		
		return true;
	}
	
	//nghiant 24102017 
	function kiemTraNgayCD(){
		if(validateDate("txtTuNgayCD","Từ ngày ", false) && validateDate("txtDenNgayCD", "Đến ngày ", false)){
			var txtTuNgay = $("#txtTuNgayCD").val();
			var txtDenNgay = $("#txtDenNgayCD").val();
			
			if(!compareDate(txtTuNgay,txtDenNgay,"DD/MM/YYYY")){
				DlgUtil.showMsg("Từ ngày không được lớn hơn Đến ngày");	
				$("#txtTuNgayCD").focus();
				return false;
			}
		}		
		return true;
	}
	//end nghiant 24102017 

	// load Danh sách bệnh nhân
	function loadDanhSachCDHA(phong, tungay, denngay, trangthai, tukhoa) {
		if(kiemTra()){
			var param = RSUtil.buildParam("",[user_id, schema, province_id, hospital_id, phong, tungay, denngay, trangthai, tukhoa]);
			if(trangthai=="3"){
				GridUtil.loadGridBySqlPage("grdDanhSachCDHA","CLS02R001.DS3",param);
			} else {
				GridUtil.loadGridBySqlPage("grdDanhSachCDHA","CLS02R001.DS",param);
			}
		}
	}

	// tìm kiếm bằng máy quét mã vạch
	function timKiemBangMayQuet(phong, tungay, denngay, trangthai, barcode) {
		if(kiemTra()){
			var param = RSUtil.buildParam("", [user_id, schema, province_id, hospital_id, phong, tungay, denngay, trangthai, barcode]);
			
			GridUtil.loadGridBySqlPage("grdDanhSachCDHA","CLS02R001.SIDS",param);	
			
			var callback = function () {
	            if ($.active !== 0) {
	                setTimeout(callback, '50');
	                return;
	            }		            
				var row = $("#grdDanhSachCDHA").jqGrid('getRowData', 1);
				
				if(row["TRANGTHAIMAUBENHPHAM"]=="3") { 
					$('#rdoTraKetQua').prop('checked', true);
				}
				else {
					$('#rdoChoThucHien').prop('checked', true);
				}

				if(row["PHONGCHUYENDENID"] != undefined && row["PHONGCHUYENDENID"] != null){
					$("#cboPhongThucHien").val(row["PHONGCHUYENDENID"]);
				}
				
				if($("#grdDanhSachCDHA").getGridParam("reccount") > 0) { 
					$("#txtTuNgay").val(row["NGAYMAUBENHPHAM"].slice(0,10));
				}
				
				$("#grdDanhSachCDHA").setSelection(1, true);
	        };
	        callback();	
		}
	}

	// hủy dịch vụ được chỉ định
	function huyDichVu(){
		var rowId =$("#grdDanhSachChiDinh").jqGrid('getGridParam','selrow'); 
		
		if(rowId==null){
			DlgUtil.showMsg("Chưa chọn dịch vụ",undefined,2000);	
		}
		else {
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C001.HDV", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Hủy bỏ dịch vụ thành công",undefined,1000);				
			}
			else if(rs == '2'){
				DlgUtil.showMsg("Dịch vụ đã thu tiền, hãy hủy phiếu thu trước!",undefined,3000);	
			}
			else if(rs == '3'){
				DlgUtil.showMsg("Dịch vụ đã trả kết quả, hãy hủy kết quả trước!",undefined,3000);	
			}
			else {
				DlgUtil.showMsg("Bạn không có quyền thực hiện thao tác này: "+rs,undefined,3000);
			}
		}
	}

	// hoàn tác việc hủy dịch vụ được chỉ định
	function khoiPhucDichVu(){
		var rowId =$("#grdDanhSachChiDinh").jqGrid('getGridParam','selrow'); 
		
		if(rowId==null){
			DlgUtil.showMsg("Chưa chọn dịch vụ",undefined,2000);	
		}
		else {
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C001.KPDV", param);
			
			if(rs == '1'){
				DlgUtil.showMsg("Khôi phục dịch vụ thành công",undefined,1000);				
			} else {
				DlgUtil.showMsg("Có lỗi xảy ra, mã lỗi: "+rs,undefined,3000);	
			} 
		}
	}

	// Bổ sung dịch vụ vào phiếu đã có
	function boSungDichVu(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần bổ sung",undefined,2000);	
		}
		else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.EV003", param.join('$'));
			
			var paramInput = { 
				benhnhanid : data_ar[0].BENHNHANID, 
				mabenhnhan : data_ar[0].MABENHNHAN, 
				khambenhid : data_ar[0].KHAMBENHID, 
				tiepnhanid : data_ar[0].TIEPNHANID, 
				hosobenhanid : data_ar[0].HOSOBENHANID, 
				doituongbenhnhanid : data_ar[0].DOITUONGBENHNHANID, 
				loaitiepnhanid : data_ar[0].LOAITIEPNHANID, 
				subDeptId : opt.subdept_id, 
				deptId : opt.dept_id, 
				mbpid_bosungdv : $("#hdfIDMauBenhPham").val(),
				loaiPhieu: 2
			};
			
			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5,paramInput,"Tạo phiếu chỉ định dịch vụ",1300,600);
			DlgUtil.open("divDlgDichVu");
		}
	}

	// tiếp nhận bệnh nhân chụp chiếu
	function batDauThucHien() {
		var rowId = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');

		if (rowId != null) {
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val() + '$' +
						$("#hdfIDDichVuKB").val();

			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02R001.TN", param);
			if (rs == '1') {
				if(showForm=="1"){
					
					var url = "manager.jsp?func=../canlamsang/CLS02C003_TraKetQuaCDHA&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&idketquacls="+$("#hdfIDKetQuaCLS").val()+"&type=update"
								+"&rolePTH="+rolePTH+"&resizingTable="+resizingTable;

					EventUtil.setEvent("CLS02C003_LUU", function (e) {
						reloadAllGrid();
					});
					
					EventUtil.setEvent("CLS02C003_LUU_DONG", function (e) {
				    	DlgUtil.showMsg('Lưu kết quả thành công',undefined,500);
						reloadAllGrid();
						DlgUtil.close("dlgSuaKetQua");
					});
					
					EventUtil.setEvent("CLS02C003_HUY", function (e) {
						DlgUtil.close("dlgSuaKetQua");
					});
					
					var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaKetQua", "divSuaKetQua", url, {}, "Kết quả chẩn đoán", 1160, 600);
					dlgPopup.open("dlgSuaKetQua");
				}
				else if(showForm=="2"){
					
					var url = "manager.jsp?func=../canlamsang/CLS02C003_TraKetQuaCDHA2&idmaubenhpham="+$("#hdfIDMauBenhPham").val()+"&idketquacls="+$("#hdfIDKetQuaCLS").val()+"&type=update"
								+"&rolePTH="+rolePTH+"&resizingTable="+resizingTable;

					EventUtil.setEvent("CLS02C003_LUU", function (e) {
						reloadAllGrid();
					});
					
					EventUtil.setEvent("CLS02C003_LUU_DONG", function (e) {
				    	DlgUtil.showMsg('Lưu kết quả thành công',undefined,500);
						reloadAllGrid();
						DlgUtil.close("dlgSuaKetQua");
					});
					
					EventUtil.setEvent("CLS02C003_HUY", function (e) {
						DlgUtil.close("dlgSuaKetQua");
					});
					
					var dlgPopup = DlgUtil.buildPopupUrl("dlgSuaKetQua", "divSuaKetQua", url, {}, "Kết quả chẩn đoán", screen.width-10, screen.height-175);
					dlgPopup.open("dlgSuaKetQua");
				}
				else {
					DlgUtil.showMsg("Đã tiếp nhận thành công",undefined,500);
				}
			}
			else if(rs == "5"){
				DlgUtil.showMsg("Bệnh nhân chưa đóng đủ tiền để thực hiện, mời bệnh nhân đến quầy viện phí để nộp tiền!",undefined,3000);
			}
			else {
				DlgUtil.showMsg("Có lỗi xảy ra, không thể tiếp nhận",undefined,1500);
			}
		} else {
			DlgUtil.showMsg("Bạn chưa chọn phiếu cần thực hiện",undefined,1000);
		}
	}
	
	// hủy tiếp nhận bệnh nhân
	function huyThucHien() {
		var rowId = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');

		if (rowId != null) {
        	var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val();
			
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02R001.HTN", param);
			
			if (rs == '1') {
				DlgUtil.showMsg('Hủy thực hiện chỉ định thành công',undefined,500);
			} else if (rs.result == '3') {
				DlgUtil.showMsg('Không thể hủy thực hiện chỉ định đã nhập kết quả',undefined,1500);
			} else {
				DlgUtil.showMsg('Có lỗi xảy ra, không thể hủy thực hiện chỉ định',undefined,1500);
			}
		} else {
			DlgUtil.showMsg('Bạn chưa chọn phiếu cần thực hiện',undefined,1500);
		}
	}

	// lưu kết quả khi người dùng nhập ở lưới kết quả phía dưới
	function traKetQua() {
		
		var param_arr = $("#grdDanhSachCDHA").jqGrid('getRowData');
		if (param_arr != null && param_arr.length > 0) {
			
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val()+'$'+
						$("#hdfIDDichVuKB").val()+'$'
						;

			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS02R001.TKQ", param);

			if (rs == '1') {
				var inphieu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'CDHA_INPHIEU_KHI_TRAKETQUA');
				if(inphieu=="1") inPhieuKetQua();
				
				DlgUtil.showMsg('Trả kết quả thành công',undefined,600);
				$("#hdfIDMauBenhPham").val("");  // làm rỗng grid kết quả khi phiếu đã ko còn trên grid dsbn
				
				//nghiant VNPTHISL2-490 25092017 Thực hiện gửi tin nhắn trả kết quả công cho khách hàng nếu cấu hình gửi tin 
				if (onOffSendSMS=="1"){
					var content = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_SMS_TRA_KQ_CDHA');
					if(content== null || content == undefined || content =="0" || content == ""){
						content=="Bệnh nhân TENBENHNHANXXX đã được trả kết quả Chẩn đoán hình ảnh cho phiếu SOPHIEUXXX";
					}
					content = content.replace("TENBENHNHANXXX",tenBN).replace("SOPHIEUXXX",sophieuBN);
					if(sdtBN!=null && sdtBN!=undefined){
						if(sdtBN.startsWith("0")){
							sdtBN = "84"+sdtBN.slice(1, sdtBN.length);
						} else if(!sdtBN.startsWith("84")){
							sdtBN="84"+sdtBN;
						}
					}
					$("input[name=phoneNumber]").val(sdtBN);
					$("input[name=contentSMS]").val(content);
		       		
					document.sendSMSFormCDHA.submit();
				}
				//end nghiant VNPTHISL2-490 25092017
			}
			else if(rs=="2"){
				DlgUtil.showMsg("Bạn vừa Trả kết quả sau thời điểm Kết thúc bệnh án. Bạn cần điều chỉnh lại thời gian Trả kết quả phiếu "+sophieuBN+"!");
			}
			else if(rs=="3"){
				DlgUtil.showMsg("Bạn chưa chọn Kĩ thuật viên thực hiện!",undefined,1500);
			}
			else if(rs=="4"){
				DlgUtil.showMsg("Chưa thể trả kết quả do bệnh nhân chưa thực hiện thanh toán viện phí hoặc có số tiền tạm ứng nhỏ hơn tổng tiền dịch vụ!",undefined,8500);
			}
			else if(rs=="5"){
				DlgUtil.showMsg("Chưa nhập số lượng phim và loại phim xquang!",undefined,1500);
			}
			else if(rs=="6"){
				DlgUtil.showMsg("Thời gian thực hiện CĐHA dưới "+b_minu+" phút, không cho phép trả kết quả!",undefined,4000);
			}
			else if(rs=="7"){
				DlgUtil.showMsg("Thời gian trả kết quả trên cùng một mã máy giữa 2 lần thực hiện dưới "+b_min_time+" phút. Vui lòng cập nhật thời gian hợp lý!",undefined,4000);
			}
			else if(rs=="9"){
				DlgUtil.showMsg("Hệ thống từ chối Trả kết quả sau thời điểm Kết thúc bệnh án! Liên hệ quản trị viên để được hỗ trợ");
			}
			else if(rs == "10"){
				DlgUtil.showMsg("Trả kết quả thành công. Số lượng phim và loại phim theo định mức (nếu có) đã được cập nhật.",undefined,3000);
			}
			else if(rs == "11"){
				DlgUtil.showMsg("Trả kết quả thành công. Thuốc vật tư đi kèm dịch vụ đã được cập nhật.",undefined,3000);
			}
			else if(rs == "12"){
				DlgUtil.showMsg("Trả kết quả thành công. Thuốc vật tư đi kèm dịch vụ (nếu có) đã được cập nhật. Số lượng phim và loại phim theo định mức (nếu có) đã được cập nhật.",undefined,3000);
			}
			else if(rs == "13"){
				DlgUtil.showMsg("Trả kết quả thành công. Tuy nhiên mã máy thực hiện chưa được nhật!");
			}
			else if(rs == "14"){
				DlgUtil.showMsg("Chưa nhập mã máy thực hiện. Không cho phép trả kết quả!");
			}
			else if(rs=="-1"){
				DlgUtil.showMsg("Có lỗi xảy ra khi cấp thuốc tự động. Bạn cần tạo phiếu thuốc thủ công!");
			}
			else if(isNaN(rs)){
				DlgUtil.showMsg("Có lỗi xảy ra khi cấp thuốc tự động: "+rs);
			}
			else {
				DlgUtil.showMsg('Có lỗi xảy ra: '+rs);
			}
		}
		else {
			DlgUtil.showMsg('Bạn chưa chọn phiếu cần thực hiện',undefined,1500);
		}
		
	}
	
	function luuKetQua() {
		var reccount = $("#grdKetQuaChanDoan").getGridParam("reccount");
		for (var i = 1; i <= reccount; i++) {
			$("#grdKetQuaChanDoan").jqGrid('saveRow', i, 'clientArray');
			var row = $("#grdKetQuaChanDoan").jqGrid('getRowData',i);

			$('#grdKetQuaChanDoan').jqGrid('setCell', i, 'MAMAY', $("#" + row.DICHVUTHUCHIENID + "_cboMAMAY").val());
		}

		var param_arr = $("#grdKetQuaChanDoan").jqGrid('getRowData');

		if (param_arr != null && param_arr.length > 0) {
			var param_arrAll=[];
			for(var i=0; i<param_arr.length; i++){
//				 param_arr[i]['PHUMO1']=$("#cboPHUMO1").val();
				 param_arrAll.push(param_arr[i]);
			}
			var param_str = JSON.stringify(param_arrAll);
			var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
						$("#hdfIDMauBenhPham").val() + '$' +
						$("#hdfIDDichVuKB").val() + '$' +
						param_str+'$$'+
						$("#hdfIDGhiChu").val();

			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS02C001.LKQ.TEMP", param);

			if (rs == '1') {
				DlgUtil.showMsg('Lưu kết quả thành công',undefined,500);
				$("#hdfIDMauBenhPham").val("");
			}
			else if(rs=="3"){
				DlgUtil.showMsg("Bạn chưa chọn Kĩ thuật viên thực hiện!",undefined,1500);
			} 
			else {
				DlgUtil.showMsg('Có lỗi xảy ra, không thể lưu kết quả',undefined,1500);
			}
		}
		else {
			DlgUtil.showMsg('Bạn chưa chọn phiếu cần thực hiện',undefined,1500);
		}
		
		$("#grdKetQuaChanDoan").trigger("reloadGrid");
	}

	// hủy trả kết quả về phòng khám
	function huyKetQua() {
		var rowId = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');
		console.log('tiepnhanid: '+tiepnhanid+', phong_chuyen_den_id: '+phong_chuyen_den_id+', opt.subdept_id: '+opt.subdept_id+', hdfIDMauBenhPham: '+$("#hdfIDMauBenhPham").val());
		if (rowId == null) {
			DlgUtil.showMsg('Bạn chưa chọn phiếu cần thực hiện',undefined,1000);
		} 
		else {
			if(!rolePTH){
				DlgUtil.showMsg("Không phải phòng thực hiện, không thể hủy kết quả",undefined,500);	
			} else {
				var param = user_id+'$'+schema+'$'+province_id+'$'+hospital_id+'$'+
							$("#hdfIDMauBenhPham").val()+'$'+
							$("#hdfIDDichVuKB").val();

				var rs = jsonrpc.AjaxJson.ajaxCALL_SP_S("CLS02R001.HKQ", param);

				if (rs=='1') {
					DlgUtil.showMsg('Hủy kết quả thành công',undefined,500);
					$("#hdfIDMauBenhPham").val("");
				} 
				else if(rs=='2'){
					DlgUtil.showMsg("Đã đóng bệnh án, không thể hủy kết quả",undefined,1000);	
				} 
				else if(rs=='3'){
					DlgUtil.showMsg("Không phải người duyệt kết quả, không thể hủy kết quả",undefined,1000);	
				} 
				else if(rs=="-1"){
					DlgUtil.showMsg("Có lỗi xảy ra khi xóa phiếu cấp thuốc tự động. Bạn cần xóa phiếu thuốc thủ công!");
				}
				else if(isNaN(rs)){
					DlgUtil.showMsg("Có lỗi xảy ra khi xóa phiếu cấp thuốc tự động: "+rs);
				}
				else {
					DlgUtil.showMsg('Có lỗi xảy ra: '+rs);
				}
			}
		}
	}
	
	// in phiếu kết quả
	function inPhieuKetQua() {
		if ($("#hdfIDMauBenhPham").val()=="") {
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name: 'id_maubenhpham', type: 'String', value: $("#hdfIDMauBenhPham").val()},
			    {name: 'maubenhphamids', type: 'String', value: $("#hdfIDMauBenhPham").val()},
			    {name: 'maubenhphamid', type: 'String', value: $("#hdfIDMauBenhPham").val()}
			];
			
			//tuyennx_add_start_20171207  L2K47TW-48
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var max = (data_ar.length>6?6:data_ar.length);
			for(var i=0; i<max; i++){
				var hinh_anh = data_ar[i]["DUONGDANFILE"];
				if (hinh_anh != "" && hinh_anh != null) {
					par.push({name: 'hinh_anh'+i, type: 'Image', value: hinh_anh });
				}
			}
			for(var i=0; i<max; i++){
				var ten_file = data_ar[i]["TENFILE"];
				if (ten_file != "" && ten_file != null) {
					par.push({name: 'ten_file'+i, type: 'String', value: ten_file });
				}
			}
			//tuyennx_add_end_20171207 
			
			var reccount = $("#grdKetQuaChanDoan").getGridParam("reccount");
			
			//huongpv add -- in phiêu theo report_code_kq trong bang dmc_dichvu
			var INPHIEU_THEO_DV= jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","INPHIEU_THEO_DV");
			var TACH_KQ_CDHA = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","TACH_KQ_CDHA");
			
			if(INPHIEU_THEO_DV!=null && INPHIEU_THEO_DV=="1"){
				if (i_hid=="1007" && reccount>1) {
					inphieu_1007(par);					
					return;
				} 
				var _par_code = [$("#hdfIDMauBenhPham").val()];	
				var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE_KQ",_par_code.join('$'));
				for(var i=0;i<i_report_code.length;i++){
					var _report_code = i_report_code[i].REPORT_CODE;
					var k = _report_code.split(';');
					if(k.length > 1){
						for(var j=0;j<k.length;j++){								
							openReport('window', k[j], 'pdf', par);
						}
					}
					else {
						if(TACH_KQ_CDHA!=0){
							var idss = $('#grdKetQuaChanDoan').jqGrid('getGridParam', 'selarrrow');
							var n = idss.length==0?reccount:idss.length
							for (t = 0; t < n; t++)
							{
								var rowSelected =  $('#grdKetQuaChanDoan').jqGrid('getRowData', idss[t]);
								var parInPhieu = par.concat({name: 'dichvukhambenhid', type: 'String', value: rowSelected.DICHVUKHAMBENHID });								
								openReport('window', _report_code, 'pdf', parInPhieu);
							}
						} else {
							openReport('window', _report_code, 'pdf', par);
						}
					}
 			   }
 			   return;				
			}

			if (TACH_KQ_CDHA == i_hid){  //cau hinh lai TACH_KQ_CDHA =1111 va TACH_KQ_CDHA =1007 
				var par2 = [
				    {name: 'id_maubenhpham', type: 'String', value: $("#hdfIDMauBenhPham").val()},
				    {name: 'maubenhphamids', type: 'String', value: $("#hdfIDMauBenhPham").val()},
				    {name: 'maubenhphamid', type: 'String', value: $("#hdfIDMauBenhPham").val()}
				];
				
				var dsdichvukbid= '';//truyen vao ham in phieu  và tra ket qua
				var idss = $('#grdKetQuaChanDoan').jqGrid('getGridParam', 'selarrrow');
				var k=0;
				var n = idss.length;
				if(n > 0){
					for (i = 0; i < n; i++)
					{
						var rowSelected =  $('#grdKetQuaChanDoan').jqGrid('getRowData', idss[i]);
						if (dsdichvukbid != '' && dsdichvukbid != ""){
							dsdichvukbid = dsdichvukbid+ ','+ rowSelected.DICHVUKHAMBENHID;
						} else {
							dsdichvukbid = rowSelected.DICHVUKHAMBENHID;
						}
						
						var data_ar2 = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.DSANHBN", $("#hdfIDMauBenhPham").val()+'$'+rowSelected.DICHVUKHAMBENHID);
						for(var j=0; j<data_ar2.length; j++){
							var hinh_anh = data_ar2[j]["DUONGDANFILE"];
							if (hinh_anh != "" && hinh_anh != null) {
								par2.push({name: 'hinh_anh'+k, type: 'Image', value: hinh_anh });
								k=k+1;
							}
						}
					}
				}
				else {
					var param2 = [$("#hdfIDMauBenhPham").val()];
					var data_ar2 = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param2.join('$'));
					
					for(var i=0; i<data_ar2.length; i++){
						var hinh_anh = data_ar2[i]["DUONGDANFILE"];
						if (hinh_anh != "" && hinh_anh != null) {
							par2.push({name: 'hinh_anh'+i, type: 'Image', value: hinh_anh });
						}
					}
				}
				
				par2.push({name:'dsdichvukbid',type:'String',value:dsdichvukbid});
				openReport('window', 'PHIEU_KETQUA_CDHA', 'pdf', par2);
				return;
			}
			else {
				if(i_hid=="10284"){
					var dvkbDatas = $('#grdKetQuaChanDoan').jqGrid('getGridParam', 'selarrrow');
					if (dvkbDatas.length == 0) {
						for (var i = 1; i <= reccount; i++) {
							var row = $("#grdKetQuaChanDoan").jqGrid('getRowData',i);		            		 
		            		var parInnhieu = par.concat([{name: 'dichvukhambenhid', type: 'String', value: row.DICHVUKHAMBENHID }]);							
							openReport('window', 'CLS_KQ_CDHA', 'pdf', parInnhieu);
						}
					}
					else {
						dvkbDatas.forEach(function(item) {
							var dvkb = $('#grdKetQuaChanDoan').jqGrid('getRowData', item);							
							var parInnhieu = par.concat([{name: 'dichvukhambenhid', type: 'String', value: dvkb.DICHVUKHAMBENHID }]);
							openReport('window', 'CLS_KQ_CDHA', 'pdf', parInnhieu);
						});
					}
					return;
				} else {
					openReport('window', 'CLS_KQ_CDHA', 'pdf', par);
					return;	
				}
			}

			if (traintungPhieu == "1"){
				var dsdichvukbid= '';//truyen vao ham in phieu  và tra ket qua
	            var idss = $('#grdKetQuaChanDoan').jqGrid('getGridParam', 'selarrrow');
	            for (i = 0, n = idss.length; i < n; i++)
	            {
	            	var rowSelected =  $('#grdKetQuaChanDoan').jqGrid('getRowData', idss[i]);
	            	console.log("rowSelected.DICHVUKHAMBENHID: "+rowSelected.DICHVUKHAMBENHID);
	            	if (dsdichvukbid != '' && dsdichvukbid != ""){
	            		dsdichvukbid = dsdichvukbid+ ','+ rowSelected.DICHVUKHAMBENHID;
	            	} else {
	            		dsdichvukbid = rowSelected.DICHVUKHAMBENHID;
	            	}
	            }
	            if ((dsdichvukbid == '' || dsdichvukbid == "") && reccount >=2){
	            	DlgUtil.showMsg("Bạn chưa chọn dịch vụ để in!",undefined,1500);
	            } else {
	            	if(reccount == 1){
            			var row = $("#grdKetQuaChanDoan").jqGrid('getRowData',1);
            			dsdichvukbid=row.DICHVUKHAMBENHID;
	            	}
	            	par.push({name:'dsdichvukbid',type:'String',value:dsdichvukbid});
	            	openReport('window', 'CLS_KQ_CDHA', 'pdf', par);
	            }
			} else {
				var dsdichvukbid= '';//truyen vao ham in phieu  và tra ket qua
				for (var i = 1; i <= reccount; i++) {
					var row = $("#grdKetQuaChanDoan").jqGrid('getRowData',i);
            		if(dsdichvukbid != '' && dsdichvukbid != ""){
	            		dsdichvukbid = dsdichvukbid + ',' + row.DICHVUKHAMBENHID;
	            	} else {
	            		dsdichvukbid = row.DICHVUKHAMBENHID;
	            	}
				}
				par.push({name:'dsdichvukbid',type:'String',value:dsdichvukbid});
				openReport('window', 'CLS_KQ_CDHA', 'pdf', par);
			}
		}
	}
	
	//in phieu kq san nhi vinh phuc
	function inphieu_1007(par){		
			var TACH_KQ_CDHA = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","TACH_KQ_CDHA");
			if (TACH_KQ_CDHA == 1){		
				openReport('window', 'PHIEU_KETQUA_CDHA', 'pdf', par);
			} else {
				openReport('window', 'CLS_KQ_CDHA', 'pdf', par);
			}			
	}
	
	// in phiếu kết quả x quang
	function inPhieu19BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
		  if(i_hid=='939'){			  
				   var sql_par = [];
				   sql_par.push({
						"name" : "[0]",
						"value" : $("#hdfIDMauBenhPham").val()
					});
				   var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("CLS01.DS.DVKB", sql_par);
				   var data_ar = JSON.parse(data);		  
				   if(data_ar!=null && data_ar.length>0){
					  for(var i=0;i<data_ar.length;i++){
						var dichvukhambenhid = data_ar[i]["DICHVUKHAMBENHID"];					
						 var par = [
							    {name: 'maubenhphamid', type: 'String', value:$("#hdfIDMauBenhPham").val()},
							    {name: 'lanthu', type: 'String', value:'' },
							    {name: 'dichvukhambenhid', type: 'String', value:dichvukhambenhid }
							];
						 openReport('window', 'NTU011_PHIEUCHIEUCHUPXQUANG_19BV01_QD4069_A4', 'pdf', par);  
					}
			  }
			  
		  }else{
			    var par = [
				    {name: 'maubenhphamid', type: 'String', value:$("#hdfIDMauBenhPham").val()},
				    {name: 'lanthu', type: 'String', value:'' }
				];
				
				openReport('window', 'NTU011_PHIEUCHIEUCHUPXQUANG_19BV01_QD4069_A4', 'pdf', par);  
		  }
			
		}
	}

	// in phiếu chụp cắt lớp vi tính
	function inPhieu20BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name: 'maubenhphamid', type: 'String', value: $("#hdfIDMauBenhPham").val()},
			    {name: 'lanthu', type: 'String', value: ''}
			];
			
			openReport('window', 'NTU035_PHIEUCHUPCATLOPVITINH_20BV01_QD4069_A4', 'pdf', par);
		}
	}

	// in phiếu chụp cộng hưởng từ
	function inPhieu21BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''}
			];
			
			openReport('window', 'NTU036_PHIEUCHUPCONGHUONGTU_21BV01_QD4069_A4', 'pdf', par);
		}
	}
	
	//inPhieuLoangXuong
	function inPhieuLoangXuong() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''}
			];
			
			openReport('window', 'PHIEU_DO_MATDO_XUONG_A4', 'pdf', par);
		}
	}
	
	//08062018 TTQNI
	function inPhieu27BV01TEST_TAMLY(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			
			par.push({name:'type',type:'String',value:"1"});
			openReport('window', 'PHIEU_TEST_TAMLY_QNI', 'pdf', par);
		}
	}
	//08062018
	function inPhieu27BV01TV_TAMLY(){
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {			
			var par = [ 
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			
			par.push({name:'type',type:'String',value:"2"});
			openReport('window', 'PHIEU_TEST_TAMLY_QNI', 'pdf', par);
		}
	}

	// in phiếu siêu âm
	function inPhieu22BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var row = data_ar[0];
			console.log(JSON.stringify(row));		
			
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh!="" && hinh_anh!=null) par.push({name:'hinh_anh'+(i+1),type:'Image',value:hinh_anh});
			}
			
			openReport('window', 'NTU012_PHIEUSIEUAM_22BV01_QD4069_A4', 'pdf', par);
		}
	}
	// in phiếu siêu âm màu
	function inPhieu27BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var row = data_ar[0];
			console.log(JSON.stringify(row));
			
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh!="" && hinh_anh!=null) par.push({name:'hinh_anh'+(i+1),type:'Image',value:hinh_anh});
			}
			
			if(hospital_id==978){
				if(data_ar.length>=2){
					openReport('window', 'NTU012_PHIEUSIEUAMMAU_24BV01_A4', 'pdf', par);					
				} else {
					openReport('window', 'NTU012_PHIEUSIEUAMMAU_24BV01_1ANH_A4', 'pdf', par);
				}
				return;
			}
			
			openReport('window', 'NTU012_PHIEUSIEUAMMAU_24BV01_A4', 'pdf', par);
		}
	}
	
	// in phiếu siêu âm DOPPLER
	function inPhieu31BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {			
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			//tuyennx_add_start_20170818 L2K47TW-128
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var hinh_anh1 = data_ar[0]["DUONGDANFILE"];
			
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh1',type:'Image',value:hinh_anh1});
			//tuyennx_add_end_20170818 L2K47TW-128
			
			openReport('window', 'NTU012_PHIEUSIEUAMDOPPLER_24BV01_A4', 'pdf', par);
		}
	}

	// in phiếu kết quả điện tim
	function inPhieu23BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'NTU013_PHIEUDIENTIM_23BV01_QD4069_A4', 'pdf', par);
		}
	}
	
	// in phiếu kết quả điện tim có header
	function inPhieu36BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'NTU013_PHIEUDIENTIM_23BV01_HEADER', 'pdf', par);
		}
	}
	
	// in phiếu kết quả đo cơ tay
	function inPhieu37BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'PHIEUKETQUA_DOCOTAY_NOHEADER', 'pdf', par);
		}
	}
	function inPhieu38BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			openReport('window', 'PHIEUKETQUA_DOCOTAY_HEADER', 'pdf', par);
		}
	}

	// in phiếu kết quả điện não
	function inPhieu24BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'NTU037_PHIEUDIENNAO_24BV01_QD4069_A4', 'pdf', par);
		}
	}
	
	//<20180918 - duongnv - them phieu in dien nao>
	function inPhieuDienNaoBV938() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'NTU037_PHIEUDIENNAO_24BV01_QD4069_A4', 'pdf', par);
		}
	}
	//  <20180918 - duongnv - them phieu in dien nao/>
	
	// in phiếu kết quả đo javal
	function inPhieu30BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'NTU013_PHIEUKETQUADOJAVAL_23BV01_QD4069_A4', 'pdf', par);
		}
	}
	
	// in phiếu kết quả điện cơ
	function inPhieu32BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'CANLAMSANG_PHIEUKETQUA_DIENCO', 'pdf', par);
		}
	}
	
	// in phiếu kết quả điện cơ có header
	function inPhieu35BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'PHIEUKETQUA_DIENCO_HEADER', 'pdf', par);
		}
	}
	
	//20180917 - duongnv - them phieu sieu am 4D
	// in phiếu kết quả điện cơ
	function inPhieuSieuAm4D() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'NTU012_PHIEUSIEUAM_22BV01_QD4069_A4_938', 'pdf', par);
		}
	}

	// in phiếu kết quả noi soi
	function inPhieu25BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''},
			    {name:'giodn',type:'String',value:''},
			    {name:'phutdn',type:'String',value:''},
			    {name:'ngaydn',type:'String',value:''}
			];
			
			//tuyennx_add_start_20170818 HISL2BVDKHN-354
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.ANHBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh!="" && hinh_anh!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh});
				var ten_file = data_ar[i]["TENFILE"];
				if(ten_file!="" && ten_file!=null) par.push({name:'ten_file'+i,type:'String',value:ten_file});
			}
			//tuyennx_add_end_20170818 HISL2BVDKHN-354
			openReport('window', 'NTU014_PHIEUNOISOI_25BV01_QD4069_A4', 'pdf', par);
		}
	}
	
	function inPhieuNoiSoiMau2() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''},
			    {name:'giodn',type:'String',value:''},
			    {name:'phutdn',type:'String',value:''},
			    {name:'ngaydn',type:'String',value:''}
			];
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.ANHBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh!="" && hinh_anh!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh});
				var ten_file = data_ar[i]["TENFILE"];
				if(ten_file!="" && ten_file!=null) par.push({name:'ten_file'+i,type:'String',value:ten_file});
			}
			openReport('window', 'PHIEUNOISOI_THUCQUAN_DADAY_TATRANG', 'pdf', par);
		}
	}
	// in phiếu kết quả siêu âm mẫu 2
	function inPhieu34BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''},
			    {name:'giodn',type:'String',value:''},
			    {name:'phutdn',type:'String',value:''},
			    {name:'ngaydn',type:'String',value:''}
			];
			
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.ANHBN", param.join('$'));//nghiant 17042019 L2PT-4118 
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			openReport('window', 'NTU012_PHIEUSIEUAM_22BV01_QD4069_A4_MAU2_951', 'pdf', par);
		}
	}
	
	//phieu test tho
	function inPhieuTestThoBV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''},
			    {name:'giodn',type:'String',value:''},
			    {name:'phutdn',type:'String',value:''},
			    {name:'ngaydn',type:'String',value:''}
			];
			
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			openReport('window', 'NTU012_TEST_THO_A4', 'pdf', par);
		}
	}
	
	//bổ sung phiếu bồi dưỡng PTTT
	function inPhieuBoiDuongPTTT() {		
		var selRowId1 = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');
		var benhNhanId = $("#grdDanhSachCDHA").jqGrid('getCell', selRowId1, 'BENHNHANID');		
		
		var selRowId = $("#grdKetQuaChanDoan").jqGrid('getGridParam', 'selrow');
		var _dichvukhambenhid = $("#grdKetQuaChanDoan").jqGrid('getCell', selRowId, 'DICHVUKHAMBENHID');     	
     	
		
		if(_dichvukhambenhid=="" || _dichvukhambenhid==undefined){
			DlgUtil.showMsg("Chưa chọn dịch vụ cần in",undefined,1500);
		} else {
			var par = [ {
  				name : 'dichvukhambenhid',
  				type : 'String',
  				value : _dichvukhambenhid
  			},{
  				name : 'benhnhanid',
  				type : 'String',
  				value : benhNhanId
  			}];
  			openReport('window', "PHIEUBOIDUONG_PTTT", "pdf", par);
		}
	}
	
	//bổ sung phiếu thanh toán bồi dưỡng PTTT
	function inPhieuThanhToanBoiDuongPTTT() {	
		var selRowId = $("#grdKetQuaChanDoan").jqGrid('getGridParam', 'selrow');
		var _dichvukhambenhid = $("#grdKetQuaChanDoan").jqGrid('getCell', selRowId, 'DICHVUKHAMBENHID');     	

		if(_dichvukhambenhid=="" || _dichvukhambenhid==undefined){
			DlgUtil.showMsg("Chưa chọn dịch vụ cần in",undefined,1500);
		} else {
			var par = [ {
  				name : 'dichvukhambenhid',
  				type : 'String',
  				value : _dichvukhambenhid
  			}];
  			openReport('window', "PHIEU_THANHTOAN_BDPTTT", "pdf", par, true, true);
		}
	}
	
	function inPhieuKQCDHA() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}			   
			];			
			
			openReport('window', 'CLS_KQ_KTV_CDHA', 'pdf', par);
		}
	}
	
	
	function inPhieuKQCDHA_BS() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()}			   
			];			
			
			openReport('window', 'KQ_CDHA_BACSY', 'pdf', par);
		}
	}	
	
	
	// in phiếu kết quả noi soi phế quản
	function inPhieuNSPQ2() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''},
			    {name:'giodn',type:'String',value:''},
			    {name:'phutdn',type:'String',value:''},
			    {name:'ngaydn',type:'String',value:''}
			];
						
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.ANHBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			openReport('window', 'KQ_NOISOI_PHEQUAN_MAU2_951', 'pdf', par);
		}
	}
	
	// in phiếu kết quả noi soi đại trực tràng
	function inPhieuNSDTT() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''},
			    {name:'giodn',type:'String',value:''},
			    {name:'phutdn',type:'String',value:''},
			    {name:'ngaydn',type:'String',value:''}
			];
			
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			openReport('window', 'KQ_NOISOI_DAITRUCTRANG_951', 'pdf', par);
		}
	}

	// ==================== START >> In Phiếu New ==================== truongle - 26/11/2019
	function inPhieuNew(target) {
		var rptData = target.data("external")
		if (rptData && rptData.report_code) {
			var maubenhphamid = $("#hdfIDMauBenhPham").val();
			if(maubenhphamid==""){
				DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
			} else {
				var par = [ {name:'maubenhphamid',type:'String',value:maubenhphamid},{name:'id_maubenhpham',type:'String',value:maubenhphamid} ];
				
				if (rptData.optional_param) {
					// Param optional
					if (rptData.optional_param.optional && rptData.optional_param.optional.length > 0) {
						par = par.concat(rptData.optional_param.optional);
					}

					// Param from grdDanhSachCDHA
					if (rptData.optional_param.maubenhpham && rptData.optional_param.maubenhpham.length > 0) {
						var selMBPRowId = $("#grdDanhSachCDHA").jqGrid('getGridParam', 'selrow');
						var dvkPar = rptData.optional_param.maubenhpham.map(function(field) {
							return {
								name : field.toLowerCase(),
								type : 'String',
								value : $("#grdDanhSachCDHA").jqGrid('getCell', selMBPRowId, field.toUpperCase())
							}
						});
						par = par.concat(dvkPar);
					}

					// Param from grdKetQuaChanDoan
					if (rptData.optional_param.ketqua && rptData.optional_param.ketqua.length > 0) {
						var selKQRowId = $("#grdKetQuaChanDoan").jqGrid('getGridParam', 'selrow');
						if(selKQRowId != null){
							var dvkPar = rptData.optional_param.ketqua.map(function(field) {
								return {
									name : field.toLowerCase(),
									type : 'String',
									value : $("#grdKetQuaChanDoan").jqGrid('getCell', selKQRowId, field.toUpperCase())
								}
							});
							par = par.concat(dvkPar);
						}
					}
				}

				if (rptData.innhieu_phieu != 0) {
					var dvkbDatas = $('#grdKetQuaChanDoan').jqGrid('getGridParam', 'selarrrow');
					if (dvkbDatas.length == 0) {
						dvkbDatas = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO("CLS01.DS.DVKB", [{"name" : "[0]","value" : maubenhphamid}]));
						dvkbDatas.forEach(function(dvkb) {
							// lấy ảnh của từng dịch vụ- vietda 27042020
							if (rptData.image_sql && rptData.image_sql != "") {
								var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(rptData.image_sql, maubenhphamid+"$"+dvkb.DICHVUKHAMBENHID);
								var imageCount = (rptData.image_count !=  null && rptData.image_count != '' ) ? parseInt(rptData.image_count) : 0;
								for(var i=0 ; i<data_ar.length ; i++){
									var hinh_anh = data_ar[i]["DUONGDANFILE"];
									if(hinh_anh!="" && hinh_anh!=null) par.push({name:'hinh_anh'+(i + imageCount),type:'Image',value:hinh_anh});
									var ten_file = data_ar[i]["TENFILE"];
									if(ten_file!="" && ten_file!=null) par.push({name:'ten_file'+ (i + imageCount),type:'String',value:ten_file});
								}
							}
							// end of -lấy ảnh của từng dịch vụ- vietda 27042020
							var parInnhieu = par.concat([{name: 'dichvukhambenhid', type: 'String', value: dvkb.DICHVUKHAMBENHID }]);
							openReport('window', rptData.report_code, rptData.file_format ? rptData.file_format : 'pdf', parInnhieu);
						});
					}
					else {
						dvkbDatas.forEach(function(item) {
							var dvkb = $('#grdKetQuaChanDoan').jqGrid('getRowData', item);
							// lấy ảnh của từng dịch vụ- vietda 27042020
							if (rptData.image_sql && rptData.image_sql != "") {
								var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(rptData.image_sql, maubenhphamid+"$"+dvkb.DICHVUKHAMBENHID);
								var imageCount = (rptData.image_count !=  null && rptData.image_count != '' ) ? parseInt(rptData.image_count) : 0;
								for(var i=0 ; i<data_ar.length ; i++){
									var hinh_anh = data_ar[i]["DUONGDANFILE"];
									if(hinh_anh!="" && hinh_anh!=null) par.push({name:'hinh_anh'+(i + imageCount),type:'Image',value:hinh_anh});
									var ten_file = data_ar[i]["TENFILE"];
									if(ten_file!="" && ten_file!=null) par.push({name:'ten_file'+ (i + imageCount),type:'String',value:ten_file});
								}
							}
							// end of -lấy ảnh của từng dịch vụ- vietda 27042020
							var parInnhieu = par.concat([{name: 'dichvukhambenhid', type: 'String', value: dvkb.DICHVUKHAMBENHID }]);
							openReport('window', rptData.report_code, rptData.file_format ? rptData.file_format : 'pdf', parInnhieu);
						});
					}
				}	
				else {
					// lấy ảnh của tất cả dịch vụ- vietda 27042020
					if (rptData.image_sql && rptData.image_sql != "") {
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(rptData.image_sql, maubenhphamid);
						var imageCount = (rptData.image_count !=  null && rptData.image_count != '' ) ? parseInt(rptData.image_count) : 0;
						for(var i=0 ; i<data_ar.length ; i++){
							var hinh_anh = data_ar[i]["DUONGDANFILE"];
							if(hinh_anh!="" && hinh_anh!=null) par.push({name:'hinh_anh'+(i + imageCount),type:'Image',value:hinh_anh});
							var ten_file = data_ar[i]["TENFILE"];
							if(ten_file!="" && ten_file!=null) par.push({name:'ten_file'+ (i + imageCount),type:'String',value:ten_file});
						}
					}
					// end of -lấy ảnh của tất cả dịch vụ- vietda 27042020
					openReport('window', rptData.report_code, rptData.file_format ? rptData.file_format : 'pdf', par);
				}
			}
		} else {
			DlgUtil.showMsg("Đã có lỗi xảy ra: rptData",undefined,1500);
		}
	}
	// ==================== END >> In Phiếu New ==================== truongle - 26/11/2019
	
	//tuyendv in phiếu kết quả fibroscan
	function inPhieu33BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''},
			    {name:'giodn',type:'String',value:''},
			    {name:'phutdn',type:'String',value:''},
			    {name:'ngaydn',type:'String',value:''}
			];
			
			//tuyennx_add_start_20170818 HISL2BVDKHN-354
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			//tuyennx_add_end_20170818 HISL2BVDKHN-354
			openReport('window', 'NTU014_PHIEUFIBROSCAN_25BV01_QD4069_A4_951', 'pdf', par);
		}
	}
	//end tuyendv
	
	// in phiếu kết quả noi soi TMH bv 74
	//tuyennx_add_start_20170818 L2K47TW-92
	function inPhieuTMH74() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''},
			    {name:'giodn',type:'String',value:''},
			    {name:'phutdn',type:'String',value:''},
			    {name:'ngaydn',type:'String',value:''}
			];
			
			
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var dem=0;
			var max= (data_ar.length>6?6:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null)
					{
					  par.push({name:'hinh_anh'+dem,type:'Image',value:hinh_anh1});
					  dem=dem+1;
					}
			}
			openReport('window', 'KQ_NOISOI_TMH_944', 'pdf', par);
		}
	}

	// in phiếu kết quả noi soi tieu hoa HDG 
	//nghiant_add_start_20180702 HISL2TK-823
	function inPhieuNoiSoiTieuHoa() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'lanthu',type:'String',value:''},
				{name:'giodn',type:'String',value:''},
				{name:'phutdn',type:'String',value:''},
				{name:'ngaydn',type:'String',value:''}
				];
			
			
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			openReport('window', 'KQ_NOISOI_TIEUHOA', 'pdf', par);
		}
	}
	//tuyennx_add_start_20180702 HISL2TK-823
	function inPhieuNoiSoiCoTuCung() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'lanthu',type:'String',value:''},
				{name:'giodn',type:'String',value:''},
				{name:'phutdn',type:'String',value:''},
				{name:'ngaydn',type:'String',value:''}
				];
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			openReport('window', 'KQ_NOISOI_COTUCUNG', 'pdf', par);
		}
	}
	function inPhieuNoiSoiMuiXoang() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
				{name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
				{name:'lanthu',type:'String',value:''},
				{name:'giodn',type:'String',value:''},
				{name:'phutdn',type:'String',value:''},
				{name:'ngaydn',type:'String',value:''}
				];
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			openReport('window', 'KQ_NOISOI_MUIXOANG', 'pdf', par);
		}
	}
	
	// in phiếu kết quả noi soi phế quản bv 74
	//tuyennx_add_start_20170818 L2K47TW-129
	function inPhieuNSPQ74() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'id_maubenhpham',type:'String',value:$("#hdfIDMauBenhPham").val()},
			    {name:'lanthu',type:'String',value:''},
			    {name:'giodn',type:'String',value:''},
			    {name:'phutdn',type:'String',value:''},
			    {name:'ngaydn',type:'String',value:''}
			];
			
			
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var max= (data_ar.length>4?4:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
			}
			openReport('window', 'KQ_NOISOI_PHEQUAN_944', 'pdf', par);
		}
	}
	//tuyennx_add_start_20170818 L2K47TW-129

	// in phiếu đo chức năng hô hấp
	function inPhieu26BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'NTU038_PHIEUDOCHUCNANGHOHAP_26BV01_QD4069_A4', 'pdf', par);
		}
	}
	function inPhieu26BV01LuuHuyetNao() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
				{name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
				];
			openReport('window', 'NTU013_PHIEUDOLUUHUYETNAO_23BV01_QD4069_A4', 'pdf', par);
		}
	}
	
	// in phiếu đo thị trường trung tâm tìm ám điểm
	function inPhieu28BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'NTU011_PHIEUDOTHITRUONGTRUNGTAM_19BV01_QD4069_A4', 'pdf', par);
		}
	}
	
	// in phiếu chụp đáy mắt không huỳnh quang
	function inPhieu29BV01() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			openReport('window', 'NTU011_PHIEUCHUPDAYMATKHONGHUYNHQUANG_19BV01_QD4069_A4', 'pdf', par);
		}
	}
	
	// in phiếu siêu âm ARFI - L2PT-18483 - BangT - 01/04/2020
	function inPhieuARFI() {
		if($("#hdfIDMauBenhPham").val()==""){
			DlgUtil.showMsg("Chưa chọn phiếu cần in",undefined,1500);
		} else {
			var param = [$("#hdfIDMauBenhPham").val()];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
			var row = data_ar[0];
			console.log(JSON.stringify(row));		
			
			var par = [
			    {name:'maubenhphamid',type:'String',value:$("#hdfIDMauBenhPham").val()}
			];
			
			var max= (data_ar.length>3?3:data_ar.length);
			for(var i=0;i<data_ar.length;i++){
				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
				if(hinh_anh1!="" && hinh_anh1!=null) par.push({name:'hinh_anh'+(i+1),type:'Image',value:hinh_anh1});
			}
			
			openReport('window', 'CLS_PHIEUSIEUAM_GAN_ARFI_A4', 'pdf', par);
		}
	}
	
	//nghiant 26062017
	function showButtonSuaNgayTraKQ(show){
		if(showNutSuaNgayTraKQ!=undefined && showNutSuaNgayTraKQ!=null){
			if(showNutSuaNgayTraKQ==1){
				if(show==true){				
					$("#btnEditTimeCDHA").show();					
				} else {
					$("#btnEditTimeCDHA").hide();
				}		
			}
		}			
	}

	function showButtonSuaNgayTN(show){
		if(showNutSuaNgayTN!=undefined && showNutSuaNgayTN!=null){
			if(showNutSuaNgayTN==1){
				if(show==true){				
					$("#btnSuaNgayTN").show();					
				} else {
					$("#btnSuaNgayTN").hide();
				}		
			}
		}			
	}
	
	function showButtonInPhieu(ngaydichvu){
		if(minCheckInPhieu=="2"){
			//var ngaydichvu = row["NGAYDICHVU"];
			var tgchidinh = moment(ngaydichvu.substr(6,4)+"-"+ngaydichvu.substr(3,2)+"-"+ngaydichvu.substr(0,2)+"T"+ngaydichvu.substr(11,6)+":59");
			var now = new Date();
			var tgthuchien = moment.duration(moment(now).diff(tgchidinh)).asMinutes();
			if(minGroup0InPhieu==""){
				if(tgthuchien<minTimeInPhieu){
					$("#btnInPhieu").attr("disabled", true);
					$("#toolbarIdbtnInPhieu").attr("disabled", true);							
				} else {
					$("#btnInPhieu").attr("disabled", false);
					$("#toolbarIdbtnInPhieu").attr("disabled", false);
				}
			}
			else {
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS02C001.CTDV", $("#hdfIDDichVuKB").val());
				if(data_ar!=undefined && data_ar!=null){					
					var codegroup=data_ar[0]["MADICHVU"];
					if(tgthuchien<minTimeInPhieu && minGroup0InPhieu.indexOf(codegroup)>-1){
						$("#btnInPhieu").attr("disabled", true);						
						$("#toolbarIdbtnInPhieu").attr("disabled", true);						
					} else {
						$("#btnInPhieu").attr("disabled", false);
						$("#toolbarIdbtnInPhieu").attr("disabled", false);
					}
				}
			}						
		} else {
			$("#btnInPhieu").attr("disabled", false);
			$("#toolbarIdbtnInPhieu").attr("disabled", false);
		}
	}
	
	//nghiant 30082017
	function checkPhongThucHien(PhThucHienArray,id_phongchuyenden){
		var checkP = false;
		for (i = 0; i < PhThucHienArray.length; i++) {
			if(PhThucHienArray[i] ==id_phongchuyenden){
				checkP = true;
				break;
			}
		}
		return checkP;
	}
	
	//L2PT-81546
	function checkRole() {
        let _parPQ = "CLS02C001_DanhSachCDHA2" + "$";
        let result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.PQSCREEN.03", _parPQ);
        for (let i = 0; i < result.length; i++) {
            if (result[i].ROLES == "1") {
                $("#" + result[i].ELEMENT_ID).show();
            } else if (result[i].ROLES == "0" || result[i].ROLES == "") {
                $("#" + result[i].ELEMENT_ID).hide();
            }
        }
    }	

	// Xử lý sự kiện liên quan ký CA => START
	function _kyCaRpt(_params){
		// Check ky cap hay khong
		let _rptCode = _params.find(element => element.name == 'RPT_CODE')['value'];
		let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
		let row = "";
		let loaiky = "";
		if (data_ar != null && data_ar.length > 0) {
			row = data_ar[0];
			loaiky = row.LOAIKY;
		}
		if(loaiky == '1') {
			CommonUtil.kyCA(_params, '', '', '', '', '1');
		} else {
			CommonUtil.kyCA(_params);
		}
		EventUtil.setEvent("eventKyCA",function(e){
			DlgUtil.showMsg(e.res);
		});
	}
}
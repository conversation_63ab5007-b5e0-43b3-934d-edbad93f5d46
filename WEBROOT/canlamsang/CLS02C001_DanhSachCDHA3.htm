<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/AjaxService.js?v=2" ></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css"/>
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css"/>
<link rel="stylesheet" href="../common/css/custom.css"/>
<link rel="stylesheet" href="../common/css/css_style.css"/>
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jquery-ui-1.12.1/jquery-ui.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js?v=20160105"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../plugin/jquery.patientInfo.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css"/>
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<script type="text/javascript" src="../noitru/cominf.js"></script>
<script type="text/javascript" src="../canlamsang/RISConnector.js?v=20171122"></script>
<script type="text/javascript" src="../canlamsang/GoiBenhNhan.js?v=2"></script>
<!-- nghiant 27102017 -->
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<!--end nghiant 27102017 -->
<script type="text/javascript" src="../common/script/responsivevoice.js?key=9L20KRrj&v=20200401"></script>
<script type="text/javascript" src="../common/script/GoogleCloudService.js?v=20200317"></script>
<script type="text/javascript" src="../common/script/barcodedetect/jquery.scannerdetection.js"></script>
<link href="../common/script/select2/dist/css/select2.min.css" rel="stylesheet" />
<script src="../common/script/select2/dist/js/select2.min.js"></script>
<script type="text/javascript" src="../common/script/SmsSend.js?v=**********"></script>
<script type="text/javascript" src="../common/script/MailSend.js?v=**********"></script>
<script type="text/javascript" src="../canlamsang/CLS02C001_DanhSachCDHA.js?v=20231029"></script>
<div id="loading" class="hide">
	<span class="spinner"></span>
</div>
<style>
	.ui-jqgrid tr.jqgrow td {
		white-space: nowrap !important;
		height:auto;
		vertical-align:text-top;
		padding-top:2px;
	}
	#toolbarId .wd100 {
	    float: none !important;
	}
	#toolbarIdbtnTiepNhanTheoLo {	
	    width: 150px;
	}
	.sitdownplease {
		vertical-align: middle !important;
		line-height: 18px;
		padding-bottom: 2px !important;
	}
	
	@keyframes spinner {
		to {transform: rotate(360deg);}
	}

	#loading.show{
		display: block;
		z-index: 999;
	}

	#loading.hide{
		display: none;
		z-index: -1;
	}

	.spinner:before {
		content: '';
		box-sizing: border-box;
		position: absolute;
		top: 50%;
		left: 50%;
		width: 40px;
		height: 40px;
		margin-top: -20px;
		margin-left: -20px;
		border-radius: 50%;
		border: 4px solid #000;
		border-top-color: #fff;
		animation: spinner 1s linear infinite;
	}

	#loading {
		position: fixed;
		z-index: 999;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #4444448a;
	}
	
	#fileUploadName.raw{
		text-align: center;
		color: blue;
	}
	#fileUploadName.data{
		text-align: left;
		padding-top: 0px;
		color: #000000bf;
		max-height: 120px;
	}
	#fileUploadName.data hr {
		height: 1px;
		margin-top: 0px;
		margin-bottom: 0px;
	}
</style>

<div width="100%" id="divMain" class="container" help="help_VI_1_NhapthongtinthuchienCDHA">
	<div id='toolbarId'></div>
	<div id="goibenhnhan">
		<div id="playlist" style="display: none;">
	        <audio controls autoplay></audio>
	    </div>
	</div>
	
	<div class="panel-body mgt5">
		<div class="col-md-3 low-padding">
			<div class="col-md-6 low-padding">
				<label class="radio-inline" for="rdoChoThucHien">
					<input id="rdoChoThucHien" type="radio" name="rdoTrangThai" value="2" checked>Đang thực hiện
				</label>
			</div>
			<div class="col-md-6 low-padding">
				<label class="radio-inline" for="rdoTraKetQua">
					<input id="rdoTraKetQua" type="radio" name="rdoTrangThai" value="3">Đã thực hiện
				</label>	
			</div>				
		</div>
		<div class="col-md-9 low-padding">
			<div class="col-md-6 low-padding">
				<div class="col-md-4 low-padding">
					<label class="mgl10">Phòng thực hiện</label> 
				</div>
				<div class="col-md-8 low-padding">
					<select class="form-control input-sm" id="cboPhongThucHien" style="width:90%">
						<option value=""></option>
					</select> 
				</div>	
			</div> 
			<div class="col-md-6 low-padding">
				<div class="col-md-1 low-padding">
					<label>Từ</label>
				</div>
				<div class="col-md-3 low-padding"> 
					<div class='input-group'>							
						<input class="form-control input-sm" id="txtTuNgay" valrule="Từ ngày,required|datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
						<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtTuNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
					</div>
					
				</div>
				<div class="col-md-1 low-padding"></div>
				<div class="col-md-1 low-padding">
					<label>Đến</label> 
				</div>
				<div class="col-md-3 low-padding"> 
					<div class='input-group'>							
						<input class="form-control input-sm" id="txtDenNgay" valrule="Đến ngày,required|datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
						<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtDenNgay','ddMMyyyy','dropdown',false,'24',true)"></span>
					</div> 
					 
				</div>
				<div class="col-md-1 low-padding"></div>
				<div class="col-md-2 low-padding">
					<button class="btn btn-sm btn-primary mgt-3" id="btnXem">
						<span class="glyphicon glyphicon-search"></span> Xem
					</button> 
				</div>
			</div>
		</div>	
	</div>
	
	<div id="divDanhSachCDHA">
		<div class="col-md-12 low-padding mgt-10 mgb-5">
			<table id="grdDanhSachCDHA"></table>
			<div id="pager_grdDanhSachCDHA"></div>
		</div>
	</div>
	
	<ul class="nav nav-tabs">
		<li role="presentation" id="tabThongTinBenhNhan"><a href="#">Thông tin bệnh nhân</a></li>
		<li role="presentation" id="tabDanhSachChiDinh"><a href="#">Danh sách chỉ định</a></li>
		<li role="presentation" id="tabKetQuaChanDoan" class="active"><a href="#">Kết quả chẩn đoán hình ảnh</a></li>
		<!-- nghiant 20102017 -->
		<li role="presentation" id="tabDanhSachDaChiDinh" style="display: none;"><a href="#" id="titDSDCD">Danh sách DV đã chỉ định</a></li>
		<!--end  nghiant 20102017 -->
	</ul>

	<div id="divThongTinBenhNhan" class="tab">
		<div class="col-md-12 low-padding">	
			<div id="ttHanhChinh"></div>
			<div id="ttChanDoan">
				<div class="col-md-1 low-padding">
					<label>Chẩn đoán: </label>
				</div>
				<div class="col-md-11 low-padding">
					<textarea id="txtChanDoan" rows="2" style="float: right; width: 100%;" disabled="true"></textarea>
				</div>
			</div>
		</div>
	</div>

	<div id="divDanhSachChiDinh" class="tab">
		<div class="mgt-10">	
			<table id="grdDanhSachChiDinh"></table>
			<div id="pager_grdDanhSachChiDinh"></div>
		</div>
		
		<div class="col-md-12 low-padding mgt-5 mgb10">
			<div class="col-md-3 low-padding" style="text-align: left;">
				<button class="btn btn-sm btn-primary" id="btnHuyDichVu">
					<span class="glyphicon glyphicon-remove"></span> Hủy bỏ dịch vụ
				</button>
				<button class="btn btn-sm btn-primary" id="btnKhoiPhuc" style="display:none;">
					<span class="glyphicon glyphicon-refresh"></span> Khôi phục dịch vụ
				</button>
			</div>
			<div class="col-md-6 low-padding">
				<span id="toolbarIdPTTT"></span>
			</div>
			<div class="col-md-3 low-padding" style="text-align: right;">				
				<button class="btn btn-sm btn-primary" id="btnBoSung" style="display:none;">
					<span class="glyphicon glyphicon-plus-sign"></span> Bổ sung dịch vụ
				</button>
			</div>
		</div>
	</div>
	
	<div id="divKetQuaChanDoan" class="tab active">
		<div class="mgt-10 mgb-5">	
			<table id="grdKetQuaChanDoan"></table>
			<div id="pager_grdKetQuaChanDoan"></div>
		</div>
		
		<div class="col-md-12 low-padding mgt-5 mgb10">
			<div class="col-md-6 low-padding">
				<div class="col-md-12 low-padding">
					<div class="col-md-3 low-padding">
						<button type="button" class="btn btn-primary" id="btnDicomViewer" style="display:none;width:136px">
							<span class="glyphicon glyphicon-picture" aria-hidden="true"></span> Xem ảnh DICOM
						</button>
					</div>
					<div class="col-md-4 low-padding">
						<input class="form-control input-sm" style="width:95% !important;" id="txtTKPHUMO1" title="" attrIcd="0" modeDisXT="" placeholder="Gõ để tìm kiếm KTV">
					</div>
					<div class="col-md-4 low-padding">
						<select class="form-control input-sm" style="width:95% !important;" id="cboPHUMO1" modeDisXT="" attrCl="clear">
							<option value="-1"></option>
						</select>
					</div>
					<div class="col-md-1 low-padding">
						<button type="button" class="form-control input-sm glyphicon" id="btnCLEARPHUMO1" modeDisXT="">
							<span class="glyphicon glyphicon-remove"></span>
						</button>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 low-padding">
				<div class="col-md-12 low-padding" style="text-align: right;">
					<button class="btn btn-sm btn-primary" id="btnHenNgay">
						<span class="glyphicon glyphicon-calendar"></span> Hẹn ngày thực hiện
					</button>
					<button class="btn btn-sm btn-primary" id="btnTiepNhan">
						<span class="glyphicon glyphicon-log-in"></span> Bắt đầu thực hiện
					</button>
					<button class="btn btn-sm btn-primary" id="btnHuyTiepNhan" style="display: none;">
						<span class="glyphicon glyphicon-remove"></span> Hủy tiếp nhận
					</button>
					<button class="btn btn-sm btn-primary" id="btnLuuKetQua" style="display: none;">
						<span class="glyphicon glyphicon-floppy-disk"></span> Lưu kết quả
					</button>
					<button class="btn btn-sm btn-primary" id="btnHenTraKetQua" style="display: none;">
						<span class="glyphicon glyphicon-calendar"></span> Hẹn trả KQ
					</button>
					<button class="btn btn-sm btn-primary" id="btnTraKetQua" style="display: none;">
						<span class="glyphicon glyphicon-check"></span> Trả kết quả
					</button>
					<button class="btn btn-sm btn-primary" id="btnSuaNgayTN" style="display: none;">
						<span class="glyphicon glyphicon-edit"></span> Sửa TG tiếp nhận
					</button>
					<!-- begin nghiant 26062017 -->
					<button class="btn btn-sm btn-primary" id="btnEditTimeCDHA" style="display: none;">
						<span class="glyphicon glyphicon-edit"></span> Sửa TG trả KQ
					</button>
					<!-- end nghiant 26062017 -->
					<button class="btn btn-sm btn-primary" id="btnHuyKetQua" style="display: none;">
						<span class="glyphicon glyphicon-remove"></span> Hủy kết quả
					</button>
					<button class="btn btn-sm btn-primary" id="btnInPhieu" style="display: none;">
						<span class="glyphicon glyphicon-print"></span> In phiếu
					</button>
					<button class="btn btn-sm btn-primary" id="btnKySo" style="display: none;">
						<span class="glyphicon glyphicon-check"></span> Ký số
					</button>
					<button class="btn btn-sm btn-primary" id="btnHuyKySo" style="display: none;">
						<span class="glyphicon glyphicon-remove-circle"></span> Hủy ký
					</button>
					<button class="btn btn-sm btn-primary" id="btnInPhieuKy" style="display: none;">
						<span class="glyphicon glyphicon-print"></span> In ký
					</button>
					<div class="col-md-12 low-padding" style="width: 647px;">
						<iframe id="ifmXLS" name="ifmXLS"  style="width: 100%; height: 100%;min-height:500px; 
						margin-left: -2px!important; display: none" >
						</iframe>
					</div>
				</div>
			</div>
			<!-- nghiant  VNPTHISL2-490 25092017 -->
			<iframe width="0" height="0" border="0" name="dummyframeCDHA" id="dummyframeCDHA"></iframe>
			<form  method="POST" name="sendSMSFormCDHA" id="sendSMSFormCDHA" 
				action="/vnpthis/servlet/otp.controller.SendSMSTEST" target="dummyframeCDHA">
				<input type="hidden" name="phoneNumber"  id="phoneNumber" value="">
				<input type="hidden"  name="contentSMS"  id="contentSMS" >
			</form>
			<!-- nghiant  VNPTHISL2-490 25092017 -->
		</div>
		<div class="col-md-12 low-padding mgt-5 mgb10">
			<div class="col-md-6 low-padding">
				<div class="col-md-12 low-padding">
					<div class="col-md-3 low-padding">				
					</div>
					<div class="col-md-4 low-padding">
						<input class="form-control input-sm" style="width:95% !important;" id="txtTKPHUMO2" title="" attrIcd="0" modeDisXT="" placeholder="Gõ để tìm kiếm Bác sĩ">
					</div>
					<div class="col-md-4 low-padding">
						<select class="form-control input-sm" style="width:95% !important;" id="cboPHUMO2" modeDisXT="" attrCl="clear">
							<option value="-1"></option>
						</select>
					</div>
					<div class="col-md-1 low-padding">
						<button type="button" class="form-control input-sm glyphicon" id="btnCLEARPHUMO2" modeDisXT="">
							<span class="glyphicon glyphicon-remove"></span>
						</button>
					</div>
				</div>
			</div>
			<div class="col-md-6 low-padding">		
			</div>	
		</div>
		<div id="divDinhKemFile" class="col-md-12 mgt5" style="display: none;">
			<div class="col-md-2 low-padding">
				<label class="mgl5">File đính kèm</label>
			</div>
			<div class="col-md-4 low-padding">
				<input type="file" id="fileUpload" multiple="" style="display: none;" accept=".pdf">
				<pre id="fileUploadName" class="raw">Đính kèm tài liệu...</pre>
			</div>
			<div class="col-md-2 low-padding">
				<button class="btn btn-sm btn-primary" id="btnLuuFile">
					<span class="glyphicon glyphicon-floppy-disk"></span> Lưu 
				</button>
			</div>
		</div>
		<div class="mgt-10">Chữ in đậm: Đã kê TVT đi kèm theo gói</div>
		<div id="divGhichu" style="display: none;">		
			<br><span style="font-weight:bold;color:#b937b3">BN ưu tiên</span>
				<span style="font-weight:bold;margin-left: 50px;color:#9e0606" >BN cấp cứu</span>
				<span style="font-weight:bold;color:green;margin-left: 50px;">BN điều trị ngoại trú</span>				
			<br>
		</div>
	</div>
	
	<!-- nghiant 20102017 -->
	<div id="divDanhSachDaChiDinh" class="tab" style="display: none;">		
		<div class="col-md-1 low-padding" style="text-align: left;">
			<label>Từ</label>
		</div>
		<div class="col-md-2 low-padding" style="text-align: left;"> 
			<div class='input-group'>							
				<input class="form-control input-sm" id="txtTuNgayCD" valrule="Từ ngày,datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
				<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtTuNgayCD','ddMMyyyy','dropdown',false,'24',true)"></span>
			</div>
		</div>
		<div class="col-md-1 low-padding" style="text-align: left;">
			<label>Đến</label> 
		</div>
		<div class="col-md-2 low-padding" style="text-align: left;"> 
			<div class='input-group'>							
				<input class="form-control input-sm" id="txtDenNgayCD" valrule="Đến ngày,datetime|max_length[10]" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy" maxlength="10">
				<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" onclick="NewCssCal('txtDenNgayCD','ddMMyyyy','dropdown',false,'24',true)"></span>
			</div> 
		</div>
		<div class="col-md-1 low-padding" ></div>
		<div class="col-md-2 low-padding" style="text-align: left;">
			<button class="mgt-3 mgl-5 btn btn-sm btn-primary" id="btnXemDVDCD">
				<span class="glyphicon glyphicon-search"></span> Xem DV đã chỉ định
			</button> 
		</div>
		<div class="col-md-1 low-padding" ></div>
		<div class="col-md-2 low-padding" style="text-align: left;">
			<button class="mgt-3 mgl-5 btn btn-sm btn-primary" id="btnXemLichSuHSBA">
				<span class="glyphicon glyphicon-search"></span> Lịch sử bệnh án
			</button>
		</div>
		<div class="col-md-12 low-padding mgt-10 mgb-5">	
			<table id="grdDanhSachDaChiDinh"></table>
			<div id="pager_grdDanhSachDaChiDinh"></div>
		</div>
	</div>
	
	<!-- Begin_HaNv_10052018: them context menu chuyen phong thuc hien HISL2TK-419 -->
	<div class="contextMenu" id="contextMenuBP" style="display:none;">
        <ul style="width:235px !important; font-size: 65%;">
            <li id="CapSTT" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Cấp STT thực hiện</span>
            </li>
            <li id="ChuyenPhongTH" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Chuyển phòng thực hiện</span>
            </li>
            <li id="LichSuThucHien" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Lịch sử thực hiện</span>
            </li>
            <li id="TaoBBHC" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Tạo biên bản hội chẩn</span>
            </li>
            <li id="DSBBHC" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Danh sách biên bản hội chẩn</span>
            </li>
			<li id="KTTSDU" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Tạo phiếu khai thác tiền sử dị ứng</span>
            </li>
			<li id="inTheBenhAn" class="menulevel1">
                <span class="ui-icon ui-icon-print" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">In mã bệnh án</span>
            </li>			
        </ul>
    </div>
    <!-- End_HaNv_10052018 -->
	
	<div class="contextMenu" id="contextMenuDetail" style="display:none;width: 250px;">
		<ul style="width:250px !important; font-size: 65%;">
			<li id="GROUP1" class="menulevel1">
				<span class="ui-icon ui-icon-pencil" style="float:left;"></span>
				<span style="font-size:130%; font-family:Verdana">CẬP NHẬT THÔNG TIN</span>
			</li>
			<li id="CapNhatXQuang" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana">Cập nhật số phim</span>
			</li>
			<li id="CapNhatPTTT" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana">Cập nhật PTTT</span>
			</li>
			<li id="SuaChiDinh" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float: left; margin-left:15px;"></span> 
				<span style="font-size: 130%; font-family: Verdana">Sửa chỉ định đã chọn</span>
			</li>
			<li id="ThanhToanVienPhi" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float: left; margin-left:15px;"></span> 
				<span style="font-size: 130%; font-family: Verdana">Thanh toán viện phí</span>
			</li>
			<li id="CapNhatDoLoangXuong" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float: left; margin-left:15px;"></span>
				<span style="font-size: 130%; font-family: Verdana">Cập nhật đo loãng xương</span>
			</li>
			<li id="PKQXNST" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float: left; margin-left:15px;"></span>
				<span style="font-size: 130%; font-family: Verdana">Nhập kết quả sinh thiết</span>
			</li>
			<li id="GROUP2" class="menulevel1">
				<span class="ui-icon ui-icon-folder-open" style="float:left;"></span>
				<span style="font-size:130%; font-family:Verdana">THUỐC, VẬT TƯ ĐI KÈM</span>
			</li>
			<li id="TAOPHIEUTHUOCKEM_HAOPHI" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="kevattutheogoi">Tạo phiếu thuốc đi kèm hao phí</span>
			</li> 
			<li id="TAOPHIEUVATTUKEM_HAOPHI" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana">Tạo phiếu vật tư đi kèm hao phí</span>
			</li>
			 <li id="TAOPHIEUTHUOCKEM" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="kethuocdikem">Tạo phiếu thuốc đi kèm</span>
			</li> 
			<li id="TAOPHIEUVATTUKEM" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="kevattudikem">Tạo phiếu vật tư đi kèm</span>
			</li>
			<li id="DSPTVT_KEM" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="dsthuocvttheogoi">Danh sách phiếu thuốc đi kèm</span>
			</li> 
			<li id="DSPTVT" class="menulevel2"> 
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana">Danh sách phiếu thuốc, vật tư</span>
			</li>
			<li id="GROUP3" class="menulevel1">
				<span class="ui-icon ui-icon-folder-open" style="float:left;"></span>
				<span style="font-size:130%; font-family:Verdana">IN PHIẾU</span>
			</li>
			<li id="themPhieuKQ" class="menulevel2">
				<span class="ui-icon ui-icon-contact" style="float: left"></span> 
				<span style="font-family: Verdana">Thêm Phiếu</span>
			</li> 
		</ul>
	</div>	
	<!-- end nghiant 20102017 -->
	
	<div class="contextMenu" id="contextMenu" style="display: none; width: 250px;">
		<ul style="width: 250px; font-size: 65%;">
			<li id="ThemPST" class="menulevel2"><span
				class="ui-icon ui-icon-copy" style="float: left"></span> <span
				style="font-size: 130%; font-family: Verdana">Thêm phiếu sinh thiết</span>
			</li>	
			 <li id="sentRequest" class="menulevel2">
                <span class="ui-icon ui-icon-seek-next" style="float:left;margin-left:15px;"></span>
                <span style="font-size:130%; font-family:Verdana">Gửi yêu cầu</span>
            </li>
            <li id="deleteRequest" class="menulevel2">
                <span class="ui-icon ui-icon-trash" style="float:left;margin-left:15px;"></span>
                <span style="font-size:130%; font-family:Verdana">Hủy yêu cầu</span>
            </li> 
             <li id="delete" class="menulevel2">
                <span class="ui-icon ui-icon-trash" style="float:left;margin-left:15px;"></span>
                <span style="font-size:130%; font-family:Verdana">Xóa </span>
            </li>
            <li id="updateCDHA" class="menulevel2">
                <span class="ui-icon ui-icon-pencil" style="float:left; margin-left:15px;"></span>
                <span style="font-size:130%; font-family:Verdana">Cập nhật phiếu CĐHA</span>
            </li>
			<li id="GROUP2" class="menulevel1">
				<span class="ui-icon ui-icon-folder-open" style="float:left;"></span>
				<span style="font-size:130%; font-family:Verdana">THUỐC, VẬT TƯ ĐI KÈM</span>
			</li>
			<li id="TAOPHIEUTHUOCKEM_HAOPHI" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="kevattutheogoi2">Tạo phiếu thuốc đi kèm hao phí</span>
			</li> 
			<li id="TAOPHIEUVATTUKEM_HAOPHI" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana">Tạo phiếu vật tư đi kèm hao phí</span>
			</li>
			 <li id="TAOPHIEUTHUOCKEM" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="kethuocdikem2">Tạo phiếu thuốc đi kèm</span>
			</li> 
			<li id="TAOPHIEUVATTUKEM" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="kevattudikem2">Tạo phiếu vật tư đi kèm</span>
			</li>
			<li id="DSPTVT_KEM" class="menulevel2">
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana" id="dsthuocvttheogoi2">Danh sách phiếu thuốc đi kèm</span>
			</li> 
			<li id="DSPTVT" class="menulevel2"> 
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana">Danh sách phiếu thuốc, vật tư</span>
			</li>
			<li id="DSPTVT" class="menulevel2"> 
				<span class="ui-icon ui-icon-copy" style="float:left; margin-left:15px;"></span>
				<span style="font-size:130%; font-family:Verdana">Danh sách phiếu thuốc, vật tư</span>
			</li>	
			<li id="themPhieuDCD" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Thêm phiếu</span>
            </li>
		</ul>
	</div>
	
	<div class="contextMenu" id="contextMenuChiDinh" style="display:none;width: 250px;">
		<ul style="width:250px !important; font-size: 65%;">			
			<li id="themPhieuCD" class="menulevel1">
                <span class="ui-icon ui-icon-pencil" style="float:left;"></span>
                <span style="font-size:130%; font-family:Verdana">Thêm phiếu</span>
            </li>			
		</ul>
	</div>	
	
	<div class="row"></div>
	
</div>

<input type="hidden" id="hdfIDMauBenhPham" value="">
<input type="hidden" id="hdfIDGhiChu" value="">
<input type="hidden" id="hdfIDDichVuKB" value="">
<input type="hidden" id="hdfIDKetQuaCLS" value="">
<input type="hidden" id="hdfSoPhieu" value="">
<input type="hidden" id="hdfNgayDichVu" value="">
<input type="hidden" id="hdfMAHOSOBENHAN" value="">

<div id="divNhapKetQua" style="width: 100%; display: none">
	<iframe src="" id="ifmView" style="width:1200px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<div id="divSuaKetQua" style="width: 100%; display: none">
	<iframe src="" id="ifmView" style="width:1200px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<script>
	var userInfo = CommonUtil.decode('{userData}');	
	var user_id = '{user_id}';
	var schema = '{db_schema}';
	var province_id = '{province_id}';
	var hospital_id = '{hospital_id}';
	var uuid = '{uuid}';
	var lang= "vn";
	console.log('user_id='+user_id+', schema='+schema+', province_id='+province_id+', hospital_id='+hospital_id);
	
	var session_par=[];
	session_par[0]=hospital_id;
	session_par[1]=user_id;
	session_par[2]=schema;
	session_par[3]=province_id;
	var table_name='{table}';
	
	var _opts=new Object();	
	_opts.fullname = userInfo.FULL_NAME;
	_opts._param=session_par;
	_opts._uuid=uuid;	
	_opts.subdept_id = '{subdept_id}';
	_opts.dept_id = '{dept_id}';//nghiant 24102017 
	
	initRest(_opts._uuid);
	
	initAjax("/vnpthis");//them lib ajax
	ajaxSvc.register("PortalWS");
	ajaxSvc.register("HL7_Gateway");
	
	var ds = new DanhSachCDHA(_opts);
	ds.load();
	
	var GBN = new JSF_GoiBenhNhan();	
</script>
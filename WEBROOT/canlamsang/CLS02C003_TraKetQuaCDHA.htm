<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<script type="text/javascript" src="../common/script/AjaxService.js?v=2"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js?v=20200605"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/ScanJs.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script src="../common/script/tinymce/tinymce.min.js"></script>
<script src="../common/script/tinymce/preventdelete.js"></script>
<script src="../common/script/nprogress/nprogress.js"></script>
<script src="../common/script/jquery/jquery.wallform.js"></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<link href="../common/script/select2/dist/css/select2.min.css" rel="stylesheet"/>
<script src="../common/script/select2/dist/js/select2.min.js"></script>
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script type="text/javascript" src="../canlamsang/webcam.min.js?v=20190611"></script>
<script type="text/javascript" src="../canlamsang/CLS02C003_TraKetQuaCDHA.js?v=20250426_03"></script>

<style>
    #container {
        margin: 0px auto;
        width: 100%;
        height: 550px;
        border: 0px #333 solid;
    }

    #videocont {
        margin: 0px auto;
        width: 480px;
        height: 360px;
        background-color: #666;
    }

    #checkboxcont {
        margin: 0px 50px;
        width: auto;
        height: 375px;
        border: 0px #333 solid;
    }

    input[type='text'] {
        width: 100%;
        margin: 0px auto;
        outline: none;
        border: 0px;
        border-bottom: 1px solid lightgrey;
    }

    input[type='text']:focus {
        width: 100%;
        margin: 0px auto;
        outline: none;
        border: 0px;
        border-bottom: 1px solid lightgrey;
    }
</style>

<div width="100%" id="divMain" style="padding-bottom: 50px;float: left;">
    <ul class="nav nav-tabs" style="margin-top: 5x;">
        <li role="presentation" id="tabKetQuaCDHA" class="active"><a href="#">Chi tiết kết quả</a></li>
        <li role="presentation" id="tabHinhAnh"><a href="#">Hình ảnh</a></li>
        <li role="presentation" id="tabChupAnh"><a href="#">Chụp ảnh</a></li>
        <li role="presentation" id="tabChupAnh"><span style="color:black" id="tabTTBN"></span></li>
    </ul>

    <div id="divKetQuaCDHA" class="tab active">
        <div class="col-md-10">
            <div class="form-inline">
                <div class="col-md-1 low-padding">
                </div>
                <div class="col-md-11 low-padding">
                    <span id="txtTenDichVu"></span>
                </div>
                <div class="col-md-1 low-padding">
                    <label>Mô tả/Kết quả</label>
                </div>
                <div class="col-md-11 low-padding">
                    <textarea id="txtKetQua" rows="12" style="width:100%;font-size:14pt"></textarea>
                </div>

                <div class="col-md-1 low-padding mgt5">
                    <label>Kết luận</label> <span id="lblCount" style="display: none;"></span>
                </div>
                <div id="fontKetLuan" class="col-md-5 low-padding mgt5" style="font-size:16pt;">
                    <textarea id="txtKetLuan" rows="3" style="width:98%;height:76px;" onkeyup="tt.countToLove()"></textarea>
                </div>
                <div class="col-md-6 low-padding">
                    <div class="form-inline">
                        <div class="col-md-2 low-padding mgt2">
                            <label class="mgl2">Ghi chú KQ</label>
                        </div>
                        <div class="col-md-10 low-padding mgt2">
                            <textarea type="text" id="txtGhiChu" rows="2" style="width:100%"></textarea>
                        </div>
                    </div>
                    <div class="form-inline" id="thoiGianCD" style="display: none;">
                        <div class="col-md-2 low-padding mgt5">
                        </div>
                        <div class="col-md-4 low-padding mgt5">
                        </div>
                        <div class="col-md-2 low-padding mgt5">
                            <label class="mgl5">Thời gian CD</label>
                        </div>
                        <div class="col-md-4 low-padding mgt5">
                            <input class="form-control input-sm" id="txtTGChiDinh" name="txtTGChiDinh">
                        </div>
                    </div>
                    <div class="form-inline" id="thoiGianBDThucHien" style="display: none;">
                        <div class="col-md-2 low-padding mgt5">
                        </div>
                        <div class="col-md-4 low-padding mgt5">
                        </div>
                        <div class="col-md-2 low-padding mgt5">
                            <span id="lblThoiGianBD" style="display: none; font-size: 90%"></span>
                        </div>
                        <div class="col-md-4 low-padding mgt5" id="TGBDThucHien">
                            <div class="input-group">
                                <input class="form-control input-sm" id="txtTGBDThucHien" name="txtTGBDThucHien" valrule="Thời gian bắt đầu thực hiện,required|datetime|max_length[19]"
                                       data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19">
                                <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btnTGBDThucHien" type="sCal"
                                      onclick="NewCssCal('txtTGBDThucHien','ddMMyyyy','dropdown',true,'24',true)"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-inline">
                        <div class="col-md-2 low-padding mgt5">
                            <label class="mgl2">Máy CLS</label>
                        </div>
                        <div class="col-md-4 low-padding mgt5">
                            <select class="form-control input-sm" id="cboMayCLS" style="width:100%" multiple="multiple">
                            </select>
                        </div>
                        <div class="col-md-2 low-padding mgt5">
                            <span id="lblThoiGianTH" style="display: none; font-size: 90%"></span>
                        </div>
                        <div class="col-md-4 low-padding mgt5" id="TGThucHien">
                            <div class="input-group">
                                <input class="form-control input-sm" id="txtTGThucHien" name="txtTGThucHien" valrule="Thời gian thực hiện,required|datetime|max_length[19]"
                                       data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19">
                                <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btnTGThucHien" type="sCal"
                                      onclick="NewCssCal('txtTGThucHien','ddMMyyyy','dropdown',true,'24',true)"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="ThoiGianTraKetQua" style="display: none;">
                    <div class="col-md-12 low-padding">
                        <div class="col-xs-1 low-padding mgt2">
                            <label></label>
                        </div>
                        <div class="col-xs-5 low-padding mgt2">
                        </div>

                        <div class="col-xs-1 low-padding mgt2">
                            <label>TG trả KQ</label>
                        </div>
                        <div class="col-xs-2 low-padding mgt2" id="TGTraKetQua">
                            <div class="input-group">
                                <input class="form-control input-sm" id="txtTGTraKetQua" name="txtTGTraKetQua" valrule="Thời gian trả kết quả,required|datetime|max_length[19]"
                                       data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19">
                                <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="btnTGTraKetQua" type="sCal"
                                      onclick="NewCssCal('txtTGTraKetQua','ddMMyyyy','dropdown',true,'24',true)"></span>
                            </div>
                        </div>
                        <div id="SoPhut_TH" class="col-xs-3 low-padding mgt2" style="display: none;">
                            <div class="col-md-4  low-padding mgt5">
                                <label>Số phút TH</label>
                            </div>
                            <div class="col-md-8  low-padding mgt5" id="SoPhutTH">
                                <div class="input-group">
                                    <select class="form-control input-sm" id="cboTHOIGIAN_PTTT_CBO" modeDis="" style="width: 150%; float: left;">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="NhanSuThucHien" style="display: none;">
                    <div class="col-md-12 low-padding">
                        <div class="col-xs-1 low-padding mgt2">
                            <label>Bác sĩ TH(*)</label>
                        </div>
                        <div class="col-xs-5 low-padding mgt2">
                            <input class="form-control input-sm" style="width:30% !important;" id="txtBSThucHien" title="" attrIcd="0" modeDisXT="">
                            <select class="form-control input-sm" style="width:60% !important;" id="cboBSThucHien"
                                    modeDisXT="" attrCl="clear" ekippttt>
                                <option value="-1">--Lựa chọn--</option>
                            </select>
                            <button type="button" class="form-control input-sm glyphicon" id="btnCLEARBSThucHien" modeDisXT="">
                                <span class="glyphicon glyphicon-remove"></span>
                            </button>
                        </div>

                        <div class="col-xs-1 low-padding mgt2">
                            <label>KTV TH</label>
                        </div>
                        <div class="col-xs-5 low-padding mgt2">
                            <input class="form-control input-sm" style="width:28% !important;" id="txtKTVThucHien" title="" attrIcd="0" modeDisXT="">
                            <select class="form-control input-sm" style="width:56% !important;" id="cboKTVThucHien"
                                    modeDisXT="" attrCl="clear" ekippttt>
                                <option value="-1">--Lựa chọn--</option>
                            </select>
                            <button type="button" class="form-control input-sm glyphicon" id="btnCLEARKTVThucHien" modeDisXT="">
                                <span class="glyphicon glyphicon-remove"></span>
                            </button>
                            <button type="button" class="form-control input-sm glyphicon" id="btnSaveKTVThucHien" modeDisXT="">
                                <span class="glyphicon glyphicon-ok"></span>
                            </button>
                        </div>
                    </div>
                </div>
                <div id="NSTH_BSKhac" style="display: none;">
                    <div class="col-md-12 low-padding">
                        <div class="col-xs-1 low-padding mgt2">
                            <label>Bác sĩ khác</label>
                        </div>
                        <div class="col-xs-5 low-padding mgt2">
                            <input class="form-control input-sm" style="width:30% !important;" id="txtBSKHAC" title="" attrIcd="0" modeDisXT="">
                            <select class="form-control input-sm" style="width:60% !important;" id="cboIDBACSYKHAC"
                                    modeDisXT="" attrCl="clear" ekippttt>
                                <option value="-1">--Lựa chọn--</option>
                            </select>
                            <button type="button" class="form-control input-sm glyphicon" id="btnCLEARBACSYKHAC" modeDisXT="">
                                <span class="glyphicon glyphicon-remove"></span>
                            </button>
                        </div>
                    </div>
                </div>
                <div id="NSTH_BSDOCKQ" style="display: none;">
                    <div class="col-md-12 low-padding">
                        <div class="col-xs-1 low-padding mgt2">
                            <label>Bs đọc KQ</label>
                        </div>
                        <div class="col-xs-5 low-padding mgt2">
                            <input class="form-control input-sm" style="width:30% !important;" id="txtBSDOCKQ" title="" attrIcd="0" modeDisXT="">
                            <select class="form-control input-sm" style="width:60% !important;" id="cboIDBACSYDOCKQ"
                                    modeDisXT="" attrCl="clear" ekippttt>
                                <option value="-1">--Lựa chọn--</option>
                            </select>
                            <button type="button" class="form-control input-sm glyphicon" id="btnCLEARBACSYDOCKQ" modeDisXT="">
                                <span class="glyphicon glyphicon-remove"></span>
                            </button>
                        </div>
                    </div>
                </div>

            </div>

            <div class="form-inline mgt5" id="PPNHUOM" style="display: none;">
                <div class="col-md-1 low-padding">
                    <label>PP nhuộm</label>
                </div>
                <div class="col-md-5 low-padding" style="font-size:14pt;">
                    <textarea id="txtPP_NHUOM" rows="1" style="width:95%;"></textarea>
                </div>
                <div class="col-md-1 low-padding">
                    <label class="mgl2">Số GPB</label>
                </div>
                <div class="col-md-2 low-padding" style="font-size:14pt;">
                    <textarea id="txtSOLUONG_GPB" rows="1" style="width:97%"></textarea>
                </div>
                <div class="col-md-1 low-padding">
                    <label class="mgl2">Số lần TH</label>
                </div>
                <div class="col-md-2 low-padding" style="font-size:14pt;">
                    <textarea id="txtSOLANTH" rows="1" style="width:97%"></textarea>
                </div>
            </div>

            <div class="form-inline mgt5" id="KHOPHIMTUTRUC" style="display: none;">
                <div class="col-xs-1 low-padding mgt2">
                    <label>Kho tiêu hao</label>
                </div>
                <div class="col-xs-5 low-padding mgt2">
                    <select class="form-control input-sm" style="width:95% !important;" id="cboMA_KHO"></select>
                </div>
                <div class="col-xs-1 low-padding mgt2">
                    <label>Phim hao phí</label>
                </div>
                <div class="col-xs-5 low-padding mgt2">
                    <input class="form-control input-sm i-col-m_fl text-left"
                           style="width: 100%;" id="txtMA_THUOC" maxlength="30"
                           name="txtMA_THUOC" title="">
                </div>
            </div>

            <div class="form-inline mgt5" id="SLXQUANG" style="display: none;">
                <div class="col-md-1 low-padding mgt2">
                    <label>Số phim</label>
                </div>
                <div class="col-md-5 low-padding mgt2">
                    <textarea rows="1" id="txtSLPhim" style="width:98%;"></textarea>
                </div>
                <div class="col-md-1 low-padding mgt2">
                    <label class="mgl2">Loại phim</label>
                </div>
                <div class="col-md-5 low-padding mgt2">
                    <input type="text" id="txtLoaiPhim" style="width:26% !important"/>
                    <select class="form-control input-sm" id="cboLoaiPhim" filterlike="txtLoaiPhim" style="width:73% !important">
                        <option value="" selected="selected"></option>
                        <option value="8x10">8x10</option>
                        <option value="11x14">11x14</option>
                        <option value="20x25">20x25</option>
                        <option value="35x43">35x43</option>
                        <option value="phimrang">Phim Răng</option>
                    </select>
                </div>
            </div>

            <div class="form-inline mgt5" id="SOTIA" style="display: none;">
                <div class="col-md-1 low-padding mgt2">
                    <label>Số phim hỏng</label>
                </div>
                <div class="col-md-5 low-padding mgt2">
                    <textarea rows="1" id="txtSLPhimHong1" style="width:98%;"></textarea>
                </div>
                <div class="col-md-1 low-padding mgt2">
                    <label class="mgl2">Số tia</label>
                </div>
                <div class="col-md-5 low-padding mgt2">
                    <textarea rows="1" id="txtSoTia" style="width:98%;"></textarea>
                </div>
            </div>

            <div class="form-inline mgt5" id="KHOPHIMTUTRUC2" style="display: none;">
                <div class="col-md-1 low-padding mgt2">
                    <label>Số phim</label>
                </div>
                <div class="col-md-5 low-padding mgt2">
                    <textarea rows="1" id="txtSLPhimKho" style="width:95%;"></textarea>
                </div>
                <div class="col-md-1 low-padding mgt2">
                    <label>Số phim hỏng</label>
                </div>
                <div class="col-md-5 low-padding mgt2">
                    <textarea rows="1" id="txtSLPhimHong" style="width:100%;"></textarea>
                </div>
            </div>

            <div class="form-inline mgt5" id="PPTHUCHIEN" style="display: none;">
                <div class="col-md-1 low-padding">
                    <label>Tình trạng BN trước soi</label>
                </div>
                <div class="col-md-5 low-padding" style="font-size:14pt;">
                    <textarea id="txt_TRUOCTHUCHIEN" rows="2" style="width:98%;"></textarea>
                </div>
                <div class="col-md-1 low-padding">
                    <label class="mgl5">Tình trạng BN sau soi</label>
                </div>
                <div class="col-md-5 low-padding" style="font-size:14pt;">
                    <textarea id="txt_SAUTHUCHIEN" rows="2" style="width:100%"></textarea>
                </div>

                <div class="col-md-1 low-padding mgt5">
                    <label>Thuốc sử dụng</label>
                </div>
                <div class="col-md-5 low-padding mgt5" style="font-size:14pt;">
                    <textarea id="txt_THUOCSUDUNG" rows="2" style="width:98%"></textarea>
                </div>
                <div class="col-md-1 low-padding mgt5">
                    <label class="mgl5">Phương pháp thực hiện</label>
                </div>
                <div class="col-md-5 low-padding mgt5" style="font-size:14pt;">
                    <textarea id="txt_PPTHUCHIEN" rows="2" style="width:100%;"></textarea>
                </div>
            </div>

            <div class="form-inline mgt5" id="ttHOICHAN" style="display: none;">
                <div class="col-md-1 low-padding">

                </div>
                <div class="col-md-5 low-padding">
                    <label><input type="checkbox" id="chkCOTHUOCCANQUANG"> Thuốc cản quang</label>
                </div>
                <div class="col-md-1 low-padding">

                </div>
                <div class="col-md-5 low-padding">
                    <label><input type="checkbox" id="chkCOHOICHAN"> Hội chẩn CĐHA</label>
                </div>

                <div class="col-md-1 low-padding">
                    <label>Tên thuốc cản quang</label>
                </div>
                <div class="col-md-5 low-padding">
                    <textarea id="txtTHUOCCANQUANG" rows="2" style="width:98%" disabled="disabled"></textarea>
                </div>
                <div class="col-md-1 low-padding">
                    <label class="mgl5">DS Bác sĩ hội chẩn CĐHA</label>
                </div>
                <div class="col-md-5 low-padding">
                    <textarea id="txtDSBSHOICHAN" rows="2" style="width:100%;" disabled="disabled"></textarea>
                </div>
            </div>
            <div class="form-inline mgt5" id="tttest" style="display: none;">
                <div class="col-md-1 low-padding">
                    <label class="mgl5">.</label>
                </div>
            </div>


            <div class="form-inline" style="position:fixed; bottom:0; left:0; right:0; background:white; border-top:1px solid #cecece; z-index:9;">
                <div class="col-md-1 low-padding">
                </div>
                <div class="col-md-11 low-padding mgt5 mgb5">

                    <button class="btn btn-sm btn-primary" id="btnLuu">
                        <span class="glyphicon glyphicon-floppy-disk"></span> Lưu kết quả
                    </button>
                    <button class="btn btn-sm btn-primary" id="btnLuuDong">
                        <span class="glyphicon glyphicon-floppy-disk"></span> Lưu & Đóng
                    </button>
                    <button class="btn btn-sm btn-primary" id="btnluuVaTraKQ" style="display:none;">
                        <span class="glyphicon glyphicon-print"></span> &nbsp;Lưu và Trả kết quả
                    </button>
                    <button class="btn btn-sm btn-primary" id="btnluuTraKQInPhieu" style="display:none;">
                        <span class="glyphicon glyphicon-print"></span> &nbsp;Lưu, Trả kết quả và In phiếu
                    </button>
                    <button class="btn btn-sm btn-primary" id="btnKySo" style="display: none;">
                        <span class="glyphicon glyphicon-check"></span> Ký số
                    </button>
                    <button class="btn btn-sm btn-primary" id="btnHuyKySo" style="display: none;">
                        <span class="glyphicon glyphicon-remove-circle"></span> Hủy ký
                    </button>
                    <button class="btn btn-sm btn-primary" id="btnInPhieuKy" style="display: none;">
                        <span class="glyphicon glyphicon-print"></span> In ký
                    </button>
                    <button class="btn btn-sm btn-primary" id="btnInGiaiPhauBenh" style="display:none;">
                        <span class="glyphicon glyphicon-print"></span> &nbsp;In Phiếu GPB sinh thiết
                    </button>
                    <button class="btn btn-sm btn-primary" id="btnHuy">
                        <span class="glyphicon glyphicon-remove-circle"></span> Đóng
                    </button>
                    <button class="btn btn-sm btn-primary" id="btnDSMauKQ">
                        <span class="glyphicon glyphicon-folder-open"></span> &nbsp;DS mẫu KQ
                    </button>
                    <select class="form-control input-sm" id="cboMauKetQua" style="width:150px">
                        <option value=""></option>
                    </select>
                    <button class="btn btn-sm btn-primary" id="btnSuDungMauKQ">
                        <span class="glyphicon glyphicon-import"></span> &nbsp;Chọn mẫu
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-2" style="text-align:center; padding: 5px 0px !important">
            <div id="pictureToPrint"></div>
        </div>
    </div>

    <div id="divHinhAnh" class="tab">
        <div id="UploadController">
            <div class="col-md-2 low-padding">
                <label>Chọn ảnh để tải lên</label>
            </div>
            <div class="col-md-3 low-padding">
                <form action="" method="post" enctype="multipart/form-data" id="formUpload" name="formUpload">
                    <input type="file" id="fileUpload" name="fileUpload" accept="image/*" multiple/>
                    <input type="hidden" id="param" name="param" value=""/>
                </form>
            </div>
            <div class="col-md-2 low-padding">
                <button type="button" class="btn btn-primary" id="btnUpload">
                    <span class="glyphicon glyphicon-upload" aria-hidden="true"></span> Upload
                </button>
            </div>
            <div class="col-md-2 low-padding">
                <button type="button" class="btn btn-primary" id="btnScan">
                    <span class="glyphicon glyphicon-refresh spin hide"></span>
                    <span class="glyphicon glyphicon-print" aria-hidden="true"></span> Scan
                </button>
            </div>
            <div class="col-md-3 low-padding">
                <button type="button" class="btn btn-primary" id="btnXoaHetAnh">
                    <span class="glyphicon glyphicon-delete"></span> Xóa tất cả ảnh
                </button>
            </div>
        </div>
        <br/><br/>
        <div>
            <div class="col-md-12 low-padding">
                <div id="list"></div>
            </div>
        </div>
        <div style="text-align: center;">
            <button type="button" class="btn btn-primary" id="btnLuuAnhChon" style="display:none;">
                <span class="glyphicon glyphicon-floppy-disk"></span> Lưu ảnh được chọn
            </button>
        </div>
    </div>

    <div id="divChupAnh" class="tab">
        <div id="container">
            <div id="videocont"></div>
            <br/>
            <div style="text-align: center;">
                <button type="button" class="btn btn-primary" id="btnSave">
                    <span class="glyphicon glyphicon-camera" aria-hidden="true"></span> Chụp ảnh
                </button>
                <button type="button" class="btn btn-primary" id="btnSend">
                    <span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span> Lưu ảnh
                </button>
                <button type="button" class="btn btn-primary" id="btnSaveSend">
                    <span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span>Chụp và lưu ảnh
                </button>
                <input type="button" id="btnHuy" value="Hủy" style="display:none;"/>
            </div>
            <br/>
            <div id="checkboxcont">
                <table>
                    <tr>
                        <td align="center" style="position: relative;">
                            <label for="cb1"><img id="imgTag1" src="" width="90" height="75"/></label>
                            <input id="cb1" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb2"><img id="imgTag2" src="" width="90" height="75"/></label>
                            <input id="cb2" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb3"><img id="imgTag3" src="" width="90" height="75"/></label>
                            <input id="cb3" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb4"><img id="imgTag4" src="" width="90" height="75"/></label>
                            <input id="cb4" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb5"><img id="imgTag5" src="" width="90" height="75"/></label>
                            <input id="cb5" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb6"><img id="imgTag6" src="" width="90" height="75"/></label>
                            <input id="cb6" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb7"><img id="imgTag7" src="" width="90" height="75"/></label>
                            <input id="cb7" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb8"><img id="imgTag8" src="" width="90" height="75"/></label>
                            <input id="cb8" type="checkbox" disabled="true"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input type="text" id="mota1" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota2" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota3" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota4" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota5" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota6" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota7" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota8" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                    </tr>
                    <tr>
                        <td align="center" style="position: relative;">
                            <label for="cb9"><img id="imgTag9" src="" width="90" height="75"/></label>
                            <input id="cb9" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb10"><img id="imgTag10" src="" width="90" height="75"/></label>
                            <input id="cb10" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb11"><img id="imgTag11" src="" width="90" height="75"/></label>
                            <input id="cb11" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb12"><img id="imgTag12" src="" width="90" height="75"/></label>
                            <input id="cb12" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb13"><img id="imgTag13" src="" width="90" height="75"/></label>
                            <input id="cb13" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb14"><img id="imgTag14" src="" width="90" height="75"/></label>
                            <input id="cb14" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb15"><img id="imgTag15" src="" width="90" height="75"/></label>
                            <input id="cb15" type="checkbox" disabled="true"/>
                        </td>
                        <td align="center" style="position: relative;">
                            <label for="cb16"><img id="imgTag16" src="" width="90" height="75"/></label>
                            <input id="cb16" type="checkbox" disabled="true"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input type="text" id="mota9" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota10" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota11" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota12" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota13" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota14" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota15" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                        <td>
                            <input type="text" id="mota16" style="width: 90%;" autocomplete="off" readOnly="true">
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div id="divChonMauKetQua" style="width: 100%; display: none">
    <iframe src="" id="ifmView" style="width:1200px;height:800px;border:dotted 1px red" frameborder="0"></iframe>
</div>

<input type="hidden" id="hdfIDMauBenhPham">
<input type="hidden" id="hdfIDKetQuaCLS">
<input type="hidden" id="hdfIDDichVuKB">
<input type="hidden" id="hdfLoaiForm">
<input type="hidden" id="hidTHUOCVATTU" value="">
<input type="hidden" id="txtTEN_THUOC" value="">
<input type="hidden" id="hdfMANHOM" value="">
<input type="hidden" id="hdfSoPhieu" value="">

<script type="text/javascript">
    var user_id = '{user_id}';
    var full_name = '{full_name}';
    var schema = '{db_schema}';
    var province_id = '{province_id}';
    var hospital_id = '{hospital_id}';
    var uuid = '{uuid}';
    var lang = "vn";
    var type = "{type}";
    var idmaubenhpham = '{idmaubenhpham}';
    var idketquacls = '{idketquacls}';
    var iddichvukb = '{iddichvukb}';
    //nghiant 28082017
    var rolePTH = '{rolePTH}';
    //end nghiant
    var origin = '{origin}';
    //HaNv_02072018
    var printGPB = '{printGPB}';
    //End_HaNv_02072018
    var resizingTable = '{resizingTable}';
    var allow_resizingTable = resizingTable == "0" ? true : false;
    console.log('user_id=' + user_id + ', schema=' + schema + ', province_id=' + province_id + ', hospital_id=' + hospital_id + ', type=' + type + ', rolePTH: ' + rolePTH);

    var session_par = [];
    session_par[1] = user_id;
    session_par[2] = schema;
    session_par[3] = province_id;
    session_par[0] = hospital_id;

    var mode = '{showMode}';
    if (mode == 'dlg') {
        parent.DlgUtil.tunnel(DlgUtil.moveEvent);
    }

    var _opts = new Object();
    _opts._param = session_par;
    _opts._uuid = uuid;
    _opts._idmaubenhpham = idmaubenhpham;
    _opts._idketquacls = idketquacls;
    _opts._iddichvukb = iddichvukb;
    _opts._rolePTH = rolePTH;//nghiant 28082017
    _opts._origin = origin;
    _opts._printGPB = printGPB;//HaNv_02072018
    _opts._hospital_id = hospital_id;
    var khoaId = {dept_id};
    var phongId = {subdept_id};
    initRest(_opts._uuid);

    tinymce.init({
        selector: '#txtKetQua',
        theme: 'modern',
        plugins: ['textcolor colorpicker table paste fullscreen noneditable preventdelete'],
        toolbar1: 'undo redo |  forecolor backcolor | formatselect fontselect fontsizeselect removeformat | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | table | fullscreen',
        menubar: false,
        statusbar: false,
        language: 'vi_VN',
        auto_focus: "txtKetQua",
        object_resizing: allow_resizingTable,
        paste_merge_formats: false,
        paste_retain_style_properties: "color font-size",
        paste_word_valid_elements: "b,strong,i,em,h1,h2,table,tr,td,th",
        fontsize_formats: "8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt",
        font_formats: "Arial=arial;Times New Roman=times new roman;",
        setup: function (ed) {
            ed.on('init', function () {
                this.getDoc().body.style.fontSize = '14pt';
                this.getDoc().body.style.fontFamily = 'Times New Roman';
                this.selection.select(this.getBody(), true);
            });
        }
    });

    var tt = new TraKetQua(_opts);
    tt.load();
    var pic = new HinhAnhCLS();
    var cap = new ChupAnhCLS();

    initAjax("/vnpthis");//them lib ajax
    ajaxSvc.register("PortalWS");
    ajaxSvc.register("HL7_Gateway");

</script>
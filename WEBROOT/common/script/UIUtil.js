var jqGrid_formater = [];
var dlgPopup;

CalMultiDatesPicker = {
    msgTpl: {
    },
    init: function (pCtrl) {
        $(`#${pCtrl}`).multiDatesPicker({
            dateFormat: "dd/mm/yy",
            showHour: false,
            showMinute: false,
            showSecond: false,
            monthNames: ["Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6", "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"],
            monthNamesShort: ["Th 1", "Th 2", "Th 3", "Th 4", "Th 5", "Th 6", "Th 7", "Th 8", "Th 9", "Th 10", "Th 11", "Th 12"]
        });
        CalMultiDatesPicker.overriddenSelectDate(`#${pCtrl}`);
        return $(`#${pCtrl}`);
    },
    show: function (pCtrl) {
        $(`#${pCtrl}`).focus();
    },
    overriddenSelectDate: function (idElement){
        let overriddenSelectDateElements=[];
        if(!overriddenSelectDateElements.includes(idElement))
        {
            overriddenSelectDateElements.push(idElement);
            console.log("listElement Overridden Select Date: ",overriddenSelectDateElements)
            $.datepicker._selectDateOverload = $.datepicker._selectDate;
            $.datepicker._selectDate = function (id, dateStr) {
                let target = $(id);
                if(overriddenSelectDateElements.includes(id))
                {
                    var inst = this._getInst(target[0]);
                    inst.inline = true;
                    $.datepicker._selectDateOverload(id, dateStr);
                    inst.inline = false;
                    if (target[0].multiDatesPicker != null)
                    {
                        target[0].multiDatesPicker.changed = false;
                    } else {
                        target.multiDatesPicker.changed = false;
                    }
                    this._updateDatepicker(inst);
                }else{
                    $.datepicker._selectDateOverload(id, dateStr);
                }
            };
        }
    }
}

function NewCssCal(pCtrl, pFormat, pScroller, pShowTime, pTimeMode, pShowSeconds, _options) {
    console.log('pShowTime=0:' + pShowTime);
    var _timeFormat = "HH:mm:ss";
    if (!pShowSeconds) {
        _timeFormat = "HH:mm";
    }
    var changingDate = false;
    if (_options && _options["onChangeMonthYear"]) {
        _options["onChangeMonthYear"] = function (year, month, inst) {
            if (changingDate) {
                return;
            }
            changingDate = true;
            var dayOfSelect = new Date(year, month - 1, inst.currentDay, inst.input.val().substring(11, 13), inst.input.val().substring(14, 16), inst.input.val().substring(17, 19));
            if (dayOfSelect.getFullYear() != year || dayOfSelect.getMonth() != (month - 1) || dayOfSelect.getDate() != inst.currentDay) {
                dayOfSelect = new Date(year, month - 1, 1, inst.input.val().substring(11, 13), inst.input.val().substring(14, 16), inst.input.val().substring(17, 19));
            }
            $(this).datepicker("setDate", dayOfSelect);
            $(".ui-datepicker-month, .ui-datepicker-year").css("color", "black");
            changingDate = false;
        }
	};
    if (pShowTime) {
        console.log('NewCssCal=1');
        var opt = $.extend(_options, {
            timeInput: pShowTime,
            showTime: pShowTime,
            dateFormat: "dd/mm/yy",
            timeFormat: _timeFormat,
            showHour: false,
            showMinute: false,
            showSecond: false,
            monthNames: ["Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6", "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"],
            monthNamesShort: ["Th 1", "Th 2", "Th 3", "Th 4", "Th 5", "Th 6", "Th 7", "Th 8", "Th 9", "Th 10", "Th 11", "Th 12"]
        });
        $('#' + pCtrl).datetimepicker(opt);
        $('#' + pCtrl).datepicker("show");
        $(".ui-datepicker-month, .ui-datepicker-year").css("color", "black");
        $(".ui_tpicker_time_input").on("input", function (e) {
            let _vl = e.currentTarget.value;
            let numberColon = (_vl.match(/:/g) || []).length;
            if (numberColon > 2) {
                this.value = _vl.substring(0, _vl.length - 1);
                return;
            }
            if (_vl.length > (6 + numberColon)) this.value = _vl.substring(0, (6 + numberColon));
        });
        setTimeout(function () {
            $(".ui_tpicker_time_input").keydown(function (e) {//HaNv_110823: L2PT-49206
                if (e.keyCode == 13) {
                    let _vl = e.currentTarget.value;
                    if (_vl.length == 5) {
                        this.value = _vl + ':00';
                    }
                    if (typeof _options["onSelect"] === 'function') {
                        let _timeS = this.value.length == 6 ? this.value.replace( /(\d{2})(\d{2})(\d{2})/, "$1:$2:$3") : (this.value.length == 4 ? this.value.replace( /(\d{2})(\d{2})/, "$1:$2:00") : this.value);
                        if (moment(_timeS, "hh:mm:ss").isValid()) {
                            _options.onSelect(moment($('#' + pCtrl).datepicker("getDate")).format("DD/MM/YYYY ") + _timeS);
                        }
                    }
                }
            });
        }, 1000)
    } else {
        console.log('NewCssCal=2 pScroller=' + pScroller);
        if (pScroller == 'month') {
            console.log('NewCssCal=3');
            $('#' + pCtrl).MonthPicker({
                Button: false
            });
            $('#' + pCtrl).MonthPicker('Open');
        } else {
            console.log('NewCssCal=4');
            var opt = $.extend(_options, {
                dateFormat: "dd/mm/yy",
                monthNames: ["Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6", "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"],
                monthNamesShort: ["Th 1", "Th 2", "Th 3", "Th 4", "Th 5", "Th 6", "Th 7", "Th 8", "Th 9", "Th 10", "Th 11", "Th 12"]
            });
            $('#' + pCtrl).datepicker(opt);
            $('#' + pCtrl).datepicker("show");
            $(".ui-datepicker-month, .ui-datepicker-year").css("color", "black");
        }
    }

    //$('.ui-datepicker-div').css('zIndex', 10099);
}


Element.prototype.getElementWidth = function () {
    if (typeof this.clip !== "undefined") {
        return this.clip.width;
    } else {
        if (this.style.pixelWidth) {
            return this.style.pixelWidth;
        } else {
            return this.offsetWidth;
        }
    }
};
/**/

$.sleep = function (milliseconds) {
    var start = new Date().getTime();

    var timer = true;
    while (timer) {
        if ((new Date().getTime() - start) > milliseconds) {
            timer = false;
        }
    }
}
$.waitUntil = function (isready, success, error, count, interval) {
    console.log('waitUntil count=' + count);
    if (count === undefined) {
        count = 300;
    }
    if (interval === undefined) {
        interval = 20;
    }
    if (isready()) {
        success();
        return;
    }
    // The call back isn't ready. We need to wait for it
    $.sleep(interval);
    if (!count) {
        // We have run out of retries
        if (error !== undefined) {
            error();
        }
    } else {
        // Try again
        $.waitUntil(isready, success, error, count - 1, interval);
    }

    /*
	    setTimeout(function(){
	        if (!count) {
	            // We have run out of retries
	            if (error !== undefined) {
	                error();
	            }
	        } else {
	            // Try again
	            $.waitUntil(isready, success, error, count -1, interval);
	        }
	    }, interval);
	    */
}
//--------- ButtonUtil --------------------------------------------------------------------------------------

$.fn.bindOnce = function (event, callback, _waitTime) {
    //console.log('bind '+event);
    var element = $(this[0]),
        defer = element.data("bind_once_defer_" + event);

    if (!defer) {
        defer = $.Deferred();

        function deferCallback() {
            console.log('unbind ' + event);
            element.unbind(event, deferCallback);
            defer.resolveWith(this, arguments);
        }

        element.bind(event, deferCallback)
        element.data("bind_once_defer_" + event, defer);
    }
    defer.done(callback).then(function () {
        //console.log('callback done!');
        element.data("bind_once_defer_" + event, false);
        //var milliseconds=10000;
        if (!_waitTime) _waitTime = 10000;
        console.log('delay ' + event + ' _waitTime=' + _waitTime);
        setTimeout(function () {
            $(element).bindOnce(event, callback, _waitTime);
        }, _waitTime);
    });

    //return defer.done( callback ).promise();
};
var ElementUtil = {
    setEnable: function (_ena, _dis) {
        for (let element of _ena) {
            $("#" + element).attr('disabled', false);
        }
        for (let element of _dis) {
            $("#" + element).attr('disabled', true);
        }
    },
    setVisibility: function (_vis, _dis) {
        for (let element of _vis) {
            $("#" + element).show();
        }
        for (let element of _dis) {
            $("#" + element).hide();
        }
    }
};
var ButtonUtil = {
    bindClick: function (btnId, _func) {
        $('#' + btnId).off();
        $('#' + btnId).on("click", function (evt) {
            var $btn = $(this);
            if ($btn.data('flag') === true) {
                // Previously submitted - don't submit again
                e.preventDefault();

            } else {
                // Mark it so that the next submit can be ignored
                $btn.data('flag', true);
                $.done(_func(evt)).then();
            }
        });
    },
    toLoadding: function (btnId) {
        $("#" + btnId + " .spin").removeClass("hide");
        $("#" + btnId + " .spin").next(".glyphicon").addClass("hide");
        $("#" + btnId).attr("disabled", "disabled");
    },

    toLoaded: function (btnId) {
        $("#" + btnId + " .spin").addClass("hide");
        $("#" + btnId + " .spin").next(".glyphicon").removeClass("hide");
        $("#" + btnId).removeAttr("disabled");
    },

    disable: function () {
        for (let i = 0; i < arguments.length; i++) {
            let btnId = arguments[i];
            if (typeof btnId == "string") {
                $("#" + btnId).attr("disabled", "disabled");
            }
        }
    },

    enable: function () {
        for (let i = 0; i < arguments.length; i++) {
            let btnId = arguments[i];
            if (typeof btnId == "string") {
                $("#" + btnId).removeAttr("disabled");
            }
        }
    }
}

//--------- EventUtil --------------------------------------------------------------------------------------
var EventUtil = {
    eventPool: {},
    varPool: {},
    getVar: function (varName) {
        return this.varPool[varName];
    },
    setVar: function (varName, varObj) {
        this.varPool[varName] = varObj;
    },

    getEvent: function (evName) {
        console.log('getEvent ' + evName);
        return this.eventPool[evName];
    },
    setEvent: function (evName, evFunc) {
        console.log('setEvent ' + evName + (typeof evFunc));
        this.eventPool[evName] = evFunc;
    },
    raiseEvent: function (evName, evObj) {
        console.log('setEvent ' + evName + (typeof evFunc));
        var evFunc = this.eventPool[evName];
        if (typeof evFunc === 'function') {
            evFunc(evObj);
        } else {
            console.log('evFunc not a function');
        }
    }
};
//--------- DlgUtil --------------------------------------------------------------------------------------
var DlgUtil = {
    dlg: [],
    buildPopupGrid: function (dlgId, gridId, _title, _width, _height) {
        //popup 1 grid
        $('#' + dlgId).html('<table id="' + gridId + '"></table><div id="pager_' + gridId + '"></div>');
        dlgPopup = new jBox('Modal', {
            title: _title,
            closeOnClick: false,
            closeButton: 'title',
            overlay: true,
            content: $('#' + dlgId),
            draggable: 'title',
            width: _width,
            height: _height,
            onClose: function () {
                EventUtil.raiseEvent(dlgId + '_onClose', dlgId);
            }
        });
        return dlgPopup;
    },
    showMsg: function (msgInfo, _func, _delay, _cssClass, _attrs) {

        //DlgUtil.showMsg("Nội dung thông báo","Thông báo",0);
        //alert(msgInfo);
        alertify.set({
            labels: {
                ok: "Đồng ý",
                cancel: "Hủy bỏ"
            },
            delay: 5000,
            buttonReverse: false,
            buttonFocus: "ok"
        });
        console.log('showMsg _delay=' + _delay);
        let attrs = [];
        if (Array.isArray(_attrs)) {
            attrs = _attrs;
        }
        if (window.GLOBAL_CONFIG
            && window.GLOBAL_CONFIG["DIALOG_LINE_CLAMP"]
            && window.GLOBAL_CONFIG["DIALOG_LINE_CLAMP"] > 2) {
            if (_cssClass) {
                _cssClass = _cssClass + " dialog_line_clamp";
            } else {
                _cssClass = "info dialog_line_clamp";
            }
            attrs.push({key: "style", value: "--clamp-line: " + window.GLOBAL_CONFIG["DIALOG_LINE_CLAMP"]});
        }
        alertify.alert(msgInfo, _func, _cssClass, _delay, attrs);
        /*
	$.waitUntil(function(){
		console.log('waitUntil.waitFlag='+waitFlag);
		return !waitFlag;
    }, function(){}, function(){},10,1000);
    */

    },

    showError: function (msgErr, delay) {
        if (!delay) delay = 5000;
        alertify.error(msgErr, delay);
    },

    showConfirm: function (msgInfo, _func, _delay, _cssClass, _attrs) {
        //DlgUtil.showMsg("Nội dung thông báo","Thông báo",0);
        //alert(msgInfo);
        alertify.set({
            labels: {
                ok: "Đồng ý",
                cancel: "Hủy bỏ"
            },
            delay: 5000,
            buttonReverse: false,
            buttonFocus: "ok"
        });
        let attrs = [];
        if (Array.isArray(_attrs)) {
            attrs = _attrs;
        }
        if (window.GLOBAL_CONFIG
            && window.GLOBAL_CONFIG["DIALOG_LINE_CLAMP"]
            && window.GLOBAL_CONFIG["DIALOG_LINE_CLAMP"] > 2) {
            if (_cssClass) {
                _cssClass = _cssClass + " dialog_line_clamp";
            } else {
                _cssClass = "info dialog_line_clamp";
            }
            attrs.push({key: "style", value: "--clamp-line: " + window.GLOBAL_CONFIG["DIALOG_LINE_CLAMP"]});
        }
        alertify.confirm(msgInfo, _func, _cssClass, _delay, attrs);
    },


    buildPopupUrl: function (dlgName, dlgId, _url, _var, _title, _width, _height, _opt) {
//dlgName: Tên dialog, sử dụng để điều khiển đóng mở dialog
//dlgId: Id thẻ div chứa nội dung dialog
//_url: url nội dung dialog
//_var: biến truyền vào dialog
//_title,_width,_height: Tiêu đề, độ rộng, chiều cao của dialog
        if (window.uiVersion && window.uiVersion != '0') {
            let _chPopupSize = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "HIS_POPUP_SIZE_PERCENT");
            if (_chPopupSize == '1') {
                let dialogName = _url.replace("manager.jsp?func=../", "").replace(/\&(.*)/, "");
                let data = [];
                try {
                    data = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DIALOG.INFO', JSON.stringify({
                        DIALOG_NAME: dialogName
                    }));
                } catch (e) {
                    console.error(e);
                }

                if (data && data.length > 0) {
                    let wScreen = $(document).find("body").width();
                    let innerW = window.innerWidth;
                    if (innerW < 1280) {
                        if (data[0]["WIDTH_2"] && data[0]["WIDTH_2"] != "" && parseInt(data[0]["WIDTH_2"]) > 0) {
                            _width = parseInt(data[0]["WIDTH_2"]) * wScreen / 100;
                        }
                        if (data[0]["HEIGHT_2"] && data[0]["HEIGHT_2"] != "" && parseInt(data[0]["HEIGHT_2"]) > 0) {
                            let hScreen = $(window).height();
                            _height = parseInt(data[0]["HEIGHT_2"]) * hScreen / 100;
                        }
                    } else if (innerW >= 1280 && innerW < 1919) {
                        if (data[0]["WIDTH"] && data[0]["WIDTH"] != "" && parseInt(data[0]["WIDTH"]) > 0) {
                            _width = parseInt(data[0]["WIDTH"]) * wScreen / 100;
                        }
                        if (data[0]["HEIGHT"] && data[0]["HEIGHT"] != "" && parseInt(data[0]["HEIGHT"]) > 0) {
                            let hScreen = $(window).height();
                            _height = parseInt(data[0]["HEIGHT"]) * hScreen / 100;
                        }
                    } else {
                        if (data[0]["WIDTH_3"] && data[0]["WIDTH_3"] != "" && parseInt(data[0]["WIDTH_3"]) > 0) {
                            _width = parseInt(data[0]["WIDTH_3"]) * wScreen / 100;
                        }
                        if (data[0]["HEIGHT_3"] && data[0]["HEIGHT_3"] != "" && parseInt(data[0]["HEIGHT_3"]) > 0) {
                            let hScreen = $(window).height();
                            _height = parseInt(data[0]["HEIGHT_3"]) * hScreen / 100;
                        }
                    }
                }
            }
        }


        dlgId = dlgName;
        if ($('#divHidden').find("#" + dlgId).length > 0) {
            console.log('divHidden.html=' + $('#divHidden').html());
        }
//			else if(document.getElementById(dlgId+'ifmView')){
//
//			}
        else {
            var _html = '';
            _html += '<div id="' + dlgId + '" style="width: 100%; display: none">';
            _html += '<iframe src="" id="' + dlgId + 'ifmView"	frameborder="0"></iframe>';
            _html += '</div>';
            $('#divHidden').html(_html);
        }
        //$('#'+dlgId+'ifmView').css("width",_width-30);
        //$('#'+dlgId+'ifmView').css("height",_height-30);

        $('#' + dlgId + 'ifmView').css("width", _width);
        //$('#'+dlgId+'ifmView').css("height",_height+11);
        console.log('#' + dlgId + 'ifmView.height' + _height);
        $('#' + dlgId + 'ifmView').height(_height);

        var glbDeptId = $('.iframe:visible', window.parent.document).prevObject.find('body').prevObject.find('#txtGLBDEPTID').val();
        var glbSubDeptId = $('.iframe:visible', window.parent.document).prevObject.find('body').prevObject.find('#txtGLBSUBDEPTID').val();

        _var.glbDeptId = glbDeptId;
        _var.glbSubDeptId = glbSubDeptId;

        EventUtil.setVar("dlgVar", _var);

        $('#' + dlgId + 'ifmView').attr("src", _url + '&showMode=dlg');

        var __opts = {
            title: _title,
            theme: 'ModalBorder',
            closeOnEsc: false,
            closeOnClick: false,
            closeButton: 'title',
            overlay: true,
            zIndex: 10000,
            content: $('#' + dlgId),
            draggable: 'title',
            width: _width,
            height: _height,
            onClose: function () {
                console.log('raiseEvent onClose =' + dlgName);
                EventUtil.raiseEvent(dlgName + '_onClose', dlgName);
            },
            onCreated: function () {
                if (window.uiVersion && window.uiVersion == '2') {
                    $("#" + this.id).css("visibility", "hidden");
                }
            },
            onOpen: function () {
                if (window.uiVersion && window.uiVersion == '2') {
                    let selt = this;
                    $("#" + selt.id).find(".wrap_loading").removeClass("hide").addClass("show");
                    $("#" + selt.id).hide();
                    const iframe = $("#" + selt.id + " iframe")[0];
                    iframe.addEventListener("load", function () {
                        $(this).css("width", $(document).width() + "px");
                        if (this.contentWindow.parent.getParameterByName("showMode") == "dlg") {
                            selt.container.parent().css("margin-left", parseInt(selt.container.parent().css("margin-left").replace(/px$/, "")) - (selt.container[0].ownerDocument.defaultView.innerWidth - $(selt.container.parents("body")[0]).width()) / 2 + "px")
                        }
                        $("#" + selt.id).css("visibility", "visible");

                        $(this.contentDocument).find("html").css("width", _width + "px");
                        $(this.contentDocument).find("html").css("overflow-x", "hidden");
                        this.contentDocument.body.style.width = "100%";
                        this.contentDocument.body.style.overflowX = "hidden";
                        $(this.contentDocument.body).find(">div").css("width", "100%");
                        $(this.contentDocument.body).find("#divMain").css("width", "100%");
                        $(this.contentDocument.body).find("#divMain").css("position", "relative");
                        try {
                            this.contentDocument.body.getElementsByTagName("div")[0].style.float = "left";
                        } catch (e) {
                            console.warn(e);
                        }
                        try {
                            $(this.contentDocument.body).find(">div>div").css("width", "100%");
                        } catch (e1) {
                            console.warn(e1)
                        }
                        if (this.contentWindow.getParameterByName("showMode") == 'dlg') {
                            let mgLeft = (-275 - (this.contentWindow.innerWidth - $($(this.contentWindow.document).find("body > div")[0]).width()) / 2) + "px";
                            let styleAlertity = this.contentDocument.getElementById("alertify-el");
                            if (styleAlertity) {
                                styleAlertity.innerText = `#alertify { margin-left: ${mgLeft} !important; }`;
                            } else {
                                styleAlertity = this.contentDocument.createElement("style");
                                styleAlertity.setAttribute("id", "alertify-el");
                                styleAlertity.innerText = `#alertify { margin-left: ${mgLeft} !important; }`;
                                this.contentDocument.getElementsByTagName("html")[0].appendChild(styleAlertity);
                            }
                            // $(this.contentWindow.document).find("html").append(`<style> #alertify { margin-left: ${mgLeft} !important; } </style>`);
                        }
                        $("#" + selt.id).find(".wrap_loading").removeClass("show").addClass("hide");
                        $("#" + selt.id).show();
                    });
                }
            }
        };
        if (_opt) {
            __opts = $.extend(__opts, _opt);
        }
        dlgPopup = new jBox('Modal', __opts);
        this.dlg[dlgName] = dlgPopup;
        return dlgPopup;
    },
    buildPopup: function (dlgName, dlgId, _title, _width, _height, _opt) {
        //popup 1 grid
        //var _dlgName = Object.create(dlgName);
        var __opts = {
            title: _title,
            closeOnEsc: false,
            closeOnClick: false,
            closeButton: 'title',
            overlay: true,
            content: $('#' + dlgId),
            draggable: 'title',
            width: _width,
            height: _height,
            onClose: function () {
                EventUtil.raiseEvent(dlgName + '_onClose', dlgName);
            }
        };
        if (_opt) {
            __opts = $.extend(__opts, _opt);
        }
        dlgPopup = new jBox('Modal', __opts);
        this.dlg[dlgName] = dlgPopup;
        return dlgPopup;
    },
    buildImageEditorPopup: function (imageUrl, closeFunc) {
        var _html = '';
        _html += '<div id="imageedittor" style="width: 100%; display: none">';
        _html += '<iframe src="/vnpthis/imageedittor/index.htm" id="imageedittor_ifmView"></iframe>';
        _html += '</div>';
        $('#divHidden').html(_html);
        $('#imageedittor_ifmView').css("width", 800);
        $('#imageedittor_ifmView').height(600);
        EventUtil.setVar("dlgVar_buildImageEditorPopup", {imageUrl: imageUrl});
        var __opts = {
            title: "HIS - Image editor",
            theme: 'ModalBorder',
            closeOnEsc: false,
            closeOnClick: false,
            closeButton: 'title',
            overlay: true,
            zIndex: 10000,
            content: $('#imageedittor'),
            draggable: 'title',
            width: 800,
            height: 604
        };
        if (typeof closeFunc == 'function') {
            EventUtil.setEvent('imageedittor_onClose', closeFunc);
        }
        dlgPopup = new jBox('Modal', __opts);
        this.dlg.imageedittor = dlgPopup;
        return dlgPopup;
    },
    buildSignPadPopup: function (title, width, height, params, closeFunc) {
        var _html = '';
        var w = isNaN(parseInt(width)) ? 628 : parseInt(width);
        var h = isNaN(parseInt(height)) ? 454 : parseInt(height);
        var t = title ? title : "HIS - Sign Pad";
        _html += '<div id="signpad" style="width: 100%; display: none">';
        _html += '<iframe src="/vnpthis/signpad/index.htm" id="signPad_ifmView"></iframe>';
        _html += '</div>';
        $('#divHidden').html(_html);
        $('#signPad_ifmView').css("width", w);
        $('#signPad_ifmView').height(h);
        EventUtil.setVar("dlgVar_buildSignPadPopup", {params: params});
        var __opts = {
            title: t,
            theme: 'ModalBorder',
            closeOnEsc: false,
            closeOnClick: false,
            closeButton: 'title',
            overlay: true,
            zIndex: 10000,
            content: $('#signpad'),
            draggable: 'title',
            width: w,
            height: h
        };
        if (typeof closeFunc == 'function') {
            EventUtil.setEvent('signpad_onClose', closeFunc);
        }
        dlgPopup = new jBox('Modal', __opts);
        this.dlg.signpad = dlgPopup;
        return dlgPopup;
    },

    buildFileManagementPopup: function (title, width, height, params, closeFunc) {
        let _html = '';
        let w = 628;
        let h = 454;
        let wScreen = $(document).find("body").width();
        let hScreen = $(window).height();
        if (width) {
            if (width.endsWith("%")) {
                w = Math.min(parseFloat(width), 95) * wScreen / 100;
            } else if (!isNaN(parseInt(width))) {
                w = parseInt(width);
            }
        }
        if (height) {
            if (height.endsWith("%")) {
                h = Math.min(parseFloat(height), 95) * hScreen / 100;
            } else if (!isNaN(parseInt(height))) {
                h = parseInt(height);
            }
        }
        let t = title ? title : "HIS - File Management";
        _html += '<div id="filemanager" style="width: 100%; display: none">';
        _html += '<iframe src="/vnpthis/pub/file-manager.htm" id="filemanager_ifmView"></iframe>';
        _html += '</div>';
        $('#divHidden').html(_html);
        $('#filemanager_ifmView').css("width", w);
        $('#filemanager_ifmView').height(h);
        if (jsonrpc && jsonrpc.AjaxJson && jsonrpc.AjaxJson.uuid) {
            params.uuid = jsonrpc.AjaxJson.uuid;
            params.RestInfo = RestInfo;
        }
        EventUtil.setVar("dlgVar_buildFileManagementPopup", params)
        let __opts = {
            title: t,
            theme: 'ModalBorder',
            closeOnEsc: false,
            closeOnClick: false,
            closeButton: 'title',
            overlay: true,
            zIndex: 10000,
            content: $('#filemanager'),
            draggable: 'title',
            width: w,
            height: h
        };
        if (typeof closeFunc == 'function') {
            EventUtil.setEvent('filemanager_onClose', closeFunc);
        }
        dlgPopup = new jBox('Modal', __opts);
        this.dlg.filemanager = dlgPopup;
        return dlgPopup;
    },

    buildFingerScanPopup: function (title, width, height, params, closeFunc) {
        var _html = '';
        var w = isNaN(parseInt(width)) ? 628 : parseInt(width);
        var h = isNaN(parseInt(height)) ? 454 : parseInt(height);
        var t = title ? title : "HIS - Finger Scanner";
        _html += '<div id="fingerpad" style="width: 100%; display: none">';
        _html += '<iframe src="/vnpthis/pub/finger.htm" id="fingerPad_ifmView"></iframe>';
        _html += '</div>';
        $('#divHidden').html(_html);
        $('#fingerPad_ifmView').css("width", w);
        $('#fingerPad_ifmView').height(h);
        if (jsonrpc && jsonrpc.AjaxJson && jsonrpc.AjaxJson.uuid) {
            params.uuid = jsonrpc.AjaxJson.uuid;
            params.RestInfo = RestInfo;
        }
        EventUtil.setVar("dlgVar_buildFingerScanPopup", {params: params});
        var __opts = {
            title: t,
            theme: 'ModalBorder',
            closeOnEsc: false,
            closeOnClick: false,
            closeButton: 'title',
            overlay: true,
            zIndex: 10000,
            content: $('#fingerpad'),
            draggable: 'title',
            width: w,
            height: h
        };
        if (typeof closeFunc == 'function') {
            EventUtil.setEvent('fingerpad_onClose', closeFunc);
        }
        dlgPopup = new jBox('Modal', __opts);
        this.dlg.fingerpad = dlgPopup;
        return dlgPopup;
    },

    buildPDFScanPopup: function (title, width, height, params, closeFunc) {
        var _html = '';
        var w = isNaN(parseInt(width)) ? 628 : parseInt(width);
        var h = isNaN(parseInt(height)) ? 454 : parseInt(height);
        var t = title ? title : "HIS - Sign Pad";
        _html += '<div id="scannerPdf" style="width: 100%; display: none">';
        _html += `<iframe src="/vnpthis/scanner/index.htm" id="scanPdf_ifmView" width="${w}" height="${h}"></iframe>`;
        _html += '</div>';
        $('#divHidden').html(_html);
        $('#signPad_ifmView').css("width", w);
        $('#signPad_ifmView').height(h);
        EventUtil.setVar("dlgVar_buildPDFScanPopup", {params: params});
        var __opts = {
            title: t,
            theme: 'ModalBorder',
            closeOnEsc: false,
            closeOnClick: false,
            closeButton: 'title',
            overlay: true,
            zIndex: 10000,
            content: $('#scannerPdf'),
            draggable: 'title',
            width: w,
            height: h
        };
        if (typeof closeFunc == 'function') {
            EventUtil.setEvent('buildPDFScanPopup_onClose', closeFunc);
        }
        dlgPopup = new jBox('Modal', __opts);
        this.dlg.signpad = dlgPopup;
        return dlgPopup;
    },
    open: function (dlgName) {
        if (this.dlg[dlgName]) {
            this.dlg[dlgName].open();
        }
    },
    close: function (dlgName) {
        if (this.dlg[dlgName]) {
            this.dlg[dlgName].close();
        }
    },
    tunnel: function (fn) {
        /*
	var fnc=function(e){
		alert('e.msg='+e.msg);
		//DlgUtil.close("dlgCDDV");
	};
	*/
        fn(EventUtil.eventPool, EventUtil.varPool);
    },
    moveEvent: function (_evt, _var) {
        EventUtil.eventPool = $.extend(EventUtil.eventPool, _evt);
        EventUtil.varPool = $.extend(EventUtil.varPool, _var);
    },
    openForm: function (url, width, height, func) {
        form = window.open(url, '_blank', 'toolbar=no,location=no,scrollbars=yes,directories=0,status=yes,menubar=no,resizable=yes, copyhistory=no, width=' + width + ', height=' + height + ',left=50,top=50');
        form.callbackHandler = func;
        return form;
    }
}
//--------- ToolbarUtil ------------------------------------------------------------------------------------
var ToolbarUtil = {
    __toolbar: [],
    __iconPrefix: 'glyphicon glyphicon-',
    build: function (tbrId, ctl_ar, icoPrefix) {
        var _self = this;
        if (icoPrefix) this.__iconPrefix = icoPrefix;
        this.__toolbar[tbrId] = new _self.JsToolbar(tbrId, this.__iconPrefix);
        if (ctl_ar) {
            //console.log('1ctl_ar='+JSON.stringify(ctl_ar));
            this.__toolbar[tbrId].buildToolbar(ctl_ar);
        }
        return this.__toolbar[tbrId];
    },
    // cho BM2
    buildVs2: function (tbrId, ctl_ar, icoPrefix) {
        var _self = this;
        if (icoPrefix) this.__iconPrefix = icoPrefix;
        this.__toolbar[tbrId] = new _self.JsToolbar(tbrId, this.__iconPrefix);
        if (ctl_ar) {
            //console.log('1ctl_ar='+JSON.stringify(ctl_ar));
            this.__toolbar[tbrId].buildToolbarVs2(ctl_ar);
        }
        return this.__toolbar[tbrId];
    },
    getToolbar: function (tbrId) {
        return this.__toolbar[tbrId];
    },
    JsToolbar: function (toolbarId, iconPrefix) {
        var __toolbarId = toolbarId;
        var __iconPrefix = iconPrefix;
        var __items = [];
        var __htmls = [];
        var _self = this;
        this.buildToolbar = function (ctl_ar) {
            //console.log('2ctl_ar='+JSON.stringify(ctl_ar));
            for (var i1 = 0; i1 < ctl_ar.length; i1++) {
                var ctl = ctl_ar[i1];
                //console.log('ctl['+i1+']='+JSON.stringify(ctl));
                _self.addToolbarCtl(ctl, true);
            }
            $('#' + __toolbarId).html(__htmls.join(""));
            //bindEvent();

        }
        // thêm động cho BM2
        this.buildToolbarVs2 = function (ctl_ar) {
            //console.log('2ctl_ar='+JSON.stringify(ctl_ar));
            for (var i1 = 0; i1 < ctl_ar.length; i1++) {
                var ctl = ctl_ar[i1];
                //console.log('ctl['+i1+']='+JSON.stringify(ctl));
                if (ctl.show != "false") {
                    _self.addToolbarCtlVs2(ctl, true);
                }
            }
            $('#' + __toolbarId).html(__htmls.join(""));
            $('#' + __toolbarId).css('height', '40px');
            //bindEvent();

        }
        this.addEvent = function (ctlId, evtName, evtFunc) {
            $('#' + __toolbarId + ctlId).on(evtName, evtFunc);
        }
        this.setValue = function (ctlId, _val) {
            return $('#' + __toolbarId + ctlId).val(_val);
        }
        this.getValue = function (ctlId) {
            return $('#' + __toolbarId + ctlId).val();
        }
        this.setCSS = function (ctlId, _attr, _val) {
            $('#' + __toolbarId + ctl.id).css(_attr, _val);
        }
        this.setAttr = function (ctlId, _attr, _val) {
            $('#' + __toolbarId + ctl.id).attr(_attr, _val);
        }
        this.removeAttr = function (ctlId, _attr, _val) {
            $('#' + __toolbarId + ctl.id).removeAttr(_attr);
        }

        this.addToolbarCtl = function (_ctl, _flag) {
            //ctlType,ctlId,ctlIcon,ctlText,ctlChild
            //ctlChild=[{id:'',text:'',icon:'',hlink:''}]
            var toolbar = $('#' + __toolbarId);
            //alert(toolbar);
            var _html = '';
            var _btnClass = "wd100";
            if (_ctl.cssClass) {
                _btnClass = _ctl.cssClass;
            }
            if (_ctl.type == 'button') {
                _html += '<button type="button" id="' + __toolbarId + _ctl.id + '" class="btn btn-lg btn-default ' + _btnClass + '"><span class="' + __iconPrefix + _ctl.icon + '" ></span>&nbsp;' + _ctl.text + '&nbsp;</button>';
            } else if (_ctl.type == 'buttongroup') {
                _html += '<div class="btn-group ' + _btnClass + '" role="group" >';
                _html += '  <button type="button" id="' + __toolbarId + _ctl.id + '" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"  style="width:100%;">';
                _html += '    <span class="' + __iconPrefix + _ctl.icon + '"></span>&nbsp;' + _ctl.text + '&nbsp;<span class="caret"></span>';
                _html += '  </button>';
                _html += '  <ul class="dropdown-menu">';
                var _cls = '';
                var childrenSorted = _ctl.children.sort(function (a, b) {
                    if (a.order) {
                        if (b.order) {
                            if (a.order <= b.order) {
                                return -1;
                            }
                            return 1;
                        }
                        return 1;
                    } else {
                        if (b.order) {
                            return -1;
                        }
                        return 0;
                    }
                });
                for (var i1 = 0; i1 < _ctl.children.length; i1++) {
                    var item = childrenSorted[i1];
                    if (item.group) {
                        _cls = '&nbsp;&nbsp;&nbsp;&nbsp;';
                        _html += '<li style="background-color:#004f9e!important" id="' + __toolbarId + item.id + '"><a href="#"><i class="' + __iconPrefix + 'folder-open"></i>&nbsp;&nbsp;' + item.text + '</a></li>';
                    } else {
                        var _dataExternal = {};
                        if (item.dataExternal) {
                            _dataExternal = item.dataExternal;
                        }
                        _html += '<li data-external=' + "'" + JSON.stringify(_dataExternal) + "'" + ' id="' + __toolbarId + item.id + '"><a href="' + item.hlink + '">' + _cls + '<i class="' + __iconPrefix + item.icon + '"></i>&nbsp;&nbsp;' + item.text + '</a></li>';
                    }
                }
                _html += '    </ul>';
                _html += '</div>';
            } else if (_ctl.type == 'textbox') {
                _html += '<input type="text" id="' + __toolbarId + _ctl.id + '" class="input-sm" placeholder="' + _ctl.text + '">';
            } else if (_ctl.type == 'datetime') {
                _html += '<div class="input-group inline" >';
                _html += '<input type="text" id="' + __toolbarId + _ctl.id + '" class="input-sm" data-mask="00/00/0000" placeholder="dd/MM/yyyy">';
                _html += '<span class="btn input-group-addon glyphicon glyphicon-calendar" style="display:inline-block;height:28px; width: 32px; padding: 6px 4px; top:0;" type="sCal"  onclick="NewCssCal(\'' + __toolbarId + _ctl.id + '\',\'ddMMyyyy\',\'dropdown\',false,\'24\',true)"></span>';
                _html += '</div>';
            } else if (_ctl.type == 'label') {
                _html += '<span id="' + __toolbarId + _ctl.id + '" class="inline navbar-right panel-title" style="padding: 4px 4px; top:0;">' + _ctl.text + '&nbsp;&nbsp;</span>';
            }
            __items.push(_ctl);
            __htmls.push(_html);
            if (!_flag) toolbar.html(__htmls.join(""));
        }
        //add toolbar BM2
        this.addToolbarCtlVs2 = function (_ctl, _flag) {
            //ctlType,ctlId,ctlIcon,ctlText,ctlChild
            //ctlChild=[{id:'',text:'',icon:'',hlink:''}]
            var toolbar = $('#' + __toolbarId);
            //alert(toolbar);
            var _html = '';
            var _btnClass = "wd100";

            if (_ctl.cssClass) {
                _btnClass = _ctl.cssClass;
            }
            if (_ctl.text.length > 10) {
                _btnClass = "wd120";
            }
            if (_ctl.type == 'button') {
                _html += '<button type="button" style="height:40px; font-weight:bold" id="' + __toolbarId + _ctl.id + '" class="btn btn-lg btn-default ' + _btnClass + '"><span class="' + __iconPrefix + _ctl.icon + '" ></span>&nbsp;' + _ctl.text + '&nbsp;</button>';
            } else if (_ctl.type == 'buttongroup') {
                _html += '<div class="btn-group ' + _btnClass + '" role="group" >';
                _html += '  <button type="button" style="height:40px;width:100%; font-weight:bold" id="' + __toolbarId + _ctl.id + '" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"  style="width:100%;">';
                _html += '    <span class="' + __iconPrefix + _ctl.icon + '"></span>&nbsp;' + _ctl.text + '&nbsp;<span class="caret"></span>';
                _html += '  </button>';
                _html += '  <ul class="dropdown-menu">';
                var _cls = '';
                for (var i1 = 0; i1 < _ctl.children.length; i1++) {
                    var item = _ctl.children[i1];
                    if (item.show != 'false') {
                        if (item.group) {
                            _cls = '&nbsp;&nbsp;&nbsp;&nbsp;';
                            _html += '<li style="background-color:#004f9e!important" id="' + __toolbarId + item.id + '"><a href="#"><i class="' + __iconPrefix + 'folder-open"></i>&nbsp;&nbsp;' + item.text + '</a></li>';
                        } else {
                            _html += '<li id="' + __toolbarId + item.id + '"><a href="' + item.hlink + '">' + _cls + '<i class="' + __iconPrefix + item.icon + '"></i>&nbsp;&nbsp;' + item.text + '</a></li>';
                        }
                    }

                }
                _html += '    </ul>';
                _html += '</div>';
            } else if (_ctl.type == 'textbox') {
                _html += '<input type="text" id="' + __toolbarId + _ctl.id + '" class="input-sm" placeholder="' + _ctl.text + '">';
            } else if (_ctl.type == 'datetime') {
                _html += '<div class="input-group inline" >';
                _html += '<input type="text" id="' + __toolbarId + _ctl.id + '" class="input-sm" data-mask="00/00/0000" placeholder="dd/MM/yyyy">';
                _html += '<span class="btn input-group-addon glyphicon glyphicon-calendar" style="display:inline-block;height:28px; width: 32px; padding: 6px 4px; top:0;" type="sCal"  onclick="NewCssCal(\'' + __toolbarId + _ctl.id + '\',\'ddMMyyyy\',\'dropdown\',false,\'24\',true)"></span>';
                _html += '</div>';
            } else if (_ctl.type == 'label') {
                _html += '<span id="' + __toolbarId + _ctl.id + '" class="inline navbar-right panel-title" style="padding: 4px 4px; top:0;">' + _ctl.text + '&nbsp;&nbsp;</span>';
            }
            __items.push(_ctl);
            __htmls.push(_html);
            if (!_flag) toolbar.html(__htmls.join(""));
        }

        this.clearAll = function () {
            $('#' + __toolbarId).html("");
            __currentButton = null;
            //__handler=[];
            __items = [];
            __htmls = [];
        }

    }
};


//--------- ComboUtil --------------------------------------------------------------------------------------
$.fn.filterLike = function (textbox, selectSingleMatch) {
    return this.each(function () {
        var select = this;
        var options = [];
        $(select).find('option').each(function () {
            options.push({
                value: $(this).val(),
                text: $(this).text(),
                extval0: $(this).attr('extval0'),
                extval1: $(this).attr('extval1'),
                extval2: $(this).attr('extval2'),
                extval3: $(this).attr('extval3'),
                extval4: $(this).attr('extval4'),
                extval5: $(this).attr('extval5')
            });
        });
        //console.log('filterLike'+JSON.stringify(options));
        $(select).data('options', options);
        $('#' + textbox).bind('change keyup', function () {
            var options = $(select).empty().scrollTop(0).data('options');
            var search = $.trim($(this).val());
            var searchReg = '';
            for (i = 0; i < search.length; i++) {
                searchReg += '.*' + search[i];
            }
            searchReg += '.*';
            var regex = new RegExp(searchReg, 'gi');

            $.each(options, function (i) {
                var option = options[i];
                if (option.text.match(regex) !== null) {
                    $(select).append(
                        $('<option>').text(option.text).val(option.value)
                            .attr('extval0', option.extval0).attr('extval1', option.extval1)
                            .attr('extval2', option.extval2).attr('extval3', option.extval3)
                            .attr('extval4', option.extval4).attr('extval5', option.extval5)
                    );
                }
            });
            if (selectSingleMatch === true &&
                $(select).children().length === 1) {
                $(select).children().get(0).selected = true;
                $(select).change();
            }
            $(select).change();
        });
    });
};


$.fn.filterLikeFc = function (textbox, selectSingleMatch) {
    return this.each(function () {
        var select = this;
        var options = [];
        $(select).find('option').each(function () {
            options.push({value: $(this).val(), text: $(this).text()});
        });
        //console.log('filterLike'+JSON.stringify(options));
        $(select).data('options', options);
        $('#' + textbox).bind('change keyup', function (evt) {
            if (evt.type != 'keyup') {
                return;
            }
            var options = $(select).empty().scrollTop(0).data('options');
            var search = $.trim($(this).val());
            var searchReg = '';
            for (i = 0; i < search.length; i++) {
                searchReg += '.*' + search[i];
            }
            searchReg += '.*';
            var regex = new RegExp(searchReg, 'gi');

            $.each(options, function (i) {
                var option = options[i];
                if (option.text.match(regex) !== null) {
                    $(select).append(
                        $('<option>').text(option.text).val(option.value)
                    );
                }
            });
            if (selectSingleMatch === true &&
                $(select).children().length === 1) {
                $(select).children().get(0).selected = true;
                $(select).change();
            } else {
                $(select).change();
            }

        });
    });
};

$.fn.filterAllLike = function (textbox, selectSingleMatch) {
    return this.each(function () {
        var select = this;
        var options = [];
        $(select).find('option').each(function () {
            options.push({value: $(this).val(), text: $(this).text()});
        });
        //console.log('filterLike'+JSON.stringify(options));
        $(select).data('options', options);
        $('#' + textbox).focusout(function () {
            var options = $(select).empty().scrollTop(0).data('options');
            var search = $.trim($(this).val());
            var searchReg = '.*' + search;
//				      for(i=0;i<search.length;i++){
//				    	  searchReg+='.*'+search[i];
//				      }
//				      searchReg+='.*';
            var regex = new RegExp(searchReg, 'gi');

            $.each(options, function (i) {
                var option = options[i];
                if (option.text.match(regex) !== null) {
                    $(select).append(
                        $('<option>').text(option.text).val(option.value)
                    );
                }
            });
            if (selectSingleMatch === true &&
                $(select).children().length === 1) {
                $(select).children().get(0).selected = true;
                $(select).change();
            } else {
                $(select).change();
            }

        });
    });
};

var ComboUtil = {
    showListCtl: function (_lstCtl, _data) {
        //"cboFIELD=ICD10CODE:ICD10NAME,txtField1=IDC10CODE,txtField2=ICD10NAME"
        var map_ar = _lstCtl.split(",");
        for (var i1 = 0; i1 < map_ar.length; i1++) {
            var fld_ar = map_ar[i1].split('=');
            if ($("#" + fld_ar[0]).is("select")) {
                var _flds = fld_ar[1].split(":");
                if (_flds.length == 2) {
                    this.showValueText(fld_ar[0], _data[_flds[0]], _data[_flds[1]]);
                } else {
                    $("#" + fld_ar[0]).val(_data[fld_ar[1]]);
                }
            } else {
                $("#" + fld_ar[0]).val(_data[fld_ar[1]]);
            }
        }
    },
    showValueText: function (cboId, _val, _text) {
        if ($("#" + cboId).is("select")) {
            if ($("#" + cboId + " option[value='" + _val + "']").length == 0) {
                $("#" + cboId).append("<option value='" + _val + "' selected >" + _text + "</option>");
            } else {
                $("#" + cboId + " option[value='" + _val + "']").attr("selected", "selected");
            }
        } else if ($("#" + cboId).is("input:text")) {
            $("#" + cboId).val(_text);
            $("#" + cboId).attr("val", _val);
        }
    },
    getComboText: function (cboId) {
        var _val = $("#" + cboId).val();
        return $("#" + cboId).find("option[value='" + _val + "']").text();
    },
    findByExtra: function (cboId, _extval, _idx) {
        if (!_idx) _idx = 0;
        console.log('ComboUtil.findByExtra ' + cboId + '=' + _extval);
        $("#" + cboId).find("option[extval" + _idx + "='" + _extval + "']").attr("selected", "selected");
        $("#" + cboId).change();
        $("#" + cboId).focus();
        //var val=$("#"+cboId).find("option[extval='"+_extval+"']").val();
        //$("#"+cboId).val(val).change();
        //$("#"+cboId).trigger("change");
    },
    _loadOptions: function (combo_id, value, rowSel, _defOpt) {
        //console.log('ComboUtil.getComboTag_loadOptions '+combo_id+'.value.length='+value.length);
        var xssConfig = {
            "stripIgnoreTag": true,
            "stripIgnoreTagBody": [
                "script"
            ],
            "whiteList": {
                "p": [],
                "strong": [],
                "img": [],
                "svg": [
                    "width", "height"
                ]
            }
            // ,
            // "escapeHtml": function(html) {
            // 	console.log(html);
            // 	return html;
            // }
        };
        var cboList = combo_id.split(",");
        var _html = '';
        if (_defOpt.value || _defOpt.value == '') {
            _html += '<option value="' + _defOpt.value + '">' + filterXSS(_defOpt.text, xssConfig) + '</option>';
        }
        var optGroupLabel = '';
        var optGroup;
        var openGroup = false;
        for (var i = 0; i < value.length; i++) {
            if (_defOpt.group) {
                if (optGroupLabel != value[i][2]) {
                    if (openGroup) {
                        _html += '</optgroup>';
                    }
                    openGroup = true;
                    optGroupLabel = value[i][2];
                    _html += '<optgroup label="' + optionLabel + '">';
                }
            }

            if (value[i].length >= 2) {
                var extval = '';
                if (_defOpt.extval) {
                    //console.log('value[i].length='+value[i].length+' value[i]='+JSON.stringify(value[i]));
                    var cnt = value[i].length - 2;
                    //extval=' extval="'+value[i][2]+'"';
                    extval = '';
                    if (cnt > 0) {
                        for (var i1 = 0; i1 < cnt; i1++) {
                            extval += ' extval' + i1 + '="' + value[i][2 + i1] + '"';
                        }
                    }

                }
                var _selected = '';
                if (value[i][0] == rowSel) {
                    _selected = ' selected="selected" ';
                }
                _html += '<option value="' + value[i][0] + '" ' + extval + ' ' + _selected + '>' + filterXSS(value[i][1], xssConfig) + '</option>';
            }

        }
        if (_defOpt.group && openGroup) {
            _html += '</optgroup>';
        }
        for (var i1 = 0; i1 < cboList.length; i1++) {
            var elem = $('#' + cboList[i1]);
            if (!elem) continue;
            elem.empty();
            elem.append(_html);
            if (elem.attr("filterLike")) {
                var filterBy = elem.attr("filterLike");
                //console.log('filterLike='+elem.attr("id")+' sql='+filterBy);
                $('#' + cboList[i1]).filterLike(filterBy, true);
            }

            if (elem.attr("filterAllLike")) {
                var filterBy = elem.attr("filterAllLike");
                //console.log('filterLike='+elem.attr("id")+' sql='+filterBy);
                $('#' + cboList[i1]).filterAllLike(filterBy, true);
            }

            if (elem.attr("filterLikeFc")) {
                var filterBy = elem.attr("filterLikeFc");
                //console.log('filterLike='+elem.attr("id")+' sql='+filterBy);
                $('#' + cboList[i1]).filterLikeFc(filterBy, true);
            }


        }
    },

    getComboTag: function (comboid, sql, sqlPar, rowSel, defOpt, sqlType, cache, callback) {
        return this.getComboTagEx(comboid, "", sql, sqlPar, rowSel, defOpt, sqlType, cache, callback);
    },
    getComboTagEx: function (comboid, db_name, sql, sqlPar, rowSel, defOpt, sqlType, cache, callback) {	//getComboTag("cboId","select * from t",[],"1",{value:'',text:''},'sql','',cbFunc);
        //console.log('getComboTag='+comboid+' sql='+sql+" sqlPar="+JSON.stringify(sqlPar));
        //console.log('1sql='+sql+' sqlPar='+sqlPar);
        var vlist;
        //defer = $.Deferred();
        var _that = this;
        if (cache && cache != '') {
            //console.log('cache='+cache);
            cacheData = $.localStorage.get(cache);
        } else {
            cacheData = null;
        }
        if (!sql) {
            vlist = [];
            _that._loadOptions(comboid, vlist, rowSel, defOpt);
            if (callback && typeof callback == 'function') {
                callback();
            }
        } else if (cacheData) {
            //console.log('cacheData');
            vlist = cacheData;
            //defer.resolve(vlist);
            if (cache && cache != '') {
                $.localStorage.set(cache, vlist);
            }
            _that._loadOptions(comboid, vlist, rowSel, defOpt);
            if (callback && typeof callback == 'function') {
                callback();
            }
        } else {
            //console.log('NOT cacheData');
            if (callback == false) {
                var data;
                if (sqlType == 'sp') {
                    data = jsonrpc.AjaxJson.dbCALL_SP_R(db_name, sql, sqlPar, []);
                } else {
                    data = jsonrpc.AjaxJson.dbExecuteQuery(db_name, sql, sqlPar);

                }
                //console.log('sql='+sql+' sqlPar='+sqlPar+' DATA='+data);
                //vlist = $.parseJSON(data);
                vlist = {result: data};
                //defer.resolve(vlist);
                if (cache && cache != '') {
                    $.localStorage.set(cache, vlist.result);
                }
                _that._loadOptions(comboid, vlist.result, rowSel, defOpt);
            } else {
                if (sqlType == 'sp') {
                    jsonrpc.AjaxJson.dbCALL_SP_R(db_name, sql, sqlPar, [], function (data) {
                        //console.log('sql='+sql+' sqlPar='+sqlPar+' DATA='+data);
                        vlist = $.parseJSON(data);
                        var _result = $.parseJSON(vlist.result);
                        //defer.resolve(vlist);
                        if (cache && cache != '') {
                            $.localStorage.set(cache, _result);
                        }
                        _that._loadOptions(comboid, _result, rowSel, defOpt);
                        if (callback && typeof callback == 'function') {
                            callback();
                        }
                    });
                } else {
                    jsonrpc.AjaxJson.dbExecuteQuery(db_name, sql, sqlPar, function (data) {
                        //console.log('data='+data);
                        vlist = $.parseJSON(data);
                        //defer.resolve(vlist);
                        if (cache && cache != '') {
                            $.localStorage.set(cache, vlist);
                        }
                        _that._loadOptions(comboid, vlist, rowSel, defOpt);
                        if (callback && typeof callback == 'function') {
                            callback();
                        }
                    });
                }
            }
        }
        //defer.done(function(vlist) {

        //});
    },
    getComboTag2: function (comboid, sql, sqlPar, rowSel, defOpt, sqlType, cache, callback) {
        return this.getComboTagEx2(comboid, "", sql, sqlPar, rowSel, defOpt, sqlType, cache, callback);
    },
    getComboTagEx2: function (comboid, db_name, sql, sqlPar, rowSel, defOpt, sqlType, cache, callback) {	//getComboTag("cboId","select * from t",[],"1",{value:'',text:''},'sql','',cbFunc);
        //console.log('getComboTag='+comboid+' sql='+sql+" sqlPar="+JSON.stringify(sqlPar));
        //console.log('1sql='+sql+' sqlPar='+sqlPar);
        var vlist;
        //defer = $.Deferred();
        var _that = this;
        if (cache && cache != '') {
            //console.log('cache='+cache);
            cacheData = $.localStorage.get(cache);
        } else {
            cacheData = null;
        }
        if (!sql) {
            vlist = [];
            _that._loadOptions(comboid, vlist, rowSel, defOpt);
            if (callback && typeof callback == 'function') {
                callback(vlist);
            }
        } else if (cacheData) {
            //console.log('cacheData');
            vlist = cacheData;
            //defer.resolve(vlist);
            if (cache && cache != '') {
                $.localStorage.set(cache, vlist);
            }
            _that._loadOptions(comboid, vlist, rowSel, defOpt);
            if (callback && typeof callback == 'function') {
                callback(vlist);
            }
        } else {
            //console.log('NOT cacheData');
            if (callback == false) {
                var data;
                if (sqlType == 'sp') {
                    data = jsonrpc.AjaxJson.dbCALL_SP_R(db_name, sql, sqlPar, []);
                } else {
                    data = jsonrpc.AjaxJson.dbExecuteQuery(db_name, sql, sqlPar);

                }
                //console.log('sql='+sql+' sqlPar='+sqlPar+' DATA='+data);
                //vlist = $.parseJSON(data);
                vlist = {result: data};
                //defer.resolve(vlist);
                if (cache && cache != '') {
                    $.localStorage.set(cache, vlist.result);
                }
                _that._loadOptions(comboid, vlist.result, rowSel, defOpt);
            } else {
                if (sqlType == 'sp') {
                    jsonrpc.AjaxJson.dbCALL_SP_R(db_name, sql, sqlPar, [], function (data) {
                        //console.log('sql='+sql+' sqlPar='+sqlPar+' DATA='+data);
                        vlist = $.parseJSON(data);
                        var _result = $.parseJSON(vlist.result);
                        //defer.resolve(vlist);
                        if (cache && cache != '') {
                            $.localStorage.set(cache, _result);
                        }
                        _that._loadOptions(comboid, _result, rowSel, defOpt);
                        if (callback && typeof callback == 'function') {
                            callback(vlist);
                        }
                    });
                } else {
                    jsonrpc.AjaxJson.dbExecuteQuery(db_name, sql, sqlPar, function (data) {
                        //console.log('data='+data);
                        vlist = $.parseJSON(data);
                        //defer.resolve(vlist);
                        if (cache && cache != '') {
                            $.localStorage.set(cache, vlist);
                        }
                        _that._loadOptions(comboid, vlist, rowSel, defOpt);
                        if (callback && typeof callback == 'function') {
                            callback(vlist);
                        }
                    });
                }
            }
        }
        //defer.done(function(vlist) {

        //});
    },
    initComboGrid: function (cboId, _sql, _sql_par, _wd, _col, _selfnc, rowsArray, rows, gridOption) {
        //comboGrid_init
        var _self = this;
        var ATTRIB_LABEL = 0;
        var ATTRIB_NAME = 1;
        var ATTRIB_WIDTH = 2;
        var ATTRIB_FORMAT = 3;
        var ATTRIB_HIDDEN = 4;
        var ATTRIB_ALIGN = 5;

        var cbo_url = this.loadGridBySql(_sql, _sql_par);
        var arHdr = _col.split(";");
        var _model = [];

        for (var j = 0; j < arHdr.length; j++) {
            var colAttr = arHdr[j].split(",");
            var colInfo = new Object();
            colInfo.label = colAttr[ATTRIB_LABEL];
            colInfo.columnName = colAttr[ATTRIB_NAME];
            colInfo.width = colAttr[ATTRIB_WIDTH];
            if (colAttr[ATTRIB_HIDDEN] == "t") {
                colInfo.hidden = true;
            }

            if (colAttr[ATTRIB_ALIGN] == "l") {
                colInfo.align = "left";
            } else if (colAttr[ATTRIB_ALIGN] == "r") {
                colInfo.align = "right";
            } else if (colAttr[ATTRIB_ALIGN] == "c") {
                colInfo.align = "center";
            }
            /*
							if (colAttr[ATTRIB_FORMAT] && colAttr[ATTRIB_FORMAT] != "0") {
								colInfo.formatter = colAttr[ATTRIB_FORMAT];
							}
							*/
            _model.push(colInfo);
        }
        //console.log("model="+JSON.stringify(_model));
        //"txtField1=IDC10CODE,txtField2=ICD10NAME,"
        //cboFIELD=ICD10CODE:ICD10NAME
        var cbFunc = null;
        if (typeof _selfnc == "function") {
            cbFunc = _selfnc;
        } else {
            cbFunc = function (event, ui) {
                _self.showListCtl(_selfnc, ui.item);
                return false;
            };
        }

        var cbo_opt = {
            url: cbo_url,
            debug: true,
            width: _wd,
            colModel: _model,
            select: cbFunc
        };
        if (rowsArray != undefined) {
            cbo_opt.rowsArray = rowsArray;
        }
        if (rows != undefined) {
            cbo_opt.rows = rows;
        }

        if (gridOption != undefined && typeof gridOption == "object") {
            cbo_opt = Object.assign(cbo_opt, gridOption);
        }

        $("#" + cboId).attr("exttype", "cbg");
        console.log("#" + cboId + ".exttype=" + $("#" + cboId).attr("exttype"));
        $("#" + cboId).combogrid(cbo_opt);
        $(".combogrid").click(function (e) {
            e.stopPropagation()
        });
    },

    loadGridBySql: function (_gridSQL, ___sqlParam) {
//comboGrid_loadGridBySql
        console.log('_sqlParam=' + JSON.stringify(___sqlParam));
        if (___sqlParam == undefined) ___sqlParam = [];
        var _data = {
            "func": "ajaxExecuteQueryPaging",
            "uuid": jsonrpc.AjaxJson.uuid,
            "params": [_gridSQL],
            "options": ___sqlParam
        };
        var _postdata = JSON.stringify(_data);
        //console.log('_postdata='+_postdata);
        _postdata = encodeURIComponent(_postdata);
        //console.log('_postdata='+_postdata);
        return RestInfo.base_url + '?func=doComboGrid&postData=' + _postdata;
    }


};
ComboUtil.init = ComboUtil.initComboGrid;
//--------- GridUtil ------------------------------------------------------------------------------------
if (!$.fn.fmatter) $.fn.fmatter = {};
$.fn.fmatter.customAction = function (act) {
    var $tr = $(this).closest("tr.jqgrow"),
        rid = $tr.attr("id"),
        $id = $(this).closest("table.ui-jqgrid-btable").attr('id').replace(/_frozen([^_]*)$/, '$1'),
        $grid = $("#" + $id),
        $t = $grid[0],
        p = $t.p,
        cm = p.colModel[$.jgrid.getCellIndex(this)];
    $grid.trigger("CustomAction", [act, rid]);
};

$.fn.fmatter.EditRowAction = function (act) {
    var $tr = $(this).closest("tr.jqgrow"),
        rid = $tr.attr("id"),
        $id = $(this).closest("table.ui-jqgrid-btable").attr('id').replace(/_frozen([^_]*)$/, '$1'),
        $grid = $("#" + $id),
        $t = $grid[0],
        p = $t.p,
        cm = p.colModel[$.jgrid.getCellIndex(this)],
        $actionsDiv = cm.frozen ? $("tr#" + rid + " td:eq(" + $.jgrid.getCellIndex(this) + ") > div", $grid) : $(this).parent(),
        op = {
            extraparam: {}
        },
        saverow = function (rowid, res) {
            if ($.isFunction(op.afterSave)) {
                op.afterSave.call($t, rowid, res);
            }
            $actionsDiv.find("div.ui-inline-edit,div.ui-inline-del").show();
            $actionsDiv.find("div.ui-inline-save,div.ui-inline-cancel").hide();
        },
        restorerow = function (rowid) {
            if ($.isFunction(op.afterRestore)) {
                op.afterRestore.call($t, rowid);
            }
            $actionsDiv.find("div.ui-inline-edit,div.ui-inline-del").show();
            $actionsDiv.find("div.ui-inline-save,div.ui-inline-cancel").hide();
        };


    if (cm.formatoptions !== undefined) {
        op = $.extend(op, cm.formatoptions);
    }
    if (p.editOptions !== undefined) {
        op.editOptions = p.editOptions;
    }
    if (p.delOptions !== undefined) {
        op.delOptions = p.delOptions;
    }
    if ($tr.hasClass("jqgrid-new-row")) {
        op.extraparam[p.prmNames.oper] = p.prmNames.addoper;
    }
    var actop = {
        keys: op.keys,
        oneditfunc: op.onEdit,
        successfunc: op.onSuccess,
        url: op.url,
        extraparam: op.extraparam,
        aftersavefunc: saverow,
        errorfunc: op.onError,
        afterrestorefunc: restorerow,
        restoreAfterError: op.restoreAfterError,
        mtype: op.mtype
    };
    switch (act) {
        case 'edit':
            console.log('editRow');
            $grid.trigger("EditRowAction", [act, rid]);
            $grid.jqGrid('editRow', rid, actop);
            $actionsDiv.find("div.ui-inline-edit,div.ui-inline-del").hide();
            $actionsDiv.find("div.ui-inline-save,div.ui-inline-cancel").show();
            //$grid.triggerHandler("jqGridAfterGridComplete");
            break;
        case 'save':
            if ($grid.jqGrid('saveRow', rid, actop)) {
                $grid.trigger("EditRowAction", [act, rid]);
                //$grid.jqGrid('restoreRow', rid, restorerow);
                $actionsDiv.find("div.ui-inline-edit,div.ui-inline-del").show();
                $actionsDiv.find("div.ui-inline-save,div.ui-inline-cancel").hide();
                $grid.triggerHandler("CustomActionCompleted", [act, rid]);
                //$grid.triggerHandler("jqGridAfterGridComplete");
            }
            break;
        case 'cancel' :
            $grid.jqGrid('restoreRow', rid, restorerow);
            $grid.trigger("EditRowAction", [act, rid]);
            $actionsDiv.find("div.ui-inline-edit,div.ui-inline-del").show();
            $actionsDiv.find("div.ui-inline-save,div.ui-inline-cancel").hide();
            //$grid.triggerHandler("jqGridAfterGridComplete");
            break;
        case 'del':
            //$grid.jqGrid('delGridRow', rid, op.delOptions);
            var rt = $grid.triggerHandler("CustomAction", [act, rid]);
            console.log('CustomAction rt=' + rt);
            if (rt) {
                $grid.jqGrid('delRowData', rid);
                $grid.triggerHandler("CustomActionCompleted", [act, rid]);
            }
            break;
        case 'formedit':
            $grid.jqGrid('setSelection', rid);
            $grid.jqGrid('editGridRow', rid, op.editOptions);
            $grid.trigger("EditRowAction", [act, rid]);
            break;
    }
};
$.fn.fmatter.updownButton = function (cellval, opts) {
    var rowid = opts.rowId, str = "", ocl;

    if (rowid === undefined || $.fmatter.isEmpty(rowid)) {
        return "";
    }

    ocl = "id='jUpButton_" + rowid + "' onclick=jQuery.fn.fmatter.customAction.call(this,'up'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover') ";
    str += "<div title='Lên' style='float:left;cursor:pointer;' class='ui-pg-div ui-inline-edit' " + ocl + "><span class='ui-icon ui-icon-circle-arrow-n'></span></div>";
    ocl = "id='jDownButton_" + rowid + "' onclick=jQuery.fn.fmatter.customAction.call(this,'down'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";
    str += "<div title='Xuống' style='float:left;margin-left:5px;' class='ui-pg-div ui-inline-del' " + ocl + "><span class='ui-icon ui-icon-circle-arrow-s'></span></div>";
    return "<div style='margin-left:8px;'>" + str + "</div>";
};

$.fn.fmatter.customButton = function (cellval, opts) {
    var op = {keys: false, editbutton: false, delbutton: false, editformbutton: false},
        rowid = opts.rowId, str = "", ocl;
    if (opts.colModel.formatoptions !== undefined) {
        op = $.extend(op, opts.colModel.formatoptions);
    }
    if (rowid === undefined || $.fmatter.isEmpty(rowid)) {
        return "";
    }
    if (op.editformbutton) {
        ocl = "id='jEditButton_" + rowid + "' onclick=jQuery.fn.fmatter.EditRowAction.call(this,'formedit'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";
        str += "<div title='" + $.jgrid.nav.edittitle + "' style='float:left;cursor:pointer;' class='ui-pg-div ui-inline-edit' " + ocl + "><span class='ui-icon ui-icon-pencil'></span></div>";
    } else if (op.editbutton) {
        console.log('customButton op.editbutton=' + op.editbutton);
        ocl = "id='jEditButton_" + rowid + "' onclick=jQuery.fn.fmatter.EditRowAction.call(this,'edit'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover') ";
        str += "<div title='" + $.jgrid.nav.edittitle + "' style='float:left;cursor:pointer;' class='ui-pg-div ui-inline-edit' " + ocl + "><span class='ui-icon ui-icon-pencil'></span></div>";
    }
    if (op.delbutton) {
        console.log('customButton op.delbutton=' + op.delbutton);
        ocl = "id='jDeleteButton_" + rowid + "' onclick=jQuery.fn.fmatter.EditRowAction.call(this,'del'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";
        str += "<div title='" + $.jgrid.nav.deltitle + "' style='float:left;margin-left:5px;' class='ui-pg-div ui-inline-del' " + ocl + "><span class='ui-icon ui-icon-trash'></span></div>";
    }
    ocl = "id='jSaveButton_" + rowid + "' onclick=jQuery.fn.fmatter.EditRowAction.call(this,'save'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";
    str += "<div title='" + $.jgrid.edit.bSubmit + "' style='float:left;display:none' class='ui-pg-div ui-inline-save' " + ocl + "><span class='ui-icon ui-icon-disk'></span></div>";
    ocl = "id='jCancelButton_" + rowid + "' onclick=jQuery.fn.fmatter.EditRowAction.call(this,'cancel'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";
    str += "<div title='" + $.jgrid.edit.bCancel + "' style='float:left;display:none;margin-left:5px;' class='ui-pg-div ui-inline-cancel' " + ocl + "><span class='ui-icon ui-icon-cancel'></span></div>";
    return "<div style='margin-left:8px;'>" + str + "</div>";
};
//--------- GridUtil --------------------------------------------------------------------------------------
var GridUtil = {
    fm: {
        iconMap: {
            '1': 'cancel',
            '2': 'trash'
        },
        iconFormat: function (cellvalue, options, rowObject) {
            //console.log('iconFormat=');
            if (cellvalue) {
                return '<i class="ui-icon ui-icon-' + GridUtil.fm.iconMap[cellvalue] + '"></i>';
            } else {
                return '<i class="ui-icon ui-icon-circle-check"></i>';
            }
        },
        imageMap: {
            '1': 'icon_share.png',
            '2': 'icon_share.png'
        },
        imageFormat: function (cellvalue, options, rowObject) {
            //console.log('imageFormat=');
            if (cellvalue) {
                return '<img src="../common/img/' + GridUtil.fm.imageMap[cellvalue] + '"/>';
            } else {
                return '<img src="../common/img/icon_share.png" style="width:16px;height:16px"/>';
            }
        },
        formatStrEncode: function (cellvalue, options, action) {
            if (cellvalue) {
                return $.jgrid.htmlEncode(cellvalue);
            }
            ;
            return "";
        },
        numberFormatVN: function (cellvalue, options, rowObject) {
            if (cellvalue) {
                let sValue = Number.parseFloat(cellvalue);
                if ($.fmatter.isNumber(sValue)) {
                    let sData = $.fmatter.util.NumberFormat(cellvalue, {decimalPlaces: 16});
                    sData = sData.replace(/0+$/, "").replace(/\.$/, "");
                    return sData;
                }
                return cellvalue;
            }
            return "";
        }
    },
    groupHeader: function (gridId, colSpan, hdrInfo) {
        //jqGird_XXXX()
        //name,3,caption
        var ATTRIB_NAME = 0;
        var ATTRIB_NAME = 1;
        var ATTRIB_WIDTH = 2;
        var arHdr = hdrInfo.split(";");
        var _ghdr = [];
        for (var j = 0; j < arHdr.length; j++) {
            var colAttr = arHdr[j].split(",");
            var colInfo = new Object();
            colInfo.startColumnName = colAttr[0];
            colInfo.numberOfColumns = colAttr[1];
            colInfo.titleText = colAttr[2];
            _ghdr.push(colInfo);
        }

        $("#" + gridId).jqGrid('setGroupHeaders', {
            useColSpanStyle: colSpan,
            groupHeaders: _ghdr
        });
    },
    buildOption: function (gridId, _width, _height, _caption, _multiselect, hdrInfo, _shrinkToFit, _numFormat, colInfsEx) {
        console.log('hdrInfo=' + hdrInfo);
//http://stackoverflow.com/questions/5385239/jqgrid-how-to-lose-focus-when-i-click-outside-the-grid-or-anywhere-else-for-th/21906387#21906387
        var ATTRIB_LABEL = 0;
        var ATTRIB_NAME = 1;
        var ATTRIB_WIDTH = 2;
        var ATTRIB_FORMAT = 3;
        var ATTRIB_HIDDEN = 4;
        var ATTRIB_ALIGN = 5;
        if (!_shrinkToFit) shrinkToFit = false;
        //label,name,width,format,hidden,align
        //hdrInfo="ICD,icd,100,0,f,l;Mô tả bệnh lý,mo_ta_benh_ly,400,0,f,l";
        //hdrInfo="ICD,Mô tả bệnh lý;100,400;number,0;t,f,l,l";
        var arHdr = hdrInfo.split(";");
        var _model = [];
        var _cellEdit = 0;
        var regExp = /^([chk|sel]+)(\{[^}]*\})$/;
        for (var j = 0; j < arHdr.length; j++) {
            console.log('arHdr[j]=' + arHdr[j]);
            var colAttr = arHdr[j].split(",");
            var colInfo = new Object();
            colInfo.label = colAttr[ATTRIB_LABEL];
            colInfo.name = colAttr[ATTRIB_NAME];
            colInfo.index = colAttr[ATTRIB_NAME];
            colInfo.width = colAttr[ATTRIB_WIDTH];
            let isEscapse = colAttr[colAttr.length - 1];
            if (colAttr[ATTRIB_HIDDEN] == "ns") {
                colInfo.search = false;
            } else if (colAttr[ATTRIB_HIDDEN] == "e") {
                colInfo.editable = true;
                colInfo.edittype = 'text';
                _cellEdit++;
            } else if (regExp.test(colAttr[ATTRIB_HIDDEN])) {
                //console.log('ATTRIB_HIDDEN '+colAttr[ATTRIB_HIDDEN]);
                //REPORT_ID,REPORT_ID,0,0,t,l;INDICATOR_ID,INDICATOR_ID,0,0,t,l;FORM_ID,FORM_ID,0,0,t,l;EDIT_MODE,EDIT_MODE,0,0,t,l;IS_GROUP,IS_GROUP,0,0,t,l;PARENT_NAME,PARENT_NAME,150,0,t,l;Mã chỉ tiêu,INDICATOR_CODE,50,0,f,l;Tên chỉ tiêu,INDICATOR_NAME,150,0,f,l;Đơn vị tính,INDICATOR_UNIT,50,0,f,l;Tổng số,VAL_1,50,0,sel{"value":"1:FedEx|2:InTime|3:TNT|4:ARAMEX"},l;Nội trú,VAL_2,50,0,e,l
                colInfo.editable = true;
                var matches = colAttr[ATTRIB_HIDDEN].match(regExp);
                console.log('matches[2] ' + matches[2].split("\|").join(";"));
                //chk{"value":"Có:Không"}
                //sel{value:"1:FedEx;2:InTime;3:TNT;4:ARAMEX"}
                if (matches[1] == 'chk') {
                    colInfo.edittype = 'checkbox';
                    colInfo.formatter = 'checkbox';
                } else {
                    colInfo.formatter = 'select';
                    colInfo.edittype = 'select';
                }

                colInfo.editoptions = JSON.parse(matches[2].split("\|").join(";"));
                _cellEdit = true;
                //editable: true,edittype:"select",editoptions:{value:"FE:FedEx;IN:InTime;TN:TNT;AR:ARAMEX"}},
            } else if (colAttr[ATTRIB_HIDDEN] == "t") {
                colInfo.hidden = true;
            }
            var _align = 'l';
            var _sort = 'n';
            if (colAttr[ATTRIB_ALIGN]) {
                var align_sort = colAttr[ATTRIB_ALIGN].split('!');//lrc|ifsdn
                _align = align_sort[0];
                _sort = 'n';
                if (align_sort.length > 1) {
                    _sort = align_sort[1];
                }
            }
            console.log('_align=' + _align + ' _sort=' + _sort);
            if (_align == "l") {
                colInfo.align = "left";
            } else if (_align == "r") {
                colInfo.align = "right";
            } else if (_align == "c") {
                colInfo.align = "center";
            }

            if (_sort == "n") {
                colInfo.sortable = false;
                colInfo.sorttype = "";
            } else if (_sort == "i") {
                colInfo.sortable = true;
                colInfo.sorttype = "int";
            } else if (_sort == "f") {
                colInfo.sortable = true;
                colInfo.sorttype = "float";
            } else if (_sort == "d") {
                colInfo.sortable = true;
                colInfo.sorttype = "date";
            } else if (_sort == "s") {
                colInfo.sortable = true;
                colInfo.sorttype = "text";
            }

            var ruleRegex = /^([aed|]+)$/;
            //if (colAttr[ATTRIB_FORMAT].indexOf('a')>0||colAttr[ATTRIB_FORMAT].indexOf('e')>0||colAttr[ATTRIB_FORMAT].indexOf('d')>0) {
            if (ruleRegex.test(colAttr[ATTRIB_FORMAT])) {
                console.log('customButton ' + colAttr[ATTRIB_FORMAT]);
                //colInfo.formatter = 'actions';
                colInfo.formatter = 'customButton';
                colInfo.search = false;
                colInfo.formatoptions = {keys: true, editbutton: false, delbutton: false};
                if (colAttr[ATTRIB_FORMAT].indexOf('a') >= 0) {
                    colInfo.formatoptions.addbutton = true;
                }
                if (colAttr[ATTRIB_FORMAT].indexOf('e') >= 0) {
                    colInfo.formatoptions.editbutton = true;
                }
                if (colAttr[ATTRIB_FORMAT].indexOf('d') >= 0) {
                    colInfo.formatoptions.delbutton = true;
                }

            } else if (colAttr[ATTRIB_FORMAT] == "ico") {
                console.log('colAttr[ATTRIB_FORMAT]=' + colAttr[ATTRIB_FORMAT]);
                colInfo.formatter = GridUtil.fm.iconFormat;
                colInfo.search = false;
            } else if (colAttr[ATTRIB_FORMAT] == "img") {
                console.log('colAttr[ATTRIB_FORMAT]=' + colAttr[ATTRIB_FORMAT]);
                colInfo.formatter = GridUtil.fm.imageFormat;
                colInfo.search = false;
            } else if (colAttr[ATTRIB_FORMAT].startsWith("udb")) {
                //button=btn[up:down]
                colInfo.search = false;

                colInfo.formatter = 'updownButton';
            } else if (colAttr[ATTRIB_FORMAT] == "b") {
                //button
                colInfo.search = false;
                colInfo.formatter = function (cellvalue, options, rowObject) {
                    var edit = "<input type='button' value='Edit' onclick=\"$('#'" + gridId + ").editRow('" + options.rowId + "');\"  />";
                    return edit;
                };
            } else if (colAttr[ATTRIB_FORMAT] && colAttr[ATTRIB_FORMAT] != "0") {
                var _fm = colAttr[ATTRIB_FORMAT].split('!');
                colInfo.formatter = _fm[0];
                if (_fm[1] != null && _fm[1] != undefined && _fm[1] != '') {
                    let decimalss = 0;
                    decimalss = parseInt(_fm[1]);
                    if (_fm[0] == 'number') {
                        colInfo.formatoptions = {thousandsSeparator: "", decimalPlaces: decimalss};
                    }
                    if (_fm[0] == 'decimal') {
                        colInfo.formatter = 'number';
                        if ("VN" == _numFormat) {
                            colInfo.formatoptions = {
                                decimalSeparator: ".",
                                thousandsSeparator: ",",
                                decimalPlaces: decimalss
                            };
                        } else {
                            colInfo.formatoptions = {
                                decimalSeparator: ",",
                                thousandsSeparator: ".",
                                decimalPlaces: decimalss
                            };
                        }

                        if (decimalss < 0) {
                            colInfo.formatter = GridUtil.fm.numberFormatVN;
                        }
                    }
                } else {
                    if (_fm[0] == 'number') {
                        colInfo.formatoptions = {thousandsSeparator: ""};
                    }
                    if (_fm[0] == 'decimal') {
                        colInfo.formatter = 'number';
                        if ("VN" == _numFormat) {
                            colInfo.formatoptions = {decimalSeparator: ".", thousandsSeparator: ","};
                        } else {
                            colInfo.formatoptions = {decimalSeparator: ",", thousandsSeparator: "."};
                        }
                    }
                }

            } else if (colAttr[ATTRIB_HIDDEN] != 'ns' && isEscapse != 'ES') {
                colInfo.formatter = GridUtil.fm.formatStrEncode;
            }

            if (colInfsEx && colInfsEx[colAttr[ATTRIB_NAME]]) {
                Object.assign(colInfo, colInfsEx[colAttr[ATTRIB_NAME]]);
            }

            _model.push(colInfo);

        }
        console.log("_height=" + _height + " width=" + _width);
        var gr_opt = {
            //url: '../RestService?postData='+_postdata,
            formatter: jqGrid_formater,
            datatype: "local",
            height: _height,
            width: _width,
            //width: null,
            autowidth: true,
            multiselect: _multiselect,
            colModel: _model,
            rowNum: 20,
            rowList: [20, 30, 50, 100, 200],
            ignoreCase: true,
            pager: '#pager_' + gridId,
            viewrecords: true,
            rownumbers: true,
            caption: _caption,
            loadonce: false,
            shrinkToFit: _shrinkToFit,
            //cellEdit:_cellEdit,
            cellsubmit: 'clientArray',
            editurl: 'clientArray',
            jsonReader: {
                root: "rows",
                page: "page",
                total: "total",
                records: "records"
            },
            loadComplete: function () {
                //var gridParentWidth = $('#gbox_' + gridId).parent().width();

                //var _parentNode = document.getElementById('gbox_' + gridId).parentNode;
                //var gridParentWidth=_parentNode.offsetWidth;
                $(this).find(">tbody>tr.jqgrow:odd>td").addClass("jqgrow_odd");
                $(this).find(">tbody>tr.jqgrow:even>td").addClass("jqgrow_even");
            },
            onSelectRow: function (rowid) {
                //var _self=this;
                //var grid=$(this);
                //GridUtil.markRow(gridId,rowid);
                //var row = $(this).getLocalRow(rowid);
                // do something with row
            },

            ondblClickRow: function (rowId, iRow, iCol, e) {
                //console.log('ondblClickRow='+rowid);
                //if(_cellEdit)	$(this).jqGrid('editGridRow', rowid);
                //this.options.editRID=rowid;
                var grid = $(this);
                //GridUtil.unmarkRow(gridId,rowId);
                if (_cellEdit > 0) {
                    //$(this).jqGrid('editRow', rowid);
                    var lastSel = grid.attr("lastSel");
                    if (lastSel) {
                        if (rowId && rowId !== lastSel) { //lastSel is a global variable
                            grid.saveRow(lastSel);
                            //lastSel=rowId;
                        }
                    }
                    grid.attr("lastSel", rowId);
                    grid.jqGrid('resetSelection');
                    grid.jqGrid('editRow', rowId, true, null, null, 'clientArray');

                    //grid.jqGrid('editRow', rowid, true);
                    if (_cellEdit == 1) {
                        $("input, select", e.target).focus().blur(function () {
                            grid.saveRow(rowId);
                        });

                    }

                    return;
                }
            }
        };

        return gr_opt;
    },
    setEditRow: function (gridId, rowid) {
        $("#" + gridId).jqGrid("editRow", rowid, {
            keys: true
            , aftersavefunc: function (rowid, response) {
                console.log('after save');
                var grid = $("#" + gridId);
                var rids = grid.jqGrid('getDataIDs');
                var iRow = grid.getInd(rowid);
                var nextRowId = rids[iRow - 1];
                for (var i1 = 0; i1 < rids.length; i1++) {
                    if (rowid == rids[i1]) {
                        if (i1 < rids.length - 1)
                            nextRowId = rids[i1 + 1];
                        else
                            nextRowId = null;
                    }
                }
                console.log('GIATRI_KETQUA iRow=' + iRow);

                if (nextRowId)
                    GridUtil.setEditRow(gridId, nextRowId);
                else
                    console.log('last row');
            }
            , errorfunc: function (rowid, response) {
                console.log('...we have a problem');
            }
        });
    },
    addExcelButton: function (gridId, _btnName, _isClient) {
        var _self = this;
        $("#" + gridId).navButtonAdd('#pager_' + gridId, {
            caption: _btnName,
            buttonicon: "ui-icon-contact",
            onClickButton: function () {	     //console.log("jqGrid_export="+gridId);
                _self.exportExcel(gridId, _isClient);
            },
            position: "last"
        });
    },
    addExcelFullButton: function (gridId, _btnName, _isClient, _option = {}) {
        var _self = this;
        $("#" + gridId).navButtonAdd('#pager_' + gridId, {
            caption: _btnName,
            buttonicon: "ui-icon-contact",
            onClickButton: function () {	     //console.log("jqGrid_export="+gridId);
                _self.exportExcelFull(gridId, _isClient, _option);
            },
            position: "last"
        });
    },
    init: function (gridId, _width, _height, _caption, _multiselect, hdrInfo, _shrinkToFit, grd_opt, _numFormat, filterToolbar) {
        //GridUtil.init("grdDanhSachPK","450px","200px","",false,"PHONGID,PHONGID,0,0,t,l;DANH SÁCH PHÒNG KHÁM,ORG_NAME,400,0,f,l", false, { rowList: [200, 300, 500, 1000, 2000]});

        //_gridHeader= "Mã thuốc,MA_THUOC,20,0,f,l;Tên thuốc,TEN_THUOC,50,0,f,l;Hoạt chất,HOAT_CHAT,20,0,f,l;ĐV tính,DONVI_TINH,20,0,f,l;Đường dùng,DUONG_DUNG,20,0,f,l;Giá,GIA_THUOC,20,0,f,l;Mã BYT,MA_BYT,20,0,f,l";
        //jqGrid_init("gridThuoc","600","100%","Danh mục thuốc",true,_gridHeader);
        //_gridHeader =
        //ten_cot,
        //ten_db_column,
        //do_rong_cot,
        //format: mac dinh = 0, chua dung
        //hidden column: t=hidden, f=show
        //align: l=left,r=right,c=center
        var _self = this;
        //console.log("gridId="+gridId+" 1_height="+_height);
        var arHdr1 = hdrInfo.split("|");
        var hdrInfo1;
        var hdrGroup1;
        if (arHdr1.length > 1) {
            hdrInfo1 = arHdr1[0];
            hdrGroup1 = arHdr1[1];
        } else {
            hdrInfo1 = arHdr1[0];
        }
        var _gr_opt = _self.buildOption(gridId, _width, _height, _caption, _multiselect, hdrInfo1, _shrinkToFit, _numFormat);
        //console.log("2_height="+_height);
        //console.log('_gr_opt='+JSON.stringify(_gr_opt));
        //$("#grdDanhSachPK").jqGrid('setGridParam', { rowList: [200, 300, 500, 1000, 2000]} );
        console.log('xxxgrd_opt=' + gridId + ":" + JSON.stringify(grd_opt));
        if (grd_opt) {

            _gr_opt = $.extend({}, _gr_opt, grd_opt);
        }

        console.log('111_gr_opt=' + JSON.stringify(_gr_opt));
        $("#" + gridId).jqGrid(_gr_opt);
        $("#" + gridId).jqGrid('navGrid', '#pager_' + gridId, {search: false, edit: false, add: false, del: false});
        //_self.addExcelButton(gridId,"Excel",true);


        if (_height) {
            if (typeof _height != "string") {
                _height = _height + "px !important";
            } else if (!_height.endsWith("px")) {
                _height = _height + "px !important";
            } else {
                _height = _height + " !important";
            }
        }

        $('#gview_' + gridId).find('.ui-jqgrid-bdiv').attr("style", "height: " + _height);
        /*
			 var _parent=$('#gbox_' + gridId).parent();
	       	 while(_parent && _parent.width()==0) {
	       		 _parent=_parent.parent();
	       	 }
			 $('#' + gridId).jqGrid('setGridWidth',_parent.width());
			 */
        if (typeof _width == "string" && _width.endsWith("%")) {
            var wdP = _width.substring(0, _width.length - 1);
            _self.setWidthPercent(gridId, parseInt(wdP));
        } else {
            console.log(gridId + ' setGridWidth=' + _width);
            $("#" + gridId).jqGrid('setGridWidth', _width);
        }
        //$('.ui-jqgrid-bdiv').height(_height+" !important");
        //$('.ui-jqgrid-bdiv').style('height', _height, 'important');

        //console.log('height='+_height+" !important");
        if (typeof filterToolbar === "undefined") {
            $("#" + gridId).jqGrid('filterToolbar', {
                ignoreCase: true,
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn"
            });
        } else {
            $("#" + gridId).jqGrid('filterToolbar', {
                ignoreCase: true,
                stringResult: true,
                searchOnEnter: true,
                defaultSearch: "cn"
            });
        }
        //$('#list').jqGrid('setGridParam', { onSelectRow: function(id){ alert(id); } } );

        // $('#gview_'+gridId).find('.ui-jqgrid-bdiv').attr('style', 'height: '+_height+' !important;width: '+_width+' !important');
        // $('#gbox_'+gridId).attr('style', 'height: '+_height+' !important;width: '+_width+' !important');
        // $('#gview_'+gridId).attr('style', 'height: '+_height+' !important;width: '+_width+' !important');

        // $('#gview_'+gridId).find('.ui-jqgrid-hbox').attr('style', 'width: '+_width+' !important');
        if (hdrGroup1) {
            var _colModel = $("#" + gridId).jqGrid("getGridParam", "colModel");
            var grp_ar1 = hdrGroup1.split('#');
            var _lc = grp_ar1.length;
            for (var i1 = 0; i1 < grp_ar1.length; i1++) {
                var grp_ar2 = grp_ar1[i1].split(';');
                var _groupHeaders = [];
                var clast = 0;
                for (var i2 = 0; i2 < grp_ar2.length; i2++) {
                    var grp_ar3 = grp_ar2[i2].split(',');
                    //id,1,.;
                    if (_lc > 1) {
                        for (var c = clast; c < _colModel.length && _colModel[c].name != grp_ar3[0]; c++) {
                            _groupHeaders.push({startColumnName: _colModel[c].name, numberOfColumns: 1, titleText: ''});
                            clast++;
                        }
                    }
                    _groupHeaders.push({
                        startColumnName: grp_ar3[0],
                        numberOfColumns: grp_ar3[1],
                        titleText: '<center>' + grp_ar3[2] + '</center>'
                    });
                    clast = clast + parseInt(grp_ar3[1]) - 1;

                }
                $("#" + gridId).jqGrid('setGroupHeaders', {useColSpanStyle: true, groupHeaders: _groupHeaders});
            }
        }
        if (window.uiVersion && window.uiVersion == '2') {
            let parentWidth = $('#' + gridId).parents(".ui-jqgrid").parent().width();
            $('#' + gridId).jqGrid('setGridWidth', parentWidth);
        }
        return $("#" + gridId);
    },
    setWidthPercent: function (gridId, _percent) {
        var _parent = $('#gbox_' + gridId).parent();
        while (_parent && _parent.width() == 0) {
            _parent = _parent.parent();
        }
        var gridParentWidth = _parent.width();
        if (gridParentWidth > $('.container').width()) {
            gridParentWidth = $('.container').width();
        }
        console.log('setWidthP.gridParentWidth=' + _parent.width() + '=' + gridParentWidth);
        console.log('setWidthP.container=' + $('.container').width());
        $('#' + gridId).jqGrid('setGridWidth', gridParentWidth * _percent / 100);
    },

    initGroup: function (gridId, _width, _height, _caption, _multiselect, _group, hdrInfo, _shrinkToFit, grd_opt, filterToolbar, colInfsEx) {
        //var _gridHeader = "manhom,MANHOMBHYT,0,0,t,l;dichvuid,DICHVUID,0,0,t,l;Tên DV,TENDICHVU,250,0,f,l;Giá BH,GIABHYT,60,0,f,l;Giá ND,GIANHANDAN,60,0,f,l;Giá YC,GIADICHVU,60,0,f,l";
        //var _group={
        //groupField : ['MANHOMBHYT'],
        //groupColumnShow : [false],
        //groupText : ['<b>{0}</b>']
        //};
        //jqGrid_initGroup("grdXetNghiem","300","100%","",true,_group,_gridHeader);
        //_gridHeader =
        //ten_cot,
        //ten_db_column,
        //do_rong_cot,
        //format: mac dinh = 0, chua dung
        //hidden column: t=hidden, f=show
        //align: l=left,r=right,c=center
        //_group =
        //groupField: ten cot tao nhom
        //groupColumnShow: hien thi cot group
        //groupText: {0}=Ten nhom, {1}=so thu tu
        var _self = this;
        var arHdr1 = hdrInfo.split("|");
        var hdrInfo1;
        var hdrGroup1;
        if (arHdr1.length > 1) {
            hdrInfo1 = arHdr1[0];
            hdrGroup1 = arHdr1[1];
        } else {
            hdrInfo1 = arHdr1[0];
        }
        var _gr_opt = this.buildOption(gridId, _width, _height, _caption, _multiselect, hdrInfo1, _shrinkToFit, '', colInfsEx);
        _gr_opt.grouping = true;
        _gr_opt.groupingView = _group;
        if (grd_opt) {

            _gr_opt = $.extend({}, _gr_opt, grd_opt);
        }
        $("#" + gridId).jqGrid(_gr_opt);
        $("#" + gridId).jqGrid('navGrid', '#pager_' + gridId, {edit: false, add: false, del: false});
        if (typeof filterToolbar === "undefined") {
            $("#" + gridId).jqGrid('filterToolbar', {
                ignoreCase: true,
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn"
            });
        } else {
            $("#" + gridId).jqGrid('filterToolbar', {
                ignoreCase: true,
                stringResult: true,
                searchOnEnter: true,
                defaultSearch: "cn"
            });
        }
        if (_height) {
            if (typeof _height != "string") {
                _height = _height + "px !important";
            } else if (!_height.endsWith("px")) {
                _height = _height + "px !important";
            } else {
                _height = _height + " !important";
            }
        }

        $('#gview_' + gridId).find('.ui-jqgrid-bdiv').attr("style", "height: " + _height);
        /*
			 var _parent=$('#gbox_' + gridId).parent();
	       	 while(_parent && _parent.width()==0) {
	       		 _parent=_parent.parent();
	       	 }
			 $('#' + gridId).jqGrid('setGridWidth',_parent.width());
			 */
        if (typeof _width == "string" && _width.endsWith("%")) {
            var wdP = _width.substring(0, _width.length - 1);
            _self.setWidthPercent(gridId, parseInt(wdP));
        } else {
            console.log(gridId + ' setGridWidth=' + _width);
            $("#" + gridId).jqGrid('setGridWidth', _width);
        }
        //$('#list').jqGrid('setGridParam', { onSelectRow: function(id){ alert(id); } } );
        if (hdrGroup1) {
            var _colModel = $("#" + gridId).jqGrid("getGridParam", "colModel");
            var grp_ar1 = hdrGroup1.split('#');
            var _lc = grp_ar1.length;
            for (var i1 = 0; i1 < grp_ar1.length; i1++) {
                var grp_ar2 = grp_ar1[i1].split(';');
                var _groupHeaders = [];
                var clast = 0;
                for (var i2 = 0; i2 < grp_ar2.length; i2++) {
                    var grp_ar3 = grp_ar2[i2].split(',');
                    //id,1,.;
                    if (_lc > 1) {
                        for (var c = clast; c < _colModel.length && _colModel[c].name != grp_ar3[0]; c++) {
                            _groupHeaders.push({startColumnName: _colModel[c].name, numberOfColumns: 1, titleText: ''});
                            clast++;
                        }
                    }
                    _groupHeaders.push({
                        startColumnName: grp_ar3[0],
                        numberOfColumns: grp_ar3[1],
                        titleText: '<center>' + grp_ar3[2] + '</center>'
                    });
                    clast = clast + parseInt(grp_ar3[1]) - 1;

                }
                $("#" + gridId).jqGrid('setGroupHeaders', {useColSpanStyle: true, groupHeaders: _groupHeaders});
            }
        }
        if (window.uiVersion && window.uiVersion == '2') {
            let parentWidth = $('#' + gridId).parents(".ui-jqgrid").parent().width();
            $('#' + gridId).jqGrid('setGridWidth', parentWidth);
        }

        if (_multiselect) {
            $('#' + gridId).find("tbody").on("change", "input[type=checkbox]", function (e) {
                var currentCB = $(this);
                var isChecked = this.checked;
                if (currentCB.is(".groupHeader")) { //if group header is checked, to check all child checkboxes
                    var checkboxes = currentCB.closest('tr').nextUntil(`[id^=${gridId}ghead]`).find('.cbox[type="checkbox"]');
                    checkboxes.each(function (i, e) {
                        let isFirst = (i == 0);
                        if (isChecked) {
                            $('#' + gridId).setSelection($(this).closest('tr').attr('id'), isFirst);
                        } else {
                            $('#' + gridId).restoreRow($(this).closest('tr').attr('id'));
                        }
                    });
                } else {
                    let headerEl = currentCB.closest('tr').prevAll(`tr[id^=${gridId}ghead]:first`);
                    let allCbs = headerEl.nextUntil(`[id^=${gridId}ghead]`).find('.cbox[type="checkbox"]');
                    var allChecked = !isChecked ? false : allCbs.filter(":checked").length === allCbs.length;
                    headerEl.find(".groupHeader").prop("checked", allChecked);
                }
            });

            $('#cb_' + gridId).on("change", function (e) {
                let isChecked = this.checked;
                $('#' + gridId).find("tbody").find("input[type=checkbox]").each(function () {
                    this.checked = isChecked;
                });
            });
        }

        return $("#" + gridId);
    },
    unmarkAll: function (gridId) {
        //$('#'+gridId).jqGrid('setGridParam', { rowList: [20, 30, 50, 100, 200]} );
        //var _self=this;
        var marked_list = $('#' + gridId).attr("marked");
        var marked = [];
        if (marked_list) {
            marked = marked_list.split(',');
        }
        for (var i1 = 0; i1 < marked.length; i1++) {
            var rowid = marked[i1];
            if (rowid) {
                var _color = '#FFFFFF';
                //_self.setRowData(rowid, false, {'background-color':_color});
                console.log('unmarkRow rowid=' + rowid + '_color=' + _color);
                //$('#'+gridId).jqGrid('setRowData', rowid, false,  {'background-color':_color} );
                $('#' + gridId).find("tr[id='" + rowid + "']").find('td').each(function (index, element) {
                    //$(element).css({'background-color':_color});
                    //$(element).removeClass("markedRow");

                    $(element).removeClass(function (_, cl) {
                        return cl.split(' ').filter(function (c) {
                            var clsName = "markedRow";
                            return c.substr(0, clsName.length) === clsName;
                        }).join(' ');
                    });
                });

            }
        }
        $('#' + gridId).attr("marked", '');
    },
    unmarkRow: function (gridId, _rowid) {
        //$('#'+gridId).jqGrid('setGridParam', { rowList: [20, 30, 50, 100, 200]} );
        //var _self=this;
        var marked_list = $('#' + gridId).attr("marked");
        console.log('unmarkRow marked_list=' + marked_list);
        var marked = [];
        if (marked_list) {
            marked = marked_list.split(',');
        }
        for (var i1 = 0; i1 < marked.length; i1++) {
            var rowid = marked[i1];
            if (rowid == _rowid) {
                var _color = '#FFFFFF';
                //_self.setRowData(rowid, false, {'background-color':_color});
                console.log('unmarkRow rowid=' + rowid + '_color=' + _color);
                //$('#'+gridId).jqGrid('setRowData', rowid, false,  {'background-color':_color} );
                $('#' + gridId).find("tr[id='" + rowid + "']").find('td').each(function (index, element) {
                    //$(element).css({'background-color':_color});
                    //$(element).removeClass("markedRow");

                    $(element).removeClass(function (_, cl) {
                        return cl.split(' ').filter(function (c) {
                            var clsName = "markedRow";
                            return c.substr(0, clsName.length) === clsName;
                        }).join(' ');
                    });
                });
                marked.splice(i1, 1);
                i1--;
            }
        }
        $('#' + gridId).attr("marked", marked.join(','));
    },
    markRow: function (gridId, rowid, _colorClass) {
        //$('#'+gridId).jqGrid('setGridParam', { rowList: [20, 30, 50, 100, 200]} );
        //var _self=this;
        //this.unmarkAll(gridId);
        var marked_list = $('#' + gridId).attr("marked");
        var marked = [];
        if (marked_list) {
            marked = marked_list.split(',');
        }
        //if(!_color) _color='#FF6F6F';
        if (!_colorClass) _colorClass = 'markedRow';
        //_self.setRowData(rowid, false, {'background-color':_color});
        console.log('markRow rowid=' + rowid + '_colorClass=' + _colorClass);
        //$('#'+gridId).jqGrid('setRowData', rowid, false,  {'background-color':_color} );
        $('#' + gridId).find("tr[id='" + rowid + "']").find('td').each(function (index, element) {
            //$(element).css({'background-color':_color});
            $(element).addClass(_colorClass);
        });
        marked.push(rowid);
        $('#' + gridId).attr("marked", marked.join(','));
    },
    getMarked: function (gridId) {
        //$('#'+gridId).jqGrid('setGridParam', { rowList: [20, 30, 50, 100, 200]} );
        //var _self=this;
        var marked_list = $('#' + gridId).attr("marked");
        var marked = [];
        if (marked_list) {
            marked = marked_list.split(',');
        }
        return marked;
    },
    clearGridData: function (gridId) {
        $("#" + gridId).jqGrid("clearGridData", true);
    },
    fetchGridData: function (gridId, jsonData) {
        //$("#"+gridId).setGridParam({datatype: 'json'});
        // show loading message
        $("#" + gridId)[0].grid.beginReq();
        $("#" + gridId).jqGrid('setGridParam', {data: jsonData});
        // hide the show message
        $("#" + gridId)[0].grid.endReq();
        // refresh the grid
        $("#" + gridId).setGridParam({datatype: 'local'});
        $("#" + gridId).trigger('reloadGrid');

    },
    //$("#listDichVu").jqGrid('setGridParam', {datatype: 'json', url: 'DanhSachDichVu?trangthai=' + $("#trangthai").val()}).trigger('reloadGrid');
    loadGridBySqlPage: function (gridId, _gridSQL, _sqlParam, callback, methodType) {
        //console.log('_sqlParam='+JSON.stringify(___sqlParam));
        //jqGrid_loadGridBySqlPage("grdChuyenKhoa",lookup_sql,sql_par);
        $grid = $("#" + gridId);
        var loadonce = $grid.getGridParam("loadonce");
        var sqltype = $grid.getGridParam("sqltype");
        if (_sqlParam == undefined) _sqlParam = [];
        var _postdata;
        $("#" + gridId).jqGrid("clearGridData", true);
        // Trường hợp loadonce == true thì lấy tất cả dữ liệu về
        if (loadonce != true) {
            //loadonce=false
            _postdata = {
                "func": "ajaxExecuteQueryPaging",
                "uuid": jsonrpc.AjaxJson.uuid,
                "params": [_gridSQL],
                "options": _sqlParam
            };
            var _querydata = JSON.stringify(_postdata);
            //console.log('_postdata='+_postdata);
            if ('POST' != methodType) {
                _querydata = encodeURIComponent(_querydata);
            }
            //console.log('_postdata='+_postdata);
            if (callback && typeof callback == "function") {
                if ('POST' == methodType) {
                    $grid.jqGrid('setGridParam', {
                        datatype: 'json',
                        mtype: 'POST',
                        url: RestInfo.base_url + "?_search=false&rows=100&page=1&sidx=&sord=asc",
                        "postData": _querydata,
                        async: true,
                        loadComplete: callback
                    }).trigger('reloadGrid');
                } else {
                    $grid.jqGrid('setGridParam', {
                        datatype: 'json',
                        url: RestInfo.base_url + '?postData=' + _querydata,
                        async: true,
                        loadComplete: callback
                    }).trigger('reloadGrid');
                }
            } else {
                if ('POST' == methodType) {
                    $grid.jqGrid('setGridParam', {
                        datatype: 'json',
                        mtype: 'POST',
                        url: RestInfo.base_url + "?_search=false&rows=100&page=1&sidx=&sord=asc",
                        "postData": _querydata
                    }).trigger('reloadGrid');
                } else {
                    $grid.jqGrid('setGridParam', {
                        datatype: 'json',
                        url: RestInfo.base_url + '?postData=' + _querydata
                    }).trigger('reloadGrid');
                }
            }
        } else {
            //loadonce=true
            if (callback && typeof callback == "function") {
                if (sqltype == "sp") {
                    var out_var = [0, ""];
                    var rt = jsonrpc.AjaxJson.ajaxCALL_SP_O(_gridSQL, _sqlParam, out_var, function (data) {
                        var griddata = $.parseJSON(data);
                        $grid.setGridParam({
                            datatype: 'local',
                            data: griddata
                        }).trigger('reloadGrid');
                        callback();
                    });
                } else {
                    jsonrpc.AjaxJson.ajaxExecuteQueryO(_gridSQL, _sqlParam, function (data) {
                        var griddata = $.parseJSON(data);
                        $grid.setGridParam({
                            datatype: 'local',
                            data: griddata
                        }).trigger('reloadGrid');
                        callback();
                    });
                }

            } else {
                var griddata = [];
                if (sqltype == "sp") {
                    var out_var = [0, ""];
                    console.log('_gridSQL=' + _gridSQL + " _sqlParam=" + JSON.stringify(_sqlParam));
                    var _par = GridUtil.getCallParam(_sqlParam);
                    griddata = jsonrpc.AjaxJson.ajaxCALL_SP_O(_gridSQL, _par + "$1$100000$$", out_var);
                    console.log('_gridSQL=' + _gridSQL + " griddata=" + JSON.stringify(griddata));
                } else {
                    griddata = $.parseJSON(jsonrpc.AjaxJson.ajaxExecuteQueryO(_gridSQL, _sqlParam));
                }
                $grid.setGridParam({
                    datatype: 'local',
                    data: griddata
                }).trigger('reloadGrid');
            }

        }
    },

    getCallParam: function (sql_par) {
        var call_par = "";
        //System.out.println("sql_par="+sql_par);
        for (var ii = 0; ii < sql_par.length; ii++) {
            var par_obj = sql_par[ii];
            var parName = par_obj["name"];
            var parValue = par_obj["value"];
            call_par += "$" + parValue;
        }
        if (call_par.length > 0) call_par = call_par.substring(1);
        return call_par;
    },
    setGridParam: function (gridId, paramObject) {
        // show loading message
        $("#" + gridId).jqGrid('setGridParam', paramObject);
    },
    base64toBlob: function (b64Data, contentType, sliceSize) {
        // function taken from http://stackoverflow.com/a/********/2591950
        // author Jeremy Banks http://stackoverflow.com/users/1114/jeremy-banks
        contentType = contentType || '';
        sliceSize = sliceSize || 512;

        var byteCharacters = window.atob(b64Data);
        var byteArrays = [];

        var offset;
        for (offset = 0; offset < byteCharacters.length; offset += sliceSize) {
            var slice = byteCharacters.slice(offset, offset + sliceSize);

            var byteNumbers = new Array(slice.length);
            var i;
            for (i = 0; i < slice.length; i = i + 1) {
                byteNumbers[i] = slice.charCodeAt(i);
            }

            var byteArray = new window.Uint8Array(byteNumbers);

            byteArrays.push(byteArray);
        }

        var blob = new window.Blob(byteArrays, {
            type: contentType
        });
        return blob;
    },
    createDownloadLink: function (anchor, base64data, exporttype, filename) {
        var _self = this;
        var blob;
        if (window.navigator.msSaveBlob) {
            console.log('window.navigator.msSaveBlob');
            blob = _self.base64toBlob(base64data, exporttype);
            window.navigator.msSaveBlob(blob, filename);
            return false;
        } else if (window.URL.createObjectURL) {
            console.log('window.navigator.msSaveBlob');
            blob = _self.base64toBlob(base64data, exporttype);
            var blobUrl = window.URL.createObjectURL(blob, exporttype, filename);
            anchor.href = blobUrl;
        } else {
            console.log('others');
            var hrefvalue = "data:" + exporttype + ";base64," + base64data;
            anchor.download = filename;
            anchor.href = hrefvalue;
        }

        // Return true to allow the link to work
        return true;
    },
    exportExcel: function (gridId, _client) {
        var _self = this;
        //var columnNms = $("#"+gridId).jqGrid('getGridParam','colNames');
        var _colModel = $("#" + gridId).jqGrid('getGridParam', 'colModel');
        var theads = "";
        //console.log('_colModel='+JSON.stringify(_colModel));
        var colNames = new Array();
        var ii = 0;
        for (var cc = 0; cc < _colModel.length; cc++) {
            if (!_colModel[cc].hidden && _colModel[cc].label) {
                theads = theads + "<th>" + _colModel[cc].label + "</th>";
                //colNames[ii++]=_colModel[cc];
                var col = new Object();
                col.name = _colModel[cc].name;
                var _wd = "100";
                var _ta = "left";
                if (_colModel[cc].width != null) {
                    _wd = _colModel[cc].width;
                }
                if (_colModel[cc].align != null) {
                    _ta = _colModel[cc].align;
                }
                //text-align:center;
                col.style = " style='width: " + _wd + ";text-align: " + _ta + "'";
                col.name = _colModel[cc].name.toUpperCase();
                colNames[ii++] = col;
            } else {
                console.log("_colModel[cc].hidden=" + _colModel[cc].hidden + " _colModel[cc].label=" + _colModel[cc].label);
            }
        }
        theads = "<tr>" + theads + "</tr>";

        var html = "<table border='1' class='tableList_1 t_space' cellspacing='10' cellpadding='0' x:str>" + theads;
        var mya = new Array();
        mya = $("#" + gridId).getDataIDs();  // Get All IDs
        if (_client) {
            /*
		    	var data=$("#"+gridId).getRowData(mya[0]);     // Get First row to get the labels
		        for(i=0;i<mya.length;i++)
		        {
		            html=html+"<tr>";
		            data=$("#"+gridId).getRowData(mya[i]); // get each row
		            for(j=0;j<colNames.length;j++){
		            	html=html+"<td "+colNames[j].style+">"+data[colNames[j].name]+"</td>";
		            }
		            html=html+"</tr>";  // output each row with end of line
		        }
		        */
            //mya= $("#"+gridId).jqGrid('getGridParam', 'data');
            mya = $("#" + gridId).jqGrid('getRowData');
            console.log('mya=' + JSON.stringify(mya));
            for (i = 0; i < mya.length; i++) {
                html = html + "<tr>";
                data = mya[i]; // get each row
                for (j = 0; j < colNames.length; j++) {
                    html = html + "<td " + colNames[j].style + ">" + data[colNames[j].name] + "</td>";
                }
                html = html + "</tr>";  // output each row with end of line
            }
        } else {
            //var data=
            var myUrl = $("#" + gridId).jqGrid('getGridParam', 'url');
            var rt = $.ajax({
                url: myUrl,
                type: "GET",
                data: "rows=full",
                contentType: "text/plain; charset=utf-8",
                async: false
            }).responseText;
            console.log("exportExcel.rt=" + rt);
            var _rtObj = JSON.parse(rt);
            mya = _rtObj.rows;
            //console.log("mya.length="+mya.length);
            console.log('colNames=' + JSON.stringify(colNames));
            for (i = 0; i < mya.length; i++) {
                html = html + "<tr>";
                data = mya[i]; // get each row
                for (j = 0; j < colNames.length; j++) {
                    var colData = data[colNames[j].name];
                    if (colData == undefined || colData == null || colData == "null") {
                        colData = "";
                    }
                    html = html + "<td " + colNames[j].style + ">" + colData + "</td>";
                }
                html = html + "</tr>";  // output each row with end of line
            }
        }


        html = html + "</table>";  // end of line at the end
        //console.log('html='+html);
        var excelFile = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:x='urn:schemas-microsoft-com:office:excel' xmlns='http://www.w3.org/TR/REC-html40'>";
        excelFile += "<head>";
        excelFile += '<meta http-equiv="Content-type" content="text/html;charset=utf-8" />';
        excelFile += "<!--[if gte mso 9]>";
        excelFile += "<xml>";
        excelFile += "<x:ExcelWorkbook>";
        excelFile += "<x:ExcelWorksheets>";
        excelFile += "<x:ExcelWorksheet>";
        excelFile += "<x:Name>";
        excelFile += "Sheet1";
        excelFile += "</x:Name>";
        excelFile += "<x:WorksheetOptions>";
        excelFile += "<x:DisplayGridlines/>";
        excelFile += "</x:WorksheetOptions>";
        excelFile += "</x:ExcelWorksheet>";
        excelFile += "</x:ExcelWorksheets>";
        excelFile += "</x:ExcelWorkbook>";
        excelFile += "</xml>";
        excelFile += "<![endif]-->";
        excelFile += "</head>";
        excelFile += "<body>";
        excelFile += html.replace(/"/g, '\'');
        excelFile += "</body>";
        excelFile += "</html>";

        var uri = "data:application/vnd.ms-excel;base64,";
        //var ctx = { worksheet: $settings.worksheetName, table: htmltable };
        //base64
        var base64_excelFile = window.btoa(unescape(encodeURIComponent(excelFile)));
        var _excelData = (uri + base64_excelFile);
        //window.open(_excelData);
        var anchor = document.createElement('a');
        _self.createDownloadLink(anchor, base64_excelFile, 'application/vnd.ms-excel', 'excelDownload.xls');
        //anchor.href = _excelData;
        //setting the file name
        //anchor.download = 'excelDownload.xls';
        //triggering the function
        anchor.click();
        //just in case, prevent default behaviour
        //e.preventDefault();

    }
    , exportExcelFull: function (gridId, _client, _option) {
        const {
            includingColumn = {}
        } = _option
        var _self = this;
        //var columnNms = $("#"+gridId).jqGrid('getGridParam','colNames');
        var _colModel = $("#" + gridId).jqGrid('getGridParam', 'colModel');
        var theads = "";
        // console.log('_colModel='+JSON.stringify(_colModel));
        var colNames = new Array();
        var ii = 0;
        for (var cc = 0; cc < _colModel.length; cc++) {
            if ((!_colModel[cc].hidden && _colModel[cc].label) || includingColumn[_colModel[cc].name]) {
                theads = theads + "<th>" + ( (!_colModel[cc].hidden && _colModel[cc].label) ? _colModel[cc].label : includingColumn[_colModel[cc].name]) + "</th>";
                // theads = theads + "<th>" + (!_colModel[cc].hidden && _colModel[cc].label) ? _colModel[cc].label : includingColumn[_colModel[cc].label] + "</th>";
                //colNames[ii++]=_colModel[cc];
                var col = new Object();
                col.name = _colModel[cc].name;
                var _wd = "100";
                var _ta = "left";
                if (_colModel[cc].width != null) {
                    _wd = _colModel[cc].width;
                }
                if (_colModel[cc].align != null) {
                    _ta = _colModel[cc].align;
                }
                //text-align:center;
                col.style = " style='width: " + _wd + ";text-align: " + _ta + "'";
                col.name = _colModel[cc].name.toUpperCase();
                colNames[ii++] = col;
            } else {
                console.log("_colModel[cc].hidden=" + _colModel[cc].hidden + " _colModel[cc].label=" + _colModel[cc].label);
            }
        }
        theads = "<tr>" + theads + "</tr>";

        var html = "<table border='1' class='tableList_1 t_space' cellspacing='10' cellpadding='0'>" + theads;
        var mya = new Array();
        mya = $("#" + gridId).getDataIDs();  // Get All IDs
        if (_client) {
            /*
				var data=$("#"+gridId).getRowData(mya[0]);     // Get First row to get the labels
				for(i=0;i<mya.length;i++)
				{
					html=html+"<tr>";
					data=$("#"+gridId).getRowData(mya[i]); // get each row
					for(j=0;j<colNames.length;j++){
						html=html+"<td "+colNames[j].style+">"+data[colNames[j].name]+"</td>";
					}
					html=html+"</tr>";  // output each row with end of line
				}
				*/
            //mya= $("#"+gridId).jqGrid('getGridParam', 'data');
            mya = $("#" + gridId).jqGrid('getRowData');
            console.log('mya=' + JSON.stringify(mya));
            for (i = 0; i < mya.length; i++) {
                html = html + "<tr>";
                data = mya[i]; // get each row
                for (j = 0; j < colNames.length; j++) {
                    html = html + "<td " + colNames[j].style + ">" + data[colNames[j].name] + "</td>";
                }
                html = html + "</tr>";  // output each row with end of line
            }
        } else {
            //var data=
            var myUrl = $("#" + gridId).jqGrid('getGridParam', 'url');
            var rt = $.ajax({
                url: myUrl + "?_search=false&rows=10000&page=1&sidx=&sord=asc",
                type: "GET",
                data: "rows=full",
                contentType: "text/plain; charset=utf-8",
                async: false
            }).responseText;
            console.log("exportExcel.rt=" + rt);
            var _rtObj = JSON.parse(rt);
            mya = _rtObj.rows;
            //console.log("mya.length="+mya.length);
            console.log('colNames=' + JSON.stringify(colNames));
            for (i = 0; i < mya.length; i++) {
                html = html + "<tr>";
                data = mya[i]; // get each row
                for (j = 0; j < colNames.length; j++) {
                    var colData = data[colNames[j].name];
                    if (colData == undefined || colData == null || colData == "null") {
                        colData = "";
                    }
                    html = html + "<td " + colNames[j].style + ">" + colData + "</td>";
                }
                html = html + "</tr>";  // output each row with end of line
            }
        }


        html = html + "</table>";  // end of line at the end
        //console.log('html='+html);
        var excelFile = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:x='urn:schemas-microsoft-com:office:excel' xmlns='http://www.w3.org/TR/REC-html40'>";
        excelFile += "<head>";
        excelFile += '<meta http-equiv="Content-type" content="text/html;charset=utf-8" />';
        excelFile += "<!--[if gte mso 9]>";
        excelFile += "<xml>";
        excelFile += "<x:ExcelWorkbook>";
        excelFile += "<x:ExcelWorksheets>";
        excelFile += "<x:ExcelWorksheet>";
        excelFile += "<x:Name>";
        excelFile += "Sheet1";
        excelFile += "</x:Name>";
        excelFile += "<x:WorksheetOptions>";
        excelFile += "<x:DisplayGridlines/>";
        excelFile += "</x:WorksheetOptions>";
        excelFile += "</x:ExcelWorksheet>";
        excelFile += "</x:ExcelWorksheets>";
        excelFile += "</x:ExcelWorkbook>";
        excelFile += "</xml>";
        excelFile += "<![endif]-->";
        excelFile += "</head>";
        excelFile += "<body>";
        excelFile += html.replace(/"/g, '\'');
        excelFile += "</body>";
        excelFile += "</html>";

        var uri = "data:application/vnd.ms-excel;base64,";
        //var ctx = { worksheet: $settings.worksheetName, table: htmltable };
        //base64
        var base64_excelFile = window.btoa(unescape(encodeURIComponent(excelFile)));
        var _excelData = (uri + base64_excelFile);
        //window.open(_excelData);
        var anchor = document.createElement('a');
        _self.createDownloadLink(anchor, base64_excelFile, 'application/vnd.ms-excel', 'excelDownload.xls');
        //anchor.href = _excelData;
        //setting the file name
        //anchor.download = 'excelDownload.xls';
        //triggering the function
        anchor.click();
        //just in case, prevent default behaviour
        //e.preventDefault();

    }
};


//--------- TreeUtil ---------------------------------------------------------------------------------------
var TreeUtil = {
    _makeTree: function (options, data) {
        var children, e, id, o, pid, temp, _i, _len, _ref;
        id = options.id || "id";
        pid = options.parentid || "parentid";
        children = options.children || "children";
        temp = {};
        o = [];
        _ref = data;

        for (_i = 0, _len = _ref.length; _i < _len; _i++) {
            //e = _ref[_i];
            e = {};
            for (var _property in _ref[_i]) {
                if (_ref[_i].hasOwnProperty(_property)) {
                    e[_property.toLowerCase()] = _ref[_i][_property];
                }
            }
            e[children] = [];
            temp[e[id]] = e;
            if (temp[e[pid]] != null) {
                temp[e[pid]][children].push(e);
            } else {
                o.push(e);
            }
        }
        //console.log("reload_newData="+JSON.stringify(o));
        return o;
    },
    init: function (treeId, _checkbox, _dnd) {
        /*
	 $("#selector").fancytree({
    activeVisible: true, // Make sure, active nodes are visible (expanded).
    aria: false, // Enable WAI-ARIA support.
    autoActivate: true, // Automatically activate a node when it is focused (using keys).
    autoCollapse: false, // Automatically collapse all siblings, when a node is expanded.
    autoScroll: false, // Automatically scroll nodes into visible area.
    clickFolderMode: 4, // 1:activate, 2:expand, 3:activate and expand, 4:activate (dblclick expands)
    checkbox: false, // Show checkboxes.
    debugLevel: 2, // 0:quiet, 1:normal, 2:debug
    disabled: false, // Disable control
    focusOnSelect: false, // Set focus when node is checked by a mouse click
    escapeTitles: false, // Escape `node.title` content for display
    generateIds: false, // Generate id attributes like <span id='fancytree-id-KEY'>
    idPrefix: "ft_", // Used to generate node id´s like <span id='fancytree-id-<key>'>.
    icon: true, // Display node icons.
    keyboard: true, // Support keyboard navigation.
    keyPathSeparator: "/", // Used by node.getKeyPath() and tree.loadKeyPath().
    minExpandLevel: 1, // 1: root node is not collapsible
    quicksearch: false, // Navigate to next node by typing the first letters.
    selectMode: 2, // 1:single, 2:multi, 3:multi-hier
    tabindex: "0", // Whole tree behaves as one single control
    titlesTabbable: false // Node titles can receive keyboard focus
});
	 */
        var t_opt = {
            extensions: ["filter"],
            checkbox: _checkbox,
            selectMode: 3,
            debugLevel: 0,
            folder: true,
            quicksearch: true,
            source: [],
            filter: {
                autoApply: true,
                // autoExpand: true,
                mode: "hide"
            }

            /*
	      ,select: function(event, data) {
	          nguoidung = $.map(data.tree.getSelectedNodes(), function(node) {
	          	console.log('select='+node.key);
	              return node.key;
	          });
	      }
	      */
        };
        if (_dnd) {
            t_opt.extensions = ["dnd", "edit", "filter"];
            t_opt.dnd = {
                autoExpandMS: 400,
                focusOnClick: true,
                preventVoidMoves: true, // Prevent dropping nodes 'before self', etc.
                preventRecursiveMoves: true, // Prevent dropping nodes on own descendants
                dragStart: _dnd.dragStart,
                dragEnter: _dnd.dragEnter,
                dragDrop: _dnd.dragDrop
            };
        }
        console.log('fcTree_init=' + treeId);
        $("#" + treeId).fancytree(t_opt);
        var tree = $("#" + treeId).fancytree("getTree");
        return tree;

    },
    load: function (treeId, _data, _bExpand) {
        var _self = this;
        if (_data != undefined) {
            /**/
            $(function () {
                var _newData = _self._makeTree({id: "key", parentid: "parent"}, _data);
                var tree = $("#" + treeId).fancytree("getTree");
                //console.log("reload_newData="+JSON.stringify(_newData));
                tree.reload(_newData);
                if (_bExpand) {
                    $("#" + treeId).fancytree("getRootNode").visit(function (node) {
                        node.setExpanded(true);
                    });
                }
            });

        } else {
            console.log('fcTree_load._data=undefined');
        }
    }
};
//--------- FileUtil ---------------------------------------------------------------------------------------
var FileUtil = {
    dataURLtoFile: function (dataurl, filename) {
        var arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n);

        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }

        return new File([u8arr], filename, {type: mime});
    },
};

//--------- LoadingUtil ---------------------------------------------------------------------------------------
var LoadingUtil = {
    disabledButton: function (element, funcExecute) {
        var lbl = $(element).html();
        $(element).addClass("disabled");
        $(element).attr("disabled", true);
        $(element).html(`<span class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></span> ${lbl}`);
        if (funcExecute && (typeof funcExecute == "function")) {
            setTimeout(function () {
                funcExecute();
                LoadingUtil.enableButton(element);
            }, 100);
        }
    },
    enableButton: function (element) {
        var lbl = $(element).html().replace('<span class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></span>', "");
        $(element).removeClass("disabled");
        $(element).attr("disabled", false);
        $(element).html(lbl);
    },

    showSpinner: function () {
        if ($("#mainOverSpinner").length < 1) {
            $("body").append(`<div id="mainOverSpinner" class="hide">
				<style>
					#mainOverSpinner {
						position: fixed;
						width: 100%;
						overflow: hidden;
						top: 0;
						left: 0;
						right: 0;
						z-index: 2;
						bottom: 0;
					}
					
					@keyframes spinner {
						to {
							transform: rotate(360deg);
						}
					}
					
					#mainOverSpinner .spinner-loading.show {
						display: block;
						z-index: 999;
					}
				
					.spinner-loading.hide {
						display: none;
						z-index: -1;
					}
				
					.spinner:before {
						content: '';
						box-sizing: border-box;
						position: absolute;
						top: 50%;
						left: 50%;
						width: 40px;
						height: 40px;
						margin-top: -20px;
						margin-left: -20px;
						border-radius: 50%;
						border: 4px solid #000;
						border-top-color: #fff;
						animation: spinner 1s linear infinite;
					}
					#mainOverSpinner .spinner-loading {
						position: absolute;
						z-index: 1;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						background-color: #cacaca85;
					}
				</style>
				<div class="spinner-loading">
					<span class="spinner"></span>
				</div>
			</div>`);
        }
        $("#mainOverSpinner").removeClass("hide");
    },
    hideSpinner: function () {
        $("#mainOverSpinner").addClass("hide");
    }
};

//--------- UploadUtil ---------------------------------------------------------------------------------------
var UploadUtil = {
    upload: function (_formId, _param, _succFnc) {
        $("#param").value = _param;
        var opts = {
            iframeSrc: 'about:blank',
            url: "../upload/saveFile.jsp?uuid=" + jsonrpc.AjaxJson.uuid,
            type: "post",
            dataType: "json",
            success: function (data) {
                console.log('upload data.id=' + data.id);
                $('#imgMEDIA_ID').attr("src", data.url);
            }

        };
        if (typeof _succFnc == "function") {
            opts.success = _succFnc;
        }

        $("#" + _formId).ajaxForm(opts).submit();
    },
    uploadBase64: function (_data, _fileType, _succFnc) {
        //var _data='data:audio/wav;base64,UklGRixAKABXQVZFZm10IBAAAAABAAIARKwAABCxAgAEABAAZGF0Y�EJ4AngCR4KHgpYClgKcApwCnwKfAqGCoYKcgpyCkEKQQoSChIK3QndCZcJlwljCWMJWglaCQ==';
        //var rt=jsonrpc.AjaxJson.uploadMediaBase64(_data,'wav');
        if (typeof _succFnc == "function") {
            jsonrpc.AjaxJson.uploadMediaBase64(_data, _fileType, _succFnc);
        } else {
            var rt = jsonrpc.AjaxJson.uploadMediaBase64(_data, _fileType);

            return {id: rt, url: '../upload/getdata.jsp?id=' + rt};
        }

    },
    deleteMedia: function (_mediaId, _succFnc) {
        //var _data='data:audio/wav;base64,UklGRixAKABXQVZFZm10IBAAAAABAAIARKwAABCxAgAEABAAZGF0Y�EJ4AngCR4KHgpYClgKcApwCnwKfAqGCoYKcgpyCkEKQQoSChIK3QndCZcJlwljCWMJWglaCQ==';
        //var rt=jsonrpc.AjaxJson.uploadMediaBase64(_data,'wav');
        if (typeof _succFnc == "function") {
            jsonrpc.AjaxJson.deleteMedia(_mediaId, _succFnc);
        } else {
            var rt = jsonrpc.AjaxJson.deleteMedia(_mediaId);

            return rt;
        }

    },
    uploadIDGPromises: function (fileItem, isEmr) {
        return new Promise(function (resolve, reject) {
            var formData = new FormData();
            formData.append("fileContent", fileItem);
            var xhr = new XMLHttpRequest();
            var url = window.location.origin + '/vnpthis/IdgStorageUpload';
            if (isEmr) {
                url = url + "?source=emr";
            }
            xhr.open("POST", url);
            xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
            xhr.send(formData);
            xhr.onload = function (e) {
                if (this.status == 200) {
                    resolve({
                        media_id: JSON.parse(this.responseText).results.media_id,
                        url_img: JSON.parse(this.responseText).results.url_img,
                        bucket: JSON.parse(this.responseText).results.bucket,
                        file_name: fileItem.name
                    });
                } else {
                    reject("Tải lên thất bại");
                }
            };
        });
    },
    uploadIDGPromisesByPath: function (fileItem, storage, path, name, params) {
        if (params.constructor === ({}).constructor) {
            return new Promise(function (resolve, reject) {
                var formData = new FormData();
                formData.append("fileContent", fileItem);
                formData.append("filepath", path);
                formData.append("storage", storage);
                formData.append("filename", name);
                var xhr = new XMLHttpRequest();
                var url = window.location.origin + '/vnpthis/IdgStorageUpload?source=path&key=' + encodeURIComponent(path);
                xhr.open("POST", url);
                xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
                xhr.send(formData);
                xhr.onload = function (e) {
                    if (this.status == 200) {
                        resolve(JSON.parse(this.responseText));
                    } else {
                        reject("Tải lên thất bại");
                    }
                };
            });
        } else {
            return Promise.reject(new Error('params is not an object'));
        }
    },
    deleteIDGPromisesByPath: function (storage, path, resolve, reject) {
        let xhr = new XMLHttpRequest();
        let url = window.location.origin + '/vnpthis/IdgStorageUpload?path=' + path + '&storage=' + encodeURIComponent(storage);
        xhr.open("DELETE", url);
        xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
        xhr.send();
        xhr.onload = function (e) {
            if (this.status == 200) {
                if (typeof resolve === 'function') {
                    resolve(JSON.parse(this.responseText).message);
                }
            } else {
                if (typeof reject === 'function') {
                    reject(JSON.parse(this.responseText).message);
                }
            }
        };
    },
    uploadIDG: function (fileItem, _success, _error) {
        var formData = new FormData();
        formData.append("fileContent", fileItem);
        var xhr = new XMLHttpRequest();
        xhr.open("POST", window.location.origin + '/vnpthis/IdgStorageUpload');
        xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
        xhr.send(formData);
        xhr.onload = function (e) {
            if (this.status == 200) {
                _success(JSON.parse(this.responseText).results);
            } else {
                _error("Tải lên thất bại");
            }
        };
    },
    deleteOnIDG: function (media_id, _success, _error, isEmr) {
        var xhr = new XMLHttpRequest();
        var url = window.location.origin + '/vnpthis/IdgStorageUpload?media_id=' + media_id;
        if (isEmr) {
            url = url + "&source=emr";
        }
        xhr.open("DELETE", url);
        xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
        xhr.send();
        xhr.onload = function (e) {
            if (this.status == 200) {
                _success(JSON.parse(this.responseText).message);
            } else {
                _error(JSON.parse(this.responseText).message);
            }
        };
    },
    getUrlDownloadFromIDG: function (media_id, _success, _error) {
        var xhr = new XMLHttpRequest();
        xhr.open("GET", window.location.origin + '/vnpthis/IdgStorageUpload?media_id=' + media_id);
        xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
        xhr.send();
        xhr.onload = function (e) {
            if (this.status == 200) {
                _success(JSON.parse(this.responseText).results);
            } else {
                _error(JSON.parse(this.responseText).message);
            }
        };
    },
    getUrlDownloadFromIDGByKey: function (key, value, _success, _error) {
        var xhr = new XMLHttpRequest();
        xhr.open("GET", window.location.origin + '/vnpthis/IdgStorageUpload?' + key + '=' + value);
        xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
        xhr.send();
        xhr.onload = function (e) {
            if (this.status == 200) {
                _success(JSON.parse(this.responseText).results);
            } else {
                _error(JSON.parse(this.responseText).message);
            }
        };
    }


};
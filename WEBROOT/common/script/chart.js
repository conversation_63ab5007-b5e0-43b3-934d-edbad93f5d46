/**
 * <AUTHOR>
 *
 *
 * Create date: 2023/11/24
 * VNPT HIS - EHealth
 *
 */

const CHART_POINT_STYLE_ENUM = {
    CIRCLE: "CIRCLE",
    RECTANGLE: "RECTANGLE",
    SYMBOL: "SY<PERSON><PERSON>",
    TRIANGLE: "TRIANGLE"
}

const CHART_BARX_POSITION_ENUM = {
    TOP: "TOP",
    BOTTOM: "BOTTOM"
}

const CHART_ZOOM_UNIT = 20;
class Chart {
    get selector() {
        return this._selector;
    }

    set selector(value) {
        this._selector = value;
    }

    get jcanvasProperties() {
        return this._jcanvasProperties;
    }

    set jcanvasProperties(value) {
        this._jcanvasProperties = value;
    }

    get backgroundColor() {
        return this._backgroundColor;
    }

    set backgroundColor(value) {
        this._backgroundColor = value;
    }

    get isUnitMapped() {
        return this._isUnitMapped;
    }

    set isUnitMapped(value) {
        this._isUnitMapped = value;
    }

    get lrange() {
        return this._lrange;
    }

    set lrange(value) {
        this._lrange = value;
    }

    get trange() {
        return this._trange;
    }

    set trange(value) {
        this._trange = value;
    }

    get cellRangeY() {
        return this._cellRangeY;
    }

    set cellRangeY(value) {
        this._cellRangeY = value;
    }

    get cellRangeX() {
        return this._cellRangeX;
    }

    set cellRangeX(value) {
        this._cellRangeX = value;
    }
    constructor(
        selector,
        config = {
            // x, y, backgroundColor,
            // barX: {numof, backgroundColor, lines, style, position},
            // valueUnit,
            // borderValueUnit
        },
        jcanvasProperties = {
            // x: {}, y: {}
        }
    ) {
        let {
            x: x = new AxisX({}),
            y: y = new AxisY({}),
            barX: barX,
            valueUnit: valueUnit,
            borderValueUnit: borderValueUnit,
            backgroundColor: backgroundColor = 'white'
        } = config || {};
        // let {
        //     numof: numoflh = 0,
        //     style: stylelh,
        //     values: valueslh = [],
        //     showLast: showLastlh = false
        //     border: borderlh = false
        // } = valueUnit || {}

        let {
            numof: h_numof = 0,
            position: h_position = CHART_BARX_POSITION_ENUM.TOP,
            backgroundColor: h_backgroundColor = 'white',
            style: h_style = new Chart.Style({}),
            text: text = [],
            lines: lines = new Chart.Lines({}),
            fullBorders: fullBorders = false,
            vuText: vuText = ""
        } = barX || {};
        this
            ._setupX(x)
            ._setupY(y)
            ._setupBarX({
                numof: h_numof,
                position: h_position,
                backgroundColor: h_backgroundColor,
                lines,
                text,
                style: h_style,
                fullBorders,
                vuText
            })
            ._setupValueUnit(valueUnit)
        this._selector = selector;
        this._backgroundColor = backgroundColor;
        this._jcanvasProperties = jcanvasProperties;
        this._isUnitMapped = true;
        // this._isUnitMapped = (valueslh && Array.isArray(valueslh) && this._isAllNumber(valueslh[0], valueslh[1]));
        this._lrange = 0;
        this._trange = 0;
        this._cellRangeY = 0;
        this._cellRangeX = 0;
        this._borderValueUnit = borderValueUnit;
        this._lineChartHighlight = [];
        this._lineChartClickWorking = false;
        this._valuePointMouseOverWorking = [];
        console.log(this);
    }

    _setupX(x) {
        this._x = x;
        return this;
    }

    _setupY(y) {
        this._y = y;
        return this;
    }

    _setupBarX({numof, position, backgroundColor, lines, text, style, fullBorders, vuText}) {
        this._barX = {numof, position, backgroundColor, lines, text, style, fullBorders, vuText};
        return this;
    }

    _setupValueUnit(valueUnit) {
        this._valueUnit = valueUnit;
        return this;
    }

    print() {
        console.log(this)
    }

    drawGrid() {
        if (!(this._selector instanceof jQuery)) {
            alert("_selector is not jquery object.");
            return;
        }
        if (this._selector.length < 1) {
            console.log(this._selector);
            console.log("_selector is not existed!");
            return;
        }

        if (Number.isNaN(parseInt(this._x.range))) {
            console.error("_x.range is NaN");
            return;
        }

        if (Number.isNaN(parseInt(this._y.range))) {
            console.error("_y.range is NaN");
            return;
        }

        if (Number.isNaN(parseInt(this._x.numof))) {
            console.error("_x.numof is NaN");
            return;
        }

        if (Number.isNaN(parseInt(this._y.numof))) {
            console.error("_y.numof is NaN");
            return;
        }


        if (this._valueUnit && Array.isArray(this._valueUnit) && this._valueUnit.length > 0) {
            this._sumValueUnitNumofs = this._valueUnit.reduce((a, c) => {
                return a + (this._isNumber(c.numof) ? c.numof : 0)
            }, 0);
            this._lrange = this._x.range * CHART_ZOOM_UNIT * this._sumValueUnitNumofs / (this._x.numof + this._sumValueUnitNumofs);
        }

        let paddingPerc = [0, 0];
        if (this._y.padding && this._y.padding.length === 2 && this._isAllNumber(...this._y.padding)) {
            paddingPerc = [
                ((this._y.padding[0] > 0 && this._y.padding[0] < 100) ? this._y.padding[0] : 0) / 100,
                ((this._y.padding[1] > 0 && this._y.padding[1] < 100) ? this._y.padding[1] : 0) / 100
            ]
        }

        let _numofHeader = 0;
        if (this._isNumber(this._barX.numof) && this._barX.numof > 0) {
            _numofHeader = this._barX.numof;
        }
        let _widthCV = this._x.range;
        let _heightCV = this._y.range * (1 + paddingPerc[0] + paddingPerc[1] + (_numofHeader / this._y.numof));
        this._selector.attr("width", _widthCV);
        this._selector.attr("height", _heightCV);
        this._selector.css("width", this._x.range);
        this._selector.css("height", this._y.range * (1 + paddingPerc[0] + paddingPerc[1] + (_numofHeader / this._y.numof)));
        this._selector.addLayer({
            type: 'rectangle',
            fillStyle: this._backgroundColor,
            x: _widthCV / 2,
            y: _heightCV / 2,
            width: _widthCV,
            height: _heightCV
        })
        if (paddingPerc[0] > 0) {
            this._trange = paddingPerc[0] * CHART_ZOOM_UNIT * this._y.range;
        }
        this._cellRangeY = this._y.range * CHART_ZOOM_UNIT / this._y.numof;
        this._cellRangeX = ((this._x.range * CHART_ZOOM_UNIT) - this._lrange) / this._x.numof;
        let _isBorderVU = (this._lrange > 0 && this._borderValueUnit && this._isTypeOf(this._borderValueUnit, Chart.Style));
        if (_numofHeader > 0) {
            if (this._barX.backgroundColor) {
                this._selector.addLayer({
                    type: 'rectangle',
                    scale: 1 / CHART_ZOOM_UNIT,
                    fillStyle: this._barX.backgroundColor,
                    x: this._calCoorByZoomUnit((CHART_ZOOM_UNIT * this._x.range) - (this._cellRangeX * this._x.numof / 2)),
                    y: this._barX.position === CHART_BARX_POSITION_ENUM.TOP ?
                        this._calCoorByZoomUnit(this._trange + (this._cellRangeY * this._barX.numof / 2)) :
                        this._calCoorByZoomUnit((2 * (this._trange + (this._cellRangeY * this._y.numof)) + (this._cellRangeY * this._barX.numof)) / 2),
                    width: (CHART_ZOOM_UNIT * this._x.range) - this._lrange,
                    height: this._cellRangeY * this._barX.numof
                })
            }
            this._trange = this._trange + (this._barX.position === CHART_BARX_POSITION_ENUM.TOP ? (_numofHeader * this._cellRangeY) : 0);
            if (this._barX.lines.enable === true) {
                let _yf = this._barX.position === CHART_BARX_POSITION_ENUM.TOP
                        ? (this._trange - (_numofHeader * this._cellRangeY))
                        : (this._trange + (_numofHeader * this._cellRangeY) + this._cellRangeY * this._y.numof);
                let _ys = this._barX.position === CHART_BARX_POSITION_ENUM.TOP
                    ? (this._trange)
                    : (this._trange + this._cellRangeY * this._y.numof - CHART_ZOOM_UNIT / 2);
                let _xf = (this._barX.fullBorders === true && _isBorderVU) ? 0 : this._lrange;
                this._selector.addLayer({
                    type: 'line',
                    scale: 1 / CHART_ZOOM_UNIT,
                    strokeStyle: this._barX.lines.strokeStyle,
                    strokeWidth: this._barX.lines.strokeWidth * CHART_ZOOM_UNIT,
                    x1: _xf,
                    y1: _yf,
                    x2: this._x.range * CHART_ZOOM_UNIT,
                    y2: _yf
                }).addLayer({
                    type: 'line',
                    scale: 1 / CHART_ZOOM_UNIT,
                    strokeStyle: this._barX.lines.strokeStyle,
                    strokeWidth: this._barX.lines.strokeWidth * CHART_ZOOM_UNIT,
                    x1: this._lrange,
                    y1: _ys,
                    x2: this._x.range * CHART_ZOOM_UNIT,
                    y2: _ys
                });
            }
        }

        // Y Coordinates
        [...Array(this._y.numof + 1).keys()].forEach(i => {
            let _shiftY = (i === 0 ? CHART_ZOOM_UNIT / 2 : (i === this._y.numof ? -CHART_ZOOM_UNIT / 2 : 0))
            let _isOddLine = (!(i % 2 === 0)) && (this._y.odd.enable === true);
            let _oddProperties = _isOddLine ? {
                strokeDash: this._y.odd.strokeDash.map(el => el * CHART_ZOOM_UNIT),
                strokeDashOffset: this._y.odd.strokeDashOffset * CHART_ZOOM_UNIT
            } : {
                strokeDash: this._y.style.strokeDash.map(el => el * CHART_ZOOM_UNIT),
                strokeDashOffset: this._y.style.strokeDashOffset * CHART_ZOOM_UNIT
            };
            this._selector.addLayer({
                ...{
                    type: 'line',
                    scale: 1 / CHART_ZOOM_UNIT,
                    strokeStyle: _isOddLine ? this._y.odd.strokeStyle : this._y.style.strokeStyle,
                    strokeWidth: (_isOddLine ? this._y.odd.strokeWidth : this._y.style.strokeWidth) * CHART_ZOOM_UNIT,
                    x1: this._lrange,
                    y1: i * this._cellRangeY + _shiftY + this._trange,
                    x2: this._x.range * CHART_ZOOM_UNIT,
                    y2: i * this._cellRangeY + _shiftY + this._trange
                },
                ..._oddProperties,
                ...this._jcanvasProperties.y
            });

            if (_isBorderVU) {
                this._selector.addLayer(
                    {
                        type: "line",
                        scale: 1 / CHART_ZOOM_UNIT,
                        strokeStyle: this._borderValueUnit.fillStyle,
                        strokeWidth: this._borderValueUnit.strokeWidth * CHART_ZOOM_UNIT,
                        x1: 0,
                        y1: i * this._cellRangeY + _shiftY + this._trange,
                        x2: this._lrange,
                        y2: i * this._cellRangeY + _shiftY + this._trange
                    }
                );
                if (i === 0) {
                    this._selector.addLayer(
                        {
                            type: "line",
                            scale: 1 / CHART_ZOOM_UNIT,
                            strokeStyle: this._borderValueUnit.fillStyle,
                            strokeWidth: this._borderValueUnit.strokeWidth * CHART_ZOOM_UNIT,
                            x1: 0,
                            y1: -1 * this._cellRangeY + _shiftY + this._trange,
                            x2: this._lrange * CHART_ZOOM_UNIT,
                            y2: -1 * this._cellRangeY + _shiftY + this._trange
                        }
                    );
                }
            }

            if (this._y.miliLine.enable === true && this._y.numof > i) {
                [...Array(4).keys()].forEach(j => {
                    this._selector.addLayer({
                        type: 'line',
                        scale: 1 / CHART_ZOOM_UNIT,
                        strokeStyle: this._y.miliLine.strokeStyle,
                        strokeWidth: this._y.miliLine.strokeWidth * CHART_ZOOM_UNIT / 3,
                        x1: this._lrange,
                        y1: (i + ((j + 1) / 5)) * this._cellRangeY + _shiftY + this._trange,
                        x2: this._x.range * CHART_ZOOM_UNIT,
                        y2: (i + ((j + 1) / 5)) * this._cellRangeY + _shiftY + this._trange,
                        strokeDash: this._y.miliLine.strokeDash.map(el => el * CHART_ZOOM_UNIT),
                        strokeDashOffset: this._y.miliLine.strokeDashOffset * CHART_ZOOM_UNIT
                    });
                })
            }
        });

        if (_numofHeader > 0
            && this._isAllTypeOf(this._barX.text, Chart.Text)
            && this._barX.text.length > 0
        ) {
            let _textHeaderAxisY = this._barX.position === CHART_BARX_POSITION_ENUM.TOP ?
                (this._trange + paddingPerc[0] * CHART_ZOOM_UNIT * this._y.range) / 2 :
                (this._trange + paddingPerc[0] * CHART_ZOOM_UNIT * this._y.range) / 2 + (this._cellRangeY * this._y.numof) + (this._cellRangeY + this._barX.numof) / 2
            ;
            let _leftShiftDnm = this._lrange;
            let _sumLots = 0;
            this._barX.text.forEach(el => {
                let _fszHeader = (el.fontSize ? el.fontSize : this._barX.style.fontSize) * CHART_ZOOM_UNIT * 2;
                if (_sumLots > this._y.numof) {
                    return;
                }
                _sumLots = _sumLots + el.lot;

                if (el.backgroundColor && el.backgroundColor !== 'none') {
                    this._selector.addLayer({
                        type: "rectangle",
                        scale: 1 / CHART_ZOOM_UNIT,
                        fillStyle: el.backgroundColor,
                        x: this._calCoorByZoomUnit((_leftShiftDnm + _sumLots * this._cellRangeX + this._lrange) / 2),
                        y: this._barX.position === CHART_BARX_POSITION_ENUM.TOP ?
                            this._calCoorByZoomUnit(this._trange - (this._cellRangeY * this._barX.numof / 2)) :
                            this._calCoorByZoomUnit((2 * (this._trange + (this._cellRangeY * this._y.numof)) + (this._cellRangeY * this._barX.numof)) / 2),
                        width: this._cellRangeX * el.lot,
                        height: this._cellRangeY * this._barX.numof
                    })
                }

                this._selector.addLayer({
                    type: "text",
                    scale: 1 / CHART_ZOOM_UNIT,
                    fillStyle: el.fillStyle ? el.fillStyle : this._barX.style.fillStyle,
                    strokeStyle: el.strokeStyle ? el.strokeStyle : this._barX.style.strokeStyle,
                    strokeWidth: el.strokeWidth ? el.strokeWidth : this._barX.style.strokeWidth,
                    fontSize: _fszHeader,
                    fontFamily: (el.fontFamily ? el.fontFamily : this._barX.style.fontFamily),
                    x: this._calCoorByZoomUnit((_leftShiftDnm + _sumLots * this._cellRangeX + this._lrange) / 2),
                    y: this._calCoorByZoomUnit(_textHeaderAxisY),
                    text: el.value
                });
                
                _leftShiftDnm = _sumLots * this._cellRangeX + this._lrange;
            })
        }

        // X Coordinates
        [...Array(this._x.numof + 1).keys()].forEach(i => {
            let _shiftX = (i === 0 ? CHART_ZOOM_UNIT / 2 : (i === this._x.numof ? (-CHART_ZOOM_UNIT / 2) : 0)) + this._lrange;
            let _isOddLine = (!(i % 2 === 0)) && (this._x.odd.enable === true);
            let _oddProperties = _isOddLine ? {
                strokeDash: this._x.odd.strokeDash.map(el => el * CHART_ZOOM_UNIT),
                strokeDashOffset: this._x.odd.strokeDashOffset * CHART_ZOOM_UNIT
            } : {
                strokeDash: this._x.style.strokeDash.map(el => el * CHART_ZOOM_UNIT),
                strokeDashOffset: this._x.style.strokeDashOffset * CHART_ZOOM_UNIT
            };
            this._selector.addLayer({
                ...{
                    type: "line",
                    scale: 1 / CHART_ZOOM_UNIT,
                    strokeStyle: _isOddLine ? this._x.odd.strokeStyle : this._x.style.strokeStyle,
                    strokeWidth: (_isOddLine ? this._x.odd.strokeWidth : this._x.style.strokeWidth) * CHART_ZOOM_UNIT,
                    x1: i * this._cellRangeX + _shiftX,
                    y1: this._trange,
                    x2: i * this._cellRangeX + _shiftX,
                    y2: this._y.range * CHART_ZOOM_UNIT + this._trange
                },
                ..._oddProperties,
                ...this._jcanvasProperties.x
            });

            if (_numofHeader > 0 && this._barX.lines.enable === true) {
                let _yf_ = this._barX.position === CHART_BARX_POSITION_ENUM.TOP ?
                    (this._trange - (_numofHeader * this._cellRangeY)) :
                    (this._trange + _numofHeader * this._cellRangeY + this._cellRangeY * this._y.numof);
                let _ys_ = this._barX.position === CHART_BARX_POSITION_ENUM.TOP ?
                    this._trange :
                    (this._trange + this._cellRangeY * this._y.numof);
                if (i === 0 || i === this._x.numof) {
                    this._selector.addLayer({
                        type: "line",
                        scale: 1 / CHART_ZOOM_UNIT,
                        strokeStyle: this._barX.lines.strokeStyle,
                        strokeWidth: this._barX.lines.strokeWidth * CHART_ZOOM_UNIT,
                        x1: i * this._cellRangeX + _shiftX,
                        y1: _yf_,
                        x2: i * this._cellRangeX + _shiftX,
                        y2: _ys_
                    });
                } else {
                    let _countLot = 0;
                    let _stepLoop = 0;
                    if (Array.isArray(this._barX.lines.lotNumber) &&
                        this._barX.lines.lotNumber.length > 0 &&
                        this._isAllNumber(...this._barX.lines.lotNumber)
                    ) {
                        while(_countLot < this._x.numof) {
                            let _lot = this._barX.lines.lotNumber[_stepLoop % this._barX.lines.lotNumber.length];
                            _countLot = _countLot + _lot;
                            this._selector.addLayer({
                                type: "line",
                                scale: 1 / CHART_ZOOM_UNIT,
                                strokeStyle: this._barX.lines.strokeStyle,
                                strokeWidth: this._barX.lines.strokeWidth * CHART_ZOOM_UNIT,
                                x1:  _countLot * this._cellRangeX + _shiftX,
                                y1: _yf_,
                                x2: _countLot * this._cellRangeX + _shiftX,
                                y2: _ys_
                            });
                            _stepLoop++;
                        }
                    } else {
                        this._selector.addLayer({
                            type: "line",
                            scale: 1 / CHART_ZOOM_UNIT,
                            strokeStyle: this._barX.lines.strokeStyle,
                            strokeWidth: this._barX.lines.strokeWidth * CHART_ZOOM_UNIT,
                            x1: i * this._cellRangeX + _shiftX,
                            y1: _yf_,
                            x2: i * this._cellRangeX + _shiftX,
                            y2: _ys_
                        });
                    }
                }
            }
        })

        if (this._lrange > 0
            && Array.isArray(this._valueUnit)
        ) {
            let _Xtemp = 0;
            this._valueUnit.forEach((valU, idx) => {
                const {
                    numof: numofVU,
                    style: styleVU,
                    values: valuesVU,
                    showLast: showLastVU
                } = valU;
                if(this._isNumber(numofVU) && numofVU > 0) {
                    let fsz = 10 * CHART_ZOOM_UNIT * 2;
                    _Xtemp = _Xtemp + numofVU;
                    let _corX = (this._lrange  / this._sumValueUnitNumofs) * _Xtemp - ((this._lrange  / this._sumValueUnitNumofs) / 2);
                    if (this._isNumber(styleVU.fontSize) && styleVU.fontSize > 4) {
                        fsz = styleVU.fontSize * CHART_ZOOM_UNIT * 2;
                    }
                    if (this._isAllNumber(valuesVU[0], valuesVU[1]) && valuesVU.length <= 2) {
                        let start = valuesVU[0];
                        let step = valuesVU[1];
                        let lastVal = start + (step * (this._y.numof - 1));
                        if (showLastVU === true) {
                            this._selector.addLayer({
                                type: "text",
                                scale: 1 / CHART_ZOOM_UNIT,
                                fillStyle: styleVU.fillStyle,
                                strokeStyle: styleVU.strokeStyle,
                                strokeWidth: styleVU.strokeWidth,
                                fontSize: fsz,
                                fontFamily: styleVU.fontFamily,
                                x: this._calCoorByZoomUnit(_corX),
                                y: this._calCoorByZoomUnit(Math.max(this._trange - (fsz * 3 / 4), fsz * 3 / 4)),
                                text: lastVal + step + ""
                            });
                        }
                        [...Array(this._y.numof).keys()].forEach(i => {
                            this._selector.addLayer({
                                type: "text",
                                scale: 1 / CHART_ZOOM_UNIT,
                                fillStyle: styleVU.fillStyle,
                                strokeStyle: styleVU.strokeStyle,
                                strokeWidth: styleVU.strokeWidth,
                                fontSize: fsz,
                                fontFamily: styleVU.fontFamily,
                                x: this._calCoorByZoomUnit(_corX),
                                y: this._calCoorByZoomUnit(this._cellRangeY * (i + 1) - (fsz * 3 / 4) + this._trange),
                                text: lastVal - (i * step) + ""
                            });
                        })
                    } else {
                        valuesVU.slice(2, this._y.numof + 3).forEach((text, i) => {
                            let yCoordinates = i === this._y.numof ? (fsz / 2) : (this._y.range * CHART_ZOOM_UNIT) - this._cellRangeY * i - (fsz / 2) + this._trange;
                            this._selector.addLayer({
                                type: "text",
                                scale: 1 / CHART_ZOOM_UNIT,
                                fillStyle: styleVU.fillStyle,
                                strokeStyle: styleVU.strokeStyle,
                                strokeWidth: styleVU.strokeWidth,
                                fontSize: fsz,
                                fontFamily: styleVU.fontFamily,
                                x: this._calCoorByZoomUnit(_corX),
                                y: this._calCoorByZoomUnit(yCoordinates),
                                text: text + ""
                            });
                        })
                    }
                }
            });
        }

        if (this._lrange > 0 && this._borderValueUnit && this._isTypeOf(this._borderValueUnit, Chart.Style)) {
            let _numOfSumTemp = 0;
            let stepUnit = this._lrange / this._sumValueUnitNumofs;
            let _yf_ = this._trange;
            let _ys_ = this._barX.position === CHART_BARX_POSITION_ENUM.TOP ?
                (this._trange + this._cellRangeY * this._y.numof) :
                (this._trange + this._cellRangeY * this._y.numof);
            const {
                fillStyle: fillStyleBVU,
                strokeWidth: strokeWidthBVU
            } = this._borderValueUnit;
            this._valueUnit.forEach(valueUnit => {
                const { numof } = valueUnit;
                this._selector.addLayer({
                    type: 'line',
                    scale: 1 / CHART_ZOOM_UNIT,
                    strokeStyle: fillStyleBVU,
                    strokeWidth: strokeWidthBVU * CHART_ZOOM_UNIT,
                    x1: stepUnit * _numOfSumTemp,
                    y1: _yf_,
                    x2: stepUnit * _numOfSumTemp,
                    y2: _ys_
                })
                _numOfSumTemp = _numOfSumTemp + (this._isNumber(numof) ? numof : 0)
            });
            if (this._barX.fullBorders === true) {
                let _yf2_ = this._barX.position === CHART_BARX_POSITION_ENUM.TOP ?
                    (this._trange - (_numofHeader * this._cellRangeY)) :
                    (this._trange + _numofHeader * this._cellRangeY + this._cellRangeY * this._y.numof);
                let _ys2_ = this._barX.position === CHART_BARX_POSITION_ENUM.TOP ?
                    this._trange :
                    (this._trange + this._cellRangeY * this._y.numof);
                this._selector.addLayer({
                    type: 'line',
                    scale: 1 / CHART_ZOOM_UNIT,
                    strokeStyle: fillStyleBVU,
                    strokeWidth: strokeWidthBVU * CHART_ZOOM_UNIT,
                    x1: 0,
                    y1: _yf2_,
                    x2: 0,
                    y2: _ys2_
                })
            }
        }

        if (_numofHeader > 0 && this._lrange > 0 && this._isTypeOf(this._barX.vuText, Chart.Text)) {
            const {
                value = "Bởi am",
                fillStyle = "black",
                fontSize = 10,
                strokeStyle = "black",
                strokeWidth = "black",
                fontFamily = "Verdana, sans-serif",
                backgroundColor = "transparent"
            } = this._barX.vuText;
            let _fszHeader = fontSize * CHART_ZOOM_UNIT * 2;
            let _corX = this._lrange / 2;
            let _corY = this._barX.position === CHART_BARX_POSITION_ENUM.TOP ?
                    (this._trange - (_numofHeader * this._cellRangeY) + (_fszHeader * 3 / 4)) :
                    (this._trange + _numofHeader * this._cellRangeY + this._cellRangeY * this._y.numof) - (_fszHeader * 3 / 4);
            if (value != "") {
                this._selector.addLayer({
                    type: "text",
                    scale: 1 / CHART_ZOOM_UNIT,
                    fillStyle: fillStyle,
                    strokeStyle: strokeStyle,
                    strokeWidth: strokeWidth,
                    fontSize: _fszHeader,
                    fontFamily: fontFamily,
                    x: _corX / CHART_ZOOM_UNIT,
                    y: _corY / CHART_ZOOM_UNIT,
                    text: value + ""
                });
            }
        }

        this._selector.drawLayers();
        return this;
    }

    drawLineChart(dataChart = new Chart.DataChart({})) {
        if (!dataChart) {
            alert("Data is nil or undefined.");
            return;
        }
        let _groups = "drawLineChart" + (new Date().getTime());

        if (dataChart.constructor.name !== Chart.DataChart.getClassName()) {
            alert("Data format is not supported (DataChart).");
            return;
        }

        let points = {};
        let unit = 1;
        try {
            if (dataChart.unit.constructor.name === 'Number') {
                unit = Math.round(dataChart.unit * 100) / 100;
            } else {
                unit = Math.round(parseInt(dataChart.unit) * 100) / 100;
            }
        } catch (e) {
        }
        let _stepDC = this._cellRangeX * unit;
        let _id = dataChart.id;
        let _valU = this._valueUnit.find((el) => el.id === _id);
        if (dataChart.data && Array.isArray(dataChart.data)) {
            dataChart.data
                .slice(0, _stepDC + 1)
                .forEach((el, i) => {
                    points["val" + (i + 1)] = el;
                    let dt = -999;
                    try {
                        dt = parseFloat(el)
                    } catch (e) {
                    }

                    points["x" + (i + 1)] = this._lrange + (dataChart.startX * this._cellRangeX) + (i * _stepDC);
                    if (this._isUnitMapped) {
                        points["y" + (i + 1)] = (this._cellRangeY * this._y.numof) - ((dt - _valU.values[0]) * this._cellRangeY / _valU.values[1]) + this._trange;
                    } else {
                        points["y" + (i + 1)] = (this._cellRangeY * this._y.numof) - (dt * this._cellRangeY) + this._trange;
                    }
                })
        }

        this._selector.addLayer({
            ...{
                type: "line",
                groups: [_groups],
                scale: 1 / CHART_ZOOM_UNIT,
                strokeStyle: dataChart.style.strokeStyle,
                strokeWidth: dataChart.style.strokeWidth * CHART_ZOOM_UNIT,
                strokeDash: dataChart.style.strokeDash.map(el => el * CHART_ZOOM_UNIT),
                cursors: "zoom-in",
                strokeDashOffset: dataChart.style.strokeDashOffset * CHART_ZOOM_UNIT
            },
            ...this._animationLineChart(dataChart, _groups),
            ...points
        });

        if (Object.keys(CHART_POINT_STYLE_ENUM).indexOf(dataChart.pointStyle.type) >= 0) {
            if (dataChart.pointStyle.type === CHART_POINT_STYLE_ENUM.CIRCLE ||
                dataChart.pointStyle.type === CHART_POINT_STYLE_ENUM.RECTANGLE ||
                dataChart.pointStyle.type === CHART_POINT_STYLE_ENUM.TRIANGLE) {
                if (dataChart.pointStyle.size &&
                    Array.isArray(dataChart.pointStyle.size) &&
                    dataChart.pointStyle.size.length >= 2 &&
                    this._isAllNumber(...dataChart.pointStyle.size.slice(0, 2))
                ) {
                    Object.keys(points)
                        .filter(el => el.startsWith("x"))
                        .forEach(el => {
                            let _groupPoint = "Point_" + _groups + "_" + el;
                            if (dataChart.pointStyle.type === CHART_POINT_STYLE_ENUM.CIRCLE) {
                                this._selector.addLayer({
                                    ...{
                                        type: "ellipse",
                                        groups: ["Point_" + _groups, _groupPoint],
                                        scale: 1 / CHART_ZOOM_UNIT,
                                        fillStyle: dataChart.pointStyle.style.strokeStyle,
                                        strokeWidth: dataChart.pointStyle.style.strokeWidth * CHART_ZOOM_UNIT,
                                        x: this._calCoorByZoomUnit(points[el]),
                                        y: this._calCoorByZoomUnit(points[el.replace("x", "y")]),
                                        width: dataChart.pointStyle.size[0] * CHART_ZOOM_UNIT,
                                        height: dataChart.pointStyle.size[1] * CHART_ZOOM_UNIT
                                    }, ...this._animationValuePoint(dataChart.pointStyle.zooming, _groups, _groupPoint)
                                })
                            } else if (dataChart.pointStyle.type === CHART_POINT_STYLE_ENUM.RECTANGLE) {
                                this._selector.addLayer({
                                    ...{
                                        type: "rectangle",
                                        groups: ["Point_" + _groups, _groupPoint],
                                        scale: 1 / CHART_ZOOM_UNIT,
                                        fillStyle: dataChart.pointStyle.style.strokeStyle,
                                        x: this._calCoorByZoomUnit(points[el]),
                                        y: this._calCoorByZoomUnit(points[el.replace("x", "y")]),
                                        width: dataChart.pointStyle.size[0] * CHART_ZOOM_UNIT,
                                        height: dataChart.pointStyle.size[1] * CHART_ZOOM_UNIT
                                    }, ...this._animationValuePoint(dataChart.pointStyle.zooming, _groupPoint)
                                })
                            } else if (dataChart.pointStyle.type === CHART_POINT_STYLE_ENUM.TRIANGLE) {
                                this._selector.addLayer({
                                    ...{
                                        type: 'polygon',
                                        strokeStyle: 'black',
                                        radius: 50,
                                        sides: 3,
                                        groups: ["Point_" + _groups, _groupPoint],
                                        scale: 1 / CHART_ZOOM_UNIT,
                                        fillStyle: dataChart.pointStyle.style.strokeStyle,
                                        x: this._calCoorByZoomUnit(points[el]),
                                        y: this._calCoorByZoomUnit(points[el.replace("x", "y")]),
                                        strokeWidth: dataChart.pointStyle.size[0] * CHART_ZOOM_UNIT,
                                    }, ...this._animationValuePoint(dataChart.pointStyle.zooming, _groupPoint)
                                })
                            }
                            this._drawValuePoint(dataChart, points, el, _groups, _groupPoint);
                        })
                }
            } else if (dataChart.pointStyle.type === CHART_POINT_STYLE_ENUM.SYMBOL) {
                if (dataChart.pointStyle.size &&
                    Array.isArray(dataChart.pointStyle.size) &&
                    dataChart.pointStyle.size.length >= 2 &&
                    this._isNumber(dataChart.pointStyle.size[0]) &&
                    dataChart.pointStyle.size[1].length >= 1
                ) {
                    Object.keys(points)
                        .filter(el => el.startsWith("x"))
                        .forEach(el => {
                            let _groupPoint = "Point_" + _groups + "_" + el;
                            this._selector.addLayer({
                                ...{
                                    type: "text",
                                    groups: ["Point_" + _groups, _groupPoint],
                                    scale: 1 / CHART_ZOOM_UNIT,
                                    fillStyle: dataChart.pointStyle.style.strokeStyle,
                                    strokeStyle: dataChart.pointStyle.style.strokeStyle,
                                    strokeWidth: dataChart.pointStyle.style.strokeWidth,
                                    fontSize: dataChart.pointStyle.style.fontSize * CHART_ZOOM_UNIT * 2,
                                    fontFamily: dataChart.pointStyle.style.fontFamily,
                                    x: this._calCoorByZoomUnit(points[el]),
                                    y: this._calCoorByZoomUnit(points[el.replace("x", "y")]),
                                    text: (dataChart.pointStyle.size[1] + "").charAt(0)
                                }, ...this._animationValuePoint(dataChart.pointStyle.zooming, _groups, _groupPoint)
                            })
                            this._drawValuePoint(dataChart, points, el, _groups, _groupPoint);
                        })
                }
            }
        }
        this._selector.drawLayers();
        return this;
    }

    drawRangeChart(dataChart = new Chart.DataChart({})) {
        if (!dataChart) {
            alert("Data is nil or undefined.");
            return;
        }
        let _groups = "drawRangeChart" + (new Date().getTime());

        if (dataChart.constructor.name !== Chart.DataChart.getClassName()) {
            alert("Data format is not supported (DataChart).");
            return;
        }

        let _unit = 1;
        try {
            if (dataChart.unit.constructor.name === 'Number') {
                _unit = Math.round(dataChart.unit * 100) / 100;
            } else {
                _unit = Math.round(parseInt(dataChart.unit) * 100) / 100;
            }
        } catch (e) {
        }
        let _stepDC = this._cellRangeX * _unit;
        let _id = dataChart.id;
        let _valU = this._valueUnit.find((el) => el.id === _id);
        if (dataChart.data && Array.isArray(dataChart.data) && this._isAllTypeOf(dataChart.data, Chart.RangeValue)) {
            dataChart.data
                .slice(0, _stepDC + 1)
                .forEach((el, i) => {
                    let dt_start = -999;
                    let dt_end = -999;
                    try { dt_start = parseFloat(el.start) } catch (e) {}
                    try { dt_end = parseFloat(el.end) } catch (e) {}
                    this._selector.addLayer({
                        type: "line",
                        groups: [_groups],
                        scale: 1 / CHART_ZOOM_UNIT,
                        fillStyle: dataChart.style.fillStyle,
                        strokeStyle: dataChart.style.strokeStyle,
                        strokeWidth: dataChart.style.strokeWidth * CHART_ZOOM_UNIT,
                        strokeDash: dataChart.style.strokeDash.map(el => el * CHART_ZOOM_UNIT),
                        strokeDashOffset: dataChart.style.strokeDashOffset * CHART_ZOOM_UNIT,
                        startArrow: true,
                        endArrow: true,
                        arrowRadius: dataChart.style.arrowRadius * CHART_ZOOM_UNIT,
                        arrowAngle: dataChart.style.arrowAngle,
                        x1: this._lrange + (dataChart.startX * this._cellRangeX) + (i * _stepDC),
                        y1: (this._cellRangeY * this._y.numof) - ((dt_start - _valU.values[0]) * this._cellRangeY / _valU.values[1]) + this._trange,
                        x2: this._lrange + (dataChart.startX * this._cellRangeX) + (i * _stepDC),
                        y2: (this._cellRangeY * this._y.numof) - ((dt_end - _valU.values[0]) * this._cellRangeY / _valU.values[1]) + this._trange
                    });
                })
        }
        this._selector.drawLayers();
        return this;
    }

    drawLineByGridUnit(coordinates = [], style = new Chart.Style({})) {
        if (!coordinates || !Array.isArray(coordinates) || !this._isAllTypeOf(coordinates, Chart.Coordinate)) {
            console.error("Parameters is not type of Coordinates array")
            return this;
        }

        let _startRootX = this._lrange;
        let _startRootY = this._trange + this._y.numof * this._cellRangeY;
        let _index = 1;
        let _points = {};

        coordinates.forEach(coor => {
            if (coor._x > this._x.numof || coor._y > this._y.numof || Math.min(coor._x, coor._y) < 0) {
                return;
            }

            _points["x" + _index] = _startRootX + coor._x * this._cellRangeX;
            _points["y" + _index] = _startRootY - (coor._y * this._cellRangeY);
            _index++;
        })

        this._selector.addLayer({
            ...{
                type: "line",
                scale: 1 / CHART_ZOOM_UNIT,
                strokeStyle: style.strokeStyle,
                strokeWidth: style.strokeWidth * CHART_ZOOM_UNIT,
                strokeDash: style.strokeDash.map(el => el * CHART_ZOOM_UNIT),
                strokeDashOffset: style.strokeDashOffset * CHART_ZOOM_UNIT
            },
            ..._points})
        this._selector.drawLayers();
        return this;
    }

    clearLineChart() {
        this._selector.clearCanvas();
        this.drawGrid();
        return this;
    }

    clearAll() {
        this._selector.clearCanvas();
        return this;
    }

    _animationValuePoint(zooming, _groups, _groupPoint){
        if (zooming !== true) {
            return {};
        }
        return {
            mouseover: (layer) => {
                let _groupPointFuncTimeout = this._valuePointMouseOverWorking[_groupPoint];
                if (_groupPointFuncTimeout || this._lineChartHighlight.indexOf(_groups) >= 0) {
                    return;
                }
                this._valuePointMouseOverWorking[_groupPoint] = setTimeout(() => {
                    this._selector.animateLayerGroup(_groupPoint, {
                        scale: 1.5 / CHART_ZOOM_UNIT
                    }, 200);
                    this._valuePointMouseOverWorking[_groupPoint] = undefined;
                }, 200);
            },
            mouseout: (layer) => {
                let _groupPointFuncTimeout = this._valuePointMouseOverWorking[_groupPoint];
                if (_groupPointFuncTimeout) {
                    clearTimeout(_groupPointFuncTimeout);
                    this._valuePointMouseOverWorking[_groupPoint] = undefined;
                } else {
                    this._selector.animateLayerGroup(_groupPoint, {
                        scale: 1 / CHART_ZOOM_UNIT
                    }, 200);
                }
            },
            cursors: {
                mouseover: 'pointer'
            }
        }
    }

    _animationLineChart(dataChart, _groups) {
        if (dataChart.zooming !== true) {
            return {};
        }
        return {
            click: () => {
                if (this._lineChartClickWorking) {
                    return;
                }
                let _idx = this._lineChartHighlight.indexOf(_groups);
                this._lineChartClickWorking = true;
                if (_idx >= 0) {
                    this._lineChartHighlight.splice(_idx, 1);
                    this._selector.animateLayerGroup(_groups, {
                        strokeWidth: dataChart.style.strokeWidth * CHART_ZOOM_UNIT
                    }, 400);
                    this._selector.animateLayerGroup("Point_" + _groups, {
                        scale: 1 / CHART_ZOOM_UNIT
                    }, 400);
                    this._selector.animateLayerGroup("ValuePoint_" + _groups, {
                        fontSize: dataChart.pointStyle.showValue.style.fontSize * CHART_ZOOM_UNIT * 2
                    }, {
                        duration: 400,
                        easing: 'swing',
                        complete: (layer) => {
                            setTimeout(() => {
                                this._lineChartClickWorking = false;
                            }, 200)
                        }
                    });
                } else {
                    this._lineChartHighlight.push(_groups);
                    this._selector.animateLayerGroup(_groups, {
                        strokeWidth: dataChart.style.strokeWidth * CHART_ZOOM_UNIT * 2
                    }, 400);
                    this._selector.animateLayerGroup("Point_" + _groups, {
                        scale: 1.5 / CHART_ZOOM_UNIT
                    }, 400);
                    this._selector.animateLayerGroup("ValuePoint_" + _groups, {
                        fontSize: dataChart.pointStyle.showValue.style.fontSize * CHART_ZOOM_UNIT * 3
                    }, {
                        duration: 400,
                        easing: 'swing',
                        complete: (layer) => {
                            setTimeout(() => {
                                this._lineChartClickWorking = false;
                            }, 200)
                        }
                    });
                }
            },
            cursors: {
                mouseover: 'zoom-in'
            }
        }
    }

    _drawValuePoint(dataChart = new Chart.DataChart, points = [], el = "_", _groups, _groupPoint) {
        if (dataChart.pointStyle.showValue.enable === true) {
            this._selector.addLayer({
            ...{
                type: "text",
                    groups: ["ValuePoint_" + _groups, _groupPoint],
                scale: 1 / CHART_ZOOM_UNIT,
                fillStyle: dataChart.pointStyle.showValue.style.fillStyle,
                strokeStyle: dataChart.pointStyle.showValue.style.strokeStyle,
                strokeWidth: dataChart.pointStyle.showValue.style.strokeWidth,
                fontSize: dataChart.pointStyle.showValue.style.fontSize * CHART_ZOOM_UNIT * 2,
                fontFamily: dataChart.pointStyle.showValue.style.fontFamily,
                x: this._calCoorByZoomUnit(points[el]),
                y: this._calCoorByZoomUnit(points[el.replace("x", "y")] - (12 * CHART_ZOOM_UNIT * 1.3)),
                text: points[el.replace("x", "val")] + ""
            }, ...this._animationValuePoint(dataChart.pointStyle.zooming, _groups, _groupPoint)})
        }
    }

    _calCoorByZoomUnit(coor) {
        return coor / CHART_ZOOM_UNIT;
    }

    _isNumber(input) {
        if (input) {
            return input.constructor.name === 'Number';
        } else if (input === 0) {
            return true;
        } else {
            return false;
        }
    }

    _isAllNumber(...inputs) {
        return !inputs.some(el => !this._isNumber(el));
    }

    _isTypeOf(obj, type) {
        if (!type || typeof type['getClassName'] !== 'function') {
            return false;
        }
        if (obj) {
            return obj.constructor.name === type.getClassName();
        } else {
            return false;
        }
    }

    _isAllTypeOf(objArray, type) {
        if (!type || typeof type['getClassName'] !== 'function') {
            return false;
        }
        if (objArray && Array.isArray(objArray)) {
            return !objArray.some(el => el.constructor.name !== type.getClassName());
        } else {
            return false;
        }
    }

    static getClassName() {
        return 'Chart';
    }

    getClassName() {
        return Chart.getClassName();
    }

    static DataChart = class {
        constructor(
            {
                id= "",
                data = [],
                config= {},
                style = new Chart.Style({}),
                pointStyle = {}
            }
        ) {
            this._data = data;
            this._id = id;
            let {
                unit: unit = 1,
                startX: startX = 0,
                zooming: zooming = false
            } = config || {};
            this._startX = startX;
            this._unit = unit;
            this._zooming = zooming;
            let {
                type: type = CHART_POINT_STYLE_ENUM.CIRCLE,
                style: styleP = new Chart.Style({}),
                width: width = 10,
                size: size = [10, 10],
                showValue: showValue = {},
                zooming: zoomingP
                // jcanvasProperties: jcanvasPropertiesP = {}
            } = pointStyle || {};

            const {
                enable: venable = false,
                style: pv_style = new Chart.Style({
                    fontSize: 10,
                    fontFamily: 'Verdana, sans-serif',
                    fillStyle: '#000',
                    strokeStyle: '#000',
                    strokeWidth: 3
                }),
            } = showValue;

            this._setupStyle(style)
                ._setupPoint({type, style: styleP, size, zooming: zoomingP})
                ._setupPoint_ShowValue({enable: venable, style: pv_style});
            console.log(this);
        };

        _setupStyle(style) {
            this._style = style
            return this;
        }

        _setupPoint({type, style, size, zooming}) {
            this._pointStyle = {type, style, size, zooming}
            return this;
        }

        _setupPoint_ShowValue({enable, style}) {
            this._pointStyle.showValue = {enable, style}
            return this;
        }

        get zooming() {
            return this._zooming;
        }

        set zooming(value) {
            this._zooming = value;
        }

        get pointStyle() {
            return this._pointStyle;
        }

        set pointStyle(value) {
            this._pointStyle = value;
        }

        get data() {
            return this._data;
        }

        set data(value) {
            this._data = value;
        }

        get unit() {
            return this._unit;
        }

        set unit(value) {
            this._unit = value;
        }

        get startX() {
            return this._startX;
        }

        set startX(value) {
            this._startX = value;
        }


        get style() {
            return this._style;
        }

        set style(value) {
            this._style = value;
        }

        static getClassName() {
            return 'DataChart';
        }


        get id() {
            return this._id;
        }

        getClassName() {
            return Chart.DataChart.getClassName();
        }
    }

    static Text = class {
        get backgroundColor() {
            return this._backgroundColor;
        }

        set backgroundColor(value) {
            this._backgroundColor = value;
        }
        get lot() {
            return this._lot;
        }

        set lot(value) {
            this._lot = value;
        }
        get value() {
            return this._value;
        }

        set value(value) {
            this._value = value;
        }
        get fontSize() {
            return this._fontSize;
        }

        set fontSize(value) {
            this._fontSize = value;
        }

        get fontFamily() {
            return this._fontFamily;
        }

        set fontFamily(value) {
            this._fontFamily = value;
        }

        get fillStyle() {
            return this._fillStyle;
        }

        set fillStyle(value) {
            this._fillStyle = value;
        }

        get strokeStyle() {
            return this._strokeStyle;
        }

        set strokeStyle(value) {
            this._strokeStyle = value;
        }

        get strokeWidth() {
            return this._strokeWidth;
        }

        set strokeWidth(value) {
            this._strokeWidth = value;
        }
        constructor(
            {
                fontSize,
                fontFamily,
                fillStyle,
                strokeStyle,
                strokeWidth,
                lot =  1,
                value = "[-]",
                backgroundColor = 'none'
            }
        ){
            this._fontSize = fontSize;
            this._fontFamily = fontFamily;
            this._fillStyle = fillStyle;
            this._strokeStyle = strokeStyle;
            this._strokeWidth = strokeWidth;
            this._value = value;
            this._lot = lot;
            this._backgroundColor = backgroundColor;
        }
        static getClassName() {
            return 'Text';
        }

        getClassName() {
            return Chart.Text.getClassName();
        }
    }

    static OddLine = class {
        get enable() {
            return this._enable;
        }

        set enable(value) {
            this._enable = value;
        }

        get strokeStyle() {
            return this._strokeStyle;
        }

        set strokeStyle(value) {
            this._strokeStyle = value;
        }

        get strokeWidth() {
            return this._strokeWidth;
        }

        set strokeWidth(value) {
            this._strokeWidth = value;
        }

        get strokeDash() {
            return this._strokeDash;
        }

        set strokeDash(value) {
            this._strokeDash = value;
        }

        get strokeDashOffset() {
            return this._strokeDashOffset;
        }

        set strokeDashOffset(value) {
            this._strokeDashOffset = value;
        }
        constructor({enable, strokeStyle, strokeWidth, strokeDash, strokeDashOffset}){
            this._enable = enable || false;
            this._strokeStyle = strokeStyle || '#7F7F83';
            this._strokeWidth = strokeWidth || 1;
            this._strokeDash = strokeDash || [5];
            this._strokeDashOffset = strokeDashOffset || 2;
        }

        static getClassName() {
            return 'OddLine';
        }

        getClassName() {
            return Chart.OddLine.getClassName();
        }
    }

    static Lines = class {
        constructor({enable, lotNumber, lotColor, fillStyle, strokeStyle, strokeWidth}){
            this._enable = enable || false;
            this._lotNumber = lotNumber || [1];
            this._lotColor = lotColor || ['white'];
            this._fillStyle = fillStyle || '#000';
            this._strokeStyle = strokeStyle || '#000';
            this._strokeWidth = strokeWidth || 1;
        }

        get enable() {
            return this._enable;
        }

        set enable(value) {
            this._enable = value;
        }

        get lotNumber() {
            return this._lotNumber;
        }

        set lotNumber(value) {
            this._lotNumber = value;
        }

        get lotColor() {
            return this._lotColor;
        }

        set lotColor(value) {
            this._lotColor = value;
        }

        get fillStyle() {
            return this._fillStyle;
        }

        set fillStyle(value) {
            this._fillStyle = value;
        }

        get strokeStyle() {
            return this._strokeStyle;
        }

        set strokeStyle(value) {
            this._strokeStyle = value;
        }

        get strokeWidth() {
            return this._strokeWidth;
        }

        set strokeWidth(value) {
            this._strokeWidth = value;
        }
        static getClassName() {
            return 'Lines';
        }

        getClassName() {
            return Chart.Lines.getClassName();
        }
    }

    static Style = class {
        get fillStyle() {
            return this._fillStyle;
        }

        set fillStyle(value) {
            this._fillStyle = value;
        }

        get strokeStyle() {
            return this._strokeStyle;
        }

        set strokeStyle(value) {
            this._strokeStyle = value;
        }

        get strokeWidth() {
            return this._strokeWidth;
        }

        set strokeWidth(value) {
            this._strokeWidth = value;
        }

        get strokeDash() {
            return this._strokeDash;
        }

        set strokeDash(value) {
            this._strokeDash = value;
        }

        get strokeDashOffset() {
            return this._strokeDashOffset;
        }

        set strokeDashOffset(value) {
            this._strokeDashOffset = value;
        }

        get fontFamily() {
            return this._fontFamily;
        }

        set fontFamily(value) {
            this._fontFamily = value;
        }

        get fontSize() {
            return this._fontSize;
        }

        set fontSize(value) {
            this._fontSize = value;
        }

        get arrowRadius() {
            return this._arrowRadius;
        }

        get arrowAngle() {
            return this._arrowAngle;
        }

        constructor(
            {
                fillStyle, strokeStyle, strokeWidth,
                strokeDash, strokeDashOffset,
                fontFamily, fontSize, arrowRadius, arrowAngle
            }) {
            this._fillStyle = fillStyle || '#000';
            this._strokeStyle = strokeStyle || '#000';
            this._strokeWidth = strokeWidth || 1;
            this._strokeDash = strokeDash || [0];
            this._strokeDashOffset = strokeDashOffset || 0;
            this._fontFamily = fontFamily || 'Verdana, sans-serif';
            this._fontSize = fontSize || 5;
            this._arrowRadius = arrowRadius || 10;
            this._arrowAngle = arrowAngle || 90;
            this._arrowRadius = arrowRadius;
            this._arrowAngle = arrowAngle;
        }
        static getClassName() {
            return 'Style';
        }

        getClassName() {
            return Chart.Style.getClassName();
        }
    }

    static AxisConfig = class {
        get range() {
            return this._range;
        }

        get numof() {
            return this._numof;
        }

        get style() {
            return this._style;
        }

        get odd() {
            return this._odd;
        }

        get miliLine() {
            return this._miliLine;
        }

        constructor({range = 800, numof = 5, style = new Chart.Style({}), odd = new Chart.OddLine({}), miliLine = new Chart.OddLine({})}) {
            this._range = range;
            this._numof = numof;
            this._style = style;
            this._odd = odd;
            this._miliLine = miliLine;
        }

        static getClassName() {
            return 'AxisConfig';
        }

        getClassName() {
            return Chart.AxisConfig.getClassName();
        }
    }

    static Coordinate = class {
        get x() {
            return this._x;
        }

        set x(value) {
            this._x = value;
        }

        get y() {
            return this._y;
        }

        set y(value) {
            this._y = value;
        }
        constructor({x, y}) {
            this._x = x;
            this._y = y;
        }

        static getClassName() {
            return 'Coordinate';
        }

        getClassName() {
            return Chart.Coordinate.getClassName();
        }
    }

    static RangeValue = class {
        constructor({ start, end }) {
            this._start = start;
            this._end = end;
        }

        get start() {
            return this._start;
        }

        get end() {
            return this._end;
        }

        static getClassName() {
            return 'RangeValue';
        }

        getClassName() {
            return Chart.RangeValue.getClassName();
        }
    }
}

class AxisY extends Chart.AxisConfig {
    constructor({range, numof, style, odd, padding, miliLine}) {
        super({
            range,
            numof,
            style: style || new Chart.Style({}),
            odd: odd || new Chart.OddLine({}),
            miliLine: miliLine || new Chart.OddLine({})
        });
        this.padding = padding || [0, 0]
    }
}

class AxisX extends Chart.AxisConfig {
    constructor({range, numof, style, odd}) {
        super({
            range,
            numof,
            style: style || new Chart.Style({}),
            odd : odd || new Chart.OddLine({})}
        );
    }
}

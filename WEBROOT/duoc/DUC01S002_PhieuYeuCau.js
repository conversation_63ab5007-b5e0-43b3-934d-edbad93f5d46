/*
Mã màn hình  : DUC47T002
File mã nguồn : DUC47T002_PhieuYeuCau.js
<PERSON><PERSON><PERSON> đích  : Danh sach phieu yeu cau 
Người lập trình	<PERSON> cập nhật  Ghi chú
SONNT	- 06092016 - Comment
 */
function DUC01S002_PhieuYeuCau(_opt) {
    this.load = doLoad;
    this.opt = $.extend({}, _opt);
    var that = this;
    var _btnDuyet = 'Duyệt';
    var _btnNhapBu = 'YC nhập bù';
    var _btnNhapKho = 'Nhập kho';

    var TAOMOI = 1;
    var CHONHAP = 2;
    var DAHUY = 3;
    var KETTHUC = 4;
    var CHODUYET = 5;
    var DADUYET = 6;
    var KHONGDUYET = 7;
    var YEUCAU = 8;
    var XULY = 9;
    var DADUYETYC = 10;
    var TUCHOIYC = 11;
    var DAXUAT = 12;
    var DANHAP = 13;
    var DATAOPHIEU = 14;
    var checkDKin = '';
    // tuan them de check hien thi man hinh du tru tren nguyen trai
    var PHARMA_GIAODIEN_NHAP_NCC = 0;
    var PHARMA_BC_NHAP_KHO = 0;
    var PHARMA_BC_XUAT_KHO = 0;
    var PHARMA_KETNOI_CONG_BYT = "";
    var PHARMA_SHOWKHOAPHONG = "";
    var PHARMA_SHOW_CHANDOAN_BN = 0;
    var PHARMA_SHOW_COLOR_VATTU_BN = 0;
    var canhBao = [];
    var canhBaoloaithuoc = '';
    // daidv: Them cau hinh cho in phieu phieu linh va hoantra da duyet -- L2PT-2711
    var PHARMA_IN_PHIEULINH_HOANTRA = 0;

    // daidv: Them cau hinh cho in phieu phieu linh va hoantra da duyet noi tru -- L2PT-4523
    var PHARMA_IN_PHIEUYC_NOITRU = 0;

    // daidv: Them cau hinh cho hien thi go duyet button khi phieu nhap xuat chua duyet -- L2PT-5165
    var PHARMA_SHOW_BTHUYDUYET_PNX = 0;

    // daidv: Them cau hinh cho hien thi popup tao phieu nhap/xuat sau khi duyet DUC35T001_ThongTinPhieu -- L2PT-5165
    var PHARMA_HIENTHI_POPUP_TUDONG = 1;

    // daidv: Them cau hinh load phieu da duyet lam san- NHIETDOI -- L2PT-5684
    var PHARMA_DUYET_DLS_BOSUNG_TUTRUC = 0;

    var PHARMA_HIENTHIKHO_THEOKHOAPHONG = '';

    // daidv: Them cau hinh chuyen man hinh duyet co quan ly co so hoat chat
    var PHARMA_CHUYEN_DUYET_COSOHOATCHAT = 0;

    // daidv: Them cau hinh chuyen doi sang thoi gian tim kiem moi dd/mm/yyyy hh:mm:ss
    var PHARMA_FORMAT_NEWTIME_SEARCH = 0;

    // daidv: Them cau hinh hien thi phieu linh cha cho phieu linh tong hop noi tru
    var PHARMA_SHOW_PHIEUCHA_NTU = 0;

    // daidv: Them cau hinh check lien thong HMIS
    var PHARMA_CHECK_LIENTHONG_HMIS = 0;

    // SONDN
//	var THUOC_HIENTHI_GOIKHAM = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_HIENTHI_GOIKHAM_THUOC');
    // END SONDN
    //Bachnv: cau hinh benh nhan ra vien cho hinhthuc =12
    var PHARMA_HIENTHI_YLENH_BN_RAVIEN = 0;
    // cấu hình hiển thị button gọi bệnh nhân
    var PHARMA_HIENTHI_GOIBN = 0;
    // Cấu hình lược bót thông tin trong phiếu yêu cầu
    var PHARMA_LUOCBOT_PHIEUYC = 0;
    var PHARMA_DUYET_THUOC_CHAN = 0;
    var PHARMA_IN_CHUNG_DONGY_NOITRU = 0;
    var PHARMA_SHOW_INPHIEULINH_DUYETPHAT = 0;

    var PHARMA_TUDONG_TAOPHIEUNHAPXUAT = 0;
    var PHARMA_CHONTATCAKHO = '';
    var PHARMA_HIENTHI_GIABHYT = 0;
    var PHARMA_CANHBAOHSD_HOANTRATUTRUC = '';
    var PHARMA_THEMCHUADUYETKT = '';
    var PHARMA_DUYETNHIEUPHIEU = '';
    var PHARMA_TUDONG_TAIFILE = 0;
    var PHARMA_DSPHIEU_THEOKHOA = 0;
    var PHARMA_DUTRUKHO_NEW = 0;
    var PHARMA_XUATKHAC_LYDOXUAT = 0;
    var PHARMA_XUATKHAC_VATTU = 0;
    var cauHinhNutInYHCT = 0;
    var PHARMA_KHOA_TVT_NEW = 0;
    var PHARMA_IN_PHIEULINH_MANHOM = 0;
    var PHARMA_NGUONXUAT_THKP = 0;
    var PHARMA_KHONGIN_PHIEUTAOMOI = 0;
    var PHARMA_NHAPBUKHAC_KHONGTHAU = 0;
    var PHARMA_HIENTHI_TIEN_SOLE = 0;
    var PHARMA_GIAODIEN_NCC_NHATHUOC = '0';
    var PHARMA_SONGAY_GODUYET = -1;
    var PHARMA_LAYDL_BNHNI = '0';
    var PHARMA_HIENTHI_GOIBENHNHAN_KB = 0;
    var VPI_XACNHAN_BANTHUOC = '0';
    var PHARMA_HIENPHIEUXUAT = '0';
    var PHARMA_BOSOTHAPPHAN = '0';
    var PHARMA_BOSOTHAPPHAN_TT = '0';
    var PHARMA_SHOW_COLOR_HSD = '0';
    var hCode = '0';
    var PHARMA_HUYPHIEU_GIUAO_DC = 0;
    var PHARMA_SONGAY_TIMKIEM_CHUCNANG = '';

    var dsHoaDon = [];
    var dsThem = [];
    var _sole = 2;
    // 10/03/2021
    var optKySo = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
    var causer = "";
    var capassword = "";
    var flagLogin = false;

    var HIS_API_MODE = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_API_MODE');
    var HIS_API_TIMEOUT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_API_TIMEOUT');

    function doLoad() {
        $("#btnBoKhoa").hide();
        _initControl();
        _bindEvent();
        GetThamSoKhoiTao();
        if (optKySo == "1" || optKySo == "2") {
            $("#btnKySo").show();
            $("#btnHuyKySo").show();
        } else {
            $("#btnKySo").hide();
            $("#btnHuyKySo").hide();
        }

        $(document).ready(function () {
            var callback = function () {
                if ($.active !== 0) {
                    setTimeout(callback, '20');
                    return;
                }
                //_loadDSPhieu();
                if (that.opt.lp == "3" && that.opt.ht == "9" && that.opt.gd == "THUOC" && PHARMA_DUYET_DLS_BOSUNG_TUTRUC == "1" && that.opt.td == "9") {
                    //if(that.opt.td == "9"){
                    _loadDSPhieuLamSan();
//					}else{
//						_loadDSPhieuLS_DaDuyet();
//					}
                } else if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
                checkRolePhieuIn();

            };
            callback();

        });
        checkRole('toolbarIdtbHoanTra');
        if (that.opt.ht == '15') {
            $('[name=divBanThuoc]').show();
            $("#lbBenhNhan").text("Khách hàng");
            $("#lbGhiChu").text("Địa chỉ");

        } else $('[name=divBanThuoc]').hide();

        if (that.opt.ht == '13') {
            $('[name=divChanDoan]').show();
        } else $('[name=divChanDoan]').hide();

        if (that.opt.ht == '12' && (that.opt.hospitalId == '30300' || that.opt.hospitalId == '30980' || that.opt.hospitalId == '35099' || that.opt.hospitalId == '33777')) {
            $('[name=divSoPhieu]').show();
        }

// 		checkRole('toolbarIdtbYCXuat');

    }

    function _initControl() {
        that.hinhThuc = _stringToList(that.opt.ht); //DucTT20181105
        var checkin = ['PHARMA_KHOA_INPHIEU_DADUYET'];
        checkDKin = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", checkin.join('$'));
        var par_canhBao = ['PHARMA_CANHBAO_DUYET_LOAITHUOC'];
        var cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", par_canhBao
            .join('$'));
        canhBao = cauhinh.split(',');
        var par_AnHien = ['HIS_HIENTHI_GOIKHAM_THUOC']
        var AnHien = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_AnHien.join('$'));
        if (AnHien == '1' && that.opt.ht == 13) {
            $("#dvCall5").show();
        } else $("#dvCall5").hide();

        var par_TimKho = ['PHARMA_O_TIMKIEMKHO']
        var PHARMA_O_TIMKIEMKHO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_TimKho.join('$'));
        if (PHARMA_O_TIMKIEMKHO == '1') {
            $("#cboKho").css("width", "113%");
            $("#divTimKho").show();
        }
        PHARMA_KETNOI_CONG_BYT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_KETNOI_CONG_BYT');
        var username = that.opt.user_name.toString();
//		$('#txtNgayBD').val(moment().startOf('month').format('DD/MM/YYYY'));
        $('#txtNgayBD').val(moment().format('DD/MM/YYYY'));
        $('#txtNgayKT').val(moment().format('DD/MM/YYYY'));

        var tungay = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01S002_GETTUNGAY", '');
        if (tungay != null || typeof tungay === "undefined") $('#txtNgayBD').val(tungay);
        var _par_ht = ['PHARMA_HIENTHIKHO_THEOKHOAPHONG'];
        PHARMA_HIENTHIKHO_THEOKHOAPHONG = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", _par_ht.join('$'));
        var _par_ncc = ['PHARMA_GIAODIEN_NHAP_NCC'];
        PHARMA_GIAODIEN_NHAP_NCC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_ncc.join('$'));
        var _par_bcnk = ['PHARMA_BC_NHAP_KHO'];
        PHARMA_BC_NHAP_KHO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA",
            _par_bcnk.join('$'));
        var _par_bcxk = ['PHARMA_BC_XUAT_KHO'];
        PHARMA_BC_XUAT_KHO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA",
            _par_bcxk.join('$'));

        var _par_inphieu = ['PHARMA_IN_PHIEULINH_HOANTRA'];
        PHARMA_IN_PHIEULINH_HOANTRA = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_inphieu.join('$'));

        var _par_inphieu = ['PHARMA_IN_PHIEUYC_NOITRU'];
        PHARMA_IN_PHIEUYC_NOITRU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_inphieu.join('$'));

        var _par_showbt = ['PHARMA_SHOW_BTHUYDUYET_PNX'];
        PHARMA_SHOW_BTHUYDUYET_PNX = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_showbt.join('$'));

        var _par_showpopup = ['PHARMA_HIENTHI_POPUP_TUDONG'];
        PHARMA_HIENTHI_POPUP_TUDONG = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_showpopup.join('$'));

        var _par_duyetls = ['PHARMA_DUYET_DLS_BOSUNG_TUTRUC'];
        PHARMA_DUYET_DLS_BOSUNG_TUTRUC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_duyetls.join('$'));

        var _par_chuyen = ['PHARMA_CHUYEN_DUYET_COSOHOATCHAT'];
        PHARMA_CHUYEN_DUYET_COSOHOATCHAT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_chuyen.join('$'));
        var _par_bvravien = ['PHARMA_HIENTHI_YLENH_BN_RAVIEN'];
        PHARMA_HIENTHI_YLENH_BN_RAVIEN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_bvravien.join('$'));
        var _par_goibn = ['PHARMA_HIENTHI_GOIBN'];
        var PHARMA_HIENTHI_GOIBN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_goibn.join('$'));

        var _par_newtime = ['PHARMA_FORMAT_NEWTIME_SEARCH'];
        PHARMA_FORMAT_NEWTIME_SEARCH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_newtime.join('$'));
        var _par_luocbo = ['PHARMA_LUOCBOT_PHIEUYC'];
        PHARMA_LUOCBOT_PHIEUYC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_luocbo.join('$'));

        var _par_duyetchan = ['PHARMA_DUYET_THUOC_CHAN'];
        PHARMA_DUYET_THUOC_CHAN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_duyetchan.join('$'));

        var _par_dongyNTU = ['PHARMA_IN_CHUNG_DONGY_NOITRU'];
        PHARMA_IN_CHUNG_DONGY_NOITRU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_dongyNTU.join('$'));

        var _par_DuyetPhat = ['PHARMA_SHOW_INPHIEULINH_DUYETPHAT'];
        PHARMA_SHOW_INPHIEULINH_DUYETPHAT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_DuyetPhat.join('$'));

        var _par_phieuxuat = ['PHARMA_TUDONG_TAOPHIEUNHAPXUAT'];
        PHARMA_TUDONG_TAOPHIEUNHAPXUAT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_phieuxuat.join('$'));

        var _par_phieucha = ['PHARMA_SHOW_PHIEUCHA_NTU'];
        PHARMA_SHOW_PHIEUCHA_NTU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_phieucha.join('$'));
        var _par_CHONTATCAKHO = ['PHARMA_CHONTATCAKHO'];
        PHARMA_CHONTATCAKHO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_CHONTATCAKHO.join('$'));

        var _par_HIENTHI_GIABHYT = ['PHARMA_HIENTHI_GIABHYT'];
        PHARMA_HIENTHI_GIABHYT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_HIENTHI_GIABHYT.join('$'));
        var par_CANHBAOHSD_HOANTRATUTRUC = ['PHARMA_CANHBAOHSD_HOANTRATUTRUC'];
        PHARMA_CANHBAOHSD_HOANTRATUTRUC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_CANHBAOHSD_HOANTRATUTRUC.join('$'));

        var par_CHECK_LIENTHONG_HMIS = ['PHARMA_CHECK_LIENTHONG_HMIS'];
        PHARMA_CHECK_LIENTHONG_HMIS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_CHECK_LIENTHONG_HMIS.join('$'));
        
        var par_PHARMA_HUYPHIEU_GIUAO_DC = ['PHARMA_HUYPHIEU_GIUAO_DC'];
        PHARMA_HUYPHIEU_GIUAO_DC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
        		par_PHARMA_HUYPHIEU_GIUAO_DC.join('$'));

        var par_PHARMA_THEMCHUADUYETKT = ['PHARMA_THEMCHUADUYETKT'];
        PHARMA_THEMCHUADUYETKT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_PHARMA_THEMCHUADUYETKT.join('$'));
        var par_PHARMA_DUYETNHIEUPHIEU = ['PHARMA_DUYETNHIEUPHIEU'];
        PHARMA_DUYETNHIEUPHIEU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_PHARMA_DUYETNHIEUPHIEU.join('$'));

        var par_QLLT_PHIEUYHCT = ['QLLT_PHIEUYHCT'];
        cauHinhNutInYHCT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_QLLT_PHIEUYHCT.join('$'));

        var _par_tudongtaifile = ['PHARMA_TUDONG_TAIFILE'];
        PHARMA_TUDONG_TAIFILE = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par_tudongtaifile.join('$'));
        PHARMA_DSPHIEU_THEOKHOA = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_DSPHIEU_THEOKHOA');

        var par_PHARMA_DUTRUKHO_NEW = ['PHARMA_DUTRUKHO_NEW'];
        PHARMA_DUTRUKHO_NEW = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_PHARMA_DUTRUKHO_NEW.join('$'));
        var par_PHARMA_XUATKHAC_LYDOXUAT = ['PHARMA_XUATKHAC_LYDOXUAT'];
        PHARMA_XUATKHAC_LYDOXUAT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_PHARMA_XUATKHAC_LYDOXUAT.join('$'));
        var par_PHARMA_XUATKHAC_VATTU = ['PHARMA_XUATKHAC_VATTU'];
        PHARMA_XUATKHAC_VATTU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_PHARMA_XUATKHAC_VATTU.join('$'));

        var par_PHARMA_KHOA_TVT_NEW = ['PHARMA_KHOA_TVT_NEW'];
        PHARMA_KHOA_TVT_NEW = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_PHARMA_KHOA_TVT_NEW.join('$'));

        var par_PHARMA_HIENPHIEUXUAT = ['PHARMA_HIENPHIEUXUAT'];
        PHARMA_HIENPHIEUXUAT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_PHARMA_HIENPHIEUXUAT.join('$'));
        var par_PHARMA_NGUONXUAT_THKP = ['PHARMA_NGUONXUAT_THKP'];
        PHARMA_NGUONXUAT_THKP = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_PHARMA_NGUONXUAT_THKP.join('$'));
        var par_PHARMA_LAYDL_BNHNI = ['PHARMA_LAYDL_BNHNI'];
        PHARMA_LAYDL_BNHNI = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_PHARMA_LAYDL_BNHNI.join('$'));

        PHARMA_IN_PHIEULINH_MANHOM = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_IN_PHIEULINH_MANHOM');
        PHARMA_GIAODIEN_NCC_NHATHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_GIAODIEN_NCC_NHATHUOC');
        PHARMA_KHONGIN_PHIEUTAOMOI = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_KHONGIN_PHIEUTAOMOI');
        PHARMA_NHAPBUKHAC_KHONGTHAU = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_NHAPBUKHAC_KHONGTHAU');
        PHARMA_HIENTHI_TIEN_SOLE = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_HIENTHI_TIEN_SOLE');
        PHARMA_SHOWKHOAPHONG = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_SHOWKHOAPHONG');
        PHARMA_SONGAY_GODUYET = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_SONGAY_GODUYET');
        PHARMA_HIENTHI_GOIBENHNHAN_KB = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_HIENTHI_GOIBENHNHAN_KB');
        PHARMA_SHOW_CHANDOAN_BN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_SHOW_CHANDOAN_BN');
        PHARMA_SHOW_COLOR_VATTU_BN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_SHOW_COLOR_VATTU_BN');

        PHARMA_BOSOTHAPPHAN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_BOSOTHAPPHAN');
        PHARMA_BOSOTHAPPHAN_TT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_BOSOTHAPPHAN_TT');
        PHARMA_SHOW_COLOR_HSD = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_SHOW_COLOR_HSD');
        PHARMA_SONGAY_TIMKIEM_CHUCNANG = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHARMA_SONGAY_TIMKIEM_CHUCNANG');
        
        hCode = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_HOSPITALCODE_FAST'].join('$'));
		if(hCode != '0' && hCode != '' && (that.opt.ht == '1' || that.opt.ht == '2' || that.opt.ht == '9' || that.opt.ht == '13' || that.opt.ht == '12')){
			$("#btnGuiFast").show();
		}else{
			$("#btnGuiFast").hide();
		}
        
        if (PHARMA_HIENTHI_TIEN_SOLE == '0'
            || PHARMA_HIENTHI_TIEN_SOLE == '1'
            || PHARMA_HIENTHI_TIEN_SOLE == '3'
            || PHARMA_HIENTHI_TIEN_SOLE == '4') {
            _sole = replaceStrtoNum(PHARMA_HIENTHI_TIEN_SOLE);
        } else {
            _sole = 2;
        }
        if (PHARMA_SHOWKHOAPHONG == '1' && that.opt.ht == '13') {
            $("#divKhoa").show();
            $("#divPhong").show();

        }
        if (PHARMA_SHOW_CHANDOAN_BN == '1' && that.opt.ht == '13') {
            $("#divCDChinh").show();
            $("#divCDKemTheo").show();
        } else {
            $("#divCDChinh").hide();
            $("#divCDKemTheo").hide();
        }
        if (PHARMA_FORMAT_NEWTIME_SEARCH == '1' && that.opt.ht == '12' && that.opt.type == '45' && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU")) {
            $("#div_new_TN").show();
            $("#div_new_DN").show();
            $("#div_old_TN").hide();
            $("#div_old_DN").hide();
            //var _ngayhientaict = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
            var _ngayhientaict = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
            $('#txtNgayBDNew').val(_ngayhientaict + ' 00:00:00');
            $('#txtNgayKTNew').val(_ngayhientaict + ' 23:59:59');
        }

        if (PHARMA_XUATKHAC_LYDOXUAT == '1' && that.opt.ht == '8' && that.opt.gd == "XUATKHACTHUOC" && that.opt.LDXUAT != '' && jQuery.type(that.opt.LDXUAT) !== "undefined") {
            $("#divLYDOXUAT").show();
            var sql_par = RSUtil.buildParam("", [that.opt.LDXUAT]);
            ComboUtil.getComboTag("cboLYDOXUAT", "DUC01S.LYDOXUATKHAC", sql_par, "", "", "sql", "", false);
        }
        

        if ((PHARMA_HIENTHIKHO_THEOKHOAPHONG != '' || jQuery.type(PHARMA_HIENTHIKHO_THEOKHOAPHONG) !== "undefined")
            && PHARMA_HIENTHIKHO_THEOKHOAPHONG.includes(that.opt.ht)
        ) {
            var a = _opt.dept;
            var sql_par = RSUtil.buildParam("", [_opt.dept, that.opt.lk]);

            ComboUtil.getComboTag("cboKho", "DUC01S002.DSKHO6", sql_par, "", "",
                "sql", "", false);
        } else {


            if (jQuery.type(that.opt.lt) === "undefined") {
                var sql_par = RSUtil.buildParam("", [that.opt.lk]);
                if (PHARMA_CHONTATCAKHO.includes(that.opt.ht) && (that.opt.type == '45' || that.opt.type == '4'))
                    ComboUtil.getComboTag("cboKho", "DUC01S002.DSKHO2", sql_par, "", "", "sql", "", false);
                else {
                    if (that.opt.ht == '12' && that.opt.gd == 'MAU') {
                        var par_PHARMA_KHOMAU = ['PHARMA_KHOMAU'];
                        var PHARMA_KHOMAU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
                            par_PHARMA_KHOMAU.join('$'));
                        if (PHARMA_KHOMAU != '' && jQuery.type(PHARMA_KHOMAU) !== "undefined") {
                            ComboUtil.getComboTag("cboKho", "DUC01S002.DSKHO7", sql_par, "", "", "sql", "", false);
                        } else
                            ComboUtil.getComboTag("cboKho", "DUC01S002.DSKHO", sql_par, "", "", "sql", "", false);
                    } else
                        ComboUtil.getComboTag("cboKho", "DUC01S002.DSKHO", sql_par, "", "", "sql", "", false);
                }
            } else {
                //			var sql_par=RSUtil.buildParam("",[that.opt.lk,that.opt.lt]);
                //			ComboUtil.getComboTag("cboKho","DUC01S002.DSKHO5",sql_par,"","","sql","",false);
                var sql_par = that.opt.lk + '$' + that.opt.lt + '$';
                ComboUtil.getComboTag("cboKho", "DUC01S002.DSKHO5",
                    sql_par, "", "", "sp", '', function () {
                    });
            }
        }


        sql_par = RSUtil.buildParam("", [that.opt.ht]);
        if (!that.opt.ht.includes(","))
            ComboUtil.getComboTag("cboHinhThuc", "DUC01S002.HINHTHUC", sql_par, "", "", "sql", "", false);
        else
            ComboUtil.getComboTag("cboHinhThuc", "DUC01S002.HINHTHUC", sql_par, "", {
                value: that.opt.ht,
                text: '--Toàn bộ--'
            }, "sql", "", false);
        sql_par = RSUtil.buildParam("", [that.opt.tt]);
        if (PHARMA_THEMCHUADUYETKT == '1' && that.opt.ht == '13') {
            ComboUtil.getComboTag("cboTrangThaiDuyet", "DUC01S002.TRANGTHAI1", sql_par, "", {
                value: that.opt.tt + ',8',
                text: '--Toàn bộ--'
            }, "sql", "", false);
        } else {
            if (!that.opt.tt.includes(","))
                ComboUtil.getComboTag("cboTrangThaiDuyet", "DUC01S002.TRANGTHAI", sql_par, "", "", "sql", "", false);
            else
                ComboUtil.getComboTag("cboTrangThaiDuyet", "DUC01S002.TRANGTHAI", sql_par, "", {
                    value: that.opt.tt,
                    text: '--Toàn bộ--'
                }, "sql", "", false);
        }


        sql_par = RSUtil.buildParam("", [that.opt.lp, that.opt.ht]);
        ComboUtil.getComboTag("cboLoaiPhieu", "DUC01S002.LOAIPHIEU", sql_par, "", {
            value: '0,1,2,3',
            text: '--Toàn bộ--'
        }, "sql", "", false);
        if (PHARMA_SHOWKHOAPHONG == '1' && that.opt.ht == '13') {
            ComboUtil.getComboTag("cboKhoa", "DMC.NV.04", [], "", {text: "-- Chọn khoa --", value: -1}, "sql");
            var _val2 = $('#cboKhoa').val();
            ComboUtil.getComboTag("cboPhong", "DMC.NV.06", [{
                "name": "[0]",
                "value": _val2
            }], "", {text: "-- Chọn phòng --", value: -1}, "sql");
        }
        /*if(!that.opt.lp.includes("0")){
			$("#cboLoaiPhieu option[value='0']").remove();
		}
		if(!that.opt.lp.includes("1")){
			$("#cboLoaiPhieu option[value='1']").remove();
		}
		if(!that.opt.lp.includes("2")){
			$("#cboLoaiPhieu option[value='2']").remove();
		}
		if(!that.opt.lp.includes("3")){
			$("#cboLoaiPhieu option[value='3']").remove();
		}*/
        if (!that.opt.lp.includes(","))
            $("#cboLoaiPhieu option[value='0,1,2,3']").remove();
        else
            $("#cboLoaiPhieu option[value='0,1,2,3']").val(that.opt.lp);

        //header + title
        var _header = '';
        if (that.opt.type.includes("4")) {
            _header = _header + "Duyệt phiếu";
        } else {
            _header = _header + "Danh sách phiếu";
        }
        if (that.opt.lp.includes("2") || that.opt.lp.includes("3")) {
            _header = _header + " yêu cầu";
        } else {
            if (that.opt.lp.includes("0")) {
                _header = _header + " nhập";
            }
            if (that.opt.lp.includes("1")) {
                _header = _header + " xuất";
            }
        }
        that.opt.title = "";
        if (that.opt.gd == 'THUOC' || that.opt.gd == 'NHAPBUTHUOC' || that.opt.gd == 'XUATHUHAOCB' || that.opt.gd == 'XUATHUYTHUOC' || that.opt.gd == 'XUATTHIEUTHUOC') {
            that.opt.title = " thuốc";
        } else if (that.opt.gd == 'VATTU' || that.opt.gd == 'NHAPBUVATTU' || that.opt.gd == 'XUATHUYVATTU' || that.opt.gd == 'XUATTHIEUVATTU') {
            that.opt.title = " vật tư";
        }
        _header = _header + that.opt.title;
        if (!that.opt.ht.includes(",")) {
            _header = _header + ' ' + $("#cboHinhThuc").text().toLowerCase();
        }
        if (that.opt.td == '1') {
            _header = 'Xác nhận giao/nhận theo phiếu lĩnh';
            _btnDuyet = 'Xác nhận';

        }
        if (that.opt.td == '9') {
            _header = 'Duyệt lâm sàng yêu cầu bổ sung thuốc tủ trực';
        }
        if (that.opt.td == '14') {
            _header = 'Duyệt phát thuốc';
        }
        if (that.opt.ht == '22') {
            _header = 'Danh sách phiếu yêu cầu khóa thuốc';
        }
        if (that.opt.gd == 'NHAPKHACTHUOC' || that.opt.gd == 'NHAPKHACVATTU') {
            _btnNhapBu = 'Nhập khác';

        }
        if (that.opt.gd == 'TRACUUNHAPKHO') {
            _header = 'DS tra cứu thuốc theo phiếu nhập kho';

        }


        var _btnText = "Dự trù";
        if ((that.opt.hospitalId == '993') && that.opt.ht == "2") {
            _btnText = "Xuất kho";
        }
        if (that.opt.ht == "9") {
            _btnText = "Bổ sung";
        }
        //create toolbar
        var ctl_ar = [{
            type: 'buttongroup', id: 'btnPrint', icon: 'print', text: 'In ấn'
            , children: [
                {id: 'tbInPhieuLinh', icon: 'print', text: 'In phiếu lĩnh', hlink: '#'},
                {id: 'tbInTHPhieuLinhHDG', icon: 'print', text: 'In tổng hợp phiếu lĩnh HDG', hlink: '#'},
                {id: 'tbInPhieuLinhVPP', icon: 'print', text: 'In phiếu lĩnh vật tư VPP ', hlink: '#'},
                {id: 'tbInPhieu2Lien', icon: 'print', text: 'In phiếu lĩnh thuốc ', hlink: '#'},
                {id: 'tbInPhieuLinhVaccin', icon: 'print', text: 'In phiếu lĩnh vaccin xuất trạm ', hlink: '#'},
                {
                    id: 'tbInPhieuLinhThuocDYNTU',
                    icon: 'print',
                    text: 'In phiếu lĩnh thang thuốc đông y nội trú ',
                    hlink: '#'
                },
                {
                    id: 'tbInPhieuLinhThuocDYHAOPHINTU',
                    icon: 'print',
                    text: 'In phiếu lĩnh thang thuốc đông y hao phí nội trú ',
                    hlink: '#'
                },
                {id: 'tbInPLinhThuocNGT', icon: 'print', text: 'In phiếu lĩnh thuốc ngoại trú', hlink: '#'},
                {id: 'tbInPLinhDSThuocNGT', icon: 'print', text: 'In danh sách bệnh nhân ngoại trú', hlink: '#'},
                {id: 'tbInDonThuocNGT', icon: 'print', text: 'In đơn thuốc ngoại trú', hlink: '#'},
                {id: 'tbInPhieuLinhMau', icon: 'print', text: 'Phiếu lĩnh máu DKLAN', hlink: '#'},
                {id: 'tbInPLMauNgoaiVien', icon: 'print', text: 'Phiếu lĩnh máu ngoài viện', hlink: '#'},
                {id: 'tbInPhieuLinhThuocTongHop', icon: 'print', text: 'In phiếu lĩnh tổng hợp', hlink: '#'},
//				{id:'tbInPhieuCapPhatVaccineSPYT',icon:'print',text:'Phiếu cấp phát vaccine, SPYT',hlink:'#'},
//				{id:'tbInPhieuCapPhatVaccineSPYT2',icon:'print',text:'Phiếu cấp phát vaccine, SPYT dược',hlink:'#'},
//				{id:'tbInPhieuVatTu2Lien',icon:'print',text:'In phiếu lĩnh vật tư',hlink:'#'},
                {id: 'tbInPhieuNoThuoc2Lien', icon: 'print', text: 'In phiếu nợ thuốc/vật tư ', hlink: '#'},
                //	{id:'tbInPhieuNoVT2Lien',icon:'print',text:'In phiếu nợ vật tư',hlink:'#'},
                {id: 'tbInPhieuLinhThuoc', icon: 'print', text: 'Phiếu lĩnh thuốc 1 liên', hlink: '#'},
                {id: 'tbInPhieuYCDieuChuyen', icon: 'print', text: 'In phiếu điều chuyển thuốc', hlink: '#'},
                {id: 'tbInPhieuLinhDieuChuyen', icon: 'print', text: 'In phiếu lĩnh điều chuyển thuốc', hlink: '#'},
                {id: 'tbInPhieuXuatDCTHUOC', icon: 'print', text: 'In phiếu xuất điều chuyển thuốc', hlink: '#'},
                {id: 'tbInPhieuLinhVatTu', icon: 'print', text: 'Phiếu lĩnh vật tư 1 liên', hlink: '#'},
                {
                    id: 'tbInPLThuocGayNghienHT1Lien',
                    icon: 'print',
                    text: 'Phiếu lĩnh thuốc gây nghiện, hướng thần 1 liên',
                    hlink: '#'
                },
                {
                    id: 'tbInPXThuocGayNghienHT',
                    icon: 'print',
                    text: 'Phiếu xuất thuốc gây nghiện, hướng thần',
                    hlink: '#'
                },
                // Dieu chinh kho thuoc => Phieu xuat thuoc gay nghien, huong than
                {
                    id: 'tbInXuatThuocGayNghienHuongThan',
                    icon: 'print',
                    text: 'Phiếu xuất thuốc gây nghiện, hướng thần',
                    hlink: '#'
                },
                {id: 'print_1', icon: 'print', text: 'Phiếu lĩnh thuốc gây nghiện, hướng thần', hlink: '#'},
                {
                    id: 'tbInPhieuDuTruGNHT',
                    icon: 'print',
                    text: 'Phiếu dự trù tủ trực thuốc gây nghiện, hướng thần',
                    hlink: '#'
                },
                {
                    id: 'tbInPhieuTraThuocGNHT',
                    icon: 'print',
                    text: 'Phiếu trả thuốc gây nghiện, hướng thần',
                    hlink: '#'
                },
                {
                    id: 'tbInPhieuLinhThuocGiaTriCao',
                    icon: 'print',
                    text: 'In phiếu lĩnh thuốc giá trị cao ',
                    hlink: '#'
                },
                {id: 'tbInDonThuocYHCT', icon: 'print', text: 'In đơn thuốc YHCT', hlink: '#'},
                {id: 'tbInNhap', icon: 'print', text: 'In phiếu nhập', hlink: '#'},
                {id: 'tbInNhapVPP', icon: 'print', text: 'In phiếu nhập vật tư VPP ', hlink: '#'},
                {id: 'tbInNhap_ChuyenKho', icon: 'print', text: 'In phiếu nhập(Chuyển kho)', hlink: '#'},
                {id: 'tbInNhapGiaDY', icon: 'print', text: 'In phiếu nhập(Theo giá đông y)', hlink: '#'},
                {id: 'tbInNhapYHCT', icon: 'print', text: 'In phiếu nhập YHCT', hlink: '#'},
                {id: 'tbInNhapMAU', icon: 'print', text: 'In phiếu nhập kho Máu', hlink: '#'},
                {id: 'tbInNhapNhaThuoc', icon: 'print', text: 'In phiếu nhập kho nhà thuốc', hlink: '#'},
                {id: 'tbInNhapKT', icon: 'print', text: 'In phiếu nhập kế toán', hlink: '#'},
                {id: 'tbInDutru', icon: 'print', text: 'In phiếu dự trù', hlink: '#'},
                {id: 'tbInNhapHoanTra', icon: 'print', text: 'In phiếu nhập(Hoàn trả)', hlink: '#'},
                {id: 'tbInXuat', icon: 'print', text: 'In phiếu xuất', hlink: '#'},
                {id: 'tbInXuatKhoDuoc', icon: 'print', text: 'In phiếu xuất kho dược', hlink: '#'},
                {id: 'tbInXuatBenhNhan', icon: 'print', text: 'In phiếu xuất BN', hlink: '#'},
                {id: 'tbInXuatKhacDakSong', icon: 'print', text: 'In phiếu xuất khác', hlink: '#'},
                {id: 'tbInXuatVPP', icon: 'print', text: 'In phiếu xuất vật tư VPP ', hlink: '#'},
                {id: 'tbInDuTruMuaVPP', icon: 'print', text: 'In dự trù mua VPP ', hlink: '#'},
                {id: 'tbInXuatMyPham', icon: 'print', text: 'In phiếu xuất mỹ phẩm', hlink: '#'},
                {id: 'tbInXuatSinhPham', icon: 'print', text: 'In phiếu xuất sinh phẩm', hlink: '#'},
                {id: 'tbInXuatYDungCu', icon: 'print', text: 'In phiếu xuất y dụng cụ', hlink: '#'},
                {id: 'tbInXuatBoSung', icon: 'print', text: 'In phiếu xuất bổ sung', hlink: '#'},
                {id: 'tbInXuatThuHoi', icon: 'print', text: 'In phiếu thu hồi', hlink: '#'},
                {id: 'tbInXuatTramYte', icon: 'print', text: 'In phiếu xuất kho trạm y tế', hlink: '#'},
                {id: 'tbInXuatChuyenKho', icon: 'print', text: 'In phiếu xuất giữa các kho', hlink: '#'},
                {id: 'tbInXuatNhaThuoc', icon: 'print', text: 'In phiếu xuất kho nhà thuốc', hlink: '#'},
                {id: 'tbInXuatHmis', icon: 'print', text: 'In phiếu xuất kho liên thông dược', hlink: '#'},
                {id: 'tbInNhapHmis', icon: 'print', text: 'In phiếu nhập kho liên thông dược', hlink: '#'},
                {id: 'tbGiayTT', icon: 'print', text: 'Giấy thanh toán Dược', hlink: '#'},
                {id: 'tbGiayTT_VT', icon: 'print', text: 'Giấy thanh toán Vật Tư', hlink: '#'},
                {id: 'tbInPhieuXuatGNHT', icon: 'print', text: 'In phiếu xuất GNHT', hlink: '#'},
				{id: 'tbInPhieuXuatKHTH', icon: 'print', text: 'In phiếu xuất hóa chất KHTH', hlink: '#'},
                {id: 'tbSoKiemNhapHoaDon', icon: 'print', text: 'Sổ Kiểm Nhập', hlink: '#'},
//				{id:'print_2',icon:'print',text:'Phiếu lĩnh vật tư tiêu hao',hlink:'#'},
                {id: 'print_3', icon: 'print', text: 'Phiếu trả thuốc', hlink: '#'},
                {id: 'print_10', icon: 'print', text: 'Phiếu hoàn trả', hlink: '#'},
                {id: 'print_4', icon: 'print', text: 'Biên bản thanh lý thuốc và hóa chất vật tư', hlink: '#'},
                {id: 'print_4_1', icon: 'print', text: 'Xuất file BB thanh lý thuốc và hóa chất vật tư', hlink: '#'},
                {
                    id: 'print_5',
                    icon: 'print',
                    text: 'Biên bản xác nhận thuốc/hóa chất/vật tư y tế mất/hỏng/vỡ',
                    hlink: '#'
                },
                {id: 'print_6', icon: 'print', text: 'Phiếu trả thuốc bệnh nhân', hlink: '#'},
                {id: 'print_7', icon: 'print', text: 'Phiếu trả vật tư', hlink: '#'},
                {id: 'print_8', icon: 'print', text: 'Hóa đơn bán lẻ', hlink: '#'},
                {id: 'print_9', icon: 'print', text: 'Phiếu XK kiêm vận chuyển nội bộ', hlink: '#'},
                {id: 'print_11', icon: 'print', text: 'Bảng kê y lệnh thuốc ', hlink: '#'},
                {id: 'tbInDonThuocBenhNhan', icon: 'print', text: 'Đơn thuốc bệnh nhân', hlink: '#'},
                {id: 'tbInDonThuocRaVien', icon: 'print', text: 'Đơn thuốc ra viện', hlink: '#'},
                {id: 'tbInPhatThuocNgoaiTru', icon: 'print', text: 'Phiếu phát thuốc ngoại trú', hlink: '#'},
                {id: 'tbInPhieuDuTru', icon: 'print', text: 'Phiếu dự trù thuốc', hlink: '#'},
                {id: 'tbInPhieuDuTruVT', icon: 'print', text: 'Phiếu dự trù vật tư', hlink: '#'},
                {id: 'tbphieutrathuoc', icon: 'print', text: 'Phiếu khách lẻ trả thuốc', hlink: '#'},
                {id: 'tbInPhieuTraHang', icon: 'print', text: 'Phiếu trả hàng', hlink: '#'},
                {id: 'tbBBKiemNhapHongVo', icon: 'print', text: 'Biên bản xác nhận hỏng vỡ', hlink: '#'},
                {id: 'tbBBKiemNhapHoaDon', icon: 'print', text: 'Biên bản kiểm nhập hóa đơn', hlink: '#'},
                {id: 'tbBBKiemNhapHoaDonExcel', icon: 'print', text: 'Biên bản kiểm nhập hóa đơn (Excel)', hlink: '#'},
                {id: 'tbBBKiemNhapHoaDon_TDY', icon: 'print', text: 'BB kiểm nhập hóa đơn theo giá Đông Y', hlink: '#'},
                {id: 'tbBBKiemNhapXuatChuyenKho', icon: 'print', text: 'BB Kiểm nhập xuất chuyển kho', hlink: '#'},
                {id: 'tbBBKiemKeHoaDon', icon: 'print', text: 'BB Kiểm kê hóa đơn', hlink: '#'}
//				{id:'tbInPhieuDuTru',icon:'print',text:'In phiếu dự trù',hlink:'#'},
                , {
                    id: 'tbInPhieuLinhTVTHaoPhi2Lien',
                    icon: 'print',
                    text: 'In phiếu lĩnh thuốc/vật tư hao phí',
                    hlink: '#'
                }
                , {id: 'tbInPhieuLinhThuocHaoPhi2Lien', icon: 'print', text: 'In phiếu lĩnh thuốc hao phí', hlink: '#'}
                , {
                    id: 'tbInPhieuHoanTraDTTH',
                    icon: 'print',
                    text: 'In phiếu hoàn trả thuốc/vật tư hao phí ',
                    hlink: '#'
                }
                , {id: 'tbInPhieuBaoHongDCCC', icon: 'print', text: 'Phiếu báo hỏng mất DCCC', hlink: '#'}
                , {id: 'tbInPhieuTraThuocHaoPhi', icon: 'print', text: 'In phiếu trả thuốc hao phí', hlink: '#'}
                , {
                    id: 'tbInLinhTVTBoSungCoSoTuTruc2Lien',
                    icon: 'print',
                    text: 'Phiếu lĩnh thuốc/vật tư bổ sung cơ số tủ trực',
                    hlink: '#'
                }
                , {
                    id: 'tbInLinhGNHTBoSungTuTruc',
                    icon: 'print',
                    text: 'Phiếu lĩnh thuốc GNHT bổ sung cơ số tủ trực',
                    hlink: '#'
                }
                , {id: 'tbInLinhTuiMau', icon: 'print', text: 'Phiếu lĩnh túi máu', hlink: '#'}
                , {id: 'tbInHoanTraCoSoTuTruc2Lien', icon: 'print', text: 'Phiếu hoàn trả cơ số tủ trực', hlink: '#'}
                , {id: 'tbInNhapXuatThuocTuNhaThuoc', icon: 'print', text: 'Nhập xuất thuốc từ nhà thuốc', hlink: '#'} //20180906
                , {id: 'tbInBienBanTraThuoc', icon: 'print', text: 'Biên bản trả thuốc', hlink: '#'} //20180927
                , {id: 'tbInXuatThuocTraKhoChinh', icon: 'print', text: 'Phiếu xuất thuốc trả kho chính', hlink: '#'} //20181008
                , {
                    id: 'tbInLinhHoaChatKhoaPhong',
                    icon: 'print',
                    text: 'Phiếu lĩnh hoá chất theo khoa phòng',
                    hlink: '#'
                } //********
                , {
                    id: 'tbInPhieuYDungCuKhoaPhong',
                    icon: 'print',
                    text: 'Phiếu lĩnh y dụng cụ theo khoa phòng',
                    hlink: '#'
                } //********
                , {
                    id: 'tbInPhieuYDC_VACCIN_TTB',
                    icon: 'print',
                    text: 'Biên bản giao nhận vaccin, y dụng cụ, trang thiết bị',
                    hlink: '#'
                } //********
                , {
                    id: 'tbInPhieuVTCNTTKhoaPhong',
                    icon: 'print',
                    text: 'Phiếu lĩnh vật tư CNTT theo khoa phòng',
                    hlink: '#'
                }
                , {id: 'tbInPhieuHoanTraDoVai', icon: 'print', text: 'Phiếu hoàn trả vật tư đồ vải', hlink: '#'} //********
                , {id: 'tbInPhieuHoanLinhDoVai', icon: 'print', text: 'Phiếu hoàn lĩnh vật tư đồ vải', hlink: '#'} //********
                , {id: 'tbInXuatTra_NCC', icon: 'print', text: 'Phiếu xuất trả NCC', hlink: '#'}
                , {
                    id: 'tbInBienBanKiemNhapHoanTra',
                    icon: 'print',
                    text: 'Biên bản kiểm nhập thuốc,vật tư, hóa chất hoàn trả',
                    hlink: '#'
                }
                , {
                    id: 'tbInBienBanKiemNhapVacXin',
                    icon: 'print',
                    text: 'Biên bản giao nhận Vaccin',
                    hlink: '#'
                }
                , {id: 'tbPhieuLinhNTCT', icon: 'print', text: 'Phiếu lĩnh nội trú chi tiết', hlink: '#'}
                , {id: 'tbBBXuatHuy', icon: 'print', text: 'Biên bản xác nhận xuất hủy', hlink: '#'}
                , {id: 'tbBBNhan_TVT', icon: 'print', text: 'In phiếu biên bản, nhận thuốc/vật tư', hlink: '#'}
                , {
                    id: 'tbInPhieuCungCapMauPL9',
                    icon: 'print',
                    text: 'In phiếu cung cấp máu và thành phần máu',
                    hlink: '#'
                }
                , {id: 'tbTruyenMau', icon: 'print', text: 'Truyền máu hòa hợp', hlink: '#'}
                , {id: 'tbBBXuatHuyPL12', icon: 'print', text: 'Biên bản xuất hủy phụ lục 12', hlink: '#'}
                , {id: 'tbPhieuXuatKhac', icon: 'print', text: 'Phiếu xuất khác', hlink: '#'}
                , {id: 'tbPhieuTraThuochni', icon: 'print', text: 'Phiếu trả thuốc', hlink: '#'}
                , {id: 'tbPhieuXuatKhacVatTu', icon: 'print', text: 'Phiếu xuất khác vật tư', hlink: '#'}
                , {id: 'tbPhieuXuatKhacHc', icon: 'print', text: 'Phiếu xuất khác hóa chất', hlink: '#'}
                , {id: 'tbPhieuXuatKhacCCDC', icon: 'print', text: 'Phiếu xuất khác CCDC', hlink: '#'}
                , {id: 'tbPhieuXuatHuyVatTu', icon: 'print', text: 'Phiếu xuất hủy vật tư', hlink: '#'}
                , {id: 'tbPhieuDuTruXa', icon: 'print', text: 'Phiếu dự trù tuyến xã', hlink: '#'}
				, {id: 'print_trakhomau', icon: 'print', text: 'Phiếu trả lại kho máu', hlink: '#'}
            ]
        }
            , {type: 'button', id: 'tbDuyet', icon: 'ok', text: _btnDuyet, hlink: '#'}
            , {type: 'button', id: 'tbGoiBN', icon: 'ok', text: 'Gọi BN', hlink: '#'}
            , {type: 'button', id: 'tbTaoPhieu', icon: 'pencil', text: 'Tạo phiếu', hlink: '#'}
            , {type: 'button', id: 'tbYCNhap', icon: 'tint', text: _btnText, hlink: '#'}
//			,{type:'button',id:'tbNhapKho',icon:'tint',text:'Nhập kho',hlink:'#'}
            , {type: 'button', id: 'tbYCXuat', icon: 'hoantra', text: 'Hoàn trả lô', hlink: '#'}
            , {type: 'button', id: 'tbHoanTra', icon: 'hoantra', text: 'Hoàn trả', hlink: '#'}
//			,{type:'button',id:'tbXuatTra',icon:'tint',text:'Xuất trả',hlink:'#'}
            , {type: 'button', id: 'tbNhapKhoNCC', icon: 'nhapkho', text: 'Nhập kho', hlink: '#'}
            //,{type:'button',id:'tbHoanThien',icon:'nhapkho',text:'Hoàn thiện HS',cssClass : 'wd120',hlink:'#'}
            /*,{type:'button',id:'tbNhapKho_PP',icon:'nhapkho',text:'Nhập kho phụ phí',cssClass : 'wd130',hlink:'#'}*/
            //,{type:'button',id:'tbNhapKho_NT',icon:'nhapkho',text:'Nhập kho',hlink:'#'}
            , {type: 'button', id: 'tbXuatTraNCC', icon: 'xuattra', text: 'Xuất trả lô', hlink: '#'}
            , {type: 'button', id: 'tbXuatTraNCC_TK', icon: 'xuattra', text: 'Xuất trả', hlink: '#'}
            , {type: 'button', id: 'tbSua', icon: 'sua', text: 'Sửa', hlink: '#'}
            , {type: 'button', id: 'tbSuaNCC', icon: 'sua', text: 'Sửa', hlink: '#'}
            , {type: 'button', id: 'tbYCNhapBu', icon: 'ycnhapbu', text: _btnNhapBu, hlink: '#'}
            , {type: 'button', id: 'tbNhapBu', icon: 'nhapbu', text: 'Nhập bù', hlink: '#'}
            , {type: 'button', id: 'tbSuaNhapBu', icon: 'sua', text: 'Sửa', hlink: '#'}
            , {type: 'button', id: 'tbYCXuatHuy', icon: 'ycxuathuy', text: 'YC Xuất hủy', cssClass: 'wd130', hlink: '#'}
            , {type: 'button', id: 'tbXuatHuy', icon: 'xuathuy', text: 'Xuất hủy', hlink: '#'}
            , {type: 'button', id: 'tbSuaXuatHuy', icon: 'sua', text: 'Sửa', hlink: '#'}
            , {
                type: 'button',
                id: 'tbYCXuatKhac',
                icon: 'ycxuathuy',
                text: 'YC Xuất khác',
                cssClass: 'wd130',
                hlink: '#'
            }
            , {type: 'button', id: 'tbXuatKhac', icon: 'xuathuy', text: 'Xuất khác', hlink: '#'}
            , {type: 'button', id: 'tbSuaXuatKhac', icon: 'sua', text: 'Sửa', hlink: '#'}
            , {
                type: 'button',
                id: 'tbYCXuatThieu',
                icon: 'ycxuatthieu',
                text: 'YC Xuất thiếu',
                cssClass: 'wd130',
                hlink: '#'
            }
            , {type: 'button', id: 'tbXuatThieu', icon: 'xuatthieu', text: 'Xuất thiếu', hlink: '#'}
            , {type: 'button', id: 'tbSuaXuatThieu', icon: 'sua', text: 'Sửa', hlink: '#'}
//			,{type:'button',id:'tbSuaHoanTra',icon:'sua',text:'Sửa',hlink:'#'}
            , {type: 'button', id: 'tbYCXuatDTTH', icon: 'tint', text: 'YC Xuất', hlink: '#'}
            , {type: 'button', id: 'tbXuatDTTH', icon: 'tint', text: 'Xuất', hlink: '#'}
            , {type: 'button', id: 'tbSuaDTTH', icon: 'sua', text: 'Sửa', hlink: '#'}
            , {type: 'button', id: 'tbHoanTraDTTH', icon: 'hoantra', text: 'Hoàn trả', hlink: '#'}
            , {type: 'button', id: 'tbNhapTra', icon: 'nhapbu', text: 'Nhập trả', hlink: '#'}
            , {type: 'button', id: 'tbDuyetHoanTra', icon: 'tint', text: 'Duyệt HT', hlink: '#'}
            , {type: 'button', id: 'tbXuatYLenhLT', icon: 'tint', text: 'YC Xuất', hlink: '#'}
            , {type: 'button', id: 'tbHTraYLenhLT', icon: 'tint', text: 'Hoàn trả', hlink: '#'}
            , {type: 'button', id: 'tbSuaYLenhLT', icon: 'sua', text: 'Sửa', hlink: '#'}
            // ban thuoc
            , {type: 'button', id: 'tbBanThuoc', icon: 'tint', text: 'Bán Thuốc', hlink: '#'}
            , {type: 'button', id: 'tbKhoaThuoc', icon: 'xuatthieu', text: 'Khóa Thuốc', hlink: '#'}
            //,{type:'button',id:'tbPhieuThuBanThuoc',icon:'tint',text:'Phiếu thu',hlink:'#'}
            , {type: 'button', id: 'tbSuaPhieuBanTHuoc', icon: 'tint', text: 'Sửa', hlink: '#'}
            , {type: 'button', id: 'tbTraThuoc', icon: 'tint', text: 'Trả thuốc', hlink: '#'}
            , {type: 'button', id: 'tbBoSung', icon: 'nhapkho', text: 'Bổ sung', hlink: '#'}
            , {type: 'button', id: 'tbDieuChinhDon', icon: 'sua', text: 'Điều chỉnh', hlink: '#'}
            , {type: 'button', id: 'tbBoSungNoThuoc', icon: 'sua', text: 'BS nợ thuốc', hlink: '#'}
            , {type: 'button', id: 'tbDuyetLamSan', icon: 'sua', text: 'Duyệt lâm sàng', cssClass: 'wd130', hlink: '#'}
            , {type: 'button', id: 'tbDuyetPhatThuoc', icon: 'sua', text: 'Duyệt phát', cssClass: 'wd130', hlink: '#'}
            , {
                type: 'button',
                id: 'tbGoDuyetPhatThuoc',
                icon: 'sua',
                text: 'Gỡ duyệt phát',
                cssClass: 'wd130',
                hlink: '#'
            }
            , {type: 'button', id: 'tbXacNhanVPP', icon: 'sua', text: 'Xác nhận', cssClass: 'wd130', hlink: '#'}
            , {type: 'button', id: 'tbBatDauSoan', icon: 'sua', text: 'Bắt đầu soạn', cssClass: 'wd130', hlink: '#'}
            , {type: 'button', id: 'tbHuySoan', icon: 'sua', text: 'Hủy soạn', cssClass: 'wd130', hlink: '#'}
            , {type: 'button', id: 'tbLCD', icon: 'sua', text: 'LCD', cssClass: 'wd130', hlink: '#'}
            , {type: 'button', id: 'tbGoiBenhNhan', icon: 'sua', text: 'Gọi bệnh nhân', cssClass: 'wd130', hlink: '#'}
            //,{type:'button',id:'tbHoanTraTVTHetHan',icon:'hoantra',text:'HT TVT hết HSD',cssClass : 'wd130',hlink:'#'}
            , {type: 'label', id: 'lblInfo', icon: '', text: _header}
        ];
        ctl_ar = _initBienBanKiemNhapThuocThua(ctl_ar);//DucTT20181030
        ToolbarUtil.build('toolbarId', ctl_ar);
        _showBienBanKiemNhapThuocThua();//DucTT20181030
        //show hide toolbar btn
        //$("#toolbarIdtbHoanTraTVTHetHan").hide();
        $("#toolbarIdtbLCD").hide();
        $("#toolbarIdtbGoiBenhNhan").hide();
        $("#toolbarIdtbDieuChinhDon").hide();
        $("#toolbarIdtbBoSungNoThuoc").hide();
        $("#toolbarIdtbDuyetLamSan").hide();
        $("#toolbarIdtbDuyetPhatThuoc").hide();
        $("#toolbarIdtbGoDuyetPhatThuoc").hide();
        $("#toolbarIdtbDuyet").hide();
        $("#tbGoiBN").hide();
        $("#toolbarIdtbTaoPhieu").hide();
        $("#toolbarIdtbYCNhap").hide();
        $("#toolbarIdtbDieuChuyen").hide();
        $("#toolbarIdtbThuHoi").hide();
//		$("#toolbarIdtbNhapKho").hide();
        $("#btnNhapKho").hide();
        $("#toolbarIdtbYCXuat").hide();
//		$("#toolbarIdtbXuatTra").hide();
        $("#btnXuatTra").hide();
        $("#toolbarIdtbNhapKhoNCC").hide();
        $("#tbHoanThien").hide();
        //$("#toolbarIdtbNhapKho_PP").hide();
        //$("#toolbarIdtbNhapKho_NT").hide();
        $("#btnNhapKhoNCC").hide();
        $("#toolbarIdtbXuatTraNCC").hide();
        $("#toolbarIdtbXuatTraNCC_TK").hide();
//		$("#btnXuatTraNCC").hide();
        $("#toolbarIdtbSua").hide();
        $("#toolbarIdtbSuaNCC").hide();
        $("#toolbarIdtbBoSung").hide();
        $("#toolbarIdtbNhapBu").hide();
        $("#toolbarIdtbNhapTra").hide();
        $("#toolbarIdtbYCNhapBu").hide();
        $("#toolbarIdtbSuaNhapBu").hide();
        $("#toolbarIdtbYCXuatHuy").hide();
        $("#toolbarIdtbXuatHuy").hide();
        $("#toolbarIdtbSuaXuatHuy").hide();
        $("#toolbarIdtbYCXuatKhac").hide();
        $("#toolbarIdtbXuatKhac").hide();
        $("#toolbarIdtbSuaXuatKhac").hide();
        $("#toolbarIdtbYCXuatThieu").hide();
        $("#toolbarIdtbXuatThieu").hide();
        $("#toolbarIdtbSuaXuatThieu").hide();
        $("#toolbarIdtbInXuatChuyenKho").hide();
        $("#toolbarIdtbInXuatNhaThuoc").hide();
        $("#toolbarIdtbInXuatHmis").hide();
        $("#toolbarIdtbInXuatBenhNhan").hide();
        $("#toolbarIdtbInXuatKhoDuoc").hide();
        $("#toolbarIdtbInNhapHmis").hide();


        $("#toolbarIdtbYCXuatDTTH").hide();
        $("#toolbarIdtbXuatDTTH").hide();
        $("#toolbarIdtbXuatYLenhLT").hide();
        $("#toolbarIdtbHTraYLenhLT").hide();

        $("#toolbarIdtbHoanTraDTTH").hide();
        $("#toolbarIdtbSuaDTTH").hide();
        $("#toolbarIdtbSuaYLenhLT").hide();


        $("#toolbarIdtbHoanTra").hide();
        // ban thuoc
        $("#toolbarIdtbBanThuoc").hide();
        //khoa thuoc
        $("#toolbarIdtbKhoaThuoc").hide();
        //$("#toolbarIdtbPhieuThuBanThuoc").hide();
        $("#toolbarIdtbSuaPhieuBanTHuoc").hide();
        $("#toolbarIdtbTraThuoc").hide();


//		$("#toolbarIdtbSuaHoanTra").hide();
        $("#toolbarIdtbDuyetHoanTra").hide();
        $("#toolbarIdtbInPXThuocGayNghienHT").hide();
        $("#toolbarIdtbInPhieuTraThuocGNHT").hide();
        $("#btnInPhieu").hide();

//		$("#toolbarIdtbIn").hide();
        $("#toolbarIdtbInPhieuLinhThuoc").hide();
        $("#toolbarIdtbInPhieuDuTru").hide();
        $("#toolbarIdtbInPhieuDuTruVT").hide();
        $("#toolbarIdtbInDonThuocRaVien").hide();
        $("#toolbarIdtbInDonThuocBenhNhan").hide();
        $("#toolbarIdtbInPhatThuocNgoaiTru").hide();
        $("#toolbarIdtbInNhap_ChuyenKho").hide();
        $("#toolbarIdtbInNhapHoanTra").hide();

        $("#toolbarIdtbInPLThuocGayNghienHT1Lien").hide();

        $("#toolbarIdtbInPhieuLinhVatTu").hide();
//		$("#toolbarIdprint_1").hide();
//		$("#toolbarIdprint_2").hide();
        $("#toolbarIdprint_3").hide();
        $("#toolbarIdprint_10").hide();
        $("#toolbarIdprint_4").hide();
        $("#toolbarIdprint_4_1").hide();
        $("#toolbarIdprint_5").hide();
        $("#toolbarIdprint_6").hide();
        $("#toolbarIdprint_7").hide();
        $("#toolbarIdprint_8").hide();
        $("#toolbarIdtbphieutrathuoc").hide();
        $("#toolbarIdtbInPhieuTraHang").hide();
        $("#toolbarIdprint_11").hide();
        //$("#toolbarIdtbGiayTT").hide();
        $("#toolbartbInPhieuDuTru").hide();
        $("#toolbarIdtbInPhieuLinhVPP").hide();
        $("#toolbarIdtbInPhieuLinhVaccin").hide();
        $("#toolbarIdtbInNhapVPP").hide();
        $("#toolbarIdtbInXuatVPP").hide();
        $("#toolbarIdtbInDuTruMuaVPP").hide();
//		$("#toolbarIdtbInPhieu2Lien").hide();
//		$("#toolbarIdtbInPhieuVatTu2Lien").hide();
        $("#toolbarIdtbInPhieuLinhThuocDYNTU").hide();
        $("#toolbarIdtbInPhieuLinhThuocDYHAOPHINTU").hide();
        $("#toolbarIdtbInPhieuLinhThuocGiaTriCao").hide();
        $("#toolbarIdtbInPhieuLinhMau").hide();
        $("#toolbarIdtbInPLMauNgoaiVien").hide();
        $("#toolbarIdtbInPhieuLinhThuocTongHop").hide();
        $("#toolbarIdtbInPhieuCungCapMauPL9").hide();
        $("#toolbarIdtbInXuatMyPham").hide();
        $("#toolbarIdtbInXuatSinhPham").hide();
        $("#toolbarIdtbInXuatYDungCu").hide();
        $("#toolbarIdtbInXuatBoSung").hide();
        $("#toolbarIdtbInXuatThuHoi").hide();
        $("#toolbarIdtbInXuatTramYte").hide();
        $("#toolbarIdtbPhieuLinhNTCT").hide();
        $("#toolbarIdtbInPhieuTraThuocHaoPhi").hide();

        $("#toolbarIdtbBBKiemNhapHoaDon").hide();
        $("#toolbarIdtbBBKiemNhapHongVo").hide();
        $("#toolbarIdtbBBKiemNhapXuatChuyenKho").hide();
        $("#toolbarIdtbBBKiemKeHoaDon").hide();
        $("#toolbarIdtbSoKiemNhapHoaDon").hide();
        $("#toolbarIdtbBBKiemNhapHoaDonExcel").hide();
        $("#btnInPhieuBBKiemKe").hide();

        $("#toolbarIdtbBBXuatHuy").hide();
        $("#toolbarIdtbBBXuatHuyPL12").hide();
        $("#toolbarIdtbInLinhTuiMau").hide();
        $("#toolbarIdtbPhieuXuatKhac").hide();
        $("#toolbarIdtbPhieuTraThuochni").hide();
        $("#toolbarIdtbPhieuXuatKhacVatTu").hide();
        $("#toolbarIdtbPhieuXuatKhacHc").hide();
        $("#toolbarIdtbPhieuXuatKhacCCDC").hide();
        $("#toolbarIdtbPhieuXuatHuyVatTu").hide();
        $("#toolbarIdtbPhieuDuTruXa").hide();

        $("#toolbarIdtbXacNhanVPP").hide();
        $("#toolbarIdtbInDonThuocYHCT").hide();
        $("#toolbarIdtbInPhieuYCDieuChuyen").hide();
        $("#toolbarIdtbInPhieuLinhDieuChuyen").hide();
        $("#toolbarIdtbInPhieuXuatDCTHUOC").hide();
        $("#toolbarIdtbInPhieuLinhThuocHaoPhi2Lien").hide();
        $("#toolbarIdtbInPhieuHoanTraDoVai").hide();
        $("#toolbarIdtbInPhieuHoanLinhDoVai").hide();
        $("#toolbarIdtbBatDauSoan").hide();
        $("#toolbarIdtbHuySoan").hide();
        $("#toolbarIdtbInXuatKhacDakSong").hide();
        $("#toolbarIdtbInLinhGNHTBoSungTuTruc").hide();
        $("#toolbarIdtbInPhieuVTCNTTKhoaPhong").hide();
        $("#toolbarIdtbBBNhan_TVT").hide();
        $("#toolbarIdtbInPLinhDSThuocNGT").hide();
        $("#toolbarIdtbInPhieuYDC_VACCIN_TTB").hide();

        if (that.opt.gd == 'MAU') {
            $("#toolbarIdtbTruyenMau").show();
        } else $("#toolbarIdtbTruyenMau").hide();

        if (that.opt.gd == 'MAU' && that.opt.ht == '12' && that.opt.hospitalId == '923') {
            $("#btnMauHoaHop").show();
        }

        if (that.opt.hospitalId == '957') {
            $("#toolbarIdtbInPhieuVTCNTTKhoaPhong").show();
        }
        
        if (that.opt.hospitalId == '1022') {
            $("#toolbarIdtbInPhieuYDC_VACCIN_TTB").show();
        }

		if (that.opt.hospitalId == '36940') {
            $("#toolbarIdtbInPhieuXuatGNHT").show();
        }

        // Dieu chinh kho thuoc => Phieu xuat thuoc gay nghien, huong than
        $("#toolbarIdtbInXuatThuocGayNghienHuongThan").hide();
        var hisOnOffPrint = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'DUC_CHECK_SHOW_PRIND');
        if (hisOnOffPrint != '0' && that.opt.ht == '5,6,7,8') {
            $("#toolbarIdtbInXuatThuocGayNghienHuongThan").show();
        }

        $("#toolbarIdtbInXuatThuocGayNghienHuongThan").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == undefined) {
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            }
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC0034_PHIEUXUATTHUOCGAYNGHIENHUONGTHAN_MSC31HD_A4', 'pdf', par);
        });

        // Them phieu du tru thuoc gay nghien, huong than
        $("#toolbarIdtbInPhieuDuTruGNHT").hide();
        if (that.opt.hospitalId == '23565') {
            $("#toolbarIdtbInPhieuDuTruGNHT").show();
        }

        $("#toolbarIdtbInPhieuDuTruGNHT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == undefined) {
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            }
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'NTU025_PHIEUDUTRUTUTRUCGAYNGHIENHUONGTHAN', 'pdf', par);
        });

        if (that.opt.ht == '1' || (that.opt.ht == '8' && that.opt.lp == '2')) {
            $("#toolbarIdtbBBKiemNhapHoaDon").show();
            $("#toolbarIdtbBBKiemKeHoaDon").show();
            $("#toolbarIdtbSoKiemNhapHoaDon").show();
            if (that.opt.hospitalId == '993' || that.opt.hospitalId == '930') {
                $("#toolbarIdtbBBKiemNhapHoaDonExcel").show();
            }
            if (that.opt.hospitalId == '30680') {
                $("#toolbarIdtbBBKiemNhapHongVo").show();
            }
            if (that.opt.hospitalId == '41140') {
                $("#btnInPhieuBBKiemKe").show();
            }
        }
        if (that.opt.ht == '7' || that.opt.ht == '8') {
            if (that.opt.hospitalId == '30680') {
                $("#toolbarIdtbBBKiemNhapHongVo").show();
            }
        }

        if ((that.opt.hospitalId == '20200' || that.opt.hospitalId == '27801' || that.opt.hospitalId == '29040' || that.opt.hospitalId == '1048' || that.opt.hospitalId == '30180' || that.opt.hospitalId == '43281') && that.opt.ht == '5,6,7,8' && that.opt.tt == '5,6,7') {
            $("#toolbarIdtbBBXuatHuy").show();
            $("#toolbarIdtbBBXuatHuyPL12").show();
        }
        if (that.opt.hospitalId == '5926' && that.opt.ht == "7") {//5926
            $("#toolbarIdtbBBXuatHuy").show();

        }
        if (that.opt.ht == '5,6,7,8' && that.opt.tt == '5,6,7' && that.opt.type == '4' && that.opt.lp == '2,3') {
            $("#toolbarIdtbInXuatHmis").show();
            $("#toolbarIdtbInNhapHmis").show();
        }

        if (that.opt.hospitalId == '954' && that.opt.ht == '13') {
            $("#toolbarIdtbInXuatBenhNhan").show();
        }
        if ((that.opt.hospitalId == '60400' || that.opt.hospitalId == '117563' || that.opt.hospitalId == '30300') && that.opt.ht == '13') {
            $("#btnInBangKe").show();
        }

        if (that.opt.hospitalId == '54600' && (that.opt.ht == '12' || that.opt.ht == '2' || that.opt.ht == '13')) {
            $("#toolbarIdtbInXuatKhoDuoc").show();
        }

        if ((that.opt.hospitalId == '10284')) {
            $("#toolbarIdtbPhieuXuatKhac").show();
            $("#toolbarIdtbPhieuTraThuochni").show();
            $("#toolbarIdtbPhieuXuatKhacVatTu").show();
            $("#toolbarIdtbPhieuXuatKhacHc").show();
            $("#toolbarIdtbPhieuXuatKhacCCDC").show();
            $("#toolbarIdtbPhieuXuatHuyVatTu").show();
        }
        if (that.opt.hospitalId == '43380' && that.opt.ht == "9") {
            $("#toolbarIdtbInLinhGNHTBoSungTuTruc").show();
        }
        if (that.opt.hospitalId == '5522' || that.opt.hospitalId == '1111' || that.opt.hospitalId == '5682' || that.opt.hospitalId == '5926' || that.opt.hospitalId == '7282' || that.opt.hospitalId == '8302' || that.opt.hospitalId == '8782' || that.opt.hospitalId == '9901') {
            $("#toolbarIdtbPhieuDuTruXa").show();
        }
        if ((that.opt.hospitalId == '987' || that.opt.hospitalId == '1174' || that.opt.hospitalId == '26720' || that.opt.hospitalId == '28600') && that.opt.ht == "12") {
            $("#toolbarIdtbInPhieuLinhThuocDYNTU").show();
            $("#toolbarIdtbInPhieuLinhThuocDYHAOPHINTU").show();
            $("#toolbarIdtbInLinhTuiMau").show();
        }
        if (that.opt.hospitalId == '1111' && (that.opt.ht == "12" || that.opt.ht == "13")) {
            $("#toolbarIdtbInPhieuLinhMau").show();
        }
        if (that.opt.hospitalId == '1111' && (that.opt.ht == "12" || that.opt.ht == "13") && that.opt.gd == "MAU") {
            $("#toolbarIdtbInPLMauNgoaiVien").show();
        }
        if (that.opt.hospitalId == '23565' && (that.opt.ht == "12" || that.opt.ht == "13") && that.opt.type == "45" && that.opt.gd == "MAU") {
            $("#toolbarIdtbInPhieuCungCapMauPL9").show();
        }
        if ((that.opt.hospitalId == '1111' || that.opt.hospitalId == '20200' || that.opt.hospitalId == '37980') && that.opt.ht == "9") {
            $("#toolbarIdtbInXuatBoSung").show();
            $("#toolbarIdtbInXuatThuHoi").show();
        }
        if (that.opt.hospitalId == '10284' && that.opt.ht == "2") {
            $("#toolbarIdtbInPhieuYCDieuChuyen").show();
            $("#toolbarIdtbInPhieuLinhDieuChuyen").show();
            if (that.opt.gd == 'THUOC') {
                $("#toolbarIdtbInPhieuXuatDCTHUOC").show();
            }
        }
        if (that.opt.hospitalId == '96520' && that.opt.ht == "2") {
            $("#toolbarIdtbBBKiemNhapXuatChuyenKho").show();
            
        }
        // Phieu linh thuoc tong hop nhieu phieu dkdno
        if (that.opt.hospitalId == '948' && (that.opt.ht == '12' || that.opt.ht.includes("9")) && that.opt.td == '14') {
            $("#toolbarIdtbInPhieuLinhThuocTongHop").show();
        }
        // Phieu nhap kho (Hoan tra) DLKHA
        if (that.opt.hospitalId == '1108') {
            $("#toolbarIdtbInNhapHoanTra").show();
        }

        if ((that.opt.hospitalId == '1108') && that.opt.ht == "4") {
            $("#toolbarIdtbInXuatMyPham").show();
            $("#toolbarIdtbInXuatSinhPham").show();
            $("#toolbarIdtbInXuatYDungCu").show();
        }

        if ((that.opt.hospitalId == '30680') && that.opt.ht == "4") {
            $("#toolbarIdtbInPhieuLinhThuocHaoPhi2Lien").show();
        }

        if (that.opt.hospitalId == '1048' && that.opt.ht == "2") {
            $("#toolbarIdtbInXuatTramYte").show();
        }
        if ((that.opt.hospitalId == '993' || that.opt.hospitalId == '930') && that.opt.ht == "12") {
            $("#toolbarIdtbInPhieuLinhThuocGiaTriCao").show();
        }
        if (that.opt.hospitalId == '965' && that.opt.ht == "12") {
            $("#toolbarIdtbPhieuLinhNTCT").show();
        }

        if (cauHinhNutInYHCT == '1' && that.opt.ht == "12") {
            $("#toolbarIdtbInDonThuocYHCT").show();
        }

        if (((that.opt.type.includes("45") && (that.opt.ht == "2" || that.opt.ht == "9")) && that.opt.td != '12')
            || (that.opt.ht == "12" || that.opt.ht == "13" && that.opt.gd == 'MAU')
        ) {
            $("#toolbarIdtbDieuChinhDon").show();
        }
        if (((that.opt.type.includes("45") && (that.opt.ht == "2" || that.opt.ht == "9")) && that.opt.td != '12')) {
            $("#toolbarIdtbBoSungNoThuoc").show();
        }

        if (that.opt.hospitalId == '1111' && that.opt.ht == "13") {
            $("#btnInDonThuoc").show();
        } else $("#btnInDonThuoc").hide();

        if (that.opt.hospitalId =="52555") {
            $("#toolbarIdtbInPhieuLinhVaccin").show();
        }

        if (that.opt.type.includes("0")) {
//			$("#toolbarIdtbNhapKho").show();
            // tuan them
            $("#btnNhapKho").show();
            $("#toolbarIdtbYCXuat").show();
            //$("#toolbarIdtbHoanTraTVTHetHan").show();
            $("#toolbarIdtbSua").show();
        }

        if (that.opt.type.includes("1")) {
//			$("#toolbarIdtbXuatTra").show();
            $("#btnXuatTra").show();
            $("#toolbarIdtbSua").show();
        }
        if (that.opt.type.includes("2")) {
            $("#toolbarIdtbYCNhap").show();
            $("#toolbarIdtbSua").show();
        }
        if (that.opt.type.includes("3")) {
            //$("#toolbarIdtbYCXuat").show();
            $("#toolbarIdtbSua").show();
        }
        if (that.opt.type.includes("4") || that.opt.td == '1') {
            $("#toolbarIdtbDuyet").show();
            $("#toolbarIdtbGoiBN").show();
//			$("#toolbarIdtbInPhieuLinhThuoc").show();
//			$("#toolbarIdprint_1").show();
//			$("#toolbarIdprint_2").show();
        }
        if (that.opt.type.includes("5")) {
            //$("#toolbarIdtbTaoPhieu").show();
//			$("#toolbarIdtbInPhieuLinhThuoc").show();
//			$("#toolbarIdprint_1").show();
//			$("#toolbarIdprint_2").show();
        }
        if (that.opt.type.includes("6")) {
            $("#toolbarIdtbNhapKhoNCC").show();
            //$("#toolbarIdtbNhapKho_PP").show();
            //$("#toolbarIdtbNhapKho_NT").show();
            $("#btnNhapKhoNCC").show();
            $("#toolbarIdtbSuaNCC").show();
            $("#toolbarIdtbBoSung").show();
        }


        if (that.opt.type.includes("7")) {
            $("#toolbarIdtbXuatTraNCC").show();
            $("#toolbarIdtbXuatTraNCC_TK").show();
//			$("#btnXuatTraNCC").show();
            $("#toolbarIdtbSuaNCC").show();
            $("#toolbarIdtbBoSung").show();
        }
        if (that.opt.type.includes("8")) {
            $("#toolbarIdtbNhapBu").show();
            $("#toolbarIdtbSuaNhapBu").show();
        }
        if (that.opt.type.includes("9")) {
            $("#toolbarIdtbYCNhapBu").show();
            $("#toolbarIdtbSuaNhapBu").show();
        }
        if (that.opt.type.includes("A")) {
            $("#toolbarIdtbYCXuatHuy").show();
            $("#toolbarIdtbSuaXuatHuy").show();
        }
        if (that.opt.type.includes("B")) {
            //	$("#toolbarIdtbXuatHuy").show();
            $("#toolbarIdtbSuaXuatHuy").show();
        }
        if (that.opt.type.includes("C")) {
            $("#toolbarIdtbYCXuatThieu").show();
            $("#toolbarIdtbSuaXuatThieu").show();
        }
        if (that.opt.type.includes("D")) {
            //$("#toolbarIdtbXuatThieu").show();
            $("#toolbarIdtbSuaXuatThieu").show();
        }
        if (that.opt.type.includes("E")) {
            $("#toolbarIdtbHoanTra").show();
            $("#toolbarIdtbSua").show();
        }
        if (that.opt.type.includes("I")) {
            $("#toolbarIdtbYCXuatDTTH").show();
            $("#toolbarIdtbXuatDTTH").show();
            $("#toolbarIdtbSuaDTTH").show();
            $("#toolbarIdtbHoanTraDTTH").show();
            $("#toolbarIdtbNhapTra").show();

        }
        if (that.opt.type.includes("F")) {
            $("#toolbarIdtbDuyetHoanTra").show();
        }
        if (that.opt.type.includes("G")) {
            $("#toolbarIdtbBanThuoc").show();
            //$("#toolbarIdtbPhieuThuBanThuoc").show();
            $("#toolbarIdtbSuaPhieuBanTHuoc").show();
            $("#toolbarIdtbTraThuoc").show();


        }
        if (that.opt.type.includes("H")) {
            $("#toolbarIdtbYCXuatKhac").show();
            $("#toolbarIdtbSuaXuatKhac").show();
        }
        if (that.opt.type.includes("J")) {
            //$("#toolbarIdtbXuatKhac").show();
            $("#toolbarIdtbSuaXuatKhac").show();
        }
        if (that.opt.type.includes("K")) {
            $("#toolbarIdtbSuaYLenhLT").show();
            $("#toolbarIdtbXuatYLenhLT").show();
            $("#toolbarIdtbHTraYLenhLT").show();


        }
        if (that.opt.type.includes("L")) {
            $("#toolbarIdtbKhoaThuoc").show();


        }


        if (that.opt.ht == "2") {
            $("#btnDaThanhToan").show();
            $("#btnGoNhapkho").show();
            $("#toolbarIdtbInNhap_ChuyenKho").show();

        }
        if ((that.opt.ht == "2" || that.opt.ht == "9") && PHARMA_GIAODIEN_NHAP_NCC == 'DUC02N001_NhapThuocNCC_BVNT') {
            $("#toolbarIdtbInPhieuLinhThuoc").show();
            $("#toolbarIdtbInPhieuLinhVatTu").show();
            $("#toolbarIdtbInPLThuocGayNghienHT1Lien").show();

        } else if (that.opt.ht == "12" && that.opt.hospitalId == "45840") {
            $("#toolbarIdtbInPLThuocGayNghienHT1Lien").show();
        }

        if (that.opt.ht == "2" || that.opt.ht == "9") {
            $("#toolbarIdtbInPhieuDuTru").show();
            $("#toolbarIdtbInPhieuDuTruVT").show();
            $("#toolbarIdtbInPXThuocGayNghienHT").show();

        }
        if ((that.opt.ht == "2" || that.opt.ht == "9") && that.opt.gd == 'VATTU') {
            $("#toolbartbInPhieuDuTruVT").show();
        } else {
            $("#btnDaThanhToan").hide();
            $("#btnGoNhapkho").hide();
        }
        if (that.opt.ht == "1") {
            $("#btnDaThanhToan").show();
            $("#btnGoNhapkho").show();

        }

        if (that.opt.ht == "9" && (that.opt.hospitalId == "30360" || that.opt.hospitalId == "31280")) {
            $("#toolbarIdtbInPhieuTraThuocGNHT").show();
        }

        if (that.opt.ht == "8" && that.opt.hospitalId == "41080") {
            $("#toolbarIdtbInXuatKhacDakSong").show();
        }

        if (that.opt.ht == "13" && PHARMA_HIENTHI_GOIBENHNHAN_KB == '1') {
            $("#toolbarIdtbLCD").show();
            $("#toolbarIdtbGoiBenhNhan").show();
        }
        // chức năng hoàn thiện hồ sơ ký gửi.
//		if(that.opt.ht = "10"){
//			$("#toolbarIdtbHoanThien").show();
//			$("#toolbarIdtbXuatTraNCC_TK").hide();
//			$("#toolbarIdtbXuatTraNCC").hide();
//			$("#toolbarIdtbNhapKhoNCC").hide();
//			$("#toolbarIdtbSuaNCC").show();
//		}else {
//			$("#toolbarIdtbHoanThien").hide();
//		}
        /*	if(that.opt.ht == "15"){
                $("#btnXoaDonMuaNgoai").show();
                $("#btnXoaDonMuaNgoai").prop("disabled",false);
            }
            else{
                $("#btnXoaDonMuaNgoai").hide();
            }*/
        // cau hinh phieu in
        //1. Phieu in nha cung cap. lk=1&lp=0,1&tt=1,4&ht=1&type=67&gd=THUOC
//		if(that.opt.type=='67'&&that.opt.ht=='1'){
//			$("#toolbarIdtbIn").show();
//		}
        //2. phieu linh thuoc va phieu linh gay nghien huong than, phieu tra thuoc.lp=2,3&ht=12&tt=5,6,7&type=45&gd=THUOC
        if (that.opt.type == '45' && that.opt.lp == '2,3' && that.opt.gd == 'THUOC'
            && (that.opt.ht == "12" || that.opt.ht == "13"
            )) {
            //$("#toolbarIdtbInPhieuLinhThuoc").show();
            $("#toolbarIdprint_1").show();

            $("#toolbarIdtbInPhieu2Lien").show();
            $("#toolbarIdtbInPhieuLinh").show();
            $("#tbInPhieuXuatGNHT").show();
			$("#tbInPhieuXuatKHTH").show();

            $("#toolbarIdtbInPhieuLinhHoaChat").show();

            $("#toolbarIdtbInPLinhThuocNGT").show();
            $("#toolbarIdtbInDonThuocNGT").show();

            $("#toolbarIdtbInPhieuLinhTVTHaoPhi2Lien").show();
            $("#toolbarIdtbInPhieuHoanTraDTTH").show();
            $("#toolbarIdtbInPhieuBaoHongDCCC").show();
            $("#toolbarIdtbInLinhTVTBoSungCoSoTuTruc2Lien").show();
            $("#toolbarIdtbInHoanTraCoSoTuTruc2Lien").show();
            $("#toolbarIdtbInNhapXuatThuocTuNhaThuoc").show();//20180906
            $("#toolbarIdtbInBienBanTraThuoc").show();//20180927
            //	$("#toolbarIdtbInPhieu2Lien").show();
//			$("#toolbarIdtbIn").show();
        }
        if (that.opt.ht == "12") {
            $("#toolbarIdprint_11").show();
            $("#toolbarIdtbInDonThuocRaVien").show();
            $("#toolbarIdtbInDonThuocBenhNhan").show();
            $("#toolbarIdprint_10").show();

        }
        
        if (that.opt.hospitalId == "1086" && that.opt.ht == "13" && that.opt.lp == "2,3" && that.opt.type == "45" && that.opt.gd == "THUOC") {
            $("#toolbarIdtbInPhatThuocNgoaiTru").show();
        }
        
        if (that.opt.hospitalId == "133099" && that.opt.ht == "13") {
            $("#toolbarIdtbInPLinhDSThuocNGT").show();
        }
        
        if ((that.opt.hospitalId == "993" || that.opt.hospitalId == '930') && that.opt.ht == "4" && that.opt.lp == "3" && that.opt.type == "I" && that.opt.gd == "XUATDTTHKP") {
            $("#toolbarIdtbInPhieuLinhVPP").show();
        }
        if (that.opt.hospitalId == "40480" && that.opt.ht == "4") {
            $("#toolbarIdtbInPhieuLinhVPP").show();
        }
        if ((that.opt.hospitalId == "993" || that.opt.hospitalId == '930') && that.opt.ht == "1" && that.opt.lp == "0,1,2,3" && that.opt.type == "67" && that.opt.gd == "VPP") {
            $("#toolbarIdtbInNhapVPP").show();
        }
        if ((that.opt.hospitalId == "993" || that.opt.hospitalId == '930') && that.opt.ht == "4" && that.opt.lp == "3" && that.opt.type == "4") {
            $("#toolbarIdtbInXuatVPP").show();
            $("#toolbarIdtbInDuTruMuaVPP").show();
        }
        if ((that.opt.lp == '2,3' && (that.opt.ht == '2' || that.opt.ht == '9') && that.opt.type == '0123E' && that.opt.gd == 'THUOC')
            || ((that.opt.lp == '2,3' || that.opt.lp == '3') && (that.opt.ht == '2' || that.opt.ht == '9' || that.opt.ht == '12') && that.opt.type == '45' && that.opt.gd == 'THUOC')
        ) {

            $("#toolbarIdprint_3").show();
            $("#toolbarIdprint_10").show();
            $("#toolbartbInPhieuDuTru").show();
            $("#toolbarIdtbInPXThuocGayNghienHT").show();
            $("#toolbarIdtbInPhieu2Lien").show();

            $("#toolbarIdtbInPhieuLinh").show();
            $("#tbInPhieuXuatGNHT").show();
			$("#tbInPhieuXuatKHTH").show();


            $("#toolbarIdtbInPhieuLinhHoaChat").show();
            $("#toolbarIdtbInPhieuLinhTVTHaoPhi2Lien").show();
            $("#toolbarIdtbInPhieuHoanTraDTTH").show();
            $("#toolbarIdtbInPhieuBaoHongDCCC").show();
            $("#toolbarIdtbInLinhTVTBoSungCoSoTuTruc2Lien").show();
            $("#toolbarIdtbInHoanTraCoSoTuTruc2Lien").show();//20180906
            $("#toolbarIdtbInNhapXuatThuocTuNhaThuoc").show();
            $("#toolbarIdtbInBienBanTraThuoc").show();//20180927
            $("#toolbarIdtbInXuatThuocTraKhoChinh").show();//20181008

        }

        if (that.opt.ht.includes("5") || that.opt.ht.includes("6") || that.opt.ht.includes("7") || that.opt.ht.includes("8")) {
            $("#toolbarIdprint_10").show();
        }

        if ((that.opt.lp == '2,3' || that.opt.lp == '3') && (that.opt.ht == '2' || that.opt.ht == '9' || that.opt.ht == '5,6,7' || that.opt.ht == '5,6,7,8')) {
            $("#toolbarIdtbInXuatChuyenKho").show();

        }
        if ((that.opt.lp == '2,3' || that.opt.lp == '2') && (that.opt.ht == '2' || that.opt.ht == '9' || that.opt.ht == '5,6,7' || that.opt.ht == '5,6,7,8')) {
            $("#toolbarIdprint_3").show();

        }
        if (that.opt.lp == '2' && that.opt.ht == '9') {
            $("#toolbarIdtbInBienBanKiemNhapHoanTra").show();
        }
        if (that.opt.lp == '2' && that.opt.ht == '9') {
            $("#toolbarIdtbInBienBanKiemNhapVacXin").show();
        }

        if (that.opt.ht == '9' && that.opt.hospitalId == "8982") {
            $("#toolbarIdtbBBNhan_TVT").show();
        }
        if (that.opt.hospitalId == "92001") {
            $("#toolbarIdtbInTHPhieuLinhHDG").show();
        }

        if (that.opt.gd == 'VATTU') {
            $("#toolbarIdtbInPhieuVatTu2Lien").show();
        }
        if (that.opt.ht == '4') {
            $("#toolbarIdtbInPhieuTraThuocHaoPhi").show();
        }
        if (that.opt.ht == '15' || that.opt.ht == '1') {
            $("#toolbarIdtbInXuatNhaThuoc").show();
        }

        if (that.opt.hospitalId == "29140" && that.opt.ht == '12') {
            $("#toolbarIdtbInPhieu2Lien").hide();
        }

        //Phieu linh vat tu tieu hao
//		if((that.opt.type=="I"&&that.opt.ht=="4"&&that.opt.lp=="3"&&that.opt.gd=="XUATDTTHKP")
//				||(that.opt.lp=="3"&&that.opt.type=="4"&&that.opt.lk=="3,7,10,11"&&that.opt.tt=="1,5,6,7")	){
//			$("#toolbarIdprint_2").show();
//		}
        //Phieu Biên bản thanh lý thuốc và hóa chất vật tư
        //Biên bản xác nhận thuốc/hóa chất/vật tư y tế mất/hỏng/vỡ
        //lp=3&ht=7&type=AB&gd=XUATHUYTHUOC
        //lp=3&ht=7&type=AB&gd=XUATHUYVATTU
        //lp=2,3&ht=5,6,7&type=4&gd=THUOC
        //lp=2,3&ht=5,6,7&type=4&gd=VATTU
        if ((that.opt.lp == "3" && that.opt.ht == "7" && that.opt.type == "AB" && (that.opt.gd == "XUATHUHAOCB" || that.opt.gd == "XUATHUYTHUOC" || that.opt.gd == "XUATHUYVATTU"))
            || (that.opt.lp == "2,3" && (that.opt.ht == "5,6,7" || that.opt.ht == "5,6,7,8") && that.opt.type == "4" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU"))
        ) {
            $("#toolbarIdprint_4").show();
            $("#toolbarIdprint_4_1").show();
            $("#toolbarIdprint_5").show();
        }
        //phieu tra thuoc benh nhan.
        if (that.opt.gd == "THUOC" && that.opt.type == "4" && that.opt.ht == "13") {
            $("#toolbarIdprint_6").show();
        }
        //Phieu tra vat tu
        if (((that.opt.ht == "2" || that.opt.ht == "9") && that.opt.gd == "VATTU")
            || ((that.opt.ht == "13" || that.opt.ht == "12" || that.opt.ht == "2") && that.opt.gd == "VATTU" && that.opt.type == "45")
            || (that.opt.ht == "9" && that.opt.type == "45")
            || that.opt.ht == "4"
        ) {
            $("#toolbarIdprint_7").show();
        }
        // Ban Thuoc cho khach le
        if (that.opt.type.includes("G") && that.opt.ht.includes("15")) {
            $("#toolbarIdprint_8").show();
            $("#toolbarIdtbphieutrathuoc").show();

        }
        // Phieu tra hang
        if (that.opt.ht == "12" || that.opt.ht == "9") {
            $("#toolbarIdtbInPhieuTraHang").show();

        }

        if (that.opt.ht == "9" && that.opt.gd == "VATTU" && that.opt.hospitalId == "923") {
            $("#toolbarIdtbInPhieuHoanTraDoVai").show();
            $("#toolbarIdtbInPhieuHoanLinhDoVai").show();
        }

        if (that.opt.gd == 'XUATHUHAOCB') {
//			$("#toolbarIdtbYCXuatHuy").val( 'YC Hư Hao CB');
            $('#toolbarIdtbYCXuatHuy').text('YC Hư Hao CB');
//			$("span", this).text("My NEW Text");

        }
        if (that.opt.gd == 'XUATDTTHKP_VPP') {
            $('#toolbarIdtbXuatDTTH').css("display", "none");
            $('#toolbarIdtbSuaDTTH').css("display", "none");
            $('#toolbarIdtbHoanTraDTTH').css("display", "none");
            $('#toolbarIdtbNhapTra').css("display", "none");

        }
        if (that.opt.gd == 'XNDTTHKP_VPP') {
            $("#toolbarIdtbXacNhanVPP").show();
            $('#toolbarIdtbXuatDTTH').css("display", "none");
            $('#toolbarIdtbSuaDTTH').css("display", "none");
            $('#toolbarIdtbHoanTraDTTH').css("display", "none");
            $('#toolbarIdtbNhapTra').css("display", "none");
            $('#toolbarIdtbYCXuatDTTH').css("display", "none");
        }

        //Duyet duoc lam san
        if (that.opt.td == "9" && that.opt.ht.includes("9")) {
            $("#toolbarIdtbDuyetLamSan").show();
            $("#toolbarIdtbDuyet").hide();
            $("#toolbarIdtbDieuChinhDon").hide();
            $("#toolbarIdtbBoSungNoThuoc").hide();
            $("#ttDuyet").hide();
            $("#ttDuyetLS").show();
        }

        //Duyet duoc phat thuoc -- DKLAN
        if (that.opt.td == "14" && (that.opt.ht.includes("9") || that.opt.ht.includes("12") || that.opt.ht.includes("13"))) {
            $("#toolbarIdtbDuyetPhatThuoc").show();
            $("#toolbarIdtbGoDuyetPhatThuoc").show();
            $("#toolbarIdtbDuyet").hide();
            $("#toolbarIdtbDieuChinhDon").hide();
            $("#toolbarIdtbBoSungNoThuoc").hide();
            $("#ttDuyet").hide();
            $("#ttDuyetPT").show();

            $("#btnGoDuyet").hide();
            $("#btnGoNhapkho").hide();
            $("#btnXemPhieu").hide();
            $("#btnDonThuoc").hide();
            $("#btnGoTuChoi").hide();


        }
        if (PHARMA_THEMCHUADUYETKT == '2' && that.opt.ht == '13') {
            $("#ttDuyetVP").show();
        } else
            $("#ttDuyetVP").hide();

        if (that.opt.ht == "22") {
            $("#btnGoNhapkho").hide();
            $("#toolbarIdbtnPrint").hide();
            $("#btnXemPhieu").hide();

            $("#btnBoKhoa").show();

            //$('#btnGoDuyet').removeAttr("disabled");
        }

        if (that.opt.gd == "TRACUUNHAPKHO") {

            $("#toolbarIdbtnPrint").hide();
            $("#btnXemPhieu").hide();
            $("#toolbarIdtbSuaNCC").hide();
            $("#toolbarIdtbSua").hide();
        }
        if (PHARMA_DUYETNHIEUPHIEU == '1' && that.opt.ht == '13')
            $("#btnDuyetNhieuDon").show();
        else $("#btnDuyetNhieuDon").hide();
        // end cau hinh phieu in

        var _gridHeader;
        if (that.opt.ht.includes("13")) {
            var _par = ['PHARMA_COL_PHIEUYC_BN'];
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
                _par.join('$'));
            if (result != '0' && result != null)
                _gridHeader = result;
            else {
                _gridHeader = ",icon,30,0,ns,l;nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "Phiếu,MA,60,0,f,l;" +
                    "Loại,TEN_KIEU,60,0,f,l;" +
                    "Đơn thuốc/VT,SOPHIEU,90,0,f,l;" +
                    "Họ tên,TENBENHNHAN,150,0,f,l;" +
                    "Mã BA,MAHOSOBENHAN,85,0,f,l;" +
                    "Mã BN,MABENHNHAN,85,0,f,l;" +
                    "Ngày sinh,NGAYSINH,70,0,f,c;" +
                    "Tổng tiền,TONGTIEN,80,decimal!2,f,l!f;"+
                    "GT,GIOITINH,40,0,f,l;" +
                    "Khoa,TENKHO,110,0,f,l;" +
                    "Phòng,PHONG,110,0,f,l;" +
                    "Trạng thái,TRANGTHAI,80,0,f,l;" +
                    "Ngày NX,NGAYNX,90,0,f,c;" +
                    "Số CT,SOCHUNGTU,50,0,f,l;" +
                    "CHANDOAN,CHANDOAN,0,0,t,l;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "KHOID,KHOID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;" +
                    "Ngày duyệt,NGAYDUYET,90,0,f,c;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l";
            }
            if (that.opt.td == '14') {
                _gridHeader = " ,icon,30,0,ns,l;" +
                    "nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "TTDUYETPHATTHUOC,TTDUYETPHATTHUOC,0,0,t,l;" +
                    "Số CT,SOCHUNGTU,50,0,f,l;" +
                    "Phiếu,MA,110,0,f,l;" +
                    "Duyệt phát,DUYETPHATTHUOC,110,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    "Ngày duyệt phát,NGAYDUYETPHAT,110,0,f,l;" +
                    "Đơn thuốc/VT,SOPHIEU,90,0,f,l;" +
                    "Mã BN,MABENHNHAN,85,0,f,l;" +
                    "Họ tên,TENBENHNHAN,150,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    "Người duyệt,NGUOIDUYET,110,0,f,l;" +
                    "Người tạo,NGUOINX,110,0,f,l;" +
                    "Đối tượng NX,TENKHO,130,0,f,l;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Người duyệt phát,NGUOIDUYETPHAT,110,0,f,l;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "LOAIKEDON,LOAIKEDON,0,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l";

            }
            if (that.opt.td == '14') {
                GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", true, _gridHeader, false);
                GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);
            } else {
                if (PHARMA_DUYETNHIEUPHIEU == '1')
                    GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", true, _gridHeader, false);
                else if (that.opt.hospitalId == '930')
                    GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", false, _gridHeader, false, {
                        rowNum: 100,
                        rowList: [20, 500, 1000, 2000, 5000]
                    });
                /*else if(that.opt.hospitalId == '987'){
                	GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", false, _gridHeader, false, {
                        loadonce: false
                        , sqltype: 'sp'
                    }, "VN");
                }*/
                else
                    GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", false, _gridHeader, false);

                GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);
            }
        } else if (that.opt.ht.includes("12")) {
//			PHARMA_COL_PHIEUYC
            var _par = ['PHARMA_COL_PHIEUYC'];
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
                _par.join('$'));
            if (result != '0' && result != null)
                _gridHeader = result;
            else {
                var icon2 = "";
                if (PHARMA_HIENTHI_YLENH_BN_RAVIEN == '1')
                    var icon2 = " ,icon2,30,0,ns,l;";
                var tongtien = "";
                if (that.opt.hospitalId == "41080" || that.opt.hospitalId == "49900") {
                    tongtien = "Tổng tiền,TONGTIEN,80,decimal,f,l;";
                }

                _gridHeader = " ,icon,30,0,ns,l;" +
                    icon2 +
                    "nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "Số CT,SOCHUNGTU,50,0,f,l;" +
                    "Phiếu,MA,110,0,f,l;" +
                    "Người duyệt,NGUOIDUYET,110,0,f,l;" +
                    tongtien +
                    "Đối tượng NX,TENKHO,130,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    "Người tạo,NGUOINX,110,0,f,l;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "LOAIKEDON,LOAIKEDON,0,0,t,l;" +
                    "DAYEUCAU,DAYEUCAU,0,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l";
            }
            if (that.opt.td == '14') {
                var maphieucha = '';
                if (PHARMA_SHOW_PHIEUCHA_NTU == '1') {
                    maphieucha = "Phiếu cha,MAPHIEUCHA,110,0,f,l;";
                }
                _gridHeader = " ,icon,30,0,ns,l;" +
                    "nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "TTDUYETPHATTHUOC,TTDUYETPHATTHUOC,0,0,t,l;" +
                    "Số CT,SOCHUNGTU,50,0,f,l;" +
                    "Phiếu,MA,110,0,f,l;" +
                    maphieucha +
                    "Duyệt phát,DUYETPHATTHUOC,110,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    "Ngày duyệt phát,NGAYDUYETPHAT,110,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    "Người duyệt phát,NGUOIDUYETPHAT,110,0,f,l;" +
                    "Người tạo,NGUOINX,110,0,f,l;" +
                    "Đối tượng NX,TENKHO,130,0,f,l;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Người duyệt,NGUOIDUYET,110,0,f,l;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "LOAIKEDON,LOAIKEDON,0,0,t,l;" +
                    "DAYEUCAU,DAYEUCAU,0,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l";

            }
//			GridUtil.init("grdPhieu","100%","370px","Danh sách phiếu",false,_gridHeader);
            if (that.opt.td == '14') {
                GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", true, _gridHeader, false);
                GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);
            } else {
                GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", false, _gridHeader, false);
                GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);
            }
        } else if (that.opt.ht.includes("9")) {
//			PHARMA_COL_PHIEUYC
            var _par = ['PHARMA_COL_PHIEUTUTRUC'];
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
                _par.join('$'));

            var tongtien = "";
            if (that.opt.hospitalId == "41080" || that.opt.hospitalId == "49900") {
                tongtien = "Tổng tiền,TONGTIEN,80,decimal,f,l;";
            }

            if (result != '0' && result != null)
                _gridHeader = result;
            else

                _gridHeader = " ,icon,30,0,ns,l;nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "Số CT,SOCHUNGTU,50,0,t,l;" +
                    "Phiếu,MA,90,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    "Đối tượng NX,TENKHO,130,0,f,l!s;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    tongtien +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "KHOID,KHOID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l;" +
                    "DAYEUCAU,DAYEUCAU,0,0,t,l;" +
                    "Người duyệt,NGUOIDUYET,100,0,f,l";
            if (that.opt.td == "9") {
                _gridHeader = " ,icon,30,0,ns,l;nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "Số CT,SOCHUNGTU,50,0,t,l;" +
                    "Phiếu,MA,90,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    "Đối tượng NX,TENKHO,130,0,f,l!s;" +
                    "Duyệt lâm sàng,DUYETLAMSAN,130,0,f,l;" +
                    "dangxuly,DANGXULY,0,0,t,l;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "KHOID,KHOID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l;" +
                    "DAYEUCAU,DAYEUCAU,0,0,t,l;" +
                    "Người duyệt,NGUOIDUYET,100,0,f,l";
            }
            if (that.opt.td == '14') {
                _gridHeader = " ,icon,30,0,ns,l;" +
                    "nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "TTDUYETPHATTHUOC,TTDUYETPHATTHUOC,0,0,t,l;" +
                    "Số CT,SOCHUNGTU,50,0,f,l;" +
                    "Phiếu,MA,110,0,f,l;" +
                    "Duyệt phát,DUYETPHATTHUOC,110,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    "Ngày duyệt phát,NGAYDUYETPHAT,110,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    "Người duyệt,NGUOIDUYET,110,0,f,l;" +
                    "Ngày duyệt,NGAYDUYET,110,0,f,l;" +
                    "Người tạo,NGUOINX,110,0,f,l;" +
                    "Đối tượng NX,TENKHO,130,0,f,l;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Người duyệt phát,NGUOIDUYETPHAT,110,0,f,l;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "LOAIKEDON,LOAIKEDON,0,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l";

            }
            /*var _par = ['PHARMA_DUYET_DLS_BOSUNG_TUTRUC'];
			var PHARMA_DUYET_DLS_BOSUNG_TUTRUC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
					_par.join('$'));*/
            if (PHARMA_DUYET_DLS_BOSUNG_TUTRUC == '1' && that.opt.td != "9" && !that.opt.type.includes("45")) {
                _gridHeader = " ,icon,30,0,ns,l;nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "Số CT,SOCHUNGTU,50,0,t,l;" +
                    "Phiếu,MA,90,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    "Đối tượng NX,TENKHO,130,0,f,l!s;" +
                    "Duyệt lâm sàng,DUYETLAMSAN,130,0,f,l;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "KHOID,KHOID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l;" +
                    "DAYEUCAU,DAYEUCAU,0,0,t,l;" +
                    "Người duyệt,NGUOIDUYET,100,0,f,l";
            }
//			GridUtil.init("grdPhieu","100%","370px","Danh sách phiếu",false,_gridHeader);
//			GridUtil.init("grdPhieu","100%","370px","Danh sách phiếu",false,_gridHeader,false);
//			GridUtil.addExcelButton("grdPhieu",'Xuất excel',true);
            if (that.opt.td == '14') {
                GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", true, _gridHeader, false);
                GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);
            } else {
                GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", false, _gridHeader, false);
                GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);
            }
        } else if (that.opt.ht.includes("7") || that.opt.ht.includes("8")) {
            var colNguoiDuyet2 = '';
            if (PHARMA_TUDONG_TAOPHIEUNHAPXUAT == '0') {
                colNguoiDuyet2 = "Người duyệt 2,NGUOIDUYET2,100,0,f,l;Ngày duyệt 2,NGAYDUYET2,100,0,f,l;"
            }
            var sct = "Số CT,SOCHUNGTU,50,0,f,l;"
            if (that.opt.hospitalId == '10284') {
                sct = "Số HĐ,SOCHUNGTU,50,0,f,l;"
                $("#lbSCT").text('Số HĐ');
            }
            var kho_tyt = '';
            var tt_guitrahmis = '';
            if (PHARMA_CHECK_LIENTHONG_HMIS == '1') {
                kho_tyt = "Kho dược TYT,TENKHO_TYT,130,0,f,l;Mã phiếu TYT,MA_PHIEU_TYT,130,0,f,l;";
                tt_guitrahmis = "TRANGTHAIIDHMIS,TRANGTHAIIDHMIS,0,0,t,l;Trạng thái gửi HMIS,TRANGTHAIHMIS,130,0,f,l;";
            }
            var _par = ['PHARMA_HMIS_COL_PHIEU'];
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
                _par.join('$'));
            if (result != '0' && result != null && PHARMA_CHECK_LIENTHONG_HMIS == '1')
                _gridHeader = result;
            else
                _gridHeader = " ,icon,30,0,ns,l;nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    sct +
                    "Phiếu,MA,110,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    kho_tyt +
                    "Lý do,LYDO,130,0,f,l;" + //DucTT20190802 -chuannt chinh lai cho xuat huy , xuat khac
                    "Đối tượng NX,TENKHO,130,0,f,l;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "KHOID,KHOID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l;" +
                    "Ngày duyệt,NGAYDUYET,100,0,f,l;" +
                    "Người duyệt,NGUOIDUYET,100,0,f,l;"
                    +"Tổng tiền,TONGTIEN,80,decimal,f,l;"
                    + colNguoiDuyet2 + tt_guitrahmis +
                    "Bệnh nhân,TENBENHNHAN,100,0,f,l"; //DucTT20181024
            GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", false, _gridHeader, false);
            GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);
        } else if (that.opt.ht == '15') {
            var _par = ['PHARMA_NT_COL_PHIEUNT'];
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
                _par.join('$'));
            if (result != '0' && result != null)
                _gridHeader = result;
            else
                _gridHeader = " ,icon,30,0,ns,l;nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "Số CT,SOCHUNGTU,50,0,f,l;" +
                    "Phiếu,MA,110,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    "Đối tượng NX,TENKHO,130,0,f,l;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    "Khánh hàng,TENBENHNHAN,100,0,f,l;" +
                    "Địa chỉ,DIACHI,100,0,f,l;" +
                    "Giới tính,GIOITINHNX,40,0,f,l;" +
                    "Năm sinh,NAMSINH,40,0,f,l;" +
                    "Người duyệt,NGUOIDUYET,100,0,f,l;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "KHOID,KHOID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l";
            GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", false, _gridHeader, false);
            GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);
        } else if (that.opt.ht == '1') {
            var _par = ['PHARMA_COL_PHIEUNCC'];
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
                _par.join('$'));
            if (result != '0' && result != null)
                _gridHeader = result;
            else
                _gridHeader = " ,icon,30,0,ns,l;nhapxuatid,NHAPXUATID,0,0,t,l;" +
                    "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                    "hinhthucid,HINHTHUCID,0,0,t,l;" +
                    "Số CT,SOCHUNGTU,50,0,f,l;" +
                    "Phiếu,MA,110,0,f,l;" +
                    "Loại,TEN_KIEU,80,0,f,l;" +
                    "Đối tượng NX,TENKHO,130,0,f,l;" +
                    "Trạng thái,TRANGTHAI,90,0,f,l;" +
                    "Ngày NX,NGAYNX,110,0,f,c;" +
                    "Tổng tiền,TONGTIEN,100,decimal,f,r;" +
                    "Người duyệt,NGUOIDUYET,100,0,f,l;" +
                    "Ghi chú,GHICHU,100,0,f,l;" +
                    "kieu,KIEU,0,0,t,l;" +
                    "doiungid,DOIUNGID,0,0,t,l;" +
                    "KHOID,KHOID,0,0,t,l;" +
                    "nhapid,NHAPID,0,0,t,l;" +
                    "xuatid,XUATID,0,0,t,l;" +
                    "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                    "Bệnh nhân,TENBENHNHAN,100,0,t,l;" +
                    "ttphieunx,TTPHIEUNX,0,0,t,l";

            GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", false, _gridHeader, false, {}, "VN");
            GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);
        } else {
            //duyet (hien thi ten nguoi duyet)
            var colNguoiDuyet2 = '';
            var colNguonXuat = '';
            if (PHARMA_TUDONG_TAOPHIEUNHAPXUAT == '0' || PHARMA_TUDONG_TAOPHIEUNHAPXUAT == '2') {
                colNguoiDuyet2 = "Người duyệt 2,NGUOIDUYET2,100,0,f,l;Ngày duyệt 2,NGAYDUYET2,100,0,f,l;"
            } else colNguoiDuyet2 = "Ngày duyệt,NGAYDUYET,110,0,f,c;"
            var dtNhapXuat = "Đối tượng NX,TENKHO,130,0,f,l;";
            if (that.opt.ht == '22') {
                dtNhapXuat = "Đối tượng NX,TENKHO,0,0,t,l;";
            }
            if (PHARMA_NGUONXUAT_THKP == '1' && that.opt.ht == '4') {
                colNguonXuat = "Nguồn xuất,LYDO,80,0,f,l;";
            }
            _gridHeader = " ,icon,30,0,ns,l;nhapxuatid,NHAPXUATID,0,0,t,l;" +
                "trangthaiid,TRANGTHAIID,0,0,t,l;" +
                "hinhthucid,HINHTHUCID,0,0,t,l;" +
                "Số CT,SOCHUNGTU,50,0,f,l;" +
                "Phiếu,MA,110,0,f,l;" +
                "Loại,TEN_KIEU,80,0,f,l;" +
                colNguonXuat +
                dtNhapXuat +
                "Trạng thái,TRANGTHAI,90,0,f,l;" +
                "Ngày NX,NGAYNX,110,0,f,c;" +
                "Người duyệt,NGUOIDUYET,100,0,f,l;"
                + colNguoiDuyet2 +
                "Tổng tiền,TONGTIEN,100,decimal,f,r;"+
                "kieu,KIEU,0,0,t,l;" +
                "doiungid,DOIUNGID,0,0,t,l;" +
                "KHOID,KHOID,0,0,t,l;" +
                "nhapid,NHAPID,0,0,t,l;" +
                "xuatid,XUATID,0,0,t,l;" +
                "NHAPXUATID_CHA,NHAPXUATID_CHA,0,0,t,l;" +
                "Bệnh nhân,TENBENHNHAN,100,0,t,l;" +
                "DAYEUCAU,DAYEUCAU,0,0,t,l;" +
                "ttphieunx,TTPHIEUNX,0,0,t,l";

//	GridUtil.init("grdPhieu","100%","370px","Danh sách phiếu",false,_gridHeader);
            GridUtil.init("grdPhieu", "100%", "370px", "Danh sách phiếu", false, _gridHeader, false);
            GridUtil.addExcelButton("grdPhieu", 'Xuất excel', true);

        }
        //$("#grdPhieu")[0].toggleToolbar();
        $('#gs_icon').hide();
        $(".clearsearchclass:first").html("");
//		_loadDSPhieu();
        var Ancot = 'f'; // f: ko an cot, t là ẩn cột
        if (checkAnGia(that.opt.ht) == 1) {
            Ancot = 't';
            $('[name=divGia]').hide();
        }
        ;
        if (that.opt.gd == "TRACUUNHAPKHO") {
            _gridHeader = "nhapxuatctid,NHAPXUATCTID,0,0,t,l;" +
                "thuocvattuid,THUOCVATTUID,0,0,t,l;" +
                "tonkhoctid,TONKHOCTID,0,0,t,l;" +
                "lieuluong,LIEULUONG,0,0,t,l;" +
                "HANSUDUNG,HANSUDUNG,0,0,t,l;" +
                "SLKHADUNG,SLKHADUNG,0,0,t,l;" +
                "Mã" + that.opt.title + ",MA,60,0,f,l;" +
                "Tên" + that.opt.title + ",TEN,150,0,f,l;" +
                "Đơn vị,TEN_DVT,60,0,f,l;" +
                "Số lượng,SOLUONG,60,decimal,f,r;" +
                "SL Cấp,SOLUONGDUYET,60,decimal,f,r;" +
                "SL Tồn,SLTONKHO,60,decimal,f,r;" +
                "Đơn giá nhập NCC,GIANHAP,100,decimal,f,r;" +
                "Thành tiền nhập NCC,THANHTIEN,110,decimal,f,r;" +
                "Số lô,SOLO,60,0,f,c;" +
                "Chi tiết sử dụng,CHITIETSD,100,0,f,c,ES;" +
                "Tồn kho,CTTONKHO,80,0,f,c,ES";
        } else if (PHARMA_DUYETNHIEUPHIEU == '1' && that.opt.ht == '13') {
            _gridHeader = "nhapxuatctid,NHAPXUATCTID,0,0,t,l;" +
                "Mã" + that.opt.title + ",MA,60,0,f,l;" +
                "Tên" + that.opt.title + ",TEN,150,0,f,l;" +
                "Hoạt chất,HOATCHAT,60,0,f,l;" +
                "Hàm lượng,LIEULUONG,60,0,f,l;" +
                "Đơn vị,TEN_DVT,60,0,f,l;" +
                "SL yêu cầu,SOLUONG,60,decimal,f,r;" +
                "SL duyệt,SOLUONGDUYET,60,decimal,f,r;" +
                "Đơn giá,GIANHAP,80,decimal," + Ancot + ",r;" +
                "VAT,XUATVAT,80,decimal," + Ancot + ",r;" +
                "Thành tiền,THANHTIEN,80,decimal," + Ancot + ",r;" +
                "Số lô,SOLO,60,0,f,c;" +
                "HSD,HANSUDUNG,60,0,f,c;" +
                "SL khả dụng,SLKHADUNG,90,number!3,f,l;" +
                "SL tồn kho,SLTONKHO,90,number!3,f,l;" +
                "Nhóm BHXH,NHOMBHXH,110,0,f,c;" +
                "BSDuyet,CHOLANHDAODUYET,60,0,f,r;" +
                "CHUY,CHUY,60,0,t,r;" +
                "LOAI,LOAI,60,0,t,l;" +
                "CHANDOAN,CHANDOAN,0,0,t,l;" +
                "TENLOAI,TENLOAI,60,0,t,l;" +
                "THUOCKHOYC,THUOCKHOYC,60,0,t,l;" +
                "TenPhongLuu,TENPHONGLUU,0,0,t,l";
        } else if ((that.opt.ht == 12 || that.opt.ht == 13) && that.opt.hospitalId == '1022' && that.opt.gd != 'MAU') {
            _gridHeader = "nhapxuatctid,NHAPXUATCTID,0,0,t,l;" +
                "Mã" + that.opt.title + ",MA,60,0,f,l;" +
                "Tên" + that.opt.title + ",TEN,150,0,f,l;" +
                "Hàm lượng,LIEULUONG,60,0,f,l;" +
                "Đơn vị,TEN_DVT,60,0,f,l;" +
                "SL yêu cầu,SOLUONG,60,decimal,f,r;" +
                "SL duyệt,SOLUONGDUYET,60,decimal,f,r;" +
                "Số lô,SOLO,60,0,f,c;" +
                "HSD,HANSUDUNG,60,0,f,c;" +
                "Giá BHYT,GIABHYT,80,decimal," + Ancot + ",r;" +
                "Đơn giá,GIANHAP,80,decimal," + Ancot + ",r;" +
                "VAT,XUATVAT,80,decimal," + Ancot + ",r;" +
                "Thành tiền,THANHTIEN,80,decimal," + Ancot + ",r;" +
                "Nhóm BHXH,NHOMBHXH,110,0,f,c;" +
                "BSDuyet,CHOLANHDAODUYET,60,0,f,r;" +
                "CHUY,CHUY,60,0,t,r;" +
                "LOAI,LOAI,60,0,t,l;" +
                "TENLOAI,TENLOAI,60,0,t,l;" +
                "CHANDOAN,CHANDOAN,0,0,t,l;" +
                "THUOCKHOYC,THUOCKHOYC,60,0,t,l;" +
                "TenPhongLuu,TENPHONGLUU,0,0,t,l";
        } else if ((that.opt.ht == 12 || that.opt.ht == 13) && that.opt.gd == 'MAU') {
            _gridHeader = "nhapxuatctid,NHAPXUATCTID,0,0,t,l;" +
                "Mã túi máu,MA,60,0,f,l;" +
                "Tên túi máu,TEN,150,0,f,l;" +
                "Barcode,BARCODE,90,0,f,l;" +
                "Nhóm máu,NHOMMAU,90,0,f,l;" +
                "Hàm lượng,LIEULUONG,60,0,t,l;" +
                "Đơn vị,TEN_DVT,50,0,f,l;" +
                "SL yêu cầu,SOLUONG,60,decimal,f,r;" +
                "SL duyệt,SOLUONGDUYET,60,decimal,f,r;" +
                "Đơn giá,GIANHAP,60,decimal," + Ancot + ",r;" +
                "VAT,XUATVAT,80,decimal," + Ancot + ",r;" +
                "Thành tiền,THANHTIEN,80,decimal," + Ancot + ",r;" +
                "Số lô,SOLO,60,0,f,c;" +
                "HSD,HANSUDUNG,60,0,f,c;" +
                "BSDuyet,CHOLANHDAODUYET,60,0,f,r;" +
                "CHUY,CHUY,60,0,t,r;" +
                "LOAI,LOAI,60,0,t,l;" +
                "TENLOAI,TENLOAI,60,0,t,l;" +
                "THUOCKHOYC,THUOCKHOYC,60,0,t,l;" +
                "TenPhongLuu,TENPHONGLUU,0,0,t,l";
        } else if (that.opt.ht == 1 && that.opt.lk == 4 && (that.opt.hospitalId == '27840' || that.opt.hospitalId == '23565')) {
            _gridHeader = "nhapxuatctid,NHAPXUATCTID,0,0,t,l;" +
                "Mã TVT,MA,60,0,f,l;" +
                "Tên TVT,TEN,150,0,f,l;" +

                "Hàm lượng,LIEULUONG,60,0,t,l;" +
                "Đơn vị,TEN_DVT,50,0,f,l;" +
                "SL yêu cầu,SOLUONG,60,decimal,f,r;" +
                "SL duyệt,SOLUONGDUYET,60,decimal,f,r;" +
                "Thặng số,THANGSO,50,0,f,r;" +
                "Đơn giá,GIANHAP,60,decimal," + Ancot + ",r;" +
                "VAT,XUATVAT,80,decimal," + Ancot + ",r;" +
                "Thành tiền,THANHTIEN,80,decimal," + Ancot + ",r;" +
                "Số lô,SOLO,60,0,f,c;" +
                "HSD,HANSUDUNG,60,0,f,c;" +
                "BSDuyet,CHOLANHDAODUYET,60,0,f,r;" +
                "CHUY,CHUY,60,0,t,r;" +
                "LOAI,LOAI,60,0,t,l;" +
                "TENLOAI,TENLOAI,60,0,t,l;" +
                "THUOCKHOYC,THUOCKHOYC,60,0,t,l;" +
                "TenPhongLuu,TENPHONGLUU,0,0,t,l";
        } else if (PHARMA_HIENTHI_GIABHYT == '1' && (that.opt.ht == 12 || that.opt.ht == 13)) {
            _gridHeader = "nhapxuatctid,NHAPXUATCTID,0,0,t,l;" +
                "Mã" + that.opt.title + ",MA,60,0,f,l;" +
                "Tên" + that.opt.title + ",TEN,150,0,f,l;" +
                "Hàm lượng,LIEULUONG,60,0,f,l;" +
                "Đơn vị,TEN_DVT,60,0,f,l;" +
                "SL yêu cầu,SOLUONG,60,decimal,f,r;" +
                "SL duyệt,SOLUONGDUYET,60,decimal,f,r;" +
                "Giá BHYT,GIABHYT,80,decimal," + Ancot + ",r;" +
                "Đơn giá,GIANHAP,80,decimal," + Ancot + ",r;" +
                "VAT,XUATVAT,80,decimal," + Ancot + ",r;" +
                "Thành tiền,THANHTIEN,80,decimal," + Ancot + ",r;" +
                "Số lô,SOLO,60,0,f,c;" +
                "HSD,HANSUDUNG,60,0,f,c;" +
                "Nhóm BHXH,NHOMBHXH,110,0,f,c;" +
                "BSDuyet,CHOLANHDAODUYET,60,0,f,r;" +
                "CHUY,CHUY,60,0,t,r;" +
                "LOAI,LOAI,60,0,t,l;" +
                "TENLOAI,TENLOAI,60,0,t,l;" +
                "CHANDOAN,CHANDOAN,0,0,t,l;" +
                "THUOCKHOYC,THUOCKHOYC,60,0,t,l;" +
                "TenPhongLuu,TENPHONGLUU,0,0,t,l";
        } else if (that.opt.ht == 1 && that.opt.gd == 'THUOC' && that.opt.hospitalId == '10284') {
            _gridHeader = "nhapxuatctid,NHAPXUATCTID,0,0,t,l;" +
                "Mã" + that.opt.title + ",MA,60,0,f,l;" +
                "Tên" + that.opt.title + ",TEN,150,0,f,l;" +
                "Hàm lượng,LIEULUONG,60,0,f,l;" +
                "Đơn vị,TEN_DVT,60,0,f,l;" +
                "SL yêu cầu,SOLUONG,60,decimal,f,r;" +
                "SL duyệt,SOLUONGDUYET,60,decimal,f,r;" +
                "Đơn giá,GIANHAP,80,decimal," + Ancot + ",r;" +
                "VAT,XUATVAT,80,decimal," + Ancot + ",r;" +
                "Thành tiền,THANHTIEN,80,decimal," + Ancot + ",r;" +
                "Số lô,SOLO,60,0,f,c;" +
                "Số ĐK,SODANGKY,50,0,f,l;" +
                "HSD,HANSUDUNG,60,0,f,c;" +
                "Nhóm BHXH,NHOMBHXH,110,0,f,c;" +
                "BSDuyet,CHOLANHDAODUYET,60,0,f,r;" +
                "CHUY,CHUY,60,0,t,r;" +
                "LOAI,LOAI,60,0,t,l;" +
                "TENLOAI,TENLOAI,60,0,t,l;" +
                "THUOCKHOYC,THUOCKHOYC,60,0,t,l;" +
                "TenPhongLuu,TENPHONGLUU,0,0,t,l";
        } else if (that.opt.ht == 1 && that.opt.gd == 'VATTU' && that.opt.hospitalId == '10284') {
            _gridHeader = "nhapxuatctid,NHAPXUATCTID,0,0,t,l;" +
                "Mã" + that.opt.title + ",MA,60,0,f,l;" +
                "Tên" + that.opt.title + ",TEN,150,0,f,l;" +
                "Hàm lượng,LIEULUONG,60,0,f,l;" +
                "Đơn vị,TEN_DVT,60,0,f,l;" +
                "SL yêu cầu,SOLUONG,60,decimal,f,r;" +
                "SL duyệt,SOLUONGDUYET,60,decimal,f,r;" +
                "Đơn giá,GIANHAP,80,decimal," + Ancot + ",r;" +
                "VAT,XUATVAT,80,decimal," + Ancot + ",r;" +
                "Tiền thuế GTGT,THUEGTGT,80,decimal," + Ancot + ",r;" +
                "Giá BHYT,GIABHYT,80,decimal," + Ancot + ",r;" +
                "Số lô,SOLO,60,0,f,c;" +
                "HSD,HANSUDUNG,60,0,f,c;" +
                "Nhóm BHXH,NHOMBHXH,110,0,f,c;" +
                "BSDuyet,CHOLANHDAODUYET,60,0,f,r;" +
                "CHUY,CHUY,60,0,t,r;" +
                "LOAI,LOAI,60,0,t,l;" +
                "TENLOAI,TENLOAI,60,0,t,l;" +
                "THUOCKHOYC,THUOCKHOYC,60,0,t,l;" +
                "TenPhongLuu,TENPHONGLUU,0,0,t,l";
        } else {
            var vat = "VAT,XUATVAT,80,decimal," + Ancot + ",r;";
            var hamluong = "Hàm lượng,LIEULUONG,60,0,f,l;";
            if (that.opt.ht == 2 && that.opt.hospitalId == '10284') {
                vat = "";
                hamluong = "";
            }
            var huongdan = '';
            if (that.opt.ht == 13 && (that.opt.hospitalId == '996' || that.opt.hospitalId == '46840')) {
                var huongdan = "Hướng dẫn sd,HUONGDANSUDUNG,150,0,f,l;";

            }
            var hoatchat = "";
            if ((that.opt.ht == 12 && that.opt.hospitalId == '930') || (that.opt.ht == 13 && that.opt.hospitalId == '117563') || (that.opt.ht == 1 && that.opt.hospitalId == '1022')) {//117563

                var hoatchat = "Hoạt chất,HOATCHAT,70,0,f,l;";
            }
            var _soluongfm = "SL yêu cầu,SOLUONG,60,decimal!3,f,r;" +
                "SL duyệt,SOLUONGDUYET,60,decimal!3,f,r;";
            if ((that.opt.ht == 1 && that.opt.hospitalId == '57520') || PHARMA_BOSOTHAPPHAN == '1') {//57520
                _soluongfm = "SL yêu cầu,SOLUONG,60,decimal,f,r;" +
                    "SL duyệt,SOLUONGDUYET,60,decimal,f,r;";
            }
            var _tongtienfm = "Đơn giá,GIANHAP,80,decimal!3," + Ancot + ",r;" +
                vat +
                "Thành tiền,THANHTIEN,80,decimal!3," + Ancot + ",r;" ;
            if (PHARMA_BOSOTHAPPHAN_TT == '1') {//57520
                _tongtienfm = "Đơn giá,GIANHAP,80,decimal," + Ancot + ",r;" +
                    vat +
                    "Thành tiền,THANHTIEN,80,decimal," + Ancot + ",r;" ;
            }
            var _chuandoan = "";
            if (PHARMA_SHOW_CHANDOAN_BN == '1' && that.opt.ht == '13') {
                _chuandoan = "Cách dùng,CACHDUNG,130,0,f,c;"
            }
            var loaidt_benhnhan = "";
            if(that.opt.ht == 13 && that.opt.hospitalId == '67510'){//67510
            	loaidt_benhnhan = "Loại TT,LOAITT,100,0,f,c;"
            }
            _gridHeader = "nhapxuatctid,NHAPXUATCTID,0,0,t,l;" +
            	loaidt_benhnhan +
                "Mã" + that.opt.title + ",MA,60,0,f,l;" +
                "Tên" + that.opt.title + ",TEN,150,0,f,l;" +
                hoatchat +
                hamluong +
                "Đơn vị,TEN_DVT,60,0,f,l;" +
                _soluongfm +
                _chuandoan +
                huongdan +
                _tongtienfm +
                "Ngày nhập,NGAYNHAP,60,0,f,c;" +
                "Số lô,SOLO,60,0,f,c;" +
                "HSD,HANSUDUNG,60,0,f,c;" +
                "Nhóm BHXH,NHOMBHXH,110,0,f,c;" +
                "BSDuyet,CHOLANHDAODUYET,60,0,f,r;" +
                "CHUY,CHUY,60,0,t,r;" +
                "LOAI,LOAI,60,0,t,l;" +
                "TENLOAI,TENLOAI,60,0,t,l;" +
                "CHANDOAN,CHANDOAN,0,0,t,l;" +
                "THUOCKHOYC,THUOCKHOYC,60,0,t,l;" +
                "TenPhongLuu,TENPHONGLUU,0,0,t,l";
        }

        //initGroup.init("grdThuoc","100%","140px","",false,_gridHeader);
//		var _group={
//				groupField : ['TENPHONGLUU'],
//				groupColumnShow : [false],
//				groupText : ['<b>{0} ({1} khoản)</b>']
//		    };
        GridUtil.init("grdThuoc", "100%", "300px", "", false, _gridHeader, false, {
            rowNum: 100,
            rowList: [100, 200, 300]
        }, "VN");
        GridUtil.addExcelButton("grdThuoc", 'Xuất excel', true);
        if (PHARMA_LUOCBOT_PHIEUYC == 1) {
            $("#spThongtin").hide();
            $("#spThongTin2").hide();
            $("#grdThuoc").parents('div.ui-jqgrid-bdiv').css("max-height", "300px");
            document.getElementById("btnShowHide").innerHTML = '<img src="../common/image/down.png" width="12px">';
        } else {
            $("#grdThuoc").parents('div.ui-jqgrid-bdiv').css("max-height", "140px");
            document.getElementById("btnShowHide").innerHTML = '<img src="../common/image/up.png" width="12px">';
        }
        //GridUtil.initGroup("grdPhong","100%","300","",true,_group,_groupHeader, true, { rowNum: 100,rowList: [100, 200, 300]});
//		GridUtil.initGroup("grdThuoc","100%","140px","",false,_group,_gridHeader,false,"");
        //$("#grdThuoc")[0].toggleToolbar();

        $('#search_grdPhieu').hide();
        $('#search_grdThuoc').hide();

        $("#toolbarIdtbDuyet").prop("disabled", true);
        $("#toolbarIdtbGoiBN").prop("disabled", true);
        //$("#toolbarIdtbTaoPhieu").prop("disabled",true);
        //$("#toolbarIdtbIn").prop("disabled",true);
        $("#toolbarIdtbInNhap").prop("disabled", true);
        $("#toolbarIdtbInNhapYHCT").prop("disabled", true);
        $("#toolbarIdtbInNhapMAU").prop("disabled", true);
        $("#toolbarIdtbInNhapGiaDY").prop("disabled", true);
        $("#toolbarIdtbInNhapKT").prop("disabled", true);
        $("#toolbarIdtbInDutru").prop("disabled", true);
        $("#toolbarIdtbSoKiemNhapHoaDon").prop("disabled", true);
        $("#toolbarIdtbBBKiemNhapHoaDon").prop("disabled", true);
        $("#toolbarIdtbBBKiemKeHoaDon").prop("disabled", true);
        $("#toolbarIdtbBBKiemNhapHoaDonExcel").prop("disabled", true);
        $("#toolbarIdtbBBKiemNhapHoaDon_TDY").prop("disabled", true);
        $("#btnInPhieuBBKiemKe").prop("disabled", true);

        $("#toolbarIdtbBBXuatHuy").prop("disabled", true);
        $("#toolbarIdtbBBXuatHuyPL12").prop("disabled", true);
        $("#toolbarIdtbInXuat").prop("disabled", true);
        $("#toolbarIdtbInXuatTra_NCC").prop("disabled", true);
        $("#toolbarIdtbGiayTT").prop("disabled", true);
        $("#toolbarIdtbGiayTT_VT").prop("disabled", true);
//		$("#toolbarIdtbNhapKho").prop("disabled",true);
        $("#btnNhapKho").prop("disabled", true);
        $("#toolbarIdtbYCXuat").prop("disabled", true);
        $("#toolbarIdtbSua").prop("disabled", true);
//		$("#toolbarIdtbXuatTra").prop("disabled",true);
        $("#btnXuatTra").prop("disabled", true);
        $("#toolbarIdtbXuatTraNCC").prop("disabled", true);
//		$("#btnXuatTraNCC").prop("disabled",true);
        $("#toolbarIdtbSuaNCC").prop("disabled", true);
        $("#toolbarIdtbBoSung").prop("disabled", true);
        $("#toolbarIdtbNhapBu").prop("disabled", true);
        $("#toolbarIdtbNhapTra").prop("disabled", true);
        $("#toolbarIdtbSuaNhapBu").prop("disabled", true);
        $("#toolbarIdtbXuatHuy").prop("disabled", true);
        $("#toolbarIdtbSuaXuatHuy").prop("disabled", true);
        $("#toolbarIdtbXuatKhac").prop("disabled", true);
        $("#toolbarIdtbSuaXuatKhac").prop("disabled", true);
        $("#toolbarIdtbXuatThieu").prop("disabled", true);
        $("#toolbarIdtbSuaXuatThieu").prop("disabled", true);
        //$("#toolbarIdtbPhieuThuBanThuoc").prop("disabled",true);

        $("#toolbarIdtbXuatDTTH").prop("disabled", true);
        $("#toolbarIdtbHoanTraDTTH").prop("disabled", true);

        $("#toolbarIdtbSuaDTTH").prop("disabled", true);
        $("#toolbarIdtbSuaYLenhLT").prop("disabled", true);

        $("#toolbarIdtbInLinhHoaChatKhoaPhong").prop("disabled", true);//********
        $("#toolbarIdtbInPhieuYDungCuKhoaPhong").prop("disabled", true);//********


        $("#cboKho").focus();


        // SONDN
//		if(THUOC_HIENTHI_GOIKHAM == "1"){
//			$("#dvCall5").show();
//		}else{
//			$("#dvCall5").hide();
//		}
        // END SONDN
        _clickHuyGuiDuyet();//DucTT20181105
        if (PHARMA_HIENTHI_GOIBN == 0 || that.opt.ht != '13') $("#toolbarIdtbGoiBN").hide();
    }

    function _loadDSPhieu() {

        var sql = "DUC01S002.LAYDL";
        if (PHARMA_LAYDL_BNHNI == '1' && that.opt.ht == '13') {
            var sql = "DUC01S002_LAYDL_10";
        }
        if (PHARMA_SHOWKHOAPHONG == '1' && that.opt.ht == '13') {
            _loadDSPhieuKP();
        }
        if (PHARMA_THEMCHUADUYETKT == '1' && that.opt.ht == '13') {
            var sql = "DUC01S002.LAYDL.02";
        }
        if (PHARMA_THEMCHUADUYETKT == '2' && that.opt.ht == '13') {
            var sql = "DUC01S002.LAYDL.03";
        }
        if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
            _loadDSPhieuBUCSTT();
        } else if (PHARMA_FORMAT_NEWTIME_SEARCH == '1' && that.opt.ht == '12' && that.opt.type == '45' && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU")) {
            _loadDSPhieuNewFormat();
        } else if (that.opt.td == '14' && that.opt.type == '45' && that.opt.gd == 'THUOC' && (that.opt.ht.includes("9") || that.opt.ht.includes("12") || that.opt.ht.includes("13"))) {
            _loadDSPhieuPhatThuoc();
            dsHoaDon = [];
        } else if (that.opt.ht == "4" && PHARMA_DSPHIEU_THEOKHOA == '1' && that.opt.gd == 'XUATDTTHKP') {
            _loadDSPhieuTHKP();
        } else if (PHARMA_XUATKHAC_LYDOXUAT == '1' && that.opt.ht == '8' && that.opt.gd == "XUATKHACTHUOC" && that.opt.LDXUAT != '' && jQuery.type(that.opt.LDXUAT) !== "undefined") {
            _loadDSPhieuLyDoXuat();
        } else {
            if (PHARMA_THEMCHUADUYETKT == '2' && that.opt.ht == '13') {
                var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), $("#cboHinhThuc").val(), $("#cboTrangThaiDuyet").val(), $("#cboLoaiPhieu").val(), $('#txtNgayBD').val().trim(), $('#txtNgayKT').val().trim(), that.opt.type, $("#cboDuyetVP").val()]);
            } else
            	var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), $("#cboHinhThuc").val(), $("#cboTrangThaiDuyet").val(), $("#cboLoaiPhieu").val(), $('#txtNgayBD').val().trim(), $('#txtNgayKT').val().trim(), that.opt.type]);
        	GridUtil.loadGridBySqlPage("grdPhieu", sql, sql_par, function () {
                if ($("#grdPhieu").getGridParam("reccount") > 0) {
                    if (PHARMA_DUYETNHIEUPHIEU != '1')
                        $("#grdPhieu").setSelection(1, true);
                } else {
                    FormUtil.clearForm("divCT", "");
                    $('#grdThuoc').clearGridData();
                    if (that.opt.ht == '22') {
                        $("#btnBoKhoa").prop("disabled", true);
                    }
                }
        	 });
            
            
           
        }
        //$("#grdPhieu").trigger("reloadGrid",[{page:1}]);
    }

    function _loadDSPhieuKP() {
        var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), $("#cboHinhThuc").val(), $("#cboTrangThaiDuyet").val(), $("#cboLoaiPhieu").val(), $('#txtNgayBD').val().trim(), $('#txtNgayKT').val().trim(), that.opt.type, $("#cboKhoa").val(), $("#cboPhong").val()]);
        GridUtil.loadGridBySqlPage("grdPhieu", "DUC01S002.LAYDLKP", sql_par, function () {
            if ($("#grdPhieu").getGridParam("reccount") > 0) {
                $("#grdPhieu").setSelection(1, true);
            } else {
                FormUtil.clearForm("divCT", "");
                $('#grdThuoc').clearGridData();
            }
        });
    }

    // daidv lay danh sach phieu theo dinh dang thoi gian moi -- SNPYN
    function _loadDSPhieuNewFormat() {
        var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), $("#cboHinhThuc").val(), $("#cboTrangThaiDuyet").val(), $("#cboLoaiPhieu").val(), $('#txtNgayBD').val().trim(), $('#txtNgayKT').val().trim(), that.opt.type]);
        GridUtil.loadGridBySqlPage("grdPhieu", "DUC01S002.LAYDL88", sql_par, function () {
            if ($("#grdPhieu").getGridParam("reccount") > 0) {
                $("#grdPhieu").setSelection(1, true);
            } else {
                FormUtil.clearForm("divCT", "");
                $('#grdThuoc').clearGridData();
            }
        });
    }

    // daidv lay danh sach phieu theo ly do xuat kho
    function _loadDSPhieuLyDoXuat() {
        var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), $("#cboHinhThuc").val(), $("#cboTrangThaiDuyet").val(), $("#cboLoaiPhieu").val(), $('#txtNgayBDNew').val().trim(), $('#txtNgayKTNew').val().trim(), that.opt.type, $("#cboLYDOXUAT").val()]);
        GridUtil.loadGridBySqlPage("grdPhieu", "DUC01S002.LAYDL.11", sql_par, function () {
            if ($("#grdPhieu").getGridParam("reccount") > 0) {
                $("#grdPhieu").setSelection(1, true);
            } else {
                FormUtil.clearForm("divCT", "");
                $('#grdThuoc').clearGridData();
            }
        });
    }

    // daidv them duyet phieu bu co so tu truc
    function _loadDSPhieuBUCSTT() {
        var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), $("#cboHinhThuc").val(), $("#cboTrangThaiDuyet").val(), $("#cboLoaiPhieu").val(), $('#txtNgayBD').val().trim(), $('#txtNgayKT').val().trim(), that.opt.type, that.opt.td]);

        GridUtil.loadGridBySqlPage("grdPhieu", "DUC01S002.CSTT.00", sql_par, function () {
            if ($("#grdPhieu").getGridParam("reccount") > 0) {
                $("#grdPhieu").setSelection(1, true);
            } else {
                FormUtil.clearForm("divCT", "");
                $('#grdThuoc').clearGridData();
            }
        });


        //$("#grdPhieu").trigger("reloadGrid",[{page:1}]);
    }

    // chuannt them ds tieu hao khoa phong loc theo khoa
    function _loadDSPhieuTHKP() {
        var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), _opt.dept, $("#cboHinhThuc").val(), $("#cboTrangThaiDuyet").val(), $("#cboLoaiPhieu").val(), $('#txtNgayBD').val().trim(), $('#txtNgayKT').val().trim(), that.opt.type]);

        GridUtil.loadGridBySqlPage("grdPhieu", "DUC01S002.THKP", sql_par, function () {
            if ($("#grdPhieu").getGridParam("reccount") > 0) {
                $("#grdPhieu").setSelection(1, true);
            } else {
                FormUtil.clearForm("divCT", "");
                $('#grdThuoc').clearGridData();
            }
        });


        //$("#grdPhieu").trigger("reloadGrid",[{page:1}]);
    }

    // daidv them duyet lam san cho phieu y/c bo sung tu truc
    function _loadDSPhieuLamSan() {
        var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), $("#cboHinhThuc").val(), $("#cboTrangThaiDuyet").val(), $("#cboLoaiPhieu").val(), $('#txtNgayBD').val().trim(), $('#txtNgayKT').val().trim(), that.opt.type, $("#cboTrangThaiDuyetLS").val()]);
        //$("#grdPhieu").setGridParam({page:1});
        //$("#grdPhieu").jqGrid("clearGridData", true);
        GridUtil.loadGridBySqlPage("grdPhieu", "DUC01S002.LAYDL.DLS", sql_par, function () {
            if ($("#grdPhieu").getGridParam("reccount") > 0) {
                $("#grdPhieu").setSelection(1, true);
            } else {
                FormUtil.clearForm("divCT", "");
                $('#grdThuoc').clearGridData();
            }
        });


        //$("#grdPhieu").trigger("reloadGrid",[{page:1}]);
    }

    // daidv them duyet lam san cho phieu y/c bo sung tu truc
    function _loadDSPhieuPhatThuoc() {
        var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), $("#cboHinhThuc").val(), that.opt.tt, $("#cboLoaiPhieu").val(), $('#txtNgayBD').val().trim(), $('#txtNgayKT').val().trim(), that.opt.type, $("#cboTrangThaiDuyetPT").val()]);
        //$("#grdPhieu").setGridParam({page:1});
        //$("#grdPhieu").jqGrid("clearGridData", true);
        GridUtil.loadGridBySqlPage("grdPhieu", "DUC01S002.LAYDL.DPT", sql_par, function () {
            if ($("#grdPhieu").getGridParam("reccount") > 0) {
                dsHoaDon = [];
//				$("#grdPhieu").setSelection(1,true);
            } else {
                dsHoaDon = [];
                FormUtil.clearForm("divCT", "");
                $('#grdThuoc').clearGridData();
            }
        });


        //$("#grdPhieu").trigger("reloadGrid",[{page:1}]);
    }

    function _loadDSPhieuLS_DaDuyet() {
        var sql_par = RSUtil.buildParam("", [$("#cboKho").val(), $("#cboHinhThuc").val(), $("#cboTrangThaiDuyet").val(), $("#cboLoaiPhieu").val(), $('#txtNgayBD').val().trim(), $('#txtNgayKT').val().trim(), that.opt.type]);
        //$("#grdPhieu").setGridParam({page:1});
        //$("#grdPhieu").jqGrid("clearGridData", true);
        GridUtil.loadGridBySqlPage("grdPhieu", "DUC01S002.DUYET.LS", sql_par, function () {
            if ($("#grdPhieu").getGridParam("reccount") > 0) {
                $("#grdPhieu").setSelection(1, true);
            } else {
                FormUtil.clearForm("divCT", "");
                $('#grdThuoc').clearGridData();
            }
        });


        //$("#grdPhieu").trigger("reloadGrid",[{page:1}]);
    }

    function checkLanhDaoDuyet() {
        var tooltip = '';
        var rowIds = $('#grdThuoc').jqGrid('getDataIDs');
        for (i = 0; i < rowIds.length; i++) {
            rowData = $('#grdThuoc').jqGrid('getRowData', rowIds[i]);
            var _color = '#FF9900';
            if (rowData['CHOLANHDAODUYET'] == '1') {
            	_color = '#00F3FF';
                $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').each(function (index, element) {
                    $(element).css('background-color', _color);
                    $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', rowData['CHUY']);
                });

            }
            if (canhBao.indexOf(rowData["LOAI"]) != -1) {
            	_color = '#FF9900';

                $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').each(function (index, element) {
                    $(element).css('background-color', _color);
                    $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', rowData['TENLOAI']);
                });
                canhBaoloaithuoc = rowData['TENLOAI'];
                tooltip = rowData['TENLOAI'] + '+';
            }
            var cauhinhbs = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_DTBS_TVT_NGOAI_DM'].join('$'));
            if ((that.hinhThuc == "9" || that.hinhThuc == "2") && cauhinhbs == '1')
                CHBoSung = '1';

            if (rowData["THUOCKHOYC"] == '') {
                tooltip = tooltip + 'Thuốc không có trong kho lập';
                $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').each(function (index, element) {
                    $(element).css('background-color', _color);
                    $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', tooltip);
                });
            }
        }
    }

    function _loadChiTiet(_id, _trangthaiid, _loaiphieu) {


        var sql_par = RSUtil.buildParam("", [_id]);
        if ((that.opt.ht == 12 || that.opt.ht == 13) && that.opt.gd == 'MAU')
            GridUtil.loadGridBySqlPage("grdThuoc", "DUC01S002.CTMAU", sql_par);
        else
            GridUtil.loadGridBySqlPage("grdThuoc", "DUC01S002.CTTHUOC", sql_par);
        if (that.opt.ht == 13 && _trangthaiid == KHONGDUYET) {
            $("#btnGoTuChoi").show();
            $("#btnGoTuChoi").prop("disabled", false);
            checkRole('btnGoTuChoi');
        } else {
            $("#btnGoTuChoi").hide();
            $("#btnGoTuChoi").prop("disabled", true);
        }
        if (_trangthaiid == CHODUYET) {

            $("#toolbarIdtbGoiBN").prop("disabled", false);
            $("#toolbarIdtbDuyet").prop("disabled", false);
            $("#toolbarIdtbDieuChinhDon").prop("disabled", false);
            $("#toolbarIdtbBoSungNoThuoc").prop("disabled", false);

            //phieu thu
            //$("#toolbarIdtbPhieuThuBanThuoc").prop("disabled",false);
            //ban thuoc khach le - tra thuoc
            if (that.opt.ht.includes("15"))
                $("#toolbarIdtbTraThuoc").prop("disabled", true);
            if (that.opt.ht == '22' && PHARMA_KHOA_TVT_NEW == '1') {
                $('#btnBoKhoa').removeAttr("disabled");
            }
        } else {

            $("#toolbarIdtbGoiBN").prop("disabled", true);
            $("#toolbarIdtbDuyet").prop("disabled", true);
            $("#toolbarIdtbDieuChinhDon").prop("disabled", true);
            $("#toolbarIdtbBoSungNoThuoc").prop("disabled", true);
            //phieu thu
            //$("#toolbarIdtbPhieuThuBanThuoc").prop("disabled",true);
            // ban thuoc khach le - tra thuoc
            if (that.opt.ht.includes("15"))
                if (_loaiphieu == 3 && _trangthaiid == 6) //DucTT20181113
                    $("#toolbarIdtbTraThuoc").prop("disabled", false);
                else
                    $("#toolbarIdtbTraThuoc").prop("disabled", true);


        }
        if (_trangthaiid == DADUYET) {
            //$("#toolbarIdtbTaoPhieu").prop("disabled",false);
            if (that.opt.type.includes("4") || that.opt.type.includes("5") || that.opt.type.includes("G")) {
                $("#btnGoDuyet").show();
//					$("#btnGoDuyet").prop("disabled",false);
                checkRole('btnGoDuyet');
            }

            if (_loaiphieu == 2) {
//				$("#toolbarIdtbNhapKho").prop("disabled",true);
                $("#btnNhapKho").prop("disabled", true);
                $("#toolbarIdtbYCXuat").prop("disabled", true);
                $("#toolbarIdtbNhapTra").prop("disabled", false);
                $("#toolbarIdtbXuatDTTH").prop("disabled", true);
//				$("#toolbarIdtbXuatTra").prop("disabled",false);
                $("#btnXuatTra").prop("disabled", false);
                checkRole('toolbarIdtbYCXuat');
            } else if (_loaiphieu == 3) {
//				$("#toolbarIdtbNhapKho").prop("disabled",false);
                $("#btnNhapKho").prop("disabled", false);
                $("#toolbarIdtbNhapTra").prop("disabled", true);
                $("#toolbarIdtbXuatDTTH").prop("disabled", false);
//				$("#toolbarIdtbXuatTra").prop("disabled",true);
                $("#btnXuatTra").prop("disabled", true);
                $("#toolbarIdtbYCXuat").prop("disabled", false);
                checkRole('toolbarIdtbYCXuat');
                checkRole('btnNhapKho');

            }

            $("#toolbarIdtbNhapBu").prop("disabled", false);

            $("#toolbarIdtbXuatThieu").prop("disabled", false);
            $("#toolbarIdtbXuatHuy").prop("disabled", false);
            $("#toolbarIdtbXuatKhac").prop("disabled", false);

            $("#toolbarIdtbHoanTraDTTH").prop("disabled", false);
            if (that.opt.ht == '22') {
                $('#btnBoKhoa').removeAttr("disabled");
            }
        } else {
            //$("#toolbarIdtbTaoPhieu").prop("disabled",true);
//			$("#btnGoDuyet").show();


//			$("#toolbarIdtbNhapKho").prop("disabled",true);
            $("#btnNhapKho").prop("disabled", true);
//			$("#toolbarIdtbXuatTra").prop("disabled",true);
            $("#btnXuatTra").prop("disabled", true);
            $("#toolbarIdtbYCXuat").prop("disabled", true);
            $("#toolbarIdtbNhapBu").prop("disabled", true);
            $("#toolbarIdtbNhapTra").prop("disabled", true);
            $("#toolbarIdtbXuatThieu").prop("disabled", true);
            $("#toolbarIdtbXuatHuy").prop("disabled", true);
            $("#toolbarIdtbXuatKhac").prop("disabled", true);
            $("#toolbarIdtbXuatDTTH").prop("disabled", true);

        }
        //	var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECKCK", parseInt(_id));
        if (_trangthaiid == KETTHUC || _trangthaiid == DADUYET) {
            //$("#toolbarIdtbYCXuat").prop("disabled",false);
            if (that.opt.ht != '22') {
                $("#btnGoNhapkho").show();
            } else {
                $("#btnGoNhapkho").hide();
            }

            if (that.opt.type == 'Z') {
                $("#btnGoNhapkho").hide();
            }

            if (_loaiphieu == 0 || _loaiphieu == 1) {
                $("#toolbarIdtbXuatTraNCC").prop("disabled", false);
//				$("#btnXuatTraNCC").prop("disabled",false);

//				if(that.opt.user_gr_id.toString() == '1' || that.opt.user_gr_id.toString() == '9'){
//					$("#btnGoNhapkho").prop("disabled",false);
//				}
                checkRole('btnGoNhapkho');
                //$("#btnGoNhapkho").prop("disabled",false);


                // check thuoc da chuyen kho hay chua


                /*if(result == '1') {
					$("#toolbarIdtbSuaNCC").prop("disabled",false);
				}*/
                //
                $("#toolbarIdtbSuaNCC").prop("disabled", false);
                if (_loaiphieu == 0) {
                    $("#toolbarIdtbBoSung").prop("disabled", false);
                } else {
                    $("#toolbarIdtbBoSung").prop("disabled", true);
                }


            } else {
                $("#toolbarIdtbXuatTraNCC").prop("disabled", true);
                //				$("#btnXuatTraNCC").prop("disabled",true);
                if (_loaiphieu == 2 && that.opt.ht == "8" && (that.opt.gd == "NHAPKHACTHUOC" || that.opt.gd == "NHAPKHACVATTU") && that.opt.hospitalId == '10284') {
                    $("#btnGoNhapkho").prop("disabled", false);
                } else {
                    $("#btnGoNhapkho").prop("disabled", true);
                }

            }
        } else {
            //$("#toolbarIdtbYCXuat").prop("disabled",true);
            $("#toolbarIdtbXuatTraNCC").prop("disabled", true);
            $("#toolbarIdtbHoanTraDTTH").prop("disabled", true);
//			$("#btnXuatTraNCC").prop("disabled",true);
            $("#btnGoNhapkho").hide();
        }
        if (_trangthaiid == TAOMOI) {
            $("#toolbarIdtbSua").prop("disabled", false);
            $("#toolbarIdtbSuaNCC").prop("disabled", false);
            $("#toolbarIdtbBoSung").prop("disabled", true);
            $("#toolbarIdtbSuaNhapBu").prop("disabled", false);
            $("#toolbarIdtbSuaXuatHuy").prop("disabled", false);
            $("#toolbarIdtbSuaXuatKhac").prop("disabled", false);
            $("#toolbarIdtbSuaXuatThieu").prop("disabled", false);
            $("#toolbarIdtbSuaDTTH").prop("disabled", false);
            $("#toolbarIdtbSuaYLenhLT").prop("disabled", false);

//			$("#toolbarIdtbSuaHoanTra").prop("disabled",false);
            // ban thuoc khach le
            $("#toolbarIdtbSuaPhieuBanTHuoc").prop("disabled", false);

        } else {
            $("#toolbarIdtbSua").prop("disabled", true);
            $("#toolbarIdtbSuaNCC").prop("disabled", false);
            //$("#toolbarIdtbBoSung").prop("disabled",false);
            //if(_loaiphieu != 0) {
            //	$("#toolbarIdtbSuaNCC").prop("disabled",true);
            //}
            $("#toolbarIdtbSuaNhapBu").prop("disabled", true);
            $("#toolbarIdtbSuaXuatHuy").prop("disabled", true);
            $("#toolbarIdtbSuaXuatKhac").prop("disabled", true);
            $("#toolbarIdtbSuaXuatThieu").prop("disabled", true);
            $("#toolbarIdtbSuaDTTH").prop("disabled", true);
            $("#toolbarIdtbSuaYLenhLT").prop("disabled", true);

//			$("#toolbarIdtbSuaHoanTra").prop("disabled",true);
            // ban thuoc khach le
            $("#toolbarIdtbSuaPhieuBanTHuoc").prop("disabled", true);
        }


        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC01S002.CTPHIEU", _id);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            FormUtil.setObjectToForm("divCT", "", row);
            if (row.CHIETKHAU == null)
                row.CHIETKHAU = '';
            if (row.TIENDON == null)
                row.TIENDON = '';
            if (row.TONGCONG == null)
                row.TONGCONG = '';
            if (row.TONGTIENDATRA == null)
                row.TONGTIENDATRA = '';
            $("#txtCHIETKHAU").val(Number(row.CHIETKHAU).format(_sole, 3, ','));
            if (row.TONGCONG != '' && row.TIENDON != '')
                //	$("#txtTIENCHIETKHAU").val(formatNumber(Math.round((row.TIENDON-row.TONGCONG)*100)/100));
                $("#txtTIENCHIETKHAU").val(Number((row.TONGCONG) * (row.CHIETKHAU) / 100).format(_sole, 3, ','));
            //var tck = Math.round((row.TONGCONG)*(row.CHIETKHAU)/100);
            var tienchietkhau = parseFloat((row.TONGCONG) * (row.CHIETKHAU) / 100);
            $("#txtTIENDON").val(Number(row.TIENDON).format(_sole, 3, ','));
            //$("#txtTONGCONG").val(formatNumber(row.TONGCONG));
            $("#txtTONGCONG").val(Number(Number(row.TONGCONG) - tienchietkhau).format(_sole, 3, ','));
            $("#txtTONGTIENDATRA").val(Number(row.TONGTIENDATRA).format(_sole, 3, ','));
            if (PHARMA_SHOW_CHANDOAN_BN == '1' && that.opt.ht == '13') {
                $("#txtCDCHINH").val(row.CHANDOAN);
                $("#txtKemTheo").val(row.CHANDOAN_KEMTHEO);
            }
        }
    }

    /*function replaceStrtoNum(strSoluong){
		return Number(strSoluong.replace(/[^0-9\.]+/g,""));
	};*/
    function _formatRow(_rId, _fIndex) {
        //GridUtil.markRow('grdPhieu',_rId,that.opt.backColor[_fIndex]);
        var _icon = '';
        if (that.opt.imgPath[_fIndex] != '')
            _icon = '<center><img src="../common/image/' + that.opt.imgPath[_fIndex] + '" width="15px"></center>';
        $("#grdPhieu").setRowData(_rId, {icon: _icon});
        $("#grdPhieu").find("tr[id='" + _rId + "']").find('td').each(function (index, element) {
            if ($("#grdPhieu").jqGrid('getRowData', _rId).TTPHIEUNX == 4) {
                $(element).css({'color': that.opt.foreColor[_fIndex]});
                if ($("#grdPhieu").jqGrid('getRowData', _rId).DANGXULY == 1 && that.opt.td == '9') {
                    $(element).css({'color': that.opt.foreColor[_fIndex + 1]});
                }
                if (that.opt.td == '14' && $("#grdPhieu").jqGrid('getRowData', _rId).TTDUYETPHATTHUOC != '4') {
                    $(element).css({'color': '#FF0000'});
                    $("#grdPhieu").setRowData(_rId, {icon: '<center><img src="../common/image/' + that.opt.imgPath[4] + '" width="15px"></center>'});
                }
            }
            //$(element).css({'background-color':that.opt.backColor[_fIndex]});
            else {
                $(element).css({'color': '#FF0000'})
                if ($("#grdPhieu").jqGrid('getRowData', _rId).HINHTHUCID != '22') {
                    if (_fIndex == 5) {
                        $(element).css({'background-color': '#FF9900'})
                        $('#grdPhieu').find("tr[id='" + _rId + "']").find('td').attr('title', 'CHƯA TẠO PHIẾU!');

                    }
                }


            }
            ;
            //$(element).css({'color':'#FF0000'});
        });
    }

    function _print(_id, _loaiphieu) {
        var sql_par = _id + "$";
        if (_loaiphieu == 0 || _loaiphieu == 3) {//phieu nhap
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01S001.03', sql_par);
            var row = data_ar[0];
            var thanhtien = row.THANH_TIEN;
            var _no = parseInt(row.NO).format(0, 3, ',');
            var _co = parseInt(row.CO).format(0, 3, ',');
            var tien_chu = CommonUtil.toWord(row.THANH_TIEN);
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            },
                {
                    name: 'so_y_te',
                    type: 'String',
                    value: row.SO_Y_TE
                },
                {
                    name: 'benh_vien',
                    type: 'String',
                    value: row.BENH_VIEN
                },
                {
                    name: 'khoa',
                    type: 'String',
                    value: row.KHOA
                },
                {
                    name: 'ngay_nhap',
                    type: 'String',
                    value: row.NGAY_NHAP
                },
                {
                    name: 'nha_cung_cap',
                    type: 'String',
                    value: row.NHA_CUNG_CAP
                },
                {
                    name: 'kho_nhap',
                    type: 'String',
                    value: row.KHO_NHAP
                },
                {
                    name: 'nguoi_giao',
                    type: 'String',
                    value: row.NGUOI_GIAO
                },
                {
                    name: 'ma_phieu',
                    type: 'String',
                    value: row.MA_PHIEU
                },
                {
                    name: 'no',
                    type: 'String',
                    value: _no
                },
                {
                    name: 'so',
                    type: 'String',
                    value: row.SO
                },
                {
                    name: 'co',
                    type: 'String',
                    value: _co
                },
                {
                    name: 'thanh_tien',
                    type: 'String',
                    value: thanhtien.toString()
                },
                {
                    name: 'tien_bang_chu',
                    type: 'String',
                    value: row.TIEN_BANG_CHU
                },
                {
                    name: 'so_ct',
                    type: 'String',
                    value: row.SO_CT
                },
                {
                    name: 'i_sch',
                    type: 'String',
                    value: that.opt.i_sch.toString()
                },
                {
                    name: 'gio',
                    type: 'String',
                    value: row.GIO
                },
                {
                    name: 'ngay',
                    type: 'String',
                    value: row.NGAY
                },
                {
                    name: 'thang',
                    type: 'String',
                    value: row.THANG
                },
                {
                    name: 'nam',
                    type: 'String',
                    value: row.NAM
                },
                {
                    name: 'ngay_ct',
                    type: 'String',
                    value: row.NGAY_CT
                },
                {
                    name: 'thang_ct',
                    type: 'String',
                    value: row.THANG_CT
                },
                {
                    name: 'nam_ct',
                    type: 'String',
                    value: row.NAM_CT
                }
            ];

            if (PHARMA_BC_NHAP_KHO == 1) {
                openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_A4_SN_HNM', 'pdf', par);
            } else {
                openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_A4', 'pdf', par);
            }

        } else if (_loaiphieu == 1 || _loaiphieu == 2) {//phieu xuat
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01S001.04', sql_par);
            var row = data_ar[0];
            var tien_chu = CommonUtil.toWord(row.TONG_TIEN);
            var _no = parseInt(row.NO).format(0, 3, ',');
            var _co = parseInt(row.CO).format(0, 3, ',');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            },
                {
                    name: 'khoa',
                    type: 'String',
                    value: row.KHOA.toString()
                },
                {
                    name: 'soPhieu',
                    type: 'String',
                    value: row.SOPHIEU.toString()
                },
                {
                    name: 'co',
                    type: 'String',
                    value: _co
                },
                {
                    name: 'no',
                    type: 'String',
                    value: _no
                },
                {
                    name: 'nguoi_giao',
                    type: 'String',
                    value: row.NGUOI_GIAO.toString()
                },
                {
                    name: 'donViNhan',
                    type: 'String',
                    value: row.DONVINHAN.toString()
                },
                {
                    name: 'lyDoXuat',
                    type: 'String',
                    value: row.LYDOXUAT.toString()
                },
                {
                    name: 'khoXuat',
                    type: 'String',
                    value: row.KHOXUAT
                }
                ,
                {
                    name: 'tienBangChu',
                    type: 'String',
                    value: row.TIEN_BANG_CHU.toString()
                },
                {
                    name: 'soChungTu',
                    type: 'String',
                    value: row.SO_CHUNG_TU.toString()
                }
            ];


            if (PHARMA_BC_XUAT_KHO == 1) {
                openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM', 'pdf', par);
            } else {
                openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4', 'pdf', par);
            }

        } else if (_loaiphieu == 13) {//phieu xuat	huy

            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01S001.04', sql_par);
            var row = data_ar[0];
            var tien_chu = CommonUtil.toWord(row.TONG_TIEN);
            var _no = parseInt(row.NO).format(0, 3, ',');
            var _co = parseInt(row.CO).format(0, 3, ',');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            },
                {
                    name: 'khoa',
                    type: 'String',
                    value: row.KHOA.toString()
                },
                {
                    name: 'soPhieu',
                    type: 'String',
                    value: row.SOPHIEU.toString()
                },
                {
                    name: 'co',
                    type: 'String',
                    value: _co
                },
                {
                    name: 'no',
                    type: 'String',
                    value: _no
                },
                {
                    name: 'nguoi_giao',
                    type: 'String',
                    value: row.NGUOI_GIAO.toString()
                },
                {
                    name: 'donViNhan',
                    type: 'String',
                    value: row.DONVINHAN.toString()
                },
                {
                    name: 'lyDoXuat',
                    type: 'String',
                    value: row.LYDOXUAT.toString()
                },
                {
                    name: 'khoXuat',
                    type: 'String',
                    value: row.KHOXUAT
                }
                ,
                {
                    name: 'tienBangChu',
                    type: 'String',
                    value: row.TIEN_BANG_CHU.toString()
                },
                {
                    name: 'soChungTu',
                    type: 'String',
                    value: row.SO_CHUNG_TU.toString()
                }
            ];
            openReport('window', 'DUC_PHIEUXUATKHO_KIEMVANCHUYEN_NB', 'pdf', par);
        }
    }

    function _bindEvent() {
        GridUtil.setGridParam("grdThuoc", {
            gridComplete: function () {
                var rowCount = $("#grdThuoc").getGridParam("reccount");
                if (rowCount > 0) {
                    checkLanhDaoDuyet();
                    var ids = $("#grdThuoc").getDataIDs();
                    for (var i = 0; i < ids.length; i++) {
                        var id = ids[i];
                        var row = $("#grdThuoc").jqGrid(
                            'getRowData', id);
//						$('#grdThuoc').jqGrid('setCell', id, 7, row.SOLUONG);
                        rowData = $('#grdThuoc').jqGrid(
                            'getRowData', ids[i]);
                        if (parseFloat(rowData['SOLUONG']) != parseFloat(rowData['SOLUONGDUYET'])) {
//								$("#grdThuoc").jqGrid('setCell', ids[i], 'SOLUONGDUYET', rowData['SLKHADUNG']);
                            $("#grdThuoc").jqGrid('setRowData', ids[i], "", {color: 'red'});
                        }
                        
                        if(PHARMA_SHOW_COLOR_HSD == '1' && (that.opt.ht == "2" || that.opt.ht.includes("8"))){
                        	_formatRowColor(1);
                        }else if(PHARMA_SHOW_COLOR_HSD == '1' && (that.opt.ht == "12" || that.opt.ht == "13" || that.opt.ht == "9" )){
                        	_formatRowColor(2);
                        }

                        var html1 = '<button type="button" class="btn btn-link" style="white-space: normal;" id="btnCHITIET_' + rowData['THUOCVATTUID'] + '">Xem </span></button>';
                        var html2 = '<button type="button" class="btn btn-link" style="white-space: normal;" id="btnTONKHO_' + rowData['THUOCVATTUID'] + '">Xem </span></button>';

                        $("#grdThuoc").jqGrid('setCell', ids[i], 'CHITIETSD', html1);
                        $("#grdThuoc").jqGrid('setCell', ids[i], 'CTTONKHO', html2);

                        $('#btnTONKHO_' + rowData['THUOCVATTUID']).on("click", function (e) {
                            if ($(e.target.parentElement).is(':button') || $(e.target).is(':button')) {
                                var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
                                var dataTmp = $("#grdThuoc").jqGrid('getRowData', rowIdTmp);
                                var myVar = {
                                    thuocvattuid: dataTmp.THUOCVATTUID,
                                    tenthuoc: dataTmp.TEN
                                };
                                dlgPopup = DlgUtil.buildPopupUrl("dlgTCThuoc", "dlgTCThuoc", "manager.jsp?func=../duoc/DUC77T001_TraCuuThuoc", myVar, "Tra cứu thông tin thuốc/vật tư", 1200, 600);
                                DlgUtil.open("dlgTCThuoc");
                            }
                        });

                        $('#btnCHITIET_' + rowData['THUOCVATTUID']).on("click", function (e) {
                            if ($(e.target.parentElement).is(':button') || $(e.target).is(':button')) {
                                var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
                                var dataTmp = $("#grdThuoc").jqGrid('getRowData', rowIdTmp);
                                var myVar = {
                                    khoid: $("#cboKho").val(),
                                    tonkhoctid: dataTmp.TONKHOCTID,
                                    thuocvattuid: dataTmp.THUOCVATTUID,
                                    nhapxuatctid: dataTmp.NHAPXUATCTID,
                                    mathuoc: dataTmp.MA,
                                    tenthuoc: dataTmp.TEN,
                                    hamluong: dataTmp.HAMLUONG,
                                    donvitinh: dataTmp.TEN_DVT,
                                    solo: dataTmp.SOLO,
                                    hansudung: dataTmp.HANSUDUNG,
                                    dongia: dataTmp.GIANHAP,
                                    thanhtien: dataTmp.THANHTIEN,
                                    soluong: dataTmp.SOLUONG,
                                    soluongcap: dataTmp.SOLUONGDUYET,
                                    sltonkho: dataTmp.SLTONKHO,
                                    slkhadung: dataTmp.SLKHADUNG
                                };
                                dlgPopup = DlgUtil.buildPopupUrl("dlgTTThuoc", "dlgTTThuoc", "manager.jsp?func=../duoc/DUC_CHITIET_SDTHUOC", myVar, "Thông tin thuốc", 1200, 650);
                                DlgUtil.open("dlgTTThuoc");
                            }
                        });
                    }


                }
            }
        });
        $('#cboKhoa').on('change', function () {
            var _val = $('#cboKhoa').val();
            ComboUtil.getComboTag("cboPhong", "DMC.NV.06", [{
                "name": "[0]",
                "value": _val
            }], "", {text: "--Chọn phòng--", value: -1}, "sql");

        });
        $("#cboKho").on("change", function (e) {
            //_loadDSPhieu();
            if (that.opt.lp == "3" && that.opt.ht == "9" && that.opt.gd == "THUOC" && PHARMA_DUYET_DLS_BOSUNG_TUTRUC == "1" && that.opt.td == "9") {
                //if(that.opt.td == "9"){
                _loadDSPhieuLamSan();
//				}else{
//					_loadDSPhieuLS_DaDuyet();
//				}
            } else if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                _loadDSPhieuBUCSTT();
            } else {
                _loadDSPhieu();
            }
        });
        $("#cboHinhThuc").on("change", function (e) {
            //_loadDSPhieu();
            if (that.opt.lp == "3" && that.opt.ht == "9" && that.opt.gd == "THUOC" && PHARMA_DUYET_DLS_BOSUNG_TUTRUC == "1" && that.opt.td == "9") {
                //if(that.opt.td == "9"){
                _loadDSPhieuLamSan();
//				}else{
//					_loadDSPhieuLS_DaDuyet();
//				}
            } else if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                _loadDSPhieuBUCSTT();
            } else {
                _loadDSPhieu();
            }
        });
        $("#cboTrangThaiDuyet").on("change", function (e) {
            //_loadDSPhieu();
            if (that.opt.lp == "3" && that.opt.ht == "9" && that.opt.gd == "THUOC" && PHARMA_DUYET_DLS_BOSUNG_TUTRUC == "1" && that.opt.td == "9") {
                //if(that.opt.td == "9"){
                _loadDSPhieuLamSan();
//				}else{
//					_loadDSPhieuLS_DaDuyet();
//				}
            } else if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                _loadDSPhieuBUCSTT();
            } else {
                _loadDSPhieu();
            }
        });
        $("#cboLoaiPhieu").on("change", function (e) {
            //_loadDSPhieu();
            if (that.opt.lp == "3" && that.opt.ht == "9" && that.opt.gd == "THUOC" && PHARMA_DUYET_DLS_BOSUNG_TUTRUC == "1" && that.opt.td == "9") {
                //if(that.opt.td == "9"){
                _loadDSPhieuLamSan();
//				}else{
//					_loadDSPhieuLS_DaDuyet();
//				}
            } else if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                _loadDSPhieuBUCSTT();
            } else {
                _loadDSPhieu();
            }
        });

        $("#cboTrangThaiDuyetPT").on("change", function (e) {
            _loadDSPhieu();
        });

        $("#btnTimKiem").on("click", function (e) {
            var _t = $("#txtNgayBD").val().trim();
            var _d = $("#txtNgayKT").val().trim();
            if (_t != '' && !moment(_t, 'DD/MM/YYYY', true).isValid()) {
                DlgUtil.showMsg("Giá trị từ ngày nhập không hợp lệ");
                $("#txtNgayBD").focus();
                return;
            }
            if (_d != '' && !moment(_d, 'DD/MM/YYYY', true).isValid()) {
                DlgUtil.showMsg("Giá trị đến ngày nhập không hợp lệ");
                $("#txtNgayKT").focus();
                return;
            }

            if (_t != '' && _d != '') {
                var tungay = moment(_t, 'DD/MM/YYYY');
                var denngay = moment(_d, 'DD/MM/YYYY');
                if (tungay > denngay) {
                    DlgUtil.showMsg("Giá trị từ ngày không được vượt quá giá trị đến ngày");
                    $("#txtNgayBD").focus();
                    return;
                }
            }
            if (PHARMA_SONGAY_TIMKIEM_CHUCNANG != '' && jQuery.type(PHARMA_SONGAY_TIMKIEM_CHUCNANG) !== "undefined"){
            	if( replaceStrtoNum(PHARMA_SONGAY_TIMKIEM_CHUCNANG) > 0 && denngay.diff(tungay , 'days') > replaceStrtoNum(PHARMA_SONGAY_TIMKIEM_CHUCNANG)){
            		DlgUtil.showMsg("Giá trị tìm kiếm vượt quá số ngày cho phép " + replaceStrtoNum(PHARMA_SONGAY_TIMKIEM_CHUCNANG) + " ngày");
                    $("#txtNgayBD").focus();
                    return;
            	}
            }
            if (that.opt.lp == "3" && that.opt.ht == "9" && that.opt.gd == "THUOC" && PHARMA_DUYET_DLS_BOSUNG_TUTRUC == "1" && that.opt.td == "9") {
                //if(that.opt.td == "9"){
                _loadDSPhieuLamSan();
//				}else{
//					_loadDSPhieuLS_DaDuyet();
//				}
            } else if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                _loadDSPhieuBUCSTT();
            } else {
                _loadDSPhieu();
            }
            var gia_tri =
                {
                    "TENKHO": $("#cboKho").val(),
                    "TUNGAY": $("#txtNgayBD").val().trim(),
                    "DENNGAY": $("#txtNgayKT").val().trim(),
                    "LOAIPHIEU": $("#cboLoaiPhieu").val(),
                    "HINHTHUC": $("#cboHinhThuc").val(),
                    "TRANGTHAI": $("#cboTrangThaiDuyet").val()
                }
            var gia_tri_json = JSON.stringify(gia_tri);
            var param_ar = $(location).attr('href') + '$' + gia_tri_json + '$';
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_ADDTHAMSOKT",
                param_ar);


        });
        $('#grdPhieu').jqGrid('setGridParam', {
            onSelectRow: function (id) {
                var ret = $("#grdPhieu").jqGrid('getRowData', id);


                _showHuyGuiPhieu(ret);//DUCTT20181105
                if ((ret.TRANGTHAIID == '6' || ret.TRANGTHAIID == '5') && that.opt.ht == 15 && (ret.NHAPID != '' || ret.XUATID != '')) {

                    if (ret.KIEU == '2') _loadChiTiet(ret.NHAPID, ret.TRANGTHAIID, ret.KIEU);
                    else
                        _loadChiTiet(ret.XUATID, ret.TRANGTHAIID, ret.KIEU);

                    $("#txtGHICHU").val(ret.DIACHI);
                    $("#txtNamSinh").val(ret.NAMSINH);
                    $("#txtGioiTinh").val(ret.GIOITINHNX);

                }else if(PHARMA_DUYETNHIEUPHIEU == '1' && that.opt.hospitalId == '1111' && that.opt.ht == 13){
                	var selRows = $("#grdPhieu").jqGrid('getGridParam','selarrrow');
                	if (selRows.indexOf(id) != -1
        					&& dsThem
        							.indexOf(ret.NHAPXUATID) == -1) {
        				dsThem.push(ret.NHAPXUATID);
        			}
        			if (selRows.indexOf(id) == -1) {
        				var i = dsThem
        						.indexOf(ret.NHAPXUATID);
        				if (i != -1) {
        					dsThem.splice(i, 1);
        				}
        			}
        			var danhsach = '';
        			if(dsThem.length > 1){ divCT
        				$("#divCT_NGT").hide();
        				for(var i = 0; i < dsThem.length; i++){
            				if(i < dsThem.length - 1){
            					danhsach += dsThem[i];
            					danhsach += ",";
            				} else{
            					danhsach += dsThem[i];
            				}
            			}
            			var sql_par_ = RSUtil.buildParam("",[danhsach]);
            			GridUtil.loadGridBySqlPage("grdThuoc","DUC01S.CTTHUOC_NGT",sql_par_);
        			}else{
        				$("#divCT_NGT").show();
        				_loadChiTiet(ret.NHAPXUATID, ret.TRANGTHAIID, ret.KIEU);
        			}
        			
        			
                }
                else if ((ret.TRANGTHAIID == '6' || ret.TRANGTHAIID == '5') && that.opt.ht == 9 && (ret.NHAPID != '' || ret.XUATID != '') && ret.KIEU == '2' && PHARMA_CANHBAOHSD_HOANTRATUTRUC == '1') {
                    _loadChiTiet(ret.XUATID, ret.TRANGTHAIID, ret.KIEU);
                } else if ((ret.TRANGTHAIID == '6' || ret.TRANGTHAIID == '5') && (that.opt.ht == 2 || that.opt.ht == 9 || that.opt.ht.includes("8") || that.opt.ht == 4 || that.opt.ht.includes("7")) && ret.XUATID != '' && (ret.KIEU == '2' || ret.KIEU == '3')
                    && ((that.opt.hospitalId == '10284' || that.opt.hospitalId == '30360' || that.opt.hospitalId == '987' || that.opt.hospitalId == '930')
                        || PHARMA_HIENPHIEUXUAT == '1')
                ) {
                    _loadChiTiet(ret.XUATID, ret.TRANGTHAIID, ret.KIEU);
                } else _loadChiTiet(ret.NHAPXUATID, ret.TRANGTHAIID, ret.KIEU);

                if (that.opt.ht == '12' && (that.opt.hospitalId == '30300' || that.opt.hospitalId == '30980' || that.opt.hospitalId == '35099' || that.opt.hospitalId == '33777')) {
                    var ret2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D044.15", ret.NHAPXUATID);
                    if (ret2 != null && ret2 != "") {
                        var _freport = ret2.split(";");
                        $("#txtSoPhieuIn").val(_freport.length);
                    } else {
                        $("#txtSoPhieuIn").val('');
                    }

                }


                if (that.opt.ht == '15' && ret.TRANGTHAIID == '5' && (that.opt.hospitalId == '36280' || that.opt.hospitalId == '1111' || that.opt.hospitalId == '38280')) {
                    $("#btnDuyetDT").show();
                } else $("#btnDuyetDT").hide();

                if (that.opt.hospitalId == '1022' && (that.opt.ht == '12' || that.opt.ht == '2' || that.opt.ht == '9' || that.opt.ht == '4') &&
                    (that.opt.type == '4' || that.opt.type == '45') && ret.TRANGTHAIID == '5') {
                    $("#toolbarIdtbBatDauSoan").show();
                    $("#toolbarIdtbHuySoan").show();
                    if (ret.DAYEUCAU == '10') {
                        $("#toolbarIdtbBatDauSoan").prop("disabled", true);
                        $("#toolbarIdtbHuySoan").prop("disabled", false);
                    } else {
                        $("#toolbarIdtbBatDauSoan").prop("disabled", false);
                        $("#toolbarIdtbHuySoan").prop("disabled", true);
                    }
                } else {
                    $("#toolbarIdtbBatDauSoan").hide();
                    $("#toolbarIdtbHuySoan").hide();
                    $("#toolbarIdtbBatDauSoan").prop("disabled", true);
                    $("#toolbarIdtbHuySoan").prop("disabled", true);
                }


                $("#txtTENBENHNHAN").val(ret.TENBENHNHAN);//DucTT20181024
                $("#txtCHANDOAN").val(ret.CHANDOAN);
                GridUtil.unmarkAll('grdPhieu');
                GridUtil.markRow('grdPhieu', id);

                $("#txtID5").val(ret.MAUBENHPHAMID);
                $("#txtHOTEN5").val(ret.TENBENHNHAN);
                //$("#toolbarIdtbIn").prop("disabled",false);
                $("#toolbarIdtbInNhap").prop("disabled", false);
                $("#toolbarIdtbInNhapYHCT").prop("disabled", false);
                $("#toolbarIdtbInNhapMAU").prop("disabled", false);
                $("#toolbarIdtbInNhapGiaDY").prop("disabled", false);

                $("#toolbarIdtbInNhapKT").prop("disabled", false);
                $("#toolbarIdtbInDuTru").prop("disabled", false);
                $("#toolbarIdtbInXuat").prop("disabled", false);
                $("#toolbarIdtbInXuatTra_NCC").prop("disabled", false);
                $("#toolbarIdtbSoKiemNhapHoaDon").prop("disabled", false);
                $("#toolbarIdtbBBKiemNhapHoaDon").prop("disabled", false);
                $("#btnInPhieuBBKiemKe").prop("disabled", false);
                $("#toolbarIdtbBBKiemKeHoaDon").prop("disabled", false);
                $("#toolbarIdtbBBKiemNhapHoaDonExcel").prop("disabled", false);
                $("#toolbarIdtbBBKiemNhapHoaDon_TDY").prop("disabled", false);
                $("#toolbarIdtbBBXuatHuy").prop("disabled", false);
                $("#toolbarIdtbBBXuatHuyPL12").prop("disabled", false);
                $("#toolbarIdtbGiayTT").prop("disabled", false);
                $("#toolbarIdtbGiayTT_VT").prop("disabled", false);

                $("#toolbarIdtbInLinhHoaChatKhoaPhong").prop("disabled", false);//********
                $("#toolbarIdtbInPhieuYDungCuKhoaPhong").prop("disabled", false);//********

                if (that.opt.ht.includes("9") || that.opt.ht.includes("12") || that.opt.ht.includes("13") || that.opt.ht.includes("14")) {
                    $("#btnDonThuoc").show();
                    $("#btnDonThuoc").prop("disabled", false);
                } else {
                    $("#btnDonThuoc").prop("disabled", true);
//					$("#btnDonThuoc").show();
                }
                //if( (ret.NHAPID == '' && ret.XUATID == '')|| !that.opt.type.includes("45")){
                //$("#btnXemPhieu").prop("disabled",true);
                //}
                if (ret.KIEU == 2 && ret.XUATID != '' && !that.opt.type.includes("45")) {
                    $("#btnXemPhieu").prop("disabled", false);
                } else if (ret.KIEU == 2 && ret.NHAPID != '' && that.opt.type.includes("4")) {
                    $("#btnXemPhieu").prop("disabled", false);
                } else if (ret.KIEU == 2 && ret.NHAPID != '' && that.opt.type.includes("45")) {
                    $("#btnXemPhieu").prop("disabled", false);
                } else if (ret.KIEU == 3 && ret.NHAPID != '' && !that.opt.type.includes("45")) {
                    $("#btnXemPhieu").prop("disabled", false);
                } else if (ret.KIEU == 3 && ret.XUATID != '' && that.opt.type.includes("45")) {
                    $("#btnXemPhieu").prop("disabled", false);
                } else if (ret.KIEU == 3 && ret.XUATID != '' && that.opt.type.includes("4")) {
                    $("#btnXemPhieu").prop("disabled", false);
//				if(ret.TTPHIEUNX == 1){
//					$("#btnGoDuyet").hide();
//				}
                } else if (ret.KIEU == 2 && ret.NHAPID != '' && that.opt.type.includes("4")) {
                    $("#btnXemPhieu").prop("disabled", false);
                } else {
                    $("#btnXemPhieu").prop("disabled", true);
                }

                if (that.opt.ht.includes("6") || that.opt.ht.includes("5")) {
                    if (ret.TRANGTHAIID == "6")
                        $("#btnXemPhieu").prop("disabled", false);
                }

                if (ret.TTPHIEUNX == '4' && PHARMA_SHOW_BTHUYDUYET_PNX == '1') {
                    $("#btnGoDuyet").prop("disabled", true);
                }

                if (PHARMA_SONGAY_GODUYET > 0 && that.opt.ht.includes("13") && ret.KIEU == 3 && ret.XUATID != '') {
                    var ngayduyet = moment(ret.NGAYDUYET, 'DD/MM/YYYY').add(Number(PHARMA_SONGAY_GODUYET), 'days');
                    var _ngayht = moment(moment(), 'DD/MM/YYYY');
                    if (_ngayht > ngayduyet) {
                        $("#btnGoDuyet").prop("disabled", true);
                    } else {
                        $("#btnGoDuyet").prop("disabled", false);
                    }
                }

                if (ret.DANGXULY == '0' && that.opt.td == "9") {
                    $("#toolbarIdtbDuyetLamSan").prop("disabled", true);
                } else {
                    $("#toolbarIdtbDuyetLamSan").prop("disabled", false);
                }
                if (ret.TRANGTHAIID != "6") {
                    $("#btnGoDuyet").prop("disabled", true);
                }

                if (PHARMA_CHECK_LIENTHONG_HMIS == '1') {
                    if (ret.TRANGTHAIIDHMIS == '8' && (ret.TRANGTHAIID == '6' || ret.TRANGTHAIID == '7')) {
                        $("#btnGuiDuyetHMIS").show();
                    } else {
                        $("#btnGuiDuyetHMIS").hide();
                    }
                }
                
                if (PHARMA_HUYPHIEU_GIUAO_DC == '1') {
                    if (that.opt.ht.includes("9") && ret.TRANGTHAIID == '5' && ret.XUATID != '' && ret.KIEU == 3 && that.opt.type.includes("45")) {
                        $("#btnHuyGiuAo").show();
                    } else {
                        $("#btnHuyGiuAo").hide();
                    }
                }
                /*if(ret.TTDUYETPHATTHUOC == '1' && that.opt.td == "14"){
                    $("#toolbarIdtbDuyetPhatThuoc").prop("disabled",false);
                    $("#toolbarIdtbGoDuyetPhatThuoc").prop("disabled",true);
                }else {
                    $("#toolbarIdtbDuyetPhatThuoc").prop("disabled",true);
                    $("#toolbarIdtbGoDuyetPhatThuoc").prop("disabled",false);
                }*/

                if (that.opt.td == "14") {
                    $("#toolbarIdtbDuyet").hide();
                    $("#toolbarIdtbDieuChinhDon").hide();
                    $("#toolbarIdtbBoSungNoThuoc").hide();
                    $("#ttDuyet").hide();

                    $("#btnGoDuyet").hide();
                    $("#btnGoNhapkho").hide();
                    $("#btnXemPhieu").hide();
                    $("#btnDonThuoc").hide();
                    var selRows = $(this).jqGrid('getGridParam', 'selarrrow');
                    //rowData = $('#gridList_TBI').jqGrid('getRowData', id);
                    if (selRows.indexOf(id) != -1 && dsHoaDon.indexOf(ret.NHAPXUATID) == -1) {
                        dsHoaDon.push(ret.NHAPXUATID);
                    }
                    if (selRows.indexOf(id) == -1) {
                        var i = dsHoaDon.indexOf(ret.NHAPXUATID);
                        if (i != -1) {
                            dsHoaDon.splice(i, 1);
                        }
                    }
                    /*if(PHARMA_SHOW_INPHIEULINH_DUYETPHAT == '1'){
                        if(ret.TTDUYETPHATTHUOC == '4'){
                            //$("#toolbarIdtbInPhieuLinh").prop("disabled",true);
                            $("#toolbarIdtbInPhieuLinh").hide();
                            $("#toolbarIdtbInPhieu2Lien").hide();
                        }else{
                            $("#toolbarIdtbInPhieuLinh").show();
                            $("#toolbarIdtbInPhieu2Lien").show();
                        }
                    }*/
                }

            },
            onSelectAll: function (id, status) {
                var rowIds = $('#grdPhieu').jqGrid(
                    'getDataIDs');
                for (i = 0; i < rowIds.length; i++) {
                    rowData = $('#grdPhieu').jqGrid(
                        'getRowData', rowIds[i]);
                    if (status == true
                        && dsHoaDon
                            .indexOf(rowData["NHAPXUATID"]) == -1) {
                        dsHoaDon.push(rowData["NHAPXUATID"]);
                    }
                    if (status == false) {
                        var j = dsHoaDon
                            .indexOf(rowData["NHAPXUATID"]);
                        if (j != -1) {
                            dsHoaDon.splice(j, 1);
                        }
                    }
                }
            },
            ondblClickRow: function (id) {
                var ret = $("#grdPhieu").jqGrid('getRowData', id);
                if (ret.TRANGTHAIID == 1) {

                    if (that.opt.type.includes("0") ||
                        that.opt.type.includes("1") ||
                        that.opt.type.includes("2") ||
                        that.opt.type.includes("3") ||
                        that.opt.type.includes("E")
                    ) {
                        tbSuaClick();
                    }
//			if(that.opt.type.includes("1")){
//				tbSuaClick();
//			}
//			if(that.opt.type.includes("2")){
//				tbSuaClick();
//			}
//			if(that.opt.type.includes("3")){
//				tbSuaClick();
//			}


                    if (that.opt.type.includes("6") ||
                        that.opt.type.includes("7")) {
                        tbSuaNCCClick();
                    }

                    if (that.opt.type.includes("8") ||
                        that.opt.type.includes("9")) {
                        tbSuaNhapBuClick();
                    }

                    if (that.opt.type.includes("A") ||
                        that.opt.type.includes("B")) {
                        tbSuaXuatHuyClick();
                    }

                    if (that.opt.type.includes("C") ||
                        that.opt.type.includes("D")) {
                        tbSuaXuatThieuClick();
                    }

//			if(that.opt.type.includes("E")){
//				tbSuaClick();
//			}
                    if (that.opt.type.includes("I")) {
                        tbSuaDTTHClick();
                    }

                }
            }
        });
        //xem phieu nhap xuat
        $("#btnXemPhieu").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _nx_id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _id;
            var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU
            if ((that.opt.type == '45' || that.opt.type == '4' || that.opt.ht == '5') && _kieu == 2) {
                _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPID;
            } else {
                _id = $("#grdPhieu").jqGrid('getRowData', row).XUATID;
            }
            /*	EventUtil.setEvent("nhapkho_cancel",function(e){
                    DlgUtil.close("dlgTTPhieu");
                });*/
            EventUtil.setEvent("nhapkho_cancel2", function (e) {
                DlgUtil.close("dlgTTPhieu");
                //_loadDSPhieu();
            });
            var myVar = {nhapxuatid: _id, nxid: _nx_id, kieu: $("#grdPhieu").jqGrid('getRowData', row).KIEU};
            dlgPopup = DlgUtil.buildPopupUrl("dlgTTPhieu", "divDlg", "manager.jsp?func=../duoc/DUC35T001_ThongTinPhieu", myVar, "Thông tin phiếu", 1200, 580);
            DlgUtil.open("dlgTTPhieu");
        });
        /*$("#btnXemPhieuXuat").on("click",function(e){
			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
			EventUtil.setEvent("nhapkho_cancel",function(e){
				DlgUtil.close("dlgTTPhieu");
			});
			var myVar={nhapxuatid:_id};
			dlgPopup=DlgUtil.buildPopupUrl("dlgTTPhieu","divDlg","manager.jsp?func=../duoc/DUC35T001_ThongTinPhieu",myVar,"Thông tin phiếu",1000,500);
			DlgUtil.open("dlgTTPhieu");
		});*/
        $("#btnDonThuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgTTPhieu");
            });
            var myVar = {nhapxuatid: _id, gd: _opt.gd};
            dlgPopup = DlgUtil.buildPopupUrl("dlgTTPhieu", "divDlg", "manager.jsp?func=../duoc/DUC51S001_DSDonThuoc", myVar, "Thông tin đơn thuốc/VT", 1200, 590);
            DlgUtil.open("dlgTTPhieu");
        });
        /*GridUtil.setGridParam("grdPhieu", {
			ondblClickRow : function(id) {
				var ret = $("#grdPhieu").jqGrid('getRowData',id);
				EventUtil.setEvent("nhapkho_cancel",function(e){
					DlgUtil.close("dlgTTPhieu");
				});
				var myVar={nhapxuatid:ret.NHAPXUATID};
				dlgPopup=DlgUtil.buildPopupUrl("dlgTTPhieu","divDlg","manager.jsp?func=../duoc/DUC35T001_ThongTinPhieu",myVar,"Thông tin phiếu",1000,500);
				DlgUtil.open("dlgTTPhieu");
			}
		});*/
        $('#grdThuoc').jqGrid('setGridParam', {
            onSelectRow: function (id) {
                GridUtil.unmarkAll('grdThuoc');
                GridUtil.markRow('grdThuoc', id);
            }
        });

        //in phieu nhap
        $("#toolbarIdtbInNhap").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            //'PHARMA_INTACHMA_PHIEUNHAP'
            var _par = ['PHARMA_INTACHMA_PHIEUNHAP'];
            var PHARMA_BC_TACH_PN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA",
                _par.join('$'));
            if (PHARMA_BC_TACH_PN == 1) {
                openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_THUOCTHUONG_TTKHA', 'pdf', par);
                openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_GNHT_TTKHA', 'pdf', par);
            } else {

                if (PHARMA_BC_NHAP_KHO == 1) {
                    //doViewReport( 'XLS','DUC008_PHIEUNHAPKHO_QD_BTC_A4_SN_HNM',par);
                    var rpName = "DUC008_PHIEUNHAPKHO_QD_BTC_A4_SN_HNM" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                    CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_QD_BTC_A4_SN_HNM", 'xls', par, rpName);
                    openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_A4_SN_HNM', 'pdf', par);
                } else if (PHARMA_BC_NHAP_KHO == 1108) {
                    var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                    var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                    if (_id == "")
                        return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                    EventUtil.setEvent("InPhieuNhap_success", function (e) {
                        DlgUtil.close("dlgInPhieuNhap");
                        if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                            _loadDSPhieuBUCSTT();
                        } else {
                            _loadDSPhieu();
                        }

                    });
                    var myVar = {nhapxuatid: _id};
                    dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuNhap", "divDlg",
                        "manager.jsp?func=../duoc/DUC_DSInPhieuNhap", myVar, "Danh sách phiếu Nhập", 350, 400);

                    dlgPopup.open("dlgInPhieuNhap");
                } else {
                    var rCheck = 0;
                    var rpName = '';
                    var Rpt = '';
                    //if(PHARMA_BC_NHAP_KHO != 965){
                    if (that.opt.ht == 2 && that.opt.hospitalId == '951' && that.opt.gd.includes("VATTU")) {
                        rpName = "DUC009_PHIEUNHAPKHO_VATTU_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                        //CommonUtil.inPhieu('window', "DUC009_PHIEUNHAPKHO_VATTU_QD_BTC_A4", 'xlsx', par, rpName);
                        Rpt = 'DUC009_PHIEUNHAPKHO_VATTU_QD_BTC_A4';
                        openReport('window', Rpt, 'pdf', par);
                    } else if (that.opt.hospitalId == '1111' && that.opt.gd.includes("VATTU")) {
                        rpName = "DUC008_PHIEUNHAPKHO_VATTU_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                        //CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_VATTU_QD_BTC_A4", 'xlsx', par, rpName);
                        Rpt = 'DUC008_PHIEUNHAPKHO_VATTU_QD_BTC_A4';
                        openReport('window', 'DUC008_PHIEUNHAPKHO_VATTU_QD_BTC_A4', 'pdf', par);
                    } else if (that.opt.hospitalId == '14866') {
                        rpName = "DUC008_PHIEUNK_DKCR_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                        // CommonUtil.inPhieu('window', "DUC008_PHIEUNK_DKCR_QD_BTC_A4", 'xlsx', par, rpName);
                        Rpt = 'DUC008_PHIEUNK_DKCR_QD_BTC_A4';
                        openReport('window', 'DUC008_PHIEUNK_DKCR_QD_BTC_A4', 'pdf', par);
                    } else if (that.opt.hospitalId == '987' && that.opt.gd.includes("VATTU")) {
                        rpName = "DUC008_PHIEUNKVATTU_QD_BTC_A4_DKLS" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                        // CommonUtil.inPhieu('window', "DUC008_PHIEUNKVATTU_QD_BTC_A4_DKLS", 'xlsx', par, rpName);
                        Rpt = 'DUC008_PHIEUNKVATTU_QD_BTC_A4_DKLS';
                        openReport('window', 'DUC008_PHIEUNKVATTU_QD_BTC_A4_DKLS', 'pdf', par);
                    } else if (that.opt.hospitalId == '987' && that.opt.gd.includes("THUOC")) {
                        rpName = "DUC008_PHIEUNKTHUOC_QD_BTC_A4_DKLS" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                        //CommonUtil.inPhieu('window', "DUC008_PHIEUNKTHUOC_QD_BTC_A4_DKLS", 'xlsx', par, rpName);
                        Rpt = 'DUC008_PHIEUNKTHUOC_QD_BTC_A4_DKLS';
                        openReport('window', 'DUC008_PHIEUNKTHUOC_QD_BTC_A4_DKLS', 'pdf', par);
                    } else if (that.opt.hospitalId == '10284' && that.opt.ht == 8) {
                        if (that.opt.gd.includes("THUOC")) {
                            Rpt = 'DUC008_PHIEUNHAPKHACTHUOC_A4_BDHN';
                            openReport('window', 'DUC008_PHIEUNHAPKHACTHUOC_A4_BDHN', 'pdf', par);
                        } else {
                            Rpt = 'DUC008_PHIEUNHAPKHACVATTU_A4_BDHN';
                            openReport('window', 'DUC008_PHIEUNHAPKHACVATTU_A4_BDHN', 'pdf', par);
                        }

                    }
                    //ductx - BVTM-1678
                    else if (that.opt.hospitalId == '10284' && (that.opt.gd && that.opt.gd.includes("VATTU"))) {
                        Rpt = 'DUC008_PHIEUNHAPKHO_VATTU_BTC_A4';
                        openReport('window', 'DUC008_PHIEUNHAPKHO_VATTU_BTC_A4', 'pdf', par);
                    } else if (that.opt.hospitalId == '10284' && that.opt.ht == '9' && that.opt.lp == "2") {
                        rCheck = 1;
                        $("#toolbarIdtbInPhieuLinh").click();
                    } else {
                        rpName = "DUC008_PHIEUNHAPKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                        // CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_QD_BTC_A4", 'xlsx', par, rpName);
                        Rpt = 'DUC008_PHIEUNHAPKHO_QD_BTC_A4';
                        openReport('window', 'DUC008_PHIEUNHAPKHO_QD_BTC_A4', 'pdf', par);
                    }

                    //}
                    if (PHARMA_TUDONG_TAIFILE == 1 && rCheck == 0) {
                        CommonUtil.inPhieu('window', Rpt, 'xlsx', par, rpName);
                    } else if (PHARMA_TUDONG_TAIFILE == 2 && rCheck == 0) {
                        rpName = "DUC008_PHIEUNHAPKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                        CommonUtil.inPhieu('window', Rpt, 'rtf', par, rpName);
                    }

                    //doViewReport( 'XLS','DUC008_PHIEUNHAPKHO_QD_BTC_A4',par);


                }
            }
        });

        $("#toolbarIdtbInNhap_ChuyenKho").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            openReport('window', 'DUC008_PHIEUNHAPKHO_CHUYENKHO', 'pdf', par);
            var rpName = "DUC008_PHIEUNHAPKHO_CHUYENKHO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_CHUYENKHO", 'xlsx', par, rpName);

        });

        // Phieu nhap kho (Hoan tra) DLKHA
        $("#toolbarIdtbInNhapHoanTra").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            openReport('window', 'DUC008_PHIEUNHAPKHO_HOANTRA', 'pdf', par);
            var rpName = "DUC008_PHIEUNHAPKHO_HOANTRA" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_HOANTRA", 'xlsx', par, rpName);
        });
        // Phieu nhap kho (Hoan tra) DLKHA

        $("#toolbarIdtbInNhapYHCT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            openReport('window', 'DUC008_PHIEUNHAPKHO_DONGY_DKLSN', 'pdf', par);
            var rpName = "DUC008_PHIEUNHAPKHO_DONGY_DKLSN" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_DONGY_DKLSN", 'xlsx', par, rpName);

        });
        $("#toolbarIdtbInNhapMAU").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            openReport('window', 'DUC008_PHIEUNK_MAU_DKLSN', 'pdf', par);
            var rpName = "DUC008_PHIEUNK_MAU_DKLSN" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC008_PHIEUNK_MAU_DKLSN", 'xlsx', par, rpName);
        });


        $("#toolbarIdtbInNhapGiaDY").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            var rpName = "DUC008_PHIEUNHAPKHO_DONGY_YDCTQNI" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_DONGY_YDCTQNI", 'xlsx', par, rpName);

            openReport('window', 'DUC008_PHIEUNHAPKHO_DONGY_YDCTQNI', 'pdf', par);

        });

        //in phieu nhap nha thuoc
        $("#toolbarIdtbInNhapNhaThuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            // var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            // _print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            // var rpName= "DUC008_PHIEUNHAPKHO_QD_BTC_A4" +
            // jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
            // CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_QD_BTC_A4",
            // 'xlsx', par, rpName);
            openReport('window', 'DUC008_PHIEUNHAPKHO_NHATHUOC', 'pdf', par);
            var rpName = "DUC008_PHIEUNHAPKHO_NHATHUOC" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHO_NHATHUOC", 'xlsx', par, rpName);


        });

        //tuyennx_add_start_20180706 L2DKHN-857 in phieu nhap ke toan
        $("#toolbarIdtbInNhapKT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            //doViewReport( 'XLS','DUC008_PHIEUNHAPKHOKT_QD_BTC_A4',par);
            var rpName = "DUC008_PHIEUNHAPKHOKT_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
            CommonUtil.inPhieu('window', "DUC008_PHIEUNHAPKHOKT_QD_BTC_A4", 'xls', par, rpName);
            openReport('window', 'DUC008_PHIEUNHAPKHOKT_QD_BTC_A4', 'pdf', par);
        });
        //tuyennx_add_end_20180706
        //nghiatd_add_phieu du tru
        $("#toolbarIdtbInDuTru").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEU_DU_TRU', 'pdf', par);
            if (PHARMA_TUDONG_TAIFILE == 1) {
                var rpName_ = "DUC_PHIEU_DU_TRU" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                CommonUtil.inPhieu('window', 'DUC_PHIEU_DU_TRU', 'xlsx', par, rpName_);
                var rpName = "DUC_PHIEU_DU_TRU" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                CommonUtil.inPhieu('window', 'DUC_PHIEU_DU_TRU', 'rtf', par, rpName);
            }
        });
        //nghiatd_add_end_phieu du tru

        //in sổ kiem nhap hoa don
        $("#toolbarIdtbSoKiemNhapHoaDon").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            var rpName = "DUC017_SOKIEMNHAPDONGY_YHCT_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC017_SOKIEMNHAPDONGY_YHCT_A4", 'xlsx', par, rpName);
            openReport('window', 'DUC017_SOKIEMNHAPDONGY_YHCT_A4', 'pdf', par);


        });

        //in bien ban kiem nhap hoa don
        $("#toolbarIdtbBBKiemNhapHoaDon").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            if (that.opt.hospitalId == '993' || that.opt.hospitalId == '930' || that.opt.hospitalId == '44280') {
                openReport('window', 'DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4', 'pdf', par);
            } else {
                if (that.opt.lk == "4" && that.opt.hospitalId == '987') {
                    var rpName = "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                    CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHATHUOC_A4", 'xls', par, rpName);

                    var rpNameword = "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                    CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHATHUOC_A4", 'rtf', par, rpNameword);

                    openReport('window', 'DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHATHUOC_A4', 'pdf', par);
                } else {
                    var rpName = "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                    CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4", 'xls', par, rpName);

                    var rpNameword = "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                    CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4", 'rtf', par, rpNameword);

                    openReport('window', 'DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4', 'pdf', par);
                }

            }

        });

        $("#btnInPhieuBBKiemKe").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            if (that.opt.hospitalId == '993' || that.opt.hospitalId == '930' || that.opt.hospitalId == '44280') {
                openReport('window', 'DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4', 'pdf', par);
            } else {
                if (that.opt.lk == "4" && that.opt.hospitalId == '987') {
                    var rpName = "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                    CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHATHUOC_A4", 'xls', par, rpName);

                    var rpNameword = "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                    CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHATHUOC_A4", 'rtf', par, rpNameword);

                    openReport('window', 'DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHATHUOC_A4', 'pdf', par);
                } else {
                    var rpName = "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                    CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4", 'xls', par, rpName);

                    var rpNameword = "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                    CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4", 'rtf', par, rpNameword);

                    openReport('window', 'DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4', 'pdf', par);
                }

            }

        });

        $("#toolbarIdtbBBKiemNhapHongVo").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            var rpName = "DUC017_BBKIEMKETTHUOCVATTUHONGVO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
            if (that.opt.hospitalId == '30680') {
                CommonUtil.inPhieu('window', "DUC017_BBKIEMKETNHAPHONGMAT_YHCTLSN", 'xls', par, rpName);
                openReport('window', 'DUC017_BBKIEMKETNHAPHONGMAT_YHCTLSN', 'pdf', par);

                var rpName1 = "DUC017_BBXACNHANHONGVO_YHCTLSN" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';

                CommonUtil.inPhieu('window', "DUC017_BBXACNHANHONGVO_YHCTLSN", 'xls', par, rpName1);
                openReport('window', 'DUC017_BBXACNHANHONGVO_YHCTLSN', 'pdf', par);
            } else {
                CommonUtil.inPhieu('window', "DUC017_BBKIEMKETNHAPHONGMAT_YHCTLSN", 'xls', par, rpName);
                openReport('window', 'DUC017_BBKIEMKETNHAPHONGMAT_YHCTLSN', 'pdf', par);
            }


        });
        
        $("#toolbarIdtbBBKiemNhapXuatChuyenKho").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            var rpName = "DUC017_BBKIEMNHAP_TTYTBAOLAM" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
            
		    CommonUtil.inPhieu('window', "DUC017_BBKIEMNHAP_TTYTBAOLAM", 'xls', par, rpName);
		    openReport('window', 'DUC017_BBKIEMNHAP_TTYTBAOLAM', 'pdf', par)


        });

        $("#btnInBangKe").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC01S002.BANGKE01", _id);
            if (data_ar != null && data_ar.length > 0) {
                var row = data_ar[0];
                vienphi_tinhtien.inBangKe(row.TIEPNHANID, row.DOITUONGBENHNHANID, row.LOAITIEPNHANID);
            }

        });
        //in bien ban kiem ke hoa don
        $("#toolbarIdtbBBKiemKeHoaDon").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            var rpName = "DUC_BBKIEMKE_KIEMNHAP" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
            CommonUtil.inPhieu('window', "DUC_BBKIEMKE_KIEMNHAP", 'xls', par, rpName);

            var rpNameword = "DUC_BBKIEMKE_KIEMNHAP" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
            CommonUtil.inPhieu('window', "DUC_BBKIEMKE_KIEMNHAP", 'rtf', par, rpNameword);

            openReport('window', 'DUC_BBKIEMKE_KIEMNHAP', 'pdf', par);


        });

        $("#toolbarIdtbBBKiemNhapHoaDonExcel").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            var rpName = "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCVATTUTIEUHAO_NHIQUANGNGAI_A4", 'xlsx', par, rpName);
        });

        $("#toolbarIdtbBBXuatHuy").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            if (that.opt.hospitalId == '5926' && that.opt.ht == "7") { //5926
                openReport('window', 'DUC_BBXUATHUY_5926', 'rtf', par);
                openReport('window', 'DUC_BBXUATHUY_5926', 'pdf', par);
            } else {
                var rpNameword = "DUC_BBXUATHUY_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                CommonUtil.inPhieu('window', "DUC_BBXUATHUY_A4", 'xlsx', par, rpNameword);
                openReport('window', 'DUC_BBXUATHUY_A4', 'pdf', par);
            }

        });

        $("#toolbarIdtbBBXuatHuyPL12").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            var rpNameword = "DUC_BBXUATHUY_PL12_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC_BBXUATHUY_PL12_A4", 'xlsx', par, rpNameword);
            openReport('window', 'DUC_BBXUATHUY_PL12_A4', 'pdf', par);
        });


        //in bien ban kiem nhap hoa don thuoc dong y
        $("#toolbarIdtbBBKiemNhapHoaDon_TDY").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'0');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            if (PHARMA_BC_XUAT_KHO == 987) {
                var rpName = "DUC017_BBKIEMKETNHAPGIADONGY_DKLSN_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                CommonUtil.inPhieu('window', "DUC017_BBKIEMKETNHAPGIADONGY_DKLSN_A4", 'xls', par, rpName);
                var rpNameword = "DUC017_BBKIEMKETNHAPGIADONGY_DKLSN_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                CommonUtil.inPhieu('window', "DUC017_BBKIEMKETNHAPGIADONGY_DKLSN_A4", 'rtf', par, rpNameword);
                openReport('window', 'DUC017_BBKIEMKETNHAPGIADONGY_DKLSN_A4', 'pdf', par);

            } else {
                var rpName = "DUC017_BBKIEMKETTHUOCGIADONGY_YHCTQNI_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCGIADONGY_YHCTQNI_A4", 'xls', par, rpName);
                var rpNameword = "DUC017_BBKIEMKETTHUOCGIADONGY_YHCTQNI_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                CommonUtil.inPhieu('window', "DUC017_BBKIEMKETTHUOCGIADONGY_YHCTQNI_A4", 'rtf', par, rpNameword);
                openReport('window', 'DUC017_BBKIEMKETTHUOCGIADONGY_YHCTQNI_A4', 'pdf', par);
            }


        });
        //in phieu xuat
        $("#toolbarIdtbInXuat").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'1');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            var rpName = '';
            var rpName_ht = '';
            var Rpt = '';
            var Rpt2 = '';
            if (PHARMA_BC_XUAT_KHO == 1) {
                //doViewReport( 'XLS','DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM',par);
                rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM", 'xls', par, rpName);
                Rpt = 'DUC009_PHIEUXUATKHO_QD_BTC_A4_SN_HNM';
                openReport('window', Rpt, 'pdf', par);

            }
            /*else if (that.opt.gd.includes("VATTU")){
				doViewReport( 'XLS','DUC009_PHIEUXUATKHO_VATTU_BVNT_A4',par);
				openReport('window', 'DUC009_PHIEUXUATKHO_VATTU_BVNT_A4', 'pdf', par);
			}*/
            else if (PHARMA_BC_NHAP_KHO == 919) {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                if (_id == "")
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                EventUtil.setEvent("InPhieuXuat_success", function (e) {
                    DlgUtil.close("dlgInPhieuXuat");
                    if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                        _loadDSPhieuBUCSTT();
                    } else {
                        _loadDSPhieu();
                    }

                });
                var myVar = {nhapxuatid: _id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
                    "manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar, "Danh sách phiếu Xuất", 350, 400);

                dlgPopup.open("dlgInPhieuXuat");
            } else if (PHARMA_BC_NHAP_KHO == 14866) {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                if (_id == "")
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                EventUtil.setEvent("InPhieuXuat_success", function (e) {
                    DlgUtil.close("dlgInPhieuXuat");
                    if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                        _loadDSPhieuBUCSTT();
                    } else {
                        _loadDSPhieu();
                    }

                });
                var myVar = {nhapxuatid: _id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
                    "manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar, "Danh sách phiếu Xuất", 350, 400);

                dlgPopup.open("dlgInPhieuXuat");
            } else if (PHARMA_BC_NHAP_KHO == 944) {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                if (_id == "")
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                EventUtil.setEvent("InPhieuXuat_success", function (e) {
                    DlgUtil.close("dlgInPhieuXuat");
                    if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                        _loadDSPhieuBUCSTT();
                    } else {
                        _loadDSPhieu();
                    }
                });
                var myVar = {nhapxuatid: _id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
                    "manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar, "Danh sách phiếu Xuất", 350, 400);
                dlgPopup.open("dlgInPhieuXuat");
            } else if (that.opt.hospitalId == '5926' || that.opt.hospitalId == '9342' || that.opt.hospitalId == '9702' || that.opt.hospitalId == '9901') {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                if (_id == "")
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                var myVar = {nhapxuatid: _id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
                    "manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar, "Danh sách phiếu Xuất", 350, 400);
                dlgPopup.open("dlgInPhieuXuat");
            } else if (that.opt.hospitalId == '923') {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                if (_id == "")
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                var myVar = {nhapxuatid: _id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
                    "manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar, "Danh sách phiếu Xuất", 350, 400);
                dlgPopup.open("dlgInPhieuXuat");
            } else if (that.opt.hospitalId == '30860') {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                if (_id == "")
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                var myVar = {nhapxuatid: _id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
                    "manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar, "Danh sách phiếu Xuất", 350, 400);
                dlgPopup.open("dlgInPhieuXuat");
            } else if (that.opt.hospitalId == '30680') {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                if (_id == "")
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                var myVar = {nhapxuatid: _id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
                    "manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar, "Danh sách phiếu Xuất", 350, 400);
                dlgPopup.open("dlgInPhieuXuat");
            } else if (PHARMA_BC_NHAP_KHO == 26780 || PHARMA_BC_NHAP_KHO == 29040) {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                if (_id == "")
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
                var myVar = {nhapxuatid: _id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
                    "manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar, "Danh sách phiếu Xuất", 350, 400);

                dlgPopup.open("dlgInPhieuXuat");
            } else if (PHARMA_BC_NHAP_KHO == 10284) {
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSXUATRA.15", _id);
                var _param = [{
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }];
                if (ret != null && ret != '') {
                    var _freport = ret.split(";");
                    for (var i = 0; i < _freport.length; i++) {
                        openReport('window', _freport[i].trim(), "pdf", _param);
                        //CommonUtil.inPhieu('window', _freport[i].trim(), 'xlsx', _param, _freport[i].trim() + '.xlsx');
                    }
                }
            } else if (PHARMA_BC_NHAP_KHO == 1108) {
                rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4_DLKHA" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_DLKHA", 'xlsx', par, rpName);
                Rpt = 'DUC009_PHIEUXUATKHO_QD_BTC_A4_DLKHA';
                openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_DLKHA', 'pdf', par);

                rpName_ht = "DUC009_PHIEUXUATKHO_QD_BTC_A4_THT" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_THT", 'xlsx', par, rpName_ht);
                Rpt2 = 'DUC009_PHIEUXUATKHO_QD_BTC_A4_THT';
                openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_THT', 'pdf', par);

            } else if (PHARMA_BC_NHAP_KHO == 987) {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                var _hinhthucid = $("#grdPhieu").jqGrid('getRowData', row).HINHTHUCID;
                if (_id == "")
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                if (_hinhthucid == 7 || _hinhthucid == 8 || _hinhthucid == 6) {
                    rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4_XUATHK_DKLSO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_XUATHK_DKLSO", 'xlsx', par, rpName);
                    Rpt = 'DUC009_PHIEUXUATKHO_QD_BTC_A4_XUATHK_DKLSO';
                    openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_XUATHK_DKLSO', 'pdf', par);
                } else if (that.opt.ht == 2 && that.opt.hospitalId == '987') {
                    if (that.opt.gd.includes("VATTU")) {
                        rpName = "DUC009_PHIEUXUAT_CHUYENKHO_VATTU_A4_DKLSO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                        //CommonUtil.inPhieu('window', "DUC009_PHIEUXUAT_CHUYENKHO_VATTU_A4_DKLSO", 'xlsx', par, rpName);
                        Rpt = 'DUC009_PHIEUXUAT_CHUYENKHO_VATTU_A4_DKLSO';
                        openReport('window', 'DUC009_PHIEUXUAT_CHUYENKHO_VATTU_A4_DKLSO', 'pdf', par);
                    } else {
                        var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                        var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                        if (_id == "")
                            return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

                        EventUtil.setEvent("InPhieuXuat_success", function (e) {
                            DlgUtil.close("dlgInPhieuXuat");
                            _loadDSPhieu();
                        });
                        var myVar = {nhapxuatid: _id};
                        dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuXuat", "divDlg",
                            "manager.jsp?func=../duoc/DUC_DSInPhieuXuatTTKHA", myVar, "Danh sách phiếu Xuất", 350, 400);

                        dlgPopup.open("dlgInPhieuXuat");
                        // var rpName= "DUC009_PHIEUXUAT_CHUYENKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
                        // CommonUtil.inPhieu('window', "DUC009_PHIEUXUAT_CHUYENKHO_QD_BTC_A4", 'xlsx', par, rpName);
                        // openReport('window', 'DUC009_PHIEUXUAT_CHUYENKHO_QD_BTC_A4', 'pdf', par);
                    }
                } else {
                    rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4_DKLSO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_DKLSO", 'xlsx', par, rpName);
                    Rpt = 'DUC009_PHIEUXUATKHO_QD_BTC_A4_DKLSO';
                    openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_DKLSO', 'pdf', par);
                }
            } else if (PHARMA_BC_NHAP_KHO == 957) {
                if (that.opt.type == 4) {
                    rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_DUYETYC_QD_BTC_A4_957", 'xlsx', par, rpName);
                    Rpt = 'DUC009_PHIEUXUATKHO_DUYETYC_QD_BTC_A4_957';
                    openReport('window', 'DUC009_PHIEUXUATKHO_DUYETYC_QD_BTC_A4_957', 'pdf', par);
                } else {
                    rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4", 'xlsx', par, rpName);
                    Rpt = 'DUC009_PHIEUXUATKHO_QD_BTC_A4';
                    openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4', 'pdf', par);
                }
            } else {
                //doViewReport( 'XLS','DUC009_PHIEUXUATKHO_QD_BTC_A4',par);
                if (that.opt.ht == 2 && that.opt.hospitalId == '987') {
                    openReport('window', 'DUC009_PHIEUXUAT_CHUYENKHO_QD_BTC_A4', 'pdf', par);
                } else if (that.opt.ht == 2 && that.opt.hospitalId == '951' && that.opt.gd.includes("VATTU")) {
                    rpName = "DUC009_PHIEUXUATKHO_VATTU_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_VATTU_QD_BTC_A4", 'xlsx', par, rpName);
                    Rpt = 'DUC009_PHIEUXUATKHO_VATTU_QD_BTC_A4';
                    openReport('window', 'DUC009_PHIEUXUATKHO_VATTU_QD_BTC_A4', 'pdf', par);
                } else if (that.opt.hospitalId == '993' || that.opt.hospitalId == '930') {
                    //START L2PT-14367
                    rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN", 'xlsx', par, rpName);
                    Rpt = 'DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN';
                    openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN', 'pdf', par);
                } else if (that.opt.ht == 2 && that.opt.hospitalId == '965' && that.opt.gd.includes("THUOC")) {
                    var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSXUATKHO.965", _id);
                    if (ret != null && ret != '') {
                        var _freport = ret.split(";");
                        for (var i = 0; i < _freport.length; i++) {
                            rpName = _freport[i].trim() + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                            CommonUtil.inPhieu('window', _freport[i], 'xlsx', par, rpName);
                            openReport('window', _freport[i].trim(), 'pdf', par);
                        }
                    }
                } else if (that.opt.hospitalId == '902') {
                //START L2PT-14367
                //rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                // CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4_SNPYN", 'xlsx', par, rpName);
                Rpt = 'DUC009_PHIEUXUATKHO_QD_BTC_A4';
                openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4', 'pdf', par);
                }
                else {
                    rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4", 'xlsx', par, rpName);
                    Rpt = 'DUC009_PHIEUXUATKHO_QD_BTC_A4';
                    openReport('window', 'DUC009_PHIEUXUATKHO_QD_BTC_A4', 'pdf', par);
                }

            }
            if (PHARMA_TUDONG_TAIFILE == 1) {
                if (Rpt != '') {
                    CommonUtil.inPhieu('window', Rpt, 'xlsx', par, rpName);
                }
                if (Rpt2 != '') {
                    CommonUtil.inPhieu('window', Rpt2, 'xlsx', par, rpName_ht);
                }
            }else if (PHARMA_TUDONG_TAIFILE == 2) {
                rpName = "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                CommonUtil.inPhieu('window', Rpt, 'rtf', par, rpName);
            }

        });

        $("#toolbarIdtbInXuatKhacDakSong").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC009_PHIEUXUATKHO_XUATKHAC_QD_BTC_A4', 'pdf', par);

        });

        //in phieu xuat  gay nghiện hướng thần bến lức LAN
        $("#toolbarIdtbInPhieuXuatGNHT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEUXUATKHO_TGN_THT_BENLUC_LAN_A4', 'pdf', par);
            if (PHARMA_TUDONG_TAIFILE == 1) {
                rpName = "DUC_PHIEUXUATKHO_TGN_THT_BENLUC_LAN_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                CommonUtil.inPhieu('window', 'DUC_PHIEUXUATKHO_TGN_THT_BENLUC_LAN_A4', 'xlsx', par, rpName);

            }

        });
		// in phieu xuat hoa chat KHTH NINHHOA
		$("#toolbarIdtbInPhieuXuatKHTH").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_DSInPhieuXuatTTKHA_36940', 'pdf', par);
            

        });

        //in phieu xuat tra nha cung cap
        $("#toolbarIdtbInXuatTra_NCC").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'1');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            //var rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
            //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4", 'xlsx', par, rpName);
            if (that.opt.hospitalId == "63540") {
                var rpName= "DUC_PHIEUHOANTRANHACUNGCAP" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
                CommonUtil.inPhieu('window', "DUC_PHIEUHOANTRANHACUNGCAP", 'xlsx', par, rpName);
            } else {
                openReport('window', 'DUC_PHIEUHOANTRANHACUNGCAP', 'pdf', par);
            }

        });
        // In phieu xuat my pham cho DLKHA
        $("#toolbarIdtbInXuatMyPham").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC009_PHIEUXUATKHO_MYPHAM_DLKHA', 'pdf', par);
        });
        // In phieu xuat sinh pham cho DLKHA
        $("#toolbarIdtbInXuatSinhPham").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC009_PHIEUXUATKHO_SINHPHAM_DLKHA', 'pdf', par);
        });
        // In phieu xuat y dung cu cho DLKHA
        $("#toolbarIdtbInXuatYDungCu").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC009_PHIEUXUATKHO_YDUNGCU', 'pdf', par);
        });
        // In phieu xuat khac BVBD HNI
        $("#toolbarIdtbPhieuXuatKhac").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == undefined) {
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            }

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            if (that.opt.gd == "XUATKHACVATTU") {
                openReport('window', 'DUC_PHIEUXUATKHAC_CHUYENKHO_VATTU_BVBD', 'pdf', par);
            } else {
                openReport('window', 'DUC_PHIEUXUATKHACTHUOC_BVBD', 'pdf', par);
            }

        });
        // In phieu tra thuoc BVBD HNI
        $("#toolbarIdtbPhieuTraThuochni").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEUTRATHUOC_BVBD', 'pdf', par);
        });
        // In phieu xuat khac vat tu BVBD HNI
        $("#toolbarIdtbPhieuXuatKhacVatTu").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEUXUATKHACVATTU_BVBD', 'pdf', par);
        });

        // In phieu xuat khac hoa chat BVBD HNI
        $("#toolbarIdtbPhieuXuatKhacHc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEUXUATKHACHOACHAT_BVBD', 'pdf', par);
        });

        // BVTM-3624 In phieu xuat khac CCDC BVBD HNI
        $("#toolbarIdtbPhieuXuatKhacCCDC").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEUXUATKHAC_CCDC_BVBD', 'pdf', par);
        });
        // In phieu xuat huy vat tu BVBD HNI
        $("#toolbarIdtbPhieuXuatHuyVatTu").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEUXUATHUYVATTU_BVBD', 'pdf', par);
        });
        // In phieu du tru tuyen xa BA TO QNI
        $("#toolbarIdtbPhieuDuTruXa").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEUDUTRUTVT_BATO_QNI', 'pdf', par);
        });
        //in phieu xuat bo sung
        $("#toolbarIdtbInXuatBoSung").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'1');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            //var rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
            //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4", 'xlsx', par, rpName);
            openReport('window', 'DUC009_PHIEUXUATKHO_BOSUNG', 'pdf', par);


        });
        //in phieu xuat koh cho tram y te DKDT10
        $("#toolbarIdtbInXuatTramYte").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'1');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            //var rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
            //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4", 'xlsx', par, rpName);
            openReport('window', 'DUC009_PHIEUXUATKHO_TRAMYTE_DT10_A4', 'pdf', par);


        });
        //in phieu xuat thu hoi
        $("#toolbarIdtbInXuatThuHoi").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            //_print(_id,'1');
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            //var rpName= "DUC009_PHIEUXUATKHO_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
            //CommonUtil.inPhieu('window', "DUC009_PHIEUXUATKHO_QD_BTC_A4", 'xlsx', par, rpName);
            openReport('window', 'DUC009_PHIEUXUATKHO_THUHOI', 'pdf', par);


        });

        //in phieu xuat kho kiem van chuyen noi bo
        $("#toolbarIdprint_9").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;

            _print(_id, '13');
        });

        // In phieu xuat lien thong duoc HMIS
        $("#toolbarIdtbInXuatHmis").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC009_PHIEUXUATKHO_HMIS_A4', 'pdf', par);
            if (PHARMA_TUDONG_TAIFILE == 1) {
                var rpName = "DUC009_PHIEUXUATKHO_HMIS_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                var Rpt = 'DUC009_PHIEUXUATKHO_HMIS_A4';
                CommonUtil.inPhieu('window', Rpt, 'xlsx', par, rpName);
            }
        });

        // In phieu xuat benh nhan
        $("#toolbarIdtbInXuatBenhNhan").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (_id == "" || _id == undefined) {
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            }

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC009_PHIEUXUATKHOBN_QD_BTC_A4', 'pdf', par);
            if (PHARMA_TUDONG_TAIFILE == 1) {
                var rpName = "DUC009_PHIEUXUATKHOBN_QD_BTC_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                var Rpt = 'DUC009_PHIEUXUATKHOBN_QD_BTC_A4';
                CommonUtil.inPhieu('window', Rpt, 'xlsx', par, rpName);
            }
        });

        // In phieu xuat kho duoc
        $("#toolbarIdtbInXuatKhoDuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (_id == "" || _id == undefined) {
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            }

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            if (that.opt.ht == '13') {
                openReport('window', 'DUC_PHIEUXUATKHO_NGOAITRU_DUOC', 'pdf', par);
            } else {
                openReport('window', 'DUC_PHIEUXUATKHO_DUOC', 'pdf', par);
            }

        });

        // In phieu nhap lien thong duoc HMIS
        $("#toolbarIdtbInNhapHmis").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC009_PHIEUNHAPKHO_HMIS_A4', 'pdf', par);
            if (PHARMA_TUDONG_TAIFILE == 1) {
                var rpName = "DUC009_PHIEUNHAPKHO_HMIS_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                var Rpt = 'DUC009_PHIEUNHAPKHO_HMIS_A4';
                CommonUtil.inPhieu('window', Rpt, 'xlsx', par, rpName);
            }
        });

        $("#toolbarIdtbGiayTT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }
            ];
            openReport('window', 'DUC_GIAYDENGHITHANHTOAN', 'pdf', par);
            var rpName = "DUC_GIAYDENGHITHANHTOAN" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
            CommonUtil.inPhieu('window', "DUC_GIAYDENGHITHANHTOAN", 'xls', par, rpName);

        });

        $("#toolbarIdtbGiayTT_VT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }
            ];
            openReport('window', 'DUC_GIAYDENGHITT_VATTU_BVNT', 'pdf', par);
            var rpName = "DUC_GIAYDENGHITT_VATTU_BVNT" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
            CommonUtil.inPhieu('window', "DUC_GIAYDENGHITT_VATTU_BVNT", 'xls', par, rpName);


        });
        $("#toolbarIdtbInPXThuocGayNghienHT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            if (that.opt.hospitalId == '29040') {
                var ret = '';
                ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC.DSPHIEU.GNHT", _id);
                if (ret == '' || ret == null) {
                    return DlgUtil.showMsg("Phiếu không có thuốc gây nghiện, hướng thần");
                }
                var rptlist = ret.split(";");
                for (var i = 0; i < rptlist.length; i++) {
                    openReport('window', rptlist[i].trim(), 'pdf', par);
                }
            } else
                openReport('window', 'DUC015_PHIEUXUATKHOTHUOCGAYNGHIENTAMTHANTIENCHATDUNGLAMTHUOC_MS3_TT192014_A4', 'pdf', par);
        });
        //in phieu linh thuoc
        $("#toolbarIdtbInPhieuLinhThuoc").on("click", function (e) {

            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            var par = [{
                name: 'nhapxuatid',
                type: 'Long',
                value: _id
            }
            ];
            openReport('window', 'DUC_PHIEU_LINH_THUOC', 'pdf', par);
        });

        // In phieu xuat giua cac kho
        $("#toolbarIdtbInXuatChuyenKho").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (PHARMA_BC_NHAP_KHO == 10284) {
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSXUATRA.15", _id);
                var _param = [{
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }];
                if (ret != null && ret != '') {
                    var _freport = ret.split(";");
                    for (var i = 0; i < _freport.length; i++) {
                        openReport('window', _freport[i].trim(), "pdf", _param);
                        //CommonUtil.inPhieu('window', _freport[i].trim(), 'xlsx', _param, _freport[i].trim() + '.xlsx');
                    }
                }
            } else {
                var par = [{
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }
                ];
                openReport('window', 'DUC_PHIEUXUATCHUYENKHO', 'pdf', par);
            }

            if (PHARMA_TUDONG_TAIFILE == 1) {
                var rpName_ = "DUC_PHIEUXUATCHUYENKHO" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                CommonUtil.inPhieu('window', 'DUC_PHIEUXUATCHUYENKHO', 'xlsx', par, rpName_);
            }

        });
        // in phieu xuat kho nha thuoc
        $("#toolbarIdtbInXuatNhaThuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }
            ];
            openReport('window', 'DUC_PHIEUXUATKHONHATHUOC', 'pdf', par);
        });

        //in phieu du tru thuoc
        $("#toolbarIdtbInPhieuDuTru").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEU_DU_TRU', 'pdf', par);
            if (PHARMA_TUDONG_TAIFILE == 1) {
                var rpName_ = "DUC_PHIEU_DU_TRU" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                CommonUtil.inPhieu('window', 'DUC_PHIEU_DU_TRU', 'xlsx', par, rpName_);
                var rpName = "DUC_PHIEU_DU_TRU" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                CommonUtil.inPhieu('window', 'DUC_PHIEU_DU_TRU', 'rtf', par, rpName);
            }
        });
        //in phieu du tru VT
        $("#toolbarIdtbInPhieuDuTruVT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            openReport('window', 'DUC_PHIEU_DUTRUVATTU', 'pdf', par);
            if (PHARMA_TUDONG_TAIFILE == 1) {
                var rpName_ = "DUC_PHIEU_DUTRUVATTU" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                CommonUtil.inPhieu('window', 'DUC_PHIEU_DUTRUVATTU', 'xlsx', par, rpName_);
                var rpName = "DUC_PHIEU_DUTRUVATTU" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                CommonUtil.inPhieu('window', 'DUC_PHIEU_DUTRUVATTU', 'rtf', par, rpName);
            }
        });
        $("#toolbarIdtbInDonThuocRaVien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_DONTHUOCRAVIEN_DKLAN', 'pdf', par);
        });

        $("#toolbarIdtbInDonThuocBenhNhan").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_DONTHUOCBN_LCI', 'pdf', par);
        });

        $("#toolbarIdtbInPhatThuocNgoaiTru").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];
            openReport('window', 'DUC_PHIEU_PHATTHUOC_NGTRU', 'pdf', par);
        });
        
        $("#toolbarIdtbInPLinhDSThuocNGT").on("click", function (e) {
        	var tungay = $("#txtNgayBD").val().trim();
    		var denngay = $("#txtNgayKT").val().trim();
            var par = [
            	{
	                name: 'khoid',
	                type: 'String',
	                value: $('#cboKho').val()
            	}
                ,
        		{
        			name : 'tungay',
        			type : 'date',
        			value : tungay
        		}
        		,
        		{
        			name : 'denngay',
        			type : 'date',
        			value : denngay
        		}
            ];
            openReport('window', 'DUC_DANHSACH_DON_NGOAITRU', 'pdf', par);
        });

        // in phieu linh thuoc gay nghien huong than 1 lien

        $("#toolbarIdtbInPLThuocGayNghienHT1Lien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'Long',
                value: _id
            }
            ];
            openReport('window', 'NTU025_PHIEULINHTHUOCGAYNGHIENHT_1LIEN_A4', 'pdf', par);
        });
        //in phieu linh thuoc
        $("#toolbarIdtbInPhieuLinhVatTu").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'Long',
                value: _id
            }
            ];
            openReport('window', 'DUC_Phieu_Linh_Vattu', 'pdf', par);
        });
        $("#toolbarIdprint_1").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];

            if (PHARMA_SHOW_INPHIEULINH_DUYETPHAT == '1') { // kiem tra neu thuoc da duyet phat thi ko cho in
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.KT.PTHUOC", _id);
                if (ret == 4) {
                    return DlgUtil.showMsg("Không in được do phiếu đã được duyệt phát thuốc.");
                }
            }

            // 'PHARMA_BC_THUOC_HUONG_THAN'
            var _par = ['PHARMA_BC_THUOC_HUONG_THAN'];
            var cauhinhHT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH.THEOMA",
                _par.join('$'));
            if (cauhinhHT == '1') {
                CommonUtil.openReportGet('window', 'NTU025_PHIEULINHTHUOCTPGAYNGHIENTIENCHAT_MS8_TT192014_A4', "pdf", par);
                //	CommonUtil.openReportGet('window', 'NTU025_PHIEULINHTHUOCTPGAYNGHIENHUONGTHANTIENCHAT_MS8_TT192014_A4', "pdf" , par);
            }
            if (that.opt.hospitalId == '924') {
                openReport('window', "NTU025_PHIEULINHTHUOCTPGAYNGHIENTPTIENCHAT_MS8_TT192014_A4", "pdf", par);
                openReport('window', "NTU025_PHIEULINHTHUOCTPHUONGTHANTPTIENCHAT_MS8_TT192014_A4", "pdf", par);
            } else if (that.opt.hospitalId == '30360') {
                openReport('window', "DUC_PHIEULINHTHUOC_GNHT_A4", "pdf", par);
            } else {
                openReport('window', "NTU025_PHIEULINHTHUOCTPGAYNGHIENHUONGTHANTIENCHAT_MS8_TT192014_A4", "pdf", par);
            }
        });

        // in phieu hoan tra thuoc GNHT
        $("#toolbarIdtbInPhieuTraThuocGNHT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }
            ];
            openReport('window', 'DUC_PHIEUTRATHUOC_GNHT_A4', 'pdf', par);
        });

//		$("#toolbarIdprint_2").on("click",function(e){
//			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
//			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
//			var par = [ {
// 				name : 'nhapxuatid',
// 				type : 'String',
// 				value : _id
// 			}];
// 		//	openReport('window', "NTU006_PHIEULINHVATTUYTETIEUHAO_03DBV01_TT23_A4", "pdf", par);
//			openReport('window', "DUC070_PHIEULINHVATTU_A4", "pdf", par);
//		});

        $("#toolbarIdprint_3").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            if (PHARMA_BC_NHAP_KHO == 965) {
                openReport('window', "NTU007_PHIEUTRALAITHUOCGNHT_05DBV01_TT23_A4_GNHT", "pdf", par);
                openReport('window', "NTU007_PHIEUTRALAITHUOCHOACHATVTYTTIEUHAO_05DBV01_TT23_A4", "pdf", par);

            } else if (PHARMA_BC_NHAP_KHO == 14866) {
                if (_id == "") {
                    return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
                }
                var myVar = {nhapxuatid: _id, hinhthucid: that.opt.ht, loaiphieu: that.opt.type};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuLinh", "divDlg",
                    "manager.jsp?func=../duoc/DUC_DanhSachPhieuIn", myVar, "Danh sách phiếu trả thuốc", 350, 400);

                dlgPopup.open("dlgInPhieuLinh");

            } else if (PHARMA_BC_NHAP_KHO == 10284) {
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSXUATRA.15", _id);
                var _param = [{
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }];
                if (ret != null && ret != '') {
                    var _freport = ret.split(";");
                    for (var i = 0; i < _freport.length; i++) {
                        openReport('window', _freport[i].trim(), "pdf", _param);
                        //CommonUtil.inPhieu('window', _freport[i].trim(), 'xlsx', _param, _freport[i].trim() + '.xlsx');
                    }
                }
            } else {
                openReport('window', "NTU007_PHIEUTRALAITHUOCHOACHATVTYTTIEUHAO_05DBV01_TT23_A4", "pdf", par);
            }

        });
        $("#toolbarIdprint_10").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (that.opt.hospitalId == '24560' && that.opt.ht == '12') {
                if (that.opt.gd == "VATTU") {
                    var par = [{
                        name: 'nhapxuatid',
                        type: 'String',
                        value: _id
                    }];
                    openReport('window', "DUC_PHIEUHOANTRAVATTU", "pdf", par);
                } else {
                    var par = [{
                        name: 'nhapxuatid',
                        type: 'String',
                        value: _id
                    }];
                    openReport('window', "DUC_PHIEUHOANTRATHUOC", "pdf", par);
                }
                return;

            }

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            if (that.opt.hospitalId == '7282' && that.opt.ht == '2') {
                openReport('window', "DUC_PHIEUHOANTRA_NHQNM", "pdf", par);
                openReport('window', "DUC_PHIEUHOANTRATVT_TUNGHIA", "pdf", par);
            } else if (that.opt.hospitalId == '39401') {
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSTRA.BH01", _id);
                var _param = [{
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }];
                if (ret != null && ret != '') {
                    var _freport = ret.split(";");
                    for (var i = 0; i < _freport.length; i++) {
                        openReport('window', _freport[i].trim(), "pdf", _param);
                    }
                }
            } else {
                openReport('window', "DUC_PHIEUHOANTRA_NHQNM", "pdf", par);
            }


        });

        // Phieu linh vpp SNPYN
        $("#toolbarIdtbInPhieuLinhVPP").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            openReport('window', 'NTU067_PHIEULINHVPP_01DBV01_TT23_A4', 'pdf', par);
        });

        // Phieu linh vaccin xuat tram
        $("#toolbarIdtbInPhieuLinhVaccin").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            openReport('window', 'NTU004_PHIEULINHVACCIN_XUATTRAM', 'pdf', par);
        });
        // Phieu nhap vpp SNPYN
        $("#toolbarIdtbInNhapVPP").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            openReport('window', 'DUC008_PHIEUNHAPKHO_VPP_QD_BTC_A4', 'pdf', par);
        });
        // Phieu xuat vpp SNPYN
        $("#toolbarIdtbInXuatVPP").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            openReport('window', 'DUC009_PHIEUXUATKHO_VPP_QD_BTC_A4', 'pdf', par);
        });

        $("#toolbarIdtbInDuTruMuaVPP").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id.toString()
            }];

            openReport('window', 'DUC009_PHIEUDUTRUMUAVPP', 'pdf', par);
        });
        /*	$("#toolbarIdtbInPhieuDuTru").on(
                    "click",
                    function(e) {
                        var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                        var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                        var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
                        var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
                        if(kieu!=3){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
                        if (_id == "")
                            return DlgUtil.showMsg("Chưa chọn phiếu dự trù!",undefined,2000);
    //					DUC01S002_GETKHOA
    //					DOIUNGID
                        var khoid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
                        var par = khoid +'$';
                        var result ='';
                        result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01S002_GETKHOA", par);
                        if(result=='-1') result ='';
                        param = $("#cboKho").val()+'$';
                        var loaikho =0;
                        loaikho = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002_GETLOAIKHO", param);
                        // DlgUtil.showMsg("bach viem hong");
                        var title ='';
    //					if(loaikho==1){
    //						title = 'PHIẾU LĨNH VẬT TƯ';
    //					}
    //					else title = 'PHIẾU LĨNH THUỐC';
                        var par = [ {
                            name : 'kinhgui',
                            type : 'String',
                            value : result
                        }, {
                            name : 'nguoilapbieu',
                            type : 'String',
                            value : ''
                        }, {
                            name : 'dutruid',//nhapxuatid
                            type : 'String',
                            value : _id
                        }, {
                            name : 'loai',
                            type : 'String',
                            value : '1'
                        } , {
                            name : 'maphieu',
                            type : 'String',
                            value : maphieu
                        } , {
                            name : 'tenphieu',
                            type : 'String',
                            value : title
                        } ];

                        openReport('window', 'DUC006_DUTRUTHUOC_06DBV01_TT22_A4',
                                'pdf', par);
                    });
            */
        $("#toolbarIdtbInPhieu2Lien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            rowdata = $("#grdPhieu").jqGrid('getRowData', row);
            var _id = rowdata.NHAPXUATID;
            var kieu = rowdata.KIEU;
            var maphieu = rowdata.MA;

            if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4' && checkDKin == '1') {
                return DlgUtil.showMsg("Đã tạo phiếu xuất, không in được phiếu!");
            }

            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            if (checkDKin == '2' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                if (rowdata.TRANGTHAIID == '5') {
                    return DlgUtil.showMsg("Phiếu chưa duyệt, không in được");
                } else if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4') {
                    return DlgUtil.showMsg("Đã nhập xuất trong kho, không in được");
                }
            }

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && rowdata.TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            if (PHARMA_IN_PHIEUYC_NOITRU == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && that.opt.ht == '12' && (that.opt.gd == 'THUOC' || that.opt.gd == 'VATTU')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            if (PHARMA_SHOW_INPHIEULINH_DUYETPHAT == '1') { // kiem tra neu thuoc da duyet phat thi ko cho in
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.KT.PTHUOC", _id);
                if (ret == 4) {
                    return DlgUtil.showMsg("Không in được do phiếu đã được duyệt phát thuốc.");
                }
            }

            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'NTU004_PHIEULINHTHUOC_01DBV01_TT23_A4',
                'pdf', par);
            if (that.opt.ht == '2' && that.opt.hospitalId == 54040) {//54040 tp hue
                var rpNameword = "NTU004_PHIEULINHTHUOC_01DBV01_TT23_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                CommonUtil.inPhieu('window', "NTU004_PHIEULINHTHUOC_01DBV01_TT23_A4", 'rtf', par, rpNameword);
            }
        });
        $("#toolbarIdtbInPhieuLinhTVTHaoPhi2Lien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;

            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var rowdata = $("#grdPhieu").jqGrid('getRowData', row);
            if (checkDKin == '2' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                if (rowdata.TRANGTHAIID == '5') {
                    return DlgUtil.showMsg("Phiếu chưa duyệt, không in được");
                } else if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4') {
                    return DlgUtil.showMsg("Đã nhập xuất trong kho, không in được");
                }
            }
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];
            if (PHARMA_SHOW_INPHIEULINH_DUYETPHAT == '1') { // kiem tra neu thuoc da duyet phat thi ko cho in
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.KT.PTHUOC", _id);
                if (ret == 4) {
                    return DlgUtil.showMsg("Không in được do phiếu đã được duyệt phát thuốc.");
                }
            }

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }
            if (PHARMA_KHONGIN_PHIEUTAOMOI == 1 && rowdata.TRANGTHAIID == '1') {
                return DlgUtil.showMsg("Không in được do phiếu đang ở trạng thái tạo mới.");
            }
            if (PHARMA_IN_PHIEULINH_MANHOM == 1) {
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC_MANHOM_NXID', _id);
                if (data_ar != null && data_ar.length > 0) {
                    for (var k = 0; k < data_ar.length; k++) {
                        openReport('window', 'DUC_PHIEULINHTIEUHAOKHOAPHONG_' + that.opt.hospitalId + '_' + data_ar[k].MANHOM,
                            'pdf', par);
                    }
                } else {
                    return DlgUtil.showMsg("Không có dữ liệu!", undefined, 2000);
                }

            } else {
                openReport('window', 'DUC_LINHTHUOCVATTUHAOPHITHEOKHOAPHONG_A4',
                    'pdf', par);
            }


        });

        $("#toolbarIdtbInPhieuLinhThuocHaoPhi2Lien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;

            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var rowdata = $("#grdPhieu").jqGrid('getRowData', row);
            if (checkDKin == '2' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                if (rowdata.TRANGTHAIID == '5') {
                    return DlgUtil.showMsg("Phiếu chưa duyệt, không in được");
                } else if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4') {
                    return DlgUtil.showMsg("Đã nhập xuất trong kho, không in được");
                }
            }
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            openReport('window', 'DUC_LINHTHUOCHAOPHITHEOKHOAPHONG_THUOC_A4',
                'pdf', par);


        });

        $("#toolbarIdtbInLinhGNHTBoSungTuTruc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;

            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];
            openReport('window', 'DUC_LINHTHUOC_GNHT_BOSUNGCOSOTUTRUC',
                'pdf', par);
        });

        //START - MinhTA - Bo sung phieu linh "Phiếu lĩnh vật tư CNTT theo khoa phòng" NHIHDG
        $("#toolbarIdtbInPhieuVTCNTTKhoaPhong").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid("getGridParam", "selrow");
            var _id = $("#grdPhieu").jqGrid("getRowData", row).NHAPXUATID;
            if (_id == "") return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            if (
                PHARMA_IN_PHIEULINH_HOANTRA == "1" &&
                $("#grdPhieu").jqGrid("getRowData", row).TRANGTHAIID != "6" &&
                (that.opt.ht == "4" || that.opt.ht == "9") &&
                (that.opt.gd == "THUOC" || that.opt.gd == "XUATDTTHKP")
            ) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }
            var par = [
                {
                    name: "nhapxuatid", //nhapxuatid
                    type: "String",
                    value: _id,
                },
            ];
            var rpName = "DUC_LINHVTCNTTTHEOKHOAPHONG_A4" + jsonrpc.AjaxJson.getSystemDate("DDMMYY-HH24MISS") + "." + "xls";
            openReport("window", "DUC_LINHVTCNTTTHEOKHOAPHONG_A4", "pdf", par);
        });
        //END - MinhTA - Bo sung phieu linh "Phiếu lĩnh vật tư CNTT theo khoa phòng" NHIHDG
        
        $("#toolbarIdtbInPhieuYDC_VACCIN_TTB").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid("getGridParam", "selrow");
            var _id = $("#grdPhieu").jqGrid("getRowData", row).NHAPXUATID;
            if (_id == "") return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            
            var par = [
                {
                    name: "nhapxuatid", //nhapxuatid
                    type: "String",
                    value: _id,
                },
            ];
            var rpName = "BB_GIAONHAN_YDCTTB" + jsonrpc.AjaxJson.getSystemDate("DDMMYY-HH24MISS") + "." + "xls";
            openReport("window", "BB_GIAONHAN_YDCTTB", "pdf", par);
        });
        
        $("#toolbarIdtbInPhieuHoanTraDTTH").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            var rowdata = $("#grdPhieu").jqGrid('getRowData', row);
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && rowdata.TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];
            if (PHARMA_KHONGIN_PHIEUTAOMOI == 1 && rowdata.TRANGTHAIID == '1') {
                return DlgUtil.showMsg("Không in được do phiếu đang ở trạng thái tạo mới.");
            }

            if (PHARMA_SHOW_INPHIEULINH_DUYETPHAT == '1') { // kiem tra neu thuoc da duyet phat thi ko cho in
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.KT.PTHUOC", _id);
                if (ret == 4) {
                    return DlgUtil.showMsg("Không in được do phiếu đã được duyệt phát thuốc.");
                }
            }

            if (PHARMA_IN_PHIEULINH_MANHOM == 1) {
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC_MANHOM_NXID', _id);
                if (data_ar != null && data_ar.length > 0) {
                    for (var k = 0; k < data_ar.length; k++) {
                        openReport('window', 'DUC_HOANTRAVATTUHAOPHIKHOAPHONG_' + that.opt.hospitalId + '_' + data_ar[k].MANHOM,
                            'pdf', par);
                    }
                } else {
                    return DlgUtil.showMsg("Không có dữ liệu!", undefined, 2000);
                }

            } else {
                openReport('window', 'DUC_HOANTRAVATTUHAOPHITHEOKHOAPHONG_A4',
                    'pdf', par);
            }


        });

        $("#toolbarIdtbInPhieuBaoHongDCCC").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);


            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'DUC_PHIEUBAOHONGMAT_DCCC_A4',
                'pdf', par);
        });

        $("#toolbarIdtbInPhieuTraThuocHaoPhi").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'DUC_PHIEUTRATHUOCHAOPHI',
                'pdf', par);
        });
        /*Begin: DucTT20181109*/
        $("#toolbarIdtbInLinhTVTBoSungCoSoTuTruc2Lien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var rowdata = $("#grdPhieu").jqGrid('getRowData', row);
            if (checkDKin == '2' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                if (rowdata.TRANGTHAIID == '5') {
                    return DlgUtil.showMsg("Phiếu chưa duyệt, không in được");
                } else if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4') {
                    return DlgUtil.showMsg("Đã nhập xuất trong kho, không in được");
                }
            }
            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'VATTU' || that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }
            if (PHARMA_SHOW_INPHIEULINH_DUYETPHAT == '1') { // kiem tra neu thuoc da duyet phat thi ko cho in
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.KT.PTHUOC", _id);
                if (ret == 4) {
                    return DlgUtil.showMsg("Không in được do phiếu đã được duyệt phát thuốc.");
                }
            }

            if ((kieu == 2 || kieu == 0) && that.opt.hospitalId == 26720) {
                DlgUtil.showMsg("Phiếu hoàn trả, không in được. Chọn phiếu dự trù!", undefined, 2000);
                return;
            }

            var parNhapXuatID = {name: 'nhapxuatid', type: 'String', value: _id};
            var str = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHIEUIN_TACH_LINHTHUOCVATTUBOSUNGCOSOTUTRUC');
            if (str == '0') {
                openReport('window', 'DUC_LINHTHUOCVATTUBOSUNGCOSOTUTRUC_A4', 'pdf', [parNhapXuatID]);
                return;
            }
            var parLoaiID = {name: 'loaiid', type: 'String', value: null};
            var loaiIdArray = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO('BDHCM.000001', [{
                "name": "[0]",
                "value": _id
            }]));
            var loaiId;
            var parAlls;
            for (var i in loaiIdArray) {
                loaiId = loaiIdArray[i].LOAIID;
                parLoaiID.value = loaiId;
                parAlls = [parNhapXuatID, parLoaiID];
                openReport('window', "DUC_LINHTHUOCVATTUBOSUNGCOSOTUTRUC_A4", "pdf", parAlls);
            }
            /*End: DucTT20181109*/
        });
        $("#toolbarIdtbInHoanTraCoSoTuTruc2Lien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            var rowdata = $("#grdPhieu").jqGrid('getRowData', row);
            if (checkDKin == '2' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                if (rowdata.TRANGTHAIID == '5') {
                    return DlgUtil.showMsg("Phiếu chưa duyệt, không in được");
                } else if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4') {
                    return DlgUtil.showMsg("Đã nhập xuất trong kho, không in được");
                }
            }

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.ht == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            if ((kieu == 3 || kieu == 1) && that.opt.hospitalId == 26720) {
                DlgUtil.showMsg("Phiếu lĩnh, không in được. Chọn phiếu hoàn trả!", undefined, 2000);
                return;
            }

            if (PHARMA_SHOW_INPHIEULINH_DUYETPHAT == '1') { // kiem tra neu thuoc da duyet phat thi ko cho in
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.KT.PTHUOC", _id);
                if (ret == 4) {
                    return DlgUtil.showMsg("Không in được do phiếu đã được duyệt phát thuốc.");
                }
            }

            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            if (that.opt.hospitalId == 965) {
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.KT.HTGN01", _id);
                if (ret > 0) {
                    openReport('window', 'DUC_PHIEUHOANTRACOSOTUTRUC_GTHN_A4_965', 'pdf', par);
                }

            }
            openReport('window', 'DUC_PHIEUHOANTRACOSOTUTRUC_A4', 'pdf', par);
        });
        // 20180906
        $("#toolbarIdtbInNhapXuatThuocTuNhaThuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];
            openReport('window', 'DUC_NHAPXUATTHUOCTUNHATHUOC_BDHCM', 'pdf', par);
        });
        // 20180927
        $("#toolbarIdtbInBienBanTraThuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var parNhapXuatObj =
                {
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                };

            var par2 = [{name: "[0]", value: _id}];
            var jsonLoaiTVT = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO("DUC_LOAITVT.05", par2));
            if (jsonLoaiTVT.length <= 0)
                return;
            var parLoai = [];
            var thuocThuongArr = [];
            for (var i in jsonLoaiTVT) {
                if (jsonLoaiTVT[i].LOAI == "6")
                    parLoai.push("6");
                else
                    thuocThuongArr.push(jsonLoaiTVT[i].LOAI);
            }
            if (thuocThuongArr.length > 0) {
                parLoai.push(thuocThuongArr.join());
            }
            for (var i in parLoai) {
                var parLoaiObj = {
                    name: 'loaitvt',
                    type: 'String',
                    value: parLoai[i]
                };
                var params = [parNhapXuatObj, parLoaiObj];
                openReport('window', 'DUC_BIENBANTRATHUOC_TAMTHAN', 'pdf', params);
            }
        });
        // 20181008
        $("#toolbarIdtbInXuatThuocTraKhoChinh").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];
            openReport('window', 'DUC_XUATTHUOCTRAKHOCHINH_BM2', 'pdf', par);
        });
        //ChuanNT them Biên bản kiểm nhập thuốc,vật tư, hóa chất hoàn trả của tam than KHA
        $("#toolbarIdtbInBienBanKiemNhapHoanTra").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var nhapXuatID = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (nhapXuatID == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var parNhapXuatID = {name: 'nhapxuatid', type: 'String', value: nhapXuatID};
            var parLoaiID = {name: 'loaiid', type: 'String', value: null};
            var loaiIdArray = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO('KHA.000002', [{
                "name": "[0]",
                "value": nhapXuatID
            }]));
            var loaiId;
            var parAlls;
            for (var i in loaiIdArray) {
                loaiId = loaiIdArray[i].LOAIID;
                parLoaiID.value = loaiId;
                parAlls = [parNhapXuatID, parLoaiID];
                openReport('window', "DUC_KIEMNHAPTHUOCVATTUHOANTRA", "pdf", parAlls);
            }
        });
        //L2PT-130273
        $("#toolbarIdtbInBienBanKiemNhapVacXin").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var nhapXuatID = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (nhapXuatID == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var parNhapXuatID = {name: 'nhapxuatid', type: 'String', value: nhapXuatID};
            var parLoaiID = {name: 'loaiid', type: 'String', value: null};
            var loaiIdArray = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO('KHA.000002', [{
                "name": "[0]",
                "value": nhapXuatID
            }]));
            var loaiId;
            var parAlls;
            for (var i in loaiIdArray) {
                loaiId = loaiIdArray[i].LOAIID;
                parLoaiID.value = loaiId;
                parAlls = [parNhapXuatID, parLoaiID];
                openReport('window', "BIENBANKIEMNHAP_VACXIN", "pdf", parAlls);
            }
        });

        // ********
        $("#toolbarIdtbInLinhHoaChatKhoaPhong").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var rowdata = $("#grdPhieu").jqGrid('getRowData', row);
            if (checkDKin == '2' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                if (rowdata.TRANGTHAIID == '5') {
                    return DlgUtil.showMsg("Phiếu chưa duyệt, không in được");
                } else if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4') {
                    return DlgUtil.showMsg("Đã nhập xuất trong kho, không in được");
                }
            }
            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            var par = [
                {
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }];
            //openReport('window', 'DUC_XUATTHUOCTRAKHOCHINH_BM2','pdf', par);
            //var rpName= "DUC_LINHHAOPHITHEOKHOAPHONG_A4_957_NHIHDG" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xls';
            //CommonUtil.inPhieu('window', "DUC_XUATTHUOCTRAKHOCHINH_BM2", 'pdf', par, rpName);
            var reportCode = 'DUC_LINHHOACHATKHOAPHONG';
            rpName = reportCode + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
            openReport('window', reportCode, 'pdf', par);
        });
        // ********
        $("#toolbarIdtbInPhieuYDungCuKhoaPhong").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;

            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            var rowdata = $("#grdPhieu").jqGrid('getRowData', row);
            if (checkDKin == '2' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                if (rowdata.TRANGTHAIID == '5') {
                    return DlgUtil.showMsg("Phiếu chưa duyệt, không in được");
                } else if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4') {
                    return DlgUtil.showMsg("Đã nhập xuất trong kho, không in được");
                }
            }

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];
            //openReport('window', 'DUC_XUATTHUOCTRAKHOCHINH_BM2','pdf', par);
            var rpName = "DUC_LINHYDUNGCUTHEOKHOAPHONG_A4_957_NHIHDG" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
            //CommonUtil.inPhieu('window', "DUC_XUATTHUOCTRAKHOCHINH_BM2", 'pdf', par, rpName);
            openReport('window', 'DUC_LINHYDUNGCUTHEOKHOAPHONG_A4_957_NHIHDG', 'pdf', par);
        });

        $("#toolbarIdtbBBNhan_TVT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;

            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);


            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];
            var rpName = "DUC_BIENBAN_NHAN_TVT" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
            CommonUtil.inPhieu('window', "DUC_BIENBAN_NHAN_TVT", 'xlsx', par, rpName);
            openReport('window', 'DUC_BIENBAN_NHAN_TVT', 'pdf', par);
        });

        $("#toolbarIdtbInTHPhieuLinhHDG").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var myVar = {nhapxuatid: _id, hinhthucid: that.opt.ht, loaiphieu: that.opt.type};
            dlgPopup = DlgUtil.buildPopupUrl("dlgInTHPhieuLinh", "divDlg", "manager.jsp?func=../duoc/DUC_DanhSachTHPhieuIn", myVar, "Danh sách Tổng hợp phiếu lĩnh HDG", 350, 400);
            dlgPopup.open("dlgInTHPhieuLinh");
        });


        $("#toolbarIdtbInPhieuLinh").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var rowdata = $("#grdPhieu").jqGrid('getRowData', row)
            var _id = rowdata.NHAPXUATID;
            var _par_INPHIEU = ['PHARMA_KHOA_INPHIEU_DAPHAT'];
            var PHARMA_KHOA_INPHIEU_DAPHAT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
                _par_INPHIEU.join('$'));
            if (that.opt.ht == '9' && PHARMA_KHOA_INPHIEU_DAPHAT == 1) {
                var par = [];
                par.push({"name": "[0]", "value": _id});
                var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC01S002.PHATTHUOC', par);
                var phieunk = JSON.parse(data_ar);
                var trangthainhap = phieunk[0].TRANGTHAINHAP;
                var trangthaiphat = phieunk[0].TRANGTHAIPHAT;
                if (trangthainhap == '4' || trangthaiphat == '4') {
                    DlgUtil.showMsg('Không in được do phiếu đã phát!');
                    return;
                }
            }

            if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4' && checkDKin == '1') {
                return DlgUtil.showMsg("Đã tạo phiếu xuất, không in được phiếu!");
            }

            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            if (checkDKin == '2' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                if (rowdata.TRANGTHAIID == '5') {
                    return DlgUtil.showMsg("Phiếu chưa duyệt, không in được");
                } else if (rowdata.TRANGTHAIID == '6' && rowdata.TTPHIEUNX == '4') {
                    return DlgUtil.showMsg("Đã nhập xuất trong kho, không in được");
                }
            }

            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && rowdata.TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'VATTU' || that.opt.gd == 'XUATDTTHKP' || that.opt.ht == '9')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            if (PHARMA_IN_PHIEUYC_NOITRU == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && that.opt.ht == '12' && (that.opt.gd == 'THUOC' || that.opt.gd == 'VATTU')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }
            if (PHARMA_SHOW_INPHIEULINH_DUYETPHAT == '1') { // kiem tra neu thuoc da duyet phat thi ko cho in
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.KT.PTHUOC", _id);
                if (ret == 4) {
                    return DlgUtil.showMsg("Không in được do phiếu đã được duyệt phát thuốc.");
                }
            }
            EventUtil.setEvent("print_success", function (e) {
                DlgUtil.close("dlgInPhieuLinh");
                if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
            });

            var _1tab = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_INPHIEULINH_1TAB');
            var kieustr = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC.000005', [{"name": "[0]", "value": _id}]);
            var kieujson = JSON.parse(kieustr);
            if (_1tab == "1" && kieujson[0].KIEU == "3") {
                var ret;
                var dsmoi = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", "PHARMA_LAY_DSPHIEUIN_MOI");
                if (dsmoi == "1") {
                    ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D044.15.NEW", _id);
                } else {
                    ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D044.15", _id);
                }
                _param = [
                    {
                        name: "nhapxuatid",
                        type: "String",
                        value: _id,
                    },
                    {
                        name: "nhomreportcode",
                        type: "String",
                        value: ret,
                    },
                ];
                var _reportCode = "NTU_IN_TONGHOP_PHIEULINH_ALL";
                CommonUtil.openReportGet("window", _reportCode, "pdf", _param);
            } else if (_1tab == "2" && kieujson[0].KIEU == "3" && that.opt.hospitalId == 41260) {
                var ret;
                var dsmoi = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", "PHARMA_LAY_DSPHIEUIN_MOI");
                if (dsmoi == "1") {
                    ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D044.15.NEW", _id);
                } else {
                    ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D044.15", _id);
                }
                var _param = [{
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }];
                if (ret != null && ret != "") {
                    var _freport = ret.split(";");
                    for (var i = 0; i < _freport.length; i++) {
                        if (_freport[i].trim().indexOf("NTU_IN_TONGHOP_PHIEULINH_ALL") >= 0) {
                            CommonUtil.openReportGet("window", _freport[i].trim(), "pdf", _param);
                        } else if (_freport[i].trim().indexOf("LINHTHUOCTPGAYNGHIENHUONGTHAN") >= 0) {
                            CommonUtil.openReportGet("window", _freport[i].trim(), "pdf", _param);
                        }
                    }
                }
            } else {
                var myVar = {nhapxuatid: _id, hinhthucid: that.opt.ht, loaiphieu: that.opt.type};
                dlgPopup = DlgUtil.buildPopupUrl("dlgInPhieuLinh", "divDlg", "manager.jsp?func=../duoc/DUC_DanhSachPhieuIn", myVar, "Danh sách phiếu lĩnh", 350, 400);
                dlgPopup.open("dlgInPhieuLinh");
            }
        });

        $("#toolbarIdtbInPhieuLinhHoaChat").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'NTU005_PHIEULINHHOACHAT_2LIEN_A4',
                'pdf', par);
        });


        $("#toolbarIdtbInPLinhThuocNGT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            if (PHARMA_IN_PHIEULINH_HOANTRA == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID != '6' && (that.opt.ht == '4' || that.opt.ht == '9') && (that.opt.gd == 'THUOC' || that.opt.gd == 'XUATDTTHKP')) {
                return DlgUtil.showMsg("Không in được do phiếu chưa được duyệt.");
            }

            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'DUC004_PHIEUCAPTHUOCNGOAITRU',
                'pdf', par);
        });

        // in don thuoc ngoai tru
        $("#toolbarIdtbInDonThuocNGT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSMBP.02", _id);

            var par = [
                {
                    name: 'maubenhphamid',
                    type: 'String',
                    value: ret
                }];

//			openReport('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5',
//					'pdf', par);
            var _loaithuoc = 0;
            var _par_loai = [ret];
            var arr_loaithuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC", _par_loai.join('$'));
            if (arr_loaithuoc != null && arr_loaithuoc.length > 0) { // in don thuoc thuong
                for (var i = 0; i < arr_loaithuoc.length; i++) {
                    _loaithuoc = arr_loaithuoc[i].LOAI;
                    if (_loaithuoc == 3) {
                        //thuoc dong y --DONTHUOCTHANG_NGOAITRU
                        CommonUtil.openReportGet('window', "NGT020_DONTHUOCTHANGNGOAITRU", "pdf", par);
                    } else if (_loaithuoc == 6) {
                        //thuoc huong than
                        CommonUtil.openReportGet('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par);
                    } else if (_loaithuoc == 7) {
                        //don thuoc gay nghien
                        CommonUtil.openReportGet('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par);
                    }
                    //tuyennx_add_start L2PT-13903
                    else if (_loaithuoc == 16 || _loaithuoc == 19) {
                        if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'IN_TACHDON_MP_TPCN') == 1) { //L2PT-30631
                            if (_loaithuoc == 16) {
                                CommonUtil.openReportGet('window', 'NGT013_MYPHAM_TT052016_A5', "pdf", par);
                            } else {
                                CommonUtil.openReportGet('window', 'NGT013_TPCN_TT052016_A5', "pdf", par);
                            }
                        } else
                            //don my pham
                            CommonUtil.openReportGet('window', "NGT013_DONMYPHAM_TPCN_TT052016_A5", "pdf", par);
                    }
                        //tuyennx_add_end L2PT-13903
                    //tuyennx_add_start L2PT-21559
                    else if (_loaithuoc == -1) {
                        //don my pham
                        CommonUtil.openReportGet('window', 'NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5', "pdf", par);

                    }
                    //tuyennx_add_end L2PT-21559
                    else {

                        var dtmuangoai = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "DT_MUANGOAI");
                        // don thuoc khac
                        if (dtmuangoai == 1) {
                            var _khothuoc = 0;
                            var arr_khothuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC", _par_loai.join('$'));
                            if (arr_khothuoc != null && arr_khothuoc.length > 0) {
                                for (var i2 = 0; i2 < arr_khothuoc.length; i2++) {
                                    _khothuoc = arr_khothuoc[i2].KHOTHUOCID;
                                    if (_khothuoc == 1) {
                                        CommonUtil.openReportGet('window', "NGT006_DONTHUOC1L_17DBV01_TT052016_A5_944", "pdf", par);
                                    } else CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
                                }
                            }
                        } else
                            CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
                    }
                }

            }
        });

        $("#toolbarIdtbInPhieuLinhThuocDYNTU").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            var sql_par = _id + "$";

            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01S001.DY.NTU01', sql_par);
            if (data_ar.length <= 0) {
                return DlgUtil.showMsg("Phiếu lĩnh không có thang thuốc đông y.", undefined, 2000);
            }
            if (PHARMA_IN_CHUNG_DONGY_NOITRU == 0) {
                for (var i in data_ar) {
                    var _par = {
                        name: 'nhapxuatid',
                        type: 'String',
                        value: data_ar[i].NHAPXUATID
                    };
                    var params = [_par];
                    openReport('window', 'NTU004_PHIEULINHTHANGTHUOCDONGY_01DBV01_TT23_A4', 'pdf', params);
                }
            } else if (PHARMA_IN_CHUNG_DONGY_NOITRU == '1174') {
                var par = [
                    {
                        name: 'nhapxuatid',//nhapxuatid
                        type: 'String',
                        value: _id
                    }];

                openReport('window', 'DUC_DONTHUOCDONGY_NOITRU_1174',
                    'pdf', par);
            } else {
                var par = [
                    {
                        name: 'nhapxuatid',//nhapxuatid
                        type: 'String',
                        value: _id
                    }];

                openReport('window', 'DUC_DONTHUOCDONGY_NOITRU_987',
                    'pdf', par);
            }

        });

        $("#toolbarIdtbInPhieuLinhThuocDYHAOPHINTU").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            var sql_par = _id + "$";

            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01S001.DY.NTU01', sql_par);
            if (data_ar.length <= 0) {
                return DlgUtil.showMsg("Phiếu lĩnh không có thang thuốc đông y.", undefined, 2000);
            }
            if (PHARMA_IN_CHUNG_DONGY_NOITRU == 0) {
                for (var i in data_ar) {
                    var _par = {
                        name: 'nhapxuatid',
                        type: 'String',
                        value: data_ar[i].NHAPXUATID
                    };
                    var params = [_par];
                    openReport('window', 'NTU004_PHIEULINHTHANGTHUOCDONGY_01DBV01_TT23_A4', 'pdf', params);
                }
            } else {
                var par = [
                    {
                        name: 'nhapxuatid',//nhapxuatid
                        type: 'String',
                        value: _id
                    }];

                openReport('window', 'DUC_DONTHUOCDONGY_HAOPHI_NOITRU_987',
                    'pdf', par);
            }

        });

        $("#toolbarIdtbInPhieuLinhThuocGiaTriCao").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            if (kieu != 3 && kieu != 0) {
                DlgUtil.showMsg("Chọn phiếu dự trù!", undefined, 2000);
                return;
            }
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'NTU004_PHIEULINHTHUOC_GIATRICAO_993',
                'pdf', par);
        });

        $("#toolbarIdtbPhieuLinhNTCT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            var ngaynx = $("#grdPhieu").jqGrid('getRowData', row).NGAYNX;
            if (kieu != 3 && kieu != 0) {
                DlgUtil.showMsg("Chọn phiếu phiếu!", undefined, 2000);
                return;
            }
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);

            var par = [
                {
                    name: 'i_sophieu',
                    type: 'String',
                    value: maphieu
                },
                {
                    name: 'i_ngaylapphieu',
                    type: 'String',
                    value: ngaynx
                },
                {
                    name: 'i_nhapxuatid',
                    type: 'String',
                    value: _id
                }
            ];

            CommonUtil.inPhieu('window', 'NTU_QLLT_PLCT',
                'XLS', par, "PhieuChiTiet.XLS");
//			  CommonUtil.inPhieu('window', _report_name, typeIn, _param,_report_name + "." + typeIn);
        });

        $("#toolbarIdtbInPhieuLinhThuocTongHop").on("click", function (e) {
            var listNhapxuat = '';
            var rowIds = $('#grdPhieu').jqGrid("getGridParam", "selarrrow");
            for (i = 0; i < rowIds.length; i++) {
                rowData = $('#grdPhieu').jqGrid('getRowData', rowIds[i]);
                listNhapxuat = (listNhapxuat == '' ? listNhapxuat : (listNhapxuat + ',')) + rowData.NHAPXUATID;
            }
            if (listNhapxuat == '') return;
            var par = [
                {
                    name: 'nhapxuatid',
                    type: 'String',
                    value: listNhapxuat,
                },
            ];
            openReport('window', 'DUC_PHIEULINHTHUOC_TONGHOP', 'pdf', par);
        });

        $("#toolbarIdtbInPhieuLinhMau").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            if (kieu != 3 && kieu != 0) {
                DlgUtil.showMsg("Chọn phiếu dự trù!", undefined, 2000);
                return;
            }
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'NTU031_PHIEULINHVAPHATMAU_15BV01_QD4069_A4_1111',
                'pdf', par);
        });

        $("#toolbarIdtbInPLMauNgoaiVien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'DUC_PHIEULINHMAU_NGOAIVIEN',
                'pdf', par);
        });

        $("#toolbarIdtbInPhieuCungCapMauPL9").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'NTU031_PHIEULINHVAPHATMAU_15BV01_QD4069_A4_23565',
                'pdf', par);
        });

        $("#toolbarIdtbInLinhTuiMau").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            //if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'NTU067_PHIEULINHTUIMAU_01DBV01_TT23_A4_987',
                'pdf', par);
        });

        $("#toolbarIdtbInPhieuVatTu2Lien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
            if (kieu != 3 && kieu != 0) {
                DlgUtil.showMsg("Chọn phiếu dự trù!", undefined, 2000);
                return;
            }
            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                }];

            openReport('window', 'NTU067_PHIEULINHVATTU_01DBV01_TT23_A4',
                'pdf', par);
        });


        // chuannt them phieu no thuoc 2 lien
        $("#toolbarIdtbInPhieuNoThuoc2Lien").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (_id == "")
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [
                {
                    name: 'nhapxuatid',//nhapxuatid
                    type: 'String',
                    value: _id
                },
                {
                    name: 'thuocvattu',//nhapxuatid
                    type: 'String',
                    value: 'THUỐC/VẬT TƯ'
                }];

            openReport('window', 'DUC_PHIEUNO_THUOCVATTU_A4',
                'pdf', par);
        });

        $("#toolbarIdtbInPhieuYCDieuChuyen").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "DUC_PHIEUDIEUCHUYENTHUOC_10284", "pdf", par);
        });

        $("#toolbarIdtbInPhieuLinhDieuChuyen").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "DUC_PHIEULINHDIEUCHUYENTHUOC_10284", "pdf", par);
        });

        $("#toolbarIdtbInPhieuXuatDCTHUOC").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "DUC_PHIEUXUATCHUYENTHUOC", "pdf", par);
        });

        // chuannt them phieu no vat tu 2 lien
        /*$("#toolbarIdtbInPhieuNoVT2Lien").on("click",function(e) {
			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

			if (_id == "")
				return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
			var par = [
				{
				name : 'nhapxuatid',//nhapxuatid
				type : 'String',
				value : _id
			},
			{
				name : 'thuocvattu',//nhapxuatid
				type : 'String',
				value : 'VẬT TƯ'
			}];

			openReport('window','DUC_PHIEUNO_THUOCVATTU_A4',
					'pdf', par);
		});*/

        /*$("#toolbarIdtbInPhieu2Lien").on("click",function(e) {
			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
			var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
			var maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;
			//if(kieu!=3 && kieu!=0){DlgUtil.showMsg("Chọn phiếu dự trù!",undefined,2000); return;}
			if (_id == "")
				return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
			var par = [
				{
				name : 'nhapxuatid',//nhapxuatid
				type : 'String',
				value : _id
			}];

			openReport('window', 'NTU004_PHIEULINHTHUOC_01DBV01_TT23_A4',
					'pdf', par);
		});*/

        $("#toolbarIdprint_4").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC007.01', _id);
            if (data_ar != null && data_ar.length > 0) {
                var row = data_ar[0];
                var par = [{
                    name: 'soyte',
                    type: 'String',
                    value: row.SO_Y_TE
                },
                    {
                        name: 'tenbenhvien',
                        type: 'String',
                        value: row.BENH_VIEN
                    },
                    {
                        name: 'khoa',
                        type: 'String',
                        value: row.KHOA
                    },
                    {
                        name: 'so',
                        type: 'String',
                        value: row.MA_PHIEU
                    },
                    {
                        name: 'chutichhd',
                        type: 'String',
                        value: row.CHUTICHHD
                    },
                    {
                        name: 'thuki',
                        type: 'String',
                        value: row.THUKI
                    },
                    {
                        name: 'truongphongtckt',
                        type: 'String',
                        value: row.TRUONGPHONGTCKT
                    },
                    {
                        name: 'truongkhoaduoc',
                        type: 'String',
                        value: row.TRUONGKHOADUOC
                    },
                    {
                        name: 'thongkeduoc',
                        type: 'String',
                        value: row.THONGKEDUOC
                    }
                    ,
                    {
                        name: 'ykiendexuat',
                        type: 'String',
                        value: row.YKIENDEXUAT
                    }
                    ,
                    {
                        name: 'giohop',
                        type: 'String',
                        value: row.GIOHOP
                    }
                    ,
                    {
                        name: 'phuthop',
                        type: 'String',
                        value: row.PHUTHOP
                    }
                    ,
                    {
                        name: 'ngayhop',
                        type: 'String',
                        value: row.NGAYHOP
                    }
                    ,
                    {
                        name: 'giohopden',
                        type: 'String',
                        value: row.GIOHOPDEN
                    }
                    ,
                    {
                        name: 'phuthopden',
                        type: 'String',
                        value: row.PHUTHOPDEN
                    }
                    ,
                    {
                        name: 'ngayhopden',
                        type: 'String',
                        value: row.NGAYHOPDEN
                    }
                    ,
                    {
                        name: 'nhapxuatid',
                        type: 'String',
                        value: _id
                    }


                ];

                openReport('window', 'DUC007_BBTHANHLYTHUOCHOACHATVATTUYTTIEUHAO_15DBV01_TT22_A4', 'pdf', par);
                var rpName = "DUC007_BBTHANHLYTHUOCHOACHATVATTUYTTIEUHAO_15DBV01_TT22_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                CommonUtil.inPhieu('window', "DUC007_BBTHANHLYTHUOCHOACHATVATTUYTTIEUHAO_15DBV01_TT22_A4", 'xls', par, rpName);

            } else {
                DlgUtil.showMsg('Phiếu chưa có xuất hủy thuốc');
            }


        });

        $("#toolbarIdprint_4_1").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC007.01', _id);
            if (data_ar != null && data_ar.length > 0) {
                var row = data_ar[0];
                var par = [{
                    name: 'soyte',
                    type: 'String',
                    value: row.SO_Y_TE
                },
                    {
                        name: 'tenbenhvien',
                        type: 'String',
                        value: row.BENH_VIEN
                    },
                    {
                        name: 'khoa',
                        type: 'String',
                        value: row.KHOA
                    },
                    {
                        name: 'so',
                        type: 'String',
                        value: row.MA_PHIEU
                    },
                    {
                        name: 'chutichhd',
                        type: 'String',
                        value: row.CHUTICHHD
                    },
                    {
                        name: 'thuki',
                        type: 'String',
                        value: row.THUKI
                    },
                    {
                        name: 'truongphongtckt',
                        type: 'String',
                        value: row.TRUONGPHONGTCKT
                    },
                    {
                        name: 'truongkhoaduoc',
                        type: 'String',
                        value: row.TRUONGKHOADUOC
                    },
                    {
                        name: 'thongkeduoc',
                        type: 'String',
                        value: row.THONGKEDUOC
                    }
                    ,
                    {
                        name: 'ykiendexuat',
                        type: 'String',
                        value: row.YKIENDEXUAT
                    }
                    ,
                    {
                        name: 'giohop',
                        type: 'String',
                        value: row.GIOHOP
                    }
                    ,
                    {
                        name: 'phuthop',
                        type: 'String',
                        value: row.PHUTHOP
                    }
                    ,
                    {
                        name: 'ngayhop',
                        type: 'String',
                        value: row.NGAYHOP
                    }
                    ,
                    {
                        name: 'giohopden',
                        type: 'String',
                        value: row.GIOHOPDEN
                    }
                    ,
                    {
                        name: 'phuthopden',
                        type: 'String',
                        value: row.PHUTHOPDEN
                    }
                    ,
                    {
                        name: 'ngayhopden',
                        type: 'String',
                        value: row.NGAYHOPDEN
                    }
                    ,
                    {
                        name: 'nhapxuatid',
                        type: 'String',
                        value: _id
                    }


                ];

                //openReport('window', 'DUC007_BBTHANHLYTHUOCHOACHATVATTUYTTIEUHAO_15DBV01_TT22_A4', 'pdf', par);
                var rpName = "DUC007_BBTHANHLYTHUOCHOACHATVATTUYTTIEUHAO_15DBV01_TT22_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xls';
                CommonUtil.inPhieu('window', "DUC007_BBTHANHLYTHUOCHOACHATVATTUYTTIEUHAO_15DBV01_TT22_A4", 'xls', par, rpName);

            } else {
                DlgUtil.showMsg('Phiếu chưa có xuất hủy thuốc');
            }


        });


        $("#toolbarIdprint_5").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC013.01', _id);
            var row = data_ar[0];
            if (row.NHAPXUATID == '0') {
                DlgUtil.showMsg('Phiếu chưa có xuất hủy thuốc');
            } else {

                //
                var par = [{
                    name: 'soyte',
                    type: 'String',
                    value: row.SO_Y_TE
                },
                    {
                        name: 'tenbenhvien',
                        type: 'String',
                        value: row.BENH_VIEN
                    },
                    {
                        name: 'khoa',
                        type: 'String',
                        value: row.KHOA
                    },
                    {
                        name: 'so',
                        type: 'String',
                        value: row.MA_PHIEU
                    }
                    ,
                    {
                        name: 'giobb',
                        type: 'String',
                        value: row.GIOBB
                    }
                    ,
                    {
                        name: 'phutbb',
                        type: 'String',
                        value: row.PHUTBB
                    }
                    ,
                    {
                        name: 'ngaybb',
                        type: 'String',
                        value: row.NGAYBB
                    }
                    ,
                    {
                        name: 'chutichhd',
                        type: 'String',
                        value: row.CHUTICHHD
                    }
                    ,
                    {
                        name: 'cdchutichhd',
                        type: 'String',
                        value: row.CDCHUTICHHD
                    }
                    ,
                    {
                        name: 'thuki',
                        type: 'String',
                        value: row.THUKI
                    }
                    ,
                    {
                        name: 'cdthuki',
                        type: 'String',
                        value: row.CDTHUKI
                    },
                    {
                        name: 'truongphongtckt',
                        type: 'String',
                        value: row.TRUONGPHONGTCKT
                    }
                    ,
                    {
                        name: 'cdtruongphongtckt',
                        type: 'String',
                        value: row.CDTRUONGPHONGTCKT
                    },
                    {
                        name: 'truongphongtckt',
                        type: 'String',
                        value: row.TRUONGPHONGTCKT
                    },
                    {
                        name: 'truongkhoaduoc',
                        type: 'String',
                        value: row.TRUONGKHOADUOC
                    }
                    ,
                    {
                        name: 'cdtruongkhoaduoc',
                        type: 'String',
                        value: row.CDTRUONGKHOADUOC
                    },
                    {
                        name: 'thongkeduoc',
                        type: 'String',
                        value: row.THONGKEDUOC
                    }
                    ,
                    {
                        name: 'cdthongkeduoc',
                        type: 'String',
                        value: row.CDTHONGKEDUOC
                    }
                    ,
                    {
                        name: 'ykiendexuat',
                        type: 'String',
                        value: row.YKIENDEXUAT
                    }
                    ,
                    {
                        name: 'taitro',
                        type: 'String',
                        value: row.TAITRO
                    }
                    ,
                    {
                        name: 'tinhtrang',
                        type: 'String',
                        value: row.TINHTRANG
                    }
                    ,
                    {
                        name: 'nhapxuatid',
                        type: 'String',
                        value: row.NHAPXUATID
                    }


                ];
                var rpName = "DUC013_BBXACNHANTHUOCHOACHATVTYTMATHONGVO_14DBV01_TT22_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                CommonUtil.inPhieu('window', "DUC013_BBXACNHANTHUOCHOACHATVTYTMATHONGVO_14DBV01_TT22_A4", 'xlsx', par, rpName);
                openReport('window', 'DUC013_BBXACNHANTHUOCHOACHATVTYTMATHONGVO_14DBV01_TT22_A4', 'pdf', par);
            }
        });

        $("#toolbarIdprint_6").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "DUC069_PHIEUTRATHUOC_A4", "pdf", par);
        });
        $("#toolbarIdprint_7").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            if (that.opt.hospitalId == 951) {
                openReport('window', "DUC071_PHIEUTRAVATTU_A4_951", "pdf", par);
            } else if (that.opt.hospitalId == 10284) {
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_DSXUATRA.15", _id);
                if (ret != null && ret != '') {
                    var _freport = ret.split(";");
                    for (var i = 0; i < _freport.length; i++) {
                        openReport('window', _freport[i].trim(), "pdf", par);
                    }
                }
            } else {
                openReport('window', "DUC071_PHIEUTRAVATTU_A4", "pdf", par);
            }
        });
        $("#toolbarIdprint_8").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');

            if (that.opt.ht == "15") {
                var _id = $("#grdPhieu").jqGrid('getRowData', row).XUATID;
                var par = [{
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }];
                openReport('window', 'PHIEU_BANTHUOC_KHACHLE', 'pdf', par);
            } else {
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                var par = [{
                    name: 'nhapxuatid',
                    type: 'String',
                    value: _id
                }];
                openReport('window', "DUC075_HOADONBANLE_A4", "pdf", par);
            }
        });
        $("#toolbarIdprint_11").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'i_nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "BC_BANGYLENH_THUOCVATTU_HANGNGAY_996", "pdf", par);
        });

        $("#toolbarIdtbInDonThuocYHCT").on("click", function (e) {
            var idx = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            if (!idx) {
                DlgUtil.showMsg("Bạn phải chọn phiếu để in!");
                return;
            }
            var data = $("#grdPhieu").jqGrid('getRowData', idx);
            var nhapxuatid = data.NHAPXUATID;

            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: nhapxuatid
            }];
            var rpName = "DUC_PHIEULINHTHUOCDONGY" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
            CommonUtil.inPhieu('window', "DUC_PHIEULINHTHUOCDONGY", 'xlsx', par, rpName);
            openReport('window', 'DUC_PHIEULINHTHUOCDONGY', 'pdf', par);

        });

        // phieu tra thuoc khach le
        $("#toolbarIdtbphieutrathuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "DUC004_PHIEUTRATHUOCKHACHLE", "pdf", par);
        });
        // phieu tra hang
        $("#toolbarIdtbInPhieuTraHang").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "DUC004_PHIEUTRAHANG", "pdf", par);
        });

        $("#toolbarIdtbInPhieuHoanTraDoVai").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "DUC_HOANTRAVATTUDOVAI_923", "pdf", par);
        });
        $("#toolbarIdtbInPhieuHoanLinhDoVai").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "DUC_HOANLINHVATTUDOVAI_923", "pdf", par);
        });

        $("#toolbarIdtbGoiBN").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var _hinhthuc = $("#grdPhieu").jqGrid('getRowData', row).HINHTHUCID;
            var par = _id + '$';
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC47T002.01", par);
            if (result == 1) DlgUtil.showMsg('Đang gọi bệnh nhân !');
            else DlgUtil.showMsg('Gọi bn lỗi !');

        });
        //------------------------------duyet yeu cau---------------------------------------------------
        //duyet
        $("#toolbarIdtbDuyet").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var _hinhthuc = $("#grdPhieu").jqGrid('getRowData', row).HINHTHUCID;

            // daidv kiem tra duyet chan voi phieu linh thuoc -- tam thoi bo
            /*if(PHARMA_DUYET_THUOC_CHAN == 1 && _hinhthuc == 12){
				var rowIds = $('#grdThuoc').jqGrid('getDataIDs');
				for (i = 0; i < rowIds.length; i++) {
					rowData = $('#grdThuoc').jqGrid('getRowData', rowIds[i]);
					if(parseFloat(rowData["SOLUONG"]) % 1 !== 0){
						DlgUtil.showMsg("Phiếu lĩnh đang có thuốc " + rowData["TEN"] + " duyệt lẻ");
						return;
					}
				}
			}*/

            EventUtil.setEvent("appr_success", function (e) {
                DlgUtil.close("dlgAppr");
                /*if(that.opt.ht.includes("13")){
					$('#grdPhieu').jqGrid('setCell',row,12,'Đã duyệt');
					$('#grdPhieu').jqGrid('setCell',row,17,e.nhapid);
					$('#grdPhieu').jqGrid('setCell',row,18,e.xuatid);
				}
				else{
					$('#grdPhieu').jqGrid('setCell',row,7,'Đã duyệt');
					$('#grdPhieu').jqGrid('setCell',row,12,e.nhapid);
					$('#grdPhieu').jqGrid('setCell',row,13,e.xuatid);
				}
				$('#grdPhieu').jqGrid('setCell',row,3,DADUYET);*/
                //_loadChiTiet(_id,DADUYET);
                //_formatRow(row,DADUYET-1);
                //$('#btnXemPhieu').click();
                //_loadDSPhieu();
                if (that.opt.lp == "3" && that.opt.ht == "9" && that.opt.gd == "THUOC" && PHARMA_DUYET_DLS_BOSUNG_TUTRUC == "1" && that.opt.td == "9") {
                    //if(that.opt.td == "9"){
                    _loadDSPhieuLamSan();
//					}else{
//						_loadDSPhieuLS_DaDuyet();
//					}
                } else if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
                //$("#grdPhieu").setSelection(row,true);
                //_loadDSPhieu();
                //alert("e.ttnhap = " + e.ttnhap);
                //alert("e.ttxuat = " + e.ttxuat);
                if ((_kieu == 2 && e.ttnhap != 4) || (_kieu == 3 && e.ttxuat != 4)) {
                    var _nhapxuatid = e.nhapid;
                    if (_kieu != 2) {
                        _nhapxuatid = e.xuatid;
                    }
                    EventUtil.setEvent("nhapkho_cancel2", function (e) {
                        DlgUtil.close("dlgTTPhieu");
                        if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                            _loadDSPhieuBUCSTT();
                        } else {
                            _loadDSPhieu();
                        }
                    });
                    if (PHARMA_HIENTHI_POPUP_TUDONG == '1') {
                        var myVar = {nhapxuatid: _nhapxuatid, nxid: _id, kieu: _kieu, edit: that.opt.cs};
                        dlgPopup = DlgUtil.buildPopupUrl("dlgTTPhieu", "divDlg", "manager.jsp?func=../duoc/DUC35T001_ThongTinPhieu", myVar, "Thông tin phiếu", 1200, 580);
                        DlgUtil.open("dlgTTPhieu");
                    }

                }
            });
            EventUtil.setEvent("appr_cancel", function (e) {
                DlgUtil.close("dlgAppr");
            });

            var myVar = {
                nhapxuatId: _id,
                kieu: _kieu,
                hinhthuc: _hinhthuc,
                ht: that.opt.ht,
                edit: that.opt.cs,
                gd: that.opt.gd,
                khoid: $("#cboKho").val()
            };
            if (that.opt.td == '1') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgAppr", "divDlg", "manager.jsp?func=../duoc/DUC75T001_XacNhanPhieuLinh", myVar, "Duyệt phiếu", 1100, 550);
                DlgUtil.open("dlgAppr");
            } else if (PHARMA_GIAODIEN_NHAP_NCC == 'DUC02N001_NhapThuocNCC_BVNT') {
                if (that.opt.ht != 17) {
                    dlgPopup = DlgUtil.buildPopupUrl("dlgAppr", "divDlg", "manager.jsp?func=../duoc/DUC47T001_NGT_YeuCauNhapThuoc", myVar, "Duyệt phiếu", 1100, 550);
                    DlgUtil.open("dlgAppr");
                } else {
                    dlgPopup = DlgUtil.buildPopupUrl("dlgAppr", "divDlg", "manager.jsp?func=../duoc/DUC47T001_YeuCauNhapThuoc", myVar, "Duyệt phiếu", 1100, 550);
                    DlgUtil.open("dlgAppr");
                }
            } else {
                if (that.opt.lp == "3" && that.opt.ht == "9" && that.opt.gd == "THUOC" && PHARMA_CHUYEN_DUYET_COSOHOATCHAT == "1") {
                    dlgPopup = DlgUtil.buildPopupUrl("dlgAppr", "divDlg", "manager.jsp?func=../duoc/DUC47T001_YeuCauNhapThuoc_DKLA", myVar, "Duyệt phiếu", 1100, 550);
                    DlgUtil.open("dlgAppr");
                } else {
                    dlgPopup = DlgUtil.buildPopupUrl("dlgAppr", "divDlg", "manager.jsp?func=../duoc/DUC47T001_YeuCauNhapThuoc", myVar, "Duyệt phiếu", 1100, 550);
                    DlgUtil.open("dlgAppr");
                }
            }

        });

        //bachnv duyet hoan tra
        $("#toolbarIdtbDuyetHoanTra").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            EventUtil.setEvent("appr_success", function (e) {
                DlgUtil.close("dlgAppr");
                $('#grdPhieu').jqGrid('setCell', row, 7, 'Đã duyệt');
                $('#grdPhieu').jqGrid('setCell', row, 3, DADUYET);
                _loadChiTiet(_id, DADUYET);
                _formatRow(row, DADUYET - 1);
            });
            var myVar = {nhapxuatId: _id};
            dlgPopup = DlgUtil.buildPopupUrl("dlgAppr", "divDlg", "manager.jsp?func=../duoc/DUC47T001_YeuCauNhapThuoc", myVar, "Duyệt phiếu", 1100, 550);
            DlgUtil.open("dlgAppr");
        });
        // end hoan tra
		
		//tuyendv L2PT-101604
		$("#toolbarIdprint_trakhomau").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [{
                name: 'nhapxuatid',
                type: 'String',
                value: _id
            }];
            openReport('window', "NTU007_PHIEUKIEMTRA_DONVIMAU", "pdf", par);
        });
		//end L2PT-101604
		
        //go duyet
        $("#btnGoDuyet").on("click", function (e) {
            if (confirm("Bạn có thực sự muốn gỡ duyệt?")) {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
                //var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC47T001.CANCEL",_id);
                if (PHARMA_CHECK_LIENTHONG_HMIS == '1' && _kieu == '3') {
                    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01.HMIS01', _id);
                    if (data_ar != null && data_ar.length > 0) {
                        var row = data_ar[0];
                        var _usr = row.URS;
                        var _pwd = row.PWD;
                        var _macsyt = row.MACSYT;
                        var ma_kcb_tuyen4 = row.MA_DK_KCB_TUYEN4;
                        var ma_kcb_tuyen3 = row.MA_DK_KCB_TUYEN3;
                        var ma_phieu = row.MA_PHIEU;
                        var ten_kho_yc = row.TEN_KHO_YC;
                        var ten_kho_duyet_yc = row.TEN_KHO_DUYET_YC;
                        var trangthai_gui = row.TRANG_THAI_GUI;
                        var loai_phieu = row.LOAI_PHIEU;
                        var start_date = row.NGAY_TAO_PHIEU;
                        var end_date = row.END_DATE;
                        var trangthai_nhan = row.TRANG_THAI_NHAN_DUOC;
                        var ngay_pheduyet = row.NGAY_PHE_DUYET;
                        var ten_nghiepvu = row.TEN_NGHIEP_VU;

                        var ret = ajaxSvc.HMIS_PHAMAR.getStatusPhieu(_usr, _pwd, _macsyt, ma_kcb_tuyen4, ma_kcb_tuyen3, ma_phieu,
                            ten_kho_yc, ten_kho_duyet_yc, trangthai_gui, loai_phieu, start_date
                            , end_date, trangthai_nhan, ngay_pheduyet, ten_nghiepvu);
                        try {
                            var ret1 = $.parseJSON(ret);
                            if (ret1.errorCode == 0) {
                                var ret2 = ret1.result;
                                if (ret2[0].TRANG_THAI_SD_DUOC != 0) {
                                    DlgUtil.showMsg("Phiếu liên thông đã được sử dụng. Không thể gỡ duyệt.");
                                    return;
                                }
                            }
                        } catch (err) {
                            DlgUtil.showMsg('Lỗi call API check trạng thái sử dụng HMIS.', function () {
                                return;
                            });
                        }

                    }
                }

                var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC47T001.CANCEL", _id));
                if (ret.SUCCESS > 0) {
                    DlgUtil.showMsg("Gỡ duyệt thành công", function () {
                        if (PHARMA_CHECK_LIENTHONG_HMIS == '1' && _kieu == '2') {
                            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01.HMIS01', _id);
                            if (data_ar != null && data_ar.length > 0) {
                                ajaxSvc.register("HMIS_PHAMAR");
                                var row = data_ar[0];
                                var _usr = row.URS;
                                var _pwd = row.PWD;
                                var _macsyt = row.MACSYT;
                                var ma_kcb_tuyen4 = row.MA_DK_KCB_TUYEN4;
                                var ma_kcb_tuyen3 = row.MA_DK_KCB_TUYEN3;
                                var ma_phieu = row.MA_PHIEU;
                                var ma_phieu_tuyen4 = row.MA_PHIEU_TUYEN4;
                                var loai_phieu = row.LOAI_PHIEU;
                                var ngay_pheduyet = row.NGAY_PHE_DUYET;
                                var trangthai_phieu = "7";
                                var dsthuoc = [];

                                var ret1 = ajaxSvc.HMIS_PHAMAR.updatePhieu(_usr, _pwd, _macsyt, ma_kcb_tuyen4,
                                    ma_kcb_tuyen3, ma_phieu, ma_phieu_tuyen4, loai_phieu, ngay_pheduyet, trangthai_phieu, dsthuoc);
                                try {
                                    var ret2 = $.parseJSON(ret1);
                                    if (ret2.errorCode != 0 || ret2.result != 0) {
                                        DlgUtil.showMsg('Lỗi update liên thông phiếu hoàn trả HMIS.', function () {

                                            var _par = [_id, '1'];
                                            var updateBN = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01.HMIS_UP01",
                                                _par.join('$'));
                                            //_loadDSPhieu();

                                        });
                                    } else {
                                        DlgUtil.showMsg('Update liên thông phiếu HMIS thành công.', function () {

                                            var _par = [_id, '2'];
                                            var updateBN = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01.HMIS_UP01",
                                                _par.join('$'));
                                            //_loadDSPhieu();

                                        });
                                    }
                                } catch (err) {
                                    DlgUtil.showMsg('Lỗi call API.Update liên thông phiếu hoàn trả HMIS thành công.', function () {

                                        var _par = [_id, '1'];
                                        var updateBN = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01.HMIS_UP01",
                                            _par.join('$'));
                                        //_loadDSPhieu();

                                    });
                                }
                            }
                        }
                    });
                    $('#grdPhieu').jqGrid('setCell', row, 7, 'Không duyệt');
                    $('#grdPhieu').jqGrid('setCell', row, 3, KHONGDUYET);
                    _loadChiTiet(_id, KHONGDUYET);

                    _formatRow(row, KHONGDUYET - 1);
                    _loadDSPhieu();
                } else {
                    DlgUtil.showMsg(ret.MESSAGE);
                }
                /*if(ret==1){
					DlgUtil.showMsg("Gỡ duyệt thành công");
					$('#grdPhieu').jqGrid('setCell',row,7,'Không duyệt');
					$('#grdPhieu').jqGrid('setCell',row,3,KHONGDUYET);
					_loadChiTiet(_id,KHONGDUYET);
					_formatRow(row,KHONGDUYET-1);
				}
				else{
					DlgUtil.showMsg("Gỡ duyệt không thành công");
				}*/
            }
        });

        $("#btnGuiDuyetHMIS").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var _trangthai = $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID;
            //var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC47T001.CANCEL",_id);
            if (PHARMA_CHECK_LIENTHONG_HMIS == '1') {
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01.HMIS01', _id);
                if (data_ar != null && data_ar.length > 0) {
                    ajaxSvc.register("HMIS_PHAMAR");
                    var row = data_ar[0];
                    var _usr = row.URS;
                    var _pwd = row.PWD;
                    var _macsyt = row.MACSYT;
                    var ma_kcb_tuyen4 = row.MA_DK_KCB_TUYEN4;
                    var ma_kcb_tuyen3 = row.MA_DK_KCB_TUYEN3;
                    var ma_phieu = row.MA_PHIEU;
                    var ma_phieu_tuyen4 = row.MA_PHIEU_TUYEN4;
                    var loai_phieu = row.LOAI_PHIEU;
                    var ngay_pheduyet = row.NGAY_PHE_DUYET;
                    var trangthai_phieu = "";
                    var dsthuoc = [];

                    if (_kieu == '3' && _trangthai == '6') {
                        trangthai_phieu = "6";
                        for (var i = 0; i < data_ar.length; i++) {
                            var tvtObj = new Object();
                            tvtObj.MA_DUOC_VT_THEO_TUYEN4 = data_ar[i].MA_DUOC_VT_THEO_TUYEN4;
                            tvtObj.MA_DUOC_VT_THEO_TUYEN3 = data_ar[i].MA_DUOC_VT_THEO_TUYEN3;
                            tvtObj.SO_LUONG_DUOC_DUYET = data_ar[i].SOLUONGDUYET;
                            tvtObj.SOLO = data_ar[i].SOLO;
                            tvtObj.HANSUDUNG = data_ar[i].HANSUDUNG;
                            tvtObj.XUATGIABAN = data_ar[i].XUATGIABAN;
                            tvtObj.GIABHYT = data_ar[i].GIABHYT;
                            tvtObj.GIANHANDAN = data_ar[i].GIANHANDAN;
                            tvtObj.GIADICHVU = data_ar[i].GIADICHVU;
                            tvtObj.GIATRANBHYT = data_ar[i].GIATRANBHYT;
                            tvtObj.GIANHAP = data_ar[i].GIANHAP;
                            dsthuoc.push(tvtObj);
                        }
                    } else if (_kieu == '3' && _trangthai == '7') {
                        trangthai_phieu = "7";
                    } else if (_kieu == '2' && _trangthai == '6') {
                        trangthai_phieu = "6";
                    } else if (_kieu == '2' && _trangthai == '7') {
                        trangthai_phieu = "7";
                    }

                    var ret1 = ajaxSvc.HMIS_PHAMAR.updatePhieu(_usr, _pwd, _macsyt, ma_kcb_tuyen4,
                        ma_kcb_tuyen3, ma_phieu, ma_phieu_tuyen4, loai_phieu, ngay_pheduyet, trangthai_phieu, dsthuoc);

                    try {
                        var ret2 = $.parseJSON(ret1);
                        if (ret2.errorCode != 0 || ret2.result != 0) {
                            DlgUtil.showMsg('Lỗi update liên thông phiếu HMIS.', function () {

                                var _par = [_id, '1'];
                                var updateBN = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01.HMIS_UP01",
                                    _par.join('$'));
                                _loadDSPhieu();

                            });
                        } else {
                            DlgUtil.showMsg('Update liên thông phiếu HMIS thành công.', function () {

                                var _par = [_id, '2'];
                                var updateBN = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01.HMIS_UP01",
                                    _par.join('$'));
                                _loadDSPhieu();

                            });
                        }
                    } catch (err) {
                        DlgUtil.showMsg('Lỗi call API.Update liên thông phiếu HMIS thành công.', function () {

                            var _par = [_id, '1'];
                            var updateBN = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01.HMIS_UP01",
                                _par.join('$'));
                            _loadDSPhieu();

                        });
                    }
                }
            }


        });
        
        $("#btnGuiFast").click(function(){
			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu cần tích hợp!", undefined, 2000);
            var reqestCode = '';
            var danhsach = [];
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O('DUC01.TH.FAST01', _id);
            if (data_ar != null && data_ar.length > 0) { 	
            	if(that.opt.ht == '1'){
            		reqestCode = 'S06';
            		for (var i = 0; i < data_ar.length; i++) {
                		var tvtObj = new Object();
                		tvtObj.YourId = data_ar[i].NHAPXUATID;
                		tvtObj.VcDate = data_ar[i].NGAYCHUNGTU;
                		if(data_ar[i].MA.length <= 12){
                			tvtObj.VcNo = data_ar[i].MA;
                		}else{
                			tvtObj.VcNo = data_ar[i].MA.substring(data_ar[i].MA.length,data_ar[i].MA.length - 12);
                		}
                		tvtObj.InvoiceDate = data_ar[i].NGAYCHUNGTU;
                		tvtObj.InvoiceNo = '';
                		tvtObj.SeriNo = '';
                		tvtObj.CusID = data_ar[i].NHACUNGCAPID;
                		tvtObj.Note = '';
                		tvtObj.TaxCode = '';
                		tvtObj.TaxRate = Number(data_ar[i].XUATVAT);
                		tvtObj.Status = '2';
                		tvtObj.ItemId = data_ar[i].THUOCVATTUID;
                		tvtObj.Uom = data_ar[i].TEN_DVT;
                		tvtObj.SiteID = data_ar[i].KHOID;
                		tvtObj.Quality = Number(data_ar[i].SOLUONG);
                		tvtObj.Price = Number(data_ar[i].XUATGIABAN);
                		danhsach.push(tvtObj);
                	}
            	}else if(that.opt.ht == '2' || that.opt.ht == '9'){
            		reqestCode = 'S10';
            		for (var i = 0; i < data_ar.length; i++) {
                		var tvtObj = new Object();
                		tvtObj.YourId = data_ar[i].NHAPXUATID;
                		tvtObj.VcDate = data_ar[i].NGAYNHAP;
                		if(data_ar[i].MA.length <= 12){
                			tvtObj.VcNo = data_ar[i].MA;
                		}else{
                			tvtObj.VcNo = data_ar[i].MA.substring(data_ar[i].MA.length,data_ar[i].MA.length - 12);
                		}
                		tvtObj.CusID = 'MPH0001';
                		tvtObj.Note = '';
                		tvtObj.Status = '3';
                		tvtObj.Buyer = '';
                		tvtObj.ItemId = data_ar[i].THUOCVATTUID;
                		tvtObj.SiteID = data_ar[i].KHOID;
                		tvtObj.ReasonCode = '';
                		tvtObj.Quality = Number(data_ar[i].SOLUONG);
                		tvtObj.Uom = data_ar[i].TEN_DVT;
                		tvtObj.Price = Number(data_ar[i].GIANHAP);
                		
                		danhsach.push(tvtObj);
                	}
            	}else if(that.opt.ht == '13' || that.opt.ht == '12'){
            		reqestCode = 'S11';
            		for (var i = 0; i < data_ar.length; i++) {
                		var tvtObj = new Object();
                		tvtObj.YourId = data_ar[i].NHAPXUATID;
                		tvtObj.VcDate = data_ar[i].NGAYNHAP;
                		if(data_ar[i].MA.length <= 12){
                			tvtObj.VcNo = data_ar[i].MA;
                		}else{
                			tvtObj.VcNo = data_ar[i].MA.substring(data_ar[i].MA.length,data_ar[i].MA.length - 12);
                		}
                		tvtObj.CusID = 'MPH0001';
                		tvtObj.Note = '';
                		tvtObj.Status = '3';
                		tvtObj.Buyer = '';
                		tvtObj.ItemId = data_ar[i].THUOCVATTUID;
                		tvtObj.SiteID = data_ar[i].KHOID;
                		tvtObj.ReasonCode = '';
                		tvtObj.Quality = Number(data_ar[i].SOLUONG);
                		tvtObj.Uom = data_ar[i].TEN_DVT;
                		tvtObj.Price = Number(data_ar[i].XUATGIABAN);
                		tvtObj.SpecificCost = true;
                		
                		danhsach.push(tvtObj);
                	}
            	}
            	
            }

        	try {
        		var strRetFastApi = ajaxSvc.HMIS_PHAMAR.callFastApi(reqestCode, danhsach,hCode,"<CHECKSUM>", "", 60, "", "", "");
        		console.log(strRetFastApi);
        		objRetFastApi = JSON.parse(strRetFastApi);
        		console.log(objRetFastApi);
        		var msg = reqestCode + ": " + objRetFastApi.message;
        		if (objRetFastApi.result && objRetFastApi.result.messages) {
        			msg = msg + ". Chi tiết lỗi: " + objRetFastApi.result.messages
        		}
        		DlgUtil.showMsg(msg);
        		return strRetFastApi;
        	} catch (err) {
        		DlgUtil.showMsg(reqestCode + ": " + err);
        		return err;
        	}
		});

        $("#btnGoTuChoi").on("click", function (e) {
            if (confirm("Bạn có thực sự muốn gỡ từ chối?")) {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                //var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC47T001.CANCEL",_id);
                var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC47T001.GOTUCHOI", _id));
                if (ret.SUCCESS > 0) {
                    DlgUtil.showMsg("Gỡ từ chối thành công");
                    $('#grdPhieu').jqGrid('setCell', row, 7, 'Chờ duyệt');
                    $('#grdPhieu').jqGrid('setCell', row, 3, KHONGDUYET);
                    _loadChiTiet(_id, KHONGDUYET);

                    _formatRow(row, KHONGDUYET - 1);
                    _loadDSPhieu();
                } else {
                    DlgUtil.showMsg(ret.MESSAGE);
                }
                /*if(ret==1){
					DlgUtil.showMsg("Gỡ duyệt thành công");
					$('#grdPhieu').jqGrid('setCell',row,7,'Không duyệt');
					$('#grdPhieu').jqGrid('setCell',row,3,KHONGDUYET);
					_loadChiTiet(_id,KHONGDUYET);
					_formatRow(row,KHONGDUYET-1);
				}
				else{
					DlgUtil.showMsg("Gỡ duyệt không thành công");
				}*/
            }
        });

        //Duyet phat thuoc
        $("#toolbarIdtbDuyetPhatThuoc").on("click", function (e) {

//			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
//			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC47T001.CANCEL",_id);

            if (dsHoaDon.length > 0) {
                var danhsach = '';
                for (var i = 0; i < dsHoaDon.length; i++) {
                    var rowIds = $('#grdPhieu').jqGrid('getDataIDs');
                    for (j = 0; j < rowIds.length; j++) {
                        var rowData = $('#grdPhieu').jqGrid('getRowData', rowIds[j]);
                        if (rowData.NHAPXUATID == dsHoaDon[i] && rowData.TTDUYETPHATTHUOC == '4') {
                            DlgUtil.showMsg("Danh sách duyệt phát có phiếu " + rowData.MA + " đã phát.");
                            return;
                        }
                    }
                    if (i < dsHoaDon.length - 1) {
                        danhsach += dsHoaDon[i];
                        danhsach += ",";
                    } else {
                        danhsach += dsHoaDon[i];
                    }
                }
                var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC14T001.APPROVAL", danhsach));
                if (ret.SUCCESS > 0) {
                    DlgUtil.showMsg(ret.MESSAGE);
//					$('#grdPhieu').jqGrid('setCell',row,7,'Không duyệt');
//					$('#grdPhieu').jqGrid('setCell',row,3,KHONGDUYET);
//						_loadChiTiet(_id,KHONGDUYET);

//					_formatRow(row,KHONGDUYET-1);
                    _loadDSPhieu();

                } else {
                    DlgUtil.showMsg(ret.MESSAGE);
                    dsHoaDon = [];
                }
            } else {
                DlgUtil.showMsg("Không có danh sách cần duyệt phát");
                return;
            }

            /*var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC14T001.APPROVAL",_id));
			if(ret.SUCCESS>0){
				DlgUtil.showMsg(ret.MESSAGE);
//				$('#grdPhieu').jqGrid('setCell',row,7,'Không duyệt');
//				$('#grdPhieu').jqGrid('setCell',row,3,KHONGDUYET);
//					_loadChiTiet(_id,KHONGDUYET);

//				_formatRow(row,KHONGDUYET-1);
				_loadDSPhieu();
			}
			else{
				DlgUtil.showMsg(ret.MESSAGE);
			}*/

        });
        //Go duyet phat thuoc
        $("#toolbarIdtbGoDuyetPhatThuoc").on("click", function (e) {

            /*var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
			//var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC47T001.CANCEL",_id);
			var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC14T001.CANCEL",_id));
			if(ret.SUCCESS>0){
				DlgUtil.showMsg(ret.MESSAGE);
//				$('#grdPhieu').jqGrid('setCell',row,7,'Không duyệt');
//				$('#grdPhieu').jqGrid('setCell',row,3,KHONGDUYET);
//					_loadChiTiet(_id,KHONGDUYET);

//				_formatRow(row,KHONGDUYET-1);
				_loadDSPhieu();
			}
			else{
				DlgUtil.showMsg(ret.MESSAGE);
			}*/
            if (dsHoaDon.length > 0) {
                var danhsach = '';
                for (var i = 0; i < dsHoaDon.length; i++) {
                    var rowIds = $('#grdPhieu').jqGrid('getDataIDs');
                    for (j = 0; j < rowIds.length; j++) {
                        var rowData = $('#grdPhieu').jqGrid('getRowData', rowIds[j]);
                        if (rowData.NHAPXUATID == dsHoaDon[i] && rowData.TTDUYETPHATTHUOC == '1') {
                            DlgUtil.showMsg("Danh sách duyệt phát có phiếu " + rowData.MA + " chưa phát.");
                            return;
                        }
                    }
                    if (i < dsHoaDon.length - 1) {
                        danhsach += dsHoaDon[i];
                        danhsach += ",";
                    } else {
                        danhsach += dsHoaDon[i];
                    }
                }
                var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC14T001.CANCEL", danhsach));
                if (ret.SUCCESS > 0) {
                    DlgUtil.showMsg(ret.MESSAGE);
                    _loadDSPhieu();

                } else {
                    DlgUtil.showMsg(ret.MESSAGE);
                    dsHoaDon = [];
                }
            } else {
                DlgUtil.showMsg("Không có danh sách cần gỡ duyệt phát");
                return;
            }
        });

        //bo khoa tuong tu go duyet
        $("#btnBoKhoa").on("click", function (e) {
            if (confirm("Bạn có thực sự muốn bỏ khóa không?")) {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                //var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC47T001.CANCEL",_id);
                var ret;
                if (PHARMA_KHOA_TVT_NEW == '1' && $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID == '5') {
                    ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01S002.HUYCK01", _id));
                } else {
                    ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC47T001.CANCEL", _id));
                }

                if (ret.SUCCESS > 0) {
                    DlgUtil.showMsg("Bỏ khóa thuốc vt thành công");
                    $('#grdPhieu').jqGrid('setCell', row, 7, 'Không duyệt');
                    $('#grdPhieu').jqGrid('setCell', row, 3, KHONGDUYET);
                    _loadChiTiet(_id, KHONGDUYET);

                    _formatRow(row, KHONGDUYET - 1);
                    _loadDSPhieu();
                } else {
                    DlgUtil.showMsg(ret.MESSAGE);
                }
                /*if(ret==1){
					DlgUtil.showMsg("Gỡ duyệt thành công");
					$('#grdPhieu').jqGrid('setCell',row,7,'Không duyệt');
					$('#grdPhieu').jqGrid('setCell',row,3,KHONGDUYET);
					_loadChiTiet(_id,KHONGDUYET);
					_formatRow(row,KHONGDUYET-1);
				}
				else{
					DlgUtil.showMsg("Gỡ duyệt không thành công");
				}*/
            }
        });
        //tao phieu
        $("#toolbarIdtbTaoPhieu").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var sql_par;
            if (_kieu == 2)
                sql_par = ['DUC47T001', _id, 'NHAP'];
            else
                sql_par = ['DUC47T001', _id, 'XUAT'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 0) {
                DlgUtil.showMsg("Phiếu đã tạo");
                return;
            }
            ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC47T001.CREATE", _id);
            if (ret == 1) {
                DlgUtil.showMsg("Tạo phiếu thành công");
            } else {
                DlgUtil.showMsg("Tạo phiếu không thành công");
            }
        });
        //------------------------------nhap xuat giua các kho---------------------------------------------------
        //YC nhap
        $("#toolbarIdtbYCNhap").on("click", function (e) {
            EventUtil.setEvent("YCNhap_success", function (e) {
                DlgUtil.close("dlgYCNhap");
                if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
            });
            var myVar = {
                loaiKhoId: $("#cboKho").val(),
                nhapxuatid: 0,
                loaiGiaoDien: that.opt.gd,
                hinhThucId: that.opt.ht
            };
            if (that.opt.gd == 'GUIDUYET') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauDuyetThuoc", myVar, "Yêu cầu gửi duyệt" + that.opt.title, 1200, 610);
                DlgUtil.open("dlgYCNhap");
            } else if (PHARMA_GIAODIEN_NHAP_NCC == 'DUC02N001_NhapThuocNCC_BVNT') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_NGT_YeuCauNhapThuoc", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 610);
                DlgUtil.open("dlgYCNhap");
            } else if (PHARMA_DUTRUKHO_NEW == '1') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc_BDHNI", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 610);
                DlgUtil.open("dlgYCNhap");
            } else {

                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 630);
                DlgUtil.open("dlgYCNhap");
            }

        });

        //bachnv 220622 sua soan thuoc
        $("#toolbarIdtbBatDauSoan").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01S001_SOANTHUOC", _id));
            if (Number(ret) > 0) {
                DlgUtil.showMsg('Đã chuyển trạng thái phiếu soạn thuốc');
                $("#toolbarIdtbHuySoan").prop("disabled", false);
                $("#toolbarIdtbBatDauSoan").prop("disabled", true);
                _loadDSPhieu();
            } else
                DlgUtil.showMsg('Có lỗi khi soạn thuốc');

        });
        $("#toolbarIdtbHuySoan").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01S001_HUYSOAN", _id));
            if (Number(ret) > 0) {
                DlgUtil.showMsg('Đã chuyển trạng thái phiếu hủy soạn thuốc');
                $("#toolbarIdtbHuySoan").prop("disabled", true);
                $("#toolbarIdtbBatDauSoan").prop("disabled", false);
                _loadDSPhieu();
            } else
                DlgUtil.showMsg('Có lỗi khi hủy soạn thuốc');
        });
        //bachnv 08102016
        //edit add Yc hoan tra
        $("#toolbarIdtbHoanTra").on("click", function (e) {
            EventUtil.setEvent("YCNhap_success", function (e) {
                DlgUtil.close("dlgYCNhap");
                if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
            });
            var myVar = {
                loaiKhoId: $("#cboKho").val(),
                nhapxuatid: 0,
                loaiGiaoDien: that.opt.gd,
                hinhThucId: that.opt.ht,
                loaiphieu: that.opt.lp,
                hoantra: '1'
            };
            if (PHARMA_GIAODIEN_NHAP_NCC == 'DUC02N001_NhapThuocNCC_BVNT') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_NGT_YeuCauNhapThuoc", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 610);
                DlgUtil.open("dlgYCNhap");
            } else if (PHARMA_DUTRUKHO_NEW == '1') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc_BDHNI", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 610);
                DlgUtil.open("dlgYCNhap");
            } else {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc", myVar, "Yêu cầu hoàn trả" + that.opt.title, 1200, 630);
                DlgUtil.open("dlgYCNhap");
            }
        });
        //end edit- bachnv

        $("#toolbarIdtbXacNhanVPP").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            EventUtil.setEvent("appr_success", function (e) {
                DlgUtil.close("dlgAppr");
                //$('#grdPhieu').jqGrid('setCell',row,7,'Đã duyệt');
                //$('#grdPhieu').jqGrid('setCell',row,3,DADUYET);
//				_loadChiTiet(_id,DADUYET);
//				_formatRow(row,DADUYET-1);
            });
            var myVar = {nhapxuatId: _id};
            dlgPopup = DlgUtil.buildPopupUrl("dlgAppr", "divDlg", "manager.jsp?func=../duoc/DUC47T001_XacNhanVPP", myVar, "Duyệt phiếu", 1100, 550);
            DlgUtil.open("dlgAppr");
        });

        //Daidv 30092019
        //e hoan tra thuoc het HSD
        /*$("#toolbarIdtbHoanTraTVTHetHan").on("click",function(e){
			EventUtil.setEvent("YCNhap_success",function(e){
				DlgUtil.close("dlgYCNhap");
				_loadDSPhieu();
			});
			var myVar={loaiKhoId:$("#cboKho").val(),nhapxuatid:0,loaiGiaoDien:that.opt.gd,hinhThucId:that.opt.ht,loaiphieu:that.opt.lp,hoantra:'1'};
			dlgPopup=DlgUtil.buildPopupUrl("dlgYCNhap","divDlg","manager.jsp?func=../duoc/DUC11N001_HoanTraTVTHetHSD",myVar,"Hoàn trả TVT hết HSD" + that.opt.title,1200,610);
			DlgUtil.open("dlgYCNhap");
		});*/
        //end

        //Sua
        $("#toolbarIdtbSua").on("click", function (e) {
            tbSuaClick();
        });

        function tbSuaClick() {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
            var trangthai = $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID;
            EventUtil.setEvent("YCNhap_success", function (e) {
                DlgUtil.close("dlgYCNhap");
                if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
            });
            var ht = 0;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            if (kieu == 2) {
                ht = 1;
            } //hoan tra
            var myVar = {
                loaiKhoId: $("#cboKho").val(),
                nhapxuatid: _id,
                loaiGiaoDien: that.opt.gd,
                hinhThucId: that.opt.ht,
                loaiphieu: that.opt.lp,
                hoantra: ht,
                trangthai: trangthai
            };
            if (PHARMA_GIAODIEN_NHAP_NCC == 'DUC02N001_NhapThuocNCC_BVNT') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_NGT_YeuCauNhapThuoc", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 610);
                DlgUtil.open("dlgYCNhap");
            } else if (PHARMA_DUTRUKHO_NEW == '1') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc_BDHNI", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 610);
                DlgUtil.open("dlgYCNhap");
            } else {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 630);
                DlgUtil.open("dlgYCNhap");
            }
        }

        //dieu chinh
        $("#toolbarIdtbDieuChinhDon").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
            EventUtil.setEvent("YCNhap_success", function (e) {
                DlgUtil.close("dlgYCNhap");
                if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
            });
            var ht = 0;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var trangthai = $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID;
            var hinhthuc = $("#grdPhieu").jqGrid('getRowData', row).HINHTHUCID;
            var khoid = $("#grdPhieu").jqGrid('getRowData', row).KHOID;
            if (that.opt.ht == '12')
                khoid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
            if (kieu == 2) {
                DlgUtil.showMsg('Không điều chỉnh phiếu hoàn trả!');
                return;
            }
            if (trangthai != 5) {
                DlgUtil.showMsg('Phiếu không điều chỉnh được!');
                return;
            }
            if (!(hinhthuc == 2 || hinhthuc == 9 || ((hinhthuc == 12 || hinhthuc == 13) && that.opt.gd == 'MAU'))) {
                DlgUtil.showMsg('Phiếu không điều chỉnh được!');
                return;
            }
            if ($("#grdPhieu").jqGrid('getRowData', row).XUATID != '') {
                DlgUtil.showMsg('Phiếu đã giữ ảo.Phiếu không điều chỉnh được!');
                return;
            }
            var myVar = {
                loaiKhoId: khoid,
                nhapxuatid: _id,
                loaiGiaoDien: that.opt.gd,
                hinhThucId: that.opt.ht,
                loaiphieu: that.opt.lp,
                hoantra: ht,
                dieuchinh: 1,
                trangthai: trangthai
            };
            if (PHARMA_GIAODIEN_NHAP_NCC == 'DUC02N001_NhapThuocNCC_BVNT') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_NGT_YeuCauNhapThuoc", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 610);
                DlgUtil.open("dlgYCNhap");
            } else {
                if ((hinhthuc == 12 || hinhthuc == 13) && that.opt.gd == 'MAU')
                    dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC86T001_DieuChinh", myVar, "Điều chỉnh máu", 1200, 610);
                else dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc", myVar, "Điều chỉnh bổ sung" + that.opt.title, 1200, 630);
                DlgUtil.open("dlgYCNhap");
            }
        });

        //duyet duoc lam san yc bo sung thuoc tu truc


        //Bổ sung nợ thuốc
        $("#toolbarIdtbBoSungNoThuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
            EventUtil.setEvent("YCNhap_success", function (e) {
                DlgUtil.close("dlgYCNhap");
                if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
            });
            var ht = 0;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var trangthai = $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID;
            var hinhthuc = $("#grdPhieu").jqGrid('getRowData', row).HINHTHUCID;
            var khoid = $("#grdPhieu").jqGrid('getRowData', row).KHOID;
            if (that.opt.ht == '12')
                khoid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
            if (kieu == 2) {
                DlgUtil.showMsg('Không điều chỉnh phiếu hoàn trả!');
                return;
            }
            if (trangthai != 5) {
                DlgUtil.showMsg('Phiếu không điều chỉnh được!');
                return;
            }
            if (!(hinhthuc == 2 || hinhthuc == 9)) {
                DlgUtil.showMsg('Phiếu không điều chỉnh được!');
                return;
            }
            var myVar = {
                loaiKhoId: khoid,
                nhapxuatid: _id,
                loaiGiaoDien: that.opt.gd,
                hinhThucId: that.opt.ht,
                loaiphieu: that.opt.lp,
                hoantra: ht,
                trangthai: trangthai
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauBoSungNoThuoc", myVar, "Điều chỉnh bổ sung" + that.opt.title, 1200, 610);
            DlgUtil.open("dlgYCNhap");

        });
        $("#toolbarIdtbDuyetLamSan").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
            EventUtil.setEvent("YCNhap_success", function (e) {
                DlgUtil.close("dlgYCNhap");
                //_loadDSPhieu();
                if (that.opt.lp == "3" && that.opt.ht == "9" && that.opt.gd == "THUOC" && PHARMA_DUYET_DLS_BOSUNG_TUTRUC == "1" && that.opt.td == "9") {
                    //if(that.opt.td == "9"){
                    _loadDSPhieuLamSan();
//					}else{
//						_loadDSPhieuLS_DaDuyet();
//					}
                } else if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
            });
            var ht = 0;
            var kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var trangthai = $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID;
            var hinhthuc = $("#grdPhieu").jqGrid('getRowData', row).HINHTHUCID;
            var khoid = $("#grdPhieu").jqGrid('getRowData', row).KHOID;

//			if(kieu ==2) {DlgUtil.showMsg('Không điều chỉnh phiếu hoàn trả!'); return;}
            if (trangthai != 5) {
                DlgUtil.showMsg('Phiếu không duyệt lâm sàng được!');
                return;
            }
            if (hinhthuc != 9) {
                DlgUtil.showMsg('Phiếu không duyệt lâm sàng được!');
                return;
            }
            var myVar = {
                loaiKhoId: khoid,
                nhapxuatid: _id,
                loaiGiaoDien: that.opt.gd,
                hinhThucId: that.opt.ht,
                loaiphieu: that.opt.lp,
                hoantra: ht,
                duyetlamsan: 1
            };
            if (PHARMA_GIAODIEN_NHAP_NCC == 'DUC02N001_NhapThuocNCC_BVNT') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_NGT_YeuCauNhapThuoc", myVar, "Duyệt lâm sàn thuốc bổ sung tủ trực", 1200, 610);
                DlgUtil.open("dlgYCNhap");
            } else {
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc", myVar, "Duyệt lâm sàng thuốc bổ sung tủ trực", 1200, 630);
                DlgUtil.open("dlgYCNhap");
            }
        });

        //Nhap kho -- Bach bo phan nay
//		$("#toolbarIdtbNhapKho").on("click",function(e){
//			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
//			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
//			var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
//			var sql_par=['DUC57N001',_id,'NHAPKHO'];
//			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK",sql_par.join('$'));
//			if(ret == 0){
//				DlgUtil.showMsg("Phiếu đã nhập kho");
//				return;
//			}
//			EventUtil.setEvent("NhapKho_success",function(e){
//				DlgUtil.close("dlgNhapKho");
////				$('#grdPhieu').jqGrid('setCell',row,7,'Đã kết thúc');
////				$('#grdPhieu').jqGrid('setCell',row,3,KETTHUC);
//				_loadDSPhieu();
//				//_loadChiTiet(_id,KETTHUC);
//				//_formatRow(row,KETTHUC-1);
//
//			});
//			if($("#cboKho").val() == _doiungid){
//				DlgUtil.showMsg("Phiếu đang thuộc kho cung ứng. Không được nhập kho");
//			}
//			else{
//				var myVar={nhapxuatid:_id,loaiGiaoDien:that.opt.gd};
//				dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC57N001_NhapThuocTKK",myVar,"Nhập kho" + that.opt.title ,1000,500);
//				DlgUtil.open("dlgNhapKho");
//			}
//		});
        $("#btnNhapKho").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
            var sql_par = ['DUC57N001', _id, 'NHAPKHO'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 0) {
                DlgUtil.showMsg("Phiếu đã nhập kho");
                return;
            }
            EventUtil.setEvent("NhapKho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
//				$('#grdPhieu').jqGrid('setCell',row,7,'Đã kết thúc');
//				$('#grdPhieu').jqGrid('setCell',row,3,KETTHUC);
                _loadDSPhieu();
                //_loadChiTiet(_id,KETTHUC);
                //_formatRow(row,KETTHUC-1);

            });
            if ($("#cboKho").val() == _doiungid) {
                DlgUtil.showMsg("Phiếu đang thuộc kho cung ứng. Không được nhập kho");
            } else {
                var myVar = {nhapxuatid: _id, loaiGiaoDien: that.opt.gd};
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC57N001_NhapThuocTKK", myVar, "Nhập kho" + that.opt.title, 1000, 500);
                DlgUtil.open("dlgNhapKho");
            }
        });
        // Duyet nhieu don
        $("#btnDuyetNhieuDon").on("click", function (e) {
            var ketqua = '';
            var count = 0;
            var countLoi = 0;
            var selRows = $('#grdPhieu').jqGrid('getGridParam', 'selarrrow');
            if (selRows.length == 0) {
                DlgUtil.showMsg("Chưa chọn đơn duyệt");
                return;
            }
            for (i = 0; i < selRows.length; i++) {
                rowData = $('#grdPhieu').jqGrid('getRowData', selRows[i]);
                if (rowData.TRANGTHAIID != '5') {
                    DlgUtil.showMsg("Có phiếu trạng thái không phải chờ duyệt");
                    return;
                }

            }
            for (i = 0; i < selRows.length; i++) {
                rowData = $('#grdPhieu').jqGrid('getRowData', selRows[i]);
                var sql_par = ['', rowData.NHAPXUATID, rowData.GHICHU];

                var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC47T001.APPR", sql_par.join('$')));
                if (ret.SUCCESS > 0) {
                    count = count + 1;
                } else {
                    countLoi = countLoi + 1;
                    ketqua = rowData.MA + ':' + ret.MESSAGE + '.';
                }
            }

            DlgUtil.showMsg("Đã duyệt thành công" + count + " phiếu, Có " + countLoi + " lỗi:" + ketqua, function () {
                _loadDSPhieu();
            });
        });

        $("#btnShowHide").on("click", function (e) {
            if (PHARMA_LUOCBOT_PHIEUYC == 1) {
                $("#spThongtin").show(1000);
                $("#spThongTin2").show(1000);

                $("#grdThuoc").parents('div.ui-jqgrid-bdiv').css("max-height", "140px");
                document.getElementById("btnShowHide").innerHTML = '<img src="../common/image/up.png" width="12px">';
//				document.getElementById("btnShowHide").src = "../common/image/up.png";

                PHARMA_LUOCBOT_PHIEUYC = 0;
            }// show lai
            else {
                $("#spThongtin").hide(1000);
                $("#spThongTin2").hide(1000);

                $("#grdThuoc").parents('div.ui-jqgrid-bdiv').css("max-height", "300px");
                document.getElementById("btnShowHide").innerHTML = '<img src="../common/image/down.png" width="12px">';
                PHARMA_LUOCBOT_PHIEUYC = 1;
//				document.getElementById("btnShowHide").src = "../common/image/down.png";
            }// an di
        });
        //Xuat tra -- Bach bo phan nay thay bang button btnXuatTra
//		$("#toolbarIdtbXuatTra").on("click",function(e){
//			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
//			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
//			var _xuatId = $("#grdPhieu").jqGrid('getRowData', row).XUATID;
//			var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
//			var sql_par=['DUC58X001',_id,'XUATTRA'];
//			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK",sql_par.join('$'));
//			if(ret == 0){
//				DlgUtil.showMsg("Phiếu đã xuất trả");
//				return;
//			}
//			EventUtil.setEvent("XuatTra_success",function(e){
//				DlgUtil.close("dlgXuatTra");
////				$('#grdPhieu').jqGrid('setCell',row,7,'Đã kết thúc');
////				$('#grdPhieu').jqGrid('setCell',row,3,KETTHUC);
//				_loadDSPhieu();
//			//	_loadChiTiet(_id,KETTHUC);
//			//	_formatRow(row,KETTHUC-1);
//			});
//			if($("#cboKho").val() == _doiungid){
//				DlgUtil.showMsg("Khong duoc xuat tra");
//			}
//			else{
//			var myVar={nhapxuatid:_xuatId,loaiGiaoDien:that.opt.gd};
//			dlgPopup=DlgUtil.buildPopupUrl("dlgXuatTra","divDlg","manager.jsp?func=../duoc/DUC58X001_XuatTraThuocChoKhoKhac",myVar,"Xuất trả" + that.opt.title ,1000,500);
//			DlgUtil.open("dlgXuatTra");
//			}
//		});
        //Xuat tra -- bach sua xuat tra thay bang btnXuatTra
        $("#btnXuatTra").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _xuatId = $("#grdPhieu").jqGrid('getRowData', row).XUATID;
            var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
            var sql_par = ['DUC58X001', _id, 'XUATTRA'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 0) {
                DlgUtil.showMsg("Phiếu đã xuất trả");
                return;
            }
            EventUtil.setEvent("XuatTra_success", function (e) {
                DlgUtil.close("dlgXuatTra");
//				$('#grdPhieu').jqGrid('setCell',row,7,'Đã kết thúc');
//				$('#grdPhieu').jqGrid('setCell',row,3,KETTHUC);
                if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                    _loadDSPhieuBUCSTT();
                } else {
                    _loadDSPhieu();
                }
                //	_loadChiTiet(_id,KETTHUC);
                //	_formatRow(row,KETTHUC-1);
            });
            if ($("#cboKho").val() == _doiungid) {
                DlgUtil.showMsg("Khong duoc xuat tra");
            } else {
                var myVar = {nhapxuatid: _xuatId, loaiGiaoDien: that.opt.gd};
                dlgPopup = DlgUtil.buildPopupUrl("dlgXuatTra", "divDlg", "manager.jsp?func=../duoc/DUC58X001_XuatTraThuocChoKhoKhac", myVar, "Xuất trả" + that.opt.title, 1000, 500);
                DlgUtil.open("dlgXuatTra");
            }
        });
        //YC xuat
        $("#toolbarIdtbYCXuat").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
            var _nhapId = $("#grdPhieu").jqGrid('getRowData', row).NHAPID;

            if (_nhapId != 0 && _nhapId != "") {
                EventUtil.setEvent("YCXuat_success", function (e) {
                    DlgUtil.close("dlgYCXuat");
                    if (that.opt.lp == "3" && that.opt.ht == "9" && (that.opt.gd == "THUOC" || that.opt.gd == "VATTU") && (that.opt.td == "12" || that.opt.td == "13")) {
                        _loadDSPhieuBUCSTT();
                    } else {
                        _loadDSPhieu();
                    }
                    //	$('#grdPhieu').jqGrid('setCell',row,7,'Đã kết thúc');
                    //	$('#grdPhieu').jqGrid('setCell',row,3,CHODUYET);
                    //	_loadChiTiet(_id,CHODUYET);
                    //	_formatRow(row,CHODUYET-1);
                });
                var myVar =
                    {
                        loaiKhoId: $("#cboKho").val(),
                        hinhThucId: that.opt.ht,
                        nhapxuatid: _nhapId,
                        loaiGiaoDien: that.opt.gd
                    };
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCXuat", "divDlg", "manager.jsp?func=../duoc/DUC30X001_KhoaPhongYCNhapTraThuoc", myVar, "Yêu cầu hoàn trả" + that.opt.title, 1000, 500);
                DlgUtil.open("dlgYCXuat");
            } else {
                DlgUtil.showMsg("Phiếu chưa nhập kho");
                return;
            }
        });
        //------------------------------nhap xuat tu nha cung cap---------------------------------------------------
        //nhap kho tu nha cung cap
        $("#toolbarIdtbNhapKhoNCC").on("click", function (e) {
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            if (that.opt.lk == '4' && (that.opt.hospitalId == '965' || that.opt.hospitalId == '1111')) {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: 0,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    edit: that.opt.cs
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_NhaThuoc_BVBD", myVar, "Nhập kho từ NCC vào nhà thuốc", 1310, 700);
                DlgUtil.open("dlgNhapKho");

            }
//			else if (that.opt.lk == '4' && that.opt.hospitalId == '1077') {
//				var myVar={khoid:$("#cboKho").val(),nhapxuatid:0,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,edit:that.opt.cs};
//				  dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_NhaThuoc_VD2",myVar,"Nhập kho từ NCC vào nhà thuốc" ,1200,580);
//				  DlgUtil.open("dlgNhapKho");
//
//			}
            else if (that.opt.lk == '4') {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: 0,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    edit: that.opt.cs
                };
                if (PHARMA_GIAODIEN_NCC_NHATHUOC != '0' && PHARMA_GIAODIEN_NCC_NHATHUOC != '') {
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/" + PHARMA_GIAODIEN_NCC_NHATHUOC, myVar, "Nhập kho từ NCC" + that.opt.title, 1300, 670);
                } else {
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_NhaThuoc", myVar, "Nhập kho từ NCC vào nhà thuốc", 1200, 580);
                }
                DlgUtil.open("dlgNhapKho");

            } else if (PHARMA_GIAODIEN_NHAP_NCC == 0) {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: 0,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    edit: that.opt.cs
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Nhập kho từ NCC" + that.opt.title, 1200, 620);
                DlgUtil.open("dlgNhapKho");
            } else if (that.opt.lt == '4') {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: 0,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    edit: that.opt.cs,
                    ht: that.opt.ht,
                    lt: that.opt.lt
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC_Mau_NhapMauNCC", myVar, "Nhập kho từ NCC máu", 1210, 610);
                DlgUtil.open("dlgNhapKho");
            } else {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: 0,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    edit: that.opt.cs,
                    ht: that.opt.ht
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/" + PHARMA_GIAODIEN_NHAP_NCC, myVar, "Nhập kho từ NCC" + that.opt.title, 1300, 700);
                DlgUtil.open("dlgNhapKho");
                // san nhi ham nam
                /*	var myVar={khoid:$("#cboKho").val(),nhapxuatid:0,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,edit:that.opt.cs};
                    dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC",myVar,"Nhập kho từ NCC" + that.opt.title ,1200,600);
                    DlgUtil.open("dlgNhapKho");*/
            }


        });

        //nhap kho tu nha cung cap vao kho thuoc nha thuoc
        /*$("#toolbarIdtbNhapKho_NT").on("click",function(e){
			EventUtil.setEvent("nhapkho_success",function(e){
				DlgUtil.close("dlgNhapKho");
				_loadDSPhieu();
			});
			EventUtil.setEvent("nhapkho_cancel",function(e){
				DlgUtil.close("dlgNhapKho");
				_loadDSPhieu();
			});

				var myVar={khoid:$("#cboKho").val(),nhapxuatid:0,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,edit:that.opt.cs};
			  dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_PhuPhi",myVar,"Nhập kho từ NCC vào nhà thuốc" + that.opt.title ,1200,600);
			  DlgUtil.open("dlgNhapKho");

		});*/

        //nhap kho tu nha cung cap có phụ phí
        /*$("#toolbarIdtbNhapKho_PP").on("click",function(e){
			EventUtil.setEvent("nhapkho_success",function(e){
				DlgUtil.close("dlgNhapKho");
				_loadDSPhieu();
			});
			EventUtil.setEvent("nhapkho_cancel",function(e){
				DlgUtil.close("dlgNhapKho");
				_loadDSPhieu();
			});
			if(PHARMA_GIAODIEN_NHAP_NCC == 1){
				var myVar={khoid:$("#cboKho").val(),nhapxuatid:0,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,edit:that.opt.cs};
				  dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_BVNT",myVar,"Nhập kho từ NCC" + that.opt.title ,1210,610);
				  DlgUtil.open("dlgNhapKho");
			}
			else{
				var myVar={khoid:$("#cboKho").val(),nhapxuatid:0,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,edit:that.opt.cs};
			  dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_PhuPhi",myVar,"Nhập kho từ NCC" + that.opt.title ,1210,630);
			  DlgUtil.open("dlgNhapKho");
			}



		});*/


        //Bach sua nut nhap kho cho xuong duoi
        $("#btnNhapKhoNCC").on("click", function (e) {
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: 0,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                edit: that.opt.cs
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Nhập kho từ NCC" + that.opt.title, 1200, 620);
            DlgUtil.open("dlgNhapKho");
        });
        //xuat tra nha cung cap theo lô
        $("#toolbarIdtbXuatTraNCC").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            EventUtil.setEvent("xuattra_success", function (e) {
                DlgUtil.close("dlgXuatTra");
                _loadDSPhieu();
                $('#grdPhieu').jqGrid('setCell', row, 7, 'Tạo mới');
                $('#grdPhieu').jqGrid('setCell', row, 3, TAOMOI);
                _loadChiTiet(_id, TAOMOI);
                _formatRow(row, TAOMOI - 1);
            });
            EventUtil.setEvent("xuattra_cancel", function (e) {
                DlgUtil.close("dlgXuatTra");
                _loadDSPhieu();
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgXuatTra", "divDlg", "manager.jsp?func=../duoc/DUC03X001_XuatTraThuocNCC", myVar, "Xuất trả NCC" + that.opt.title, 1200, 600);
            DlgUtil.open("dlgXuatTra");
        });

        //xuat tra nha cung cap theo ton kho
        $("#toolbarIdtbXuatTraNCC_TK").on("click", function (e) {
            EventUtil.setEvent("YCNhap_success", function (e) {
                DlgUtil.close("dlgYCNhap");
                _loadDSPhieu();
            });
            var myVar = {
                loaiKhoId: $("#cboKho").val(),
                nhapxuatid: 0,
                loaiGiaoDien: that.opt.gd,
                hinhThucId: that.opt.ht,
                loainhapbu: "XUATTRANCC_TK"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc", myVar, "Xuất trả " + that.opt.title + " tồn kho NCC", 1200, 630);
            DlgUtil.open("dlgYCNhap");
        });
        // Bach sua xuat tra nha cung cap ( tao nut xuat tra rieng) bo nut nay di
//		$("#btnXuatTraNCC").on("click",function(e){
//			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
//			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
//			EventUtil.setEvent("xuattra_success",function(e){
//				DlgUtil.close("dlgXuatTra");
//				$('#grdPhieu').jqGrid('setCell',row,7,'Tạo mới');
//				$('#grdPhieu').jqGrid('setCell',row,3,TAOMOI);
//				_loadChiTiet(_id,TAOMOI);
//				_formatRow(row,TAOMOI-1);
//			});
//			EventUtil.setEvent("xuattra_cancel",function(e){
//				DlgUtil.close("dlgXuatTra");
//				_loadDSPhieu();
//			});
//			var myVar={khoid:$("#cboKho").val(),nhapxuatid:_id,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd};
//			dlgPopup=DlgUtil.buildPopupUrl("dlgXuatTra","divDlg","manager.jsp?func=../duoc/DUC03X001_XuatTraThuocNCC",myVar,"Xuất trả NCC" + that.opt.title ,1200,600);
//			DlgUtil.open("dlgXuatTra");
//		});

        //bổ thong tin tu nha cung cap lạng sơn
        $("#toolbarIdtbBoSung").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var _trangthaiid = $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID;
            var _sochungtu = $("#grdPhieu").jqGrid('getRowData', row).SOCHUNGTU;
            // xac dinh phieu tra theo phieu nhap hay phieu tra theo lô
            var _xuatid = $("#grdPhieu").jqGrid('getRowData', row).XUATID;
            if (_kieu == 0) {
                EventUtil.setEvent("nhapkho_success", function (e) {
                    DlgUtil.close("dlgNhapKho");
                    _loadDSPhieu();
                });
                EventUtil.setEvent("nhapkho_cancel", function (e) {
                    DlgUtil.close("dlgNhapKho");
                    _loadDSPhieu();
                });
                var _title = "";
                if (_trangthaiid == "4") {
                    _title = "Bổ sung phiếu" + that.opt.title + " đã Nhập Kho";
                } else {
                    _title = "Sửa phiếu" + that.opt.title + " đã tạo";
                }

                if (that.opt.lk == '4' && PHARMA_BC_NHAP_KHO == 965) {
                    var myVar = {
                        khoid: $("#cboKho").val(),
                        nhapxuatid: _id,
                        tenkho: $("#cboKho option:selected").text(),
                        kieu: that.opt.gd,
                        trangthai: '2',
                        loaiphieu: _kieu,
                        edit: that.opt.cs
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_NhaThuoc_BVBD", myVar, "Sửa phiếu nhập kho nhà thuốc", 1210, 600);
                    DlgUtil.open("dlgNhapKho");

                } else if (that.opt.lk == '4') {
                    var myVar = {
                        khoid: $("#cboKho").val(),
                        nhapxuatid: _id,
                        tenkho: $("#cboKho option:selected").text(),
                        kieu: that.opt.gd,
                        trangthai: '2',
                        loaiphieu: _kieu,
                        edit: that.opt.cs
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_NhaThuoc", myVar, "Sửa phiếu nhập kho nhà thuốc", 1200, 580);
                    DlgUtil.open("dlgNhapKho");

                } else if (PHARMA_GIAODIEN_NHAP_NCC == 0) {
                    //var myVar={khoid:$("#cboKho").val(),nhapxuatid:0,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,edit:that.opt.cs};
                    var myVar = {
                        khoid: $("#cboKho").val(),
                        nhapxuatid: _id,
                        tenkho: $("#cboKho option:selected").text(),
                        kieu: that.opt.gd,
                        trangthai: '2',
                        loaiphieu: _kieu,
                        edit: that.opt.cs
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, _title, 1200, 620);
                    DlgUtil.open("dlgNhapKho");
                } else {
                    var myVar = {
                        khoid: $("#cboKho").val(),
                        nhapxuatid: _id,
                        tenkho: $("#cboKho option:selected").text(),
                        kieu: that.opt.gd,
                        trangthai: '2',
                        loaiphieu: _kieu,
                        edit: that.opt.cs,
                        ht: that.opt.ht
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/" + PHARMA_GIAODIEN_NHAP_NCC, myVar, _title, 1300, 700);
                    DlgUtil.open("dlgNhapKho");
                    // san nhi ham nam
                    /*	var myVar={khoid:$("#cboKho").val(),nhapxuatid:_id,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,trangthai:_trangthaiid,loaiphieu:_kieu,edit:that.opt.cs};
                        dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC",myVar,_title,1200,600);
                        DlgUtil.open("dlgNhapKho");*/
                }
            } else {

            }


        });


        //sua thong tin tu nha cung cap
        $("#toolbarIdtbSuaNCC").on("click", function (e) {
            tbSuaNCCClick();
        });

        function tbSuaNCCClick() {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var _trangthaiid = $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID;
            var _sochungtu = $("#grdPhieu").jqGrid('getRowData', row).SOCHUNGTU;
            //var _nxid_cha = $("#grdPhieu").jqGrid('getRowData', row).XUATID;
            // xac dinh phieu tra theo phieu nhap hay phieu tra theo lô
            var _xuatid = $("#grdPhieu").jqGrid('getRowData', row).XUATID;
            if (_kieu == 0) {
                EventUtil.setEvent("nhapkho_success", function (e) {
                    DlgUtil.close("dlgNhapKho");
                    _loadDSPhieu();
                });
                EventUtil.setEvent("nhapkho_cancel", function (e) {
                    DlgUtil.close("dlgNhapKho");
                    _loadDSPhieu();
                });
                var _title = "";
                if (_trangthaiid == "4") {
                    _title = "Sửa phiếu" + that.opt.title + " đã Nhập Kho";
                } else {
                    _title = "Sửa phiếu" + that.opt.title + " đã tạo";
                }

                if (that.opt.lk == '4' && that.opt.hospitalId == '965') {
                    var myVar = {
                        khoid: $("#cboKho").val(),
                        nhapxuatid: _id,
                        tenkho: $("#cboKho option:selected").text(),
                        kieu: that.opt.gd,
                        trangthai: _trangthaiid,
                        loaiphieu: _kieu,
                        edit: that.opt.cs
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_NhaThuoc_BVBD", myVar, "Sửa phiếu nhập kho nhà thuốc", 1210, 600);
                    DlgUtil.open("dlgNhapKho");

                } else if (that.opt.lk == '4' && that.opt.hospitalId == '1077') {
                    var myVar = {
                        khoid: $("#cboKho").val(),
                        nhapxuatid: _id,
                        tenkho: $("#cboKho option:selected").text(),
                        kieu: that.opt.gd,
                        trangthai: _trangthaiid,
                        loaiphieu: _kieu,
                        edit: that.opt.cs
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_NhaThuoc_VD2", myVar, "Sửa phiếu nhập kho từ NCC vào Nhà thuốc", 1200, 580);
                    DlgUtil.open("dlgNhapKho");

                } else if (that.opt.lk == '4') {
                    var myVar = {
                        khoid: $("#cboKho").val(),
                        nhapxuatid: _id,
                        tenkho: $("#cboKho option:selected").text(),
                        kieu: that.opt.gd,
                        trangthai: _trangthaiid,
                        loaiphieu: _kieu,
                        edit: that.opt.cs
                    };
                    if (PHARMA_GIAODIEN_NCC_NHATHUOC != '0' && PHARMA_GIAODIEN_NCC_NHATHUOC != '') {
                        dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/" + PHARMA_GIAODIEN_NCC_NHATHUOC, myVar, "Sửa phiếu nhập kho nhà thuốc", 1200, 580);
                    } else {
                        dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_NhaThuoc", myVar, "Sửa phiếu nhập kho nhà thuốc", 1200, 580);
                    }

                    DlgUtil.open("dlgNhapKho");

                } else if (PHARMA_GIAODIEN_NHAP_NCC == 0) {
                    //var myVar={khoid:$("#cboKho").val(),nhapxuatid:0,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,edit:that.opt.cs};
                    var myVar = {
                        khoid: $("#cboKho").val(),
                        nhapxuatid: _id,
                        tenkho: $("#cboKho option:selected").text(),
                        kieu: that.opt.gd,
                        trangthai: _trangthaiid,
                        loaiphieu: _kieu,
                        edit: that.opt.cs
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, _title, 1200, 620);
                    DlgUtil.open("dlgNhapKho");
                } else {
                    var myVar = {
                        khoid: $("#cboKho").val(),
                        nhapxuatid: _id,
                        tenkho: $("#cboKho option:selected").text(),
                        kieu: that.opt.gd,
                        trangthai: _trangthaiid,
                        loaiphieu: _kieu,
                        edit: that.opt.cs,
                        ht: that.opt.ht
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/" + PHARMA_GIAODIEN_NHAP_NCC, myVar, _title, 1300, 700);
                    DlgUtil.open("dlgNhapKho");
                    // san nhi ham nam
                    /*	var myVar={khoid:$("#cboKho").val(),nhapxuatid:_id,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,trangthai:_trangthaiid,loaiphieu:_kieu,edit:that.opt.cs};
                        dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC",myVar,_title,1200,600);
                        DlgUtil.open("dlgNhapKho");*/
                }
            } else if (_kieu == 1 && (_xuatid == '0' || _xuatid == '')) {
                EventUtil.setEvent("xuattra_success", function (e) {
                    DlgUtil.close("dlgXuatTra");
                    _loadDSPhieu();
                });
                EventUtil.setEvent("xuattra_cancel", function (e) {
                    DlgUtil.close("dlgXuatTra");
                    _loadDSPhieu();
                });
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: _id,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    trangthai: _trangthaiid,
                    sochungtu: _sochungtu
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgXuatTra", "divDlg", "manager.jsp?func=../duoc/DUC03X001_XuatTraThuocNCC", myVar, "Sửa phiếu xuất trả" + that.opt.title, 1200, 600);
                DlgUtil.open("dlgXuatTra");
            } else if (_kieu == 1 && _xuatid != '0') {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                var _khoid = $("#grdPhieu").jqGrid('getRowData', row).KHOID;
                EventUtil.setEvent("YCNhap_success", function (e) {
                    DlgUtil.close("dlgYCNhap");
                    _loadDSPhieu();
                });


                var myVar = {
                    loaiKhoId: $("#cboKho").val(),
                    nhapxuatid: _id,
                    loaiGiaoDien: that.opt.gd,
                    hinhThucId: "1",
                    loaiphieu: "1",
                    trangthai: _trangthaiid,
                    loainhapbu: "XUATTRANCC_TK"
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc", myVar, "Sửa xuất trả " + that.opt.title + " tồn kho NCC", 1200, 630);
                DlgUtil.open("dlgYCNhap");
            }
        }

        // vietda, them button ky so
        $("#btnKySo").on("click", function (e) {
            EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                causer = e.username;
                capassword = e.password;
                DlgUtil.close("divCALOGIN");
                _caRpt('1');
            });
            EventUtil.setEvent("dlgCaLogin_close", function (e) {
                DlgUtil.close("divCALOGIN");
            });
            var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
            var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", "CA - LOGIN", 505, 268);
            popup.open("divCALOGIN");
        });

        // vietda, them button huy ky so
        $("#btnHuyKySo").on("click", function (e) {
            EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                causer = e.username;
                capassword = e.password;
                DlgUtil.close("divCALOGIN");
                _caRpt('2');
            });
            EventUtil.setEvent("dlgCaLogin_close", function (e) {
                DlgUtil.close("divCALOGIN");
            });
            var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
            var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", "CA - LOGIN", 505, 268);
            popup.open("divCALOGIN");
        });

        function _caRpt(signType) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            //var oData = '';
            if (that.opt.ht == '12') {
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D044.15.NEW", _id);
                var _freport = ret.split(";");

                for (var i = 0; i < _freport.length; i++) {
                    var oData = {
                        sign_type: signType,
                        causer: causer,
                        capassword: capassword,
                        params: [
                            {
                                name: 'nhapxuatid',
                                type: 'String',
                                value: _id
                            },
                            {
                                name: 'RPT_CODE',
                                type: 'String',
                                value: _freport[i]
                            }
                        ]
                    };

                    var _rs = null;
                    var result = $.ajax({
                        url: '/vnpthis/apicarpt',
                        type: "POST",
                        data: JSON.stringify(oData),
                        contentType: 'application/json; charset=utf-8',
                        dataType: "json",
                        async: false
                    }).done(function (_response) {
                        _rs = _response;
                    });

                    // Response Null
                    if (!result) {
                        alert('Sign CA: Return NULL');
                        return false;
                    }

                    // success
                    if (_rs.CODE == 0) {
                        if (signType == '1') {
                            DlgUtil.showMsg('Ký số CA thành công!');
                            _exportKyCA_TP(_freport[i], _id);

                        } else {
                            DlgUtil.showMsg('Hủy ký số CA thành công!');
                        }
                    } else if (_rs.CODE == 1) {
                        DlgUtil.showMsg('Phiếu đã thực hiện ký số!');
                    } else if (_rs.CODE == 2) {
                        DlgUtil.showMsg('Phiếu chưa thực hiện ký số!');
                    } else if (_rs.CODE == 5) {
                        DlgUtil.showMsg('sign_type không đúng định dạng!');
                    } else {
                        if (signType == '1') {
                            DlgUtil.showMsg('Ký số CA thất bại!');
                        } else {
                            DlgUtil.showMsg('Hủy ký số CA thất bại!');
                        }
                    }

                }
            } else {
                var oData = {
                    sign_type: signType,
                    causer: causer,
                    capassword: capassword,
                    params: [
                        {
                            name: 'dotnhapid',
                            type: 'String',
                            value: _id
                        },
                        {
                            name: 'RPT_CODE',
                            type: 'String',
                            value: 'DUC008_PHIEUNHAPKHO_QD_BTC_A4'
                        }
                    ]
                };

                var _rs = null;
                var result = $.ajax({
                    url: '/vnpthis/apicarpt',
                    type: "POST",
                    data: JSON.stringify(oData),
                    contentType: 'application/json; charset=utf-8',
                    dataType: "json",
                    async: false
                }).done(function (_response) {
                    _rs = _response;
                });

                // Response Null
                if (!result) {
                    alert('Sign CA: Return NULL');
                    return false;
                }

                // success
                if (_rs.CODE == 0) {
                    if (signType == '1') {
                        _exportKyCA();
                        DlgUtil.showMsg('Ký số CA thành công!');

                    } else {
                        DlgUtil.showMsg('Hủy ký số CA thành công!');

                    }
                } else if (_rs.CODE == 1) {
                    DlgUtil.showMsg('Phiếu đã thực hiện ký số!');
                } else if (_rs.CODE == 2) {
                    DlgUtil.showMsg('Phiếu chưa thực hiện ký số!');
                } else if (_rs.CODE == 5) {
                    DlgUtil.showMsg('sign_type không đúng định dạng!');
                } else {
                    if (signType == '1') {
                        DlgUtil.showMsg('Ký số CA thất bại!');
                    } else {
                        DlgUtil.showMsg('Hủy ký số CA thất bại!');
                    }
                }
            }

        }

        function _exportKyCA() {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            var reportCode = 'DUC008_PHIEUNHAPKHO_QD_BTC_A4';
            var parReport = [
                {
                    name: 'dotnhapid',
                    type: 'String',
                    value: _id
                },
                {
                    name: 'RPT_CODE',
                    type: 'String',
                    value: 'DUC008_PHIEUNHAPKHO_QD_BTC_A4'
                }];
            var data = CommonUtil.buildDataCA(101, RestInfo.uuid, {}, reportCode, parReport);
            var notSign = CommonUtil.isSignCANotYet(data);
            if (notSign) {
                DlgUtil.showMsg('Phiếu chưa được ký CA');
                return false;
            }
            CommonUtil.openReportGetXml('window', reportCode, "pdf", parReport);
        }

        function _exportKyCA_TP(reportCode, id) {
            var parReport = [
                {
                    name: 'nhapxuatid',
                    type: 'String',
                    value: id
                },
                {
                    name: 'RPT_CODE',
                    type: 'String',
                    value: reportCode
                }];
            var data = CommonUtil.buildDataCA(101, RestInfo.uuid, {}, reportCode,
                parReport);
            var notSign = CommonUtil.isSignCANotYet(data);
            if (notSign) {
                DlgUtil.showMsg('Phiếu chưa được ký CA');
                return false;
            }
            CommonUtil.openReportGetXml('window', reportCode, "pdf", parReport);
        }

        // tuan sua, them button go duyet
        $("#btnGoNhapkho").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _parId = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            var _loaiphieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            var _maphieu = $("#grdPhieu").jqGrid('getRowData', row).MA;

            DlgUtil.showConfirm("Bạn có muốn hủy phiếu không ?", function (flag) {
                if (flag) {
                    var _par = [_parId];
                    var ctl_sql = "";
                    if (_loaiphieu == 0) {
                        ctl_sql = "DUC01S001.09";
                    } else if (_loaiphieu == 2 && that.opt.ht == "8" && (that.opt.gd == "NHAPKHACTHUOC" || that.opt.gd == "NHAPKHACVATTU") && that.opt.hospitalId == '10284') {
                        ctl_sql = "DUC01S001.BDHN11";
                    } else {
                        ctl_sql = "DUC01S001.10";
                    }
                    var result = jsonrpc.AjaxJson.ajaxCALL_SP_S(ctl_sql,
                        _par.join('$'));
                    var data = $.parseJSON(result);

                    var _succes = data.SUCCESS;
                    if (_succes == '0') {
                        DlgUtil.showMsg(data.MESSAGE);
                        // _loadDSPhieu();

                    } else {
                        if (_loaiphieu == 0 && PHARMA_KETNOI_CONG_BYT == '1') {
                            // thuc hien huy phieu len cong BYT
                            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC_NTHUOC_BYT", _maphieu + '$' + '2');
                            var user = data_ar[0].WS_USR;
                            var pas = data_ar[0].WS_PWD;
                            var url = data_ar[0].WS_URL;
                            var xmldata = data_ar[0].NTBYT;

                            var resultCongBYT = XML_BYT_nhapThuoc(url, user, pas, xmldata);
                            var res = JSON.parse(resultCongBYT);

                            var _error = res.Data.Error.Error_Message;
                            DlgUtil.showMsg("Hủy phiếu thành công");
                            DlgUtil.showMsg("Thông báo Gửi Dữ liệu hủy phiếu sang BYT :" + _error);
                        } else {
                            DlgUtil.showMsg("Hủy phiếu thành công");
                        }

                        _loadDSPhieu();
                    }


                    return true;
                }

            });


            /*			EventUtil.setEvent("xuattra_success",function(e){
                            DlgUtil.close("dlgXuatTra");
                            $('#grdPhieu').jqGrid('setCell',row,7,'Tạo mới');
                            $('#grdPhieu').jqGrid('setCell',row,3,TAOMOI);
                            _loadChiTiet(_id,TAOMOI);
                            _formatRow(row,TAOMOI-1);
                        });
                        EventUtil.setEvent("xuattra_cancel",function(e){
                            DlgUtil.close("dlgXuatTra");
                            _loadDSPhieu();
                        });*/

        });
        //------------------------------ ChuanNT them nut truyen mau hoa hop-----------------------------

        $("#btnMauHoaHop").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            if (_id == "" || _id == null)
                return DlgUtil.showMsg("Chưa chọn phiếu!", undefined, 2000);
            var par = [];
            par.push({"name": "[0]", "value": _id});
            var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC01S002.MAU', par);
            var phieunk = JSON.parse(data_ar);
            var benhnhanid = phieunk[0].BENHNHANID;
            var khambenhid = phieunk[0].KHAMBENHID;

            EventUtil.setEvent("assignTruyenMau_cancel", function (e) {
                DlgUtil.close("divDlgPTruyenMau");
                //_loadDSPhieu();
            });
            var myVar = {khambenh_id: khambenhid, benhnhanid: benhnhanid};
            dlgPopup = DlgUtil.buildPopupUrl("divDlgPTruyenMau", "divDlg", "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenMau_LCI", myVar, "Tạo phiếu truyền máu", 1000, 550);
            DlgUtil.open("divDlgPTruyenMau");

        });

        //------------------------------nhap bu---------------------------------------------------
        //YC nhap bu
        $("#toolbarIdtbYCNhapBu").on("click", function (e) {
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaNhapBu").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaNhapBu").prop("disabled", true);
            });
            /*if(PHARMA_GIAODIEN_NHAP_NCC ='DUC02N001_NhapThuocNCC_BVNT'){
				var myVar={khoid:$("#cboKho").val(),nhapxuatid:0,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,loainhapbu:"YC_NHAPBU"};
				  dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"Yêu cầu nhập bù" + that.opt.title ,1210,610);
				  DlgUtil.open("dlgNhapKho");
			}
			else{
				var myVar={khoid:$("#cboKho").val(),nhapxuatid:0,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,loainhapbu:"YC_NHAPBU"};
				dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC",myVar,"Yêu cầu nhập bù" + that.opt.title ,1200,600);
				DlgUtil.open("dlgNhapKho");
			}*/
            var _loainhapbu = 'YC_NHAPBU';
            var _text = "Yêu cầu nhập bù" + that.opt.title;
            if (that.opt.gd == 'NHAPKHACTHUOC' || that.opt.gd == 'NHAPKHACVATTU') {
                _loainhapbu = 'YC_NHAPKHAC';
                _text = 'Yêu cầu nhập khác'
            }
            if (PHARMA_NHAPBUKHAC_KHONGTHAU == '1') {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: 0,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: _loainhapbu
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, _text, 1200, 600);
                DlgUtil.open("dlgNhapKho");
            } else {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: 0,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: _loainhapbu
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/" + PHARMA_GIAODIEN_NHAP_NCC, myVar, _text, 1210, 700);
                DlgUtil.open("dlgNhapKho");
            }


        });

        //Nhap bu
        $("#toolbarIdtbNhapBu").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var sql_par = ['DUC02N001', _id, 'NHAP'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 0) {
                DlgUtil.showMsg("Đã tạo phiếu nhập bù. Không tiếp tục tạo phiếu nhập bù được");
                return;
            }

            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                $('#grdPhieu').jqGrid('setCell', row, 7, 'Đã kết thúc');
                $('#grdPhieu').jqGrid('setCell', row, 3, KETTHUC);
                _loadChiTiet(_id, KETTHUC);
                _formatRow(row, KETTHUC - 1);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            /*if(PHARMA_GIAODIEN_NHAP_NCC ='DUC02N001_NhapThuocNCC_BVNT'){
				var myVar={khoid:$("#cboKho").val(),nhapxuatid:_id,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,loainhapbu:"NHAPBU"};
				  dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"Nhập bù" + that.opt.title ,1210,610);
				  DlgUtil.open("dlgNhapKho");
			}else{
				var myVar={khoid:$("#cboKho").val(),nhapxuatid:_id,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,loainhapbu:"NHAPBU"};
				dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC",myVar,"Nhập bù" + that.opt.title ,1200,600);
				DlgUtil.open("dlgNhapKho");
			}*/

            if (PHARMA_NHAPBUKHAC_KHONGTHAU == '1') {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: _id,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: "NHAPBU"
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Nhập bù" + that.opt.title, 1200, 600);
                DlgUtil.open("dlgNhapKho");
            } else {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: _id,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: "NHAPBU"
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/" + PHARMA_GIAODIEN_NHAP_NCC, myVar, "Nhập bù" + that.opt.title, 1210, 700);
                DlgUtil.open("dlgNhapKho");
            }


        });

        //Nhap tr
        $("#toolbarIdtbNhapTra").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var sql_par = ['DUC02N001', _id, 'NHAP'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 0) {
                DlgUtil.showMsg("Đã tạo phiếu nhập trả. Không tiếp tục tạo phiếu nhập trả được");
                return;
            }

            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                $('#grdPhieu').jqGrid('setCell', row, 7, 'Đã kết thúc');
                $('#grdPhieu').jqGrid('setCell', row, 3, KETTHUC);
                _loadChiTiet(_id, KETTHUC);
                _formatRow(row, KETTHUC - 1);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });

            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: 'NTDTTHKP',
                loainhapbu: "NHAPBU"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Nhập trả" + that.opt.title, 1210, 620);
            DlgUtil.open("dlgNhapKho");

        });
        //Sua nhap bu
        $("#toolbarIdtbSuaNhapBu").on("click", function (e) {
            tbSuaNhapBuClick();
        });

        function tbSuaNhapBuClick() {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaNhapBu").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaNhapBu").prop("disabled", true);
            });
            //if(PHARMA_GIAODIEN_NHAP_NCC == 'DUC02N001_NhapThuocNCC_BVNT'){
            var _loainhapbu = 'YC_NHAPBU';
            var _text = "Yêu cầu nhập bù" + that.opt.title;
            if (that.opt.gd == 'NHAPKHACTHUOC' || that.opt.gd == 'NHAPKHACVATTU') {
                _loainhapbu = 'YC_NHAPKHAC';
            }
            if (PHARMA_NHAPBUKHAC_KHONGTHAU == '1') {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: _id,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: _loainhapbu
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Nhập bù" + that.opt.title, 1200, 600);
                DlgUtil.open("dlgNhapKho");
            } else {
                var myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: _id,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: _loainhapbu
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/" + PHARMA_GIAODIEN_NHAP_NCC, myVar, "Nhập bù" + that.opt.title, 1210, 700);
                DlgUtil.open("dlgNhapKho");
            }


            //	}
            /*else{
				var myVar={khoid:$("#cboKho").val(),nhapxuatid:_id,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,loainhapbu:"YC_NHAPBU"};
				dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC",myVar,"Nhập bù" + that.opt.title ,1200,600);
				DlgUtil.open("dlgNhapKho");
			}*/


        }

        //hoan tra
//		$("#toolbarIdtbSuaHoanTra").on("click",function(e){
//			var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
//			var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
//			var _doiungid = $("#grdPhieu").jqGrid('getRowData', row).DOIUNGID;
//			EventUtil.setEvent("YCNhap_success",function(e){
//				DlgUtil.close("dlgYCNhap");
//				_loadDSPhieu();
//			});
//			var myVar={loaiKhoId:$("#cboKho").val(),nhapxuatid:_id,loaiGiaoDien:that.opt.gd,hinhThucId:that.opt.ht};
//			dlgPopup=DlgUtil.buildPopupUrl("dlgYCNhap","divDlg","manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc",myVar,"Nhập kho" + that.opt.title,1200,610);
//			DlgUtil.open("dlgYCNhap");
//		});
        //end hoan tra
        //------------------------------xuat huy---------------------------------------------------
        //YC xuat huy
        $("#toolbarIdtbYCXuatHuy").on("click", function (e) {
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatHuy").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatHuy").prop("disabled", true);
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: 0,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "YC_XUATHUY"
            };
            if (that.opt.gd == 'XUATHUHAOCB')
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_HuHaoCheBien", myVar, "YC tính hư hao chế biến", 1200, 600);
            else
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "YC Xuất hủy" + that.opt.title, 1200, 620);
            // dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"YC Xuất hủy" + that.opt.title ,1210,610);

            DlgUtil.open("dlgNhapKho");
        });
        //Xuat huy
        $("#toolbarIdtbXuatHuy").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var sql_par = ['DUC02N001', _id, 'XUAT'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 0) {
                DlgUtil.showMsg("Đã tạo phiếu xuất hủy. Không tiếp tục tạo phiếu xuất hủy được");
                return;
            }

            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                $('#grdPhieu').jqGrid('setCell', row, 7, 'Đã kết thúc');
                $('#grdPhieu').jqGrid('setCell', row, 3, KETTHUC);
                _loadChiTiet(_id, KETTHUC);
                _formatRow(row, KETTHUC - 1);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "XUATHUY"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Xuất hủy" + that.opt.title, 1200, 620);
            //dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"Xuất hủy" + that.opt.title ,1210,610);
            DlgUtil.open("dlgNhapKho");
        });
        //Sua xuat huy
        $("#toolbarIdtbSuaXuatHuy").on("click", function (e) {
            tbSuaXuatHuyClick();
        });

        function tbSuaXuatHuyClick() {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatHuy").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatHuy").prop("disabled", true);
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "YC_XUATHUY"
            };
            if (that.opt.gd == 'XUATHUHAOCB')
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_HuHaoCheBien", myVar, "YC tính hư hao chế biến", 1200, 600);
            else
                dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Xuất hủy" + that.opt.title, 1200, 620);
            //dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"Xuất hủy" + that.opt.title ,1210,610);
            DlgUtil.open("dlgNhapKho");
        }

        //------------------------------khoa giữ thuốc của việt đức 2---------------------------------------------------
        $("#toolbarIdtbKhoaThuoc").on("click", function (e) {
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgkhoathuoc");
                _loadDSPhieu();
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: 0,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "XUATKHOATHUOC"
            };
            if (PHARMA_KHOA_TVT_NEW == '1') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgkhoathuoc", "divDlg",
                    "manager.jsp?func=../duoc/DUC02N001_KHOATHUOCVATTU", myVar, "Khóa giữ thuốc", 1200, 600);
            } else {
                dlgPopup = DlgUtil.buildPopupUrl("dlgkhoathuoc", "divDlg",
                    "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Khóa giữ thuốc", 1200, 600);
            }

            dlgPopup.open("dlgkhoathuoc");
        });

        //------------------------------xuat khac---------------------------------------------------
        //YC xuat khac
        $("#toolbarIdtbYCXuatKhac").on("click", function (e) {
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatKhac").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatKhac").prop("disabled", true);
            });
            var myVar;
            if (PHARMA_XUATKHAC_LYDOXUAT == '1' && that.opt.ht == '8' && that.opt.gd == "XUATKHACTHUOC" && that.opt.LDXUAT != '' && jQuery.type(that.opt.LDXUAT) !== "undefined") {
                myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: 0,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: "YC_XUATKHAC",
                    LYDOXUAT: that.opt.LDXUAT
                };
            } else {
                myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: 0,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: "YC_XUATKHAC",
                    LYDOXUAT: ''
                };
            }

            var _dlgURL = "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC";
            if (PHARMA_XUATKHAC_VATTU == '1' && that.opt.ht == '8' && that.opt.gd == "XUATKHACVATTU") {
                _dlgURL = "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_BDHNI_XKVT";
            }
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", _dlgURL, myVar, "YC Xuất khác" + that.opt.title, 1200, 620);
            //dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"Yc Xuất khác" + that.opt.title ,1210,610);
            DlgUtil.open("dlgNhapKho");
        });
        //Xuat khác
        $("#toolbarIdtbXuatKhac").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var sql_par = ['DUC02N001', _id, 'XUAT'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 0) {
                DlgUtil.showMsg("Đã tạo phiếu xuất khác. Không tiếp tục tạo phiếu xuất khác được");
                return;
            }

            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                $('#grdPhieu').jqGrid('setCell', row, 7, 'Đã kết thúc');
                $('#grdPhieu').jqGrid('setCell', row, 3, KETTHUC);
                _loadChiTiet(_id, KETTHUC);
                _formatRow(row, KETTHUC - 1);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "XUATKHAC"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Xuất khác" + that.opt.title, 1200, 620);
            //dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"Xuất khác" + that.opt.title ,1210,610);
            DlgUtil.open("dlgNhapKho");
        });
        //Sua xuat khac
        $("#toolbarIdtbSuaXuatKhac").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _trangthaiid = $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID;
            if (_trangthaiid == 6) {
                DlgUtil.showMsg("Phiếu đã được duyệt, không thể sửa");
                return;
            }
            tbSuaXuatKhacClick();
        });

        function tbSuaXuatKhacClick(_trangthaiid) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatKhac").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatKhac").prop("disabled", true);
            });
            //var myVar={khoid:$("#cboKho").val(),nhapxuatid:_id,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,loainhapbu:"YC_XUATKHAC"};
            var myVar;
            if (PHARMA_XUATKHAC_LYDOXUAT == '1' && that.opt.ht == '8' && that.opt.gd == "XUATKHACTHUOC" && that.opt.LDXUAT != '' && jQuery.type(that.opt.LDXUAT) !== "undefined") {
                myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: _id,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: "YC_XUATKHAC",
                    LYDOXUAT: that.opt.LDXUAT
                };
            } else {
                myVar = {
                    khoid: $("#cboKho").val(),
                    nhapxuatid: _id,
                    tenkho: $("#cboKho option:selected").text(),
                    kieu: that.opt.gd,
                    loainhapbu: "YC_XUATKHAC",
                    LYDOXUAT: ''
                };
            }

            var _dlgURL = "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC";
            if (PHARMA_XUATKHAC_VATTU == '1' && that.opt.ht == '8' && that.opt.gd == "XUATKHACVATTU") {
                _dlgURL = "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC_BDHNI_XKVT";
            }

            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", _dlgURL, myVar, "Xuất khác" + that.opt.title, 1200, 600);
            //dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"Xuất khác" + that.opt.title ,1210,610);
            DlgUtil.open("dlgNhapKho");
        }

        // end xuat khac
        //------------------------------xuat thieu---------------------------------------------------
        //YC xuat thieu
        $("#toolbarIdtbYCXuatThieu").on("click", function (e) {
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatThieu").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatThieu").prop("disabled", true);
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: 0,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "YC_XUATTHIEU"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "YC Xuất thiếu" + that.opt.title, 1200, 620);
            //dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"YC Xuất thiếu" + that.opt.title ,1210,610);
            DlgUtil.open("dlgNhapKho");
        });
        //Xuat thieu
        $("#toolbarIdtbXuatThieu").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var sql_par = ['DUC02N001', _id, 'XUAT'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 0) {
                DlgUtil.showMsg("Đã tạo phiếu xuất thiếu. Không tiếp tục tạo phiếu xuất thiếu được");
                return;
            }
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                $('#grdPhieu').jqGrid('setCell', row, 7, 'Đã kết thúc');
                $('#grdPhieu').jqGrid('setCell', row, 3, KETTHUC);
                _loadChiTiet(_id, KETTHUC);
                _formatRow(row, KETTHUC - 1);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "XUATTHIEU"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Xuất thiếu" + that.opt.title, 1200, 620);
            //dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"Xuất thiếu" + that.opt.title ,1210,610);
            DlgUtil.open("dlgNhapKho");
        });
        //Sua xuat thieu
        $("#toolbarIdtbSuaXuatThieu").on("click", function (e) {
            tbSuaXuatThieuClick();
        });

        function tbSuaXuatThieuClick() {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatThieu").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaXuatThieu").prop("disabled", true);
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "YC_XUATTHIEU"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Xuất thiếu" + that.opt.title, 1200, 620);
            //dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/"+ PHARMA_GIAODIEN_NHAP_NCC,myVar,"Xuất thiếu" + that.opt.title ,1210,610);
            DlgUtil.open("dlgNhapKho");
        }

        //------------------------------ban thuoc cho khach le va benh nhan---------------------------------------------------
        $("#toolbarIdtbBanThuoc").on("click", function (e) {


            EventUtil.setEvent("banthuocbn", function (e) {
                DlgUtil.close("dlgbanthuoc");
                _loadDSPhieu();
            });
            var myVar = {khoid: $("#cboKho").val()};
            dlgPopup = DlgUtil.buildPopupUrl("dlgbanthuoc", "divDlg",
                "manager.jsp?func=../duoc/DUC34X001_BanThuocChoKhachLe", myVar, "Bán thuốc", 1200, 580);
            if (that.opt.hospitalId == '965') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgbanthuoc", "divDlg",
                    "manager.jsp?func=../duoc/DUC34X001_BanThuocChoKhachLe_BDHCM", myVar, "Bán thuốc", 1200, 580);
            }
            dlgPopup.open("dlgbanthuoc");
        });


        /*	$("#toolbarIdtbPhieuThuBanThuoc").on("click",function(e){
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

                EventUtil.setEvent("banthuocbn",function(e){
                    DlgUtil.close("dlgbanthuoc");
                    _loadDSPhieu();
                });
                var myVar={nhapxuatid:_id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgbanthuoc", "divDlg",
                        "manager.jsp?func=../duoc/DUC34X002_PhieuThuBanThuoc", myVar,"Phiếu thu", 1200, 590);

                dlgPopup.open("dlgbanthuoc");
            });
            */
        // sua phieu thu ban thuoc
        //DucTT20181113
        $("#toolbarIdtbSuaPhieuBanTHuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            if (_kieu == '3') {
                EventUtil.setEvent("banthuocbn", function (e) {
                    DlgUtil.close("dlgbanthuoc");
                    _loadDSPhieu();
                });
                var myVar = {khoid: $("#cboKho").val(), nhapxuatid: _id, kieu: _kieu};
                var title = "Bán thuốc";
                dlgPopup = DlgUtil.buildPopupUrl("dlgbanthuoc", "divDlg",
                    "manager.jsp?func=../duoc/DUC34X001_BanThuocChoKhachLe", myVar, "Bán thuốc", 1200, 580);
                dlgPopup.open("dlgbanthuoc");
            } else if (_kieu == '2') {
                EventUtil.setEvent("trathuoc", function (e) {
                    DlgUtil.close("dlgtrathuoc");
                    _loadDSPhieu();
                });
                var myVar = {nhapxuatid: _id};
                dlgPopup = DlgUtil.buildPopupUrl("dlgtrathuoc", "divDlg",
                    "manager.jsp?func=../duoc/DUC34X003_PhieuTraThuoc", myVar, "Trả thuốc khách lẻ", 1200, 580);
                dlgPopup.open("dlgtrathuoc");
            }
        });
        // xoa phieu thuoc mua ngoai
        /*$("#btnXoaDonMuaNgoai").on("click",function(e){
			DlgUtil
			.showConfirm("Bạn có muốn hủy phiếu thuốc này không ?",
					function(flag) {
						if (flag) {
							var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
							var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
							var _trangthaiid = $("#grdPhieu").jqGrid('getRowData', row).TRANGTHAIID;
							if(_trangthaiid == 6){
								DlgUtil.showMsg("Bạn phải hủy phiếu thu trước khi xóa phiếu!");
							}
							else{
								var _par = [ _id ];
								var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC34X001.07",
										_par.join('$'));
								if (result == '0') {
									DlgUtil.showMsg('Hủy phiếu thuốc lỗi');
								} else {
									DlgUtil.showMsg('Hủy phiếu thuốc thành công');
									_loadDSPhieu();
								}
							}


							return true;
						}
			});

		});*/
        $("#toolbarIdtbTraThuoc").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _xuatid = $("#grdPhieu").jqGrid('getRowData', row).XUATID;

            EventUtil.setEvent("trathuoc", function (e) {
                DlgUtil.close("dlgtrathuoc");
                _loadDSPhieu();
            });
            var myVar = {nhapxuatid: _xuatid};
            dlgPopup = DlgUtil.buildPopupUrl("dlgtrathuoc", "divDlg",
                "manager.jsp?func=../duoc/DUC34X003_PhieuTraThuoc", myVar, "Trả thuốc khách lẻ", 1200, 580);

            dlgPopup.open("dlgtrathuoc");
        });
        //------------------ dự trù thuốc vật tư tiêu hao khoa phòng
        //YC xuat  du tru
        $("#toolbarIdtbYCXuatDTTH").on("click", function (e) {
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaDTTH").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaDTTH").prop("disabled", true);
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: 0,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "YC_XUATDTTHKP"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "YC Xuất dự trù tiêu hao khoa phòng" + that.opt.title, 1200, 620);
            DlgUtil.open("dlgNhapKho");
        });
        //Xuat dự trù
        $("#toolbarIdtbXuatDTTH").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var sql_par = ['DUC02N001', _id, 'XUAT'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 0) {
                DlgUtil.showMsg("Đã tạo phiếu dự trù tiêu hao khoa phòng, Không tiếp tục tạo phiếu được");
                return;
            }

            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                $('#grdPhieu').jqGrid('setCell', row, 7, 'Đã kết thúc');
                $('#grdPhieu').jqGrid('setCell', row, 3, KETTHUC);
                _loadChiTiet(_id, KETTHUC);
                _formatRow(row, KETTHUC - 1);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "XUATDTTHKP"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Xuất dự trù tiêu hao khoa phòng" + that.opt.title, 1200, 620);
            DlgUtil.open("dlgNhapKho");
        });
        //Hoan tra dự trù
        $("#toolbarIdtbHoanTraDTTH").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var sql_par = ['DUC02N001', _id, 'XUAT'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.CHECK", sql_par.join('$'));
            if (ret == 1) {
                DlgUtil.showMsg("Chưa tạo phiếu dự trù, Không hoàn trả phiếu được !");
                return;
            }

            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                $('#grdPhieu').jqGrid('setCell', row, 7, 'Đã kết thúc');
                $('#grdPhieu').jqGrid('setCell', row, 3, KETTHUC);
                _loadChiTiet(_id, KETTHUC);
                _formatRow(row, KETTHUC - 1);
                _loadDSPhieu();
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: "HTDTTHKP",
                loainhapbu: "HTDTTHKP"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Hoàn trả dự trù tiêu hao khoa phòng" + that.opt.title, 1200, 620);
            DlgUtil.open("dlgNhapKho");
        });
        //Sua xuat dự trù
        $("#toolbarIdtbSuaDTTH").on("click", function (e) {
            tbSuaDTTHClick();
        });

        function tbSuaDTTHClick() {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            EventUtil.setEvent("nhapkho_success", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaDTTH").prop("disabled", true);
            });
            EventUtil.setEvent("nhapkho_cancel", function (e) {
                DlgUtil.close("dlgNhapKho");
                _loadDSPhieu();
                $("#toolbarIdtbSuaDTTH").prop("disabled", true);
            });
            var myVar = {
                khoid: $("#cboKho").val(),
                nhapxuatid: _id,
                tenkho: $("#cboKho option:selected").text(),
                kieu: that.opt.gd,
                loainhapbu: "YC_XUATDTTHKP"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Sửa dự trù tiêu hao khoa phòng" + that.opt.title, 1200, 620);
            DlgUtil.open("dlgNhapKho");
        }

        GridUtil.setGridParam("grdPhieu", {
            gridComplete: function () {
                var ids = $("#grdPhieu").getDataIDs();
                for (var i = 0; i < ids.length; i++) {
                    var id = ids[i];
                    var row = $("#grdPhieu").jqGrid('getRowData', id);
                    _formatRow(id, row.TRANGTHAIID - 1);
                    //bachnv them bn ra vien
                    if (PHARMA_HIENTHI_YLENH_BN_RAVIEN == '1' && row.HINHTHUCID == "12" && row.LOAIKEDON == "21") {
//		        			_formatRowBNRaVien(id,row.LOAIKEDON-1)
                        var _icon = '';
                        if (that.opt.imgPath[row.LOAIKEDON - 1] != '')
                            _icon = '<center><img src="../common/image/' + that.opt.imgPath[row.LOAIKEDON - 1] + '" width="15px"></center>';
                        $("#grdPhieu").setRowData(id, {icon2: _icon});
                    }
                    if (that.opt.ht == '12' && that.opt.hospitalId == '1111') {

                        var par = [];
                        par.push({"name": "[0]", "value": row.NHAPXUATID});
                        var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC01S002.GNHT', par);
                        var phieunk = JSON.parse(data_ar);
                        var gnht = phieunk[0].GNHT;
                        if (Number(gnht) > 0) {
//		        				$("#grdPhieu").jqGrid('setRowData', id, "", {
//		        					color : '#33FFD4'
//		        				});
                            $('#grdPhieu').find("tr[id='" + id + "']").find('td').each(function (index, element) {
                                $(element).css('background-color', '#33FFD4');
                            });
                        }
                    }
                    if (that.opt.ht == '13' && that.opt.hospitalId == '50860') {
                        $("#grdPhieu").setCell(id, 'TENBENHNHAN', '', {'font-weight': 'bold'});


                    }
                    if (that.opt.ht == '13' ) {
                    	 if (row.TRANGTHAITIEPNHAN == '1'){
	                    		 $('#grdPhieu').find("tr[id='" + id + "']").find('td').each(function (index, element) {
	                                 $(element).css('background-color', '#33FFD4');
	                             });
	                    		 $('#grdPhieu').find("tr[id='" + id + "']").find('td').attr('title', 'Đã duyệt kế toán');
                    	 }
                    	 

                    }
                    if(PHARMA_SHOW_COLOR_VATTU_BN == '1' && that.opt.ht == '13' ){
                    	var par = [];
                        par.push({"name": "[0]", "value": row.NHAPXUATID});
                        var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC01S002.COUNT.VT', par);
                        var phieunk = JSON.parse(data_ar);
                        var vt = phieunk[0].VT;
                        if (Number(vt) > 0) {
//		        				$("#grdPhieu").jqGrid('setRowData', id, "", {
//		        					color : '#33FFD4'
//		        				});
                            $('#grdPhieu').find("tr[id='" + id + "']").find('td').each(function (index, element) {
                                $(element).css('background-color', '#EAD1DC');
                            });
                        }
                    }
                    if (that.opt.hospitalId == '1022' && (that.opt.ht == '12' || that.opt.ht == '2' || that.opt.ht == '9' || that.opt.ht == '4') &&
                        (that.opt.type == '4' || that.opt.type == '45')) {
                        if (row.DAYEUCAU == 10 && row.TRANGTHAIID == '5') {

                            $('#grdPhieu').find("tr[id='" + id + "']").find('td').each(function (index, element) {
                                $(element).css('background-color', '#33FFD4');
                            });
                            $('#grdPhieu').find("tr[id='" + id + "']").find('td').attr('title', 'Đang soạn phiếu');
                        }
                    }

                    if (PHARMA_CHECK_LIENTHONG_HMIS == '1') {
                        if (row.TRANGTHAIIDHMIS == '8' && (row.TRANGTHAIID == '6' || row.TRANGTHAIID == '7')) {

                            $('#grdPhieu').find("tr[id='" + id + "']").find('td').each(function (index, element) {
                                $(element).css('background-color', '#00ff00');
                            });
                            $('#grdPhieu').find("tr[id='" + id + "']").find('td').attr('title', 'Phiếu gửi HMIS không thành công');
                        }
                    }
                    //BN ra vien
                }

                // ChuanNt them chuot phai copy phieu
                if ((that.opt.gd == 'THUOC'
                        || that.opt.gd == 'VATTU'
                        || that.opt.gd == 'XUATDTTHKP'
                        || that.opt.gd == 'XUATKHACTHUOC'
                        || that.opt.gd == 'XUATKHACVATTU'
                        || that.opt.gd == 'XUATHUYTHUOC'
                        || that.opt.gd == 'XUATHUYVATTU')
                    && that.opt.tt.includes('1')) {
                    var _gridLogId = "grdPhieu";
                    $(".jqgrow", "#" + _gridLogId).contextMenu('contextMenu', {
                        bindings: {
                            'selCopyPhieu': function (t) {

                                var rowId = $(t).attr("id");
                                var rowData = $('#' + _gridLogId).jqGrid('getRowData', rowId);
                                // delLogBed(rowId);

                                if (rowData != null) {
                                    if (rowData.HINHTHUCID == '1'
                                        //|| rowData.HINHTHUCID == '4'
                                        || rowData.HINHTHUCID == '5'
                                        || rowData.HINHTHUCID == '6'
                                    ) {
                                        var _id = rowData.NHAPXUATID;


                                        /*	var myVar={khoid:$("#cboKho").val(),nhapxuatid:_id,tenkho:$("#cboKho option:selected").text(),kieu:that.opt.gd,loainhapbu:"copy"};
											dlgPopup=DlgUtil.buildPopupUrl("dlgNhapKho","divDlg","manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC",myVar,"Copy phiếu nhập hoặc yêu cầu" + that.opt.title ,1200,600);
											DlgUtil.open("dlgNhapKho");*/


                                        var myVar = {
                                            khoid: $("#cboKho").val(),
                                            nhapxuatid: _id,
                                            tenkho: $("#cboKho option:selected").text(),
                                            kieu: that.opt.gd,
                                            copy: 'true'
                                        };
                                        dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "Copy Nhập kho từ NCC" + that.opt.title, 1200, 620);
                                        DlgUtil.open("dlgNhapKho");
                                    } else if (rowData.HINHTHUCID == '4') {
                                        var _id = rowData.NHAPXUATID;
                                        var myVar = {
                                            khoid: $("#cboKho").val(),
                                            nhapxuatid: _id,
                                            tenkho: $("#cboKho option:selected").text(),
                                            kieu: that.opt.gd,
                                            loainhapbu: "YC_XUATDTTHKP",
                                            copy: 'true'
                                        };
                                        dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "YC Xuất dự trù tiêu hao khoa phòng" + that.opt.title, 1200, 620);
                                        DlgUtil.open("dlgNhapKho");
                                    } else if (rowData.HINHTHUCID == '7') {
                                        var _id = rowData.NHAPXUATID;
                                        var myVar = {
                                            khoid: $("#cboKho").val(),
                                            nhapxuatid: _id,
                                            tenkho: $("#cboKho option:selected").text(),
                                            kieu: that.opt.gd,
                                            loainhapbu: "YC_XUATHUY",
                                            copy: 'true'
                                        };
                                        dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "YC Xuất hủy" + that.opt.title, 1200, 620);
                                        DlgUtil.open("dlgNhapKho");
                                    } else if (rowData.HINHTHUCID == '8') {
                                        var _id = rowData.NHAPXUATID;
                                        var myVar = {
                                            khoid: $("#cboKho").val(),
                                            nhapxuatid: _id,
                                            tenkho: $("#cboKho option:selected").text(),
                                            kieu: that.opt.gd,
                                            loainhapbu: "YC_XUATKHAC",
                                            copy: 'true'
                                        };
                                        dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKho", "divDlg", "manager.jsp?func=../duoc/DUC02N001_NhapThuocNCC", myVar, "YC Xuất khác" + that.opt.title, 1200, 620);
                                        DlgUtil.open("dlgNhapKho");
                                    } else if (rowData.HINHTHUCID == '2'
                                        || rowData.HINHTHUCID == '9'
                                    ) {

                                        var _id = rowData.NHAPXUATID;
                                        var _doiungid = rowData.DOIUNGID;
                                        EventUtil.setEvent("YCNhap_success", function (e) {
                                            DlgUtil.close("dlgYCNhap");
                                            _loadDSPhieu();
                                        });
                                        var ht = 0;
                                        var kieu = rowData.KIEU;


                                        if (kieu == 2) {
                                            ht = 1;
                                        } //hoan tra
                                        var myVar = {
                                            loaiKhoId: $("#cboKho").val(),
                                            nhapxuatid: _id,
                                            loaiGiaoDien: that.opt.gd,
                                            hinhThucId: that.opt.ht,
                                            loaiphieu: that.opt.lp,
                                            hoantra: ht,
                                            copy: '1'
                                        };


                                        if (PHARMA_GIAODIEN_NHAP_NCC == 'DUC02N001_NhapThuocNCC_BVNT') {
                                            dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_NGT_YeuCauNhapThuoc", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 610);
                                            DlgUtil.open("dlgYCNhap");
                                        } else if (PHARMA_DUTRUKHO_NEW == '1') {
                                            dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc_BDHNI", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 610);
                                            DlgUtil.open("dlgYCNhap");
                                        } else {
                                            dlgPopup = DlgUtil.buildPopupUrl("dlgYCNhap", "divDlg", "manager.jsp?func=../duoc/DUC11N001_YeuCauNhapThuoc", myVar, "Yêu cầu dự trù" + that.opt.title, 1200, 630);
                                            DlgUtil.open("dlgYCNhap");
                                        }
                                    } else if (rowData.HINHTHUCID == '15') {
                                        if (rowData.KIEU == '3') {
                                            EventUtil.setEvent("banthuocbn", function (e) {
                                                DlgUtil.close("dlgbanthuoc");
                                                _loadDSPhieu();
                                            });
                                            var myVar = {
                                                khoid: $("#cboKho").val(),
                                                nhapxuatid: rowData.NHAPXUATID,
                                                kieu: rowData.KIEU,
                                                copy: '1'
                                            };
                                            var title = "Bán thuốc";
                                            dlgPopup = DlgUtil.buildPopupUrl("dlgbanthuoc", "divDlg",
                                                "manager.jsp?func=../duoc/DUC34X001_BanThuocChoKhachLe", myVar, "Bán thuốc", 1200, 580);
                                            dlgPopup.open("dlgbanthuoc");
                                        } else if (rowData.KIEU == '2') {
                                            EventUtil.setEvent("trathuoc", function (e) {
                                                DlgUtil.close("dlgtrathuoc");
                                                _loadDSPhieu();
                                            });
                                            var myVar = {nhapxuatid: rowData.NHAPXUATID, kieu: rowData.KIEU, copy: '1'};
                                            dlgPopup = DlgUtil.buildPopupUrl("dlgtrathuoc", "divDlg",
                                                "manager.jsp?func=../duoc/DUC34X003_PhieuTraThuoc", myVar, "Trả thuốc khách lẻ", 1200, 580);
                                            dlgPopup.open("dlgtrathuoc");
                                        }
                                    } else {
                                        DlgUtil.showMsg("Phiếu không được phép sao chép !");
                                        return;
                                    }

                                }
                            }
                        },
                        onContextMenu: function (event, menu) {
                            var rowId = $(event.target).parent("tr").attr("id");
                            var grid = $("#" + _gridLogId);
                            grid.setSelection(rowId);
                            GridUtil.unmarkAll(_gridLogId);
                            GridUtil.markRow(_gridLogId, rowId);
                            return true;
                        }

                    });

                    EventUtil.setEvent("nhapkho_cancel", function (e) {
                        DlgUtil.close("dlgNhapKho");
                        _loadDSPhieu();
                    });
                    EventUtil.setEvent("nhapkho_success", function (e) {
                        DlgUtil.close("dlgNhapKho");
                        _loadDSPhieu();

                    });
                }
            }
        });
        //------------------------------xuat khac cho y lenh linh thuoc---------------------------------------------------
        //YC nhap
        $("#toolbarIdtbXuatYLenhLT").on("click", function (e) {
            EventUtil.setEvent("dlgXuatKhac", function (e) {
                DlgUtil.close("dlgXuatKhac");
                _loadDSPhieu();
            });
            var subdept = _opt.subdept;
            var dept = _opt.dept;
            var myVar = {
                loaiKhoId: $("#cboKho").val(),
                nhapxuatid: 0,
                khoa_id: dept,
                phong_id: subdept,
                loaiGiaoDien: that.opt.gd,
                hinhThucId: that.opt.ht
            };

            dlgPopup = DlgUtil.buildPopupUrl("dlgXuatKhac", "divDlg", "manager.jsp?func=../duoc/DUC12X001_XuatKhacChoYLenhLinhThuoc", myVar, "Yêu cầu xuất khác cho y lệnh lĩnh thuốc", 1200, 610);
            DlgUtil.open("dlgXuatKhac");


        });
        //YC hoan tra
        $("#toolbarIdtbHTraYLenhLT").on("click", function (e) {
            EventUtil.setEvent("dlgHoanTraYL", function (e) {
                DlgUtil.close("dlgHoanTraYL");
                _loadDSPhieu();
            });
            var subdept = _opt.subdept;
            var dept = _opt.dept;
            var myVar = {
                loaiKhoId: $("#cboKho").val(),
                nhapxuatid: 0,
                khoa_id: dept,
                phong_id: subdept,
                loaiGiaoDien: that.opt.gd,
                hinhThucId: that.opt.ht
            };

            dlgPopup = DlgUtil.buildPopupUrl("dlgHoanTraYL", "divDlg", "manager.jsp?func=../duoc/DUC13N001_NhapHoanTraChoYLenhLinhThuoc", myVar, "Yêu cầu hoàn trả cho y lệnh lĩnh thuốc", 1200, 610);
            DlgUtil.open("dlgHoanTraYL");


        });
        $("#toolbarIdtbTruyenMau").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var par = [];
            par.push({"name": "[0]", "value": _id});
            var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC01S002.MAU', par);
            var phieunk = JSON.parse(data_ar);
            var benhnhanid = phieunk[0].BENHNHANID;
            var khambenhid = phieunk[0].KHAMBENHID;

            EventUtil.setEvent("assignTruyenMau_cancel", function (e) {
                DlgUtil.close("divDlgPTruyenMau");
                //_loadDSPhieu();
            });
            var myVar = {khambenh_id: khambenhid, benhnhanid: benhnhanid};
            dlgPopup = DlgUtil.buildPopupUrl("divDlgPTruyenMau", "divDlg", "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenMau_LCI", myVar, "Tạo phiếu truyền máu", 1000, 550);
            DlgUtil.open("divDlgPTruyenMau");


        });

        //Sua
        $("#toolbarIdtbSuaYLenhLT").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
            var _kieu = $("#grdPhieu").jqGrid('getRowData', row).KIEU;
            EventUtil.setEvent("dlgXuatKhac", function (e) {
                DlgUtil.close("dlgXuatKhac");
                _loadDSPhieu();
            });
            EventUtil.setEvent("dlgHoanTraYL", function (e) {
                DlgUtil.close("dlgHoanTraYL");
                _loadDSPhieu();
            });
            var subdept = 0;
            var dept = 0;

            var sql_par = [_id];
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC12X001.05", sql_par
                .join('$'));


            if (data_ar != null && data_ar.length > 0) {
                var row = data_ar[0];
                dept = row.KHOAID;
                subdept = row.PHONGID;
            }
            if (_kieu == 3) {
                var myVar = {
                    loaiKhoId: $("#cboKho").val(),
                    nhapxuatid: _id,
                    khoa_id: dept,
                    phong_id: subdept,
                    loaiGiaoDien: that.opt.gd,
                    hinhThucId: that.opt.ht
                };

                dlgPopup = DlgUtil.buildPopupUrl("dlgXuatKhac", "divDlg", "manager.jsp?func=../duoc/DUC12X001_XuatKhacChoYLenhLinhThuoc", myVar, "Yêu cầu xuất khác cho y lệnh lĩnh thuốc", 1200, 610);
                DlgUtil.open("dlgXuatKhac");
            } else {
                var myVar = {
                    loaiKhoId: $("#cboKho").val(),
                    nhapxuatid: _id,
                    khoa_id: dept,
                    phong_id: subdept,
                    loaiGiaoDien: that.opt.gd,
                    hinhThucId: that.opt.ht
                };

                dlgPopup = DlgUtil.buildPopupUrl("dlgHoanTraYL", "divDlg", "manager.jsp?func=../duoc/DUC13N001_NhapHoanTraChoYLenhLinhThuoc", myVar, "Yêu cầu hoàn trả cho y lệnh lĩnh thuốc", 1200, 610);
                DlgUtil.open("dlgHoanTraYL");

            }

        });
        _onClickButtons();//DucTT20181029

        $("#toolbarIdtbLCD").on("click", function () {
            var param = "";
            window.open('manager.jsp?func=../duoc/DUC053_GOIKHAM_LCD55&type=5&showMode=dlg', '', 'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
        });

        $("#toolbarIdtbGoiBenhNhan").on("click", function (e) {
            var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn bệnh nhân!", undefined, 2000);

            var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC053.UDGK.01", _id));

            if (ret > 0) {
                var textSpeed = "Mời bệnh nhân " + $("#grdPhieu").jqGrid('getRowData', row).TENBENHNHAN + " nhận đơn thuốc";
                goiKhamGG(textSpeed, HIS_API_MODE, HIS_API_TIMEOUT);
            } else {
                DlgUtil.showMsg('Gọi không thành công.');
            }

        });
    }


    //private function
    function GetThamSoKhoiTao() {
        var param = $(location).attr('href') + '$';
        var ketqua = jsonrpc.AjaxJson.ajaxCALL_SP_S(
            "DUC_GETTHAMSOKT", param);
        if (ketqua == '' || ketqua == null) return;
        var obj = jQuery.parseJSON(ketqua);
        $("#cboKho").val(obj.TENKHO);
//		$("#txtNgayBD").val(obj.TUNGAY);
//		$("#txtNgayKT").val(obj.DENNGAY);
        $("#cboLoaiPhieu").val(obj.LOAIPHIEU);
        $("#cboHinhThuc").val(obj.HINHTHUC);
        $("#cboTrangThaiDuyet").val(obj.TRANGTHAI);

    }

    function formatNumber(nStr) {
        nStr += '';
        x = nStr.split('.');
        x1 = x[0];
        x2 = x.length > 1 ? ',' + x[1] : '';
        var rgx = /(\d+)(\d{3})/;
        while (rgx.test(x1)) {
            x1 = x1.replace(rgx, '$1' + '.' + '$2');
        }
        return x1 + x2;
    }

    function doViewReport(fileType, reportCode, _param) {

        // Something you want delayed.
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": reportCode});
        var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D044.19", sql_par);
        var data_ar_af = JSON.parse(data_ar);
        var arrayLength = data_ar_af.length;
        for (var i = 0; i < arrayLength; i++) {
            var iframe = document.getElementById("ifmXLS");
            var report_url = window.location.protocol + "//" + window.location.host + "/" + "dreport/";
//				var report_url=  window.location.protocol + "//" +"baocao"+ window.location.host + "/" + "dreport/";
            if (report_url == '') report_url = "../";
            if (fileType == 'grid') {
                document.getElementById("ifmXLS").src = report_url + "report/parameter/ParamBuilder?report_id=" + data_ar_af[0].REPORT_ID + "&filetype=" + fileType + "&reportParam=" + getParamToServer(_param) + "&db_name=" + DATA_DB_NAME + "&db_schema=" + DB_SCHEMA_NAME;
            } else {
                window.open(report_url + "report/parameter/ParamBuilder?report_id=" + data_ar_af[0].REPORT_ID + "&filetype=" + fileType + "&reportParam=" + getParamToServer(_param), '_blank');
            }
            if (navigator.userAgent.indexOf("MSIE") > -1 && !window.opera) {
                iframe.onreadystatechange = function () {
                    if (iframe.readyState == "complete") {
                        $("#dLoading").addClass("hidden");
                    }
                };
            } else {
                iframe.onload = function () {
                    $("#dLoading").addClass("hidden");
                };
            }

            //var winReport = window.open(window.location.protocol + "//" + window.location.host + "/vnpthis/report/viewReport.jsp", '_blank');
            //winReport.close();
        }

    }

    function getParamToServer(_param) {
        var par_data = JSON.stringify(_param);
        var par_str = window.btoa(unescape(encodeURIComponent(par_data))); //base64.encode(par_data);
        console.log("par_str11=" + par_str);
        return par_str;
    }

//	function checkRole(){
//		var _parPQ = 'DUC01S002_PhieuYeuCau' +'%ht='+that.opt.ht+ '$';
//		  var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.PQSCREEN.03", _parPQ	);
//		    for(var i =0; i< result.length ;i++){
//		    	if (result[i].ROLES =='1')
//		    		$('#'+result[i].ELEMENT_ID).show();
//		    	if (result[i].ROLES  =='0'||result[i].ROLES  =='')
//		    		$('#'+result[i].ELEMENT_ID).hide();
//		    }
//	};
    function checkRole(control) {
//		that.opt.ht
        var _parPQ = 'DUC01S002_PhieuYeuCau' + '%ht=' + that.opt.ht + '$';
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.PQSCREEN.03", _parPQ);
        for (var i = 0; i < result.length; i++) {
            if (result[i].ELEMENT_ID == control) {
                if (result[i].ROLES == '1') {
//						$('#'+result[i].ELEMENT_ID).show();
                    $('#' + result[i].ELEMENT_ID).prop("disabled", false);
                }
                if (result[i].ROLES == '0' || result[i].ROLES == '') {
//						$('#'+result[i].ELEMENT_ID).hide();
                    $('#' + result[i].ELEMENT_ID).prop("disabled", true);
                }
            }
        }
    };

    function checkRolePhieuIn() {
//		var _parPQ = 'DUC01S002_PhieuYeuCau'+'%lp='+that.opt.lp+'&'+'%ht='+that.opt.ht+'&'+'%type='+that.opt.type+'$';
//		  var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC81S001.04", _parPQ);
//		    for(var i =0; i< result.length ;i++){
//		    	if(result[i].CHECKCN =='1')
//		    		$('#'+result[i].PHIEUINID).show();
//		    	else $('#'+result[i].PHIEUINID).hide();
//		    }
        var report_url = window.location.protocol + "//" + window.location.host;
        var link = window.location.href;
        var ssid = link.indexOf("&ssid");
        var linkcat = link.slice(report_url.length, link.length);
        var _parPQ = linkcat + '$';
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC81S001.04", _parPQ);
        for (var i = 0; i < result.length; i++) {
            $('#' + result[i].PHIEUINID).hide();
        }

    };

    function checkAnGia(_hinhthuc) {
        var _par = ['PHARMA_GD_AN_GIA_TVT'];
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            _par.join('$'));
        if (typeof result === "undefined") return 0;
        var hinhthuc = result.split(",");
        if (hinhthuc.indexOf(_hinhthuc) != -1) return 1;
        return 0;
    };

//	var par =[] ;
//	par.push({"name":"[0]","value": ""} );
//	var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC80S001_01', par);
//	var data_json = JSON.parse(data_ar);


    function loadKho() {

        var sql_par = that.opt.lk + '$' + $("#txtTimKho").val().trim() + '$';
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O(
            "DUC01S002.DSKHO4", sql_par);
        ComboUtil.getComboTag("cboKho", "DUC01S002.DSKHO4",
            sql_par, "", "", "sp", '', function () {
            });

    };
    $('#txtTimKho').on('change', function (e) {
        loadKho();
    });


    //SONDN
//======== START SU KIEN CHO BV BACH MAI 2;
    $("#btnGOITIEP5").on("click", function () {
        _goisttbm2("5", "1"); 					// call from cominf;
    });

    $("#btnGOILAI5").on("click", function () {
        _goisttbm2("5", "2");
    });

    $("#btnLCDNHO5").on("click", function () {
        var param = "";
        window.open('manager.jsp?func=../ngoaitru/NGT02K053_VPI_LCDBM55&type=5&showMode=dlg', '', 'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
    });

//$("#btnDSGOILAI5").on("click", function(){
//var myVar = {
//		phongid : _opts.phongid
//};
//dlgPopup=DlgUtil.buildPopupUrl("dlgCV","divDlg","manager.jsp?func=../ngoaitru/NGT02K053_BM2_BNLOHEN"
//		,myVar,"Danh sách bệnh nhân gọi lại",750,400);
//DlgUtil.open("dlgCV");
//
//});
//
//EventUtil.setEvent("evt_kios_bnlohen", function(e) {
//if(typeof(e) != 'undefined'){
//	DlgUtil.close("dlgCV");
////	DlgUtil.showMsg("Đã gọi lại bệnh nhân " + e.tenbenhnhan);
//	$("#txtID5").val(e.id);
//	_goisttbm2("2");
//}
//});
//========= END SU KIEN CHO BV BACH MAI 2
// END SONDN
    /*Begin: DucTT20181030*/
    var isBienBanKiemNhapThuocThua = false;

    function _initBienBanKiemNhapThuocThua(ctl_ar) {
        var str = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'PHIEUIN_BIENBANKIEMNHAPTHUOCTHUA');
        if (str == '0')
            return ctl_ar;
        if (!ctl_ar)
            return ctl_ar;
        var childs;
        var child;
        for (var i in ctl_ar) {
            if (ctl_ar[i].id != 'btnPrint')
                continue;
            childs = ctl_ar[i].children;
            child = {id: 'print12', icon: 'print', text: 'Biên bản kiểm nhập thuốc thừa', hlink: '#'};
            childs.push(child);
            ctl_ar[i].children = childs;
            break;
        }
        isBienBanKiemNhapThuocThua = true;
        return ctl_ar;
    }

    function _showBienBanKiemNhapThuocThua() {
        if (!isBienBanKiemNhapThuocThua)
            return;
        if (that.opt.lp.includes('2') && that.opt.ht.includes('12'))
            $("#toolbarIdprint12").show();
        else
            $("#toolbarIdprint12").hide();
    }

    function _onClickButtons() {
        _clickBienBanKiemNhapThuocThua();
    }

    function _clickBienBanKiemNhapThuocThua() {
        if (isBienBanKiemNhapThuocThua) {
            $("#toolbarIdprint12").on("click", function (e) {
                var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
                var nhapXuatID = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;
                var parNhapXuatID = {name: 'nhapxuatid', type: 'String', value: nhapXuatID};
                var parLoaiID = {name: 'loaiid', type: 'String', value: null};
                var loaiIdArray = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO('KHA.000001', [{
                    "name": "[0]",
                    "value": nhapXuatID
                }]));
                var loaiId;
                var parAlls;
                for (var i in loaiIdArray) {
                    loaiId = loaiIdArray[i].LOAIID;
                    parLoaiID.value = loaiId;
                    parAlls = [parNhapXuatID, parLoaiID];
                    openReport('window', "DUC_KIEMNHAPTHUOCTHUA", "pdf", parAlls);
                }
            });
            return;
        }
        $("#toolbarIdprint12").off("click");
    }

    /*End: DucTT20181030*/

    /*Begin: DucTT20181105*/
    function _stringToList(str) {
        if (!str)
            return null;
        var arr = str.replace(" ", "").split(",");
        if (!arr)
            return null;
        var obj = {};
        for (var i in arr) {
            obj[arr[i]] = arr[i];
        }
        return obj;
    }

    function _showHuyGuiPhieu(objPhieu) {
        $("#btnHuyGuiDuyet").hide();
        if (!objPhieu)
            return;
        if (!that.hinhThuc)
            return;
        if ((that.hinhThuc.hasOwnProperty("15") ||
                that.hinhThuc.hasOwnProperty("2") ||
                that.hinhThuc.hasOwnProperty("4") ||
                (that.hinhThuc.hasOwnProperty("9") && that.opt.hospitalId != '957') ||
                that.hinhThuc.hasOwnProperty("5") ||
                that.hinhThuc.hasOwnProperty("6") ||
                that.hinhThuc.hasOwnProperty("7") ||
                that.hinhThuc.hasOwnProperty("8"))
            && objPhieu.TRANGTHAIID == "5")
            $("#btnHuyGuiDuyet").show();
        checkRole('btnHuyGuiDuyet');
    }

    function _clickHuyGuiDuyet() {
        $("#btnHuyGuiDuyet").on("click", function (e) {
            var rowIndex = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
            var row = $("#grdPhieu").jqGrid('getRowData', rowIndex);
            var params = [row.NHAPXUATID];
            if (that.hinhThuc.hasOwnProperty("2") || that.hinhThuc.hasOwnProperty("9") || that.hinhThuc.hasOwnProperty("4")
                || that.hinhThuc.hasOwnProperty("5") || that.hinhThuc.hasOwnProperty("6") || that.hinhThuc.hasOwnProperty("7") || that.hinhThuc.hasOwnProperty("8")) {
                var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01S002.HUYCK01", params.join('$')));
                if (ret.SUCCESS > 0) {
                    DlgUtil.showMsg("Hủy gửi duyệt thành công");
                    _loadDSPhieu();
                } else {
                    DlgUtil.showMsg(ret.MESSAGE);
                }
            } else {
                var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.0001", params.join('$'));
                if (result == '1') {
                    DlgUtil.showMsg('Huỷ gửi duyệt thành công.');
                    _loadDSPhieu();
                    return;
                }
                DlgUtil.showMsg('Huỷ gửi duyệt thất bại.');
            }

        });
    }

    $("#btnDuyetDT").click(function () {
        duyetDonThuoc();
    });
    
    $("#btnHuyGiuAo").on("click", function (e) {
        var rowIndex = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
        var row = $("#grdPhieu").jqGrid('getRowData', rowIndex);
        var params = [row.NHAPXUATID];
        
        var ret = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01.HUYGIUAO01", params.join('$')));
        if (ret.SUCCESS > 0) {
            DlgUtil.showMsg("Hủy giữ ảo thành công");
            _loadDSPhieu();
        } else {
            DlgUtil.showMsg(ret.MESSAGE);
        }

    });

    function duyetDonThuoc() {

        var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
        var _id = $("#grdPhieu").jqGrid('getRowData', row).NHAPXUATID;

        var maubenhphamid = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_CHECKDT_02", _id);

        if (Number(maubenhphamid) == 0) {
            DlgUtil.showMsg("Chưa chọn phiếu");
            return;
        }

        var checkdt = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC_CHECKDT_01", maubenhphamid);
        if (checkdt == '0') {
            DlgUtil.showMsg("Kiểm tra lại trạng thái thu tiền");
            return;
        }

        var _duyet = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI_DUYET_BANTHUOC', 'DUYET_BANTHUOC$' + maubenhphamid);
        // L2PT-24280 start
        var objLog = new Object();
        objLog.DONTHUOCID = maubenhphamid + "";
        objLog.LOAI = "0";
        objLog.TRANGTHAI = "0";
        objLog.GHICHU = "Duyệt bán thuốc";
        objLog.KETQUADUYET = _duyet + "";
        objLog.KETQUA = "";
        // L2PT-24280 end
        var par_VPI_XACNHAN_BANTHUOC = ['VPI_XACNHAN_BANTHUOC'];
        VPI_XACNHAN_BANTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",
            par_VPI_XACNHAN_BANTHUOC.join('$'));
        if (_duyet && _duyet > 0) {
            // L2PT-24280 start
            objLog.TRANGTHAI = "1";
            objLog.GHICHU = objLog.GHICHU + ";Duyệt thành công";
            // L2PT-24280 end
            DlgUtil.showMsg("Duyệt thành công");
            if (VPI_XACNHAN_BANTHUOC == 1) {
                // L2PT-24280 start
                var _ketqua = "";
                try {
                    _ketqua = xacNhanBanThuoc(maubenhphamid, 'Ins', _khoid); //L2PT-22913 : truyền khoid vào hàm xác nhận bán thuốc
                } catch (err) {
                    _ketqua = err.message;
                    DlgUtil.showMsg("Có lỗi xảy ra: " + err.message);
                }
                objLog.LOAI = "1";
                objLog.TRANGTHAI = "2";
                objLog.KETQUA = _ketqua;
                objLog.GHICHU = objLog.GHICHU + ";Gửi xác nhận";
                // L2PT-24280 end
            }
            _loadDSPhieu();
        } else {
            DlgUtil.showMsg("Duyệt không thành công");
        }
        // L2PT-24280 start
        var log_dt = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.GHILOG.DONTHUOC', JSON.stringify(objLog));
        if (log_dt == -1) {
            DlgUtil.showMsg("Cập nhật log duyệt/ gửi đơn thuốc không thành công");
        }
        // L2PT-24280 end
    }

    /*End: DucTT20181105*/

    /*Begin: BACHNV20191022*/
    function checkBNRaVien() {
        var tooltip = '';
        var rowIds = $('#grdThuoc').jqGrid('getDataIDs');
        for (i = 0; i < rowIds.length; i++) {
            rowData = $('#grdThuoc').jqGrid('getRowData', rowIds[i]);
            var _color = '#FF9900';
            if (rowData['CHOLANHDAODUYET'] == '1') {
                $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').each(function (index, element) {
                    $(element).css('background-color', _color);
                    $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', rowData['CHUY']);
                });

            }
            if (canhBao.indexOf(rowData["LOAI"]) != -1) {

                $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').each(function (index, element) {
                    $(element).css('background-color', _color);
                    $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', rowData['TENLOAI']);
                });
                canhBaoloaithuoc = rowData['TENLOAI'];
                tooltip = rowData['TENLOAI'] + '+';
            }
            var cauhinhbs = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", ['PHARMA_DTBS_TVT_NGOAI_DM'].join('$'));
            if ((that.hinhThuc == "9" || that.hinhThuc == "2") && cauhinhbs == '1')
                CHBoSung = '1';

            if (rowData["THUOCKHOYC"] == '') {
                tooltip = tooltip + 'Thuốc không có trong kho lập';
                $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').each(function (index, element) {
                    $(element).css('background-color', _color);
                    $('#grdThuoc').find("tr[id='" + rowIds[i] + "']").find('td').attr('title', tooltip);
                });
            }
        }
    }

    /*Begin: BACHNV20191022*/
    function replaceStrtoNum(strSoluong) {
        return Number(strSoluong.replace(/[^0-9\.]+/g, ""));
    };
    function subtractdays(str1, str2, format, time) {
        var fromDate = moment(str1, format);
        var toDate = moment(str2, format);
        if (typeof time == 'undefined') {
            time = 'seconds';
        }
        var diff = fromDate.diff(toDate, time);
        return diff;
    }
    function _formatRowColor(str1) {
        var d = new Date();

        var _month = (d.getMonth() + 1);
        var _date = d.getDate();
        if (_month < 10) {
            _month = '0' + _month;
        }
        if (_date < 10) {
            _date = '0' + _date;
        }

        var strDate = _date + "/" + _month + "/" + d.getFullYear();
        var _datenow = moment(strDate, 'DD/MM/YYYY');
        var row = $("#grdPhieu").jqGrid('getGridParam', 'selrow');
        var _data = $("#grdPhieu").jqGrid('getRowData', row);

        var rowIds = $('#grdThuoc').jqGrid('getDataIDs');

        for (var i = 1; i <= rowIds.length; i++) {
            var rowData = $('#grdThuoc').jqGrid('getRowData', i);
            var hsd = rowData['HANSUDUNG'];
            var _hsd = moment(hsd, 'DD/MM/YYYY');
            var _color = '';

            if (0 < Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) && Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) < 91 && _data.TRANGTHAIID == '5' && str1 == 1) {

                _color = '#FF0000';

            }else if(0 < Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) && Number(subtractdays(_hsd, _datenow, 'DD/MM/YYYY', 'days')) < 91
            		&& (_data.TRANGTHAIID == '5' ||  _data.TRANGTHAIID == '6') && str1 == 2){
            	_color = '#FF0000';
            }
            
            
            $("#grdThuoc").find("tr[id='" + i + "']").find('td').each(function (index, element) {
                $(element).css({'background-color': _color});

            });
        }
    }
}
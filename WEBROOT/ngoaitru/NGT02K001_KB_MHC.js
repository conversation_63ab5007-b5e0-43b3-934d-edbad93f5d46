var ctl_ar = [
    {
        type: 'buttongroup', id: 'btnPrintCa', icon: 'print', text: 'In ký số',
        children: [
            {id: 'group_0_2_CA', icon: 'print', text: 'Gi<PERSON><PERSON> chuyển viện', hlink: '#'},
            {id: 'group_0_3_CA', icon: 'print', text: 'Giấy hẹn khám lại', hlink: '#'},
            {id: 'group_0_1_CA', icon: 'print', text: 'Giấy ra viện', hlink: '#'},
            {id: 'print_PKBVV_CA', icon: 'print', text: 'Phiếu khám bệnh vào viện', hlink: '#'},
            {id: 'group_0_PKchuyenkhoa_CA', icon: 'print', text: 'Phiếu khám chuyên khoa (Chỉ định)', hlink: '#'},
            {id: 'group_0_NHBHYT_CA', icon: 'print', text: 'Phiếu nghỉ ốm, giấy nghỉ hưởng BHXH', hlink: '#'},
            {id: 'group_0_TTPT_KHOADY_CA', icon: 'print', text: 'Phiếu PTTT khoa đông y', hlink: '#'}
        ]
    }, {
        type: 'buttongroup', id: 'btnPrint', icon: 'print', text: 'In ấn'
        , children: [
            {id: 'group_0', icon: 'print', text: 'IN PHIẾU', hlink: '#', group: true}
            , {id: 'group_0_1', icon: 'print', text: 'Giấy ra viện', hlink: '#'}
            , {id: 'group_0_1_doc', icon: 'print', text: 'Giấy ra viện Doc', hlink: '#'}
            , {id: 'group_0_2', icon: 'print', text: 'Giấy chuyển viện', hlink: '#'}
            , {id: 'group_0_3', icon: 'print', text: 'Giấy hẹn khám', hlink: '#'}
            , {id: 'group_0_31', icon: 'print', text: 'Giấy hẹn khám doc', hlink: '#'}
            , {id: 'group_0_4', icon: 'print', text: 'Bảng kê', hlink: '#'}
            , {id: 'group_0_7', icon: 'print', text: 'Bảng kê bhyt', hlink: '#'}
            , {id: 'group_0_5', icon: 'print', text: 'Bảng kê VT hao phí', hlink: '#'}
            , {id: 'group_0_6', icon: 'print', text: 'Phiếu điều trị', hlink: '#'}
            , {id: 'group_2_1', icon: 'print', text: 'Phiếu chỉ định CLS chung', hlink: '#'}
            , {id: 'group_2_2', icon: 'print', text: 'Đơn thuốc', hlink: '#'}
            , {id: 'group_2_2_1', icon: 'print', text: 'Đơn thuốc không thuốc', hlink: '#'}
            , {id: 'group_2_5', icon: 'print', text: 'Phiếu khám bệnh vào viện', hlink: '#'}
            , {id: 'group_2_6', icon: 'print', text: 'In tờ điều trị', hlink: '#'}
            , {id: 'group_2_7', icon: 'print', text: 'In bệnh lịch', hlink: '#'}
            , {id: 'group_2_8', icon: 'print', text: 'In bệnh án', hlink: '#'}
            , {id: 'group_2_9', icon: 'print', text: 'In phiếu đánh giá ban đầu bệnh nhân', hlink: '#'}
            , {id: 'group_2_10', icon: 'print', text: 'In phiếu sàng lọc và đánh giá dinh dưỡng', hlink: '#'}
            , {id: 'group_2_11', icon: 'print', text: 'In các phiếu vào viện', hlink: '#'}
            , {id: 'group_2_12', icon: 'print', text: 'In Xét nghiệm chung', hlink: '#'}
            , {id: 'group_2_13', icon: 'print', text: 'In giấy nhận trả phim x-quang', hlink: '#'}
            , {id: 'group_2_14', icon: 'print', text: 'In tách bảng kê theo khoa', hlink: '#'}
            , {id: 'group_2_15', icon: 'print', text: 'In giấy chứng nhận thương tích', hlink: '#'}
            , {id: 'group_2_16', icon: 'print', text: 'In biên bản hội chẩn', hlink: '#'}
            , {id: 'group_2_17', icon: 'print', text: 'In giấy nghỉ ốm', hlink: '#'}
            , {id: 'group_2_18', icon: 'goikham', text: 'Nghỉ hưởng BHXH', hlink: '#'}
            , {id: 'group_2_21', icon: 'print', text: 'In CĐHA chung', hlink: '#'}
            , {id: 'group_2_Tamung', icon: 'goikham', text: 'Lập phiếu tạm ứng', hlink: '#'}
            , {id: 'group_0_cc', icon: 'print', text: 'In giấy xác nhận cấp cứu', hlink: '#'}
            , {id: 'group_0_trathe', icon: 'print', text: 'Phiếu trả thẻ', hlink: '#'}
            , {id: 'group_0_pkbenh', icon: 'print', text: 'Phiếu khám bệnh', hlink: '#'}
            , {id: 'group_0_ck39k', icon: 'print', text: 'Phiếu công khám', hlink: '#'}
            , {id: 'btn_pkcdphcn', icon: 'print', text: 'Phiếu khám và chỉ định phục hồi chức năng', hlink: '#'}
            , {
                id: 'btn_plghdcntg',
                icon: 'print',
                text: 'Phiếu lượng giá hoạt động chức năng và sự tham gia',
                hlink: '#'
            }
            , {id: 'btn_thphcn', icon: 'print', text: 'Tạo phiếu thực hiện kỹ thuật', hlink: '#'}
            , {id: 'group_0_InPhieuXuTri', icon: 'print', text: 'In giấy Khám bệnh', hlink: '#'}
            , {id: 'print_159', icon: 'print', text: 'In đơn thuốc không thuốc', hlink: '#'}
            , {id: 'group_0_TNTT', icon: 'goikham', text: 'Tai nạn thương tích', hlink: '#'}
            , {id: 'group_0_Phieu_PhuThu_Scanner', icon: 'print', text: 'Giấy cam đoan phụ thu CT Scanner', hlink: '#'}
            , {id: 'btnCongKham', icon: 'print', text: 'Thêm công khám', hlink: '#'}
            , {id: 'btnPhieuTruyenMau', icon: 'print', text: 'In giấy cam kết truyền máu', hlink: '#'}
            , {id: 'btnTomTatBA', icon: 'print', text: 'Giấy tóm tắt bệnh án', hlink: '#'}
            , {id: 'btnTomTatBA', icon: 'print', text: 'Giấy tóm tắt bệnh án', hlink: '#'}
            , {id: 'group_2_rv', icon: 'goikham', text: 'Cập nhật thông tin ra viện', hlink: '#'}
            , {id: 'group_phieu_ck', icon: 'goikham', text: 'Phiếu khám bệnh chuyên khoa', hlink: '#'}
            , {id: 'group_0_2_rtf', icon: 'print', text: 'Giấy chuyển viện RTF', hlink: '#'}
            , {id: 'print_nntv', icon: 'goikham', text: 'Phiếu chẩn đoán nguyên nhân tử vong', hlink: '#'}
            , {id: 'print_xinve', icon: 'print', text: 'Phiếu tóm tắt thông tin người bệnh nặng xin về', hlink: '#'}
            , {id: 'print_donkinh', icon: 'print', text: 'Đơn kính', hlink: '#'}
            , {id: 'group_0_nghiduongthai', icon: 'print', text: 'Nghỉ dưỡng thai', hlink: '#'}
            , {id: 'group_0_ckdtnt', icon: 'print', text: 'Bản cam kết về việc điều trị nội trú', hlink: '#'}
        ]
    }
    , {type: 'button', id: 'btnDSKham', icon: 'dskham', text: 'DS Khám'},
    {
        type: 'buttongroup', id: 'btnBANGT', icon: 'benhan', text: 'BA NGT'
        , children: [
            {id: 'btnBANGT_0', icon: 'benhan', text: 'Bệnh án', hlink: '#'}
            , {id: 'btnBANGT_1', icon: 'benhan', text: 'Chọn bệnh án', hlink: '#'}
            , {id: 'btnBANGT_2', icon: 'benhan', text: 'Mở bệnh án', hlink: '#'}
            , {id: 'btnBANGT_3', icon: 'benhan', text: 'Đóng bệnh án', hlink: '#'}
            , {id: 'btnBANGT_4', icon: 'benhan', text: 'Đưa ra khỏi bệnh án', hlink: '#'}
        ]
    }
    , {type: 'button', id: 'btnCall', icon: 'volume-up', text: 'Gọi khám'}
    , {type: 'button', id: 'btnStart', icon: 'khac', text: 'Bắt đầu'}
    , {type: 'button', id: 'btnExam', icon: 'kham', text: 'Khám bệnh'}
    /*,{type:'buttongroup',id:'btnTreat',icon:'edit',text:'Điều trị'
		,children:[
			        {id:'treat_1',icon:'edit',text:'Tạo phiếu điều trị',hlink:'#'}
			        ]}*/
    , {type: 'button', id: 'btnService', icon: 'dichvu', text: 'Dịch vụ', hlink: '#'}
    , {
        type: 'buttongroup', id: 'btndrug', icon: 'thuoc', text: 'Thuốc', hlink: '#'
        , children: [
            {id: 'drug_thuoc', icon: 'thuoc', text: 'THUỐC', hlink: '#', group: true},
            {id: 'drug_khothuoc', icon: 'thuoc', text: 'Tạo đơn thuốc từ kho', hlink: '#'},
            {id: 'drug_tutruc', icon: 'thuoc', text: 'Tạo đơn thuốc từ tủ trực', hlink: '#'},
            /*{id:'drug_1',icon:'thuoc',text:'Tạo đơn thuốc',hlink:'#'},
		           {id:'drug_le',icon:'thuoc',text:'Kê thuốc lẻ',hlink:'#'},*/
            /*{id:'drug_2',icon:'thuoc',text:'Tạo phiếu trả thuốc',hlink:'#'},	*/
            {id: 'drug_mn', icon: 'thuoc', text: 'Tạo đơn thuốc mua ngoài', hlink: '#'},
            /*{id:'drug_1kt',icon:'thuoc',text:'Tạo đơn thuốc không thuốc',hlink:'#'},*/
            {id: 'drug_dtnhathuoc', icon: 'thuoc', text: 'Mua thuốc nhà thuốc', hlink: '#'},
            {id: 'drug_phieuđinhuong', icon: 'thuoc', text: 'Phiếu tư vấn dinh dưỡng (Kê)', hlink: '#'},
            {id: 'drug_thuocdy', icon: 'thuoc', text: 'THUỐC ĐÔNG Y', hlink: '#', group: true},
            {id: 'drug_1dy', icon: 'thuoc', text: 'Tạo đơn thuốc đông y', hlink: '#'},
            /*{id:'drug_2dy',icon:'thuoc',text:'Tạo phiếu trả thuốc đông y',hlink:'#'},*/

            {id: 'drug_vattu', icon: 'thuoc', text: 'VẬT TƯ', hlink: '#', group: true},
            {id: 'drug_3', icon: 'thuoc', text: 'Tạo phiếu vật tư', hlink: '#'}
            //tuyennx_add_start_20181022 L2HOTRO-11542
            , {id: 'group_2', icon: '', text: 'Khác', hlink: '#', group: true}
            , {id: 'drug_8', icon: 'thuoc', text: 'Tạo phiếu hao phí', hlink: '#'}
            , {id: 'drug_hpvt', icon: 'thuoc', text: 'Tạo phiếu VT hao phí', hlink: '#'}
            //tuyennx_add_end_20181022
            /*,{id:'drug_4',icon:'thuoc',text:'Tạo phiếu trả vật tư',hlink:'#'}*/
        ]
    }
    , {type: 'button', id: 'btnPhieuKham', icon: 'xutri', text: 'Xử trí KB'}
    , {
        type: 'buttongroup', id: 'btnKHAC', icon: 'goikham', text: 'Khác', hlink: '#'
        , children: [
            {id: 'group_0', icon: 'goikham', text: 'KHÁC', hlink: '#', group: true},
            {id: 'handling_1', icon: 'goikham', text: 'Chuyển phòng khám', hlink: '#'},
            {id: 'handling_2', icon: 'goikham', text: 'Đổi phòng khám', hlink: '#'},
            {id: 'handling_4', icon: 'goikham', text: 'Hồ sơ quản lý sức khỏe cá nhân', hlink: '#'},//nghiant 14062017*/
            {id: 'handling_3', icon: 'goikham', text: 'Trả bệnh nhân (không khám)', hlink: '#'},
            {id: 'btnKHAC_3', icon: 'goikham', text: 'Tai nạn thương tích', hlink: '#'},
            {id: 'btnKHAC_8', icon: 'goikham', text: 'Phiếu vận chuyển', hlink: '#'},
            {id: 'btnKHAC_9', icon: 'goikham', text: 'Nghỉ hưởng BHXH', hlink: '#'},
            {id: 'btnFORM_KSKXINVIEC', icon: 'goikham', text: 'Nhập KSK Xin Việc', hlink: '#'},
            /*{id:'btnKHAC_10',icon:'goikham',text:'Chỉ định thu khác',hlink:'#'},*/
            {id: 'bntKHAC_ptu', icon: 'goikham', text: 'Lập phiếu tạm ứng', hlink: '#'},
            {id: 'btnKHAC_10', icon: 'goikham', text: 'Nhập bệnh án', hlink: '#'},
            {id: 'btnKHAC_11', icon: 'goikham', text: 'Cập nhật ĐT Ngoại trú', hlink: '#'},
            {id: 'btnChuyenTuyenCS', icon: 'dichvu', text: 'Chuyển BN về tuyến cơ sở', hlink: '#'},
            {id: 'btnCapSoBenh', icon: 'dichvu', text: 'Cấp sổ bệnh', hlink: '#'},
            {id: 'btnThuTienKhac', icon: 'thutien', text: 'Thu khác', hlink: '#'},
            {id: 'btnKSKLaiXe', icon: 'dichvu', text: 'Giấy khám SK lái xe', hlink: '#'}, //L2PT-16020
            {id: 'group_huytrabnkkham', icon: 'dichvu', text: 'Hủy trả BN K.Khám', hlink: '#'},
            {id: 'btnHUYKTKHAM', icon: 'dichvu', text: 'Hủy KT Khám', hlink: '#'},
            {id: 'btnNhapTomTatBA', icon: 'dichvu', text: 'Nhập tóm tắt bệnh án', hlink: '#'},
            {id: 'ban_QLHIV', icon: 'goikham', text: 'QL bệnh nhân HIV', hlink: '#'},
            {id: 'group_0', icon: 'print', text: 'XỬ TRÍ', hlink: '#', group: true},
            {id: 'btnNewBed', icon: 'dichvu', text: 'Kê giường', hlink: '#'},
            {id: 'handling_10', icon: 'goikham', text: 'Xếp giường cho bệnh nhân', hlink: '#'},
            {id: 'btnKHAC_0', arr: '1', icon: 'goikham', text: 'Thông tin tử vong', hlink: '#'},
            {id: 'btnDOICONGKHAM', icon: 'goikham', text: 'Đổi công khám', hlink: '#'},
            /*  {id:'btnKHAC_1',icon:'bell',text:'Biên bản tử vong',hlink:'#'},*/
            {id: 'btnKHAC_2', icon: 'goikham', text: 'Kiểm điểm tử vong', hlink: '#'},
            {id: 'btnKHAC_4', icon: 'goikham', text: 'Thông tin ra viện', hlink: '#'},
            {id: 'btnKHAC_5', icon: 'goikham', text: 'Chuyển viện', hlink: '#'},
            {id: 'btnKHAC_6', icon: 'goikham', text: 'Hẹn khám mới', hlink: '#'},
            {id: 'btnKHAC_7', icon: 'goikham', text: 'Hẹn khám tiếp', hlink: '#'}
        ]
    }
    /*,{type:'button',id:'handling_1',icon:'share',text:'Chuyển PK',hlink:'#'}*/
//	,{type:'button',id:'btnNewBed',icon:'dichvu',text:'Kê giường',hlink:'#'}
    , {type: 'button', id: 'btnKTKH', icon: 'ketthuc', text: 'Kết thúc khám', hlink: '#'}
    , {type: 'button', id: 'btnTIEPNHANCC', icon: 'khac', text: 'Tiếp nhận CC', hlink: '#'}
    , {type: 'button', id: 'btnKSK', icon: 'khac', text: 'Khám sức khỏe', hlink: '#'}
];
var cfObj = new Object();

function benhnhanList(opt) {
    // lấy cấu hình động cho menu button
    var par_ctl = ['TOOLBAR_MENU_KHAMBENH_VS1'];
    var ctl_par = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', par_ctl.join('$'));
    if (ctl_par != 0) {
        ctl_ar = JSON.parse(ctl_par);
    }
    //var toolbar=new JsToolbar('toolbarId');
    //toolbar.buildToolbar(ctl_ar);
    // var toolbar = ToolbarUtil.build('toolbarId', ctl_ar);
    toolbar = ToolbarUtil.buildVs2('toolbarId', ctl_ar);
    toolbar.addEvent("btnStart", "click", function (e) {
        console.log('btnStart_click');
        console.log('txtFromDate=' + toolbar.getValue("txtFromDate"));
    });
    var _toolbar = ToolbarUtil.getToolbar('toolbarId');

    _toolbar.addEvent("print_1", "click", function (e) {
        console.log('print_1_click');
        console.log('txtToDate=' + toolbar.getValue("txtToDate"));
    });
    toolbar.addEvent("txtSearch", "click", function (e) {
        console.log('txtSearch_click');
        console.log('txtFromDate=' + toolbar.getValue("txtFromDate"));
    });
    var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "KHAM_ONLINE_WS;NGT_HIENTHI_TGBDKHAM;NGT_HIENTHI_MAU_SN;NGT_LOADBADN_ALLPHONG;" +
        "HIS_SHOW_TAB_UX2023;NGT_HIENTHI_SLTKB1;NGT_TN_GHICHU;NGT_TIMKIEMXUTRI_KBMHC;NGT_TABTRUYENDICH;NGT_HIENTHI_TABCHAMSOC;NGT_MAUBNKSK_DSKB;CHECK65BHYT;HIS_SUDUNG_KYSO_KYDIENTU;NGT_MAUCONTHUOC_KB2;NGT_MAUCONTHUOC_UUTIEN_KB2;CHECK65BHYT_CHAN;" +
        "KETHUOC_LOADKHO_HAOPHI;NGT02K001_SHOW_PTU;NGT_HIENTHI_GOISOBN;NGT_HUYNHAPVIEN_HIENTHI;NGT_SCROLL_TTHC;NGT_KB_CHUCDANH;NGT_HIENTHIMAU_DATLICH;NGT_THEMTRUONG_DIACHI;NGT_DSKHAM_PHANBIETMAU;NGT_PHANBIET_BSKHAM_BSXUTRI;" +
        "NGT_CHONNGAY_AUTOSEARCH;NGT_KETHUOC_KHI_NHAPVIEN;NGT_HIENTHI_CBUT_BVSK;NGT_BANGT_DISABLE;NGT_DOIPHONGKHAM_DV;HIS_SHOW_TTBENHAN_BS;NGT_HIENTHITUOI_KB1;NGT_ENABLE_HENKHAM_KB1;NGT_TIEPNHAN_SHOWTHUOCKTK;VP_IN_PHOI_KHI_DONG_BENHAN;" +
        "NGT_CHECKTAMUNG_BDKHAM;CHAN_CDDV_KETHUOC;HIS_CHUYENKHOA_BS_DV;NGT_LUUQUANLYBENHAN;HIS_KHOA_SOLIEU;HIS_KHOA_BENHAN;NGT_ICON_BNTRAITUYEN;KB_MHC2_NO_DBLCLICK;NGT_KB_CC_TAB_NG;CDDV_GIAODIEN_KHA;NGT_CHECKTRUNGTHAOTAC_TGBD;NGT_KBHB_FULL;NGT_KBHB_FITSCREEN;" +
        "NGT_LOAI_KT_DONGY;NGT_DONTHUOC_FITSCREEN;NGT_XUTRI_FITSCREEN;NGT_CHANXUTRI_BADN;KBH_CHAN_XUTRI_KCK;NGT_KETHUOC_KHO;NGT_KB_CHECKTGCDDV;NGT_DONTHUOC_BADN;NGT_GOISO_QUANY15;NGT_KIEUGOIKHAM_GG;NGT_KIEUGOI_GG;NGT_CHECKTGIANBDKHAM_2BN;NGT_MO_POPUP_BATDAUKHAM;QD130_DAYXMLCHECKIN_BDKHAM;" +
        "NGT_KHAMBENH_TK_TG;NGT_CHECKTGBDKHAM_TGTIEPNHAN;NGT_LOADBTNKSK;HIS_CHECKMALOAIKCB_7;HIS_KTBA_CHECK_XN_NULL;" + //L2PT-102859
        "NGT_KTBA_CHECKTGTOIDA;NGT_MAPHONGKCHECKTG_BNKSK;NGT_DUYETKETOAN_KTKHAM_BNHYT;NGT_KIEUKE_THUOCDONGY;" + //L2PT-121068
        "NGT_XUTRI_CHECKDVCHUAHOANTHANH;RPT_CODE_KYSO_BANGKE;NGT_KYSOBANGKE_KTK;NTU_KTBA_CHECK_KQCLS;VPI_AUTO_DUYET_BHYT_NGT;VP_DUYET_BH_KHI_DUYET_KETOAN;NGT_DUYETKETOAN_KTKHAM;DTDT_DAY_DONTHUOC;HIS_CHAN_SAI_TYLE;HIS_CANHBAO_KHONG_TTDT;NGT_XUTRI_CHECKMBPBHYTCHUACOKQ;" +
        "HIS_CHECKMALOAIKCB_10;CHECK65BN_CHECKALL_DOITUONG;HIS_SHOW_TTTUYENCHITIET;NGT_CHECKLIENTHONG_CHAN;NGT_SOLUOTKHAM_CHECKLIENTHONG;NGT_LIENTHONG_LUOTKHAM_BSI;NOTIFY_APP_VNCARE;NTU_KTBA_CHECK_TG_YLENH;CHK_CHAN_CHIDINH_TRUNGTIME;KETHUOC_CHIDINH_TRUNGTIME");//L2PT-115262
    if (config_ar != null && config_ar.length > 0) {
        cfObj = config_ar[0];
    } else {
        DlgUtil.showMsg("Không tải được thông tin cấu hình sử dụng trên màn hình");
        return;
    }

    var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","KETHUOC_CHIDINH_TRUNGTIME");
    config_ar[0] =0;


    var LNMBP_XetNghiem = 1;
    var LNMBP_CDHA = 2;
    var LNMBP_ChuyenKhoa = 5;
    var LNMBP_DieuTri = 4;
    var LNMBP_Phieuvattu = 8;
    var LNMBP_Phieuthuoc = 7;
    var LNMBP_ChamSoc = 9;
    var LNMBP_TruyenDich = 13;

    var _flgModeView = '0';
    var _tsmobenhan = '0';
    var _enable_mobenhan = '0';
    var _checktienkhiketthuckham = '0';
    var _idkhambenh = 0;
    var _sophongkham = "0";
    var _ngtgoibenhnhan = "0";
    var _tylevpchuyenphanhe = "2"; 				// VIEN PHI;
    var _khamchinhphu = "0";
    var _khamchinhphudetail = "0";
    var _chedogoikham = "0"; 					// CHE DO THUONG;

    _grdDieuTri = "grdDieuTri";
    $grdDieuTri = $("#" + _grdDieuTri);
    var _type = "";
    var _opt = opt;
    var _gridId = "grdDSBenhNhan";
    var tabThuKhac = "0";
    var _hinhthucvaovienid = "0";
    var _HIS_TIMEOUT_THONGBAO = 0;
    var NGT_CANHBAO_TIEN = "0"; //L2PT-26177
    var _pkbvvcapcuu = "0";
    var _hienthiBNVPI = "0";
    var _modeGoiKham = "0";
    var _timeOutGoiKham = "5000";
    var _canhbaobdkham = "0";
    var _doicongkhamkb = "-1";
    var _sudungKhamOnline = cfObj.KHAM_ONLINE_WS;
    var _thoigianbdkham = cfObj.NGT_HIENTHI_TGBDKHAM;
    var _chedolcd = "0";
    var _doipkhienthi = "0";
    var NGT_DSKHAM_PHANBIETMAU = "0";
    var check_note = ""; //L2PT-30940
    var _mau_khamtrongthang = "0"; 											// mau hien thi bn kham lai trong thang;
    var _setmausn = cfObj.NGT_HIENTHI_MAU_SN;
    var NGT_GOIKHAM_HANGDOI = "0"; 	//L2PT-28376
    var htmlDlgmoBA = "";
    var showTTBNWidget = false;
    var NGT_THEMTRUONG_DIACHI = 0;
    var NGT_HIENTHIMAU_BADN = "0"; 	//L2PT-44037
    //L2PT-42611
    var NGT_MENUKHAC_KTK = "0";
    var NGT_MENUKHAC_AN_KTK = "0";
    var _mau_conthuoc = "0";
    var mauconthuoc_bnthuong = "0";
    var mauconthuoc_bnuutien = "0";
    var checkphieuchuacokq = 0;
    var maubnksk = "0";
    var checkbdkham_3phut = 0;
    var startkham = 0;
    var NGAYSINH = '';
    var arrReportCode = [];
    var causer = '';
    var capassword = '';
    var smartcauser = '';
    var NGT_LOADBADN_ALLPHONG = cfObj.NGT_LOADBADN_ALLPHONG;
    var sqlloadbadn = '';
    var check_kqcls = 0;
    var cf = new Object();
    var check65_giohanhchinh;
    var check_tgksk = 0;
    var checkDsFull= false;
    this._gridHeader = " ,ICON,30,0,ns,l; ,ICONCLS,30,0,ns,l;KQCLS,KQCLS,0,0,t,l;khambenhid,KHAMBENHID,0,0,t,l;" +
        "hosobenhanid,HOSOBENHANID,0,0,t,l;" +
        "phongkhamdangkyid,PHONGKHAMDANGKYID,0,0,t,l;" +
        "benhnhanid,BENHNHANID,0,0,t,l;doituongbenhnhanid,DOITUONGBENHNHANID,0,0,t,l;" +
        "tiepnhanid,TIEPNHANID,0,0,t,l;loaitiepnhanid,LOAITIEPNHANID,0,0,t,l;" +
        "BADAINGAY,BADAINGAY,10,0,t,l;" + //L2PT-44037
        "trangthaikhambenh,TRANGTHAIKHAMBENH,10,0,t,l;hinhthucvaovienid,HINHTHUCVAOVIENID,10,0,t,l;" +

        (_sudungKhamOnline == "1" ?
            "Gọi,onlineGOI,50,0,f,l,1,2,ES;Xem lại,onlineXEMLAI,50,0,f,l,1,2,ES;"
            : "Gọi,onlineGOI,50,0,t,l,1,2,ES;Xem lại,onlineXEMLAI,50,0,t,l,1,2,ES;") +
        "USER_CHUYEN,USER_CHUYEN,60,0,t,l;MA_CSYT_CHUYEN,MA_CSYT_CHUYEN,60,0,t,l;" +
        "Mã người nhận,MANGUOINHAN,60,0,t,l;Mã đăng ký,MADANGKY,60,0,t,l;" +

        "xutrikhambenhid,XUTRIKHAMBENHID,10,0,t,l;SỐ TT,SOTHUTU,50,0,f,l;" +
        "Lần gọi,LANGOIKHAM,30,0,f,l;LOẠI DK,LOAIHENKHAM,70,0,f,l;SL Khám,LANKHAMTRONGTHANG,40,0,f,l;LẦN GỌI,LANGOI,70,0,t,l;MÃ BA,MAHOSOBENHAN,70,0,f,l;" +
        "MÃ BN,MABENHNHAN,80,0,f,l;HỌ TÊN,TENBENHNHAN,180,0,f,l;" +
        "MÃ BHYT,MA_BHYT,110,0,f,l;TRẠNG THÁI,TENTRANGTHAIKB,110,0,t,l;MAUBENHPHAMID,MAUBENHPHAMID,110,0,t,l;DICHVUKHAMBENHID,DICHVUKHAMBENHID,110,0,t,l;" +
        "ĐỐI TƯỢNG,DOITUONGBENHNHAN,80,0,f,l;HIENTHIBNVPI,HIENTHIBNVPI,80,0,t,l;" +
        "madichvu,MADICHVU,0,0,t,l;uutienkhamid,UUTIENKHAMID,0,0,t,l;TRANGTHAITIEPNHAN,TRANGTHAITIEPNHAN,0,0,t,l;NGAYTHUOC,NGAYTHUOC,0,0,t,l;NGAYTIEPNHAN,NGAYTIEPNHAN,0,0,t,l;" +
        "loaibenhanid,LOAIBENHANID,0,0,t,l;DACODICHVUTHUTIEN,DACODICHVUTHUTIEN,0,0,t,l;BHYT_LOAIID,BHYT_LOAIID,0,0,t,l;DONGTIEN,DONGTIEN,0,0,t,l;" +
        "TRANG_THAI,TRANG_THAI,0,0,t,l;PHONGID,PHONGID,0,0,t,l;KHOAID,KHOAID,0,0,t,l;DIEUTRI_NGT,DIEUTRI_NGT,0,0,t,l;CHUYENKHAMNGT,CHUYENKHAMNGT,0,0,t,l;LOAIDOITUONG,LOAIDOITUONG,0,0,t,l;" +
        "KHAMCHINHPHU,KHAMCHINHPHU,0,0,t,l;TEN_FILE,TEN_FILE,0,0,t,l;SUB_DTBNID,SUB_DTBNID,0,0,t,l;KSKCN,KSKCN,0,0,t,l;KSK,KSK,0,0,t,l;SINHNHAT,SINHNHAT,0,0,t,l;Xử trí,XUTRI_VIEW,100,0,f,l" +
        (_thoigianbdkham == "1" ?
            ";Thời gian bắt đầu,THOIGIANBD,130,0,f,l"
            : "");
    //thaiph sap xep cot trong danh sach kham benh theo cau hinh//
    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(
        "COM.DS_CAUHINH", "NGT_TT_DS_KB;NGT_KB_THU_KHAC");
    if (data_ar[0].NGT_TT_DS_KB.length > 100) {
        this._gridHeader = data_ar[0].NGT_TT_DS_KB;
    }
    tabThuKhac = data_ar[0].NGT_KB_THU_KHAC;
    if (tabThuKhac == 1) {
        $("#tabPhieuThuKhacTab").show();
        $("#toolbarIdbtnThuTienKhac").show();
    }
    if (tabThuKhac == 0) {
        $("#tabPhieuThuKhacTab").hide();
        $("#toolbarIdbtnThuTienKhac").hide();
    }
    //end thaiph//

    if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == "1") {
        $('#toolbarIdbtnPrintCa').show();
    } else {
        $('#toolbarIdbtnPrintCa').hide();
    }

    var _SQL = ["NGT02K001.DANHSACH", "NGT02K001.EV002", "NGT02K001.EV003", "NGT02K001.EV004", "NGT02K009.RV005", "NGT02K009.RV002"];
    this.load = doLoad;

    function doLoad() {
        // null: khambenh; 1 - kham benh trai ngay;; 2 - kham benh cap cuu; 3 - kham benh theo bac sy;
        _type = getParameterByName('type', window.location.search.substring(1));
        if (_type == 2) {
            var _hienthitabngaygiuong = cfObj.NGT_KB_CC_TAB_NG;
            if (_hienthitabngaygiuong == 1) {
                $('#tabNgayGiuongTab').css('display', '');
            }
        }
        ssid = getParameterByName('ssid', window.location.search.substring(1));
        $('#txtMABENHNHANTK').focus();
        $('#toolbarIdbtnhandling').css('width', '105px');
        $('#toolbarIdtxtFromDate').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
        $('#toolbarIdtxtToDate').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));

        $("#toolbarIdtxtFromDate").attr("valrule", "Từ ngày,date|max_length[10]");
        $("#toolbarIdtxtToDate").attr("valrule", "Đến ngày,date|max_length[10]");

        $("#toolbarIdbtndrug").attr("disabled", true);
        $("#toolbarIdbtnStart").attr("disabled", true);
        //if(opt.hospital_id!=965){
        //$("#toolbarIdgroup_2_6").addClass("hidden");
        //}
        var _sql_pr = [];
        _sql_pr.push({
            "name": "[0]",
            value: opt.phongid
        });
        check_note = jsonrpc.AjaxJson.getOneValue("NGT.PHONG.NOTE", _sql_pr) //L2PT-30940
        ComboUtil.getComboTag("cboXUTRIKHAMBENHID", "NGT05.XUTRI", [], "", {
            extval: true,
            value: 0,
            text: 'Chọn xử trí'
        }, "sql", "", "");
        var sql_par = [];
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("LAY.CAUHINH", sql_par.join('$'));

        if (data_ar != null && data_ar.length > 0) {
            cf = data_ar[0];
            _tsmobenhan = data_ar[0].DK_MOBENHAN;
            if (data_ar[0].HIS_SHOW_TTBENHAN_WIDGET == "1") {
                showTTBNWidget = true;
            }
            if (data_ar[0].HIS_DOI_BACSI_DT == "1") {
                $('#divBsikham').css('display', '');
                var r_data = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H046.EV003", ssid);
                if (r_data != null && r_data.length > 0) {
                    data = r_data[0];
                    $("#labelBACSIDIEUTRIMS").html(data.OFFICER_NAME + ' (' + data.USER_NAME + ')');
                    $("#hidBACSYKE").val(data.USER_ID);
                }
            }

            if (data_ar[0].NGAYPK == "0") {
                $('#toolbarIdtxtFromDate').attr("disabled", true);
                $('#lblTuNgay').attr("disabled", true);
                $('#toolbarIdtxtToDate').attr("disabled", true);
                $('#lblDenNgay').attr("disabled", true);
            } else {
                $('#toolbarIdtxtFromDate').attr("disabled", false);
                $('#lblTuNgay').attr("disabled", false);
                $('#lblTuNgay').attr("disabled", false);
                $('#lblDenNgay').attr("disabled", false);
            }
            if (data_ar[0].DSBNFULL == "1"){
                $("#divhanhchinh").hide();
                $("#divtimkiem").removeClass("col-xs-7");
                $("#divtimkiem").addClass("col-xs-12");
                $('#tabAnHien').css('display','');
                $('#tabAnHien ins').html('');
                $('#tabAnHien ins').html('Hiện chi tiết');
                GridUtil.setWidthPercent(_gridId,100);
                checkDsFull = true;
            }

            if (data_ar[0].HIDE_BTN_MO_BA == '1') {
                _enable_mobenhan = '1';
            }

            if (data_ar[0].CHUPANH == '1') {
                $('#divIMG').css("display", "");
            }

            if (data_ar[0].ANBANGT == '0') {
                $("#toolbarIdbtnBANGT").hide();
            } else {
                $("#toolbarIdbtnBANGT").show();
            }

            if (data_ar[0].HIDEDONTHUOCKT == "0") {
                $("#toolbarIddrug_1kt").hide();
            } else {
                $("#toolbarIddrug_1kt").show();
            }

            if (data_ar[0].TABHC_DT == "1") {
                $("#tabHoiChuanTab").show();
                $("#tabDieuTriTab").show();
                $("#tabHoiChuan").css("display", "");
            }
            if (cfObj.NGT_HIENTHI_TABCHAMSOC == 1) {
                $("#tabChamSocTab").show();
            } else {
                $("#tabChamSocTab").hide();
            }
            if (data_ar[0].BBHC == "1") {
                $("#tabHoiChuan").show();
                $("#tabHoiChuanTab").show();
                $("#tabHoiChuan").css("display", "");
            }
            if (cfObj.NGT_TABTRUYENDICH == "1") {
                //$("#tabPhieuTruyenDich").show();
                $("#tabPhieuTruyenDichTab").show();
                $("#tabPhieuTruyenMau").show(); //L2PT-63268
            }
            if (cfObj.NGT_TIMKIEMXUTRI_KBMHC == "1") {
                $('#dvXUTRIKHAMBENH').show();
            }
            if (cfObj.NGT_TN_GHICHU == 1) {
                $("#dvGHICHUTIEPDON").show();
            }
            $("#hidTATTHONGBAOKBHB").val(data_ar[0].TATTHONGBAOKBHB);
            _ngtgoibenhnhan = data_ar[0].NGTGOIBENHNHAN;
            _tylevpchuyenphanhe = data_ar[0].TYLEVPCHUYENPHANHE;
            _khamchinhphu = data_ar[0].KHAMCHINHPHU;
            _chedogoikham = data_ar[0].CHEDOGOIKHAM;
            //tuyennx_add_start_20190425  L2PT-14910
            _HIS_TIMEOUT_THONGBAO = data_ar[0].HIS_TIMEOUT_THONGBAO;
            NGT_CANHBAO_TIEN = data_ar[0].NGT_CANHBAO_TIEN;
            //tuyennx_add_end_20190425  L2PT-14910
            //L2PT-28376
            NGT_GOIKHAM_HANGDOI = data_ar[0].NGT_GOIKHAM_HANGDOI;
            NGT_HIENTHIMAU_BADN = data_ar[0].NGT_HIENTHIMAU_BADN;//L2PT-44037
            //L2PT-42611
            NGT_MENUKHAC_KTK = data_ar[0].NGT_MENUKHAC_KTK;
            NGT_MENUKHAC_AN_KTK = data_ar[0].NGT_MENUKHAC_AN_KTK;
            _pkbvvcapcuu = typeof data_ar[0].PKBVVCAPCUU == 'undefined' ? "0" : data_ar[0].PKBVVCAPCUU;
            _hienthiBNVPI = typeof data_ar[0].HIENTHIBNVPI == 'undefined'
            || data_ar[0].HIENTHIBNVPI == null
            || data_ar[0].HIENTHIBNVPI == 'null'
            || data_ar[0].HIENTHIBNVPI == '' ? "0" : data_ar[0].HIENTHIBNVPI;

            _doipkhienthi = typeof data_ar[0].DOIPKHIENTHI == 'undefined'
            || data_ar[0].DOIPKHIENTHI == null
            || data_ar[0].DOIPKHIENTHI == 'null'
            || data_ar[0].DOIPKHIENTHI == '' ? "0" : data_ar[0].DOIPKHIENTHI;

            _mau_khamtrongthang = typeof data_ar[0].MAUKHAMTRONGTHANG == 'undefined'
            || data_ar[0].MAUKHAMTRONGTHANG == null
            || data_ar[0].MAUKHAMTRONGTHANG == 'null'
            || data_ar[0].MAUKHAMTRONGTHANG == '' ? "0" : data_ar[0].MAUKHAMTRONGTHANG;
            _mau_conthuoc = typeof data_ar[0].MAUCONTHUOC == 'undefined'
            || data_ar[0].MAUCONTHUOC == null
            || data_ar[0].MAUCONTHUOC == 'null'
            || data_ar[0].MAUCONTHUOC == '' ? "0" : data_ar[0].MAUCONTHUOC;
            mauconthuoc_bnthuong = cfObj.NGT_MAUCONTHUOC_KB2;
            mauconthuoc_bnuutien = cfObj.NGT_MAUCONTHUOC_UUTIEN_KB2;
            maubnksk = cfObj.NGT_MAUBNKSK_DSKB;
            check65_giohanhchinh = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'CHECK65BHYT_KOCHECK_GIOHANHCHINH');
            sqlloadbadn = NGT_LOADBADN_ALLPHONG == '1' ? "BAN.DAINGAY.DS2" : "BAN.DAINGAY.DS";
            if (data_ar[0].HIENTHICALL5 == "1") {
                $("#dvCall5").show();
            } else {
                $("#dvCall5").hide();
            }

            // CHE DO GOI BENH NHAN TU BEN NGOAI VAO PHONG KHAM;
            if (_chedogoikham == "1") {
                $("#dvGOIKHAMSTT").show();
                $('#toolbarIdbtnCall').hide();
            } else {
                $("#dvGOIKHAMSTT").hide();
                $('#toolbarIdbtnCall').show();
            }

            _modeGoiKham = typeof data_ar[0].MODEGOIKHAM == 'undefined' ? "0" : data_ar[0].MODEGOIKHAM;
            _doicongkhamkb = typeof data_ar[0].DOICONGKHAMKB == 'undefined' ? "-1" : data_ar[0].DOICONGKHAMKB;
            _timeOutGoiKham = typeof data_ar[0].TIMEOUTGOIKHAM == 'undefined' ? "5000" : data_ar[0].TIMEOUTGOIKHAM;
            _canhbaobdkham = typeof data_ar[0].CANHBAOBDKHAM == 'undefined' ? "0" : data_ar[0].CANHBAOBDKHAM;
            _chedolcd = typeof data_ar[0].CHEDOLCD == 'undefined' ? "0" : data_ar[0].CHEDOLCD;

            if (data_ar[0].MABENHANNDH == "1") {
                $("#lbllMABENHNHANTK").text("Mã BA");
            }

            if (data_ar[0].DSBNGHICHU == "1") {
                $("#dvGHICHU").show();
            }

            if (_doicongkhamkb != "-1") {
                $("#toolbarIdbtnDOICONGKHAM").show();
            } else {
                $("#toolbarIdbtnDOICONGKHAM").hide();
            }
            if (cfObj.NGT_HIENTHI_SLTKB1 == "1") {
                $("#dvSOLUUTRU").show();
            }
        }
        if (cfObj.HIS_SHOW_TAB_UX2023 == "1") {
            $('#tabXetNghiemTab').hide();
            $('#tabCDHATab').hide();
            $('#tabChuyenKhoaTab').hide();
            $('#tabThuocTab').hide();
            $('#tabPhieuTruyenDichTab').hide();
            $('#tabVatTuTab').hide();
            $('#tabPhieuVanChuyenTab').hide();
            $('#tabBADTNGTTab').hide();
            $('#tabChamSoc').hide();
            $('#tabDieuTri').hide();
            $('#tabHoiChuanTab').hide();
        }

        var sql_par = [];
        sql_par.push({"name": "[0]", "value": 87});
        ComboUtil.getComboTag("cboTRANGTHAI", _SQL[5], sql_par, "49", {value: 0, text: 'Tất cả'}, "sql", "", "");

        $('#lblKHOA').text(_opt.dept_name);
        $('#lblPHONG').text(_opt.subdept_name);
        $("#hidHisId").val(opt.hospital_id);
        $("#hidDbSchema").val(opt.db_schema);
        $("#hidUserID").val(opt.user_id);
        $('#toolbarIdbtnKTKH').css('width', '120px');
        $("#hidPHONGID").val(opt.phongid);					// set phong, khoa theo session;
        $("#hidKHOAID").val(opt.khoaid);
        $("#toolbarIdgroup_0_4").addClass("disabled");
        $("#toolbarIdgroup_0_7").addClass("disabled");
        $("#toolbarIdgroup_0_5").addClass("disabled");
        GridUtil.init(_gridId, "100%", "360px", "", false, this._gridHeader, false, {
            rowNum: 100,
            rowList: [100, 200, 300]
        });
        GridUtil.addExcelButton(_gridId, 'Xuất excel', true);
        _loadGridData(_opt.phongid);
        _setButton(true);

        // lay số phòng gọi tiep don
        /*var sql_par=[];
		sql_par.push({"name":"[0]","value":_opt.khoaid});
		sql_par.push({"name":"[1]","value":_opt.phongid});

		var vsophong = jsonrpc.AjaxJson.ajaxExecuteQueryO("SOPHONG.TIEPDON", sql_par);
		vphong = JSON.parse(vsophong);
		if(vphong != null && vphong.length > 0 && vphong[0].SOPHONG != ""){
			if(_chedogoikham == "2"){
				_sophongkham = vphong[0].SOPHONG;
			}else if (!isNaN(vphong[0].SOPHONG) ){
				_sophongkham = vphong[0].SOPHONG;
			}else{
				_sophongkham = "0";
			}
		}*/

        $("#toolbarIdbtnThuTienKhac").attr("disabled", true);
        var NGT_PHANBIET_BSKHAM_BSXUTRI = cfObj.NGT_PHANBIET_BSKHAM_BSXUTRI;
        if (NGT_PHANBIET_BSKHAM_BSXUTRI == 1) {
            $("#lbBacsyxutri").html("");
            $("#lbBacsyxutri").html("Bác sỹ");
            $("#divBACSYXUTRI").show();
        }
        NGT_DSKHAM_PHANBIETMAU = cfObj.NGT_DSKHAM_PHANBIETMAU;
        NGT_THEMTRUONG_DIACHI = cfObj.NGT_THEMTRUONG_DIACHI;
        if (NGT_THEMTRUONG_DIACHI == 1) {
            $("#lblDiachibhyt").text("Địa chỉ BHYT");
            $("#dvDIACHIHC").show();
        }
        NGT_HIENTHIMAU_DATLICH = cfObj.NGT_HIENTHIMAU_DATLICH;
        if (NGT_HIENTHIMAU_DATLICH != "" && NGT_HIENTHIMAU_DATLICH != "0") {
            $("#spGhichuDatLich").css("color", NGT_HIENTHIMAU_DATLICH);
        }

        //HungND - L2PT-67014
        if (cfObj.NGT_KB_CHUCDANH == 1) {
            $("#dvCHUCDANH").show();
        }
        //HungND - L2PT-67014 END
        if (cfObj.NGT_MAPHONGKCHECKTG_BNKSK != '0') {
            var maphong = jsonrpc.AjaxJson.getOneValue("NGT.GETMAKHOA", [{"name":"[0]", "value": opt.phongid}]);
            if (cfObj.NGT_MAPHONGKCHECKTG_BNKSK.includes(maphong) )  {
                check_tgksk = 1;
            }
        }
        var scrollTTHC = cfObj.NGT_SCROLL_TTHC;
        if (scrollTTHC == '1') {
            var tthc = document.getElementById("divhanhchinh");
            var tthc1 = document.getElementById("divhanhchinh1");
            tthc.style["height"] = "650px";
            tthc1.style["height"] = "350px";
            tthc1.style["overflow-y"] = "scroll";
        }
        var par_ctl = ['CONTEXT_MENU_KHAMBENH'];
        var ctl_par = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', par_ctl.join('$'));
        if(ctl_par != '0'){
            $( "#contextMenu" ).html(ctl_par);
        }
        htmlDlgmoBA = $('#dlgWRAP_P').html();
        _bindEvent();

        //L2PT-38649
        try {
            var str_menu = jsonrpc.AjaxJson.ajaxCALL_SP_X('COM.CAUHINH.CLOB', 'NGT02K001_KB_MHC_MENU_KHAC');
            var _list = JSON.parse(str_menu);
            for (var i = 0; i < _list.length; i++) {
                var _itemMenu = _list[i];
                var _id = 'toolbarId_MENU_' + _itemMenu.ID;
                var _url = _itemMenu.ID;
                var _name = _itemMenu.NAME;
                $('<li data-external="{}" id="' + _id + '"><a href="#">&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-goikham"></i>&nbsp;&nbsp;' + _name + '</a></li>').insertAfter(
                    $('#toolbarIdbtnKHAC').next('.dropdown-menu').children().first()
                );
                $("#" + _id).click(function () {
                    var myVar = {
                        khambenhId: $("#hidKHAMBENHID").val()
                        , benhnhanId: $("#hidBENHNHANID").val()
                        , tiepnhanId: $("#hidTIEPNHANID").val()
                        , mabenhnhan: $("#txtMABENHNHAN").val()
                        , hosobenhanId: $("#hidHOSOBENHANID").val()
                        , loaibenhanId: $("#hidLOAIBENHANID").val()
                        , phongId: $("#hidPHONGID").val()
                        , phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
                        //loaikskid : '4',
                        //maloaiksk : 'KSKT18'
                    };
                    var _width = $(document).width() - 150;
                    var _height = $(window).height() - 50;
                    dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../ksk/" + _url, myVar, _name, _width, _height);
                    DlgUtil.open("divDlgBenhAnDetail");
                });
            }
        } catch (ex) {
        }

        //L2PT-45300
        THEM_MENU();
    }

    //L2PT-45300
    function THEM_MENU() {
        try {
            var str_menu = jsonrpc.AjaxJson.ajaxCALL_SP_X('COM.CAUHINH.CLOB', 'NGT02K001_KB_MHC_MENU');
            if (str_menu != '0') {
                var _list = JSON.parse(str_menu);
                for (var i = 0; i < _list.length; i++) {
                    var _itemMenu = _list[i];
                    var _id = 'toolbarId_createAuto' + i;
                    var _name = _itemMenu.NAME;
                    var _strMenu =
                        '<input type="hidden" id="Data_' + _id + '" value="" />'
                        + '<li data-external="{}" id="' + _id + '"><a href="#">&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-goikham"></i>&nbsp;&nbsp;' + _name + '</a></li>';

                    $(_strMenu).insertAfter(//toolbarIdbtnKHAC
                        $('#' + _itemMenu.PARENT).next('.dropdown-menu').children().last()
                    );

                    $('#Data_' + _id).val(JSON.stringify(_itemMenu));

                    $("#" + _id).click(function () {
                        if (!$("#hidKHAMBENHID").val() || $("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '0' || $("#hidKHAMBENHID").val() == '-1') {
                            DlgUtil.showMsg("Chưa chọn bệnh nhân");
                            return;
                        }

                        var _itemStr = $('#Data_' + $(this).attr('id')).val();
                        var _item = JSON.parse(_itemStr);

                        if (_item.TYPE == 'POPUP') {
                            //L2PT-85244
                            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
                            var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
                            var _trangthaikhambenh = rowData.TRANGTHAIKHAMBENH;

                            var name = {
                                KHAMBENHID: $("#hidKHAMBENHID").val()
                                , BENHNHANID: $("#hidBENHNHANID").val()
                                , TIEPNHANID: $("#hidTIEPNHANID").val()
                                , MABENHNHAN: $("#hidMABENHNHAN").val()
                                , HOSOBENHANID: $("#hidHOSOBENHANID").val()
                                , DOITUONGBENHNHANID: $("#hidDOITUONGBENHNHANID").val()
                                , LOAITIEPNHANID: $("#hidLOAITIEPNHANID").val()
                                , KHOAID: _opt.khoaid
                                , PHONGID: _opt.phongid
                                , LOAI_PHIEU: "NGT_KHAMBENH"  //dùng cho form Thêm phiếu
                                //biến Biên bản hội chẩn
                                //L2PT-85244
                                , khambenhid: $("#hidKHAMBENHID").val()
                                , loaitiepnhanid: "1"
                                , trangthaikhambenh: _trangthaikhambenh

                                , khambenh_id: $("#hidKHAMBENHID").val()
                                , maubenhpham_id: ""
                                , benhnhanid: $("#hidBENHNHANID").val()
                                , hosobenhanid: $("#hidHOSOBENHANID").val()

                            };
                            dlgPopup = DlgUtil.buildPopupUrl("divDlgPopup", "divDlg", "manager.jsp?func=../" + _item.URL, name, _item.NAME
                                , window.innerWidth * 0.90, window.innerHeight * 0.90);
                            DlgUtil.open("divDlgPopup");
                        } else if (_item.TYPE == 'NOPOPUP') {  //    noitru/NTU02D170_FORMPHIEU&showMode=dlg
                            var name = _item.CHUCNANG + '@' + $("#hidKHAMBENHID").val() + '@' + $("#hidTRANGTHAIKHAMBENH").val();
                            var dlgWindow = window.open('manager.jsp?func=../' + _item.URL, name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
                                screen.height + ',width=' + screen.width);
                            dlgWindow.moveTo(0, 0);
                        } else if (_item.TYPE == 'DOC' || _item.TYPE == 'PDF') {  //
                            var par = [
                                {name: 'hosobenhanid', type: 'String', value: $("#hidHOSOBENHANID").val()}
                                , {name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()}
                                , {name: 'mabenhnhan', type: 'String', value: $("#hidMABENHNHAN").val()}
                                , {name: 'loaibenhanid', type: 'String', value: $("#hidLOAIBENHANID").val()}
                                , {name: 'benhnhanid', type: 'String', value: $("#hidBENHNHANID").val()}
                                , {name: 'tiepnhanid', type: 'String', value: $("#hidTIEPNHANID").val()}
                                , {name: 'phongid', type: 'String', value: $("#hidPHONGID").val()}
                                , {name: 'khoaid', type: 'String', value: _opt.khoaid}
                                , {name: 'i_hid', type: 'String', value: $("#hidHisId").val()}
                                , {name: 'i_sch', type: 'String', value: opt.db_schema}
                                , {name: 'i_hosobenhanid', type: 'String', value: $("#hidHOSOBENHANID").val()}
                                , {name: 'i_khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()}
                                , {name: 'i_mabenhnhan', type: 'String', value: $("#hidMABENHNHAN").val()}
                                , {name: 'i_loaibenhanid', type: 'String', value: $("#hidLOAIBENHANID").val()}
                                , {name: 'i_benhnhanid', type: 'String', value: $("#hidBENHNHANID").val()}
                                , {name: 'i_tiepnhanid', type: 'String', value: $("#hidTIEPNHANID").val()}
                                , {name: 'i_phongid', type: 'String', value: $("#hidPHONGID").val()}
                                , {name: 'i_khoaid', type: 'String', value: _opt.khoaid}
                                , {name: 'RPT_CODE', type: 'String', value: _item.RPT_CODE}  // cho in Emr
                            ];

                            if (_item.TYPE == 'DOC') {
                                var rpName = _item.RPT_CODE + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                                CommonUtil.inPhieu('window', _item.RPT_CODE, 'docx', par, rpName);
                            } else if (_item.TYPE == 'PDF') {
                                openReport('window', _item.RPT_CODE, "pdf", par);
                            } else if (_item.TYPE == 'EMR') {
                                CommonUtil.openReportEmr(par, false);
                            }
                        }

                    });
                }
            }
        } catch (ex) {
        }
    }


    function _bindEvent() {
        $.jMaskGlobals = {
            maskElements: 'input,td,span,div',
            dataMaskAttr: '*[data-mask]',
            dataMask: true,
            watchInterval: 300,
            watchInputs: true,
            watchDataMask: true,
            byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
            translation: {
                '0': {pattern: /\d/},
                '9': {pattern: /\d/, optional: true},
                '#': {pattern: /\d/, recursive: true},
                'A': {pattern: /[a-zA-Z0-9]/},
                'S': {pattern: /[a-zA-Z]/}
            }
        };

        // click button tim kiem
        $("#toolbarIdbtnSearchDate").on("click", function () {
            _loadTabHanhChinh(-1);
            _setButton(true);
            _loadGridData(_opt.phongid);
        });

        // L2PT-13299
        $("#toolbarIdgroup_0_Phieu_PhuThu_Scanner").on("click", function () {
            if (!$("#hidKHAMBENHID").val()) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân!');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "RPT_GIAYCAMDOANPHUTHU_CTSCANNER", "pdf", par);
        });

        $("#toolbarIdgroup_phieu_ck").on("click", function () {
            if (!$("#hidKHAMBENHID").val()) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân!');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "PHIEU_KHAMCK_MATHN", "pdf", par);
        });

        $("#toolbarIdgroup_2_rv").on("click", function () {
            $("#toolbarIdbtnKHAC_4").click();
        });

        $("#toolbarIdgroup_0_nghiduongthai").on("click", function () {
            var _hosobenhanid = $('#hidHOSOBENHANID').val();
            if (_hosobenhanid != null && _hosobenhanid != -1) {
                var paramInput = {
                    hosobenhanid: _hosobenhanid
                };
                var url = "manager.jsp?func=../noitru/NTU02D157_NghiDuongThai";
                var popup = DlgUtil.buildPopupUrl("divDlgNDT", "divDlg", url, paramInput, "Phiếu nghỉ dưỡng thai", 1000, 500);
                popup.open("divDlgNDT");
            } else {
                DlgUtil.showMsg('Chưa chọn bệnh nhân');
            }
        });

        $("#toolbarIdgroup_0_ckdtnt").on("click", function () {
            if (!$("#hidKHAMBENHID").val()) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân!');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "GIAYCAMKET_DIEUTRINOITRU", "pdf", par);
        });

        $("#toolbarIdgroup_0_Ketqua_ngoaitru").on("click", function () {
            var _hosobenhanid = $('#hidHOSOBENHANID').val();
            if (_hosobenhanid != null && _hosobenhanid != -1) {
                var par = [{
                    name: 'i_khambenhid',
                    type: 'String',
                    value: $("#hidKHAMBENHID").val()
                }];
                openReport('window', "PHIEUIN_KETQUA_NGOAITRU_25266", "pdf", par);
            } else {
                DlgUtil.showMsg('Chưa chọn bệnh nhân');
            }
        });
        $("#toolbarIdprint_166").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
                return;
            }

            var paramInput = {
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                lnmbp: 4
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In phiếu ĐT các khoa", 1100, 600);
            DlgUtil.open("divDlgDeleteXN");
        });

        // click view anh lon
        $("#imgBN").on("click", function (e) {
            var paramInput = {
                url: $('#imgBN').attr('src')
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgTTA", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K059_show_img", paramInput, 'THÔNG TIN ẢNH', 650, 540);
            DlgUtil.open("dlgTTA");
        });

        $('#txtMABENHNHANTK').change(function () {
            if ($(this).val().length > 9 || $(this).val().length <= 0) {
                _loadGridData(_opt.phongid);
            }
        });

        $("#btnDOIBACSI").on("click", function () {
            paramInput = {
                ssid: ssid
            };

            dlgPopup = DlgUtil.buildPopupUrl("divDlgBacSyChiDinh", "divDlg", "manager.jsp?func=../noitru/NTU01H046_ChonBacsi", paramInput, "Gán bác sỹ", 600, 230);

            DlgUtil.open("divDlgBacSyChiDinh");
        });

        EventUtil.setEvent("assignSevice_saveChangeBSDT", function (e) {
            DlgUtil.showMsg(e.msg);
            $('#labelBACSIDIEUTRIMS').html(e.name);
            DlgUtil.close(e.divId);

            _loadGridData(_opt.phongid);
        });

        // click thay doi trang thai tim kiem
        $('#cboTRANGTHAI').change(function () {
            _loadGridData(_opt.phongid);
        });

        $('#toolbarIdtxtFromDate').change(function () {
            if (cfObj.NGT_CHONNGAY_AUTOSEARCH == "1") {
                _loadGridData(_opt.phongid);
            }
        });

        $('#toolbarIdtxtToDate').change(function () {
            if (cfObj.NGT_CHONNGAY_AUTOSEARCH == "1") {
                _loadGridData(_opt.phongid);
            }
        });

        // click thay doi tu ngay
        $('#toolbarIdtxtFromDate').change(function () {
            var from = $('#toolbarIdtxtFromDate').val().substr(6, 4) + $('#toolbarIdtxtFromDate').val().substr(3, 2) + $('#toolbarIdtxtFromDate').val().substr(0, 2)
                + $('#toolbarIdtxtFromDate').val().substr(11, 2) + $('#toolbarIdtxtFromDate').val().toString().substr(14, 2);

            var sdate = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');
            if (from < sdate && _type != "1" && _type != "2") {
                $("#toolbarId button").addClass('disabled');
                $("#toolbarIdbtnPrint").removeClass('disabled');
                $("#toolbarIdbtnPrintCa").removeClass('disabled');
            } else {
                $("#toolbarId button").removeClass('disabled');
            }
        });

        // click chon benh nhan trong danh sach
        GridUtil.setGridParam(_gridId, {
            onSelectRow: function (index, selected) {
                if (selected === false) {
                    FormUtil.clearForm('divContentHC');
                    $("#toolbarIdbtnLS").attr("disabled", true);
                } else {
                    _selectedRow(index);
                    $("#toolbarIdbtnLS").attr("disabled", false);
                }
                GridUtil.unmarkAll(_gridId);
                GridUtil.markRow(_gridId, index);
                // L2PT-118724 start
                var notification_chiho = $(".check-chiho");
                if (notification_chiho.length > 0) {
	                notification_chiho.alert('close'); // Close the notification
	            }
                checkYeuCauChiHo($("#hidTIEPNHANID").val(), "-2");
                // L2PT-118724 start

                /*var trangThai = $("#" + _gridId).jqGrid('getCell', index, 11);
				switch (Number(trangThai)) {
					case 9:
						$("#toolbarIdbtnThuTienKhac").attr("disabled", true);
						if (cfObj.NGT_BANGT_DISABLE == "1"){
								$('#toolbarIdbtnBANGT').attr("disabled", true);
						}
						break;
					default:
						if (cfObj.NGT_BANGT_DISABLE == "1"){
							$('#toolbarIdbtnBANGT').attr("disabled", false);
						}
						$("#toolbarIdbtnThuTienKhac").attr("disabled", false);
						break;
				}*/
            },
            //tuyenn_add_start_20181102 L2HOTRO-11553
            ondblClickRow: function (id) {
                var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
                var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
                var _trangthaikhambenh = rowData.TRANGTHAIKHAMBENH;
                if (_trangthaikhambenh == "1") {
                    if (cfObj.KB_MHC2_NO_DBLCLICK == "1") {
                        return;
                    }
                    //L2PT-52797 start ttlinh
                    var _ch_chuyenkhoa = cfObj.HIS_CHUYENKHOA_BS_DV; //0: default, 1: canh bao, 2: chan
                    if (_ch_chuyenkhoa && _ch_chuyenkhoa != '0') {
                        var _sql_par = [];
                        _sql_par.push({
                            "name": "[0]",
                            value: opt.user_id
                        });
                        var chuyenkhoa_bs = jsonrpc.AjaxJson.getOneValue("NGT.CHUYENKHOA.BS", _sql_par);
                        if (chuyenkhoa_bs == "0") {
                            DlgUtil.showMsg("Bác sĩ chưa mô tả phạm vi chuyên khoa theo chứng chỉ hành nghề, liên hệ IT để được hỗ trợ.");
                            if (_ch_chuyenkhoa == '2') {
                                return false;
                            }
                        }
                        var _sql_par1 = [];
                        _sql_par1.push({
                            "name": "[0]",
                            value: $('#hidDICHVUID').val()
                        });
                        var chuyenkhoa_dv = jsonrpc.AjaxJson.getOneValue("NGT.CHUYENKHOA.DV", _sql_par1);
                        if (chuyenkhoa_dv == "0") {
                            DlgUtil.showMsg("Dịch vụ có mã " + $("#hidMADICHVU").val() + " chưa mô tả chuyên khoa, vui lòng liên hệ KHTH để xử lý.");
                        }
                        if (chuyenkhoa_bs != "0" && chuyenkhoa_dv != "0" && !chuyenkhoa_bs.includes(chuyenkhoa_dv)) {
                            DlgUtil.showMsg("Bác sĩ có chuyên khoa khác với chuyên khoa của dịch vụ.");
                            if (_ch_chuyenkhoa == '2') {
                                return false;
                            }
                        }
                    }
                    //L2PT-52797 end
                    checklienthongslkham();
                }
            },
            //tuyenn_add_end_20181102
            gridComplete: function (id) {
                if (_opt.hospital_id !== "1111" && cfObj.NGT_HIENTHI_GOISOBN == "0") {
                    $("#goiso").remove();
                }
                if (cfObj.NGT_HUYNHAPVIEN_HIENTHI == 0) {
                    $("#xoabnchonhapvien").remove();
                }
                $(".jqgrow", '#' + _gridId).contextMenu('contextMenu', {
                    bindings: {
                        'yeucaumolaibenhan': function (t) {
                            _molaibenhan(0, "YÊU CẦU MỞ LẠI BỆNH ÁN");
                        },
                        'molaibenhan': function (t) {
                            _molaibenhan(1, "MỞ LẠI BỆNH ÁN");
                        },
                        'goilaibnchuyenphong': function (t) {
                            _goilaibnchuyenkham();
                        },
                        'xoabnchonhapvien': function (t) {
                            var par = [];
                            par.push({
                                "name": "[0]",
                                "value": $("#hidHOSOBENHANID").val()
                            });
                            var hosobenhanid_ntu = jsonrpc.AjaxJson.getOneValue("NGT.GET.KBIDNTU", par);
                            if (hosobenhanid_ntu !== 'null' || hosobenhanid_ntu !== '0') {
                                var myVar = {
                                    khambenhid: hosobenhanid_ntu,
                                    tiepnhanid: $("#hidTIEPNHANID").val()
                                };
                                var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H001.EV029", JSON.stringify(myVar));
                                if (result == -1) {
                                    DlgUtil.showMsg("Xóa bệnh nhân lỗi!", undefined, undefined, "error");
                                } else {
                                    if (result == 2) {
                                        DlgUtil.showMsg("Xóa bệnh nhân không thành công do sai trạng thái!", undefined, undefined, "error");
                                    } else if (result == 3) {
                                        DlgUtil.showMsg("Đã có chỉ định dịch vụ trong khoa nội trú không thể xóa!");
                                    }
                                    else if (result == 9) {
                                        DlgUtil.showMsg("Đã có phiếu khám bệnh vào viện được ký số, không thể xóa!");
                                    }
                                    else {
                                        DlgUtil.showMsg("Xóa bệnh nhân thành công!");
                                        _loadGridData(_opt.phongid);
                                    }
                                }
                            } else {
                                DlgUtil.showMsg("Bệnh nhân không nhập viện không thể xóa!");
                            }
                        },
                        'btnLS_1': function (t) {
                            paramInput = {
                                benhnhanId: $("#hidBENHNHANID").val()
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K025_LichSuDieuTri", paramInput, "LỊCH SỬ ĐIỀU TRỊ", 1350, 600);
                            DlgUtil.open("dlgLichSuDieuTri");
                        },
                        'btnLS_2': function (t) {
                            paramInput = {
                                benhnhanId: $("#hidBENHNHANID").val()
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuBA", "divDlg", "manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn", paramInput, "LỊCH SỬ BỆNH ÁN", 1320, 610);
                            DlgUtil.open("dlgLichSuBA");
                        },

                        'btnLSCongBHYT': function (t) {
                            var paramInput = {
                                MABHYT: $('#txtSOTHEBHYT').val(),
                                TENBENHNHAN: $('#txtTENBENHNHAN').val(),
                                NGAYSINH: NGAYSINH,
                                QRCODE: '',
                                TUNGAY: '',
                                DENNGAY: ''
                            };

                            dlgPopup = DlgUtil.buildPopupUrl(
                                "divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB",
                                paramInput, "Thông tin lịch sử điều trị bệnh nhân", window.innerWidth * 0.95, window.innerHeight * 0.95);

                            DlgUtil.open("divDlgDDT");
                        },
                        //L2PT-61798
                        'btnLSCongGW': function (t) {
                            var paramInput = {
                                MABHYT: $('#txtSOTHEBHYT').val(),
                                NGAYKHAM: $('#txtDENKHAMLUC').val(),
                                MAKCBBD: opt.hospital_code															// MA DON VI DUNG CHECK ;
                            };

                            dlgPopup = DlgUtil.buildPopupUrl(
                                "divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB_GW",
                                paramInput, "Thông tin lịch sử KCB Trong Tỉnh", window.innerWidth * 0.95, window.innerHeight * 0.93);

                            var parent = DlgUtil.open("divDlgDDT");
                        },

                        'btnCapNhatTTHC': function (t) {
                            paramInput = {
                                tiepnhanid: $("#hidTIEPNHANID").val()
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("divDlgSuaBenhNhan", "divDlg", "manager.jsp?func=../noitru/NTU01H020_ThongTinBenhNhan", paramInput, "HIS - Cập nhật bệnh nhân", 1100, 580);

                            DlgUtil.open("divDlgSuaBenhNhan");
                        },

                        'btnLSKCB': function (t) {
                            var paramInput = {
                                MABHYT: $('#txtSOTHEBHYT').val(),
                                TENBENHNHAN: $('#txtTENBENHNHAN').val(),
                                NGAYSINH: NGAYSINH,
                                QRCODE: '',
                                TUNGAY: '',
                                DENNGAY: ''
                            };

                            dlgPopup = DlgUtil.buildPopupUrl(
                                "divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K049_TraCuuCongBYT",
                                paramInput, "Tra cứu thông tin Bộ Y Tế", window.innerWidth * 0.95, window.innerHeight * 0.95);
                            DlgUtil.open("divDlgDDT");
                        },

                        'thanhtoanvp': function (t) {
                            paramInput = {
                                tiepnhanid: $("#hidTIEPNHANID").val()
                            };
                            dlgPopup = DlgUtil.buildPopupUrl("dlgTTVP", "divDlg", "manager.jsp?func=../vienphi/VPI01T006_thanhtoanvienphi", paramInput, "THANH TOÁN VIỆN PHÍ", 1250, 600);
                            DlgUtil.open("dlgTTVP");
                        },

                        'capnhatdtngt': function (t) {
                            DlgUtil.showConfirm("Bạn có muốn cập nhật BADTNGT? ", function (flag) {
                                if (flag) {
                                    _capnhatBADTNGT();
                                }
                            });
                        },

                        'gocapnhatdtngt': function (t) {
                            DlgUtil.showConfirm("Bạn có muốn gỡ cập nhật BADTNGT này? ", function (flag) {
                                if (flag) {
                                    _gocapnhatBADTNGT("0");
                                }
                            });
                        },

                        'gocapnhatdtngtall': function (t) {
                            DlgUtil.showConfirm("Bạn có muốn gỡ cập nhật cả đợt điều trị này? ", function (flag) {
                                if (flag) {
                                    _gocapnhatBADTNGT("1");
                                }
                            });
                        },

                        'nhapbenhlich': function (t) {
                            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in bệnh lịch.');
                                return;
                            }

//	            			if($('#hidXUTRIKHAMBENHID').val() != 7){
//	            				DlgUtil.showMsg('Bệnh nhân chưa có thông tin xử trí chuyển viện. ');
//	            				return;
//	            			}

                            var myVar = {
                                khambenhid: $('#hidKHAMBENHID').val(),
                                benhnhanid: $("#hidBENHNHANID").val(),
                                hosobenhanid: $("#hidHOSOBENHANID").val()
                            };

                            dlgPopup = DlgUtil.buildPopupUrl("dlgPhieuKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K087_BENHLICH", myVar, "Thông tin bệnh lịch", 1200, 550);
                            DlgUtil.open("dlgPhieuKham");
                        },

                        'inbenhlich': function (t) {
                            $("#toolbarIdgroup_2_7").click();
                        },

                        'thietlapuutien': function (t) {
                            DlgUtil.showConfirm("Bạn có muốn thiết lập ưu tiên cho BN này? ", function (flag) {
                                if (flag) {
                                    var _obj = new Object();
                                    _obj.PHONGKHAMDANGKYID = $("#hidPHONGKHAMDANGKYID").val();
                                    _obj.KHAMBENHID = $("#hidKHAMBENHID").val();
                                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K001.TLUT", JSON.stringify(_obj));
                                    if (fl != '0' && fl != '-1') {
                                        DlgUtil.showMsg("Đã thiết lập ưu tiên thành công");
                                        _loadGridData(_opt.phongid);
                                    } else if (fl == '0') {
                                        DlgUtil.showMsg("Bệnh nhân đang là đối tượng ưu tiên. ");
                                    } else {
                                        DlgUtil.showMsg("Lỗi trong quá trình xử lý", undefined, undefined, "error");
                                    }

                                }
                            });
                        }, 'tangquasn': function (t) {
                            _tangqua_sinhnhat();
                        }, 'goiso': function (t) {
                            _get_filegoiso();
                        }
                    },
                    onContextMenu: function (event, menu) {
                        var rowId = $(event.target).parent("tr").attr("id");
                        var grid = $('#' + _gridId);
                        grid.setSelection(rowId);
                        return true;
                    }
                });

                var ids = $("#" + _gridId).getDataIDs();
                for (var i = 0; i < ids.length; i++) {
                    var id = ids[i];
                    var row = $("#" + _gridId).jqGrid('getRowData', id);
                    var _icon = '';
                    var _iconcls = '';
                    if (row.TRANGTHAIKHAMBENH == 1) {
                        if (row.BHYT_LOAIID == '4' && row.DACODICHVUTHUTIEN == '1' &&
                            cfObj.NGT_ICON_BNTRAITUYEN == '1' && row.DONGTIEN != '0') {
                            _icon = '<center><img src="' + _opt.imgPath[5] + '" width="15px"></center>';
                        } else if (row.DACODICHVUTHUTIEN == '1') {
                            _icon = '<center><img src="' + _opt.imgPath[0] + '" width="15px"></center>';
                        } else {
                            _icon = '<center><img src="' + _opt.imgPath[5] + '" width="15px"></center>';
                        }
                    } else if (row.TRANGTHAIKHAMBENH == 4) {
                        _icon = '<center><img src="' + _opt.imgPath[1] + '" width="15px"></center>';
                    } else if (row.TRANGTHAIKHAMBENH == 9) {
                        _icon = '<center><img src="' + _opt.imgPath[2] + '" width="15px"></center>';
                    }

                    if (row.KQCLS == "1") {
                        _iconcls = '<center><img src="' + _opt.imgPath[3] + '" width="15px"></center>';
                    } else if (row.KQCLS == "2") {
                        _iconcls = '<center><img src="' + _opt.imgPath[4] + '" width="15px"></center>';
                    }

                    $("#" + _gridId).jqGrid('setCell', id, 1, _icon);
                    $("#" + _gridId).jqGrid('setCell', id, 2, _iconcls);

                    if (row.KSKCN == "1") {
                        $("#" + _gridId).setCell(id, 'TENBENHNHAN', '', {'font-weight': 'bold'});
                        $("#" + _gridId).setCell(id, 'MABENHNHAN', '', {'font-weight': 'bold'});
                        $("#" + _gridId).setCell(id, 'MAHOSOBENHAN', '', {'font-weight': 'bold'});
                        $("#" + _gridId).setCell(id, 'MA_BHYT', '', {'font-weight': 'bold'});
                        $("#" + _gridId).setCell(id, 'SOTHUTU', '', {'font-weight': 'bold'});
                        $("#" + _gridId).setCell(id, 'NAMSINH', '', {'font-weight': 'bold'});
                        $("#" + _gridId).setCell(id, 'GIOITINH', '', {'font-weight': 'bold'});
                        $("#" + _gridId).setCell(id, 'DIACHI', '', {'font-weight': 'bold'});
                        $("#" + _gridId).setCell(id, 'TENBENHNHAN', '', {'font-style': 'italic'});
                        $("#" + _gridId).setCell(id, 'MABENHNHAN', '', {'font-style': 'italic'});
                        $("#" + _gridId).setCell(id, 'MAHOSOBENHAN', '', {'font-style': 'italic'});
                        $("#" + _gridId).setCell(id, 'MA_BHYT', '', {'font-style': 'italic'});
                        $("#" + _gridId).setCell(id, 'SOTHUTU', '', {'font-style': 'italic'});
                        $("#" + _gridId).setCell(id, 'NAMSINH', '', {'font-style': 'italic'});
                        $("#" + _gridId).setCell(id, 'GIOITINH', '', {'font-style': 'italic'});
                        $("#" + _gridId).setCell(id, 'DIACHI', '', {'font-style': 'italic'});
                    }

                    //hunglv L2PT-7034
//					if(row.DOITUONGBENHNHANID == 1) $("#"+_gridId).jqGrid ('setCell', id, 'DOITUONGBENHNHAN_TEXT', 'BHYT');
//					else if(row.DOITUONGBENHNHANID == 2) $("#"+_gridId).jqGrid ('setCell', id, 'DOITUONGBENHNHAN_TEXT', 'Viện phí');
//					else if(row.DOITUONGBENHNHANID == 3) $("#"+_gridId).jqGrid ('setCell', id, 'DOITUONGBENHNHAN_TEXT', 'Dịch vụ');
//					else if(row.DOITUONGBENHNHANID == 5) $("#"+_gridId).jqGrid ('setCell', id, 'DOITUONGBENHNHAN_TEXT', 'Miễn phí');

                    // CHECK UU TIEN KHAM ID VA BENH AN DIEU TRI NGOAI TRU;
                    if (row.HIENTHIBNVPI == "1" && _hienthiBNVPI != "0") {
                        $("#" + _gridId).jqGrid('setRowData', id, "", {color: _hienthiBNVPI});
                    } else if (row.UUTIENKHAMID == "3") {
                        $("#" + _gridId).jqGrid('setRowData', id, "", {color: 'blue'});
                    } else if (row.CHUYENKHAMNGT == "1") {
                        $("#" + _gridId).jqGrid('setRowData', id, "", {color: 'orange'});
                    } else if (typeof row.DIEUTRI_NGT != 'undefined' && row.DIEUTRI_NGT != "0" && row.DIEUTRI_NGT != "") {
                        $("#" + _gridId).jqGrid('setRowData', id, "", {color: 'green'});							// green;
                    }

                    // sondn L2PT-8260
                    if (_mau_khamtrongthang != '0') {
                        var _maukhamtrongthangArr = _mau_khamtrongthang.split('@'); 									// 0@0@0@brown@purple
                        for (t = _maukhamtrongthangArr.length - 1; t >= 0; t--) {
                            if (_maukhamtrongthangArr[t] != '0' && row.LANKHAMTRONGTHANG == t) {
                                $("#" + _gridId).jqGrid('setRowData', id, "", {color: _maukhamtrongthangArr[t]});

                                $("#" + _gridId).setCell(id, 'TENBENHNHAN', '', {'font-weight': 'bold'});
                                $("#" + _gridId).setCell(id, 'MABENHNHAN', '', {'font-weight': 'bold'});
                                $("#" + _gridId).setCell(id, 'MAHOSOBENHAN', '', {'font-weight': 'bold'});
                                $("#" + _gridId).setCell(id, 'MA_BHYT', '', {'font-weight': 'bold'});
                                $("#" + _gridId).setCell(id, 'SOTHUTU', '', {'font-weight': 'bold'});

                                break;
                            }
                        }
                    }
                    // end sondn L2PT-8260
                    if (_mau_conthuoc != '0') {
                        if (row.NGAYTHUOC != "" && row.NGAYTHUOC != null) {
                            var t_ngaythuoc = row.NGAYTHUOC;
                            var ngay = t_ngaythuoc.split('/');
                            var ngaythuoc = ngay[2].substring(0, 4) + ngay[1] + ngay[0];							// 11/11/2021 10:20:00
                            var t_ngaytn = row.NGAYTIEPNHAN; 													// 05/05/2021 14:33:41
                            ngay = t_ngaytn.split('/');
                            var ngaytn = ngay[2].substring(0, 4) + ngay[1] + ngay[0];

                            if (parseInt(ngaythuoc) > parseInt(ngaytn)) {
                                $("#" + _gridId).jqGrid('setRowData', id, "", {color: _mau_conthuoc});							// blue
                            }
                        }
                    }
                    if (mauconthuoc_bnuutien != "0" && mauconthuoc_bnthuong != "0") {
                        if (row.NGAYTHUOC != "" && row.NGAYTHUOC != null) {
                            var t_ngaythuoc = row.NGAYTHUOC;
                            var ngay = t_ngaythuoc.split('/');
                            var ngaythuoc = ngay[2].substring(0, 4) + ngay[1] + ngay[0];
                            var ngaykham = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');
                            if (parseInt(ngaythuoc) > parseInt(ngaykham)) {
                                if (row.UUTIENKHAMID != "0") {
                                    $('#' + _gridId).find("tr[id='" + id + "']").find('td').css('background-color', mauconthuoc_bnuutien);
                                } else {
                                    $('#' + _gridId).find("tr[id='" + id + "']").find('td').css('background-color', mauconthuoc_bnthuong);
                                }
                            }
                        }
                    }
                    //tuyennx_add_start_20200823 L2PT-26511
                    if (NGT_DSKHAM_PHANBIETMAU == 1) {
                        var _sql_par = [];
                        _sql_par.push({
                            "name": "[0]",
                            value: row.SUB_DTBNID
                        });
                        var checkmau = jsonrpc.AjaxJson.getOneValue("NGT.HIENTHI.MAU", _sql_par)
                        if (checkmau)
                            $("#" + _gridId).jqGrid('setRowData', id, "", {color: checkmau});
                    }
                    //tuyennx_add_end_20200823 L2PT-26511
                    //L2PT-44037
                    if (NGT_HIENTHIMAU_BADN && NGT_HIENTHIMAU_BADN != '0' && row.BADAINGAY == '1') {
                        $("#" + _gridId).jqGrid('setRowData', id, "", {color: NGT_HIENTHIMAU_BADN});
                    }

                    if (row.KHAMBENHID == $("#hidKHAMBENHID").val() && row.TRANGTHAIKHAMBENH != 1) {
                        GridUtil.unmarkAll(_gridId);
                        GridUtil.markRow(_gridId, id);
                        $('#' + _gridId).find("tr[id='" + id + "']").find('td').trigger("click");
                    }

                    $("#" + _gridId).setCell(id, 'TENBENHNHAN', '', {'font-weight': 'bold'});
                    $("#" + _gridId).setCell(id, 'MABENHNHAN', '', {'font-weight': 'bold'});
                    $("#" + _gridId).setCell(id, 'MAHOSOBENHAN', '', {'font-weight': 'bold'});
                    $("#" + _gridId).setCell(id, 'MA_BHYT', '', {'font-weight': 'bold'});
                    $("#" + _gridId).setCell(id, 'SOTHUTU', '', {'font-weight': 'bold'});

                    if (row.SINHNHAT == "1" && _setmausn == "1") {
                        $("#" + _gridId).jqGrid('setRowData', id, "", {color: '#F700C0'});
                    }

                    if (row.MADANGKY != 'undefined' && row.MADANGKY != '' && row.MADANGKY != '0') {
                        $("#" + _gridId).jqGrid('setRowData', id, "", {color: NGT_HIENTHIMAU_DATLICH});
                    }
                    if (maubnksk != '0' && row.KSK == "1") {
                        $("#" + _gridId).jqGrid('setRowData', id, "", {color: maubnksk});
                    }
                }
            }
        });

        // TICH HOP KHAM ONLINE
        $("#" + _gridId).bind("jqGridAfterLoadComplete", function (e, rowid, orgClickEvent) {
            var htmlGOIKHAM = '<button type="button" class="btn btn-sm btn-primary heightbtn" id="@thisisid" style="width:90%;height:90%"><span class="glyphicon glyphicon-earphone"></span></button>';
            var htmlXEMLAI = '<button type="button" class="btn btn-sm btn-primary heightbtn" id="@thisisid" style="width:90%;height:90%"><span class="glyphicon glyphicon-earphone"></span></button>';
            var html = "";

            var data_grDaChon = $("#" + _gridId).getDataIDs();
            for (var i = 0; i < data_grDaChon.length; i++) {
                var tt = $("#" + _gridId).jqGrid('getRowData', data_grDaChon[i]);

                html = "";
                html = htmlGOIKHAM.replace('@thisisid', "onlineGOI_" + tt.PHONGKHAMDANGKYID);
                $("#" + _gridId).jqGrid('setCell', data_grDaChon[i], 'onlineGOI', html);
                $("#onlineGOI_" + tt.PHONGKHAMDANGKYID).on('click', function (e) {
                    var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
                    var rowData = $("#" + _gridId).jqGrid('getRowData', rowIdTmp);
                    var param = "";
                    window.open('manager.jsp?func=../ngoaitru/NGT03K009_GOIKHAM_VIDEO&manguoigoi=' + opt.fullname + '&manguoinhan=' + rowData.MANGUOINHAN + '&madangky=' + rowData.MADANGKY + '&showMode=dlg' + param, '',
                        'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
                });
            }
        });
        // END TICH HOP KHAM ONLINE

        // open popup yeu cau mo lai/mo lai benh an
        function _molaibenhan(_kieu, _title) {
            if (cfObj.HIS_KHOA_BENHAN == '1') {
                var _hosobenhanid = $('#hidHOSOBENHANID').val();
                var check_khoa = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.LAY.TTKHOA", {
                    "LOAIKHOA": "1",
                    "GIATRI": _hosobenhanid.toString()
                });
                if (check_khoa == '1') {
                    DlgUtil.showMsg('Bệnh án đã được khóa, liên hệ phòng kế toán để mở khóa');
                    return false;
                }
            }
            //tuyennx_add_start_L2PT-1744
            if (cfObj.HIS_KHOA_SOLIEU == '1') {
                var sql_par = [$("#hidTIEPNHANID").val(), jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS')];
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC32.KSL.05", sql_par.join('$'));
                var rets = ret.split(';');
                if (rets[0] > 0) {
                    DlgUtil.showMsg(rets[1]);
                    return;
                }
            }
            //tuyennx_add_end_L2PT-1744
            //tuyennx_add_start 20170412
            var check = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK_MOBA", $('#hidTIEPNHANID').val());
            if (check == 1) {
                DlgUtil.showMsg('Đã duyệt kế toán/bảo hiểm ko thể mở lại bệnh án');
                return false;
            }
            //tuyennx_add_end 20170412
            if ($('#hidDUYETKETOAN').val() == "1" || $('#hidDUYETBH').val() == "2") {
                DlgUtil.showMsg('Đã duyệt kế toán/bảo hiểm ko thể mở lại bệnh án');
                return false;
            }

            // Chan doi voi truong hop BN nhap vien va tat cau hinh;
            if (_tsmobenhan == "0" && check == 2 && $('#hidXUTRIKHAMBENHID').val() == "6") {
                DlgUtil.showMsg('Bệnh nhân đã nhập viện không thể mở bệnh án');
                return false;
            }

            $('#toolbarIdbtnSearchDate').focus();
            var myVar = {
                khambenhid: $('#hidKHAMBENHID').val(),
                benhnhanid: $('#hidBENHNHANID').val(),
                tiepnhanid: $('#hidTIEPNHANID').val(),
                hosobenhanid: $('#hidHOSOBENHANID').val(),
                phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
                kieu: _kieu // 1: mo benh an, 0: yêu cầu mở.
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgMOLAIBA", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K029_YeuCauMoBenhAn", myVar, _title, 500, 220);
            DlgUtil.open("dlgMOLAIBA");

            //tuyennx_edit_20170713_start
            EventUtil.setEvent("assignSevice_mobenhan", function (e) {
                if (e.msg == "1") {
                    if ($('#hidXUTRIKHAMBENHID').val() != '2' && $('#hidXUTRIKHAMBENHID').val() != '6'
                        && $('#hidXUTRIKHAMBENHID').val() != '12' && cfObj.NGT_LUUQUANLYBENHAN == '1') {
                        luuQuanLyBenhAn('2');
                    }
                    //tuyennx_edit_start_20190425 L1PT-661 L2PT-14910
                    DlgUtil.showMsg('Bệnh nhân đã mở bệnh án', undefined, _HIS_TIMEOUT_THONGBAO);
                    //tuyennx_edit_end_20190425 L1PT-661

                    DlgUtil.close("dlgMOLAIBA");
                    _loadGridData(_opt.phongid);
                } else if (e.msg == "2") {
                    //tuyennx_edit_start_20190425 L1PT-661 L2PT-14910
                    DlgUtil.showMsg('Đã gửi yêu cầu mở lại bệnh án', undefined, _HIS_TIMEOUT_THONGBAO);
                    //tuyennx_edit_end_20190425 L1PT-661

                    DlgUtil.close("dlgMOLAIBA");
                    _loadGridData(_opt.phongid);
                } else {
                    DlgUtil.showMsg('Cập nhật thông tin không thành công', undefined, undefined, "error");
                }
            });
            //tuyennx_edit_20170713_end
        }

        // click button bắt đầu trên toolbar
        //tuyenn_edit_start_20181102 L2HOTRO-11553
        $("#toolbarIdbtnStart").on("click", function (e) {
            //L2PT-52797 start ttlinh
            var _ch_chuyenkhoa = cfObj.HIS_CHUYENKHOA_BS_DV; //0: default, 1: canh bao, 2: chan
            if (_ch_chuyenkhoa && _ch_chuyenkhoa != '0') {
                var _sql_par = [];
                _sql_par.push({
                    "name": "[0]",
                    value: opt.user_id
                });
                var chuyenkhoa_bs = jsonrpc.AjaxJson.getOneValue("NGT.CHUYENKHOA.BS", _sql_par);
                if (chuyenkhoa_bs == "0") {
                    DlgUtil.showMsg("Bác sĩ chưa mô tả phạm vi chuyên khoa theo chứng chỉ hành nghề, liên hệ IT để được hỗ trợ.");
                    if (_ch_chuyenkhoa == '2') {
                        return false;
                    }
                }
                var _sql_par1 = [];
                _sql_par1.push({
                    "name": "[0]",
                    value: $('#hidDICHVUID').val()
                });
                var chuyenkhoa_dv = jsonrpc.AjaxJson.getOneValue("NGT.CHUYENKHOA.DV", _sql_par1);
                if (chuyenkhoa_dv == "0") {
                    DlgUtil.showMsg("Dịch vụ có mã " + $("#hidMADICHVU").val() + " chưa mô tả chuyên khoa, vui lòng liên hệ KHTH để xử lý.");
                }
                if (chuyenkhoa_bs != "0" && chuyenkhoa_dv != "0" && !chuyenkhoa_bs.includes(chuyenkhoa_dv)) {
                    DlgUtil.showMsg("Bác sĩ có chuyên khoa khác với chuyên khoa của dịch vụ.");
                    if (_ch_chuyenkhoa == '2') {
                        return false;
                    }
                }
            }
            //L2PT-52797 end
          //L2PT-125837 start
            /* if (cfObj.CHK_CHAN_CHIDINH_TRUNGTIME != '0') {
				var objchk = new Object();
				objchk["KHAMBENHID"] = $("#hidKHAMBENHID").val();
				objchk["MAUBENHPHAMID"] = $("#hidMAUBENHPHAMID").val() == "" ? '-1' : $("#hidMAUBENHPHAMID").val();
				objchk["TGCHIDINH"] = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
				var msgchk = '';
				var reschk = jsonrpc.AjaxJson.ajaxCALL_SP_S("CD.CB_TRUNGTIME", JSON.stringify(objchk));
				if (reschk && reschk != '0') {
					if (reschk == '-1') {
						DlgUtil.showMsg('Có lỗi khi xử lý');
						return false;
					} else {
						msgchk = 'Thời gian y lệnh của bệnh nhân quá gần với bệnh nhân trước đó\n' + reschk + '.' + ' Thời gian y lệnh tối thiểu giữa 2 bệnh nhân là 2 phút.';//L2PT-86422
						if (cfObj.CHK_CHAN_CHIDINH_TRUNGTIME == '1') {
							if (!confirm(msgchk + '\nCó tiếp tục?')) {
								return false;
							}
						} else {
							DlgUtil.showMsg(msgchk);
							return false;
						}
					}
				}
			} */
            //L2PT-125837 end
            startkham = 1;
            checklienthongslkham();
        });

        function checklienthongslkham() {
            if (cfObj.NGT_LIENTHONG_LUOTKHAM_BSI == '1' && $("#hidDOITUONGBENHNHANID").val() == '1') {
                var _sql_par = [];
                _sql_par.push({
                    "name": "[0]",
                    value: opt.user_id
                });
                var mabsi = jsonrpc.AjaxJson.getOneValue("HIS.GETMABS", _sql_par);
                var objData = {
                    "NGAY_KHAM": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'),
                    "MA_BAC_SI": mabsi
                };
                var request_url = '/vnpthis/api/hisgw/lienthong/LUOTKHAMBHYT';
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(objData),
                    url: request_url,
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer ' + jsonrpc.AjaxJson.uuid);
                    },
                    success: function (data) {
                        console.log("response success=" + JSON.stringify(data));
                        var thongbao = "";
                        var luotkham_check = cfObj.NGT_SOLUOTKHAM_CHECKLIENTHONG;
                        var check_chan = cfObj.NGT_CHECKLIENTHONG_CHAN;
                        if (data.code == '00') {
                            var result = data.result;
                            for (var i = 0; i < result.length; i++) {
                                if (result[i].code == "00") {
                                var result1 = result[i].result;
                                if (parseInt(result1.SO_LUONG) > luotkham_check) {
                                    thongbao = result1.SO_LUONG + " lượt khám tại " + result1.TENCSKCB + "; "
                                    }
                                }
                            }
                            if (thongbao) {
                                if (check_chan == 1) {
                                    DlgUtil.showMsg("Bác sỹ đã khám " + thongbao + " .Không thể tiếp tục khám! ");
                                    return;
                                } else {
                                    DlgUtil.showConfirm("Bác sỹ đã khám " + thongbao + " .Có tiếp tục?", function (flag) {
                                        if (flag) {
                                            _start_kham();
                                        }
                                    });
                                }
                            } else {
                                _start_kham();
                            }
                        } else {
                            _start_kham();
                        }
                    },
                    error: function (xhr) {
                        _start_kham();
                    }
                });
            } else {
                _start_kham();
            }
        }

        function _start_kham() {
            var _sql_par = [];
            _sql_par.push({
                "name": "[0]",
                value: opt.user_id
            });
            var rs = jsonrpc.AjaxJson.getOneValue("GET.OFFICER.KHAMBHYT", _sql_par);
            var NGT_CHANTHAOTAC_NVKHONGKHAMBHYT = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'NGT_CHANTHAOTAC_NVKHONGKHAMBHYT');
            var DOITUONGBENHNHANID = $("#hidDOITUONGBENHNHANID").val();
            if (NGT_CHANTHAOTAC_NVKHONGKHAMBHYT == 1 && rs == '0' && DOITUONGBENHNHANID == '1') {
                DlgUtil.showMsg('Người dùng không có quyền thao tác đối với bệnh nhân BHYT ');
                return;
            }
            //hunglv L2PT-8984 L2PT-9574
            var kocheck_capcuu = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", '65BN_KOCHECK_CAPCUU');
            if (kocheck_capcuu == 1 && _hinhthucvaovienid == 2) {
                _start_kham2();
            } else {
                if (_type != 1 && parseInt(cfObj.CHECK65BHYT) > 0 && ( (cfObj.CHECK65BN_CHECKALL_DOITUONG == 0 && DOITUONGBENHNHANID == 1) || cfObj.CHECK65BN_CHECKALL_DOITUONG == 1 ) ) {
                    var so_bn_bhyt = 0;
                    var _obj = new Object();
                    _obj.TYPE = "1";
                    var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK.65BNBHYT", JSON.stringify(_obj));
                    if (ret != 0) {
                        so_bn_bhyt = ret;
                    }
                    if (parseInt(so_bn_bhyt) >= parseInt(cfObj.CHECK65BHYT)){ // cảnh báo nếu khám từ bn thứ (CHECK65BHYT+1)
                        if (cfObj.CHECK65BHYT_CHAN == "0") {
                            DlgUtil.showConfirm("Khám quá " + cfObj.CHECK65BHYT + " bệnh nhân Bảo hiểm y tế trong ngày, có tiếp tục khám?", function (flag) {
                                if (flag) {
                                    _start_kham2();
                                }
                            });
                        } else {
                            DlgUtil.showMsg("Khám quá " + cfObj.CHECK65BHYT + " bệnh nhân Bảo hiểm y tế trong ngày, không thể tiếp tục khám");
                            return;
                        }
                    } else {
                        _start_kham2();
                    }
                } else {
                    _start_kham2();
                }
            }
        }

        function _checkData(mode) {
            // SONDN 17/12/2019 L2PT-13468
            var _sql_par = [];
            _sql_par.push($("#hidTIEPNHANID").val());
            _sql_par.push($("#hidKHAMBENHID").val());
            _sql_par.push($("#hidPHONGKHAMDANGKYID").val());
            var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK.DATA1", _sql_par.join('$'));
            if (_result == 1) {
                DlgUtil.showMsg('Bệnh nhân đã duyệt kế toán không thể thao tác');
            } else if (_result == 2) {
                //tuyennx edit start dklongan L2PT-13323
                if ((mode != 11 && mode != 3 && mode != 12) || cfObj.CHAN_CDDV_KETHUOC == '0') {
                    DlgUtil.showMsg('Bệnh nhân đã nhập viện không thể thao tác');
                    return _result;
                }
                return;
                //tuyennx edit end dklongan
            }
            return _result;
            // END SONDN 17/12/2019 L2PT-13468
        }

        function _start_kham2() {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            var objData = new Object();
            objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
            objData['KHAMBENHID'] = $("#hidKHAMBENHID").val();
            objData['TIEPNHANID'] = $("#hidTIEPNHANID").val();
            objData['DOITUONGID'] = $("#hidDOITUONGBENHNHANID").val();
            objData['PHONGID'] = _opt.phongid;
            objData["BSIKHAMID"] = $("#hidBACSYKE").val();
            objData["HOSOBENHANID"] = $("#hidHOSOBENHANID").val();

            // kiem tra con thuoc va xac nhan truoc khi vao kham:
            var b_MaBenhNhan = $("#txtSOTHEBHYT").val();
            var b_Loai = "2"; 			// ?
            var _objData = new Object();
            _objData["tenbenhnhan"] = $("#txtTENBENHNHAN").val();
            _objData["ngaysinh"] = NGAYSINH;
            _objData["gioitinhid"] = $("#cboGIOITINH").val() == "Nam" ? "1" : "2";
            _objData["benhnhanid"] = $("#hidBENHNHANID").val();
            _objData["khambenhid"] = $("#hidKHAMBENHID").val();

            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.TKBN1", b_MaBenhNhan + '$' + b_Loai + '$' + JSON.stringify(_objData));

            if (_canhbaobdkham == "1" && data_ar != null && data_ar.length > 0) {
                if (data_ar[0].NGAYTHUOC != "" && data_ar[0].NGAYTHUOC != null) {
                    var t_ngaythuoc = data_ar[0].NGAYTHUOC;
                    var ngay = t_ngaythuoc.split('/');
                    var ngaythuoc = ngay[2].substring(0, 4) + ngay[1] + ngay[0];
                    var ngaytn = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');
                    var ngaykedon = data_ar[0].NGAYMAUBENHPHAM;

                    if (parseInt(ngaykedon) != parseInt(ngaytn) && parseInt(ngaythuoc) > parseInt(ngaytn)) {
                        DlgUtil.showConfirm('Bệnh nhân đang còn thuốc của lần khám trước, có tiếp tục bắt đầu khám', function (flag) {
                            if (flag) {
                                _batdaukhamm(objData);
                            }
                        });
                    } else {
                        _batdaukhamm(objData);
                    }
                } else {
                    _batdaukhamm(objData);
                }
            } else {
                _batdaukhamm(objData);
            }
        };

        //tuyenn_edit_end_20181102 L2HOTRO-11553

        function _batdaukhamm(objData) {
            var rs = dayCongBYT();
            if (rs == 1)
                return;
            else if (rs != 1 && rs != 0) {
                DlgUtil.showConfirm("Kết quả kiểm tra cổng BYT: " + rs + "\n Bạn có muốn tiếp tục?", function (flag) {
                    if (flag) {
                        _batdaukham(objData);
                    } else {
                        dayCongBYT_KTK();
                    }
                });
            } else if (rs == 0) {
                if (cfObj.NGT_CHECKTAMUNG_BDKHAM != '0') {
                    var sql_par = new Object();
                    sql_par.TIEPNHANID = $("#hidTIEPNHANID").val();
                    sql_par.DOITUONGBENHNHANID = $("#hidDOITUONGBENHNHANID").val();
                    sql_par.PHONGID = _opt.phongid;
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.TAMUNGBDKHAM", JSON.stringify(sql_par));
                    if (fl == null || fl == 'null' || fl == '' || fl == 0) {
                        if (cfObj.NGT_CHECKTAMUNG_BDKHAM == 2) {
                            DlgUtil.showMsg('Bệnh nhân cần phải tạm ứng tiền khám trước khi vào khám');
                            return;
                        } else {
                            DlgUtil.showConfirm("Bệnh nhân chưa tạm ứng. Bạn có muốn tiếp tục ?", function (flag) {
                                if (flag) {
                                    _batdaukham(objData);
                                }
                            });
                        }
                    } else {
                        _batdaukham(objData);
                    }
                } else {
                    _batdaukham(objData);
                }
            }
        }

        function _batdaukham(objData) {
            objData.CHECKBDKHAM = checkbdkham_3phut;
            objData.KB1 = 1;
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S(_SQL[3], JSON.stringify(objData));
            if (ret == '1') {
                checkbdkham_3phut = 0;
                _setButton(false);
                /*	var item_id = $("#hidINDEX").val();
				$("#"+_gridId).jqGrid ('setCell', item_id, 8, 4);*/

                $("#toolbarIdbtnStart").attr("disabled", true);

                var index = $('#hidINDEX').val();

                if ($("#" + _gridId).jqGrid('getCell', index, "TRANGTHAIKHAMBENH") == 1) {
                    if (cfObj.NOTIFY_APP_VNCARE == 1) {
                        sendNotifyMH2(1);
                    }
                }
                _icon = '<center><img src="' + _opt.imgPath[1] + '" width="15px"></center>';

                $("#" + _gridId).jqGrid('setCell', index, 1, _icon);
                $("#" + _gridId).jqGrid('setCell', index, 11, 4);

                var objj = new Object();
                var ttkb = "4";				// dang kham;
                objj._xutrikhambenh = "0"; 			// khong co xu tri;
                loadTabStatus(ttkb, objj);

                var show = cfObj.NGT_MO_POPUP_BATDAUKHAM;
                if (show == '1') {
                    $("#toolbarIdbtnExam").trigger("click");
                } else if (show == '2') {
                    $("#toolbarIdbtnService").trigger("click");
                }
                gw_batdaukham($('#hidTIEPNHANID').val());//L2PT-40477
                var sql_par = [];
                sql_par.push({	"name" : "[0]",	value : $('#hidKHAMBENHID').val()});
                var countpk = jsonrpc.AjaxJson.getOneValue("NGT.COUNTPK", sql_par);
                if (cfObj.QD130_DAYXMLCHECKIN_BDKHAM == 1 && $("#" + _gridId).jqGrid('getCell', index, "TRANGTHAIKHAMBENH") == 1 && countpk == 1) {
                    setTimeout(function () {
                        gui_cong_bhxh('', $('#hidTIEPNHANID').val(), '', "0", "1", true)
                    }, 3000)
                }
            } else if (ret == '200') {
                DlgUtil.showMsg('Bệnh nhân đối tượng BHYT + DV nhưng chưa thanh toán tiền DV');
            } else if (ret == '300' || ret == '301' || ret == '302' || ret == '303'
                || ret == '304' || ret == '305' || ret == '306' || ret == '307'
                || ret == '308' || ret == '309' || ret == '310' || ret == '311') {
                DlgUtil.showMsg('Bệnh nhân chưa thanh toán tiền công khám');
            } else if (ret == '500') {
                DlgUtil.showMsg('Thiếu thông tin mã bác sỹ, hoặc user này không phải là bác sỹ, không thể bắt đầu khám. ');
            }	//L2PT-21593
            else if (ret == '800') {
                DlgUtil.showMsg("BN có thời gian chỉ định dịch vụ nhỏ hơn thời gian bắt đầu khám, vui lòng kiểm tra lại!");
                return;
            } else if (ret == '600') {
                DlgUtil.showMsg("Tài khoản đã khám đủ " + parseInt(cfObj.CHECK65BHYT) + " bệnh nhân Bảo hiểm y tế trong ngày, không thể tiếp tục khám!");
                return;
            } else if (ret == '750') {
                DlgUtil.showMsg("Thời gian bắt đầu trùng ngày giờ phút, thử lại trong ít phút!");
                return;
            } else if (ret == '650') {
                DlgUtil.showMsg('Thời gian khám bệnh giữa các bệnh nhân chưa đủ số phút cấu hình trong tham số, không thể thực hiện!');
                return;
            } else if (ret == '900') {
                DlgUtil.showMsg('Bệnh nhân chưa đến thời gian khám đặt lịch, yêu cầu chờ trong ít phút!');
                return;
            }else if (ret == '250') {
                DlgUtil.showMsg('Còn phòng khám chưa kết thúc không thể bắt đầu khám!');
                return;
            }else if (ret == '950') {
                DlgUtil.showMsg('Thời gian từ lúc tiếp nhận đến lúc bắt đầu khám chưa đủ '+  cfObj.NGT_CHECKTGBDKHAM_TGTIEPNHAN + ' phút, không thể bắt đầu!');
                return;
            } else if (ret.split('@')[0] == '3phut') {
                if (cfObj.NGT_CHECKTGIANBDKHAM_2BN.split(';')[0] == 1) {
                    DlgUtil.showConfirm("Thời gian bắt đầu khám của bệnh nhân quá gần với bệnh nhân trước đó " + ret.split('@')[1] + ". " +
                        "Thời gian bắt đầu khám tối thiểu giữa 2 bệnh nhân là "+cfObj.NGT_CHECKTGIANBDKHAM_2BN.split(';')[1] +" phút. Bạn có muốn tiếp tục không?!", function (flag) {
                        if (flag) {
                            checkbdkham_3phut = 1;
                            _batdaukham(objData);
                        } else {
                            return false;
                        }
                    });
                    return false;
                } else if (cfObj.NGT_CHECKTGIANBDKHAM_2BN.split(';')[0] == 2) {
                    DlgUtil.showMsg("Thời gian bắt đầu khám của bệnh nhân quá gần với bệnh nhân trước đó " + ret.split('@')[1] + ". " +
                        "Thời gian bắt đầu khám tối thiểu giữa 2 bệnh nhân là "+cfObj.NGT_CHECKTGIANBDKHAM_2BN.split(';')[1] +" phút. Không thế bắt đầu khám!");
                    return;
                }
            }
        }

        //tuyennx_add_start tích hợp cổng dữ liệu y tế
        function dayCongBYT() {
            var sql_par = [];
            sql_par.push({"name": "[0]", "value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')});
            var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT", sql_par);
            vsothutu = JSON.parse(vsothutu);
            if (vsothutu[0].BYTDAYDL == "1") {
                var _parram = ["1"];
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK", _parram.join('$'));
                var data_bv = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETTT_BV", []);

                var objCHECKIN = new Object();
                objCHECKIN.MA_LK = $("#hidHOSOBENHANID").val();
                objCHECKIN.Sender_Code = opt.hospital_code;
                objCHECKIN.Sender_Name = "";
                objCHECKIN.Action_Type = "1";				// 0: bắt đầu khám, 1: kết thúc khám
                objCHECKIN.Transaction_Type = "M0001";
                objCHECKIN.MABENHVIEN = data_ar[0].I_U1;
                objCHECKIN.MA_THE = $("#txtSOTHEBHYT").val();
                objCHECKIN.NGAYGIOKHAM = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');
                var objHeader = XML_BYT_TaoHeader(objCHECKIN); 										// tao doi tuong header;
                var objIn = XML_BYT_TaoTheCHECKIN(objCHECKIN); 									// tao the
                var obj3 = XML_BYT_TaoKhung(objHeader, objIn, "1"); 											// tao JSON full => XML

                var resultCongBYT = ajaxSvc.CongDLYTWS.guiTTBDK(vsothutu[0].BYTURL
                    , data_ar[0].I_U1
                    , data_ar[0].I_P1
                    , data_ar[0].I_U1						// csytid
                    , data_bv[0].MADIAPHUONG							// ma tinh
                    , obj3);
                var rets = resultCongBYT.split(';');
                if (rets[0] == '10') {
                    return rets[1];
                } else if (rets[0] != '0') {
                    DlgUtil.showMsg("Lỗi đẩy dữ liệu cổng y tế: " + rets[1], undefined, undefined, "error");
                    if (vsothutu[0].BYTSTOPCHUCNANG == "1")
                        return 1;
                }

            }
            return 0;
        }

        function dayCongBYT_KTK() {
            var sql_par = [];
            sql_par.push({"name": "[0]", "value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')});
            var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT", sql_par);
            vsothutu = JSON.parse(vsothutu);
            if (vsothutu[0].BYTDAYDL == "1") {
                var _parram = ["1"];
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK", _parram.join('$'));
                var data_bv = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETTT_BV", []);

                var objCHECKIN = new Object();
                objCHECKIN.MA_LK = $("#hidHOSOBENHANID").val();
                objCHECKIN.Sender_Code = data_ar[0].I_U1;
                objCHECKIN.Sender_Name = "";
                objCHECKIN.Action_Type = "1";				// 0: bắt đầu khám, 1: kết thúc khám
                objCHECKIN.Transaction_Type = "M0001";
                objCHECKIN.MABENHVIEN = data_ar[0].I_U1;
                objCHECKIN.MA_THE = $("#txtSOTHEBHYT").val();
                objCHECKIN.MA_LOAI_KCB = "1";
                objCHECKIN.TEN_BENH = "";
                objCHECKIN.MA_BENH = "";
//				objCHECKIN.MA_BENHKHAC = $("#lblMA_BHYT").val();
                objCHECKIN.NGAYHETTHUOC = "";

                objCHECKIN.NGAYGIORA = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');
                var objHeader = XML_BYT_TaoHeaderKTK(objCHECKIN, "0"); 										// tao doi tuong header;
                var objIn = XML_BYT_TaoTheCHECKIN(objCHECKIN); 									// tao the
                var obj3 = XML_BYT_TaoKhung(objHeader, objIn, "1"); 											// tao JSON full => XML

                var resultCongBYT = ajaxSvc.CongDLYTWS.guiTTKTK(
                    vsothutu[0].BYTURL
                    , data_ar[0].I_U1
                    , data_ar[0].I_P1
                    , data_ar[0].I_U1						// csytid
                    , data_bv[0].MADIAPHUONG							// ma tinh
                    , "1", obj3);
                var rets = resultCongBYT.split(';');
            }
        }

        //tuyennx_add_end

        $('#toolbarIdbtnMissList').click(function () {
            DlgUtil.showMsg("Danh sách gọi lại đang trong quá trình hoàn thiện");
        });

        function goiBenhNhanCheDo(sothutubd, sothutukt, _sophongkham) {
            if (_chedogoikham == "0") {
                if (sothutubd != "") {
                    call.goivaokham(sothutubd, _sophongkham, _ngtgoibenhnhan);
                    var objData = new Object();
                    objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.GOIKHAM", JSON.stringify(objData));
                    _loadGridData(_opt.phongid);
                }
            } else if (_chedogoikham == "1") {
                if (sothutubd != "" && sothutukt != "") {

                    call.goidangky(sothutubd, sothutukt, _ngtgoibenhnhan, _sophongkham);  //L2PT-40222 thêm sophong

                    var objData = new Object();
                    objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.GOIKHAM", JSON.stringify(objData));
                    _loadGridData(_opt.phongid);
                }
            } else if (_chedogoikham == "2") {
                if ($("#hidHOTENBNGOI").val() == "") {
                    DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để gọi. ");
                    return;
                }

                var kieugoi = cfObj.NGT_KIEUGOI_GG;
                var _texts = "";
                if (kieugoi == 1 || opt.hospital_id == "24560" || opt.hospital_id == "29560" || opt.hospital_id == "987") {
                    _texts = "Mời bệnh nhân " + $("#hidSOTHUTU").val() + "." + $("#hidHOTENBNGOI").val() + " vào " + _sophongkham;
                } else if (kieugoi == 2 || opt.hospital_id == "46840") {
                    _texts = "Mời bệnh nhân " + $("#hidSOTHUTU").val() + "." + $("#hidHOTENBNGOI").val() + " vào " + _opt._subdept_name + "." + _sophongkham;
                } else if (kieugoi == 3) {
                    _texts = "Mời bệnh nhân " + $("#hidSOTHUTU").val() + "." + $("#hidHOTENBNGOI").val() + " vào phòng khám " + _opt._subdept_name;
                }else if  (opt.hospital_id == "44416")  { //L2PT-100756
                    _texts = "Mời bệnh nhân số " + $("#hidSOTHUTU").val() + " vào phòng 105";
                }else {
                    _texts = "Mời bệnh nhân " + $("#hidHOTENBNGOI").val() + " vào " + _sophongkham;
                }
                if (cfObj.NGT_KIEUGOIKHAM_GG == 1) {
                    _texts = "";
                    if ($("#hidDONVI_TUOI").val() == 1) {
                        _texts = "Mời bệnh nhân có số thứ tự là " + $("#hidSOTHUTU").val() + "." + $("#hidHOTENBNGOI").val() + "  " + $("#hidSOTUOI").val() + " tuổi vào " + _opt._subdept_name;
                    } else {
                        _texts = "Mời bệnh nhân có số thứ tự là " + $("#hidSOTHUTU").val() + "." + $("#hidHOTENBNGOI").val() + "  " + $("#hidTUOI").val() + " vào " + _opt._subdept_name;
                    }
                }
                //L2PT-28376
                if (NGT_GOIKHAM_HANGDOI == '1' && check_note) {
                    var _jsonGoiKham = new Object();
                    _jsonGoiKham.ORG_ID = _opt.phongid;
                    _jsonGoiKham.STT = "";
                    _jsonGoiKham.BENHNHANID = $("#hidBENHNHANID").val();
                    _jsonGoiKham.LOAI = "1";
                    _jsonGoiKham.GOIKHAM_TEXT = _texts;
                    ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.GOIKHAM.INS", JSON.stringify(_jsonGoiKham));
                } else
                    goiKhamGG(_texts, _modeGoiKham, _timeOutGoiKham);

                var objData = new Object();
                objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
                jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.GOIKHAM", JSON.stringify(objData));
            }

            if (cfObj.NOTIFY_APP_VNCARE == 1) {
                sendNotifyMH2(2);
            }
        }

        $('#toolbarIdbtnCall').click(function () {
            if (_chedogoikham == "0") {
                var stt = $('#hidSOTHUTU').val();
                if (cfObj.NGT_GOISO_QUANY15 == 1) {
                    stt = _sophongkham + stt;
                }
                goiBenhNhanCheDo(stt, null, _sophongkham); 				// Goi 1 BN
            } else if (_chedogoikham == "1") {
                var stt = $("#txtGOIKHAMSTT_BD").val().trim(); 					// Goi BN tu so ... den so ...
                var sttkt = $("#txtGOIKHAMSTT_KT").val().trim();

                if (stt != "") {
                    if (isNaN(stt)) {
                        DlgUtil.showMsg("Số thứ tự bắt đầu cần đúng định dạng");
                        return;
                    }
                    if (sttkt != "") {
                        if (isNaN(sttkt)) {
                            DlgUtil.showMsg("Số thứ tự kết thúc cần đúng định dạng");
                            return;
                        }
                        if (Number(sttkt) > Number(stt)) {
                            goiBenhNhanCheDo(stt, sttkt, _sophongkham);
                        } else if (Number(sttkt) == Number(stt)) {
                            goiBenhNhanCheDo(stt, null, _sophongkham);
                        } else {
                            DlgUtil.showMsg("STT kết thúc phải lớn hơn STT bắt đầu. ");
                            return;
                        }
                    } else {
                        goiBenhNhanCheDo(stt, null, _sophongkham);
                    }
                } else {
                    // LAY BN DANG DUOC CHON;
                    stt = $('#hidSOTHUTU').val();
                    if (stt == "") {
                        DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để gọi");
                        return;
                    }
                    goiBenhNhanCheDo(stt, null, _sophongkham);
                }
            } else if (_chedogoikham == "2") {
                // SU DUNG GIONG DOC GOOGLE BAT KY;
                if ($("#hidPHONGKHAMDANGKYID").val() == "") {
                    DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để gọi ");
                    return;
                }
                var stt = $('#hidSOTHUTU').val();
                goiBenhNhanCheDo(stt, null, _sophongkham);
            }
        });

        // XU LY CHUC NANG GOI SO THEO KHOANG;
        function _tanggiam_stt(id, kieu) { // kieu 1: tang, 2: giam.
            var value = $('#' + id).val().trim();
            if (isNaN(value) && value != "") {
                DlgUtil.showMsg("Giá trị gọi khám phải là số. ");
                return;
            }
            if (value < 1) {
                $('#' + id).val(1);
            } else {
                if (kieu == 1) {
                    value = parseInt(value) + 1;
                } else {
                    value = parseInt(value) - 1;
                    if (value < 1) {
                        value = 1;
                    }
                }
                $('#' + id).val(value);
            }
        }

        $("#txtstt_bdtang").on("click", function (e) {
            _tanggiam_stt('txtGOIKHAMSTT_BD', 1);
        });

        $("#txtstt_bdgiam").on("click", function (e) {
            _tanggiam_stt('txtGOIKHAMSTT_BD', 2);
        });
        $("#txtstt_kttang").on("click", function (e) {
            _tanggiam_stt('txtGOIKHAMSTT_KT', 1);
        });

        $("#txtstt_ktgiam").on("click", function (e) {
            _tanggiam_stt('txtGOIKHAMSTT_KT', 2);
        });

        $("#btnGOIKHAMSTT_CALL").on("click", function () {
            $('#toolbarIdbtnCall').click();
        });

        $("#txtGOIKHAMSTT_BD").on("click", function (e) {
            $(this).select();
        });

        $("#txtGOIKHAMSTT_KT").on("click", function (e) {
            $(this).select();
        });


        $("#btnGOIKHAMSTT_RESET").on("click", function () {
            $("#txtGOIKHAMSTT_BD").val("");
            $("#txtGOIKHAMSTT_KT").val("");
        });
        // END XU LY CHUC NANG GOI SO THEO KHOANG;
        $("#tabAnHien").on("click",function(e){
            if(checkDsFull == false){
                $("#divhanhchinh").hide();
                $("#divtimkiem").removeClass("col-xs-7");
                $("#divtimkiem").addClass("col-xs-12");
                $('#tabAnHien').css('display','');
                $('#tabAnHien ins').html('');
                $('#tabAnHien ins').html('Hiện chi tiết');
                GridUtil.setWidthPercent(_gridId,100);
                checkDsFull = true;
            }
            else{
                $("#divhanhchinh").show();
                $("#divtimkiem").removeClass("col-xs-12");
                $("#divtimkiem").addClass("col-xs-7");
                $('#tabAnHien').css('display','');
                $('#tabAnHien ins').html('');
                $('#tabAnHien ins').html('Ẩn chi tiết');
                GridUtil.setWidthPercent(_gridId,100);
                checkDsFull = false;
            }
        });
        // thong tin tai nan thuong tich
        $("#toolbarIdbtnKHAC_3").on("click", function () {
            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgTaiNanThuongTich", "divDlg", "manager.jsp?func=../noitru/NTU02D035_Thongtintainanthuongtich", paramInput, "THÔNG TIN TAI NẠN THƯƠNG TÍCH", 1400, 600);
            DlgUtil.open("dlgTaiNanThuongTich");
        });

        $("#toolbarIdgroup_0_TNTT").on("click", function () {
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
                return false;
            }
            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgTaiNanThuongTich", "divDlg", "manager.jsp?func=../noitru/NTU02D035_Thongtintainanthuongtich", paramInput, "THÔNG TIN TAI NẠN THƯƠNG TÍCH", 1400, 600);
            DlgUtil.open("dlgTaiNanThuongTich");
        });

        //callback cho tai nan thuong tich
        EventUtil.setEvent("assignSevice_saveTNTT", function (e) {
            DlgUtil.showMsg(e.msg);
            DlgUtil.close("dlgTaiNanThuongTich");
        });

        // click button khám trên toolbar
        $("#toolbarIdbtnExam").on("click", function () {
            var myVar = {
                khambenhId: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                madichvu: $("#hidMADICHVU").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                //tuyennx_add_start_20171020 yc L2DKBD-692
                hosobenhanId: $("#hidHOSOBENHANID").val(),
                loaibenhanId: $("#hidLOAIBENHANID").val(),
                //tuyennx_add_end_20171020 yc L2DKBD-692
                //tuyennx_add_start_20170112 L2DKBD-880
                phongId: $("#hidPHONGID").val(),
                //tuyennx_add_end_20170112 L2DKBD-880
                // L2PT-8475 L2PT-8437 L2PT-8487 L2PT-8483 L2PT-8481 L2PT-8485 ngocnva start
                dichvuid: $('#hidDICHVUID').val(),
                // L2PT-8475 L2PT-8437 L2PT-8487 L2PT-8483 L2PT-8481 L2PT-8485 ngocnva end
                startkham: startkham,
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()

            };
            var heightwindow = $(window).height();
            var heightdlg = heightwindow - 50;
            var widthwindow = $(window).width();
            var widthdlg = widthwindow - 100;
            var heightdlg = 650;
            if (cfObj.NGT_KBHB_FULL == 1) {
                heightdlg = 660;
            }
            if (cfObj.NGT_KBHB_FITSCREEN == 1) {
                var heightwindow = $(window).height();
                var widthwindow = $(window).width();
                dlgPopup = DlgUtil.buildPopupUrl("dlgKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K002_KhamBenhHoiBenh", myVar, "Khám hỏi bệnh", widthwindow - 100, heightwindow - 50);
            } else {
                dlgPopup = DlgUtil.buildPopupUrl("dlgKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K002_KhamBenhHoiBenh", myVar, "Khám hỏi bệnh", 1300, heightdlg);
            }

            //dlgPopup.open();
            DlgUtil.open("dlgKham");
            startkham = 0;
        });

        // Khám sức khỏe cá nhân
        $("#toolbarIdbtnKSK").on("click", function () {
            var myVar = {
                khambenhId: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                madichvu: $("#hidMADICHVU").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                hosobenhanId: $("#hidHOSOBENHANID").val(),
                loaibenhanId: $("#hidLOAIBENHANID").val(),
                phongId: $("#hidPHONGID").val(),
                dichvuid: $('#hidDICHVUID').val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
            };
            var _width = $(document).width() - 250;
            var _height = $(window).height() - 150;
            dlgPopup = DlgUtil.buildPopupUrl("dlgKhamSK", "divDlg", "manager.jsp?func=../ngoaitru/NGT05K004_KhamSucKhoe", myVar, "Khám sức khỏe", _width, _height);
            DlgUtil.open("dlgKhamSK");
        });

        // Khám sức khỏe cán bộ
        $("#toolbarIdbtnKSKCANBO").on("click", function () {
            var myVar = {
                khambenhId: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                hosobenhanId: $("#hidHOSOBENHANID").val(),
                loaibenhanId: $("#hidLOAIBENHANID").val(),
                phongId: $("#hidPHONGID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
                loaikskid: '1',
                maloaiksk: 'KSKCB'
            };
            var _width = $(document).width() - 250;
            var _height = $(window).height() - 150;
            dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../ksk/KSK_CANBO", myVar, "Khám sức khỏe cán bộ", _width, _height);
            DlgUtil.open("divDlgBenhAnDetail");
        });
        // Khám sức khỏe lái xe
        $("#toolbarIdbtnKSKLAIXE").on("click", function () {
            var myVar = {
                khambenhId: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                hosobenhanId: $("#hidHOSOBENHANID").val(),
                loaibenhanId: $("#hidLOAIBENHANID").val(),
                phongId: $("#hidPHONGID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
                loaikskid: '2',
                maloaiksk: 'KSKLX'
            };
            var _width = $(document).width() - 250;
            var _height = $(window).height() - 150;
            dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../ksk/KSK_LAIXE", myVar, "Khám sức khỏe lái xe", _width, _height);
            DlgUtil.open("divDlgBenhAnDetail");
        });
        // Khám sức khỏe nghề nghiệp
        $("#toolbarIdbtnKSKNGHENGHIEP").on("click", function () {
            var myVar = {
                khambenhId: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                hosobenhanId: $("#hidHOSOBENHANID").val(),
                loaibenhanId: $("#hidLOAIBENHANID").val(),
                phongId: $("#hidPHONGID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
                loaikskid: '6',
                maloaiksk: 'KSKNN'
            };
            var _width = $(document).width() - 250;
            var _height = $(window).height() - 150;
            dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../ksk/KSK_NGHENGHIEP", myVar, "Khám sức khỏe nghề nghiệp", _width, _height);
            DlgUtil.open("divDlgBenhAnDetail");
        });
        // Khám sức khỏe dưới 18 tuổi
        $("#toolbarIdbtnKSKDUOI18TUOI").on("click", function () {
            var myVar = {
                khambenhId: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                hosobenhanId: $("#hidHOSOBENHANID").val(),
                loaibenhanId: $("#hidLOAIBENHANID").val(),
                phongId: $("#hidPHONGID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
                loaikskid: '3',
                maloaiksk: 'KSKD18'
            };
            var _width = $(document).width() - 250;
            var _height = $(window).height() - 150;
            dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../ksk/KSK_DUOI18TUOI", myVar, "Khám sức khỏe dưới 18 tuổi", _width, _height);
            DlgUtil.open("divDlgBenhAnDetail");
        });
        // Khám sức khỏe trên 18 tuổi
        $("#toolbarIdbtnKSKTREN18TUOI").on("click", function () {
            var myVar = {
                khambenhId: $("#hidKHAMBENHID").val(),
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                hosobenhanId: $("#hidHOSOBENHANID").val(),
                loaibenhanId: $("#hidLOAIBENHANID").val(),
                phongId: $("#hidPHONGID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
                loaikskid: '4',
                maloaiksk: 'KSKT18'
            };
            var _width = $(document).width() - 250;
            var _height = $(window).height() - 150;
            dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../ksk/KSK_TREN18TUOI", myVar, "Khám sức khỏe trên 18 tuổi", _width, _height);
            DlgUtil.open("divDlgBenhAnDetail");
        });

        //tao phieu cong kham
        $("#toolbarIdbtnCongKham").on("click", function () {
            paramInput = {
                chidinhdichvu: '1',
                loaidichvu: '2',
                loaiphieumbp: '3',
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                hinhthucvaovienid: _hinhthucvaovienid,
                loaibenhanid: $("#hidLOAIBENHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: $('#hidPHONGID').val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 2, paramInput, "Phiếu công khám", 1300, 600);
            DlgUtil.open("divDlgDichVu");
        });

        $("#toolbarIdbtnPhieuTruyenMau").on("click", function () {
            if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
                return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
            } else {
                var i_tiepnhanid = $("#hidTIEPNHANID").val();
                var par = [{
                    name: 'i_tiepnhanid',
                    type: 'String',
                    value: i_tiepnhanid
                }];
                openReport('window', "RPT_GIAYCAMKET_TRUYENMAU_951", "pdf", par);
            }
        });

        $("#toolbarIdbtnTomTatBA").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
                return;
            } else {
                var par = [{
                    name: 'hosobenhanid',
                    type: 'String',
                    value: $("#hidHOSOBENHANID").val()
                }, {
                    name: 'loaibenhanid',
                    type: 'String',
                    value: $("#hidLOAIBENHANID").val()
                }, {
                    name: 'khambenhid',
                    type: 'String',
                    value: $("#hidKHAMBENHID").val()
                }];
                openReport('window', "TOMTAT_BA", "pdf", par);
            }
        });

        // mo benh an da co ra de cap nhat
        $("#toolbarIdbtnBANGT_0").on("click", function () {
            _mobenhan_daingay();
        });

        //mo benh an moi
        $("#toolbarIdbtnBANGT_2").on("click", function () {
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            if (rowData.TRANGTHAIKHAMBENH == 1) {
                DlgUtil.showMsg("Bệnh nhân chờ khám không được phép mở bệnh án dài ngày!");
                return false;
            }
            if ($("#hidDOITUONGBENHNHANID").val() == '1' && cfObj.HIS_CHECKMALOAIKCB_7 != '0') {
                var maloaikcb = jsonrpc.AjaxJson.ajaxCALL_SP_S("HIS.GETMALOAIKCB", $("#hidTIEPNHANID").val());
                if (maloaikcb == '07') {
                    DlgUtil.showMsg("Mã loại khám chữa bệnh của bệnh nhân không được phép mở bệnh án dài ngày!");
                    return false;
                }
            }
            if (cfObj.NGT_DONTHUOC_BADN == 2) {
                var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, -1];
                var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
                if (_return == 1) {
                    DlgUtil.showConfirm("Bạn có chắc chắn mở bệnh án ngoai trú dài ngày cho bệnh nhân không?", function (flag) {
                        if (flag) {
                            // xu tri dieu tri ngoai tru
                            // kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
                            var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, 0];
                            var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
                            if (_return == -1) {
                                DlgUtil.showMsg('Bệnh chính của bệnh nhân không thuộc nhóm bệnh dài ngày nên không thể mở bệnh án');
                            } else if (_return == 0) {
                                DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thất bại');
                            } else if (_return == -2) {
                                DlgUtil.showMsg('Bệnh nhân đã được mở bệnh án dài ngày,vui lòng đóng bệnh án dài ngày cũ để mở bệnh án dài mới!');
                            } else if (_return == -3) {
                                DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán bệnh chính');
                            } else if (_return == -5) {
                                DlgUtil.showMsg('Bệnh nhân đã được mở bệnh án ngoại trú dài ngày, vào menu Bệnh an để cập nhật thông tin');
                            } else {
                                DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thành công', undefined, _HIS_TIMEOUT_THONGBAO);
                            }
                        }
                    });
                } else if (_return == -2) {
                    $('.popup_remove').remove();
                    $('#dlgWRAP_P').append($(htmlDlgmoBA));
                    var dlgMOBA_DN = DlgUtil.buildPopup("dlgMOBA", "dlgMOBA_DN", "Mở BA dài ngày", 500, 110, {"zIndex": 998});
                    DlgUtil.open("dlgMOBA");
                    var sql_par = [];
                    sql_par.push({"name": "[0]", "value": $('#hidBENHNHANID').val()}, {
                        "name": "[1]",
                        "value": _opt.phongid
                    }, {"name": "[2]", "value": 1});
                    ComboUtil.getComboTag("cboHOSOBENHANDAINGAYID", sqlloadbadn, sql_par, "49", {
                        value: 0,
                        text: '--Chọn--'
                    }, "sql", "", "");
                    var btnOK = $('#btn_MOBA_OK');
                    var btnClose = $('#btn_MOBA_CLOSE');
                    btnOK.click(function () {
                        if ($("#cboHOSOBENHANDAINGAYID").val() == 0) {
                            DlgUtil.showMsg('Chưa chọn bệnh án dài ngày để mở !');
                            return;
                        }
                        var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, $("#cboHOSOBENHANDAINGAYID").val()];
                        var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
                        if (_return == -1) {
                            DlgUtil.showMsg('Bệnh chính của bệnh nhân không thuộc nhóm bệnh dài ngày nên không thể mở bệnh án');
                            return;
                        } else if (_return == 0) {
                            DlgUtil.showMsg('Cập nhật bệnh án ngoại trú dài ngày thất bại');
                            return;
                        } else {
                            DlgUtil.showMsg('Cập nhật bệnh án ngoại trú dài ngày thành công');
                            dlgMOBA_DN.close();
                        }
                    });
                    btnClose.click(function () {
                        dlgMOBA_DN.close();
                    });
                } else if (_return == -3) {
                    DlgUtil.showMsg('Bệnh nhân đã mở bệnh án trong ngày!');
                    return;
                }

            } else {
                DlgUtil.showConfirm("Bạn có chắc chắn mở bệnh án ngoai trú dài ngày cho bệnh nhân không?", function (flag) {
                    if (flag) {
                        // xu tri dieu tri ngoai tru
                        // kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
                        var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, 0];
                        var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
                        if (_return == -1) {
                            DlgUtil.showMsg('Bệnh chính của bệnh nhân không thuộc nhóm bệnh dài ngày nên không thể mở bệnh án');
                        } else if (_return == -2) {
                            DlgUtil.showMsg('Bệnh nhân đã được mở bệnh án ngoại trú dài ngày, vào menu Bệnh an để cập nhật thông tin');
                        } else if (_return == 1) {
                            //tuyennx_edit_start_20190425 L1PT-661 L2PT-14910
                            DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thành công', undefined, _HIS_TIMEOUT_THONGBAO);
                            //tuyennx_edit_end_20190425 L1PT-661
                        } else if (_return == 0) {
                            DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thất bại');
                        } else if (_return == -3) {
                            DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán bệnh chính');
                        }
                    }
                });
            }
        });

        //tuyennx_add_start_20171027 yc HISL2NT-485
        //dong benh an
        $("#toolbarIdbtnBANGT_3").on("click", function () {
            // var _par=[];
            // var _hosobenhanid=null;
            // _par=RSUtil.buildParam("",[ $("#hidHOSOBENHANID").val()]);
            // var dataDaingay=jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.CHECK_DAINGAY", _par);
            // _rowsDaingay= JSON.parse(dataDaingay);
            // var _loaibadaingay = -1;
            // if(_rowsDaingay!=null && _rowsDaingay.length>0){
            //    _loaibadaingay=_rowsDaingay[0].LOAIBENHANID;
            //    _hosobenhanid = _rowsDaingay[0].HOSOBENHANID;
            // }

            var object = {};
            FormUtil.setFormToObject('divContentHC', '', object);
            var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
            var dataDaingay = {};
            if (dataDaingays && dataDaingays.length > 0) {
                dataDaingay = dataDaingays[0];
            }
            if (!dataDaingay) {
                DlgUtil.showMsg("Bệnh nhân chưa có bệnh án dài ngày!");
                return;
            }
            var _hosobenhanid = dataDaingay.HOSOBENHANID;
            var _loaibadaingay = dataDaingay.LOAIBENHANID;
            if (cfObj.NGT_DONTHUOC_BADN == 2) {
                $('.popup_remove').remove();
                $('#dlgWRAP_P').append($(htmlDlgmoBA));

                var dlgDONGBA_DN = DlgUtil.buildPopup("dlgDONGBA", "dlgDONGBA_DN", "Đóng BA dài ngày", 500, 110, {"zIndex": 998});
                DlgUtil.open("dlgDONGBA");
                $("#txtNGAYDONGBA").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
                var sql_par = [];
                sql_par.push({"name": "[0]", "value": $('#hidBENHNHANID').val()}, {
                    "name": "[1]",
                    "value": _opt.phongid
                }, {"name": "[2]", "value": '1,9'});
                ComboUtil.getComboTag("cboHOSOBENHANDAINGAY_ID", sqlloadbadn, sql_par, "49", {
                    value: 0,
                    text: '--Chọn--'
                }, "sql", "", "");
                var btnOK = $('#btn_DONGBA_OK');
                var btnMO = $('#btn_MOBA');
                var btnClose = $('#btn_DONGBA_CLOSE');
                var check = 0;
                $('#cboHOSOBENHANDAINGAY_ID').on('change', function (e) {
                    var sql_par = [];
                    sql_par.push({"name": "[0]", value: $('#cboHOSOBENHANDAINGAY_ID').val()});
                    var ret = jsonrpc.AjaxJson.getOneValue("BAN.DAINGAY.CHECKTT", sql_par);
                    if (ret == 9) {
                        $('#btn_DONGBA_OK').attr("disabled", true);
                        $('#btn_MOBA').attr("disabled", false);
                        $("#txtNGAYDONGBA").val('');
                        $("#txtNGAYDONGBA").attr("disabled", true);
                    } else {
                        $('#btn_DONGBA_OK').attr("disabled", false);
                        $('#btn_MOBA').attr("disabled", true);
                    }
                });
                btnOK.click(function () {
                    if ($("#txtNGAYDONGBA").val().trim() == "") {
                        DlgUtil.showMsg("Bạn chưa nhập ngày đóng BA!");
                        return false;
                    }
                    if (check == 1)
                        return;
                    var _hosobenhanid = $("#cboHOSOBENHANDAINGAY_ID").val();
                    var _par_ins = [_hosobenhanid, $("#txtNGAYDONGBA").val()];
                    var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONG.DAINGAY1", _par_ins.join('$'));
                    switch (Number(_return)) {
                        case -2:
                            DlgUtil.showMsg("Bệnh nhân đã được đóng BADN!");
                            break;
                        case 1:
                            DlgUtil.showMsg("Đóng BADN thành công!");
                            dlgDONGBA_DN.close();
                            check = 1;
                            break;
                        case -1:
                            DlgUtil.showMsg("Đóng BADN không thành công!", undefined, undefined, "error");
                            break;
                        default:
                            DlgUtil.showMsg("Đã có lỗi xảy ra!", undefined, undefined, "error");
                            break;
                    }
                });
                btnClose.click(function () {
                    dlgDONGBA_DN.close();
                });
                btnMO.click(function () {

                    if (check == 1)
                        return;
                    var _hosobenhanid = $("#cboHOSOBENHANDAINGAY_ID").val();
                    var _par_ins = [_hosobenhanid, $("#txtNGAYDONGBA").val()];
                    var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONG.DAINGAY1", _par_ins.join('$'));
                    switch (Number(_return)) {
                        case -2:
                            DlgUtil.showMsg("Bệnh nhân đã được đóng BADN!");
                            break;
                        case 1:
                            DlgUtil.showMsg("Mở BADN thành công!");
                            dlgDONGBA_DN.close();
                            check = 1;
                            break;
                        case -1:
                            DlgUtil.showMsg("Mở BADN không thành công!", undefined, undefined, "error");
                            break;
                        default:
                            DlgUtil.showMsg("Đã có lỗi xảy ra!", undefined, undefined, "error");
                            break;
                    }
                });
            } else {
                if (_loaibadaingay == 36) {
                    DlgUtil.showConfirm("Bạn có chắc chắn muốn đóng bệnh án ngoại trú dài ngày cho bệnh nhân không?", function (flag) {
                        if (flag) {
                            // xu tri dieu tri ngoai tru
                            // kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
                            var _par_ins = [$('#hidHOSOBENHANID').val()];
                            var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONG.DAINGAY", _par_ins.join('$'));
                            if (_return == 1) {
                                DlgUtil.showMsg('Đóng bệnh án dài ngày thành công');
                                _loadGridData(_opt.phongid);
                            } else if (_return == 0) {
                                DlgUtil.showMsg('Đóng bệnh án thất bại');
                            }
                        }
                    });
                } else {
                    DlgUtil.showMsg('Bệnh nhân chưa được mở bệnh án dài ngày!');
                }
            }
        });
        //tuyennx_add_start_20171027
        //Chọn bệnh án
        $("#toolbarIdbtnBANGT_1").on("click", function () {
            if(opt.hospital_id == "93882"){ // doi thanh 93882 cho vietmedi
                paramInput={
                    benhnhanid : $("#hidBENHNHANID").val(),
                    khambenhid : $("#hidKHAMBENHID").val(),
                    hosobenhanid : $("#hidHOSOBENHANID").val(),
                    tiepnhanid : $("#hidTIEPNHANID").val(),
                    doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
                    loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
                    phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),//L2PT-9165
                    subDeptId : _opt.phongid
                };
                dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU01H031_NhapBenhAn", paramInput, "Nhập bệnh án", 500, 250);
                DlgUtil.open("divDlgNhapBenhAn");
            }else{
            var object = {};
            FormUtil.setFormToObject('divContentHC', '', object);
            var _hosobenhanid_hientai = object.HOSOBENHANID;
            var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
            dataDaingay = {};
            if (dataDaingays && dataDaingays.length > 0) {
                var loaibenhanid = $("#hidLOAIBENHANID").val();
                var sovaovien = $("#hidSOTHUTU").val();

                var _sql_par1 = RSUtil.buildParam("", [loaibenhanid]);
                var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
                var _rows1 = JSON.parse(_data1);
                var _sreenName = _rows1[0].URL;
                var _tenloaibenhan = _rows1[0].TENLOAIBENHAN;
                var _maloaibenhan = _rows1[0].MALOAIBENHAN;

                if (_sreenName != '') {
                    paramInput = {
                        khambenhid: $("#hidKHAMBENHID").val(),
                        hosobenhanid: $("#hidHOSOBENHANID").val(),
                        benhnhanid: $("#hidBENHNHANID").val(),
                        loaibenhanid: loaibenhanid,
                        maloaibenhan: _maloaibenhan,
                        // nvangoc start L2PT-6142
                        sovaovien: sovaovien,
                        hosobenhanid_hientai: _hosobenhanid_hientai
                        // nvangoc end L2PT-6142
                    };
                    dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../benhan/" + _sreenName, paramInput, "Cập nhật " + _tenloaibenhan, 1300, 610);
                    DlgUtil.open("divDlgBenhAnDetail");
                } else {
                    paramInput = {
                        benhnhanid: $("#hidBENHNHANID").val(),
                        khambenhid: $("#hidKHAMBENHID").val(),
                        hosobenhanid: $("#hidHOSOBENHANID").val(),
                        tiepnhanid: $("#hidTIEPNHANID").val(),
                        doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                        loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                        phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),//L2PT-9165
                        subDeptId: _opt.phongid
                    };

                    dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU01H031_NhapBenhAn", paramInput, "Nhập bệnh án", 500, 250);
                    DlgUtil.open("divDlgNhapBenhAn");
                }
            }
            if (dataDaingays.length == 0) {
                DlgUtil.showMsg("Bệnh nhân chưa mở hoặc đã đóng bệnh án dài ngày!");
                return;
            }
            }
        });
        $("#toolbarIdbtnBANGT_4").on("click", function () {
            if(opt.hospital_id == "93882"){ 
                var i_hosobenhanid = $("#hidHOSOBENHANID").val();
                var i_loaibenhanid ;
                var i_benhnhanid = $("#hidBENHNHANID").val();
                var i_rptcode ;
                var par = [];
                par.push({ "name" : "[0]", "value" : $("#hidHOSOBENHANID").val()});
                var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("GET.THONGTINLOAIBA", par);
                var row = JSON.parse(data);
                if (row != null && row.length > 0) { 
                    i_loaibenhanid = row[0].LOAIBENHANID;
                    i_rptcode = row[0].REPORT_CODE;
                }
                var _params = [{
                        name: 'hosobenhanid',
                        type: 'String',
                        value: i_hosobenhanid
                    },
                    {
                        name: 'loaibenhanid',
                        type: 'String',
                        value: i_loaibenhanid
                    },
                    {
                        name: 'benhnhanid',
                        type: 'String',
                        value: i_benhnhanid
                    },
                    {
                        name: 'RPT_CODE',
                        type: 'String',
                        value: i_rptcode
                    }];
                var _check = CommonUtil.checkKyCaByParam(_params);
                if (_check > 0) {
                     DlgUtil.showMsg("Bệnh án đã được ký số, không thể đưa ra khỏi bệnh án!");
                } else {
                    DlgUtil.showConfirm("Bạn có chắc chắn đưa bệnh án ra khỏi bệnh án ?", function (flag) {
                        if (flag) {
                            var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, 1];
                            var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
                            if (_return == 1) {
                                DlgUtil.showMsg('Đưa ra khỏi bệnh án thành công!');
                            }else {
                                DlgUtil.showMsg('Đưa ra khỏi bệnh án không thành công!');
                            }
                        }
                    });
                }
            }else{
            DlgUtil.showConfirm("Bạn có chắc chắn đưa bệnh án ra khỏi bệnh án dài ngày ?", function (flag) {
                if (flag) {
                    // xu tri dieu tri ngoai tru
                    // kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
                    var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, 1];
                    var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
                    if (_return == 1) {
                        DlgUtil.showMsg('Đưa ra khỏi bệnh án dài ngày thành công!');
                    } else if (_return == -3) {
                        DlgUtil.showMsg('Bệnh nhân chưa được mở bệnh án dài ngày!');
                    } else {
                        DlgUtil.showMsg('Đưa ra khỏi bệnh án dài ngày không thành công!', undefined, undefined, "error");
                    }
                }
            });
            }
        });
        $("#toolbarIdbtnBANGT_5").on("click", function () {
            var paramInput = {
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),//L2PT-9165
                subDeptId: _opt.phongid
            };

            dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU01H031_NhapBenhAn", paramInput, "Nhập bệnh án", 500, 250);
            DlgUtil.open("divDlgNhapBenhAn");
        });
        $("#toolbarIdgroup_0_BangKe3455").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in bảng kê 3455.');
                return;
            }
            var _loaitiepnhanid = $("#hidLOAITIEPNHANID").val();
            var dtbnid = $("#hidDOITUONGBENHNHANID").val();
            vienphi_tinhtien.inBangKe3455($("#hidTIEPNHANID").val(), dtbnid, _loaitiepnhanid);
        });
        // click button khám dich vu trên toolbar
        $("#toolbarIdbtnService").on("click", function () {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData(3) > 0) {
                return;
            }
            if (cfObj.NGT_CHECKTRUNGTHAOTAC_TGBD != '0' && check_tgksk == 0) {
                var _sql_par = [];
                _sql_par.push({
                    "name": "[0]",
                    "value": $("#hidPHONGKHAMDANGKYID").val()
                });
                _sql_par.push({
                    "name": "[1]",
                    "value": opt.user_id
                });
                var checkphut = jsonrpc.AjaxJson.getOneValue("NGT_CHECKTRUNG_TGBD", _sql_par);
                if (checkphut == '0') {
                    DlgUtil.showMsg('Thời gian thao tác trùng với thời gian bắt đầu khám!');
                    return false;
                }
            }
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
            //dannd_L2PT-70290
            var checktgiancddv = cfObj.NGT_KB_CHECKTGCDDV;
            if (rowData.THOIGIANBD != '' && checktgiancddv != 0) {
                var ressult = diffDate(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'), rowData.THOIGIANBD, 'DD/MM/YYYY HH:mm:ss', 'minutes');
                if (ressult < checktgiancddv) {
                    DlgUtil.showMsg("Thời gian bắt đầu khám đến thời gian chỉ định dịch vụ chưa đủ " + checktgiancddv + " phút không thể thực hiện!");
                    return;
                }
            }
            var _sql_par = [];
            _sql_par.push({
                "name": "[0]",
                value: opt.user_id
            });
            var rs = jsonrpc.AjaxJson.getOneValue("GET.OFFICER.KHAMBHYT", _sql_par);
            var NGT_CHANTHAOTAC_NVKHONGKHAMBHYT = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'NGT_CHANTHAOTAC_NVKHONGKHAMBHYT');
            var DOITUONGBENHNHANID = $("#hidDOITUONGBENHNHANID").val();
            if (NGT_CHANTHAOTAC_NVKHONGKHAMBHYT == 1 && rs == '0' && DOITUONGBENHNHANID == '1') {
                DlgUtil.showMsg('Người dùng không có quyền thao tác đối với bệnh nhân BHYT ');
                return;
            }
            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                doituongbenhnhanid:
                    (_tylevpchuyenphanhe == "2" || _tylevpchuyenphanhe == "3") && $("#hidCHUYENKHAMNGT").val() == "1"
                        ? _tylevpchuyenphanhe : $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: $('#hidPHONGID').val(),
                deptId: $('#hidKHOAID').val(),
                bacsike: $('#hidBACSYKE').val()
            };
            var cddvDLKHA = cfObj.CDDV_GIAODIEN_KHA == '1' ? true : false;
            if (cddvDLKHA) {
                dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV_KHA" + "&loaidichvu=" + 5, myVar, "Tạo phiếu chỉ định dịch vụ", 1300, 660);
                DlgUtil.open("divDlgDichVu");
            } else {
                dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, myVar, "Tạo phiếu chỉ định dịch vụ", 1300, 660);
                DlgUtil.open("divDlgDichVu");
            }
        });

        // click button khám > tạo phiếu khám trên toolbar
        $("#toolbarIdtreat_1").on("click", function () {
            var myVar = {
                khambenhId: $("#hidKHAMBENHID").val(),
                maubenhphamId: -1,
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                hosobenhanId: $("#hidHOSOBENHANID").val(),
                doituongbenhnhanId: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanId: $("#hidLOAITIEPNHANID").val(),
                subDeptId: _opt.phongid

            };
            EventUtil.setEvent("assignSevice_cancelP", function (e) {
                DlgUtil.close("dlgPhieukham");
            });

            EventUtil.setEvent("treatment_cancel", function (e) {
                $('#tabDieuTri').ntu02d027_dt({
                    _grdDieuTri: 'grdDieuTri',
                    _khambenhid: $("#hidKHAMBENHID").val(),
                    _benhnhanid: $("#hidBENHNHANID").val(),
                    _lnmbp: LNMBP_DieuTri,
                    _modeView: _flgModeView, // =1 chi view; !=1 la update
                    _hosobenhanid: ""
                });
                DlgUtil.close("dlgPhieukham");
                DlgUtil.close("divDlgPhieuDieuTri");
            });
            dlgPopup = DlgUtil.buildPopupUrl("dlgPhieukham", "divDlg1", "manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT", myVar, "Phiếu điều trị", 1330, 620);

            //dlgPopup.open();
            DlgUtil.open("dlgPhieukham");
            EventUtil.setEvent("assignSevice_cancel", function (e) {
                DlgUtil.close("dlgCDDV");
            });
        });

        // click button Thuốc > Tạo phiếu thuốc.
        $("#toolbarIddrug_1").on("click", function (e) {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            _openDialogThuoc('02D010', 1, "Chỉ định thuốc");
        });

        // tạo đơn thuốc từ kho
        $("#toolbarIddrug_khothuoc").on("click", function (e) {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            //L2PT-14262
            var loadkho = cfObj.NGT_KETHUOC_KHO;
            _openDialogThuoc('02D010', 1, "Chỉ định thuốc từ kho", loadkho);

        });

        // tạo đơn thuốc từ tủ trực
        $("#toolbarIddrug_tutruc").on("click", function (e) {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData(11) > 0) {
                return;
            }
            _openDialogThuoc('02D010', 1, "Chỉ định thuốc từ tủ trực", '2');
        });

        // click button Thuốc > Tạo phiếu thuốc.
        $("#toolbarIddrug_le").on("click", function (e) {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            _openDialogThuoc('02D010_1', 0, "Kê thuốc lẻ");
        });

        $("#toolbarIddrug_dtnhathuoc").on("click", function (e) {
            EventUtil.setEvent("assignDrug_fail", function (e) {
                DlgUtil.close("dlgCDT");
            });

            _openDialogThuoc('02D019', 0, "Mua thuốc nhà thuốc");
        });

        // click button Thuốc > Tạo phiếu thuốc không thuốc.
        $("#toolbarIddrug_1kt").on("click", function (e) {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            EventUtil.setEvent("assignDrug_fail", function (e) {
                DlgUtil.close("dlgCDT");
            });
            _openDialogThuocK('02K044', 0, "Chỉ định thuốc không thuốc");
        });

        // click button Thuốc > Tạo phiếu thuốc đông y.
        $("#toolbarIddrug_1dy").on("click", function (e) {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            EventUtil.setEvent("assignDrug_fail", function (e) {
                DlgUtil.close("dlgCDT");
            });
            //L2PT-121068
            _openDialogThuoc('02D017', cfObj.NGT_KIEUKE_THUOCDONGY, "Chỉ định thuốc YHCT");
        });

        // click button Thuốc > Tạo phiếu trả thuốc đông y.
        $("#toolbarIddrug_2dy").on("click", function (e) {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            EventUtil.setEvent("assignDrug_fail", function (e) {
                DlgUtil.close("dlgCDT");
            });
            //L2PT-121068
            _openDialogThuoc('02D018', cfObj.NGT_KIEUKE_THUOCDONGY, "Trả thuốc YHCT");
        });

        // click button Thuốc > Tạo phiếu huy thuốc.
        $("#toolbarIddrug_2").on("click", function (e) {
            EventUtil.setEvent("assignDrug_fail", function (e) {
                DlgUtil.close("dlgCDT");
            });

            _openDialogThuoc('02D014', 0, "Tạo phiếu trả thuốc");
        });

        //Tao phieu don thuoc mua ngoai
        $("#toolbarIddrug_mn").on("click", function () {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
                maubenhphamid: "",
                loaikedon: jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'LOAIKEDON_MUANGOAI'), //tuyennx_edit_L2PT-6845
                dichvuchaid: "",
                opt: "02D011"
            };
            var chchucnang = ['CHUCNANG_KEDON_MUANGOAI'];
            var _chucnang = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', chchucnang.join('$'));

            dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D011", "divDlg", "manager.jsp?func=../noitru/" + _chucnang, paramInput, "Tạo đơn thuốc mua ngoài", 1300, 590);
            DlgUtil.open("divDlgTaoPhieuThuoc" + "02D011");
        });

        //Tao phieu don thuoc mua ngoai
        $("#toolbarIddrug_phieuđinhuong").on("click", function () {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
                maubenhphamid: "",
                loaikedon: 1,
                dichvuchaid: "",
                opt: "02D011",
                dinhduong: 1
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D011", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Phiếu tư vấn dinh dưỡng", 1300, 590);
            DlgUtil.open("divDlgTaoPhieuThuoc" + "02D011");
        });

        // click button Thuốc > Tạo phiếu vat tu.
        $("#toolbarIddrug_3").on("click", function (e) {
            // SONDN 17/12/2019 L2PT-13468
            if (_checkData() > 0) {
                return;
            }
            EventUtil.setEvent("assignDrug_fail", function (e) {
                DlgUtil.close("dlgCDT");
            });
            _openDialogThuoc('02D015', 1, "Chỉ định vật tư");
        });

        // click button Thuốc > Tạo phiếu huy vat tu.
        $("#toolbarIddrug_4").on("click", function (e) {
            EventUtil.setEvent("assignDrug_fail", function (e) {
                DlgUtil.close("dlgCDT");
            });
            _openDialogThuoc('02D016', 1, "Tạo phiếu trả vật tư");
        });

        //tuyennx_add_start_20181022 L2HOTRO-11542 L2HOTRO-11471
        var loadkho = cfObj.KETHUOC_LOADKHO_HAOPHI;
        //Tao phieu hao phi
        $("#toolbarIddrug_8").on("click", function () {
            var par = ['HIS_KEDONTHUOC_CHITIET_NGT'];
            var _loaikedon = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
                maubenhphamid: "",
                loaikedon: _loaikedon,
                dichvuchaid: "",
                opt: "02D010",
                macdinh_hao_phi: 9,
                loadkhotheo: loadkho // 0: kho và tủ trực, 1: kho, 2: tủ trực.
            };

            //tuyennx_edit_start_20181023 L2PT-5325
            dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu hao phí", 1300, 590);
            //tuyennx_edit_end_20181023 L2PT-5325
            DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
        });
        //Tao phieu mien phi L2PT-29648
        $("#toolbarIddrug_mp").on("click", function () {

            //tuyennx_add_start_20190916 L2PT-8847
            if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'CHANKETHUOC_TUKHO_BNTT') == '1'
                && $("#txtTUYEN").val() == 'Trái tuyến') {
                DlgUtil.showMsg('Bệnh nhân trái tuyến không thể kê từ kho');
                return;
            }
            //tuyennx_add_end_20190916 L2PT-8847

            var par = ['HIS_KEDONTHUOC_CHITIET_NGT'];
            var _loaikedon = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                maubenhphamid: "",
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
                loaikedon: _loaikedon,
                dichvuchaid: "",
                opt: "02D010",
                macdinh_hao_phi: 15,
                loadkhotheo: loadkho // 0: kho và tủ trực, 1: kho, 2: tủ trực.
            };
            _openDialogThuoc
            //tuyennx_edit_start_20181023 L2PT-5325
            dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu miễn phí", 1300, 590);
            //tuyennx_edit_end_20181023 L2PT-5325
            DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
        });

        //Tao phieu hao phi vat tu
        $("#toolbarIddrug_hpvt").on("click", function () {
            var par = ['HIS_KEDONTHUOC_CHITIET_NGT'];
            var _loaikedon = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
                maubenhphamid: "",
                loaikedon: _loaikedon,
                dichvuchaid: "",
                opt: "02D015",
                macdinh_hao_phi: 9,
                loadkhotheo: loadkho // 0: kho và tủ trực, 1: kho, 2: tủ trực.
            };

            dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D015", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu hao phí VT", 1300, 590);
            DlgUtil.open("divDlgTaoPhieuThuoc" + "02D015");
        });
        //tuyennx_add_end_20181022

        //tao phieu van chuyen
        $("#toolbarIdbtnKHAC_8").on("click", function () {
            paramInput = {
                chidinhdichvu: '1',
                loaidichvu: '14',
                loaiphieumbp: '16',
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: _opt.phongid,
                loadkhotheo: 2 // 0: kho và tủ trực, 1: kho, 2: tủ trực.
            };

            dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 14, paramInput, "Phiếu vận chuyển", 1300, 600);
            DlgUtil.open("divDlgDichVu");
        });
        $("#toolbarIdbtnKQKCK").on("click", function () {
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);

            var paramInput = {
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
                trangthaikhambenh : rowData.TRANGTHAIKHAMBENH
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgDieutriKetHop", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K074_NHAPKQ_KHAMCK", paramInput, "Nhập kết quả khám chuyên khoa", 1000, 300);
            DlgUtil.open("divDlgDieutriKetHop");
        });
        EventUtil.setEvent("ngt02k047_chuyenpknoitru_close", function (e) {
            _loadGridData(_opt.phongid);
            DlgUtil.close("divDlgDieutriKetHop");
        });

        $("#toolbarIdbtnKHAC_16").on("click", function () {
            paramInput = {
                khambenh_id: $("#hidKHAMBENHID").val(),
                maubenhpham_id: "",
                isCopy: '0',
                userID: opt.user_id
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgPTruyenDich", "divDlg", "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenDich", paramInput, "Tạo Phiếu truyền dịch", 1000, 500);
            DlgUtil.open("divDlgPTruyenDich");
        });
        //L2PT-63268
        $("#toolbarIdtreat_7").on("click", function () {
            paramInput = {
                khambenh_id: $("#hidKHAMBENHID").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                thoigianvaovien: $('#txtDENKHAMLUC').val()
            };
            // L2PT-41986 duonghn: chỉnh sửa kích thước cửa sổ
            dlgPopup = DlgUtil.buildPopupUrl("divDlgPTruyenMau", "divDlg", "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenMau_Insert", paramInput, "Tạo phiếu truyền máu ", 1000, 500);
            DlgUtil.open("divDlgPTruyenMau");
        });
        $("#toolbarIdtreatdt_3").on("click", function () {
            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                mabenhan: $('#lblMSG_MABENHAN').val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                namsinh: $('#hidNAMSINH').val() //L2PT-14751
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgChucNangSong", "divDlg", "manager.jsp?func=../noitru/NTU02D084_ChucNangSong", paramInput, "Theo dõi chức năng sống ", 1000, 600);
            DlgUtil.open("divDlgChucNangSong");
        });
        //nhap benh an
        $("#toolbarIdbtnKHAC_10").on("click", function () {
            paramInput = {
                chidinhdichvu: '1',
                loaidichvu: '14',
                loaiphieumbp: '16',
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: _opt.phongid
            };

            dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU01H031_NhapBenhAn", paramInput, "Nhập bệnh án", 500, 250);
            DlgUtil.open("divDlgNhapBenhAn");
        });
        $("#toolbarIdbtnDoThiLuc").on("click", function (e) {
            if ($('#hidKHAMBENHID').val() == "") {
                DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác. ");
                return;
            }
            var param = {
                khambenhid: $('#hidKHAMBENHID').val(),
                benhnhanid: $('#hidBENHNHANID').val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
            };

            EventUtil.setEvent("assignSevice_savetv", function (e) {
                if (e.msg != "") {
                    DlgUtil.showMsg(e.msg);
                }
            });

            dlgPopup = DlgUtil.buildPopupUrl("dlgCV", "divDlg", "manager.jsp?func=../ngoaitru/NGT06K002_DonKinh", param, 'ĐO THỊ LỰC', 1300, 650, close);
            DlgUtil.open("dlgCV");
        });

        EventUtil.setEvent("openBa", function (e) {
            DlgUtil.close("divDlgNhapBenhAn");

            var _sql_par1 = RSUtil.buildParam("", [e.loaibenhanid]);
            var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
            var _rows1 = JSON.parse(_data1);
            var _sreenName = _rows1[0].URL;
            var _tenloaibenhan = _rows1[0].TENLOAIBENHAN;
            var _maloaibenhan = _rows1[0].MALOAIBENHAN;

            if (_sreenName != '') {
                paramInput = {
                    khambenhid: $("#hidKHAMBENHID").val(),
                    hosobenhanid: $("#hidHOSOBENHANID").val(),
                    benhnhanid: $("#hidBENHNHANID").val(),
                    loaibenhanid: e.loaibenhanid,
                    maloaibenhan: _maloaibenhan,
                    hosobenhanid_hientai: $("#hidHOSOBENHANID").val()
                };
                dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../benhan/" + _sreenName, paramInput, "Cập nhật " + _tenloaibenhan, 1300, 610);
                DlgUtil.open("divDlgBenhAnDetail");
            } else {
                DlgUtil.showMsg('Không tồn tại loại bệnh án này trong dữ liệu');
                return;
            }


        });
        //dannd_L2PT-5407_L2PT-5230_L2PT-5408

        $("#toolbarIdbtnKHAC_PDT").on("click", function () {
            var myVar = {
                khambenhId: $("#hidKHAMBENHID").val(),
                maubenhphamId: -1,
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                hosobenhanId: $("#hidHOSOBENHANID").val(),
                doituongbenhnhanId: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanId: $("#hidLOAITIEPNHANID").val(),
                subDeptId: _opt.phongid

            };
            EventUtil.setEvent("assignSevice_cancelP", function (e) {
                DlgUtil.close("dlgPhieukham");
            });

            EventUtil.setEvent("treatment_cancel", function (e) {
                $('#tabDieuTri').ntu02d027_dt({
                    _grdDieuTri: 'grdDieuTri',
                    _khambenhid: $("#hidKHAMBENHID").val(),
                    _benhnhanid: $("#hidBENHNHANID").val(),
                    _lnmbp: LNMBP_DieuTri,
                    _modeView: _flgModeView, // =1 chi view; !=1 la update
                    _hosobenhanid: ""
                });
                DlgUtil.close("dlgPhieukham");
                DlgUtil.close("divDlgPhieuDieuTri");
            });
            if (cf.HIS_PHIEUDT_FORMLV == '1') {
                dlgPopup = DlgUtil.buildPopupUrl("dlgPhieukham", "divDlg1", "manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT_LV", myVar, "Tạo phiếu điều trị", 1330, 700);
            } else {
                dlgPopup = DlgUtil.buildPopupUrl("dlgPhieukham", "divDlg1", "manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT", myVar, "Tạo phiếu điều trị", 1330, 700);
            }

            //dlgPopup.open();
            DlgUtil.open("dlgPhieukham");
            EventUtil.setEvent("assignSevice_cancel", function (e) {
                DlgUtil.close("dlgCDDV");
            });
        });

        $("#toolbarIdbtnKHAC_HOICHAN").on("click", function () {
            var i_khambenhid = $("#hidKHAMBENHID").val();
            if (i_khambenhid == '' || i_khambenhid == '-1') {
                DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
                return false;
            }
            var paramInput = {
                khambenh_id: $("#hidKHAMBENHID").val(),
                maubenhpham_id: "",
                benhnhanid: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                phong_id: $('#hidPHONGKHAMDANGKYID').val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgHoiChan", "divDlg", "manager.jsp?func=../noitru/NTU02D008_BienBanHoiChuan", paramInput, "Tạo biên bản hội chẩn", 1000, 550);
            DlgUtil.open("divDlgHoiChan");
            EventUtil.setEvent("assignSevice_SaveHoiChan", function (e) {
                DlgUtil.showMsg(e.msg, undefined, jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_TIMEOUT_THONGBAO')); //L2PT-30583
                DlgUtil.close("divDlgHoiChan");
            });
        });
        $("#toolbarIdbtnKHAC_TUVONG").on("click", function () {
            paramInput = {
                khambenh_id: $("#hidKHAMBENHID").val(),
                maubenhpham_id: ""
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgTuVong", "divDlg", "manager.jsp?func=../noitru/NTU02D119_KiemDiemTuVong", paramInput, "Biên bản kiểm điểm tử vong", 1000, 550);
            DlgUtil.open("divDlgTuVong");
        });
        //dannd_end
        $("#toolbarIdbtnPHIEUTSDU").on("click", function () {
            var i_khambenhid = $("#hidKHAMBENHID").val();
            if (i_khambenhid == '' || i_khambenhid == '-1') {
                DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
                return false;
            }
            paramInput = {
                hosobenhan_id: $("#hidHOSOBENHANID").val(),
                khambenh_id: $("#hidKHAMBENHID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuTSDU", "divDlg", "manager.jsp?func=../noitru/NTU02D128_PhieuTienSuDiUng", paramInput, "Phiếu tiền sử dị ứng", 1250, 700);
            DlgUtil.open("divDlgPhieuTSDU");
        });
        // click button Phiếu khám
        $("#toolbarIdbtnPhieuKham").on("click", function (e) {
            if ($("#hidCHUYENKHAMNGT").val() != '0'
                && cfObj.KBH_CHAN_XUTRI_KCK == "1") {
                DlgUtil.showMsg("Bệnh nhân khám chuyên khoa phòng khám, không cho phép xử trí");
                return;
            }
            var _sql_par = [];
            _sql_par.push({
                "name": "[0]",
                value: opt.user_id
            });
            var rs = jsonrpc.AjaxJson.getOneValue("GET.OFFICER.KHAMBHYT", _sql_par);
            var NGT_CHANTHAOTAC_NVKHONGKHAMBHYT = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'NGT_CHANTHAOTAC_NVKHONGKHAMBHYT');
            var DOITUONGBENHNHANID = $("#hidDOITUONGBENHNHANID").val();
            if (NGT_CHANTHAOTAC_NVKHONGKHAMBHYT == 1 && rs == '0' && DOITUONGBENHNHANID == '1') {
                DlgUtil.showMsg('Người dùng không có quyền thao tác đối với bệnh nhân BHYT ');
                return;
            }
            _phieukham();
            _loadGridData(_opt.phongid);
        });

        // click button xu tri > xu tri kham benh
        $("#toolbarIdhandling_5").on("click", function (e) {
            _phieukham();
            _loadGridData(_opt.phongid);
        });

        // open popup phieu kham benh
        function _phieukham() {
            if (cfObj.NGT_CHANXUTRI_BADN == '1') {
                var _hosobenhanid = $("#hidHOSOBENHANID").val();
                var _khambenhid = $("#hidKHAMBENHID").val();
                var _chandoan = $('#hidMACDC').val();
                var slthuoc;
                var BADN = jsonrpc.AjaxJson.getOneValue("CHECK_BADN", [{"name": "[0]", "value": _hosobenhanid}]);
                var check_thuockhoNGT = jsonrpc.AjaxJson.getOneValue("CHECK_THUOCKHO_NGT", [{
                    "name": "[0]",
                    "value": _khambenhid
                }]);
                var check_keduoi28ngay = jsonrpc.AjaxJson.getOneValue("CHECK_THUOCKHO_NGT1", [{
                    "name": "[0]",
                    "value": _khambenhid
                }]);
                var sql_par = [_khambenhid, $("#hidPHONGID").val()];
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(_SQL[1], sql_par.join('$'));
                if (data_ar != null && data_ar.length > 0) {
                    slthuoc = data_ar[0].SLTHUOC;
                }
                if (BADN !== '0' && slthuoc != 0 && check_thuockhoNGT == '0' && _chandoan !== 'N18'
                    && _chandoan !== 'N18.0' && _chandoan !== 'N18.9') {
                    DlgUtil.showMsg('Bệnh nhân đã mở bệnh án mãn tính và có thuốc thuộc kho ' +
                        'không phải loại kho ngoại trú không thể xử trí hoặc kết thúc khám!');
                    return;
                }
                if (BADN !== '0' && check_thuockhoNGT !== '0' && check_keduoi28ngay !== '0' && _chandoan !== 'N18'
                    && _chandoan !== 'N18.0' && _chandoan !== 'N18.9') {
                    DlgUtil.showMsg('Bệnh nhân đã mở bệnh án mãn tính và có thuốc thuộc kho ngoại trú ' +
                        'nhưng có số ngày kê dưới 28 ngày không thể xử trí hoặc kết thúc khám!');
                    return;
                }
            }
            //tuyennx_add_start HISL2TK-597
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHECKCANNNANG", $("#hidKHAMBENHID").val());
            if (ret == 'kococannang') {
                DlgUtil.showMsg('Bệnh nhân chưa có thông tin cân nặng');
                return;
            }
            //tuyennx_add_end

            //tuyennx_add_start L2PT-7035
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHECKXUTRI", $("#hidPHONGKHAMDANGKYID").val() + '$' + '2');
            if (ret !== '1') {
                DlgUtil.showMsg(ret);
                return;
            }
            //tuyennx_add_end

            var myVar = {
                benhnhanId: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                phongid: $("#hidPHONGID").val(),
                khoaid: _opt.khoaid,
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                type: _type									// loai kham benh;
            };
            var widthdlg = 1300;
            var heightdlg = 500;
            if (cfObj.NGT_XUTRI_FITSCREEN == 1) {
                widthdlg = $(window).width() - 50;
                heightdlg = $(window).height() - 100;
            }
            dlgPopup = DlgUtil.buildPopupUrl("dlgPhieuKB", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K005_phieukhambenh", myVar, "Phiếu khám bệnh", widthdlg, heightdlg);
            DlgUtil.open("dlgPhieuKB");
        }

        // Danh sach kham
        $("#toolbarIdbtnDSKham").on("click", function (e) {
            /*var param = "&ngaybd="+$("#toolbarIdtxtFromDate").val()+"&ngaykt="+$("#toolbarIdtxtToDate").val() + "&fname=" + encodeURIComponent(opt.fullname).replace(/[%]/g, '/');
			var _cauhinhLCD = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT_LCD_KB_FORM');
			if(_cauhinhLCD != "-1"){
				_cauhinhLCD = _cauhinhLCD.replace('[PARAM]', param);
				_cauhinhLCD = FormUtil.unescape(_cauhinhLCD);
				window.open(_cauhinhLCD,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			}
			else{
				if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT_LCD_BNDANGKHAM') == '1'){
				window.open('manager.jsp?func=../ngoaitru/NGT02K092_LCDBM32&showMode=dlg'+param,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
				}else if (_chedolcd == "1" || _chedolcd == "2"){
				window.open('manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham1&showMode=dlg'+param,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
				}else{
				window.open('manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham&showMode=dlg'+param,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
				}
			}*/
            var par = ['NGT_LCD_KHAMBENH'];
            var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
            var param = "&ngaybd=" + $("#toolbarIdtxtFromDate").val() + "&ngaykt=" + $("#toolbarIdtxtToDate").val() + "&fname=" + encodeURIComponent(opt.fullname).replace(/[%]/g, '/');
            window.open('manager.jsp?func=..' + dt + '&showMode=dlg' + param, '', 'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');

        });
        $("#toolbarIdbtnDSKham_1").on("click", function (e) {
            var param = "&ngaybd=" + $("#toolbarIdtxtFromDate").val() + "&ngaykt=" + $("#toolbarIdtxtToDate").val() + "&fname=" + encodeURIComponent(opt.fullname).replace(/[%]/g, '/');
            window.open('manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham_MP&showMode=dlg' + param, '', 'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
        });

        $("#toolbarIdhandling_1").on("click", function (e) {
            var strkcc = _khamchinhphu == "1" ? "chuyển khám chính/khám thêm" : "chuyển phòng khám";
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn ' + strkcc);
                return false;
            }
            var str = "";
            if ($("#hidCHUYENKHAMNGT").val() == "1") {
                str = "Bệnh nhân đang khám chuyên khoa, yêu cầu vào nội trú để thao tác " + strkcc
            } else {
                if ($('#hidXUTRIKHAMBENHID').val() == "6" || $('#hidXUTRIKHAMBENHID').val() == "2") {
                    str = "Bệnh nhân đã xử trí nhập viện hoặc điều trị ngoại trú không thể " + strkcc;
                }
            }
            if (str != "") {
                DlgUtil.showMsg(str);
                return false;
            }

            var myVar = {
                kieu: 1, 										//thêm phòng
                khambenhid: $('#hidKHAMBENHID').val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                dichvuid: $('#hidDICHVUID').val(),
                phongid: _opt._phongid,
                benhnhanid: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                sub_dtbnid: $("#hidSUB_DTBNID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                chinhphukieu: "0", 							// 0: kham them phong; 1: doi phong chinh
                i_hid: opt.hospital_id,
                i_sch: opt.db_schema
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgPhieuKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K046_them_chuyenphongkham", myVar, "Chuyển phòng khám", 700, 400);

            /*if(_khamchinhphu == "0"){
				dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K046_them_chuyenphongkham",myVar,"Chuyển phòng khám",700,300);
				//dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham",myVar,"Chuyển phòng khám",700,300);
			}else{
				dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K046_them_chuyenphongkham1",myVar,"Khám thêm phòng",700,400);
			} */
            DlgUtil.open("dlgPhieuKham");
        });

        $("#toolbarIdgroup_0_InPhieuXuTri").on("click", function (e) {
            var checkxutri = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'CHECK_XTRI_PKB');
            if (checkxutri == "1") {
                var _xtkbid = $('#hidXUTRIKHAMBENHID').val();
                if (_xtkbid == "0" || _xtkbid == "") {
                    DlgUtil.showMsg('Bệnh nhân chưa có xử trí. ');
                    return;
                }
            }
            var par = [{name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()},
                {name: 'phongkhamdangkyid', type: 'String', value: $("#hidPHONGKHAMDANGKYID").val()}];
            openReport('window', "PHIEU_XUTRI_KHAMBENH", "pdf", par);
        });
        $("#toolbarIdXuat_XML").on("click", function (e) {

            var myVar = {
                TIEPNHANID: $("#hidTIEPNHANID").val()
            };
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("XML_CHECKIN", JSON.stringify(myVar));
            console.log(ret)
            const link = document.createElement("a");
            const file = new Blob([ret], {type: 'text/plain'});
            link.href = URL.createObjectURL(file);
            link.download = "xml_checkin_" + $("#hidMAHOSOBENHAN").val() + ".xml";
            link.click();
            URL.revokeObjectURL(link.href);
        });
        $("#toolbarIdhandling_2").on("click", function (e) {
            var str = "";
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
                return false;
            }

            if ($("#hidCHUYENKHAMNGT").val() == "1") {
                str = "Bệnh nhân đang khám chuyên khoa, yêu cầu vào nội trú để thao tác đổi phòng khám"
            } else {
                if ($('#hidXUTRIKHAMBENHID').val() == "6" || $('#hidXUTRIKHAMBENHID').val() == "2") {
                    str = "Bệnh nhân đã xử trí nhập viện hoặc điều trị ngoại trú không thể " + _khamchinhphu == "0" ? "đổi phòng khám" : "đổi khám chính";
                }
            }
            if (str != "") {
                DlgUtil.showMsg(str);
                return false;
            }

            if (_khamchinhphu == "0") {
                // CHUC NANG DOI PHONG KHAM THUONG;
                var myVar = {
                    kieu: 0, //chuyen
                    khambenhid: $("#hidKHAMBENHID").val(),
                    tiepnhanid: $("#hidTIEPNHANID").val(),
                    phongid: _opt.phongid,
                    dichvuid: $('#hidDICHVUID').val(),
                    phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
                    maubenhphamid: $("#hidMAUBENHPHAMID").val(),
                    doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val()
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgPhieuKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham", myVar, "Đổi phòng khám", 700, 300);
                DlgUtil.open("dlgPhieuKham");
            } else {
                // CHUC NANG DOI KHAM CHINH;
                var myVar = {
                    kieu: 1, 										//thêm phòng
                    khambenhid: $('#hidKHAMBENHID').val(),
                    tiepnhanid: $("#hidTIEPNHANID").val(),
                    dichvuid: $('#hidDICHVUID').val(),
                    phongid: _opt.phongid,
                    benhnhanid: $("#hidBENHNHANID").val(),
                    hosobenhanid: $("#hidHOSOBENHANID").val(),
                    phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
                    doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                    sub_dtbnid: $("#hidSUB_DTBNID").val(),
                    maubenhphamid: $("#hidMAUBENHPHAMID").val(),
                    loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                    chinhphukieu: "0", 							// 0: kham them phong; 1: doi phong chinh
                    i_hid: opt.hospital_id,
                    i_sch: opt.db_schema
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgPhieuKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K046_them_chuyenphongkham1", myVar, "Đổi khám chính", 700, 400);
                DlgUtil.open("dlgPhieuKham");
            }
        });

        //hanv_20170712:
        // cau hinh: Hien thi/an lap phieu tam ung
        var showPTU = cfObj.NGT02K001_SHOW_PTU;
        if (showPTU == '0') {
            $("#toolbarIdbntKHAC_ptu").remove();
        }
        //Lap phieu tam ung
        $("#toolbarIdbntKHAC_ptu").on("click", function (e) {
            var paramInput = {
                tiepnhanid : $("#hidTIEPNHANID").val(), // L2PT-133110
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                khoaid: opt.khoaid,
                thoigianvaovien: $('#txtDENKHAMLUC').val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuTamUng", "divDlg", "manager.jsp?func=../noitru/NTU01H021_PhieuTamUngBenhNhan", paramInput, "Lập phiếu tạm ứng", 900, 460);
            DlgUtil.open("divDlgPhieuTamUng");
        });
        //end hanv

        $("#toolbarIdprint_tuvong").on("click", function() {
            var rpt_code = 'BIENBAN_TUVONG';
            var par = [ {
                name : 'i_khambenhid',
                type : 'String',
                value : $("#hidKHAMBENHID").val()
            } ];
            openReport('window', rpt_code, "pdf", par);
        });
        //nghiant 14062017
        $("#toolbarIdhandling_4").on("click", function (e) {
            var myVar = {
                benhnhanId: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                phongid: _opt.phongid,
                khoaid: _opt.khoaid
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgHoSoSkCaNhan", "divDlg", "manager.jsp?func=../benhan/HOSO_QUANLYSUCKHOECANHAN", myVar, "Hồ sơ quản lý sức khỏe cá nhân", 1300, 600);
            DlgUtil.open("dlgHoSoSkCaNhan");
        });
        //end nghiant 14062017
        $("#toolbarIdbtn_pkcdphcn").on("click", function () {
            var khambenhid = $("#hidKHAMBENHID").val();
            var chucNang = 'PKCDPHCN';
            var name = chucNang + '@' + khambenhid;
            var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
                screen.height + ',width=' + screen.width);
            popup.moveTo(0, 0);
            popup.onbeforeunload = function () {
                _loadGridData(_opt.phongid);
            }
        });
        $("#toolbarIdbtn_plghdcntg").on("click", function () {
            var khambenhid = $("#hidKHAMBENHID").val();
            var chucNang = 'PLGHDCNTG';
            var name = chucNang + '@' + khambenhid;
            var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
                screen.height + ',width=' + screen.width);
            popup.moveTo(0, 0);
            popup.onbeforeunload = function () {
                _loadGridData(_opt.phongid);
            }
        });
        $("#toolbarIdbtn_thphcn").on("click", function () {
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);

            var paramInput = {
                khambenhid: rowData.KHAMBENHID,
                benhnhanid: rowData.BENHNHANID,
                trangthaikhambenh: rowData.TRANGTHAIKHAMBENH,
                dichvuKhambenhID: '',
                maubenhphamid: '',
                hosobenhanid: rowData.HOSOBENHANID,
                tiepnhanid: rowData.TIEPNHANID
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgThucHienPHCN", "divDlg", "manager.jsp?func=../noitru/NTU02D199_PhieuThucHienPHCN", paramInput, "Tạo phiếu thực hiện kỹ thuật", 1100, 635);
            DlgUtil.open("divDlgThucHienPHCN");
        });
        $("#toolbarIdgroup_0_phieuxn_hiv").on("click", function () {
            var _hosobenhanid = $('#hidHOSOBENHANID').val();
            if (_hosobenhanid != null && _hosobenhanid != -1) {
                var par = [{
                    name: 'khambenhid',
                    type: 'String',
                    value: $("#hidKHAMBENHID").val()
                }];
                openReport('window', "REPORT_PHIEU_XACNHAN_HIV", "pdf", par);
            } else {
                DlgUtil.showMsg('Chưa chọn bệnh nhân');
            }
        });
        $("#toolbarIdgroup_0_Phieu_KB").on("click", function () {
            if (!$("#hidKHAMBENHID").val()) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân!');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'phongkhamdangkyid',
                type: 'String',
                value: $("#hidPHONGKHAMDANGKYID").val()
            }];
            openReport('window', "PHIEU_XUTRI_KHAMBENH", "pdf", par);
        });
        $("#toolbarIdgroup_0_phieu_chat_tnt").on("click", function () {
            if (!$("#hidKHAMBENHID").val()) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân!');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "GIAY_CD_CHAY_THANNHANTAO", "pdf", par);
        });
        $("#toolbarIdgroup_2_19").on("click", function () {
            var _benhnhanid = $("#hidBENHNHANID").val();
            var _khambenhid = $("#hidKHAMBENHID").val();
            var _hosobenhanid = $("#hidHOSOBENHANID").val();
            var _self = this;
            paramInput = {
                benhnhanid: _benhnhanid,
                khambenhid: _khambenhid,
                hosobenhanid: _hosobenhanid,
                khoaid: _opt.khoaid,
                phongid: _opt.phongid
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgtheXanhCNSK", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K109_theXanhCNSK", paramInput, "In Thẻ Xanh Chứng Nhận Sức Khỏe", 800, 600);
            DlgUtil.open("divDlgtheXanhCNSK");
        });
        //Phiếu ra viện.
        $("#toolbarIdgroup_0_1").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy ra viện.');
                return;
            }

            if ($('#hidXUTRIKHAMBENHID').val() != 1 && $('#hidXUTRIKHAMBENHID').val() != 9 && $('#hidXUTRIKHAMBENHID').val() != 3) {
                DlgUtil.showMsg('Bệnh nhân không phải xử trí ra viện, In phiếu chỉ với những bệnh nhân ra viện/khác');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'i_hid',
                type: 'String',
                value: opt.hospital_id
            }, {
                name: 'i_sch',
                type: 'String',
                value: opt.db_schema
            }];

            var pars = ['NTU_PRINT_DOC_DKTNN'];
            var data_CH_DH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));

            if (_opt.hospital_id == "919") {			// TTKHA: XUAT EXCEL
                var rpName = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", 'xlsx', par, rpName);
            } else if (_opt.hospital_id == "965") {			// TTKHA: XUAT EXCEL
                var rpName = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", 'docx', par, rpName);
            } else if (_opt.hospital_id == "939" && data_CH_DH != null && data_CH_DH == '1') {			// L2PT-2615 DKDHTNN: XUAT docx
                var rpName = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5_939" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5_939", 'docx', par, rpName);
            } else {
                openReport('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", "pdf", par);
            }
        });
        //Phiếu ra viện dạng doc
        $("#toolbarIdgroup_0_1_doc").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy ra viện.');
                return;
            }

            if ($('#hidXUTRIKHAMBENHID').val() != 1 && $('#hidXUTRIKHAMBENHID').val() != 9 && $('#hidXUTRIKHAMBENHID').val() != 3) {
                DlgUtil.showMsg('Bệnh nhân không phải xử trí ra viện, In phiếu chỉ với những bệnh nhân ra viện/khác');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'i_hid',
                type: 'String',
                value: opt.hospital_id
            }, {
                name: 'i_sch',
                type: 'String',

                value: opt.db_schema
            }];

            var rpName = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
            CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", 'docx', par, rpName);
        });

        //Phiếu hẹn khám.
        function _inGiayHenKham(_format) {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
                return;
            }

            var obj = new Object();
            obj.KHAMBENHID = $("#hidKHAMBENHID").val();
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.HK", JSON.stringify(obj));

            if (ret > 0) {
                if (_format == "pdf") {
                    var par = [{
                        name: 'khambenhid',
                        type: 'String',
                        value: $("#hidKHAMBENHID").val()
                    }];
                    openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", _format, par);
                } else {
                    var par = [{
                        name: 'khambenhid',
                        type: 'String',
                        value: $("#hidKHAMBENHID").val()
                    }, {
                        name: 'i_hid',
                        type: 'String',
                        value: opt.hospital_id
                    }, {
                        name: 'i_sch',
                        type: 'String',

                        value: opt.db_schema
                    }];
                    var rpName = "NGT014_GIAYHENKHAMLAI_TT402015_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                    CommonUtil.inPhieu('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", 'docx', par, rpName);
                }
            } else {
                DlgUtil.showMsg("Không có thông tin hẹn khám của bệnh nhân này.");
            }
        }

        $("#toolbarIdgroup_0_3").on("click", function () {
            _inGiayHenKham('pdf')
        });

        $("#toolbarIdgroup_0_31").on("click", function () {
            _inGiayHenKham('rtf')

        });

        // Xử lý sự kiện liên quan ký CA => START
        $("#toolbarIdgroup_0_1_CA").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
                return;
            }

            var _param = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            }, {
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: 'NTU009_GIAYRAVIEN_01BV01_QD4069_A5'
            }];
            _kyCaRpt(_param);
        });

        $("#toolbarIdgroup_0_2_CA").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thực hiện.');
                return;
            }

            var _param = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            }, {
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: 'NGT003_GIAYCHUYENTUYEN_TT14_A4'
            }];
            _kyCaRpt(_param);
        });

        $("#toolbarIdgroup_0_3_CA").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
                return;
            }

            var par_rpt_KySo = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $('#hidHOSOBENHANID').val()
            }, {
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: 'NGT014_GIAYHENKHAMLAI_TT402015_A4'
            }];

            _kyCaRpt(par_rpt_KySo);
        });

        $("#toolbarIdprint_PKBVV_CA").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thực hiện.');
                return;
            }

            var par_rpt_KySo = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $('#hidHOSOBENHANID').val()
            }, {
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4'
            }
            ];
            _kyCaRpt(par_rpt_KySo);
        });

        $("#toolbarIdgroup_0_PKchuyenkhoa_CA").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
                return;
            }
            var par_rpt_KySo = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $('#hidHOSOBENHANID').val()
            }, {
                name: 'i_khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'i_phongids',
                type: 'String',
                value: _opt.phongid
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: 'RPT_PHIEUKHAMCHUYENKHOA_A4'
            }];

            _kyCaRpt(par_rpt_KySo);
        });
        $("#toolbarIdbtnKySoPHIEUKHAMBENH").on("click", function() {
            if($('#hidKHAMBENHID').val() == "-1"){
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
                return false;
            }
            var obj = new Object();
            var _param = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            }, {
                name : 'phongkhamdangkyid',
                type : 'String',
                value : $("#hidPHONGKHAMDANGKYID").val()
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: 'PHIEU_XUTRI_KHAMBENH'
            }];
            _kyCaRpt(_param);
        });

        $("#toolbarIdgroup_0_PKchuyenkhoa").on("click", function () {

            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu khám chuyên khoa.');
                return;
            }

            var par = [{
                name: 'i_khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }, {
                name: 'i_phongids',
                type: 'String',
                value: $('#hidPHONGID').val()
            }, {
                name: 'i_pkdkids',
                type: 'String',
                value: $('#hidPHONGKHAMDANGKYID').val()
            }];
            openReport('window', "RPT_PHIEUKHAMCHUYENKHOA_A4", 'pdf', par);
        });

        $("#toolbarIdgroup_0_NHBHYT_CA").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
                return;
            }

            var par_rpt_KySo = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $('#hidHOSOBENHANID').val()
            }, {
                name: 'i_khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'i_phongids',
                type: 'String',
                value: _opt.phongid
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: 'RPT_GIAYNGHI_BHXH'
            }];

            _kyCaRpt(par_rpt_KySo);
        });
        $("#toolbarIdgroup_0_TTPT_KHOADY_CA").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu PTTT khoa đông y.');
                return;
            }
            var par_rpt_KySo = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $('#hidHOSOBENHANID').val()
            }, {
                name: 'maubenhphamid',
                type: 'String',
                value: $("#hidMAUBENHPHAMID").val()
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: 'PHIEUGHINHAN_TTPT_KHOADY_A5'
            }];
            _kyCaRpt(par_rpt_KySo);
        });
        // Xử lý sự kiện liên quan ký CA => END

        //In Don thuoc.
        $("#toolbarIdgroup_2_2_1").on("click", function () {
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
                return false;
            }
            var par = [{
                name: 'i_khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "NGT006_DONTHUOCK_17DBV01_TT052016_A5", "pdf", par);
        });
        //In Don thuoc.
        $("#toolbarIdgroup_2_2").on("click", function () {
            var sql_par = [];
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
                return false;
            }
            sql_par.push({"name": "[0]", "value": $('#hidKHAMBENHID').val()});

            var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
            var rows = $.parseJSON(data);


            if (rows.length >= 1) {
                if (_opt.hospital_id == 996) {
                    // hunglv L2PT-5484
                    var thuoc_thuong = '';
                    var thuoc_huongthan = '';
                    var thuoc_gaynghien = '';

                    for (var i = 0; i < rows.length; i++) { //từng đơn thuốc
                        var _par_loai = [rows[i].MAUBENHPHAMID];
                        var arr_loaithuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC", _par_loai.join('$'));
                        if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
                            for (var j = 0; j < arr_loaithuoc.length; j++) {
                                var _loaithuoc = arr_loaithuoc[j].LOAI;
                                if (_loaithuoc == 6) { //thuoc huong than
                                    thuoc_huongthan = thuoc_huongthan + ',' + rows[i].MAUBENHPHAMID;
                                } else if (_loaithuoc == 7) { //don thuoc gay nghien
                                    thuoc_gaynghien = thuoc_gaynghien + ',' + rows[i].MAUBENHPHAMID;
                                } else {
                                    thuoc_thuong = thuoc_thuong + ',' + rows[i].MAUBENHPHAMID;
                                }
                            }
                        }
                    }

                    if (thuoc_thuong.length > 0) {
                        thuoc_thuong = thuoc_thuong.substring(1);
                        var par_thuoc_thuong = [{
                            name: 'maubenhphamid',
                            type: 'String',
                            value: thuoc_thuong
                        }];
                        CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par_thuoc_thuong);
                    }
                    if (thuoc_huongthan.length > 0) {
                        thuoc_huongthan = thuoc_huongthan.substring(1);
                        var par_thuoc_huongthan = [{
                            name: 'maubenhphamid',
                            type: 'String',
                            value: thuoc_huongthan
                        }];
                        CommonUtil.openReportGet('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par_thuoc_huongthan);
                    }
                    if (thuoc_gaynghien.length > 0) {
                        thuoc_gaynghien = thuoc_gaynghien.substring(1);
                        var par_thuoc_gaynghien = [{
                            name: 'maubenhphamid',
                            type: 'String',
                            value: thuoc_gaynghien
                        }];
                        CommonUtil.openReportGet('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par_thuoc_gaynghien);
                    }

                    // TTQNI - 996
                    //var par = [ {
                    //	name : 'khambenhid',
                    //	type : 'String',
                    //	value : $('#hidKHAMBENHID').val()
                    //}];
                    //openReport('window', "NGT006_ALL_DONTHUOC_17DBV01_TT052016_A5_996", "pdf", par);

                    // end hunglv
                    return;
                }
            } else {
                DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
            }

            if (rows.length >= 1 && (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_INDONTHUOC_QUANGNGAI') == '1' ||
                jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_INDONTHUOC_QUANY15') == '1')) {
                paramInput = {
                    data: rows,
                    khambenhid: $("#hidKHAMBENHID").val()
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgInDonThuoc", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc", paramInput, "IN ĐƠN THUỐC", 800, 300);
                DlgUtil.open("dlgInDonThuoc");
                return;
            } else {
                DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
            }
            if (rows.length == 1) {
                var par = [{
                    name: 'maubenhphamid',
                    type: 'String',
                    value: rows[0].MAUBENHPHAMID
                }];
                openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
            } else if (rows.length > 1) {
                paramInput = {
                    data: rows,
                    khambenhid: $("#hidKHAMBENHID").val()
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgInDonThuoc", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc", paramInput, "IN ĐƠN THUỐC", 420, 260);
                DlgUtil.open("dlgInDonThuoc");
            } else {
                DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
            }
            // if(_opt.hospital_id == "965"){					// DA KHOA BUU DIEN IN DOCX
        });

        //Phoi thanh toan
        $("#toolbarIdgroup_0_4").on("click", function () {
            /*var flag = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T004.10",$("#hidTIEPNHANID").val());
			var _dtbnid=$("#hidDOITUONGBENHNHANID").val();
			var _tiepnhanid=$("#hidTIEPNHANID").val();
			if(_dtbnid == 1) {
				vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_A4');
				if(flag==1)
					vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT035_BKCPKCBTUTUCNGOAITRU_A4');
			} else {
				if(flag==1)
					vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT035_BKCPKCBTUTUCNGOAITRU_A4');
			}*/
            vienphi_tinhtien.inBangKe($("#hidTIEPNHANID").val(), $("#hidDOITUONGBENHNHANID").val(), '1');
        });

        //Phoi thanh toan bhyt
        $("#toolbarIdgroup_0_7").on("click", function () {
            var par = [{
                name: 'tiepnhanid',
                type: 'String',
                value: $("#hidTIEPNHANID").val()
            }];

            openReport('window', "NGT001_BKCPKCBBHYT_QD6556_DOCMOI_A4", "pdf", par);
        });

        $("#toolbarIdgroup_0_5").on("click", function () {
            var flag = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T004.10", $("#hidTIEPNHANID").val());
            var _dtbnid = $("#hidDOITUONGBENHNHANID").val();
            var _tiepnhanid = $("#hidTIEPNHANID").val();
            vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCB_HAOPHI_01BV_QD3455_A4');
        });

        //Phoi thanh toan
        $("#toolbarIdgroup_0_6").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu điều trị.');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }
                , {
                    name: 'khoaid',
                    type: 'String',
                    value: _opt.khoaid
                }
                , {
                    name: 'phongid',
                    type: 'String',
                    value: _opt.phongid
                }  //L2PT-44633 thêm khoaid, phongid
            ];

            openReport('window', "PHIEUDIEUTRI_915", "pdf", par);
        });

        //In phiếu điều trị  L2PT-30031
        $("#toolbarIdgroup_3_1").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu điều trị.');
                return;
            }

            var par = [{
                name: 'i_khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "PHIEUIN_TOMTAT_DIEUTRI", "pdf", par);
        });

        //Phiếu chuyển viện.
        $("#toolbarIdgroup_0_2").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy chuyển viện.');
                return;
            }

            if ($('#hidXUTRIKHAMBENHID').val() != 7) {
                DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];

            if (_opt.hospital_id == "902") {				// BVNT: in 2 ban khac nhau cho BHYT va DV;
                if ($("#hidDOITUONGBENHNHANID").val() == "1") {
                    openReport('window', "NGT003_GIAYCHUYENTUYEN_BHYT_A4_902", "pdf", par);
                } else {
                    openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
                }
            } else if (_opt.hospital_id == "965") {					// DA KHOA BUU DIEN IN DOCX
                var rpName = "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'docx', par, rpName);
            } else if (_opt.hospital_id == "932") {					// NHQNM: in mau khac;
                openReport('window', "NGT003_GIAYCHUYENTUYEN_BHYT_A4_932", "pdf", par);
            } else if (_opt.hospital_id == "1111") {					// DK LAN: Ham khac;
                var par1111 = [{
                    name: 'phongkhamdangkyid',
                    type: 'String',
                    value: $("#hidPHONGKHAMDANGKYID").val()
                }];
                openReport('window', "NGT003_GIAYCHUYENTUYEN_LAN_1111_2", "pdf", par1111);

            } else {
                openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
            }
        });

        $("#toolbarIdgroup_0_2_rtf").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy chuyển viện.');
                return;
            }

            if ($('#hidXUTRIKHAMBENHID').val() != 7) {
                DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];

            var rpName = "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS');
            CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'rtf', par, rpName);
        });

        $("#toolbarIdprint_nntv").on("click", function () {
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "RPT_CHANDOAN_NGUYENNHANTUVONG", "pdf", par);
        });

        $("#toolbarIdprint_xinve").on("click", function () {
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "RPT_TOMTAT_THONGTIN_NGUOIBENH_NANGXINVE", "pdf", par);
        });

        $("#toolbarIdgroup_0_2_doc").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy chuyển viện.');
                return;
            }

            if ($('#hidXUTRIKHAMBENHID').val() != 7) {
                DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];

            if (_opt.hospital_id == "965") {					// DA KHOA BUU DIEN IN DOCX
                var rpName = "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'docx', par, rpName);
            } else if (_opt.hospital_id == "1111") {					// DK LAN: Ham khac;
                var par1111 = [{
                    name: 'phongkhamdangkyid',
                    type: 'String',
                    value: $("#hidPHONGKHAMDANGKYID").val()
                }];
//				openReport('window', "NGT003_GIAYCHUYENTUYEN_LAN_1111_2", "pdf", par1111);
                var rpName = "NGT003_GIAYCHUYENTUYEN_LAN_1111_2" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_LAN_1111_2", 'docx', par, rpName);

            } else {
//				openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
                var rpName = "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'docx', par, rpName);
            }
        });

        //report phieu chi dinh CLS
        $("#toolbarIdgroup_2_1").on("click", function () {
            var par = [];
            if (_opt.hospital_id == 922) {									// BV LAO PHOI LONG AN
                _openReportClsLan(par, "PHIEU_CLSC_922", 1); // in xet nghiem rieng
                _openReportClsLan(par, "PHIEU_CLSC_922", 2); // in cdha rieng
                _openReportClsLan(par, "PHIEU_CLSC_922", 5);//in pttt rieng
            } else if (_opt.hospital_id == 1028) {				// PK VINH DUC HCM
                var param = [{
                    name: 'i_hid',
                    type: 'String',
                    value: _opt.hospital_id
                }, {
                    name: 'i_sch',
                    type: 'String',
                    value: _opt.db_schema
                }, {
                    name: 'khambenhid',
                    type: 'String',
                    value: $("#hidKHAMBENHID").val()
                }];

                var rpName = "PHIEU_CLSC" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                CommonUtil.inPhieu('window', "PHIEU_CLSC", 'xlsx', param, rpName);
            } else {
                _openReport(par, "PHIEU_CLSC", "");
            }

        });
        //L2PT-107030
        $("#toolbarIdprintCHIDINHBHYT").on("click", function () {
            var par = [];
            _openReport(par, "PHIEUBHYT_CLSC", "");
        });

        //tuyennx_add_start L2DKBD-1246
        $("#toolbarIdgroup_2_7").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in bệnh lịch.');
                return;
            }

//			if($('#hidXUTRIKHAMBENHID').val() != 7){
//				DlgUtil.showMsg('Bệnh nhân chưa có thông tin xử trí chuyển viện. ');
//				return;
//			}

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }];

            if (_opt.hospital_id == "982") {
                openReport('window', "BENHLICHCC115", 'pdf', par);// Bệnh lịch cho TTKHA
            } else {
                openReport('window', "BENHLICH_A4_951", 'pdf', par);// bệnh lịch chung
            }
        });
        $("#toolbarIdgroup_2_8").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in bệnh án.');
                return;
            }

            if ($('#hidXUTRIKHAMBENHID').val() != 2 && $('#hidXUTRIKHAMBENHID').val() != 6) {
                DlgUtil.showMsg('Bệnh nhân không phải xử trí nhập viện, In phiếu chỉ với những bệnh nhân nhập viện');
                return;
            }

            var par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $('#hidHOSOBENHANID').val()
            }];
            openReport('window', $('#hidTEN_FILE').val(), 'pdf', par);// bệnh lịch.
        });
        $("#toolbarIdprint_BANgtTT32").on("click", function () {
            var par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            }, {
                name: 'loaibenhanid',
                type: 'String',
                value: $("#hidLOAIBENHANID").val()
            }, {
                name: 'benhnhanid',
                type: 'String',
                value: $("#hidBENHNHANID").val()
            }, {
                name: 'i_sch',
                type: 'String',
                value: _opt.db_schema
            }];
            openReport('window', "BAN003_NGOAITRU_QD4069_A4_TT32", 'pdf', par);
        });
        $("#toolbarIdgroup_2_9").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu đánh giá ban đầu bệnh nhân.');
                return;
            }

            if ($('#hidXUTRIKHAMBENHID').val() != 2 && $('#hidXUTRIKHAMBENHID').val() != 6) {
                DlgUtil.showMsg('Bệnh nhân không phải xử trí nhập viện, In phiếu chỉ với những bệnh nhân nhập viện');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }];
            openReport('window', "NGT005_PHIEUDANHGIANHAPVIEN_A4_951", 'pdf', par);
        });
        $("#toolbarIdprint_CAMKETPTTT").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in !');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "NTU027_GIAYCAMDOANCHAPNHANPTTTVAGAYMEHS_03BV01_QD4069_A4", "pdf", par);
        });
        $("#toolbarIdgroup_2_10").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu sàng lọc và đánh giá dinh dưỡng.');
                return;
            }

            if ($('#hidXUTRIKHAMBENHID').val() != 2 && $('#hidXUTRIKHAMBENHID').val() != 6) {
                DlgUtil.showMsg('Bệnh nhân không phải xử trí nhập viện, In phiếu chỉ với những bệnh nhân nhập viện');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            },
                {
                    name: 'khoaid',
                    type: 'String',
                    value: _opt.khoaid
                }
            ];
            openReport('window', "PHIEU_SANGLOC_DANHGIA_DINHDUONG_A4_951", 'pdf', par);
        });

        $("#toolbarIdgroup_2_11").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in các phiếu nhập viện.');
                return;
            }

            if ($('#hidXUTRIKHAMBENHID').val() != 2 && $('#hidXUTRIKHAMBENHID').val() != 6) {
                DlgUtil.showMsg('Bệnh nhân không phải xử trí nhập viện, In phiếu chỉ với những bệnh nhân nhập viện');
                return;
            }
            var _print1tab = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'IN_GOPPHIEU_NV_1TAB');
            if (_print1tab == '1') {
                var par = [{
                    name: 'benhnhanid',
                    type: 'String',
                    value: $('#hidBENHNHANID').val()
                },
                    {
                        name: 'hosobenhanid',
                        type: 'String',
                        value: $('#hidHOSOBENHANID').val()
                    }, {
                        name: 'khambenhid',
                        type: 'String',
                        value: $('#hidKHAMBENHID').val()
                    }, {
                        name: 'report_code_loaiba',
                        type: 'String',
                        value: $('#hidTEN_FILE').val()
                    }, {
                        name: 'khoaid',
                        type: 'String',
                        value: opt.khoaid
                    }
                ];
                openReport('window', "PHIEU_IN_BENHNHAN_NHAPVIEN", 'pdf', par);
            } else {
                var par = [{
                    name: 'khambenhid',
                    type: 'String',
                    value: $('#hidKHAMBENHID').val()
                },
                    {
                        name: 'khoaid',
                        type: 'String',
                        value: _opt.khoaid
                    },
                    {
                        name: 'hosobenhanid',
                        type: 'String',
                        value: $('#hidHOSOBENHANID').val()
                    },
                    {
                        name: 'benhnhanid',
                        type: 'String',
                        value: $('#hidBENHNHANID').val()
                    },

                ];
                openReport('window', "BENHLICH_A4_951", 'pdf', par);// bệnh lịch.
                openReport('window', $('#hidTEN_FILE').val(), 'pdf', par);
                openReport('window', "PHIEU_SANGLOC_DANHGIA_DINHDUONG_A4_951", 'pdf', par);
                openReport('window', "NGT005_PHIEUDANHGIANHAPVIEN_A4_951", 'pdf', par);
                openReport('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf', par);
            }
        });
        //tuyennx_add_end
        //in phoi chenh lech
        $("#toolbarIdbtnIN_CHENHLECH").on("click", function () {
            var par = [{
                name: 'tiepnhanid',
                type: 'String',
                value: $('#hidTIEPNHANID').val()
            }];
            openReport('window', "NGT001_BKCPKCB_QD6556_DOC_CHENHLECH_A4", "pdf", par);
        });
        //in bang ke vienphi dannd L2PT-6061
        $("#toolbarIdprint_vp").on("click", function () {
            var par = [{
                name: 'tiepnhanid',
                type: 'String',
                value: $("#hidTIEPNHANID").val()
            }];
            openReport('window', "NGT001_BKCPKCBDICHVU_QD6556_A4", "pdf", par);
        });

        //START HISL2TK-1080
        $("#toolbarIdgroup_2_12").on("click", function () {
            if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
                return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
            } else {
                var _benhnhanid = $("#hidBENHNHANID").val();
                var _khambenhid = $("#hidKHAMBENHID").val();
                var _hosobenhanid = $("#hidHOSOBENHANID").val();
                var _self = this;
                paramInput = {
                    benhnhanid: _benhnhanid,
                    khambenhid: _khambenhid,
                    hosobenhanid: _hosobenhanid,
                    lnmbp: LNMBP_XetNghiem,
                    ingop: '1'//In gộp cùng phiếu
                };

                dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In phiếu XN", 1100, 600);
                DlgUtil.open("divDlgDeleteXN");
            }
        });
        //END HISL2TK-1080

        $("#toolbarIdgroup_2_21").on("click", function () {
            if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
                return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
            } else {
                var _benhnhanid = $("#hidBENHNHANID").val();
                var _khambenhid = $("#hidKHAMBENHID").val();
                var _hosobenhanid = $("#hidHOSOBENHANID").val();
                var _self = this;
                paramInput = {
                    benhnhanid: _benhnhanid,
                    khambenhid: _khambenhid,
                    hosobenhanid: _hosobenhanid,
                    lnmbp: LNMBP_CDHA,
                    ingop: '1'//In gộp cùng phiếu
                };

                dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In CĐHA chung", 1100, 600);
                DlgUtil.open("divDlgDeleteXN");
            }
        });

        //in giay nhan tra x quang cho benh nhan
        $("#toolbarIdgroup_2_13").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy nhận trả phim x-quang.');
                return;
            }

            var par = [{
                name: 'i_khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }
            ];
            openReport('window', "PHIEU_NHANTRA_XQUANG_A4", 'pdf', par);
        });

        //START HISL2TK-1085
        $("#toolbarIdgroup_2_14").on("click", function () {

            if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
                return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
            } else {
                var i_tiepnhanid = $("#hidTIEPNHANID").val();
                var i_dtbn_id = $("#hidDOITUONGBENHNHANID").val();
                var i_loaitiepnhanid = $("#hidLOAITIEPNHANID").val();
                inBangKeLPLSO(i_tiepnhanid, i_dtbn_id, i_loaitiepnhanid);
            }
        });
        //END HISL2TK-1085

        //START tuyendv
        $("#toolbarIdgroup_2_15").on("click", function () {

            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy chứng nhận thương tích.');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }
            ];
            openReport('window', "GIAY_CHUNGNHAN_THUONGTICH_965", 'pdf', par);
        });
        //END tuyendv

        //START tuyendv
        $("#toolbarIdgroup_2_16").on("click", function () {

            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in biên bản hội chẩn.');
                return;
            }

            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }
            ];
            openReport('window', "PHIEU_BIENBAN_HOICHAN", 'pdf', par);
        });
        //END tuyendv

        //START nghiant 15012019
        $("#toolbarIdgroup_2_17").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy nghỉ ốm.');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }
            ];
            openReport('window', "RPT_GIAYNGHI_BHXH", 'pdf', par);
        });
        //END nghiant
        $("#toolbarIdgroup_0_InGiayBaoTu").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu khám bệnh.');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "NTU02_PHIEUKIEMDIEMTUVONG_41BV01", "pdf", par);

        });
        $("#toolbarIdgroup_GiayBaoTu").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "NTU_PHIEUIN_GIAYBAOTU", "pdf", par);
        });
        //hunglv L2PT-8449
        $("#toolbarIdgroup_0_cc").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy xác nhận cấp cứu.');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }
            ];
            openReport('window', "PHIEU_XACNHAN_BENHNHAN_CAPCUU", 'pdf', par);
        });
        //hunglv L2PT-10660
        $("#toolbarIdgroup_0_trathe").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu trả thẻ.');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }];
            openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par);
        });
        $("#toolbarIdgroup_0_ck39k").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
                return;
            }
            //if ( ($("#hidDOITUONGBENHNHANID").val() == "2" || $("#hidDOITUONGBENHNHANID").val() == "6") )
            {
                var par = [{
                    name: 'maubenhphamid',
                    type: 'String',
                    value: $("#hidMAUBENHPHAMID").val()
                }, {
                    name: 'i_hid',
                    type: 'String',
                    value: _opt.hospital_id
                }, {
                    name: 'i_sch',
                    type: 'String',
                    value: _opt.db_schema
                }];
                openReport('window', "DKBD_PCD_THEM_CONG_KHAM_A5", "pdf", par);
            }
        });


        $("#toolbarIdgroup_2_18").on("click", function () {
            $("#toolbarIdbtnKHAC_9").click();
        });
        $("#toolbarIdgroup_2_Tamung").on("click", function () {
            var paramInput = {
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                khoaid: opt.khoaid,
                thoigianvaovien: $('#txtDENKHAMLUC').val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuTamUng", "divDlg", "manager.jsp?func=../noitru/NTU01H021_PhieuTamUngBenhNhan", paramInput, "Lập phiếu tạm ứng", 900, 460);
            DlgUtil.open("divDlgPhieuTamUng");
        });

        $("#toolbarIddrug_donkinh").on("click", function () {
            var dvkbId = '';
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
            };

            dlgPopup = DlgUtil.buildPopupUrl("dlgKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT06K001_DOTHILUC", myVar, "Đơn kính", 1300, 650);

            //dlgPopup.open();
            DlgUtil.open("dlgKham");
        });

        $("#toolbarIdprint_donkinh").on("click", function () {
            $("#toolbarIddrug_donkinh").trigger("click");
        });

        $("#toolbarIdbtnNhapTomTatBA").on("click", function () {
            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgTomTatBA", "divDlg", "manager.jsp?func=../noitru/NTU02D0113_DLG_TONGKETBENHAN", paramInput, "Tóm tắt bệnh án", 1000, 450);
            DlgUtil.open("divDlgTomTatBA");//
        });

        $("#toolbarIdbtnHUYKTKHAM").on("click", function (e) {
            var _pkdkid = $("#hidPHONGKHAMDANGKYID").val();
            if (_pkdkid == "-1" || _pkdkid == "") {
                DlgUtil.showMsg("Chọn bệnh nhân để thao tác. ");
                return;
            }
            DlgUtil.showConfirm("Thao tác sẽ hủy kết thúc khám bệnh nhân. Bạn có tiếp tục?", function (flag) {
                if (flag) {
                    var objData = new Object();
                    objData.PHONGKHAMDANGKYID = _pkdkid;
                    objData.MAUBENHPHAMID = $("#hidMAUBENHPHAMID").val();
                    objData.KHAMBENHID = $("#hidKHAMBENHID").val();
                    objData.MODE = "huyktkham";

                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.HUYKKHAM", JSON.stringify(objData));
                    if (fl == "1") {
                        DlgUtil.showMsg("Hủy kết thúc khám thành công");
                        _loadGridData(_opt.phongid);
                    } else if (fl == "-3") {
                        DlgUtil.showMsg("Bệnh nhân đang khám hoặc đã khám, không được dùng thao tác này. ");
                    } else {
                        DlgUtil.showMsg("Hủy kết thúc khám không thành công", undefined, undefined, "error");
                    }
                }
            });

        });

        // sondn L2PT-27851
        $("#toolbarIdgroup_huytrabnkkham").on("click", function () {
            var _pkdkid = $("#hidPHONGKHAMDANGKYID").val();
            if (_pkdkid == "-1" || _pkdkid == "") {
                DlgUtil.showMsg("Chọn bệnh nhân để thao tác. ");
                return;
            }
            DlgUtil.showConfirm("Bạn có muốn hủy trạng thái với Bệnh nhân này? ", function (flag) {
                if (flag) {
                    var objData = new Object();
                    objData.PHONGKHAMDANGKYID = _pkdkid;
                    objData.MAUBENHPHAMID = $("#hidMAUBENHPHAMID").val();
                    objData.KHAMBENHID = $("#hidKHAMBENHID").val();
                    objData.MODE = 'huytrabnkkham';

                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.HUYKKHAM", JSON.stringify(objData));
                    if (fl == "1") {
                        DlgUtil.showMsg("Hủy trả BN K.Khám thành công");
                        _loadGridData(_opt.phongid);
                    } else if (fl == "-3") {
                        DlgUtil.showMsg("Bệnh nhân đang khám hoặc đã khám, không được dùng thao tác này. ");
                    } else {
                        DlgUtil.showMsg("Hủy thông tin không thành công", undefined, undefined, "error");
                    }
                }
            });
        });
        // end sondn L2PT-27851

        //thaiph L2PT-12930
        $("#toolbarIdgroup_0_pkbenh").on("click", function (e) {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Chọn bệnh nhân trước khi in phiếu khám bệnh.');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }, {
                name: 'phongid',
                type: 'String',
                value: $("#hidPHONGID").val()
            }, {
                name: 'i_sch',
                type: 'String',
                value: _opt.db_schema
            }];

            var bieumau = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_MHKB_PKB');
            var template = (bieumau.split(";"))[1].split(",");
            var checkDTBN = (bieumau.split(";"))[0].split(",");
            if ($.inArray($('#hidDOITUONGBENHNHANID').val(), checkDTBN) >= 0) {
                openReport('window', template[0], "pdf", par);
            } else {
                openReport('window', template[1], "pdf", par);
            }
        });
        //end thaiph
        //dannd L2PT-9971
        $("#toolbarIdprint_ntmp").on("click", function (e) {
            var par = [{
                name: 'tiepnhanid',
                type: 'String',
                value: $("#hidTIEPNHANID").val()
            }];
            openReport('window', "NGT001_BKCPKCBBHYT_QD6556_DOC_MIENPHI_A4", "pdf", par);
        });
        //end dannd

        //dannd L2PT-126421
        $("#toolbarIdcamket_sddvktbhyt").on("click", function (e) {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Chọn bệnh nhân trước khi in phiếu khám bệnh.');
                return;
            }
            var par = [{
                name: 'benhnhanid',
                type: 'String',
                value: $("#hidBENHNHANID").val()
            },{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            },{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "RPT_CAMKET_SUDUNG_DVKT_NGOAIBHYT", "pdf", par);
        });
        //end dannd

        //dannd L2PT-126421
        $("#toolbarIdcamket_dvktbhyt_cl").on("click", function (e) {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Chọn bệnh nhân trước khi in phiếu khám bệnh.');
                return;
            }
            var par = [{
                name: 'benhnhanid',
                type: 'String',
                value: $("#hidBENHNHANID").val()
            },{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            },{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "RPT_CAMKET_DVKT_BHYT_CHENHLECH", "pdf", par);
        });
        //end dannd
        //huongpv them
        function _openReportClsLan(param, reportName, i_loainhommaubenhpham) {
            param.push({name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()});
            param.push({name: 'i_loainhommaubenhpham', type: 'String', value: i_loainhommaubenhpham});
            CommonUtil.openReportGet('window', reportName, "pdf", param);
        }

        function _openReport(param, reportName, format1) {
            var format = format1 != "rtf" && format1 != "docx" ? "pdf" : format1;

            if ($("#hidKHAMBENHID").val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
                return;
            }

            if (format == "pdf") {
                param.push({name: 'i_hid', type: 'String', value: _opt.hospital_id});
                param.push({name: 'i_sch', type: 'String', value: _opt.db_schema});
                param.push({name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()});
                param.push({name: 'phongid', type: 'String', value: phongid});
                openReport('window', reportName, format, param);
            } else {
                param.push({name: 'i_hid', type: 'String', value: _opt.hospital_id});
                param.push({name: 'i_sch', type: 'String', value: _opt.db_schema});
                param.push({name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()});

                var rpName = reportName + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + format;
                CommonUtil.inPhieu('window', reportName, format, param, rpName);
            }

        }

        //in phieu cong khai dich vu dannd_L2PT-5848
        $("#toolbarIdgroup_0_congkhaidv").on("click", function () {
            var _khambenhid = $("#hidKHAMBENHID").val();
            if (_khambenhid != null && _khambenhid != -1) {
                var paramInput = {
                    khambenhid: _khambenhid,
                    tiepnhanid: $("#hidTIEPNHANID").val()
                };
                // if (opt.hospital_id==996||opt.hospital_id==902||opt.hospital_id==944||opt.hospital_id==915||opt.hospital_id==932||opt.hospital_id==965||opt.hospital_id==957){
                var _isPopup = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "NTU_INPHIEU_CONGKHAI");
                if (_opt.hospital_id == "32620") {
                    var url = "manager.jsp?func=../noitru/NTU01H039_InPhieuCongKhaiDV2";
                    var popup = DlgUtil.buildPopupUrl("divDlgPCKDV", "divDlg", url, paramInput, "In phiếu công khai dịch vụ", 505, 268);
                    popup.open("divDlgPCKDV");
                    return;
                } else if (_isPopup == '1') {
                    var url = "manager.jsp?func=../noitru/NTU01H039_InPhieuCongKhaiDV";
                    var popup = DlgUtil.buildPopupUrl("divDlgPCKDV", "divDlg", url, paramInput, "In phiếu công khai dịch vụ", 505, 268);
                    popup.open("divDlgPCKDV");
                    return;
                } else {
                    var par = [{
                        name: 'khambenhid',
                        type: 'String',
                        value: _khambenhid
                    }];
                    openReport('window', "PHIEU_CONGKHAI_DICHVU", "pdf", par);
                }
            } else {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
                return;
            }
        });

        //Phiếu khám bệnh vào viện
        $("#toolbarIdgroup_2_5").on("click", function () {
            var par = [];
            if (_opt.hospital_id == "965") {			// BDHCM : xuat excel chuc nang nay;
                par = [{
                    name: 'i_hid',
                    type: 'String',
                    value: _opt.hospital_id
                }, {
                    name: 'i_sch',
                    type: 'String',
                    value: _opt.db_schema
                }, {
                    name: 'khambenhid',
                    type: 'String',
                    value: $("#hidKHAMBENHID").val()
                }];
                if ($("#hidDOITUONGBENHNHANID").val() == 3) {
                    var rpName = "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    CommonUtil.inPhieu('window', "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'xlsx', par, rpName);
                } else {
                    var rpName = "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    CommonUtil.inPhieu('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'xlsx', par, rpName);
                }
            } else if (_opt.hospital_id == "1007") {						// SN VPC
                _openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
            } else if (_opt.hospital_id == "1133") {
                var paramInput = {
                    khambenhid: $("#hidKHAMBENHID").val(),
                    hosobenhanid: $("#hidHOSOBENHANID").val(),
                    doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val()
                };
                dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuKhamBenhVaoVien", "divDlg", "manager.jsp?func=../ngoaitru/NGT04K008_PhieuKhamBenhVaoVien", paramInput,
                    "In giấy hồ sơ khám", 580, 500);
                DlgUtil.open("divDlgPhieuKhamBenhVaoVien");
            } else {
                if (_type == "2" && _pkbvvcapcuu == "1" && _hinhthucvaovienid == "2") {
                    // SONDN 20191009 L2PT-9604
                    if ($("#hidKHAMBENHID").val() == "-1") {
                        DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
                        return;
                    }
                    var param = [];
                    param.push({name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()});
                    CommonUtil.openReportGet('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_CAPCUU_A4", "pdf", param);
                    // END SONDN 20191009 L2PT-9604
                } else {
                    var cau_hinh_phieu_in = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_KB_MHC_TEMP_KB_NV');
                    if (cau_hinh_phieu_in == "-1") {
                        // sondn L2PT-600
                        if (_opt.hospital_id == "1103" || _opt.hospital_id == "26320" || _opt.hospital_id == "1108") {			// nan, hth, dl_kha;
                            _openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
                        } else {
                            if ($("#hidDOITUONGBENHNHANID").val() == 3) {
                                _openReport(par, "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'pdf');
                            } else {
                                if ($("#hidXUTRIKHAMBENHID").val() == 2 && _opt.hospital_id == 7282) {
                                    par = [{
                                        name: 'khambenhid',
                                        type: 'String',
                                        value: $("#hidKHAMBENHID").val()
                                    }];
                                    _openReport(par, "NGT005_PHIEUKBVAOVIEN_NGOAITRU_42BV01_QD4069_A4", 'pdf');
                                } else {
                                    _openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
                                }
                            }
                        }
                        // end sondn L2PT-600
                    } else {
                        var template = (cau_hinh_phieu_in.split(";"));
                        let map = new Map();
                        for (var i = 0; i < template.length; i++) {
                            var value = (template[i].split(","));
                            map.set(value[0].trim(), value[1].trim());
                        }
                        _openReport(par, map.get($("#hidDOITUONGBENHNHANID").val()), 'pdf');
                    }
                }
            }
        });

        //In phiếu khám bệnh chuyển viện //hunglv L2PT-7935
        //if(_opt.hospital_id == "993") $("#toolbarIdgroup_2_20").show();  // SN PYN
        //else $("#toolbarIdgroup_2_20").hide();
        $("#toolbarIdgroup_2_20").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu khám bệnh.');
                return;
            }
            var par = [
                {
                    name: 'khambenhid',
                    type: 'String',
                    value: $("#hidKHAMBENHID").val()
                }, {
                    name: 'phongid',
                    type: 'String',
                    value: $("#hidPHONGID").val()
                }
            ];
            _openReport(par, "PHIEU_KHAMBENHCHUYENVIEN_A5", 'pdf');
        });

        //Phiếu khám bệnh vào viện ck mat L2PT-27727
        $("#toolbarIdgroup_2_22").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            _openReport(par, "NGT005_PHIEUKBVAOVIEN_CK_MAT_A4", 'pdf');

        });
        //Phiếu khám bệnh vào viện ck tmh
        $("#toolbarIdbtn_pkbvv_tmh").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            _openReport(par, "NGT005_PHIEUKBVAOVIEN_CK_TMH_A4", 'pdf');
        });
        //Phiếu khám bệnh vào viện ck rhm
        $("#toolbarIdbtn_pkbvv_rhm").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
                return;
            }
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            _openReport(par, "NGT005_PHIEUKBVAOVIEN_CK_RHM_A4", 'pdf');
        });

        $("#toolbarIdprint_159").on("click", function () {
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
                return false;
            }
            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                mabenhnhan: $('#txtMABENHNHAN').val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                maubenhphamid: "",
                action: "Add"
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgCDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K044_CapThuocK", myVar, "Chỉ định thuốc không thuốc", 800, 520);
            DlgUtil.open("dlgCDT");
        });

        //In  tờ điều trị
        $("#toolbarIdgroup_2_6").on("click", function () {
            var _benhnhanid = $("#hidBENHNHANID").val();
            var _khoaid = _opt.khoaid;
            var par = [{
                name: 'i_benhnhanid',
                type: 'String',
                value: _benhnhanid
            }, {
                name: 'i_khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'i_phongid',
                type: 'String',
                value: $("#hidPHONGID").val()
            }];
            if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_INTODIEUTRI_PREVIEW') == '1') {
                openReport('window', "NGT020_TODIEUTRI_39BV01_QD4069_A4", "pdf", par);
            } else {
                var _type = "docx";				// yeu cau tu dev HUONGPV
                var rpName = "NGT020_TODIEUTRI_39BV01_QD4069_A4_" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + _type;
                CommonUtil.inPhieu('window', 'NGT020_TODIEUTRI_39BV01_QD4069_A4_965', _type, par, rpName);
            }


        });

        //ký tờ điều trị
        $("#toolbarIdgroup_2_6_CA").on("click", function () {
            var _param = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            }, {
                name: 'i_benhnhanid',
                type: 'String',
                value: $("#hidBENHNHANID").val()
            }, {
                name: 'i_khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'i_phongid',
                type: 'String',
                value: $("#hidPHONGID").val()
            }, {
                name: 'RPT_CODE',
                type: 'String',
                value: 'NGT020_TODIEUTRI_39BV01_QD4069_A4_965'
            }];
            _kyCaRpt(_param);
        });
        // click button xu tri > trả bệnh nhân (không khám)
        $("#toolbarIdhandling_3").on("click", function (e) {
            DlgUtil.showConfirm("Thao tác sẽ trả bệnh nhân không khám. Bạn có tiếp tục?", function (flag) {
                if (flag) {
                    _xutribenhnhan(1);
                }
            });

        });

        // click button xu tri > ket thuc kham
        $("#toolbarIdbtnKTKH").on("click", function (e) {
            var trangthaitiepnhan = jsonrpc.AjaxJson.getOneValue("NGT.GETCHECKKTBA", [{
                "name": "[0]",
                "value": $('#hidTIEPNHANID').val()
            }]);
            if (trangthaitiepnhan != 0 && trangthaitiepnhan != null) {
                DlgUtil.showMsg("Bệnh án đã kết thúc không thể thao tác, yêu cầu tải lại trang để cập nhật lại trang thái của bệnh nhân!");
                return false;
            }
            if ($("#hidDOITUONGBENHNHANID").val() == '1' && cfObj.HIS_CHECKMALOAIKCB_10 != '0') {
                var maloaikcb = jsonrpc.AjaxJson.ajaxCALL_SP_S("HIS.GETMALOAIKCB", $("#hidTIEPNHANID").val());
                if (maloaikcb == '10') {
                    if (cfObj.HIS_CHECKMALOAIKCB_10 == '1') {
                        if (!confirm("Mã loại khám chữa bệnh của bệnh nhân là 10 chưa được cổng BHXH chấp nhận khi đẩy cổng, có muốn tiếp tục?")) {
                            return -1;
                        }
                    } else {
                        DlgUtil.showMsg("Mã loại khám chữa bệnh của bệnh nhân là 10  chưa được cổng BHXH chấp nhận khi đẩy cổng!");
                        return -1;
                    }
                }
            }
            //tuyennx_add_start_L2PT-1744
            if (cfObj.HIS_KHOA_SOLIEU == '1') {
                var sql_par = [$("#hidTIEPNHANID").val(), jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS')];
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC32.KSL.05", sql_par.join('$'));
                var rets = ret.split(';');
                if (rets[0] > 0) {
                    DlgUtil.showMsg(rets[1]);
                    return;
                }
            }
            //L2PT-102859
            if (cfObj.HIS_KTBA_CHECK_XN_NULL != '0') {
                var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("KTK.CHECK.KQXN", $("#hidKHAMBENHID").val() + "$" + $("#hidHOSOBENHANID").val()+ "$" + $("#hidPHONGKHAMDANGKYID").val());
                if (result_ct != '0') {
                    if (result_ct == '-1') {
                        DlgUtil.showMsg('Có lỗi khi xử lý check kq XN trống');
                        return;
                    } else {
                        var msgchk = 'Tồn tại phiếu còn dịch vụ trống kết quả' + result_ct;
                        if (cfObj.HIS_KTBA_CHECK_XN_NULL == '1') {
                            if (!confirm(msgchk + '\nCó tiếp tục?')) {
                                return false;
                            }
                        } else {
                            DlgUtil.showMsg(msgchk);
                            return false;
                        }
                    }
                }
            }
            
            //L2PT-115262
            if(cfObj.NTU_KTBA_CHECK_TG_YLENH != '0'){
				var obj = {
					hosobenhanid : $('#hidHOSOBENHANID').val()
				}
				var param = JSON.stringify(obj);
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.L17", param);
				if (result && result != '-1') {
					var msgchk = ' Phiếu ' +result +' có giờ thực hiện Y Lệnh > giờ KQ';
					if (cfObj.NTU_KTBA_CHECK_TG_YLENH == '2') {
						if (!confirm(msgchk + '\nCó tiếp tục?')) {
                            return false;
                        }
					}
					else if(cfObj.NTU_KTBA_CHECK_TG_YLENH == '1') {
						DlgUtil.showMsg(msgchk);
                    	return false;
					}
				}
			}

            if (cfObj.NGT_CHANXUTRI_BADN == '1') {
                var _hosobenhanid = $("#hidHOSOBENHANID").val();
                var _khambenhid = $("#hidKHAMBENHID").val();
                var _chandoan = $('#hidMACDC').val();
                var slthuoc;
                var BADN = jsonrpc.AjaxJson.getOneValue("CHECK_BADN", [{"name": "[0]", "value": _hosobenhanid}]);
                var check_thuockhoNGT = jsonrpc.AjaxJson.getOneValue("CHECK_THUOCKHO_NGT", [{
                    "name": "[0]",
                    "value": _khambenhid
                }]);
                var check_keduoi28ngay = jsonrpc.AjaxJson.getOneValue("CHECK_THUOCKHO_NGT1", [{
                    "name": "[0]",
                    "value": _khambenhid
                }]);
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(_SQL[1], sql_par.join('$'));
                if (data_ar != null && data_ar.length > 0) {
                    slthuoc = data_ar[0].SLTHUOC;
                }
                if (BADN !== '0' && slthuoc != 0 && check_thuockhoNGT == '0' && _chandoan !== 'N18'
                    && _chandoan !== 'N18.0' && _chandoan !== 'N18.9') {
                    DlgUtil.showMsg('Bệnh nhân đã mở bệnh án mãn tính và có thuốc thuộc kho ' +
                        'không phải loại kho ngoại trú không thể xử trí hoặc kết thúc khám!');
                    return;
                }
                if (BADN !== '0' && slthuoc != 0 && check_thuockhoNGT !== '0' && check_keduoi28ngay !== '0' && _chandoan !== 'N18'
                    && _chandoan !== 'N18.0' && _chandoan !== 'N18.9') {
                    DlgUtil.showMsg('Bệnh nhân đã mở bệnh án mãn tính và có thuốc thuộc kho ngoại trú ' +
                        'nhưng có số ngày kê dưới 28 ngày không thể xử trí hoặc kết thúc khám!');
                    return;
                }

            }
            //tuyennx_add_end_L2PT-1744
            //tuyennx_edit_start_20191209 L2PT-12390
            var _sql_par = [];
            _sql_par.push({
                "name": "[0]",
                value: $("#hidTIEPNHANID").val()
            });
            var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECKKHAM5P", _sql_par);
            if (ret != '0') {
                DlgUtil.showConfirm("BN có thời gian khám bệnh dưới 5 phút bạn có muốn tiếp tục?", function (flag) {
                    if (flag) {
                        _xutribenhnhan(2);
                    }
                });
            } else
                _xutribenhnhan(2);

            //tuyennx_edit_end_20191209 L2PT-12390
        });

        // L2PT-8640 - SONDN - 10/09/2019
        $("#toolbarIdbtnTIEPNHANCC").on("click", function (e) {
            window.location.replace("/vnpthis/main/manager.jsp?func=../ngoaitru/NGT01T001_tiepnhan_ngt&hd=2");
        });
        //START L2PT-52392
        $("#toolbarIdban_QLHIV").on("click", function () {
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            paramInput = {
                khambenhid: rowData.KHAMBENHID,
                bacsydieutriid: rowData.BACSYDIEUTRIID,
                benhnhanid: rowData.BENHNHANID,
                thoigianvaovien: $("#hidNGAYTIEPNHAN").val(),
                tiepnhanid: rowData.TIEPNHANID,
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                ma_bhyt: rowData.MA_BHYT,
                cccd: rowData.SOCMTND
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgHIV", "divDlg", "manager.jsp?func=../noitru/NTU02D228_QLBN_HIV", paramInput, "QL bệnh nhân HIV", 1200, 600);
            DlgUtil.open("divDlgHIV");
        });
        //L2PT-52392 end

        // HungND L2PT-81800
        $("#toolbarIdban_QLLAO").on("click", function () {
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            paramInput = {
                khambenhid: rowData.KHAMBENHID,
                bacsydieutriid: rowData.BACSYDIEUTRIID,
                benhnhanid: rowData.BENHNHANID,
                thoigianvaovien: $("#hidNGAYTIEPNHAN").val(),
                tiepnhanid: rowData.TIEPNHANID,
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                ma_bhyt: rowData.MA_BHYT
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgHIV", "divDlg", "manager.jsp?func=../noitru/NTU02D232_QLBN_LAO", paramInput, "QL bệnh nhân Lao", 1200, 600);
            DlgUtil.open("divDlgHIV");
        });
        // HungND L2PT-81800 END
        //START L2PT-98369
        $("#toolbarIdbtnTHANHTOANVP").on("click", function () {
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            paramInput = {
                mahosobenhan: rowData.MAHOSOBENHAN,
                tiepnhanid: rowData.TIEPNHANID
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgVP", "divDlg", "manager.jsp?func=../vienphi/VPI01T001_thuvienphi", paramInput, "Thanh toán viện phí", $(window).width() - 50, $(window).height() - 100);
            DlgUtil.open("divDlgVP");
        });
        //L2PT-98369 end
        //Beg_DoanPV_20240223: Dien bien benh nhan - L2PT-74416
        $("#toolbarIdbtnLichSuDieuTri").on("click", function () {
            paramInput = {
                benhnhanid: $("#hidBENHNHANID").val(),
                mabenhnhan: $('#txtMABENHNHAN').val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgLSBenh", "divDlg", "manager.jsp?func=../noitru/NTU02D231_LichSuDieuTri", paramInput, "Lịch sử điều trị", 1200, 600);
            DlgUtil.open("dlgLSBenh");
        });
        //End_DoanPV_20240223
        //Kiểm điểm tử vong
        $("#toolbarIdbtnKHAC_2").on("click", function () {
            paramInput = {
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                diachi: $('#txtDIACHI').val(),
                namsinh: $('#hidNAMSINH').val(),
                khambenhid: $('#hidKHAMBENHID').val(),
                ngaytn: $('#hidNGAYTN').val(),
                nghenghiep: $("#hidTENNGHENGHIEP").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgKDTV", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K033_KiemdiemTuVong", paramInput, "KIỂM ĐIỂM TỬ VONG", 900, 430);
            DlgUtil.open("dlgKDTV");
        });

        //Biên bản tử vong
        $("#toolbarIdbtnKHAC_1").on("click", function () {
            paramInput = {
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                diachi: $('#txtDIACHI').val(),
                namsinh: $('#hidNAMSINH').val(),
                khambenhid: $('#hidKHAMBENHID').val(),
                ngaytn: $('#hidNGAYTN').val(),
                nghenghiep: $("#hidTENNGHENGHIEP").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgBBTV", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K032_BienbanTuVong", paramInput, "BIÊN BẢN TỬ VONG", 900, 430);
            DlgUtil.open("dlgBBTV");
        });

        // thong tin tu vong
        $("#toolbarIdbtnKHAC_0").on("click", function (e) {
            _changexutri("8");
        });

        $("#toolbarIdbtnDOICONGKHAM").on("click", function (e) {
            if (_doicongkhamkb == "-1") {
                DlgUtil.showMsg("Yêu cầu bật cấu hình Cho phép sử dụng chức năng này. ");
                return;
            }

            var myVar = {
                khambenhid: $('#hidKHAMBENHID').val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                maubenhphamid: $("#hidMAUBENHPHAMID").val(),
                dichvukhambenhid: $("#hidDICHVUKHAMBENHID").val(),			// chua co;
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                mode: _doicongkhamkb,					// 0; 1; 2
                manhinhkhambenh: 1
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgCongKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K054_CHUYENYCKHAM", myVar, "Cập nhật công khám/phòng khám", 900, 300);
            DlgUtil.open("dlgCongKham");
        });
        //tao phieu nghi duong thai
        $("#toolbarIdbtnNGHIDUONGTHAI").on("click", function () {
            var _hosobenhanid = $("#hidHOSOBENHANID").val();
            if (_hosobenhanid != null && _hosobenhanid != -1) {
                var paramInput = {
                    hosobenhanid: _hosobenhanid,
                    khambenhid: $("#hidKHAMBENHID").val()
                };
                var url = "manager.jsp?func=../noitru/NTU02D157_NghiDuongThai";
                var popup = DlgUtil.buildPopupUrl("divDlgNDT", "divDlg", url, paramInput, "Tạo phiếu nghỉ dưỡng thai", 1200, 600);
                popup.open("divDlgNDT");
            } else {
                DlgUtil.showMsg('Chưa chọn bệnh nhân');
            }
        });
        //tao phieu cham soc
        $("#toolbarIdbtnKHAC_PHIEUCS").on("click", function () {
            paramInput = {
                maubenhpham_id: "",
                khambenh_id: $("#hidKHAMBENHID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgChamSoc", "divDlg", "manager.jsp?func=../noitru/NTU02D004_PhieuChamSoc", paramInput, "Tạo phiếu chăm sóc", 750, 500);
            DlgUtil.open("divDlgChamSoc");

        });
        //callback cho phieu cham soc
        EventUtil.setEvent("assignSevice_SaveChamSoc", function (e) {
            //widget cho tab cham soc
            $('#tabChamSoc').ntu02d028_pcs({
                _grdChamSoc: 'grdChamSoc',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_ChamSoc,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
            DlgUtil.close("divDlgChamSoc");
            //setSLPhieuDVInTab($("#hidKHAMBENHID").val(),"","1");
        });

        //dannd_L2PT-1149
        $("#toolbarIdbtnTaoPhieuTienSuDiUng").on("click", function () {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
                DlgUtil.showMsg('Bạn chưa chọn bệnh nhân.');
                return;
            }
            paramInput = {
                hosobenhan_id: $("#hidHOSOBENHANID").val(),
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuTSDU", "divDlg", "manager.jsp?func=../noitru/NTU02D128_PhieuTienSuDiUng", paramInput, "Phiếu tiền sử dị ứng", $(document).width() - 50, $(window).height() - 50);
            DlgUtil.open("divDlgPhieuTSDU");
        });
        // thong tin ra viện
        $("#toolbarIdbtnKHAC_4").on("click", function (e) {
            _changexutri("1");
        });

        // click tab lay ds
        $("#tabHanhChinhTab").on("click", function (e) {
            height_window = $(window).height();   // returns height of browser viewport
            height_dsbn = $('#tabHanhChinh').height();
            height_divMain = $('#hidDocumentHeight').val();
            console.log('height_window1:' + height_window);
            console.log('height_divMain1:' + height_divMain);
            if (height_dsbn + 110 < height_window) {
                $('#divMain').css('height', height_dsbn + 110);
            } else if (height_window < height_dsbn + 110) {
                $("#divMain").css('height', height_dsbn + 110);
            } else if (height_dsbn + 110 == height_window) {
                $('#divMain').css('height', height_dsbn + 110);
            }
            _loadGridData(_opt.phongid);
        });

        // thong tin hen kham tiep
        $("#toolbarIdbtnKHAC_6").on("click", function (e) {
            _changexutri("4");
        });

        // thong tin hen kham moi
        $("#toolbarIdbtnKHAC_7").on("click", function (e) {
            _changexutri("5");
        });

        $("#toolbarIdbtnKHAC_11").on("click", function (e) {
            DlgUtil.showConfirm("Bạn có muốn cập nhật BADTNGT này? ", function (flag) {
                if (flag) {
                    _capnhatBADTNGT();
                }
            });
        });

        EventUtil.setEvent("NGT02K087_BENHLICH_CLOSE", function (e) {
            DlgUtil.close("dlgPhieuKham");
        });

        // thong tin hen kham moi
        $("#toolbarIdbtnKHAC_9").on("click", function (e) {
            var param = {
                khambenhid: $('#hidKHAMBENHID').val()
            };
            _showDialog("NGT02K058_Thongtin_nghiduong", param, 'Thông tin nghỉ hưởng BHXH', 800, 450);
        });

        $("#toolbarIdbtnFORM_KSKXINVIEC").on("click", function (e) {
            if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == null || $("#hidKHAMBENHID").val() == 'null') {
                DlgUtil.showMsg("Chọn bệnh nhân để thực hiện tiếp chức năng này. ");
                return;
            }
            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgPhieukskxinviec", "divDlg", "manager.jsp?func=../ksk/KSK06D006_FORM_KSKXINVIEC", myVar, "Phiếu KSK Xin Việc", 1600, 900);
            DlgUtil.open("dlgPhieukskxinviec");
        });
        // tuyennx_add_start L2PT-16020
        $("#toolbarIdbtnKSKLaiXe").on("click", function () {
            var param = {
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                namsinh: $('#hidNAMSINH').val(),
                khambenhid: $('#hidKHAMBENHID').val(),
                benhnhanid: $('#hidBENHNHANID').val(),
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgKSKLaiXe", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K079_KSK_LaiXe", param, "Khám sức khỏe lái xe", 1200, 520);
            DlgUtil.open("dlgKSKLaiXe");
        });
        //tuyennx_add_end L2PT-16020
        $("#toolbarIdbtnKSK_DinhKyNLD").on("click", function () {
            var param = {
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                namsinh: $('#hidNAMSINH').val(),
                khambenhid: $('#hidKHAMBENHID').val(),
                benhnhanid: $('#hidBENHNHANID').val(),
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgKSK_DinhKyNLD", "divDlg", "manager.jsp?func=../ksk/KSK07D007_KSK_DinhKyHangQuyNLD", param, "Sổ khám sức khỏe định kỳ hàng quý cho NLĐ", 1280, 600);
            DlgUtil.open("dlgKSK_DinhKyNLD");
        });
        $("#toolbarIdbtnKHAC_PhieuSinhSan").on("click", function () {
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
                return false;
            }
            var myVar = {
                benhnhanId: $("#hidBENHNHANID").val(),
                tiepnhanId: $("#hidTIEPNHANID").val(),
                khambenhId: $("#hidKHAMBENHID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgKhamSinhSan", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K011_KhamSinhSan", myVar, "Khám sinh sản", 890, 660);
            DlgUtil.open("dlgKhamSinhSan");
        });

        // thong tin chuyển viện
        $("#toolbarIdbtnKHAC_5").on("click", function (e) {
            _changexutri("7");
        });
        // dannd_Ky so
        $("#toolbarIdbtnKHAC_KySo").on("click", function () {
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
                return false;
            }
            var paramInput = {
                tiepnhanid: $("#hidTIEPNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                //tuoi: _row.TUOI,
                diachi: $('#txtDIACHI').val()
            }
            dlgPopup = DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../noitru/NTU01H101_DayLaiBenhAn", paramInput, "HIS - Đẩy hồ sơ bệnh án điện tử", 1200, 650);
            DlgUtil.open("divDlgBA");

        });
        $("#toolbarIdbtnKHAC_TTDD").on("click", function () {
            var _hosobenhanid = $("#hidHOSOBENHANID").val();
            var _khambenhid = $("#hidKHAMBENHID").val();
            paramInput = {
                hosobenhanid: _hosobenhanid,
                khambenhid: _khambenhid,
                mode: _flgModeView //L2PT-27784
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgTTDD", "divDlg", "manager.jsp?func=../noitru/NTU02D126_PhieuTinhTrangDinhDuong", paramInput,
                "Phiếu đánh giá trình trạng dinh dưỡng (Trưởng thành)", 1250, 600);
            DlgUtil.open("dlgTTDD");
        });
        $("#toolbarIdbtnKHAC_TTDDNHI").on(
            "click",
            function () {
                var _hosobenhanid = $("#hidHOSOBENHANID").val();
                var _khambenhid = $("#hidKHAMBENHID").val();
                paramInput = {
                    hosobenhanid: _hosobenhanid,
                    khambenhid: _khambenhid,
                    mode: _flgModeView //L2PT-27784
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgTTDDNhi", "divDlg", "manager.jsp?func=../noitru/NTU02D129_PhieuTinhTrangDinhDuong_Nhi", paramInput,
                    "Phiếu đánh giá trình trạng dinh dưỡng (Nhi)", 1100, 600);
                DlgUtil.open("dlgTTDDNhi");
            });
        $("#toolbarIdbtnKHAC_TTDDMANGTHAI").on(
            "click",
            function () {
                paramInput = {
                    type: 2,
                    khambenhid: $("#hidKHAMBENHID").val(),
                    mode: _flgModeView //L2PT-27784
                };
                dlgPopup = DlgUtil.buildPopupUrl("divDlgSLDDMT", "divDlg", "manager.jsp?func=../noitru/NTU02D159_SangLocDinhDuongMangThai", paramInput, "Sàng lọc dinh dưỡng cho phụ nữ mang thai",
                    1100, 600);
                DlgUtil.open("divDlgSLDDMT");
            });
        $("#toolbarIdfunction_26").on("click", function() {
            if (!checkBeforeClickOnMenu())
                return;
            var par = [ {
                name : 'khambenhid',
                type : 'String',
                value : $("#hidKHAMBENHID").val()
            } ];
            CommonUtil.openReportGet('window', "REPORT_PHIEU_XACNHAN_HIV", "pdf", par);
        });
        function checkBeforeClickOnMenu() {
            var i_khambenhid = $("#hidKHAMBENHID").val();
            if (i_khambenhid == '' || i_khambenhid == '-1') {
                DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
                return false;
            } else {
                return true;
            }
        }
        $("#toolbarIdbtnKHAC_TTDD18").on(
            "click",
            function () {
                paramInput = {
                    khambenhid: $("#hidKHAMBENHID").val(),
                    mode: _flgModeView //L2PT-27784
                };
                dlgPopup = DlgUtil.buildPopupUrl("divDlgSLDD_BNT", "divDlg", "manager.jsp?func=../noitru/NTU02D086_SangLocDinhDuongBTN", paramInput,
                    "Đánh giá tình trạng dinh dưỡng >=18 tuổi, không mang thai", 1000, 600);
                DlgUtil.open("divDlgSLDD_BNT");
            });
          $("#toolbarIdbtnTHEMGOIKHAM").on('click', function (e) {
            if ($("#txtMABENHNHAN").val() == "" || $("#txtTENBENHNHAN").val() == ""
                || $("#hidBENHNHANID").val() == "" || $("#hidHOSOBENHANID").val() == "") {
                DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để thêm gói khám");
                return;
            }
            var myVar = {
                mabenhnhan: $("#txtMABENHNHAN").val(),
                tenbenhnhan: $("#txtTENBENHNHAN").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgGOIKHAMLVVPC", "divDlg", "manager.jsp?func=../noitru/NTU02D192_GoikhamBenhnhan", myVar, "Chỉ định gói KCB", 1000, 400);
            DlgUtil.open("dlgGOIKHAMLVVPC");
        });
        //In Don thuoc HEN
        $("#toolbarIdprint_hen").on("click", function () {

            var sql_par = [];
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc HEN.');
                return false;
            }
            sql_par.push({"name": "[0]", "value": $('#hidKHAMBENHID').val()});

            var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
            var rows = $.parseJSON(data);
            if (rows.length == 1) {
                var par = [{
                    name: 'maubenhphamid',
                    type: 'String',
                    value: rows[0].MAUBENHPHAMID
                }];
                openReport('window', "NGT006_DONTHUOCHEN_17DBV01_TT052016_A5_924", "pdf", par);
            } else if (rows.length > 1) {
                paramInput = {
                    data: rows,
                    dthen: '1'
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgInDonThuoc", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc", paramInput, "IN ĐƠN THUỐC HEN", 420, 260);
                DlgUtil.open("dlgInDonThuoc");
            } else {
                DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
            }
        });

        //In Don thuoc COPD.
        $("#toolbarIdprint_copd").on("click", function () {

            var sql_par = [];
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc COPD.');
                return false;
            }
            sql_par.push({"name": "[0]", "value": $('#hidKHAMBENHID').val()});

            var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
            var rows = $.parseJSON(data);
            if (rows.length == 1) {
                var par = [{
                    name: 'maubenhphamid',
                    type: 'String',
                    value: rows[0].MAUBENHPHAMID
                }];
                openReport('window', "NGT006_DONTHUOCCOPD_17DBV01_TT052016_A5_924", "pdf", par);
            } else if (rows.length > 1) {
                paramInput = {
                    data: rows,
                    dtcopd: '1'
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgInDonThuoc", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc", paramInput, "IN ĐƠN THUỐC COPD", 420, 260);
                DlgUtil.open("dlgInDonThuoc");
            } else {
                DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
            }
        });

        $("#toolbarIdprintGOIXETNGHIEM").on("click", function () {

            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
                return false;
            }
            var par = [{
                name: 'tiepnhanid',
                type: 'String',
                value: $('#hidTIEPNHANID').val()
            }
            ];
            openReport('window', "RPT_CHIDINH_DICHVU_GOI", 'pdf', par);

        });

        // click tab benh an
        $("#tabBenhAnTab").on("click", function (e) {
            //widget thong tin benh an
            $('#tabBenhAn').ntu02d022_ttba({
                _khambenhid: $("#hidKHAMBENHID").val()
            });
        });

        // click tab dieu tri
        $("#tabDieuTriTab").on("click", function (e) {
            //widget khoi tao grid dieu tri
            $('#tabDieuTri').ntu02d027_dt({
                _grdDieuTri: 'grdDieuTri',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_DieuTri,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        EventUtil.setEvent("treatment_print", function (e) {
            //widget phieu dieu tri
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            $('#tabDieuTri').ntu02d027_dt({
                _grdDieuTri: 'grdDieuTri',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _thoigianvaovien: $("#hidTHOIGIANVAOVIEN").val(),
                _bacsidieutri: rowData.BACSYDIEUTRIID,
                _lnmbp: LNMBP_DieuTri,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
            DlgUtil.close("divDlgPhieuDieuTri");

            var _benhnhanid = $("#hidBENHNHANID").val();
            var _khoaid = $("#hidKHOAID").val();
            var _maubenhphamid = e.msg;
            var par = [{
                name: 'i_benhnhanid',
                type: 'String',
                value: _benhnhanid
            },
                {
                    name: 'i_maubenhphamid',
                    type: 'String',
                    value: _maubenhphamid
                }, {
                    name: 'i_khoaid',
                    type: 'String',
                    value: _khoaid
                }];
            openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", "pdf", par);

        });

        $("#tabHoiChuanTab").on("click", function (e) {
            $('#tabHoiChuan').ntu02d032_phc({
                _grdHoiChan: 'grdPhieuHoiChuan',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: '15',								// phieu hoi chan;
                _modeView: '0',
                _hosobenhanid: "",
                _formCall: "KB_MHC" //L2PT-55717
            });
        });

        //calback cho dong man hinh phieu dieu tri
        EventUtil.setEvent("treatment_cancel", function (e) {
            //widget phieu dieu tri
            $('#tabDieuTri').ntu02d027_dt({
                _grdDieuTri: 'grdDieuTri',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_DieuTri,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
            DlgUtil.close("divDlgPhieuDieuTri");
        });

        //hongdq-- luu in phieu dieu tri
        EventUtil.setEvent("treatment_print", function (e) {
            var par = [{
                name: 'i_benhnhanid',
                type: 'String',
                value: $("#hidBENHNHANID").val()
            }, {
                name: 'i_maubenhphamid',
                type: 'String',
                value: e.msg
            }, {
                name: 'i_khoaid',
                type: 'String',
                value: _opt.khoaid
            }];
            // kham benh ngt
            if ($("#hidLOAITIEPNHANID").val() == '1' && $("#company_id").val() == '10284') {
                openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_NGT", "pdf", par);
            } else {
                openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", "pdf", par);
            }
        });

        // click tab xet nghiem
        $("#tabXetNghiemTab").on("click", function (e) {
            //widget khoi tao grid danh sach xet nghiem
            $('#tabXetNghiem').ntu02d024_ttxn({
                _gridXnId: "grdXetNghiem",
                _gridXnDetailId: "grdXetNghiemChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_XetNghiem,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _formCall: "KB_MHC",
                _hosobenhanid: ""
            });
        });
        //L2PT-63268
        $("#tabPhieuTruyenMau").on("click", function (e) {
            $('#tcPhieuTruyenMau').ntu02d070_ttptm({
                _gridXnId: "grdPhieuTruyenMau",
                _gridXnDetailId: "grdPhieuTruyenMauChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _modeView: _flgModeView
                // =1 chi view; !=1 la update
                //_hosobenhanid: ""
            });
        });
        //widget cho tab cham soc
        $("#tabChamSocTab").on("click", function (e) {
            $('#tabChamSoc').ntu02d028_pcs({
                _grdChamSoc: 'grdChamSoc',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_ChamSoc,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        // click tab CDHA
        $("#tabCDHATab").on("click", function (e) {
            //widget khoi tao grid danh sach CDHA
            $('#tabCDHA').ntu02d025_cdha({
                _gridCDHA: "grdCDHA",
                _gridCDHADetail: "grdCDHAChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_CDHA,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _formCall: "KB_MHC",
                _hosobenhanid: ""
            });
        });

        // click tab chuyen khoa
        $("#tabChuyenKhoaTab").on("click", function (e) {
            //widget khoi tao grid danh sach chuyen khoa
            $('#tabChuyenKhoa').ntu02d026_ck({
                _gridCK: 'grdCK',
                _gridCKDetail: 'grdCKChitiet',
                _grdCKketQua: 'grdCKketQua',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_ChuyenKhoa,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _formCall: "KB_MHC",
                _hosobenhanid: ""
            });
        });

        // click tab thuoc
        $("#tabThuocTab").on("click", function (e) {
            //widget cho tab thong tin thuoc
            $('#tabThuoc').ntu02d033_pt({
                _grdPhieuthuoc: 'grdThuoc',
                _gridPhieuthuocDetail: 'grdChiTietThuoc',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_Phieuthuoc,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: "",
                _loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                _doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val()
            });
        });

        // click tab vat tu
        $("#tabVatTuTab").on("click", function (e) {
            //widget cho tab thong tin vat tu
            $('#tabVatTu').ntu02d034_pvt({
                _grdVatTu: 'grdVatTu',
                _gridVatTuDetail: 'grdChiTietVatTu',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_Phieuvattu,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: "",
                _loaitiepnhanid: $("#hidLOAITIEPNHANID").val()
            });
        });

        // click tab phieu thu khac
        $("#tabPhieuThuKhacTab").on("click", function (e) {
            $('#tabPhieuThuKhac').ntu02d029_pdv({
                _grdSuatAn: 'grdSuatAn',
                _grdSuatAnChitiet: 'grdSuatAnChitiet',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: '17',
                _loaidichvu: "1",
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        // click tab viện phí
        $("#tabVienPhiTab").on("click", function (e) {
            $('#tabVienPhi').vpi01t006_ttvp({
                _tiepnhanid: $("#hidTIEPNHANID").val(),
                _dept_id: _opt.khoaid
            });
        });

        $("#tabDanhSachTab").on("click", function (e) {
            $('#tabDanhSach').ngt02k078_dstn({
                _grdDSTN: "grdDanhSachTiepNhan",
                imgPath: _opt.imgPath
            });
        });

        $("#tabBADTNGTTab").on("click", function (e) {
            if ($("#hidKHAMBENHID").val() == "-1") {

                DlgUtil.showMsg("Yêu cầu chọn thông tin bệnh nhân để cập nhật", function () {
                    $("#tabBADTNGTTab").removeClass("active");
                    $("#tabHanhChinh").addClass("active");

                });
                return;
            }

            $('#tabBADTNGT').ngt02k090_badtngt({
                _grdDSTN: "grdDanhSachTiepNhan",
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
            });
        });

        //widget cho tab phieu van chuyen
        $("#tabPhieuVanChuyenTab").on("click", function (e) {
            $('#tabPhieuVanChuyen').ntu02d029_pdv({
                _grdSuatAn: 'grdSuatAn',
                _grdSuatAnChitiet: 'grdSuatAnChitiet',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: '16',
                _loaidichvu: "14",
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });
        $("#tabPhieuTruyenDichTab").on("click", function (e) {
            $('#tabPhieuTruyenDich').ntu02d030_td({
                _grdTruyenDich: 'grdPhieuTruyenDich',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_TruyenDich,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });
        //L2PT-63268
        EventUtil.setEvent("assignSevice_SaveTruyenMau", function (e) {
            DlgUtil.showMsg(e.msg);
            //widget cho tab truyen mau	cap nhat phieu
            $('#tcPhieuTruyenMau').ntu02d070_ttptm({
                _gridXnId: "grdPhieuTruyenMau",
                _gridXnDetailId: "grdPhieuTruyenMauChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val()
            });
            DlgUtil.close("divDlgPTruyenMau");
            //setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
        });
        //L2PT-23418
        $("#tabNgayGiuongTab").on("click", function (e) {
            //widget cho tab ngay giuong
            $('#tabNgayGiuong').ntu02d029_pdv({
                _grdSuatAn: 'grdSuatAn',
                _grdSuatAnChitiet: 'grdSuatAnChitiet',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: '12',
                _loaidichvu: '13',
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        });

        //end L2PT-23418

        function _openDialogThuoc(_opt, _loaikedon, _title, _loadkhotheo) {
            var par = ['HIS_KEDONTHUOC_CHITIET_NGT'];
            var _loaikedon = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
            //L2PT-15697 nang cap dkhnm
            if (_opt == '02D017' && cfObj.NGT_LOAI_KT_DONGY == '1') {
                _loaikedon = '1';
            }

            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
                maubenhphamid: "",
                opt: _opt,
                phongId: $('#hidPHONGID').val(),
                loaikedon: _loaikedon,
                dichvuchaid: '',
                sub_dtbnid: $("#hidSUB_DTBNID").val(),
                khamchinhphu: $("#hidKHAMCHINHPHU").val(), 				// start manhnv L2HOTRO-11801
                loadkhotheo: _loadkhotheo 									// 0: kho và tủ trực, 1: kho, 2: tủ trực.
            };
            var widthdlg = 1300;
            var heightdlg = 600;

            if (cfObj.NGT_DONTHUOC_FITSCREEN == 1) {
                widthdlg = $(window).width() - 50;
                heightdlg = $(window).height() - 100;
            }
            dlgPopup = DlgUtil.buildPopupUrl("dlgCDT", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", myVar, _title, widthdlg, heightdlg);
            DlgUtil.open("dlgCDT");
        }

        // mo popup don thuoc khong thuoc
        function _openDialogThuocK(_opt, _loaikedon, _title) {
            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                mabenhnhan: $("#txtMABENHNHAN").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                maubenhphamid: "",
                action: "Add"

            };

            dlgPopup = DlgUtil.buildPopupUrl("dlgCDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K044_CapThuocK", myVar, _title, 800, 520);
            DlgUtil.open("dlgCDT");
        }

        function _ketthuckham(vkieu) {
            //=========== vkieu = 2: BAM NUT KET THUC KHAM
            if (vkieu == 2) {
                if (cfObj.NGT_XUTRI_CHECKDVCHUAHOANTHANH != '0') {
                    var objData = new Object();
                    objData["HOSOBENHANID"] = $("#hidHOSOBENHANID").val();
                    var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHECKDVCHUAHT", JSON.stringify(objData));
                    if (ret != '0') {
                        if (cfObj.NGT_XUTRI_CHECKDVCHUAHOANTHANH == '1') {
                            if (!confirm("Tồn tại các mẫu bệnh phẩm có số phiếu : " + ret + " chưa hoàn thành!")) {
                                return -1;
                            }
                        } else {
                            DlgUtil.showMsg("Tồn tại các mẫu bệnh phẩm có số phiếu : " + ret + " chưa hoàn thành!");
                            return -1;
                        }
                    }
                }
                var NGT_XUTRI_CHECKMBPBHYTCHUACOKQ = cfObj.NGT_XUTRI_CHECKMBPBHYTCHUACOKQ;
                if (NGT_XUTRI_CHECKMBPBHYTCHUACOKQ != '0') {
                    var _sql_par = [];
                    _sql_par.push({
                        "name": "[0]",
                        "value": $("#hidKHAMBENHID").val()
                    });
                    var datakq = jsonrpc.AjaxJson.getOneValue("NGT_CHECK_PHIEUKQ", _sql_par);
                    if (datakq != null && datakq != 'null' && datakq != '' && datakq != undefined && checkphieuchuacokq == 0 &&
                        ($("#hidXUTRIKHAMBENHID").val() == "1" || $("#hidXUTRIKHAMBENHID").val() == "13" || $("#hidXUTRIKHAMBENHID").val() == "15")) {
                        if (NGT_XUTRI_CHECKMBPBHYTCHUACOKQ == '1') {
                            DlgUtil.showMsg("Tồn tại các mẫu bệnh phẩm có số phiếu : " + datakq + " chưa có kết quả!");
                            return false;
                        } else if (NGT_XUTRI_CHECKMBPBHYTCHUACOKQ == '2') {
                            DlgUtil.showConfirm("Tồn tại các mẫu bệnh phẩm có số phiếu : " + datakq + " chưa có kết quả.Có muốn kết thúc khám cho bệnh nhân? ", function (flag) {
                                if (flag) {
                                    checkphieuchuacokq = 1;
                                    _ketthuckham(vkieu);
                                } else {
                                    return false;
                                }
                            });
                            return false;
                        }

                    }
                }
                checkphieuchuacokq = 0;
                var checkTt = cfObj.HIS_CANHBAO_KHONG_TTDT;

                if (checkTt == '1') {
                    var msgCheckTt = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV018", $("#hidTIEPNHANID").val());
                    if (msgCheckTt && msgCheckTt != '') {
                        DlgUtil.showMsg('Các dịch vụ ' + msgCheckTt + ' miễn giảm thanh toán đồng thời');
                        return;
                    }
                }
                if (cfObj.NGT_KTBA_CHECKTGTOIDA != '0' && $('#hidXUTRIKHAMBENHID').val() != '2' && $('#hidXUTRIKHAMBENHID').val() != '6') {
                var ngayravien = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS')
                var diffInMinutes = getMinutesDiff($('#txtDENKHAMLUC').val(), ngayravien);
                if (diffInMinutes > cfObj.NGT_KTBA_CHECKTGTOIDA.split('@')[1]) {
                    var msgchk = 'Bệnh nhân có thời gian khám vượt quá thời gian khám tối đa được cấu hình';
                    if (cfObj.NGT_KTBA_CHECKTGTOIDA.split('@')[0] == '2') {
                        if (!confirm(msgchk + ', Có tiếp tục?')) {
                            return false;
                        }
                    }else {
                        DlgUtil.showMsg(msgchk+"!");
                        return false;
                    }
                }
            }
            }

            //tuyennx_add_start_20190308 L2PT-2448
            if ($("#hidDOITUONGBENHNHANID").val() == '1') {
                var result_tl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV017", $("#hidTIEPNHANID").val());
                if (result_tl != 'OK') {
                    DlgUtil.showMsg('Dịch vụ ' + result_tl + ' có tỷ lệ dịch vụ không hợp lý.');
                    if (cfObj.HIS_CHAN_SAI_TYLE == '1') {
                        return;
                    }
                }
            }
            //tuyennx_add_end_20190308
            //===========

            var myVar = {
                kieu: vkieu,
                khambenhid: $("#hidKHAMBENHID").val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val()
            };
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K001.KTKHAM", JSON.stringify(myVar));
            var rets = ret.split(',');

            if (rets[0] == 'r_checkdl') {
                var myVar = {
                    thongbao: rets[1]
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT04K006_ThongBao", myVar, "Thông báo", 600, 420);
                DlgUtil.open("dlgBPKT");
                return false;
            }


            if (rets[0] == '1') {
            	//L2PT-113659 tinh lai tien dvkt thanh toan bhyt cho tuyen 4
				if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_TINHTIEN_TUYEN4') == '1' && $("#hidDOITUONGBENHNHANID").val() == '1') {
					var _par_vpi = [ $("#hidKHAMBENHID").val(), $("#hidTIEPNHANID").val(), _opt.khoaid ];
					var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("TINHTIEN.HANGBV", _par_vpi.join('$'));
				}
				//L2PT-113659 end
                _dayDonThuocDT_KTK(cfObj.DTDT_DAY_DONTHUOC, $('#hidKHAMBENHID').val(), $("#hidLOAITIEPNHANID").val())	 //L2PT-30504
                if (cfObj.KHAM_ONLINE_WS == "1") {
                    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT", [$('#hidKHAMBENHID').val(), opt.hospital_code].join('$'));
                    if (data_ar && data_ar.length > 0) {
                        var objKhamOL = {
                            "NGAYKETTHUC": data_ar[0].NGAY_RAVIEN,
                            "MALUOTKHAM_HIS": $("#hidMAHOSOBENHAN").val(),
                            "TRANGTHAI": "1",
                            "NGAYHEN": data_ar[0].THOIGIANLICHHEN,
                            "DONTHUOC": data_ar[0].SOPHIEU == "" ? '0' : data_ar[0].SOPHIEU,
                            "MA_ICD": data_ar[0].MACHANDOANRAVIEN,
                            "TEN_ICD": data_ar[0].CHANDOANRAVIEN,
                            "BACSY": _opt.fullname,
                            "MABS": _opt.user_name, //L2PT-5883
                            "BENHPHU": data_ar[0].BENHPHU,
                            "PHONGKHAM": _opt._subdept_name,
                            "HUONG_DIEUTRI": data_ar[0].HUONGDIEUTRI
                        };
                        //var objKhamOL = new Object();
//						var inputJson = JSON.stringify(objKhamOL);
                        //var inputJson = "{\"NGAYKETTHUC\":\"20200410 1600\",\"MALUOTKHAM_HIS\":\"BV0000017641\",\"TRANGTHAI\":\"1\",\"NGAYHEN\":\"\",\"DONTHUOC\":\"\",\"MA_ICD\":\"0004607 - Chính\",\"TEN_ICD\":\"0004607 - Chính\"}";
//						var ret = ajaxSvc.KhamOnlineWS.sendDataKetThuc(inputJson);
//						if(ret.includes("OK;")){
//							DlgUtil.showMsg("Gửi thông tin kết thúc khám thành công!");
//						}else{
//							DlgUtil.showMsg("Lỗi gửi thông tin kết thúc khám!");
//						}
                        var _objThongtinLog = new Object();
                        _objThongtinLog["I_CHUCNANG"] = "Trả KQK 1: " + $("#hidMAHOSOBENHAN").val();
                        _objThongtinLog["I_KQKETNOI"] = JSON.stringify(objKhamOL);
                        _objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
                        var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS", JSON.stringify(_objThongtinLog));

                        var ret = ajaxSvc.KhamOnlineWS.callApiAppBN('1', objKhamOL, opt.hospital_code);

                        _objThongtinLog["I_CHUCNANG"] = "Trả KQK 2: " + $("#hidMAHOSOBENHAN").val();
                        _objThongtinLog["I_KQKETNOI"] = ret;
                        _objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
                        var f2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS", JSON.stringify(_objThongtinLog));
                        if (ret == "-1") {
                            DlgUtil.showMsg("Đã có lỗi xảy ra khi gửi dữ liệu khám online !", undefined, undefined, "error");
                        }
                    }
                }
                //tuyennx_edit_start_20171011 yc HISL2NT-393
                if (typeof rets[1] != 'undefined' && rets[1] != '' && rets[1] != '0' && rets[1] != '100') {
                    //tuyennx_edit_end_20171017 yc HISL2NT-393
                    DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?", function (flag) {
                        if (flag) {
                            _mobenhan_daingay();
                            _loadGridData(_opt.phongid);
                            _setButton(true);
                            $("#toolbarIdbtndrug").attr("disabled", true);
                            DlgUtil.showMsg('Cập nhật thông tin thành công');
                            _loadGridData(_opt.phongid);
                        }
                    });
                } else {
                    _loadGridData(_opt.phongid);
                    _setButton(true);
                    $("#toolbarIdbtndrug").attr("disabled", true);
                    DlgUtil.showMsg('Cập nhật thông tin thành công');
                    dayCongHSSK($("#hidKHAMBENHID").val());
                    _loadGridData(_opt.phongid);
                }
                // SONDN L2PT-5934 START
                var _tudongduyetketoan = cfObj.NGT_DUYETKETOAN_KTKHAM;
                var VP_DUYET_BH_KHI_DUYET_KETOAN = cfObj.VP_DUYET_BH_KHI_DUYET_KETOAN;
                if (_tudongduyetketoan == "1" &&  ($("#hidDOITUONGBENHNHANID").val() != "1" || (cfObj.NGT_DUYETKETOAN_KTKHAM_BNHYT == "1" && $("#hidDOITUONGBENHNHANID").val() == "1")) ) {
                    var objData_DUYET = new Object();
                    objData_DUYET["TIEPNHANID"] = $("#hidTIEPNHANID").val();
                    objData_DUYET["NGAY"] = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
                    objData_DUYET["NGAYRAVIEN"] = ($("#txtNGAYRAVIEN").val() == '' ? jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS') : $("#txtNGAYRAVIEN").val());
                    objData_DUYET["DATRONVIEN"] = 0;
                    objData_DUYET["SOLUONGQUYETTOAN"] = 0;
                    objData_DUYET["LOAIDUYETBHYT"] = 0;
                    objData_DUYET["KHOAID"] = _opt.khoaid;
                    objData_DUYET["PHONGID"] = _opt.phongid;
                    objData_DUYET["DUYET"] = 1;
                    if ($("#hidDOITUONGBENHNHANID").val() == 1 &&
                        (VP_DUYET_BH_KHI_DUYET_KETOAN == 0 || (VP_DUYET_BH_KHI_DUYET_KETOAN == 2
                            && $("#hidLOAITIEPNHANID").val() != 0) || (VP_DUYET_BH_KHI_DUYET_KETOAN == 3
                            && $("#hidLOAITIEPNHANID").val() == 0))) {
                        objData_DUYET.FLAG_DUYET_BH = 0;
                        var obj_BH = new Object();
                        obj_BH.TIEPNHANID = $("#hidTIEPNHANID").val();
                        obj_BH.NGAYDUYET = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
                        var month = obj_BH.NGAYDUYET.split("/")[1];
                        if (month <= 3)
                            obj_BH.QUYDUYET = "1";
                        else if (month <= 6)
                            obj_BH.QUYDUYET = "2";
                        else if (month <= 9)
                            obj_BH.QUYDUYET = "3";
                        else
                            obj_BH.QUYDUYET = "4";
                        obj_BH.HOSPITAL_CODE = opt.hospital_code;
                        objData_DUYET.DATA_BH = obj_BH;
                    } else {
                        objData_DUYET.FLAG_DUYET_BH = 1;
                    }
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI01T001.08", JSON.stringify(objData_DUYET));
                    var ltext = "Tự động duyệt kế toán khi kết thúc khám bệnh ngoại trú: " + fl;
                    save_log_act_form("VPI01T001.08", "TUDONGDUYETKETOAN_NGT", ltext, $("#hidTIEPNHANID").val().toString());
                    if (fl.substr(0, 2) == -1) {
                        DlgUtil.showMsg(fl.slice(2));
                    } else if (fl == -89) {
                        DlgUtil.showMsg("Có nhiều hơn 1 phiếu duyệt kế toán");
                    } else if (fl == -90) {
                        DlgUtil.showMsg("Chưa thiết lập khoa phòng");
                    } else if (fl == -91) {
                        DlgUtil.showMsg("Lỗi lưu dữ liệu dịch vụ");
                    } else if (fl == -92) {
                        DlgUtil.showMsg("Ngày ra viện không được lớn hơn thời gian hiện tại");
                    } else if (fl == -930) {
                        DlgUtil.showMsg("Bạn không có quyền duyệt kế toán, liên hệ với người quản trị");
                    } else if (fl == -93) {
                        DlgUtil.showMsg("Bạn không có quyền gỡ duyệt kế toán, liên hệ với người quản trị");
                    } else if (fl == -931) {
                        DlgUtil.showMsg("Hết thời gian được phép gỡ duyệt hồ sơ này, liên hệ với người quản trị");
                    } else if (fl == -94) {
                        DlgUtil.showMsg("Bệnh nhân chưa gỡ duyệt bảo hiểm");
                    } else if (fl == -95) {
                        DlgUtil.showMsg("Hồ sơ đã khóa");
                    } else if (fl == -96) {
                        DlgUtil.showMsg("Còn khoa/phòng chưa kết thúc");
                    } else if (fl == -97) {
                        DlgUtil.showMsg("Còn phòng khám chưa kết thúc");
                    } else if (fl == -98) {
                        DlgUtil.showMsg("Bệnh nhân cần thanh toán viện phí");
                    } else if (fl == -981) {
                        DlgUtil.showMsg("Lỗi khi kiểm tra dịch vụ");
                    } else if (fl == -982) {
                        DlgUtil.showMsg("Bệnh nhân còn dịch vụ chưa thu tiền");
                    } else if (fl == -99) {
                        DlgUtil.showMsg("Bệnh nhân chưa đóng bệnh án");
                    } else if (fl == -88) {
                        DlgUtil.showMsg("Bệnh nhân đã được duyệt kế toán trước đó");
                    } else if (fl == -87) {
                        DlgUtil.showMsg("Bệnh nhân chưa được duyệt kế toán hoặc đã gỡ duyệt");
                    } else if (fl == -86) {
                        DlgUtil.showMsg("Công khám đầu tiên có tỷ lệ khác 100%");
                    } else if (fl == -85) {
                        DlgUtil.showMsg("Hãy gỡ duyệt bảo hiểm trước");
                    } else if (fl == -84) {
                        DlgUtil.showMsg("Hồ sơ đã khóa, không thể gỡ duyệt kế toán");
                    }
                }
                // SONDN L2PT-5934 END

                // sondn L2PT-4494
                if (cfObj.VPI_AUTO_DUYET_BHYT_NGT == '1'
                    && $("#hidDOITUONGBENHNHANID").val() == "1") {
                    var dtar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT10", $("#hidKHAMBENHID").val() + '$' + _opt.phongid + '$' + $("#hidHOSOBENHANID").val());
                    if (dtar != null && dtar.length > 0) {
                        if (dtar[0].TRANGTHAITIEPNHAN == "1") {
                            var _duyet_bh = duyetBHYT($("#hidTIEPNHANID").val(), opt.user_id, opt.hospital_code);
                            var ltext = "Tự động duyệt BHYT khi kết thúc khám bệnh ngoại trú: " + _duyet_bh;
                            save_log_act_form("duyetBHYT", "TUDONGDUYETBHYT_NGT", ltext, $("#hidTIEPNHANID").val().toString());
                            if (_duyet_bh != 0) {
                                DlgUtil.showMsg("Có lỗi xảy ra trong quá trình đẩy dữ liệu lên cổng giám định!", undefined, undefined, "error");
                            }
                        }
                    } else {
                        DlgUtil.showMsg("Không lấy được thông tin bệnh nhân sau xử trí. 1526");
                    }
                }
                // end sondn L2PT-4494
                if ($('#hidXUTRIKHAMBENHID').val() != '2' && $('#hidXUTRIKHAMBENHID').val() != '6'
                    && $('#hidXUTRIKHAMBENHID').val() != '12' && vkieu == 2 && cfObj.NGT_LUUQUANLYBENHAN == '1') {
                    luuQuanLyBenhAn('1');
                }
            } else if (ret == 'kocoxutri') {
                DlgUtil.showMsg('Bệnh nhân hiện chưa có xử trí.');
            } else if (ret == 'coxutri') {
                DlgUtil.showMsg('Bệnh nhân có xử trí không trả bệnh nhân.');
            } else if (ret == 'khamchuyenkhoa') {
                DlgUtil.showMsg('Bệnh nhân này khám chuyên khoa phòng khám, yêu cầu vào Nội trú / Điều trị ngoại trú để thao tác bệnh nhân. ');
            } else if (ret == 'codvcls') {
                DlgUtil.showMsg('Bệnh nhân đang có chỉ định dịch vụ CLS hoặc đã kê đơn thuốc');
            } else if (ret == 'connotien') {
                DlgUtil.showMsg('Bệnh nhân còn nợ tiền, phải thanh toán mới kết thúc khám.');
            } else if (ret == 'cophieudangsua') {
                DlgUtil.showMsg('Bệnh nhân có phiếu CLS/Đơn thuốc đang sửa, không kết thúc khám được.');
            } else if (ret == 'chuacochandoan') {
                DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán.');
            } else if (ret == 'kococannang') {
                DlgUtil.showMsg('Bệnh nhân chưa có thông tin cân nặng');
            } else if (ret == 'dathanhtoan') {
                DlgUtil.showMsg('Bệnh nhân có công khám đã thanh toán, không thể trả BN. ');
            }
            //tuyennx_add_start_20171205
            else if (ret == 'thoigianxtri') {
                DlgUtil.showMsg('Bệnh nhân chưa có thời gian ra viện hoạc thời gian ra viện nhỏ hơn ngày hiện tại, cập nhật lại ngày ra viện trước.');
            } else if (ret == 'coloibhyt') {
                dlgPopup = DlgUtil.buildPopupUrl("divErrInfo", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K067_ErrInfo", [], "Danh sách chi tiết lỗi", 1100, 500);
                DlgUtil.open("divErrInfo");
            }
                //tuyennx_add_end_20171205
            //tuyennx_add_start_20180927   L2PT-26869
            else if (ret == "khongchuyenphong") {
                DlgUtil.showMsg('Bệnh nhân khám 1 phòng, không thể trả BN. ');
            } else if (ret == "dachuyenphong") {
                DlgUtil.showMsg('Bệnh nhân đã chuyển phòng khám, không thể trả BN. ');
            }
                //tuyennx_add_end_20171205 L2PT-26869
            //dannd
            else if (ret == 'dacophitt') {
                DlgUtil.showMsg('Bệnh nhân đã có tiền thanh toán dịch vụ, không được xóa bệnh nhân.');
            } else if (ret == 'daduyet') {
                DlgUtil.showMsg('Bệnh nhân đã duyệt kế toán / duyệt bảo hiểm, không được thao tác');
            } else if (ret == 'tontaibenhankhac') {
                DlgUtil.showMsg('Bệnh án đang được dùng cho BN nhập viên/ điều trị ngoại trú, không được xóa bệnh nhân');
            } else if (ret == 'dacotamung') {
                DlgUtil.showMsg('Bệnh nhân đã có tạm ứng/phát sinh phiếu thu, không được xóa bệnh nhân.');
            } else if (ret == 'dangkhamdakham') {
                DlgUtil.showMsg('Bệnh nhân đang khám / đã khám xong. Không được xóa. ');
            } else if (ret == 'tontaidvkt_thuocvt') {
                DlgUtil.showMsg('Tồn tại phiếu dịch vụ kỹ thuật hoặc thuốc/vật tư. ');
            } else if (ret == 'tontaidvkt_thuocvt_daxoa') {
                DlgUtil.showMsg(' Bệnh nhân có phát sinh chỉ định các dịch vụ không được trả bệnh nhân liên hệ quản trị để được hỗ trợ!  ');
            } else if (ret == 'ptngayhuykhacngaythu') {
                DlgUtil.showMsg('Tồn tại phiếu thu có thời gian thu và thời gian hủy không cùng ngày. ');
            } else if (ret == 'ngaydichvuketthuc') {
                DlgUtil.showMsg('Bệnh nhân có thời gian kết thúc PTTT lớn hơn thời gian ra viện, không thể kết thúc khám.');
            } else if (ret == "vuotquaicd") {
                DlgUtil.showMsg('Tổng số mã ICD trong một điều trị vượt quá số lượng quy định của BHXH!');
            }else if (ret == "cobadn") {
                DlgUtil.showMsg('Bệnh nhân đã mở bệnh án dài ngày, không thể trả bệnh nhân không khám!');
            } else {
                DlgUtil.showMsg('Cập nhật thông tin không thành công', undefined, undefined, "error");
            }
        }

        function _xutribenhnhan(vkieu) {
            //tuyennx_add_start L2PT-7035
            var NTU_KTBA_CHECK_KQCLS = cfObj.NTU_KTBA_CHECK_KQCLS;
            if (NTU_KTBA_CHECK_KQCLS && NTU_KTBA_CHECK_KQCLS != '0' && check_kqcls == 0) {
                var objData = new Object();
                objData["HOSOBENHANID"] = $('#hidHOSOBENHANID').val();
                var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV090", JSON.stringify(objData));
                if (fl != '1') {
                    if (NTU_KTBA_CHECK_KQCLS == '2') {
                        DlgUtil.showConfirm("Còn dịch vụ " + fl + " chưa trả kết quả. Yêu cầu trả kết quả hoặc xóa nếu không thực hiện! Có muốn hoàn tất khám cho bệnh nhân? ", function (flag) {
                            if (flag) {
                                check_kqcls = 1;
                                _xutribenhnhan(vkieu)
                            } else {
                                return false;
                            }
                        });
                        return false;
                    } else if (NTU_KTBA_CHECK_KQCLS == '1') {
                        DlgUtil.showMsg("Còn dịch vụ " + fl + " chưa trả kết quả. Yêu cầu trả kết quả hoặc xóa nếu không thực hiện !");
                        return false;
                    }
                }
            }
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHECKXUTRI", $("#hidPHONGKHAMDANGKYID").val() + '$' + '1');
            if (ret !== '1') {
                DlgUtil.showMsg(ret);
                return;
            }
            //tuyennx_add_end

            // sondn L2PT-3840
            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                phongid: _opt.phongid,
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
            };
            var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KTKHAM2", JSON.stringify(myVar));
            if (check == 'codvcls') {
                DlgUtil.showMsg('Bệnh nhân đang có chỉ định dịch vụ trạng thái đang chờ tiếp nhận, có thể hủy phiếu để kết thúc khám');
                return false;
            }
                // end sondn L2PT-3840

            //tuyennx_add_start_20170727  y/c HISL2BVDKHN-247
            else if (check == 'ngaydichvu') {
                DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện không thể kết thúc khám');
                return false;
            }
            //tuyennx_add_end_20170727
            else if (check == 'pasdvcls') {
                DlgUtil.showConfirm("Bệnh nhân có dịch vụ đang thực hiện, bạn có muốn kết thúc khám không.", function (flag) {
                    if (flag) {
                        _ketthuckham(vkieu);
                    }
                });
            } else if (check == '1') {
                _ketthuckham(vkieu);
            }
        }

        //calback cho man hinh chuyen mo thanh cong
        EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function (e) {
            if ($("#hidTATTHONGBAOKBHB").val() == "0" && e.msg != "") {
                //tuyennx_edit_start_20190425 L1PT-661 L2PT-14910
                DlgUtil.showMsg(e.msg, undefined, _HIS_TIMEOUT_THONGBAO);
                //tuyennx_edit_end_20190425 L1PT-661
            }

            //widget khoi tao grid danh sach xet nghiem
            $('#tabXetNghiem').ntu02d024_ttxn({
                _gridXnId: "grdXetNghiem",
                _gridXnDetailId: "grdXetNghiemChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_XetNghiem,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });

            //widget khoi tao grid danh sach CDHA
            $('#tabCDHA').ntu02d025_cdha({
                _gridCDHA: "grdCDHA",
                _gridCDHADetail: "grdCDHAChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_CDHA,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });

            //widget khoi tao grid danh sach chuyen khoa
            $('#tabChuyenKhoa').ntu02d026_ck({
                _gridCK: 'grdCK',
                _gridCKDetail: 'grdCKChitiet',
                _grdCKketQua: 'grdCKketQua',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_ChuyenKhoa,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
            if (!e.isLuuTiep) {
                DlgUtil.close("divDlgDichVu");
            }
            _loadGridData(_opt.phongid);
        });

        // calback cho man hinh tao phieu thuoc, vat tu
        EventUtil.setEvent("assignSevice_saveTaoPhieuThuoc", function (e) {
            if ($("#hidTATTHONGBAOKBHB").val() == "0" && e.msg != "") {
                //DlgUtil.showMsg(e.msg);
                DlgUtil.showMsg(e.msg, undefined, _HIS_TIMEOUT_THONGBAO); //L2PT-30583
            }

            // reload lai widget cho tung man hinh
            if (e.option == "02D010" || e.option == "02D014") {
                // widget cho tab thong tin thuoc
                $('#tabThuoc').ntu02d033_pt({
                    _grdPhieuthuoc: 'grdThuoc',
                    _gridPhieuthuocDetail: 'grdChiTietThuoc',
                    _khambenhid: $("#hidKHAMBENHID").val(),
                    _benhnhanid: $("#hidBENHNHANID").val(),
                    _lnmbp: LNMBP_Phieuthuoc,
                    _modeView: _flgModeView, // =1 chi view; !=1 la update
                    _hosobenhanid: ""
                });
            } else if (e.option == "02D015" || e.option == "02D016") {
                // widget cho tab thong tin vat tu
                $('#tabVatTu').ntu02d034_pvt({
                    _grdVatTu: 'grdVatTu',
                    _gridVatTuDetail: 'grdChiTietVatTu',
                    _khambenhid: $("#hidKHAMBENHID").val(),
                    _benhnhanid: $("#hidBENHNHANID").val(),
                    _lnmbp: LNMBP_Phieuvattu,
                    _modeView: _flgModeView, // =1 chi view; !=1 la update
                    _hosobenhanid: ""
                });
            }
            //DlgUtil.close("dlgCDT");
            //DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
            _loadGridData(_opt.phongid);
        });
        EventUtil.setEvent("assignSevice_SaveTruyenDich", function (e) {
            DlgUtil.showMsg(e.msg);
            //widget cho tab truyen dich
            $('#tabPhieuTruyenDich').ntu02d030_td({
                _grdTruyenDich: 'grdPhieuTruyenDich',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_TruyenDich,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
            DlgUtil.close("divDlgPTruyenDich");
            setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
        });
        EventUtil.setEvent("assignSevice_closechuyenphong", function (e) {
            _loadGridData(_opt.phongid);
        });

        EventUtil.setEvent("assignDrug_cancel", function (e) {
            //tuyennx_edit_start_20171017 yc HISL2NT-393
            if (typeof e.badaingay != 'undefined' && e.badaingay != '' && e.badaingay != '0' && e.badaingay != '100') {
                //tuyennx_edit_start_20171017 yc HISL2NT-393
                DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?", function (flag) {
                    if (flag) {
                        _mobenhan_daingay();

                        var HIS_KOKTKHAM_KHICODONTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_I('his_common.com_lay_cauhinh_nd', 'HIS_KOKTKHAM_KHICODONTHUOC');
                        var HIS_KETTHUCKHAM_KHICODONTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'HIS_KETTHUCKHAM_KHICODONTHUOC');
                        if (e.type == '1' && HIS_KOKTKHAM_KHICODONTHUOC == '0' && HIS_KETTHUCKHAM_KHICODONTHUOC == '1') {
                            _loadTabHanhChinh(-1);
                            _loadGridData(_opt.phongid);
                            _setButton(true);

                            $('#tabXetNghiemTab').text("Xét nghiệm");
                            $('#tabCDHATab').text("CĐHA");
                            $('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
                            $('#tabThuocTab').text("Thuốc");
                            $('#tabVatTuTab').text("Vật tư");
                            $('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
                            $('#lblSTT').html('');
                        }
                    }
                });
            }

            DlgUtil.close("dlgCDT");
            DlgUtil.close("divDlgTaoPhieuThuoc" + e.option);
        });

        //callback don thuoc khong thuoc
        EventUtil.setEvent("assignDrugK_cancel", function (e) {
            DlgUtil.close("dlgCDT");
            DlgUtil.close("divDlgTaoPhieuThuoc" + e.option);
            DlgUtil.close("divDlgTaoPhieuThuoc");
        });

        //callback cho ho so benh an
        EventUtil.setEvent("assignSevice_saveHSBADetail", function (e) {
            DlgUtil.showMsg(e.msg);
            DlgUtil.close("divDlgBenhAnDetail");
        });

        EventUtil.setEvent("assignDrug_loisaibn", function (e) {
            DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');

            DlgUtil.close("dlgCDT");
            DlgUtil.close("divDlgTaoPhieuThuoc" + e.option);
        });

        EventUtil.setEvent("assignDrug_khacbn", function (e) {
            DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');

            DlgUtil.close("dlgCDT");
            DlgUtil.close("divDlgTaoPhieuThuoc" + e.option);
        });

        EventUtil.setEvent("assignDrug_xutri", function (e) {
            $("#hidKHAMBENHID").val(e.khambenhid);
            DlgUtil.close("dlgCDT");
            DlgUtil.close("divDlgTaoPhieuThuoc" + e.option);
            _phieukham();
        });

        EventUtil.setEvent("assignSevice_savePTTT", function (e) {
            DlgUtil.showMsg(e.msg);
            $('#tabChuyenKhoa').ntu02d026_ck({
                _gridCK: 'grdCK',
                _gridCKDetail: 'grdCKChitiet',
                _grdCKketQua: 'grdCKketQua',
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_ChuyenKhoa,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
            DlgUtil.close("dlgPTTT");
        });

        EventUtil.setEvent("assignSevice_closephieukham", function (e) {
            if (e.msg == "6" || e.msg == "8" || e.msg == "0") {
                $("#toolbarIdbtndrug").attr("disabled", true);
            } else {
                $("#toolbarIdbtndrug").attr("disabled", false);
            }

            //tuyennx_edit_start_20171017 yc HISL2NT-393
            if (typeof e.badaingay != 'undefined' && e.badaingay != '' && e.badaingay != '0' && e.badaingay != '100') {
                //tuyennx_edit_start_20171017 yc HISL2NT-393
                DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?", function (flag) {
                    if (flag) {
                        _mobenhan_daingay();

                        if (e.type == '1') {
                            _loadTabHanhChinh(-1);
                            _loadGridData(_opt.phongid);
                            _setButton(true);

                            $('#tabXetNghiemTab').text("Xét nghiệm");
                            $('#tabCDHATab').text("CĐHA");
                            $('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
                            $('#tabThuocTab').text("Thuốc");
                            $('#tabVatTuTab').text("Vật tư");
                            $('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
                            $('#lblSTT').html('');
                        }
                    } else {
                        if (e.type == '1') {
                            _loadTabHanhChinh(-1);
                            _loadGridData(_opt.phongid);
                            _setButton(true);

                            $('#tabXetNghiemTab').text("Xét nghiệm");
                            $('#tabCDHATab').text("CĐHA");
                            $('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
                            $('#tabThuocTab').text("Thuốc");
                            $('#tabVatTuTab').text("Vật tư");
                            $('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
                            $('#lblSTT').html('');
                        }
                    }
                });
            } else {
                if (e.type == '1') {
                    _loadTabHanhChinh(-1);
                    _loadGridData(_opt.phongid);
                    _setButton(true);

                    $('#tabXetNghiemTab').text("Xét nghiệm");
                    $('#tabCDHATab').text("CĐHA");
                    $('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
                    $('#tabThuocTab').text("Thuốc");
                    $('#tabVatTuTab').text("Vật tư");
                    $('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
                    $('#lblSTT').html('');
                }
            }
            DlgUtil.close("dlgPhieuKB");
        });

        EventUtil.setEvent("assignSevice_khacbnxutri", function (e) {
            DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');
            DlgUtil.close("dlgPhieuKB");
        });

        EventUtil.setEvent("assignSevice_capthuoc", function (e) {
            DlgUtil.close("dlgPhieuKB");
            _openDialogThuoc('02D010', 0, "Chỉ định thuốc");
        });

        EventUtil.setEvent("exam_cancel", function (e) {
            $('#tabBenhAn').ntu02d022_ttba({
                _khambenhid: $("#hidKHAMBENHID").val()
            });
            _loadTabHanhChinh($("#hidKHAMBENHID").val());
            //_loadGridData(_opt.phongid); tạm bỏ
            DlgUtil.close("dlgKham");
        });

        EventUtil.setEvent("close_chuyenphongkham", function (e) {
            //tuyennx_edit_start_20171017 yc HISL2NT-393
            if (typeof e.badaingay != 'undefined' && e.badaingay != '' && e.badaingay != '0' && e.badaingay != '100') {
                DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?", function (flag) {
                    if (flag) {
                        _mobenhan_daingay();
                        if (e.type == '1') {
                            _loadTabHanhChinh(-1);
                            _loadGridData(_opt.phongid);
                            _setButton(true);
                        }
                    } else {
                        if (e.type == '1') {
                            _loadTabHanhChinh(-1);
                            _loadGridData(_opt.phongid);
                            _setButton(true);
                        }
                    }
                });
            } else {
                if (e.type == '1') {
                    _loadTabHanhChinh(-1);
                    _loadGridData(_opt.phongid);
                    _setButton(true);
                }
            }
            //tuyennx_edit_start_20171017 yc HISL2NT-393
            DlgUtil.close("dlgPhieuKham");
        });

        EventUtil.setEvent("chidinhthukhac", function (e) {

            paramInput = {
                chidinhdichvu: '1',
                loaidichvu: '1',
                loaiphieumbp: '17',
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: e.phongchidinh
            };

            dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 1, paramInput, "Phiếu thu khác", 1300, 600);
            DlgUtil.open("divDlgDichVu");

            DlgUtil.close("dlgPhieuKham");
        });

        EventUtil.setEvent("exam_kbhb", function (e) {
            $("#toolbarIdbtnExam").click();
        });

        EventUtil.setEvent("exam_khacbnkbhb", function (e) {
            DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');
            DlgUtil.close("dlgKham");
        });

        EventUtil.setEvent("exam_cddv", function (e) {
            $("#hidKHAMBENHID").val(e.khambenhid);
            DlgUtil.close("dlgKham");

            var myVar = {
                khambenhid: $("#hidKHAMBENHID").val(),
                benhnhanid: $("#hidBENHNHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: _opt.phongid
            };

            dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, myVar, "Tạo phiếu chỉ định dịch vụ", 1300, 660);
            DlgUtil.open("divDlgDichVu");
        });

        EventUtil.setEvent("exam_capthuoc", function (e) {
            $("#hidKHAMBENHID").val(e.khambenhid);
            DlgUtil.close("dlgKham");
            DlgUtil.close("dlgPhieuKB");
            //L2PT-24736
            var loadkho = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_KHO_KETHUOC_XUTRI');
            _openDialogThuoc('02D010', 0, "Chỉ định thuốc", loadkho);
        });

        EventUtil.setEvent("exam_capthuocmuangoai", function (e) {
            $("#hidKHAMBENHID").val(e.khambenhid);
            DlgUtil.close("dlgKham");
            DlgUtil.close("dlgPhieuKB");
            //tuyennx_edit_start_L2PT-23087
            if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'KHAMBENH_HIENTHI_MUANHATHUOC') == '0')
                _openDialogThuoc('02D011', 1, "Chỉ định thuốc mua ngoài");
            else
                _openDialogThuoc('02D019', 0, "Mua thuốc nhà thuốc");
            //tuyennx_edit_end_L2PT-23087
        });
        EventUtil.setEvent("exam_capthuocnhathuoc", function (e) {
            $("#hidKHAMBENHID").val(e.khambenhid);
            DlgUtil.close("dlgKham");
            DlgUtil.close("dlgPhieuKB");
            _openDialogThuoc('02D019', 0, "Mua thuốc nhà thuốc");
        });
        EventUtil.setEvent("exam_capthuoctutruc", function (e) {
            $("#hidKHAMBENHID").val(e.khambenhid);
            DlgUtil.close("dlgKham");
            DlgUtil.close("dlgPhieuKB");
            _openDialogThuoc('02D010', 1, "Chỉ định thuốc từ tủ trực", '2');
        });
        EventUtil.setEvent("exam_capthuocdongy", function (e) {
            $("#hidKHAMBENHID").val(e.khambenhid);
            DlgUtil.close("dlgKham");
            DlgUtil.close("dlgPhieuKB");
            EventUtil.setEvent("assignDrug_fail", function (e) {
                DlgUtil.close("dlgCDT");
            });
            _openDialogThuoc('02D017', 1, "Chỉ định thuốc YHCT");
        });
        EventUtil.setEvent("exam_xutri", function (e) {
            $("#hidKHAMBENHID").val(e.khambenhid);
            DlgUtil.close("dlgKham");

            _phieukham();
        });

        EventUtil.setEvent("exam_save", function (e) {
            $("#hidKHAMBENHID").val(e.khambenhid);
            //widget thong tin benh an
            $('#tabBenhAn').ntu02d022_ttba({
                _khambenhid: $("#hidKHAMBENHID").val()
            });
            _loadGridData(_opt.phongid);
            //DlgUtil.close("dlgKham");
        });

        EventUtil.setEvent("assignSevice_savethemchuyenphong", function (e) {
            // load du lieu cho tab Hanh Chinh
            _loadTabHanhChinh($('#hidKHAMBENHID').val());
            DlgUtil.close("dlgPhong");
        });

        //calback cho MAN HINH TAO BAN SAO DON THUOC
        EventUtil.setEvent("assignSevice_SaveCopyMbp", function (e) {
            DlgUtil.showMsg(e.msg);
            if (e.type == '7') {
                //widget cho tab thong tin thuoc
                $('#tabThuoc').ntu02d033_pt({
                    _grdPhieuthuoc: 'grdThuoc',
                    _gridPhieuthuocDetail: 'grdChiTietThuoc',
                    _khambenhid: $("#hidKHAMBENHID").val(),
                    _benhnhanid: $("#hidBENHNHANID").val(),
                    _lnmbp: LNMBP_Phieuthuoc,
                    _modeView: _flgModeView, // =1 chi view; !=1 la update
                    _hosobenhanid: ""
                });
            } else if (e.type == '8') {
                //widget cho tab thong tin thuoc
                $('#tabVatTu').ntu02d034_pvt({
                    _grdVatTu: 'grdVatTu',
                    _gridVatTuDetail: 'grdChiTietVatTu',
                    _khambenhid: $("#hidKHAMBENHID").val(),
                    _benhnhanid: $("#hidBENHNHANID").val(),
                    _lnmbp: LNMBP_Phieuvattu,
                    _modeView: _flgModeView, // =1 chi view; !=1 la update
                    _hosobenhanid: ""
                });
            } else if (e.type == '1') {
                $('#tabXetNghiem').ntu02d024_ttxn({
                    _gridXnId: "grdXetNghiem",
                    _gridXnDetailId: "grdXetNghiemChiTiet",
                    _khambenhid: $("#hidKHAMBENHID").val(),
                    _benhnhanid: $("#hidBENHNHANID").val(),
                    _lnmbp: LNMBP_XetNghiem,
                    _modeView: _flgModeView, // =1 chi view; !=1 la update
                    _hosobenhanid: ""
                });
            } else if (e.type == '2') {
                $('#tabCDHA').ntu02d025_cdha({
                    _gridCDHA: "grdCDHA",
                    _gridCDHADetail: "grdCDHAChiTiet",
                    _khambenhid: $("#hidKHAMBENHID").val(),
                    _benhnhanid: $("#hidBENHNHANID").val(),
                    _lnmbp: LNMBP_CDHA,
                    _modeView: _flgModeView, // =1 chi view; !=1 la update
                    _hosobenhanid: ""
                });
            } else if (e.type == '5') {
                $('#tabChuyenKhoa').ntu02d026_ck({
                    _gridCK: 'grdCK',
                    _gridCKDetail: 'grdCKChitiet',
                    _grdCKketQua: 'grdCKketQua',
                    _khambenhid: $("#hidKHAMBENHID").val(),
                    _benhnhanid: $("#hidBENHNHANID").val(),
                    _lnmbp: LNMBP_ChuyenKhoa,
                    _modeView: _flgModeView, // =1 chi view; !=1 la update
                    _hosobenhanid: ""
                });
            }
            _loadGridData(_opt.phongid);
            DlgUtil.close("divDlgCopyMbp");
        });

        //tuyennx_add_start ke giuong
        //tao phieu ngay giuong
        $("#toolbarIdbtnNewBed").on("click", function () {
            var kegiuongDs = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_CONFIG_KEGIUONG_DANHSACH');
            if (kegiuongDs != 1) {
                var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
                var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
                if (rowData.TRANGTHAIKHAMBENH != 4) {
                    DlgUtil.showMsg('Bệnh nhân không ở trạng thái đang khám');
                    return;
                }
                if (selRowId != null && selRowId > 0) {
                    paramInput = {
                        chidinhdichvu: '1',
                        loaidichvu: '13',
                        loaiphieumbp: '12',
                        benhnhanid: $("#hidBENHNHANID").val(),
                        khambenhid: $("#hidKHAMBENHID").val(),
                        hosobenhanid: $("#hidHOSOBENHANID").val(),
                        tiepnhanid: $("#hidTIEPNHANID").val(),
                        doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                        hinhthucvaovienid: '2',
                        loaibenhanid: $("#hidLOAIBENHANID").val(),
                        loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                        subDeptId: $('#hidPHONGID').val()
                    };

                    dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 13, paramInput, "Phiếu ngày giường", 1300, 600);
                    DlgUtil.open("divDlgDichVu");
                } else {
                    DlgUtil.showMsg('Chưa chọn bệnh nhân');
                }
            } else {
                var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
                var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
                if (rowData.TRANGTHAIKHAMBENH != 4) {
                    DlgUtil.showMsg('Bệnh nhân không ở trạng thái đang khám');
                    return;
                }
                paramInput = {
                    khambenhid: $("#hidKHAMBENHID").val(),
                    tiepnhanid: $("#hidTIEPNHANID").val()
                };

                dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVuGiuong", "divDlg", "manager.jsp?func=../noitru/NTU01H034_BenhNhanGiuong", paramInput, "Ngày giường", 1300, 600);
                DlgUtil.open("divDlgDichVuGiuong");
            }

        });
        $("#toolbarIdhandling_10").on("click", function () {
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
            if (rowData.TRANGTHAIKHAMBENH != 4) {
                DlgUtil.showMsg('Bệnh nhân đang ở trạng thái chờ nhập khoa hoặc kết thúc, không thể xếp giường');
                return;
            }

            paramInput = {
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                phongid: $("#hidPHONGID").val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgCBNVaoBuongPhong", "divDlg", "manager.jsp?func=../noitru/NTU01H032_XepGiuong", paramInput, "Xếp giường cho bệnh nhân", 1300, 600);

            DlgUtil.open("divDlgCBNVaoBuongPhong");
        });
        if (_type == "2") {
            $("#toolbarIdhandling_10").removeClass("disabled");
            $("#toolbarIdbtnNewBed").removeClass("disabled");
            var check_par = [];
            check_par.push({"name": "[0]", "value": "2"});
            $("#toolbarIdtxtFromDate").val(jsonrpc.AjaxJson.getOneValue("COM.GETSYSDATE", check_par));
            $("#toolbarIdbtnTIEPNHANCC").removeClass("disabled");
        } else {
            $("#toolbarIdhandling_10").addClass("disabled");
            $("#toolbarIdbtnNewBed").addClass("disabled");
            $("#toolbarIdbtnTIEPNHANCC").addClass("disabled");
        }

        //tuyennx_add_end

        //tuyennx_add_start chuyen vien cs HISL2TK-461
        $("#toolbarIdbtnChuyenTuyenCS").on("click", function () {

            var chandoan = "";
            chandoan = $('#hidMACDC').val() + '-' + $('#txtCDC').val() +
                ($('#txtCDP').val() == "" ? "" : ";") + $('#txtCDP').val();
//			var FOMAT_MA_BENHPHU = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','FOMAT_MA_BENHPHU');
//			if(FOMAT_MA_BENHPHU == 1){
//				chandoan = $('#hidMACDC').val()+'-'+ $('#txtCDC').val()+
//				($('#txtCDP').val() == "" ?"":";")+$('#txtCDP').val();
//			}
//			else{
//				chandoan =  $('#txtCDC').val()+ '('+ $('#hidMACDC').val()+')'+
//				($('#txtCDP').val() == "" ?"":";")+$('#txtCDP').val();
//			}
            var param = {
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                nghenghiep: $("#hidTENNGHENGHIEP").val(),
                diachi: $('#txtDIACHI').val(),
                namsinh: $('#hidNAMSINH').val(),
                khambenhid: $('#hidKHAMBENHID').val(),
                benhnhanid: $('#hidBENHNHANID').val(),
                ngaytiepnhan: $('#txtDENKHAMLUC').val(),
                chandoan: chandoan,
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgChuyenvienCS", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K071_ChuyenvienCS", param, "Chuyển viện về tuyến cơ sở", 1200, 440);
            DlgUtil.open("dlgChuyenvienCS");
        });
        // cap so benh
        $("#toolbarIdbtnCapSoBenh").on("click", function () {
            var param = {
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                ICD10CODE: $('#hidMACDC').val(),
                ICD10NAME: $('#txtCDC').val(),
                namsinh: $('#hidNAMSINH').val(),
                khambenhid: $('#hidKHAMBENHID').val(),
                benhnhanid: $('#hidBENHNHANID').val(),
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgCapSo", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K072_CapSo", param, "Cấp sổ bệnh mới", 1200, 440);
            DlgUtil.open("dlgCapSo");
        });
        //tuyennx_add_end
        //======== START SU KIEN CHO BV BACH MAI 2;
        $("#btnGOITIEP5").on("click", function () {
            _goisttbm2("1", "1"); 					// call from cominf;
        });

        $("#btnGOILAI5").on("click", function () {
//			_goisttbm2("1","2");
            $('#toolbarIdbtnCall').click();
        });

        $("#btnLCDNHO5").on("click", function () {
            var param = "&ngaybd=" + $("#toolbarIdtxtFromDate").val() + "&ngaykt=" + $("#toolbarIdtxtToDate").val() + "&fname=" + encodeURIComponent(opt.fullname).replace(/[%]/g, '/');
            window.open('manager.jsp?func=../ngoaitru/NGT02K092_LCDBM32&showMode=dlg' + param, '', 'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
        });

        // daidv them bao dong do noi vien
        /*$("#btnBAODONGDO").on("click", function(){
			var _par = {};
			dlgPopup = DlgUtil
					.buildPopupUrl(
							"dlgNTNCC",
							"divDlg",
							"manager.jsp?func=../duoc/DUC99_KichHoatBDD",
							_par, "Kích hoạt báo động đỏ ", window.innerWidth*0.55,window.innerHeight*0.55);

			dlgPopup.open("dlgNTNCC");
		});*/

        $("#btnDSGOILAI5").on("click", function () {
//	        var myVar = {
//	        		phongid : _opts.phongid
//	        };
//			dlgPopup=DlgUtil.buildPopupUrl("dlgCV","divDlg","manager.jsp?func=../ngoaitru/NGT02K053_BM2_BNLOHEN"
//					,myVar,"Danh sách bệnh nhân gọi lại",750,400);
//			DlgUtil.open("dlgCV");

        });

        EventUtil.setEvent("evt_kios_bnlohen", function (e) {
//			if(typeof(e) != 'undefined'){
//				DlgUtil.close("dlgCV");
////				DlgUtil.showMsg("Đã gọi lại bệnh nhân " + e.tenbenhnhan);
//				$("#txtID5").val(e.id);
//				_goisttbm2("2");
//			}
        });
        //========= END SU KIEN CHO BV BACH MAI 2
        $("#btnONLINECHUYENCSYT").on('click', function () {
            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);

            var nguoiGoi = {
                "hoTen": opt.fullname,
                "maCSYT": opt.hospital_code,
                "tenDangNhap": opt.user_name
            };

            var nguoiNhan = {
                "hoTen": "",
                "maCSYT": rowData.MA_CSYT_CHUYEN,
                "tenDangNhap": rowData.USER_CHUYEN
            };
            VideoCall.createCall(nguoiGoi, nguoiNhan);
        });

        $('#toolbarIdbtnThuTienKhac').on('click', function (e) {
            paramInput = {
                chidinhdichvu: '1',
                loaidichvu: '1',
                loaiphieumbp: '17',
                benhnhanid: $("#hidBENHNHANID").val(),
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: $("#hidHOSOBENHANID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
                loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
                subDeptId: $("#cboPHONGKHAMID").val()
            };

            dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 1, paramInput, "Phiếu thu khác.", 1300, 600);
            DlgUtil.open("divDlgDichVu");
        });
        $("#toolbarIdbtnHenTaiKham").on("click", function (e) {
            var param = {
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                nghenghiep: $("#hidTENNGHENGHIEP").val(),
                diachi: $('#txtDIACHI').val(),
                namsinh: $('#hidNAMSINH').val(),
                khambenhid: $('#hidKHAMBENHID').val(),
                benhnhanid: $('#hidBENHNHANID').val(),
                chandoan: $('#txtCDC').val(),
                ngaytiepnhan: $('#txtDENKHAMLUC').val(),
                capnhat: '1',
                hosobenhanid: $('#hidHOSOBENHANID').val(),
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
            };
            _showDialog("NGT02K008_Thongtin_Lichkham", param, 'Thông tin lịch hẹn', 900, 300);
        });
        $('#toolbarIdbtnNghiDuongThai').on('click', function (e) {
            var _hosobenhanid = $("#hidHOSOBENHANID").val();
            if (_hosobenhanid != null && _hosobenhanid != -1) {
                var paramInput = {
                    hosobenhanid: _hosobenhanid,
                    khambenhid: $("#hidKHAMBENHID").val()
                };
                var url = "manager.jsp?func=../noitru/NTU02D157_NghiDuongThai";
                var popup = DlgUtil.buildPopupUrl("divDlgNDT", "divDlg", url, paramInput, "Phiếu nghỉ dưỡng thai", 1200, 600);
                popup.open("divDlgNDT");
            } else {
                DlgUtil.showMsg('Chưa chọn bệnh nhân');
            }
        });
		// L2PT-118724 start
		$("#toolbarIdbtnYCChiHo").on("click", function() {
			yeuCauChiHo($("#hidTIEPNHANID").val());
		});
		$("#toolbarIdbtnInYCChiHo").click(function() {
			var khamBenhId = $("#hidKHAMBENHID").val()
			if (!khamBenhId || khamBenhId == "" || khamBenhId == "-1" || khamBenhId == "0") {
				DlgUtil.showMsg("Vui lòng chọn bệnh nhân !");
				return;
			}
			var _sql_par = [{ name : 'khambenhid', type : 'String', value : khamBenhId }]
			CommonUtil.openReportGet('window', 'PHIEUYC_GHINHANTT_NHANTIEN_A4', 'pdf', _sql_par); 
		});
		// L2PT-118724 end
    }

    function _mobenhan_daingay() {
        // var _par=[];
        // var _hosobenhanid=null;
        // _par=RSUtil.buildParam("",[ $("#hidHOSOBENHANID").val()]);
        // var dataDaingay=jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.CHECK_DAINGAY", _par);
        // _rowsDaingay= JSON.parse(dataDaingay);
        // var _loaibadaingay = -1;
        // if(_rowsDaingay!=null && _rowsDaingay.length>0){
        //    _loaibadaingay=_rowsDaingay[0].LOAIBENHANID;
        //    _hosobenhanid = _rowsDaingay[0].HOSOBENHANID;
        // }

        var object = {};
        FormUtil.setFormToObject('divContentHC', '', object);
        var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
        var dataDaingay = {};
        if (dataDaingays && dataDaingays.length > 0) {
            dataDaingay = dataDaingays[0];
        }
        if (!dataDaingay) {
            DlgUtil.showMsg("Bệnh nhân chưa có bệnh án dài ngày!");
            return;
        }
        var _hosobenhanid = dataDaingay.HOSOBENHANID;
        var _hosobenhanid_hientai = object.HOSOBENHANID;
        var _loaibadaingay = dataDaingay.LOAIBENHANID;

        if (_loaibadaingay == 36) {
            var _sql_par1 = [];
            _sql_par1 = RSUtil.buildParam("", [36]);
            var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
            var _rows1 = JSON.parse(_data1);
            var _sreenName = _rows1[0].URL;
            var _tenloaibenhan = _rows1[0].TENLOAIBENHAN
            var _maloaibenhan = _rows1[0].MALOAIBENHAN;

            paramInput = {
                khambenhid: $("#hidKHAMBENHID").val(),
                hosobenhanid: _hosobenhanid,
                benhnhanid: $("#hidBENHNHANID").val(),
                loaibenhanid: 36,
                maloaibenhan: _maloaibenhan,
//						tuyennx_add_start_20171023 YC HISL2NT-485
                trang_thai: $("#hidTRANG_THAI").val(),
                hosobenhanid_hientai: _hosobenhanid_hientai
//						tuyennx_add_end_20171023 YC HISL2NT-485
            };
            dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../benhan/" + _sreenName, paramInput, "Cập nhật " + _tenloaibenhan, 1300, 610);
            DlgUtil.open("divDlgBenhAnDetail");//
        } else {
            DlgUtil.showMsg('Bệnh nhân chưa được mở bệnh án dài ngày để cập nhật!');
        }
    }

    function _loadGridData(phongid) {
        var from = $('#toolbarIdtxtFromDate').val().substr(6, 4) + $('#toolbarIdtxtFromDate').val().substr(3, 2) + $('#toolbarIdtxtFromDate').val().substr(0, 2)
            + $('#toolbarIdtxtFromDate').val().substr(11, 2) + $('#toolbarIdtxtFromDate').val().toString().substr(14, 2);

        var to = $('#toolbarIdtxtToDate').val().substr(6, 4) + $('#toolbarIdtxtToDate').val().substr(3, 2) + $('#toolbarIdtxtToDate').val().substr(0, 2)
            + $('#toolbarIdtxtToDate').val().substr(11, 2) + $('#toolbarIdtxtToDate').val().toString().substr(14, 2);
        if (from > to) {
            setErrValidate('toolbarIdtxtFromDate');
            DlgUtil.showMsg('Sai điều kiện tìm kiếm, từ ngày không thể lớn hơn đến ngày');
            return false;
        }

        var thoigian = getDaysDiff($('#toolbarIdtxtFromDate').val(), $('#toolbarIdtxtToDate').val());
        if (cfObj.NGT_KHAMBENH_TK_TG != 0 && thoigian > parseInt(cfObj.NGT_KHAMBENH_TK_TG) ) {
            DlgUtil.showMsg("Khoảng thời gian tìm kiếm vượt quá giới hạn để đảm bảo hiệu năng tìm kiếm là " + cfObj.NGT_KHAMBENH_TK_TG +
                " ngày. Đề nghị chỉ tìm kiếm trong khoảng ngày kiến nghị!");
            return false;
        }
        
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": $("#toolbarIdtxtFromDate").val()});
        sql_par.push({"name": "[1]", "value": $("#toolbarIdtxtToDate").val()});
        sql_par.push({"name": "[2]", "value": phongid});
        sql_par.push({"name": "[3]", "value": $("#cboTRANGTHAI").val()});
        sql_par.push({"name": "[4]", "value": $("#txtMABENHNHANTK").val()});
        sql_par.push({"name": "[5]", "value": _type == null || _type == 'null' ? "0" : _type});
        sql_par.push({"name": "[6]", "value": $("#cboXUTRIKHAMBENHID").val()});
        GridUtil.loadGridBySqlPage(_gridId, _SQL[0], sql_par);

        // load 1 so thong tin co ban sau do;
        var _obj = new Object();
        _obj.KHOAID = _opt.khoaid;
        _obj.PHONGID = _opt.phongid;
        _obj.TUNGAY = $('#toolbarIdtxtFromDate').val();
        _obj.DENNGAY = $('#toolbarIdtxtToDate').val();
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T001.LAYTTKB", JSON.stringify(_obj));
        if (data_ar != null && data_ar.length > 0) {
            // lay thong tin so phong;
            if (_chedogoikham == "2") {
                _sophongkham = data_ar[0].SOPHONG;
            } else if (!isNaN(data_ar[0].SOPHONG)) {
                _sophongkham = data_ar[0].SOPHONG;
            } else {
                _sophongkham = "0";
            }
            // lay thong tin so benh nhan da kham;
            $("#txtSLKHAMTRONGNGAY").val(data_ar[0].SLKHAMTRONGNGAY)
        }
    }
    function stringToDateTime(date) {
        var parts = date.split("/");
        var tails = parts[2].split(" ");
        var times = tails[1].split(":");
        var ret = new Date(tails[0], parts[1] - 1, parts[0], times[0], times[1], times[2]);
        return ret;
    }
    function _goilaibnchuyenkham() {
        if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_HIENTHIDS_HUYCHUYENKHAM') == "1") {
            $('.popup_remove').remove();
            $('#dlgWRAP_P').append($(htmlDlgmoBA));
            var dlgHuyChuyenKham = DlgUtil.buildPopup("dlgHUYCHUYENKHAM", "dlgHUYCHUYENKHAM", "Danh sách phòng khám chuyển đến", 500, 110, {"zIndex": 998});
            DlgUtil.open("dlgHUYCHUYENKHAM");
            var sql_par = [];
            sql_par.push({"name": "[0]", "value": $('#hidKHAMBENHID').val()}, {
                "name": "[1]",
                "value": $('#hidPHONGKHAMDANGKYID').val()
            });
            ComboUtil.getComboTag("cboPHONGKHAMCHUYEN", "NGT.DSPKHAMDACHUYEN", sql_par, "", {
                value: 0,
                text: '--Chọn--'
            }, "sql", "", "");
            var btnOK = $('#btn_HUYCHUYEN');
            var btnClose = $('#btn_HUYCHUYEN_CLOSE');
            btnOK.click(function () {
                if ($("#cboHOSOBENHANDAINGAYID").val() == 0) {
                    DlgUtil.showMsg('Chưa chọn bệnh án dài ngày để mở !');
                }
                var myVar = {
                    phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
                    phongkhamdangkyidnext: $('#cboPHONGKHAMCHUYEN').val(),
                    chidinhphong: 1
                };
                var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("GOILAI.CHUYENKHAM", JSON.stringify(myVar));
                if (check == 'kochuyenphong') {
                    DlgUtil.showMsg('Bệnh nhân chưa chuyển sang phòng khám khác từ phòng này');
                } else if (check == 'dakham') {
                    DlgUtil.showMsg('Phòng chuyển tới đã hoặc đang khám, không gọi lại được');
                } else if (check == 'dathutien') {
                    DlgUtil.showMsg('Phòng chuyển tới bệnh nhân đã đóng tiền, không hủy được');
                } else if (check == 'dathuphi') {
                    DlgUtil.showMsg('Bệnh nhân đã đóng tiền không hủy được, muốn hủy phải hủy hóa đơn trước');
                } else if (check == '1') {
                    DlgUtil.showMsg('Hủy chuyển phòng khám thành công');
                    dlgHuyChuyenKham.close();
                }
            });
            btnClose.click(function () {
                dlgHuyChuyenKham.close();
            });
        } else {
            var myVar = {
                phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
                tiepnhanid: $("#hidTIEPNHANID").val(),
                doituongbtid: $("#hidDOITUONGBENHNHANID").val(),
                chidinhphong: 0
            };
            var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("GOILAI.CHUYENKHAM", JSON.stringify(myVar));
            if (check == 'kochuyenphong') {
                DlgUtil.showMsg('Bệnh nhân chưa chuyển sang phòng khám khác từ phòng này');
            } else if (check == 'dakham') {
                DlgUtil.showMsg('Phòng chuyển tới đã hoặc đang khám, không gọi lại được');
            } else if (check == 'dathutien') {
                DlgUtil.showMsg('Phòng chuyển tới bệnh nhân đã đóng tiền, không hủy được');
            } else if (check == 'dathuphi') {
                DlgUtil.showMsg('Bệnh nhân đã đóng tiền không hủy được, muốn hủy phải hủy hóa đơn trước');
//		}else if (check == 'cothukhacdatt'){
//			DlgUtil.showMsg('Tồn tại thu khác đã thanh toán, đề nghị hủy thu khác trước khi hủy chuyển khám. ');
            } else if (check == '1') {
                DlgUtil.showMsg('Hủy chuyển phòng khám thành công');
            }
        }
    }

    function _tangqua_sinhnhat() {
        var myVar = {hosobenhanid: $("#hidHOSOBENHANID").val()};
        var _tangqua = jsonrpc.AjaxJson.ajaxCALL_SP_S("TANGQUA_SN", JSON.stringify(myVar));
        DlgUtil.showMsg(_tangqua);
        $("#toolbarIdbtnSearchDate").trigger("click");
    }

    function _get_filegoiso() {
        var _goiso = new Object();
        var dsbn = [];
        _goiso['pk_id'] = _sophongkham;
        _goiso['pk_name'] = _opt._subdept_name;
        var par = [];
        par.push({
            "name": "[0]",
            "value": _opt.phongid
        }, {
            "name": "[1]",
            "value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')
        }, {
            "name": "[2]",
            "value": parseInt($("#hidSOTHUTU").val())
        });
        var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K001.GETGOISO", par);
        var row = JSON.parse(data);
        if (row != null && row.length > 0) {
            for (var i = 0; i < row.length; i++) {
                var _bn = new Object();
                _bn['stt_bn_dv'] = "";
                _bn['stt_bn_pb'] = row[i].STT;
                _bn['ma_bn'] = row[i].MABENHNHAN;
                _bn['ten_bn'] = row[i].TENBENHNHAN;
                _bn['uu_tien'] = parseInt(row[i].UUTIEN);
                _bn['sothebhyt'] = row[i].MABHYT;
                _bn['diachi'] = row[i].DIACHI;
                _bn['ngaysinh'] = row[i].NGAYSINH;
                dsbn.push(_bn)
            }
            _goiso['dsbn'] = dsbn;
        }
        const link = document.createElement("a");
        const content = JSON.stringify(_goiso);
        const file = new Blob([content], {type: 'text/plain'});
        link.href = URL.createObjectURL(file);
        link.download = "GOISO.lgs";
        link.click();
        URL.revokeObjectURL(link.href);
    }

    //======= SU DUNG TRONG TRUONG HOP CHO PHEP
    function loadTabStatus(_trangthaikhambenh, obj) {
        var _xutrikhambenh = obj._xutrikhambenh;
        var INBANGKE_DONGBENHAN = cfObj.VP_IN_PHOI_KHI_DONG_BENHAN;
        // HungNd - L2PT-64729
        var SHOWTHUOCKTK = cfObj.NGT_TIEPNHAN_SHOWTHUOCKTK;
        if (_trangthaikhambenh == '9') {
            _flgModeView = '1';
            if ($("#hidTRANGTHAITIEPNHAN").val() != '0') {
                $("#toolbarIdgroup_0_4").removeClass("disabled");
                $("#toolbarIdgroup_0_7").removeClass("disabled");
                $("#toolbarIdgroup_0_5").removeClass("disabled");
            } else {
                $("#toolbarIdgroup_0_4").addClass("disabled");
                $("#toolbarIdgroup_0_7").addClass("disabled");
                $("#toolbarIdgroup_0_5").addClass("disabled");
            }
            $("#goilaibnchuyenphong").addClass("disabled");
            $("#xoabnchonhapvien").removeClass("disabled");
            _setButton(true);
            $("#toolbarIdbtndrug").attr("disabled", true);
            $("#toolbarIdbtnStart").attr("disabled", true);

            if (_enable_mobenhan != '1') {
                $("#yeucaumolaibenhan").addClass('disabled');
                $("#molaibenhan").remove();
            } else {
                $("#yeucaumolaibenhan").removeClass('disabled');
            }
            //L2PT-45149
            if (NGT_MENUKHAC_KTK == '1') {
                $("#toolbarIdbtnKHAC").attr("disabled", false);
                $("#toolbarIdbtnKHAC_0").removeClass("disabled");
                hiddenControl(NGT_MENUKHAC_AN_KTK);
            }
        } else {
            //L2PT-42611
            if (NGT_MENUKHAC_KTK == '1') {
                showControl(NGT_MENUKHAC_AN_KTK);
            }
            _flgModeView = '0';
            $("#toolbarIdgroup_0_4").addClass("disabled");
            $("#toolbarIdgroup_0_7").addClass("disabled");
            $("#toolbarIdgroup_0_5").addClass("disabled");
            $("#yeucaumolaibenhan").addClass('disabled');
            $("#xoabnchonhapvien").addClass("disabled");
            if (_trangthaikhambenh == "4") {
                $("#goilaibnchuyenphong").removeClass("disabled");
                _setButton(false);
                /*if(_xutrikhambenh == "6" || _xutrikhambenh == "8" || _xutrikhambenh == "0"){
					$("#toolbarIdbtndrug").attr("disabled", true);
				}else{
					$("#toolbarIdbtndrug").attr("disabled", false);
				}*/
                if (INBANGKE_DONGBENHAN == '1') {
                    $("#toolbarIdgroup_0_4").removeClass("disabled");
                    $("#toolbarIdgroup_0_7").removeClass("disabled");
                    $("#toolbarIdgroup_0_5").removeClass("disabled");
                }
                _disableMenuXuTri(_xutrikhambenh);

                $("#toolbarIdbtnStart").attr("disabled", true);
                $("#toolbarIdbtnKHAC_3").removeClass("disabled");
                $("#toolbarIdbtnKHAC_8").removeClass("disabled");
                $("#toolbarIdhandling_1").removeClass("disabled");
                $("#toolbarIdbtnKHAC_PDT").removeClass("disabled");
                if (cfObj.NGT_ENABLE_HENKHAM_KB1 == '1') {
                    $("#toolbarIdbtnKHAC_6").removeClass("disabled");
                }
            } else if (_trangthaikhambenh == 1) {
                $("#toolbarIdbtnStart").attr("disabled", false);
                $("#toolbarIdbtndrug").attr("disabled", true);
                $("#goilaibnchuyenphong").addClass("disabled");
                _setButton(true);
                $("#toolbarIdbtnKHAC").attr("disabled", false);
                $("#toolbarIdbtnKHAC_3").addClass("disabled");
                $("#toolbarIdbtnKHAC_8").addClass("disabled");
                $("#toolbarIdhandling_1").addClass("disabled");
                $("#toolbarIdbtnKHAC_PDT").addClass("disabled");
                $("#xoabnchonhapvien").addClass("disabled");
                _disableMenuXuTri(_xutrikhambenh);
            }
        }

        // BN KHAM CHUYEN KHOA, CHUA KET THUC KHAM;
        if ($("#hidCHUYENKHAMNGT").val() == "1" && _trangthaikhambenh != "9") {
            if (opt.hospital_id == "965") {
                $("#toolbarIdbtndrug").attr("disabled", true);
                $("#toolbarIdbtnPhieuKham").attr("disabled", true);
            } else if (opt.hospital_id == "987") { //DKLS.ADMIN
                $("#toolbarIdbtnPhieuKham").attr("disabled", true);
            } else {
                $("#toolbarIdbtndrug").attr("disabled", false);
                $("#toolbarIddrug_khothuoc").addClass("disabled");
                $("#toolbarIddrug_phieuđinhuong").addClass("disabled");
                $("#toolbarIddrug_1dy").addClass("disabled");
                $("#toolbarIddrug_3").addClass("disabled");
                $("#toolbarIddrug_tutruc").addClass("disabled");
            }
        }
        /*else{
			$("#toolbarIdbtndrug").attr("disabled", false);
			$("#toolbarIdbtnPhieuKham").attr("disabled", false);
			$("#toolbarIddrug_khothuoc").removeClass("disabled");
			$("#toolbarIddrug_phieuđinhuong").removeClass("disabled");
			$("#toolbarIddrug_1dy").removeClass("disabled");
			$("#toolbarIddrug_3").removeClass("disabled");
			$("#toolbarIddrug_tutruc").removeClass("disabled");
		}*/

        /*if (_khamchinhphu == "1"){
			if(_khamchinhphudetail == "1"){
				$("#toolbarIdhandling_1").removeClass("disabled");
				$("#toolbarIdbtnPhieuKham").removeClass("disabled");
				$("#toolbarIdbtnKTKH").removeClass("disabled");
				$("#toolbarIdhandling_2").removeClass("disabled");
			}else{
				$("#toolbarIdhandling_1").addClass("disabled");
				$("#toolbarIdbtnPhieuKham").addClass("disabled");
				$("#toolbarIdbtnKTKH").addClass("disabled");
				$("#toolbarIdhandling_2").addClass("disabled");
			}
		}*/

        // HungNd - L2PT-64729
        // Hiển thị đơn thuốc sau khi kết thúc khám
        if (_trangthaikhambenh == "9" && SHOWTHUOCKTK == "1") {
            $("#toolbarIdbtndrug").attr("disabled", false);
            $("#toolbarIddrug_khothuoc").addClass("disabled");
            $("#toolbarIddrug_tutruc").addClass("disabled");
            $("#toolbarIddrug_phieuđinhuong").addClass("disabled");
            $("#toolbarIddrug_1dy").addClass("disabled");
            $("#toolbarIddrug_3").addClass("disabled");
            $("#toolbarIddrug_8").addClass("disabled");
            $("#toolbarIddrug_hpvt").addClass("disabled");
            $("#toolbarIddrug_donkinh").addClass("disabled");

        }
    }

    // ======= SU DUNG TRONG TRUONG HOP CHO PHEP
    function _selectedRow(item_id) {
        if (item_id != null && item_id.length > 0) {
            FormUtil.clearForm('divContentHC');

            var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
            var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);

            $("#hidKHAMBENHID").val('-1');

            var _khambenhid = rowData.KHAMBENHID;
            var _hosobenhanid = rowData.HOSOBENHANID;
            var _phongkhamdangkyid = rowData.PHONGKHAMDANGKYID;
            var _benhnhanid = rowData.BENHNHANID;
            var _doituongbenhnhanid = rowData.DOITUONGBENHNHANID;
            var _tiepnhanid = rowData.TIEPNHANID;
            var _loaitiepnhanid = rowData.LOAITIEPNHANID;
            var _trangthaikhambenh = rowData.TRANGTHAIKHAMBENH;
            var _xutrikhambenh = rowData.XUTRIKHAMBENHID;
            var _sothutu = rowData.SOTHUTU;

            $("#txtSTT5").val(_sothutu);
            $("#txtHOTEN5").val(rowData.TENBENHNHAN);
            $('#txtMSGSTT').val(rowData.TENBENHNHAN + " - lần " + rowData.LANGOIKHAM);

            $("#hidHOTENBNGOI").val(rowData.TENBENHNHAN);
            $("#hidKHAMBENHID").val(_khambenhid);
            $("#hidHOSOBENHANID").val(_hosobenhanid);
            //tuyennx_add_start_20171020 yc HISL2NT-485
            $("#hidTRANG_THAI").val(rowData.TRANG_THAI);
            //tuyennx_add_end_20171020 yc HISL2NT-485
            //tuyennx_add_start_20171020 yc L2DKBD-692
            $("#hidLOAIBENHANID").val(rowData.LOAIBENHANID);
            $("#hidTEN_FILE").val(rowData.TEN_FILE);
            //tuyennx_add_end_20171020 yc L2DKBD-692
            $("#hidPHONGKHAMDANGKYID").val(_phongkhamdangkyid);
            $("#hidBENHNHANID").val(_benhnhanid);
            $("#hidDOITUONGBENHNHANID").val(_doituongbenhnhanid);
            $("#hidTIEPNHANID").val(_tiepnhanid);
            $("#hidLOAITIEPNHANID").val(_loaitiepnhanid);
            $('#hidXUTRIKHAMBENHID').val(_xutrikhambenh);
            $("#hidINDEX").val(item_id);
            $("#hidSOTHUTU").val(_sothutu);
            $("#hidHisId").val(opt.hospital_id);
            $("#hidUserID").val(opt.user_id);
            $("#hidMADICHVU").val(rowData.MADICHVU);
            $('#lblSTT').html(_sothutu);
            $("#hidPHONGID").val(rowData.PHONGID);
            $("#hidKHOAID").val(rowData.KHOAID);
            //tuyennx_add_start_20171020 congboyte
            $("#hidMAHOSOBENHAN").val(rowData.MAHOSOBENHAN);
            //tuyennx_add_end_20171020
            $("#hidTRANGTHAITIEPNHAN").val(rowData.TRANGTHAITIEPNHAN);
            $("#hidCHUYENKHAMNGT").val(rowData.CHUYENKHAMNGT);
            $("#hidSUB_DTBNID").val(rowData.SUB_DTBNID);
            $("#hidKHAMCHINHPHU").val(rowData.KHAMCHINHPHU);
            $("#hidMAUBENHPHAMID").val(rowData.MAUBENHPHAMID);
            $("#hidDICHVUKHAMBENHID").val(rowData.DICHVUKHAMBENHID);
            $("#hidLOAIDOITUONG").val(rowData.LOAIDOITUONG);
            _khamchinhphudetail = rowData.KHAMCHINHPHU;
            _hinhthucvaovienid = rowData.HINHTHUCVAOVIENID;

            var objj = new Object();
            objj._xutrikhambenh = _xutrikhambenh;
            loadTabStatus(_trangthaikhambenh, objj);

            // load du lieu cho tab Hanh Chinh
            _loadTabHanhChinh(_khambenhid);

            if (rowData.THOIGIANBD) {
                var _tgtn = $("#txtDENKHAMLUC").val();
                $("#txtDENKHAMLUC").val(_tgtn + "-Thời gian BĐ khám: " + rowData.THOIGIANBD);
            }

            var vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.VIENPHI", rowData.TIEPNHANID);
            if (vp_ar != null && vp_ar.length > 0) {
                var data = vp_ar[0];
                $('#txtTIENTAMUNG').val(formatNumber(data.TAMUNG) + " đ");
                $('#txtTONGCHIPHI').val(formatNumber(data.TONGTIENDV) + " đ");
                $('#txtDANOP').val(formatNumber(data.DANOP) + " đ");
                //L2PT-26177
                if (NGT_CANHBAO_TIEN == '1') {
                    if (parseInt(data.TONGTIENDV) > parseInt(data.TAMUNG) + parseInt(data.DANOP)) {
                        $('#txtTIENTAMUNG').css({'color': 'red'});
                        $('#txtTONGCHIPHI').css({'color': 'red'});
                        $('#txtDANOP').css({'color': 'red'});
                        $('#txtBSXUTRI').css({'color': 'red'});
                        $('#txtMABENHNHAN').css({'color': 'red'});
                    }
//			 		var check_cp = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.GET.CHIPHI", $("#hidTIEPNHANID").val() + '$' + '9');
//					if (check_cp != '0') {
//
//					}
                }
            }

            //tuyennx_add_start_20171115
            if (_trangthaikhambenh == "4" && cfObj.NGT_KETHUOC_KHI_NHAPVIEN == "1") {
                if (_xutrikhambenh == "6" || _xutrikhambenh == "2") {
                    $("#toolbarIdbtndrug").attr("disabled", true);
                } else {
                    $("#toolbarIdbtndrug").attr("disabled", false);
                }
            }
            //bn da kham ko cho doi phong kham
            if (_khamchinhphu == "0") {

                // sondn L2PT-14160
                if (_trangthaikhambenh == "9") {
                    $("#toolbarIdhandling_2").addClass("disabled");
                } else if (_trangthaikhambenh == "1" || _trangthaikhambenh == "4") {
                    if (_doipkhienthi == "0") {
                        if (_trangthaikhambenh == "1") {
                            $("#toolbarIdhandling_2").removeClass("disabled");
                        } else {
                            $("#toolbarIdhandling_2").addClass("disabled");
                        }
                    } else {
                        // cau hinh bat: luon hien thi;
                        $("#toolbarIdhandling_2").removeClass("disabled");
                    }
                }
                // end sondn L2PT-14160

                //tuyennx_add_start_L2PT-26480
                if (_trangthaikhambenh == "4" && cfObj.NGT_DOIPHONGKHAM_DV == '1') {
                    var _sql_par = [];
                    _sql_par.push({
                        "name": "[0]",
                        value: $("#hidKHAMBENHID").val()
                    });
                    var _check = jsonrpc.AjaxJson.getOneValue("NGT.CHECK.DICHVU", _sql_par);
                    if (_check == 0)
                        $("#toolbarIdhandling_2").removeClass("disabled");
                    else
                        $("#toolbarIdhandling_2").addClass("disabled");
                }
                //tuyennx_add_end_L2PT-26480
            }
            //tuyennx_add_end_20171115
            /*
			/*vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.05",rowData.TIEPNHANID);
			if(vp_ar != null && vp_ar.length > 0){
				var data = vp_ar[0];
				$('#txtTONGCHIPHI').val(formatNumber(data.TONGTIENDV) + " đ");
			}*/

            switch (Number(_trangthaikhambenh)) {
                case 9:
                    $("#toolbarIdbtnThuTienKhac").attr("disabled", true);
                    if (cfObj.NGT_BANGT_DISABLE == "1") {
                        $('#toolbarIdbtnBANGT').attr("disabled", true);
                    }
                    break;
                default:
                    if (cfObj.NGT_BANGT_DISABLE == "1") {
                        $('#toolbarIdbtnBANGT').attr("disabled", false);
                    }
                    $("#toolbarIdbtnThuTienKhac").attr("disabled", false);
                    break;
            }

        } else {
            FormUtil.clearForm("divContentHC", "");
            FormUtil.clearForm("tabBenhAn", "");
            $("#hidKHAMBENHID").val(-1);
            $("#toolbarIdbtnStart").attr("disabled", true);
            $('#lblTAMUNG').html('');
            $('#lblTONGCHIPHI').html('');
            $('#lblBAOHIEMTHANHTOAN').html('');
            $('#hidXUTRIKHAMBENHID').val('');
        }
    }

    //calback cho MA HINH SUA PHONG THUC HIEN
    EventUtil.setEvent("assignSevice_SavePhongThucHien", function (e) {
        DlgUtil.showMsg(e.msg);
        //reload danh sach xet nghiem
        if (e.loaiPhieu == LNMBP_XetNghiem) {
            $('#tabXetNghiem').ntu02d024_ttxn({
                _gridXnId: "grdXetNghiem",
                _gridXnDetailId: "grdXetNghiemChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_XetNghiem,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        } else if (e.loaiPhieu == LNMBP_CDHA) {
            $('#tabCDHA').ntu02d025_cdha({
                _gridCDHA: "grdCDHA",
                _gridCDHADetail: "grdCDHAChiTiet",
                _khambenhid: $("#hidKHAMBENHID").val(),
                _benhnhanid: $("#hidBENHNHANID").val(),
                _lnmbp: LNMBP_CDHA,
                _modeView: _flgModeView, // =1 chi view; !=1 la update
                _hosobenhanid: ""
            });
        }

        DlgUtil.close("divDlgEditOrgDone");
    });

    function _loadTabHanhChinh(khambenhid) {
        var sql_par = [khambenhid, $("#hidPHONGID").val()];
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(_SQL[1], sql_par.join('$'));
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            FormUtil.clearForm("subdivContentHC", "");
            FormUtil.setObjectToForm("divContentHC", "", row);
            var ux2023 = cfObj.HIS_SHOW_TAB_UX2023;
            if (NGT_THEMTRUONG_DIACHI == 1) {
                $("#lblDiachibhyt").text("Địa chỉ BHYT");
            } else {
                $("#lblDiachibhyt").text("Địa chỉ");
            }
            if (row.ANHBENHNHAN != null) {
                var imgTag = document.getElementById('imgBN');
                imgTag.src = row.ANHBENHNHAN;
            } else {
                $('#imgBN').removeAttr('src');
            }

            if (row.BTNTHUOC == "1" && row.TRANGTHAI_STT == "4" && $("#hidCHUYENKHAMNGT").val() == "0") {
                $("#toolbarIdbtndrug").attr("disabled", false);
            }
            if (cfObj.HIS_SHOW_TTTUYENCHITIET == 1) {
                $("#txtTUYEN").attr('style', 'width: 100% !important');
                $("#txtTUYEN").attr('style', 'color:red');

                $('#txtTUYEN').val(row.TUYEN + (row.SUBTUYEN != "" ? (' (' + row.SUBTUYEN + ')') : ''));
            }
            /*if (row.TRANGTHAI_STT == "9"){
				$("#toolbarIdhandling_1").attr("disabled", true);
			}else{
				$("#toolbarIdhandling_1").attr("disabled", false);
			}*/

            if (data_ar[0].SLXN != 0) {
                $('#tabXetNghiemTab').html("Xét nghiệm <b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLXN + ")</b>");
                $('#tabXetNghiemTab').show();
            } else if (data_ar[0].SLXN == 0 && ux2023 == 1) {
                $('#tabXetNghiemTab').text("Xét nghiệm");
                $('#tabXetNghiemTab').hide();
            } else if (data_ar[0].SLXN == 0 && ux2023 != 1) {
                $('#tabXetNghiemTab').text("Xét nghiệm");
            }

            if (data_ar[0].SLCDHA != 0) {
                $('#tabCDHATab').html("CĐHA<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLCDHA + ")</b>");
                $('#tabCDHATab').show();
            } else if (data_ar[0].SLCDHA == 0 && ux2023 == 1) {
                $('#tabCDHATab').text("CĐHA");
                $('#tabCDHATab').hide();
            } else if (data_ar[0].SLCDHA == 0 && ux2023 != 1) {
                $('#tabCDHATab').text("CĐHA");
            }

            if (data_ar[0].SLCHUYENKHOA != 0) {
                $('#tabChuyenKhoaTab').html("Phẫu thuật thủ thuật<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLCHUYENKHOA + ")</b>");
                $('#tabChuyenKhoaTab').show();
            } else if (data_ar[0].SLCHUYENKHOA == 0 && ux2023 == 1) {
                $('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
                $('#tabChuyenKhoaTab').hide();
            } else if (data_ar[0].SLCHUYENKHOA == 0 && ux2023 != 1) {
                $('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
            }

            if (data_ar[0].SLTHUOC != 0) {
                $('#tabThuocTab').html("Thuốc<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLTHUOC + ")</b>");
                $('#tabThuocTab').show();
            } else if (data_ar[0].SLTHUOC == 0 && ux2023 == 1) {
                $('#tabThuocTab').text("Thuốc");
                $('#tabThuocTab').hide();
            } else if (data_ar[0].SLTHUOC == 0 && ux2023 != 1) {
                $('#tabThuocTab').text("Thuốc");
            }
            if (data_ar[0].SLTRUYENDICH != 0) {
                $('#tabPhieuTruyenDichTab').html("Truyền dịch<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLTRUYENDICH + ")</b>");
                $('#tabPhieuTruyenDichTab').show();
            } else if (data_ar[0].SLTRUYENDICH == 0 && ux2023 == 1) {
                $('#tabPhieuTruyenDichTab').text("Truyền dịch");
                $('#tabPhieuTruyenDichTab').hide();
            } else if (data_ar[0].SLTRUYENDICH == 0 && ux2023 != 1) {
                $('#tabPhieuTruyenDichTab').text("Truyền dịch");
            }
            //L2PT-63268
            if (data_ar[0].SLTRUYENMAU && data_ar[0].SLTRUYENMAU != 0) {
                $('#tabPhieuTruyenMau').html("Truyền máu<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLTRUYENMAU + ")</b>");
                $('#tabPhieuTruyenMau').show();
            } else {
                $('#tabPhieuTruyenMau').text("Truyền máu");
                //$('#tabPhieuTruyenMau').hide();
            }


            if (data_ar[0].SLVATTU != 0) {
                $('#tabVatTuTab').html("Vật tư<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLVATTU + ")</b>");
                $('#tabVatTuTab').show();
            } else if (data_ar[0].SLVATTU == 0 && ux2023 == 1) {
                $('#tabVatTuTab').text("Vật tư");
                $('#tabVatTuTab').hide();
            } else if (data_ar[0].SLVATTU == 0 && ux2023 != 1) {
                $('#tabVatTuTab').text("Vật tư");
            }

            if (data_ar[0].SLVANCHUYEN != 0) {
                $('#tabPhieuVanChuyenTab').html("Phiếu vận chuyển<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLVANCHUYEN + ")</b>");
                $('#tabPhieuVanChuyenTab').show();
            } else if (data_ar[0].SLVANCHUYEN == 0 && ux2023 == 1) {
                $('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
                $('#tabPhieuVanChuyenTab').hide();
            } else if (data_ar[0].SLVANCHUYEN == 0 && ux2023 != 1) {
                $('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
            }


            if (data_ar[0].SLBADTNGT != 0) {
                $('#tabBADTNGTTab').html("BA ĐT Ngoại trú<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLBADTNGT + ")</b>");
                $('#tabBADTNGTTab').show();
            } else if (data_ar[0].SLBADTNGT == 0 && ux2023 == 1) {
                $('#tabBADTNGTTab').text("BA ĐT Ngoại trú");
                $('#tabBADTNGTTab').hide();
            } else if (data_ar[0].SLBADTNGT == 0 && ux2023 != 1) {
                $('#tabBADTNGTTab').text("BA ĐT Ngoại trú");
            }

            if (data_ar[0].SLDIEUTRI != 0) {
                $('#tabDieuTriTab').html("Điều trị<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLDIEUTRI + ")</b>");
                $('#tabDieuTriTab').show();
            } else if (data_ar[0].SLDIEUTRI == 0 && ux2023 == 1) {
                $('#tabDieuTriTab').text("Điều trị");
                $('#tabDieuTriTab').hide();
            } else if (data_ar[0].SLDIEUTRI == 0 && ux2023 != 1) {
                $('#tabDieuTriTab').text("Điều trị");
            }
            if (data_ar[0].SLCHAMSOC != 0) {
                $('#tabChamSocTab').html("Chăm sóc <b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLCHAMSOC + ")</b>");
                $('#tabChamSocTab').show();
            } else if (data_ar[0].SLCHAMSOC == 0 && ux2023 == 1) {
                $('#tabChamSocTab').text("Chăm sóc");
                $('#tabChamSocTab').hide();
            } else if (data_ar[0].SLCHAMSOC == 0 && ux2023 != 1) {
                $('#tabChamSocTab').text("Chăm sóc");
            }
            // sondn L2PT-1711
            if (data_ar[0].SLHOICHUAN != 0) {
                $('#tabHoiChuanTab').html("Hội chẩn<b style = 'color:red;font-weight: bolder;'> (" + data_ar[0].SLHOICHUAN + ")</b>");
                $('#tabHoiChuanTab').show();
            } else if (data_ar[0].SLHOICHUAN == 0 && ux2023 == 1) {
                $('#tabHoiChuanTab').text("Hội chẩn");
                $('#tabHoiChuanTab').hide();
            } else if (data_ar[0].SLHOICHUAN == 0 && ux2023 != 1) {
                $('#tabHoiChuanTab').text("Hội chẩn");
            }
            // end sondn L2PT-1711
            // cap nhat kham online
            if (_sudungKhamOnline == "1") {
                $("#dvONLINEKHAM").show();
            } else {
                $("#dvONLINEKHAM").hide();
            }
            if (cfObj.NGT_HIENTHI_CBUT_BVSK == "1") {
                if (data_ar[0].CANBOUUTIEN == 1) {
                    $("#txtDOITUONG").val(data_ar[0].DOITUONG + ' (CB.ƯT) ');
                }
                if (data_ar[0].BVSK == 1) {
                    $("#txtDOITUONG").val(data_ar[0].DOITUONG + ' (BVSK) ');
                }
                if (data_ar[0].BVSK == 1 && data_ar[0].CANBOUUTIEN == 1) {
                    $("#txtDOITUONG").val(data_ar[0].DOITUONG + ' (CB.ƯT + BVSK) ');
                }
            }
            if (cfObj.NGT_HIENTHITUOI_KB1 == "1") {
                $("#txtNGAYSINH").val(data_ar[0].NGAYSINH + '( ' + data_ar[0].TUOI + ' )');
            }
            NGAYSINH = data_ar[0].NGAYSINH;
            //load thong tin BN
            if (showTTBNWidget) {
                $('#divMsg').show();
                $('#lblMSG_MABENHAN').html(row.MABENHAN);
                $('#lblMSG_TENBENHNHAN').html(row.TENBENHNHAN);
                $('#lblMSG_NGAYSINH').html(row.NGAYSINH);
                $('#lblMSG_GIOITINH').html(row.GIOITINH);
                $('#lblMSG_DIACHI').html(row.DIACHI);
                if (cfObj.HIS_SHOW_TTBENHAN_BS == '1') {
                    var txtbs = ' | ' + data_ar[0].NGAYTIEPNHAN;
                    if (data_ar[0].CDC) {
                        txtbs = txtbs + ' | ' + data_ar[0].CDC;
                    }
                    if (data_ar[0].SOTHEBHYT) {
                        txtbs = txtbs + ' | ' + data_ar[0].SOTHEBHYT;
                    }
                    $('#lblMSG_BOSUNG').html(txtbs);
                }
            }
            if (cfObj.NGT_LOADBTNKSK == "1") {
                disableBtnKSK(row.LOAIKSKID);
            }
        } else {
            FormUtil.clearForm("divContentHC", "");
        }
    }
    function disableBtnKSK(loaikskid) {
        var arr = ["toolbarIdbtnKSKCANBO","toolbarIdbtnKSKLAIXE","toolbarIdbtnKSKDUOI18TUOI","toolbarIdbtnKSKTREN18TUOI","toolbarId_MENU_KSK_DINHKY","toolbarIdbtnKSKNGHENGHIEP"]
        for (var i = 0; i < arr.length; i++) {
            if(loaikskid == (i+1) ){
                $('#'+ arr[i]).removeClass("disabled")
            }else {
                $('#'+ arr[i]).addClass("disabled")
            }
        }
    }
    function _formatRow(_rId, _fIndex) {
        var _icon = '';
        if (_opt.imgPath[_fIndex] != '')
            _icon = '<center><img src="../common/img/' + _opt.imgPath[_fIndex] + '" width="15px"></center>';
        $("#" + _grdDieuTri).setRowData(_rId, {icon: _icon});
        $("#" + _grdDieuTri).find("tr[id='" + _rId + "']").find('td').each(function (index, element) {
            $(element).css({'color': _opt.foreColor[_fIndex]});
        });
    }

    function _setButton(value) {
        $("#toolbarIdbtnExam").attr("disabled", value);
        $("#toolbarIdbtnTreat").attr("disabled", value);
        $("#toolbarIdbtnService").attr("disabled", value);
        //$("#toolbarIdbtndrug").attr("disabled", value);
        $("#toolbarIdbtnhandling").attr("disabled", value);
        $("#toolbarIdbtnPhieuKham").attr("disabled", value);
        $("#toolbarIdbtnHoaHong").attr("disabled", value);
        //$("#toolbarIdbtnStart").attr("disabled", value);
        $("#toolbarIdbtnKTKH").attr("disabled", value);
        //$("#toolbarIdbtnCall").attr("disabled", value);
        $("#toolbarIdbtnKHAC").attr("disabled", value);
        $("#toolbarIdbtndrug").attr("disabled", value);
        $("#toolbarIdhandling_1").attr("disabled", value);
    }

    function _disableMenuXuTri(xutriid) {
        var arrID = ['toolbarIdbtnKHAC_0', 'toolbarIdbtnKHAC_1', 'toolbarIdbtnKHAC_2', 'toolbarIdbtnKHAC_4', 'toolbarIdbtnKHAC_5', 'toolbarIdbtnKHAC_6', 'toolbarIdbtnKHAC_7'];

        for (var i = 0; i < arrID.length; i++) {
            $("#" + arrID[i]).addClass("disabled");
        }

        if (xutriid == "8") {
            $('#toolbarIdbtnKHAC_0').removeClass("disabled");
            $('#toolbarIdbtnKHAC_1').removeClass("disabled");
            $('#toolbarIdbtnKHAC_2').removeClass("disabled");
        } else if (xutriid == "1" || xutriid == "3" || xutriid == "9") {
            $('#toolbarIdbtnKHAC_4').removeClass("disabled");
        } else if (xutriid == "4") {// hẹn kham tiep
            $('#toolbarIdbtnKHAC_7').removeClass("disabled");
        } else if (xutriid == "7") {
            $('#toolbarIdbtnKHAC_5').removeClass("disabled");
        } else if (xutriid == "5") { // hẹn kham moi
            $('#toolbarIdbtnKHAC_6').removeClass("disabled");
        }
    }

    // hiển thị dialog xử trí khám bệnh
    function _showDialog(url, param, title, w, h) {
        _objData = {};
        dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/" + url, param, title, w, h);
        DlgUtil.open("dlgXuTri");
    }

    function _changexutri(value) {
        var param = {
            mabenhnhan: $('#txtMABENHNHAN').val(),
            tenbenhnhan: $('#txtTENBENHNHAN').val(),
            nghenghiep: $("#hidTENNGHENGHIEP").val(),
            diachi: $('#txtDIACHI').val(),
            namsinh: $('#hidNAMSINH').val(),
            khambenhid: $('#hidKHAMBENHID').val(),
            benhnhanid: $('#hidBENHNHANID').val(),
            chandoan: $('#txtCDC').val(),
            ngaytiepnhan: $('#txtDENKHAMLUC').val(),
            capnhat: '1',
            hosobenhanid: $('#hidHOSOBENHANID').val()
        };

        if (value === "1" || value === "3" || value === "9") { // Cấp toa cho về, hẹn, khác
            _showDialog("NGT02K007_Thongtin_Ravien", param, 'Thông tin ra viện', 1000, 380);
        } else if (value === "4" || value === "5") { // hẹn khám tiếp, khám mới
            param['xutri'] = value;
            _showDialog("NGT02K008_Thongtin_Lichkham", param, 'Thông tin lịch hẹn', 900, 300);
        } else if (value === "7") { // chuyển viện
            _showDialog("NGT02K009_Chuyenvien", param, 'Thông tin chuyển viện', 1200, 440);
        } else if (value === "8") { // tử vong
            _showDialog("NGT02K010_Tuvong", param, 'Thông tin tử vong', 1000, 400);
        }
    }

    function _capnhatBADTNGT() {
        var hsbaid = $("#hidHOSOBENHANID").val();
        var benhnhanid = $("#hidBENHNHANID").val();

        if (hsbaid == "" || hsbaid == "-1") {
            DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để cập nhật. ");
            return;
        }

        var sql_par = [benhnhanid, hsbaid, opt.phongid];
        var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02T001.UPDBADTNGT", sql_par.join('$'));

        if (fl == "1") {
            DlgUtil.showMsg("Cập nhật thành công bệnh án Điều trị Ngoại trú cho bệnh nhân " + $("#txtTENBENHNHAN").val());
            $('#toolbarIdbtnSearchDate').click();
        } else if (fl == "-20") {
            DlgUtil.showMsg("Bệnh án này đã đánh dấu điều trị ngoại trú. ");
        } else if (fl == "-21") {
            DlgUtil.showMsg("Bệnh án này không thuộc đợt điều trị cuối cùng, không thể mở lại.");
        } else if (fl == "-22") {
            DlgUtil.showMsg("Đợt điều trị ngoại trú lần này đã đóng. ");
        } else if (fl == "-30") {
            DlgUtil.showMsg("Không thêm mới được dữ liệu vào bảng map. ");
        } else {
            DlgUtil.showMsg("Lỗi cập nhật thông tin. ", undefined, undefined, "error");
        }
    }

    function _gocapnhatBADTNGT(mode) {
        var hsbaid = $("#hidHOSOBENHANID").val();
        var benhnhanid = $("#hidBENHNHANID").val();

        if (hsbaid == "" || hsbaid == "-1") {
            DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để cập nhật. ");
            return;
        }

        var sql_par = [mode, benhnhanid, hsbaid, opt.phongid];
        var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02T001.REVBADTNGT", sql_par.join('$'));

        if (fl == "1") {
            DlgUtil.showMsg("Gỡ cập nhật thành công bệnh án Điều trị Ngoại trú cho bệnh nhân " + $("#txtTENBENHNHAN").val());
            $('#toolbarIdbtnSearchDate').click();
        } else if (fl == "-20") {
            DlgUtil.showMsg("Bệnh nhân chưa làm bệnh án điều trị ngoại trú ở phòng này. ");
        } else {
            DlgUtil.showMsg("Lỗi cập nhật thông tin. ", undefined, undefined, "error");
        }
    }

    //START HISL2TK-1085
    function inBangKeLPLSO(_tiepnhanid, _dtbnid, _loaitiepnhanid) {
        var opt = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "VP_IN_TACH_BANGKE");
        var IN_BK_VP = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "c");
        var IN_GOP_BKNTRU = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "VPI_GOP_BANGKENTRU");
        //check quan y 15 cac tram xa in bang ke rieng
        var flagTramxa = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01.CHECK.TRAMXA", _tiepnhanid);
        if (!IN_BK_VP) IN_BK_VP = 0;
        if (opt == 1) {
            var flag = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T004.10", _tiepnhanid);
            if (_loaitiepnhanid == 0) {
                if (IN_GOP_BKNTRU == 1) {
                    inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBBHYTNOITRU_02BV_QD3455_A4');
                } else {
                    if (_dtbnid == 1) {
                        jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.SINH.STT", _tiepnhanid);
                        if (flagTramxa != null && flagTramxa == 1) {
                            inPhoiVP('1', _tiepnhanid, 'NTU001_BKCPKCBBHYTNOITRU_02BV_QD3455_TRAMXA_A4');
                        } else {
                            inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBBHYTNOITRU_02BV_QD3455_A4');
                        }

                        if (IN_BK_VP == 0 && flag == 1)
                            inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBTUTUCNOITRU_02BV_QD3455_A4');
                    } else {
                        if (flag == 1)
                            inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBTUTUCNOITRU_02BV_QD3455_A4');
                    }
                }
            } else {
                if (_dtbnid == 1) {
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.SINH.STT", _tiepnhanid);
                    if (flagTramxa != null && flagTramxa == 1) {
                        inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_TRAMXA_A4');
                    } else {
                        inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_A4');
                    }

                    if (IN_BK_VP == 0 && flag == 1)
                        inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NGT035_BKCPKCBTUTUCNGOAITRU_A4');
                } else {
                    if (flag == 1)
                        inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NGT035_BKCPKCBTUTUCNGOAITRU_A4');
                }
            }
        } else {
            jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.SINH.STT", _tiepnhanid);
            if (_loaitiepnhanid == 0) {
                inPhoiVP('1', _tiepnhanid, 'NTU001_BKCPKCBNOITRU_02BV_QD3455_A4');
            } else {
                inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBNGOAITRU_01BV_QD3455_A4');
            }
        }
        // in bang ke hao phi neu co
        var flag_haophi = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T005.11", _tiepnhanid);
        var opt_haophi = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "VP_IN_BANGKE_HAOPHI");
        if (opt_haophi == 1) {
            if (flag_haophi == 1) {
                inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCB_HAOPHI_01BV_QD3455_A4');
            }
        }

    }

    function luuQuanLyBenhAn(signType) {
        if (opt.hospital_id == 10284) {
            //ko xử lý gì
        } else {
            arrReportCode = [];
            vienphi_tinhtien.inBangKe($("#hidTIEPNHANID").val(), $("#hidDOITUONGBENHNHANID").val(), $("#hidLOAITIEPNHANID").val(), arrReportCode, 0, 0);
            arrReportCode.forEach(function (el) {
                //lưu bảng quản lý phiếu
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", el);
                if (data_ar != null && data_ar.length > 0) {
                    var obj = new Object();
                    obj.hosobenhanid = $("#hidHOSOBENHANID").val();
                    obj.tiepnhanid = $("#hidTIEPNHANID").val();
                    obj.rpt_code = el;
                    obj.transtype = '10';
                    obj.delete = signType;
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H028.EV003", JSON.stringify(obj));
                }
            });
            if (arrReportCode.length > 0 && cfObj.NGT_KYSOBANGKE_KTK == '1') {
                if (cfObj.KYSO_BANGKE_NGTRU_NAMDAN == '1') {
                    _caRpt3(1);
                }else{
                _caRpt(signType);
                }
            }
        }
    }

    function _caRpt3(signType) {
        var _rptCode = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'RPT_CODE_KYSO_BANGKE_KHAMBENH');
        var _params = [ {
            name: 'hosobenhanid',
            type: 'String',
            value: $("#hidHOSOBENHANID").val()
        }, {
            name : 'tiepnhanid',
            type : 'String',
            value : $("#hidTIEPNHANID").val()
        }, {
            name : 'RPT_CODE',
            type : 'String',
            value : _rptCode
        } ];
        if(signType == '0') {
            CommonUtil.openReportGetCA2(_params, false);
        } else {
            var msg = CommonUtil.kyCASingerPad(_params, signType, true);
            EventUtil.setEvent("eventKyCA",function(e){
                DlgUtil.showMsg(e.res);
            });
        }
    }
    function _caRpt(signType) {
        var _rptCode = cfObj.RPT_CODE_KYSO_BANGKE;
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            var catype = row.CA_TYPE;
            var kieuky = row.KIEUKY;
            if (catype == '5') {
                //isKyTocken = true;
            } else if (catype == '3' || catype == '6') {
                var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                let _paramInput = {
                    params: null,
                    smartca_method: 0
                };
                EventUtil.setEvent("dlgCaLogin_confirm", function () {
                    DlgUtil.close("divCALOGIN");
                    let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                    causer = _hisl2SmartCa.token.refresh_token;
                    capassword = _hisl2SmartCa.token.access_token;
                    smartcauser = _hisl2SmartCa.user.uid;
                    _caRpt2(signType, kieuky, catype);
                });
                let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
                    _paramInput.smartca_method = 1;
                    let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
                    _popup.open("divCALOGIN");
                    return;
                } else {
                    EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function (e) {
                        if (e.data && e.data.token && e.data.token.access_token) {
                            _paramInput.smartca_method = 1;
                        }
                        DlgUtil.close("dlgCA_SMARTCA_LOGIN");
                        let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
                        _popup.open("divCALOGIN");
                        return;
                    });
                    DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {
                        isSignPopup: true
                    }, "Smart Ca Login", 500, 650);
                    DlgUtil.open("dlgCA_SMARTCA_LOGIN");
                    return;
                }
            } else {
                EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                    causer = e.username;
                    capassword = e.password;
                    DlgUtil.close("divCALOGIN");
                    _caRpt2(signType, kieuky, catype);
                });
                EventUtil.setEvent("dlgCaLogin_close", function (e) {
                    DlgUtil.close("divCALOGIN");
                });
                var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
                popup.open("divCALOGIN");
            }
        } else {
            DlgUtil.showMsg('Chưa cấu hình phiếu CA theo rpt_code trong cấu hình RPT_CODE_KYSO_BANGKE!');
            return;
        }
    }

    function _caRpt2(signType, kieuky, catype) {
        arrReportCode.forEach(function (el) {
            var params = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            }, {
                name: 'tiepnhanid',
                type: 'String',
                value: $("#hidTIEPNHANID").val()
            }, {
                name: 'rpt_code',
                type: 'String',
                value: el
            }];
            var oData = {
                sign_type: signType,
                causer: causer,
                capassword: capassword,
                smartcauser: smartcauser,
                params: params
            };
            var msg = CommonUtil.caRpt(oData, el, true, '', false, kieuky, catype);
            DlgUtil.showMsg(msg);
        });
    }

    //L2PT-42611
    function hiddenControl(str) {
        var arSTR = str.split(';');
        for (var i = 0; i < arSTR.length; i++) {
            $("#" + arSTR[i]).css("display", 'none');
        }
    }

    function showControl(str) {
        var arSTR = str.split(';');
        for (var i = 0; i < arSTR.length; i++) {
            $("#" + arSTR[i]).css("display", '');
        }
    }

    function inPhoiVP(_inbangkechuan, _tiepnhanid, _report_code) {
        var par = [];
        par.push({name: 'inbangkechuan', type: 'String', value: _inbangkechuan.toString()});
        par.push({name: 'tiepnhanid', type: 'String', value: _tiepnhanid.toString()});
        var typeExport = "pdf";//$("#sltInBieuMau").val();
        CommonUtil.openReportGet('window', _report_code, typeExport, par);
    }

    //END HISL2TK-1085
    function sendNotifyMH2(type) {
        if (type == 2) {
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.BN.VNCARE", $("#hidBENHNHANID").val() + '$' + $("#hidHOSOBENHANID").val() + '$' + "-1" + '$' + "-1");
            if (data_ar != null && data_ar.length > 0) {
                var _objThongtin = new Object();
                _objThongtin["maCSYT"] = opt.hospital_code;
                _objThongtin["tenCSYT"] = opt.hospital_name;
                _objThongtin["maBN"] = data_ar[0].MABENHNHAN;
                _objThongtin["tenBN"] = data_ar[0].TENBENHNHAN;
                _objThongtin["phone"] = data_ar[0].TK_LIENKET;
                _objThongtin["STTBN"] = $("#hidSOTHUTU").val();
                _objThongtin["soPhong"] = _sophongkham == '0' ? "" : _sophongkham;
                _objThongtin["tenPhong"] = _opt._subdept_name;
                _objThongtin["maLuotKham"] = data_ar[0].MAHOSOBENHAN;
                sendNotify(2, JSON.stringify(_objThongtin));
            }
        }
        if (type == 1) {
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.BN.VNCARE", $("#hidBENHNHANID").val() + '$' + $("#hidHOSOBENHANID").val() + '$' + "-1" + '$' + $('#hidPHONGKHAMDANGKYID').val());
            if (data_ar != null && data_ar.length > 0) {
                for (var i = 0; i < data_ar.length; i++) {
                    var data = data_ar[i];
                    var _objThongtin = new Object();
                    _objThongtin["maCSYT"] = opt.hospital_code;
                    _objThongtin["tenCSYT"] = opt.hospital_name;
                    _objThongtin["maBN"] = data.MABENHNHAN;
                    _objThongtin["tenBN"] = data.TENBENHNHAN;
                    _objThongtin["phone"] = data.TK_LIENKET;
                    _objThongtin["STTHienTai"] = data.STTBN_HT;
                    _objThongtin["STTBN"] = data.STTBN_TIEPTHEO;
                    _objThongtin["soPhong"] = _sophongkham == '0' ? "" : _sophongkham;
                    _objThongtin["tenPhong"] = _opt._subdept_name;
                    _objThongtin["maLuotKham"] = data.MAHOSOBENHAN;
                    sendNotify(1, JSON.stringify(_objThongtin));
                }
            }
        }
    }

    function _kyCaRpt(_params) {
        //check ky cap hay khong
        var _rptCode = _params.find(element => element.name == 'RPT_CODE')['value'];
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            var loaiky = row.LOAIKY;
        }
        if (loaiky == '1') {
            CommonUtil.kyCA(_params, '', '', '', '', '1');
        } else {
            CommonUtil.kyCA(_params);
        }
        EventUtil.setEvent("eventKyCA", function (e) {
            DlgUtil.showMsg(e.res);
        });
    }

    function save_log_act_form(lcode, lfunc, ltext, lkey) {
        var objLogActForm = new Object();
        objLogActForm.LCODE = lcode;
        objLogActForm.LFUNC = lfunc;
        objLogActForm.LTEXT = ltext;
        objLogActForm.LKEY = lkey;
        var _result_log = jsonrpc.AjaxJson.ajaxCALL_SP_S('LOG.ACT.FORM', objLogActForm);
        if (_result_log != '1' && _result_log != '2') {
            DlgUtil.showMsg("Cập nhật log thao tác không thành công: (" + _result_log + ")");
        }
    }
     function getMinutesDiff(date1, date2) {
        let toDate = str  => {
            let [d, m, y, h, i, s] = str.match(/\d+/g).map(Number);
            return new Date(y, m - 1, d, h, i, s);
        };
        return Math.floor((toDate(date2) - toDate(date1)) / (1000 * 60));
    }
}


var ctl_ar_df=[{type:'buttongroup',id:'btnPrint',icon:'print',text:'In ấn'
	,children:[
		{id:'group_0',icon:'print',text:'IN PHIẾU',hlink:'#',group:true}
		,{id:'group_0_1',icon:'print',text:'Gi<PERSON>y ra viện',hlink:'#'}
		,{id:'group_0_1_doc',icon:'print',text:'Gi<PERSON>y ra viện-Doc',hlink:'#'}
		,{id:'group_0_2',icon:'print',text:'Gi<PERSON>y chuyển viện',hlink:'#'}
		,{id:'group_0_3',icon:'print',text:'Giấy hẹn khám',hlink:'#'}
		,{id:'group_0_4',icon:'print',text:'Bảng kê',hlink:'#'}
		,{id:'group_0_5',icon:'print',text:'<PERSON><PERSON><PERSON> kê VT hao phí',hlink:'#'}
		,{id:'group_0_6',icon:'print',text:'Phiếu điều trị',hlink:'#'}
		,{id:'group_2_1',icon:'print',text:'Phiếu chỉ định CLS chung',hlink:'#'}
		,{id:'group_2_2',icon:'print',text:'Đơn thuốc',hlink:'#'}
		,{id:'group_2_5',icon:'print',text:'Phiếu khám bệnh vào viện',hlink:'#'}
		,{id:'group_2_51',icon:'print',text:'Giấy KCB theo yêu cầu',hlink:'#'}
		,{id:'group_2_52',icon:'print',text:'Phiếu khám và theo dõi bệnh nhân lưu',hlink:'#'}
		,{id:'group_2_21',icon:'print',text:'Phiếu khám bệnh vào viện chuyên khoa mắt',hlink:'#'} //L2PT-27727
		,{id:'group_2_6',icon:'print',text:'In tờ điều trị',hlink:'#'}
		,{id:'group_2_7',icon:'print',text:'In bệnh lịch',hlink:'#'}
		,{id:'group_2_8',icon:'print',text:'In bệnh án',hlink:'#'}
		,{id:'group_2_9',icon:'print',text:'In phiếu đánh giá ban đầu bệnh nhân',hlink:'#'}
		,{id:'group_2_10',icon:'print',text:'In phiếu sàng lọc và đánh giá dinh dưỡng',hlink:'#'}
		,{id:'group_2_11',icon:'print',text:'In các phiếu vào viện',hlink:'#'}
		,{id:'group_2_12',icon:'print',text:'In Xét nghiệm chung',hlink:'#'}
		,{id:'group_2_13',icon:'print',text:'In giấy nhận trả phim x-quang',hlink:'#'}
		,{id:'group_2_14',icon:'print',text:'In tách bảng kê theo khoa',hlink:'#'}
		,{id:'group_2_15',icon:'print',text:'In giấy chứng nhận thương tích',hlink:'#'}
		,{id:"group_2_19","icon":"print","text":"Phiếu xác nhận đồng ý xét nghiệm HIV","hlink":"#"}
		,{id:'group_0_InPhieuXuTri',icon:'print',text:'In giấy Khám bệnh',hlink:'#'}
		,{id:'group_0_pkbenh',icon:'print',text:'Phiếu khám bệnh',hlink:'#'}
		,{id:'btnKHAC_9_1',icon:'goikham',text:'Nghỉ hưởng BHXH',hlink:'#'}
		,{id:'group_0_PKchuyenkhoa',icon:'print',text:'In phiếu khám chuyên khoa',hlink:'#'}
		,{id:'btnTVDD',icon:'goikham',text:'Tạo phiếu tư vấn dinh dưỡng',hlink:'#'}
		,{id:'btnBBHC',icon:'goikham',text:'Tạo biên bản hội chẩn',hlink:'#'}
		,{id:'print_159',icon:'print',text:'In đơn thuốc không thuốc',hlink:'#'}
		,{id:'btnXNBT_HCG',icon:'goikham',text:'Giấy cam kết XN beta HCG',hlink:'#'}
		,{id:'btnLichHen',icon:'goikham',text:'Thông tin lịch hẹn',hlink:'#'}
		,{id:'treatdt_18_1',icon:'goikham',text:'Đánh giá tình trạng dinh dưỡng >=18 tuổi, không mang thai',hlink:'#'}
		,{id:'btnChuyenPKKT',icon:'goikham',text:'Chuyển phòng khám',hlink:'#'}
		,{id:'btnInGIAYBAOTU', arr:'1',icon:'goikham',text:'In giấy báo tử',hlink:'#'}
	]}
	,{type:'button',id:'btnDSKham',icon:'dskham',text:'DS Khám'},
	{type:'buttongroup',id:'btnBANGT',icon:'benhan',text:'BA NGT'
		,children:[
			{id:'btnBANGT_0',icon:'benhan',text:'Bệnh án',hlink:'#'}
			,{id:'btnBANGT_1',icon:'benhan',text:'Chọn bệnh án',hlink:'#'}
			,{id:'btnBANGT_2',icon:'benhan',text:'Mở bệnh án',hlink:'#'}
			,{id:'btnBANGT_3',icon:'benhan',text:'Đóng bệnh án',hlink:'#'}
			,{id:'btnBANGT_4',icon:'benhan',text:'Đưa ra khỏi bệnh án',hlink:'#'}
		]}
	,{type:'button',id:'btnCall',icon:'volume-up',text:'Gọi khám',show:'true'}
	,{type:'button',id:'btnStart',icon:'khac',text:'Bắt đầu',show:'false'}
	//,{type:'button',id:'btnLuu',icon:'floppy-disk',text:'Lưu'}
	,{type:'button',id:'btnExam',icon:'kham',text:'Khám bệnh'}
	/*,{type:'buttongroup',id:'btnTreat',icon:'edit',text:'Điều trị'
		,children:[
			        {id:'treat_1',icon:'edit',text:'Tạo phiếu điều trị',hlink:'#'}
			        ]}*/
	,{type:'button',id:'btnService',icon:'dichvu',text:'Dịch vụ',hlink:'#'}
	,{type:'buttongroup',id:'btndrug',icon:'thuoc',text:'Thuốc',hlink:'#'
		,children:[
			{id:'drug_thuoc',icon:'thuoc',text:'THUỐC',hlink:'#',group:true},
			{id:'drug_khothuoc',icon:'thuoc',text:'Tạo đơn thuốc từ kho',hlink:'#'},
			{id:'drug_tutruc',icon:'thuoc',text:'Tạo đơn thuốc từ tủ trực',hlink:'#'},
			/*{id:'drug_1',icon:'thuoc',text:'Tạo đơn thuốc',hlink:'#'},
		           {id:'drug_le',icon:'thuoc',text:'Kê thuốc lẻ',hlink:'#'},*/
			/*{id:'drug_2',icon:'thuoc',text:'Tạo phiếu trả thuốc',hlink:'#'},	*/
			{id:'drug_mn',icon:'thuoc',text:'Tạo đơn thuốc mua ngoài',hlink:'#'},
			{id:'drug_tuvan',icon:'thuoc',text:'Tạo đơn thuốc tư vấn',hlink:'#'},
			/*{id:'drug_1kt',icon:'thuoc',text:'Tạo đơn thuốc không thuốc',hlink:'#'},*/
			{id:'drug_dtnhathuoc',icon:'thuoc',text:'Mua thuốc nhà thuốc',hlink:'#'},
			/*{id:'drug_phieuđinhuong',icon:'thuoc',text:'Phiếu tư vấn dinh dưỡng (Kê)',hlink:'#'},*/
			{id:'drug_thuocdy',icon:'thuoc',text:'THUỐC ĐÔNG Y',hlink:'#',group:true},
			{id:'drug_1dy',icon:'thuoc',text:'Tạo đơn thuốc đông y',hlink:'#'},
			/*{id:'drug_2dy',icon:'thuoc',text:'Tạo phiếu trả thuốc đông y',hlink:'#'},*/

			{id:'drug_vattu',icon:'thuoc',text:'VẬT TƯ',hlink:'#',group:true},
			{id:'drug_3',icon:'thuoc',text:'Tạo phiếu vật tư',hlink:'#'}
			//tuyennx_add_start_20181022 L2HOTRO-11542
			,{id:'group_2',icon:'',text:'Khác',hlink:'#',group:true}
			,{id:'drug_8',icon:'thuoc',text:'Tạo phiếu hao phí',hlink:'#'}
			,{id:'drug_hpvt',icon:'thuoc',text:'Tạo phiếu VT hao phí',hlink:'#'}
			//tuyennx_add_end_20181022
			/*,{id:'drug_4',icon:'thuoc',text:'Tạo phiếu trả vật tư',hlink:'#'}*/
		]}
	/*,{type:'buttongroup',id:'btnhandling',icon:'bell',text:'Xử trí khác',hlink:'#'
		,children:[
		           {id:'handling_1',icon:'bell',text:'Chuyển phòng khám',hlink:'#'},
		           {id:'handling_2',icon:'bell',text:'Khám thêm phòng',hlink:'#'},
		           {id:'handling_3',icon:'bell',text:'Trả bệnh nhân (không khám)',hlink:'#'},
		           {id:'handling_4',icon:'bell',text:'Kết thúc khám',hlink:'#'},
		           {id:'handling_5',icon:'bell',text:'Xử trí khám bệnh',hlink:'#'}
		           ]}*/
	,{type:'button',id:'btnPhieuKham',icon:'xutri',text:'Xử trí KB', show:'false'}
	,{type:'buttongroup',id:'btnKHAC',icon:'goikham',text:'Khác',hlink:'#'
		,children:[
			{id:'group_0',icon:'goikham',text:'KHÁC',hlink:'#',group:true},
			{id:'handling_1',icon:'goikham',text:'Chuyển phòng khám',hlink:'#'},
			{id:'handling_4',icon:'goikham',text:'Hồ sơ quản lý sức khỏe cá nhân',hlink:'#'},//nghiant 14062017*/
			{id:'handling_3',icon:'goikham',text:'Trả bệnh nhân (không khám)',hlink:'#'},
			{id:'btnKHAC_3',icon:'goikham',text:'Tai nạn thương tích',hlink:'#'},
			{id:'btnKHAC_8',icon:'goikham',text:'Phiếu vận chuyển',hlink:'#'},
			{id:'handling_2',icon:'goikham',text:'Đổi phòng khám',hlink:'#'},
			{id:'btnKHAC_9',icon:'goikham',text:'Nghỉ hưởng BHXH',hlink:'#'},
			/*{id:'btnKHAC_10',icon:'goikham',text:'Chỉ định thu khác',hlink:'#'},*/
			//Lập phiếu tạm ứng
			{id:'bntKHAC_ptu',icon:'goikham',text:'Lập phiếu tạm ứng',hlink:'#'},
			{id:'btnKHAC_10',icon:'goikham',text:'Nhập bệnh án',hlink:'#'},
			{id:'btnKHAC_11',icon:'goikham',text:'Cập nhật ĐT Ngoại trú',hlink:'#'},
			{id:'btnLuuTruPhoiThai',icon:'goikham',text:'Lưu trữ phôi thai',hlink:'#'},
			{id:'btnThemGoiKham',icon:'goikham',text:'Thêm gói khám',hlink:'#'},
			{id:'btnTienSuDiUng',icon:'goikham',text:'Tạo phiếu khai thác tiền sử dị ứng',hlink:'#'},
			{id:'btnDiUngThuoc',icon:'goikham',text:'Dị ứng thuốc',hlink:'#'},
			{id:'treat_1',icon:'goikham',text:'Tạo phiếu điều trị',hlink:'#'},
			//tuyennx_add_start
			{id:'btnChuyenTuyenCS',icon:'dichvu',text:'Chuyển BN về tuyến cơ sở',hlink:'#'},
			{id:'btnCapSoBenh',icon:'dichvu',text:'Cấp sổ bệnh',hlink:'#'},
			{id: 'btnThuTienKhac', icon: 'thutien', text: 'Thu khác', hlink: '#'},
			{id:'ban_QLHIV',icon:'goikham',text:'QL bệnh nhân HIV',hlink:'#'},
			//tuyennx_add_end
			{id:'group_0',icon:'print',text:'XỬ TRÍ',hlink:'#',group:true},
			{id:'btnNewBed',icon:'dichvu',text:'Kê giường',hlink:'#'},
			{id:'handling_10',icon:'goikham',text:'Xếp giường cho bệnh nhân',hlink:'#'},
			{id:'btnKHAC_0', arr:'1',icon:'goikham',text:'Thông tin tử vong',hlink:'#'},
			{id:'btnDOICONGKHAM',icon:'goikham',text:'Đổi công khám',hlink:'#'},

			{id:'btnKHAC_2',icon:'goikham',text:'Kiểm điểm tử vong',hlink:'#'},
			{id:'btnKHAC_4',icon:'goikham',text:'Thông tin ra viện',hlink:'#'},
			{id:'btnKHAC_5',icon:'goikham',text:'Chuyển viện',hlink:'#'},
			{id:'btnKHAC_6',icon:'goikham',text:'Hẹn khám mới',hlink:'#'},
			{id:'btnKHAC_7',icon:'goikham',text:'Hẹn khám tiếp',hlink:'#'},
			{id:'btnBIENBANHOICHAN',icon:'goikham',text:'Tạo biên bản hội chẩn',hlink:'#'}
		]}
	/*,{type:'button',id:'handling_1',icon:'share',text:'Chuyển PK',hlink:'#'}*/
//	,{type:'button',id:'btnNewBed',icon:'dichvu',text:'Kê giường',hlink:'#'}
	,{type:'button',id:'btnTTSMARTCARD',icon:'usd',text:'TT Thẻ TM',hlink:'#'}
	,{type:'button',id:'btnKTKH',icon:'ketthuc',text:'Kết thúc khám',hlink:'#'}
	,{type:'button',id:'btnTIEPNHANCC',icon:'khac',text:'Tiếp nhận CC',hlink:'#'}
];
//var toolbar=new JsToolbar('toolbarId');
//toolbar.buildToolbar(ctl_ar);
var cfObj = new Object();

function benhnhanList(opt) {
	var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NGT_THONGTINKHAMBENH;HIS_KETTHUC_KHAM_KHI_XUTRI;KB_MHC2_NO_DBLCLICK;NGT_MHKB_PKB;HIS_SHOW_TTBENHAN_BS;HIS_CANHBAO_KHAM5P_TGBATDAU;NGT_LOADBADN_ALLPHONG;" +
		"NGT_CHANKTK_CHOKHAM;KHAM_ONLINE_WS;NGT_SINHTON_CANNANG;SMS_BN_CSYT;NGT_KYSOBANGKE_KTK;NGT_CHANKHAMBENH_KETTHUCKHAM;MENU_KHAC_THONGTIN_TUVONG;NGT_GIATRI_KETQUA_DIEUTRI;NGT_KHAMBENH_CHECKCLS;NGT_MAUCONTHUOC_KB2;" +
		"NGT_CHAN_KHAMBENH;NGT_HIENTHIMAU_CAPCUU;CDDV_GIAODIEN_KHA;NGT_CPK_CANHBAO;NGT_LCD_BNDANGKHAM;NGT_CHONNGAY_AUTOSEARCH;c;INDONTHUOC_PHIEULINH;NGT_KBHB_FULL;KHAMBENH_CHUYENPHONG_ICD;NGT_MHC2_COLOR;NOTIFY_APP_VNCARE;" +
		"NGT_CHECKTGKHAMGIUA2BN;NGT_CHECKICDDAINGAY_30NGAY;KBH_SETNGAYTIEPNHAN;NGT_MO_POPUP_BATDAUKHAM;HIS_CANHBAO_KHAM5P_TGBATDAU1;HIS_CANHBAO_BN;NGT_ANHIEN_CHUYENDOI_PKCK;NGT_LUUQUANLYBENHAN;HIS_SHOWLANHDAO;" +
		"NGT_KHAMBENH_DISBTN_DANGKHAM;NGT_KB_DSKB;SOLUONG_ICD_TRONGMOTLANDIEUTRI;NGT_FIXED_TOOLBAR;NGT_HIENTHIMAU_DATLICH;HIS_SUDUNG_KYSO_KYDIENTU;NGT_CHINHSUABENHCHINH;NGT_BENHNHAN_GHEPCAP;NGT_DSKHAM_PHANBIETMAU;" +
		"NGT_DUYETKETOAN_KTKHAM;NGT_LCD_KB_FORM;NGT_KB_CAPNHAPTTHC;NGT_HIENTHI_TUOI_GRIDKB2;NGT_CHECKTRUNGTGIAN_NHAPVIEN;NGT_CANHBAO_TGKQCLS;HIS_TUDONG_IN_BANGKE;HIS_POPUP_SHOWTTBN;NGT_KIEUGOIKHAM_GG;NGT_HUYNHAPVIEN_HIENTHI;" +
		"KETHUOC_LOADKHO_HAOPHI;NGT_DONTHUOC_BADN;NGT_KB2_DISABLE_BACSY;HIS_TUDONG_IN_PHIEU_HK;LOAIKEDON_MUANGOAI;HIS_TUDONG_IN_PHIEU_CV;VPI_TTSMC_DAY_HDDT;HIS_SHOW_TAB_UX2023;QD_4750;HIS_CANHBAO_BNTUVONG;NTU_KTBA_CHECK_KQCLS;" +
		"NGT_CHANTHOIGIAN_BATDAUKHAM;NGT_BAOTINCHOKB2_SHOW;HIS_CONFIG_KEGIUONG_DANHSACH;KETHUOC_CHAN_KHAMCK;NGT_TABTRUYENDICH;HIS_CANHBAO_KHAM5P_TL;HIS_TUDONG_IN_PHIEU_NV;NGT_ANNUTLUU_KB2;VP_DUYET_BH_KHI_DUYET_KETOAN;" +
		"NTU_PHIEUTRUYENDICH_BDHN;VPI_GOP_BANGKENTRU;VPI_AUTO_DUYET_BHYT_NGT;NGT02K001_SHOW_PTU;RPT_CODE_KYSO_BANGKE;NGT_TIMKIEMXUTRI_KBMHC;APPBN_KTK_XUTRIDAYDL;HIS_CANHBAO_KHAM5P;NGT_DOIPHONGKHAM_DV;NGT_HUYCK_THEOPHONG;" +
		"AUTO_SMS_QC;NGT_GHICHU_BENHCHINH;DTDT_DAY_DONTHUOC;NGT_FORMAT_PRINT_DOC;NGT_PHIMTAT_KB2;HIS_KTBA_CHECK_KYCA;HIS_CHUYENKHOA_BS_DV;NGT_CHANCDDVTHUOC_KTK;NGT_MAUCONTHUOC_UUTIEN_KB2;NGT_CANHBAO_DONGTIEN;HIS_QD130;" +
		"FOMAT_MA_BENHPHU;CHECK_XTRI_PKB;NGT_CHECKTRUNGTHAOTAC_TGBD;NGT_BATBUOC_QTBL_KB2;NGT_CHECKTGIANBDKHAM_2BN;HIS_GIOIHAN_CANNANG;CHECK65BHYT_CHAN;NGHIHUONG_BHXH;NGT02K001_KB_MHC_VS2_DIVMSG;HIS_KTBA_GUI_EBHYT;" +
		"VP_IN_BANGKE_HAOPHI;AUTO_LIENKET_VNCARE;CHECK65BHYT;NGT_DOIMAU_PHONGDANGKY;IN_GOPPHIEU_NV_1TAB;VP_IN_TACH_BANGKE;CHAN_CDDV_KETHUOC;QD130_DAYXMLCHECKIN_BDKHAM;NGT_KB_CHECKTGCDDV;HIS_CANHBAO_KHAM5P_XT;" +
		"NGT_TRANGTHAI_BN_LOAD;NGT_HT_BADAINGAY;HIS_SHOW_ICDYHCT;HIS_KHOA_BENHAN;NGT_LOADBACSY_NDH;HIS_KETTHUCKHAM_KHICODONTHUOC;BVBD_SMS_HTSS_CAMON;NGT_CHAN_XUTRI_CAPTOA;NGT_CHECK_BADNCONMO;" +
		"NGT_KHAMBENH_TK_TG;KYSO_BANGKE_NGTRU_NAMDAN;NGT_CHECKTGBDKHAM_TGTIEPNHAN;NGT_KETHUOC_KHI_NHAPVIEN;NGT_KB2_ENTER_TKBN;KBH_SINHTON_BATBUOC;NGT_DOIVITRI_KQDT_XT;NGT_NHAPSINHTON_BANGKHONG;NGT_KBMHC_IN_DONTHUOC");//L2PT-103732 // L2PT-126285
	if (config_ar != null && config_ar.length > 0) {
		cfObj = config_ar[0];
	} else {
		DlgUtil.showMsg("Không tải được thông tin cấu hình sử dụng trên màn hình");
		return;
	}
	var LNMBP_XetNghiem=1;
	var LNMBP_CDHA=2;
	var LNMBP_ChuyenKhoa=5;
	var LNMBP_DieuTri=4;
	var LNMBP_ChamSoc = 9;
	var LNMBP_Phieuvattu = 8;
	var LNMBP_Phieuthuoc = 7;
	var LNMBP_TruyenDich=13; //L2PT-61755
	var _flgModeView='0';
	var _tsmobenhan = '0';
	var _enable_mobenhan = '0';
	var _checktienkhiketthuckham = '0';
	var _idkhambenh = 0;
	var _sophongkham = "0";
	var _ngtgoibenhnhan = "0";
	var _tylevpchuyenphanhe = "2"; 				// VIEN PHI;
	var _khamchinhphu = "0";
	var _khamchinhphudetail = "0";
	var _chedogoikham = "0"; 					// CHE DO THUONG;
	var _loadFirstICD = false; 						// load ICD lan dau;
	var _loadFirstICDYHCT = false; 						// load ICD lan dau; 	
	var data_arkb = [];
	var _loai = "";//phan loai man kham benh va man tiem chung
	_loai = getParameterByName('loai',window.location.search.substring(1));
	var _tinhngaycapcuu = "2"; 					// mac dinh tru di 2 ngay;
	var _bnvpinhathuoc = "0";
	var _pkbvvcapcuu = "0";
	var _kbatbuocBenhChinh = 0;
	var _batbuoccdbd = 0;
	var _batbuoccddv = 0;
	var _modeGoiKham = "0";
	var _timeOutGoiKham = "5000";
	var _canhbaobdkham = "0";
	var _inphieuxutrikb = "0";
	var _doicongkhamkb = "-1";
	var _chedolcd = "0";
	var _hienthigoikham = "0";
	var _hienthikqdt = "0";
	var _batbuockqdt = "0";
	var selectIndex = 1;
	var _leghtList = 0;
	var loadlandau = 0 ;
	var checkDsFull = false;
	var _mau_chuyenkhamngt = "0"; 											// mau hien thi bn chuyen kham chuyen khoa;
	var _disableTTKB = cfObj.NGT_THONGTINKHAMBENH;
	var _vssid = "0";
	var _mabenhanndh = "0";
	var tenbn = '';
	var _mau_conthuoc = "0";
	var _ktkhamxutri = cfObj.HIS_KETTHUC_KHAM_KHI_XUTRI;

	var sdate = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');
	var _setngaytiepnhan = cfObj.KBH_SETNGAYTIEPNHAN;
	var sms_info = cfObj.SMS_BN_CSYT;
	var NGT_CANHBAO_DONGTIEN = cfObj.NGT_CANHBAO_DONGTIEN;
	var NGT_CALLBN_SMARTADS = "0";
	var htmlDlg = "";
	var showTTBNWidget = false;
	var NGT02K001_KB_MHC_VS2_DIVMSG = false;//L2PT-46578
	var _disableBacsy = 0;
	var mauconthuoc_bnthuong = "0";
	var mauconthuoc_bnuutien = "0";
	var NGT_LOADBADN_ALLPHONG = cfObj.NGT_LOADBADN_ALLPHONG;
	var sqlloadbadn = '';
	var check_kqcls = 0;
	var showyhct4750 = '0';
	var cf = new Object(); //L2PT-93594
	//lấy cấu hình động cho menu button
	var par_ctl = ['TOOLBAR_MENU_KHAMBENH'];
	if(_loai == "tiemchung"){
		par_ctl = ['TOOLBAR_MENU_TC'];
		$('#tabLSTiemChungTab').css('display', 'block');
		$('#tabLSTiemChung').css('display', 'block');
		$("#dvLIENHE").css("display","");
		$("#dvNGUOILH").css("display","none");
		$("#dvSDT").css("display","");
	}
	var ctl_par = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', par_ctl.join('$'));
	var ctl_ar_other = JSON.parse(ctl_par);
	var toolbar;
	if(ctl_ar_other != '0'){
		toolbar=ToolbarUtil.buildVs2('toolbarId',ctl_ar_other);
	} else{
		toolbar=ToolbarUtil.buildVs2('toolbarId',ctl_ar_df);
	}

	toolbar.addEvent("btnStart","click",function(e){
		console.log('btnStart_click');
		console.log('txtFromDate='+toolbar.getValue("txtFromDate"));
	});
	var _toolbar=ToolbarUtil.getToolbar('toolbarId');

	_toolbar.addEvent("print_1","click",function(e){
		console.log('print_1_click');
		console.log('txtToDate='+toolbar.getValue("txtToDate"));
	});
	toolbar.addEvent("txtSearch","click",function(e){
		console.log('txtSearch_click');
		console.log('txtFromDate='+toolbar.getValue("txtFromDate"));
	});

	_grdDieuTri="grdDieuTri";
	$grdDieuTri = $("#" + _grdDieuTri);
	var _type = "";
	var _opt = opt;
	var _gridId="grdDSBenhNhan";
	this.optn = $.extend({},opt);
	var that = this;
	var _batbuocSinhTon = 0;
	var _hinhthucvaovienid = "0";
	var _sudungKhamOnline = cfObj.KHAM_ONLINE_WS;
	var NGT_HIENTHIMAU_CAPCUU = "0";  //L2PT-27662
	var _colorMHC = cfObj.NGT_MHC2_COLOR;
	var NGT_DSKHAM_PHANBIETMAU = "0";
	var NGT_CHECK_TG_KHAM_CLS = "0"; //L2PT-43979
	var NGT_LOAI_KT_DONGY = "0"; //L2PT-45780
	var NGT_HIENTHIMAU_DATLICH = "0";  //BVTM-4311
	var sendeBHYT = cfObj.HIS_KTBA_GUI_EBHYT;
	var checktrungtgian = 0 ;
	var checkthaotactrungtgbd = 0 ;
	var checkbdkham_3phut = 0;
	var thongtinbn = '';
	this._gridHeader=" ,ICON,30,0,ns,l; ,ICONCLS,30,0,ns,l;KQCLS,KQCLS,0,0,t,l;khambenhid,KHAMBENHID,0,0,t,l;" +
		"hosobenhanid,HOSOBENHANID,0,0,t,l;" +
		"phongkhamdangkyid,PHONGKHAMDANGKYID,0,0,t,l;phongkhamdangkyid_cha,PHONGKHAMDANGKYID_CHA,0,0,t,l;" +
		"benhnhanid,BENHNHANID,0,0,t,l;doituongbenhnhanid,DOITUONGBENHNHANID,0,0,t,l;Thời gian bắt đầu,THOIGIANBD,130,0,t,l;" +
		"tiepnhanid,TIEPNHANID,0,0,t,l;loaitiepnhanid,LOAITIEPNHANID,0,0,t,l;" +
		"trangthaikhambenh,TRANGTHAIKHAMBENH,10,0,t,l;hinhthucvaovienid,HINHTHUCVAOVIENID,10,0,t,l;" +

		(_sudungKhamOnline == "1"?
			"Gọi,onlineGOI,50,0,f,l,1,2,ES;Xem lại,onlineXEMLAI,50,0,f,l,1,2,ES;"
			: "Gọi,onlineGOI,50,0,t,l,1,2,ES;Xem lại,onlineXEMLAI,50,0,t,l,1,2,ES;") +

		"USER_CHUYEN,USER_CHUYEN,60,0,t,l;MA_CSYT_CHUYEN,MA_CSYT_CHUYEN,60,0,t,l;" +
		"Mã người nhận,MANGUOINHAN,60,0,t,l;Mã đăng ký,MADANGKY,60,0,t,l;" +

		"xutrikhambenhid,XUTRIKHAMBENHID,10,0,t,l;hinhthucxutriid,HINHTHUCXUTRIID,10,0,t,l;SỐ TT,SOTHUTU,60,0,f,l;" +
		"HỌ TÊN,TENBENHNHAN,230,0,f,l;"+
		"MÃ CẶP,GHEPCAPID,50,0,f,l;" +
		(cfObj.NGT_HIENTHI_TUOI_GRIDKB2 == "1" ? "TUỔI,TUOI,88,0,f,l;" : "NĂM SINH,NAMSINH,88,0,f,l;" ) +
		"GIỚI TÍNH,GIOITINH,88,0,f,l;ĐỊA CHỈ,DIACHI,165,0,f,l;MÃ BN,MABENHNHAN,60,0,f,l;" +
		"Lần gọi,LANGOIKHAM,0,0,f,l;LẦN GỌI,LANGOI,0,0,t,l;MÃ BA,MAHOSOBENHAN,60,0,f,l;" +
		"MÃ BHYT,MA_BHYT,100,0,f,l;TRẠNG THÁI,TENTRANGTHAIKB,0,0,t,l;MAUBENHPHAMID,MAUBENHPHAMID,110,0,t,l;DICHVUKHAMBENHID,DICHVUKHAMBENHID,110,0,t,l;" +
		"madichvu,MADICHVU,0,0,t,l;uutienkhamid,UUTIENKHAMID,0,0,t,l;TRANGTHAITIEPNHAN,TRANGTHAITIEPNHAN,0,0,t,l;" +
		"loaibenhanid,LOAIBENHANID,0,0,t,l;DACODICHVUTHUTIEN,DACODICHVUTHUTIEN,0,0,t,l;NGAYTHUOC,NGAYTHUOC,0,0,t,l;NGAYTIEPNHAN,NGAYTIEPNHAN,0,0,t,l;" +
		"TRANG_THAI,TRANG_THAI,0,0,t,l;PHONGID,PHONGID,0,0,t,l;DIEUTRI_NGT,DIEUTRI_NGT,0,0,t,l;CHUYENKHAMNGT,CHUYENKHAMNGT,0,0,t,l;" +
		"KHAMCHINHPHU,KHAMCHINHPHU,0,0,t,l;TEN_FILE,TEN_FILE,0,0,t,l;SUB_DTBNID,SUB_DTBNID,0,0,t,l;" +
		"SOPHONGKHAM,SOPHONGKHAM,0,0,t,l;BADAINGAY,BADAINGAY,0,0,t,l"; //L2PT-10023
	//thaiph sap xep cot trong danh sach kham benh theo cau hinh//
	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(
		"COM.DS_CAUHINH","NGT_TT_DS_KB_2");
	if(data_ar[0].NGT_TT_DS_KB_2.length > 100){
		this._gridHeader = data_ar[0].NGT_TT_DS_KB_2;
	}
	par_ctl = ['GRID_TABLE_KB2'];
	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', par_ctl.join('$'));
	if(data_ar.length > 100){
		this._gridHeader = data_ar;
	}
	if (cfObj.NGT_BENHNHAN_GHEPCAP != "1")
		this._gridHeader = this._gridHeader.replace("MÃ CẶP,GHEPCAPID,50,0,f,l;","")

	//end thaiph//
	var _SQL=["NGT02K001.EV001", "NGT02K001.EV002", "NGT02K001.EV003","NGT02K001.EV004", "NGT02K009.RV005", "NGT02K009.RV002"];

	//L2PT-6928 ngocnva start
	var BADN_DONTHUOC = cfObj.NGT_DONTHUOC_BADN;
	//L2PT-6928 ngocnva end

	//L2PT-7901 ngocnva start
	var sinhTonCanNang = cfObj.NGT_SINHTON_CANNANG;
	//L2PT-7901 ngocnva end
	//L2PT-103732 start
	if (cfObj.NGT_NHAPSINHTON_BANGKHONG == '1') {
		$('#txtKHAMBENH_CHIEUCAO, #txtKHAMBENH_NHIPTHO, #txtKHAMBENH_CANNANG, #txtSPO2, #txtKHAMBENH_HUYETAP_HIGH, #txtKHAMBENH_HUYETAP_LOW, #txtKHAMBENH_NHIETDO, #txtKHAMBENH_MACH').removeAttr("valrule");
	}
	//L2PT-103732 end
	var dataDaingay = {}; //L2PT-23853

	this.load=doLoad;

	function doLoad() {
		$.i18n().locale = (typeof this.optn.lang !== "undefined") ? this.optn.lang : "vn";//L2PT-103732
		// null: khambenh; 1 - kham benh trai ngay;; 2 - kham benh cap cuu; 3 - kham benh theo bac sy;
		_type = getParameterByName('type',window.location.search.substring(1));
		if(_type != 2){
			$("#toolbarIdbtn_TNTT").hide();

		}
		if(_type != 1){
			$("#sttDiv").css("display","none");
			$('#dvXUTRIKHAMBENH').remove();
			$('#dvXUTRIKHAMBENH1').show();
		}else {
			$('#dvXUTRIKHAMBENH').show();
			$('#dvXUTRIKHAMBENH1').remove();
		}

		ssid = getParameterByName('ssid',window.location.search.substring(1));
		$('#txtMABENHNHANTK').focus();
		$('#toolbarIdbtnhandling').css('width','105px');
		$('#toolbarIdtxtFromDate').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
		$('#toolbarIdtxtToDate').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));

		$("#toolbarIdtxtFromDate").attr("valrule", "Từ ngày,date|max_length[10]");
		$("#toolbarIdtxtToDate").attr("valrule", "Đến ngày,date|max_length[10]");

		$("#toolbarIdbtndrug").attr("disabled", true);
		$("#toolbarIdbtnStart").attr("disabled", true);
		// if(opt.hospital_id!=965){
		// 	$("#toolbarIdgroup_2_6").addClass("hidden");
		// }

		if(cfObj.NGT_DOIVITRI_KQDT_XT == "0"){
			$("#dvKQDTXT1").remove();
		}else{
			$("#dvKQDTXT0").remove();
		}

		ComboUtil.getComboTag("cboXUTRIID","NGT05.XUTRI",[], "" ,{extval: true,value:0, text:'Chọn xử trí'},"sql","","");
		ComboUtil.getComboTag("cboXUTRIKHAMBENHID","NGT.TT.01.02",[], 0 ,{extval: true,value:0, text:'Chọn xử trí'},"sql","","");
		ComboUtil.getComboTag("cboBACSIID","NGT02K001.BACSI1",[], 0 ,{value:0, text:'Chọn Bác sĩ'},"sql","","");
		if (cfObj.HIS_SHOWLANHDAO == '1') {
			$('#divLANHDAO').show();
			ComboUtil.getComboTag("cboLANHDAOVIENID", "DMC.GETLANHDAOBV", [], 0, {value : 0,text : 'Chọn'}, "sql", "", "");
			ComboUtil.getComboTag("cboLANHDAOKHOAID", "DMC.GETLANHDAOKHOA", [ {"name" : "[1]", "value" : _opt.khoaid} ], "", {value : 0, text : 'Chọn'}, "sql", "", "");
		}

		NGT02K001_KB_MHC_VS2_DIVMSG = cfObj.NGT02K001_KB_MHC_VS2_DIVMSG; //L2PT-46578

		_batbuocSinhTon = cfObj.KBH_SINHTON_BATBUOC;
		var sql_par=[];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("LAY.CAUHINH",sql_par.join('$'));

		if (data_ar != null && data_ar.length > 0) {
			cf = data_ar[0]; //L2PT-93594
			if(data_ar[0].HIS_SHOW_TTBENHAN_WIDGET == "1"){
				showTTBNWidget = true;
			}
			if(data_ar[0].NGT_HIENTHI_CHON_ICDCHINH == "1"){
				$("#div_icdchinh").show();
			}
			if(data_ar[0].DK_MOBENHAN == "1"){
				_tsmobenhan = 1;
			}

			// SONDN L2PT-31163
			if (data_ar[0].DSBNFULL == "1"){
				$("#divHANHCHINH").hide();
				$("#divDSKham").removeClass("col-xs-6");
				$("#divDSKham").addClass("col-xs-12");
				$('#tabAnHien').css('display','');
				$('#tabAnHien ins').html('');
				$('#tabAnHien ins').html('Hiện chi tiết');
				checkDsFull = true;
			}

			if (data_ar[0].NGT_MH2_TTHC == "1"){
				$("#btnTTHC_REMOVE").show();
			}

			if (data_ar[0].DSBNKB != "0"){
//	    		var _addedCol = ";Đối tượng,DOITUONGBENHNHAN,60,0,f,l;Đơnvị/CT,LOAIDVCT,60,0,f,l;Loại khám,LOAIKHAM,60,0,f,l" +
//	    				";Tổng CP,TONGCP,60,0,f,l;Tạm ứng,TAMUNG,60,0,f,l;Đã nộp,DANOP,60,0,f,l" +
//	    				";Còn nợ,CONNO,60,0,f,l;Xử trí,XUTRIKHAMBENH,60,0,f,l;Kết quả điều trị,KETQUADIEUTRI,60,0,f,l" +
//	    				";Trạng thái PTTT,TRANGTHAIPTTT,60,0,f,l;Thân nhân,THANNHAN,60,0,t,l";
				this._gridHeader= this._gridHeader + cfObj.NGT_KB_DSKB;
			}

			if (data_ar[0].DSBNGHICHU == "1"){
				$("#dvGHICHU").show();
			}
			// END SONDN L2PT-31163

			if(data_ar[0].HIS_DOI_BACSI_DT == "1"){
				$('#divBsikham').css('display','');
				var r_data = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H046.EV003",ssid);
				if(r_data != null && r_data.length > 0){
					data = r_data[0];
					$("#labelBACSIDIEUTRIMS").html(data.OFFICER_NAME + ' (' + data.USER_NAME +')');
					$("#hidBACSYKE").val(data.USER_ID);
				}
			}

			if(data_ar[0].NGAYPK == "0"){
				$('#toolbarIdtxtFromDate').attr("disabled", true);
				$('#lblTuNgay').attr("disabled", true);
				$('#toolbarIdtxtToDate').attr("disabled", true);
				$('#lblDenNgay').attr("disabled", true);
			}else{
				$('#toolbarIdtxtFromDate').attr("disabled", false);
				$('#lblTuNgay').attr("disabled", false);
				$('#lblTuNgay').attr("disabled", false);
				$('#lblDenNgay').attr("disabled", false);
			}

			if(data_ar[0].HIDE_BTN_MO_BA == '1'){
				_enable_mobenhan = '1';
			}

			if(data_ar[0].CHUPANH == '1'){
				$('#divIMG').css("display", "");
			}

			if(data_ar[0].ANBANGT == '0'){
				$("#toolbarIdbtnBANGT").hide();
			}else{
				$("#toolbarIdbtnBANGT").show();
			}

			if(data_ar[0].HIDEDONTHUOCKT == "0"){
				$("#toolbarIddrug_1kt").hide();
			}else{
				$("#toolbarIddrug_1kt").show();
			}

			$("#hidTATTHONGBAOKBHB").val(data_ar[0].TATTHONGBAOKBHB);
			_ngtgoibenhnhan = data_ar[0].NGTGOIBENHNHAN;
			_tylevpchuyenphanhe = data_ar[0].TYLEVPCHUYENPHANHE;
			_khamchinhphu = data_ar[0].KHAMCHINHPHU;
			_chedogoikham = data_ar[0].CHEDOGOIKHAM;
			_hienthigoikham = data_ar[0].HIENTHIGOIKHAM;
			_vssid = data_ar[0].VSSID;

			NGT_CALLBN_SMARTADS = data_ar[0].NGT_CALLBN_SMARTADS;
			NGT_CHECK_TG_KHAM_CLS = data_ar[0].NGT_CHECK_TG_KHAM_CLS; //L2PT-43979
			NGT_LOAI_KT_DONGY =  data_ar[0].NGT_LOAI_KT_DONGY;  //L2PT-45780

			if(data_ar[0].HIENTHICALL5 == "1"){
				$("#dvCall5").show();
			}else{
				$("#dvCall5").hide();
			}

			_modeGoiKham = typeof data_ar[0].MODEGOIKHAM == 'undefined' ? "0" : data_ar[0].MODEGOIKHAM;
			_timeOutGoiKham = typeof data_ar[0].TIMEOUTGOIKHAM == 'undefined' ? "5000" : data_ar[0].TIMEOUTGOIKHAM;

			if(data_ar[0].CANBOUUTIEN == "1"){
				$("#dvDOITUONGBENHNHANTEXT").show();
			}

			// CHE DO GOI BENH NHAN TU BEN NGOAI VAO PHONG KHAM;
			if(_chedogoikham == "1"){
				$("#dvGOIKHAMSTT").show();
				$('#toolbarIdbtnCall').hide();
			}else{
				$("#dvGOIKHAMSTT").hide();
				$('#toolbarIdbtnCall').show();
			}
			//tuyennx_edit_start_20190419 L1PT-560
			// khambenh 2 va  se an chuc nang nay di;
			if (_type == null ){
				//tuyennx_edit_end_20190419 L1PT-560
				$("#tabDieuTriTab").hide();
				$("#tabChamSocTab").hide();
				$("#toolbarIdbtnPhieuDT").hide();
				// $("#toolbarIdbtnTAOPHIEUCHAMSOC").hide();
				$("#idTabNgayGiuong").hide();
				// BVTM-7972
				if(cfObj.NGT_SD_PHIEUDT == "1"){
					$("#tabDieuTriTab").show();
				}else{
					$("#tabDieuTriTab").hide();
				}
			}else{
				$("#tabDieuTriTab").show();
				$("#tabChamSocTab").show();
				$("#idTabNgayGiuong").show();
				$("#toolbarIdbtnPhieuDT").show();
				// $("#toolbarIdbtnTAOPHIEUCHAMSOC").show();
			}

			if(_type != 1){
				$("#sttDiv").css("display","none");
				$("#timkiemDiv").css("display","none");
				$("#radTrangthai").css("display","");
			}

			if (data_ar[0].BBHC == "1"){
				$("#tabHoiChuan").show();
				$("#tabHoiChuanTab").show();
				$("#tabHoiChuan").css("display", "");
			}
			//L2PT-54954
			if (data_ar[0].NGT_MH2_LSDIEUTRI == "1"){
				$("#tabLSDieuTri").show();
				$("#tabLSDieuTriTab").show();
			}
			//L2PT-61755
			if (cfObj.NGT_TABTRUYENDICH == "1"){
				$("#tabPhieuTruyenDich").show();
				$("#tabPhieuTruyenDichTab").show();
			}

			_tinhngaycapcuu = typeof data_ar[0].TINHNGAYCAPCUU == 'undefined' ? "2" : data_ar[0].TINHNGAYCAPCUU;
			_bnvpinhathuoc = typeof data_ar[0].BNVPINHATHUOC == 'undefined' ? "0" : data_ar[0].BNVPINHATHUOC;
			_pkbvvcapcuu = typeof data_ar[0].PKBVVCAPCUU == 'undefined' ? "0" : data_ar[0].PKBVVCAPCUU;
			_kbatbuocBenhChinh = typeof data_ar[0].KBATBUOCBENHCHINH == 'undefined' ? "0" : data_ar[0].KBATBUOCBENHCHINH;
			_batbuoccddv = typeof data_ar[0].BATBUOCCDDV == 'undefined' ? "0" : data_ar[0].BATBUOCCDDV;
			_batbuoccdbd = typeof data_ar[0].BATBUOCCDBD == 'undefined' ? "0" : data_ar[0].BATBUOCCDBD;

			_canhbaobdkham = typeof data_ar[0].CANHBAOBDKHAM == 'undefined' ? "0" : data_ar[0].CANHBAOBDKHAM;
			_inphieuxutrikb = typeof data_ar[0].INPHIEUXUTRIKB == 'undefined' ? "0" : data_ar[0].INPHIEUXUTRIKB;
			_doicongkhamkb = typeof data_ar[0].DOICONGKHAMKB == 'undefined' ? "-1" : data_ar[0].DOICONGKHAMKB;
			_chedolcd = typeof data_ar[0].CHEDOLCD == 'undefined' ? "0" : data_ar[0].CHEDOLCD;
			_hienthikqdt = typeof data_ar[0].NHAPKQDT == 'undefined' ? "0" : data_ar[0].NHAPKQDT;
			_mabenhanndh =  typeof data_ar[0].MABENHANNDH == 'undefined' ? "0" : data_ar[0].MABENHANNDH;

			_mau_conthuoc = typeof data_ar[0].MAUCONTHUOC == 'undefined'
			|| data_ar[0].MAUCONTHUOC == null
			|| data_ar[0].MAUCONTHUOC == 'null'
			|| data_ar[0].MAUCONTHUOC == '' ? "0" : data_ar[0].MAUCONTHUOC;
			_mau_chuyenkhamngt = typeof data_ar[0].MAUCHUYENKHAMNGT == 'undefined'
			|| data_ar[0].MAUCHUYENKHAMNGT == null
			|| data_ar[0].MAUCHUYENKHAMNGT == 'null'
			|| data_ar[0].MAUCHUYENKHAMNGT == '' ? "orange" : data_ar[0].MAUCHUYENKHAMNGT;
			mauconthuoc_bnthuong = cfObj.NGT_MAUCONTHUOC_KB2;
			mauconthuoc_bnuutien = cfObj.NGT_MAUCONTHUOC_UUTIEN_KB2;
			if (_mabenhanndh == "1"){
				$("#divMABENHANDAINGAY").show();
			}

			if(_hienthikqdt != "0"){
				$("#divKETQUADIEUTRI").show();
				_batbuockqdt = typeof data_ar[0].NHAPKQDTBB == 'undefined' ? "0" : data_ar[0].NHAPKQDTBB;
			}else{
				$("#divKETQUADIEUTRI").hide();
				_batbuockqdt = "0";
			}
			sqlloadbadn =  NGT_LOADBADN_ALLPHONG == '1' ? "BAN.DAINGAY.DS2" : "BAN.DAINGAY.DS";
			ComboUtil.getComboTag("cboKETQUADIEUTRIID","NT.0010",[{"name":"[0]","value":"6"}], "", {value:'0',text:'--Chọn--'},"sql","",function(){
				var _kqdt = "0";
				if(_hienthikqdt != "0"){
					_kqdt = $('#cboXUTRIKHAMBENHID option:selected').attr('extval0');
					if(_kqdt == '-1' ||  _kqdt == undefined || typeof _kqdt == 'undefined'){
						// set kqdt theo cau hinh
						_kqdt = cfObj.NGT_GIATRI_KETQUA_DIEUTRI;
					}
				}else{
					// set kqdt theo cau hinh
					_kqdt = cfObj.NGT_GIATRI_KETQUA_DIEUTRI;
				}
				if (_kqdt != 0){
					$("#cboKETQUADIEUTRIID").val(_kqdt);
				}
			});

			if (_doicongkhamkb != "-1"){
				$("#toolbarIdbtnDOICONGKHAM").show();
			}else{
				$("#toolbarIdbtnDOICONGKHAM").hide();
			}

		}
		if (cfObj.HIS_SHOW_TAB_UX2023 == "1"){
			$('#tabXetNghiemTab').hide();
			$('#tabCDHATab').hide();
			$('#tabChuyenKhoaTab').hide();
			$('#tabThuocTab').hide();
			$('#tabDieuTriTab').hide();
			$('#idTabNgayGiuong').hide();
			$('#tabVatTuTab').hide();
			$('#tabPhieuVanChuyenTab').hide();
			$('#tabBADTNGTTab').hide();
			$('#tabHoiChuanTab').hide();
			$('#tabDieuTriTab').hide();
			$('#tabChamSocTab').hide();
			$('#tabPhieuThuKhacTab').hide();
		}

		var sql_par=[];
		sql_par.push({"name":"[0]","value": 87});
		ComboUtil.getComboTag("cboTRANGTHAI",_SQL[5],sql_par, "49",{value:0, text:'Tất cả'},"sql","","");

		$('#lblKHOA').text(_opt.dept_name);
		$('#lblPHONG').text(_opt.subdept_name);
		$("#hidHisId").val(opt.hospital_id);
		$("#hidDbSchema").val(opt.db_schema);
		$("#hidUserID").val(opt.user_id);
		$('#toolbarIdbtnKTKH').css('width','120px');
		$("#hidPHONGID").val(opt.phongid);
		$("#toolbarIdgroup_0_4").addClass("disabled");
		$("#toolbarIdgroup_0_5").addClass("disabled");

		var dt = cfObj.NGT_KB2_ENTER_TKBN;
		if(dt == '1'){
			if(_type == 1){
				GridUtil.init(_gridId,"100%","318px","",false,this._gridHeader,false, { rowNum: 100,rowList: [100, 200, 300]}, "", true);
			}else{
				GridUtil.init(_gridId,"100%","353px","",false,this._gridHeader,false, { rowNum: 100,rowList: [100, 200, 300]}, "", true);
			}
		}else{
			if(_type == 1){
				GridUtil.init(_gridId,"100%","318px","",false,this._gridHeader,false, { rowNum: 100,rowList: [100, 200, 300]});
			}else{
				GridUtil.init(_gridId,"100%","353px","",false,this._gridHeader,false, { rowNum: 100,rowList: [100, 200, 300]});
			}
		}

		var trangthai_load = cfObj.NGT_TRANGTHAI_BN_LOAD;
		$("#cboTRANGTHAI").val(trangthai_load);
		var $radios = $('input:radio[name=tabChoKham]');
		$radios.filter('[value='+trangthai_load+']').prop('checked', true);

		_loadGridData(_opt.phongid);
		_setButton(true);

		//tuyennx_add_start_20170724 check an hien nut sua ghi chu benh chinh
		var NGT_GHICHU_BENHCHINH = cfObj.NGT_GHICHU_BENHCHINH;
		if(NGT_GHICHU_BENHCHINH=='1'){
			$("#divBc").addClass("col-md-7");
			$('#divSuaBc').css('display','');
		}
		//tuyennx_add_end_20170724

		//tuyennx_add_start_20200407 L2PT-19293
		if(cfObj.NGT_ANNUTLUU_KB2 == '1')
			$("#toolbarIdbtnLuu").addClass("hidden");
		//tuyennx_add_end_20200407

		NGT_HIENTHIMAU_CAPCUU = cfObj.NGT_HIENTHIMAU_CAPCUU; //L2PT-27662

		if(NGT_HIENTHIMAU_CAPCUU != "" && NGT_HIENTHIMAU_CAPCUU != "0"){
			$("#spGhichuCapCuu").css("color",NGT_HIENTHIMAU_CAPCUU);
		}

		NGT_HIENTHIMAU_DATLICH = cfObj.NGT_HIENTHIMAU_DATLICH;
		if(NGT_HIENTHIMAU_DATLICH != "" && NGT_HIENTHIMAU_DATLICH != "0"){
			$("#spGhichuDatLich").css("color",NGT_HIENTHIMAU_DATLICH);
		}
		//dannd_L2PT-22060
		if(cfObj.NGT_BAOTINCHOKB2_SHOW == '1'){
			$("#dvBAOTINCHO").show();
		}
		if (cfObj.NGT_TIMKIEMXUTRI_KBMHC == "1"){
			$('#dvXUTRIKHAMBENH').show();
			$('#dvXUTRIKHAMBENH1').show();
		}else{
			$('#dvXUTRIKHAMBENH').hide();
			$('#dvXUTRIKHAMBENH1').hide();
		}
		if(cfObj.QD_4750 == '1'){
			$("#dvPPDIEUTRI").show();
			$("#lblCANNANG").addClass("required")
		}
		if(cfObj.NGT_FIXED_TOOLBAR == '1'){
			$("#toolbarId").css('position', 'fixed');
			$("#toolbarId").css('z-index', 2);
			$("#toolbarId").css('background', '#FFF');
			$("#dvTab").css('position', 'fixed');
			$("#dvTab").css('z-index', 1);
			$("#dvTab").css('margin-top', '40px');
			$("#dvTab").css('background', '#FFF');
			$("#dvTHONGTIN").css('margin-top', '85px');
		}
		NGT_DSKHAM_PHANBIETMAU = cfObj.NGT_DSKHAM_PHANBIETMAU;
		_bindEvent();
		_disableControl(['toolbarIdbtnCall', 'toolbarIdbtnLuu', 'toolbarIdbtnExam', 'toolbarIdbtnService','toolbarIdbtndrug',
			'toolbarIdbtnPhieuKham','toolbarIdbtnKHAC','toolbarIdbtnKTKH','toolbarIdbtn_TNTT','toolbarIdbtnKTKHVS2'], false);
		if(_disableTTKB == 1 || _disableTTKB == 2){
			$("#divKHAMBENH :input").attr("disabled", true);
			if (_disableTTKB == 2){
				$("#cboBACSIID").attr("disabled", false);
			}
		}
		_disableBacsy = cfObj.NGT_KB2_DISABLE_BACSY;
		if(_disableBacsy == 1) {
			$("#cboBACSIID").attr("disabled", true);
		}
		var par_ctl = ['CONTEXT_MENU_KHAMBENH'];
		var ctl_par = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', par_ctl.join('$'));
		if(ctl_par != '0'){
			$( "#contextMenu" ).html(ctl_par);
		}
		htmlDlg = $('#dlgDONGBA_DN').html();
		try{
			var str_menu = jsonrpc.AjaxJson.ajaxCALL_SP_X('COM.CAUHINH.CLOB', 'NGT02K001_KB_MHC_MENU_KHAC');
			var _list = JSON.parse(str_menu);
			for (var i=0; i<_list.length; i++){
				var _itemMenu = _list[i];
				var _id = 'toolbarId_MENU_' + _itemMenu.ID;
				var _url = _itemMenu.ID;
				var _name = _itemMenu.NAME;
				$('<li data-external="{}" id="'+_id+'"><a href="#">&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-goikham"></i>&nbsp;&nbsp;'+_name+'</a></li>').insertAfter(
					$('#toolbarIdbtnKHAC').next('.dropdown-menu').children().first()
				);
				$("#"+_id).click(function() {
					var myVar = {
						khambenhId: $("#hidKHAMBENHID").val()
						, benhnhanId: $("#hidBENHNHANID").val()
						, tiepnhanId: $("#hidTIEPNHANID").val()
						, mabenhnhan: $("#txtMABENHNHAN").val()
						, hosobenhanId: $("#hidHOSOBENHANID").val()
						, loaibenhanId: $("#hidLOAIBENHANID").val()
						, phongId: $("#hidPHONGID").val()
						, phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
						//loaikskid : '4',
						//maloaiksk : 'KSKT18'
					};
					var _width = $( document ).width()-150;
					var _height	 = $( window ).height()-50;
					dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../ksk/"+_url,myVar,_name,_width,_height);
					DlgUtil.open("divDlgBenhAnDetail");
				});
			}
		}catch(ex){

		}
		//L2PT-45300
		THEM_MENU();

		//hungnd - L2PT-62585
		_setButtonDangKham(true);
	}

	//L2PT-45300
	function THEM_MENU(){
		try{
			var str_menu = jsonrpc.AjaxJson.ajaxCALL_SP_X('COM.CAUHINH.CLOB', 'NGT02K001_KB_MHC_VS2_MENU');
			if (str_menu != '0'){
				var _list = JSON.parse(str_menu);
				for (var i=0; i<_list.length; i++){
					var _itemMenu = _list[i];
					var _id = 'toolbarId_createAuto' + i;
					var _name = _itemMenu.NAME;
					var _strMenu =
						'<input type="hidden" id="Data_' + _id + '" value="" />'
						+ '<li data-external="{}" id="'+_id+'"><a href="#">&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-goikham"></i>&nbsp;&nbsp;'+_name+'</a></li>';

					$(_strMenu).insertAfter(//toolbarIdbtnKHAC
						$('#'+_itemMenu.PARENT).next('.dropdown-menu').children().last()
					);

					$('#Data_' + _id).val( JSON.stringify(_itemMenu) );

					$("#"+_id).click(function() {
						if (!$("#hidKHAMBENHID").val() || $("#hidKHAMBENHID").val()=='' || $("#hidKHAMBENHID").val()=='0' || $("#hidKHAMBENHID").val()=='-1') {
							DlgUtil.showMsg("Chưa chọn bệnh nhân");
							return;
						}

						var _itemStr = $('#Data_' + $(this).attr('id')).val();
						var _item = JSON.parse(_itemStr);

						if (_item.TYPE == 'POPUP'){
							var name = {
								KHAMBENHID : $("#hidKHAMBENHID").val()
								, BENHNHANID : $("#hidBENHNHANID").val()
								, TIEPNHANID : $("#hidTIEPNHANID").val()
								, MABENHNHAN : $("#hidMABENHNHAN").val()
								, HOSOBENHANID : $("#hidHOSOBENHANID").val()
								, DOITUONGBENHNHANID : $("#hidDOITUONGBENHNHANID").val()
								, LOAITIEPNHANID : $("#hidLOAITIEPNHANID").val()
								, KHOAID: _opt.khoaid
								, PHONGID: _opt.phongid
								, LOAI_PHIEU : "NGT_KHAMBENH2"  //dùng cho form Thêm phiếu
								//biến Biên bản hội chẩn
								, khambenh_id : $("#hidKHAMBENHID").val()
								, maubenhpham_id : ""
								, benhnhanid : $("#hidBENHNHANID").val()
								, hosobenhanid : $("#hidHOSOBENHANID").val()

							};
							dlgPopup = DlgUtil.buildPopupUrl("divDlgPopup", "divDlg", "manager.jsp?func=../" + _item.URL, name, _item.NAME
								, window.innerWidth * 0.90, window.innerHeight * 0.90);
							DlgUtil.open("divDlgPopup");
						}
						else if (_item.TYPE == 'NOPOPUP'){  //    noitru/NTU02D170_FORMPHIEU&showMode=dlg
							var name = _item.CHUCNANG + '@' +  $("#hidKHAMBENHID").val() + '@' + $("#hidTRANGTHAIKHAMBENH").val() ;
							var dlgWindow = window.open('manager.jsp?func=../' + _item.URL , name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
								screen.height + ',width=' + screen.width);
							dlgWindow.moveTo(0, 0);
						}
						else if (_item.TYPE == 'DOC' || _item.TYPE == 'PDF'){  //
							var par = [
								{ name : 'hosobenhanid', type : 'String', value : $("#hidHOSOBENHANID").val() }
								, { name : 'khambenhid', type : 'String', value : $("#hidKHAMBENHID").val() }
								, { name : 'mabenhnhan', type : 'String', value : $("#hidMABENHNHAN").val() }
								, { name : 'loaibenhanid', type : 'String', value : $("#hidLOAIBENHANID").val() }
								, { name : 'benhnhanid', type : 'String', value : $("#hidBENHNHANID").val() }
								, { name : 'tiepnhanid', type : 'String', value : $("#hidTIEPNHANID").val() }
								, { name : 'phongid', type : 'String', value : $("#hidPHONGID").val() }
								, { name : 'khoaid', type : 'String', value : _opt.khoaid }
								, { name : 'i_hid', type : 'String', value : $("#hidHisId").val() }
								, { name : 'i_sch', type : 'String', value : opt.db_schema }
								, { name : 'i_hosobenhanid', type : 'String', value : $("#hidHOSOBENHANID").val() }
								, { name : 'i_khambenhid', type : 'String', value : $("#hidKHAMBENHID").val() }
								, { name : 'i_mabenhnhan', type : 'String', value : $("#hidMABENHNHAN").val() }
								, { name : 'i_loaibenhanid', type : 'String', value : $("#hidLOAIBENHANID").val() }
								, { name : 'i_benhnhanid', type : 'String', value : $("#hidBENHNHANID").val() }
								, { name : 'i_tiepnhanid', type : 'String', value : $("#hidTIEPNHANID").val() }
								, { name : 'i_phongid', type : 'String', value : $("#hidPHONGID").val() }
								, { name : 'i_khoaid', type : 'String', value : _opt.khoaid }
								, { name : 'RPT_CODE', type : 'String', value : _item.RPT_CODE }  // cho in Emr
							];

							if (_item.TYPE == 'DOC'){
								var rpName = _item.RPT_CODE + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
								CommonUtil.inPhieu('window', _item.RPT_CODE, 'docx', par, rpName);
							}
							else if (_item.TYPE == 'PDF'){
								openReport('window', _item.RPT_CODE, "pdf", par);
							}
							else if (_item.TYPE == 'EMR'){
								CommonUtil.openReportEmr(par, false);
							}
						}

					});
				}
			}
		}catch(ex){}
	}


	function _disableControl(arrID, value){
		for(var i = 0; i < arrID.length; i++){
			$("#"+arrID[i]).attr("disabled", value);
		}
	}
	function _bindEvent() {
		$.jMaskGlobals = {
			maskElements: 'input,td,span,div',
			dataMaskAttr: '*[data-mask]',
			dataMask: true,
			watchInterval: 300,
			watchInputs: true,
			watchDataMask: true,
			byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
			translation: {
				'0': {pattern: /\d/},
				'9': {pattern: /\d/, optional: true},
				'#': {pattern: /\d/, recursive: true},
				'A': {pattern: /[a-zA-Z0-9]/},
				'S': {pattern: /[a-zA-Z]/}
			}
		};
		var k = 75 ;
		var g = 71 ;
		var h = 72;
		var down = 40 ;
		var up = 38 ;
		var enter = 13 ;
		var esc = 27  ;
		$(document).ready(function(){
			$(document).bind('keydown',function(e){
				if(e.keyCode == k && e.altKey){
					$("#toolbarIdbtnExam").trigger('click');
				}
				if(e.keyCode == g && e.altKey){
					$("#gs_SOTHUTU").focus();
				}
				if(e.keyCode == h && e.altKey){
					$("#gs_TENBENHNHAN").focus();
				}
				if (cfObj.NGT_PHIMTAT_KB2 == 1) {
					if(e.keyCode == down){
						if (e.target.localName == 'body' || e.target.localName == 'tr') {
							if (selectIndex < _leghtList){
								selectIndex++;
								var focused = $(':focus');
								focused.blur();
								e.preventDefault();
								GridUtil.unmarkAll(_gridId);
								GridUtil.markRow(_gridId, selectIndex.toString());
							}
						}
					}
					if(e.keyCode == up){
						if (e.target.localName == 'body' || e.target.localName == 'tr') {
							if (selectIndex > 1){
								selectIndex--;
								var focused = $(':focus');
								focused.blur();
								e.preventDefault();
								GridUtil.unmarkAll(_gridId);
								GridUtil.markRow(_gridId, selectIndex.toString());
							}
						}
					}
					if(e.keyCode == enter){
						if (e.target.localName == 'body' || e.target.localName == 'tr') {
							var focused = $(':focus');
							focused.blur();
							e.preventDefault();
							$("#cboXUTRIKHAMBENHID").val(0);
							_selectedRow(selectIndex.toString());
							$("#toolbarIdbtnLS").attr("disabled", false);
							_calBMI();
							_loadFirstICD = true;
							_loadFirstICDYHCT = true;
							$("#txtMACHANDOANRAVIEN").focus();
						}
					}
				}
				if(e.keyCode == esc){
					$("#txtMACHANDOANRAVIEN").blur();
				}
			});
		});
		$("#toolbarIdgroup_0_Ketqua_ngoaitru").on("click", function() {
			var _hosobenhanid = $('#hidHOSOBENHANID').val();
			if (_hosobenhanid != null && _hosobenhanid != -1) {
				var par = [ {
					name : 'i_khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}];
				openReport('window', "PHIEUIN_KETQUA_NGOAITRU_25266", "pdf", par);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});

		$("#toolbarIdgroup_0_2_doc").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy chuyển viện.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 7){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];

			if(_opt.hospital_id == "965"){					// DA KHOA BUU DIEN IN DOCX
				var rpName= "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
				CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'docx', par, rpName);
			}
			/*else if(_opt.hospital_id == "1111"){					// DK LAN: Ham khac;
				var par1111 = [ {
					name : 'phongkhamdangkyid',
					type : 'String',
					value : $("#hidPHONGKHAMDANGKYID").val()
				}];
//				openReport('window', "NGT003_GIAYCHUYENTUYEN_LAN_1111_2", "pdf", par1111);
				var rpName= "NGT003_GIAYCHUYENTUYEN_LAN_1111_2" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
				CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_LAN_1111_2", 'docx', par, rpName);

			}*/
			else{
//				openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
				var rpName= "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
				CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'docx', par, rpName);
			}
		});

		// ttlinh in phieu kham chuyen khoa
		$("#toolbarIdgroup_0_PKchuyenkhoa").on("click", function() {

			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu khám chuyên khoa.');
				return;
			}

			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()
			}, {
				name : 'i_phongids',
				type : 'String',
				value : $('#hidPHONGID').val()
			}, {
				name : 'i_pkdkids',
				type : 'String',
				value : $('#hidPHONGKHAMDANGKYID').val()
			}];
			if(opt.hospital_id == 10284){
				paramInput={
					khambenhid : $('#hidKHAMBENHID').val(),
					phongkhamdangkyid : $('#hidPHONGKHAMDANGKYID').val()
				};
				dlgPopup=DlgUtil.buildPopupUrl("dlgKhamCK","divDlg","manager.jsp?func=../ngoaitru/NGT03K005_KhamChuyenKhoa1",paramInput
					,"Khám chuyên khoa",900,450);
				DlgUtil.open("dlgKhamCK");
			}else {
				openReport('window', "RPT_PHIEUKHAMCHUYENKHOA_A4_965", 'pdf', par);
			}
		});

		$("#toolbarIdgroup_0_XNCovid").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu XN Covid.');
				return;
			}

			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()
			}];
			openReport('window', "RPT_GIAYXACNHAN_TIEMCHUNG_VACXIN", 'pdf', par);
		});
		// chinh huong cho thong tin kham benh
		$(':input').keydown(function (e) {
			var id = $(this).attr('id');
			if (e.which === 13 || e.which === 9) {				// tab hoac enter
				//tuyennx_edit_start_20190425 L1PT-654
				if (id == "txtKHAMBENH_MACH"){
					$("#txtKHAMBENH_CHIEUCAO").focus();
				}else if(id=="txtKHAMBENH_HUYETAP_HIGH"){
					$("#txtKHAMBENH_HUYETAP_LOW").focus();
				}else if(id=="txtKHAMBENH_HUYETAP_LOW"){
					$("#txtKHAMBENH_MACH").focus();
				}
				else if(id=="txtKHAMBENH_CHIEUCAO"){
					$("#txtKHAMBENH_NHIETDO").focus();
				}
				else if(id=="txtKHAMBENH_NHIETDO"){
					$("#txtKHAMBENH_NHIPTHO").focus();
				}
				else if(id=="txtKHAMBENH_NHIPTHO"){
					$("#txtKHAMBENH_CANNANG").focus();
				}
				else if(id=="txtKHAMBENH_CANNANG"){
					$("#txtSPO2").focus();
				}
				else if(id=="txtSPO2"){
					$("#txtQUATRINHBENHLY").focus();
				}
				else if(id=="txtQUATRINHBENHLY" && e.which !== 13){
					$("#txtCHANDOANBANDAU").focus();
				}
				else if(id=="txtCHANDOANBANDAU"){
					$("#txtMACHANDOANRAVIEN").focus();
				}
				else if(id=="txtMACHANDOANRAVIEN"){
					$("#txtGHICHU_BENHCHINH").focus();
				}
				else if(id=="txtGHICHU_BENHCHINH"){
					$("#txtMACHANDOANRAVIEN_KEMTHEO").focus();
				}
				else if(id=="txtMACHANDOANRAVIEN_KEMTHEO"){
					$("#btnEDITBP").focus();
				}
				else if(id=="btnEDITBP" && e.which !== 13){
					$("#txtGHICHU_KHAMBENH").focus();
				}
				else if(id=="txtGHICHU_KHAMBENH"){
					$("#cboXUTRIKHAMBENHID").focus();
				}
				else if(id=="cboXUTRIKHAMBENHID"){
					$("#cboKETQUADIEUTRIID").focus();
				}
				else if(id=="cboKETQUADIEUTRIID"){
					$("#cboBACSIID").focus();
				}
				else if(id=="cboBACSIID"){
					$("#toolbarIdbtnLuu").focus();
				}
				//tuyennx_edit_end_20190425 L1PT-654


				if (e.which === 9 && id !="txtMACHANDOANRAVIEN") {						// Tab
					e.preventDefault(); 					// chan cac event ung voi phim Tab
				}

			}
			if (e.shiftKey && e.which == 9  ) {				// shift + tab
				//tuyennx_edit_start_20190425 L1PT-654
				if(id=="toolbarIdbtnLuu"){
					$("#cboBACSIID").focus();
				}else if(id=="cboBACSIID"){
					$("#cboKETQUADIEUTRIID").focus();
				}else if(id=="cboKETQUADIEUTRIID"){
					$("#cboXUTRIKHAMBENHID").focus();
				}else if(id=="cboXUTRIKHAMBENHID"){
					$("#txtGHICHU_KHAMBENH").focus();
				}else if(id=="txtGHICHU_KHAMBENH"){
					$("#btnEDITBP").focus();
				}else if(id=="btnEDITBP"){
					$("#txtMACHANDOANRAVIEN_KEMTHEO").focus();
				}else if(id=="txtMACHANDOANRAVIEN_KEMTHEO"){
					$("#txtGHICHU_BENHCHINH").focus();
				} else if(id=="txtGHICHU_BENHCHINH"){
					$("#txtMACHANDOANRAVIEN").focus();
				}else if(id=="txtMACHANDOANRAVIEN"){
					$("#txtCHANDOANBANDAU").focus();
				}else if(id=="txtCHANDOANBANDAU"){
					$("#txtQUATRINHBENHLY").focus();
				}else if(id=="txtQUATRINHBENHLY"){
					$("#txtSPO2").focus();
				} else if(id=="txtSPO2"){
					$("#txtKHAMBENH_CANNANG").focus();
				}else if(id=="txtKHAMBENH_CANNANG"){
					$("#txtKHAMBENH_NHIPTHO").focus();
				}else if(id=="txtKHAMBENH_NHIPTHO"){
					$("#txtKHAMBENH_NHIETDO").focus();
				}else if(id=="txtKHAMBENH_NHIETDO"){
					$("#txtKHAMBENH_CHIEUCAO").focus();
				}else if(id=="txtKHAMBENH_CHIEUCAO"){
					$("#txtKHAMBENH_MACH").focus();
				}else if(id=="txtKHAMBENH_MACH"){
					$("#txtKHAMBENH_HUYETAP_LOW").focus();
				}else if(id=="txtKHAMBENH_HUYETAP_LOW"){
					$("#txtKHAMBENH_HUYETAP_HIGH").focus();
				}
				if (e.which === 9 && id !="txtMACHANDOANRAVIEN") {						// Tab
					e.preventDefault(); 					// chan cac event ung voi phim Tab
				}
			}
		});

		// click button tim kiem
		$("#toolbarIdbtnSearchDate").on("click", function() {
			_loadTabHanhChinh(-1);
			_setButton(true);
			_loadGridData(_opt.phongid);
		});

		$('#cboXUTRIID').on('change', function (e) {
			_loadGridData(_opt.phongid);
		});
		// click view anh lon
		$("#imgBN").on("click",function(e){
			var paramInput = {
				url : $('#imgBN').attr('src')
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgTTA","divDlg","manager.jsp?func=../ngoaitru/NGT02K059_show_img",paramInput,'THÔNG TIN ẢNH', 650,540);
			DlgUtil.open("dlgTTA");
		});

		$('#txtMABENHNHANTK').change(function(){
			if($(this).val().length > 9 || $(this).val().length <= 0){
				_loadGridData(_opt.phongid);
			}
		});

		$('#txtMABENHNHANTK2').change(function(){
			$('#txtMABENHNHANTK').val($('#txtMABENHNHANTK2').val());
			if($('#txtMABENHNHANTK').val().length > 9 || $('#txtMABENHNHANTK').val().length <= 0){
				_loadGridData(_opt.phongid);
			}
		});

		$("#btnDOIBACSI").on("click", function() {
			paramInput={
				ssid: ssid
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgBacSyChiDinh","divDlg","manager.jsp?func=../noitru/NTU01H046_ChonBacsi",paramInput,"Gán bác sỹ",600,230);

			DlgUtil.open("divDlgBacSyChiDinh");
		});

		EventUtil.setEvent("assignSevice_saveChangeBSDT", function(e) {
			DlgUtil.showMsg(e.msg);
			$('#labelBACSIDIEUTRIMS').html(e.name);
			DlgUtil.close(e.divId);

			_loadGridData(_opt.phongid);
		});

		// click thay doi trang thai tim kiem
		$('#cboTRANGTHAI').change(function(){
			_loadGridData(_opt.phongid);
		});

		$('#toolbarIdtxtFromDate').change(function(){
			if (cfObj.NGT_CHONNGAY_AUTOSEARCH == "1") {
				_loadGridData(_opt.phongid);
			}
		});

		$('#toolbarIdtxtToDate').change(function(){
			if (cfObj.NGT_CHONNGAY_AUTOSEARCH == "1") {
				_loadGridData(_opt.phongid);
			}
		});
		//change combo xu tri
		$('#cboXUTRIKHAMBENHID').change(function(){
			var value = $(this).val();
			$("#divKhoa").css("display","none");
			// ngocnva L2PT-8362 start
			var sysdate = value != 0 ? jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS') : '';
			$('#txtTHOIGIANRAVIEN').val(sysdate);
			// ngocnva L2PT-8362 end
			_changexutri(value);
		});

		// click thay doi tu ngay
		$('#toolbarIdtxtFromDate').change(function(){
			var from = $('#toolbarIdtxtFromDate').val().substr(6,4) + $('#toolbarIdtxtFromDate').val().substr(3,2) + $('#toolbarIdtxtFromDate').val().substr(0,2)
				+ $('#toolbarIdtxtFromDate').val().substr(11,2) + $('#toolbarIdtxtFromDate').val().toString().substr(14,2);

			if(from < sdate && _type != "1" && _type != "2"){
				$("#toolbarId button").addClass('disabled');
				$("#toolbarIdbtnPrint").removeClass('disabled');
			}else{
				$("#toolbarId button").removeClass('disabled');
			}
		});

		// click chon benh nhan trong danh sach
		GridUtil.setGridParam(_gridId,{
			onSelectRow: function(index, selected) {
				selectIndex = index;
				if(selected === false){
					FormUtil.clearForm('divContentHC');
					//$("#toolbarIdbtnLS").attr("disabled", true);
					//$("#toolbarIdbtnLuu").attr("disabled", true);
					//$("#cboXUTRIKHAMBENHID").attr("disabled", true);
				}else{
					$("#cboXUTRIKHAMBENHID").val(0);
					_selectedRow(index);
					$("#toolbarIdbtnLS").attr("disabled", false);
					selectIndex = index;
					//$("#toolbarIdbtnLuu").attr("disabled", false);
					//$("#cboXUTRIKHAMBENHID").attr("disabled", false);
					if(_disableTTKB == 1 || _disableTTKB == 2){
						$("#divKHAMBENH :input").attr("disabled", true);
						if (_disableTTKB == 2){
							$("#cboBACSIID").attr("disabled", false);
						}
					}
					if(_disableBacsy == 1) {
						$("#cboBACSIID").attr("disabled", true);
					}
				}
				_calBMI();
				GridUtil.unmarkAll(_gridId);
				if(_colorMHC == 1){
					GridUtil.markRow(_gridId,index,"markedRowVD2");
				}else if(_colorMHC == 2){
					GridUtil.markRow(_gridId,index, "markedRowBVBD");
				}else {
					GridUtil.markRow(_gridId,index);
				}
				_loadFirstICD = true;
	    		_loadFirstICDYHCT = true;
				//ductx -bvtm-5440
				var selRowId = $('#' + _gridId).jqGrid ('getGridParam', 'selrow');
				var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
				tenbn = rowData.TENBENHNHAN;
				var _ch_popuptitle = cfObj.HIS_POPUP_SHOWTTBN;
				if (_ch_popuptitle && _ch_popuptitle != 0) {
					var _ch_data = _ch_popuptitle.split(';');
					var data = [];
					for (var i = 0; i < _ch_data.length; i++) {
						var x = _ch_data[i];
						data.push(rowData[x]);
					}
					thongtinbn = data.join('/ ');
				}
				//end bvtm-5440
				if (rowData.hasOwnProperty('TONGTIENDICHVUCORE') && _colorMHC == 2){
					var _arrGiaDV = rowData.TONGTIENDICHVUCORE.split(';');
					if (_arrGiaDV.length > 1){
						rowData.TONGCP = _arrGiaDV[0];
						rowData.TAMUNG = _arrGiaDV[1];
						rowData.DANOP = _arrGiaDV[2];
						rowData.CONNO = _arrGiaDV[3];
						if(rowData.CONNO > 0){
							$("#"+ _gridId).setCell(selRowId,'TONGCP','',{'background-color':'#FF0000 !important'});
							$("#"+ _gridId).setCell(selRowId,'TAMUNG','',{'background-color':'#FF0000 !important'});
							$("#"+ _gridId).setCell(selRowId,'CONNO','',{'background-color':'#FF0000 !important'});
						}
					}
				}
				var _kqdt = cfObj.NGT_GIATRI_KETQUA_DIEUTRI;
				if (_kqdt != 0){
					$("#cboKETQUADIEUTRIID").val(_kqdt);
				}
			},
			//tuyenn_add_start_20181102 L2HOTRO-11553
			ondblClickRow: function(id) {
				var selRowId = $('#' + _gridId).jqGrid ('getGridParam', 'selrow');
				var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
				var _trangthaikhambenh = rowData.TRANGTHAIKHAMBENH;
				if(_trangthaikhambenh == "1"){
					//HaNv_130123: L2PT-33195
					if (cfObj.KB_MHC2_NO_DBLCLICK == "1"){
						return;
					}
					_start_kham(0, null);
				}else {
					if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == "1"){
						var paramInput = {
							khambenhid: $("#hidKHAMBENHID").val(),
							tiepnhanid: $("#hidTIEPNHANID").val(),
							hosobenhanid: $("#hidHOSOBENHANID").val(),
							mabenhnhan: $('#txtMABENHNHAN').val(),
							tenbenhnhan: $('#txtTENBENHNHAN').val(),
							//tuoi: _row.TUOI,
							diachi: $('#txtDIACHI').val()
						}
						dlgPopup = DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../noitru/NTU01H101_DayLaiBenhAn", paramInput, "HIS - Đẩy hồ sơ bệnh án điện tử", 1200, 650);
						DlgUtil.open("divDlgBA");
					}
				}
			},
			//tuyenn_add_end_20181102
			gridComplete: function(id){
				if(cfObj.NGT_KB_CAPNHAPTTHC != "1"){
					$("#liUpdateTTHC").remove();
				}
				if(cfObj.NGT_HUYNHAPVIEN_HIENTHI == 0 ){
					$("#xoabnchonhapvien").remove();
				}
				$(".jqgrow", '#' + _gridId).contextMenu( 'contextMenu', {
					bindings: {
						'yeucaumolaibenhan': function (t) {
							_molaibenhan(0, "YÊU CẦU MỞ LẠI BỆNH ÁN");
						},
						'molaibenhan': function (t) {
							_molaibenhan(1, "MỞ LẠI BỆNH ÁN");
						},
						'goilaibnchuyenphong': function (t) {
							if(cfObj.NGT_HUYCK_THEOPHONG != 1){
								_goilaibnchuyenkham();
							} else {
								_huychuyenkhamtheophong();
							}
						},
						'xoabnchonhapvien': function (t) {
							var par = [];
							par.push({
								"name" : "[0]",
								"value" : $("#hidHOSOBENHANID").val()
							});
							var hosobenhanid_ntu = jsonrpc.AjaxJson.getOneValue("NGT.GET.KBIDNTU", par);
							if(hosobenhanid_ntu !== 'null' || hosobenhanid_ntu !== '0') {
								var myVar = {
									khambenhid :hosobenhanid_ntu,
									tiepnhanid : $("#hidTIEPNHANID").val()
								};
								var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H001.EV029", JSON.stringify(myVar));
								if (result == -1) {
									DlgUtil.showMsg("Xóa bệnh nhân lỗi!");
								} else {
									if (result == 2) {
										DlgUtil.showMsg("Xóa bệnh nhân không thành công do sai trạng thái!");
									} else if (result == 3) {
										DlgUtil.showMsg("Đã có chỉ định dịch vụ trong khoa nội trú không thể xóa!");
									}
									else if (result == 9) {
										DlgUtil.showMsg("Đã có phiếu khám bệnh vào viện được ký số, không thể xóa!");
									}
									else {
										DlgUtil.showMsg("Xóa bệnh nhân thành công!");
										_loadGridData(_opt.phongid);
									}
								}
							}else {
								DlgUtil.showMsg("Bệnh nhân không nhập viện không thể xóa!");
							}
						},
						'btnLS_1': function (t) {
							paramInput={
								benhnhanId : $("#hidBENHNHANID").val()
							};
							dlgPopup=DlgUtil.buildPopupUrl("dlgLichSuDieuTri","divDlg","manager.jsp?func=../ngoaitru/NGT02K025_LichSuDieuTri",paramInput,"LỊCH SỬ ĐIỀU TRỊ",1350,600);
							DlgUtil.open("dlgLichSuDieuTri");
						},
						'btnLS_2': function (t) {
							paramInput={
								benhnhanId : $("#hidBENHNHANID").val()
							};
							dlgPopup=DlgUtil.buildPopupUrl("dlgLichSuBA","divDlg","manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn",paramInput,"LỊCH SỬ BỆNH ÁN",1320,610);
							DlgUtil.open("dlgLichSuBA");
						},

						'btnLSCongBHYT': function (t) {
							var paramInput={
								MABHYT : $('#hidMABHYT').val(),
								TENBENHNHAN : $('#hidHOTENBNGOI').val(),
								NGAYSINH : $('#txtNGAYSINH').val(),
								QRCODE : '',
								TUNGAY : '',
								DENNGAY : ''
							};

							dlgPopup=DlgUtil.buildPopupUrl(
								"divDlgDDT","divDlg","manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB",
								paramInput,"Thông tin lịch sử điều trị bệnh nhân",window.innerWidth*0.95,window.innerHeight*0.95);

							DlgUtil.open("divDlgDDT");
						},
						//L2PT-61798
						'btnLSCongGW': function (t) {
							var paramInput={
								MABHYT : $('#hidMABHYT').val(),
								NGAYKHAM : $('#txtDENKHAMLUC').val(),
								MAKCBBD : opt.hospital_code															// MA DON VI DUNG CHECK ;
							};

							dlgPopup=DlgUtil.buildPopupUrl(
								"divDlgDDT","divDlg","manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB_GW",
								paramInput,"Thông tin lịch sử KCB Trong Tỉnh",window.innerWidth*0.95,window.innerHeight*0.93);

							var parent = DlgUtil.open("divDlgDDT");
						},

						'btnLSKCB': function (t) {
							var paramInput={
								MABHYT : $('#txtSOTHEBHYT').val(),
								TENBENHNHAN : $('#txtTENBENHNHAN').val(),
								NGAYSINH : $('#txtNGAYSINH').val(),
								QRCODE : '',
								TUNGAY : '',
								DENNGAY : ''
							};

							dlgPopup=DlgUtil.buildPopupUrl(
								"divDlgDDT","divDlg","manager.jsp?func=../ngoaitru/NGT02K049_TraCuuCongBYT",
								paramInput,"Tra cứu thông tin Bộ Y Tế",window.innerWidth*0.95,window.innerHeight*0.95);
							DlgUtil.open("divDlgDDT");
						},

						'thanhtoanvp': function (t) {
							paramInput={
								tiepnhanid : $("#hidTIEPNHANID").val()
							};
							dlgPopup=DlgUtil.buildPopupUrl("dlgTTVP","divDlg","manager.jsp?func=../vienphi/VPI01T006_thanhtoanvienphi",paramInput,"THANH TOÁN VIỆN PHÍ",1250,600);
							DlgUtil.open("dlgTTVP");
						},

						'capnhatdtngt': function (t) {
							DlgUtil.showConfirm("Bạn có muốn cập nhật BADTNGT? ", function(flag){
								if(flag){
									_capnhatBADTNGT();
								}
							});
						},

						'gocapnhatdtngt': function (t) {
							DlgUtil.showConfirm("Bạn có muốn gỡ cập nhật BADTNGT này? ", function(flag){
								if(flag){
									_gocapnhatBADTNGT("0");
								}
							});
						},

						'gocapnhatdtngtall': function (t) {
							DlgUtil.showConfirm("Bạn có muốn gỡ cập nhật cả đợt điều trị này? ", function(flag){
								if(flag){
									_gocapnhatBADTNGT("1");
								}
							});
						},

						'thietlapuutien': function (t) {
							DlgUtil.showConfirm("Bạn có muốn thiết lập ưu tiên cho BN này? ", function(flag){
								if (flag){
									var _obj = new Object();
									_obj.PHONGKHAMDANGKYID =  $("#hidPHONGKHAMDANGKYID").val();
									_obj.KHAMBENHID =  $("#hidKHAMBENHID").val();
									var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K001.TLUT", JSON.stringify(_obj));
									if (fl != '0' && fl != '-1'){
										DlgUtil.showMsg("Đã thiết lập ưu tiên thành công");
										_loadGridData(_opt.phongid);
									}else if (fl == '0'){
										DlgUtil.showMsg("Bệnh nhân đang là đối tượng ưu tiên. ");
									}else{
										DlgUtil.showMsg("Lỗi trong quá trình xử lý");
									}

								}
							});
						},

						'setngaytiepnhan': function (t) {
							// canh bao ngay tiep nhan <> ngay hien tai
							var _ngaytnBN = $("#hidNGAYTN").val(); 					// YYYYMMDDHH24MI
							if (_setngaytiepnhan == "1" && _ngaytnBN != "" && _ngaytnBN != null && _ngaytnBN.length >= 8){
								if (Number(_ngaytnBN.substring(0,8)) - Number(sdate) > 0 ){
									// chi khi thoi gian tiep nhan > thoi gian he thong, moi cho phep cap nhat lại;
									DlgUtil.showConfirm("Bạn muốn thiết lập lại ngày tiếp nhận cho BN này? ", function(flag){
										if (flag){
											paramInput={
												tiepnhanid : $("#hidTIEPNHANID").val(),
												ngaytiepnhan : $("#txtNGAYTIEPNHAN").val(),
												loai : "7"
											};
											dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K089_SUACHITIET",paramInput,"Cập nhật ngày tiếp nhận",600,400);
											DlgUtil.open("divDlgDichVu");

										}
									});
								}
							}
						},
						'liUpdateTTHC': function (t) {
							$("#"+_gridId).jqGrid('getRowData',t.id);
							var paramInput = {
								tiepnhanid: $("#hidTIEPNHANID").val(),
								khambenhid: $("#hidKHAMBENHID").val(),
								trangthaikhambenh : row.TRANGTHAIKHAMBENH
							};
							dlgPopup = DlgUtil.buildPopupUrl("divDlgSuaBenhNhan", "divDlg", "manager.jsp?func=../noitru/NTU01H020_ThongTinBenhNhan", paramInput, "HIS - Cập nhật bệnh nhân", 1100, 650);
							DlgUtil.open("divDlgSuaBenhNhan");
						}
					},
					onContextMenu: function (event, menu) {
						var rowId = $(event.target).parent("tr").attr("id");
						var grid = $('#' + _gridId);
						grid.setSelection(rowId);
						return true;
					}
				});

				var ids = $("#"+_gridId).getDataIDs();
				if(_colorMHC == 0){
					for(var i=0;i<ids.length;i++){
						var id = ids[i];
						var row = $("#"+_gridId).jqGrid('getRowData',id);
						var _icon = '';
						var _iconcls = '';

						// sondn L2PT-31163
						if (row.hasOwnProperty('TONGTIENDICHVUCORE')){
							var _arrGiaDV = row.TONGTIENDICHVUCORE.split(';');
							if (_arrGiaDV.length > 1){
//							r_tongtien_dv||';'||r_tamung||';'||r_danop||';'||r_chenhlech ;
								row.TONGCP = _arrGiaDV[0];
								row.TAMUNG = _arrGiaDV[1];
								row.DANOP = _arrGiaDV[2];
								row.CONNO = _arrGiaDV[3];
								$("#"+_gridId).jqGrid('setRowData', id, row);
							}
						}
						// end sondn L2PT-31163

						if(row.TRANGTHAIKHAMBENH == 1){
							if(row.DACODICHVUTHUTIEN == '1'){
								_icon = '<center><img src="'+ _opt.imgPath[0] +'" width="15px"></center>';
							}else{
								_icon = '<center><img src="'+ _opt.imgPath[5] +'" width="15px"></center>';
							}
						}else if(row.TRANGTHAIKHAMBENH == 4){
							_icon = '<center><img src="'+ _opt.imgPath[1] +'" width="15px"></center>';
						}else if(row.TRANGTHAIKHAMBENH == 9){
							_icon = '<center><img src="'+ _opt.imgPath[2] +'" width="15px"></center>';
						}

						if(row.KQCLS == "1"){
							_iconcls = '<center><img src="'+ _opt.imgPath[3] +'" width="15px"></center>';
						}else if(row.KQCLS == "2"){
							_iconcls = '<center><img src="'+ _opt.imgPath[4] +'" width="15px"></center>';
						}

						$("#"+_gridId).jqGrid ('setCell', id, 1, _icon);
						$("#"+_gridId).jqGrid ('setCell', id, 2, _iconcls);

						if(row.DOITUONGBENHNHANID == "1"){
							$('#'+_gridId).find("tr[id='" + id + "']").find('td').css('background-color','#ffcccc');
							if(row.KHAMCHINHPHU != "1"){
								$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'DIACHI','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'DIACHI','',{'font-style': 'italic'});
							}
						} else if(row.DOITUONGBENHNHANID != "1"){
							$('#'+_gridId).find("tr[id='" + id + "']").find('td').css('background-color','#ffffff');
							if(row.KHAMCHINHPHU != "1"){
								$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'DIACHI','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'DIACHI','',{'font-style': 'italic'});
							}
						}

						// BN UU TIEN;
						if(row.UUTIENKHAMID != "0"){
							//$("#"+_gridId).jqGrid('setRowData', id, "", {color : 'blue'});
							$('#'+_gridId).find("tr[id='" + id + "']").find('td').css('background-color','#b3ffb3');
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : 'blue'});
						}
						// BN CHUYEN PK;
						if (row.PHONGKHAMDANGKYID_CHA != "0"){
							$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'DIACHI','',{'font-weight':'bold'});
						}

						if(row.CHUYENKHAMNGT != "0"){
							// BN KHAM CHUYEN KHOA;
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : _mau_chuyenkhamngt});
						}else if(row.BADAINGAY != "0" && row.BADAINGAY != undefined){
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : '#ff09e1'});
						}else if(row.DIEUTRI_NGT != "0"){
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : 'green'});
						}
						if (_mau_chuyenkhamngt != "0" && _mau_chuyenkhamngt != null && _mau_chuyenkhamngt != "" &&  _mau_chuyenkhamngt != "orange") {
							if(parseInt(row.SOPHONGKHAM) > 1){
								$("#"+_gridId).jqGrid('setRowData', id, "", {color : _mau_chuyenkhamngt});
							}
						}
						// sondn L2PT-10468
						if (_mau_conthuoc != '0'){
							if(row.NGAYTHUOC != "" && row.NGAYTHUOC != null){
								var t_ngaythuoc = row.NGAYTHUOC;
								var ngay = t_ngaythuoc.split('/');
								var ngaythuoc = ngay[2].substring(0,4)+ngay[1]+ngay[0];							// 11/11/2021 10:20:00

								var t_ngaytn = row.NGAYTIEPNHAN; 													// 05/05/2021 14:33:41
								ngay = t_ngaytn.split('/');
								var ngaytn = ngay[2].substring(0,4)+ngay[1]+ngay[0];

								if(parseInt(ngaythuoc) > parseInt(ngaytn)){
									$("#"+_gridId).jqGrid('setRowData', id, "", {color : _mau_conthuoc});							// blue
								}
							}
						}
						// end sondn L2PT-10468

						//tuyennx_add_start_20200823 L2PT-26511
						if(NGT_DSKHAM_PHANBIETMAU == 1){
							var _sql_par = [];
							_sql_par.push({
								"name" : "[0]",
								value : row.SUB_DTBNID
							});
							var checkmau = jsonrpc.AjaxJson.getOneValue("NGT.HIENTHI.MAU", _sql_par)
							if(checkmau)
								$("#"+_gridId).jqGrid('setRowData', id, "", {color : checkmau});
						}
						//tuyennx_add_end_20200823 L2PT-26511
						if (mauconthuoc_bnuutien != "0" && mauconthuoc_bnthuong != "0"){
							if (row.NGAYTHUOC != "" && row.NGAYTHUOC != null) {
								var t_ngaythuoc = row.NGAYTHUOC;
								var ngay = t_ngaythuoc.split('/');
								var ngaythuoc = ngay[2].substring(0, 4) + ngay[1] + ngay[0];
								var ngaykham = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');
								if (parseInt(ngaythuoc) > parseInt(ngaykham)) {
									if(row.UUTIENKHAMID != "0"){
										$('#'+_gridId).find("tr[id='" + id + "']").find('td').css('background-color',mauconthuoc_bnuutien);
									}else {
										$('#'+_gridId).find("tr[id='" + id + "']").find('td').css('background-color',mauconthuoc_bnthuong);
									}
								}
							}
						}

						//tuyennx_add_start_20200823 L2PT-27662
						if(NGT_HIENTHIMAU_CAPCUU && NGT_HIENTHIMAU_CAPCUU != '0' && row.HINHTHUCVAOVIENID == 2){
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : NGT_HIENTHIMAU_CAPCUU});
						}
						//tuyennx_add_end_20200823 L2PT-27662

						if(row.KHAMBENHID == $("#hidKHAMBENHID").val() && row.TRANGTHAIKHAMBENH != 1){
							GridUtil.unmarkAll(_gridId);
							GridUtil.markRow(_gridId,id);
							$('#'+_gridId).find("tr[id='" + id + "']").find('td').trigger( "click" );
						}
					}
				}
				if(_colorMHC == 2){ // BĐ HNI
					for(var i=0;i<ids.length;i++){
						var id = ids[i];
						var row = $("#"+_gridId).jqGrid('getRowData',id);
						var _icon = '';
						var _iconcls = '';

						// sondn L2PT-31163
						if (row.hasOwnProperty('TONGTIENDICHVUCORE')){
							var _arrGiaDV = row.TONGTIENDICHVUCORE.split(';');
							if (_arrGiaDV.length > 1){
//							r_tongtien_dv||';'||r_tamung||';'||r_danop||';'||r_chenhlech ;
								row.TONGCP = _arrGiaDV[0];
								row.TAMUNG = _arrGiaDV[1];
								row.DANOP = _arrGiaDV[2];
								row.CONNO = _arrGiaDV[3];
								$("#"+_gridId).jqGrid('setRowData', id, row);
								if(row.CONNO > 0){
									$("#"+ _gridId).setCell(id,'TONGCP','',{'background-color':'red'});
									$("#"+ _gridId).setCell(id,'TAMUNG','',{'background-color':'red'});
									$("#"+ _gridId).setCell(id,'CONNO','',{'background-color':'red'});
								}
							}
						}
						// end sondn L2PT-31163

						if(row.TRANGTHAIKHAMBENH == 1){
							if(row.DACODICHVUTHUTIEN == '1'){
								_icon = '<center><img src="'+ _opt.imgPath[0] +'" width="15px"></center>';
							}else{
								_icon = '<center><img src="'+ _opt.imgPath[5] +'" width="15px"></center>';
							}
						}else if(row.TRANGTHAIKHAMBENH == 4){
							_icon = '<center><img src="'+ _opt.imgPath[1] +'" width="15px"></center>';
						}else if(row.TRANGTHAIKHAMBENH == 9){
							_icon = '<center><img src="'+ _opt.imgPath[2] +'" width="15px"></center>';
						}

						if(row.KQCLS == "1"){
							_iconcls = '<center><img src="'+ _opt.imgPath[3] +'" width="15px"></center>';
						}else if(row.KQCLS == "2"){
							_iconcls = '<center><img src="'+ _opt.imgPath[4] +'" width="15px"></center>';
						}

						$("#"+_gridId).jqGrid ('setCell', id, 1, _icon);
						$("#"+_gridId).jqGrid ('setCell', id, 2, _iconcls);

						if(row.DOITUONGBENHNHANID == "1"){
							if(row.KHAMCHINHPHU != "1"){
								$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'DIACHI','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'DIACHI','',{'font-style': 'italic'});
							}
						} else if(row.DOITUONGBENHNHANID != "1"){
							if(row.KHAMCHINHPHU != "1"){
								$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'DIACHI','',{'font-weight':'bold'});
								$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-style': 'italic'});
								$("#"+ _gridId).setCell(id,'DIACHI','',{'font-style': 'italic'});
							}
						}
						if(row.THANNHAN != "0" && row.THANNHAN != undefined){
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : 'green'});
							$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-weight':'bold'});
						}
						//tuyennx_add_start_20200823 L2PT-27662
						if(NGT_HIENTHIMAU_CAPCUU && NGT_HIENTHIMAU_CAPCUU != '0' && row.HINHTHUCVAOVIENID == 2){
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : NGT_HIENTHIMAU_CAPCUU});
						}
						//tuyennx_add_end_20200823 L2PT-27662
						if(row.UUTIENKHAMID != "0"){
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : '#0000FF'});
						}

						if(row.MADANGKY != 'undefined' && row.MADANGKY != '' && row.MADANGKY != '0'){
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : NGT_HIENTHIMAU_DATLICH});
						}


						if(row.BADAINGAY != "0" && row.BADAINGAY != undefined){
							$("#"+_gridId).jqGrid('setRowData', id, "", {color : '#3fa300'});
						}

						if (row.PHONGKHAMDANGKYID_CHA != "0"){
							$("#"+ _gridId).setCell(id,'TENBENHNHAN','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'MABENHNHAN','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'MAHOSOBENHAN','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'MA_BHYT','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'SOTHUTU','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'NAMSINH','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'GIOITINH','',{'font-weight':'bold'});
							$("#"+ _gridId).setCell(id,'DIACHI','',{'font-weight':'bold'});
						}

						if(row.KHAMBENHID == $("#hidKHAMBENHID").val() && row.TRANGTHAIKHAMBENH != 1){
							GridUtil.unmarkAll(_gridId);
							GridUtil.markRow(_gridId,id);
							$('#'+_gridId).find("tr[id='" + id + "']").find('td').trigger( "click" );
						}
					}
				}
			}
		});

		// TICH HOP KHAM ONLINE
		$("#"+_gridId).bind("jqGridAfterLoadComplete", function (e, rowid, orgClickEvent) {
			var htmlGOIKHAM = '<button type="button" class="btn btn-sm btn-primary heightbtn" id="@thisisid" style="width:90%;height:90%"><span class="glyphicon glyphicon-earphone"></span></button>';
			var htmlXEMLAI = '<button type="button" class="btn btn-sm btn-primary heightbtn" id="@thisisid" style="width:90%;height:90%"><span class="glyphicon glyphicon-earphone"></span></button>';
			var html = "";

			var data_grDaChon = $("#"+_gridId).getDataIDs();
			for(var i=0;i<data_grDaChon.length;i++){
				var tt = $("#"+_gridId).jqGrid('getRowData', data_grDaChon[i]);
				if(tt.MADANGKY != 'undefined' && tt.MADANGKY != '' && tt.MADANGKY != '0'){
					html = "";
					html = htmlGOIKHAM.replace('@thisisid', "onlineGOI_" + tt.PHONGKHAMDANGKYID);
					$("#"+_gridId).jqGrid ('setCell', data_grDaChon[i], 'onlineGOI', html);
					$("#onlineGOI_" + tt.PHONGKHAMDANGKYID).on('click', function(e){
						var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
						var rowData =  $("#"+_gridId).jqGrid('getRowData', rowIdTmp);
						var param = "";
						//window.open('manager.jsp?func=../ngoaitru/NGT03K009_GOIKHAM_VIDEO&manguoigoi='+opt.fullname+'&manguoinhan='+rowData.MANGUOINHAN+'&madangky='+rowData.MADANGKY+'&showMode=dlg'+param,'',
						//			'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');

						window.open('manager.jsp?func=../ngoaitru/NGT03K009_GOIKHAM_VIDEO&macsyt='+opt.hospital_code+'&tennguoigoi='+opt.fullname+'&manguoigoi='+opt.user_name+'&manguoinhan='+rowData.MANGUOINHAN+'&madangky='+rowData.MADANGKY+'&showMode=dlg'+param,'',
							'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
					});
				}
			}
		});
		// END TICH HOP KHAM ONLINE

		// open popup yeu cau mo lai/mo lai benh an
		function _molaibenhan(_kieu, _title){
			if(cfObj.HIS_KHOA_BENHAN == '1'){
				var _hosobenhanid = $('#hidHOSOBENHANID').val();
				var check_khoa = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.LAY.TTKHOA", {
					"LOAIKHOA" : "1",
					"GIATRI" : _hosobenhanid.toString()
				});
				if (check_khoa == '1'){
					DlgUtil.showMsg('Bệnh án đã được khóa, liên hệ phòng kế toán để mở khóa');
					return false;
				}
			}
			//tuyennx_add_start 20170412
			var check = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK_MOBA",$('#hidTIEPNHANID').val());
			if(check == 1){
				DlgUtil.showMsg('Đã duyệt kế toán/bảo hiểm ko thể mở lại bệnh án');
				return false;
			}
			//tuyennx_add_end 20170412
			if($('#hidDUYETKETOAN').val() == "1" || $('#hidDUYETBH').val() == "2"){
				DlgUtil.showMsg('Đã duyệt kế toán/bảo hiểm ko thể mở lại bệnh án');
				return false;
			}
			if(_tsmobenhan != "1" && check ==2){
				DlgUtil.showMsg('Bệnh nhân đã nhập viện không thể mở bệnh án');
				return false;
			}

			$('#toolbarIdbtnSearchDate').focus();
			var myVar={
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
				tiepnhanid : $('#hidTIEPNHANID').val(),
				hosobenhanid : $('#hidHOSOBENHANID').val(),
				phongkhamdangkyid : $('#hidPHONGKHAMDANGKYID').val(),
				kieu : _kieu // 1: mo benh an, 0: yêu cầu mở.
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgMOLAIBA","divDlg","manager.jsp?func=../ngoaitru/NGT02K029_YeuCauMoBenhAn",myVar,_title,500,220);
			DlgUtil.open("dlgMOLAIBA");

			//tuyennx_edit_20170713_start
			EventUtil.setEvent("assignSevice_mobenhan",function(e){
				if(e.msg == "1"){
					if ($('#hidXUTRIKHAMBENHID').val()  != '2' && $('#hidXUTRIKHAMBENHID').val()  != '6'
						&& $('#hidXUTRIKHAMBENHID').val()  != '12' && cfObj.NGT_LUUQUANLYBENHAN == '1') {
						luuQuanLyBenhAn('2');
					}
					DlgUtil.showMsg('Bệnh nhân đã mở bệnh án');

					DlgUtil.close("dlgMOLAIBA");
					_loadGridData(_opt.phongid);
				}else if(e.msg == "2"){
					DlgUtil.showMsg('Đã gửi yêu cầu mở lại bệnh án');

					DlgUtil.close("dlgMOLAIBA");
					_loadGridData(_opt.phongid);
				}
				else{
					DlgUtil.showMsg('Cập nhật thông tin không thành công');
				}
			});
			//tuyennx_edit_20170713_end
		}

		// click button bắt đầu trên toolbar
		//tuyenn_edit_start_20181102 L2HOTRO-11553
		$("#toolbarIdbtnStart").on("click", function(e){
			_start_kham(0, null);
		});

		function _openKBHB(){
			var par = ['NGT_DIEUDUONG_TK_TEXT'];
			var _mhc_vs2 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
			var myVar={
				khambenhId : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				madichvu : $("#hidMADICHVU").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				//tuyennx_add_start_20171020 yc L2DKBD-692
				hosobenhanId : $("#hidHOSOBENHANID").val(),
				loaibenhanId : $("#hidLOAIBENHANID").val(),
				//tuyennx_add_end_20171020 yc L2DKBD-692
				//tuyennx_add_start_20170112 L2DKBD-880
				phongId : $("#hidPHONGID").val()
				//tuyennx_add_end_20170112 L2DKBD-880
				,dichvuid : $('#hidDICHVUID').val() // hunglv L2PT-7075
				,mhc_vs2 : _mhc_vs2 //hunglv L2PT-7525
				,phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
			};
			var heightwindow = $(window).height();
			var heightdlg = heightwindow - 50 ;
			var widthwindow = $(window).width();
			var widthdlg = widthwindow - 100 ;
			if (cfObj.NGT_KBHB_FULL == 1) {
				heightdlg = 660;
			}
			dlgPopup=DlgUtil.buildPopupUrl("dlgKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K002_KhamBenhHoiBenh",myVar,"Khám hỏi bệnh",widthdlg,heightdlg);
			DlgUtil.open("dlgKham");
		}
		// BVTM-6625 : phiếu khám gây mê hôi sức
		$("#toolbarIdbtnGAYMETRUOCPTT").on("click", function () {

			var dvkbId = ' ';
			if (opt.hospital_id == "10284"){
				var selRowId_DV = $('#tabChuyenKhoagrdCKChitiet').jqGrid ('getGridParam', 'selrow');
				var rowData_DV = $("#tabChuyenKhoagrdCKChitiet").jqGrid('getRowData',selRowId_DV);
				dvkbId = rowData_DV.DICHVUKHAMBENHID;
			}
			var selRowId = $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);
			if (dvkbId == ' ' || dvkbId == 'undefined'){
				dvkbId = rowData.DICHVUKHAMBENHID;
			}
			var myVar = {
				khambenhId: $("#hidKHAMBENHID").val(),
				benhnhanId: $("#hidBENHNHANID").val(),
				tiepnhanId: $("#hidTIEPNHANID").val(),
				madichvu: $("#hidMADICHVU").val(),
				doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
				mabenhnhan: $("#txtMABENHNHAN").val(),
				hosobenhanId: $("#hidHOSOBENHANID").val(),
				loaibenhanId: $("#hidLOAIBENHANID").val(),
				phongId: $("#hidPHONGID").val(),
				trangthaikhambenh : rowData.TRANGTHAIKHAMBENH,
				dichvukhambenhId: dvkbId,
				formID: 'KB',
				phongkhamdangkyid : rowData.PHONGKHAMDANGKYID
			};

			dlgPopup=DlgUtil.buildPopupUrl("dlgKham","divDlg","manager.jsp?func=../ngoaitru/NGT04K007_PhieuKhamGayMeHoiSuc",myVar,"KHÁM GÂY MÊ TRƯỚC PTTT",1300,650);

			//dlgPopup.open();
			DlgUtil.open("dlgKham");
		});

		function _start_kham(mode, obj){

			//tuyennx_add_start L2PT-13920
			if(cfObj.NGT_CHAN_KHAMBENH == '1'){
				var _sql_par = [];
				_sql_par.push({
					"name" : "[0]",
					value : opt.user_id
				});
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.GETLOAIBS", _sql_par);
				if(ret !== '4'){
					DlgUtil.showMsg('Chỉ có User Bác sỹ mới có quyền khám bệnh!');
					return;
				}
			}
			//tuyennx_add_end L2PT-13920
			var _ch_chuyenkhoa = cfObj.HIS_CHUYENKHOA_BS_DV; //0: default, 1: canh bao, 2: chan
			if (_ch_chuyenkhoa && _ch_chuyenkhoa != '0') {
				var _sql_par = [];
				_sql_par.push({
					"name" : "[0]",
					value : opt.user_id
				});
				var chuyenkhoa_bs = jsonrpc.AjaxJson.getOneValue("NGT.CHUYENKHOA.BS", _sql_par);
				if (chuyenkhoa_bs == "0") {
					DlgUtil.showMsg("Bác sĩ chưa mô tả phạm vi chuyên khoa theo chứng chỉ hành nghề, liên hệ IT để được hỗ trợ.");
					if (_ch_chuyenkhoa == '2') {
						return false;
					}
				}
				var _sql_par1 = [];
				_sql_par1.push({
					"name" : "[0]",
					value : $('#hidDICHVUID').val()
				});
				var chuyenkhoa_dv = jsonrpc.AjaxJson.getOneValue("NGT.CHUYENKHOA.DV", _sql_par1);
				if (chuyenkhoa_dv == "0") {
					DlgUtil.showMsg("Dịch vụ có mã " + $("#hidMADICHVU").val() + " chưa mô tả chuyên khoa, vui lòng liên hệ KHTH để xử lý.");
				}
				if (chuyenkhoa_bs != "0" && chuyenkhoa_dv != "0" && !chuyenkhoa_bs.includes(chuyenkhoa_dv)) {
					DlgUtil.showMsg("Bác sĩ có chuyên khoa khác với chuyên khoa của dịch vụ.");
					if (_ch_chuyenkhoa == '2') {
						return false;
					}
				}
			}
			var _sql_par = [];
			_sql_par.push($("#hidTIEPNHANID").val());
			_sql_par.push($("#hidKHAMBENHID").val());
			_sql_par.push($("#hidPHONGKHAMDANGKYID").val());

			// SONDN L2PT-16309 28/02/2020
			if (mode != 2){
				var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK.DATA1", _sql_par.join('$'));
				if(_result == 1){
					DlgUtil.showMsg('Bệnh nhân đã duyệt kế toán không thể thao tác');
					return;
				}else if(_result == 2){
					//tuyennx edit start dklongan L2PT-13323
					if((mode !=11 && mode != 3 && mode !=12 && mode !=1) || cfObj.CHAN_CDDV_KETHUOC == '0')
					{
						DlgUtil.showMsg('Bệnh nhân đã nhập viện không thể thao tác');
						return;
					}
					//tuyennx edit end dklongan
				}
			}
			//tuyennx_add_end
			var NGT_CHECKTRUNGTHAOTAC_TGBD = cfObj.NGT_CHECKTRUNGTHAOTAC_TGBD
			if (mode != 1 &&  NGT_CHECKTRUNGTHAOTAC_TGBD != '0' && checkthaotactrungtgbd == 0){
				var _sql_par = [];
				_sql_par.push({
					"name" : "[0]",
					"value" : $("#hidPHONGKHAMDANGKYID").val()
				});
				_sql_par.push({
					"name" : "[1]",
					"value" : opt.user_id
				});
				var checkphut = jsonrpc.AjaxJson.getOneValue("NGT_CHECKTRUNG_TGBD", _sql_par);
				if (checkphut == '0') {
					if (NGT_CHECKTRUNGTHAOTAC_TGBD == '1'){
						DlgUtil.showConfirm("Thời gian thao tác trùng với thời gian bắt đầu khám, bạn có muốn tiếp tục ? ",function(flag) {
							if (flag) {
								checkthaotactrungtgbd = 1;
								_start_kham(mode, obj);
							}
							else{
								return false;
							}
						});
						return false;
					}else{
						DlgUtil.showMsg('Thời gian thao tác trùng với thời gian bắt đầu khám!');
						return false;
					}
				}
			}
			checkthaotactrungtgbd = 0;

			//tuyennx_add_start_L2PT-13047
			if(mode == 2){
				if(cfObj.NGT_CHAN_XUTRI_CAPTOA== '1' && $("#cboXUTRIKHAMBENHID").val() == '1'
					&& ($("#txtTUYENHIENTHI").val() == '' || ($('#txtTUYENHIENTHI').val()).includes("Trái tuyến"))){
					DlgUtil.showMsg('Không thể xử trí cấp toa cho về với bệnh nhân viện phí hoặc trái tuyến!');
					return;
				}

			}

			//tuyennx_add_end


			var objData = new Object();
			objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
			objData['KHAMBENHID'] = $("#hidKHAMBENHID").val();
			objData['TIEPNHANID'] = $("#hidTIEPNHANID").val();
			objData['DOITUONGID'] = $("#hidDOITUONGBENHNHANID").val();
			objData['PHONGID'] = _opt.phongid;
			objData["BSIKHAMID"] = $("#hidBACSYKE").val();
			objData['MODE'] = mode;
			objData['BACSIID'] = $("#cboBACSIID").val();													// =0: chi bat dau kham; =1: bd kham + mo form kbhb;
			if (obj != null){
				objData['_opt'] = obj._opt;
				objData['_loaikedon'] = obj._loaikedon;
				objData['_title'] = obj._title;
				objData['_loadkhotheo'] = obj._loadkhotheo;
			}

			// kiem tra con thuoc va xac nhan truoc khi vao kham:
			var b_MaBenhNhan = $("#hidMABHYT").val();
			var b_Loai = "2"; 			// ?
			var _objData = new Object();
			_objData["tenbenhnhan"] = $("#txtTENBENHNHAN").val();
			_objData["ngaysinh"] = $("#txtNGAYSINH").val();
			_objData["gioitinhid"] = $("#cboGIOITINH").val() == "Nam" ? "1" : "2";
			_objData["benhnhanid"] = $("#hidBENHNHANID").val();
			_objData["khambenhid"] = $("#hidKHAMBENHID").val(); //L2PT-18747

			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.TKBN1",b_MaBenhNhan+'$'+b_Loai+'$'+JSON.stringify(_objData));
			if (_canhbaobdkham == "1" && data_ar != null && data_ar.length > 0 && mode == 1){ //L2PT-18747
				if(data_ar[0].NGAYTHUOC != "" && data_ar[0].NGAYTHUOC != null){
					var t_ngaythuoc = data_ar[0].NGAYTHUOC;
					var ngay = t_ngaythuoc.split('/');
					var ngaythuoc = ngay[2].substring(0,4)+ngay[1]+ngay[0];
					var ngaytn = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');
					var ngaykedon = data_ar[0].NGAYMAUBENHPHAM;

					//if(parseInt(ngaykedon) != parseInt(ngaytn) && parseInt(ngaythuoc) > parseInt(ngaytn)){
					var _objj = new Object();
					_objj.BENHNHANID = $("#hidBENHNHANID").val();
					_objj.MA_BHYT = $("#hidMABHYT").val();
					var data_ar11 = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.CONTHUOC", JSON.stringify(_objj));
					if(parseInt(ngaythuoc) > parseInt(ngaytn)){
						DlgUtil.showConfirm("Bệnh nhân mã bệnh án: "+data_ar11[0].MAHOSOBENHAN+" có chỉ định thuốc ngày " + data_ar11[0].NGAYCHIDINH
							+ ", hết thuốc ngày: "+data_ar11[0].NGAYTHUOC+", của PK " + data_ar11[0].TENPHONG + ", có tiếp tục bắt đầu khám?", function(flag){//tuyennx_L2PT-18747
							if(flag){
								_batdaukhamm(objData);
							}
						});
					}else{
						_batdaukhamm(objData);
					}
				}else{
					_batdaukhamm(objData);
				}
			}else{
				_batdaukhamm(objData);
			}
		};
		//tuyenn_edit_end_20181102 L2HOTRO-11553

		function _batdaukhamm(objData){
			// sondn L2PT-2214
			var _arrx = [$("#hidKHAMBENHID").val(), $("#hidPHONGKHAMDANGKYID").val(), $("#cboBACSIID").val()];
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT01T002.CHECKUSER",_arrx.join('$'));
			if (ret == "-1"){
				DlgUtil.showMsg("Lỗi trong quá trình kiểm tra dữ liệu");
				return;
			}else if (ret == "-2"){
				DlgUtil.showMsg("Yêu cầu chọn thông tin bác sỹ khám trong combobox");
				return;
			}
			// end sondn L2PT-2214

			var rs = dayCongBYT();
			if(rs ==1)
				return;
			else if(rs !=1 && rs !=0){
				DlgUtil.showConfirm("Kết quả kiểm tra cổng BYT: " +rs+ "\n Bạn có muốn tiếp tục?",function(flag) {
					if (flag) {
						_batdaukham(objData);
					}else{
						dayCongBYT_KTK();
					}
				});
			}
			else if(rs ==0){
				_batdaukham(objData);
			}
		}

		// Mode = 4: Sua cac toolbar sau:
		// toolbarIddrug_1
		// toolbarIddrug_khothuoc
		// toolbarIddrug_tutruc
		// toolbarIddrug_le
		// toolbarIddrug_dtnhathuoc
		// toolbarIddrug_1dy
		// toolbarIddrug_2dy
		// toolbarIddrug_2
		// toolbarIddrug_3
		// toolbarIddrug_4
		function _batdaukham(objData){
			if (objData.MODE == 3 && objData.KHAMBENHID == -1) {
				DlgUtil.showMsg('Vui lòng chọn bệnh nhân trước khi thực hiện');
				return;
			}
			var index = $('#hidINDEX').val();
			// if($("#"+_gridId).jqGrid ('getCell', index, "TRANGTHAIKHAMBENH") == 1 ){
			// 	if (cfObj.NGT_CHECKTGKHAMGIUA2BN !== '0' ){
			// 		var _sql_par = [];
			// 		_sql_par.push({
			// 			"name" : "[0]",
			// 			value : _opt.phongid
			// 		});
			// 		var ret = jsonrpc.AjaxJson.getOneValue("NGT.TGKHAM2BN", _sql_par);
			// 		if (ret !== '0') {
			// 			DlgUtil.showMsg('Thời gian khám bệnh giữa các bệnh nhân chưa đủ số phút cấu hình trong tham số, không thể thực hiện!');
			// 			return;
			// 		}
			// 	}
			// }
			objData.CHECKBDKHAM = checkbdkham_3phut;
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S(_SQL[3],JSON.stringify(objData));
			if(ret == '1'){
				checkbdkham_3phut = 0;
				_setButton(false);
				/*	var item_id = $("#hidINDEX").val();
				$("#"+_gridId).jqGrid ('setCell', item_id, 8, 4);*/

				$("#toolbarIdbtnStart").attr("disabled", true);

				var index = $('#hidINDEX').val();

				if($("#"+_gridId).jqGrid ('getCell', index, "MADANGKY") != 'undefined' &&
					$("#"+_gridId).jqGrid ('getCell', index, "MADANGKY") != '' &&
					$("#"+_gridId).jqGrid ('getCell', index, "MADANGKY") != '0'){
					if(cfObj.AUTO_LIENKET_VNCARE == 1){
						lienketVncare($("#hidBENHNHANID").val(),$("#"+_gridId).jqGrid ('getCell', index, "MANGUOINHAN"));
					}
				}

				if($("#"+_gridId).jqGrid ('getCell', index, "TRANGTHAIKHAMBENH") == 1){
					if(cfObj.NOTIFY_APP_VNCARE == 1){
						sendNotifyMH2(1);
					}
				}
				_icon = '<center><img src="'+ _opt.imgPath[1] +'" width="15px"></center>';
				var sql_par = [];
				sql_par.push({	"name" : "[0]",	value : $('#hidKHAMBENHID').val()});
				var countpk = jsonrpc.AjaxJson.getOneValue("NGT.COUNTPK", sql_par);
if (cfObj.QD130_DAYXMLCHECKIN_BDKHAM == 1 && $("#"+_gridId).jqGrid ('getCell', index, "TRANGTHAIKHAMBENH") == 1 && countpk == 1){
					setTimeout(function () {
						gui_cong_bhxh('', $('#hidTIEPNHANID').val(), '', "0", "1", true)
                    }, 3000)
				}

				$("#"+_gridId).jqGrid ('setCell', index, 1, _icon);
				$("#"+_gridId).jqGrid ('setCell', index, "TRANGTHAIKHAMBENH", 4);

				var objj = new Object();
				var ttkb = "4";				// dang kham;
				objj._xutrikhambenh = "0"; 			// khong co xu tri;
				loadTabStatus(ttkb, objj);

//				if(typeof objData.MODE != 'undefined' && objData.MODE == "1"){
//					var _show = cfObj.NGT_MO_POPUP_BATDAUKHAM;
//					if(_show == '' || _show == '0' || _show == '1') {
//						_openKBHB(); 								// mo form kham benh hoi benh truong hop click truc tiep;
//					}else if(_show == '2'){
//						$("#toolbarIdbtnService").trigger("click");
//					}
//				}
				gw_batdaukham($('#hidTIEPNHANID').val());//L2PT-40477
				
				if(typeof objData.MODE != 'undefined' && objData.MODE == "1"){						// start + mo phieu KBHB
					_openKBHB();
				}else if(typeof objData.MODE != 'undefined' && objData.MODE == "2"){				// start + luu
					_capnhat(false, "toolbarIdbtnLuu");
				}else if(typeof objData.MODE != 'undefined' && objData.MODE == "3"){				// start + CDDV
					_openCDDV();
				}else if(typeof objData.MODE != 'undefined' && (objData.MODE == "4" || objData.MODE == "11" || objData.MODE == "12") ){				// mo don thuoc cac loai  tuyennx edit dklongan L2PT-13323
					_openDialogThuoc(objData['_opt'], objData['_loaikedon'], objData['_title'], objData['_loadkhotheo']);
				}else if(typeof objData.MODE != 'undefined' && objData.MODE == "5"){				// mo don thuoc mua ngoai
					_openDonThuocMuaNgoai();
				}
				else if(typeof objData.MODE != 'undefined' && objData.MODE == "13"){				// mo don thuoc mua ngoai dy L2PT-17921
					_openDonThuocMuaNgoaiDY();
				}
				else if(typeof objData.MODE != 'undefined' && objData.MODE == "6"){				// mo phieu tu van
					_openPhieuTuVan();
				}else if(typeof objData.MODE != 'undefined' && objData.MODE == "7"){				// mo phieu dinh huong
					_openPhieuDinhHuong();
				}else if(typeof objData.MODE != 'undefined' && objData.MODE == "8"){				// mo phieu drug 8
					_openDrug8();
				}else if(typeof objData.MODE != 'undefined' && objData.MODE == "9"){				// mo phieu hao phi vat tu;
					_openHaoPhiVatTu();
				}else if(typeof objData.MODE != 'undefined' && objData.MODE == "10"){				// mo don thuoc khong thuoc;
					_openDialogThuocK('02K044', 0, "Chỉ định thuốc không thuốc");
				}
				//L2PT-35459
				else if(typeof objData.MODE != 'undefined' && objData.MODE == "15"){				// mo don thuoc vien phi;
					_openDialogThuocVP(objData['_opt'], objData['_loaikedon'], objData['_title'], objData['_loadkhotheo']);
				}

				//BVTM-5938 day emr khi ket thuc kham
				day_emr_ktkham($('#hidHOSOBENHANID').val(), $("#hidTIEPNHANID").val());

			}else if(ret == '200'){
				DlgUtil.showMsg('Bệnh nhân đối tượng BHYT + DV nhưng chưa thanh toán tiền DV');
			}else if(ret == '300' || ret == '301' || ret == '302' || ret == '303'
				|| ret == '304' || ret == '305' || ret == '306' || ret == '307'
				|| ret == '308' || ret == '309' || ret == '310' || ret == '311'){
				DlgUtil.showMsg('Bệnh nhân chưa thanh toán tiền công khám');
			}else if(ret == '500'){
				DlgUtil.showMsg('Thiếu thông tin mã bác sỹ, hoặc user này không phải là bác sỹ, không thể bắt đầu khám. ');
			}
			else if(ret == '600'){
				var CHECK65BHYT = cfObj.CHECK65BHYT;
				DlgUtil.showMsg("Khám quá "+CHECK65BHYT+" số lượng bệnh nhân Bảo hiểm y tế trong ngày, không thể tiếp tục khám");
			}
			//L2PT-28568
			else if(ret == '400'){
				DlgUtil.showMsg('Chưa có mã chẩn đoán chính, không thể bắt đầu khám. ');
			}
			else if(ret == '700'){
				var CHECK65BHYT = cfObj.CHECK65BHYT;
				if (cfObj.CHECK65BHYT_CHAN == "0"){
					DlgUtil.showConfirm("Khám quá "+CHECK65BHYT+" bệnh nhân Bảo hiểm y tế trong ngày, có tiếp tục khám?",function(flag) {
						if (flag) {
							objData.BOQUACHECK65 = '1';
							_batdaukham(objData);
						}
					});
				}else{
					DlgUtil.showMsg("Khám quá "+CHECK65BHYT+" bệnh nhân Bảo hiểm y tế trong ngày, không thể tiếp tục khám");
					return;
				}
			}//L2PT-21593
			else if(ret == '800'){
				DlgUtil.showMsg("BN có thời gian chỉ định dịch vụ nhỏ hơn thời gian bắt đầu khám, vui lòng kiểm tra lại!");
				return ;
			}else if(ret == '750'){
				DlgUtil.showMsg("Thời gian bắt đầu trùng ngày giờ phút, thử lại trong ít phút!");
				return;
			}else if(ret == '650'){
				DlgUtil.showMsg('Thời gian khám bệnh giữa các bệnh nhân chưa đủ số phút cấu hình trong tham số, không thể thực hiện!');
				return;
			}else if (ret == '250') {
				DlgUtil.showMsg('Còn phòng khám chưa kết thúc không thể bắt đầu khám!');
				return;
			}else if (ret == '950') {
				DlgUtil.showMsg('Thời gian từ lúc tiếp nhận đến lúc bắt đầu khám chưa đủ '+  cfObj.NGT_CHECKTGBDKHAM_TGTIEPNHAN + ' phút, không thể bắt đầu!');
				return;
			}else if(ret.split('@')[0] == '3phut'){
				if (cfObj.NGT_CHECKTGIANBDKHAM_2BN.split(';')[0] == 1) {
					DlgUtil.showConfirm("Thời gian bắt đầu khám của bệnh nhân quá gần với bệnh nhân trước đó " + ret.split('@')[1] + ". " +
						"Thời gian bắt đầu khám tối thiểu giữa 2 bệnh nhân là "+cfObj.NGT_CHECKTGIANBDKHAM_2BN.split(';')[1] +" phút. Bạn có muốn tiếp tục không?!", function (flag) {
						if (flag) {
							checkbdkham_3phut = 1;
							_batdaukham(objData);
						} else {
							return false;
						}
					});
					return false;
				} else if (cfObj.NGT_CHECKTGIANBDKHAM_2BN.split(';')[0] == 2) {
					DlgUtil.showMsg("Thời gian bắt đầu khám của bệnh nhân quá gần với bệnh nhân trước đó " + ret.split('@')[1] + ". " +
						"Thời gian bắt đầu khám tối thiểu giữa 2 bệnh nhân là "+cfObj.NGT_CHECKTGIANBDKHAM_2BN.split(';')[1] +" phút. Không thế bắt đầu khám!");
					return;
				}
			}else{
				DlgUtil.showMsg(ret);
			}
		}
		//tuyennx_add_start tích hợp cổng dữ liệu y tế
		function dayCongBYT(){
			var sql_par=[];
			sql_par.push({"name":"[0]","value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')});
			var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT",sql_par);
			vsothutu = JSON.parse(vsothutu);
			if (vsothutu[0].BYTDAYDL == "1"){
				var _parram = ["1"];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",_parram.join('$'));
				var data_bv = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETTT_BV",[]);

				var objCHECKIN = new Object();
				objCHECKIN.MA_LK = $("#hidHOSOBENHANID").val();
				objCHECKIN.Sender_Code = opt.hospital_code;
				objCHECKIN.Sender_Name = "";
				objCHECKIN.Action_Type = "1";				// 0: bắt đầu khám, 1: kết thúc khám
				objCHECKIN.Transaction_Type = "M0001";
				objCHECKIN.MABENHVIEN = data_ar[0].I_U1;
				objCHECKIN.MA_THE = $("#txtSOTHEBHYT").val();
				objCHECKIN.NGAYGIOKHAM = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');
				var objHeader = XML_BYT_TaoHeader(objCHECKIN); 										// tao doi tuong header;
				var objIn = XML_BYT_TaoTheCHECKIN(objCHECKIN); 									// tao the
				var obj3 = XML_BYT_TaoKhung(objHeader, objIn, "1"); 											// tao JSON full => XML

				var resultCongBYT = ajaxSvc.CongDLYTWS.guiTTBDK(vsothutu[0].BYTURL
					, data_ar[0].I_U1
					, data_ar[0].I_P1
					, data_ar[0].I_U1						// csytid
					, data_bv[0].MADIAPHUONG							// ma tinh
					, obj3);
				var rets = resultCongBYT.split(';');
				if(rets[0] == '10'){
					return rets[1];
				}
				else if (rets[0] != '0'){
					DlgUtil.showMsg("Lỗi đẩy dữ liệu cổng y tế: "+rets[1]);
					if(vsothutu[0].BYTSTOPCHUCNANG == "1")
						return 1;
				}

			}
			return 0;
		}

		function dayCongBYT_KTK(){
			var sql_par=[];
			sql_par.push({"name":"[0]","value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')});
			var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT",sql_par);
			vsothutu = JSON.parse(vsothutu);
			if (vsothutu[0].BYTDAYDL == "1"){
				var _parram = ["1"];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",_parram.join('$'));
				var data_bv = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETTT_BV",[]);

				var objCHECKIN = new Object();
				objCHECKIN.MA_LK = $("#hidHOSOBENHANID").val();
				objCHECKIN.Sender_Code = data_ar[0].I_U1;
				objCHECKIN.Sender_Name = "";
				objCHECKIN.Action_Type = "1";				// 0: bắt đầu khám, 1: kết thúc khám
				objCHECKIN.Transaction_Type = "M0001";
				objCHECKIN.MABENHVIEN = data_ar[0].I_U1;
				objCHECKIN.MA_THE = $("#txtSOTHEBHYT").val();
				objCHECKIN.MA_LOAI_KCB = "1";
				objCHECKIN.TEN_BENH = "";
				objCHECKIN.MA_BENH = "";
//				objCHECKIN.MA_BENHKHAC = $("#lblMA_BHYT").val();
				objCHECKIN.NGAYHETTHUOC = "";

				objCHECKIN.NGAYGIORA = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');
				var objHeader = XML_BYT_TaoHeaderKTK(objCHECKIN,"0"); 										// tao doi tuong header;
				var objIn = XML_BYT_TaoTheCHECKIN(objCHECKIN); 									// tao the
				var obj3 = XML_BYT_TaoKhung(objHeader, objIn, "1"); 											// tao JSON full => XML

				var resultCongBYT = ajaxSvc.CongDLYTWS.guiTTKTK(
					vsothutu[0].BYTURL
					, data_ar[0].I_U1
					, data_ar[0].I_P1
					, data_ar[0].I_U1						// csytid
					, data_bv[0].MADIAPHUONG							// ma tinh
					, "1", obj3);
				var rets = resultCongBYT.split(';');
			}
		}
		//tuyennx_add_end

		$('#toolbarIdbtnMissList').click(function(){
			DlgUtil.showMsg("Danh sách gọi lại đang trong quá trình hoàn thiện");
		});

		function goiBenhNhanCheDo(sothutubd, sothutukt, _sophongkham){
			if (_chedogoikham == "0"){
				if(sothutubd != ""){
					call.goivaokham(sothutubd, _sophongkham, _ngtgoibenhnhan);
					var objData = new Object();
					objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
					jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.GOIKHAM",JSON.stringify(objData));
					_loadGridData(_opt.phongid);
				}
			}else if (_chedogoikham == "1"){
				if(sothutubd != "" && sothutukt != ""){
					call.goidangky(sothutubd, sothutukt, _ngtgoibenhnhan);
					var objData = new Object();
					objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
					jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.GOIKHAM",JSON.stringify(objData));
					_loadGridData(_opt.phongid);
				}
			}else if (_chedogoikham == "2"){
				if($("#hidHOTENBNGOI").val() == ""){
					DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để gọi. ");
					return;
				}

				var _texts = "Mời bệnh nhân " + $("#hidHOTENBNGOI").val() + " vào " + _sophongkham;
				// BV Quynh Phu TBH dung cach goi khac:
				if (opt.hospital_id == "24560"){
					//if (opt.hospital_id == "965"){
					_texts = "Mời bệnh nhân " + $("#hidSOTHUTU").val() + "." + $("#hidHOTENBNGOI").val() + " vào " + _sophongkham;
				}else if(opt.hospital_id == "46840"){
					_texts = "Mời bệnh nhân " + $("#hidSOTHUTU").val() + "." + $("#hidHOTENBNGOI").val() + " vào " + _sophongkham + "." + _opt._subdept_name;
				}

				if ( cfObj.NGT_KIEUGOIKHAM_GG == 1 ){
					_texts = "";
					if ($("#hidDONVI_TUOI").val() == 1 ){
						_texts = "Mời bệnh nhân có thứ tự là " + parseInt($("#hidSOTHUTU").val()) + ". " + $("#hidHOTENBNGOI").val() + "  " + $("#hidTUOI").val() + " vào " + _opt._subdept_name;
					}else {
						_texts = "Mời bệnh nhân có thứ tự là " + parseInt($("#hidSOTHUTU").val()) + ". " + $("#hidHOTENBNGOI").val() + "  " + $("#hidTUOI").val() + " tuổi vào " + _opt._subdept_name;
					}
				}
				goiKhamGG(_texts, _modeGoiKham, _timeOutGoiKham);

				var objData = new Object();
				objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
				jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.GOIKHAM",JSON.stringify(objData));
			}
			if(cfObj.NOTIFY_APP_VNCARE == 1){
				sendNotifyMH2(2);
			}
		}

		$('#toolbarIdbtnCall').click(function(){
			if(NGT_CALLBN_SMARTADS != '0'){
				var sql_par1 = [];
				sql_par1.push({"name" : "[0]","value" : _opt.phongid});
				var data_maphong = jsonrpc.AjaxJson.ajaxExecuteQueryO("PHONG.LAYMA", sql_par1);
				var maphongs = $.parseJSON(data_maphong);
				var ds_phong = NGT_CALLBN_SMARTADS.split(',');
				if(NGT_CALLBN_SMARTADS == '1' ||  $.inArray( maphongs[0].MAPHONG.replaceAll("\'", ""), ds_phong) >= 0){
					if($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1'){
						return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
					}

					var objData = {
						"MA_PHONG": maphongs[0].MAPHONG.replaceAll("\'", ""),
						"TENBENHAN" : $("#txtTENBENHNHAN").val(),
						"MABENHAN" : $("#hidMAHOSOBENHAN").val()
					};
					var request_url = '/vnpthis/api/service/smartads/goikham';
					$.ajax({
						type : "POST",
						contentType : "application/json; charset=utf-8",
						data : JSON.stringify(objData),
						url : request_url,
						beforeSend : function(xhr) {
							xhr.setRequestHeader('Authorization', 'Bearer ' + jsonrpc.AjaxJson.uuid);
						},
						success : function(data) {
							console.log("response success=" + JSON.stringify(data));
						},
						error : function(xhr) {
							DlgUtil.showMsg("Lỗi gọi khám qua API!");
						}
					});
				}
				else
					goibnvaokham();

			}else{
				goibnvaokham();
			}
		});

		function goibnvaokham(){
			if(_chedogoikham == "0"){
				var stt = $('#hidSOTHUTU').val();
				goiBenhNhanCheDo(stt, null, _sophongkham); 				// Goi 1 BN
			}else if (_chedogoikham == "1"){
				var stt = $("#txtGOIKHAMSTT_BD").val().trim(); 					// Goi BN tu so ... den so ...
				var sttkt = $("#txtGOIKHAMSTT_KT").val().trim();

				if (stt != ""){
					if(isNaN(stt)){
						DlgUtil.showMsg("Số thứ tự bắt đầu cần đúng định dạng");
						return;
					}
					if (sttkt != ""){
						if(isNaN(sttkt)){
							DlgUtil.showMsg("Số thứ tự kết thúc cần đúng định dạng");
							return;
						}
						if (Number(sttkt) > Number(stt)){
							goiBenhNhanCheDo(stt, sttkt, _sophongkham);
						}else if (Number(sttkt) == Number(stt)){
							goiBenhNhanCheDo(stt, null, _sophongkham);
						}else{
							DlgUtil.showMsg("STT kết thúc phải lớn hơn STT bắt đầu. ");
							return;
						}
					}else{
						goiBenhNhanCheDo(stt, null, _sophongkham);
					}
				}else{
					// LAY BN DANG DUOC CHON;
					stt = $('#hidSOTHUTU').val();
					if(stt == ""){
						DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để gọi");
						return;
					}
					goiBenhNhanCheDo(stt, null, _sophongkham);
				}
			}else if (_chedogoikham == "2"){
				// SU DUNG GIONG DOC GOOGLE BAT KY;
				if($("#hidPHONGKHAMDANGKYID").val() == ""){
					DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để gọi ");
					return;
				}
				var stt = $('#hidSOTHUTU').val();
				goiBenhNhanCheDo(stt, null, _sophongkham);
			}
		}

		// XU LY CHUC NANG GOI SO THEO KHOANG;
		function _tanggiam_stt(id, kieu){ // kieu 1: tang, 2: giam.
			var value = $('#'+id).val().trim();
			if (isNaN(value) && value != ""){
				DlgUtil.showMsg("Giá trị gọi khám phải là số. ");
				return;
			}
			if(value < 1){
				$('#'+id).val(1);
			}else{
				if(kieu == 1){
					value = parseInt(value) + 1;
				}else{
					value = parseInt(value) - 1;
					if(value < 1){
						value = 1;
					}
				}
				$('#'+id).val(value);
			}
		}
		$("#txtKHAMBENH_CANNANG").on("blur",function(e){
			_calBMI();
		});
		$("#txtKHAMBENH_CHIEUCAO").on("blur",function(e){
			_calBMI();
		});
		$("#btnBP").on("click",function(e){
			DlgUtil.showConfirm("Bạn có muốn xóa thông tin bệnh kèm theo này ? ", function(flag){
				if(flag){
					$('#txtMACHANDOANRAVIEN_KEMTHEO').val('');
					$('#txtCHANDOANRAVIEN_KEMTHEO').val('');
					var _par=[$("#hidKHAMBENHID").val(), $("#hidPHONGKHAMDANGKYID").val(), "", ""];
					var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K001.BP.UPD",_par.join('$'));

					if (resultCheck != "1"){
						DlgUtil.showMsg("Lỗi reset bệnh kèm theo");
					}
				}
			});
		});

		// dong popup sua thong tin ngay tiep nhan benh nhan;
		EventUtil.setEvent("ev_dongcuaso", function(e) {
			DlgUtil.close("divDlgDichVu");
			_loadGridData(_opt.phongid);
		});

		EventUtil.setEvent("chinhsua_benhphu",function(e){
			var _icdchinh = $("#hidICD_RAVIEN").val();
			var _tenicdchinh = $("#txtCHANDOANRAVIEN").val();
			var _listicdphu = e.benhphu.split(';');
			for(i=0;i<_listicdphu.length;i++){
				var _detail = _listicdphu[i].split('-');
				if(_detail[0] == _icdchinh && _detail[1] == _tenicdchinh){
					DlgUtil.showMsg("Bệnh kèm theo trùng với bệnh chính, yêu cầu chọn lại, bệnh chính là: " + $("#txtCHANDOANRAVIEN").val());
					return;
				}
			}

			var _par=[$("#hidKHAMBENHID").val(), $("#hidPHONGKHAMDANGKYID").val(), "", e.benhphu];
			var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K001.BP.UPD",_par.join('$'));
			if (resultCheck != "1"){
				DlgUtil.showMsg("Lỗi cập nhật bệnh kèm theo");
				return;
			}
			$('#txtCHANDOANRAVIEN_KEMTHEO').val(e.benhphu);
			if( showyhct4750 == '1'){
				$('#txtCHANDOANYHCTPHU').val(e.benhphu1);
			}
			DlgUtil.close("dlgBPKT");
		});

		$("#btnEDITBP").on("click",function(e){
			if(showyhct4750 == '1'){
				var myVar = {
					benhphu : $('#txtCHANDOANRAVIEN_KEMTHEO').val(),
					yhct : showyhct4750,
					benhphu1 : $('#txtCHANDOANYHCTPHU').val(),
					chandoan_kt_bd : ""
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
				DlgUtil.open("dlgBPKT");
			}else {
				var myVar={
					benhphu : $('#txtCHANDOANRAVIEN_KEMTHEO').val()
				};
				dlgPopup=DlgUtil.buildPopupUrl("dlgBPKT","divDlg","manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu",myVar,"Chỉnh sửa bệnh kèm theo",600,420);
				DlgUtil.open("dlgBPKT");
			}
		});

		$("#btnSAVEICD").on("click", function () {
			var myVar = "";
			EventUtil.setEvent("chon_benhchinh", function (e) {
				DlgUtil.close("dlgBenhChinh");
				$("#hidICD_RAVIEN").val(e.icd10code);
				$('#txtMACHANDOANRAVIEN').val(e.icd10code);
				$('#txtCHANDOANRAVIEN').val(e.icd10name);
			});
			dlgPopup = DlgUtil.buildPopupUrl("dlgBenhChinh", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChonBenhChinh", myVar, "Chọn bệnh chính", 680, 480);
			DlgUtil.open("dlgBenhChinh");
		});

		function _calBMI(){
			if($("#txtKHAMBENH_CHIEUCAO").val() != '' && $("#txtKHAMBENH_CANNANG").val() != ''){
				if(isNaN(parseFloat($("#txtKHAMBENH_CANNANG").val())) || isNaN(parseFloat($("#txtKHAMBENH_CHIEUCAO").val())))
					return;
				var _cannang = parseFloat($("#txtKHAMBENH_CANNANG").val());
				var _chieucao = parseFloat($("#txtKHAMBENH_CHIEUCAO").val());

				var bmi = chisobmi(_cannang, _chieucao);

				$("#txtBMI").val(bmi + nhandinh_bmi(bmi));
			}
		}
		var f6 = 117;
		$(document).unbind('keydown').keydown(function (e) {
			if(e.keyCode == f6){
				getIcd(e.target);
			}
		});

		//THAIPH L2PT-6357//
		EventUtil.setEvent("assignSevice_resultTK", function(e) {
			if(e.mode == '0'){
				$('#' + e.ctrId).combogrid("setValue",e.text);
			} else if(e.mode == '1'){
				$('#' + e.ctrTargetId).val($('#' + e.ctrTargetId).val() == '' ? "" + e.text : $('#' + e.ctrTargetId).val() + ";" + e.text);
			}
			DlgUtil.close(e.popupId);
		});
		//END THAIPH//
		if (cfObj.NGT_PHIMTAT_KB2 != 1) {
			$("#txtMACHANDOANRAVIEN").change(function(){
				if($("#txtMACHANDOANRAVIEN").val()!="" )
					$('#txtMACHANDOANRAVIEN_KEMTHEO').focus();
			});
		}

		//tuyennx_add_start_L2PT-14715
		$("#txtMACHANDOANRAVIEN").focusout(function(){
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : $("#txtMACHANDOANRAVIEN").val()
			});
			var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECKICD", _sql_par);
			if(ret == '0')
				$("#txtCHANDOANRAVIEN").val("");
			if (cfObj.NGT_CHECKICDDAINGAY_30NGAY == 1){
				var sql_par = [];
				sql_par.push({
					"name" : "[0]",
					value : $("#hidBENHNHANID").val()
				});
				sql_par.push({
					"name" : "[1]",
					value : $("#txtMACHANDOANRAVIEN").val()
				});
				sql_par.push({
					"name" : "[2]",
					value : $("#hidKHAMBENHID").val()
				});
				sql_par.push({
					"name" : "[3]",
					value : $("#txtMACHANDOANRAVIEN").val()
				});
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECKICD.30NGAY", sql_par);
				if(ret != 0) {
					DlgUtil.showMsg("ICD chính vừa nhập đã được nhập trong vòng 30 ngày, yêu cầu kiểm tra lại!");
					return false;
				}
			}
		});
		//tuyennx_add_end_L2PT-14715

		var sql_par=[];
		var _sql="CG.ICD10";
		var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";

		var sql_par_p = [];
		sql_par_p.push({"name":"[0]", value: _opt.phongid});
		var check_p = jsonrpc.AjaxJson.getOneValue('CHECK.PHONGYHCT', sql_par_p);

		if(cfObj.HIS_SHOW_ICDYHCT =='1' && check_p != '0'){
			showyhct4750 = '1';
			$("#divICDYHTC").show();
			_sql = 'NT.008.YHCTV4';
			_col = 'Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l';
			ComboUtil.initComboGrid("txtMACHANDOANYHCT", _sql, [], "900px", _col, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtCHANDOANYHCTPHU").val();
				if (str.indexOf(_ui.YHCTCODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
					return false;
				}
				$("#hidICD_RAVIEN").val(_ui.ICD10CODE);
				if(_loadFirstICDYHCT == false){
				$("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
				$("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
				$("#hidICD_RAVIEN").val(_ui.ICD10CODE);
				$("#txtMACHANDOANRAVIEN").val(_ui.ICD10CODE);
				$("#txtCHANDOANRAVIEN").val(_ui.ICD10NAME);
				}else{
					if (data_arkb != null && data_arkb.length > 0 && data_arkb[0].MACHANDOANYHCT != '') {
						_loadFirstICDYHCT = false; 							// lan dau load co du lieu: khong load lai nua
					}else{
						$("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
						$("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
						$("#hidICD_RAVIEN").val(_ui.ICD10CODE);
						$("#txtMACHANDOANRAVIEN").val(_ui.ICD10CODE);
						$("#txtCHANDOANRAVIEN").val(_ui.ICD10NAME);
				        _loadFirstICDYHCT = false; 	
					}
				}
				return false;
			});
			ComboUtil.init("txtTKMACHANDOANYHCTPHU", _sql, [], "900px", _col, function(event, ui) {
				if (ui.item.YHCTCODE == $("#cboMACHANDOANYHCT").val()) {
					DlgUtil.showMsg("Bệnh kèm theo vừa nhập không được trùng với bệnh chính.");
					return false;
				}
				var stryhct = $("#txtCHANDOANYHCTPHU").val();
				if(stryhct.indexOf(ui.item.YHCTCODE+'-') > -1){
					DlgUtil.showMsg("Bệnh kèm theo đã được nhập");
					return false;
				}
				if(ui.item.YHCTCODE){
					if (stryhct != '')
						stryhct += ";";
					$("#txtCHANDOANYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
				}
				var str = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
				if (str != '')
					str += ";";
				$("#txtCHANDOANRAVIEN_KEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
				return false;
			});
			ComboUtil.initComboGrid("txtMACHANDOANRAVIEN", _sql, [], "900px", _col, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
					return false;
				}
				$("#hidICD_RAVIEN").val(_ui.ICD10CODE);
				if(_loadFirstICD == false){
					$("#hidICD_RAVIEN").val(_ui.ICD10CODE);
				$("#txtMACHANDOANRAVIEN").val(_ui.ICD10CODE);
				$("#txtCHANDOANRAVIEN").val(_ui.ICD10NAME);
				$("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
				$("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
				}else{
					if (data_arkb != null && data_arkb.length > 0 && data_arkb[0].MACHANDOANRAVIEN != '') {
						_loadFirstICD = false; 							// lan dau load co du lieu: khong load lai nua
					}else{
						$("#hidICD_RAVIEN").val(_ui.ICD10CODE);
						$("#txtMACHANDOANRAVIEN").val(_ui.ICD10CODE);
						$("#txtCHANDOANRAVIEN").val(_ui.ICD10NAME);
						$("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
						$("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
				        _loadFirstICD = false; 	
					}
				}
				return false;
			});
			ComboUtil.init("txtMACHANDOANRAVIEN_KEMTHEO", _sql, [], "900px", _col, function(event, ui) {
				if (ui.item.ICD10CODE == $("#txtMACHANDOANRAVIEN").val()) {
					DlgUtil.showMsg("Bệnh kèm theo vừa nhập không được trùng với bệnh chính.");
					return false;
				}
				var str = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
				if(str.indexOf(ui.item.ICD10CODE+'-') > -1){
					DlgUtil.showMsg("Bệnh kèm theo đã được nhập");
					return false;
				}
				if (str != '')
					str += ";";
				$("#txtCHANDOANRAVIEN_KEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
				var stryhct = $("#txtCHANDOANYHCTPHU").val();
				if(ui.item.YHCTCODE){
					if (stryhct != '')
						stryhct += ";";
					$("#txtCHANDOANYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
				}
				return false;
			});
		}else {
			ComboUtil.initComboGrid("txtMACHANDOANRAVIEN",_sql,sql_par, "600px", _col, function(event, ui) {
				//L2PT-15056
				var icdcheck ;
				if(_loadFirstICD == false){
					icdcheck = ui.item.ICD10CODE;
				}else{
					if (data_arkb != null && data_arkb.length > 0 && data_arkb[0].MACHANDOANRAVIEN != '') {
						icdcheck = data_arkb[0].MACHANDOANRAVIEN;
					}else {
						icdcheck = ui.item.ICD10CODE;
					}
				}
				var check = _checkCBBN(icdcheck, "1");
				if(check == 1)
					return false;
				$("#hidICD_RAVIEN").val(ui.item.ICD10CODE);
				//			$("#txtMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
				//	        $("#txtCHANDOANRAVIEN").val(ui.item.ICD10NAME);
				//	        $("#txtMACHANDOANRAVIEN_KEMTHEO").focus();
				if(_loadFirstICD == false){
					$("#hidICD_RAVIEN").val(ui.item.ICD10CODE);
					$("#txtMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
					$("#txtCHANDOANRAVIEN").val(ui.item.ICD10NAME);
					if (cfObj.NGT_PHIMTAT_KB2 != 1) {
						$("#txtMACHANDOANRAVIEN_KEMTHEO").focus();
					}
				}else{
					if (data_arkb != null && data_arkb.length > 0 && data_arkb[0].MACHANDOANRAVIEN != '') {
						_loadFirstICD = false; 							// lan dau load co du lieu: khong load lai nua
					}else{
						$("#hidICD_RAVIEN").val(ui.item.ICD10CODE);
						$("#txtMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
						$("#txtCHANDOANRAVIEN").val(ui.item.ICD10NAME);
						if (cfObj.NGT_PHIMTAT_KB2 != 1) {
							$("#txtMACHANDOANRAVIEN_KEMTHEO").focus();
						}
						_loadFirstICD = false;
					}

				}
				return false;
			});
			ComboUtil.initComboGrid("txtMACHANDOANRAVIEN_KEMTHEO",_sql,sql_par, "600px", _col, function(event, ui) {
				if(ui.item.ICD10CODE == $('#hidICD_RAVIEN').val() && $('#hidICD_RAVIEN').val() != ""){
					DlgUtil.showMsg("Bệnh kèm theo vừa nhập không được trùng với bệnh chính.");
					return false;
				}

				var str = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
				if(str.indexOf(ui.item.ICD10CODE+'-') > -1){
					DlgUtil.showMsg("Bệnh kèm theo đã được nhập.");
					return false;
				}

				var _par=[_opt.khambenhId, _opt.phongId,ui.item.ICD10CODE,"1"];
				var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.ICD.TR",_par.join('$'));
				if(resultCheck == '0'){
					DlgUtil.showMsg("Đã tồn tại mã bệnh kèm theo trùng với phòng khám khác");
					return false;
				}

				//L2PT-15056
				var check = _checkCBBN(ui.item.ICD10CODE, "0");
				if(check == 1)
					return false;

				if(str != '')
					str += ";";
				//			var FOMAT_MA_BENHPHU = cfObj.FOMAT_MA_BENHPHU;
				//			if(FOMAT_MA_BENHPHU == 1){
				//				$("#txtCHANDOANRAVIEN_KEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
				//			}
				//			else{
				//				$("#txtCHANDOANRAVIEN_KEMTHEO").val(str +  ui.item.ICD10NAME+ "("+ ui.item.ICD10CODE + ")" );
				//			}
				$("#txtCHANDOANRAVIEN_KEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
				$("#txtMACHANDOANRAVIEN_KEMTHEO").val("");
				if (cfObj.NGT_PHIMTAT_KB2 != 1) {
					$("#cboXUTRIKHAMBENHID").focus();
				}
				return false;
			});
		}

		$("#txtstt_bdtang").on("click",function(e){
			_tanggiam_stt('txtGOIKHAMSTT_BD', 1);
		});

		$("#txtstt_bdgiam").on("click",function(e){
			_tanggiam_stt('txtGOIKHAMSTT_BD', 2);
		});
		$("#txtstt_kttang").on("click",function(e){
			_tanggiam_stt('txtGOIKHAMSTT_KT', 1);
		});

		$("#txtstt_ktgiam").on("click",function(e){
			_tanggiam_stt('txtGOIKHAMSTT_KT', 2);
		});

		$("#btnGOIKHAMSTT_CALL").on("click", function(){
			$('#toolbarIdbtnCall').click();
		});

		$("#txtGOIKHAMSTT_BD").on("click",function(e){
			$(this).select();
		});

		$("#txtGOIKHAMSTT_KT").on("click",function(e){
			$(this).select();
		});


		$("#btnGOIKHAMSTT_RESET").on("click", function(){
			$("#txtGOIKHAMSTT_BD").val("");
			$("#txtGOIKHAMSTT_KT").val("");
		});
		// END XU LY CHUC NANG GOI SO THEO KHOANG;

		// thong tin tai nan thuong tich
		$("#toolbarIdbtnKHAC_3").on("click", function() {
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				hosobenhanid: $("#hidHOSOBENHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgTaiNanThuongTich","divDlg","manager.jsp?func=../noitru/NTU02D035_Thongtintainanthuongtich",paramInput,"THÔNG TIN TAI NẠN THƯƠNG TÍCH",1400,600);
			DlgUtil.open("dlgTaiNanThuongTich");
		});

		$("#toolbarIdgroup_0_TNTT").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				hosobenhanid: $("#hidHOSOBENHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgTaiNanThuongTich","divDlg","manager.jsp?func=../noitru/NTU02D035_Thongtintainanthuongtich",paramInput,"THÔNG TIN TAI NẠN THƯƠNG TÍCH",1400,600);
			DlgUtil.open("dlgTaiNanThuongTich");
		});

		$("#toolbarIdbtn_TNTT").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				hosobenhanid: $("#hidHOSOBENHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgTaiNanThuongTich","divDlg","manager.jsp?func=../noitru/NTU02D035_Thongtintainanthuongtich",paramInput,"THÔNG TIN TAI NẠN THƯƠNG TÍCH",1400,600);
			DlgUtil.open("dlgTaiNanThuongTich");
		});

		//callback cho tai nan thuong tich
		EventUtil.setEvent("assignSevice_saveTNTT", function(e) {
			DlgUtil.showMsg(e.msg);
			DlgUtil.close("dlgTaiNanThuongTich");
		});
		// dannd_Ky so
		$("#toolbarIdbtnKHAC_KySo").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var paramInput = {
				tiepnhanid: $("#hidTIEPNHANID").val(),
				hosobenhanid: $("#hidHOSOBENHANID").val(),
				mabenhnhan: $('#txtMABENHNHAN').val(),
				tenbenhnhan: $('#txtTENBENHNHAN').val(),
				//tuoi: _row.TUOI,
				diachi: $('#txtDIACHI').val()
			}
			dlgPopup = DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../noitru/NTU01H101_DayLaiBenhAn", paramInput, "HIS - Đẩy hồ sơ bệnh án điện tử", 1200, 650);
			DlgUtil.open("divDlgBA");

		});
		// dannd_Ky so
		$("#toolbarIdbtnKySoPHIEUKBVV").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var obj = new Object();
			var _param = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4'
			}];
			_kyCaRpt(_param);
		});
		$("#toolbarIdbtnKySoPHIEUKBNGT").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var obj = new Object();
			var _param = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'NGT005_PHIEUKHAMBENHNGOAITRU_A4_10284'
			}];
			_kyCaRpt(_param);
		});
		$("#toolbarIdbtnKySoPHIEUKBNGT").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var obj = new Object();
			var _param = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'NGT005_PHIEUKHAMBENHNGOAITRU_A4_10284'
			}];
			_kyCaRpt(_param);
		});
		$("#toolbarIdbtnKySoPHIEUTHEODOI").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var obj = new Object();
			var _param = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'NGT02_PHIEUKHAM_THEODOIBN'
			}];
			_kyCaRpt(_param);
		});
		$("#toolbarIdbtnKySoPHIEUCAMKETXN").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var obj = new Object();
			var _param = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'RPT_GIAYCAMKETXN_BETAHCG'
			}];
			_kyCaRpt(_param);
		});
		// click button khám trên toolbar
		$("#toolbarIdbtnExam").on("click", function() {
			if (cfObj.NGT_CHECK_BADNCONMO == '1'){
				var tenphong = jsonrpc.AjaxJson.getOneValue("GET.PHONGMOBADN", [{"name":"[0]", "value": $("#hidHOSOBENHANID").val()}]);
				if (tenphong != undefined && tenphong != '' && tenphong != null   && tenphong != "null") {
					DlgUtil.showConfirm("Bệnh nhân đang điều trị bệnh án dài ngày tại " + tenphong,function(flag) {
						if (flag) {
							_start_kham(1, null);						// bat dau kham + mo form kbhb;
						}
					});
				}else{
					_start_kham(1, null);						// bat dau kham + mo form kbhb;
				}
			}else{
				_start_kham(1, null);						// bat dau kham + mo form kbhb;
			}
		});

		// mo benh an da co ra de cap nhat
		$("#toolbarIdbtnBANGT_0").on("click", function() {
			if (opt.hospital_id == '32260'){
				if ($("#cboXUTRIKHAMBENHID").val() == '6'){
					DlgUtil.showMsg('Bệnh nhân nhập viện không thể thao tác!');
					return false;
				}
				var paramInput={
					khambenhid: $("#hidKHAMBENHID").val(),
					hosobenhanid: $("#hidHOSOBENHANID").val(),
					benhnhanid: $("#hidBENHNHANID").val(),
					loaibenhanid: 20,
					maloaibenhan: 'NT01',
					// nvangoc start L2PT-6142
					sovaovien: ""
					// nvangoc end L2PT-6142
				};
				dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../benhan/"+'BAN01NT01_NgoaiTru',paramInput,"Cập nhật Bệnh án ngoại trú (chung)",1300,610);
				DlgUtil.open("divDlgBenhAnDetail");
			}else {
				_mobenhan_daingay();
			}
		});

		//mo benh an moi
		$("#toolbarIdbtnBANGT_2").on("click", function() {
			if (BADN_DONTHUOC !== "0" && BADN_DONTHUOC !== "1" ){
				var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, -1];
				var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
				if (_return == 1) {
					DlgUtil.showConfirm("Bạn có chắc chắn mở bệnh án ngoai trú dài ngày cho bệnh nhân không?", function (flag) {
						if (flag) {
							// xu tri dieu tri ngoai tru
							// kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
							var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, 0];
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
							if (_return == -1) {
								DlgUtil.showMsg('Bệnh chính của bệnh nhân không thuộc nhóm bệnh dài ngày nên không thể mở bệnh án');
							} else if (_return == 0) {
								DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thất bại');
							}  else if (_return == -3) {
								DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán bệnh chính');
							}else if (_return == -5) {
								DlgUtil.showMsg('Bệnh nhân đã được mở bệnh án ngoại trú dài ngày, vào menu Bệnh an để cập nhật thông tin');
							}else {
								DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thành công');
								var _khambenhid = $("#hidKHAMBENHID").val();
								_loadTabHanhChinh(_khambenhid);
							}
						}
					});
				}else if (_return == -2) {
					var dlgMOBA_DN = DlgUtil.buildPopup("dlgMOBA", "dlgMOBA_DN", "Mở BA dài ngày", 500, 110, {"zIndex":998});
					DlgUtil.open("dlgMOBA");
					var sql_par=[];
					sql_par.push({"name":"[0]","value": $('#hidBENHNHANID').val()} , {"name":"[1]","value": _opt.phongid }, {"name":"[2]","value": 1 });
					ComboUtil.getComboTag("cboHOSOBENHANDAINGAYID",sqlloadbadn,sql_par, "49",{value:0, text:'--Chọn--'},"sql","","");
					var btnOK = $('#btn_MOBA_OK');
					var btnClose = $('#btn_MOBA_CLOSE');
					btnOK.click(function() {
						if($("#cboHOSOBENHANDAINGAYID").val() == 0 ){
							DlgUtil.showMsg('Chưa chọn bệnh án dài ngày để mở !');
							return false;
						}
						var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, $("#cboHOSOBENHANDAINGAYID").val()];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
						if (_return == -1) {
							DlgUtil.showMsg('Bệnh chính của bệnh nhân không thuộc nhóm bệnh dài ngày nên không thể mở bệnh án');
						}else if (_return == 0) {
							DlgUtil.showMsg('Cập nhật bệnh án ngoại trú dài ngày thất bại');
						}else if (_return == -5) {
							DlgUtil.showMsg('Bệnh nhân đã được mở bệnh án ngoại trú dài ngày, vào menu Bệnh an để cập nhật thông tin');
						}else {
							DlgUtil.showMsg('Cập nhật bệnh án ngoại trú dài ngày thành công');
							dlgMOBA_DN.close();
							var _khambenhid = $("#hidKHAMBENHID").val();
							_loadTabHanhChinh(_khambenhid);
						}
					});
					btnClose.click(function() {
						dlgMOBA_DN.close();
					});
				}
			}else {
				DlgUtil.showConfirm("Bạn có chắc chắn mở bệnh án ngoai trú dài ngày cho bệnh nhân không?", function (flag) {
					if (flag) {
						if (BADN_DONTHUOC == "1") {
							moBADNTheoPhong();
						} else {
							moBenhAnDaiNgay();
						}
					}
				});
			}
		});
		//dong benh an
		$("#toolbarIdbtnBANGT_3").on("click", function() {
			if (BADN_DONTHUOC !== "0" && BADN_DONTHUOC !== "1"){
				var object = {};
				FormUtil.setFormToObject('divContentHC', '', object);
				var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
				dataDaingay = {};
				if (dataDaingays && dataDaingays.length > 0) {
					dataDaingay = dataDaingays[0];
				}
				if (dataDaingays.length == 0) {
					DlgUtil.showMsg("Bệnh nhân chưa mở hoặc đã đóng bệnh án dài ngày!");
					return;
				}
				// if(_loaibadaingay==36){
				var dlgDONGBA_DN = DlgUtil.buildPopup("dlgDONGBA", "dlgDONGBA_DN", "Đóng BA dài ngày", 500, 110, {"zIndex":998});
				DlgUtil.open("dlgDONGBA");
				$("#txtNGAYDONGBA").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
				var sql_par=[];
				sql_par.push({"name":"[0]","value": $('#hidBENHNHANID').val()} , {"name":"[1]","value": _opt.phongid }, {"name":"[2]","value": '1,9' });
				ComboUtil.getComboTag("cboHOSOBENHANDAINGAY_ID",sqlloadbadn,sql_par, "49",{value:0, text:'--Chọn--'},"sql","","");
				var btnOK = $('#btn_DONGBA_OK');
				var btnMO = $('#btn_MOBA');
				var btnClose = $('#btn_DONGBA_CLOSE');
				var check = 0;
				$('#cboHOSOBENHANDAINGAY_ID').on('change', function (e) {
					var sql_par = [];
					sql_par.push({	"name" : "[0]",	value : $('#cboHOSOBENHANDAINGAY_ID').val()});
					var ret = jsonrpc.AjaxJson.getOneValue("BAN.DAINGAY.CHECKTT", sql_par);
					if (ret == 9) {
						$('#btn_DONGBA_OK').attr("disabled", true);
						$('#btn_MOBA').attr("disabled", false);
						$("#txtNGAYDONGBA").val('');
						$("#txtNGAYDONGBA").attr("disabled", true);
						$("#calNGAYDONGBA").attr("disabled", true);
					} else {
						$('#btn_DONGBA_OK').attr("disabled", false);
						$('#btn_MOBA').attr("disabled", true);
					}
				});
				btnOK.click(function() {
					if($("#cboHOSOBENHANDAINGAY_ID").val() == 0 ){
						DlgUtil.showMsg('Chưa chọn bệnh án dài ngày để đóng!');
						return false;
					}
					if($("#txtNGAYDONGBA").val().trim() == ""){
						DlgUtil.showMsg("Bạn chưa nhập ngày đóng BA!");
						return false;
					}
					if(check == 1)
						return;
					var _hosobenhanid = $("#cboHOSOBENHANDAINGAY_ID").val();
					var _par_ins = [ _hosobenhanid, $("#txtNGAYDONGBA").val() ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONG.DAINGAY1",_par_ins.join('$'));
					switch (Number(_return)) {
						case -2:
							DlgUtil.showMsg("Bệnh nhân đã được đóng BADN!");
							break;
						case 1:
							DlgUtil.showMsg("Đóng BADN thành công!");
							dlgDONGBA_DN.close();
							check =1;
							var _khambenhid = $("#hidKHAMBENHID").val();
							_loadTabHanhChinh(_khambenhid);
							break;
						case -1:
							DlgUtil.showMsg("Đóng BADN không thành công!");
							break;
						default:
							DlgUtil.showMsg("Đã có lỗi xảy ra!");
							break;
					}
				});
				btnClose.click(function() {
					dlgDONGBA_DN.close();
				});
				btnMO.click(function() {
					if($("#cboHOSOBENHANDAINGAY_ID").val() == 0 ){
						DlgUtil.showMsg('Chưa chọn bệnh án dài ngày để mở!');
						return false;
					}
					if(check == 1)
						return;
					var _hosobenhanid = $("#cboHOSOBENHANDAINGAY_ID").val();
					var _par_ins = [ _hosobenhanid, $("#txtNGAYDONGBA").val() ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONG.DAINGAY1",_par_ins.join('$'));
					switch (Number(_return)) {
						case -2:
							DlgUtil.showMsg("Bệnh nhân đã được đóng BADN!");
							break;
						case 1:
							DlgUtil.showMsg("Mở BADN thành công!");
							dlgDONGBA_DN.close();
							var _khambenhid = $("#hidKHAMBENHID").val();
							_loadTabHanhChinh(_khambenhid);
							check =1;
							break;
						case -1:
							DlgUtil.showMsg("Mở BADN không thành công!");
							break;
						default:
							DlgUtil.showMsg("Đã có lỗi xảy ra!");
							break;
					}
				});
			}else{
				//L2PT-6928 ngocnva start
				if (BADN_DONTHUOC == "1") {
					dongBADNTheoPhong();
				} else {
					dongBenhAnDaiNgay();
				}
				//L2PT-6928 ngocnva end
			}
		});
		//tuyennx_add_start_20171027
		//Chọn bệnh án
		$("#toolbarIdbtnBANGT_1").on("click", function() {
			if(opt.hospital_id == "10284"){
				paramInput={
					benhnhanid : $("#hidBENHNHANID").val(),
					khambenhid : $("#hidKHAMBENHID").val(),
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					tiepnhanid : $("#hidTIEPNHANID").val(),
					doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
					loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
					phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),//L2PT-9165
					subDeptId : _opt.phongid
				};

				dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU01H031_NhapBenhAn", paramInput, "Nhập bệnh án", 500, 250);
				DlgUtil.open("divDlgNhapBenhAn");
			}else{
				var object = {};
				FormUtil.setFormToObject('divContentHC', '', object);
				var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
				dataDaingay = {};
				if (dataDaingays && dataDaingays.length > 0) {
					var loaibenhanid = $("#hidLOAIBENHANID").val();
					var sovaovien = $("#hidSOTHUTU").val();

					var _sql_par1 = RSUtil.buildParam("", [loaibenhanid]);
					var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
					var _rows1 = JSON.parse(_data1);
					var _sreenName = _rows1[0].URL;
					var _tenloaibenhan = _rows1[0].TENLOAIBENHAN;
					var _maloaibenhan = _rows1[0].MALOAIBENHAN;

					if (_sreenName != '') {
						paramInput = {
							khambenhid: $("#hidKHAMBENHID").val(),
							hosobenhanid: $("#hidHOSOBENHANID").val(),
							benhnhanid: $("#hidBENHNHANID").val(),
							loaibenhanid: loaibenhanid,
							maloaibenhan: _maloaibenhan,
							// nvangoc start L2PT-6142
							sovaovien: sovaovien
							// nvangoc end L2PT-6142
						};
						dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../benhan/" + _sreenName, paramInput, "Cập nhật " + _tenloaibenhan, 1300, 610);
						DlgUtil.open("divDlgBenhAnDetail");
					} else {
						paramInput={
							benhnhanid : $("#hidBENHNHANID").val(),
							khambenhid : $("#hidKHAMBENHID").val(),
							hosobenhanid : $("#hidHOSOBENHANID").val(),
							tiepnhanid : $("#hidTIEPNHANID").val(),
							doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
							loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
							phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),//L2PT-9165
							subDeptId : _opt.phongid
						};

						dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU01H031_NhapBenhAn", paramInput, "Nhập bệnh án", 500, 250);
						DlgUtil.open("divDlgNhapBenhAn");
					}
				}
				if (dataDaingays.length == 0) {
					DlgUtil.showMsg("Bệnh nhân chưa mở hoặc đã đóng bệnh án dài ngày!");
					return;
				}
			}
		});
		$("#toolbarIdbtnBANGT_5").on("click", function() {
			var  paramInput={
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),//L2PT-9165
				subDeptId : _opt.phongid
			};

			dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU01H031_NhapBenhAn", paramInput, "Nhập bệnh án", 500, 250);
			DlgUtil.open("divDlgNhapBenhAn");
		});
		EventUtil.setEvent("openBa", function (e) {
			DlgUtil.close("divDlgNhapBenhAn");
			_loadGridData(_opt.phongid);
			var _sql_par1 = RSUtil.buildParam("", [e.loaibenhanid]);
			var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
			var _rows1 = JSON.parse(_data1);
			var _sreenName = _rows1[0].URL;
			var _tenloaibenhan = _rows1[0].TENLOAIBENHAN;
			var _maloaibenhan = _rows1[0].MALOAIBENHAN;

			if (_sreenName != '') {
				paramInput = {
					khambenhid: $("#hidKHAMBENHID").val(),
					hosobenhanid: $("#hidHOSOBENHANID").val(),
					benhnhanid: $("#hidBENHNHANID").val(),
					loaibenhanid: e.loaibenhanid,
					maloaibenhan: _maloaibenhan,
					// nvangoc start L2PT-6142
					sovaovien: e.sovaovien
					// nvangoc end L2PT-6142
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../benhan/" + _sreenName, paramInput, "Cập nhật " + _tenloaibenhan, 1300, 610);
				DlgUtil.open("divDlgBenhAnDetail");
			} else {
				DlgUtil.showMsg('Không tồn tại loại bệnh án này trong dữ liệu');
				return;
			}
		});
		$("#toolbarIdbtnBANGT_4").on("click", function () {
			if(opt.hospital_id == "10284"){
				var i_hosobenhanid = $("#hidHOSOBENHANID").val();
				var i_loaibenhanid ;
				var i_benhnhanid = $("#hidBENHNHANID").val();
				var i_rptcode ;

				var par = [];
				par.push({ "name" : "[0]", "value" : $("#hidHOSOBENHANID").val()});

				var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("GET.THONGTINLOAIBA", par);
				var row = JSON.parse(data);
				if (row != null && row.length > 0) {
					i_loaibenhanid = row[0].LOAIBENHANID;
					i_rptcode = row[0].REPORT_CODE;
				}
				var _params = [{
					name: 'hosobenhanid',
					type: 'String',
					value: i_hosobenhanid
				},
					{
						name: 'loaibenhanid',
						type: 'String',
						value: i_loaibenhanid
					},
					{
						name: 'benhnhanid',
						type: 'String',
						value: i_benhnhanid
					},
					{
						name: 'RPT_CODE',
						type: 'String',
						value: i_rptcode
					}];

				var _check = CommonUtil.checkKyCaByParam(_params);

				if (_check > 0) {
					DlgUtil.showMsg("Bệnh án đã được ký số, không thể đưa ra khỏi bệnh án!");
				} else {
					DlgUtil.showConfirm("Bạn có chắc chắn đưa bệnh án ra khỏi bệnh án ?", function (flag) {
						if (flag) {
							// xu tri dieu tri ngoai tru
							// kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
							var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, 1];
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
							if (_return == 1) {
								DlgUtil.showMsg('Đưa ra khỏi bệnh án thành công!');
							}else {
								DlgUtil.showMsg('Đưa ra khỏi bệnh án không thành công!');
							}
						}
					});
				}
			}else{
				DlgUtil.showConfirm("Bạn có chắc chắn đưa bệnh án ra khỏi bệnh án dài ngày ?", function (flag) {
					if (flag) {
						// xu tri dieu tri ngoai tru
						// kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
						var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, 1];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
						if (_return == 1) {
							DlgUtil.showMsg('Đưa ra khỏi bệnh án dài ngày thành công!');
							var _khambenhid = $("#hidKHAMBENHID").val();
							_loadTabHanhChinh(_khambenhid);
						}else if(_return == -3 ){
							DlgUtil.showMsg('Bệnh nhân chưa được mở bệnh án dài ngày!');
						}  else {
							DlgUtil.showMsg('Đưa ra khỏi bệnh án dài ngày không thành công!');
						}
					}
				});
			}

		});
		// refresh lại thông tin sinh tồn cho form
		$("#toolbarIdbtnTiepTheo").on("click",function(e){
			refreshInfo();
		});
		// xoa thong tin nhap cho benh nhan
		$("#toolbarIdbtnXoa").on("click",function(e){
			var objData = new Object();
			FormUtil.setFormToObject("divKB","",objData);

			objData["KHAMBENHID"] = $("#hidKHAMBENHID").val();
			objData["PKHAMDANGKYID"] = $("#hidPHONGKHAMDANGKYID").val();
			objData["PHONGID"] = _opt.phongid;
			objData["KHOAID"] = _opt.khoaid;
			objData["CHINHPHU"] = _khamchinhphudetail;
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K002.XOA.VS2",JSON.stringify(objData));
			DlgUtil.showMsg("Cập nhật thành công",function(){
				if(close){
					EventUtil.raiseEvent("exam_cancel");
				}
				EventUtil.raiseEvent("exam_save",{khambenhid:$("#hidKHAMBENHID").val()});
			});
			/*if(ret === 1){
				DlgUtil.showMsg("Cập nhật thành công",function(){
					if(close){
						EventUtil.raiseEvent("exam_cancel");
					}
					EventUtil.raiseEvent("exam_save",{khambenhid:$("#hidKHAMBENHID").val()});
				});
			}else{
				DlgUtil.showMsg("Cập nhật không thành công",function(){
					EventUtil.raiseEvent("exam_save",{khambenhid:$("#hidKHAMBENHID").val()});
				});
			}*/
		});
		function refreshInfo(){
			$("#txtKHAMBENH_MACH").val('');
			$("#txtKHAMBENH_HUYETAP_HIGH").val('');
			$("#txtKHAMBENH_HUYETAP_LOW").val('');
			$("#txtKHAMBENH_CHIEUCAO").val('');
			$("#txtKHAMBENH_NHIETDO").val('');
			$("#txtKHAMBENH_NHIPTHO").val('');
			$("#txtKHAMBENH_CANNANG").val('');
			$("#txtBMI").val('');
			$("#txtQUATRINHBENHLY").val('');
			$("#txtMACHANDOANRAVIEN").val('');
			$("#txtCHANDOANRAVIEN").val('');
			$("#txtMACHANDOANRAVIEN_KEMTHEO").val('');
			$("#txtCHANDOANRAVIEN_KEMTHEO").val('');

		}

		//Ẩn hiện tab TTHC
		$("#btnTTHC_REMOVE").on("click",function(e){
			if($("#divTTHCBN").is(":visible")){
				$("#divTTHCBN").hide();
				$('#btnTTHC_REMOVE').html('');
				$('#btnTTHC_REMOVE').html('Hiện');
			}
			else{
				$("#divTTHCBN").show();
				$('#btnTTHC_REMOVE').html('');
				$('#btnTTHC_REMOVE').html('Ẩn');
			}
		});

		$("#toolbarIdbtnLuu").bindOnce("click",function() {
			//L2PT-103732
			var _validator=new DataValidator("divMain");
			var valid= _validator.validateForm();
			if(!valid){
				return false;
			}
			//L2PT-103732
			if (NGT_CANHBAO_DONGTIEN == '1') {
				var check_cp = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.GET.CHIPHI", $("#hidTIEPNHANID").val() + '$' + '9');
				if (check_cp != '0') {
					DlgUtil.showConfirm("Bệnh nhân còn dịch vụ chưa đóng tiền, Bạn có kết thúc khám chữa bệnh không? ",function(flag) {
						if(flag){
							_check_dongtien();
						}
					});
				}
				else
					_check_dongtien();
			}
			else
				_check_dongtien();
		},5000);
		function _check_dongtien() {
			//tuyennx_edit_start_20191209 L2PT-12390
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : $("#hidTIEPNHANID").val()
			});
			_sql_par.push({
				"name" : "[1]",
				value : $("#txtTHOIGIANRAVIEN").val()
			});
			_sql_par.push({
				"name" : "[2]",
				value : $("#hidKHAMBENHID").val()
			});
			var rs = jsonrpc.AjaxJson.getOneValue("NGT.NGT.TGKHAM", _sql_par);
			if(rs >0){
				DlgUtil.showMsg("BN có thời gian ra viện nhỏ hơn thời gian tiếp nhận, vui lòng nhập lại!");
				return ;
			}
			var rs_tg = jsonrpc.AjaxJson.getOneValue("NGT.CHECKDV.TGXTRI", _sql_par);
			if(rs_tg >0 && ($("#cboXUTRIKHAMBENHID").val() == "2" || $("#cboXUTRIKHAMBENHID").val() == "6")){
				var sophieu_arr = jsonrpc.AjaxJson.getOneValue("NGT.CHECKDV.TGXTRI1", _sql_par );
				DlgUtil.showMsg("BN có thời gian chỉ định dịch vụ lớn hơn thời gian xử trí, vui lòng nhập lại! số phiếu :" + sophieu_arr);
				return ;
			}
			if(rs_tg >0 && cfObj.NGT_KHAMBENH_CHECKCLS == '1'){
				var sophieu_arr = jsonrpc.AjaxJson.getOneValue("NGT.CHECKDV.TGXTRI1", _sql_par );
				DlgUtil.showMsg("BN có thời gian chỉ định dịch vụ lớn hơn thời gian xử trí, vui lòng nhập lại! số phiếu :" + sophieu_arr);
				return ;
			}

			if (_batbuockqdt == "1"){
				var _kqdtt = $("#cboKETQUADIEUTRIID").val();
				if (_kqdtt == null || _kqdtt == 'null' || _kqdtt == '0' || _kqdtt == "-1"){
					DlgUtil.showMsg("Chưa nhập thông tin kết quả điều trị. ");
					return ;
				}
			}
			var icd_phu_check = $('#txtCHANDOANRAVIEN_KEMTHEO').val();
			if(checkSoLuongICDKemTheo(icd_phu_check) >= 0 && parseInt(cfObj.SOLUONG_ICD_TRONGMOTLANDIEUTRI) !== 0) {
				DlgUtil.showMsg('Tổng số mã ICD trong một điều trị vượt quá số lượng quy định của BHXH!');
				return false;
			}
			//tuyennx_add_end_L2PT-16401

			//L2PT-43979
			var _obj1 = new Object();
			_obj1.PHONGKHAMDANGKYID = $('#hidPHONGKHAMDANGKYID').val();
			_obj1.HINHTHUCXUTRIID = $("#cboXUTRIKHAMBENHID").val();
			_obj1.THOIGIANRAVIEN = $("#txtTHOIGIANRAVIEN").val();
			if(NGT_CHECK_TG_KHAM_CLS != '0'){
				var _checktg = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.TGKHAM.CLS", JSON.stringify(_obj1));
				if(_checktg == '1'){
					DlgUtil.showMsg("BN có chỉ định dịch vụ CLS thời gian khám bệnh dưới "+NGT_CHECK_TG_KHAM_CLS+" phút không thể thao tác!");
					return ;
				}
			}

			var tgthietlap = cfObj.HIS_CANHBAO_KHAM5P_TL  ;
			var check5p_xt = cfObj.HIS_CANHBAO_KHAM5P_XT.split(';')  ;
			if (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU == '1'){
				var _sql_par = [];
				_sql_par.push({
						"name" : "[0]",
						value : $("#txtTHOIGIANRAVIEN").val()
					},{
						"name" : "[1]",
						value : $("#hidPHONGKHAMDANGKYID").val()
					}
				);
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECK5P.TGBD1", _sql_par)
			}else{
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.NGTKHAM5P1", _sql_par);
			}
			if(ret != '0'  && check5p_xt.includes($("#cboXUTRIKHAMBENHID").val())){
				if(cfObj.HIS_CANHBAO_KHAM5P == '2'){  //chặn  L2PT-13836
					DlgUtil.showMsg("BN có thời gian khám bệnh dưới " + tgthietlap + " phút không thể thao tác!");
					return ;
				}

				DlgUtil.showConfirm("BN có thời gian khám bệnh dưới " + tgthietlap + " phút bạn có muốn tiếp tục?",function(flag) {
					if(flag){
						if ( cfObj.HIS_CANHBAO_BNTUVONG == '1' && $("#cboXUTRIKHAMBENHID").val() == '8' ) {
							DlgUtil.showConfirm("Bệnh nhân xác nhận tử vong?", function (flag) {
								if (flag) {
									_start_kham(2, null);				// start kham + luu thong tin;
								}
							});
						}else{
							_start_kham(2, null);				// start kham + luu thong tin;
						}
						if (cfObj.NGT_PHIMTAT_KB2 == 1) {
							loadlandau = 1;
							_selectedRow(selectIndex.toString());
						}
					}
				});
			}
			else {
				if ( cfObj.HIS_CANHBAO_BNTUVONG == '1' && $("#cboXUTRIKHAMBENHID").val() == '8' ) {
					DlgUtil.showConfirm("Bệnh nhân xác nhận tử vong?", function (flag) {
						if (flag) {
							_start_kham(2, null);				// start kham + luu thong tin;
						}
					});
				}else{
					_start_kham(2, null);				// start kham + luu thong tin;
				}
				if (cfObj.NGT_PHIMTAT_KB2 == 1) {
					loadlandau = 1;
					_selectedRow(selectIndex.toString());
				}
			}
			//tuyennx_edit_end_20191209 L2PT-12390

			//BVTM-5938 day emr khi ket thuc kham
			day_emr_ktkham($('#hidHOSOBENHANID').val(), $("#hidTIEPNHANID").val());
		}

		// Khám sức khỏe cá nhân
		$("#toolbarIdbtnKSK").on("click", function () {
			var myVar = {
				khambenhId: $("#hidKHAMBENHID").val(),
				benhnhanId: $("#hidBENHNHANID").val(),
				tiepnhanId: $("#hidTIEPNHANID").val(),
				madichvu: $("#hidMADICHVU").val(),
				doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
				mabenhnhan: $("#txtMABENHNHAN").val(),
				hosobenhanId: $("#hidHOSOBENHANID").val(),
				loaibenhanId: $("#hidLOAIBENHANID").val(),
				phongId: $("#hidPHONGID").val(),
				dichvuid: $('#hidDICHVUID').val(),
				phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
			};
			var _width = $( document ).width()-250;
			var _height	 = $( window ).height()-150;
			dlgPopup=DlgUtil.buildPopupUrl("dlgKhamSK","divDlg","manager.jsp?func=../ngoaitru/NGT05K004_KhamSucKhoe",myVar,"Khám sức khỏe",_width,_height);
			DlgUtil.open("dlgKhamSK");
		});
		// Khám sức khỏe cán bộ
		$("#toolbarIdbtnKSKCANBO").on("click", function () {
			var myVar = {
				khambenhId: $("#hidKHAMBENHID").val(),
				benhnhanId: $("#hidBENHNHANID").val(),
				tiepnhanId: $("#hidTIEPNHANID").val(),
				mabenhnhan: $("#txtMABENHNHAN").val(),
				hosobenhanId: $("#hidHOSOBENHANID").val(),
				loaibenhanId: $("#hidLOAIBENHANID").val(),
				phongId: $("#hidPHONGID").val(),
				phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
				loaikskid : '1',
				maloaiksk : 'KSKCB'
			};
			var _width = $( document ).width()-250;
			var _height	 = $( window ).height()-150;
			dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../ksk/KSK_CANBO",myVar,"Khám sức khỏe cán bộ",_width,_height);
			DlgUtil.open("divDlgBenhAnDetail");
		});
		// Khám sức khỏe lái xe
		$("#toolbarIdbtnKSKLAIXE").on("click", function () {
			var myVar = {
				khambenhId: $("#hidKHAMBENHID").val(),
				benhnhanId: $("#hidBENHNHANID").val(),
				tiepnhanId: $("#hidTIEPNHANID").val(),
				mabenhnhan: $("#txtMABENHNHAN").val(),
				hosobenhanId: $("#hidHOSOBENHANID").val(),
				loaibenhanId: $("#hidLOAIBENHANID").val(),
				phongId: $("#hidPHONGID").val(),
				phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
				loaikskid : '2',
				maloaiksk : 'KSKLX'
			};
			var _width = $( document ).width()-250;
			var _height	 = $( window ).height()-150;
			dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../ksk/KSK_LAIXE",myVar,"Khám sức khỏe lái xe",_width,_height);
			DlgUtil.open("divDlgBenhAnDetail");
		});
		// Khám sức khỏe dưới 18 tuổi
		$("#toolbarIdbtnKSKDUOI18TUOI").on("click", function () {
			var myVar = {
				khambenhId: $("#hidKHAMBENHID").val(),
				benhnhanId: $("#hidBENHNHANID").val(),
				tiepnhanId: $("#hidTIEPNHANID").val(),
				mabenhnhan: $("#txtMABENHNHAN").val(),
				hosobenhanId: $("#hidHOSOBENHANID").val(),
				loaibenhanId: $("#hidLOAIBENHANID").val(),
				phongId: $("#hidPHONGID").val(),
				phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
				loaikskid : '3',
				maloaiksk : 'KSKD18'
			};
			var _width = $( document ).width()-250;
			var _height	 = $( window ).height()-150;
			dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../ksk/KSK_DUOI18TUOI",myVar,"Khám sức khỏe dưới 18 tuổi",_width,_height);
			DlgUtil.open("divDlgBenhAnDetail");
		});
		// Khám sức khỏe trên 18 tuổi
		$("#toolbarIdbtnKSKTREN18TUOI").on("click", function () {
			var myVar = {
				khambenhId: $("#hidKHAMBENHID").val(),
				benhnhanId: $("#hidBENHNHANID").val(),
				tiepnhanId: $("#hidTIEPNHANID").val(),
				mabenhnhan: $("#txtMABENHNHAN").val(),
				hosobenhanId: $("#hidHOSOBENHANID").val(),
				loaibenhanId: $("#hidLOAIBENHANID").val(),
				phongId: $("#hidPHONGID").val(),
				phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
				loaikskid : '4',
				maloaiksk : 'KSKT18'
			};
			var _width = $( document ).width()-250;
			var _height	 = $( window ).height()-150;
			dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../ksk/KSK_TREN18TUOI",myVar,"Khám sức khỏe trên 18 tuổi",_width,_height);
			DlgUtil.open("divDlgBenhAnDetail");
		});

		$("#toolbarIddrug_donkinh").on("click", function () {
			var dvkbId = '';
			var selRowId = $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);
			var myVar = {
				khambenhid: $("#hidKHAMBENHID").val(),
				phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgKham","divDlg","manager.jsp?func=../ngoaitru/NGT06K001_DOTHILUC",myVar,"Đơn kính",1300,650);
			DlgUtil.open("dlgKham");
		});
		$("#toolbarIdbtnDoThiLuc").on("click",function(e){
			if($('#hidKHAMBENHID').val() == ""){
				DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để tiếp tục thao tác!");
				return;
			}
			var param = {
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
				phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val()
			};
			EventUtil.setEvent("assignSevice_savetv",function(e){
				if(e.msg != ""){
					DlgUtil.showMsg(e.msg);
				}
			});
			dlgPopup=DlgUtil.buildPopupUrl("dlgCV","divDlg","manager.jsp?func=../ngoaitru/NGT06K002_DonKinh",param,'Đơn Kính',1300,650, close);
			DlgUtil.open("dlgCV");
		});

		$("#toolbarIdbtnLuuKhac").bindOnce("click",function() {
			//tuyennx_edit_start_20191209 L2PT-12390
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : $("#hidTIEPNHANID").val()
			});
			_sql_par.push({
				"name" : "[1]",
				value : $("#txtTHOIGIANRAVIEN").val()
			});
			_sql_par.push({
				"name" : "[2]",
				value : $("#hidKHAMBENHID").val()
			});
			var rs = jsonrpc.AjaxJson.getOneValue("NGT.NGT.TGKHAM", _sql_par);
			if(rs >0){
				DlgUtil.showMsg("BN có thời gian ra viện nhỏ hơn thời gian tiếp nhận, vui lòng nhập lại!");
				return ;
			}
			var rs_tg = jsonrpc.AjaxJson.getOneValue("NGT.CHECKDV.TGXTRI", _sql_par);
			if(rs_tg >0 && ($("#cboXUTRIKHAMBENHID").val() == "2" || $("#cboXUTRIKHAMBENHID").val() == "6")){
				DlgUtil.showMsg("BN có thời gian chỉ định dịch vụ lớn hơn thời gian xử trí, vui lòng nhập lại!");
				return ;
			}
			//L2PT-43979
			var _obj1 = new Object();
			_obj1.PHONGKHAMDANGKYID = $('#hidPHONGKHAMDANGKYID').val();
			_obj1.HINHTHUCXUTRIID = $("#cboXUTRIKHAMBENHID").val();
			_obj1.THOIGIANRAVIEN = $("#txtTHOIGIANRAVIEN").val();
			if(NGT_CHECK_TG_KHAM_CLS != '0'){
				var _checktg = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.TGKHAM.CLS", JSON.stringify(_obj1));
				if(_checktg == '1'){
					DlgUtil.showMsg("BN có chỉ định dịch vụ CLS thời gian khám bệnh dưới "+NGT_CHECK_TG_KHAM_CLS+" phút không thể thao tác!");
					return ;
				}
			}

			//tuyennx_add_end_L2PT-16401
			if (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU == '1'){
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.CHECK5PTGBATDAU", [$("#hidPHONGKHAMDANGKYID").val()].join('$'));
				if (data_ar != null && data_ar.length > 0) {
					var ret = data_ar[0].CHECK5P;
				}else{
					var ret = 0;
				}
			}else{
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.NGTKHAM5P", _sql_par);
			}

			if(ret != '0'
				&& ( (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU1 == '0' && $("#cboXUTRIKHAMBENHID").val() != 0  && $("#cboXUTRIKHAMBENHID").val() != "2" && $("#cboXUTRIKHAMBENHID").val() != "6" )
					|| (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU1 == '1' && $("#cboXUTRIKHAMBENHID").val() != 0 ) )){
				if(cfObj.HIS_CANHBAO_KHAM5P == '2'){  //chặn  L2PT-13836
					DlgUtil.showMsg("BN có thời gian khám bệnh dưới 5 phút không thể thao tác!");
					return ;
				}

				DlgUtil.showConfirm("BN có thời gian khám bệnh dưới 5 phút bạn có muốn tiếp tục?",function(flag) {
					if(flag){
						_start_kham(2, null);				// start kham + luu thong tin;
					}
				});
			}
			else
				_start_kham(2, null);
			//tuyennx_edit_end_20191209 L2PT-12390
		},5000);
		$("#toolbarIdtreatdt_pkcdphcn").on(
			"click",
			function() {
				if(!$("#hidKHAMBENHID").val()){
					DlgUtil.showMsg('Hãy chọn bệnh nhân!');
					return;
				}
				var khambenhid = $("#hidKHAMBENHID").val();
				var chucNang = 'PKCDPHCN';
				var name = chucNang + '@' + khambenhid;
				var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
					screen.height + ',width=' + screen.width);
				popup.moveTo(0, 0);
				popup.onbeforeunload = function() {
					loadGrid();
				}
			});

		//L2PT-15056
		function _checkCBBN(ICD, _type) {
			//if (cfObj.HIS_CANHBAO_BN == '1') {
			var objCheck = new Object();
			objCheck["TIEPNHANID"] = $("#hidTIEPNHANID").val();
			objCheck["ICD10"] = nvl(ICD, '0');
			objCheck["TYPE"] = _type;
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("CDDV.CBBN", JSON.stringify(objCheck));
			var obj = JSON.parse(result);
			if (obj.KETQUA && obj.KETQUA != '0' && obj.KETQUA != '1') {
				DlgUtil.showMsg(obj.GHICHU);
				if (obj.KETQUA == '3') {
					return 1;
				}
			}
			return 0;
			//}
		}

		function _capnhat(close, eventsource){
			if($("#hidKHAMCHINHPHU").val() != 1 && $("#cboXUTRIKHAMBENHID").val() != 0){
				DlgUtil.showMsg("Không thể xử trí với bệnh nhân khám thêm");
				return;
			}

			if(_kbatbuocBenhChinh == "0" && $('#txtMACHANDOANRAVIEN').val().trim() == ""){
				setErrValidate('txtMACHANDOANRAVIEN');
				DlgUtil.showMsg('Hãy nhập bệnh chính');
				return false;
			}

			if(_batbuoccdbd == "1" && $('#txtCHANDOANBANDAU').val().trim() == ""){
				setErrValidate('txtCHANDOANBANDAU');
				DlgUtil.showMsg('Hãy nhập chẩn đoán ban đầu');
				return false;
			}

			if($("#cboXUTRIKHAMBENHID").val() == "2" || $("#cboXUTRIKHAMBENHID").val() == "6"){
				if($("#cboKHOA").val() == "0" || $("#cboKHOA").val() == null || $("#cboKHOA").val() == 'null'){
					DlgUtil.showMsg("Chưa chọn Tới Khoa cho hướng xử trí này. ");
					$("#cboKHOA").focus();
					return;
				}
			}
			if ($('#txtTHOIGIANRAVIEN').val() != "" && !compareDate($('#txtNGAY_BD').val(),$('#txtTHOIGIANRAVIEN').val() , 'DD/MM/YYYY HH:mm:ss')) {
				DlgUtil.showMsg('Thời gian kết thúc khám không được nhỏ hơn thời gian bắt đầu khám!');
				return false;
			}
			if($("#hidCHUYENKHAMNGT").val() == "1"
				&& ($("#cboXUTRIKHAMBENHID").val() != "0" && $("#cboXUTRIKHAMBENHID").val() != null && $("#cboXUTRIKHAMBENHID").val() != 'null')){
				DlgUtil.showMsg('Kết thúc BN khám chuyên khoa bằng cách chọn chức năng [Kết Thúc Khám]. Chức năng chỉ lưu thông tin Khám bệnh Hỏi bệnh với đối tượng này. ');
			}
			if(cfObj.QD_4750 == 1 && $('#txtKHAMBENH_CANNANG').val().trim() == "" ){
				DlgUtil.showMsg("Chưa nhập cân nặng!");
				$('#txtKHAMBENH_CANNANG').focus();
				return false;
			}

			//L2PT-7901 ngocnva start
			if (sinhTonCanNang != 0 && ($("#txtDONVI_TUOI").val() == 3 || $("#txtDONVI_TUOI").val() == 2 && Number($('#txtSOTUOI').val()) < sinhTonCanNang * 12
				|| $("#txtDONVI_TUOI").val() == 1 && Number($('#txtSOTUOI').val()) < sinhTonCanNang) && !$('#txtKHAMBENH_CANNANG').val()) {
				DlgUtil.showMsg("Bệnh nhân dưới " + sinhTonCanNang + " tuổi yêu cầu nhập cân nặng");
				$('#txtKHAMBENH_CANNANG').focus();
				return false;
			}
			//L2PT-7901 ngocnva end

			var HIS_GIOIHAN_CANNANG = cfObj.HIS_GIOIHAN_CANNANG;
			//if($('#txtTUOI').val() != "" && HIS_GIOIHAN_CANNANG != null && HIS_GIOIHAN_CANNANG != "" && $('#txtKHAMBENH_CANNANG').val() != "" && ($("#cboTUOIID").val() == 3 || ($("#cboTUOIID").val() == 2 && $('#txtTUOI').val() <12 ))){
			if(HIS_GIOIHAN_CANNANG != null && HIS_GIOIHAN_CANNANG != "" && $('#txtKHAMBENH_CANNANG').val() != "" && Number($('#txtKHAMBENH_CANNANG').val()) > Number(HIS_GIOIHAN_CANNANG)){
				DlgUtil.showMsg("Cân nặng phải nhỏ hơn hoặc bằng "+ HIS_GIOIHAN_CANNANG);
				$('#txtKHAMBENH_CANNANG').focus();
				return false;
			}
			//}

			//tuyennx_add_end

			// SONDN 06/03/2020 L2PT-16781
			if($("#hidICD_RAVIEN").val() != $("#txtMACHANDOANRAVIEN").val()){
				DlgUtil.showMsg("Trường ICD chính đang chưa đúng, đang nhập là [" + $("#txtMACHANDOANRAVIEN").val()
					+ "], đang ghi nhận là [" + $("#hidICD_RAVIEN").val() + "], yêu cầu kiểm tra lại. ", function(){
					$('#txtMACHANDOANRAVIEN').focus();
				});
				return false;
			}
			// END SONDN 06/03/2020 L2PT-16781

			if(cfObj.QD_4750 == 1 && $("#cboXUTRIKHAMBENHID").val() != 0
				&& $('#cboXUTRIKHAMBENHID').val() != '2'
				&& $('#cboXUTRIKHAMBENHID').val() != '6'
				&& $("#txtKHAMBENH_PPDIEUTRI").val() == ''){
				DlgUtil.showMsg('Chưa nhập phương pháp điều trị!');
				$("#txtKHAMBENH_PPDIEUTRI").focus()
				return false;
			}
			// CHECK THONG TIN SINH TON;
			var _errmsg = "";
			if (_batbuocSinhTon == "1" || _batbuocSinhTon == "3" || _batbuocSinhTon == "4"){
				var _mach = $("#txtKHAMBENH_MACH").val();
				var _nhietdo = $("#txtKHAMBENH_NHIETDO").val().trim();
				var _huyetapthap = $("#txtKHAMBENH_HUYETAP_LOW").val().trim();
				var _huyetapcao = $("#txtKHAMBENH_HUYETAP_HIGH").val().trim();
				var _nhiptho = $("#txtKHAMBENH_NHIPTHO").val().trim();
				var _cannang = $("#txtKHAMBENH_CANNANG").val().trim();
				var _chieucao = $("#txtKHAMBENH_CHIEUCAO").val().trim();

				if (_mach == "") {
					_errmsg = "Chưa nhập thông tin mạch";
				} else if (_nhietdo == "") {
					_errmsg = "Chưa nhập thông tin nhiệt độ";
				} else if (_huyetapthap == "" || _huyetapcao == "") {
					_errmsg = "Chưa nhập thông tin huyết áp";
				} else if (_nhiptho == "") {
					_errmsg = "Chưa nhập thông tin nhịp thở";
				}
				//L2PT-7901 ngocnva start
				else if (sinhTonCanNang != 0 && !_cannang &&
					($("#cboTUOIID").val() == 3 || $("#cboTUOIID").val() == 2 && Number($('#txtTUOI').val()) < sinhTonCanNang * 12
						|| $("#cboTUOIID").val() == 1 && Number($('#txtTUOI').val()) < sinhTonCanNang)) {
					_errmsg = "Chưa nhập thông tin cân nặng";
				} else if (sinhTonCanNang == 0 && !_cannang) {
					_errmsg = "Chưa nhập thông tin cân nặng";
				}
				//L2PT-7901 ngocnva end
				else if (_chieucao == "") {
					_errmsg = "Chưa nhập thông tin chiều cao";
				}
			}

			if (
				(_batbuocSinhTon == "1" || _batbuocSinhTon == "3"
					|| (_batbuocSinhTon == "4" && $("#hidHINHTHUCVAOVIENID").val() == "3")
				) && _errmsg != ""
			){
				DlgUtil.showMsg(_errmsg);
				return;
			}

			var objData = new Object();
			FormUtil.setFormToObject("divKB","",objData);

			objData["TIEPNHANID"] = $("#hidTIEPNHANID").val();
			objData["KHAMBENHID"] = $("#hidKHAMBENHID").val();
			objData["PKHAMDANGKYID"] = $("#hidPHONGKHAMDANGKYID").val();
			objData["PHONGID"] = _opt.phongid;
			objData["KHOAID"] = _opt.khoaid;
			objData["CHINHPHU"] = _khamchinhphudetail;
			//objData["TENDIEUDUONG"] = $("#cboDIEUDUONGID option:selected").text();
			//L2PT-7289 ngocnva start
			// if(objData.XUTRIKHAMBENHID && !$('#txtTHOIGIANRAVIEN').val()){
			// 	$("#txtTHOIGIANRAVIEN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI'));
			// }

			if($("#hidCHUYENKHAMNGT").val() == "1"){
				objData["XUTRIKHAMBENHID"] = "0"; 				// chi luu thong tin kham benh hoi benh voi BN kham chuyen khoa;
			}

			//L2PT-7289 ngocnva end
			if (_batbuocSinhTon == "4" && _errmsg != ""){
				DlgUtil.showConfirm("Bệnh nhân chưa nhập đủ thông tin sinh tồn, bạn có muốn tiếp tục? ", function(flag){
					if (flag){
						_capnhat1(close, eventsource, objData);
					}
				});
			}else{
				_capnhat1(close, eventsource, objData);
			}
		}

		function _dayHenKhamQLHK(){
			// sondn L2PT-3523
			var _xttempArr = cfObj.APPBN_KTK_XUTRIDAYDL.split(',');
			if ($.inArray( $("#cboXUTRIKHAMBENHID").val()  , _xttempArr) >= 0){
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT10",$("#hidKHAMBENHID").val() + '$'+opt.phongid+ '$'+$("#hidHOSOBENHANID").val());
				if (data_ar && data_ar.length > 0){
					var objHenKham = {
						"MACOSOKHAM": opt.hospital_code,
						"TENBENHNHAN": data_ar[0].TENBENHNHAN,
						"MABENHNHAN": data_ar[0].MABENHNHAN,
						"NGAYSINH": data_ar[0].NGAYSINH,
						"MANGHENGHIEP": data_ar[0].MANGHENGHIEP,
						"GIOITINHID": data_ar[0].GIOITINHID,
						"MADT": data_ar[0].MADT,
						"MAQT": data_ar[0].MAQT,
						"DIENTHOAI": data_ar[0].DIENTHOAI,
						"CMT": data_ar[0].CMT,
						"MATINH": data_ar[0].MATINH,
						"MAHUYEN": data_ar[0].MAHUYEN,
						"MAXA": data_ar[0].MAXA,
						"DIACHI": data_ar[0].DIACHI,
						"SOTHE_BHYT": data_ar[0].SOTHE_BHYT,
						"NGAYGIOKHAM": data_ar[0].NGAYGIOKHAM,
						"MADVKHAM": data_ar[0].MADVKHAM,
						"MAPHONGKHAM": data_ar[0].MAPHONGKHAM,
						"MABS": data_ar[0].MABS,
						"LOAIHENKHAM": "1",						// 1 qua portal; 2 qua app mobile

						"DOITUONGBENHNHAN": data_ar[0].DOITUONGBENHNHAN,
						"TUYEN": data_ar[0].TUYEN,
						"GT_THE_TU": data_ar[0].GT_THE_TU,
						"GT_THE_DEN": data_ar[0].GT_THE_DEN,
						"MA_DKBH": data_ar[0].MA_DKBH,
						"MA_KHUVUC": data_ar[0].MA_KHUVUC,
						"BHYT_DIACHI": data_ar[0].BHYT_DIACHI,

						"GHICHU": "Đẩy từ form Khám bệnh 2 HIS L2 " + data_ar[0].GHICHU
					};


//						var ret = ajaxSvc.KhamOnlineWS.callApiAppBN_tiepnhanlichkhamcsyt(opt.hospital_code);
					var ret = ajaxSvc.KhamOnlineWS.callApiAppBN_tiepnhanlichkhamcsyt(objHenKham, opt.hospital_code);
					try{
						ret = $.parseJSON(ret);
						if (ret.statusCode == "000"){
							try{
								var _kq = ret.data;
								if (_kq.hasOwnProperty("errorCode")){
									if (_kq.errorCode == "0" && _kq.hasOwnProperty("result") && _kq.result.hasOwnProperty("MADANGKY")){
										var _objThongtinLog = new Object();
										_objThongtinLog["I_CHUCNANG"] = "QLHK day hen kham " + $('#hidHOSOBENHANID').val();
										_objThongtinLog["I_KQKETNOI"] = JSON.stringify(_kq.result);
										_objThongtinLog["MADANGKY"] = _kq.result.MADANGKY;
										var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS",JSON.stringify(_objThongtinLog));
										DlgUtil.showMsg("Đẩy thông tin hẹn khám lên App thành công, mã đăng ký: " + _kq.result.MADANGKY);
									}else{
										DlgUtil.showMsg("Thông báo từ hệ thống QLHK: " + _kq.errorCode + ":" + _kq.errorMessage);
									}
								}else{
									DlgUtil.showMsg("Không tìm thấy thông tin errorCode");
								}
							}catch(e){
								DlgUtil.showMsg("Lỗi thông tin dữ liệu trả về từ QLHK Data. ");
							}

						}else if (ret.statusCode == "099"){
							// qua thoi gian quy dinh;
							DlgUtil.showMsg("Không kết nối được tới API lấy dữ liệu (API Timeout). ");
						}else{
							// khong co du lieu;
							DlgUtil.showMsg("Không kết nối được tới API lấy dữ liệu. ");
						}
					}catch(e){
						DlgUtil.showMsg("Gửi thông tin lên QLHK thất bại. 1372");
					}
				}else{
					DlgUtil.showMsg("Lỗi không lấy được thông tin BN Gettt10");
				}
			}
			// end sondn L2PT-3523
		}

		function _capnhat1(close, eventsource, objData){
			objData["TRUNGTGIANNHAPVIEN"] = checktrungtgian;
			objData["LOAIRAVIEN"] = objData["LOAIRAVIEN"] == null || objData["LOAIRAVIEN"] == 'null' ? "0" : objData["LOAIRAVIEN"];
			objData["LANHDAOVIENID"] = objData["LANHDAOVIENID"] == null || objData["LANHDAOVIENID"] == 'null' ? "0" : objData["LANHDAOVIENID"];
			objData["LANHDAOKHOAID"] = objData["LANHDAOKHOAID"] == null || objData["LANHDAOKHOAID"] == 'null' ? "0" : objData["LANHDAOKHOAID"];

			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K002.LUU.VS2",JSON.stringify(objData));

			var rets = ret.split(',');
			if(rets[0] == 'r_checkdl'){
				var myVar={
					thongbao : rets[1]
				};
				dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT04K006_ThongBao",myVar,"Thông báo",600,420);
				DlgUtil.open("divDlgDichVu");
				return false;
			}

			// sondn L2PT-10548 : chinh lai cach return ham nay;
			if(rets[0] == '1'){
				//EventUtil.raiseEvent("exam_success",{msg:'Cập nhật thành công'});
				//tuyennx_edit_start_20171023 YC L2DKBD-692
				//yc L2DKBD-983 start
				/*var _par = [$("#hidKHAMBENHID").val(), _opt.phongid, $("#hidLOAIBENHANID").val(), _opt.khoaid ];
				var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONGBO.LEFT1",_par.join('$'));
				//yc L2DKBD-983 end
				if(_return==1){
					if(_enableTuDongLuu == "1"){
						EventUtil.raiseEvent("exam_save",{khambenhid:$("#hidKHAMBENHID").val()});
						if(eventsource == "btnCDDV")				{EventUtil.raiseEvent("exam_cddv",{khambenhid:that.opt.khambenhId});}
						else if(eventsource == "btnThuoc")			{EventUtil.raiseEvent("exam_capthuoc",{khambenhid:that.opt.khambenhId});}
						else if(eventsource == "btnXutri")			{EventUtil.raiseEvent("exam_xutri",{khambenhid:that.opt.khambenhId});}
						else if(eventsource == "btnThuocMuaNgoai")	{EventUtil.raiseEvent("exam_capthuocmuangoai",{khambenhid:that.opt.khambenhId});}
						else if(eventsource == "btnLuuDong")		{EventUtil.raiseEvent("exam_cancel");}
						else if(eventsource == "btnLuu")			{DlgUtil.showMsg("Cập nhật thành công");}
					}else{
						DlgUtil.showMsg("Cập nhật thành công",function(){
							if(close){
								EventUtil.raiseEvent("exam_cancel");
							}
							EventUtil.raiseEvent("exam_save",{khambenhid:$("#hidKHAMBENHID").val()});
						});
					//}

				}else{
					DlgUtil.showMsg("Có lỗi khi thực hiện!");
				}*/
				//tuyennx_edit_end
				//L2PT-40477
				if($('#cboXUTRIKHAMBENHID').val() == "2" || $('#cboXUTRIKHAMBENHID').val() == "6"){
					gw_tiepnhankham($('#hidTIEPNHANID').val());
					//gw_batdaukham($('#hidTIEPNHANID').val());
				}
				_dayDonThuocDT_KTK(cfObj.DTDT_DAY_DONTHUOC, $('#hidKHAMBENHID').val(), $("#hidLOAITIEPNHANID").val())	 //L2PT-30504
				//tuyennx_edit_start_20190425 L1PT-661
				if(cfObj.KHAM_ONLINE_WS == "1" && _ktkhamxutri == "1"){
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT",[$('#hidKHAMBENHID').val(),_opt.hospital_code].join('$'));
					if(data_ar && data_ar.length > 0){
						var objKhamOL = {
							"NGAYKETTHUC":data_ar[0].NGAY_RAVIEN,
							"MALUOTKHAM_HIS":$("#hidMAHOSOBENHAN").val(),
							"TRANGTHAI":"1",
							"NGAYHEN":data_ar[0].THOIGIANLICHHEN,
							"DONTHUOC":data_ar[0].SOPHIEU == "" ? '0' : data_ar[0].SOPHIEU,
							"MA_ICD":data_ar[0].MACHANDOANRAVIEN,
							"TEN_ICD":data_ar[0].CHANDOANRAVIEN,
							"BACSY":  _opt.fullname,
							"MABS":  _opt.user_name, //L2PT-5883
							"BENHPHU": data_ar[0].BENHPHU,
							"PHONGKHAM":  _opt._subdept_name,
							"HUONG_DIEUTRI": data_ar[0].HUONGDIEUTRI
						};
						//var objKhamOL = new Object();
//		    			var inputJson = JSON.stringify(objKhamOL);
						//var inputJson = "{\"NGAYKETTHUC\":\"20200410 1600\",\"MALUOTKHAM_HIS\":\"BV0000017641\",\"TRANGTHAI\":\"1\",\"NGAYHEN\":\"\",\"DONTHUOC\":\"\",\"MA_ICD\":\"0004607 - Chính\",\"TEN_ICD\":\"0004607 - Chính\"}";
						var _objThongtinLog = new Object();
						_objThongtinLog["I_CHUCNANG"] = "Trả KQK 1: "+$("#hidMAHOSOBENHAN").val();
						_objThongtinLog["I_KQKETNOI"] = JSON.stringify(objKhamOL);
						_objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
						var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS",JSON.stringify(_objThongtinLog));

						var ret = ajaxSvc.KhamOnlineWS.callApiAppBN('1',objKhamOL,_opt.hospital_code);

						_objThongtinLog["I_CHUCNANG"] = "Trả KQK 2: "+$("#hidMAHOSOBENHAN").val();
						_objThongtinLog["I_KQKETNOI"] = ret;
						_objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
						var f2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS",JSON.stringify(_objThongtinLog));
						if(ret == "-1"){
							DlgUtil.showMsg("Đã có lỗi xảy ra khi gửi dữ liệu khám online !");
						}
//						var ret = ajaxSvc.KhamOnlineWS.sendDataKetThuc(inputJson);
//						if(ret.includes("OK;")){
//							DlgUtil.showMsg("Gửi thông tin kết thúc khám thành công!");
//						}else{
//							DlgUtil.showMsg("Lỗi gửi thông tin kết thúc khám!");
//						}
					}
				}

				_dayHenKhamQLHK(); 					// sondn L2PT-3523

				if($("#hidTATTHONGBAOKBHB").val() == "0" ){
					DlgUtil.showMsg("Cập nhật thành công",function(){
						if(close){
							EventUtil.raiseEvent("exam_cancel");
						}
						EventUtil.raiseEvent("exam_save",{khambenhid:$("#hidKHAMBENHID").val()});
					});
				}
				else
					EventUtil.raiseEvent("exam_save",{khambenhid:$("#hidKHAMBENHID").val()});
				//tuyennx_edit_end_20190425 L1PT-661

				// ADDED BY SONDN 20082019 - L2PT-8183
				var _xtkbid = $("#cboXUTRIKHAMBENHID").val();
				var _tudonginbk = cfObj.HIS_TUDONG_IN_BANGKE;

				if (_tudonginbk == '1' && (_xtkbid == "1" || _xtkbid == "5" || _xtkbid == "7" || _xtkbid == "9")){
					vienphi_tinhtien.inBangKe($("#hidTIEPNHANID").val(), $("#hidDOITUONGBENHNHANID").val(), '1');
				}
				// END ADDED BY SONDN 20082019 - L2PT-8183
				if (_inphieuxutrikb == "1"){
					var par = [ {name : 'khambenhid', type : 'String', value : $("#hidKHAMBENHID").val()},
						{name : 'phongkhamdangkyid',type : 'String',value : $("#hidPHONGKHAMDANGKYID").val()} ];
					openReport('window', "PHIEU_XUTRI_KHAMBENH", "pdf", par);
				}

				// sondn L2PT-10548 : co bat cau hinh, va da ket thuc kham, moi tu dong duyet ke toan;
				if (_ktkhamxutri == "1" && rets.length >= 2 && rets[1] == "9"){
					var _tudongduyetketoan = cfObj.NGT_DUYETKETOAN_KTKHAM;
					var VP_DUYET_BH_KHI_DUYET_KETOAN = cfObj.VP_DUYET_BH_KHI_DUYET_KETOAN ;
					if (_tudongduyetketoan == "1" && $("#hidDOITUONGBENHNHANID").val() != "1"){
						var objData_DUYET = new Object();
						objData_DUYET["TIEPNHANID"] = $("#hidTIEPNHANID").val();
						objData_DUYET["NGAY"] = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
						objData_DUYET["NGAYRAVIEN"] = ($("#txtTHOIGIANRAVIEN").val() == '' ? jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS') : $("#txtTHOIGIANRAVIEN").val());
						objData_DUYET["DATRONVIEN"] = 0;
						objData_DUYET["SOLUONGQUYETTOAN"] = 0;
						objData_DUYET["LOAIDUYETBHYT"] = 0;
						objData_DUYET["KHOAID"] = _opt.khoaid;
						objData_DUYET["PHONGID"] = _opt.phongid;
						objData_DUYET["DUYET"] = 1;
						if ($("#hidDOITUONGBENHNHANID").val() == 1 &&
							(VP_DUYET_BH_KHI_DUYET_KETOAN == 0 || (VP_DUYET_BH_KHI_DUYET_KETOAN == 2
								&& $("#hidLOAITIEPNHANID").val() != 0) || (VP_DUYET_BH_KHI_DUYET_KETOAN == 3
								&& $("#hidLOAITIEPNHANID").val() == 0))) {
							objData_DUYET.FLAG_DUYET_BH = 0;
							var obj_BH = new Object();
							obj_BH.TIEPNHANID = $("#hidTIEPNHANID").val();
							obj_BH.NGAYDUYET = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
							var month = obj_BH.NGAYDUYET.split("/")[1];
							if (month <= 3)
								obj_BH.QUYDUYET = "1";
							else if (month <= 6)
								obj_BH.QUYDUYET = "2";
							else if (month <= 9)
								obj_BH.QUYDUYET = "3";
							else
								obj_BH.QUYDUYET = "4";
							obj_BH.HOSPITAL_CODE = _opt.hospital_code;;
							objData_DUYET.DATA_BH = obj_BH;
						} else {
							objData_DUYET.FLAG_DUYET_BH = 1;
						}
						var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI01T001.08",JSON.stringify(objData_DUYET));
						var ltext = "Tự động duyệt kế toán khi kết thúc khám bệnh ngoại trú: " + fl;
						save_log_act_form("VPI01T001.08", "TUDONGDUYETKETOAN_NGT", ltext, $("#hidTIEPNHANID").val().toString());
						if (fl.substr(0, 2) == -1) {
							DlgUtil.showMsg(fl.slice(2));
						} else if (fl == -89) {
							DlgUtil.showMsg("Có nhiều hơn 1 phiếu duyệt kế toán");
						} else if (fl == -90) {
							DlgUtil.showMsg("Chưa thiết lập khoa phòng");
						} else if (fl == -91) {
							DlgUtil.showMsg("Lỗi lưu dữ liệu dịch vụ");
						} else if (fl == -92) {
							DlgUtil.showMsg("Ngày ra viện không được lớn hơn thời gian hiện tại");
						} else if (fl == -930) {
							DlgUtil.showMsg("Bạn không có quyền duyệt kế toán, liên hệ với người quản trị");
						} else if (fl == -93) {
							DlgUtil.showMsg("Bạn không có quyền gỡ duyệt kế toán, liên hệ với người quản trị");
						} else if (fl == -931) {
							DlgUtil.showMsg("Hết thời gian được phép gỡ duyệt hồ sơ này, liên hệ với người quản trị");
						} else if (fl == -94) {
							DlgUtil.showMsg("Bệnh nhân chưa gỡ duyệt bảo hiểm");
						} else if (fl == -95) {
							DlgUtil.showMsg("Hồ sơ đã khóa");
						} else if (fl == -96) {
							DlgUtil.showMsg("Còn khoa/phòng chưa kết thúc");
						} else if (fl == -97) {
							DlgUtil.showMsg("Còn phòng khám chưa kết thúc");
						} else if (fl == -98) {
							DlgUtil.showMsg("Bệnh nhân cần thanh toán viện phí");
						} else if (fl == -981) {
							DlgUtil.showMsg("Lỗi khi kiểm tra dịch vụ");
						} else if (fl == -982) {
							DlgUtil.showMsg("Bệnh nhân còn dịch vụ chưa thu tiền");
						} else if (fl == -99) {
							DlgUtil.showMsg("Bệnh nhân chưa đóng bệnh án");
						} else if (fl == -88) {
							DlgUtil.showMsg("Bệnh nhân đã được duyệt kế toán trước đó");
						} else if (fl == -87) {
							DlgUtil.showMsg("Bệnh nhân chưa được duyệt kế toán hoặc đã gỡ duyệt");
						} else if (fl == -86) {
							DlgUtil.showMsg("Công khám đầu tiên có tỷ lệ khác 100%");
						} else if (fl == -85) {
							DlgUtil.showMsg("Hãy gỡ duyệt bảo hiểm trước");
						} else if (fl == -84) {
							DlgUtil.showMsg("Hồ sơ đã khóa, không thể gỡ duyệt kế toán");
						}
					}

					//dannd tu dong day cong giam dinh bhyt L2PT-21529
					if (cfObj.VPI_AUTO_DUYET_BHYT_NGT == '1'
						&& $("#hidDOITUONGBENHNHANID").val() == "1" ) {
						var dtar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT10",$("#hidKHAMBENHID").val() + '$'+_opt.phongid+ '$'+$("#hidHOSOBENHANID").val());
						if (dtar != null && dtar.length > 0) {
							if (dtar[0].TRANGTHAITIEPNHAN == "1"){
								var _duyet_bh = duyetBHYT($("#hidTIEPNHANID").val(), opt.user_id, opt.hospital_code);
								var ltext = "Tự động duyệt BHYT khi kết thúc khám bệnh ngoại trú: " + _duyet_bh;
								save_log_act_form("duyetBHYT", "TUDONGDUYETBHYT_NGT", ltext, $("#hidTIEPNHANID").val().toString());
								if (_duyet_bh != 0) {
									DlgUtil.showMsg("Có lỗi xảy ra trong quá trình đẩy dữ liệu lên cổng giám định!");
								}
							}
						}else{
							DlgUtil.showMsg("Không lấy được thông tin bệnh nhân sau xử trí. 1526");
						}
					}
					//end dannd

				}
				// end sondn L2PT-10548

				// sondn nhanh return 200
				if (cfObj.HIS_TUDONG_IN_PHIEU_NV == "1" && $("#cboXUTRIKHAMBENHID").val() == "6"){
					var par = [];
					if(opt._hospital_id == "965"){			// BDHCM : xuat excel chuc nang nay;
						par = [ {
							name : 'i_hid',
							type : 'String',
							value : opt._hospital_id
						}, {
							name : 'i_sch',
							type : 'String',
							value : opt.db_schema
						}, {
							name : 'khambenhid',
							type : 'String',
							value : $("#hidKHAMBENHID").val()
						} ];


						if($("#cboDOITUONGBENHNHANID").val()== 3){
							var rpName= "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
							CommonUtil.inPhieu('window', "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'xlsx', par, rpName);
						}else{
							var rpName= "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
							CommonUtil.inPhieu('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'xlsx', par, rpName);
						}
					}else{
						if($("#hidDOITUONGBENHNHANID").val()== 3){
							_openReport(par, "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'pdf');
						}else{
							_openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
						}
					}

				}

				// sondn nhanh return 300
				if (cfObj.HIS_TUDONG_IN_PHIEU_CV == "1" && $("#cboXUTRIKHAMBENHID").val() == "7"){
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : $("#hidKHAMBENHID").val()
					}];

					if(opt._hospital_id == "965"){					// DA KHOA BUU DIEN IN DOCX
						var rpName= "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
						CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'docx', par, rpName);
					}else if(opt._hospital_id == "1111"){					// DK LAN: Ham khac;
						var par1111 = [ {
							name : 'phongkhamdangkyid',
							type : 'String',
							value : $("#hidPHONGKHAMDANGKYID").val()
						}];
						openReport('window', "NGT003_GIAYCHUYENTUYEN_LAN_1111_2", "pdf", par1111);

					}else{
						openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
					}

				}

				// sondn nhanh return 400;
				if (cfObj.HIS_TUDONG_IN_PHIEU_HK == "1" && $("#cboXUTRIKHAMBENHID").val() == "5"){
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : $("#hidKHAMBENHID").val()
					}];
					openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);

				}
			}else if(ret =='810'){
				DlgUtil.showMsg('ICD chính không được để trống !');
			}
			else if(ret =='loixoaxutri'){
				DlgUtil.showMsg('Bệnh nhân đã duyệt BH/Kế toán/đã bắt đầu thông tin nội trú hoặc điều trị ngoại trú.');
			}
			//L2PT-18998
			else if(ret =='910'){
				DlgUtil.showMsg('Định dạng mã bệnh kèm theo không đúng.');
			}
				//L2PT-18998
			//tuyennx_add_start_L2PT-14715
			else if(ret =='saimabenhchinh'){
				DlgUtil.showMsg('Định dạng mã bệnh chính theo không đúng.');
			}
				//tuyennx_add_end_L2PT-14715
			//L2PT-7289 ngocnva start
			else if (ret == -2) {
				DlgUtil.showMsg('Bệnh nhân chưa có đơn thuốc');
			}else if (ret == 'trungmabenhandaingay'){
				DlgUtil.showMsg('Trùng mã bệnh án, yêu cầu kiểm tra lại. ');
			} else if (ret == -3) {
				DlgUtil.showMsg('Bệnh nhân chưa nhập thông tin chuyển viện');
			} else if (ret == -4) {
				DlgUtil.showMsg('Thời gian xử trí phải là ngày hiện tại');
			}else if(ret.startsWith("conphongdangkham")){
				DlgUtil.showMsg("Bệnh nhân còn phòng chờ/đang khám khác phòng hiện tại, yêu cầu kết thúc trước khi xử trí:<br>- "+ret.replace("conphongdangkham", ""));
			}
			//tuyennx_add_end_20180927
			else if(ret == "dvcls"){
				DlgUtil.showMsg('Chưa hoàn thành dịch vụ CLS.');
			}else if(ret == "dadongbenhan"){
				DlgUtil.showMsg('Bệnh nhân đã đóng bệnh án. Yêu cầu mở bệnh án để xử trí tiếp. ');
			}
			//L2PT-7289 ngocnva end
			else if(ret == "codonthuoc"){
				DlgUtil.showMsg('Bệnh nhân có đơn thuốc!');
			}
			else if(ret.startsWith("LoiDuyetBHYT")){
				DlgUtil.showMsg("Lỗi duyệt BHYT:<br>"+ret.replace("LoiDuyetBHYT", ""));
			}
			else if(ret == "tnttcapcuu"){
				DlgUtil.showMsg('Bệnh nhân cấp cứu bắt buộc nhập tai nan thương tích!');
			}else if(ret == "khamchinhconphong"){
				DlgUtil.showMsg('Còn phòng khám phụ chưa hoàn thành, không cho phép xử trí kết thúc');
			}else if(ret == "ICD_BB_CLS"){
				DlgUtil.showMsg('Mã bệnh chính này bắt buộc phải có chỉ định CLS !');
			}else if(ret == "icdketthuckham"){
				DlgUtil.showMsg('ICD không được để trống!');
			}else if(ret == "bnkhactrongnganh"){
				DlgUtil.showMsg('Bệnh nhân không phải đối tượng trong ngành, không được thực hiện xử trí này.');
			}else if(ret == "his_kham_chinhphu_conphongkhamphu"){
				DlgUtil.showMsg('Còn phòng khám phụ chưa kết thúc khám, không thể kết thúc khám phòng khám chính !');
			}else if(ret == "thoigiantrakqcls"){
				DlgUtil.showMsg('Thời gian hoàn thành CLS lớn hơn thời gian kết thúc khám!');
			}else if(ret == "thoigiankqcls"){
				var sophut = cfObj.NGT_CANHBAO_TGKQCLS
				DlgUtil.showMsg('Thời gian trả kết quả CLS đến thời gian xử trí nhỏ hơn '+ sophut +' phút!');
			}else if(ret == "thoigianktpttt"){
				DlgUtil.showMsg('Thời gian kết thúc PTTT lớn hơn thời gian kết thúc khám!');
			}else if(ret == "codichvudvvpchuatt"){
				DlgUtil.showMsg('Bệnh nhân có dịch vụ cần thu tiền trước khi xử trí nhập viện!');
			}else if(ret == 'ngaydichvu'){
				DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện hoặc thời gian chỉ định nhỏ hơn thời gian tiếp nhận, không thể kết thúc khám.');
			}else if (ret == "vuotquaicd") {
				DlgUtil.showMsg('Tổng số mã ICD trong một điều trị vượt quá số lượng quy định của BHXH!');
			}else if(ret == "nhapvien_trungtgian"){
				if (cfObj.NGT_CHECKTRUNGTGIAN_NHAPVIEN == 1) {
					DlgUtil.showConfirm("Thời gian nhập viện trùng với bệnh nhân khác, bạn có tiếp tục? ",function(flag) {
						if(flag){
							checktrungtgian = 1;
							_capnhat1(close, eventsource, objData);
						}else {
							return false;
						}
					});
					return false;
				}else {
					DlgUtil.showMsg('Thời gian nhập viện trùng với bệnh nhân khác vui lòng thử lại trong ít phút!');
				}
			}else{
				DlgUtil.showMsg("Cập nhật không thành công " + ret,function(){
					EventUtil.raiseEvent("exam_save",{khambenhid:$("#hidKHAMBENHID").val()});
				});
			}
		}

		//tuyennx_add_start_20171027 yc HISL2NT-485


		function _openCDDV(){
			if(check_doituongbn() == '-1'){
				DlgUtil.showMsg('Hãy chọn lại bệnh nhân muốn thao tác do BN đã thay đổi đối tượng');
				return;
			}
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid :
					(_tylevpchuyenphanhe == "2" || _tylevpchuyenphanhe == "3") && $("#hidCHUYENKHAMNGT").val() == "1"
						? _tylevpchuyenphanhe : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $('#hidPHONGID').val(),
				bacsike : $('#hidBACSYKE').val()
			};
			var cddvDLKHA = cfObj.CDDV_GIAODIEN_KHA == '1' ? true : false;
			if (cddvDLKHA) {
				dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV_KHA"+ "&loaidichvu=" + 5,myVar,"Tạo phiếu chỉ định dịch vụ",1300,660);
				DlgUtil.open("divDlgDichVu");
			}
			else{
				dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV"+ "&loaidichvu=" + 5,myVar,"Tạo phiếu chỉ định dịch vụ",1300,660);
				DlgUtil.open("divDlgDichVu");
			}
		}

		// click button khám dich vu trên toolbar
		$("#toolbarIdbtnService").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
			if ($("#hidKHAMBENHID").val() == '-1') {
				DlgUtil.showMsg("Vui lòng chọn bệnh nhân trước khi thực hiện.");
				return;
			}
			// sondn L2PT-26510
			if (_batbuoccddv == "1"){
				//L2PT-69380
				if ($("#txtCHANDOANBANDAU").val().trim() == "" && $("#txtMACHANDOANRAVIEN").val().trim() == ""
					&& $("#txtCHANDOANRAVIEN_KEMTHEO").val().trim() == "" ){
					DlgUtil.showMsg("Yêu cầu nhập ICD chính hoặc Chẩn đoán ban đầu trước khi chỉ định dịch vụ. ");
					return;
				}
			}
			if ($("#txtQUATRINHBENHLY").val().trim() == '' &&  cfObj.NGT_BATBUOC_QTBL_KB2 == "1" ){
				DlgUtil.showMsg("Yêu cầu nhập quá trình bệnh lý ");
				return;
			}
			// end sondn L2PT-26510
			//dannd_L2PT-70290
			var checktgiancddv = cfObj.NGT_KB_CHECKTGCDDV;
			if (rowData.THOIGIANBD != '' &&  checktgiancddv != 0 ){
				var ressult = diffDate(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'),rowData.THOIGIANBD,'DD/MM/YYYY HH:mm:ss','minutes');
				if (ressult < checktgiancddv ) {
					DlgUtil.showMsg("Thời gian bắt đầu khám đến thời gian chỉ định dịch vụ chưa đủ " + checktgiancddv + " phút không thể thực hiện!");
					return;
				}
			}
			_start_kham(3, null);							// start kham + chi dinh dich vu;
		});

		$("#toolbarIdbtndrug").on("click", function() {
			if ($("#txtQUATRINHBENHLY").val().trim() == '' &&  cfObj.NGT_BATBUOC_QTBL_KB2 == "1" ){
				DlgUtil.showMsg("Yêu cầu nhập quá trình bệnh lý ");
				return;
			}
		});

		// click button khám > tạo phiếu khám trên toolbar BVTM-7972
		$("#toolbarIdtreat_1").on("click", function() {
			var myVar={
				khambenhId : $("#hidKHAMBENHID").val(),
				maubenhphamId:-1,
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				hosobenhanId : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanId : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanId : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid

			};
			EventUtil.setEvent("assignSevice_cancelP",function(e){
				DlgUtil.close("dlgPhieukham");
			});

			EventUtil.setEvent("treatment_cancel",function(e){
				$('#tabDieuTri').ntu02d027_dt({
					_grdDieuTri : 'grdDieuTri',
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid:  $("#hidBENHNHANID").val(),
					_lnmbp:	LNMBP_DieuTri,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
				DlgUtil.close("dlgPhieukham");
				DlgUtil.close("divDlgPhieuDieuTri");
			});

			var _width = $(document).width() - 150;
			var _height = $(window).height() - 150;
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieukham","divDlg1","manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT",myVar,"Phiếu điều trị",_width,_height);

			//dlgPopup.open();
			DlgUtil.open("dlgPhieukham");
			EventUtil.setEvent("assignSevice_cancel",function(e){
				DlgUtil.close("dlgCDDV");
			});
		});

		//Tao phieu dieu tri
		$("#toolbarIdbtnPhieuDT").on("click", function() {
			//START -- HISL2TK-297--hongdq-29052018
			var selRowId = $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);
			//END -- HISL2TK-297--hongdq-29052018
			paramInput={
				khambenhId : $("#hidKHAMBENHID").val(),
				maubenhphamId : -1,
				subDeptId:$("#hidPHONGID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				hosobenhanId : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanId : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanId : $("#hidLOAITIEPNHANID").val(),
				//START -- HISL2TK-297--hongdq-29052018
				thoigian_vaovien : $("#hidTHOIGIANVAOVIEN").val(),
				bacsydieutriid : rowData.BACSYDIEUTRIID
				//END -- HISL2TK-297--hongdq-29052018
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgPhieuDieuTri","divDlg","manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT",paramInput,"Tạo phiếu điều trị",1330,600);
			EventUtil.setEvent("divDlgPhieuDieuTri_onClose",function(name){
				$('#tcDieuTri').ntu02d027_dt({
					_grdDieuTri : 'grdDieuTri',
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid: $("#hidBENHNHANID").val(),
					_thoigianvaovien: $("#hidTHOIGIANVAOVIEN").val(),
					_bacsidieutri: rowData.BACSYDIEUTRIID,
					_lnmbp:	LNMBP_DieuTri,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			});
			DlgUtil.open("divDlgPhieuDieuTri");
		});
		//tao phieu cham soc
		$("#toolbarIdbtnTAOPHIEUCHAMSOC").on("click", function() {
			paramInput = {
				maubenhpham_id : "",
				khambenh_id : $("#hidKHAMBENHID").val()
			};
			//START -- HISL2TK-536 -- hongdq -- 22052018
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "HIS_NTU_CHAMSOC_BND;HIS_NTU_CHAMSOC_DHYTB");
			if (data_ar != null && data_ar.length > 0) {
				if (data_ar[0].HIS_NTU_CHAMSOC_BND == '1') {
					if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
						DlgUtil.showMsg('Bạn chưa chọn bệnh nhân.');
						return;
					}
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					paramInput = {
						khambenhid : rowData.KHAMBENHID,
						tiepnhanid : rowData.TIEPNHANID,
						hosobenhanid : rowData.HOSOBENHANID,
						benhnhanid : rowData.BENHNHANID,
						mabenhan : rowData.MAHOSOBENHAN,
						mabenhnhan : rowData.MABENHNHAN,
						tenbenhnhan : rowData.TENBENHNHAN,
						maubenhpham_id : "",
						action : 'add'
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuchamsoc", "divDlg", "manager.jsp?func=../noitru/NTU02D092_PhieuChamsoc_bnd", paramInput, "Tạo phiếu chăm sóc cấp I", 1300, 580);
					DlgUtil.open("divDlgPhieuchamsoc");
//				dlgPopup=DlgUtil.buildPopupUrl("divDlgChamSoc","divDlg","manager.jsp?func=../noitru/NTU02D092_PhieuChamsoc_bnd",paramInput,"Tạo phiếu chăm sóc",750,500);
//				DlgUtil.open("divDlgChamSoc");
				} else if (data_ar[0].HIS_NTU_CHAMSOC_DHYTB == '1') {
					if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
						DlgUtil.showMsg('Bạn chưa chọn bệnh nhân.');
						return;
					}
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					paramInput = {
						khambenhid : rowData.KHAMBENHID,
						tiepnhanid : rowData.TIEPNHANID,
						hosobenhanid : rowData.HOSOBENHANID,
						benhnhanid : rowData.BENHNHANID,
						mabenhan : rowData.MAHOSOBENHAN,
						mabenhnhan : rowData.MABENHNHAN,
						tenbenhnhan : rowData.TENBENHNHAN,
						maubenhpham_id : "",
						action : 'add'
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgChamSoc", "divDlg", "manager.jsp?func=../noitru/NTU02D092_PhieuChamsoc_TBH", paramInput, "Tạo phiếu chăm sóc", 1300, 580);
					DlgUtil.open("divDlgChamSoc");
				} else {
					dlgPopup = DlgUtil.buildPopupUrl("divDlgChamSoc", "divDlg", "manager.jsp?func=../noitru/NTU02D004_PhieuChamSoc", paramInput, "Tạo phiếu chăm sóc", 750, 500);
					DlgUtil.open("divDlgChamSoc");
				}
				//END -- HISL2TK-536 -- hongdq -- 22052018
			}
		});

		EventUtil.setEvent("assignSevice_SaveChamSoc", function(e) {
			DlgUtil.showMsg(e.msg);
			//widget cho tab cham soc
			$('#tabChamSoc').ntu02d028_pcs({
				_grdChamSoc : 'grdChamSoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_ChamSoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			DlgUtil.close("divDlgChamSoc");
		});

		// click button Thuốc > Tạo phiếu thuốc.
		$("#toolbarIddrug_1").on("click",function(e){
			var _obj = new Object();
			_obj._opt = '02D010';
			_obj._loaikedon = 1;
			_obj._title = "Chỉ định thuốc";
			_start_kham(4, _obj);
//			_openDialogThuoc('02D010', 1, "Chỉ định thuốc");
		});

		// tạo đơn thuốc từ kho
		//HaNv_051222: Thao tác menu trên mobie - L2PT-29887
		$("#toolbarIddrug_khothuoc").on("click",function(e){
			//$("#toolbarIddrug_khothuoc").hammer().bind("tap", function() {
			var _obj = new Object();
			_obj._opt = '02D010';
			_obj._loaikedon = 1;
			_obj._title = "Chỉ định thuốc từ kho";
			_obj._loadkhotheo = '1';
			//tuyennx_add_start L2PT-14584
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : $("#hidTIEPNHANID").val()
			});
			var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECKKT_VP", _sql_par);
			if(ret == '1'){
				DlgUtil.showMsg('Không thể kê thuốc từ kho với bệnh nhân viện phí hoặc trái tuyến!');
				return;
			}
			//tuyennx_add_end L2PT-14584
			_start_kham(4, _obj);

//			_openDialogThuoc('02D010', 1, "Chỉ định thuốc từ kho", '1');
		});

		// tạo đơn thuốc từ tủ trực
		//HaNv_051222: Thao tác menu trên mobie - L2PT-29887
		$("#toolbarIddrug_tutruc").on("click",function(e){
			//$("#toolbarIddrug_tutruc").hammer().bind("tap", function() {
			var _obj = new Object();
			_obj._opt = '02D010';
			_obj._loaikedon = 1;
			_obj._title = "Chỉ định thuốc từ tủ trực";
			_obj._loadkhotheo = '2';
			_start_kham(11, _obj);  //tuyennx edit start dklongan

//			_openDialogThuoc('02D010', 1, "Chỉ định thuốc từ tủ trực", '2');
		});

		//L2PT-35459
		$("#toolbarIddrug_khothuoc_vp").hammer().bind("tap", function() {
			var _obj = new Object();
			_obj._opt = '02D010';
			_obj._loaikedon = 1;
			_obj._title = "Chỉ định thuốc từ kho (viện phí)";
			_obj._loadkhotheo = '1';
			//tuyennx_add_start L2PT-14584
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : $("#hidTIEPNHANID").val()
			});
			var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECKKT_VP", _sql_par);
			if(ret == '1'){
				DlgUtil.showMsg('Không thể kê thuốc từ kho với bệnh nhân viện phí hoặc trái tuyến!');
				return;
			}
			//tuyennx_add_end L2PT-14584
			_start_kham(15, _obj);
//			_openDialogThuoc('02D010', 1, "Chỉ định thuốc từ kho", '1');
		});

		// click button Thuốc > Tạo phiếu thuốc.
		$("#toolbarIddrug_le").on("click",function(e){
			var _obj = new Object();
			_obj._opt = '02D010_1';
			_obj._loaikedon = 0;
			_obj._title = "Kê thuốc lẻ";
			_start_kham(4, _obj);

//			_openDialogThuoc('02D010_1', 0, "Kê thuốc lẻ");
		});

		$("#toolbarIddrug_dtnhathuoc").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			var _obj = new Object();
			_obj._opt = '02D019';
			_obj._loaikedon = 0;
			_obj._title = "Mua thuốc nhà thuốc";
			_start_kham(4, _obj);

//			_openDialogThuoc('02D019', 0, "Mua thuốc nhà thuốc");
		});

		// click button Thuốc > Tạo phiếu thuốc không thuốc.
		$("#toolbarIddrug_1kt").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});
			_start_kham(10, null);
//			_openDialogThuocK('02K044', 0, "Chỉ định thuốc không thuốc");
		});

		// click button Thuốc > Tạo phiếu thuốc đông y.
		$("#toolbarIddrug_1dy").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			var _obj = new Object();
			_obj._opt = '02D017';
			_obj._loaikedon = 1;
			_obj._title = "Chỉ định thuốc YHCT";
			_start_kham(4, _obj);
//			_openDialogThuoc('02D017', 1, "Chỉ định thuốc YHCT");
		});

		// click button Thuốc > Tạo phiếu trả thuốc đông y.
		$("#toolbarIddrug_2dy").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			var _obj = new Object();
			_obj._opt = '02D018';
			_obj._loaikedon = 1;
			_obj._title = "Trả thuốc YHCT";
			_start_kham(4, _obj);
//			_openDialogThuoc('02D018', 1, "Trả thuốc YHCT");
		});

		// click button Thuốc > Tạo phiếu huy thuốc.
		$("#toolbarIddrug_2").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			var _obj = new Object();
			_obj._opt = '02D014';
			_obj._loaikedon = 0;
			_obj._title = "Tạo phiếu trả thuốc";
			_start_kham(4, _obj);
//			_openDialogThuoc('02D014', 0, "Tạo phiếu trả thuốc");
		});

		//L2PT-17921
		function _openDonThuocMuaNgoaiDY(){
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
				maubenhphamid : "",
				loaikedon: cfObj.LOAIKEDON_MUANGOAI, //tuyennx_edit_L2PT-6845
				dichvuchaid: "",
				opt : "02D011",
				dongy: '1' //loai dong y
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D011","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,"Tạo đơn thuốc mua ngoài",1300,590);
			DlgUtil.open("divDlgTaoPhieuThuoc"+"02D011");
		}


		function _openDonThuocMuaNgoai(){
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon: cfObj.LOAIKEDON_MUANGOAI, //tuyennx_edit_L2PT-6845
				dichvuchaid: "",
				opt : "02D011"
			};

			var chchucnang  = ['CHUCNANG_KEDON_MUANGOAI'];
			var _chucnang = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', chchucnang.join('$'));

			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D011","divDlg","manager.jsp?func=../noitru/"+_chucnang,paramInput,"Tạo đơn thuốc mua ngoài",1300,590);
			DlgUtil.open("divDlgTaoPhieuThuoc"+"02D011");
		}

		//Tao phieu don thuoc mua ngoai
		$("#toolbarIddrug_mn").on("click", function() {
			_start_kham(5, null); 						// bat dau kham + mo don thuoc mua ngoai;
		});

		//Tao phieu don thuoc mua ngoai dy //tuyennx L2PT-17921
		$("#toolbarIddrug_1mndy").on("click", function() {
			_start_kham(13, null); 						// bat dau kham + mo don thuoc mua ngoai;
		});

		function _openPhieuTuVan(){
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
				maubenhphamid : "",
				loaikedon: 1,
				dichvuchaid: "",
				opt : "02D011_1",
				dinhduong : 1
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D011_1","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,"Tạo đơn thuốc tư vấn",1300,590);
			DlgUtil.open("divDlgTaoPhieuThuoc"+"02D011_1");
		}
		//Tao phieu don thuoc tư vấn
		$("#toolbarIddrug_tuvan").on("click", function() {
			_start_kham(6, null); 						// bat dau kham + mo phieu tu van
		});

		function _openPhieuDinhHuong(){
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon: 1,
				dichvuchaid: "",
				opt : "02D011",
				dinhduong : 1
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D011","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,"Phiếu tư vấn dinh dưỡng",1300,590);
			DlgUtil.open("divDlgTaoPhieuThuoc"+"02D011");
		}
		//Tao phieu don thuoc mua ngoai
		$("#toolbarIddrug_phieuđinhuong").on("click", function() {
			_start_kham(7, null); 						// bat dau kham + mo phieu dinh huong
		});

		// click button Thuốc > Tạo phiếu vat tu.
		$("#toolbarIddrug_3").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			var _obj = new Object();
			_obj._opt = '02D015';
			_obj._loaikedon = 1;
			_obj._title = "Chỉ định vật tư";
			_start_kham(4, _obj);
//			_openDialogThuoc('02D015', 1, "Chỉ định vật tư");
		});

		// click button Thuốc > Tạo phiếu vat tu tu truc. L2PT-13323
		$("#toolbarIddrugtt_3").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			var _obj = new Object();
			_obj._opt = '02D015';
			_obj._loaikedon = 1;
			_obj._title = "Chỉ định vật tư từ tủ trực";
			_obj._loadkhotheo = '2';
			_start_kham(12, _obj);
//			_openDialogThuoc('02D015', 1, "Chỉ định vật tư");
		});

		// click button Thuốc > Tạo phiếu huy vat tu.
		$("#toolbarIddrug_4").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});
			var _obj = new Object();
			_obj._opt = '02D016';
			_obj._loaikedon = 1;
			_obj._title = "Tạo phiếu trả vật tư";
			_start_kham(4, _obj);
//			_openDialogThuoc('02D016', 1, "Tạo phiếu trả vật tư");
		});

		//tuyennx_add_start_20181022 L2HOTRO-11542 L2HOTRO-11471
		var loadkho = cfObj.KETHUOC_LOADKHO_HAOPHI;

		function _openDrug8(){
			var par = ['HIS_KEDONTHUOC_CHITIET_NGT'];
			var _loaikedon = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
			var loadkho = cfObj.KETHUOC_LOADKHO_HAOPHI;

			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
				maubenhphamid : "",
				loaikedon: _loaikedon,
				dichvuchaid: "",
				opt : "02D010",
				macdinh_hao_phi : 9	,
				loadkhotheo : loadkho // 0: kho và tủ trực, 1: kho, 2: tủ trực.
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D010","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,"Tạo phiếu thuốc",1300,590);
			DlgUtil.open("divDlgTaoPhieuThuoc"+"02D010");
		}

		//Tao phieu hao phi
		$("#toolbarIddrug_8").on("click", function() {
			_start_kham(8, null);
		});

		//Tao phieu hao phi vat tu
		function _openHaoPhiVatTu(){
			var par = ['HIS_KEDONTHUOC_CHITIET_NGT'];
			var _loaikedon = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
			var loadkho = cfObj.KETHUOC_LOADKHO_HAOPHI;

			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
				maubenhphamid : "",
				loaikedon: _loaikedon,
				dichvuchaid: "",
				opt : "02D015",
				macdinh_hao_phi : 9,
				loadkhotheo : loadkho 					// 0: kho và tủ trực, 1: kho, 2: tủ trực.
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D015","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,"Tạo phiếu hao phí VT",1300,590);
			DlgUtil.open("divDlgTaoPhieuThuoc"+"02D015");
		}

		$("#toolbarIddrug_hpvt").on("click", function() {
			_start_kham(9, null);
		});

		//tuyennx_add_end_20181022

		$("#toolbarIdbtnKHAC_PhieuSinhSan").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var myVar = {
				benhnhanId: $("#hidBENHNHANID").val(),
				tiepnhanId: $("#hidTIEPNHANID").val(),
				khambenhId : $("#hidKHAMBENHID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgKhamSinhSan", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K011_KhamSinhSan", myVar, "Khám sinh sản", 900, 670);
			DlgUtil.open("dlgKhamSinhSan");
		});

		$("#toolbarIdbtnCHUCNANGSONG").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var selRowId = $('#' + _gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				tiepnhanid : rowData.TIEPNHANID,
				hosobenhanid : rowData.HOSOBENHANID,
				benhnhanid : rowData.BENHNHANID,
				mabenhan : rowData.MAHOSOBENHAN,
				mabenhnhan : rowData.MABENHNHAN,
				tenbenhnhan : rowData.TENBENHNHAN,
				namsinh : rowData.NAMSINH
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgChucNangSong", "divDlg", "manager.jsp?func=../noitru/NTU02D084_ChucNangSong", paramInput, "Theo dõi chức năng sống " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 600);
			DlgUtil.open("divDlgChucNangSong");
		});


		//tao phieu van chuyen
		$("#toolbarIdbtnKHAC_8").on("click", function() {
			if(check_doituongbn() == '-1'){
				DlgUtil.showMsg('Hãy chọn lại bệnh nhân muốn thao tác do BN đã thay đổi đối tượng');
				return;
			}
			paramInput={
				chidinhdichvu : '1',
				loaidichvu : '14',
				loaiphieumbp: '16',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
				loaibenhanid : $("#hidLOAIBENHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $('#hidPHONGID').val()
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV"+ "&loaidichvu=" + 14,paramInput,"Phiếu vận chuyển",1300,600);
			DlgUtil.open("divDlgDichVu");
		});

		$("#toolbarIdbtnKQKCK").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);

			var paramInput = {
				benhnhanid: $("#hidBENHNHANID").val(),
				khambenhid: $("#hidKHAMBENHID").val(),
				hosobenhanid: $("#hidHOSOBENHANID").val(),
				tiepnhanid: $("#hidTIEPNHANID").val(),
				doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
				phongkhamdangkyid: $("#hidPHONGKHAMDANGKYID").val(),
				trangthaikhambenh : rowData.TRANGTHAIKHAMBENH
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgDieutriKetHop","divDlg","manager.jsp?func=../ngoaitru/NGT02K074_NHAPKQ_KHAMCK",paramInput,"Nhập kết quả khám chuyên khoa",1000,300);
			DlgUtil.open("divDlgDieutriKetHop");
		});
		EventUtil.setEvent("ngt02k047_chuyenpknoitru_close",function(e){
			_loadGridData(_opt.phongid);
			DlgUtil.close("divDlgDieutriKetHop");
		});

		//nhap benh an
		$("#toolbarIdbtnKHAC_10").on("click", function() {
			paramInput={
				chidinhdichvu : '1',
				loaidichvu : '14',
				loaiphieumbp: '16',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgNhapBenhAn","divDlg","manager.jsp?func=../noitru/NTU01H031_NhapBenhAn",paramInput,"Nhập bệnh án",500,250);
			DlgUtil.open("divDlgNhapBenhAn");
		});

		EventUtil.setEvent("openBa", function(e) {
			DlgUtil.close("divDlgNhapBenhAn");

			var _sql_par1=RSUtil.buildParam("",[e.loaibenhanid]);
			var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
			var _rows1 = JSON.parse(_data1);
			var _sreenName=_rows1[0].URL;
			var _tenloaibenhan=_rows1[0].TENLOAIBENHAN;
			var _maloaibenhan=_rows1[0].MALOAIBENHAN;

			if(_sreenName != ''){
				paramInput={
					khambenhid : $("#hidKHAMBENHID").val(),
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					benhnhanid :   $("#hidBENHNHANID").val(),
					loaibenhanid : e.loaibenhanid,
					maloaibenhan : _maloaibenhan
				};
				dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../benhan/"+_sreenName,paramInput,"Cập nhật " +_tenloaibenhan,1300,610);
				DlgUtil.open("divDlgBenhAnDetail");
			} else {
				DlgUtil.showMsg('Không tồn tại loại bệnh án này trong dữ liệu');
				return;
			}


		});

		// click button Phiếu khám
		$("#toolbarIdbtnPhieuKham").on("click",function(e){
			$("#toolbarIdbtnStart").trigger("click");
			_phieukham();
			_loadGridData(_opt.phongid);
		});

		// click button xu tri > xu tri kham benh
		$("#toolbarIdhandling_5").on("click",function(e){
			_phieukham();
			_loadGridData(_opt.phongid);
		});

		// open popup phieu kham benh
		function _phieukham(){
			//tuyennx_add_start HISL2TK-597
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHECKCANNNANG", $("#hidKHAMBENHID").val());
			if(ret == 'kococannang'){
				DlgUtil.showMsg('Bệnh nhân chưa có thông tin cân nặng');
				return;
			}
			//tuyennx_add_end
			var myVar={
				benhnhanId : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				phongid : $("#hidPHONGID").val(),
				khoaid:_opt.khoaid,
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				type : _type									// loai kham benh;
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKB","divDlg","manager.jsp?func=../ngoaitru/NGT02K005_phieukhambenh",myVar,"Phiếu khám bệnh",1300,500);
			DlgUtil.open("dlgPhieuKB");
		}

		// Danh sach kham
		$("#toolbarIdbtnDSKham").on("click",function(e){
			/*var param = "&ngaybd="+$("#toolbarIdtxtFromDate").val()+"&ngaykt="+$("#toolbarIdtxtToDate").val() + "&fname=" + encodeURIComponent(opt.fullname).replace(/[%]/g, '/');
			var _cauhinhLCD = cfObj.NGT_LCD_KB_FORM;
			if(_cauhinhLCD != "-1"){
				_cauhinhLCD = _cauhinhLCD.replace('[PARAM]', param);
				_cauhinhLCD = FormUtil.unescape(_cauhinhLCD);
				window.open(_cauhinhLCD,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			}
			else{
				if(cfObj.NGT_LCD_BNDANGKHAM == '1'){
					window.open('manager.jsp?func=../ngoaitru/NGT02K092_LCDBM32&showMode=dlg'+param,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
				}else if (_chedolcd == "1" || _chedolcd == "2"){
					window.open('manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham1&showMode=dlg'+param,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
				}else{
					window.open('manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham&showMode=dlg'+param,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
				}
			}*/
			var par = ['NGT_LCD_KHAMBENH'];
			var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
			var param = "&ngaybd="+$("#toolbarIdtxtFromDate").val()+"&ngaykt="+$("#toolbarIdtxtToDate").val() + "&fname=" + encodeURIComponent(opt.fullname).replace(/[%]/g, '/');
			window.open('manager.jsp?func=..'+dt+'&showMode=dlg'+param,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');

		});
		// Danh sach kham
		$("#toolbarIdbtnDSKhamSmartAds").on("click",function(e){
			var myVar={
				benhnhanId : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				phongid : $("#hidPHONGID").val(),
				khoaid:_opt.khoaid,
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgSmartAds","divDlg","manager.jsp?func=../ngoaitru/NGT02K028_DanhSachKhamSmartAds",myVar,"Gửi SmartAds",700,300);
			DlgUtil.open("dlgSmartAds");
		});

		// sondn IT360-327837
		$("#toolbarIdbtnDSKhamCLS").on("click",function(e){
			var par = ['NGT_LCD_KHAMBENHCLS'];
			var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
			var param = "&ngaybd="+$("#toolbarIdtxtFromDate").val()+"&ngaykt="+$("#toolbarIdtxtToDate").val() + "&fname=" + encodeURIComponent(opt.fullname).replace(/[%]/g, '/');
			window.open('manager.jsp?func=..'+dt+'&showMode=dlg'+param,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		});
		// end sondn IT360-327837

		$("#toolbarIdbtnChuyenPKKT").on("click",function(e){
			$("#toolbarIdhandling_1").trigger("click");
		});

		$("#toolbarIdhandling_1").on("click",function(e){
			var strkcc = _khamchinhphu == "1" ? "chuyển khám chính/khám thêm" : "chuyển phòng khám";
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn ' + strkcc);
				return false;
			}

			if (cfObj.NGT_CPK_CANHBAO == '1') {
				var slKCC = 0;
				try {
					slKCC = parseInt(jsonrpc.AjaxJson.getOneValue("NGT.CPK.SLKCC", [{"name": "[0]", "value": $('#hidKHAMBENHID').val()}]));
				} catch (e) {
					console.error(e);
				}
				if (slKCC > 1) {
					DlgUtil.showConfirm("Bệnh nhân đã khám " + slKCC + " phòng. Bạn có muốn đăng ký thêm phòng khám cho bệnh nhân?", function (flag){
						if (flag){
							chuyenPhongKham();
						}
					});
				} else {
					chuyenPhongKham();
				}
			} else {
				chuyenPhongKham();
			}
		});

		function chuyenPhongKham() {
			var strkcc = _khamchinhphu == "1" ? "chuyển khám chính/khám thêm" : "chuyển phòng khám";
			var str = "";
			if($("#hidCHUYENKHAMNGT").val() == "1"){
				str = "Bệnh nhân đang khám chuyên khoa, yêu cầu vào nội trú để thao tác " + strkcc
			}else{
				if($('#hidXUTRIKHAMBENHID').val() == "6" || $('#hidXUTRIKHAMBENHID').val() == "2"){
					str = "Bệnh nhân đã xử trí nhập viện hoặc điều trị ngoại trú không thể " + strkcc;
				}
			}
			if(str != ""){
				DlgUtil.showMsg(str);
				return false;
			}

			//tuyennx_add_start_20200919_L2PT-27833
			if(cfObj.KHAMBENH_CHUYENPHONG_ICD == '1' && !$("#hidICD_RAVIEN").val()){
				DlgUtil.showMsg('Bệnh nhân chưa có ICD chính không thể chuyển phòng khám');
				return false;
			}
			//tuyennx_add_end_20200919_L2PT-27833

			var myVar={
				kieu : 1, 										//thêm phòng
				khambenhid : $('#hidKHAMBENHID').val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				dichvuid : $('#hidDICHVUID').val(),
				phongid : _opt._phongid,
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				phongkhamdangkyid : $('#hidPHONGKHAMDANGKYID').val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				sub_dtbnid : $("#hidSUB_DTBNID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				chinhphukieu : "0", 							// 0: kham them phong; 1: doi phong
				i_hid : opt.hospital_id, 						// in phieu thu khac
				i_sch : opt.db_schema							// in phieu thu khac
			};

			if(_khamchinhphu == "0"){
				dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K046_them_chuyenphongkham",myVar,"Chuyển phòng khám",700,300);
				//dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham",myVar,"Chuyển phòng khám",700,300);
			}else{
				dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K046_them_chuyenphongkham1",myVar,"Khám thêm phòng",700,400);
			}
			DlgUtil.open("dlgPhieuKham");
		}

		$("#toolbarIdhandling_13").on("click",function(e){
			var str = "";
			if($("#hidCHUYENKHAMNGT").val() == "1"){
				str = "Bệnh nhân đang khám chuyên khoa, yêu cầu vào nội trú để thao tác đổi phòng khám"
			}else{
				if($('#hidXUTRIKHAMBENHID').val() == "6" || $('#hidXUTRIKHAMBENHID').val() == "2"){
					str = "Bệnh nhân đã xử trí nhập viện hoặc điều trị ngoại trú không thể đổi phòng khám.";
				}
			}
			if(str != ""){
				DlgUtil.showMsg(str);
				return false;
			}

			//tuyennx_add_20170906_end
			/*var myVar={
				kieu : 0, //chuyen
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				phongid : _opt._phongid,
				dichvuid : $('#hidDICHVUID').val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham",myVar,"Chuyển phòng khám",700,300);
			DlgUtil.open("dlgPhieuKham");*/
			// CHUC NANG DOI PHONG KHAM THUONG;
			var myVar={
				kieu : 0, //chuyen
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				phongid : _opt._phongid,
				dichvuid : $('#hidDICHVUID').val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
				maubenhphamid : $("#hidMAUBENHPHAMID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham",myVar,"Đổi phòng khám",700,300);
			DlgUtil.open("dlgPhieuKham");
		});

		$("#toolbarIdhandling_2").on("click",function(e){
			var str = "";
			if($("#hidCHUYENKHAMNGT").val() == "1"){
				str = "Bệnh nhân đang khám chuyên khoa, yêu cầu vào nội trú để thao tác đổi phòng khám"
			}else{
				if($('#hidXUTRIKHAMBENHID').val() == "6" || $('#hidXUTRIKHAMBENHID').val() == "2"){
					str = "Bệnh nhân đã xử trí nhập viện hoặc điều trị ngoại trú không thể đổi phòng khám.";
				}
			}
			if(str != ""){
				DlgUtil.showMsg(str);
				return false;
			}

			//tuyennx_add_20170906_end
			/*var myVar={
				kieu : 0, //chuyen
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				phongid : _opt._phongid,
				dichvuid : $('#hidDICHVUID').val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham",myVar,"Chuyển phòng khám",700,300);
			DlgUtil.open("dlgPhieuKham");*/
			if(_khamchinhphu == "0"){
				// CHUC NANG DOI PHONG KHAM THUONG;
				var myVar={
					kieu : 0, //chuyen
					khambenhid : $("#hidKHAMBENHID").val(),
					tiepnhanid : $("#hidTIEPNHANID").val(),
					phongid : _opt._phongid,
					dichvuid : $('#hidDICHVUID').val(),
					phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
					maubenhphamid : $("#hidMAUBENHPHAMID").val(),
					doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val()
				};
				dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham",myVar,"Đổi phòng khám",700,300);
				DlgUtil.open("dlgPhieuKham");
			}else{
				// CHUC NANG DOI KHAM CHINH;
				var myVar={
					kieu : 1, 										//thêm phòng
					khambenhid : $('#hidKHAMBENHID').val(),
					tiepnhanid : $("#hidTIEPNHANID").val(),
					dichvuid : $('#hidDICHVUID').val(),
					phongid : _opt._phongid,
					benhnhanid : $("#hidBENHNHANID").val(),
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					phongkhamdangkyid : $('#hidPHONGKHAMDANGKYID').val(),
					doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
					sub_dtbnid : $("#hidSUB_DTBNID").val(),
					maubenhphamid : $("#hidMAUBENHPHAMID").val(),
					loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
					chinhphukieu : "1", 							// 0: kham them phong; 1: doi phong chinh
					i_hid : opt.hospital_id,
					i_sch : opt.db_schema
				};
				dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K046_them_chuyenphongkham1",myVar,"Đổi khám chính",700,400);
				DlgUtil.open("dlgPhieuKham");
			}
		});

		//hanv_20170712:
		// cau hinh: Hien thi/an lap phieu tam ung
		var showPTU = cfObj.NGT02K001_SHOW_PTU;
		if(showPTU=='0') {
			$("#toolbarIdbntKHAC_ptu").remove();
		}
		//Lap phieu tam ung
		$("#toolbarIdbntKHAC_ptu").on("click",function(e){
			var paramInput={
				tiepnhanid : $("#hidTIEPNHANID").val(), // L2PT-133110
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid: $("#hidKHAMBENHID").val(),
				khoaid: opt.khoaid,
				thoigianvaovien: $('#txtDENKHAMLUC').val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgPhieuTamUng","divDlg","manager.jsp?func=../noitru/NTU01H021_PhieuTamUngBenhNhan",paramInput,"Lập phiếu tạm ứng",900,460);
			DlgUtil.open("divDlgPhieuTamUng");
		});
		//end hanv

		//nghiant 14062017
		$("#toolbarIdhandling_4").on("click",function(e){
			var myVar={
				benhnhanId : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				mabenhnhan : $("#txtMABENHNHAN").val(),
				phongid:_opt.phongid,
				khoaid:_opt.khoaid
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgHoSoSkCaNhan","divDlg","manager.jsp?func=../benhan/HOSO_QUANLYSUCKHOECANHAN",myVar,"Hồ sơ quản lý sức khỏe cá nhân",1300,600);
			DlgUtil.open("dlgHoSoSkCaNhan");
		});
		//end nghiant 14062017
		//dannd 16082022
		$("#toolbarIdTUVONG").on("click",function(e){
			if ($("#hidHOSOBENHANID").val() == "" || $("#hidHOSOBENHANID").val() == "-1"){
				DlgUtil.showMsg("Chọn bệnh nhân để thao tác");
				return;
			}
			var paramInput = {
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				nghenghiep : $("#hidTENNGHENGHIEP").val(),
				diachi : $('#txtDIACHI').val(),
				namsinh : $('#hidNAMSINH').val(),
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
				chandoan : $('#txtCDC').val(),
				ngaytiepnhan : $('#txtDENKHAMLUC').val(),
				capnhat : '1',
				hosobenhanid : $('#hidHOSOBENHANID').val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
				maubenhphamid : $("#hidMAUBENHPHAMID").val()

			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgTuVong", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K010_Tuvong", paramInput, "Thông tin tử vong", 900, 375);
			DlgUtil.open("dlgTuVong");
		});
		// tuyennx_add_start L2PT-16020
		$("#toolbarIdbtnKSKLaiXe").on("click", function() {
			var param = {
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				namsinh : $('#hidNAMSINH').val(),
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgKSKLaiXe","divDlg","manager.jsp?func=../ngoaitru/NGT02K079_KSK_LaiXe",param,"Khám sức khỏe lái xe",1200,520);
			DlgUtil.open("dlgKSKLaiXe");
		});
		//Phiếu ra viện.
		$("#toolbarIdgroup_0_1").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy ra viện.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 1 && $('#hidXUTRIKHAMBENHID').val() != 9 && $('#hidXUTRIKHAMBENHID').val() != 3){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí ra viện, In phiếu chỉ với những bệnh nhân ra viện/khác');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name : 'i_hid',
				type : 'String',
				value : opt.hospital_id
			}, {
				name : 'i_sch',
				type : 'String',

				value : opt.db_schema
			}];

			if(_opt.hospital_id == "919"){			// TTKHA: XUAT EXCEL
				var rpName= "NTU009_GIAYRAVIEN_01BV01_QD4069_A5" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
				CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", 'xlsx', par, rpName);
			}else{
				openReport('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", "pdf", par);
			}
		});
		$("#toolbarIdprint_tuvong").on("click", function() {
            var rpt_code = 'BIENBAN_TUVONG';
            var par = [ {
                name : 'i_khambenhid',
                type : 'String',
                value : $("#hidKHAMBENHID").val()
            } ];
            openReport('window', rpt_code, "pdf", par);
        });

		//Phiếu hẹn khám.
		function _inGiayHenKham(_format){
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
				return;
			}

			var obj = new Object();
			obj.KHAMBENHID = $("#hidKHAMBENHID").val();
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.HK", JSON.stringify(obj));

			if(ret > 0){
				if (_format == "pdf"){
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : $("#hidKHAMBENHID").val()
					}];
					openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", _format, par);
				}else{
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : $("#hidKHAMBENHID").val()
					}, {
						name : 'i_hid',
						type : 'String',
						value : opt.hospital_id
					}, {
						name : 'i_sch',
						type : 'String',

						value : opt.db_schema
					}];
					var rpName= "NGT014_GIAYHENKHAMLAI_TT402015_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
					CommonUtil.inPhieu('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", 'docx', par, rpName);
				}
			}else{
				DlgUtil.showMsg("Không có thông tin hẹn khám của bệnh nhân này.");
			}
		}

		//Phiếu ra viện dạng doc
		$("#toolbarIdgroup_0_1_doc").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy ra viện.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 1 && $('#hidXUTRIKHAMBENHID').val() != 9 && $('#hidXUTRIKHAMBENHID').val() != 3){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí ra viện, In phiếu chỉ với những bệnh nhân ra viện/khác');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name : 'i_hid',
				type : 'String',
				value : opt.hospital_id
			}, {
				name : 'i_sch',
				type : 'String',

				value : opt.db_schema
			}];

			var rpName= "NTU009_GIAYRAVIEN_01BV01_QD4069_A5" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
			CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", 'docx', par, rpName);
		});
		//Giấy xác nhận bệnh tật
		$("#toolbarIdprint_xacnhanbenhtat_doc").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy ra viện.');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}
			];

			var rpName= "NGT_GIAYXACNHAN_TINHTRANGBENHTAT.docx";
			CommonUtil.inPhieu('window', "NGT_GIAYXACNHAN_TINHTRANGBENHTAT", 'docx', par, rpName);
		});

		$("#toolbarIdgroup_0_31").on("click", function() {
			_inGiayHenKham('rtf')

		});
		$("#toolbarIdgroup_0_7").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in!');
				return;
			}

			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			} ];
			openReport('window', "NGT001_BKCPKCBBHYT_QD6556_DOCMOI_A4_46840", "pdf", par);

		});

		$("#toolbarIdgroup_2_16").on("click", function() {

			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in biên bản hội chẩn.');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()}
			];
			openReport('window', "PHIEU_BIENBAN_HOICHAN", 'pdf', par);
		});

		//Phiếu hẹn khám.
		$("#toolbarIdgroup_0_3").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
				return;
			}

			var obj = new Object();
			obj.KHAMBENHID = $("#hidKHAMBENHID").val();
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.HK", JSON.stringify(obj));

			if(ret > 0){
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}];
				openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
			}else{
				DlgUtil.showMsg("Không có thông tin hẹn khám của bệnh nhân này.");
			}

		});

		//DS nghi huong BHXH
		$("#toolbarIdgroup_0_InNghiBHXH").on("click",function(e){
			var myVar={
				i_hid : _opt.hospital_id,
				i_sch : _opt.db_schema
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT03K006_DS_NghiBHXH",myVar,"DS Nghỉ hưởng BHXH",1000,600);
			DlgUtil.open("dlgPhieuKham");
		});

		// ttlinh phieu kham chuyen khoa
		$("#toolbarIdgroup_0_PKchuyenkhoa").on("click", function() {

			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu khám chuyên khoa.');
				return;
			}

			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()
			}, {
				name : 'i_phongids',
				type : 'String',
				value : $('#hidPHONGID').val()
			}, {
				name : 'i_pkdkids',
				type : 'String',
				value : $('#hidPHONGKHAMDANGKYID').val()
			}];
			openReport('window', "RPT_PHIEUKHAMCHUYENKHOA_A4_965", 'pdf', par);
		});

		$("#toolbarIdgroup_2_2_1").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
				return false;
			}
			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];
			openReport('window', "NGT006_DONTHUOCK_17DBV01_TT052016_A5", "pdf", par);
		});
		//In Don thuoc.
		$("#toolbarIdgroup_2_2").on("click", function() {
			var sql_par=[];
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
				return false;
			}
			sql_par.push({"name":"[0]","value":$('#hidKHAMBENHID').val()});

			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
			var rows=$.parseJSON(data);

			// hunglv L2PT-7676
			// L2PT-126285: bo fix theo csytid, an theo cau hinh
			//if(_opt.hospital_id == 1111){ //LAN-ĐK
			if(cfObj.NGT_KBMHC_IN_DONTHUOC == '1') {
				if(rows.length>=1){
					var thuoc_thuong='';
					var thuoc_huongthan='';
					var thuoc_gaynghien='';
					var thuoc_dongy='';
					for(var i=0;i< rows.length;i++){ //từng đơn thuốc
						var _par_loai = [rows[i].MAUBENHPHAMID];
						var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));
						if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
							for(var j=0;j< arr_loaithuoc.length;j++){
								var _loaithuoc=arr_loaithuoc[j].LOAI;
								if(_loaithuoc==6){ //thuoc huong than
									thuoc_huongthan = thuoc_huongthan+','+rows[i].MAUBENHPHAMID;
								}else if(_loaithuoc==7){ //don thuoc gay nghien
									thuoc_gaynghien = thuoc_gaynghien+','+rows[i].MAUBENHPHAMID;
								}else if(_loaithuoc==3){ //thuoc dong y
									thuoc_dongy = thuoc_dongy+','+rows[i].MAUBENHPHAMID;
								}else{
									thuoc_thuong= thuoc_thuong+','+rows[i].MAUBENHPHAMID;
								}
							}
						}
					}

					if (thuoc_thuong.length > 0){
						thuoc_thuong = thuoc_thuong.substring(1);
						var par_thuoc_thuong = [ { name : 'maubenhphamid', type : 'String', value : thuoc_thuong }];
						CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par_thuoc_thuong);
					}
					if (thuoc_huongthan.length > 0){
						thuoc_huongthan = thuoc_huongthan.substring(1);
						var par_thuoc_huongthan = [ { name : 'maubenhphamid', type : 'String', value : thuoc_huongthan }];
						CommonUtil.openReportGet('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par_thuoc_huongthan);
					}
					if (thuoc_gaynghien.length > 0){
						thuoc_gaynghien = thuoc_gaynghien.substring(1);
						var par_thuoc_gaynghien = [ { name : 'maubenhphamid', type : 'String', value : thuoc_gaynghien }];
						CommonUtil.openReportGet('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par_thuoc_gaynghien);
					}
					if (thuoc_dongy.length > 0){
						thuoc_dongy = thuoc_dongy.substring(1);
						var par_thuoc_dongy = [ { name : 'maubenhphamid', type : 'String', value : thuoc_dongy }];
						CommonUtil.openReportGet('window', "NGT020_DONTHUOCTHANGNGOAITRU", "pdf", par_thuoc_dongy);
					}
				}
				else
					DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');

				return;
			}
			// end hunglv


			if(rows.length>=1){
				if(_opt.hospital_id == 996){
					// TTQNI - 996
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : $('#hidKHAMBENHID').val()
					}];
					openReport('window', "NGT006_ALL_DONTHUOC_17DBV01_TT052016_A5_996", "pdf", par);
					return;
				}
			}else{
				DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
			}


			if(rows.length == 1 && cfObj.INDONTHUOC_PHIEULINH == 0){ //L2PT-27574
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rows[0].MAUBENHPHAMID
				}];
				openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
			}else if(rows.length >= 1){			//L2PT-27574
				paramInput={
					data : rows
				};
				dlgPopup=DlgUtil.buildPopupUrl("dlgInDonThuoc","divDlg","manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc",paramInput,"IN ĐƠN THUỐC",420,260);
				DlgUtil.open("dlgInDonThuoc");
			}else{
				DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
			}
			// if(_opt.hospital_id == "965"){					// DA KHOA BUU DIEN IN DOCX
		});

		//Phoi thanh toan
		$("#toolbarIdgroup_0_4").on("click", function() {
			/*var flag = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T004.10",$("#hidTIEPNHANID").val());
			var _dtbnid=$("#hidDOITUONGBENHNHANID").val();
			var _tiepnhanid=$("#hidTIEPNHANID").val();
			if(_dtbnid == 1) {
				vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_A4');
				if(flag==1)
					vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT035_BKCPKCBTUTUCNGOAITRU_A4');
			} else {
				if(flag==1)
					vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT035_BKCPKCBTUTUCNGOAITRU_A4');
			}*/
			vienphi_tinhtien.inBangKe( $("#hidTIEPNHANID").val(),$("#hidDOITUONGBENHNHANID").val(),'1');
		});
		$("#toolbarIdgroup_bangkeBADN").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in bảng kê.');
				return;
			}
			var _tiepnhanid=$("#hidTIEPNHANID").val();
			vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBBHYT_QD6556_BADN_A4');
		});

		$("#toolbarIdgroup_0_5").on("click", function() {
			var flag = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T004.10",$("#hidTIEPNHANID").val());
			var _dtbnid=$("#hidDOITUONGBENHNHANID").val();
			var _tiepnhanid=$("#hidTIEPNHANID").val();
			vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCB_HAOPHI_01BV_QD3455_A4');
		});
		$("#toolbarIdgroup_0_BangKe3455").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in bảng kê 3455.');
				return;
			}
			var _loaitiepnhanid=$("#hidLOAITIEPNHANID").val();
			var dtbnid=$("#hidDOITUONGBENHNHANID").val();
			vienphi_tinhtien.inBangKe3455( $("#hidTIEPNHANID").val(),dtbnid,_loaitiepnhanid);
		});

		//Phoi thanh toan
		$("#toolbarIdgroup_0_6").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu điều trị.');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}
				, {
					name : 'khoaid',
					type : 'String',
					value : _opt.khoaid
				}
				, {
					name : 'phongid',
					type : 'String',
					value : _opt.phongid
				}  //L2PT-44633 thêm khoaid, phongid
			];

			openReport('window', "PHIEUDIEUTRI_915", "pdf", par);
		});
		//Beg_HaNv_290723: Phiếu công khai dịch vụ - L2PT-49218
		$("#toolbarIdgroup_0_7").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu công khai dịch vụ.');
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "PHIEU_CONGKHAI_DICHVU", "pdf", par);
		});
		//End_HaNv_290723
		$("#toolbarIdprint_PHIEUCONGKHAITHUOC").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					khambenhid : _khambenhid
				};
				var url = "manager.jsp?func=../noitru/NTU01H028_InPhieuCongKhaiThuoc";
				var popup = DlgUtil.buildPopupUrl("divDlgPCKT", "divDlg", url, paramInput, "In phiếu công khai thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")") , 505, 268);
				popup.open("divDlgPCKT");
			}
		});
		$("#toolbarIdcaPHIEUCONGKHAITHUOC").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					khambenhid : _khambenhid,
					kyca : '1'
				};
				var width = $(document).width() - 50;
				var height= $(window).height() - 50;
				var url = "manager.jsp?func=../noitru/NTU01H028_InPhieuCongKhaiThuoc";
				var popup = DlgUtil.buildPopupUrl("divDlgPCKT", "divDlg", url, paramInput, "In phiếu công khai thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), width, height);
				popup.open("divDlgPCKT");
			}
		});
		//Phiếu chuyển viện.
		$("#toolbarIdgroup_0_2").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy chuyển viện.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 7){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];

			if(_opt.hospital_id == "965"){					// DA KHOA BUU DIEN IN DOCX
				var rpName= "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
				CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'docx', par, rpName);
			}
			/*else if(_opt.hospital_id == "1111"){					// DK LAN: Ham khac;
				var par1111 = [ {
					name : 'phongkhamdangkyid',
					type : 'String',
					value : $("#hidPHONGKHAMDANGKYID").val()
				}];
				openReport('window', "NGT003_GIAYCHUYENTUYEN_LAN_1111_2", "pdf", par1111);

			}*/
			else{
				openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
			}
		});

		$("#toolbarIdgroup_0_2_doc").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy chuyển viện.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 7){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];

			if(_opt.hospital_id == "965"){					// DA KHOA BUU DIEN IN DOCX
				var rpName= "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
				CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'docx', par, rpName);
			}else if(_opt.hospital_id == "1111"){					// DK LAN: Ham khac;
				var par1111 = [ {
					name : 'phongkhamdangkyid',
					type : 'String',
					value : $("#hidPHONGKHAMDANGKYID").val()
				}];
//				openReport('window', "NGT003_GIAYCHUYENTUYEN_LAN_1111_2", "pdf", par1111);
				var rpName= "NGT003_GIAYCHUYENTUYEN_LAN_1111_2" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
				CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_LAN_1111_2", 'docx', par, rpName);

			}else{

				//L2PT-29632
				var format_print = cfObj.NGT_FORMAT_PRINT_DOC
//				openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
				var rpName= "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + format_print;
				CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", format_print, par, rpName);
			}
		});

		//In phiếu điều trị  L2PT-30031
		$("#toolbarIdgroup_3_1").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu điều trị.');
				return;
			}

			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];
			openReport('window', "PHIEUIN_TOMTAT_DIEUTRI", "pdf", par);
		});

		//report phieu chi dinh CLS
		$("#toolbarIdgroup_2_1").on("click", function() {
			var par = [];
			if(_opt.hospital_id==922){									// BV LAO PHOI LONG AN
				_openReportClsLan(par, "PHIEU_CLSC_922",1); // in xet nghiem rieng
				_openReportClsLan(par, "PHIEU_CLSC_922",2); // in cdha rieng
				_openReportClsLan(par, "PHIEU_CLSC_922",5);//in pttt rieng
			}else if (_opt.hospital_id == 1028){				// PK VINH DUC HCM
				var param = [ {
					name : 'i_hid',
					type : 'String',
					value : _opt.hospital_id
				}, {
					name : 'i_sch',
					type : 'String',
					value : _opt.db_schema
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				} ];

				var rpName= "PHIEU_CLSC" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
				CommonUtil.inPhieu('window', "PHIEU_CLSC", 'xlsx', param, rpName);
			}else{
				_openReport(par, "PHIEU_CLSC", "");
			}

		});
		$("#toolbarIdgroup_2_30").on("click", function() {
			var par = [];
			_openReport(par, "PHIEU_CLSC2", "");

		});

		//tuyennx_add_start L2DKBD-1246
		$("#toolbarIdgroup_2_7").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in bệnh lịch.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 2 && $('#hidXUTRIKHAMBENHID').val() != 6){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí nhập viện, In phiếu chỉ với những bệnh nhân nhập viện');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()
			}];

			if (_opt.hospital_id == "919"){
				openReport('window', "BENHLICHCC115", 'pdf', par);// Bệnh lịch cho TTKHA
			}else{
				openReport('window', "BENHLICH_A4_951", 'pdf', par);// bệnh lịch chung
			}
		});
		$("#toolbarIdgroup_2_8").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in bệnh án.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 2 && $('#hidXUTRIKHAMBENHID').val() != 6){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí nhập viện, In phiếu chỉ với những bệnh nhân nhập viện');
				return;
			}

			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $('#hidHOSOBENHANID').val()
			}];
			openReport('window', $('#hidTEN_FILE').val(), 'pdf', par);// bệnh lịch.
		});
		$("#toolbarIdgroup_2_9").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu đánh giá ban đầu bệnh nhân.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 2 && $('#hidXUTRIKHAMBENHID').val() != 6){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí nhập viện, In phiếu chỉ với những bệnh nhân nhập viện');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()
			}];
			openReport('window', "NGT005_PHIEUDANHGIANHAPVIEN_A4_951", 'pdf', par);
		});
		$("#toolbarIdgroup_2_10").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu sàng lọc và đánh giá dinh dưỡng.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 2 && $('#hidXUTRIKHAMBENHID').val() != 6){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí nhập viện, In phiếu chỉ với những bệnh nhân nhập viện');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()},
				{
					name : 'khoaid',
					type : 'String',
					value : _opt.khoaid}
			];
			openReport('window', "PHIEU_SANGLOC_DANHGIA_DINHDUONG_A4_951", 'pdf', par);
		});

		$("#toolbarIdgroup_2_11").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in các phiếu nhập viện.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 2 && $('#hidXUTRIKHAMBENHID').val() != 6){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí nhập viện, In phiếu chỉ với những bệnh nhân nhập viện');
				return;
			}
			var _print1tab = cfObj.IN_GOPPHIEU_NV_1TAB;
			if(_print1tab == '1'){
				var par = [ {
					name : 'benhnhanid',
					type : 'String',
					value : $('#hidBENHNHANID').val()
				},
					{
						name : 'hosobenhanid',
						type : 'String',
						value : $('#hidHOSOBENHANID').val()
					},{
						name : 'khambenhid',
						type : 'String',
						value : $('#hidKHAMBENHID').val()
					},{
						name : 'report_code_loaiba',
						type : 'String',
						value : $('#hidTEN_FILE').val()
					},{
						name:'khoaid',
						type:'String',
						value:opt.khoaid
					}
				];
				openReport('window', "PHIEU_IN_BENHNHAN_NHAPVIEN", 'pdf', par);
			}else{
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $('#hidKHAMBENHID').val()},
					{
						name : 'khoaid',
						type : 'String',
						value : _opt.khoaid},
					{
						name : 'hosobenhanid',
						type : 'String',
						value : $('#hidHOSOBENHANID').val()},
					{
						name : 'benhnhanid',
						type : 'String',
						value : $('#hidBENHNHANID').val()},

				];
				openReport('window', "BENHLICH_A4_951", 'pdf', par);// bệnh lịch.
				openReport('window', $('#hidTEN_FILE').val(), 'pdf', par);
				openReport('window', "PHIEU_SANGLOC_DANHGIA_DINHDUONG_A4_951", 'pdf', par);
				openReport('window', "NGT005_PHIEUDANHGIANHAPVIEN_A4_951", 'pdf', par);
				openReport('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf', par);
			}
		});
		//tuyennx_add_end

		//START HISL2TK-1080
		$("#toolbarIdgroup_2_12").on("click", function() {
			if($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1'){
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}else{
				var _benhnhanid = $("#hidBENHNHANID").val();
				var _khambenhid = $("#hidKHAMBENHID").val();
				var _hosobenhanid=$("#hidHOSOBENHANID").val();
				var _self=this;
				paramInput={
					benhnhanid : _benhnhanid,
					khambenhid : _khambenhid,
					hosobenhanid : _hosobenhanid,
					lnmbp : LNMBP_XetNghiem,
					ingop : '1'//In gộp cùng phiếu
				};

				dlgPopup=DlgUtil.buildPopupUrl("divDlgDeleteXN","divDlg","manager.jsp?func=../noitru/NTU02D093_InPhieuXN",paramInput,"In phiếu XN",1100,600);
				DlgUtil.open("divDlgDeleteXN");
			}
		});
		//END HISL2TK-1080

		//in giay nhan tra x quang cho benh nhan
		$("#toolbarIdgroup_2_13").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy nhận trả phim x-quang.');
				return;
			}

			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()}
			];
			openReport('window', "PHIEU_NHANTRA_XQUANG_A4", 'pdf', par);
		});

		//START HISL2TK-1085
		$("#toolbarIdgroup_2_14").on("click", function() {

			if($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1'){
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}else{
				var i_tiepnhanid = $("#hidTIEPNHANID").val();
				var i_dtbn_id = $("#hidDOITUONGBENHNHANID").val();
				var i_loaitiepnhanid = $("#hidLOAITIEPNHANID").val();
				inBangKeLPLSO(i_tiepnhanid, i_dtbn_id, i_loaitiepnhanid);
			}
		});
		//END HISL2TK-1085

		$("#toolbarIdgroup_0_InPhieuXuTri").on("click", function(e){
			var checkxutri = cfObj.CHECK_XTRI_PKB;
			if(checkxutri == "1"){
				var _xtkbid = $('#hidXUTRIKHAMBENHID').val();
				if(_xtkbid == "0" || _xtkbid == ""){
					DlgUtil.showMsg('Bệnh nhân chưa có xử trí. ');
					return;
				}
			}
			var par = [ {name : 'khambenhid', type : 'String', value : $("#hidKHAMBENHID").val()},
				{name : 'phongkhamdangkyid',type : 'String',value : $("#hidPHONGKHAMDANGKYID").val()} ];
			openReport('window', "PHIEU_XUTRI_KHAMBENH", "pdf", par);
		});
		$("#toolbarIdXuat_XML").on("click", function(e){

			var myVar={
				TIEPNHANID : $("#hidTIEPNHANID").val()
			};
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("XML_CHECKIN", JSON.stringify(myVar));
			console.log(ret)
			const link = document.createElement("a");
			const file = new Blob([ret], { type: 'text/plain' });
			link.href = URL.createObjectURL(file);
			link.download = "xml_checkin_" + $("#hidMAHOSOBENHAN").val() +".xml";
			link.click();
			URL.revokeObjectURL(link.href);
		});
		//START tuyendv
		$("#toolbarIdgroup_2_15").on("click", function() {

			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy chứng nhận thương tích.');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()}
			];
			openReport('window', "GIAY_CHUNGNHAN_THUONGTICH_965", 'pdf', par);
		});
		//END tuyendv

		//START thaiph
		$("#toolbarIdgroup_2_19").on("click", function() {

			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu xác nhận đồng ý xét nghiệm HIV.');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()}
			];
			openReport('window', "REPORT_PHIEU_XACNHAN_HIV_1014", 'pdf', par);
		});
		//END thaiph

		//tuyennx_add_start_20190423 L1PT-613
		$("#toolbarIdgroup_2_17").on("click", function() {

			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Chọn bệnh nhân trước khi in biên bản kiểm điểm tử vong.');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()}
			];
			openReport('window', "NGT004_TRICHBBKDTUVONG_41BV01_QD4069_A4", 'pdf', par);
		});


		$("#toolbarIdgroup_2_18").on("click", function() {

			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Chọn bệnh nhân trước khi in bản kiểm thảo tử vong.');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()}
			];
			openReport('window', "NGT004_TRICHBBKIEMTHAOTUVONG_41BV01_QD406_A4", 'pdf', par);
		});
		//tuyennx_add_end_20190423 L1PT-613

		//tuyennx_add_start_20190423 L2PT-27977
		$("#toolbarIdgroup_2_22").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Chọn bệnh nhân trước khi in');
				return;
			}

			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgInPhieuCK","divDlg","manager.jsp?func=../ngoaitru/NGT02K054_InPhieuCK",paramInput,"In phiếu khám chuyên khoa",700,350);
			DlgUtil.open("divDlgInPhieuCK");
		});
		//tuyennx_add_end_20190423 L2PT-27977
		// L2PT-8626 start
		$("#toolbarIdprint_174").on("click", function() {
			if($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1'){
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}
			var par =
				[{
					name: 'khambenhid',
					type: 'String',
					value: $("#hidKHAMBENHID").val()
				}];
			openReport('window', "PHIEUTHEODOINGHIEN_MATUYTONGHOP", "pdf", par);
		});
		$("#toolbarIdprint_175").on("click", function() {
			if($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1'){
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}
			var par =
				[{
					name: 'khambenhid',
					type: 'String',
					value: $("#hidKHAMBENHID").val()
				}];
			openReport('window', "BANGKIEM_TRUOCTIEMCHUNG_SOSINH", "pdf", par);
		});
		$("#toolbarIdprint_176").on("click", function() {
			if($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1'){
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}
			var par =
				[{
					name: 'khambenhid',
					type: 'String',
					value: $("#hidKHAMBENHID").val()
				}];
			openReport('window', "PHIEUKETQUA_NGHIENMATUY", "pdf", par);
		});
		$("#toolbarIdprint_177").on("click", function() {
			if($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1'){
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}
			var par =
				[{
					name: 'khambenhid',
					type: 'String',
					value: $("#hidKHAMBENHID").val()
				}];
			openReport('window', "PHIEUTHEODOINGHIEN_THUOCPHIEN", "pdf", par);
		});
		// L2PT-8626 end
		//thaiph L2PT-12930
		$("#toolbarIdgroup_0_pkbenh").on("click",function(e){
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Chọn bệnh nhân trước khi in phiếu khám bệnh.');
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()
			}, {
				name : 'phongid',
				type : 'String',
				value : $("#hidPHONGID").val()
			}, {
				name : 'i_sch',
				type : 'String',
				value : _opt.db_schema
			} ];

			var bieumau = cfObj.NGT_MHKB_PKB;
			var template = (bieumau.split(";"))[1].split(",");
			var checkDTBN = (bieumau.split(";"))[0].split(",");
			if($.inArray($('#hidDOITUONGBENHNHANID').val(),checkDTBN) >= 0 ){
				openReport('window', template[0], "pdf", par);
			}
			else{
				openReport('window', template[1], "pdf", par);
			}
		});
		//end thaiph

		$("#toolbarIdgroup_0_ck39k").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu.');
				return;
			}
			//if ( ($("#hidDOITUONGBENHNHANID").val() == "2" || $("#hidDOITUONGBENHNHANID").val() == "6") )
			{
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'i_hid',
					type : 'String',
					value : _opt.hospital_id
				}, {
					name : 'i_sch',
					type : 'String',
					value : _opt.db_schema
				} ];
				openReport('window',"DKBD_PCD_THEM_CONG_KHAM_A5","pdf", par);
			}
		});

		//huongpv them
		function _openReportClsLan(param, reportName,i_loainhommaubenhpham){
			param.push({name : 'khambenhid', type : 'String',value : $("#hidKHAMBENHID").val()});
			param.push({name : 'i_loainhommaubenhpham', type : 'String',value : i_loainhommaubenhpham});
			CommonUtil.openReportGet('window', reportName, "pdf", param);
		}

		function _openReport(param, reportName, format1){
			var format = format1 != "rtf" && format1 != "docx" ? "pdf" : format1;

			if($("#hidKHAMBENHID").val() == "-1" ){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
				return;
			}

			if(format == "pdf"){
				param.push({name : 'i_hid', type : 'String',value : _opt.hospital_id});
				param.push({name : 'i_sch', type : 'String',value : _opt.db_schema});
				param.push({name : 'khambenhid', type : 'String',value : $("#hidKHAMBENHID").val()});
				param.push({name : 'phongid', type : 'String',value : phongid});
				openReport('window', reportName, format, param);
			}else{
				param.push({name : 'i_hid', type : 'String',value : _opt.hospital_id});
				param.push({name : 'i_sch', type : 'String',value : _opt.db_schema});
				param.push({name : 'khambenhid', type : 'String',value : $("#hidKHAMBENHID").val()});

				var rpName= reportName + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + format;
				CommonUtil.inPhieu('window', reportName, format, param, rpName);
			}

		}

		//Phiếu khám bệnh vào viện
		$("#toolbarIdgroup_2_5").on("click", function() {
			var par = [];
			if(_opt.hospital_id == "965"){			// BDHCM : xuat excel chuc nang nay;
				par = [ {
					name : 'i_hid',
					type : 'String',
					value : _opt.hospital_id
				}, {
					name : 'i_sch',
					type : 'String',
					value : _opt.db_schema
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				} ];

				if($("#hidDOITUONGBENHNHANID").val()== 3){
					var rpName= "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
					CommonUtil.inPhieu('window', "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'xlsx', par, rpName);
				}else{
					var rpName= "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
					CommonUtil.inPhieu('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'xlsx', par, rpName);
				}
			}else if(_opt.hospital_id == "1007"){						// SN VPC
				_openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
			}else{
				if (_type == "2" && _pkbvvcapcuu == "1" && _hinhthucvaovienid == "2") {
					// SONDN 20191009 L2PT-9604
					if($("#hidKHAMBENHID").val() == "-1" ){
						DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
						return;
					}
					var param = [];
					param.push({name : 'khambenhid', type : 'String',value : $("#hidKHAMBENHID").val()});
					CommonUtil.openReportGet('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_CAPCUU_A4", "pdf", param);
					// END SONDN 20191009 L2PT-9604
				} else{
					if (_opt.hospital_id == "1103" ||  _opt.hospital_id == "26320"){
						_openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
					}else{
						if($("#hidDOITUONGBENHNHANID").val()== 3){
							_openReport(par, "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'pdf');
						}else{
							_openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
						}
					}
				}
			}
		});

		// sondn L2PT-301
		$("#toolbarIdgroup_2_51").on("click", function() {
			var par = [];
			_openReport(par, "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'pdf');
		});
		// end sondn L2PT-301

		//Phiếu khám bệnh vào viện ck mat L2PT-27727
		$("#toolbarIdgroup_2_21").on("click", function() {
			if($("#hidKHAMBENHID").val() == "-1" ){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			_openReport(par, "NGT005_PHIEUKBVAOVIEN_CK_MAT_A4", 'pdf');

		});

		//Phiếu khám bệnh vào viện ck rhm
		$("#toolbarIdbtn_pkbvv_tmh").on("click", function() {
			if($("#hidKHAMBENHID").val() == "-1" ){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			_openReport(par, "NGT005_PHIEUKBVAOVIEN_CK_RHM_A4", 'pdf');

		});
		//Phiếu khám bệnh vào viện ck tmh
		$("#toolbarIdbtn_pkbvv_rhm").on("click", function() {
			if($("#hidKHAMBENHID").val() == "-1" ){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			_openReport(par, "NGT005_PHIEUKBVAOVIEN_CK_TMH_A4", 'pdf');

		});

		$("#toolbarIdbtnKySoPKBVV_CKMAT").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var obj = new Object();
			var _param = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'NGT005_PHIEUKBVAOVIEN_CK_MAT_A4'
			}];
			_kyCaRpt(_param);
		});
		$("#toolbarIdbtnKySoPKBVV_CKTMH").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var obj = new Object();
			var _param = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'NGT005_PHIEUKBVAOVIEN_CK_TMH_A4'
			}];
			_kyCaRpt(_param);
		});
		$("#toolbarIdbtnKySoPKBVV_CKRHM").on("click", function() {
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn thao tác. ');
				return false;
			}
			var obj = new Object();
			var _param = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'NGT005_PHIEUKBVAOVIEN_CK_RHM_A4'
			}];
			_kyCaRpt(_param);
		});

		//Phiếu khám và theo dõi bệnh nhân lưu BVTM-7969
		$("#toolbarIdgroup_2_52").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					khambenhid : _khambenhid
				};
				var url = "manager.jsp?func=../ngoaitru/NGT02K001_InPhieuKhamVaTheoDoi";
				var popup = DlgUtil.buildPopupUrl("divDlgPCKT", "divDlg", url, paramInput, "In phiếu khám và theo dõi bệnh nhân lưu", 505, 150);
				popup.open("divDlgPCKT");
			} else {
				DlgUtil.showMsg('Chưa có bệnh nhân nào được chọn!');
			}
		});
		$("#toolbarIdin_mabenhan").on("click", function() {
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value :$("#hidHOSOBENHANID").val()
			}];
			openReport('window', "IN_MABENHAN_A6", "pdf", par);
		});

		//In phiếu khám bệnh chuyển viện //hunglv L2PT-7935
		//if(_opt.hospital_id == "993") $("#toolbarIdgroup_2_20").show();  // SN PYN
		//else $("#toolbarIdgroup_2_20").hide();
		$("#toolbarIdgroup_2_20").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu khám bệnh.');
				return;
			}
			var par = [
				{
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				},{
					name : 'phongid',
					type : 'String',
					value : $("#hidPHONGID").val()
				}
			];
			_openReport(par, "PHIEU_KHAMBENHCHUYENVIEN_A5", 'pdf');
		});

		$("#toolbarIdprint_159").on("click", function() {
			if ($('#hidKHAMBENHID').val() == "-1") {
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
				return false;
			}
			var myVar = {
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				mabenhnhan : $('#txtMABENHNHAN').val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				maubenhphamid : "",
				action : "Add"
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgCDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K044_CapThuocK", myVar, "Chỉ định thuốc không thuốc", 800, 520);
			DlgUtil.open("dlgCDT");
		});

		//In  tờ điều trị
		$("#toolbarIdgroup_2_6").on("click", function() {
			var _benhnhanid = $("#hidBENHNHANID").val();
			var _khoaid = _opt.khoaid;
			var par = [{
				name : 'i_benhnhanid',
				type : 'String',
				value : _benhnhanid
			},{
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			},{
				name : 'i_phongid',
				type : 'String',
				value : $("#hidPHONGID").val()
			}];
			var _type="docx";				// yeu cau tu dev HUONGPV
			var rpName="NGT020_TODIEUTRI_39BV01_QD4069_A4_" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + _type;
			CommonUtil.inPhieu('window', 'NGT020_TODIEUTRI_39BV01_QD4069_A4_965', _type, par,rpName);
			//openReport('window', "NGT020_TODIEUTRI_39BV01_QD4069_A4_965", "rtf", par);

		});

		// click button xu tri > trả bệnh nhân (không khám)
		$("#toolbarIdhandling_3").on("click",function(e){
			DlgUtil.showConfirm("Thao tác sẽ trả bệnh nhân không khám. Bạn có tiếp tục?",function(flag) {
				if(flag){
					_xutribenhnhan(1);
				}
			});

		});

		// SMARDCARD start
		$("#toolbarIdbtnTTSMARTCARD").on("click", function() {
			var IDGDSM = paySmartCard($("#hidTIEPNHANID").val(), _opt.hospital_code);
			var dsCH = layDsCH(_opt.hospital_id);
			var _config_hddt = [];
			if (dsCH.VPI_N_TKHD == '1') {
				_config_hddt = jsonrpc.AjaxJson.ajaxCALL_SP_O('T.GET.DATA', JSON.stringify({
					TNAME : 'TAIKHOAN_HOADON_DIENTU',
					TSTS : 'STATUS',
					THID : 'CSYTID'
				}));
			}
			if (!IDGDSM) {
				DlgUtil.showMsg("có lỗi xảy ra");
			} else if (IDGDSM.responseCode == '00') {
				DlgUtil.showMsg(IDGDSM.responseMessage);
				if (cfObj.VPI_TTSMC_DAY_HDDT == '1') { // duonghn 220919
					if (IDGDSM.responseData) {
						var dsPhieu = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI.LAYHD.SHD', IDGDSM.responseData.ma_hethong);
						if (dsPhieu && dsPhieu.length > 0) {
							if (dsCH.VPI_DS_MSKH && dsCH.VPI_DS_MSKH != 0) {
								var myVar = new Object();
								myVar.arrPhieu = dsPhieu;
								myVar.dsCH = dsCH;
								myVar._config_hddt = _config_hddt;
								DlgUtil.buildPopupUrl("dlgChonMSKH", "dlgChonMSKHID", "manager.jsp?func=../vienphi/sendInv", myVar, "Chọn mẫu số/ ký hiệu", 600, 200);
								DlgUtil.open("dlgChonMSKH");
							} else {
								var msgSendInv = guiHDDTTheoDSPhieu(dsPhieu, dsCH, _config_hddt);
								DlgUtil.showMsg(msgSendInv);
							}
						}
					} else {
						DlgUtil.showMsg(JSON.stringify(IDGDSM));
					}
				}
			} else {
				DlgUtil.showMsg(IDGDSM.responseMessage);
			}
		});
		// SMARDCARD end

		// click button xu tri > ket thuc kham
		$("#toolbarIdbtnKTKH").bindOnce("click",function() {
			var trangthaitiepnhan = jsonrpc.AjaxJson.getOneValue("NGT.GETCHECKKTBA", [{"name":"[0]", "value":$('#hidTIEPNHANID').val()}]);
			if (trangthaitiepnhan != 0 && trangthaitiepnhan != null ) {
				DlgUtil.showMsg("Bệnh án đã kết thúc không thể thao tác, yêu cầu tải lại trang để cập nhật lại trang thái của bệnh nhân!");
				return false;
			}
			//tuyennx_edit_start_20191209 L2PT-12390
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : $("#hidTIEPNHANID").val()
			});
			var _tgrv = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			if($('#txtTHOIGIANRAVIEN').val() != "")
				_tgrv = $('#txtTHOIGIANRAVIEN').val();
			_sql_par.push({
				"name" : "[1]",
				value : _tgrv
			});
			_sql_par.push({
				"name" : "[2]",
				value : $("#hidKHAMBENHID").val()
			});
			var rs = jsonrpc.AjaxJson.getOneValue("NGT.NGT.TGKHAM", _sql_par);
			if(rs >0){
				DlgUtil.showMsg("BN có thời gian ra viện nhỏ hơn thời gian tiếp nhận, vui lòng nhập lại!");
				return ;
			}
			var rs_tg = jsonrpc.AjaxJson.getOneValue("NGT.CHECKDV.TGXTRI", _sql_par);
			if(rs_tg >0 && ($("#cboXUTRIKHAMBENHID").val() == "2" || $("#cboXUTRIKHAMBENHID").val() == "6")){
				DlgUtil.showMsg("BN có thời gian chỉ định dịch vụ lớn hơn thời gian xử trí, vui lòng nhập lại!");
				return ;
			}
			//L2PT-43979
			var _obj1 = new Object();
			_obj1.PHONGKHAMDANGKYID = $('#hidPHONGKHAMDANGKYID').val();
			_obj1.HINHTHUCXUTRIID = $("#cboXUTRIKHAMBENHID").val();
			_obj1.THOIGIANRAVIEN = $("#txtTHOIGIANRAVIEN").val();
			if(NGT_CHECK_TG_KHAM_CLS != '0'){
				var _checktg = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.TGKHAM.CLS", JSON.stringify(_obj1));
				if(_checktg == '1'){
					DlgUtil.showMsg("BN có chỉ định dịch vụ CLS thời gian khám bệnh dưới "+NGT_CHECK_TG_KHAM_CLS+" phút không thể thao tác!");
					return ;
				}
			}
			//tuyennx_add_end_L2PT-16401
			if (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU == '1'){
				var _sql_par = [];
				_sql_par.push({
						"name" : "[0]",
						value : $("#txtTHOIGIANRAVIEN").val()
					},{
						"name" : "[1]",
						value : $("#hidPHONGKHAMDANGKYID").val()
					}
				);
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECK5P.TGBD", _sql_par)
			}else{
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.NGTKHAM5P", _sql_par);
			}
			if(ret != '0'
				&& ( (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU1 == '0' && $("#cboXUTRIKHAMBENHID").val() != 0  && $("#cboXUTRIKHAMBENHID").val() != "2" && $("#cboXUTRIKHAMBENHID").val() != "6" )
					|| (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU1 == '1' && $("#cboXUTRIKHAMBENHID").val() != 0 ) )){
				if(cfObj.HIS_CANHBAO_KHAM5P == '2'){  //chặn  L2PT-13836
					DlgUtil.showMsg("BN có thời gian khám bệnh dưới 5 phút không thể thao tác!");
					return ;
				}
				DlgUtil.showConfirm("BN có thời gian khám bệnh dưới 5 phút bạn có muốn tiếp tục?",function(flag) {
					if(flag){
						if ( cfObj.HIS_CANHBAO_BNTUVONG == '1' && $("#cboXUTRIKHAMBENHID").val() == '8' ) {
							DlgUtil.showConfirm("Bệnh nhân xác nhận tử vong?", function (flag) {
								if (flag) {
									_xutribenhnhan(2);
								}
							});
						}else {
							_xutribenhnhan(2);
						}
					}
				});
			}
			else {
				if ( cfObj.HIS_CANHBAO_BNTUVONG == '1' && $("#cboXUTRIKHAMBENHID").val() == '8' ) {
					DlgUtil.showConfirm("Bệnh nhân xác nhận tử vong?", function (flag) {
						if (flag) {
							_xutribenhnhan(2);
						}
					});
				}else {
					_xutribenhnhan(2);
				}
			}
			//tuyennx_edit_end_20191209 L2PT-12390
		},5000);

		//dannd_L2PT-1149
		$("#toolbarIdbtnTaoPhieuTienSuDiUng").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Bạn chưa chọn bệnh nhân.');
				return;
			}
			paramInput={
				hosobenhan_id : $("#hidHOSOBENHANID").val(),
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgPhieuTSDU","divDlg","manager.jsp?func=../noitru/NTU02D128_PhieuTienSuDiUng",paramInput,"Phiếu tiền sử dị ứng",$( document ).width()-50, $( window ).height()-50);
			DlgUtil.open("divDlgPhieuTSDU");
		});
		//dannd_end
		//dannd_L2PT-55338
		$("#toolbarIdbtnPHIEUTRUYENDICH").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Bạn chưa chọn bệnh nhân.');
				return;
			}
			paramInput = {
				khambenh_id : $("#hidKHAMBENHID").val(),
				maubenhpham_id : "",
				isCopy : '0',//L2PT-2085
				userID : opt.user_id,//L2PT-4346s
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			if (cfObj.NTU_PHIEUTRUYENDICH_BDHN == '1') {
				dlgPopup = DlgUtil.buildPopupUrl("divDlgPTruyenDich", "divDlg", "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenDich_BDHN", paramInput, "Tạo Phiếu truyền dịch " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 650);
				DlgUtil.open("divDlgPTruyenDich");
			} else {
				dlgPopup = DlgUtil.buildPopupUrl("divDlgPTruyenDich", "divDlg", "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenDich", paramInput, "Tạo Phiếu truyền dịch " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 500);
				DlgUtil.open("divDlgPTruyenDich");
			}
		});
		EventUtil.setEvent("assignTruyenDich_cancel", function(e) {
			DlgUtil.close("divDlgPTruyenDich");
		});
		EventUtil.setEvent("assignSevice_SaveTruyenDich", function(e) {
			DlgUtil.showMsg(e.msg);
			DlgUtil.close("divDlgPTruyenDich");
		});
		// L2PT-8640 - SONDN - 10/09/2019
		$("#toolbarIdbtnTIEPNHANCC").on("click",function(e){
			window.location.replace("/vnpthis/main/manager.jsp?func=../ngoaitru/NGT01T001_tiepnhan_ngt_vs2&hd=2");
		});
		$("#toolbarIdbtnDAYEBHYT").on("click",function(e) {
			//Tích hợp chức năng gửi XML đến cổng eBHYT- dannd_L2PT-21153
			if (sendeBHYT == '1') {
				checkXML('-1', $("#txtTHOIGIANRAVIEN").val(), $("#txtTHOIGIANRAVIEN").val(), $("#hidTIEPNHANID").val(), opt.hospital_id);
			}
		});
		// xu ly ket thuc kham cho BM2
		$("#toolbarIdbtnKTKHVS2").bindOnce("click",function(e){
			var trangthaitiepnhan = jsonrpc.AjaxJson.getOneValue("NGT.GETCHECKKTBA", [{"name":"[0]", "value":$('#hidTIEPNHANID').val()}]);
			if (trangthaitiepnhan != 0 && trangthaitiepnhan != null ) {
				DlgUtil.showMsg("Bệnh án đã kết thúc không thể thao tác, yêu cầu tải lại trang để cập nhật lại trang thái của bệnh nhân!");
				return false;
			}
			var NTU_KTBA_CHECK_KQCLS = cfObj.NTU_KTBA_CHECK_KQCLS;
			if(NTU_KTBA_CHECK_KQCLS && NTU_KTBA_CHECK_KQCLS != '0' ){
				var objData = new Object();
				objData["HOSOBENHANID"] = $('#hidHOSOBENHANID').val();
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV090", JSON.stringify(objData));
				if (fl != '1') {
					DlgUtil.showMsg("Còn dịch vụ " + fl + " chưa trả kết quả. Yêu cầu trả kết quả hoặc xóa nếu không thực hiện !");
					return false;
				}
			}
			var objData = new Object();
			FormUtil.setFormToObject("divKB","",objData);

			var _xutri = $("#cboXUTRIKHAMBENHID").val();
			if ($("#hidCHUYENKHAMNGT").val() == "1"){
				if (cfObj.NGT_CHANKTK_CHOKHAM == '1') {
					var selRowId =  $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
					var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);
					if(rowData.TRANGTHAIKHAMBENH == 1 ){
						DlgUtil.showMsg('Bệnh nhân ở trạng thái chờ khám không thể kết thúc khám');
						return;
					}
				}
				_xutri = "9"; 								// xu tri khac voi bn kham chuyen khoa;
			}else {
				if((_xutri == "" || _xutri == null || _xutri == 'null' || _xutri == "0")&& ($("#hidKHAMCHINHPHU").val() != "0")){
					DlgUtil.showMsg("Yêu cầu nhập xử trí trước khi kết thúc khám");
					return;
				}
			}
			objData["HOSOBENHANID"] = $("#hidHOSOBENHANID").val();
			objData["TIEPNHANID"] = $("#hidTIEPNHANID").val();
			objData["KHAMBENHID"] = $("#hidKHAMBENHID").val();
			objData["XUTRIKHAMBENHID"] = _xutri;
//			objData["XUTRIKHAMBENHID"] = $("#hidXUTRIKHAMBENHID").val();
			objData["PHONGKHAMDANGKYID"] = $("#hidPHONGKHAMDANGKYID").val();
			objData["PHONGID"] = _opt.phongid;
			objData["KHOAID"] = _opt.khoaid;
			objData["CHINHPHU"] = _khamchinhphudetail;

			//dannd_L2PT-68426 check ky so ket thuc kham
			if (cfObj.HIS_KTBA_CHECK_KYCA == '1' ) {
				if ($("#hidCHUYENKHAMNGT").val() == "1"){
					var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02.CHECKKYSOKCK",JSON.stringify(objData));
				}else{
					var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV079", $("#hidHOSOBENHANID").val());
				}
				if (result_ct != '|') {
					if (result_ct == '-1') {
						DlgUtil.showMsg('Có lỗi khi xử lý');
						return;
					} else {
						var msg = result_ct.split("|");
						if (msg[0] != '') {
							DlgUtil.showMsg('Tồn tại ' + msg[0] + ' chưa thực hiện ký số/điện tử.');
							return;
						}
						if (msg[1] != '') {
							DlgUtil.showMsg('Tồn tại ' + msg[1] + ' chưa hoàn thành ký số/điện tử.');
							return;
						}
					}
				}
			}
			//dannd_L2PT-22445 thoi gian xu tri nam trong khoang cau hinh  SO_NGAY_XUTRI_DIEUTRI thiet lap
			var par = ['SO_NGAY_XUTRI_DIEUTRI'];
			var _songaycheck = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
			if (_songaycheck != '0' && $("#hidKHAMCHINHPHU").val() == '1' && $('#cboXUTRIKHAMBENHID').val() !== '2' && $('#cboXUTRIKHAMBENHID').val() !== '6') {
				var _songay = parseInt(_songaycheck);
				var ngayHientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MM:SS');
				var ngayRa = $('#txtTHOIGIANRAVIEN').val();
				var t_truoc = diffDate(ngayHientai,ngayRa,'DD/MM/YYYY HH:mm','minutes')/60;
				var t_sau = diffDate(ngayRa,ngayHientai,'DD/MM/YYYY HH:mm','minutes')/60;
				if (t_truoc > _songay*24 || t_sau > _songay*24) {
					return DlgUtil.showMsg("Thời gian xử trí của bệnh nhân không nằm trong khoảng với cấu hình SO_NGAY_XUTRI_DIEUTRI đã thiết lập");
				}
			}
			//tuyennx_edit_start_20191209 L2PT-12390
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : $("#hidTIEPNHANID").val()
			});
			var _tgrv = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			if($('#txtTHOIGIANRAVIEN').val() != "")
				_tgrv = $('#txtTHOIGIANRAVIEN').val();
			_sql_par.push({
				"name" : "[1]",
				value : _tgrv
			});
			_sql_par.push({
				"name" : "[2]",
				value : $("#hidKHAMBENHID").val()
			});
			var rs = jsonrpc.AjaxJson.getOneValue("NGT.NGT.TGKHAM", _sql_par);
			if(rs >0){
				DlgUtil.showMsg("BN có thời gian ra viện nhỏ hơn thời gian tiếp nhận, vui lòng nhập lại!");
				return ;
			}
			var rs_tg = jsonrpc.AjaxJson.getOneValue("NGT.CHECKDV.TGXTRI", _sql_par);
			if(rs_tg >0 && ($("#cboXUTRIKHAMBENHID").val() == "2" || $("#cboXUTRIKHAMBENHID").val() == "6")){
				var sophieu_arr = jsonrpc.AjaxJson.getOneValue("NGT.CHECKDV.TGXTRI1", _sql_par );
				DlgUtil.showMsg("BN có thời gian chỉ định dịch vụ lớn hơn thời gian xử trí, vui lòng nhập lại! số phiếu :" + sophieu_arr);
				return ;
			}

			// sondn L2PT-3840
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				phongid : _opt.phongid,
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val()
			};
			var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KTKHAM2", JSON.stringify(myVar));
			if(check == 'codvcls'){
				DlgUtil.showMsg('Bệnh nhân đang có chỉ định dịch vụ trạng thái đang chờ tiếp nhận, có thể hủy phiếu để kết thúc khám');
				return false;
			}
				// end sondn L2PT-3840

			//tuyennx_add_start_20170727  y/c HISL2BVDKHN-247
			else if(check == 'ngaydichvu'){
				DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện không thể kết thúc khám');
				return false;
			}
			// END sondn L2PT-3840

			//tuyennx_add_end_L2PT-16401
			if (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU == '1'){
				var _sql_par = [];
				_sql_par.push({
						"name" : "[0]",
						value : $("#txtTHOIGIANRAVIEN").val()
					},{
						"name" : "[1]",
						value : $("#hidPHONGKHAMDANGKYID").val()
					}
				);
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECK5P.TGBD", _sql_par)
			}else{
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.NGTKHAM5P", _sql_par);
			}
			if(ret != '0'
				&& ( (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU1 == '0' && $("#cboXUTRIKHAMBENHID").val() != 0  && $("#cboXUTRIKHAMBENHID").val() != "2" && $("#cboXUTRIKHAMBENHID").val() != "6" )
					|| (cfObj.HIS_CANHBAO_KHAM5P_TGBATDAU1 == '1' && $("#cboXUTRIKHAMBENHID").val() != 0 ) )){
				if(cfObj.HIS_CANHBAO_KHAM5P == '2'){  //chặn  L2PT-13836
					DlgUtil.showMsg("BN có thời gian khám bệnh dưới 5 phút không thể thao tác!");
					return ;
				}
				DlgUtil.showConfirm("BN có thời gian khám bệnh dưới 5 phút bạn có muốn tiếp tục?",function(flag) {
					if(flag){
						var ret1 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.KETTHUC.VS2",JSON.stringify(objData));

						var rets = ret1.split(',');

						if(rets[0] == 'r_checkdl'){
							var myVar={
								thongbao : rets[1]
							};
							dlgPopup=DlgUtil.buildPopupUrl("dlgBPKT","divDlg","manager.jsp?func=../ngoaitru/NGT04K006_ThongBao",myVar,"Thông báo",600,420);
							DlgUtil.open("dlgBPKT");
							return false;
						}

						DlgUtil.showMsg(ret1);
						//ductx -bvtm-5439
						if(opt.hospital_id == '10284'){
							sendSmsAuto();
						}
						//end bvtm-5439
						//L2PT-40477
						if($('#cboXUTRIKHAMBENHID').val() == "2" || $('#cboXUTRIKHAMBENHID').val() == "6"){
							gw_tiepnhankham($('#hidTIEPNHANID').val());
							//gw_batdaukham($('#hidTIEPNHANID').val());
						}
						_dayDonThuocDT_KTK(cfObj.DTDT_DAY_DONTHUOC, $('#hidKHAMBENHID').val(), $("#hidLOAITIEPNHANID").val())	 //L2PT-30504
						if(cfObj.KHAM_ONLINE_WS == "1"){
							var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT",[$('#hidKHAMBENHID').val(),_opt.hospital_code].join('$'));
							if(data_ar && data_ar.length > 0){
								var objKhamOL = {
									"NGAYKETTHUC":data_ar[0].NGAY_RAVIEN,
									"MALUOTKHAM_HIS":$("#hidMAHOSOBENHAN").val(),
									"TRANGTHAI":"1",
									"NGAYHEN":data_ar[0].THOIGIANLICHHEN,
									"DONTHUOC":data_ar[0].SOPHIEU == "" ? '0' : data_ar[0].SOPHIEU,
									"MA_ICD":data_ar[0].MACHANDOANRAVIEN,
									"TEN_ICD":data_ar[0].CHANDOANRAVIEN,
									"BACSY":  _opt.fullname,
									"MABS":  _opt.user_name, //L2PT-5883
									"BENHPHU": data_ar[0].BENHPHU,
									"PHONGKHAM":  _opt._subdept_name,
									"HUONG_DIEUTRI": data_ar[0].HUONGDIEUTRI
								};
								//var objKhamOL = new Object();
//		    			var inputJson = JSON.stringify(objKhamOL);
								//var inputJson = "{\"NGAYKETTHUC\":\"20200410 1600\",\"MALUOTKHAM_HIS\":\"BV0000017641\",\"TRANGTHAI\":\"1\",\"NGAYHEN\":\"\",\"DONTHUOC\":\"\",\"MA_ICD\":\"0004607 - Chính\",\"TEN_ICD\":\"0004607 - Chính\"}";
								var _objThongtinLog = new Object();
								_objThongtinLog["I_CHUCNANG"] = "Trả KQK 1: "+$("#hidMAHOSOBENHAN").val();
								_objThongtinLog["I_KQKETNOI"] = JSON.stringify(objKhamOL);
								_objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
								var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS",JSON.stringify(_objThongtinLog));

								var ret = ajaxSvc.KhamOnlineWS.callApiAppBN('1',objKhamOL,_opt.hospital_code);

								_objThongtinLog["I_CHUCNANG"] = "Trả KQK 2: "+$("#hidMAHOSOBENHAN").val();
								_objThongtinLog["I_KQKETNOI"] = ret;
								_objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
								var f2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS",JSON.stringify(_objThongtinLog));

								if(ret == "-1"){
									DlgUtil.showMsg("Đã có lỗi xảy ra khi gửi dữ liệu khám online !");
								}
//						var ret = ajaxSvc.KhamOnlineWS.sendDataKetThuc(inputJson);
//						if(ret.includes("OK;")){
//							DlgUtil.showMsg("Gửi thông tin kết thúc khám thành công!");
//						}else{
//							DlgUtil.showMsg("Lỗi gửi thông tin kết thúc khám!");
//						}
							}
						}
						_loadGridData(_opt.phongid);
					}
				});
			}
			else {
				var ret1 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.KETTHUC.VS2",JSON.stringify(objData));

				var rets = ret1.split(',');

				if(rets[0] == 'r_checkdl'){
					var myVar={
						thongbao : rets[1]
					};
					dlgPopup=DlgUtil.buildPopupUrl("dlgBPKT","divDlg","manager.jsp?func=../ngoaitru/NGT04K006_ThongBao",myVar,"Thông báo",600,420);
					DlgUtil.open("dlgBPKT");
					return false;
				}

				DlgUtil.showMsg(ret1);
				//ductx -bvtm-5439
				if(opt.hospital_id == '10284'){
					sendSmsAuto();
				}
				//end bvtm-5439
				//L2PT-40477
				if($('#cboXUTRIKHAMBENHID').val() == "2" || $('#cboXUTRIKHAMBENHID').val() == "6"){
					gw_tiepnhankham($('#hidTIEPNHANID').val());
					//gw_batdaukham($('#hidTIEPNHANID').val());
				}
				_dayDonThuocDT_KTK(cfObj.DTDT_DAY_DONTHUOC, $('#hidKHAMBENHID').val(), $("#hidLOAITIEPNHANID").val())	 //L2PT-30504
				if(cfObj.KHAM_ONLINE_WS == "1"){
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT",[$('#hidKHAMBENHID').val(),_opt.hospital_code].join('$'));
					if(data_ar && data_ar.length > 0){
						var objKhamOL = {
							"NGAYKETTHUC":data_ar[0].NGAY_RAVIEN,
							"MALUOTKHAM_HIS":$("#hidMAHOSOBENHAN").val(),
							"TRANGTHAI":"1",
							"NGAYHEN":data_ar[0].THOIGIANLICHHEN,
							"DONTHUOC":data_ar[0].SOPHIEU == "" ? '0' : data_ar[0].SOPHIEU,
							"MA_ICD":data_ar[0].MACHANDOANRAVIEN,
							"TEN_ICD":data_ar[0].CHANDOANRAVIEN,
							"BACSY":  _opt.fullname,
							"MABS":  _opt.user_name, //L2PT-5883
							"BENHPHU": data_ar[0].BENHPHU,
							"PHONGKHAM":  _opt._subdept_name,
							"HUONG_DIEUTRI": data_ar[0].HUONGDIEUTRI
						};
						//var objKhamOL = new Object();
//		    			var inputJson = JSON.stringify(objKhamOL);
						//var inputJson = "{\"NGAYKETTHUC\":\"20200410 1600\",\"MALUOTKHAM_HIS\":\"BV0000017641\",\"TRANGTHAI\":\"1\",\"NGAYHEN\":\"\",\"DONTHUOC\":\"\",\"MA_ICD\":\"0004607 - Chính\",\"TEN_ICD\":\"0004607 - Chính\"}";
						var _objThongtinLog = new Object();
						_objThongtinLog["I_CHUCNANG"] = "Trả KQK 1: "+$("#hidMAHOSOBENHAN").val();
						_objThongtinLog["I_KQKETNOI"] = JSON.stringify(objKhamOL);
						_objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
						var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS",JSON.stringify(_objThongtinLog));

						var ret = ajaxSvc.KhamOnlineWS.callApiAppBN('1',objKhamOL,_opt.hospital_code);

						_objThongtinLog["I_CHUCNANG"] = "Trả KQK 2: "+$("#hidMAHOSOBENHAN").val();
						_objThongtinLog["I_KQKETNOI"] = ret;
						_objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
						var f2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS",JSON.stringify(_objThongtinLog));

						if(ret == "-1"){
							DlgUtil.showMsg("Đã có lỗi xảy ra khi gửi dữ liệu khám online !");
						}
//						var ret = ajaxSvc.KhamOnlineWS.sendDataKetThuc(inputJson);
//						if(ret.includes("OK;")){
//							DlgUtil.showMsg("Gửi thông tin kết thúc khám thành công!");
//						}else{
//							DlgUtil.showMsg("Lỗi gửi thông tin kết thúc khám!");
//						}
					}
				}
				_loadGridData(_opt.phongid);
			}
			//tuyennx_edit_end_20191209 L2PT-12390
		},5000);
		//in phoi chenh lech
		$("#toolbarIdbtnIN_CHENHLECH").on("click", function() {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value :  $('#hidTIEPNHANID').val()
			} ];
			openReport('window', "NGT001_BKCPKCB_QD6556_DOC_CHENHLECH_A4", "pdf", par);
		});
		//START L2PT-52392
		$("#toolbarIdban_QLHIV").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				bacsydieutriid : rowData.BACSYDIEUTRIID,
				benhnhanid : rowData.BENHNHANID,
				thoigianvaovien : $("#hidNGAYTIEPNHAN").val(),
				tiepnhanid : rowData.TIEPNHANID,
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				ma_bhyt: rowData.MA_BHYT,
				cccd: rowData.SOCMTND
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgHIV", "divDlg", "manager.jsp?func=../noitru/NTU02D228_QLBN_HIV", paramInput, "QL bệnh nhân HIV", 1200, 600);
			DlgUtil.open("divDlgHIV");
		});
		//L2PT-52392 end
		$("#toolbarIdban_QLLAO").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				bacsydieutriid : rowData.BACSYDIEUTRIID,
				benhnhanid : rowData.BENHNHANID,
				thoigianvaovien : $("#hidNGAYTIEPNHAN").val(),
				tiepnhanid : rowData.TIEPNHANID,
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				ma_bhyt: rowData.MA_BHYT
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgHIV", "divDlg", "manager.jsp?func=../noitru/NTU02D232_QLBN_LAO", paramInput, "QL bệnh nhân Lao", 1200, 600);
			DlgUtil.open("divDlgHIV");
		});
		//Kiểm điểm tử vong
		$("#toolbarIdbtnKHAC_2").on("click", function() {
			paramInput={
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				diachi : $('#txtDIACHI').val(),
				namsinh : $('#hidNAMSINH').val(),
				khambenhid : $('#hidKHAMBENHID').val(),
				ngaytn : $('#hidNGAYTN').val(),
				nghenghiep : $("#hidTENNGHENGHIEP").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgKDTV","divDlg","manager.jsp?func=../ngoaitru/NGT02K033_KiemdiemTuVong",paramInput,"KIỂM ĐIỂM TỬ VONG",900,430);
			DlgUtil.open("dlgKDTV");
		});
		//Start_Dannd_L2PT-7766
		$("#toolbarIdbtnDINHDUONG").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
			paramInput={
				type : 3,
				khambenhid : $("#hidKHAMBENHID").val(),
				modeView: 2
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgSangLocNguyCo","divDlg","manager.jsp?func=../noitru/NTU02D086_SangLocDinhDuong",paramInput,"Sàng lọc và đánh giá dinh dưỡng",1000,500);
			DlgUtil.open("divDlgSangLocNguyCo");
		});
		$("#toolbarIdbtn_ttdd").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
			var _hosobenhanid = $("#hidHOSOBENHANID").val();
			var _khambenhid = $("#hidKHAMBENHID").val();
			paramInput = {
				hosobenhanid: _hosobenhanid,
				khambenhid: _khambenhid
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgTTDD", "divDlg", "manager.jsp?func=../noitru/NTU02D126_PhieuTinhTrangDinhDuong", paramInput, "Phiếu đánh giá trình trạng dinh dưỡng (Trưởng thành)", 1250, 600);
			DlgUtil.open("dlgTTDD");
		});

		$("#toolbarIdbtn_ttdd_nhi").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
			var _hosobenhanid = $("#hidHOSOBENHANID").val();
			var _khambenhid = $("#hidKHAMBENHID").val();
			paramInput = {
				hosobenhanid: _hosobenhanid,
				khambenhid: _khambenhid
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgTTDDNhi", "divDlg", "manager.jsp?func=../noitru/NTU02D129_PhieuTinhTrangDinhDuong_Nhi", paramInput, "Phiếu đánh giá trình trạng dinh dưỡng (Nhi)", 1100, 600);
			DlgUtil.open("dlgTTDDNhi");
		});
		$("#toolbarIdbtndt_18_1").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgSLDD_BNT","divDlg","manager.jsp?func=../noitru/NTU02D086_SangLocDinhDuongBTN",paramInput,"Đánh giá tình trạng dinh dưỡng >=18 tuổi, không mang thai",1000,600);
			DlgUtil.open("divDlgSLDD_BNT");
		});
		//End_Dannd_L2PT-7766
		//Biên bản tử vong
		$("#toolbarIdbtnKHAC_1").on("click", function() {
			paramInput={
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				diachi : $('#txtDIACHI').val(),
				namsinh : $('#hidNAMSINH').val(),
				khambenhid : $('#hidKHAMBENHID').val(),
				ngaytn : $('#hidNGAYTN').val(),
				nghenghiep : $("#hidTENNGHENGHIEP").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgBBTV","divDlg","manager.jsp?func=../ngoaitru/NGT02K032_BienbanTuVong",paramInput,"BIÊN BẢN TỬ VONG",900,430);
			DlgUtil.open("dlgBBTV");
		});

		// thong tin tu vong
		$("#toolbarIdbtnKHAC_0").on("click",function(e){
			_changexutri("8");
		});

		$("#toolbarIdbtnInGIAYBAOTU").on("click",function(e){
			$("#toolbarIdbtnKHAC_0").trigger("click");
		});

		EventUtil.setEvent("ngt02k054_chuyenyckham_close",function(e){
			_loadGridData(_opt.phongid);
			DlgUtil.close("dlgCongKham");
		});

		$("#toolbarIdbtnDOICONGKHAM").on("click",function(e){
			if (_doicongkhamkb == "-1"){
				DlgUtil.showMsg("Yêu cầu bật cấu hình Cho phép sử dụng chức năng này. ");
				return;
			}

			var myVar={
				khambenhid : $('#hidKHAMBENHID').val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				maubenhphamid : $("#hidMAUBENHPHAMID").val(),
				dichvukhambenhid : $("#hidDICHVUKHAMBENHID").val(),			// chua co;
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				mode : _doicongkhamkb,					// 0; 1; 2
				manhinhkhambenh : 1
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCongKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K054_CHUYENYCKHAM",myVar, "Cập nhật công khám/phòng khám", 900,300);
			DlgUtil.open("dlgCongKham");
		});

		// thong tin ra viện
		$("#toolbarIdbtnKHAC_4").on("click",function(e){
			_changexutri("1");
		});

		// click tab lay ds
		$("#tabHanhChinhTab").on("click",function(e){
			height_window = $(window).height();   // returns height of browser viewport
			height_dsbn = $('#tabHanhChinh').height();
			height_divMain = $('#hidDocumentHeight').val();
			console.log('height_window1:' + height_window );
			console.log('height_divMain1:' + height_divMain );
			if(height_dsbn + 110 < height_window){
				$('#divMain').css('height',height_dsbn + 110);
			} else if(height_window < height_dsbn + 110){
				$("#divMain").css('height',height_dsbn + 110);
			} else if(height_dsbn + 110 == height_window) {
				$('#divMain').css('height',height_dsbn + 110);
			}
			_loadGridData(_opt.phongid);
		});

		// thong tin hen kham tiep
		$("#toolbarIdbtnKHAC_6").on("click",function(e){
			_changexutri("4");
		});

		// thong tin hen kham moi
		$("#toolbarIdbtnKHAC_7").on("click",function(e){
			_changexutri("5");
		});

		$("#toolbarIdbtnKHAC_11").on("click",function(e){
			DlgUtil.showConfirm("Bạn có muốn cập nhật BADTNGT này? ", function(flag){
				if(flag){
					_capnhatBADTNGT();
				}
			});
		});

		$("#toolbarIdgroup_NghihuongBHXH").on("click",function(e){
			var param = {
				khambenhid : $('#hidKHAMBENHID').val()
			};
			var w = $( document ).width()-50;
			var h = $( window ).height()-50;
			_showDialog("NGT02K058_Thongtin_nghiduong", param, 'Thông tin nghỉ hưởng BHXH',w,h);
		});

		// thong tin hen kham moi
		$("#toolbarIdbtnKHAC_9").on("click",function(e){
			var param = {
				khambenhid : $('#hidKHAMBENHID').val()
			};
			var w = $( document ).width()-50;
			var h = $( window ).height()-50;
			_showDialog("NGT02K058_Thongtin_nghiduong", param, 'Thông tin nghỉ hưởng BHXH',w,h);
		});

		$("#toolbarIdbtnTVDD").on("click",function(e){
			var param = {
				mahosobenhan : $('#hidMAHOSOBENHAN').val(),
				loaibenhanid : 23
			};

			var w = $( document ).width()-50;
			var h = $( window ).height()-50;
			dlgPopup=DlgUtil.buildPopupUrl("dlgXuTri","divDlg","manager.jsp?func=../noitru/NTU02D089_HoiChanDinhDuong",param,"Tạo phiếu tư vẫn dinh dưỡng",w,h);
			DlgUtil.open("dlgXuTri");
		});

		$("#toolbarIdbtnFORM_KSKXINVIEC").on("click",function(e){
			if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == null || $("#hidKHAMBENHID").val() == 'null'){
				DlgUtil.showMsg("Chọn bệnh nhân để thực hiện tiếp chức năng này. ");
				return;
			}
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieukskxinviec","divDlg","manager.jsp?func=../ksk/KSK06D006_FORM_KSKXINVIEC",myVar,"Phiếu KSK Xin Việc",1600,900);
			DlgUtil.open("dlgPhieukskxinviec");
		});

		// tuyennx_add_start L2PT-16020
		$("#toolbarIdbtnKSKLaiXe1").on("click", function() {
			var param = {
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				namsinh : $('#hidNAMSINH').val(),
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgKSKLaiXe","divDlg","manager.jsp?func=../ngoaitru/NGT02K079_KSK_LaiXe",param,"Khám sức khỏe lái xe",1200,440);
			DlgUtil.open("dlgKSKLaiXe");
		});
		//tuyennx_add_end L2PT-16020

		$("#toolbarIdbtnKHAC_9_1").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
			$("#toolbarIdbtnKHAC_9").click();
		});

		$("#toolbarIdgroup_2_Tamung").on("click",function(e){
			var paramInput={
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid: $("#hidKHAMBENHID").val(),
				khoaid: opt.khoaid,
				thoigianvaovien: $('#txtDENKHAMLUC').val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgPhieuTamUng","divDlg","manager.jsp?func=../noitru/NTU01H021_PhieuTamUngBenhNhan",paramInput,"Lập phiếu tạm ứng",900,460);
			DlgUtil.open("divDlgPhieuTamUng");
		});

		$("#toolbarIdgroup_0_cc").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy xác nhận cấp cứu.');
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()}
			];
			openReport('window', "PHIEU_XACNHAN_BENHNHAN_CAPCUU", 'pdf', par);
		});

		$("#toolbarIdgroup_0_trathe").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in phiếu trả thẻ.');
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $('#hidKHAMBENHID').val()
			}];
			openReport('window', "PHIEU_NHANTRATHEBHYT_A4_924", "pdf", par);
		});

		// thong tin chuyển viện
		$("#toolbarIdbtnKHAC_5").on("click",function(e){
			_changexutri("7");
		});

		// click tab benh an
		$("#tabBenhAnTab").on("click",function(e){
			//widget thong tin benh an
			$('#tabBenhAn').ntu02d022_ttba({
				_khambenhid: $("#hidKHAMBENHID").val()
			});
		});

		// click tab dieu tri
		$("#tabDieuTriTab").on("click",function(e){
			//widget khoi tao grid dieu tri
			$('#tabDieuTri').ntu02d027_dt({
				_grdDieuTri : 'grdDieuTri',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_DieuTri,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		//widget cho tab cham soc
		$("#tabChamSocTab").on("click", function(e) {
			$('#tabChamSoc').ntu02d028_pcs({
				_grdChamSoc : 'grdChamSoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_ChamSoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		});


		$("#idTabNgayGiuong").on("click",function(e){
			//widget cho tab ngay giuong
			$('#divNgayGiuong').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid: $("#hidBENHNHANID").val(),
				_lnmbp:	'12',
				_loaidichvu: '13',
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		//calback cho dong man hinh phieu dieu tri
		EventUtil.setEvent("treatment_cancel", function(e) {
			//widget phieu dieu tri
			$('#tabDieuTri').ntu02d027_dt({
				_grdDieuTri : 'grdDieuTri',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_DieuTri,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
			DlgUtil.close("divDlgPhieuDieuTri");
		});
		//hongdq-- luu in phieu dieu tri
		EventUtil.setEvent("treatment_print", function(e) {
			var par = [ {
				name : 'i_benhnhanid',
				type : 'String',
				value : $("#hidBENHNHANID").val()
			}, {
				name : 'i_maubenhphamid',
				type : 'String',
				value : e.msg
			}, {
				name : 'i_khoaid',
				type : 'String',
				value : _opt.khoaid
			} ];
			// kham benh ngt
			if($("#hidLOAITIEPNHANID").val() == '1' && $("#company_id").val() == '10284'){
				openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_NGT", "pdf", par);
			}else{
				openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", "pdf", par);
			}
		});

		// click tab xet nghiem
		$("#tabXetNghiemTab").on("click",function(e){
			//widget khoi tao grid danh sach xet nghiem
			$('#tabXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_XetNghiem,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_formCall:"KB_MHC",
				_hosobenhanid: ""
			});
		});

		// click tab CDHA
		$("#tabCDHATab").on("click",function(e){
			//widget khoi tao grid danh sach CDHA
			$('#tabCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_CDHA,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_formCall:"KB_MHC",
				_hosobenhanid: ""
			});
		});

		// click tab chuyen khoa
		$("#tabChuyenKhoaTab").on("click",function(e){
			//widget khoi tao grid danh sach chuyen khoa
			$('#tabChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua  : 'grdCKketQua',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_ChuyenKhoa,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_formCall:"KB_MHC",
				_hosobenhanid: ""
			});
		});

		// click tab thuoc
		$("#tabThuocTab").on("click",function(e){
			//widget cho tab thong tin thuoc
			$('#tabThuoc').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_Phieuthuoc,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: "",
				_loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				_doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val()
			});
		});

		// click tab vat tu
		$("#tabVatTuTab").on("click",function(e){
			//widget cho tab thong tin vat tu
			$('#tabVatTu').ntu02d034_pvt({
				_grdVatTu : 'grdVatTu',
				_gridVatTuDetail : 'grdChiTietVatTu',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_Phieuvattu,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: "",
				_loaitiepnhanid : $("#hidLOAITIEPNHANID").val()
			});
		});

		// click tab viện phí
		$("#tabVienPhiTab").on("click",function(e){
			$('#tabVienPhi').vpi01t006_ttvp({
				_tiepnhanid : $("#hidTIEPNHANID").val(),
				_dept_id : _opt.khoaid
			});
		});

		//widget cho tab phieu van chuyen
		$("#tabCongKhamTab").on("click",function(e){
			$('#tabCongKham').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid: $("#hidBENHNHANID").val(),
				_lnmbp:	'3',
				_loaidichvu: "2",
				_modeView: 1, // =1 chi view; !=1 la update // hunglv L2PT-7815 fix = 1
				_hosobenhanid: "",
				_modeXoaphieu: "1"						//START L2PT-1357
			});
		});

		// click tab ẩn hiện
		$("#tabAnHien").on("click",function(e){
			if(checkDsFull == false){
				$("#divHANHCHINH").hide();
				$("#divDSKham").removeClass("col-xs-6");
				$("#divDSKham").addClass("col-xs-12");
				$('#tabAnHien ins').html('');
				$('#tabAnHien ins').html('Hiện chi tiết');
				checkDsFull = true;
			}
			else{
				$("#divHANHCHINH").show();
				$("#divDSKham").addClass("col-xs-6");
				$("#divDSKham").removeClass("col-xs-12");
				$('#tabAnHien ins').html('');
				$('#tabAnHien ins').html('Ẩn chi tiết');
				checkDsFull = false;
			}
		});

		// sondn L2PT-26844
		$("#tabHoiChuanTab").on("click",function(e){
			$('#tabHoiChuan').ntu02d032_phc({
				_grdHoiChan : 'grdPhieuHoiChuan',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid: $("#hidBENHNHANID").val(),
				_lnmbp:	'15',								// phieu hoi chan;
				_modeView: '0',
				_hosobenhanid: "",
				_formCall:"KB_MHC" //L2PT-55717
			});
		});
		// end sondn L2PT-26844

		//L2PT-54954 start
		$("#tabLSDieuTriTab").on("click",function(e){
			paramInput={
				benhnhanId : $("#hidBENHNHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgLichSuBA","divDlg","manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn",paramInput,"LỊCH SỬ BỆNH ÁN",1320,610);
			DlgUtil.open("dlgLichSuBA");
		});
		//L2PT-54954 end

		$("#tabDanhSachTab").on("click",function(e){
			$('#tabDanhSach').ngt02k078_dstn({
				_grdDSTN: "grdDanhSachTiepNhan",
				imgPath : _opt.imgPath
			});
		});

		$("#tabBADTNGTTab").on("click",function(e){
			if($("#hidKHAMBENHID").val() == "-1"){

				DlgUtil.showMsg("Yêu cầu chọn thông tin bệnh nhân để cập nhật", function(){
					$("#tabBADTNGTTab").removeClass("active");
					$("#tabHanhChinh").addClass("active");

				});
				return;
			}

			$('#tabBADTNGT').ngt02k090_badtngt({
				_grdDSTN: "grdDanhSachTiepNhan",
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val()
			});
		});

		//widget cho tab phieu van chuyen
		$("#tabPhieuVanChuyenTab").on("click",function(e){
			$('#tabPhieuVanChuyen').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid: $("#hidBENHNHANID").val(),
				_lnmbp:	'16',
				_loaidichvu: "14",
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});
		$("#tabPhieuTruyenDichTab").on("click",function(e){
			$('#tabPhieuTruyenDich').ntu02d030_td({
				_grdTruyenDich : 'grdPhieuTruyenDich',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid: $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_TruyenDich,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		//widget cho tab ls tiêm chủng
		$("#tabLSTiemChungTab").on("click", function (e) {
			$('#tabLSTiemChung').ngt03k003_lstiemchung({
				_grdLSTC: 'grdLSTiemChung',
				_hoSoBenhNhanId: $("#hidHOSOBENHANID").val()
			});
		});

		$("#tabPhieuThuKhacTab").on("click",function(e){
			$('#tabPhieuThuKhac').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid: $("#hidBENHNHANID").val(),
				_lnmbp:	'17',
				_loaidichvu: "1",
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		//tab danh sach cho kham
		$("#tabChoDangKham").on("click",function(e){
			$('#cboTRANGTHAI').val(49);
			$('#cboTRANGTHAI').trigger('change');
		});

		$("#tabChoKham").on("click",function(e){
			$('#cboTRANGTHAI').val(1);
			$('#cboTRANGTHAI').trigger('change');
		});

		$("#tabDSKham").on("click",function(e){
			$('#cboTRANGTHAI').val(4);
			$('#cboTRANGTHAI').trigger('change');
		});
		$("#tabKTKham").on("click",function(e){
			$('#cboTRANGTHAI').val(9);
			$('#cboTRANGTHAI').trigger('change');

		});

		$("#tabDSTatCa").on("click",function(e){
			$('#cboTRANGTHAI').val(0);
			$('#cboTRANGTHAI').trigger('change');
		});

		function _openDialogThuoc(_opt,_loaikedon, _title, _loadkhotheo){
			var par = ['HIS_KEDONTHUOC_CHITIET_NGT'];
			var _loaikedon = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));

			//L2PT-15697 L2PT-45780 nang cap dkhnm
			if(_opt == '02D017' && NGT_LOAI_KT_DONGY == '1'){
				_loaikedon = '1';
			}
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
				maubenhphamid : "",
				opt : _opt,
				phongId : $('#hidPHONGID').val(),
				loaikedon : _loaikedon,
				dichvuchaid : '',
				sub_dtbnid : $("#hidSUB_DTBNID").val(),
				khamchinhphu : $("#hidKHAMCHINHPHU").val(), 				// start manhnv L2HOTRO-11801
				loadkhotheo : _loadkhotheo // 0: kho và tủ trực, 1: kho, 2: tủ trực.
			};

			dlgPopup=DlgUtil.buildPopupUrl("dlgCDT","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",myVar,_title,1300,600);
			DlgUtil.open("dlgCDT");

			EventUtil.setEvent("dlgCDT_onClose",function(){
				if (cfObj.NGT_PHIMTAT_KB2 == 1) {
					$("#cboKETQUADIEUTRIID").focus();
				}
			});
		}
		//L2PT-35459
		function _openDialogThuocVP(_opt,_loaikedon, _title, _loadkhotheo){
			var par = ['HIS_KEDONTHUOC_CHITIET_NGT'];
			var _loaikedon = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
			_start_kham();
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),//L2PT-20192
				maubenhphamid : "",
				opt : _opt,
				phongId : $('#hidPHONGID').val(),
				loaikedon : _loaikedon,
				dichvuchaid : '',
				macdinh_hao_phi : 4	,
				sub_dtbnid : $("#hidSUB_DTBNID").val(),
				khamchinhphu : $("#hidKHAMCHINHPHU").val(), 				// start manhnv L2HOTRO-11801
				loadkhotheo : _loadkhotheo // 0: kho và tủ trực, 1: kho, 2: tủ trực.
			};

			dlgPopup=DlgUtil.buildPopupUrl("dlgCDT","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",myVar,_title,1300,600);
			DlgUtil.open("dlgCDT");

			EventUtil.setEvent("dlgCDT_onClose",function(){
				if (cfObj.NGT_PHIMTAT_KB2 == 1) {
					$("#cboKETQUADIEUTRIID").focus();
				}
			});
		}

		// mo popup don thuoc khong thuoc
		function _openDialogThuocK(_opt,_loaikedon, _title){
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				maubenhphamid : "",
				action : "Add"

			};

			dlgPopup=DlgUtil.buildPopupUrl("dlgCDT","divDlg","manager.jsp?func=../ngoaitru/NGT02K044_CapThuocK",myVar,_title,800,520);
			DlgUtil.open("dlgCDT");
		}

		function _ketthuckham(vkieu){
			//=========== check dich vu thanh toan dong thoi; chi check khi CLICK KET THUC KHAM;
			if(vkieu == 2){
				var par11 = ['HIS_CANHBAO_KHONG_TTDT'];
				var checkTt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par11.join('$'));

				if(checkTt == '1'){
					var msgCheckTt = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV018",$("#hidTIEPNHANID").val());
					if(msgCheckTt && msgCheckTt != ''){
						DlgUtil.showMsg('Các dịch vụ ' + msgCheckTt + ' miễn giảm thanh toán đồng thời');
						return;
					}
				}
			}
			//=========== ===============

			var myVar={
				kieu : vkieu,
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val()
			};
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K001.KTKHAM", JSON.stringify(myVar));
			var rets = ret.split(',');
			if(rets[0] == '1'){
				//L2PT-113659 tinh lai tien dvkt thanh toan bhyt cho tuyen 4
				if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_TINHTIEN_TUYEN4') == '1' && $("#hidDOITUONGBENHNHANID").val() == '1') {
					var _par_vpi = [ $("#hidKHAMBENHID").val(), $("#hidTIEPNHANID").val(), _opt.khoaid ];
					var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("TINHTIEN.HANGBV", _par_vpi.join('$'));
				}
				//L2PT-113659 end
				//tuyennx_edit_start_20171011 yc HISL2NT-393
				if(typeof rets[1] != 'undefined' && rets[1] != '' && rets[1] != '0' && rets[1] != '100' ){
					//tuyennx_edit_end_20171017 yc HISL2NT-393
					DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?",function(flag) {
						if (flag) {
							_mobenhan_daingay();
							_setButton(true);
							$("#toolbarIdbtndrug").attr("disabled", true);
							DlgUtil.showMsg('Cập nhật thông tin thành công');
							_loadGridData(_opt.phongid);
						}
					});
				}else{
					_setButton(true);
					$("#toolbarIdbtndrug").attr("disabled", true);
					DlgUtil.showMsg('Cập nhật thông tin thành công');
					dayCongHSSK($("#hidKHAMBENHID").val());
					_loadGridData(_opt.phongid);
				}
				// SONDN L2PT-5934 START : DA XU LY TRONG SQL;
				var _tudongduyetketoan = cfObj.NGT_DUYETKETOAN_KTKHAM;
				var VP_DUYET_BH_KHI_DUYET_KETOAN = cfObj.VP_DUYET_BH_KHI_DUYET_KETOAN ;
				if (_tudongduyetketoan == "1" && $("#hidDOITUONGBENHNHANID").val() != "1"){
					var objData_DUYET = new Object();
					objData_DUYET["TIEPNHANID"] = $("#hidTIEPNHANID").val();
					objData_DUYET["NGAY"] = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
					objData_DUYET["NGAYRAVIEN"] = ($("#txtTHOIGIANRAVIEN").val() == '' ? jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS') : $("#txtTHOIGIANRAVIEN").val());
					objData_DUYET["DATRONVIEN"] = 0;
					objData_DUYET["SOLUONGQUYETTOAN"] = 0;
					objData_DUYET["LOAIDUYETBHYT"] = 0;
					objData_DUYET["KHOAID"] = _opt.khoaid;
					objData_DUYET["PHONGID"] = _opt.phongid;
					objData_DUYET["DUYET"] = 1;
					if ($("#hidDOITUONGBENHNHANID").val() == 1 &&
						(VP_DUYET_BH_KHI_DUYET_KETOAN == 0 || (VP_DUYET_BH_KHI_DUYET_KETOAN == 2
							&& $("#hidLOAITIEPNHANID").val() != 0) || (VP_DUYET_BH_KHI_DUYET_KETOAN == 3
							&& $("#hidLOAITIEPNHANID").val() == 0))) {
						objData_DUYET.FLAG_DUYET_BH = 0;
						var obj_BH = new Object();
						obj_BH.TIEPNHANID = $("#hidTIEPNHANID").val();
						obj_BH.NGAYDUYET = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
						var month = obj_BH.NGAYDUYET.split("/")[1];
						if (month <= 3)
							obj_BH.QUYDUYET = "1";
						else if (month <= 6)
							obj_BH.QUYDUYET = "2";
						else if (month <= 9)
							obj_BH.QUYDUYET = "3";
						else
							obj_BH.QUYDUYET = "4";
						obj_BH.HOSPITAL_CODE = _opt.hospital_code;
						objData_DUYET.DATA_BH = obj_BH;
					} else {
						objData_DUYET.FLAG_DUYET_BH = 1;
					}
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI01T001.08",JSON.stringify(objData_DUYET));
					if (fl.substr(0, 2) == -1) {
						DlgUtil.showMsg(fl.slice(2));
					} else if (fl == -89) {
						DlgUtil.showMsg("Có nhiều hơn 1 phiếu duyệt kế toán");
					} else if (fl == -90) {
						DlgUtil.showMsg("Chưa thiết lập khoa phòng");
					} else if (fl == -91) {
						DlgUtil.showMsg("Lỗi lưu dữ liệu dịch vụ");
					} else if (fl == -92) {
						DlgUtil.showMsg("Ngày ra viện không được lớn hơn thời gian hiện tại");
					} else if (fl == -930) {
						DlgUtil.showMsg("Bạn không có quyền duyệt kế toán, liên hệ với người quản trị");
					} else if (fl == -93) {
						DlgUtil.showMsg("Bạn không có quyền gỡ duyệt kế toán, liên hệ với người quản trị");
					} else if (fl == -931) {
						DlgUtil.showMsg("Hết thời gian được phép gỡ duyệt hồ sơ này, liên hệ với người quản trị");
					} else if (fl == -94) {
						DlgUtil.showMsg("Bệnh nhân chưa gỡ duyệt bảo hiểm");
					} else if (fl == -95) {
						DlgUtil.showMsg("Hồ sơ đã khóa");
					} else if (fl == -96) {
						DlgUtil.showMsg("Còn khoa/phòng chưa kết thúc");
					} else if (fl == -97) {
						DlgUtil.showMsg("Còn phòng khám chưa kết thúc");
					} else if (fl == -98) {
						DlgUtil.showMsg("Bệnh nhân cần thanh toán viện phí");
					} else if (fl == -981) {
						DlgUtil.showMsg("Lỗi khi kiểm tra dịch vụ");
					} else if (fl == -982) {
						DlgUtil.showMsg("Bệnh nhân còn dịch vụ chưa thu tiền");
					} else if (fl == -99) {
						DlgUtil.showMsg("Bệnh nhân chưa đóng bệnh án");
					} else if (fl == -88) {
						DlgUtil.showMsg("Bệnh nhân đã được duyệt kế toán trước đó");
					} else if (fl == -87) {
						DlgUtil.showMsg("Bệnh nhân chưa được duyệt kế toán hoặc đã gỡ duyệt");
					} else if (fl == -86) {
						DlgUtil.showMsg("Công khám đầu tiên có tỷ lệ khác 100%");
					} else if (fl == -85) {
						DlgUtil.showMsg("Hãy gỡ duyệt bảo hiểm trước");
					} else if (fl == -84) {
						DlgUtil.showMsg("Hồ sơ đã khóa, không thể gỡ duyệt kế toán");
					}
				}
				// SONDN L2PT-5934 END

				//dannd tu dong day cong giam dinh bhyt L2PT-21529
				if (cfObj.VPI_AUTO_DUYET_BHYT_NGT == '1'
					&& $("#hidDOITUONGBENHNHANID").val() == "1" ) {
					var dtar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT10",$("#hidKHAMBENHID").val() + '$'+_opt.phongid+ '$'+$("#hidHOSOBENHANID").val());
					if (dtar != null && dtar.length > 0) {
						if (dtar[0].TRANGTHAITIEPNHAN == "1"){
							var _duyet_bh = duyetBHYT($("#hidTIEPNHANID").val(), opt.user_id, opt.hospital_code);
							if (_duyet_bh != 0) {
								DlgUtil.showMsg("Có lỗi xảy ra trong quá trình đẩy dữ liệu lên cổng giám định!");
							}
						}
					}else{
						DlgUtil.showMsg("Không lấy được thông tin bệnh nhân sau xử trí. 1526");
					}
				}
				//end dannd
				if ($('#hidXUTRIKHAMBENHID').val()  != '2' && $('#hidXUTRIKHAMBENHID').val()  != '6'
					&& $('#hidXUTRIKHAMBENHID').val()  != '12' && vkieu == 2  && cfObj.NGT_LUUQUANLYBENHAN == '1') {
					luuQuanLyBenhAn('1');
				}
			}else if(ret == 'kocoxutri'){
				DlgUtil.showMsg('Bệnh nhân hiện chưa có xử trí.');
			}else if(ret == 'coxutri'){
				DlgUtil.showMsg('Bệnh nhân có xử trí không trả bệnh nhân.');
			}else if(ret == 'khamchuyenkhoa'){
				DlgUtil.showMsg('Bệnh nhân này khám chuyên khoa phòng khám, yêu cầu vào Nội trú / Điều trị ngoại trú để thao tác bệnh nhân. ');
			}else if(ret == 'codvcls'){
				DlgUtil.showMsg('Bệnh nhân đang có chỉ định dịch vụ CLS hoặc đã kê đơn thuốc');
			} else if(ret == 'connotien'){
				DlgUtil.showMsg('Bệnh nhân còn nợ tiền, phải thanh toán mới kết thúc khám.');
			}
			//tuyennx_add_start_20180927   L2PT-22513
			else if(ret == "cophieuthuocdangsua"){
				DlgUtil.showMsg('Có phiếu thuốc đang sửa');
			}
			else if(ret == "cophieuvattudangsua"){
				DlgUtil.showMsg('Có phiếu vật tư đang sửa');
			}
				//tuyennx_add_end_20171205
			//L2PT-18998
			else if(ret == 'saibenhphu'){
				DlgUtil.showMsg('Định dạng mã bệnh kèm theo không đúng.');
			}
			//end L2PT-18998
			else if(ret == 'cophieudangsua'){
				DlgUtil.showMsg('Bệnh nhân có phiếu CLS/Đơn thuốc đang sửa, không kết thúc khám được.');
			}else if(ret == 'chuacochandoan'){
				DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán.');
			}else if(ret == 'kococannang'){
				DlgUtil.showMsg('Bệnh nhân chưa có thông tin cân nặng');
			}else if(ret == 'dathanhtoan'){
				DlgUtil.showMsg('Bệnh nhân có công khám đã thanh toán, không thể trả BN. ');
			}
			//tuyennx_add_start_20171205
			else if(ret == 'thoigianxtri'){
				DlgUtil.showMsg('Bệnh nhân chưa có thời gian ra viện hoạc thời gian ra viện nhỏ hơn ngày hiện tại, cập nhật lại ngày ra viện trước.');
			}
			else if(ret == 'coloibhyt'){
				dlgPopup=DlgUtil.buildPopupUrl("divErrInfo","divDlg","manager.jsp?func=../ngoaitru/NGT02K067_ErrInfo" ,[],"Danh sách chi tiết lỗi",1100,500);
				DlgUtil.open("divErrInfo");
			}
			//tuyennx_add_end_20171205
			else{
				DlgUtil.showMsg('Cập nhật thông tin không thành công');
			}
		}

		function _xutribenhnhan(vkieu){
			// sondn L2PT-3840
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				phongid : _opt.phongid,
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val()
			};
			var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KTKHAM2", JSON.stringify(myVar));
			if(check == 'codvcls'){
				DlgUtil.showMsg('Bệnh nhân đang có chỉ định dịch vụ trạng thái đang chờ tiếp nhận, có thể hủy phiếu để kết thúc khám');
				return false;
			}
				// end sondn L2PT-3840

			//tuyennx_add_start_20170727  y/c HISL2BVDKHN-247
			else if(check == 'ngaydichvu'){
				DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện không thể kết thúc khám');
				return false;
			}
			//tuyennx_add_end_20170727
			else if(check == 'pasdvcls'){
				DlgUtil.showConfirm("Bệnh nhân có dịch vụ đang thực hiện, bạn có muốn kết thúc khám không.",function(flag) {
					if (flag) {
						_ketthuckham(vkieu);
						//ductx -bvtm-5439
						if(opt.hospital_id == '10284'){
							sendSmsAuto();
						}
						//end bvtm-5439
					}
				});
			}else if(check == '1'){
				_ketthuckham(vkieu);
				//ductx -bvtm-5439
				if(opt.hospital_id == '10284'){
					sendSmsAuto();
				}
				//end bvtm-5439
			}
		}

		//calback cho man hinh chuyen mo thanh cong
		EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function(e) {
			if($("#hidTATTHONGBAOKBHB").val() == "0" && e.msg != ""){	DlgUtil.showMsg(e.msg);	}

			//widget khoi tao grid danh sach xet nghiem
			$('#tabXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_XetNghiem,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});

			//widget khoi tao grid danh sach CDHA
			$('#tabCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_CDHA,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});

			//widget khoi tao grid danh sach chuyen khoa
			$('#tabChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua  : 'grdCKketQua',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_ChuyenKhoa,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
			if (!e.isLuuTiep) {
				DlgUtil.close("divDlgDichVu");
			}
			DlgUtil.showMsg(e.msg);
			_loadGridData(_opt.phongid);
		});

		// calback cho man hinh tao phieu thuoc, vat tu
		EventUtil.setEvent("assignSevice_saveTaoPhieuThuoc", function(e) {
			if($("#hidTATTHONGBAOKBHB").val() == "0" && e.msg != ""){	DlgUtil.showMsg(e.msg);	}

			// reload lai widget cho tung man hinh
			if (e.option == "02D010" || e.option == "02D014") {
				// widget cho tab thong tin thuoc
				$('#tabThuoc').ntu02d033_pt({
					_grdPhieuthuoc : 'grdThuoc',
					_gridPhieuthuocDetail : 'grdChiTietThuoc',
					_khambenhid : $("#hidKHAMBENHID").val(),
					_benhnhanid : $("#hidBENHNHANID").val(),
					_lnmbp : LNMBP_Phieuthuoc,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if (e.option == "02D015" || e.option == "02D016") {
				// widget cho tab thong tin vat tu
				$('#tabVatTu').ntu02d034_pvt({
					_grdVatTu : 'grdVatTu',
					_gridVatTuDetail : 'grdChiTietVatTu',
					_khambenhid : $("#hidKHAMBENHID").val(),
					_benhnhanid : $("#hidBENHNHANID").val(),
					_lnmbp : LNMBP_Phieuvattu,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			}
			//DlgUtil.close("dlgCDT");
			//DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
			_loadGridData(_opt.phongid);
		});

		EventUtil.setEvent("assignSevice_closechuyenphong", function(e) {
			_loadGridData(_opt.phongid);
		});

		EventUtil.setEvent("assignDrug_cancel", function(e) {
			//tuyennx_edit_start_20171017 yc HISL2NT-393
			if(typeof e.badaingay != 'undefined' && e.badaingay != '' && e.badaingay != '0' && e.badaingay != '100'){
				//tuyennx_edit_start_20171017 yc HISL2NT-393
				DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?",function(flag) {
					if (flag) {
						_mobenhan_daingay();

						var HIS_KOKTKHAM_KHICODONTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_I('his_common.com_lay_cauhinh_nd','HIS_KOKTKHAM_KHICODONTHUOC');
						var HIS_KETTHUCKHAM_KHICODONTHUOC = cfObj.HIS_KETTHUCKHAM_KHICODONTHUOC;
						if(e.type == '1' && HIS_KOKTKHAM_KHICODONTHUOC == '0' && HIS_KETTHUCKHAM_KHICODONTHUOC == '1'){
							_loadTabHanhChinh(-1);
							_loadGridData(_opt.phongid);
							_setButton(true);

							$('#tabXetNghiemTab').text("Xét nghiệm");
							$('#tabCDHATab').text("CĐHA");
							$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
							$('#tabThuocTab').text("Thuốc");
							$('#tabVatTuTab').text("Vật tư");
							$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
							$('#lblSTT').html('');
						}
					}
				});
			}

			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
		});

		//callback don thuoc khong thuoc
		EventUtil.setEvent("assignDrugK_cancel", function(e) {
			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
			DlgUtil.close("divDlgTaoPhieuThuoc");
		});

		//callback cho ho so benh an
		EventUtil.setEvent("assignSevice_saveHSBADetail", function(e) {
			DlgUtil.showMsg(e.msg);
			DlgUtil.close("divDlgBenhAnDetail");
		});

		EventUtil.setEvent("assignDrug_loisaibn", function(e) {
			DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');

			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
		});

		EventUtil.setEvent("assignDrug_khacbn", function(e) {
			DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');

			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
		});

		EventUtil.setEvent("assignDrug_xutri", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
			_phieukham();
		});

		EventUtil.setEvent("assignSevice_savePTTT", function(e) {
			DlgUtil.showMsg(e.msg);
			$('#tabChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua  : 'grdCKketQua',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid: $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_ChuyenKhoa,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
			DlgUtil.close("dlgPTTT");
		});

		EventUtil.setEvent("assignSevice_closephieukham", function(e) {
			if(e.msg == "6" || e.msg == "8" || e.msg == "0"){
				$("#toolbarIdbtndrug").attr("disabled", true);
			}else{
				$("#toolbarIdbtndrug").attr("disabled", false);
			}

			//tuyennx_edit_start_20171017 yc HISL2NT-393
			if(typeof e.badaingay != 'undefined' && e.badaingay != '' && e.badaingay != '0' && e.badaingay != '100'){
				//tuyennx_edit_start_20171017 yc HISL2NT-393
				DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?",function(flag) {
					if (flag) {
						_mobenhan_daingay();

						if(e.type == '1'){
							_loadTabHanhChinh(-1);
							_loadGridData(_opt.phongid);
							_setButton(true);

							$('#tabXetNghiemTab').text("Xét nghiệm");
							$('#tabCDHATab').text("CĐHA");
							$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
							$('#tabThuocTab').text("Thuốc");
							$('#tabVatTuTab').text("Vật tư");
							$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
							$('#lblSTT').html('');
						}
					}else{
						if(e.type == '1'){
							_loadTabHanhChinh(-1);
							_loadGridData(_opt.phongid);
							_setButton(true);

							$('#tabXetNghiemTab').text("Xét nghiệm");
							$('#tabCDHATab').text("CĐHA");
							$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
							$('#tabThuocTab').text("Thuốc");
							$('#tabVatTuTab').text("Vật tư");
							$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
							$('#lblSTT').html('');
						}
					}
				});
			}else{
				if(e.type == '1'){
					_loadTabHanhChinh(-1);
					_loadGridData(_opt.phongid);
					_setButton(true);

					$('#tabXetNghiemTab').text("Xét nghiệm");
					$('#tabCDHATab').text("CĐHA");
					$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
					$('#tabThuocTab').text("Thuốc");
					$('#tabVatTuTab').text("Vật tư");
					$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
					$('#lblSTT').html('');
				}
			}
			DlgUtil.close("dlgPhieuKB");
		});

		EventUtil.setEvent("assignSevice_khacbnxutri", function(e) {
			DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');
			DlgUtil.close("dlgPhieuKB");
		});

		EventUtil.setEvent("assignSevice_capthuoc", function(e) {
			DlgUtil.close("dlgPhieuKB");
			_openDialogThuoc('02D010', 0, "Chỉ định thuốc");
		});

		EventUtil.setEvent("exam_cancel", function(e) {
			$('#tabBenhAn').ntu02d022_ttba({
				_khambenhid: $("#hidKHAMBENHID").val()
			});
			_loadFirstICD = true;
			_loadFirstICDYHCT = true; 	
			//_loadTabHanhChinh($("#hidKHAMBENHID").val());
			//_loadGridData(_opt.phongid); tạm bỏ

			DlgUtil.close("dlgKham");
		});

		EventUtil.setEvent("close_chuyenphongkham", function(e) {
			//tuyennx_edit_start_20171017 yc HISL2NT-393
			if(typeof e.badaingay != 'undefined' && e.badaingay != '' && e.badaingay != '0' && e.badaingay != '100'){
				DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?",function(flag) {
					if (flag) {
						_mobenhan_daingay();
						if(e.type == '1'){
							_loadTabHanhChinh(-1);
							_loadGridData(_opt.phongid);
							_setButton(true);
						}
					}else{
						if(e.type == '1'){
							_loadTabHanhChinh(-1);
							_loadGridData(_opt.phongid);
							_setButton(true);
						}
					}
				});
			}else{
				if(e.type == '1'){
					_loadTabHanhChinh(-1);
					_loadGridData(_opt.phongid);
					_setButton(true);
				}
			}
			//tuyennx_edit_start_20171017 yc HISL2NT-393
			DlgUtil.close("dlgPhieuKham");
		});

		EventUtil.setEvent("chidinhthukhac", function(e) {
			if(check_doituongbn() == '-1'){
				DlgUtil.showMsg('Hãy chọn lại bệnh nhân muốn thao tác do BN đã thay đổi đối tượng');
				return;
			}
			paramInput={
				chidinhdichvu : '1',
				loaidichvu : '1',
				loaiphieumbp: '17',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : e.phongchidinh
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV"+ "&loaidichvu=" + 1,paramInput,"Phiếu thu khác",1300,600);
			DlgUtil.open("divDlgDichVu");

			DlgUtil.close("dlgPhieuKham");
		});

		EventUtil.setEvent("exam_kbhb", function(e) {
			$("#toolbarIdbtnExam").click();
		});

		EventUtil.setEvent("exam_khacbnkbhb", function(e) {
			DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');
			DlgUtil.close("dlgKham");
		});

		EventUtil.setEvent("exam_cddv", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			DlgUtil.close("dlgKham");

			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid
			};
			if(check_doituongbn() == '-1'){
				DlgUtil.showMsg('Hãy chọn lại bệnh nhân muốn thao tác do BN đã thay đổi đối tượng');
				return;
			}

			var cddvDLKHA = cfObj.CDDV_GIAODIEN_KHA == '1' ? true : false;
			if (cddvDLKHA) {
				dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV_KHA"+ "&loaidichvu=" + 5,myVar,"Tạo phiếu chỉ định dịch vụ",1300,660);
				DlgUtil.open("divDlgDichVu");
			}
			else{
				dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV"+ "&loaidichvu=" + 5,myVar,"Tạo phiếu chỉ định dịch vụ",1300,660);
				DlgUtil.open("divDlgDichVu");
			}
		});

		EventUtil.setEvent("exam_capthuoc", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			DlgUtil.close("dlgKham");
			DlgUtil.close("dlgPhieuKB");
			_openDialogThuoc('02D010', 0, "Chỉ định thuốc");
		});

		EventUtil.setEvent("exam_capthuocmuangoai", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			DlgUtil.close("dlgKham");
			DlgUtil.close("dlgPhieuKB");
			_openDialogThuoc('02D011', 1, "Chỉ định thuốc mua ngoài");
		});

		EventUtil.setEvent("exam_xutri", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			DlgUtil.close("dlgKham");

			_phieukham();
		});

		EventUtil.setEvent("exam_save", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			//widget thong tin benh an
			$('#tabBenhAn').ntu02d022_ttba({
				_khambenhid: $("#hidKHAMBENHID").val()
			});
			_loadGridData(_opt.phongid);
			//DlgUtil.close("dlgKham");
		});

		EventUtil.setEvent("assignSevice_savethemchuyenphong", function(e) {
			// load du lieu cho tab Hanh Chinh
			_loadTabHanhChinh($('#hidKHAMBENHID').val());
			DlgUtil.close("dlgPhong");
		});

		//calback cho MAN HINH TAO BAN SAO DON THUOC
		EventUtil.setEvent("assignSevice_SaveCopyMbp", function(e) {
			DlgUtil.showMsg(e.msg);
			if(e.type == '7'){
				//widget cho tab thong tin thuoc
				$('#tabThuoc').ntu02d033_pt({
					_grdPhieuthuoc : 'grdThuoc',
					_gridPhieuthuocDetail : 'grdChiTietThuoc',
					_khambenhid : $("#hidKHAMBENHID").val(),
					_benhnhanid : $("#hidBENHNHANID").val(),
					_lnmbp : LNMBP_Phieuthuoc,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if(e.type == '8'){
				//widget cho tab thong tin thuoc
				$('#tabVatTu').ntu02d034_pvt({
					_grdVatTu : 'grdVatTu',
					_gridVatTuDetail : 'grdChiTietVatTu',
					_khambenhid : $("#hidKHAMBENHID").val(),
					_benhnhanid : $("#hidBENHNHANID").val(),
					_lnmbp : LNMBP_Phieuvattu,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if(e.type == '1'){
				$('#tabXetNghiem').ntu02d024_ttxn({
					_gridXnId : "grdXetNghiem",
					_gridXnDetailId : "grdXetNghiemChiTiet",
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid:  $("#hidBENHNHANID").val(),
					_lnmbp:	LNMBP_XetNghiem,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if(e.type == '2'){
				$('#tabCDHA').ntu02d025_cdha({
					_gridCDHA : "grdCDHA",
					_gridCDHADetail : "grdCDHAChiTiet",
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid:  $("#hidBENHNHANID").val(),
					_lnmbp:	LNMBP_CDHA,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if(e.type == '5'){
				$('#tabChuyenKhoa').ntu02d026_ck({
					_gridCK : 'grdCK',
					_gridCKDetail : 'grdCKChitiet',
					_grdCKketQua  : 'grdCKketQua',
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid:  $("#hidBENHNHANID").val(),
					_lnmbp:	LNMBP_ChuyenKhoa,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			}
			_loadGridData(_opt.phongid);
			DlgUtil.close("divDlgCopyMbp");
		});

		//tuyennx_add_start ke giuong
		//tao phieu ngay giuong
		$("#toolbarIdbtnNewBed").on("click", function() {
			var kegiuongDs = cfObj.HIS_CONFIG_KEGIUONG_DANHSACH;
			if(kegiuongDs!=1){
				var selRowId =  $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
				var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);
				if(rowData.TRANGTHAIKHAMBENH != 4){
					DlgUtil.showMsg('Bệnh nhân không ở trạng thái đang khám');
					return;
				}
				if(selRowId != null && selRowId > 0){
					paramInput={
						chidinhdichvu : '1',
						loaidichvu : '13',
						loaiphieumbp: '12',
						benhnhanid : $("#hidBENHNHANID").val(),
						khambenhid : $("#hidKHAMBENHID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val(),
						tiepnhanid : $("#hidTIEPNHANID").val(),
						doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
						hinhthucvaovienid : '2',
						loaibenhanid : $("#hidLOAIBENHANID").val(),
						loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
						subDeptId : $('#hidPHONGID').val()
					};

					if(check_doituongbn() == '-1'){
						DlgUtil.showMsg('Hãy chọn lại bệnh nhân muốn thao tác do BN đã thay đổi đối tượng');
						return;
					}

					dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 13,paramInput,"Phiếu ngày giường",1300,600);
					DlgUtil.open("divDlgDichVu");
				} else {
					DlgUtil.showMsg('Chưa chọn bệnh nhân');
				}
			} else {
				var selRowId =  $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
				var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);
				if(rowData.TRANGTHAIKHAMBENH != 4){
					DlgUtil.showMsg('Bệnh nhân không ở trạng thái đang khám');
					return;
				}
				paramInput={
					khambenhid : $("#hidKHAMBENHID").val(),
					tiepnhanid : $("#hidTIEPNHANID").val()
				};

				dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVuGiuong","divDlg","manager.jsp?func=../noitru/NTU01H034_BenhNhanGiuong",paramInput,"Ngày giường",1300,600);
				DlgUtil.open("divDlgDichVuGiuong");
			}

		});
		//dannd_L2PT-503
		$("#toolbarIdgroup_0_giayKCBYC").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in Giấy khám/chữa bệnh theo yêu cầu.');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];

			_openReport(par, "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'pdf');
		});
		//dannd_end
		$("#toolbarIdhandling_10").on("click", function() {
			var selRowId =  $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);
			if(rowData.TRANGTHAIKHAMBENH != 4){
				DlgUtil.showMsg('Bệnh nhân đang ở trạng thái chờ nhập khoa hoặc kết thúc, không thể xếp giường');
				return;
			}

			paramInput={
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				phongid : $("#hidPHONGID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgCBNVaoBuongPhong","divDlg","manager.jsp?func=../noitru/NTU01H032_XepGiuong",paramInput,"Xếp giường cho bệnh nhân",1300,600);

			DlgUtil.open("divDlgCBNVaoBuongPhong");
		});

		// sondn L2PT-26844
		$("#toolbarIdbtnBIENBANHOICHAN").on("click", function() {
			if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1"){
				DlgUtil.showMsg("Chọn bệnh nhân để thao tác");
				return;
			}
			paramInput={
				khambenh_id : $("#hidKHAMBENHID").val(),
				maubenhpham_id : "",
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				phong_id : $('#hidPHONGKHAMDANGKYID').val()

			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgHoiChan","divDlg","manager.jsp?func=../noitru/NTU02D008_BienBanHoiChuan",paramInput,"Tạo biên bản hội chẩn",1000,550);
			DlgUtil.open("divDlgHoiChan");
		});

		// BVTM-7113
		$("#toolbarIdbtnBBHC").on("click", function() {
			if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1"){
				DlgUtil.showMsg("Chọn bệnh nhân để thao tác");
				return;
			}

			paramInput={
				khambenh_id : $("#hidKHAMBENHID").val(),
				maubenhpham_id : ""

			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgHoiChan","divDlg","manager.jsp?func=../noitru/NTU02D008_BienBanHoiChuan",paramInput,"Tạo biên bản hội chẩn",1000,550);
			DlgUtil.open("divDlgHoiChan");
		});

		$("#toolbarIdbtnXNBT_HCG").on("click", function() {
			if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1"){
				DlgUtil.showMsg("Chọn bệnh nhân để thao tác");
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];
			openReport('window', "RPT_GIAYCAMKETXN_BETAHCG", "pdf", par);
		});

		$("#toolbarIdbtnLichHen").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if(opt.hospital_id == sms_info){
					var pos = rowData.MABENHNHAN.indexOf("-");
					var maBN = rowData.MABENHNHAN.substring(0, pos - 1);
				}
				paramInput = {
					khambenhid : rowData.KHAMBENHID,
					tiepnhanid : rowData.TIEPNHANID,
					hosobenhanid : rowData.HOSOBENHANID,
					benhnhanid : rowData.BENHNHANID,
					mabenhan : rowData.MAHOSOBENHAN,
					mabenhnhan : maBN,
					tenbenhnhan : rowData.TENBENHNHAN,
					namsinh : $('#hidNAMSINH').val(),
					nghenghiep : $("#hidTENNGHENGHIEP").val(),
					diachi : $('#txtDIACHI').val(),
					capnhat : '1',
					ngaytiepnhan : $('#txtDENKHAMLUC').val()
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgHenKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K008_Thongtin_Lichkham", paramInput, "Thông tin lịch hẹn", 900, 300);
				DlgUtil.open("divDlgHenKham");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});

		$("#toolbarIdtreatdt_18_1").on("click",function() {
			paramInput = {khambenhid : $("#hidKHAMBENHID").val()};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgSLDD_BNT", "divDlg", "manager.jsp?func=../noitru/NTU02D086_SangLocDinhDuongBTN", paramInput,
				"Đánh giá tình trạng dinh dưỡng >=18 tuổi, không mang thai", 1000, 600);
			DlgUtil.open("divDlgSLDD_BNT");
		});

		$("#toolbarIdbtnLuuTruPhoiThai").on("click", function() {
			if ($("#hidHOSOBENHANID").val() == "" || $("#hidHOSOBENHANID").val() == "-1"){
				DlgUtil.showMsg("Chọn bệnh nhân để thao tác");
				return;
			}
			var selRowId = $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);
			var par = {
				khambenhid : rowData.KHAMBENHID,
				tiepnhanid : rowData.TIEPNHANID,
				hosobenhanid : rowData.HOSOBENHANID,
				benhnhanid : rowData.BENHNHANID,
				mabenhan : rowData.MAHOSOBENHAN,
				tenbenhnhan : rowData.TENBENHNHAN,

				ngayvaovien: rowData.NGAYVAOKHOA,
				tuoi: $("#txtTUOI").val(),
				gioi: $("#txtGIOITINH").val(),

				mabenhnhan: rowData.MABENHNHAN,
				doituongbenhnhanid: rowData.DOITUONGBENHNHANID,
				loaitiepnhanid: rowData.LOAITIEPNHANID,
				subDeptId: opt.phongid
			};

			var dlgPopup = DlgUtil.buildPopupUrl("divDlgGBT","divDlg","manager.jsp?func=../noitru/NTU02D160_LuuTruPhoiThai",par,"Lưu trữ phôi thai",1500,700);
			DlgUtil.open("divDlgGBT");
		});

		//tao phieu cong kham
		$("#toolbarIdbtnPhieuCongKham").on("click", function() {
			if(check_doituongbn() == '-1'){
				DlgUtil.showMsg('Hãy chọn lại bệnh nhân muốn thao tác do BN đã thay đổi đối tượng');
				return;
			}
			paramInput={
				chidinhdichvu : '1',
				loaidichvu : '2',
				loaiphieumbp: '3',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
				loaibenhanid : '23',
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid,
				bacsike : ''
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 2,paramInput,"Phiếu công khám",1300,600);
			DlgUtil.open("divDlgDichVu");
		});

		//tao gói khám
		$("#toolbarIdbtnThemGoiKham").on("click", function() {
			if ($("#hidBENHNHANID").val() == "" || $("#hidHOSOBENHANID").val() == ""){
				DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để thêm gói khám");
				return;
			}

			var selRowId = $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);

			//BVTM-5811
			// check nếu kham phong yeu cau và doi tuong vip ko cho gan goi kham
			var _obj = new Object();
			_obj.TIEPNHANID = rowData.TIEPNHANID;
			var _checkdk = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHCK_GOIKHAM", JSON.stringify(_obj));

			var par = ['NGT_TN2_CHECK_THEMGOIKHAM'];
			var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
			if(_checkdk != "0" && dt == '1'){
				DlgUtil.showMsg(_checkdk);
				return;
			}

			var myVar={
				mabenhnhan : rowData.MABENHNHAN,
				tenbenhnhan : rowData.TENBENHNHAN,
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val()

			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgGOIKHAMLVVPC","divDlg","manager.jsp?func=../noitru/NTU02D192_GoikhamBenhnhan",myVar,"Chỉ định gói KCB",$( document ).width()-50, $( window ).height()-50);
			DlgUtil.open("dlgGOIKHAMLVVPC");
		});

		$("#toolbarIdbtnTienSuDiUng").on("click", function() {
			var paramInput={
				hosobenhan_id : $("#hidHOSOBENHANID").val(),
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgPhieuTSDU","divDlg","manager.jsp?func=../noitru/NTU02D128_PhieuTienSuDiUng",paramInput,"Phiếu tiền sử dị ứng",$( document ).width()-50, $( window ).height()-50);
			DlgUtil.open("divDlgPhieuTSDU");
		});

		$("#toolbarIdbtnDiUngThuoc").on("click", function() {
			if ($("#hidHOSOBENHANID").val() == "" || $("#hidHOSOBENHANID").val() == "-1"){
				DlgUtil.showMsg("Chọn bệnh nhân để thao tác");
				return;
			}
			var selRowId = $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);
			var par = {
				benhnhanid : rowData.BENHNHANID,
				khambenhid : rowData.KHAMBENHID,
				hosobenhanid : rowData.HOSOBENHANID,
				tiepnhanid : rowData.TIEPNHANID,
				mabenhan : rowData.MAHOSOBENHAN,
				tenbenhnhan : rowData.TENBENHNHAN,
				mabenhnhan: rowData.MABENHNHAN
			};

			var dlgPopup = DlgUtil.buildPopupUrl("divDlgDiungThuoc","divDlg","manager.jsp?func=../noitru/NTU02D083_DiUngThuoc",par,"Dị ứng thuốc",1500, 775);
			DlgUtil.open("divDlgDiungThuoc");
		});

		// start jira L2PT-31697
		$("#toolbarIdbtnGIAYBAOTU").on("click", function() {
			if ($("#hidHOSOBENHANID").val() == "" || $("#hidHOSOBENHANID").val() == "-1"){
				DlgUtil.showMsg("Chọn bệnh nhân để thao tác");
				return;
			}
			var paramInput={
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};

			var dlgPopup = DlgUtil.buildPopupUrl("divDlgGBT","divDlg","manager.jsp?func=../noitru/NTU02D166_GiayBaoTu",paramInput,"Giấy báo tử",900, 375);
			DlgUtil.open("divDlgGBT");
		});
		// end jira L2PT-31697

		EventUtil.setEvent("assignSevice_SaveHoiChan", function(e) {
			DlgUtil.showMsg(e.msg);
			//widget cho tab tao hoi chan
			$('#tcHoiChuan').ntu02d032_phc({
				_grdHoiChan : 'grdPhieuHoiChuan',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	"15", //L2PT-88403
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
			DlgUtil.close("divDlgHoiChan");
		});
		// end sondn L2PT-26844

		if(_type == "2"){
			$("#toolbarIdhandling_10").removeClass("disabled");
			$("#toolbarIdbtnNewBed").removeClass("disabled");
			var check_par=[];
			check_par.push({"name":"[0]","value": _tinhngaycapcuu});
			$("#toolbarIdtxtFromDate").val(jsonrpc.AjaxJson.getOneValue("COM.GETSYSDATE", check_par));
			$("#toolbarIdbtnTIEPNHANCC").removeClass("disabled");
		}
		else{
			$("#toolbarIdhandling_10").addClass("disabled");
			$("#toolbarIdbtnNewBed").addClass("disabled");
			$("#toolbarIdbtnTIEPNHANCC").addClass("disabled");
		}
		// sau khi lay xong che do thi tu dong load ra du lieu;
		_loadGridData(_opt.phongid);

		//tuyennx_add_end

		//tuyennx_add_start chuyen vien cs HISL2TK-461
		$("#toolbarIdbtnChuyenTuyenCS").on("click", function() {

			var chandoan = "";
			chandoan = $('#hidMACDC').val()+'-'+ $('#txtCDC').val()+
				($('#txtCDP').val() == "" ?"":";")+$('#txtCDP').val();
//			var FOMAT_MA_BENHPHU = cfObj.FOMAT_MA_BENHPHU;
//			if(FOMAT_MA_BENHPHU == 1){
//				chandoan = $('#hidMACDC').val()+'-'+ $('#txtCDC').val()+
//				($('#txtCDP').val() == "" ?"":";")+$('#txtCDP').val();
//			}
//			else{
//				chandoan =  $('#txtCDC').val()+ '('+ $('#hidMACDC').val()+')'+
//				($('#txtCDP').val() == "" ?"":";")+$('#txtCDP').val();
//			}
			var param = {
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				nghenghiep : $("#hidTENNGHENGHIEP").val(),
				diachi : $('#txtDIACHI').val(),
				namsinh : $('#hidNAMSINH').val(),
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
				ngaytiepnhan : $('#txtDENKHAMLUC').val(),
				chandoan : chandoan,
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgChuyenvienCS","divDlg","manager.jsp?func=../ngoaitru/NGT02K071_ChuyenvienCS",param,"Chuyển viện về tuyến cơ sở",1200,440);
			DlgUtil.open("dlgChuyenvienCS");
		});
		// cap so benh
		$("#toolbarIdbtnCapSoBenh").on("click", function() {
			var param = {
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				ICD10CODE : $('#hidMACDC').val(),
				ICD10NAME : $('#txtCDC').val(),
				namsinh : $('#hidNAMSINH').val(),
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCapSo","divDlg","manager.jsp?func=../ngoaitru/NGT02K072_CapSo",param,"Cấp sổ bệnh mới",1200,440);
			DlgUtil.open("dlgCapSo");
		});
		//tuyennx_add_end
		//======== START SU KIEN CHO BV BACH MAI 2;
		$("#btnGOITIEP5").on("click", function(){
			_goisttbm2("1","1"); 					// call from cominf;
		});

		$("#btnGOILAI5").on("click", function(){
//			_goisttbm2("1","2");
			$('#toolbarIdbtnCall').click();
		});

		$("#btnLCDNHO5").on("click", function(){
			var param = "&ngaybd="+$("#toolbarIdtxtFromDate").val()+"&ngaykt="+$("#toolbarIdtxtToDate").val() + "&fname=" + encodeURIComponent(opt.fullname).replace(/[%]/g, '/');
			window.open('manager.jsp?func=../ngoaitru/NGT02K092_LCDBM32&showMode=dlg'+param,'','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		});

		$("#btnDSGOILAI5").on("click", function(){
//	        var myVar = {
//	        		phongid : _opts.phongid
//	        };
//			dlgPopup=DlgUtil.buildPopupUrl("dlgCV","divDlg","manager.jsp?func=../ngoaitru/NGT02K053_BM2_BNLOHEN"
//					,myVar,"Danh sách bệnh nhân gọi lại",750,400);
//			DlgUtil.open("dlgCV");

		});

		$("#btnONLINECHUYENCSYT").on('click', function(){
			var selRowId =  $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $("#"+_gridId).jqGrid('getRowData',selRowId);

			var nguoiGoi =  {
				"hoTen": opt.fullname,
				"maCSYT":opt.hospital_code,
				"tenDangNhap": opt.user_name
			};

			var nguoiNhan =  {
				"hoTen": "",
				"maCSYT": rowData.MA_CSYT_CHUYEN,
				"tenDangNhap": rowData.USER_CHUYEN
			};
			VideoCall.createCall(nguoiGoi, nguoiNhan);
		});

		$('#toolbarIdbtnThuTienKhac').on('click', function (e) {
			if(check_doituongbn() == '-1'){
				DlgUtil.showMsg('Hãy chọn lại bệnh nhân muốn thao tác do BN đã thay đổi đối tượng');
				return;
			}
			paramInput={
				chidinhdichvu : '1',
				loaidichvu : '1',
				loaiphieumbp: '17',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $("#cboPHONGKHAMID").val()
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV"+ "&loaidichvu=" + 1,paramInput,"Phiếu thu khác",1300,600);
			DlgUtil.open("divDlgDichVu");
		});

		EventUtil.setEvent("evt_kios_bnlohen", function(e) {
//			if(typeof(e) != 'undefined'){
//				DlgUtil.close("dlgCV");
////				DlgUtil.showMsg("Đã gọi lại bệnh nhân " + e.tenbenhnhan);
//				$("#txtID5").val(e.id);
//				_goisttbm2("2");
//			}
		});
		//========= END SU KIEN CHO BV BACH MAI 2
		$('#txtKHAMBENH_CANNANG').keypress(function(e) {
			if(e.keyCode == '44' || e.charCode == '44') {
				if(document.selection) {
					var range = document.selection.createRange();
					range.text = '.';
				}else if(this.selectionStart || this.selectionStart == '0') {
					var start = this.selectionStart;
					var end = this.selectionEnd;
					$(this).val($(this).val().substring(0, start) + '.' + $(this).val().substring(end, $(this).val().length));
					this.selectionStart = start + 1;
					this.selectionEnd = start +1;
				} else {
					$(this).val($(this).val() + '.');
				}
				return false;
			}
		});
		$('#txtKHAMBENH_CANNANG').on("cut copy paste",function(e) {
			e.preventDefault();
		});
		// L2PT-132026 start
		//in phieu cong khai dich vu kcb noi tru L2PT-4472
		$("#toolbarIdprint_27").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					khambenhid : _khambenhid,
					tiepnhanid : $("#hidTIEPNHANID").val()
				};
				var url = "manager.jsp?func=../noitru/NTU01H098_InPhieuCongKhaiDVKCB";
				var popup = DlgUtil.buildPopupUrl("divDlgPCKDV", "divDlg", url, paramInput, "In phiếu công khai dịch vụ KCB nội trú " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 268);
				popup.open("divDlgPCKDV");
			}
		});
		// L2PT-132026 end
	}

	function _mobenhan_daingay(){
		// var _par=[];
		// var _hosobenhanid=null;
		// _par=RSUtil.buildParam("",[ $("#hidHOSOBENHANID").val()]);
		// var dataDaingay=jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.CHECK_DAINGAY", _par);
		// _rowsDaingay= JSON.parse(dataDaingay);
		// var _loaibadaingay = -1;
		// if(_rowsDaingay!=null && _rowsDaingay.length>0){
		//    _loaibadaingay=_rowsDaingay[0].LOAIBENHANID;
		//    _hosobenhanid = _rowsDaingay[0].HOSOBENHANID;
		// }

		var object = {};
		FormUtil.setFormToObject('divContentHC', '', object);
		var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
		var dataDaingay = {};
		if (dataDaingays && dataDaingays.length > 0) {
			dataDaingay = dataDaingays[0];
		}
		if (!dataDaingay) {
			DlgUtil.showMsg("Bệnh nhân chưa có bệnh án dài ngày!");
			return;
		}
		var _hosobenhanid = dataDaingay.HOSOBENHANID;
		var _hosobenhanid_hientai = object.HOSOBENHANID;
		var _loaibadaingay = dataDaingay.LOAIBENHANID;

		if(_loaibadaingay==36){
			var _sql_par1 = [];
			_sql_par1=RSUtil.buildParam("",[36]);
			var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
			var _rows1 = JSON.parse(_data1);
			var _sreenName=_rows1[0].URL;
			var _tenloaibenhan=_rows1[0].TENLOAIBENHAN
			var _maloaibenhan=_rows1[0].MALOAIBENHAN;

			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : _hosobenhanid,
				benhnhanid :  $("#hidBENHNHANID").val(),
				loaibenhanid : 36,
				maloaibenhan : _maloaibenhan,
//						tuyennx_add_start_20171023 YC HISL2NT-485
				trang_thai : $("#hidTRANG_THAI").val(),
//						tuyennx_add_end_20171023 YC HISL2NT-485
				//L2PT-6928 ngocnva start
				phongKhamDKId: $('#hidPHONGKHAMDANGKYID').val(),
				khamBenhId: $('#hidKHAMBENHID').val(),
				hosobenhanid_hientai :  _hosobenhanid_hientai
				//L2PT-6928 ngocnva end
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../benhan/"+_sreenName,paramInput,"Cập nhật " +_tenloaibenhan,1300,610);
			DlgUtil.open("divDlgBenhAnDetail");//
		}else{
			DlgUtil.showMsg('Bệnh nhân chưa được mở bệnh án dài ngày để cập nhật!');
		}
	}

	function _loadGridData(phongid) {
		var from = $('#toolbarIdtxtFromDate').val().substr(6,4) + $('#toolbarIdtxtFromDate').val().substr(3,2) + $('#toolbarIdtxtFromDate').val().substr(0,2)
			+ $('#toolbarIdtxtFromDate').val().substr(11,2) + $('#toolbarIdtxtFromDate').val().toString().substr(14,2);

		var to = $('#toolbarIdtxtToDate').val().substr(6,4) + $('#toolbarIdtxtToDate').val().substr(3,2) + $('#toolbarIdtxtToDate').val().substr(0,2)
			+ $('#toolbarIdtxtToDate').val().substr(11,2) + $('#toolbarIdtxtToDate').val().toString().substr(14,2);
		if(from > to){
			setErrValidate('toolbarIdtxtFromDate');
			DlgUtil.showMsg('Sai điều kiện tìm kiếm, từ ngày không thể lớn hơn đến ngày');
			return false;
		}
		var thoigian = getDaysDiff($('#toolbarIdtxtFromDate').val(), $('#toolbarIdtxtToDate').val());
		if (cfObj.NGT_KHAMBENH_TK_TG != 0 && thoigian > parseInt(cfObj.NGT_KHAMBENH_TK_TG) ) {
			DlgUtil.showMsg("Khoảng thời gian tìm kiếm vượt quá giới hạn để đảm bảo hiệu năng tìm kiếm là " + cfObj.NGT_KHAMBENH_TK_TG +
				" ngày. Đề nghị chỉ tìm kiếm trong khoảng ngày kiến nghị!");
			return false;
		}

		var sql_par=[];
		sql_par.push({"name":"[0]","value":$("#toolbarIdtxtFromDate").val()});
		sql_par.push({"name":"[1]","value":$("#toolbarIdtxtToDate").val()});
		sql_par.push({"name":"[2]","value": phongid});
		sql_par.push({"name":"[3]","value": $("#cboTRANGTHAI").val()});
		sql_par.push({"name":"[4]","value": $("#txtMABENHNHANTK").val()});
		sql_par.push({"name":"[5]","value": _type == null || _type == 'null' ? "0" : _type});
		sql_par.push({"name":"[6]","value":  $("#cboXUTRIID").val() });
		GridUtil.loadGridBySqlPage(_gridId,"NGT02K001.DANHSACH",sql_par,function () {
			var ids = $("#" + _gridId).getDataIDs();
			if (ids.length != 0) {
				if (cfObj.NGT_PHIMTAT_KB2 == 1) {
					_leghtList = ids.length;
					if(loadlandau == 0){
						$("#" + _gridId).setSelection(1);
					}else {
						$("#" + _gridId).setSelection(parseInt(selectIndex) + 1);
					}
				}
			}
		});			// Cac don vi khac;

		// load 1 so thong tin co ban sau do;
		var _obj = new Object();
		_obj.KHOAID = _opt.khoaid;
		_obj.PHONGID = _opt.phongid;
		_obj.TUNGAY = $('#toolbarIdtxtFromDate').val();
		_obj.DENNGAY = $('#toolbarIdtxtToDate').val();
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T001.LAYTTKB", JSON.stringify(_obj));
		if(data_ar != null && data_ar.length > 0){
			// lay thong tin so phong;
			if(_chedogoikham == "2"){
				_sophongkham = data_ar[0].SOPHONG;
			}else if (!isNaN(data_ar[0].SOPHONG) ){
				_sophongkham = data_ar[0].SOPHONG;
			}else{
				_sophongkham = "0";
			}
			// lay thong tin so benh nhan da kham;
			$("#txtSLKHAMTRONGNGAY").val(data_ar[0].SLKHAMTRONGNGAY);
			if (opt.hospital_id == "947"){
				if (Number(data_ar[0].SLBHYT) == 65){
					DlgUtil.showMsg("Số lượt khám BHYT đã tới 65 lượt");
				}else if (Number(data_ar[0].SLBHYT) >= 65){
					DlgUtil.showMsg("Số lượt khám BHYT đã quá 65 lượt");
				}
			}
		}
	}

	function _goilaibnchuyenkham(){
		var myVar={
			phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
			tiepnhanid : $("#hidTIEPNHANID").val(),
			doituongbtid : $("#hidDOITUONGBENHNHANID").val(),
			chidinhphong :  0
		};
		var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("GOILAI.CHUYENKHAM", JSON.stringify(myVar));
		if(check == 'kochuyenphong'){
			DlgUtil.showMsg('Bệnh nhân chưa chuyển sang phòng khám khác từ phòng này');
		}else if(check == 'dakham'){
			DlgUtil.showMsg('Phòng chuyển tới đã hoặc đang khám, không gọi lại được');
		}else if(check == 'dathutien'){
			DlgUtil.showMsg('Phòng chuyển tới bệnh nhân đã đóng tiền, không hủy được');
		}else if(check == 'dathuphi'){
			DlgUtil.showMsg('Bệnh nhân đã đóng tiền không hủy được, muốn hủy phải hủy hóa đơn trước');
		}else if(check == 'condichvu'){
			DlgUtil.showMsg('Phòng chuyển tới đang tồn tại dịch vụ chưa hủy / xóa hết, yêu cầu kiểm tra lại');
//		}else if (check == 'cothukhacdatt'){
//			DlgUtil.showMsg('Tồn tại thu khác đã thanh toán, đề nghị hủy thu khác trước khi hủy chuyển khám. ');
		}else if(check == '1'){
			DlgUtil.showMsg('Hủy chuyển phòng khám thành công');
		}
	}
	function _huychuyenkhamtheophong(){
		var param = {
			mabenhnhan : $('#txtMABENHNHAN').val(),
			khambenhid : $('#hidKHAMBENHID').val(),
			benhnhanid : $('#hidBENHNHANID').val(),
			hosobenhanid : $('#hidHOSOBENHANID').val(),
			phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val()
		};
		_showDialog("NGT02K046_HuyChuyenKham", param, 'Danh sách phòng khám',1000,480);
	}
	//======= SU DUNG TRONG TRUONG HOP CHO PHEP
	function loadTabStatus(_trangthaikhambenh, obj){
		var _xutrikhambenh = obj._xutrikhambenh;

		if(_trangthaikhambenh=='9'){
			_flgModeView='1';
			if($("#hidTRANGTHAITIEPNHAN").val() != '0'){
				$("#toolbarIdgroup_0_4").removeClass("disabled");
				$("#toolbarIdgroup_0_5").removeClass("disabled");
			}else{
				$("#toolbarIdgroup_0_4").addClass("disabled");
				$("#toolbarIdgroup_0_5").addClass("disabled");
			}
			$("#goilaibnchuyenphong").addClass("disabled");
			$("#xoabnchonhapvien").removeClass("disabled");
			_setButton(true);
			$("#toolbarIdbtndrug").attr("disabled", true);
			$("#toolbarIdbtnStart").attr("disabled", true);

			if(_enable_mobenhan != '1'){
				$("#yeucaumolaibenhan").addClass('disabled');
				$("#molaibenhan").remove();
			}else{
				$("#yeucaumolaibenhan").removeClass('disabled');
			}

			// SONDN L2PT-19530 09/04/2020
			if ($("#hidHINHTHUCXUTRIID").val() == "7"){
				$("#toolbarIdbtnKHAC_5").removeClass("disabled");
			}
			// END SONDN L2PT-19530 09/04/2020
		}else{
			_flgModeView='0';
			$("#toolbarIdgroup_0_4").addClass("disabled");
			$("#toolbarIdgroup_0_5").addClass("disabled");
			$("#yeucaumolaibenhan").addClass('disabled');
			$("#xoabnchonhapvien").addClass("disabled");
			if(_trangthaikhambenh == "4"){
				$("#goilaibnchuyenphong").removeClass("disabled");
				_setButton(false);
				_disableMenuXuTri(_xutrikhambenh);

				$("#toolbarIdbtnStart").attr("disabled", true);
				$("#toolbarIdbtnKHAC_3").removeClass("disabled");
				$("#toolbarIdbtnKHAC_8").removeClass("disabled");
				$("#toolbarIdhandling_1").removeClass("disabled");
			}else if(_trangthaikhambenh == 1){
				$("#toolbarIdbtnStart").attr("disabled", false);

				$("#toolbarIdbtndrug").attr("disabled", false);
				$("#toolbarIdbtnExam").attr("disabled", false);
				$("#toolbarIdbtnService").attr("disabled", false);
				$("#toolbarIdbtnCall").attr("disabled", false);

				$("#goilaibnchuyenphong").addClass("disabled");
				//_setButton(true); //BM2 dai.dv
				$("#toolbarIdbtnKHAC").attr("disabled", false);
				$("#toolbarIdbtn_TNTT").attr("disabled", false);
				$("#toolbarIdbtnKHAC_3").addClass("disabled");
				$("#toolbarIdbtnKHAC_8").addClass("disabled");
				$("#toolbarIdhandling_1").addClass("disabled");
				$("#toolbarIdbtnLuu").attr("disabled", false);
				$("#xoabnchonhapvien").addClass("disabled");
				_disableMenuXuTri(_xutrikhambenh);
			}
		}

		//L2PT-41126
		if (cfObj.MENU_KHAC_THONGTIN_TUVONG == "1") {
			$('#toolbarIdbtnKHAC_0').removeClass("disabled");
		}


		// BN KHAM CHUYEN KHOA, CHUA KET THUC KHAM;
		if($("#hidCHUYENKHAMNGT").val() == "1" && _trangthaikhambenh != "9"){
			if(opt.hospital_id == "965"){
				$("#toolbarIdbtndrug").attr("disabled", true);
				$("#toolbarIdbtnPhieuKham").attr("disabled", true);
			}else{
				$("#toolbarIdbtndrug").attr("disabled", false);
				//L2PT-93594
				if(cf.NGT_KETHUOC_TUKHO_CK =='0'){
					$("#toolbarIddrug_khothuoc").addClass("disabled");
				}
				$("#toolbarIddrug_phieuđinhuong").addClass("disabled");
				$("#toolbarIddrug_1dy").addClass("disabled");
				//tuyennx_add_start_ fix bv longan
				if(cfObj.KETHUOC_CHAN_KHAMCK =='0'){
					$("#toolbarIddrug_3").addClass("disabled");
					$("#toolbarIddrug_tutruc").addClass("disabled");
				}
				//tuyennx_add_end_ fix bv longan
			}
		}
		/*
		else{
			if(opt.hospital_id == "10284"){
				$("#toolbarIdbtndrug").attr("disabled", false);
				$("#toolbarIdbtnPhieuKham").attr("disabled", false);
				$("#toolbarIddrug_khothuoc").removeClass("disabled");
				$("#toolbarIddrug_phieuđinhuong").removeClass("disabled");
				$("#toolbarIddrug_1dy").removeClass("disabled");
				$("#toolbarIddrug_3").removeClass("disabled");
				$("#toolbarIddrugtt_3").removeClass("disabled"); //L2PT-13323
				$("#toolbarIddrug_tutruc").removeClass("disabled");
			}
		}

		if (_khamchinhphu == "1"){
			if(_khamchinhphudetail == "1"){
				//$("#toolbarIdhandling_1").removeClass("disabled");
				$("#toolbarIdbtnPhieuKham").removeClass("disabled");
			}else{
				$("#toolbarIdhandling_1").addClass("disabled");
				$("#toolbarIdbtnPhieuKham").addClass("disabled");
			}
		}*/
	}

	// ======= SU DUNG TRONG TRUONG HOP CHO PHEP
	function _selectedRow(item_id){
		if (item_id != null && item_id.length > 0) {
			FormUtil.clearForm('divContentHC');
			$("#hidICD_RAVIEN").val(''); //L2PT-28568

			var selRowId = $('#' + _gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $('#' + _gridId).jqGrid('getRowData', item_id);

			$("#hidKHAMBENHID").val('-1');

			var _khambenhid = rowData.KHAMBENHID;
			var _hosobenhanid = rowData.HOSOBENHANID;
			var _phongkhamdangkyid = rowData.PHONGKHAMDANGKYID;
			var _benhnhanid = rowData.BENHNHANID;
			var _doituongbenhnhanid = rowData.DOITUONGBENHNHANID;
			var _tiepnhanid = rowData.TIEPNHANID;
			var _loaitiepnhanid = rowData.LOAITIEPNHANID;
			var _trangthaikhambenh = rowData.TRANGTHAIKHAMBENH;
			var _xutrikhambenh = rowData.XUTRIKHAMBENHID;
			var _sothutu = rowData.SOTHUTU;

			$("#txtSTT5").val(_sothutu);
			$("#txtHOTEN5").val(rowData.TENBENHNHAN);
			$('#txtMSGSTT').val(rowData.TENBENHNHAN + " - lần " + rowData.LANGOIKHAM);

			$("#hidHOTENBNGOI").val(rowData.TENBENHNHAN);
			$("#hidKHAMBENHID").val(_khambenhid);
			$("#hidHOSOBENHANID").val(_hosobenhanid);
			//tuyennx_add_start_20171020 yc HISL2NT-485
			$("#hidTRANG_THAI").val(rowData.TRANG_THAI);
			//tuyennx_add_end_20171020 yc HISL2NT-485
			//tuyennx_add_start_20171020 yc L2DKBD-692
			$("#hidLOAIBENHANID").val(rowData.LOAIBENHANID);
			$("#hidTEN_FILE").val(rowData.TEN_FILE);
			//tuyennx_add_end_20171020 yc L2DKBD-692
			$("#hidPHONGKHAMDANGKYID").val(_phongkhamdangkyid);
			$("#hidBENHNHANID").val(_benhnhanid);
			$("#hidDOITUONGBENHNHANID").val(_doituongbenhnhanid);
			$("#hidTIEPNHANID").val(_tiepnhanid);
			$("#hidLOAITIEPNHANID").val(_loaitiepnhanid);
			$('#hidXUTRIKHAMBENHID').val(_xutrikhambenh);
			$('#hidHINHTHUCXUTRIID').val(rowData.HINHTHUCXUTRIID);
			$("#hidINDEX").val(item_id);
			$("#hidSOTHUTU").val(_sothutu);
			$("#hidHisId").val(opt.hospital_id);
			$("#hidUserID").val(opt.user_id);
			$("#hidMADICHVU").val(rowData.MADICHVU);
			$('#lblSTT').html(_sothutu);
			$("#hidPHONGID").val(rowData.PHONGID);
			//tuyennx_add_start_20171020 congboyte
			$("#hidMAHOSOBENHAN").val(rowData.MAHOSOBENHAN);
			//tuyennx_add_end_20171020
			$("#hidTRANGTHAITIEPNHAN").val(rowData.TRANGTHAITIEPNHAN);
			$("#hidCHUYENKHAMNGT").val(rowData.CHUYENKHAMNGT);
			$("#hidSUB_DTBNID").val(rowData.SUB_DTBNID);
			_khamchinhphudetail = rowData.KHAMCHINHPHU;
			$("#hidKHAMCHINHPHU").val(rowData.KHAMCHINHPHU);
			$("#hidMABHYT").val(rowData.MA_BHYT);
			$("#hidMAUBENHPHAMID").val(rowData.MAUBENHPHAMID);
			$("#hidDICHVUKHAMBENHID").val(rowData.DICHVUKHAMBENHID);
			_hinhthucvaovienid = rowData.HINHTHUCVAOVIENID;

			var NGHIHUONG_BHXH = cfObj.NGHIHUONG_BHXH;
			if(_trangthaikhambenh == "9" ){
				// xu ly disable menu cho benh nhan ket thuc kham
				_disableControl(['toolbarIdbtnCall', 'toolbarIdbtnLuu', 'toolbarIdbtnExam', 'toolbarIdbtnService','toolbarIdbtnPhieuDT','toolbarIdbtndrug',
					'toolbarIdbtnPhieuKham','toolbarIdbtnKHAC','toolbarIdbtnKTKH','toolbarIdbtn_TNTT','toolbarIdbtnKTKHVS2'], true);

				//hunglv L2PT-10100
				if (NGHIHUONG_BHXH == '1'){
					_disableControl(['toolbarIdbtnKHAC'], false);
					$("#toolbarIdhandling_1").addClass("disabled");
					$("#toolbarIdhandling_4").addClass("disabled");
					$("#toolbarIdhandling_3").addClass("disabled");
					$("#toolbarIdbtnKHAC_3").addClass("disabled");
					$("#toolbarIdbtnKHAC_8").addClass("disabled");
					$("#toolbarIdhandling_2").addClass("disabled");
					//$("#toolbarIdbtnKHAC_9").addClass("disabled"); form nghỉ hưởng
					$("#toolbarIdbntKHAC_ptu").addClass("disabled");
					$("#toolbarIdbtnKHAC_10").addClass("disabled");
					$("#toolbarIdbtnKHAC_11").addClass("disabled");
					$("#toolbarIdbtnChuyenTuyenCS").addClass("disabled");
					$("#toolbarIdbtnCapSoBenh").addClass("disabled");


					$("#toolbarIdbtnNewBed").addClass("disabled");
					$("#toolbarIdhandling_10").addClass("disabled");
					$("#toolbarIdbtnKHAC_0").addClass("disabled");
					$("#toolbarIdbtnKHAC_2").addClass("disabled");
					$("#toolbarIdbtnKHAC_4").addClass("disabled");
					$("#toolbarIdbtnKHAC_5").addClass("disabled");
					$("#toolbarIdbtnKHAC_6").addClass("disabled");
					$("#toolbarIdbtnKHAC_7").addClass("disabled");
				}
			}
			else{
				//hunglv L2PT-10100
				if (NGHIHUONG_BHXH == '1'){
					$("#toolbarIdhandling_1").removeClass("disabled");
					$("#toolbarIdhandling_4").removeClass("disabled");
					$("#toolbarIdhandling_3").removeClass("disabled");
					$("#toolbarIdbtnKHAC_3").removeClass("disabled");
					$("#toolbarIdbtnKHAC_8").removeClass("disabled");
					$("#toolbarIdhandling_2").removeClass("disabled");
					//$("#toolbarIdbtnKHAC_9").removeClass("disabled");
					$("#toolbarIdbntKHAC_ptu").removeClass("disabled");
					$("#toolbarIdbtnKHAC_10").removeClass("disabled");
					$("#toolbarIdbtnKHAC_11").removeClass("disabled");
					$("#toolbarIdbtnChuyenTuyenCS").removeClass("disabled");
					$("#toolbarIdbtnCapSoBenh").removeClass("disabled");


					$("#toolbarIdbtnNewBed").removeClass("disabled");
					$("#toolbarIdhandling_10").removeClass("disabled");
					$("#toolbarIdbtnKHAC_0").removeClass("disabled");
					$("#toolbarIdbtnKHAC_2").removeClass("disabled");
					$("#toolbarIdbtnKHAC_4").removeClass("disabled");
					$("#toolbarIdbtnKHAC_5").removeClass("disabled");
					$("#toolbarIdbtnKHAC_6").removeClass("disabled");
					$("#toolbarIdbtnKHAC_7").removeClass("disabled");

				}
			}

			var objj = new Object();
			objj._xutrikhambenh = _xutrikhambenh;
			loadTabStatus(_trangthaikhambenh, objj);

			// load du lieu cho tab Hanh Chinh
			_loadTabHanhChinh(_khambenhid);

			var vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.VIENPHI",rowData.TIEPNHANID);
			if(vp_ar != null && vp_ar.length > 0){
				var data = vp_ar[0];
				$('#txtTIENTAMUNG').val(formatNumber(data.TAMUNG) + " đ");
				$('#txtTONGCHIPHI').val(formatNumber(data.TONGTIENDV) + " đ");
				$('#txtDANOP').val(formatNumber(data.DANOP) + " đ");
				$('#txtTIEN_BH').val(formatNumber(data.TIEN_BH) + " đ"); //L2PT-13893
			}

			//tuyennx_add_start_20171115

			if(_trangthaikhambenh == "4" && cfObj.NGT_KETHUOC_KHI_NHAPVIEN == "1"){
				if(_xutrikhambenh == "6" || _xutrikhambenh == "2" ){
					$("#toolbarIdbtndrug").attr("disabled", true);
				}else{
					$("#toolbarIdbtndrug").attr("disabled", false);
				}
			}
			//bn da kham ko cho doi phong kham
// 			if(_trangthaikhambenh == "4" ){
// 				$("#toolbarIdhandling_2").addClass("disabled");
// 			}else{
// 				$("#toolbarIdhandling_2").removeClass("disabled");
// 			}
			if(_trangthaikhambenh == "4" && cfObj.NGT_DOIPHONGKHAM_DV == 0){
				// var _sql_par = [];
				// _sql_par.push({
				// 	"name" : "[0]",
				// 	value : $("#hidKHAMBENHID").val()
				// });
				// var _check = jsonrpc.AjaxJson.getOneValue("NGT.CHECK.DICHVU", _sql_par);
				// if(_check == 0){
				// 	$("#toolbarIdhandling_2").removeClass("disabled");
				// }
				// else {
				$("#toolbarIdhandling_2").addClass("disabled");
				// }
			}

			// xu ly disable menu cho benh nhan ket thuc kham

			//tuyennx_add_end_20171115
			/*
			/*vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.05",rowData.TIEPNHANID);
			if(vp_ar != null && vp_ar.length > 0){
				var data = vp_ar[0];
				$('#txtTONGCHIPHI').val(formatNumber(data.TONGTIENDV) + " đ");
			}*/


			//thaiph - L2PT-21687
			var checkBADAINGAY = cfObj.NGT_HT_BADAINGAY;
			if(checkBADAINGAY == 1){
				var sql_par = [];
				sql_par.push(
					{
						"name" : "[0]",
						"value" : _benhnhanid
					},
					{
						"name" : "[1]",
						"value" : _hosobenhanid
					});
				var ret = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT.DSBADAINGAY1", sql_par);
				var rows = JSON.parse(ret);
				if (rows != null && rows.length > 0) {
					if (rows[0]["BADAINGAY"].length > 0) {
						$("#dvBADAINGAY").show();
						$("#txtTTBADAINGAY").val(rows[0]["BADAINGAY"]);
					}
				}
				else{
					$("#dvBADAINGAY").hide();
				}
			}
			else{
				$("#dvBADAINGAY").hide();
			}
			//end - L2PT-21687
			if(_trangthaikhambenh == "9" ){
				//hunglv L2PT-10100
				if (NGHIHUONG_BHXH == '1'){
					_disableControl(['toolbarIdbtnKHAC'], false);
				}
			}
			if(_trangthaikhambenh == 9){
				$("#cboXUTRIKHAMBENHID").attr("disabled", true);
				$("#cboKETQUADIEUTRIID").attr("disabled", true);
				$("#cboBACSIID").attr("disabled", true);
				$("#cboKHOA").attr("disabled", true);
				$("#toolbarIdbtnDAYEBHYT").removeClass("disabled");
			}else{
				if(_khamchinhphudetail != "0"){
					$("#cboXUTRIKHAMBENHID").attr("disabled", false);
					$("#cboKETQUADIEUTRIID").attr("disabled", false);
					$("#cboBACSIID").attr("disabled", false);
					$("#cboKHOA").attr("disabled", false);
					$("#txtMACHANDOANRAVIEN").attr("disabled", false);
					$("#txtCHANDOANRAVIEN").attr("disabled", false);
					$("#toolbarIdbtnDAYEBHYT").addClass("disabled");
				}else{
					$("#cboXUTRIKHAMBENHID").attr("disabled", true);
					$("#cboKETQUADIEUTRIID").attr("disabled", true);
					$("#cboBACSIID").attr("disabled", true);
					$("#cboKHOA").attr("disabled", true);
					$("#txtMACHANDOANRAVIEN").attr("disabled", true);
					$("#txtCHANDOANRAVIEN").attr("disabled", true);
					$("#toolbarIdbtnDAYEBHYT").addClass("disabled");
				}

			}
			//thaiph - L2PT-4340
			$("#txtCHANDOANRAVIEN").prop('disabled', true);
			var checkBC = cfObj.NGT_CHINHSUABENHCHINH;
			if(checkBC =='1' && _doituongbenhnhanid=='2'){
				$("#txtCHANDOANRAVIEN").prop('disabled', false);
			}
			if(checkBC =='2'){
				$("#txtCHANDOANRAVIEN").prop('disabled', false);
			}
			if(checkBC =='0'){
				$("#txtCHANDOANRAVIEN").prop('disabled', true);
			}
			//dannd_L2PT-25820
			if (cfObj.NGT_ANHIEN_CHUYENDOI_PKCK == 1) {
				if(_trangthaikhambenh == 1){
					$("#toolbarIdhandling_1").addClass("disabled");
					$("#toolbarIdhandling_2").removeClass("disabled");
					$("#toolbarIdbtnDOICONGKHAM").removeClass("disabled");
				}else if(_trangthaikhambenh == 4) {
					$("#toolbarIdhandling_1").removeClass("disabled");
					$("#toolbarIdhandling_2").addClass("disabled");
					$("#toolbarIdbtnDOICONGKHAM").addClass("disabled");
				}
			}
			//end thaiph - L2PT-4340

			//hungnd - L2PT-62585
			if(_trangthaikhambenh == "4" && cfObj.NGT_KHAMBENH_DISBTN_DANGKHAM == '1' ){
				_setButtonDangKham(false)
			}
			else{
				_setButtonDangKham(true)
			}

		}else{
			FormUtil.clearForm("divContentHC", "");
			FormUtil.clearForm("tabBenhAn", "");
			$("#hidKHAMBENHID").val(-1);
			$("#toolbarIdbtnStart").attr("disabled", true);
			$('#lblTAMUNG').html('');
			$('#lblTONGCHIPHI').html('');
			$('#lblBAOHIEMTHANHTOAN').html('');
			$('#hidXUTRIKHAMBENHID').val('');
		}
	}

	//calback cho MA HINH SUA PHONG THUC HIEN
	EventUtil.setEvent("assignSevice_SavePhongThucHien", function(e) {
		DlgUtil.showMsg(e.msg);
		//reload danh sach xet nghiem
		if(e.loaiPhieu==LNMBP_XetNghiem){
			$('#tabXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_XetNghiem,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		}else if(e.loaiPhieu==LNMBP_CDHA){
			$('#tabCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_CDHA,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		}

		DlgUtil.close("divDlgEditOrgDone");
	});

	function _loadTabHanhChinh(khambenhid){
		var sql_par=[khambenhid, $("#hidPHONGID").val()];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(_SQL[1],sql_par.join('$'));
		if (data_ar != null && data_ar.length > 0) {
			var row=data_ar[0];
			FormUtil.clearForm("subdivContentHC","");
			FormUtil.setObjectToForm("divContentHC","",row);
			var ux2023 = cfObj.HIS_SHOW_TAB_UX2023;
			// SONDN 27/11/2019 L2PT-12032
			if (row.DOITUONGBENHNHANID == "1"){
				$("#txtTUYENHIENTHI").val(row.TUYEN);
				var _tientothe = row.SOTHEBHYT.substr(0,3).toUpperCase();
				var objBH = new Object();
				objBH.MATHE = row.SOTHEBHYT;
				objBH.DOITUONGSONG = row.DT_SINHSONG;
				var data_ar1 = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.MUCHUONG.BHYT",
					row.KHOAID+'$'+_tientothe+'$'+row.BHYT_LOAIID+'$'+JSON.stringify(objBH)+'$'+row.HINHTHUCVAOVIENID);
				if (data_ar1 != null && data_ar1.length > 0){
					var _bstr = "Ngoại (" + data_ar1[0].MUCHUONG_NGOAI + "%)-Nội (" + data_ar1[0].MUCHUONG_NOI + "%)";
					$("#txtMUCHUONGHIENTHI").val(_bstr);
				}
			}else{
				$("#txtTUYENHIENTHI").val("");
				$("#txtMUCHUONGHIENTHI").val("");
			}
			// END SONDN 27/11/2019 L2PT-12032

			if (row.TKB == 1) {
				$('#dvTENBENHNHAN').removeClass("col-xs-10 low-padding");
				$('#dvTENBENHNHAN').addClass("col-xs-9 low-padding");
				$('#dvTKB').show();
			}else {
				$('#dvTENBENHNHAN').removeClass("col-xs-9 low-padding");
				$('#dvTENBENHNHAN').addClass("col-xs-10 low-padding");
				$('#dvTKB').hide();
			}
			if(cfObj.NGT_DOIMAU_PHONGDANGKY == '1'){
				$("#txtPHONGDK").css("color","red");
			}
			var xutriid = row['XUTRIKHAMBENHID'];
			$("#divKhoa").css("display","none");
			if(xutriid == 2 || xutriid == 6){
				_changexutri(xutriid);
			}
			if ( xutriid == 7 && cfObj.HIS_QD130 == '1'){
				$('#divChuyenVien').show();
			}else{
				$('#divChuyenVien').hide();
			}
			if(_khamchinhphudetail == 1){
				$("#txtKHAMCHINHPHU").val("Chính");
			}else{
				$("#txtKHAMCHINHPHU").val("Phụ");
			}
			$("#txtMABENHNHAN").val($("#txtMABENHNHAN").val() + " - " + $("#txtKHAMCHINHPHU").val());
			$("#txtTENBENHNHAN").val($("#txtTENBENHNHAN").val() + " - " + $("#txtTUOI").val() + " - " + $("#txtGIOITINH").val());
			if($("#txtnguoilienhe").val() != "" && $("#txtSDTLH").val() != ""){
				$("#txtnguoilienhe").val($("#txtnguoilienhe").val() + " - " + $("#txtSDTLH").val());
			} else if($("#txtnguoilienhe").val() != "" && $("#txtSDTLH").val() == ""){
				$("#txtnguoilienhe").val($("#txtnguoilienhe").val());
			} else if($("#txtnguoilienhe").val() != "" && $("#txtSDTLH").val() == ""){
				$("#txtnguoilienhe").val($("#txtSDTLH").val());
			}
			if($("#txtSOTHEBHYT").val() != ""){
				$("#txtSOTHEBHYT").val($("#txtSOTHEBHYT").val() + " - " + $("#txtKCBBD").val() + " - " + $("#txtngaybaohiem").val());
			}
			if(row.KHAMLAITRONGNGAY ==  1) {
				$("#dvKHAMLAI").show();
				$("#lblKHAMLAITRONGNGAY").text("Bệnh án vào viện hai lần trong ngày");
			}else {
				$("#dvKHAMLAI").hide();
			}
			if (row.ANHBENHNHAN != null){
				var imgTag = document.getElementById('imgBN');
				imgTag.src = row.ANHBENHNHAN;
			}else{
				$('#imgBN').removeAttr('src');
			}

			if (row.BTNTHUOC == "1" && row.TRANGTHAI_STT == "4" && $("#hidCHUYENKHAMNGT").val() == "0"){
				$("#toolbarIdbtndrug").attr("disabled", false);
			}
			var ngaytiepnhan = moment(row.NGAYTIEPNHAN, 'DD/MM/YYYY HH:mm:ss');
			var thoigiangientai = moment(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'), 'DD/MM/YYYY HH:mm:ss');

			if((cfObj.NGT_CHANTHOIGIAN_BATDAUKHAM == 1  )
				&& (ngaytiepnhan > thoigiangientai)){
				$("#toolbarIdbtndrug").attr("disabled", true);
				$("#toolbarIdbtnService").attr("disabled", true);
				$("#toolbarIdbtnExam").attr("disabled", true);
			}else {
				$("#toolbarIdbtndrug").attr("disabled", false);
				$("#toolbarIdbtnService").attr("disabled", false);
				$("#toolbarIdbtnExam").attr("disabled", false);
			}
			if (row.TRANGTHAI_STT == "9" && cfObj.NGT_CHANKHAMBENH_KETTHUCKHAM == 1){
				$("#toolbarIdbtnExam").attr("disabled", true);
			}else{
				$("#toolbarIdbtnExam").attr("disabled", false);
			}
			if (row.TRANGTHAI_STT == "9" && cfObj.NGT_CHANCDDVTHUOC_KTK == 1){
				$("#toolbarIdbtndrug").attr("disabled", true);
				$("#toolbarIdbtnService").attr("disabled", true);
			}else{
				$("#toolbarIdbtndrug").attr("disabled", false);
				$("#toolbarIdbtnService").attr("disabled", false);
			}

			var _sql_par=[$("#hidKHAMBENHID").val(), $("#hidPHONGKHAMDANGKYID").val(), ];
			_msgphong = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET_MSG_PK", _sql_par.join('$'));
			if(_msgphong && _msgphong != '0' && row.TRANGTHAI_STT == "1"){
				var _msg = _msgphong[0].DSPHONGKHAM;
				DlgUtil.showMsg("Bệnh nhân "+ $("#txtTENBENHNHAN").val() + " đã khám tại :" + _msg);
			}

			if(data_ar[0].SLXN != 0){
				$('#tabXetNghiemTab').html("Xét nghiệm <b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLXN+")</b>");
				$('#tabXetNghiemTab').show();
			}else if(data_ar[0].SLXN == 0 && ux2023 == 1){
				$('#tabXetNghiemTab').text("Xét nghiệm");
				$('#tabXetNghiemTab').hide();
			}else if(data_ar[0].SLXN == 0 && ux2023 != 1){
				$('#tabXetNghiemTab').text("Xét nghiệm");
			}

			if(data_ar[0].SLCDHA != 0){
				$('#tabCDHATab').html("CĐHA<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLCDHA+")</b>");
				$('#tabCDHATab').show();
			}else if(data_ar[0].SLCDHA == 0 && ux2023 == 1){
				$('#tabCDHATab').text("CĐHA");
				$('#tabCDHATab').hide();
			}else if(data_ar[0].SLCDHA == 0 && ux2023 != 1){
				$('#tabCDHATab').text("CĐHA");
			}

			if(data_ar[0].SLCHUYENKHOA != 0){
				$('#tabChuyenKhoaTab').html("Phẫu thuật thủ thuật<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLCHUYENKHOA+")</b>");
				$('#tabChuyenKhoaTab').show();
			}else if(data_ar[0].SLCHUYENKHOA == 0 && ux2023 == 1){
				$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
				$('#tabChuyenKhoaTab').hide();
			}else if(data_ar[0].SLCHUYENKHOA == 0 && ux2023 != 1){
				$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
			}

			if(data_ar[0].SLTHUOC != 0){
				$('#tabThuocTab').html("Thuốc<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLTHUOC+")</b>");
				$('#tabThuocTab').show();
			}else if(data_ar[0].SLTHUOC == 0 && ux2023 == 1){
				$('#tabThuocTab').text("Thuốc");
				$('#tabThuocTab').hide();
			}else if(data_ar[0].SLTHUOC == 0 && ux2023 != 1){
				$('#tabThuocTab').text("Thuốc");
			}

			//L2PT-61755
			if(data_ar[0].SLTRUYENDICH != 0){
				$('#tabPhieuTruyenDichTab').html("Truyền dịch<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLTRUYENDICH+")</b>");
				$('#tabPhieuTruyenDichTab').show();
			}else if(data_ar[0].SLTRUYENDICH == 0 && ux2023 == 1){
				$('#tabPhieuTruyenDichTab').text("Truyền dịch");
				$('#tabPhieuTruyenDichTab').hide();
			}else if(data_ar[0].SLTRUYENDICH == 0 && ux2023 != 1){
				$('#tabPhieuTruyenDichTab').text("Truyền dịch");
			}

			if(data_ar[0].SLDIEUTRI != 0){
				$('#tabDieuTriTab').html("Điều trị<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLDIEUTRI+")</b>");
				$('#tabDieuTriTab').show();
			}else if(data_ar[0].SLDIEUTRI == 0 && ux2023 == 1){
				$('#tabDieuTriTab').text("Điều trị");
				$('#tabDieuTriTab').hide();
			}else if(data_ar[0].SLDIEUTRI == 0 && ux2023 != 1){
				$('#tabDieuTriTab').text("Điều trị");
			}

			if(data_ar[0].SLNGAYGIUONG != 0){
				$('#idTabNgayGiuong').html("Ngày giường<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLNGAYGIUONG+")</b>");
				$('#idTabNgayGiuong').show();
			}else if(data_ar[0].SLNGAYGIUONG == 0 && ux2023 == 1){
				$('#idTabNgayGiuong').text("Ngày giường");
				$('#idTabNgayGiuong').hide();
			}else if(data_ar[0].SLNGAYGIUONG == 0 && ux2023 != 1){
				$('#idTabNgayGiuong').text("Ngày giường");
			}


			if(data_ar[0].SLVATTU != 0){
				$('#tabVatTuTab').html("Vật tư<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLVATTU+")</b>");
				$('#tabVatTuTab').show();
			}else if(data_ar[0].SLVATTU == 0 && ux2023 == 1){
				$('#tabVatTuTab').text("Vật tư");
				$('#tabVatTuTab').hide();
			}else if(data_ar[0].SLVATTU == 0 && ux2023 != 1){
				$('#tabVatTuTab').text("Vật tư");
			}

			if(data_ar[0].SLVANCHUYEN != 0){
				$('#tabPhieuVanChuyenTab').html("Phiếu vận chuyển<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLVANCHUYEN+")</b>");
				$('#tabPhieuVanChuyenTab').show();
			}else if(data_ar[0].SLVANCHUYEN == 0 && ux2023 == 1){
				$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
				$('#tabPhieuVanChuyenTab').hide();
			}else if(data_ar[0].SLVANCHUYEN == 0 && ux2023 != 1){
				$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
			}


			if(data_ar[0].SLBADTNGT != 0){
				$('#tabBADTNGTTab').html("BA ĐT Ngoại trú<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLBADTNGT+")</b>");
				$('#tabBADTNGTTab').show();
			}else if(data_ar[0].SLBADTNGT == 0 && ux2023 == 1){
				$('#tabBADTNGTTab').text("BA ĐT Ngoại trú");
				$('#tabBADTNGTTab').hide();
			}else if(data_ar[0].SLBADTNGT == 0 && ux2023 != 1){
				$('#tabBADTNGTTab').text("BA ĐT Ngoại trú");
			}

			// sondn L2PT-1711
			if(data_ar[0].SLHOICHUAN != 0){
				$('#tabHoiChuanTab').html("Hội chẩn<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLHOICHUAN+")</b>");
				$('#tabHoiChuanTab').show();
			}else if(data_ar[0].SLHOICHUAN == 0 && ux2023 == 1){
				$('#tabHoiChuanTab').text("Hội chẩn");
				$('#tabHoiChuanTab').hide();
			}else if(data_ar[0].SLHOICHUAN == 0 && ux2023 != 1){
				$('#tabHoiChuanTab').text("Hội chẩn");
			}
			// end sondn L2PT-1711
			if(data_ar[0].SLDIEUTRI != 0){
				$('#tabDieuTriTab').html("Điều trị<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLDIEUTRI+")</b>");
				$('#tabDieuTriTab').show();
			}else if(data_ar[0].SLDIEUTRI == 0 && ux2023 == 1){
				$('#tabDieuTriTab').text("Điều trị");
				$('#tabDieuTriTab').hide();
			}else if(data_ar[0].SLDIEUTRI == 0 && ux2023 != 1){
				$('#tabDieuTriTab').text("Điều trị");
			}
			if(data_ar[0].SLCHAMSOC != 0){
				$('#tabChamSocTab').html("Chăm sóc<b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].SLCHAMSOC+")</b>");
				$('#tabChamSocTab').show();
			}else if(data_ar[0].SLCHAMSOC == 0 && ux2023 == 1){
				$('#tabChamSocTab').text("Chăm sóc");
				$('#tabChamSocTab').hide();
			}else if(data_ar[0].SLCHAMSOC == 0 && ux2023 != 1){
				$('#tabChamSocTab').text("Chăm sóc");
			}
			if(data_ar[0].CONGKHAM != 0){
				$('#tabCongKhamTab').html("Phiếu công khám <b style = 'color:red;font-weight: bolder;'> ("+data_ar[0].CONGKHAM+")</b>");
				$('#tabCongKhamTab').show();
			}else if(data_ar[0].CONGKHAM == 0 && ux2023 == 1){
				$('#tabCongKhamTab').text("Phiếu công khám");
				$('#tabCongKhamTab').hide();
			}else if(data_ar[0].CONGKHAM == 0 && ux2023 != 1){
				$('#tabCongKhamTab').text("Phiếu công khám");
			}
			// sondn IT360-190771
			if (_vssid == "1" && data_ar[0].VSSID == "1"){
				$.bootstrapGrowl('Lưu ý bệnh nhân sử dụng thẻ BHYT VssID',{
					type: 'warning',
					delay: 3000,
				});
			}
			// end sondn IT360-190771
			var _trangthaikhambenh = row.TRANGTHAI_STT;
			//load thong tin sinh ton BM2
			// data_arkb = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K002.LAYDL",[khambenhid, _opt.phongid].join('$'));
			data_arkb = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K002.LAYDL1",[khambenhid, _opt.phongid, $("#hidPHONGKHAMDANGKYID").val()].join('$'));
			if (data_arkb != null && data_arkb.length > 0) {
				var row=data_arkb[0];
				row['XUTRIKHAMBENHID'] = xutriid;
				FormUtil.setObjectToForm("divKB","",row);
				$("#cboXUTRIKHAMBENHID").val(xutriid);
				//$("#cboXUTRIKHAMBENHID").trigger('change');
				//$("#cboBACSIID").val(opt.user_id);
				if (cfObj.NGT_LOADBACSY_NDH == '1' &&  _trangthaikhambenh  != '9' ){
					$("#cboBACSIID").val(opt.user_id);
				}else{
					if ($("#cboBACSIID").val() == null) $("#cboBACSIID").val(opt.user_id);
				}
				// sondn L2PT-29895
				if (row.MABENHANDAINGAY.length == 0){
					$("#txtMABENHANDAINGAY").prop("disabled", false);
				}
				// end sondn L2PT-29895
			}

			// cap nhat kham online
			if (_sudungKhamOnline == "1"){
				$("#dvONLINEKHAM").show();
			}else{
				$("#dvONLINEKHAM").hide();
			}
			//load thong tin BN
			if(showTTBNWidget) {
				$('#divMsg').show();
				$('#lblMSG_MABENHAN').html(row.MABENHAN);

				if (NGT02K001_KB_MHC_VS2_DIVMSG == '1' && row.MAHOSOBENHAN) $('#lblMSG_MABENHAN').html( row.MAHOSOBENHAN + ' | ' + $('#lblMSG_MABENHAN').html() ); //L2PT-46578

				$('#lblMSG_TENBENHNHAN').html(row.TENBENHNHAN);
				$('#lblMSG_NGAYSINH').html(row.NGAYSINH+ ' (' + row.TUOI + ' ' + row.DVTUOI + ')');
				$('#lblMSG_GIOITINH').html( row.GIOITINH == "01" ? "NAM": "NỮ");
				$('#lblMSG_DIACHI').html(row.DIACHI);
				if(cfObj.HIS_SHOW_TTBENHAN_BS == '1') {
					var txtbs = ' | ' + data_ar[0].NGAYTIEPNHAN;
					if (data_ar[0].CDC) {
						txtbs = txtbs + ' | ' + data_ar[0].CDC;
					}
					if (data_ar[0].SOTHEBHYT) {
						txtbs = txtbs + ' | ' + data_ar[0].SOTHEBHYT;
					}
					$('#lblMSG_BOSUNG').html(txtbs);
				}
			}
			// canh bao ngay tiep nhan <> ngay hien tai
			var _ngaytnBN = $("#hidNGAYTN").val(); 					// YYYYMMDDHH24MI
			if (_setngaytiepnhan == "1" && _ngaytnBN != "" && _ngaytnBN != null && _ngaytnBN.length >= 8){
				if (Number(_ngaytnBN.substring(0,8)) - Number(sdate) > 0 ){
					DlgUtil.showMsg("Bệnh nhân có thời gian tiếp nhận lớn hơn thời gian hệ thống");
				}
			}
		}else{
			FormUtil.clearForm("divContentHC","");
		}
	}

	function _formatRow(_rId, _fIndex)
	{
		var _icon = '';
		if(_opt.imgPath[_fIndex] != '')
			_icon = '<center><img src="../common/img/' + _opt.imgPath[_fIndex] + '" width="15px"></center>';
		$("#"+_grdDieuTri).setRowData(_rId,{icon:_icon});
		$("#"+_grdDieuTri).find("tr[id='" + _rId + "']").find('td').each(function(index, element) {
			$(element).css({'color':_opt.foreColor[_fIndex]});
		});
	}

	function _setButton(value){
		$("#toolbarIdbtnExam").attr("disabled", value);
		$("#toolbarIdbtnTreat").attr("disabled", value);
		$("#toolbarIdbtnService").attr("disabled", value);
		$("#toolbarIdbtnPhieuDT").attr("disabled", value);
		//$("#toolbarIdbtndrug").attr("disabled", value);
		$("#toolbarIdbtnhandling").attr("disabled", value);
		$("#toolbarIdbtnPhieuKham").attr("disabled", value);
		$("#toolbarIdbtnHoaHong").attr("disabled", value);
		//$("#toolbarIdbtnStart").attr("disabled", value);
		$("#toolbarIdbtnKTKH").attr("disabled", value);
		$("#toolbarIdbtnKTKHVS2").attr("disabled", value);
		//$("#toolbarIdbtnCall").attr("disabled", value);
		$("#toolbarIdbtnKHAC").attr("disabled", value);
		$("#toolbarIdbtn_TNTT").attr("disabled", value);
		$("#toolbarIdbtndrug").attr("disabled", value);
		$("#toolbarIdhandling_1").attr("disabled", value);
		$("#toolbarIdbtnLuu").attr("disabled", value);
	}

	//hungnd - L2PT-62585
	function _setButtonDangKham(value){
		$("#toolbarIdbtndrug").attr("disabled", value);
		$("#toolbarIdbtnService").attr("disabled", value);
		$("#toolbarIdbtnPhieuKham").attr("disabled", value);

	}
	//end hungnd

	function _disableMenuXuTri(xutriid){
		//ductx - bvtm-5174
		var arrID = [];
		if(opt.hospital_id == '10284'){
			arrID = ['toolbarIdbtnKHAC_0', 'toolbarIdbtnKHAC_1', 'toolbarIdbtnKHAC_2', 'toolbarIdbtnKHAC_4', 'toolbarIdbtnKHAC_5', 'toolbarIdbtnKHAC_6'];
		}else{
			arrID = ['toolbarIdbtnKHAC_0', 'toolbarIdbtnKHAC_1', 'toolbarIdbtnKHAC_2', 'toolbarIdbtnKHAC_4', 'toolbarIdbtnKHAC_5', 'toolbarIdbtnKHAC_7'];
		}
		//end bvtm-5174

		for(var i = 0; i < arrID.length; i++){
			$("#"+arrID[i]).addClass("disabled");
		}

		if(xutriid == "8"){
			$('#toolbarIdbtnKHAC_0').removeClass("disabled");
			$('#toolbarIdbtnKHAC_1').removeClass("disabled");
			$('#toolbarIdbtnKHAC_2').removeClass("disabled");
		}else if(xutriid == "1" || xutriid == "3" || xutriid == "9"){
			$('#toolbarIdbtnKHAC_4').removeClass("disabled");
		}else if(xutriid == "4"){// hẹn kham tiep
			$('#toolbarIdbtnKHAC_7').removeClass("disabled");
		}
		else if(xutriid == "7"){
			$('#toolbarIdbtnKHAC_5').removeClass("disabled");
		}else if(xutriid == "5"){ // hẹn kham moi
			$('#toolbarIdbtnKHAC_6').removeClass("disabled");
		}
	}

	// hiển thị dialog xử trí khám bệnh
	function _showDialog(url, param, title, w, h){
		_objData = {};
		dlgPopup=DlgUtil.buildPopupUrl("dlgXuTri","divDlg","manager.jsp?func=../ngoaitru/"+url,param,title,w,h);
		DlgUtil.open("dlgXuTri");
		EventUtil.setEvent("dlgXuTri_onClose",function(){
			if (cfObj.NGT_PHIMTAT_KB2 == 1) {
				$("#cboKETQUADIEUTRIID").focus();
			}
		});
	}

	function _changexutri(value){
		var qd130 = cfObj.HIS_QD130;
		var _kqdt = "0";
		if(_hienthikqdt != "0"){
			_kqdt = $('#cboXUTRIKHAMBENHID option:selected').attr('extval0');
			if(_kqdt == '-1' ||  _kqdt == undefined || typeof _kqdt == 'undefined'){
				_kqdt = $("#cboKETQUADIEUTRIID").val();
			}
		}else{
			_kqdt = "0"; 								// khong set kqdt;
		}
		$("#cboKETQUADIEUTRIID").val(_kqdt);
		var heightwindow = $(window).height();
		var widthwindow = $(window).width();
		var param = {
			mabenhnhan : $('#txtMABENHNHAN').val(),
			tenbenhnhan : $('#txtTENBENHNHAN').val(),
			nghenghiep : $("#hidTENNGHENGHIEP").val(),
			diachi : $('#txtDIACHI').val(),
			namsinh : $('#hidNAMSINH').val(),
			khambenhid : $('#hidKHAMBENHID').val(),
			benhnhanid : $('#hidBENHNHANID').val(),
			chandoan : $('#txtCDC').val(),
			ngaytiepnhan : $('#txtDENKHAMLUC').val(),
			capnhat : '1',
			hosobenhanid : $('#hidHOSOBENHANID').val(),
			maubenhphamid : $("#hidMAUBENHPHAMID").val(),
			phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val()
		};
		if(value === "1"){
			if (_bnvpinhathuoc == "1" && $("#hidDOITUONGBENHNHANID").val() != "1"){
				// SONDN L2PT-8739 300919
				$("#toolbarIddrug_dtnhathuoc").trigger("click");
				// END SONDN L2PT-8739 300919
			}else{
				$("#toolbarIddrug_khothuoc").trigger("click");
			}
			if (qd130 == 1){
				$("#divChuyenVien").hide();
			}
		}else if(value === "3" || value === "9"){ // Cấp toa cho về, hẹn, khác
			_showDialog("NGT02K007_Thongtin_Ravien", param, 'Thông tin ra viện',widthwindow - 100,heightwindow - 50);
			//tuyennx_add_start_L2PT-16107
			EventUtil.setEvent("assignSevice_saverv", function(e) {
				var data = e.msg;
				$('#txtTHOIGIANRAVIEN').val(data.PTHOIGIANRAVIEN);
				DlgUtil.close("dlgXuTri");
			});
			if (qd130 == 1){
				$("#divChuyenVien").hide();
			}
			//tuyennx_add_end_L2PT-16107
		}else if(value === "4" || value === "5"){ // hẹn khám tiếp, khám mới
			param['xutri'] = value;
			//ductx -bvtm-5174

			if(opt.hospital_id == '10284'){
				var _hosobenhanid = $('#hidHOSOBENHANID').val();
				if (_hosobenhanid != null && _hosobenhanid != -1){
					param['mhc2'] = "mhc2";
					param['MA_BHYT'] = $("#hidMABHYT").val();
					_showDialog("NGT02K008_Thongtin_Lichkham", param, 'Thông tin lịch hẹn',widthwindow - 100,heightwindow - 50);
				}else{
					DlgUtil.showMsg('Chưa chọn bệnh nhân');
				}
			}else{
				_showDialog("NGT02K008_Thongtin_Lichkham", param, 'Thông tin lịch hẹn',widthwindow - 100,heightwindow - 50);
			}
			if (qd130 == 1){
				$("#divChuyenVien").hide();
			}
			// end bvtm-5174
		}else if(value === "7"){ // chuyển viện
			_showDialog("NGT02K009_Chuyenvien", param, 'Thông tin chuyển viện',widthwindow - 100,heightwindow - 50);
			if (qd130 == 1){
				$("#divChuyenVien").show();
			}
		}else if(value === "8"){ // tử vong
			_showDialog("NGT02K010_Tuvong", param, 'Thông tin tử vong',widthwindow - 100,heightwindow - 50);
			if (qd130 == 1){
				$("#divChuyenVien").hide();
			}
		}else if(value === "2"){ 							// Điều trị ngoại trú
			//ComboUtil.getComboTag("cboKHOA",_sql[4],[],opt.khoaid,{value:0, text:''},"sql");
			$("#divKhoa").css("display","");
			//ComboUtil.getComboTag("cboKHOA","KHOA.DTNGT",[],"",{value:0, text:''},"sql");
			ComboUtil.getComboTag("cboKHOA","KHOA.DTNGT",[], '',{extval: true, value:0, text:''},"sql", "", function(){
				$("#cboKHOA").val($("#hidKHOACHUYENDENID").val());
			});
			if (qd130 == 1){
				$("#divChuyenVien").hide();
			}
		}else if(value === "6"){
			$("#divKhoa").css("display","");// nhập viện
			//ComboUtil.getComboTag("cboKHOA","KHOA.DTNT",[],"",{extval: true, value:0, text:''},"sql");
			ComboUtil.getComboTag("cboKHOA","KHOA.DTNT",[], '',{extval: true, value:0, text:''},"sql", "", function(){
				$("#cboKHOA").val($("#hidKHOACHUYENDENID").val());
			});
			if (qd130 == 1){
				$("#divChuyenVien").hide();
			}
			/*if($("#hidBATBUOCSINHTON").val() == value){
				openSinhTon();
			}*/
		}
	}

	function _capnhatBADTNGT(){
		var hsbaid = $("#hidHOSOBENHANID").val();
		var benhnhanid = $("#hidBENHNHANID").val();

		if(hsbaid == "" || hsbaid == "-1"){
			DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để cập nhật. ");
			return;
		}

		var sql_par=[benhnhanid, hsbaid, opt.phongid];
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02T001.UPDBADTNGT",sql_par.join('$'));

		if(fl == "1"){
			DlgUtil.showMsg("Cập nhật thành công bệnh án Điều trị Ngoại trú cho bệnh nhân " + $("#txtTENBENHNHAN").val());
			$('#toolbarIdbtnSearchDate').click();
		}else if (fl == "-20"){
			DlgUtil.showMsg("Bệnh án này đã đánh dấu điều trị ngoại trú. ");
		}else if (fl == "-21"){
			DlgUtil.showMsg("Bệnh án này không thuộc đợt điều trị cuối cùng, không thể mở lại.");
		}else if (fl == "-22"){
			DlgUtil.showMsg("Đợt điều trị ngoại trú lần này đã đóng. ");
		}else if (fl == "-30"){
			DlgUtil.showMsg("Không thêm mới được dữ liệu vào bảng map. ");
		}else{
			DlgUtil.showMsg("Lỗi cập nhật thông tin. ");
		}
	}

	function _gocapnhatBADTNGT(mode){
		var hsbaid = $("#hidHOSOBENHANID").val();
		var benhnhanid = $("#hidBENHNHANID").val();

		if(hsbaid == "" || hsbaid == "-1"){
			DlgUtil.showMsg("Yêu cầu chọn bệnh nhân để cập nhật. ");
			return;
		}

		var sql_par=[mode, benhnhanid, hsbaid, opt.phongid];
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02T001.REVBADTNGT",sql_par.join('$'));

		if(fl == "1"){
			DlgUtil.showMsg("Gỡ cập nhật thành công bệnh án Điều trị Ngoại trú cho bệnh nhân " + $("#txtTENBENHNHAN").val());
			$('#toolbarIdbtnSearchDate').click();
		}else if (fl == "-20"){
			DlgUtil.showMsg("Bệnh nhân chưa làm bệnh án điều trị ngoại trú ở phòng này. ");
		}else{
			DlgUtil.showMsg("Lỗi cập nhật thông tin. ");
		}
	}
	//START HISL2TK-1085
	function inBangKeLPLSO(_tiepnhanid, _dtbnid, _loaitiepnhanid) {
		var opt = cfObj.VP_IN_TACH_BANGKE;
		var IN_BK_VP = cfObj.c;
		var IN_GOP_BKNTRU = cfObj.VPI_GOP_BANGKENTRU;
		//check quan y 15 cac tram xa in bang ke rieng
		var flagTramxa=jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01.CHECK.TRAMXA",_tiepnhanid);
		if(!IN_BK_VP) IN_BK_VP = 0;
		if(opt==1) {
			var flag = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T004.10",_tiepnhanid);
			if(_loaitiepnhanid == 0) {
				if(IN_GOP_BKNTRU==1){
					inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBBHYTNOITRU_02BV_QD3455_A4');
				}else{
					if(_dtbnid == 1) {
						jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.SINH.STT",_tiepnhanid);
						if(flagTramxa != null && flagTramxa==1){
							inPhoiVP('1', _tiepnhanid, 'NTU001_BKCPKCBBHYTNOITRU_02BV_QD3455_TRAMXA_A4');
						}else{
							inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBBHYTNOITRU_02BV_QD3455_A4');
						}

						if(IN_BK_VP == 0 && flag==1)
							inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBTUTUCNOITRU_02BV_QD3455_A4');
					} else {
						if(flag==1)
							inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBTUTUCNOITRU_02BV_QD3455_A4');
					}
				}
			} else{
				if(_dtbnid == 1) {
					jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.SINH.STT",_tiepnhanid);
					if(flagTramxa != null && flagTramxa==1){
						inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_TRAMXA_A4');
					}else{
						inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_A4');
					}

					if(IN_BK_VP == 0 && flag==1)
						inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NGT035_BKCPKCBTUTUCNGOAITRU_A4');
				} else {
					if(flag==1)
						inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NGT035_BKCPKCBTUTUCNGOAITRU_A4');
				}
			}
		} else {
			jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.SINH.STT",_tiepnhanid);
			if(_loaitiepnhanid == 0) {
				inPhoiVP('1', _tiepnhanid, 'NTU001_BKCPKCBNOITRU_02BV_QD3455_A4');
			} else{
				inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBNGOAITRU_01BV_QD3455_A4');
			}
		}
		// in bang ke hao phi neu co
		var flag_haophi = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T005.11",_tiepnhanid);
		var opt_haophi = cfObj.VP_IN_BANGKE_HAOPHI;
		if(opt_haophi==1){
			if(flag_haophi==1){
				inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCB_HAOPHI_01BV_QD3455_A4');
			}
		}

	}

	function  inPhoiVP(_inbangkechuan,_tiepnhanid, _report_code) {
		var par = [];
		par.push({name : 'inbangkechuan',type : 'String',value : _inbangkechuan.toString()});
		par.push({name : 'tiepnhanid',type : 'String',value : _tiepnhanid.toString()});
		var typeExport = "pdf";//$("#sltInBieuMau").val();
		CommonUtil.openReportGet('window', _report_code, typeExport, par);
	}

	//L2PT-6928 ngocnva start
	function moBenhAnDaiNgay() {
		// xu tri dieu tri ngoai tru
		// kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
		var _par_ins = [$('#hidBENHNHANID').val(), 36, $("#hidKHAMBENHID").val(), "", _opt.phongid, 0];
		var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY1", _par_ins.join('$'));
		if(_return==-1){
			DlgUtil.showMsg('Bệnh chính của bệnh nhân không thuộc nhóm bệnh dài ngày nên không thể mở bệnh án');
		}else if(_return==-2){
			DlgUtil.showMsg('Bệnh nhân đã được mở bệnh án ngoại trú dài ngày, vào menu Bệnh an để cập nhật thông tin');
		}else if(_return==1){
			DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thành công');
		}else if(_return==0){
			DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thất bại');
		}else if(_return==-3){
			DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán bệnh chính');
		}
	}

	function dongBenhAnDaiNgay() {
		// var _par=[];
		// var _hosobenhanid=null;
		// _par=RSUtil.buildParam("",[ $("#hidHOSOBENHANID").val()]);
		// var dataDaingay=jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.CHECK_DAINGAY", _par);
		// _rowsDaingay= JSON.parse(dataDaingay);
		// var _loaibadaingay = -1;
		// if(_rowsDaingay!=null && _rowsDaingay.length>0){
		// 	_loaibadaingay=_rowsDaingay[0].LOAIBENHANID;
		// 	_hosobenhanid = _rowsDaingay[0].HOSOBENHANID;
		// }
		var object = {};
		FormUtil.setFormToObject('divContentHC', '', object);
		var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
		var dataDaingay = {};
		if (dataDaingays && dataDaingays.length > 0) {
			dataDaingay = dataDaingays[0];
		}
		if (!dataDaingay) {
			DlgUtil.showMsg("Bệnh nhân chưa có bệnh án dài ngày!");
			return;
		}
		var _hosobenhanid = dataDaingay.HOSOBENHANID;
		var _loaibadaingay = dataDaingay.LOAIBENHANID;

		if(_loaibadaingay==36){
			DlgUtil.showConfirm("Bạn có chắc chắn muốn đóng bệnh án ngoại trú dài ngày cho bệnh nhân không?",function(flag) {
				if (flag) {
					// xu tri dieu tri ngoai tru
					// kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
					var _par_ins = [ $('#hidHOSOBENHANID').val()];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONG.DAINGAY",_par_ins.join('$'));
					if(_return==1){
						DlgUtil.showMsg('Đóng bệnh án dài ngày thành công');
						_loadGridData(_opt.phongid);
					}else if(_return==0){
						DlgUtil.showMsg('Đóng bệnh án thất bại');
					}
				}
			});
		}else{
			DlgUtil.showMsg('Bệnh nhân chưa được mở bệnh án dài ngày!');
		}
	}

	function moBADNTheoPhong() {
		var object = {};
		FormUtil.setFormToObject('divContentHC', '', object);
		console.log(object);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.BADN.PHONG", JSON.stringify(object));
		switch (Number(result)) {
			case -2:
				DlgUtil.showMsg("Bệnh nhân chưa có đơn thuốc tại phòng khám, vui lòng kê đơn thuốc!");
				break;
			case -3:
				DlgUtil.showMsg("Bệnh nhân chưa có đơn thuốc lớn hơn 7 ngày, vui lòng kê đơn thuốc!");
				break;
			case -4:
				DlgUtil.showMsg("Bệnh nhân đã có BADN tại phòng khám!");
				break;
			case 1:
				DlgUtil.showMsg("Mở BADN tại phòng khám thành công!");
				break;
			case -1:
				DlgUtil.showMsg("Mở BADN không thành công!");
				break;
			default:
				DlgUtil.showMsg("Đã có lỗi xảy ra !");
				break;
		}
	}

	function dongBADNTheoPhong() {
		// var object = {};
		// FormUtil.setFormToObject('divContentHC', '', object);
		// var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
		// dataDaingay = {};
		// if (dataDaingays && dataDaingays.length > 0) {
		// 	dataDaingay = dataDaingays[0];
		// }
		// if (dataDaingays.length == 0) {
		// 	DlgUtil.showMsg("Bệnh nhân chưa mở hoặc đã đóng bệnh án dài ngày!");
		// 	return;
		// }
		$('#dlgDONGBA').append($(htmlDlg));
		var dlgDONGBA_DN = DlgUtil.buildPopup("dlgDONGBA", "dlgDONGBA_DN", "Đóng BA dài ngày", 500, 110, {"zIndex":998});
		DlgUtil.open("dlgDONGBA");
		$("#txtNGAYDONGBA").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		var sql_par=[];
		sql_par.push({"name":"[0]","value": $('#hidBENHNHANID').val()} , {"name":"[2]","value": '1,9' });
		ComboUtil.getComboTag("cboHOSOBENHANDAINGAY_ID",'BAN.DAINGAY.DS1',sql_par, "49",{value:0, text:'--Chọn--'},"sql","","");
		var btnOK = $('#btn_DONGBA_OK');
		$('#btn_MOBA').hide();
		var btnClose = $('#btn_DONGBA_CLOSE');
		btnOK.click(function() {
			if($("#txtNGAYDONGBA").val().trim() == ""){
				DlgUtil.showMsg("Bạn chưa nhập ngày đóng BA!");
				return false;
			}
			var _hosobenhanid = $("#cboHOSOBENHANDAINGAY_ID").val();
			var param = {
				'HOSOBENHANID': _hosobenhanid,
				'NGAYDONGBADN': $("#txtNGAYDONGBA").val(),
				'PHONGKHAMDANGKYID': $('#hidPHONGKHAMDANGKYID').val()
			};
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.BADN.DONG", JSON.stringify(param));
			switch (Number(result)) {
				case -2:
					DlgUtil.showMsg("Bệnh nhân đã được đóng BADN!");
					break;
				case 1:
					DlgUtil.showMsg("Đóng BADN thành công!");
					//dataDaingay = {};
					dlgDONGBA_DN.close();
					check =1;
					break;
				case -1:
					DlgUtil.showMsg("Đóng BADN không thành công!");
					break;
				default:
					DlgUtil.showMsg("Đã có lỗi xảy ra!");
					break;
			}
		});
		btnClose.click(function() {
			dlgDONGBA_DN.close();
		});
//		var _hosobenhanid = dataDaingay.HOSOBENHANID;
//		var param = {
//			'HOSOBENHANID': _hosobenhanid,
//			'PHONGKHAMDANGKYID': $('#hidPHONGKHAMDANGKYID').val()
//		};
//		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.BADN.DONG", JSON.stringify(param));
//		switch (Number(result)) {
//			case -2:
//				DlgUtil.showMsg("Bệnh nhân đã được đóng BADN!");
//				break;
//			case 1:
//				DlgUtil.showMsg("Đóng BADN thành công!");
//				break;
//			case -1:
//				DlgUtil.showMsg("Đóng BADN không thành công!");
//				break;
//			default:
//				DlgUtil.showMsg("Đã có lỗi xảy ra!");
//				break;
//		}
	}
	//L2PT-6928 ngocnva end
	function luuQuanLyBenhAn(signType) {
		if (opt.hospital_id == 10284) {
			//ko xử lý gì
		} else {
			var arrReportCode = [];
			vienphi_tinhtien.inBangKe($("#hidTIEPNHANID").val(), $("#hidDOITUONGBENHNHANID").val(), $("#hidLOAITIEPNHANID").val(), arrReportCode, 0, 0);
			arrReportCode.forEach(function(el) {
				//lưu bảng quản lý phiếu
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", el);
				if (data_ar != null && data_ar.length > 0) {
					var obj = new Object();
					obj.hosobenhanid = $("#hidHOSOBENHANID").val();
					obj.tiepnhanid = $("#hidTIEPNHANID").val();
					obj.rpt_code = el;
					obj.transtype = '10';
					obj.delete = signType;
					jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H028.EV003", JSON.stringify(obj));
				}
			});
			if (arrReportCode.length > 0 && cfObj.NGT_KYSOBANGKE_KTK == '1') {
				if (cfObj.KYSO_BANGKE_NGTRU_NAMDAN == '1') {
                    _caRpt3(1);
                }else{
                    _caRpt1(signType);
                }
			}
		}
	}
	function _caRpt3(signType) {
        var _rptCode = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'RPT_CODE_KYSO_BANGKE_KHAMBENH');
        var _params = [ {
            name: 'hosobenhanid',
            type: 'String',
            value: $("#hidHOSOBENHANID").val()
        }, {
            name : 'tiepnhanid',
            type : 'String',
            value : $("#hidTIEPNHANID").val()
        }, {
            name : 'RPT_CODE',
            type : 'String',
            value : _rptCode
        } ];
        if(signType == '0') {
            CommonUtil.openReportGetCA2(_params, false);
        } else {
            var msg = CommonUtil.kyCASingerPad(_params, signType, true);
            EventUtil.setEvent("eventKyCA",function(e){
                DlgUtil.showMsg(e.res);
            });
		}
	}
	function _caRpt(signType) {
		var _rptCode = cfObj.RPT_CODE_KYSO_BANGKE;
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			var catype = row.CA_TYPE;
			var	kieuky = row.KIEUKY;
			if (catype == '5') {
				//isKyTocken = true;
			} else if (catype == '3' || catype == '6') {

			} else {
				EventUtil.setEvent("dlgCaLogin_confirm", function(e) {
					causer = e.username;
					capassword = e.password;
					DlgUtil.close("divCALOGIN");
					_caRpt2(signType, kieuky, catype);
				});
				EventUtil.setEvent("dlgCaLogin_close", function(e) {
					DlgUtil.close("divCALOGIN");
				});
				var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
				var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
				popup.open("divCALOGIN");
			}
		} else {
			DlgUtil.showMsg('Chưa cấu hình phiếu CA theo rpt_code trong cấu hình RPT_CODE_KYSO_BANGKE!');
			return;
		}
	}
	function _caRpt2(signType, kieuky, catype) {
		arrRptCodeCa.forEach(function (el) {
			var params = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name: 'tiepnhanid',
				type: 'String',
				value: $("#hidTIEPNHANID").val()
			}, {
				name: 'rpt_code',
				type: 'String',
				value: el
			}];
			var oData = {
				sign_type: signType,
				causer: causer,
				capassword: capassword,
				smartcauser: smartcauser,
				params: params
			};
			var msg = CommonUtil.caRpt(oData, el, true, '', false, kieuky, catype);
			DlgUtil.showMsg(msg);
		});
	}
	//END HISL2TK-1085
	function sendNotifyMH2(type){
		if(type == 2){
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.BN.VNCARE",$("#hidBENHNHANID").val()+'$'+$("#hidHOSOBENHANID").val()+'$'+"-1"+'$'+"-1");
			if(data_ar != null && data_ar.length > 0){
				var _objThongtin = new Object();
				_objThongtin["maCSYT"] = opt.hospital_code;
				_objThongtin["tenCSYT"] = opt.hospital_name;
				_objThongtin["maBN"] = data_ar[0].MABENHNHAN;
				_objThongtin["tenBN"] = data_ar[0].TENBENHNHAN;
				_objThongtin["phone"] = data_ar[0].TK_LIENKET;
				_objThongtin["STTBN"] = $("#hidSOTHUTU").val();
				_objThongtin["soPhong"] = _sophongkham == '0'?"":_sophongkham;
				_objThongtin["tenPhong"] = _opt._subdept_name;
				_objThongtin["maLuotKham"] = data_ar[0].MAHOSOBENHAN;
				sendNotify(2,JSON.stringify(_objThongtin));
			}
		}
		if(type == 1){
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.BN.VNCARE",$("#hidBENHNHANID").val()+'$'+$("#hidHOSOBENHANID").val()+'$'+"-1"+'$'+$('#hidPHONGKHAMDANGKYID').val());
			if(data_ar != null && data_ar.length > 0){
				for(var i = 0 ; i < data_ar.length; i++){
					var data = data_ar[i];
					var _objThongtin = new Object();
					_objThongtin["maCSYT"] = opt.hospital_code;
					_objThongtin["tenCSYT"] = opt.hospital_name;
					_objThongtin["maBN"] = data.MABENHNHAN;
					_objThongtin["tenBN"] = data.TENBENHNHAN;
					_objThongtin["phone"] = data.TK_LIENKET;
					_objThongtin["STTHienTai"] = data.STTBN_HT;
					_objThongtin["STTBN"] = data.STTBN_TIEPTHEO;
					_objThongtin["soPhong"] = _sophongkham == '0'?"":_sophongkham;
					_objThongtin["tenPhong"] = _opt._subdept_name;
					_objThongtin["maLuotKham"] = data.MAHOSOBENHAN;
					sendNotify(1,JSON.stringify(_objThongtin));
				}
			}
		}
	}

	function lienketVncare(benhnhanid,taikhoanlienket){
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT04K001.02",benhnhanid);
		if(data_ar != null && data_ar.length > 0){
			var text = '{ "phone":"' + taikhoanlienket
				+ '", "maCSYT":"' + opt.hospital_code
				+ '","maBN":"' + data_ar[0].MABENHNHAN
				+ '","tenBN":"' + data_ar[0].TENBENHNHAN
				+ '","ngaySinh":"' + data_ar[0].NGAYSINH
				+ '","gioiTinh":"' + (data_ar[0].GIOITINHID==1?1:0)
				+ '","quanHe":"'
				+ '","MANGHENGHIEP":"'+data_ar[0].MANGHENGHIEP
				+ '","MADT":"'+data_ar[0].MADANTOC
				+ '","MAQT":"'+data_ar[0].MAQT
				+ '","MATINH":"'+data_ar[0].MATINH
				+ '","MAHUYEN":"'+data_ar[0].MAHUYEN
				+ '","MAXA":"'+data_ar[0].MAXA
				+ '","DIACHI":"'+data_ar[0].DIACHI
				+ '","DIENTHOAI":"'+data_ar[0].SDTBENHNHAN
				+ '","CMT":"'+data_ar[0].CMND
				+ '","SOTHE_BHYT":"'+data_ar[0].MA_BHYT+'"}';
			strReturn = ajaxSvc.PortalWS.lienket_tk_vncare(text,1);

			if(strReturn == "-1" || strReturn == "-2" || strReturn == "-3"){
				DlgUtil.showMsg("Có lỗi xảy ra trong quá trình liên kết tài khoản!");
			}
			else{
				ret = JSON.parse(strReturn);
				if(ret.errorCode == '0'){
					var objData = new Object();
					objData["BENHNHANID"]= benhnhanid;
					objData["TK_LIENKET"]= taikhoanlienket;
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT04K001.UPD", JSON.stringify(objData));
				}
				else{
					DlgUtil.showMsg(ret.errorMessage);
				}
			}
		}
	}

	//ductx -bvtm-5439
	function sendSmsAuto(){
		var sms_auto = cfObj.BVBD_SMS_HTSS_CAMON;
		var sms_qc = cfObj.AUTO_SMS_QC;
		var hsbacao = jsonrpc.AjaxJson.getOneValue("NGT.GETCHKBA", [{"name":"[0]", "value":$('#hidKHAMBENHID').val()}]);
		var makhoa = jsonrpc.AjaxJson.getOneValue("NGT.GETMAKHOA", [{"name":"[0]", "value":opt.phongid}]);
		var dskhoa = [];
		dskhoa = sms_qc.split(',');
		var pos = $('#txtMABENHNHAN').val().indexOf("-");
		var mabn = "";
		if(pos >= 0){
			mabn = $('#txtMABENHNHAN').val().substring(0, pos-1);
		}else{
			mabn = $('#txtMABENHNHAN').val();
		}
		var _par = [mabn];
		var sdo = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CH_SDT',_par.join('$'));
		var sd = typeof sdo == 'undefined' ? '' : sdo;
		if(sd == ''){
			DlgUtil.showMsg("Không gửi được sms do Bệnh nhân thiếu thông tin Số điện thoại!");
			return;
		}
		else if(sd.startsWith("0")){
			sd = "84"+sd.slice(1, sd.length);
		}
		else if(!sd.toString().startsWith("84")){
			sd="84"+sd;
		}
		var sdt = typeof sd == 'undefined' ? '' : sd;

		if((jQuery.inArray( makhoa, dskhoa ) > -1) && hsbacao < 1){
			var sms = new SmsSend();
			var partn = [];
			var time = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI');
			var par=[];
			par.push({"name" : "[0]", "value" :   $('#hidKHAMBENHID').val()});
			var _res1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT.SMS.GETKHOAPHONG", par);
			var datakhoaphong = JSON.parse(_res1);
			var thongtinkhoaphong = {
				ma_khoa: datakhoaphong[0].MA_KHOA,
				ten_khoa: datakhoaphong[0].TEN_KHOA,
				ma_phong: datakhoaphong[0].MA_PHONG,
				ten_phong: datakhoaphong[0].TEN_PHONG
			}
			var kq = sms.sendPostTemplate(sdt, "QC_TBGOC", partn, "", $("#hidKHAMBENHID").val(), "2",thongtinkhoaphong);
			_RESP_CODE = kq.RESP_CODE;
			if(_RESP_CODE == 0){
				DlgUtil.showMsg("Gửi tin nhắn quảng cáo thành công");
			} else {
				DlgUtil.showMsg("Gửi tin nhắn quảng cáo không thành công, mã lỗi:  "+ kq.RESP_CODE);
			}
		}

		if(sms_auto == "1" && (makhoa == 'PKHM1' || makhoa == 'PKHM2' || makhoa == 'PKHM3' || makhoa == 'PKHM4' || makhoa == 'PKHM5')){
			var noidung = jsonrpc.AjaxJson.getOneValue("NGT.GETSMSAUTO", []);
			var sms = new SmsSend();

			var partn = [];
			partn.push(removeVietnameseTones(tenbn));
			var time = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI');
			var par=[];
			par.push({"name" : "[0]", "value" :   $('#hidKHAMBENHID').val()});
			var _res1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT.SMS.GETKHOAPHONG", par);
			var datakhoaphong = JSON.parse(_res1);
			var thongtinkhoaphong = {
				ma_khoa: datakhoaphong[0].MA_KHOA,
				ten_khoa: datakhoaphong[0].TEN_KHOA,
				ma_phong: datakhoaphong[0].MA_PHONG,
				ten_phong: datakhoaphong[0].TEN_PHONG
			}
			var kq = sms.sendPostTemplate(sdt, "CAMON_HT_SS", partn, "", $("#hidKHAMBENHID").val(), "2",thongtinkhoaphong);
			_RESP_CODE = kq.RESP_CODE;
			if(_RESP_CODE == 0){
				DlgUtil.showMsg("Gửi tin nhắn cảm ơn thành công");
			} else {
				DlgUtil.showMsg("Gửi tin nhắn cảm ơn không thành công, mã lỗi:  "+ kq.RESP_CODE);
			}
		}
	}

	function removeVietnameseTones(str) {
		str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g,"a");
		str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g,"e");
		str = str.replace(/ì|í|ị|ỉ|ĩ/g,"i");
		str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g,"o");
		str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g,"u");
		str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g,"y");
		str = str.replace(/đ/g,"d");
		str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
		str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
		str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
		str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
		str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
		str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
		str = str.replace(/Đ/g, "D");
		// Some system encode vietnamese combining accent as individual utf-8 characters
		// Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
		str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
		str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
		// Remove extra spaces
		// Bỏ các khoảng trắng liền nhau
		str = str.replace(/ + /g," ");
		str = str.trim();
		// Remove punctuations
		// Bỏ dấu câu, kí tự đặc biệt
		str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g," ");
		return str;
	}
	//end bvtm-5439

	// Xử lý sự kiện liên quan ký CA => START
	function _kyCaRpt(_params){
		//check ky cap hay khong
		var _rptCode = _params.find(element => element.name == 'RPT_CODE')['value'];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			var loaiky = row.LOAIKY;
		}
		if(loaiky == '1') {
			CommonUtil.kyCA(_params, '', '', '', '', '1');
		} else {
			CommonUtil.kyCA(_params);
		}
		EventUtil.setEvent("eventKyCA",function(e){
			DlgUtil.showMsg(e.res);
		});
	}
	function check_doituongbn(){
		var pars = [ 'CHECK_DTBN_CDDV' ];
		var data_ch = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
		if(data_ch == '1'){
			var par = [];
			par.push({"name":"[0]","value": $("#hidTIEPNHANID").val()});
			var result = jsonrpc.AjaxJson.getOneValue("NTU02D080.L01", par);

			if(result != $("#hidDOITUONGBENHNHANID").val()){
				return -1;
			}
		}
	}
	function save_log_act_form(lcode, lfunc, ltext, lkey) {
		var objLogActForm = new Object();
		objLogActForm.LCODE = lcode;
		objLogActForm.LFUNC = lfunc;
		objLogActForm.LTEXT = ltext;
		objLogActForm.LKEY = lkey;
		var _result_log = jsonrpc.AjaxJson.ajaxCALL_SP_S('LOG.ACT.FORM', objLogActForm);
		if (_result_log != '1' && _result_log != '2') {
			DlgUtil.showMsg("Cập nhật log thao tác không thành công: (" + _result_log + ")");
		}
	}
}
function checkCanNang(evt, ele) {
	var theEvent = evt || window.event;
	var key = theEvent.keyCode || theEvent.which;
	key = String.fromCharCode( key );
	var value = ele.value + key;
	var regex = /^\d+(.\d{0,2})?$/;
	if( !regex.test(value) ) {
		theEvent.returnValue = false;
		if(theEvent.preventDefault) theEvent.preventDefault();
	}
}


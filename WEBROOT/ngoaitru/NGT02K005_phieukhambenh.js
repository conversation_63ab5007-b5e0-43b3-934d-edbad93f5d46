function NGT02K005_phi<PERSON><PERSON><PERSON><PERSON>(opt) {
    this.load = doLoad;
    var _colBENH = "MÃ BỆNH,ICD10CODE,20,0,f,l;TÊN BỆNH,ICD10NAME,60,0,f,l";
    var _sql = ["NGT05.XUTRI", "DV.BHYT.001", "NGTDV.002", "NT.008", "NGT.KHOA", "NGT01T001.LSDT", "NT.KHOA"];
    var _objData;
    var isLuu = false;
    var _tatpopupravien = "0";
    var _xutrikhambenhid = 0;
    var _tudonginbangke = 0;
    var _badaingay = "0";
    var _chandoanchuyenvien = "";
    var _luu = 0;
    var _loadFirstICD = false; 						// load ICD lan dau; 	
    var _loadFirstICDYHCT = false; 						// load ICD lan dau;	
    var _hienthiloaiba = "0";
    var _loaibamacdinh = "0";
    var _hienthisvv = "0";
    var _hienthikqdt = "0";
    var _batbuockqdt = "0";
    var _anbutton = "0";

    var load_icd_yhct_sl = false;//L2PT-9446 load icd yhct
    //dannd_kyso_BVTM
    var causer = '';
    var capassword = '';
    var isKyCa = false;
    var cfObj = new Object(); //L2PT-28890
    var checktrungtgian = 0;
    var checktgianxtri2bn = 0;
    var checktien = 0;
    var checkphongdangkham = 0;
    var checkphieuchuacokq = 0;
    var thoigianmopopup;
    var check_kqcls = 0;
    var showyhct4750 = '0';
    var check_tgksk = 0;
    function doLoad() {
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        $('#txtMACHANDOANRAVIEN').focus();
        $('#btnInPhieu').attr("disabled", true);
        $('#btnInPhieuDoc').attr("disabled", true);
        var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NGT_CANHBAO_VUOT_ICD;NGT_CHECKXUTRI_MOBADN;XUTRI_SHOW_LOAIDTRI;HIS_KTBA_CHECK_KYCA;HIS_CHANXUTRI_NHOHONTGHIENTAI;HIS_CHANXUTRI_SUATHOIGIAN;QD_4750;HIS_SHOWLANHDAO;NGT_CHECKTRUNGTGIAN_NHAPVIEN;NGT_XUTRI_CHECKTRUNGTGIAN;NGT_CHECK_TGIANXUTRI;" +
            "NGT_KQDIEUTRI_MACDINH;NGT_NHAP_KQDT_BB;NGT_NHAP_SOVAOVIEN;KBH_SINHTON_BATBUOC_XUTRI;NGT_NHAP_KQDT;HIS_KHONGCHON_BA;NGT_GHICHU_BENHCHINH;NGT_HIENTHI_NGAYDIEUTRI;NGT_CHINHSUABENHCHINH;HIS_SHOW_ICDYHCT;NGT_MABENHAN_NDH;HIS_SUDUNG_DOITUONG_KHAIBAO;NGT_XT_ANBUTTON;NTU_BAC_LOAD_YHCT_SELECT;NGT_DOIVITRI_KQDT_XT;HIS_TUDONG_IN_BANGKE;" +
            "NGT_XUTRI_INGOP_DT;NGT_INDON_SAUHENKHAM;XUTRI_NHAPVIEN_POPUP;NGT_HIENTHI_BNGIUONG;NGT_MOPOPUPHENKHAM_XUTRI14;HIS_QD130;IN_GOPPHIEU_NV_1TAB;IN_GOPPHIEU_NV;XUTRI_KETHUOC_BHYT;NGT_CANHBAO_KHOANHI;NGT_HENKHAM_KTK;NGT_CHUYENVIEN_KTK;NGT_PKBVV_CAPCUU;NGT_LUUQUANLYBENHAN;HIS_KETTHUC_KHAM_KHI_XUTRI;HIS_SUDUNG_KYSO_KYDIENTU;" +
            "KHAM_ONLINE_WS;NGT_XTDISABLE_LUU;DTDT_DAY_DONTHUOC;HIS_TIMEOUT_THONGBAO;VALID_XUTRI;NGT_CHECK_THONGTINNGUOINHA;NGT_SINHTON_CANNANG;NGT_CHECK_VUOTCANBHYT;NGT_XUTRI_CHECKMBPBHYTCHUACOKQ;NGT_XUTRI_CHECKPHONGCHUAKT;NGT_CHECK_TG_KHAM_CLS;HIS_CANHBAO_KHAM5P;HIS_CANHBAO_KHAM5P_TGBATDAU1;HIS_CANHBAO_KHAM5P_TGBATDAU;HIS_CHAN_SAI_TYLE;" +
            "VPI_AUTO_DUYET_BHYT_NGT;NGT_INGIAYHENKHAM_KHIXUTRI;NGT_HIENTHI_RPT;NGT_KB_MHC_TEMP_KB_NV;NGT_INGIAYRAVIEN_KHIXUTRI;SO_NGAY_XUTRI_DIEUTRI;NGT_KIEMTRA_THOIGIAN_XUTRI;NBN_XTCVCCNHHKTD;NGT_CAPTOACHOVE;NGT_CANHBAO_DONGTIEN;HIS_CANHBAO_BNTUVONG;CHECK65BHYT_XUTRI;CHECK65BHYT;NTU_KTBA_CHECK_KQCLS;HIS_DSXUTRI_TUDONG_INBANGKE;" +
            "NGT_XUTRI_CHECKBSKHAM;NGT_KTBA_CHECKTGTOIDA;KYSO_BANGKE_NGTRU_NAMDAN;NGT_MAPHONGKCHECKTG_BNKSK;NGT_DUYETKETOAN_KTKHAM_BNHYT;NGT_XUTRI_CHECKDVBHYT;HIS_KHONGXOALICHHEN;NGT_SHOW_CHONPPDIEUTRI;HIS_CHECKMALOAIKCB_7;HIS_CHECKMALOAIKCB_10;HIS_KTBA_CHECK_XN_NULL;" + //L2PT-102859
			"NGT_CHECK_TG_KHAM_CLS;NGT_LOAIBA_XT;NHAPVIEN_BN_DICHVU;NGT_KHONGBATBUOCICD_XUTRI;NGT_CHANXUTRI_BADN;HIS_KHOA_SOLIEU;NGT_TUDONG_LOAI_BANGKE;NGT_KYSOBANGKE_KTK;HIS_TUDONG_GUICONGBMTE;NGT_GUITNHENKHAM_BADN;HIS_TUDONG_IN_PHIEUKBNGT;NGT_IN_PHIEUXUTRI;NGT_INDONTHUOC_SAUXUTRI;APPBN_KTK_XUTRIDAYDL;VP_DUYET_BH_KHI_DUYET_KETOAN;NGT_DUYETKETOAN_KTKHAM;" +
            "CHECK65BN_CHECKALL_DOITUONG;HIS_FORMAT_LYDOVNT;HIS_GIOIHANKYTU_LYDOVNT;HIS_CANHBAO_KHAM5P_TL;VALID_SINHTON_XUTRI;NGT_XUTRI_CHECKDVCHUAHOANTHANH;SOLUONG_ICD_TRONGMOTLANDIEUTRI;NGT_BATBUOC_LANHDAO;HIS_TUDONG_IN_PHIEU_CV;HIS_TUDONG_INPHIEUKBVV_DTNGT;HIS_TUDONG_IN_PHIEU_NV;QD_3176");//L2PT-114286
        if (config_ar != null && config_ar.length > 0) {
            cfObj = config_ar[0];
        } else {
            DlgUtil.showMsg("Không tải được thông tin cấu hình sử dụng trên màn hình");
            return;
        }
        if (cfObj.NGT_DOIVITRI_KQDT_XT == "0") {
            $("#dvKQDTXT1").remove();
        } else {
            $("#dvKQDTXT0").remove();
        }
        if (cfObj.NTU_BAC_LOAD_YHCT_SELECT == 1) {
            load_icd_yhct_sl = true;
        }
        if (cfObj.QD_4750 == 1) {
            $("#dvPPDIEUTRI").show();
             $("#lblPPDIEUTRI").addClass('required');
        }
        // sondn L2PT-7282
        _anbutton = cfObj.NGT_XT_ANBUTTON;
        if (_anbutton != "0") {
            var _controlArr = _anbutton.split('@');
            for (i = 0; i < _controlArr.length; i++) {
                $("#" + _controlArr[i]).hide();
            }
        }
        // end sondn L2PT-7282


        // load thông tin hành chính
        // $('#').({
        //     benhnhanId: opt.benhnhanId
        var data_arr_TTBN = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.BENHNHAN", opt.benhnhanId);
        if (data_arr_TTBN != null && data_arr_TTBN.length > 0) {
            var row = data_arr_TTBN[0];
            FormUtil.setObjectToForm("dvTTBN", "", row);
            $("#cboNGHENGHIEPID").empty();
            var optionText = '<option value="' + row.NGHENGHIEPID + '">' + row.TENNGHENGHIEP + '</option>';
            $("#cboNGHENGHIEPID").append(optionText);
            $("#cboDANTOCID").empty();
            var optionText = '<option value="' + row.DANTOCID + '">' + row.TEN_DANTOC + '</option>';
            $("#cboDANTOCID").append(optionText);
            $("#cboQUOCGIAID").empty();
            var optionText = '<option value="' + row.QUOCGIAID + '">' + row.TENDIAPHUONG + '</option>';
            $("#cboQUOCGIAID").append(optionText);
            if (!row.DIABANID)
                data_arr_TTBN = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DIACHI", row.DIAPHUONGID);
            else {
                data_arr_TTBN = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DIACHI", row.DIABANID);
            }
            if (data_arr_TTBN != null && data_arr_TTBN.length > 0) {
                var dc = data_arr_TTBN[0];
                FormUtil.setObjectToForm("dvTTBN", "", dc);
            }
        }

        // sondn L2PT-29895
        if (cfObj.NGT_MABENHAN_NDH == "1") {
            $("#divMABENHANDAINGAY").show();
        }
        // end sondn L2PT-29895
        if (cfObj.NGT_SHOW_CHONPPDIEUTRI == "1") {
            $("#dvCHONPPDIEUTRI").show();
            $("#dvNHAPPPDIEUTRI").removeClass("col-md-10");
            $("#dvNHAPPPDIEUTRI").addClass("col-md-9");
        }
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": "58"});
        ComboUtil.getComboTag("cboBHYT_LOAIID", _sql[1], [], "", "", 'sql', "", "");
        ComboUtil.getComboTag("cboDICHVUID", _sql[2], [], "", "", 'sql', "", "");
        if (cfObj.HIS_SUDUNG_DOITUONG_KHAIBAO != 1) {
            ComboUtil.getComboTag("cboDOITUONGBENHNHANID", "NT.007", sql_par, "", {}, 'sql', "", "");
        } else {
            ComboUtil.getComboTag("cboDOITUONGBENHNHANID", "NT.007.01", sql_par, "", {}, 'sql', "", "");
        }

        //ComboUtil.getComboTag("cboKHOA",_sql[4],[],"",{value:0, text:''},"sql","","");

        // text change CĐ sơ bộ
        ComboUtil.initComboGrid("txtMACHANDOANBANDAU", _sql[3], [], "500px", _colBENH,
            "txtMACHANDOANBANDAU=ICD10CODE,txtCHANDOANBANDAU=ICD10NAME,hidMACHANDOANBANDAU=ICD10CODE");

        // text change bệnh chính
        //ComboUtil.initComboGrid("txtMACHANDOANRAVIEN",_sql[3],[],"500px", _colBENH, "txtMACHANDOANRAVIEN=ICD10CODE,txtCHANDOANRAVIEN=ICD10CODE:ICD10NAME,hidMACHANDOANRAVIEN=ICD10CODE");
        //tuyennx_edit_start_20170822 yc HISL2BVDKHN-368
//		var check = 0;

        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K005_PKBCT1", opt.khambenhid + '$' + opt.phongid + '$' + opt.hosobenhanid);
        if (load_icd_yhct_sl) {
            _sql[3] = 'NT.008.YHCTV3';
            _colBENH = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
        }
        var sql_par_p = [];
        sql_par_p.push({"name": "[0]", value: opt.phongid});
        var check_p = jsonrpc.AjaxJson.getOneValue('CHECK.PHONGYHCT', sql_par_p);
        if (cfObj.HIS_SHOW_ICDYHCT == '1' && check_p != '0') {
            showyhct4750 = '1';
            $("#divICDYHTC").show();
            _sql[3] = 'NT.008.YHCTV4';
            _colBENH = 'Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l';
            ComboUtil.initComboGrid("txtMACHANDOANRAVIEN", _sql[3], [], "900px", _colBENH, function (event, ui) {
                var _ui = ui.item;
                // dannd_L2PT-97104
                var icdcheck;
                if (_loadFirstICD == false) {
                    icdcheck = _ui.ICD10CODE;
                } else {
                    if (data_ar != null && data_ar.length > 0 && data_ar[0].MACHANDOANRAVIEN != '') {
                        icdcheck = data_ar[0].MACHANDOANRAVIEN;
                    } else {
                        icdcheck = _ui.ICD10CODE;
                    }
                }
                var check = _checkCBBN(icdcheck, "1");
                if (check == 1)
                    return false;

                var str = $("#txtCHANDOANRAVIEN_KHAC").val();
                if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
                    DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
                    return false;
                }
                if (_loadFirstICD == false) {
                    $("#hidMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
                    $("#txtMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
                    $("#txtCHANDOANRAVIEN").val(ui.item.ICD10NAME);
                    $("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
                    $("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
                } else {
                    if (data_ar != null && data_ar.length > 0 && data_ar[0].MACHANDOANRAVIEN != '') {
                        _loadFirstICD = false; 							// lan dau load co du lieu: khong load lai nua
                    } else {
                        $("#hidMACHANDOANRAVIEN").val(ui.item.ICD10CODE);		// lan dau load k co du lieu: cho phep lay tu do;
                        $("#txtMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
                        $("#txtCHANDOANRAVIEN").val(ui.item.ICD10NAME);
                        $("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
                        $("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
                        _loadFirstICD = false;
                    }
                }

                return false;
            });
            ComboUtil.init("txtMACHANDOANRAVIEN_KHAC", _sql[3], [], "900px", _colBENH, function (event, ui) {
                if (ui.item.ICD10CODE == $("#txtMACHANDOANRAVIEN").val()) {
                    DlgUtil.showMsg("Bệnh kèm theo vừa nhập không được trùng với bệnh chính.");
                    return false;
                }
                var str = $("#txtCHANDOANRAVIEN_KHAC").val();
                if (str.indexOf(ui.item.ICD10CODE + '-') > -1) {
                    DlgUtil.showMsg("Bệnh kèm theo đã được nhập");
                    return false;
                }

                if (str != '')
                    str += ";";
                $("#txtCHANDOANRAVIEN_KHAC").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);

                var stryhct = $("#txtCHANDOANYHCTPHU").val();
                if (ui.item.YHCTCODE) {
                    if (stryhct != '')
                        stryhct += ";";
                    $("#txtCHANDOANYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
                }

                return false;
            });
            ComboUtil.initComboGrid("txtMACHANDOANYHCT", _sql[3], [], "900px", _colBENH, function (event, ui) {
                var _ui = ui.item;
                // dannd_L2PT-97104
                var icdcheck;
                if (_loadFirstICD == false) {
                    icdcheck = _ui.ICD10CODE;
                } else {
                    if (data_ar != null && data_ar.length > 0 && data_ar[0].MACHANDOANRAVIEN != '') {
                        icdcheck = data_ar[0].MACHANDOANRAVIEN;
                    } else {
                        icdcheck = _ui.ICD10CODE;
                    }
                }
                var check = _checkCBBN(icdcheck, "1");
                if (check == 1)
                    return false;
                var str = $("#txtCHANDOANYHCTPHU").val();
                if (str.indexOf(_ui.YHCTCODE + "-") > -1) {
                    DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
                    return false;
                }
                if (_loadFirstICDYHCT == false) {
                    $("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
                    $("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
                    $("#hidMACHANDOANRAVIEN").val(_ui.ICD10CODE);
                    $("#txtMACHANDOANRAVIEN").val(_ui.ICD10CODE);
                    $("#txtCHANDOANRAVIEN").val(_ui.ICD10NAME);

                } else {
                    if (data_ar != null && data_ar.length > 0 && data_ar[0].MACHANDOANYHCT != '') {
                        _loadFirstICDYHCT = false; 							// lan dau load co du lieu: khong load lai nua
                    } else {
                        $("#txtMACHANDOANYHCT").val(_ui.YHCTCODE);
                        $("#txtCHANDOANYHCT").val(_ui.YHCTNAME);
                        $("#hidMACHANDOANRAVIEN").val(_ui.ICD10CODE);
                        $("#txtMACHANDOANRAVIEN").val(_ui.ICD10CODE);
                        $("#txtCHANDOANRAVIEN").val(_ui.ICD10NAME);
                        _loadFirstICDYHCT = false;
                    }
                }
                return false;
            });
            ComboUtil.init("txtTKMACHANDOANYHCTPHU", _sql[3], [], "900px", _colBENH, function (event, ui) {
                if (ui.item.YHCTCODE == $("#cboMACHANDOANYHCT").val()) {
                    DlgUtil.showMsg("Bệnh kèm theo vừa nhập không được trùng với bệnh chính.");
                    return false;
                }
                var stryhct = $("#txtCHANDOANYHCTPHU").val();
                if (stryhct.indexOf(ui.item.YHCTCODE + '-') > -1) {
                    DlgUtil.showMsg("Bệnh kèm theo đã được nhập");
                    return false;
                }
                if (ui.item.YHCTCODE) {
                    if (stryhct != '')
                        stryhct += ";";
                    $("#txtCHANDOANYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
                }
                var str = $("#txtCHANDOANRAVIEN_KHAC").val();
                if (str != '')
                    str += ";";
                $("#txtCHANDOANRAVIEN_KHAC").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
                return false;
            });
        } else {
            ComboUtil.initComboGrid("txtMACHANDOANRAVIEN", _sql[3], [], "500px", _colBENH, function (event, ui) {
                // dannd_L2PT-97104
                var icdcheck;
                if (_loadFirstICD == false) {
                    icdcheck = ui.item.ICD10CODE;
                } else {
                    if (data_ar != null && data_ar.length > 0 && data_ar[0].MACHANDOANRAVIEN != '') {
                        icdcheck = data_ar[0].MACHANDOANRAVIEN;
                    } else {
                        icdcheck = ui.item.ICD10CODE;
                    }
                }
                var check = _checkCBBN(icdcheck, "1");
                if (check == 1)
                    return false;

                var str = $("#txtCHANDOANRAVIEN_KHAC").val();
                if (str.indexOf(ui.item.ICD10CODE + '-') > -1) {
                    DlgUtil.showMsg("Bệnh chính vừa nhập không được trùng với bệnh kèm theo.");
                    return false;
                }
                if (_loadFirstICD == false) {
                    $("#hidMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
                    $("#txtMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
                    $("#txtCHANDOANRAVIEN").val(ui.item.ICD10NAME);
                    if (load_icd_yhct_sl) {
                        $("#txtMA_CHANDOAN_RA_CHINH_YHCT").val(ui.item.YHCTCODE);
                        $("#txtTEN_CHANDOAN_RA_CHINH_YHCT").val(ui.item.YHCTNAME + ' [' + ui.item.ICD10NAME + '] ');
                    }
                } else {
                    if (data_ar != null && data_ar.length > 0 && data_ar[0].MACHANDOANRAVIEN != '') {
                        _loadFirstICD = false; 							// lan dau load co du lieu: khong load lai nua
                    } else {
                        $("#hidMACHANDOANRAVIEN").val(ui.item.ICD10CODE);		// lan dau load k co du lieu: cho phep lay tu do; 
                        $("#txtMACHANDOANRAVIEN").val(ui.item.ICD10CODE);
                        $("#txtCHANDOANRAVIEN").val(ui.item.ICD10NAME);
                        _loadFirstICD = false;
                    }

                }
                return false;
            });
            ComboUtil.initComboGrid("txtMACHANDOANRAVIEN_KHAC", _sql[3], [], "500px", _colBENH, function (event, ui) {
                var check = _checkCBBN(ui.item.ICD10CODE, "0");
                if (check == 1)
                    return false;
                if (ui.item.ICD10CODE == $('#txtMACHANDOANRAVIEN').val() && $('#txtMACHANDOANRAVIEN').val() != "") {
                    DlgUtil.showMsg("Bệnh kèm theo vừa nhập không được trùng với bệnh chính.");
                    return false;
                }
                var str = $("#txtCHANDOANRAVIEN_KHAC").val();
                if (str.indexOf(ui.item.ICD10CODE + '-') > -1) {
                    DlgUtil.showMsg("Bệnh kèm theo đã được nhập.");
                    return false;
                }
                var _par = [opt.khambenhid, opt.phongid, ui.item.ICD10CODE, "1"];
                var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.ICD.TR", _par.join('$'));
                if (resultCheck == '0') {
                    DlgUtil.showMsg("Đã tồn tại mã bệnh kèm theo trùng với phòng khám khác");
                    return false;
                }
                if (str != '')
                    str += ";";
                $("#txtCHANDOANRAVIEN_KHAC").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
                $("#txtMACHANDOANRAVIEN_KHAC").val("");
                if (load_icd_yhct_sl) {
                    var str1 = $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").val();
                    if (str1 != '')
                        str1 += ";";
                    $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").val(str1 + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME + '[' + ui.item.ICD10NAME + ']');
                }
                return false;
            });
        }
        //tuyennx_edit_end_20170822 yc HISL2BVDKHN-368

        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            FormUtil.setObjectToForm("khambenh", "", row);

            //thaiph - L2PT-4340
            var checkBC = cfObj.NGT_CHINHSUABENHCHINH;
            if (checkBC == '1' && row.DOITUONGBENHNHANID == '2') {
                $("#txtCHANDOANRAVIEN").prop('disabled', false);
            }
            if (checkBC == '2') {
                $("#txtCHANDOANRAVIEN").prop('disabled', false);
            }
            if (checkBC == '0') {
                $("#txtCHANDOANRAVIEN").prop('disabled', true);
            }
            //end thaiph - L2PT-4340

            _tudonginbangke = row.TUDONGINBANGKE;
            if (row.MABENHNHAN.trim() !== opt.mabenhnhan.trim()) {
                EventUtil.raiseEvent("assignSevice_khacbnxutri");
            }
            $("#txtMAHOSOBENHAN").text(row.MAHOSOBENHAN);
            $("#hidMAHOSOBENHAN").text(row.MAHOSOBENHAN); //day congboyte
            if (row.XUTRIKHAMBENHID == "" || row.XUTRIKHAMBENHID == "0") {
                _xutrikhambenhid = row.XTMACDINH;
            } else {
                _xutrikhambenhid = row.XUTRIKHAMBENHID;
            }
            _tatpopupravien = row.TAT_POPUP_TTRAVIEN;
            _setXutri(_xutrikhambenhid);

            _loadFirstICD = true;
            _loadFirstICDYHCT = true;

            // sondn L2PT-29895
            if (row.MABENHANDAINGAY.length == 0) {
                $("#txtMABENHANDAINGAY").prop("disabled", false);
            }
            // end sondn L2PT-29895
            if (load_icd_yhct_sl == true) {
                $("#txtTEN_CHANDOAN_RA_CHINH_YHCT").show();
                $("#txtCHANDOANRAVIEN").hide();
                $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").show();
                $("#txtCHANDOANRAVIEN_KHAC").hide();
                $("#txtMA_CHANDOAN_RA_CHINH_YHCT").val(row.MA_CHANDOAN_RA_CHINH_YHCT);
                $("#txtTEN_CHANDOAN_RA_CHINH_YHCT").val(row.TEN_CHANDOAN_RA_CHINH_YHCT);
                $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").val(row.TEN_CHANDOAN_RA_KEMTHEO_YHCT);
            }
        } else {
            EventUtil.raiseEvent("assignSevice_khacbnxutri");
            _loadFirstICD = true;
            _loadFirstICDYHCT = true;
        }
        if (cfObj.NGT_MAPHONGKCHECKTG_BNKSK != '0') {
            var maphong = jsonrpc.AjaxJson.getOneValue("NGT.GETMAKHOA", [{"name":"[0]", "value": opt.phongid}]);
            if (cfObj.NGT_MAPHONGKCHECKTG_BNKSK.includes(maphong))  {
                check_tgksk = 1;
            }
        }
        //tuyennx_add_start_20201010 L2PT-28325
        if (cfObj.NGT_HIENTHI_NGAYDIEUTRI == 1) {
            $("#divNGAYDIEUTRI").show();
            if (!$("#txtNGAYKETLUAN").val())
                $("#txtNGAYKETLUAN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
            var _sql_par = [];
            _sql_par.push({
                "name": "[0]",
                value: $("#hidKHAMBENHID").val()
            });
            var rs = jsonrpc.AjaxJson.getOneValue("GET.NGAYKT.MAX", _sql_par);
            if (rs && rs != 'null' && !$("#txtSONGAYDIEUTRI").val())
                $("#txtSONGAYDIEUTRI").val(rs);
        } else {
            $("#divNGAYDIEUTRI").hide();
        }
        //tuyennx_add_end_20201010 L2PT-28325

        if ($("#txtTHOIGIANRAVIEN").val() == "") {
            $("#txtTHOIGIANRAVIEN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI'));
        }

        _loadLS();

        ComboUtil.getComboTag("cboXUTRIKHAMBENHID", _sql[0], [], _xutrikhambenhid, {
            extval: true,
            value: 0,
            text: 'Chọn xử trí'
        }, "sql", "", "");
        $("#txtMAXUTRIKHAMBENHID").val(_xutrikhambenhid);
        $("#cboXUTRIKHAMBENHID").change();
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_XUTRI_FILL_PPDT') == '1') {
        	$('#txtKHAMBENH_PPDIEUTRI').val($('#cboXUTRIKHAMBENHID option:selected').text().split('-')[1]);//L2PT-110111
        }

        if ($("#hidXUTRIKHAMBENHID").val() != 0) {
            $('#btnShowPopUp').attr("disabled", false);

            // nhập viện, điều trị ngoại trú
            if ($("#hidXUTRIKHAMBENHID").val() === "2" || $("#hidXUTRIKHAMBENHID").val() === "6") {
                $("#divKhoa").css("display", "");
                //L2PT-63895
                if (cfObj.XUTRI_SHOW_LOAIDTRI == '1')
                    $("#divLOAIDTRI").css("display", "");
                $('#btnInPhieu').attr("disabled", true);
                $('#btnInPhieuDoc').attr("disabled", true);
                $('#btnShowPopUp').attr("disabled", true);
            }
        } else {
            $('#btnShowPopUp').attr("disabled", true);
        }

        //tuyennx_add_start_20170724 check an hien nut sua ghi chu benh chinh
        if (cfObj.NGT_GHICHU_BENHCHINH == '1') {
            $("#divBc").addClass("col-md-8");
            $('#divSuaBc').css('display', '');
        }
        //tuyennx_add_end_20170724 

        //tuyennx_add_start_20170802
        ComboUtil.getComboTag("cboKHOA", "KHOA.DTNT", [], "", {
            extval: true,
            value: 0,
            text: '--Chọn khoa--'
        }, "sql", "", "");
        $("#cboKHOA").val(data_ar[0].KHOA);
        //tuyennx_add_end_20170802

        _hienthiloaiba = cfObj.HIS_KHONGCHON_BA;
        _hienthiloaiba == "1" && data_ar[0].XUTRIKHAMBENHID == "6" ? $("#divLOAIBA").show() : $("#divLOAIBA").hide();

        _hienthisvv = cfObj.NGT_NHAP_SOVAOVIEN;
        _hienthisvv == "1" && data_ar[0].XUTRIKHAMBENHID == "6" ? $("#dvSOVAOVIEN").show() : $("#dvSOVAOVIEN").hide();

        _hienthikqdt = cfObj.NGT_NHAP_KQDT;

        $("#hidBATBUOCSINHTON").val(cfObj.KBH_SINHTON_BATBUOC_XUTRI);

        ComboUtil.getComboTag("cboLOAIBA", "PKB_LOAI_BA", [], "", {
            extval: true,
            value: 0,
            text: 'Chọn bệnh án'
        }, "sql", "", function () {
            $("#cboLOAIBA").val(data_ar[0].LOAIBA);
        });

        if (_hienthikqdt != "0") {
            $("#divKETQUADIEUTRI").show();
            _batbuockqdt = cfObj.NGT_NHAP_KQDT_BB;
        } else {
            $("#divKETQUADIEUTRI").hide();
            _batbuockqdt = "0";
        }
        if (cfObj.QD_4750 == '1') {
            $('#divLANHDAO').show();
            ComboUtil.getComboTag("cboLANHDAOVIENID", "DMC.GETLANHDAOBV", [{
                "name": "[0]",
                "value": "0"
            }], "", {value: 0, text: 'Chọn'}, "sql", "", function () {
                if (data_ar[0].LANHDAOVIENID != "0" && data_ar[0].LANHDAOVIENID != "null" && data_ar[0].LANHDAOVIENID != null) {
                    $("#cboLANHDAOVIENID").val(data_ar[0].LANHDAOVIENID);
                }
            });
            ComboUtil.getComboTag("cboLANHDAOKHOAID", "DMC.GETLANHDAOKHOA", [{
                "name": "[1]",
                "value": opt.khoaid
            }], "", {value: 0, text: 'Chọn'}, "sql", "", function () {
                if (data_ar[0].LANHDAOKHOAID != "0" && data_ar[0].LANHDAOKHOAID != "null" && data_ar[0].LANHDAOKHOAID != null) {
                    $("#cboLANHDAOKHOAID").val(data_ar[0].LANHDAOKHOAID);
                }
            });
        }

        ComboUtil.getComboTag("cboKETQUADIEUTRIID", "NT.0010_1", [{"name": "[0]", "value": "6"}], "", {
            value: '0',
            text: '--Chọn--'
        }, "sql", "", function () {
        	//L2PT-114286
            if (cfObj.QD_3176 == '1') {
            	$("#cboKETQUADIEUTRIID option[value=5]").text("Tử vong tại cơ sở KBCB"); 
            }
            if (data_ar[0].KETQUADIEUTRIID != "0") {
                _kqdtid = data_ar[0].KETQUADIEUTRIID;
            } else {
                var _kqdt = $('#cboXUTRIKHAMBENHID option:selected').attr('extval0');
                if (_kqdt != '-1' && _kqdt != undefined) {
                    _kqdtid = _kqdt;
                } else {
                    if (_hienthikqdt != "0") {
                        if (_hienthikqdt == "-1") {
                            _kqdtid = "0";
                        } else {
                            _kqdtid = _hienthikqdt;
                        }
                    } else {
                        //_hienthikqdt = "1";
                        _kqdtid = "1";              // khoi benh;
                    }
                }
            }
            $("#cboKETQUADIEUTRIID").val(_kqdtid);
            $('#txtTKKETQUADIEUTRI').val(_kqdtid);
            var _kqdt_macdinh = cfObj.NGT_KQDIEUTRI_MACDINH;
            if (_kqdt_macdinh != '0') {
                $("#cboKETQUADIEUTRIID").val(_kqdt_macdinh);
                $('#txtTKKETQUADIEUTRI').val(_kqdt_macdinh);
            }
          
        });

        // SONDN 20191009 L2PT-9604
        // END SONDN 20191009 L2PT-9604
        thoigianmopopup = $('#txtTHOIGIANRAVIEN').val();
        if(cfObj.NGT_BATBUOC_LANHDAO == '1') {
            $('#lblLANHDAOKHOA').addClass('required');
            $('#lblLANHDAOVIEN').addClass('required');
        }
        if (cfObj.HIS_KHONGXOALICHHEN == 1) {
            $('#btnHENKHAM').show();
        }
    }

    function _bindEvent() {
        //dannd_kyso_BVTM
        $('document').ready(function () {
            if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == "1") {
                $("#btnKyCa").show();
                $("#btnHuyCa").show();
                $("#btnInKySo").show();
                //check ky CA
                var _rptCode = '';
                var _par = [{
                    name: 'hosobenhanid',
                    type: 'String',
                    value: $("#hidHOSOBENHANID").val()
                }, {
                    name: 'khambenhid',
                    type: 'String',
                    value: $("#hidKHAMBENHID").val()
                }];
                if ($('#cboXUTRIKHAMBENHID').val() == '6') {
                    _rptCode = 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4';
                } else if ($('#cboXUTRIKHAMBENHID').val() == '7') {
                    _rptCode = 'NGT003_GIAYCHUYENTUYEN_TT14_A4';
                } else if ($('#cboXUTRIKHAMBENHID').val() == '5') {
                    _rptCode = 'NGT014_GIAYHENKHAMLAI_TT402015_A4';
                } else if ($('#cboXUTRIKHAMBENHID').val() == '2') {
                    _rptCode = 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4';
                }else {
                    _rptCode = 'NGT005_PHIEUKHAMBENHNGOAITRU_A4_10284';
                }
                _par.push({
                    name: 'RPT_CODE',
                    type: 'String',
                    value: _rptCode
                });
                var _check = CommonUtil.checkKyCaByParam(_par);
                if (_check > 0) {
                    $("#btnKyCa").prop('disabled', true);
                } else {
                    $("#btnHuyCa").prop('disabled', true);
                }
            }

        });

        var f6 = 117;
        $(document).unbind('keydown').keydown(function (e) {
            if (e.keyCode == f6) {
                getIcd(e.target);
            }
        });
        var f6 = 117;
        var esc = 27;
        var f3 = 114;
        var f4 = 115;

        $(document).unbind('keydown').keydown(function (e) {
            var id = $(this).attr('id');
            // if(e.keyCode == 40){
            // 	if (id == "txtMACHANDOANBANDAU"){
            // 		var focused = $(':focus');
            // 		focused.blur();
            // 		e.preventDefault();
            // 		$("#txtMACHANDOANRAVIEN").focus();
            // 	}else
            // 	if (id == "txtMACHANDOANRAVIEN"){
            // 		var focused = $(':focus');
            // 		focused.blur();
            // 		e.preventDefault();
            // 		$("#txtGHICHU_BENHCHINH").focus();
            // 	}else
            // 	if (id == "txtGHICHU_BENHCHINH"){
            // 		var focused = $(':focus');
            // 		focused.blur();
            // 		e.preventDefault();
            // 		$("#txtMACHANDOANRAVIEN_KHAC").focus();
            // 	}else
            // 	if (id == "txtMACHANDOANRAVIEN_KHAC"){
            // 		var focused = $(':focus');
            // 		focused.blur();
            // 		e.preventDefault();
            // 		$("#txtMAXUTRIKHAMBENHID").focus();
            // 	}else
            // 	if (id == "txtMACHANDOANRAVIEN_KHAC"){
            // 		var focused = $(':focus');
            // 		focused.blur();
            // 		e.preventDefault();
            // 		$("#txtTKKETQUADIEUTRI").focus();
            // 	}else
            // 	if (id == "txtMACHANDOANRAVIEN_KHAC"){
            // 		var focused = $(':focus');
            // 		focused.blur();
            // 		e.preventDefault();
            // 		$("#txtTHOIGIANRAVIEN").focus();
            // 	}
            // }
            // $(':input').keydown(function (e) {
            // 	var id = $(this).attr('id');
            // 	if (e.which === 40 ) {
            // 		var focused = $(':focus');
            // 		focused.blur();
            // 		e.preventDefault();
            // 		if (id == "txtMACHANDOANBANDAU"){ $("#cboCHANDOANBANDAU").focus(); }
            // 		else if(id=="cboCHANDOANBANDAU"){ $("#txtMACHANDOANRAVIEN").focus(); }
            // 		else if(id=="txtMACHANDOANRAVIEN"){ $("#txtGHICHU_BENHCHINH").focus(); }
            // 		else if(id=="txtGHICHU_BENHCHINH"){ $("#txtMAXUTRIKHAMBENHID").focus(); }
            // 		else if(id=="txtMAXUTRIKHAMBENHID"){ $("#cboXUTRIKHAMBENHID").focus(); }
            // 		else if(id=="cboXUTRIKHAMBENHID"){ $("#txtTHOIGIANRAVIEN").focus(); }
            // 	}
            // 	if (e.which === 38 ) {
            // 		var focused = $(':focus');
            // 		focused.blur();
            // 		e.preventDefault();
            // 		if (id == "txtTHOIGIANRAVIEN"){ $("#cboXUTRIKHAMBENHID").focus(); }
            // 		else if(id=="cboXUTRIKHAMBENHID"){ $("#txtMAXUTRIKHAMBENHID").focus(); }
            // 		else if(id=="txtMAXUTRIKHAMBENHID"){ $("#txtGHICHU_BENHCHINH").focus(); }
            // 		else if(id=="txtGHICHU_BENHCHINH"){ $("#txtMACHANDOANRAVIEN").focus(); }
            // 		else if(id=="txtMACHANDOANRAVIEN"){ $("#cboCHANDOANBANDAU").focus(); }
            // 		else if(id=="cboCHANDOANBANDAU"){ $("#txtMACHANDOANBANDAU").focus(); }
            //
            // 	}
            // });
            if (e.keyCode == f6) {
                getIcd(e.target);
            }
            if (e.keyCode == esc) {
                var focused = $(':focus');
                focused.blur();
                e.preventDefault();
                $("#btnDong").trigger("click");
            }
            if (e.keyCode == f3) {
                var focused = $(':focus');
                focused.blur();
                e.preventDefault();
                $("#btnLuu").trigger("click");
            }
            if (e.keyCode == f4) {
                var focused = $(':focus');
                focused.blur();
                e.preventDefault();
                $("#btnLuuDong").trigger("click");
            }
        });
        EventUtil.setEvent("assignSevice_resultTK", function (e) {
            if (e.mode == '0') {
                $('#' + e.ctrId).combogrid("setValue", e.text);
            } else if (e.mode == '1') {
                $('#' + e.ctrTargetId).val($('#' + e.ctrTargetId).val() == '' ? "" + e.text : $('#' + e.ctrTargetId).val() + ";" + e.text);
            }
            DlgUtil.close(e.popupId);
        });

        $("#btnEDITBP").on("click", function (e) {
            if(showyhct4750 == '1'){
            var myVar = {
                    benhphu : $('#txtCHANDOANRAVIEN_KHAC').val(),
                    yhct : showyhct4750,
                    benhphu1 : $('#txtCHANDOANYHCTPHU').val(),
                    chandoan_kt_bd : ""
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
                DlgUtil.open("dlgBPKT");
            }else {
                var myVar = {
                benhphu: $('#txtCHANDOANRAVIEN_KHAC').val()
            };
            dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
            DlgUtil.open("dlgBPKT");
            }
        });

        EventUtil.setEvent("chinhsua_benhphu", function (e) {
            $('#txtCHANDOANRAVIEN_KHAC').val(e.benhphu);
            if( showyhct4750 == '1'){//L2PT-96824
                $('#txtCHANDOANYHCTPHU').val(e.benhphu1);
            }
            DlgUtil.close("dlgBPKT");
        });

        $('#txtTKKETQUADIEUTRI').on('change', function (e) {
            $('#cboKETQUADIEUTRIID').val($('#txtTKKETQUADIEUTRI').val());
        });

        $('#cboKETQUADIEUTRIID').on('change', function (e) {
            $('#txtTKKETQUADIEUTRI').val($('#cboKETQUADIEUTRIID').val());
        });

        // on change ma xu tri kham benh
        $('#txtMAXUTRIKHAMBENHID').change(function () {
            $('#cboXUTRIKHAMBENHID').val($('#txtMAXUTRIKHAMBENHID').val());
			if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_XUTRI_FILL_PPDT') == '1') {
            	$('#txtKHAMBENH_PPDIEUTRI').val($('#cboXUTRIKHAMBENHID option:selected').text().split('-')[1]);//L2PT-110111
            }
            _changexutri($('#txtMAXUTRIKHAMBENHID').val());
        });

        $("#btnPPDIEUTRI").on("click",function(e){
            EventUtil.setEvent("assignSevice_luuttmau",function(e){
                $('#txtKHAMBENH_PPDIEUTRI').val(e.ketqua);
                DlgUtil.close("dlgTRIEUCHUNGMAU");
            });
            var myVar={
                mode : '1'
            };
            dlgPopup=DlgUtil.buildPopupUrl("dlgTRIEUCHUNGMAU","divDlg","manager.jsp?func=../danhmuc/DMC174_TRIEUCHUNGMAU&type=1",myVar,'Phương pháp điều trị mẫu',1000,450);
            DlgUtil.open("dlgTRIEUCHUNGMAU");
        });
        // on change combo xu tri kham benh
        $('#cboXUTRIKHAMBENHID').change(function () {
            var value = $(this).val();
            $('#txtMAXUTRIKHAMBENHID').val(value);
			if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_XUTRI_FILL_PPDT') == '1') {
            	$('#txtKHAMBENH_PPDIEUTRI').val($('#cboXUTRIKHAMBENHID option:selected').text().split('-')[1]);//L2PT-110111
            }
            $("#divKhoa").css("display", "none");
            //L2PT-63895
            if (cfObj.XUTRI_SHOW_LOAIDTRI == '1')
                $("#divLOAIDTRI").css("display", "none");
            //L2PT-25697
            var _kqdt = $('#cboXUTRIKHAMBENHID option:selected').attr('extval0');
            if (_kqdt == '-1' || _kqdt == undefined) {
                _kqdt = _hienthikqdt == "0" ? "1" : $("#cboKETQUADIEUTRIID").val();
            }
            $("#txtTKKETQUADIEUTRI").val(_kqdt);
            $("#cboKETQUADIEUTRIID").val(_kqdt);
            //end L2PT-25697

            EventUtil.setEvent("assignSevice_savetv", function (e) {
                var data = e.msg;
                _objData = $.extend(_objData, data);
                $('#txtTHOIDIEMTUVONG').val(data.TUVONGLUC);
                DlgUtil.close("dlgXuTri");
            });

            //L2PT-15452
            EventUtil.setEvent("assignSevice_savekhoadt", function (e) {
                $("#cboKHOA").val(e.ID);
                DlgUtil.close("dlgXuTri");
            });

            EventUtil.setEvent("assignSevice_saverv", function (e) {
                var data = e.msg;
                $('#txtTHOIGIANRAVIEN').val(data.PTHOIGIANRAVIEN);
                _objData = $.extend(_objData, data);
                DlgUtil.close("dlgXuTri");
            });

            EventUtil.setEvent("assignSevice_savecv", function (e) {
                var data = e.msg;
                $("#divVien").css("display", "");
                $('#txtMAVIEN').val(data.MABENHVIENCHUYENDEN);
                $('#txtTENVIEN').val(data.CHUYENVIENDEN);

                $('#txtTHOIGIANRAVIEN').val(data.THOIGIANCHUYENVIEN);
                //tuyennx_add_start_20170823 yc HISL2NT-193
                _chandoanchuyenvien = data.CHANDOAN;
                //tuyennx_add_end_20170823 yc HISL2NT-193

                _objData = $.extend(_objData, data);

                DlgUtil.close("dlgXuTri");
                var ketthuckham = cfObj.NGT_CHUYENVIEN_KTK;
                if (data.KYSO == 1) {
                    $('#btnKyCa').click();
                } else if (ketthuckham == 1) {
                    $('#btnLuuDong').click();
                }

            });

            EventUtil.setEvent("assignSevice_updatecv", function (e) {
                var data = e.msg;
                $("#divVien").css("display", "");
                $('#txtMAVIEN').val(data.MABENHVIENCHUYENDEN);
                $('#txtTENVIEN').val(data.CHUYENVIENDEN);
                $('#txtTHOIGIANRAVIEN').val(data.THOIGIANCHUYENVIEN);
                _chandoanchuyenvien = data.CHANDOAN;
                _objData = $.extend(_objData, data);
            });


            EventUtil.setEvent("assignSevice_savehk", function (e) {
                var data = e.msg;
                _objData = $.extend(_objData, data);
                DlgUtil.close("dlgXuTri");
                var ketthuckham = cfObj.NGT_HENKHAM_KTK;
                if (ketthuckham == 1) {
                    $("#btnLuuDong").trigger('click');
                }
            });

            _changexutri(value);
        });

        //open popup xu tri
        $("#btnShowPopUp").on("click", function (e) {
            $('#cboXUTRIKHAMBENHID').change();
        });

        //change value cbo khoa. 
        $('#cboKHOA').change(function () {
            // L2PT-14719
            var checkkhoanhi = cfObj.NGT_CANHBAO_KHOANHI;
            if (checkkhoanhi == 1) {
                var _sql_par = [];
                _sql_par.push({
                    "name": "[0]",
                    value: $("#cboKHOA").val()
                });
                var rs = jsonrpc.AjaxJson.getOneValue("NGT.CHECKKHOANHI", _sql_par);
                if (rs == 1) {
                    if ($("#hidTUOI").val() >= 16 && $("#hidDVTUOI").val() == 1) {
                        DlgUtil.showMsg("Bệnh nhân trên 16 tuổi không thể nhập viện khoa Nhi");
                        $("#cboKHOA").val(-1);
                        $("#cboKHOA").focus();
                        return;
                    }
                }
            }
            $('#txtMAKHOA').val($(this).val());
            Optvalue = $('#cboKHOA' + " option:selected").attr('extval0');
            $('#cboLOAIBA').val(Optvalue);
        });
        //change value cbo khoa. 
        $('#txtTHOIGIANRAVIEN').change(function () {
            //dannd_L2PT-84144
            if (cfObj.HIS_CHANXUTRI_NHOHONTGHIENTAI == '1') {
                if (checkTime($('#txtTHOIGIANRAVIEN').val(), thoigianmopopup)) {
                    DlgUtil.showMsg("Thời gian xử trí nhỏ hơn so với thời gian hiện tại không thể xử trí!");
                    return false;
                }
            }
        });


        // click button luu
        $("#btnLuu").bindOnce("click", function () {
            _save(0);
        }, 5000);

        // click button luu + in
        $("#btnLuuDong").bindOnce("click", function () {
            _save(1);
        }, 5000);

        $("#btnBP").on("click", function (e) {
            $('#txtCHANDOANRAVIEN_KHAC').val('');
            $('#txtMACHANDOANRAVIEN_KHAC').val('');
            $('#txtCHANDOANYHCTPHU').val('');
        });

        $("#btnThuoc").on("click", function (e) {
            //tuyennx_add_start L2PT-6049
            var _sql_par = [];
            _sql_par.push($("#hidTIEPNHANID").val());
            _sql_par.push($("#hidKHAMBENHID").val());
            _sql_par.push($("#hidPHONGKHAMDANGKYID").val());
            var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK.DATA1", _sql_par.join('$'));
            if (_result == 1) {
                DlgUtil.showMsg('Bệnh nhân đã duyệt kế toán không thể thao tác');
                return;
            } else if (_result == 2) {
                DlgUtil.showMsg('Bệnh nhân đã nhập viện không thể thao tác');
                return;
            }
            //tuyennx_add_end L2PT-6049
            //tuyennx_add_end L2PT-6049
            //L2PT-14617
            if ($("#cboDOITUONGBENHNHANID").val() != 1 && cfObj.XUTRI_KETHUOC_BHYT == 1) {
                DlgUtil.showMsg('Chỉ kê thuốc BHYT cho bệnh nhân BHYT!');
                return;
            }
            EventUtil.raiseEvent("exam_capthuoc", {khambenhid: $("#hidKHAMBENHID").val()});
        });

        // SONDN 18/05/2020 L2PT-20858
        $("#btnThuocMN").on("click", function (e) {
            EventUtil.raiseEvent("exam_capthuocmuangoai", {khambenhid: $("#hidKHAMBENHID").val()});
        });
        // END SONDN 18/05/2020 L2PT-20858

        // SONDN: hien thi form kham benh hoi benh; 
        $("#btnKBNHAPVIEN").on("click", function (e) {
            EventUtil.raiseEvent("exam_kbhb", {khambenhid: $("#hidKHAMBENHID").val()});
        });

        // In Phieu
        $("#btnInPhieu").on("click", function (e) {
            var _typeIn = cfObj.IN_GOPPHIEU_NV;
            var _print1tab = cfObj.IN_GOPPHIEU_NV_1TAB;
            var _fileBA = Optvalue = $('#cboLOAIBA' + " option:selected").attr('extval0');
            var fileReport = _getTypePrint();
            if (_typeIn == '1' && $('#cboXUTRIKHAMBENHID').val() == '6') {
                if (_print1tab == '1') {
                    var par = [{
                        name: 'benhnhanid',
                        type: 'String',
                        value: $('#hidBENHNHANID').val()
                    },
                        {
                            name: 'hosobenhanid',
                            type: 'String',
                            value: $('#hidHOSOBENHANID').val()
                        }, {
                            name: 'khambenhid',
                            type: 'String',
                            value: $('#hidKHAMBENHID').val()
                        }, {
                            name: 'report_code_loaiba',
                            type: 'String',
                            value: _fileBA
                        }, {name: 'khoaid', type: 'String', value: opt.khoaid}
                    ];
                    openReport('window', "PHIEU_IN_BENHNHAN_NHAPVIEN", 'pdf', par);
                } else {
                    var par = [{
                        name: 'benhnhanid',
                        type: 'String',
                        value: $('#hidBENHNHANID').val()
                    }];
                    var par1 = [{
                        name: 'hosobenhanid',
                        type: 'String',
                        value: $('#hidHOSOBENHANID').val()
                    }];
                    _printPhieu($('#hidKHAMBENHID').val(), fileReport); // phiếu NV
                    openReport('window', "BENHLICH_A4_951", 'pdf', par);// bệnh lịch.				
                    openReport('window', _fileBA, 'pdf', par1); // bệnh an 1 tờ đâu.
                    //them phieu danh gia ban dau benh nhan
                    _printPhieu($('#hidKHAMBENHID').val(), "NGT005_PHIEUDANHGIANHAPVIEN_A4_951");
                }
            } else {
                _printPhieu($('#hidKHAMBENHID').val(), fileReport);
            }
        });

        $("#btnSINHTON").on("click", function () {
            openSinhTon();
        });
        $("#btnHENKHAM").on("click", function () {
            var chandoan = "";
            chandoan = $('#txtMACHANDOANRAVIEN').val() + '-' + $('#txtCHANDOANRAVIEN').val() + ($('#txtGHICHU_BENHCHINH').val() == "" ? "" : "(") + $('#txtGHICHU_BENHCHINH').val() + ($('#txtGHICHU_BENHCHINH').val() == "" ? "" : ")") +
                ($('#txtCHANDOANRAVIEN_KHAC').val() == "" ? "" : ";") + $('#txtCHANDOANRAVIEN_KHAC').val();
            var param = {
                mabenhnhan: $('#txtMABENHNHAN').val(),
                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                nghenghiep: $("#cboNGHENGHIEPID option:selected").text(),
                diachi: $('#hidDIACHI').val(),
                namsinh: $('#txtNAM').val(),
                khambenhid: opt.khambenhid,
                //tuyennx_add_start_20190128 L2PT-1114
                benhnhanid: $('#hidBENHNHANID').val(),
                hosobenhanid: $('#hidHOSOBENHANID').val(),
                //tuyennx_add_end_20190128
                chandoan: chandoan,
                phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
                doituongbenhnhanid: $("#cboDOITUONGBENHNHANID").val(),
                ngaytiepnhan: $('#hidNGAYTN').val(),
                capnhat:  1 ,
                machandoanravien : $('#txtMACHANDOANRAVIEN').val(),
                chandoanravien : $('#txtCHANDOANRAVIEN').val(),
                chandoanravien_kemtheo : $('#txtCHANDOANRAVIEN_KHAC').val()
            };
            param['xutri'] = $('#cboXUTRIKHAMBENHID').val();
            _showDialog("NGT02K008_Thongtin_Lichkham", param, 'Thông tin lịch hẹn', 1100, 440);
        });

        //tuyennx_add_start In Phieu doc
        $("#btnInPhieuDoc").on("click", function (e) {
            var fileReport = _getTypePrint();
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }];
            var _type = "rtf";
            var rpName = fileReport + "_" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + _type;
            CommonUtil.inPhieu('window', fileReport, _type, par, rpName);
//			CommonUtil.inPhieu('window', fileReport, 'rtf', par);
        });
        //tuyennx_add_end

        // SONDN: in ra phieu kham benh vao vien; 
        function _openReport(param, reportName, format1) {
            var format = "pdf";
            param.push({name: 'i_hid', type: 'String', value: opt._hospital_id});
            param.push({name: 'i_sch', type: 'String', value: opt.db_schema});
            param.push({name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()});
            param.push({name: 'phongid', type: 'String', value: opt.phongid});
            openReport('window', reportName, format, param);
        }

        $("#btnInPKBVV").on("click", function () {
            var par = [];
            if (opt._hospital_id == "965") {			// BDHCM : xuat excel chuc nang nay; 
                par = [{
                    name: 'i_hid',
                    type: 'String',
                    value: opt._hospital_id
                }, {
                    name: 'i_sch',
                    type: 'String',
                    value: opt.db_schema
                }, {
                    name: 'khambenhid',
                    type: 'String',
                    value: $("#hidKHAMBENHID").val()
                }];


                if ($("#cboDOITUONGBENHNHANID").val() == 3) {
                    var rpName = "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    CommonUtil.inPhieu('window', "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'xlsx', par, rpName);
                } else {
                    var rpName = "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                    CommonUtil.inPhieu('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'xlsx', par, rpName);
                }
            } else if (opt._hospital_id == "1007" || opt._hospital_id == "1133" || opt._hospital_id == "26320") {					// SN VPC , LACVIET , DKHTH
                _openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
            } else {
                if (_type == "2" && cfObj.NGT_PKBVV_CAPCUU == "1" && $("#hidHINHTHUCVAOVIENID").val() == "2") {
                    var param = [];
                    param.push({name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()});
                    CommonUtil.openReportGet('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_CAPCUU_A4", "pdf", param);
                } else {
                    if ($("#cboDOITUONGBENHNHANID").val() == 3) {
                        _openReport(par, "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'pdf');
                    } else {
                        if ($("#cboXUTRIKHAMBENHID").val() == 2 && opt._hospital_id == 7282) {
                            par = [{
                                name: 'khambenhid',
                                type: 'String',
                                value: $("#hidKHAMBENHID").val()
                            }];
                            _openReport(par, "NGT005_PHIEUKBVAOVIEN_NGOAITRU_42BV01_QD4069_A4", 'pdf');
                        } else {
                            _openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
                        }
                    }
                }
            }
        });

        $("#btnDong").on("click", function (e) {
            var evFunc = EventUtil.getEvent("assignSevice_closephieukham");
            if (typeof evFunc === 'function') {
                evFunc({msg: $('#cboXUTRIKHAMBENHID').val(), type: 1, badaingay: _badaingay});
            } else {
                console.log('evFunc not a function');
            }
        });

        $.jMaskGlobals = {
            maskElements: 'input,td,span,div',
            dataMaskAttr: '*[data-mask]',
            dataMask: true,
            watchInterval: 300,
            watchInputs: true,
            watchDataMask: true,
            byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
            translation: {
                '0': {pattern: /\d/},
                '9': {pattern: /\d/, optional: true},
                '#': {pattern: /\d/, recursive: true},
                'A': {pattern: /[a-zA-Z0-9]/},
                'S': {pattern: /[a-zA-Z]/}
            }
        };

        //dannd_kyso_BVTM
        $('#btnKyCa').bindOnce("click", function () {
            isKyCa = true;
            $('#btnLuu').click();
        });

        $("#btnHuyCa").on("click", function (e) {
            isKyCa = true;
            _caRpt('2');
        });
        $("#btnInKySo").on("click", function (e) {
            isKyCa = true;
            var _rptCode = '';
            var _par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            }, {
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            if ($('#cboXUTRIKHAMBENHID').val() == '6') {
                _rptCode = 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4';
            } else if ($('#cboXUTRIKHAMBENHID').val() == '7') {
                _rptCode = 'NGT003_GIAYCHUYENTUYEN_TT14_A4';
            } else if ($('#cboXUTRIKHAMBENHID').val() == '5') {
                _rptCode = 'NGT014_GIAYHENKHAMLAI_TT402015_A4';
            } else if ($('#cboXUTRIKHAMBENHID').val() == '2') {
                _rptCode = 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4';
            }else {
                _rptCode = 'NGT005_PHIEUKHAMBENHNGOAITRU_A4_10284';
            }
            _par.push({
                name: 'RPT_CODE',
                type: 'String',
                value: _rptCode
            });
            CommonUtil.openReportGetCA2(_par, false);
        });
        // L2PT-132047 start
        $("#btnDSCLS").on("click", function (e) {
        	var khambenhid = opt.khambenhId;
			if (khambenhid == "-1" || khambenhid == "") {
				DlgUtil.showMsg("Chưa có thông tin khám bệnh. Xin kiểm tra lại.");
				return;
			}
			var myVar = {
				khambenhid : khambenhid,
	            benhnhanid: $('#hidBENHNHANID').val(),
	            hosobenhanid: $('#hidHOSOBENHANID').val(),
				mode : '1',
				thoigianchidinh : "",
				loadlaidulieu : "0",
				ids_cls : "",
				formCall: 'PKB'
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgKQCLS", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K031_ChonKQCLS", myVar, "CHỌN KẾT QUẢ CẬN LÂM SÀNG", 1100, 512); // L2PT-43257 duonghn
			DlgUtil.open("dlgKQCLS");
        });
        // L2PT-132047 end
    }

    //dannd_kyso_BVTM
    function _caRpt(signType, _msgCheck) {
        var _rptCode = '';
        var _par = [{
            name: 'hosobenhanid',
            type: 'String',
            value: $("#hidHOSOBENHANID").val()
        }, {
            name: 'khambenhid',
            type: 'String',
            value: $("#hidKHAMBENHID").val()
        }];
        if ($('#cboXUTRIKHAMBENHID').val() == '6') {
            _rptCode = 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4';
        } else if ($('#cboXUTRIKHAMBENHID').val() == '7') {
            _rptCode = 'NGT003_GIAYCHUYENTUYEN_TT14_A4';
        } else if ($('#cboXUTRIKHAMBENHID').val() == '5') {
            _rptCode = 'NGT014_GIAYHENKHAMLAI_TT402015_A4';
        } else if ($('#cboXUTRIKHAMBENHID').val() == '2') {
            _rptCode = 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4';
        }else {
            _rptCode = 'NGT005_PHIEUKHAMBENHNGOAITRU_A4_10284';
        }
        _par.push({
            name: 'RPT_CODE',
            type: 'String',
            value: _rptCode
        });
        var oData = {
            sign_type: signType,
            causer: causer,
            capassword: capassword,
            params: _par
        };
        CommonUtil.kyCA(_par, signType, true, true);
        EventUtil.setEvent("eventKyCA", function (e) {
            var _code = e.res.split("|")[0];
            var _msg = e.res.split("|")[1];
            if (_code == '0') {
                if (signType == '1') {
                    var evFunc = EventUtil.getEvent("assignSevice_closephieukham");
                    if (typeof evFunc === 'function') {
                        evFunc({
                            msg: 'Xử trí thành công' + '</br>' + _msgCheck + '</br>' + _msg,
                            type: 1,
                            badaingay: _badaingay
                        });
                    } else {
                        console.log('evFunc not a function');
                    }
                } else {
                    DlgUtil.showMsg(_msg);
                    $("#btnKyCa").prop('disabled', false);

                }
            } else {
                DlgUtil.showMsg(_msg);


            }
        });


    }

    function _getTypePrint() {
        var fileReport = "";
        var value = $('#cboXUTRIKHAMBENHID').val();

        if (value === "1" || value === "3" || value === "9" || value === "14") { // Cấp toa cho về, hẹn, khác
            fileReport = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5";
        } else if (value === "4" || value === "5") { // hẹn khám tiếp, khám mới
            fileReport = "NGT014_GIAYHENKHAMLAI_TT402015_A4";
        } else if (value === "7") { 					// chuyển viện
            if (opt._hospital_id == "902") {
                if ($("#cboDOITUONGBENHNHANID").val() == "1") {
                    fileReport = "NGT003_GIAYCHUYENTUYEN_BHYT_A4_902";
                } else {
                    fileReport = "NGT003_GIAYCHUYENTUYEN_TT14_A4";
                }
            } else if (opt._hospital_id == "932") {					// in chung 1 mau; 
                fileReport = "NGT003_GIAYCHUYENTUYEN_BHYT_A4_932";
            } else {
                fileReport = "NGT003_GIAYCHUYENTUYEN_TT14_A4";
            }
        } else if (value === "6") { // nhap vien
            fileReport = "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4";
        } else if (value === "8") { // tử vong
            fileReport = "";
        }
        return fileReport;
    }

    function _changexutri(value) {
        //tuyennx_add_start
        var chandoan = "";
        var qd130 = cfObj.HIS_QD130;
        chandoan = $('#txtMACHANDOANRAVIEN').val() + '-' + $('#txtCHANDOANRAVIEN').val() + ($('#txtGHICHU_BENHCHINH').val() == "" ? "" : "(") + $('#txtGHICHU_BENHCHINH').val() + ($('#txtGHICHU_BENHCHINH').val() == "" ? "" : ")") +
            ($('#txtCHANDOANRAVIEN_KHAC').val() == "" ? "" : ";") + $('#txtCHANDOANRAVIEN_KHAC').val();

        var param = {
            mabenhnhan: $('#txtMABENHNHAN').val(),
            tenbenhnhan: $('#txtTENBENHNHAN').val(),
            nghenghiep: $("#cboNGHENGHIEPID option:selected").text(),
            diachi: $('#hidDIACHI').val(),
            namsinh: $('#txtNAM').val(),
            khambenhid: opt.khambenhid,
            //tuyennx_add_start_20190128 L2PT-1114
            benhnhanid: $('#hidBENHNHANID').val(),
            hosobenhanid: $('#hidHOSOBENHANID').val(),
            //tuyennx_add_end_20190128 
            chandoan: chandoan,
            phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
            doituongbenhnhanid: $("#cboDOITUONGBENHNHANID").val(),
            ngaytiepnhan: $('#hidNGAYTN').val(),
            capnhat: _xutrikhambenhid == value ? 1 : 0,
            machandoanravien : $('#txtMACHANDOANRAVIEN').val(),
            chandoanravien : $('#txtCHANDOANRAVIEN').val(),
            chandoanravien_kemtheo : $('#txtCHANDOANRAVIEN_KHAC').val()
        };

        if (value === "1" || value === "3" || value === "9" || (value === "14" && cfObj.NGT_MOPOPUPHENKHAM_XUTRI14 != 1)) { // Cấp toa cho về, hẹn, khác
            if (_tatpopupravien != "1") {
                _showDialog("NGT02K007_Thongtin_Ravien", param, 'Thông tin ra viện', 1000, 380);
            }

            $('#btnInPhieu').attr("disabled", false);
            $('#btnInPhieuDoc').attr("disabled", false);
            $('#btnShowPopUp').attr("disabled", false);
            $("#divLOAIBA").hide();
            $("#dvSOVAOVIEN").hide();
            $("#divVien").hide();
            $("#txtMAVIEN").val("");
            $("#txtTENVIEN").val("");
            $("#txtTHOIDIEMTUVONG").val("");
            if (qd130 == 1) {
                $("#divChuyenVien").hide();
            }
            if (cfObj.QD_4750  == 1) {
                $("#txtLY_DO_VNT").val('');
                $("#dvLYDOVAONOITRU").hide();
            }
        } else if (value === "4" || value === "5" || (value === "14" && cfObj.NGT_MOPOPUPHENKHAM_XUTRI14 == 1)) { 			// hẹn khám tiếp, khám mới
            param['xutri'] = value;
            $('#btnInPhieu').attr("disabled", false);
            $('#btnInPhieuDoc').attr("disabled", false);
            $('#btnShowPopUp').attr("disabled", false);
            $("#divLOAIBA").hide();
            $("#dvSOVAOVIEN").hide();
            $("#divVien").hide();
            $("#txtMAVIEN").val("");
            $("#txtTENVIEN").val("");
            if (qd130 == 1) {
                $("#divChuyenVien").hide();
            }
            // ChienDV START L2PT-95654
            _showDialog("NGT02K008_Thongtin_Lichkham", param, 'Thông tin lịch hẹn', 1100, 440);
            // ChienDV END L2PT-95654
            if (cfObj.QD_4750  == 1) {
                $("#txtLY_DO_VNT").val('');
                $("#dvLYDOVAONOITRU").hide();
            }
        } else if (value === "7") { 							// chuyển viện
            $('#btnInPhieu').attr("disabled", false);
            $('#btnInPhieuDoc').attr("disabled", false);
            $('#btnShowPopUp').attr("disabled", false);
            $("#divLOAIBA").hide();
            $("#dvSOVAOVIEN").hide();
            $("#divVien").hide();
            $("#txtMAVIEN").val("");
            $("#txtTENVIEN").val("");
            $("#txtTHOIDIEMTUVONG").val("");
            if (qd130 == 1) {
                $("#divChuyenVien").show();
            }
            if (opt._hospital_id == "951") {
                _showDialog("NGT02K009_Chuyenvien", param, 'Thông tin chuyển viện', 1200, 480); //440
            } else {
                _showDialog("NGT02K009_Chuyenvien", param, 'Thông tin chuyển viện', 1200, 440); //440
            }
            if (_hienthikqdt != "0" && $("#cboKETQUADIEUTRIID").val() != "3") {
                DlgUtil.showMsg('Chưa chọn kết quả điều trị là [KHÔNG THAY ĐỔI] đối với hướng xử trí chuyển viện. ');
            }
            if (cfObj.QD_4750  == 1) {
                $("#txtLY_DO_VNT").val('');
                $("#dvLYDOVAONOITRU").hide();
            }
        } else if (value === "8") { 							// tử vong
            _showDialog("NGT02K010_Tuvong", param, 'Thông tin tử vong', 1000, 330);
            $('#btnInPhieu').attr("disabled", false);
            $('#btnInPhieuDoc').attr("disabled", false);
            $('#btnShowPopUp').attr("disabled", false);
            $("#divLOAIBA").hide();
            $("#dvSOVAOVIEN").hide();
            $("#divVien").hide();
            $("#txtMAVIEN").val("");
            $("#txtTENVIEN").val("");
            $("#txtTHOIDIEMTUVONG").val("");
            if (qd130 == 1) {
                $("#divChuyenVien").hide();
            }
            if (cfObj.QD_4750  == 1) {
                $("#txtLY_DO_VNT").val('');
                $("#dvLYDOVAONOITRU").hide();
            }
        } else if (value === "2") { 							// Điều trị ngoại trú
            $("#divKhoa").css("display", "");
            if (cfObj.XUTRI_SHOW_LOAIDTRI == '1')
                $("#divLOAIDTRI").css("display", "");
            $('#btnInPhieu').attr("disabled", true);
            $('#btnInPhieuDoc').attr("disabled", true);
            $('#btnShowPopUp').attr("disabled", true);
            $("#divLOAIBA").hide();
            $("#dvSOVAOVIEN").hide();
            $("#divVien").hide();
            $("#txtMAVIEN").val("");
            $("#txtTENVIEN").val("");
            $("#txtTHOIDIEMTUVONG").val("");
            //ComboUtil.getComboTag("cboKHOA",_sql[4],[],opt.khoaid,{value:0, text:''},"sql");
            ComboUtil.getComboTag("cboKHOA", "KHOA.DTNGT", [], "", {value: 0, text: '--Chọn khoa--'}, "sql");
            if (qd130 == 1) {
                $("#divChuyenVien").hide();
            }
            if (cfObj.QD_4750  == 1) {
                $("#txtLY_DO_VNT").val('');
                $("#dvLYDOVAONOITRU").hide();
            }
        } else if (value === "6") {							// nhập viện
            if (cfObj.NGT_HIENTHI_BNGIUONG == 1) {
                ComboUtil.getComboTag("cboKHOA", "KHOA.DTNT2", [], "", {
                    extval: true,
                    value: 0,
                    text: '--Chọn khoa--'
                }, "sql", "", "");
            } else {
                ComboUtil.getComboTag("cboKHOA", "KHOA.DTNT", [], "", {
                    extval: true,
                    value: 0,
                    text: '--Chọn khoa--'
                }, "sql", "", "");
            }
//			$("#divLOAIBA").css("display","");
            _hienthiloaiba == "1" ? $("#divLOAIBA").show() : $("#divLOAIBA").hide();
            _hienthisvv == "1" ? $("#dvSOVAOVIEN").show() : $("#dvSOVAOVIEN").hide();
            $("#divKhoa").css("display", "");
            if (cfObj.XUTRI_SHOW_LOAIDTRI == '1')
                $("#divLOAIDTRI").css("display", "");
            $('#btnInPhieu').attr("disabled", false);
            $('#btnInPhieuDoc').attr("disabled", false);
            $('#btnShowPopUp').attr("disabled", false);//L2PT-15452
            $("#divVien").hide();
            $("#txtMAVIEN").val("");
            $("#txtTENVIEN").val("");
            $("#txtTHOIDIEMTUVONG").val("");
            //L2PT-15452
            if (cfObj.XUTRI_NHAPVIEN_POPUP == 1)
                _showDialog("NGT02K010_KhoaNoiTru", param, 'Tình hình điều trị nội trú', 1100, 450);

            if ($("#hidBATBUOCSINHTON").val() == value) {
                openSinhTon();
            }
            if (qd130 == 1) {
                $("#divChuyenVien").hide();
            }
            if (cfObj.QD_4750  == 1) {
                if (cfObj.HIS_FORMAT_LYDOVNT  == 1) {
                    $("#txtLY_DO_VNT").val('');
                }else if( cfObj.HIS_FORMAT_LYDOVNT != 0 && cfObj.HIS_FORMAT_LYDOVNT != 1  ) {
                    var _obj = new Object();
                    _obj.KHAMBENHID = $("#hidKHAMBENHID").val();
                    var dataVNT = jsonrpc.AjaxJson.ajaxCALL_SP_O("HIS.GETLYDOVNT", JSON.stringify(_obj) );
                    if (dataVNT != null && dataVNT != 'null' && dataVNT.length > 0 ){
                        var data= dataVNT[0];
                        var  lydovnt = cfObj.HIS_FORMAT_LYDOVNT.replace(/{(.*?)}/g, (match, p1) => {
                            return data[p1] !== undefined && data[p1] !== "" ? data[p1] : "";
                        });
                        $("#txtLY_DO_VNT").val(lydovnt);
                    }
                }
                $("#dvLYDOVAONOITRU").show();
            }
        }
    }

    function openSinhTon() {
        var param = {
            tuoi: $('#hidTUOI').val(),
            dvtuoi: $('#hidDVTUOI').val(),
            khambenhid: $('#hidKHAMBENHID').val(),
            batbuocsinhton: $("#hidBATBUOCSINHTON").val(),
            phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),
            trangthaikhambenh: "1" // $('#hidTRANGTHAIKHAMBENH').val()
        };

        EventUtil.setEvent("assignSevice_savetv", function (e) {
            objSinhTon = e.msg;
        });

        dlgPopup = DlgUtil.buildPopupUrl("dlgCV", "divDlg"
            , "manager.jsp?func=../ngoaitru/NGT01T001_sinhton", param, 'THÔNG TIN SINH TỒN-NHÂN TRẮC', 550, 285);
        DlgUtil.open("dlgCV");

    }

    // hiển thị dialog xử trí khám bệnh
    function _showDialog(url, param, title, w, h) {
        _objData = {};
        dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/" + url, param, title, window.innerWidth * 0.90, window.innerHeight * 0.95);
        DlgUtil.open("dlgXuTri");
    }

    // SONDN IT360-276445
    function _inDonThuoc_SauHenKham() {
        if (cfObj.NGT_INDON_SAUHENKHAM == "1" && $("#cboXUTRIKHAMBENHID").val() == "5") {

            var sql_par = [];
            if ($('#hidKHAMBENHID').val() == "-1") {
                DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
                return false;
            }
            sql_par.push({"name": "[0]", "value": $('#hidKHAMBENHID').val()});

            var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
            var rows = $.parseJSON(data);


            if (rows.length >= 1) {
                if (opt._hospital_id == 996) {
                    // hunglv L2PT-5484
                    var thuoc_thuong = '';
                    var thuoc_huongthan = '';
                    var thuoc_gaynghien = '';

                    for (var i = 0; i < rows.length; i++) { //từng đơn thuốc
                        var _par_loai = [rows[i].MAUBENHPHAMID];
                        var arr_loaithuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC", _par_loai.join('$'));
                        if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
                            for (var j = 0; j < arr_loaithuoc.length; j++) {
                                var _loaithuoc = arr_loaithuoc[j].LOAI;
                                if (_loaithuoc == 6) { //thuoc huong than 
                                    thuoc_huongthan = thuoc_huongthan + ',' + rows[i].MAUBENHPHAMID;
                                } else if (_loaithuoc == 7) { //don thuoc gay nghien
                                    thuoc_gaynghien = thuoc_gaynghien + ',' + rows[i].MAUBENHPHAMID;
                                } else {
                                    thuoc_thuong = thuoc_thuong + ',' + rows[i].MAUBENHPHAMID;
                                }
                            }
                        }
                    }

                    if (thuoc_thuong.length > 0) {
                        thuoc_thuong = thuoc_thuong.substring(1);
                        var par_thuoc_thuong = [{
                            name: 'maubenhphamid',
                            type: 'String',
                            value: thuoc_thuong
                        }];
                        CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par_thuoc_thuong);
                    }
                    if (thuoc_huongthan.length > 0) {
                        thuoc_huongthan = thuoc_huongthan.substring(1);
                        var par_thuoc_huongthan = [{
                            name: 'maubenhphamid',
                            type: 'String',
                            value: thuoc_huongthan
                        }];
                        CommonUtil.openReportGet('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par_thuoc_huongthan);
                    }
                    if (thuoc_gaynghien.length > 0) {
                        thuoc_gaynghien = thuoc_gaynghien.substring(1);
                        var par_thuoc_gaynghien = [{
                            name: 'maubenhphamid',
                            type: 'String',
                            value: thuoc_gaynghien
                        }];
                        CommonUtil.openReportGet('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par_thuoc_gaynghien);
                    }
                    return;
                }
            } else {
                DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
            }


            if (rows.length == 1) {
                var par = [{
                    name: 'maubenhphamid',
                    type: 'String',
                    value: rows[0].MAUBENHPHAMID
                }];
                openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
            } else if (rows.length > 1) {
                paramInput = {
                    data: rows,
                    khambenhid: $("#hidKHAMBENHID").val()
                };
                if (opt._hospital_id == 5926) {
                    dlgPopup = DlgUtil.buildPopupUrl("dlgInDonThuoc", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc", paramInput, "IN ĐƠN THUỐC", 800, 300);
                } else {
                    dlgPopup = DlgUtil.buildPopupUrl("dlgInDonThuoc", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc", paramInput, "IN ĐƠN THUỐC", 420, 260);
                }
                DlgUtil.open("dlgInDonThuoc");
            } else {
                DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
            }
        }
    }

    // SONDN IT360-276445

    //dannd_L2PT-19805
    function _indonthuocsauxutri() {
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": $("#hidKHAMBENHID").val()});
        sql_par.push({"name": "[1]", "value": opt.phongid});
        var datathuoc = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT2", sql_par);
        var rowsthuoc = $.parseJSON(datathuoc);
        console.log(rowsthuoc);
        if (rowsthuoc.length > 0) {
            //L2PT-24728
            if (cfObj.NGT_XUTRI_INGOP_DT == "1") {
                var lst_mbp = '';
                for (var i = 0; i < rowsthuoc.length; i++) {
                    lst_mbp = lst_mbp + ',' + rowsthuoc[i].MAUBENHPHAMID;
                }

                var par = [{
                    name: 'maubenhphamid',
                    type: 'String',
                    value: lst_mbp.substring(1)
                }];
                openReport('window', "DONGOP_NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
            } else {
                for (var i = 0; i < rowsthuoc.length; i++) {
                    var par = [{
                        name: 'maubenhphamid',
                        type: 'String',
                        value: rowsthuoc[i].MAUBENHPHAMID
                    }];
                    openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
                }
            }
        }

    }

    function _save(_loai) {
        var icd_phu_check = $('#txtCHANDOANRAVIEN_KHAC').val();
        if(checkSoLuongICDKemTheo(icd_phu_check) >= 0 && parseInt(cfObj.SOLUONG_ICD_TRONGMOTLANDIEUTRI) != 0) {
            DlgUtil.showMsg('Tổng số mã ICD trong một điều trị vượt quá số lượng quy định của BHXH!');
            return false;
        }
        if (cfObj.HIS_KTBA_CHECK_KYCA == '1') {
            var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV079", $("#hidHOSOBENHANID").val());
            if (result_ct != '|') {
                if (result_ct == '-1') {
                    DlgUtil.showMsg('Có lỗi khi xử lý');
                    return;
                } else {
                    var msg = result_ct.split("|");
                    if (msg[0] != '') {
                        DlgUtil.showMsg('Tồn tại ' + msg[0] + ' chưa thực hiện ký số/điện tử.');
                        return;
                    }
                    if (msg[1] != '') {
                        DlgUtil.showMsg('Tồn tại ' + msg[1] + ' chưa hoàn thành ký số/điện tử.');
                        return;
                    }
                }
            }
        }
        // Dannd - Check dich vu co ket qua cls hay khong
        var NTU_KTBA_CHECK_KQCLS = cfObj.NTU_KTBA_CHECK_KQCLS;
        if (NTU_KTBA_CHECK_KQCLS && NTU_KTBA_CHECK_KQCLS != '0' && check_kqcls == 0 && $("#cboXUTRIKHAMBENHID").val() != "2" && $("#cboXUTRIKHAMBENHID").val() != "6") {
            var objData = new Object();
            objData["HOSOBENHANID"] = $('#hidHOSOBENHANID').val();
            var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV090", JSON.stringify(objData));
            if (fl != '1') {
                if (NTU_KTBA_CHECK_KQCLS == '2') {
                    DlgUtil.showConfirm("Còn dịch vụ " + fl + " chưa trả kết quả. Yêu cầu trả kết quả hoặc xóa nếu không thực hiện! Có muốn hoàn tất khám cho bệnh nhân? ", function (flag) {
                        if (flag) {
                            check_kqcls = 1;
                            _save(_loai);
                        } else {
                            return false;
                        }
                    });
                    return false;
                } else if (NTU_KTBA_CHECK_KQCLS == '1') {
                    DlgUtil.showMsg("Còn dịch vụ " + fl + " chưa trả kết quả. Yêu cầu trả kết quả hoặc xóa nếu không thực hiện !");
                    return false;
                }
            }
        }
        // Dannd - END
		//L2PT-102859
		if (cfObj.HIS_KTBA_CHECK_XN_NULL != '0' && $('#cboXUTRIKHAMBENHID').val() != '2' && $('#cboXUTRIKHAMBENHID').val() != '6') {
			var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("KTK.CHECK.KQXN", $("#hidKHAMBENHID").val() + "$" + $("#hidHOSOBENHANID").val()+ "$" + $("#hidPHONGKHAMDANGKYID").val());
			if (result_ct != '0') {
				if (result_ct == '-1') {
					DlgUtil.showMsg('Có lỗi khi xử lý check kq XN trống');
					return;
				} else {
					var msgchk = 'Tồn tại phiếu còn dịch vụ trống kết quả' + result_ct;
					if (cfObj.HIS_KTBA_CHECK_XN_NULL == '1') {
						if (!confirm(msgchk + '\nCó tiếp tục?')) {
							return false;
						}
					} else {
						DlgUtil.showMsg(msgchk);
						return false;
					}
				}
			}
		}
        //dannd_L2PT-84144

        //L2PT-121541
        if (cfObj.NGT_XUTRI_CHECKDVBHYT != '0' &&  $("#cboDOITUONGBENHNHANID").val() == 1) {
            var objData = new Object();
            objData["HOSOBENHANID"] = $('#hidHOSOBENHANID').val();
            objData["KHAMBENHID"] = $('#hidKHAMBENHID').val();

            var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.XUTI.CHECKDVBHYT", JSON.stringify(objData));
            if (fl != '0') {
                if (fl == '-1') {
                    DlgUtil.showMsg('Có lỗi khi xử lý check các dịch vụ BHYT!');
                    return;
                } else {
                    var msgchk = 'Bệnh nhân khám chữa bệnh bảo hiểm nhưng không có chi phí nằm trong bảo hiểm, không thể xử trí/ kết thúc khám. Vui lòng kiểm tra lại thông tin tuyến và chi phí khám chữa bệnh! ' + result_ct;
                    if (cfObj.NGT_XUTRI_CHECKDVBHYT == '1') {
                        if (!confirm(msgchk + '\nCó tiếp tục?')) {
                            return false;
                        }
                    } else {
                        DlgUtil.showMsg(msgchk);
                        return false;
                    }
                }
            }
        }
        if (cfObj.NGT_KTBA_CHECKTGTOIDA != '0' && $('#cboXUTRIKHAMBENHID').val() != '2' && $('#cboXUTRIKHAMBENHID').val() != '6') {
            var diffInMinutes = getMinutesDiff($('#hidNGAYTN').val(), $('#txtTHOIGIANRAVIEN').val());
            if (diffInMinutes > cfObj.NGT_KTBA_CHECKTGTOIDA.split('@')[1]) {
                var msgchk = 'Bệnh nhân có thời gian khám vượt quá thời gian khám tối đa được cấu hình';
                if (cfObj.NGT_KTBA_CHECKTGTOIDA.split('@')[0] == '2') {
                    if (!confirm(msgchk + ', Có tiếp tục?')) {
                        return false;
                    }
                }else {
                    DlgUtil.showMsg(msgchk+"!");
                    return false;
                }
            }
        }
        //dannd_L2PT-84144
        if (cfObj.HIS_CHANXUTRI_NHOHONTGHIENTAI == '1') {
            if (checkTime($('#txtTHOIGIANRAVIEN').val(), thoigianmopopup)) {
                DlgUtil.showMsg("Thời gian xử trí nhỏ hơn so với thời gian hiện tại không thể xử trí!");
                return false;
            }
        }
        //dannd_L2PT-76338
        if (cfObj.HIS_CHANXUTRI_SUATHOIGIAN != '0') {
            var cxtstg = cfObj.HIS_CHANXUTRI_SUATHOIGIAN.split(';');
            for (var i = 0; i < cxtstg.length; i++) {
                var loaidieutri = cxtstg[i].split(',')[0];
                var khoangthoigian = cxtstg[i].split(',')[1];
                if (loaidieutri == 1) {
                    var result = compareTimeWithCurrent($("#txtTHOIGIANRAVIEN").val(), parseInt(khoangthoigian));
                    if (!result) {
                        DlgUtil.showMsg("Thời gian xử trí lớn hơn " + khoangthoigian + " phút so với thời gian hiện tại không thể xử trí!");
                        return false;
                    }
                }
            }
        }
        //End_Dannd_L2PT-76338
        var CHECK65BHYT = cfObj.CHECK65BHYT;
        var chck65xutri = cfObj.CHECK65BHYT_XUTRI;
        //Beg_HaNv_110123: L2PT-30928
        var chkCapCuu = true;
        var chkocheckcc = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", '65BN_KOCHECK_CAPCUU');
        if (chkocheckcc == '1' && $("#hidHINHTHUCVAOVIENID").val() == "2") {
            chkCapCuu = false;
        }
        //End_HaNv_110123
        if (chck65xutri != "0" && parseInt(CHECK65BHYT) > 0 &&
            ( (cfObj.CHECK65BN_CHECKALL_DOITUONG == 0 && $("#cboDOITUONGBENHNHANID").val() == 1) || cfObj.CHECK65BN_CHECKALL_DOITUONG == 1 ) &&
            chkCapCuu && check_tgksk == 0) {
            var so_bn_bhyt = 0;
                //HaNv_110123: L2PT-30928
            var _obj = new Object();
            _obj.TYPE = "2";
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK.65BNBHYT", JSON.stringify(_obj) );
            if (ret != 0 ){
                so_bn_bhyt = ret;
            }
            if (parseInt(so_bn_bhyt) >= parseInt(CHECK65BHYT)) {
                if (chck65xutri == "1") {
                    DlgUtil.showMsg("Khám quá " + CHECK65BHYT + " bệnh nhân Bảo hiểm y tế trong ngày");
                    return;
                } else {
                    DlgUtil.showConfirm("Khám quá " + CHECK65BHYT + " bệnh nhân Bảo hiểm y tế trong ngày, bạn có muốn tiếp tục?", function (flag) {
                        if (flag) {
                            checkKham5P(_loai);
                        } else {
                            return false;
                        }
                    });
                }
            } else {
                checkKham5P(_loai);
            }
        } else {
            checkKham5P(_loai);
        }
    }
    function checkKham5P(mode) {
        var _obj1 = new Object();
        _obj1.PHONGKHAMDANGKYID = $('#hidPHONGKHAMDANGKYID').val();
        _obj1.HINHTHUCXUTRIID = $("#cboXUTRIKHAMBENHID").val();
        _obj1.HINHTHUCVAOVIENID = $("#hidHINHTHUCVAOVIENID").val();
        _obj1.THOIGIANRAVIEN = $("#txtTHOIGIANRAVIEN").val();
        var _check5p = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHECKKHAM5PF", JSON.stringify(_obj1));
        var _check5pArr = _check5p.split('@');
        if (_check5pArr[0] == "1" && check_tgksk == 0) {
            DlgUtil.showConfirm("BN có thời gian khám bệnh dưới " + _check5pArr[1] + " phút bạn có muốn tiếp tục?", function (flag) {
                if (flag) {
                    if (cfObj.HIS_CANHBAO_BNTUVONG == '1' && $("#cboXUTRIKHAMBENHID").val() == '8') {
                        DlgUtil.showConfirm("Bệnh nhân xác nhận tử vong?", function (flag) {
                            if (flag) {
                                _checkDongTien(mode); 						// sondn L2PT-27006
                            }
                        });
                    } else {
                        _checkDongTien(mode); 						// sondn L2PT-27006
                    }
                    //ductx -bvtm-5439
                    if (opt._hospital_id == '10284') {
                        sendSmsAuto();
                    }
                    //end bvtm-5439
                }
            });
        } else if (_check5pArr[0] == "2" && check_tgksk == 0) {
            DlgUtil.showMsg("BN có thời gian khám bệnh dưới " + _check5pArr[1] + " phút không thể thao tác!");
            return;
        } else if (_check5pArr[0] == "-2") {
            DlgUtil.showMsg("BN chưa có ngày bắt đầu khám, yêu cầu kiểm tra lại. ");
            return;
        } else {
            if (cfObj.HIS_CANHBAO_BNTUVONG == '1'
                && $("#cboXUTRIKHAMBENHID").val() == '8') {
                DlgUtil.showConfirm("Bệnh nhân xác nhận tử vong?", function (flag) {
                    if (flag) {
                        _checkDongTien(mode); 						// sondn L2PT-27006
                    }
                });
            } else {
                _checkDongTien(mode); 						// sondn L2PT-27006
            }
            //ductx -bvtm-5439
            if (opt._hospital_id == '10284') {
                sendSmsAuto();
            }
            //end bvtm-5439
        }
    }
    //L2PT-22949
    function _checkDongTien(mode) {
        var NGT_CANHBAO_DONGTIEN = cfObj.NGT_CANHBAO_DONGTIEN;
        if (NGT_CANHBAO_DONGTIEN == '1') {
            var check_cp = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.GET.CHIPHI", $("#hidTIEPNHANID").val() + '$' + '9');
            if (check_cp != '0') {
                DlgUtil.showConfirm("Bệnh nhân còn dịch vụ chưa đóng tiền, Bạn có kết thúc khám chữa bệnh không? ", function (flag) {
                    if (flag) {
                        _check_moBADN(mode);
                    }
                });
            } else
                _check_moBADN(mode);
        } else
            _check_moBADN(mode);
    }

    function _check_moBADN(mode) {
        if (cfObj.NGT_CHECKXUTRI_MOBADN != '0') {
            var _par = [$('#hidHOSOBENHANID').val()];
            var check_badn = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHECKXUTRIMOBADN", _par.join('$'));
            if (check_badn == '0') {
                if (cfObj.NGT_CHECKXUTRI_MOBADN.split(',')[0] == '1') {
                    DlgUtil.showConfirm(" Bệnh nhân chưa được mở bệnh án để quản lý bệnh mãn tính theo mặt bệnh " + $("#txtMACHANDOANRAVIEN").val() + " - " + $("#txtCHANDOANRAVIEN").val() + ".Có muốn tiếp tục?", function (flag) {
                        if (flag) {
                            _checkICD_vuotchiphi(mode);
                        }
                    });
                } else {
                    DlgUtil.showMsg(" Bệnh nhân chưa được mở bệnh án để quản lý bệnh mãn tính theo mặt bệnh " + $("#txtMACHANDOANRAVIEN").val() + " - " + $("#txtCHANDOANRAVIEN").val() + ".Yêu cầu mở bệnh án để quản lý bệnh mán tính!");
                }
            } else {
                _checkICD_vuotchiphi(mode);
            }
        } else
            _checkICD_vuotchiphi(mode);
    }

    //L2PT-28890
    function _checkICD_vuotchiphi(mode) {
        if (cfObj.NGT_CANHBAO_VUOT_ICD == '1') {
            var check_cp = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHIPHI.ICD", $("#hidTIEPNHANID").val() + '$' + $("#txtMACHANDOANRAVIEN").val());
            if (check_cp != '0') {
                DlgUtil.showConfirm("Tổng tiền đã vượt qua hạn mức bệnh, Bạn có muốn tiếp tục? ", function (flag) {
                    if (flag) {
                        _capnhat(mode);
                    }
                });
            } else
                _capnhat(mode);
        } else
            _capnhat(mode);
    }

    // END DANND L2PT-19805
    function _capnhat(inMB) {
        if (_validate()) {
            objData = new Object();
            FormUtil.setFormToObject("khambenh", "", objData);
            var xx = _objData;
            if (typeof xx === 'undefined') {
                var sql = "";
                if (objData['XUTRIKHAMBENHID'] == "8") {
                    sql = "NGT02K010.RV001";
                } else if (objData['XUTRIKHAMBENHID'] == "1" || objData['XUTRIKHAMBENHID'] == "3" || objData['XUTRIKHAMBENHID'] == "9") {
                    sql = "NGT02K007.RV001";
                } else if (objData['XUTRIKHAMBENHID'] == "4" || objData['XUTRIKHAMBENHID'] == "5" || objData['XUTRIKHAMBENHID'] == "14") {
                    sql = "NGT02K008.RV001";
                } else if (objData['XUTRIKHAMBENHID'] == "7") {
                    sql = "NGT02K009.RV004";
                }

                if (sql != "") {
                    var sql_par = [];
                    sql_par.push({"name": "[0]", "value": $('#hidKHAMBENHID').val()});
                    data = jsonrpc.AjaxJson.ajaxExecuteQueryO(sql, sql_par);
                    var rows = $.parseJSON(data);
                    if (rows.length > 0) {
                        _objData = $.extend(_objData, rows[0]);
                    }
                }
            }
            if ($("#cboDOITUONGBENHNHANID").val() == '1' && cfObj.HIS_CHECKMALOAIKCB_7 != '0' && $('#cboXUTRIKHAMBENHID').val() != 1 && $('#cboXUTRIKHAMBENHID').val() != 14) {
                var maloaikcb = jsonrpc.AjaxJson.ajaxCALL_SP_S("HIS.GETMALOAIKCB", $("#hidTIEPNHANID").val());
                if (maloaikcb == '07') {
                    DlgUtil.showMsg("Mã loại khám chữa bệnh của bệnh nhân không được phép xử trí đối với xử trí này!");
                    return false;
                }
            }
            if ($("#cboDOITUONGBENHNHANID").val() == '1' && cfObj.HIS_CHECKMALOAIKCB_10 != '0') {
                var maloaikcb = jsonrpc.AjaxJson.ajaxCALL_SP_S("HIS.GETMALOAIKCB", $("#hidTIEPNHANID").val());
                if (maloaikcb == '10') {
                    if (cfObj.HIS_CHECKMALOAIKCB_10 == '1') {
                        if (!confirm("Mã loại khám chữa bệnh của bệnh nhân là 10 chưa được cổng BHXH chấp nhận khi đẩy cổng, có muốn tiếp tục?")) {
                            return -1;
                        }
                    } else {
                        DlgUtil.showMsg("Mã loại khám chữa bệnh của bệnh nhân là 10 chưa được cổng BHXH chấp nhận khi đẩy cổng!");
                        return -1;
                    }
                }
            }
            _objData = $.extend(_objData, objData);
            _objData['PHONGKHAMID'] = opt.phongid;
            _objData['XUTRIKHAMBENHID'] = $('#cboXUTRIKHAMBENHID').val();
            //tuyennx_add_start_20170823 yc HISL2NT-193
            _objData['CHANDOANCHUYENVIEN'] = _chandoanchuyenvien;
            //tuyennx_add_end_20170823 yc HISL2NT-193

            if (cfObj.QD_4750 == 1 && ($('#cboXUTRIKHAMBENHID').val() == '5' || ($('#cboXUTRIKHAMBENHID').val() == '14' && cfObj.NGT_MOPOPUPHENKHAM_XUTRI14 == 1))) {
                if (_objData['THOIGIANLICHHEN'] == null || _objData['THOIGIANLICHHEN'] == '') {
                    DlgUtil.showMsg("Không có dữ liệu hẹn khám, vui lòng kiểm tra lại!");
                    $('#txtTHOIGIANRAVIEN').focus();
                    return false;
                }
                if (_objData['THOIGIANLICHHEN'] != null && _objData['THOIGIANLICHHEN'] != '') {
                    if (!compareDate($('#txtTHOIGIANRAVIEN').val(), _objData['THOIGIANLICHHEN'], 'DD/MM/YYYY HH:mm:ss')) {
                        DlgUtil.showMsg("Thời gian hẹn khám không được nhỏ hơn thời gian ra viện!");
                        $('#txtTHOIGIANRAVIEN').focus();
                        return false;
                    }
                }
            }
            if ($('#cboXUTRIKHAMBENHID').val() == '1' || $('#cboXUTRIKHAMBENHID').val() == '14') {
                var myVar = {
                    khambenhid: $("#hidKHAMBENHID").val()
                };
                var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.DONTHUOC", JSON.stringify(myVar));
                if (check == '0' && cfObj.NGT_CAPTOACHOVE == "0") {
                    DlgUtil.showMsg('Bệnh nhân chưa có đơn thuốc');
                    $('#btnLuu').attr("disabled", true);
                    $('#btnLuuDong').attr("disabled", true);
                    $('#btnInPhieu').attr("disabled", true);
                    $('#btnInPhieuDoc').attr("disabled", true);
                    return;
                }
            }
            //tuyennx_add_start_20171120
            if ($('#cboXUTRIKHAMBENHID').val() == '7') {
                if (cfObj.NBN_XTCVCCNHHKTD == "1" && $("#cboKETQUADIEUTRIID").val() != 3 && $("#cboKETQUADIEUTRIID").val() != 4) {
                    DlgUtil.showMsg('Chỉ xử trí chuyển viện khi kết quả điều trị là "Nặng hơn" hoặc "Không thay đổi" !');
                    return;
                }
                if ($('#txtMAVIEN').val() == '') {
                    DlgUtil.showMsg('Bệnh nhân chưa nhập thông tin chuyển viện');
                    return;
                }
                var rs = dayCongBYT(_objData);
                if (rs == 1)
                    return;
            }
            if ($('#cboXUTRIKHAMBENHID').val() == '8') {
                if ($('#txtTHOIDIEMTUVONG').val() == '') {
                    DlgUtil.showMsg('Bệnh nhân chưa nhập thông tin tử vong');
                    return;
                }
            }
            if (cfObj.QD_4750 == 1 && $('#cboXUTRIKHAMBENHID').val() != '2' && $('#cboXUTRIKHAMBENHID').val() != '6' && $("#txtKHAMBENH_PPDIEUTRI").val() == '') {
                DlgUtil.showMsg('Chưa nhập phương pháp điều trị!');
                $("#txtKHAMBENH_PPDIEUTRI").focus()
                return;
            }

            if (_hienthisvv == "1" && $("#cboXUTRIKHAMBENHID").val() == "6") {
                _objData["SOVAOVIEN"] = $("#txtSOVAOVIEN").val().trim();
            } else {
                _objData["SOVAOVIEN"] = "";
            }

            var sysdate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
            if (cfObj.NGT_KIEMTRA_THOIGIAN_XUTRI == 1 && $('#txtTHOIGIANRAVIEN').val().indexOf(sysdate) == '-1' && $('#cboXUTRIKHAMBENHID').val() !== '2' && $('#cboXUTRIKHAMBENHID').val() !== '6') {
                DlgUtil.showMsg('Thời gian xử trí phải là ngày hiện tại');
                return;
            }
            // dannd_L2PT-22445 thoi gian xu tri nam trong khoang cau hinh  SO_NGAY_XUTRI_DIEUTRI thiet lap
            if (cfObj.SO_NGAY_XUTRI_DIEUTRI != '0' && $('#cboXUTRIKHAMBENHID').val() !== '2' && $('#cboXUTRIKHAMBENHID').val() !== '6') {
                //HaNv_201223: Chuyển cấu hình số ngày thành số giờ - L2PT-70339
                var _songay = parseInt(cfObj.SO_NGAY_XUTRI_DIEUTRI);
                var ngayHientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MM:SS');
                var ngayRa = $('#txtTHOIGIANRAVIEN').val();
                var t_truoc = diffDate(ngayHientai, ngayRa, 'DD/MM/YYYY HH:mm', 'minutes') / 60;
                var t_sau = diffDate(ngayRa, ngayHientai, 'DD/MM/YYYY HH:mm', 'minutes') / 60;
                if (t_truoc > _songay || t_sau > _songay) {
                    return DlgUtil.showMsg("Thời gian xử trí của bệnh nhân không nằm trong khoảng với cấu hình SO_NGAY_XUTRI_DIEUTRI đã thiết lập");
                }
            }

            // sondn L2PT-5758
            if (_hienthiloaiba == "1" && $("#cboXUTRIKHAMBENHID").val() == "6") {
                if ($("#cboLOAIBA").val() == null || $("#cboLOAIBA").val() == 'null' || $("#cboLOAIBA").val() == "0") {
                    _loaibamacdinh = cfObj.NGT_LOAIBA_XT;
                    if (_loaibamacdinh == "0") {
                        DlgUtil.showMsg('Yêu cầu chọn loại bệnh án cho xử trí nhập viện. ');
                        return;
                    } else {
                        _objData["LOAIBA"] = _loaibamacdinh; 		// neu k chon thi truyen vao loai benh an mac dinh; k canh bao nua;
                    }
                }
            }
            // sondn L2PT-5758

            if (_hienthisvv == "1" && $("#cboXUTRIKHAMBENHID").val() == "6") {
                if ($("#txtSOVAOVIEN").val().trim() == "" || isNaN($("#txtSOVAOVIEN").val().trim())) {
                    DlgUtil.showMsg('Số vào viện không hợp lệ. ', function () {
                        $("#txtSOVAOVIEN").focus();
                    });
                    return;
                }

            }

            //tuyennx_add_end_20171120
            //tuyennx_add_start_20180222 L2DKHN-744
//			var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.CANNANG1TUOI",$('#hidKHAMBENHID').val());
//			if(resultCheck == '1'){
//				DlgUtil.showMsg("Bệnh nhân dưới 1 tuổi và chưa nhập cân nặng không kết thúc khám được");
//			   return;
//			}
            //tuyennx_add_end_20180222

            //tuyennx_add_start_20190308 L2PT-2448
            if ($("#cboDOITUONGBENHNHANID").val() == '1') {
                var result_tl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV017", $("#hidTIEPNHANID").val());
                if (result_tl != 'OK') {
                    DlgUtil.showMsg('Dịch vụ ' + result_tl + ' có tỷ lệ dịch vụ không hợp lý.');
                    if (cfObj.HIS_CHAN_SAI_TYLE == '1') {
                        return;
                    }
                }
            }
            //tuyennx_add_end_20190308

            //tuyennx_edit_start_20191209 L2PT-12390
            var _sql_par = [];
            _sql_par.push({
                "name": "[0]",
                value: $("#hidTIEPNHANID").val()
            });
            _sql_par.push({
                "name": "[1]",
                value: $("#txtTHOIGIANRAVIEN").val().length == 19 ? $("#txtTHOIGIANRAVIEN").val() : $("#txtTHOIGIANRAVIEN").val() + ':00'
            });

            //tuyennx_add_start_L2PT-16401
            var rs = jsonrpc.AjaxJson.getOneValue("NGT.NGT.TGKHAM", _sql_par);
            if (rs > 0) {
                DlgUtil.showMsg("BN có thời gian ra viện nhỏ hơn thời gian tiếp nhận, vui lòng nhập lại!");
                return;
            }

            if (cfObj.NGT_CHECK_TG_KHAM_CLS != '0') {
                var _obj1 = new Object();
                _obj1.PHONGKHAMDANGKYID = $('#hidPHONGKHAMDANGKYID').val();
                _obj1.HINHTHUCXUTRIID = $("#cboXUTRIKHAMBENHID").val();
                _obj1.THOIGIANRAVIEN = $("#txtTHOIGIANRAVIEN").val();
                var _checktg = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.TGKHAM.CLS", JSON.stringify(_obj1));
                if (_checktg == '1') {
                    DlgUtil.showMsg("BN có chỉ định dịch vụ CLS thời gian khám bệnh dưới " + cfObj.NGT_CHECK_TG_KHAM_CLS + " phút không thể thao tác!");
                    return;
                }
            }
            if (cfObj.NGT_XUTRI_CHECKPHONGCHUAKT != '0') {
                var _sql_par = [];
                _sql_par.push({
                    "name": "[0]",
                    "value": $("#hidKHAMBENHID").val()
                });
                _sql_par.push({
                    "name": "[1]",
                    "value": $("#hidPHONGKHAMDANGKYID").val()
                });
                var datapk = jsonrpc.AjaxJson.getOneValue("NGT_XUTRI_PKCHUAKT", _sql_par);
                if (datapk != null && datapk != 'null' && datapk != '' && datapk != undefined && checkphongdangkham == 0) {
                    DlgUtil.showConfirm("Còn các phòng khám : " + datapk + " chưa kết thúc.Có muốn hoàn tất khám cho bệnh nhân? ", function (flag) {
                        if (flag) {
                            checkphongdangkham = 1;
                            _capnhat(inMB);
                        } else {
                            return false;
                        }
                    });
                    return false;
                }
            }
            checkphongdangkham = 0;
            if (cfObj.NGT_XUTRI_CHECKDVCHUAHOANTHANH != '0') {
                var objData = new Object();
                objData["HOSOBENHANID"] =  $("#hidHOSOBENHANID").val();
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHECKDVCHUAHT",JSON.stringify(objData));
                if (ret != '0') {
                    if (cfObj.NGT_XUTRI_CHECKDVCHUAHOANTHANH == '1') {
                        if (!confirm("Tồn tại các mẫu bệnh phẩm có số phiếu : " + ret + " chưa hoàn thành!")) {
                            return -1;
                        }
                    } else {
                        DlgUtil.showMsg("Tồn tại các mẫu bệnh phẩm có số phiếu : " + ret + " chưa hoàn thành!");
                        return -1;
                    }
                }
            }
            if (cfObj.NGT_XUTRI_CHECKBSKHAM != '0') {
                var objData = new Object();
                objData["PHONGKHAMDANGKYID"] =  $("#hidPHONGKHAMDANGKYID").val();
                objData["KHAMBENHID"] =  $("#hidKHAMBENHID").val();
                var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.CHECKBSKHAM",JSON.stringify(objData));
                if (ret != '0') {
                    if (cfObj.NGT_XUTRI_CHECKBSKHAM == '1') {
                        if (!confirm("Bác sĩ khám là " + ret.split('@')[0] + " khác với bác sĩ xử trí là " + ret.split('@')[1] +  " bạn có muốn xử trí hay không ?")) {
                            return -1;
                        }
                    } else {
                        DlgUtil.showMsg("Bác sĩ khám là " + ret.split('@')[0] + " khác với bác sĩ xử trí là " +ret.split('@')[1]+  "  không thể lưu xử trí!");
                        return -1;
                    }
                }
            }
            var NGT_XUTRI_CHECKMBPBHYTCHUACOKQ = cfObj.NGT_XUTRI_CHECKMBPBHYTCHUACOKQ;
            if (NGT_XUTRI_CHECKMBPBHYTCHUACOKQ != '0') {
                var _sql_par = [];
                _sql_par.push({
                    "name": "[0]",
                    "value": $("#hidKHAMBENHID").val()
                });
                var datakq = jsonrpc.AjaxJson.getOneValue("NGT_CHECK_PHIEUKQ", _sql_par);
                if (datakq != null && datakq != 'null' && datakq != '' && datakq != undefined && checkphieuchuacokq == 0 &&
                    ($("#cboXUTRIKHAMBENHID").val() == "1" || $("#cboXUTRIKHAMBENHID").val() == "13" || $("#cboXUTRIKHAMBENHID").val() == "15")) {
                    if (NGT_XUTRI_CHECKMBPBHYTCHUACOKQ == '1') {
                        DlgUtil.showMsg("Tồn tại các mẫu bệnh phẩm có số phiếu : " + datakq + " chưa có kết quả!");
                        return false;
                    } else if (NGT_XUTRI_CHECKMBPBHYTCHUACOKQ == '2') {
                        DlgUtil.showConfirm("Tồn tại các mẫu bệnh phẩm có số phiếu : " + datakq + " chưa có kết quả.Có muốn hoàn tất khám cho bệnh nhân? ", function (flag) {
                            if (flag) {
                                checkphieuchuacokq = 1;
                                _capnhat(inMB);
                            } else {
                                return false;
                            }
                        });
                        return false;
                    }
                }
            }
            checkphieuchuacokq = 0;
            //tuyennx_add_end_L2PT-16401

            // sondn L2PT-25869
            if (_batbuockqdt == "1") {
                var _kqdtt = $("#cboKETQUADIEUTRIID").val();
                if (_kqdtt == null || _kqdtt == 'null' || _kqdtt == '0' || _kqdtt == "-1") {
                    DlgUtil.showMsg("Chưa nhập thông tin kết quả điều trị. ");
                    return;
                }
            }
            // end sondn L2PT-25869

            var arr_vienphi = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.05", $("#hidTIEPNHANID").val());
            if (cfObj.NGT_CHECK_VUOTCANBHYT == 1 && ($("#cboXUTRIKHAMBENHID").val() == '1' || $("#cboXUTRIKHAMBENHID").val() == '9') && checktien == 0) {
                if (arr_vienphi[0].TAMUNG_CONLAI < 0) {
                    DlgUtil.showConfirm("Bệnh nhân có tổng chi phí khám bệnh lớn hơn 15% mức lương cơ sở. Có muốn hoàn tất khám cho bệnh nhân?" +
                        "Tổng chi phí khám bệnh của bệnh nhân là " + formatNumber(arr_vienphi[0].TONGTIENDV) + "đ, BHYT sẽ chi trả " + formatNumber(arr_vienphi[0].BHYT_THANHTOAN) + "đ, bệnh nhân cùng chi trả " + formatNumber(arr_vienphi[0].BNTRA) + "đ.", function (flag) {
                        if (flag) {
                            checktien = 1;
                            _capnhat(inMB);
                        } else {
                            return false;
                        }
                    });
                    return false;
                }
            }
            // sondn L2PT-29895
            //if (_mabandh == "1" && $("#txtMABENHANDAINGAY").val().trim() == ""){
            //	DlgUtil.showMsg("Yêu cầu nhập thông tin mã bệnh án. ");
            //	return ;
            //}
            // end sondn L2PT-29895

            var sinhTonCanNang = cfObj.NGT_SINHTON_CANNANG;
            if (sinhTonCanNang != 0 && ($("#hidDVTUOI").val() == 2 && Number($('#hidTUOI').val()) < sinhTonCanNang * 12
                || $("#hidDVTUOI").val() == 1 && Number($('#hidTUOI').val()) < sinhTonCanNang
                || $("#hidDVTUOI").val() == 3 && Number($('#hidTUOI').val()) < sinhTonCanNang * 365) && !$('#hidKHAMBENH_CANNANG1').val()) {
                DlgUtil.showMsg("Bệnh nhân dưới " + sinhTonCanNang + " tuổi yêu cầu nhập cân nặng");
                return false;
            }

            // dannd L2PT-32085
            var checkthongtin_nguoinha = cfObj.NGT_CHECK_THONGTINNGUOINHA;
            if (checkthongtin_nguoinha != 0 && ($("#hidDVTUOI").val() == 2 && Number($('#hidTUOI').val()) < checkthongtin_nguoinha * 12
                    || $("#hidDVTUOI").val() == 1 && Number($('#hidTUOI').val()) < checkthongtin_nguoinha
                    || $("#hidDVTUOI").val() == 3 && Number($('#hidTUOI').val()) < checkthongtin_nguoinha * 365)
                && !$('#txtTENNGUOITHAN').val() && $("#cboXUTRIKHAMBENHID").val() == '6') {
                DlgUtil.showMsg("Bệnh nhân dưới " + checkthongtin_nguoinha + " tuổi yêu cầu nhập thông tin người nhà! ");
                return false;
            }

            _objData["LS"] = null;					// tam thoi set = null; 
            _objData["LOAIBA"] = _objData["LOAIBA"] == null || _objData["LOAIBA"] == 'null' ? "23" : _objData["LOAIBA"];
            //L2PT-96895
            if (cfObj.VALID_XUTRI == "1" &&
                ($("#hidKHAMBENH_CANNANG1").val() == "" || $("#hidLYDOVAOVIEN1").val() == "" || $("#hidQUATRINHBENHLY1").val() == ""
                    || $("#hidTIENSUBENH_BANTHAN1").val() == "" || $("#hidTIENSUBENH_GIADINH1").val() == "" || $("#hidKHAMBENH_TOANTHAN1").val() == ""
                    || $("#hidKHAMBENH_MACH1").val() == "" || $("#hidKHAMBENH_NHIETDO1").val() == "" || $("#hidKHAMBENH_HUYETAP_LOW1").val() == ""
                    || $("#hidKHAMBENH_HUYETAP_HIGH1").val() == "" || $("#hidKHAMBENH_BOPHAN1").val() == "" || $("#hidDAXULY1").val() == "")) {
                DlgUtil.showMsg("Bệnh nhân chưa nhập đủ một trong các thông tin sinh tồn, khám bệnh hỏi bệnh! ");
                return false;
            }
            if ((parseFloat($("#hidKHAMBENH_CANNANG1").val()) == 0 ||
                parseFloat($("#hidKHAMBENH_MACH1").val()) == 0 ||
                parseFloat($("#hidKHAMBENH_NHIETDO1").val()) == 0 ||
                parseFloat($("#hidKHAMBENH_CHIEUCAO1").val()) == 0 ||
                parseFloat($("#hidKHAMBENH_NHIPTHO1").val()) == 0 ||
                parseFloat($("#hidKHAMBENH_HUYETAP_LOW1").val()) == 0 ||
                parseFloat($("#hidKHAMBENH_HUYETAP_HIGH1").val() == 0))
                && $("#cboXUTRIKHAMBENHID").val() != "8" && cfObj.VALID_SINHTON_XUTRI == "1") {
                DlgUtil.showMsg("Bệnh nhân có chỉ số sinh tồn bằng 0, vui lòng kiểm tra lại!");
                return false;
            }
            _objData['CHECKTRUNGTGIAN'] = checktrungtgian;
            _objData['CHECKTGIANXTRI2BN'] = checktgianxtri2bn;
            _objData["LOAIRAVIEN"] = _objData["LOAIRAVIEN"] == null || _objData["LOAIRAVIEN"] == 'null' ? "0" : _objData["LOAIRAVIEN"];
            _objData["LANHDAOVIENID"] = _objData["LANHDAOVIENID"] == null || _objData["LANHDAOVIENID"] == 'null' ? "0" : _objData["LANHDAOVIENID"];
            _objData["LANHDAOKHOAID"] = _objData["LANHDAOKHOAID"] == null || _objData["LANHDAOKHOAID"] == 'null' ? "0" : _objData["LANHDAOKHOAID"];

            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K005_PKBNHAP", JSON.stringify(_objData));
            var rets = ret.split(',');

            if (rets[0] == 'r_checkdl' && rets.length > 1) {
                var myVar = {
                    thongbao: rets[1]
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT04K006_ThongBao", myVar, "Thông báo", 600, 420);
                DlgUtil.open("dlgBPKT");
                return false;
            }
            if (rets[0] == 'badn_chanxt' && rets.length > 1) {
                DlgUtil.showMsg('Bệnh nhân đã mở bệnh án mãn tính tại '+ rets[1] + ', thực hiện đưa bệnh nhân ra khỏi bệnh án mãn tính ở các phòng này trước khi xử trí nhập viện nội trú/điều trị ngoại trú!');
                return false;
            }
            if (rets[0] == 'check_tgianxutri' && rets.length > 1) {
                if (cfObj.NGT_CHECK_TGIANXUTRI.split(',')[0] == 1) {
                    DlgUtil.showConfirm("Thời gian xử trí/ kết thúc khám quá gần so với thời gian xử trí của bệnh nhân " + rets[1] + ", bạn có tiếp tục? ", function (flag) {
                        if (flag) {
                            checktgianxtri2bn = 1;
                            _capnhat(inMB);
                        } else {
                            return false;
                        }
                    });
                    return false;
                } else {
                    DlgUtil.showMsg('Thời gian xử trí/ kết thúc khám quá gần so với thời gian xử trí của bệnh nhân ' + rets[1] + ' vui lòng thử lại trong ít phút!');
                    return false;
                }
            }
            _badaingay = rets[1];
            if ($.isNumeric(rets[0]) && ret != -1 && rets[0] != 0) {
                isLuu = true;
                //tuyennx_add_start L2K74TW-206 tu dong in phieu chuyen vien
                var HIS_TUDONG_IN_PHIEU_CV = cfObj.HIS_TUDONG_IN_PHIEU_CV;
                if (HIS_TUDONG_IN_PHIEU_CV == '1' && $('#cboXUTRIKHAMBENHID').val() == "7") {
                    var fileReport = _getTypePrint(); //check
                    _printPhieu($('#hidKHAMBENHID').val(), fileReport);
                }
                //tuyennx_add_end L2K74TW-206
                //tuyennx_edit_start_20190425 L1PT-661 L2PT-14910
                //L2PT-40477
                if ($('#cboXUTRIKHAMBENHID').val() == "2" || $('#cboXUTRIKHAMBENHID').val() == "6") {
                    gw_tiepnhankham($('#hidTIEPNHANID').val());
                    //gw_batdaukham($('#hidTIEPNHANID').val());
                }

                DlgUtil.showMsg('Cập nhật thông tin thành công', undefined, cfObj.HIS_TIMEOUT_THONGBAO);

                _dayDonThuocDT_KTK(cfObj.DTDT_DAY_DONTHUOC, $('#hidKHAMBENHID').val(), "1")	 //L2PT-30504

                //tuyennx_edit_end_20190425 L1PT-661
                //$('#btnThuoc').attr("disabled", true);
                var _xtsauluu = cfObj.NGT_XTDISABLE_LUU; 				// mac dinh: btnThuoc
                if (_xtsauluu != '0') {
                    var _xtsauluuArr = _xtsauluu.split('@');
                    for (t = 0; t < _xtsauluuArr.length; t++) {
                        $('#' + _xtsauluuArr[t]).attr("disabled", true);
                    }
                }

                //tuyennx_add_start day hssk
                var HIS_KETTHUC_KHAM_KHI_XUTRI = cfObj.HIS_KETTHUC_KHAM_KHI_XUTRI;
                if (HIS_KETTHUC_KHAM_KHI_XUTRI == 1) {
                    if (cfObj.KHAM_ONLINE_WS == "1") {
                        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT", [$('#hidKHAMBENHID').val(), _opts.hospital_code].join('$'));
                        if (data_ar && data_ar.length > 0) {
                            var objKhamOL = {
                                "NGAYKETTHUC": data_ar[0].NGAY_RAVIEN,
                                "MALUOTKHAM_HIS": $("#hidMAHOSOBENHAN").val(),
                                "TRANGTHAI": "1",
                                "NGAYHEN": data_ar[0].THOIGIANLICHHEN,
                                "DONTHUOC": data_ar[0].SOPHIEU == "" ? '0' : data_ar[0].SOPHIEU,
                                "MA_ICD": data_ar[0].MACHANDOANRAVIEN,
                                "TEN_ICD": data_ar[0].CHANDOANRAVIEN,
                                "BACSY": _opts.fullname,
                                "MABS": opt.user_name, //L2PT-5883
                                "BENHPHU": data_ar[0].BENHPHU,
                                "PHONGKHAM": _opts._subdept_name,
                                "HUONG_DIEUTRI": data_ar[0].HUONGDIEUTRI
                            };
                            //var objKhamOL = new Object();
//							var inputJson = JSON.stringify(objKhamOL);
                            //var inputJson = "{\"NGAYKETTHUC\":\"20200410 1600\",\"MALUOTKHAM_HIS\":\"BV0000017641\",\"TRANGTHAI\":\"1\",\"NGAYHEN\":\"\",\"DONTHUOC\":\"\",\"MA_ICD\":\"0004607 - Chính\",\"TEN_ICD\":\"0004607 - Chính\"}";
//							var ret = ajaxSvc.KhamOnlineWS.sendDataKetThuc(inputJson);
//							if(ret.includes("OK;")){
//								DlgUtil.showMsg("Gửi thông tin kết thúc khám thành công!");
//							}else{
//								DlgUtil.showMsg("Lỗi gửi thông tin kết thúc khám!");
//							}
                            var _objThongtinLog = new Object();
                            _objThongtinLog["I_CHUCNANG"] = "Trả KQK 1: " + $("#hidMAHOSOBENHAN").val();
                            _objThongtinLog["I_KQKETNOI"] = JSON.stringify(objKhamOL);
                            _objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
                            var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS", JSON.stringify(_objThongtinLog));

                            var ret = ajaxSvc.KhamOnlineWS.callApiAppBN('1', objKhamOL, opt.hospital_code);

                            _objThongtinLog["I_CHUCNANG"] = "Trả KQK 2: " + $("#hidMAHOSOBENHAN").val();
                            _objThongtinLog["I_KQKETNOI"] = ret;
                            _objThongtinLog["MADANGKY"] = data_ar[0].MADANGKY;
                            var f2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS", JSON.stringify(_objThongtinLog));

                            if (ret == "-1") {
                                DlgUtil.showMsg("Đã có lỗi xảy ra khi gửi dữ liệu khám online !");
                            }
                        }
                    }
                    dayCongHSSK($("#hidKHAMBENHID").val());
                }
                //tuyennx_add_end

                _luu = 1;
                if (inMB == 1) {
                    if (opt._hospital_id == "1133" && $("#cboXUTRIKHAMBENHID").val() != "2" && $("#cboXUTRIKHAMBENHID").val() != "6") {
                        var cau_hinh_phieu_in = cfObj.NGT_KB_MHC_TEMP_KB_NV;
                        var template = (cau_hinh_phieu_in.split(";"));
                        let map = new Map();
                        for (var i = 0; i < template.length; i++) {
                            var value = (template[i].split(","));
                            map.set(value[0].trim(), value[1].trim());
                        }
                        _openReport(par, map.get($("#cboDOITUONGBENHNHANID").val()), 'pdf');
                    }
                    if (cfObj.NGT_INGIAYRAVIEN_KHIXUTRI == "1") {
                        if (cfObj.NGT_HIENTHI_RPT == 1 && $('#cboXUTRIKHAMBENHID').val() !== '2') {
                            $("#btnInPhieu").trigger('click');
                        }
                    }
                    //L2PT-30165
                    else {
                        if (cfObj.NGT_INGIAYHENKHAM_KHIXUTRI == 1 && $('#cboXUTRIKHAMBENHID').val() == '5')
                            $("#btnInPhieu").trigger('click');
                    }

                    // sondn L2PT-4494
                    if (cfObj.VPI_AUTO_DUYET_BHYT_NGT == '1'
                        && $("#cboDOITUONGBENHNHANID").val() == "1" && $("#cboXUTRIKHAMBENHID").val() !== "2" && $("#cboXUTRIKHAMBENHID").val() !== "6") {
                        var dtar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT10", opt.khambenhid + '$' + opt.phongid + '$' + opt.hosobenhanid);
                        if (dtar != null && dtar.length > 0) {
                            if (dtar[0].TRANGTHAITIEPNHAN == "1") {
                                var _duyet_bh = duyetBHYT($("#hidTIEPNHANID").val(), opt.user_id, opt.hospital_code);
                                var ltext = "Tự động duyệt BHYT khi xử trí khám bệnh ngoại trú: " + _duyet_bh;
                                save_log_act_form("duyetBHYT", "TUDONGDUYETBHYT_NGT", ltext, $("#hidTIEPNHANID").val().toString());
                                if (_duyet_bh != 0) {
                                    DlgUtil.showMsg("Có lỗi xảy ra trong quá trình đẩy dữ liệu lên cổng giám định!");
                                }
                            }
                        } else {
                            DlgUtil.showMsg("Không lấy được thông tin bệnh nhân sau xử trí. 1526");
                        }
                    }
                    var evFunc = EventUtil.getEvent("assignSevice_closephieukham");
                    if (typeof evFunc === 'function') {
                        evFunc({msg: $('#cboXUTRIKHAMBENHID').val(), type: 1, badaingay: rets[1]});
                    } else {
                        console.log('evFunc not a function');
                    }
                }

                if (_objData.PrintCV == 1) {
                    var par = [{
                        name: 'khambenhid',
                        type: 'String',
                        value: $("#hidKHAMBENHID").val()
                    }];

                    if (opt.hospital_id == "902") {				// BVNT: in 2 ban khac nhau cho BHYT va DV; 
                        if ($("#hidDOITUONGBENHNHANID").val() == "1") {
                            openReport('window', "NGT003_GIAYCHUYENTUYEN_BHYT_A4_902", "pdf", par);
                        } else {
                            openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
                        }
                    } else if (opt.hospital_id == "965") {					// DA KHOA BUU DIEN IN DOCX
                        var rpName = "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
                        CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'docx', par, rpName);
                    } else if (opt.hospital_id == "932") {					// NHQNM: in mau khac; 
                        openReport('window', "NGT003_GIAYCHUYENTUYEN_BHYT_A4_932", "pdf", par);
                    } else {
                        var rpName = "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                        CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'rtf', par, rpName);
                    }
                }

                // nhanh return 100; 
                //L2PT-96127
                var dsxutri = cfObj.HIS_DSXUTRI_TUDONG_INBANGKE.split(',')
                if (cfObj.HIS_TUDONG_IN_BANGKE == "1" && dsxutri.includes($("#cboXUTRIKHAMBENHID").val())) {
                    _printBangKe();
                }
                //L2PT-43380
                else if (cfObj.HIS_TUDONG_IN_BANGKE == "2"
                    && dsxutri.includes($("#cboXUTRIKHAMBENHID").val())) {
                    //kiểm tra bệnh án này ở tất cả các phòng khám đã kết thúc thì mới in
                    var objData = new Object({
                        KEY: 'NGT02K005_PHIEUKHAMBENH_CHECK_KTKHAM'
                        , HOSOBENHANID: opt.hosobenhanid
                    });
                    var ret = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU_FUNC_ACTION", [JSON.stringify(objData)].join('$'));
                    if (ret && ret.length > 0 && ret[0].CHECK_KTKHAM == 0) {
                        _printBangKe();
                    }
                }

                // nhanh return 200; 
                if (cfObj.HIS_TUDONG_IN_PHIEU_NV == "1" && $("#cboXUTRIKHAMBENHID").val() == "6") {
                    var par = [];
                    if (opt._hospital_id == "965") {			// BDHCM : xuat excel chuc nang nay; 
                        par = [{
                            name: 'i_hid',
                            type: 'String',
                            value: opt._hospital_id
                        }, {
                            name: 'i_sch',
                            type: 'String',
                            value: opt.db_schema
                        }, {
                            name: 'khambenhid',
                            type: 'String',
                            value: $("#hidKHAMBENHID").val()
                        }];


                        if ($("#cboDOITUONGBENHNHANID").val() == 3) {
                            var rpName = "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                            CommonUtil.inPhieu('window', "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'xlsx', par, rpName);
                        } else {
                            var rpName = "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
                            CommonUtil.inPhieu('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'xlsx', par, rpName);
                        }
                    } else if (opt._hospital_id == "26320") {	// DKHTH
                        _printPhieu($('#hidKHAMBENHID').val(), "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4");
                    }  else if (opt._hospital_id == "951") {	// thay bang 951 benh nhiet doi khi upcode
                            var _fileBA = $('#cboLOAIBA' + " option:selected").attr('extval0');
                            var par = [ {
                                name : 'benhnhanid',
                                type : 'String',
                                value : $('#hidBENHNHANID').val()
                            },
                            {
                                name : 'hosobenhanid',
                                type : 'String',
                                value : $('#hidHOSOBENHANID').val()
                            },{
                                name : 'khambenhid',
                                type : 'String',
                                value : $('#hidKHAMBENHID').val()
                            },{
                                name : 'report_code_loaiba',
                                type : 'String',
                                value : _fileBA
                            },{
                                name:'khoaid',
                                type:'String',
                                value:opt.khoaid
                            }
                        ];
                        openReport('window', "PHIEU_IN_BENHNHAN_NHAPVIEN", 'pdf', par);
                    } else {
                        if ($("#cboDOITUONGBENHNHANID").val() == 3) {
                            if (!isKyCa) {
                                _printPhieu($('#hidKHAMBENHID').val(), "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4");
                            }
                        } else {
                            if (!isKyCa) {
                                _printPhieu($('#hidKHAMBENHID').val(), "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4");
                            }
                        }
                    }
                }
                if (cfObj.HIS_TUDONG_INPHIEUKBVV_DTNGT == "1" && $("#cboXUTRIKHAMBENHID").val() == "2") {
                    if ($("#cboDOITUONGBENHNHANID").val() == 3) {
                        if (!isKyCa) {
                            _printPhieu($('#hidKHAMBENHID').val(), "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4");
                        }
                    } else {
                        if (!isKyCa) {
                            if (opt.hospital_id == 7282) {
                                _printPhieu($('#hidKHAMBENHID').val(), "NGT005_PHIEUKBVAOVIEN_NGOAITRU_42BV01_QD4069_A4");
                            } else {
                                _printPhieu($('#hidKHAMBENHID').val(), "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4");
                            }

                        }
                    }
                }

                // SONDN L2PT-5934 START 
                var _tudongduyetketoan = cfObj.NGT_DUYETKETOAN_KTKHAM;
                var VP_DUYET_BH_KHI_DUYET_KETOAN = cfObj.VP_DUYET_BH_KHI_DUYET_KETOAN;
                if (_tudongduyetketoan == "1" && ($("#cboDOITUONGBENHNHANID").val() != "1" || (cfObj.NGT_DUYETKETOAN_KTKHAM_BNHYT == "1" && $("#cboDOITUONGBENHNHANID").val() == "1")) )  {
                    var objData_DUYET = new Object();
                    objData_DUYET["TIEPNHANID"] = $("#hidTIEPNHANID").val();
                    objData_DUYET["NGAY"] = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
                    objData_DUYET["NGAYRAVIEN"] = ($("#txtTHOIGIANRAVIEN").val() == '' ? jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS') : $("#txtTHOIGIANRAVIEN").val());
                    objData_DUYET["DATRONVIEN"] = 0;
                    objData_DUYET["SOLUONGQUYETTOAN"] = 0;
                    objData_DUYET["LOAIDUYETBHYT"] = 0;
                    objData_DUYET["KHOAID"] = opt.khoaid;
                    objData_DUYET["PHONGID"] = opt.phongid;
                    objData_DUYET["DUYET"] = 1;
                    if ($("#cboDOITUONGBENHNHANID").val() == '1' &&
                        (VP_DUYET_BH_KHI_DUYET_KETOAN == 0 || (VP_DUYET_BH_KHI_DUYET_KETOAN == 2
                            && $("#hidLOAITIEPNHANID").val() != 0) || (VP_DUYET_BH_KHI_DUYET_KETOAN == 3
                            && $("#hidLOAITIEPNHANID").val() == 0))) {
                        objData_DUYET.FLAG_DUYET_BH = 0;
                        var obj_BH = new Object();
                        obj_BH.TIEPNHANID = $("#hidTIEPNHANID").val();
                        obj_BH.NGAYDUYET = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
                        var month = obj_BH.NGAYDUYET.split("/")[1];
                        if (month <= 3)
                            obj_BH.QUYDUYET = "1";
                        else if (month <= 6)
                            obj_BH.QUYDUYET = "2";
                        else if (month <= 9)
                            obj_BH.QUYDUYET = "3";
                        else
                            obj_BH.QUYDUYET = "4";
                        obj_BH.HOSPITAL_CODE = opt.hospital_code;
                        objData_DUYET.DATA_BH = obj_BH;
                    } else {
                        objData_DUYET.FLAG_DUYET_BH = 1;
                    }
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI01T001.08", JSON.stringify(objData_DUYET));
                    var ltext = "Tự động duyệt kế toán khi xử trí khám bệnh ngoại trú: " + fl;
                    save_log_act_form("VPI01T001.08", "TUDONGDUYETKETOAN_NGT", ltext, $("#hidTIEPNHANID").val().toString());
                    if (fl.substr(0, 2) == -1) {
                        DlgUtil.showMsg(fl.slice(2));
                    } else if (fl == -89) {
                        DlgUtil.showMsg("Có nhiều hơn 1 phiếu duyệt kế toán");
                    } else if (fl == -90) {
                        DlgUtil.showMsg("Chưa thiết lập khoa phòng");
                    } else if (fl == -91) {
                        DlgUtil.showMsg("Lỗi lưu dữ liệu dịch vụ");
                    } else if (fl == -92) {
                        DlgUtil.showMsg("Ngày ra viện không được lớn hơn thời gian hiện tại");
                    } else if (fl == -930) {
                        DlgUtil.showMsg("Bạn không có quyền duyệt kế toán, liên hệ với người quản trị");
                    } else if (fl == -93) {
                        DlgUtil.showMsg("Bạn không có quyền gỡ duyệt kế toán, liên hệ với người quản trị");
                    } else if (fl == -931) {
                        DlgUtil.showMsg("Hết thời gian được phép gỡ duyệt hồ sơ này, liên hệ với người quản trị");
                    } else if (fl == -94) {
                        DlgUtil.showMsg("Bệnh nhân chưa gỡ duyệt bảo hiểm");
                    } else if (fl == -95) {
                        DlgUtil.showMsg("Hồ sơ đã khóa");
                    } else if (fl == -96) {
                        DlgUtil.showMsg("Còn khoa/phòng chưa kết thúc");
                    } else if (fl == -97) {
                        DlgUtil.showMsg("Còn phòng khám chưa kết thúc");
                    } else if (fl == -98) {
                        DlgUtil.showMsg("Bệnh nhân cần thanh toán viện phí");
                    } else if (fl == -981) {
                        DlgUtil.showMsg("Lỗi khi kiểm tra dịch vụ");
                    } else if (fl == -982) {
                        DlgUtil.showMsg("Bệnh nhân còn dịch vụ chưa thu tiền");
                    } else if (fl == -99) {
                        DlgUtil.showMsg("Bệnh nhân chưa đóng bệnh án");
                    } else if (fl == -88) {
                        DlgUtil.showMsg("Bệnh nhân đã được duyệt kế toán trước đó");
                    } else if (fl == -87) {
                        DlgUtil.showMsg("Bệnh nhân chưa được duyệt kế toán hoặc đã gỡ duyệt");
                    } else if (fl == -86) {
                        DlgUtil.showMsg("Công khám đầu tiên có tỷ lệ khác 100%");
                    } else if (fl == -85) {
                        DlgUtil.showMsg("Hãy gỡ duyệt bảo hiểm trước");
                    } else if (fl == -84) {
                        DlgUtil.showMsg("Hồ sơ đã khóa, không thể gỡ duyệt kế toán");
                    }

                }

                // sondn L2PT-3523
                var _xttempArr = cfObj.APPBN_KTK_XUTRIDAYDL.split(',');
                if ($.inArray(_objData.XUTRIKHAMBENHID, _xttempArr) >= 0) {
                    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT10", opt.khambenhid + '$' + opt.phongid + '$' + opt.hosobenhanid);
                    if (data_ar && data_ar.length > 0) {
                        var objHenKham = {
                            "MACOSOKHAM": opt.hospital_code,
                            "TENBENHNHAN": data_ar[0].TENBENHNHAN,
                            "MABENHNHAN": data_ar[0].MABENHNHAN,
                            "NGAYSINH": data_ar[0].NGAYSINH,
                            "MANGHENGHIEP": data_ar[0].MANGHENGHIEP,
                            "GIOITINHID": data_ar[0].GIOITINHID,
                            "MADT": data_ar[0].MADT,
                            "MAQT": data_ar[0].MAQT,
                            "DIENTHOAI": data_ar[0].DIENTHOAI,
                            "CMT": data_ar[0].CMT,
                            "MATINH": data_ar[0].MATINH,
                            "MAHUYEN": data_ar[0].MAHUYEN,
                            "MAXA": data_ar[0].MAXA,
                            "DIACHI": data_ar[0].DIACHI,
                            "SOTHE_BHYT": data_ar[0].SOTHE_BHYT,
                            "NGAYGIOKHAM": data_ar[0].NGAYGIOKHAM,
                            "MADVKHAM": data_ar[0].MADVKHAM,
                            "MAPHONGKHAM": data_ar[0].MAPHONGKHAM,
                            "MABS": data_ar[0].MABS,
                            "LOAIHENKHAM": "1",						// 1 qua portal; 2 qua app mobile

                            "DOITUONGBENHNHAN": data_ar[0].DOITUONGBENHNHAN,
                            "TUYEN": data_ar[0].TUYEN,
                            "GT_THE_TU": data_ar[0].GT_THE_TU,
                            "GT_THE_DEN": data_ar[0].GT_THE_DEN,
                            "MA_DKBH": data_ar[0].MA_DKBH,
                            "MA_KHUVUC": data_ar[0].MA_KHUVUC,
                            "BHYT_DIACHI": data_ar[0].BHYT_DIACHI,

                            "GHICHU": "Đẩy từ form xử trí HIS L2 " + data_ar[0].GHICHU,
                            "IDBLOCK": _objData.IDBLOCK,
                            "HOTEN_NN": data_ar[0].HOTEN_NN,
                            "DIACHI_NN": data_ar[0].DIACHI_NN,
                            "SDT_NN": data_ar[0].SDT_NN
                        };

                        var ret = ajaxSvc.KhamOnlineWS.callApiAppBN_tiepnhanlichkhamcsyt(objHenKham, opt.hospital_code);
                        try {
                            ret = $.parseJSON(ret);
                            if (ret.statusCode == "000") {
                                try {
                                    var _kq = ret.data;
                                    if (_kq.hasOwnProperty("errorCode")) {
                                        if (_kq.errorCode == "0" && _kq.hasOwnProperty("result") && _kq.result.hasOwnProperty("MADANGKY")) {
                                            var _objThongtinLog = new Object();
                                            _objThongtinLog["I_CHUCNANG"] = "QLHK day hen kham " + $('#hidHOSOBENHANID').val();
                                            _objThongtinLog["I_KQKETNOI"] = JSON.stringify(_kq.result);
                                            _objThongtinLog["MADANGKY"] = _kq.result.MADANGKY;
                                            var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K075.APILOGS", JSON.stringify(_objThongtinLog));
                                            DlgUtil.showMsg("Đẩy thông tin hẹn khám lên App thành công, mã đăng ký: " + _kq.result.MADANGKY);
                                        } else {
                                            DlgUtil.showMsg("Thông báo từ hệ thống QLHK: " + _kq.errorCode + ":" + _kq.errorMessage);
                                        }
                                    } else {
                                        DlgUtil.showMsg("Không tìm thấy thông tin errorCode");
                                    }
                                } catch (e) {
                                    DlgUtil.showMsg("Lỗi thông tin dữ liệu trả về từ QLHK Data. ");
                                }

                            } else if (ret.statusCode == "099") {
                                // qua thoi gian quy dinh; 
                                DlgUtil.showMsg("Không kết nối được tới API lấy dữ liệu (API Timeout). ");
                            } else {
                                // khong co du lieu; 
                                DlgUtil.showMsg("Không kết nối được tới API lấy dữ liệu. ");
                            }
                        } catch (e) {
                            DlgUtil.showMsg("Gửi thông tin lên QLHK thất bại. 1372");
                        }
                    } else {
                        DlgUtil.showMsg("Không có thông tin Phòng khám / công khám. Không đẩy thông tin API Hẹn khám cho trường hợp này.");
                    }
                }
                // end sondn L2PT-3523

                // SONDN IT360-276445
                _inDonThuoc_SauHenKham();
                // END SONDN IT360-276445


                //dannd_L2PT-19805
                if (cfObj.NGT_INDONTHUOC_SAUXUTRI == "1") {
                    _indonthuocsauxutri();
                }
                // END DANND L2PT-19805

                // SONDN L2PT-5934 END 
                if (cfObj.NGT_IN_PHIEUXUTRI == "1") {
                    var par = [{name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()},
                        {name: 'phongkhamdangkyid', type: 'String', value: $("#hidPHONGKHAMDANGKYID").val()}];
                    openReport('window', "PHIEU_XUTRI_KHAMBENH", "pdf", par);
                }
                if (cfObj.HIS_TUDONG_IN_PHIEUKBNGT == "1"
                    && ($("#cboXUTRIKHAMBENHID").val() == "1" || $("#cboXUTRIKHAMBENHID").val() == "5"
                        || $("#cboXUTRIKHAMBENHID").val() == "7" || $("#cboXUTRIKHAMBENHID").val() == "9" || $("#cboXUTRIKHAMBENHID").val() == "15")
                ) {
                    var par = [{name: 'hosobenhanid', type: 'String', value: $("#hidHOSOBENHANID").val()},
                        {name: 'khambenhid', type: 'String', value: $("#hidKHAMBENHID").val()},
                        {name: 'phongkhamdangkyid', type: 'String', value: $("#hidPHONGKHAMDANGKYID").val()}];
                    openReport('window', "NGT005_PHIEUKHAMBENHNGOAITRU_A4_10284", "pdf", par);
                }
                // sondn L2PT-4494
                if (cfObj.VPI_AUTO_DUYET_BHYT_NGT == '1'
                    && $("#cboDOITUONGBENHNHANID").val() == "1" && $("#cboXUTRIKHAMBENHID").val() !== "2" && $("#cboXUTRIKHAMBENHID").val() !== "6") {
                    var dtar = jsonrpc.AjaxJson.ajaxCALL_SP_O("KHAMOL.GETTT10", opt.khambenhid + '$' + opt.phongid + '$' + opt.hosobenhanid);
                    if (dtar != null && dtar.length > 0) {
                        if (dtar[0].TRANGTHAITIEPNHAN == "1") {
                            var _duyet_bh = duyetBHYT($("#hidTIEPNHANID").val(), opt.user_id, opt.hospital_code);
                            var ltext = "Tự động duyệt BHYT khi xử trí khám bệnh ngoại trú: " + _duyet_bh;
                            save_log_act_form("duyetBHYT", "TUDONGDUYETBHYT_NGT", ltext, $("#hidTIEPNHANID").val().toString());
                            if (_duyet_bh != 0) {
                                DlgUtil.showMsg("Có lỗi xảy ra trong quá trình đẩy dữ liệu lên cổng giám định!");
                            }
                        }
                    } else {
                        DlgUtil.showMsg("Không lấy được thông tin bệnh nhân sau xử trí. 1526");
                    }
                }
                //dannd_kyso_BVTM   
                if (cfObj.HIS_SUDUNG_KYSO_KYDIENTU == "1" && isKyCa) {
                    isKyCa = false;
                    _caRpt('1', '');
                }
                // end sondn L2PT-4494

                //BVTM-5938 day emr khi ket thuc kham
                day_emr_ktkham($('#hidHOSOBENHANID').val(), $("#hidTIEPNHANID").val());
                if ($('#cboXUTRIKHAMBENHID').val() != '2' && $('#cboXUTRIKHAMBENHID').val() != '6'
                    && $('#cboXUTRIKHAMBENHID').val() != '12' && cfObj.HIS_KETTHUC_KHAM_KHI_XUTRI == "1" && cfObj.NGT_LUUQUANLYBENHAN == '1') {
                    luuQuanLyBenhAn('1');
                }
                var BADN = jsonrpc.AjaxJson.getOneValue("CHECK_BADN", [{"name": "[0]", "value": opt.hosobenhanid}]);
                if (($('#cboXUTRIKHAMBENHID').val() == '1' || $('#cboXUTRIKHAMBENHID').val() == '5') && ( (BADN != 0
                    && cfObj.NGT_GUITNHENKHAM_BADN == "1" ) ) || (cfObj.NGT_SENDSMS_AUTO != '0') ) {
                    var ngayhen = _objData.THOIGIANLICHHEN.substring(0, 10);
                    var ngayhen1;
                    if (BADN != 0  && cfObj.NGT_GUITNHENKHAM_BADN == "1" ){
                        if ($('#cboXUTRIKHAMBENHID').val() == '1') {
                            ngayhen = jsonrpc.AjaxJson.getOneValue("NGT.GETNGAYTHUOC.SMS", [{
                                "name": "[0]",
                                "value": opt.hosobenhanid
                            }]);
                            ngayhen1 = subtract_date(ngayhen, 1);
                        }
                        if ($('#cboXUTRIKHAMBENHID').val() == '5') {
                            ngayhen1 = subtract_date(ngayhen, 1);
                        }
                        var guitn = sendSmsAuto_BADN($('#txtMABENHNHAN').val(), $('#hidKHAMBENHID').val(), ngayhen, ngayhen, $('#txtTENBENHNHAN').val())
                        var guitn1 = sendSmsAuto_BADN($('#txtMABENHNHAN').val(), $('#hidKHAMBENHID').val(), ngayhen1, ngayhen, $('#txtTENBENHNHAN').val())
                        if (guitn == 1 && guitn1 == 1) {
                            DlgUtil.showMsg("Gửi tin nhắn hẹn khám thành công");
                        }
                    }
                    if (cfObj.NGT_SENDSMS_AUTO != '0') {
                        for (var i = cfObj.NGT_SENDSMS_AUTO.split('@')[0] ; i = 1  ; i--) {
                            ngayhen1 = subtract_date(ngayhen, (i + cfObj.NGT_SENDSMS_AUTO.split('@')[1]) );
                            var guitn = sendSmsAuto_BADN($('#txtMABENHNHAN').val(), $('#hidKHAMBENHID').val(), ngayhen1, ngayhen, $('#txtTENBENHNHAN').val())
                        }
                        if (guitn == 1 ) {
                            DlgUtil.showMsg("Gửi tin nhắn hẹn khám thành công");
                        }
                    }

                }
                if (cfObj.HIS_TUDONG_GUICONGBMTE == 1) {
                    tuDongGuiCongBMTE($("#hidTIEPNHANID").val());
                }

            } else if (ret == "dieutrinoitru") {
                DlgUtil.showMsg('Bệnh nhân đang điều trị nội trú');
            } else if (ret == "dieutringoaitru") {
                DlgUtil.showMsg('Bệnh nhân đang điều trị ngoại trú');
            } else if (ret == "dadongbenhan") {
                DlgUtil.showMsg('Bệnh nhân đã đóng bệnh án, yêu cầu mở bệnh án trước khi xử trí');
            } else if (ret == "loixoaxutri") {
                DlgUtil.showMsg('Bệnh nhân đã duyệt BH/Kế toán/đã bắt đầu thông tin nội trú hoặc điều trị ngoại trú.');
            } else if (ret == "chuyenphong_chanxt") {
                DlgUtil.showMsg('Bệnh nhân đã chuyển phòng khám, không thể xử trí được.');
            } else if (ret.startsWith("conphongdangkham")) {
                DlgUtil.showMsg("Bệnh nhân còn phòng chờ/đang khám khác phòng hiện tại, yêu cầu kết thúc trước khi xử trí:<br>- " + ret.replace("conphongdangkham", ""));
            } else if (ret == "chuahoanthanhpttt") {
                DlgUtil.showMsg('Bệnh nhân đang có chỉ định PTTT chưa hoàn thành');
            } else if (ret == "codonthuoc") {
                DlgUtil.showMsg('Bệnh nhân có đơn thuốc');
                //dannd - L2PT-866
            } else if (ret == "xutricv_codonthuocbhyt") {
                DlgUtil.showMsg('Bệnh nhân có đơn thuốc, vật tư BHYT, không cho phép tiếp tục xử trí này. ');
            } else if (ret == "xutricv_codonthuoc") {
                DlgUtil.showMsg('Bệnh nhân có đơn thuốc, không cho phép tiếp tục xử trí này. ');
            } else if (ret == 'connotien') {
                DlgUtil.showMsg('Bệnh nhân còn nợ tiền, phải thanh toán mới kết thúc khám.');
            } else if (ret == 'cophieudangsua') {
                DlgUtil.showMsg('Bệnh nhân có phiếu CLS/Đơn thuốc đang sửa, không kết thúc khám được.');
            } else if (ret == 'kococannang') {
                DlgUtil.showMsg('Bệnh nhân chưa có thông tin cân nặng');
            } else if (ret == 'chuyennturangt') {
                DlgUtil.showMsg('Bệnh nhân chuyển từ nội trú ra khám bệnh, không xử trí được. ');
            } else if (ret == 'thieuthongtinsinhton') {
                DlgUtil.showMsg('Thiếu thông tin sinh tồn, yêu cầu bổ sung trước khi lưu. ');
            } else if (ret == 'tiepnhanntu') {
                DlgUtil.showMsg('Bệnh nhân đã tiếp nhận nội trú, không thể thay đổi xử trí. ');
            } else if (ret == 'bnkhactrongnganh') {
                DlgUtil.showMsg('Bệnh nhân không phải đối tượng trong ngành, không được thực hiện xử trí này.');
            } else if (ret == 'trungmabenhandaingay') {
                DlgUtil.showMsg('Trùng mã bệnh án, yêu cầu kiểm tra lại. ');
            }
            //tuyennx_add_start_20180927  y/c HISL2BVDKHN-247 L2PT-5940
            else if (ret == 'ngaydichvu') {
                DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện hoặc thời gian chỉ định nhỏ hơn thời gian tiếp nhận, không thể kết thúc khám.');
            }
                //tuyennx_add_end_20170727
            //dannd_add_start_20210223 
            else if (ret == 'ngaydichvuketthuc') {
                DlgUtil.showMsg('Bệnh nhân có thời gian kết thúc PTTT lớn hơn thời gian ra viện, không thể kết thúc khám.');
            }
                //dannd_add_start_20210223
            //tuyennx_add_start_20180927  y/c HISL2BVDKHN-247
            else if (ret == 'bncothuoc') {
                DlgUtil.showMsg('Bệnh nhân có đơn thuốc, không thể xử trí chuyển viện.');
            }
                //tuyennx_add_end_20180927
            //tuyennx_add_start_20190129
            else if (ret == 'saibenhphu') {
                DlgUtil.showMsg('Định dạng mã bệnh kèm theo không đúng.');
            }
            //tuyennx_add_end_20180927   
            else if (ret == "dvcls") {
                if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'HIS_DVCHUAHOANTHANH_CHITIET') == 1) {
                    var _parram = [$('#hidKHAMBENHID').val()];
                    var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT_GET_CLSCHUAHT", _parram.join('$'));
                    var str = "Bệnh nhân có dịch vụ chưa hoàn thành";
                    for (var i = 0; i < data_ar.length; i++) {
                        str = str + ', ' + data_ar[i].SOPHIEU + '-' + data_ar[i].TENDICHVU;
                    }
                    DlgUtil.showMsg(str);
                } else {
                    DlgUtil.showMsg('Chưa hoàn thành dịch vụ CLS');
                }
            }
            // hunglv L2PT-7740, sondn L2PT-9338 
            else if (ret == "xutrikhac_codonthuoc") {
                DlgUtil.showMsg('Bệnh nhân có đơn thuốc ở kho, không cho phép tiếp tục xử trí này. ');
            } else if (ret.startsWith("LoiDuyetBHYT")) {
                DlgUtil.showMsg("Lỗi duyệt BHYT:<br>" + ret.replace("LoiDuyetBHYT", ""));
            } else if (ret == "tnttcapcuu") {
                DlgUtil.showMsg('Bệnh nhân cấp cứu bắt buộc nhập tai nan thương tích!');
            } else if (ret == "ICD_BB_CLS") {
                DlgUtil.showMsg('Mã bệnh chính này bắt buộc phải có chỉ định CLS !');
            } else if (ret == "his_kham_chinhphu_conphongkhamphu") {
                DlgUtil.showMsg('Còn phòng khám phụ chưa kết thúc khám, không thể kết thúc khám phòng khám chính !');
            } else if (ret == "thoigiancls") {
                var sophut = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_CANHBAO_TGCLS')
                DlgUtil.showMsg('Thời gian chỉ định CLS đến thời gian xử trí nhỏ hơn ' + sophut + ' phút!');
            } else if (ret == "thoigian_thuocvt") {
                var sophut = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_CANHBAO_TGKETHUOC')
                DlgUtil.showMsg('Thời gian kê thuốc đến thời gian xử trí nhỏ hơn ' + sophut + ' phút!');
            } else if (ret == "thoigiankqcls") {
                var sophut = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_CANHBAO_TGKQCLS')
                DlgUtil.showMsg('Thời gian trả kết quả CLS đến thời gian xử trí nhỏ hơn ' + sophut + ' phút!');
            } else if (ret == "thoigiantrakqcls") {
                DlgUtil.showMsg('Thời gian trả kết quả CLS lớn hơn thời gian kết thúc khám!');
            } else if (ret == "badn_chanxt") {
                DlgUtil.showMsg('Bệnh nhân đã mở bệnh án mãn tính, xử trí không thành công!');
            } else if (ret == "kophaiicddaingay") {
                DlgUtil.showMsg('Bệnh chính của bệnh nhân không thuộc nhóm bệnh dài ngày vui lòng kiểm tra lại!');
            } else if (ret == "chuyendoituong") {
                DlgUtil.showMsg('Đối tượng BN hoặc Tỷ lệ thanh toán có sự thay đổi. Đề nghị tắt chức năng và chỉ định lại cho bệnh nhân!');
            } else if (ret == "kophaibacsy") {
                DlgUtil.showMsg('Tài khoản xử trí không phải là bác sỹ hoặc là bác sỹ không có chứng chỉ hành nghề, vui lòng kiểm tra lại!');
            } else if (ret == "trungthoigian_thuocvt") {
                DlgUtil.showMsg('Thời gian xử trí trùng với thời gian kê thuốc,vật tư,CLS yêu cầu thử lại trong ít phút !');
            } else if (ret == "thoigianktpttt") {
                DlgUtil.showMsg('Thời gian kết thúc PTTT lớn hơn thời gian kết thúc khám!');
            } else if (ret == "tgpk_lonhontgxutri") {
                DlgUtil.showMsg('Tồn tại phòng khám có thời gian kết thúc lớn hơn thời gian xử trí!');
            } else if (ret == "tgcls_sautgthuoc") {
                DlgUtil.showMsg('Thời gian chỉ định cls sau thời gian kê thuốc!');
            } else if (ret == "loi_thoigianbatdau") {
                DlgUtil.showMsg('Thời gian xử trí nhỏ hơn thời gian bắt đầu khám, yêu cầu kiểm tra lại!');
            } else if (ret == "codichvudvvpchuatt") {
                DlgUtil.showMsg('Bệnh nhân có dịch vụ cần thu tiền trước khi xử trí nhập viện!');
            } else if (ret == "kodungketquadieutri") {
                DlgUtil.showMsg('Kết quả điều trị của bệnh nhân không đúng với xử trí khám bệnh được map trong danh mục, vui lòng kiểm tra lại!');
            }else if (ret == "vuotquaicd") {
                DlgUtil.showMsg('Tổng số mã ICD trong một điều trị vượt quá số lượng quy định của BHXH!');
            } else if (ret == "kocothuocbhyt") {
                DlgUtil.showMsg('Bệnh nhân có mã loại kcb là 07 nhưng không có thuốc BHYT vui lòng kiểm tra lại!');
            } else if (ret == "congkhamkodung") {
                DlgUtil.showMsg('Bệnh nhân có mã loại kcb là 07 nhưng công khám không phải miễn phí, hao phí hoặc không đồng, vui lòng kiểm tra lại!');
            } else if (ret == "nhapvien_trungtgian") {
                if (cfObj.NGT_CHECKTRUNGTGIAN_NHAPVIEN == 1) {
                    DlgUtil.showConfirm("Thời gian nhập viện trùng với bệnh nhân khác, bạn có tiếp tục? ", function (flag) {
                        if (flag) {
                            checktrungtgian = 1;
                            _capnhat(inMB);
                        } else {
                            return false;
                        }
                    });
                    return false;
                } else {
                    DlgUtil.showMsg('Thời gian nhập viện trùng với bệnh nhân khác vui lòng thử lại trong ít phút!');
                }
            } else if (ret == "xutri_trungtgian") {
                if (cfObj.NGT_XUTRI_CHECKTRUNGTGIAN == 1) {
                    DlgUtil.showConfirm("Thời gian xử trí trùng với bệnh nhân khác, bạn có tiếp tục? ", function (flag) {
                        if (flag) {
                            checktrungtgian = 1;
                            _capnhat(inMB);
                        } else {
                            return false;
                        }
                    });
                    return false;
                } else {
                    DlgUtil.showMsg('Thời gian  xử trí trùng với bệnh nhân khác vui lòng thử lại trong ít phút!');
                }
            } else {
                DlgUtil.showMsg('Lỗi khi cập nhật thông tin');
            }
        }
    }

    //tuyennx_add_start tích hợp cổng dữ liệu y tế
    function dayCongBYT(_objData) {
        var sql_par = [];
        sql_par.push({"name": "[0]", "value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')});
        var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT", sql_par);
        vsothutu = JSON.parse(vsothutu);
        if (vsothutu[0].BYTDAYDL == "1") {
            var _parram = ["1"];
            var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK", _parram.join('$'));

            var data_bv = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETTT_BV", []);

            var objCHECKIN = new Object();
            objCHECKIN.MA_LK = $("#hidMAHOSOBENHAN").val();
            objCHECKIN.Sender_Code = opt.hospital_code;
            objCHECKIN.Sender_Name = "";
            objCHECKIN.Action_Type = "0";				// 0: bắt đầu khám, 1: kết thúc khám
            objCHECKIN.Transaction_Type = "M0003";
            objCHECKIN.MACSKCBDI = data_ar[0].I_U1;//data_bv[0].HOSPITAL_CODE;
            objCHECKIN.TENCSKCBDI = data_bv[0].ORG_NAME;
            objCHECKIN.MACSKCBDEN = _objData.MABENHVIENCHUYENDEN;
            objCHECKIN.TENCSKCBDEN = _objData.CHUYENVIENDEN;
            objCHECKIN.SOHOSO = data_ar[0].I_U1;
            objCHECKIN.HO_TEN = $("#txtTENBENHNHAN").val();
            objCHECKIN.GIOI_TINH = $("#cboGIOITINHID").val();
            objCHECKIN.NAM_SINH = convertDateyyyymmdd($("#txtNGAYSINH").val());
            objCHECKIN.DIA_CHI = _objData.DIACHI;
            objCHECKIN.MA_THE = _objData.MA_BHYT;
            objCHECKIN.GT_THE_TU = convertDateyyyymmdd(_objData.BHYT_BD);
            objCHECKIN.GT_THE_DEN = convertDateyyyymmdd(_objData.BHYT_KT);
            objCHECKIN.KHAMDIEUTRITAI = data_bv[0].ORG_NAME;
            objCHECKIN.LYDO_CHUYEN = _objData.LYDOID;

            objCHECKIN.NGAY_CHUYENTUYEN = convertDateyyyymmdd(_objData.THOIGIANCHUYENVIEN);

            var objHeader = XML_BYT_TaoHeader(objCHECKIN); 										// tao doi tuong header; 
            var objIn = XML_BYT_TaoTheCHECKIN(objCHECKIN); 									// tao the 
            var objChuyenTuyen = new Object();
            objChuyenTuyen.PHIEUCHUYENTUYEN = objIn;

            var obj3 = XML_BYT_TaoKhung(objHeader, objChuyenTuyen, "3"); 											// tao JSON full => XML

            var resultCongBYT = ajaxSvc.CongDLYTWS.guiTTCT(
                vsothutu[0].BYTURL
                , data_ar[0].I_U1
                , data_ar[0].I_P1
                , data_ar[0].I_U1
                , data_bv[0].MADIAPHUONG
                , obj3);
            var rets = resultCongBYT.split(';');
            if (rets[0] != '0') {
                DlgUtil.showMsg("Lỗi đẩy dữ liệu cổng y tế: " + rets[1]);
                if (vsothutu[0].BYTSTOPCHUCNANG == "1")
                    return 1;
            }
        }
        return 0;
    }

    function convertDateyyyymmdd(date) {
        var dt;
        if (date.length >= 10)
            dt = date.substr(6, 4) + date.substr(3, 2) + date.substr(0, 2);
        else dt = date + "0101";
        return dt;
    }

    //tuyennx_add_end

    function luuQuanLyBenhAn(signType) {
        if (opt.hospital_id == 10284) {
            //ko xử lý gì
        } else {
            var arrReportCode = [];
            vienphi_tinhtien.inBangKe($("#hidTIEPNHANID").val(), $("#cboDOITUONGBENHNHANID").val(), $("#hidLOAITIEPNHANID").val(), arrReportCode, 0, 0);
            arrReportCode.forEach(function (el) {
                //lưu bảng quản lý phiếu
                var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", el);
                if (data_ar != null && data_ar.length > 0) {
                    var obj = new Object();
                    obj.hosobenhanid = $("#hidHOSOBENHANID").val();
                    obj.tiepnhanid = $("#hidTIEPNHANID").val();
                    obj.rpt_code = el;
                    obj.transtype = '10';
                    obj.delete = signType;
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H028.EV003", JSON.stringify(obj));
                }
            });
            if (arrReportCode.length > 0 && cfObj.NGT_KYSOBANGKE_KTK == '1') {
                if (cfObj.KYSO_BANGKE_NGTRU_NAMDAN == '1') {
                    _caRpt3(1);
                }else{
                _caRpt1(signType);
                }
            }
        }
    }
    function _caRpt3(signType) {
        var _rptCode = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'RPT_CODE_KYSO_BANGKE_KHAMBENH');
        var _params = [ {
            name: 'hosobenhanid',
            type: 'String',
            value: $("#hidHOSOBENHANID").val()
        }, {
            name : 'tiepnhanid',
            type : 'String',
            value : $("#hidTIEPNHANID").val()
        }, {
            name : 'RPT_CODE',
            type : 'String',
            value : _rptCode
        } ];
        if(signType == '0') {
            CommonUtil.openReportGetCA2(_params, false);
        } else {
            var msg = CommonUtil.kyCASingerPad(_params, signType, true);
            EventUtil.setEvent("eventKyCA",function(e){
                DlgUtil.showMsg(e.res);
            });
        }
    }

    function _caRpt1(signType) {
        var _rptCode = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'RPT_CODE_KYSO_BANGKE');
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
        if (data_ar != null && data_ar.length > 0) {
            var row = data_ar[0];
            var catype = row.CA_TYPE;
            var kieuky = row.KIEUKY;
            if (catype == '5') {
                //isKyTocken = true;
            } else if (catype == '3' || catype == '6') {

            } else {
                EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                    causer = e.username;
                    capassword = e.password;
                    DlgUtil.close("divCALOGIN");
                    _caRpt2(signType, kieuky, catype);
                });
                EventUtil.setEvent("dlgCaLogin_close", function (e) {
                    DlgUtil.close("divCALOGIN");
                });
                var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
                popup.open("divCALOGIN");
            }
        } else {
            DlgUtil.showMsg('Chưa cấu hình phiếu CA theo rpt_code trong cấu hình RPT_CODE_KYSO_BANGKE!');
            return;
        }
    }

    function _caRpt2(signType, kieuky, catype) {
        arrReportCode.forEach(function (el) {
            var params = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            }, {
                name: 'tiepnhanid',
                type: 'String',
                value: $("#hidTIEPNHANID").val()
            }, {
                name: 'rpt_code',
                type: 'String',
                value: el
            }];
            var oData = {
                sign_type: signType,
                causer: causer,
                capassword: capassword,
                smartcauser: smartcauser,
                params: params
            };
            var msg = CommonUtil.caRpt(oData, el, true, '', false, kieuky, catype);
            DlgUtil.showMsg(msg);
        });
    }

    function _printBangKe() {
        //L2PT-14180
        var check = cfObj.NGT_TUDONG_LOAI_BANGKE;
        if (check == "ALL") {
            vienphi_tinhtien.inBangKe($("#hidTIEPNHANID").val(), $("#cboDOITUONGBENHNHANID").val(), '1');
        } else {
            var par = [{
                name: 'tiepnhanid',
                type: 'String',
                value: $("#hidTIEPNHANID").val()
            }];

            openReport('window', "NGT001_BKCPKCBBHYT_QD6556_DOCMOI_A4", "pdf", par);
        }
        //end L2PT-14180
    }

    function _printPhieu(khambenhid, code) {
        var par = [{
            name: 'khambenhid',
            type: 'String',
            value: khambenhid
        }, {
            name: 'phongid',
            type: 'String',
            value: opt.phongid
        }];

        openReport('window', code, 'pdf', par);
    }

    function _validate() {
        //tuyennx_add_start_L2PT-1744
        if (cfObj.HIS_KHOA_SOLIEU == '1') {
            var sql_par = [$("#hidTIEPNHANID").val(), $("#txtTHOIGIANRAVIEN").val().length == 19 ? $("#txtTHOIGIANRAVIEN").val() : $("#txtTHOIGIANRAVIEN").val() + ':00'];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DMC32.KSL.05", sql_par.join('$'));
            var rets = ret.split(';');
            if (rets[0] > 0) {
                DlgUtil.showMsg(rets[1]);
                return;
            }
        }
        //tuyennx_add_end_L2PT-1744
        var xutri = $('#cboXUTRIKHAMBENHID').val();
        if ((xutri == "2" || xutri == "6") && ($('#cboKHOA').val() == "0")) {
            setErrValidate('cboKHOA');
            DlgUtil.showMsg('Hãy chọn khoa');
            return false;
        }

        if (cfObj.NGT_CHANXUTRI_BADN == '1') {
            var BADN = jsonrpc.AjaxJson.getOneValue("CHECK_BADN", [{"name": "[0]", "value": opt.hosobenhanid}]);
            console.log(BADN);
            if (xutri == '7' && BADN !== "0") {
                DlgUtil.showMsg('Bệnh nhân đã mở bệnh án mãn tính không thể xử trí chuyển viện ');
                return false;
            }
        }
        if ($('#txtTHOIGIANRAVIEN').val() == '') {
            setErrValidate('txtTHOIGIANRAVIEN');
            DlgUtil.showMsg('Hãy nhập ngày ra viên');
            return false;
        }

        var ngaytiepnhan = $('#hidNGAYTN').val().substr(6, 4) + $('#hidNGAYTN').val().substr(3, 2) + $('#hidNGAYTN').val().substr(0, 2);
        var ngayrv = $('#txtTHOIGIANRAVIEN').val().substr(6, 4) + $('#txtTHOIGIANRAVIEN').val().substr(3, 2) + $('#txtTHOIGIANRAVIEN').val().substr(0, 2);

        if (ngaytiepnhan > ngayrv) {
            setErrValidate('txtTHOIGIANRAVIEN');
            DlgUtil.showMsg('Ngày ra viện không thể nhỏ hơn ngày tiếp nhận');
            return false;
        }

        if ( ($('#txtMACHANDOANRAVIEN').val() == '' || $('#txtCHANDOANRAVIEN').val() == '') && cfObj.NGT_KHONGBATBUOCICD_XUTRI != '1') {
            setErrValidate('txtMACHANDOANRAVIEN');
            DlgUtil.showMsg('Hãy nhập bệnh chính');
            return false;
        }

        if ($("#txtMACHANDOANBANDAU").val() != $("#hidMACHANDOANBANDAU").val()) {
            setErrValidate('txtMACHANDOANBANDAU');
            DlgUtil.showMsg('Mã chẩn đoán sơ bộ không hợp lệ');
            return false;
        }

        if ($("#txtMACHANDOANRAVIEN").val() != $("#hidMACHANDOANRAVIEN").val()) {
            setErrValidate('txtMACHANDOANRAVIEN');
            DlgUtil.showMsg('Mã chẩn đoán bệnh chính không hợp lệ');
            return false;
        }

        if ($("#cboXUTRIKHAMBENHID").val() == 0) {
            setErrValidate('cboXUTRIKHAMBENHID');
            DlgUtil.showMsg('Hãy chọn xử trí');
            return false;
        }

        if (cfObj.NHAPVIEN_BN_DICHVU != '1' && $("#cboDOITUONGBENHNHANID").val() == 3 && ($("#cboXUTRIKHAMBENHID").val() == 2 || $("#cboXUTRIKHAMBENHID").val() == 6)) {
            setErrValidate('cboXUTRIKHAMBENHID');
            DlgUtil.showMsg('Không xử trí nhập viện hoặc điều trị ngoại trú với bệnh nhân dịch vụ');
            return false;
        }
        if(cfObj.NGT_BATBUOC_LANHDAO == '1' &&  ( $("#cboLANHDAOKHOAID").val() == '0' ||  $("#cboLANHDAOVIENID").val() == '0'  )) {
            DlgUtil.showMsg('Chưa chọn lãnh đạo khoa hoặc lãnh đạo viện!');
            return false;
        }
        if (cfObj.QD_4750 == 1  &&  $("#cboXUTRIKHAMBENHID").val() == 6 )  {
            if ($("#txtLY_DO_VNT").val().trim() == '') {
                DlgUtil.showMsg('Lý do vào nội trú không được để trống!');
                return false;
            }     
            if (parseInt(cfObj.HIS_GIOIHANKYTU_LYDOVNT) > 0 && $("#txtLY_DO_VNT").val().length > parseInt(cfObj.HIS_GIOIHANKYTU_LYDOVNT)) {
                DlgUtil.showMsg('Lý do vào nội trú vượt quá '+ parseInt(cfObj.HIS_GIOIHANKYTU_LYDOVNT) +' ký tự!');
                return false;
            }       
        }
        return true;
    }

    function _setXutri(value) {
        if (value === "1" || value === "3" || value === "9" || value === "14") { // Cấp toa cho về, hẹn, khác
            _fileReport = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5";
            $('#btnInPhieu').attr("disabled", false);
            $('#btnInPhieuDoc').attr("disabled", false);
        } else if (value === "4" || value === "5") { // hẹn khám tiếp, khám mới
            _fileReport = "NGT014_GIAYHENKHAMLAI_TT402015_A4";
            $('#btnInPhieu').attr("disabled", false);
        } else if (value === "7") { // chuyển viện
            if (opt._hospital_id == "902") {
                if ($("#cboDOITUONGBENHNHANID").val() == "1") {
                    fileReport = "NGT003_GIAYCHUYENTUYEN_BHYT_A4_902";
                } else {
                    fileReport = "NGT003_GIAYCHUYENTUYEN_TT14_A4";
                }
            } else if (opt._hospital_id == "932") {
                fileReport = "NGT003_GIAYCHUYENTUYEN_BHYT_A4_932";
            } else {
                fileReport = "NGT003_GIAYCHUYENTUYEN_TT14_A4";
            }

            $('#btnInPhieu').attr("disabled", false);
            $('#btnInPhieuDoc').attr("disabled", false);
            $("#divChuyenVien").show();
        } else if (value === "8") { // tử vong				
            _fileReport = "";
            $('#btnInPhieu').attr("disabled", false);
            $('#btnInPhieuDoc').attr("disabled", false);
        }
    }

    function _loadLS() {
        var sql_par = $('#hidBENHNHANID').val() + "$";
        ComboUtil.getComboTag("cboLS", _sql[5], sql_par, "", "", 'sp', "", false);
    }

    function _checkCBBN(ICD, _type) {
        //if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'HIS_CANHBAO_BN') == '1') {
        var objCheck = new Object();
        objCheck["TIEPNHANID"] = $("#hidTIEPNHANID").val();
        objCheck["ICD10"] = nvl(ICD, '0');
        objCheck["TYPE"] = _type;
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("CDDV.CBBN", JSON.stringify(objCheck));
        var obj = JSON.parse(result);
        if (obj.KETQUA && obj.KETQUA != '0' && obj.KETQUA != '1') {
            DlgUtil.showMsg(obj.GHICHU);
            if (obj.KETQUA == '3') {
                return 1;
            }
        }
        return 0;
        //}
    }

    //dannd_L2PT-76338
    function compareTimeWithCurrent(inputTime, timeCompare) {
        var currentTime = new Date();
        var parts = inputTime.split(' ');
        var datePart = parts[0].split('/');
        var timePart = parts[1];
        var formattedInputTime = datePart[2] + '-' + datePart[1] + '-' + datePart[0] + 'T' + timePart;
        var inputDate = new Date(formattedInputTime);
        var timeDifference = (inputDate - currentTime) / (1000 * 60);
        if (timeDifference > timeCompare) {
            return false;
        } else {
            return true;
        }
    }

    //dannd_L2PT-76338
    function checkTime(inputTimeStr, currentTimeStr) {
        var inputTime = new Date(inputTimeStr.replace(/(\d{2})\/(\d{2})\/(\d{4}) (\d{2}):(\d{2}):(\d{2})/, "$3-$2-$1T$4:$5:$6"));
        var currentTime = new Date(currentTimeStr.replace(/(\d{2})\/(\d{2})\/(\d{4}) (\d{2}):(\d{2}):(\d{2})/, "$3-$2-$1T$4:$5:$6"));

        if (inputTime < currentTime) {
            return true;
        } else {
            return false;
        }
    }

    //ductx -bvtm-5439
    function sendSmsAuto() {
        var sms_auto = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'BVBD_SMS_HTSS_CAMON');
        var sms_qc = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'AUTO_SMS_QC');
        var hsbacao = jsonrpc.AjaxJson.getOneValue("NGT.GETCHKBA", [{
            "name": "[0]",
            "value": $('#hidKHAMBENHID').val()
        }]);
        var makhoa = jsonrpc.AjaxJson.getOneValue("NGT.GETMAKHOA", [{"name": "[0]", "value": opt.phongid}]);
        var dskhoa = [];
        dskhoa = sms_qc.split(',');
        var pos = $('#txtMABENHNHAN').val().indexOf("-");
        var mabn = "";
        if (pos >= 0) {
            mabn = $('#txtMABENHNHAN').val().substring(0, pos - 1);
        } else {
            mabn = $('#txtMABENHNHAN').val();
        }
        var _par = [mabn];
        var sdo = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CH_SDT', _par.join('$'));
        var sd = typeof sdo == 'undefined' ? '' : sdo;
        if (sd == '') {
            DlgUtil.showMsg("Bệnh nhân thiếu thông tin Số điện thoại");
            return;
        } else if (sd.startsWith("0")) {
            sd = "84" + sd.slice(1, sd.length);
        } else if (!sd.toString().startsWith("84")) {
            sd = "84" + sd;
        }
        var sdt = typeof sd == 'undefined' ? '' : sd;

        if ((jQuery.inArray(makhoa, dskhoa) > -1) && hsbacao < 1) {
            var sms = new SmsSend();
            var partn = [];
            var time = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI');
            var par = [];
            par.push({"name": "[0]", "value": $('#hidKHAMBENHID').val()});
            var _res1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT.SMS.GETKHOAPHONG", par);
            var datakhoaphong = JSON.parse(_res1);
            var thongtinkhoaphong = {
                ma_khoa: datakhoaphong[0].MA_KHOA,
                ten_khoa: datakhoaphong[0].TEN_KHOA,
                ma_phong: datakhoaphong[0].MA_PHONG,
                ten_phong: datakhoaphong[0].TEN_PHONG
            }
            var kq = sms.sendPostTemplate(sdt, "QC_TBGOC", partn, "", $("#hidKHAMBENHID").val(), "2", thongtinkhoaphong);
            _RESP_CODE = kq.RESP_CODE;
            if (_RESP_CODE == 0) {
                DlgUtil.showMsg("Gửi tin nhắn quảng cáo thành công");
            } else {
                DlgUtil.showMsg("Gửi tin nhắn quảng cáo không thành công, mã lỗi:  " + kq.RESP_CODE);
            }
        }

        if (sms_auto == "1" && (makhoa == 'PKHM1' || makhoa == 'PKHM2' || makhoa == 'PKHM3' || makhoa == 'PKHM4' || makhoa == 'PKHM5')) {
            var noidung = jsonrpc.AjaxJson.getOneValue("NGT.GETSMSAUTO", []);
            var sms = new SmsSend();
            var partn = [];
            partn.push(removeVietnameseTones($('#txtTENBENHNHAN').val()));
            var time = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI');
            var par = [];
            par.push({"name": "[0]", "value": $('#hidKHAMBENHID').val()});
            var _res1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT.SMS.GETKHOAPHONG", par);
            var datakhoaphong = JSON.parse(_res1);
            var thongtinkhoaphong = {
                ma_khoa: datakhoaphong[0].MA_KHOA,
                ten_khoa: datakhoaphong[0].TEN_KHOA,
                ma_phong: datakhoaphong[0].MA_PHONG,
                ten_phong: datakhoaphong[0].TEN_PHONG
            }
            var kq = sms.sendPostTemplate(sdt, "CAMON_HT_SS", partn, "", $("#hidKHAMBENHID").val(), "2", thongtinkhoaphong);
            _RESP_CODE = kq.RESP_CODE;
            if (_RESP_CODE == 0) {
                DlgUtil.showMsg("Gửi tin nhắn cảm ơn thành công");
            } else {
                DlgUtil.showMsg("Gửi tin nhắn cảm ơn không thành công, mã lỗi:  " + kq.RESP_CODE);
            }
        }
    }

    function _openReport(param, reportName, format1) {
        var format = format1 != "rtf" && format1 != "docx" ? "pdf" : format1;
        if (format == "pdf") {
            param.push({name: 'i_hid', type: 'String', value: opt._hospital_id});
            param.push({name: 'i_sch', type: 'String', value: opt.db_schema});
            param.push({name: 'khambenhid', type: 'String', value: $('#hidKHAMBENHID').val()});
            param.push({name: 'phongid', type: 'String', value: opt.phongid});
            openReport('window', reportName, format, param);
        } else {
            param.push({name: 'i_hid', type: 'String', value: opt._hospital_id});
            param.push({name: 'i_sch', type: 'String', value: opt.db_schema});
            param.push({name: 'khambenhid', type: 'String', value: $('#hidKHAMBENHID').val()});
            var rpName = reportName + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + format;
            CommonUtil.inPhieu('window', reportName, format, param, rpName);
        }
    }

    function removeVietnameseTones(str) {
        str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
        str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
        str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
        str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
        str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
        str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
        str = str.replace(/đ/g, "d");
        str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
        str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
        str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
        str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
        str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
        str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
        str = str.replace(/Đ/g, "D");
        // Some system encode vietnamese combining accent as individual utf-8 characters
        // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
        str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
        str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
        // Remove extra spaces
        // Bỏ các khoảng trắng liền nhau
        str = str.replace(/ + /g, " ");
        str = str.trim();
        // Remove punctuations
        // Bỏ dấu câu, kí tự đặc biệt
        str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g, " ");
        return str;
    }

    //end bvtm-5439
    function save_log_act_form(lcode, lfunc, ltext, lkey) {
        var objLogActForm = new Object();
        objLogActForm.LCODE = lcode;
        objLogActForm.LFUNC = lfunc;
        objLogActForm.LTEXT = ltext;
        objLogActForm.LKEY = lkey;
        var _result_log = jsonrpc.AjaxJson.ajaxCALL_SP_S('LOG.ACT.FORM', objLogActForm);
        if (_result_log != '1' && _result_log != '2') {
            DlgUtil.showMsg("Cập nhật log thao tác không thành công: (" + _result_log + ")");
        }
    }
    function getMinutesDiff(date1, date2) {
        let toDate = str  => {
            let [d, m, y, h, i, s] = str.match(/\d+/g).map(Number);
            return new Date(y, m - 1, d, h, i, s);
        };
        return Math.floor((toDate(date2) - toDate(date1)) / (1000 * 60));
    }
}
/*
File mã nguồn : NGT02K016_ChiDinhDV_KHA.js
<PERSON><PERSON><PERSON> đích  : Chỉ định dịch vụ giao diện mới cho KHA
Dev		-	Date		-	Note
HANV	-	28112020	-	Tạo mới
*/
function NGT02K016_ChiDinhDV_KHA(_opt) {
	this.load = doLoad;
	opt = $.extend({}, _opt);
	_col = "Mã bệnh,ICD10CODE,30,0,f,l;Tê<PERSON> bệnh,ICD10NAME,70,0,f,l";
	_colDv = "DICHVUID,DICHVUID,0,0,t,l;Mã DV,MADICHVU,15,0,f,l;Tên DV,TENDICHVU,40,0,f,l;Giá BH,GIABHYT,10,0,f,l;Giá <PERSON>,GIANHANDAN,10,0,f,r;"
			+ "<PERSON><PERSON><PERSON>,GIADICHVU,10,0,f,r;<PERSON><PERSON><PERSON> lệch,GIACHENHLECH,10,0,f,r";
	var _objDichVu = {};
	_sql = "CG.ICD10";
	_gridHeader = "MANHOM,NHOMDICHVUID,0,0,t,l;Tên nhóm,TENNHOM,0,0,t,l;Mã CK,CHUYENKHOAID,50,0,f,l;Mã DV,MADICHVU,70,0,f,l;dichvuid,DICHVUID,0,0,t,l;"
			+ "Tên DV,TENDICHVU,180,0,f,l;Tên TT37,TEN_TT37,180,0,f,l;Giá BH,GIABHYT,80,number,f,r;Giá ND,GIANHANDAN,80,number,f,r;"
			+ "Giá YC,GIADICHVU,80,number,f,r;Phòng thực hiện,ORG_NAME,60,0,t,l;Phòng ID,PHONGID,60,0,t,l;Loại,LOAINHOMDICHVU,60,0,t,l;"
			+ "Loại nhóm BHYT,NHOM_MABHYT_ID,60,0,t,l;DICHVU_BHYT_DINHMUC,DICHVU_BHYT_DINHMUC,10,0,t,l;GIA_DVC,GIA_DVC,10,0,t,l;"
			+ "NHOM MABHYT,MANHOM_BHYT,10,0,t,l;DONVI,DONVI,10,0,t,l;KHOANMUCID,KHOANMUCID,10,0,t,l;LOAIDV,LDV,1,0,t,l;DICHVUCHINH,DICHVUCHINH,1,0,t,l;"
			+ "DICHVUDIKEM,DICHVUDIKEM,1,0,t,l;MAGIUONG,MAGIUONG,1,0,t,l;LOAIGIUONGID,LOAIGIUONGID,1,0,t,l;GIO_SUATAN,GIO_SUATAN,1,0,t,l;"
			+ "SO_LIT,SO_LIT,0,0,t,l;LOAIMAUBENHPHAM,LOAIMAUBENHPHAM,50,0,t,l;Giá chênh lệch,GIACHENHLECH,100,number,f,r;KHOA_DTK,KHOA_DTK,0,0,t,l;"
			+ "CANHBAO_XUATTOAN,CANHBAO_XUATTOAN,0,0,t,l;Phụ thu,PHUTHU,0,0,t,l;LOAI_MBP,LOAI_MBP,0,0,t,l";
	_gridHeader_NT = "MANHOM,NHOMDICHVUID,0,0,t,l;Tên nhóm,TENNHOM,0,0,t,l;Mã CK,CHUYENKHOAID,50,0,f,l;Mã DV,MADICHVU,60,0,f,l;dichvuid,DICHVUID,0,0,t,l;"
			+ "Tên DV,TENDICHVU,180,0,f,l;Giá ND,GIANHANDAN,60,number,f,r;Giá BH,GIABHYT,60,number,f,r;"
			+ "Giá YC,GIADICHVU,60,number,f,r;Phòng thực hiện,ORG_NAME,60,0,t,l;Phòng ID,PHONGID,60,0,t,l;Loại,LOAINHOMDICHVU,60,0,t,l;"
			+ "Loại nhóm BHYT,NHOM_MABHYT_ID,60,0,t,l;DICHVU_BHYT_DINHMUC,DICHVU_BHYT_DINHMUC,10,0,t,l;GIA_DVC,GIA_DVC,10,0,t,l;"
			+ "NHOM MABHYT,MANHOM_BHYT,10,0,t,l;DONVI,DONVI,10,0,t,l;KHOANMUCID,KHOANMUCID,10,0,t,l;LOAIDV,LDV,1,0,t,l;DICHVUCHINH,DICHVUCHINH,1,0,t,l;"
			+ "DICHVUDIKEM,DICHVUDIKEM,1,0,t,l;MAGIUONG,MAGIUONG,1,0,t,l;LOAIGIUONGID,LOAIGIUONGID,1,0,t,l;GIO_SUATAN,GIO_SUATAN,1,0,t,l;SO_LIT,SO_LIT,0,0,t,l;"
			+ "Phụ thu,PHUTHU,0,0,t,1;GIACHENHLECH,GIACHENHLECH,100,number,f,l";
	lookup_sql = "NTU02D009.EV001";
	_grdCDHeader = " ,ACTION,30,d,f,l;nhomdichvu,NHOMDICHVUID,0,0,t,l;dichvuid,DICHVUID,0,0,t,l;Hẹn,HEN,50,0,f,l,ES;Tên dịch vụ,TENDICHVU,250,0,f,l;"
			+ "SL,SOLUONG,50,0,e,r;Loại MBP,LOAIMAUBENHPHAM,60,0,f,l,ES;Phòng thực hiện,PHONG_TH,230,0,f,l,ES;Tình trạng,TINHTRANG_PTTT,80,0,f,l,ES;"
			+ "Thực hiện vi sinh,THUCHIENVISINH,80,0,f,l,ES;ISVISINH,ISVISINH,0,0,t,l;Ghi chú,GHICHU,300,0,e,l;Giá tiền,GIA_TIEN,100,number,f,r;"
			+ "BHYT trả,BHYT_TRA,100,number,f,r;Miễn giảm,MIENGIAM,100,number,f,r;ND trả,THANH_TIEN,100,number,f,r;Loại TT cũ,LOAITT_CU,100,0,f,l;"
			+ "Loai,LOAINHOMDICHVU,60,0,t,l;Loại nhóm BHYT,NHOM_MABHYT_ID,60,0,t,l;Giá BH,GIABHYT,100,0,t,l;Giá ND,GIANHANDAN,100,0,t,l;"
			+ "Giá YC,GIADICHVU,100,0,t,l;Loai DT,LOAIDOITUONG,60,0,t,l;Mau BP,MAUBENHPHAMID,60,0,t,l;NHOMBENHPHAM,NHOMBENHPHAM,60,0,t,l;"
			+ "DICHVU_BHYT_DINHMUC,DICHVU_BHYT_DINHMUC,10,0,t,l;LOAI_DT_MOI,LOAI_DT_MOI,10,0,t,l;Giá chênh,GIA_CHENH,100,number,f,r;GIA_DVC,GIA_DVC,10,0,t,l;"
			+ "Loại TT mới,LOAITT_MOI,100,0,f,l;PHONG_TH1,PHONG_TH1,10,0,t,l;NHOM MABHYT,MANHOM_BHYT,10,0,t,l;DONVI,DONVI,10,0,t,l;"
			+ "KHOANMUCID,KHOANMUCID,10,0,t,l;BHYT trả,BHYT_TRAFULL,1,number,t,r;ND trả,THANH_TIENFULL,1,number,t,r;Miễn giảm,MIENGIAMFULL,0,number,t,r;"
			+ "BHYT trả,BHYT_TRAFINAL,1,number,t,r;ND trả,THANH_TIENFINAL,1,number,t,r;Miễn giảm,MIENGIAMFINAL,0,number,t,r;Chênh Full,GIA_CHENHFULL,1,number,t,r;"
			+ "Chênh Final,GIA_CHENHFINAL,1,number,t,r;LOAIPTTT,LOAIPTTT,0,0,t,l;TYLEDVTEMP,TYLEDVTEMP,0,0,t,r;DICHVUCHINH,DICHVUCHINH,1,0,t,l;"
			+ "DICHVUDIKEM,DICHVUDIKEM,1,0,t,l;Phiếu hẹn,PHIEUHEN,1,0,t,l;Old value,OLDVALUE,1,0,t,l;Has order,HAS_ORDER,1,0,t,l;Tách phiếu,TACHPHIEU,1,0,t,l;"
			+ "JSON_TEMP,JSON_TEMP,0,0,t,l;Giờ Suất ăn,GIO_SUATAN,1,0,t,l;JSON_NUOICAY,JSON_NUOICAY,0,0,t,l;DIEUKIEN_DV,DIEUKIEN_DV,0,0,t,l;"
			+ "Tỷ lệ,TYLEDV,0,0,t,1;Phụ thu,PHUTHU,0,0,t,1;GIACHENHLECH,GIACHENHLECH,0,0,t,l;LOAIKHAM,LOAIKHAM,0,0,t,l;LOAI_MBP,LOAI_MBP,0,0,t,l";
	_grdCDHeader_dvk = "nhomdichvu,NHOMDICHVUID,0,0,t,l;dichvuid,DICHVUID,0,0,t,l;Tên dịch vụ,TENDICHVU,250,0,f,l;SL,SOLUONG,50,0,e,r;"
			+ "Loại MBP,LOAIMAUBENHPHAM,60,0,f,l;Tỷ lệ,TYLEDV,80,0,f,r,ES;Phòng thực hiện,PHONG_TH,300,0,t,l,ES;Ghi chú,GHICHU,300,0,e,l;"
			+ "Giá tiền,GIA_TIEN,100,number,f,r;BHYT trả,BHYT_TRA,100,number,f,r;Miễn giảm,MIENGIAM,100,number,f,r;ND trả,THANH_TIEN,100,number,f,r;"
			+ "Tổng,TONGTIEN,100,number,f,r;Loại TT cũ,LOAITT_CU,100,0,f,l;Loai,LOAINHOMDICHVU,60,0,t,l;Loại nhóm BHYT,NHOM_MABHYT_ID,60,0,t,l;"
			+ "Giá BH,GIABHYT,100,0,t,l;Giá ND,GIANHANDAN,100,0,t,l;Giá YC,GIADICHVU,100,0,t,l;Loai DT,LOAIDOITUONG,60,0,t,l;Mau BP,MAUBENHPHAMID,60,0,t,l;"
			+ "NHOMBENHPHAM,NHOMBENHPHAM,60,0,t,l;DICHVU_BHYT_DINHMUC,DICHVU_BHYT_DINHMUC,10,0,t,l;LOAI_DT_MOI,LOAI_DT_MOI,10,0,t,l;"
			+ "Giá chênh,GIA_CHENH,100,number,f,r;GIA_DVC,GIA_DVC,10,0,t,l;Loại TT mới,LOAITT_MOI,100,0,f,l;PHONG_TH1,PHONG_TH1,10,0,t,l;"
			+ "NHOM MABHYT,MANHOM_BHYT,10,0,t,l;DONVI,DONVI,10,0,t,l;KHOANMUCID,KHOANMUCID,10,0,t,l;BHYT trả,BHYT_TRAFULL,1,number,t,r;"
			+ "Miễn giảm,MIENGIAMFULL,0,number,t,r;ND trả,THANH_TIENFULL,1,number,t,r;BHYT trả,BHYT_TRAFINAL,1,number,t,r;Miễn giảm,MIENGIAMFINAL,0,number,t,r;"
			+ "ND trả,THANH_TIENFINAL,1,number,t,r;Chênh Full,GIA_CHENHFULL,1,number,t,r;Chênh Final,GIA_CHENHFINAL,1,number,t,r;TYLEDVTEMP,TYLEDVTEMP,0,0,t,r;"
			+ "Old value,OLDVALUE,1,0,t,l;JSON_TEMP,JSON_TEMP,0,0,t,l;KHOA_DTK,KHOA_DTK,0,0,t,l;Phụ thu,PHUTHU,0,0,t,1;GIACHENHLECH,GIACHENHLECH,0,0,f,l";
	_grdCDHeaderOld = "dichvuid,DICHVUID,0,0,t,l;Tên dịch vụ,TENDICHVU,100,0,f,l;Phòng chỉ định,PHONGCHIDINH,50,0,f,l";
	//Begin_HaNv_12102020: Chỉ định dịch vụ theo bộ dịch vụ - L2PT-28041
	var _gridGoiDvHeader = "bodvid,bodvid,50,0,t,l,1,2;Mã bộ,mabo,250,0,f,l,1,2;Tên bộ,tenbo,300,0,f,l,1,2";
	//End_HaNv_12102020
	var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4} (\d{2}):(\d{2}):(\d{2})$/;
	var dGridCddv = $.Deferred();
	var dGridXn = $.Deferred();
	var dGridCdha = $.Deferred();
	var dGridCk = $.Deferred();
	var dGridDv = $.Deferred();
	var validator = null;
	var flagMsg = true; //Đánh dấu thông báo vượt tiền(tắt thông báo không hiển thị lần sau nữa)
	var flagLoad = true;
	var icdchinh = "";
	var textchinh = "";
	var icdphu = "";
	var config = 1;
	var _insurance_bf = 0;
	var tongbh = 0;
	var currentTime = null;
	var noEdit = false;
	var printPrivare = true;
	var book = false;
	var bookAll = false;
	var appointment = false;
	var printAll = false;
	var existConfig = false;
	var isTranspost = false;
	var plDv = '0';
	var khambenhId = opt._khambenhId;
	var record = 100;
	var isColspan = false;
	var isSwapColumn = false;
	var isDisplayColumnTt37 = false;
	var isGroupService = false;
	var isNotEdit = false;
	var isShowNumService = false;
	var isSearchLike = false;
	var hideColCk = false;
	var isCbPd = false;
	var isEditPttt = false;
	var isShowMsgWr = false;
	var isKeGiuongTheoNgay = false;
	var isBacsiKe = false;
	var isKeChung = false;
	var isTachCdha = false;
	var isCbNbh = false;
	var isCbQuaTg = false;
	var loaigiuongid = _opt.loaigiuongid;
	var magiuong = _opt.magiuong;
	var isNhomDvKtp = "";
	var isHienThiMaGiuong = false;
	var isLayBenhPham = false;
	var isAutoPrint = false;
	var isUncheckDuplicate = false;
	var isNhapGiuongTp = false;
	var isPhongcd = false;
	var isNgaydv = false;
	var isKeleCls = false;
	var isChanMg = false;
	var isChancbTu = false;
	var isQn = '';
	var isHtvv = '';
	var isAutoP = '';
	var isCPdt = false;
	var isKhoabs = false;
	var isGiuongdv = false;
	var giuongdv = '';
	var isIcdPd = false;
	var _loainhom_mau = (typeof opt._chidinhdichvu == "undefined") ? '0' : opt._loaiphieumbp;//HaNv_20082020: Fix loainhom phiếu mẫu - L2PT-26065
	var _jsonPhieuMau = {
		DS_DICHVU : [],
		TEN_MAUPHIEU : "",
		KHAMBENHID : ""
	};
	var _colICD = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
	var flagMsgMoney = false;//Đánh dấu đã vượt tiền khi loadform
	var dGridKctt = $.Deferred();
	var checkKctt = true;
	var checkInthukhac = false;// nghiant L2DKBD-404 20092017
	var _typePrint = 'pdf';
	var isShowMsgVuotTien = false;
	var isNgaydvVienphi = false;
	var isNhomDvViSinh = false; //Co ton tai danh sach nhom dich vu vi sinh duoc cau hinh
	var isShowSoLuongCls = false; //Hiển thị số lượng dịch vụ để nhân số bản ghi trên grdDSCD OnSelectRow
	var isCdNgoaigio = false;
	var isHcSangBd = '';
	var isHcSangKt = '';
	var isHcChieuBd = '';
	var isHcChieuKt = '';
	var isKhoaYc = false;
	var isKhoaChHc = false;
	var isChDt = false;
	var showTgKetThucGiuong = false;//HaNv_05072018
	var isTachPhieuTheoLoaiDT = false;//HaNv_06072018
	var isShowMsgVuotTienKB = false;//HaNv_23082018
	var isLuuTiep = false;//hongdq_07092018
	var tgSDLuuTiep = '';//hongdq_07092018
	var isCar = false;
	var isMauCls = false;
	var isMauPrint = 'pdf';
	var isTinhXang = false;
	var isGiaxang = 0;
	var isDvDieuKien = false;
	var config_hanthe = '0';
	var cf_dauthe = '';
	var isSaveView = false;//L2HOTRO-12397
	var isSaveNotMsg = false;//HaNv_05012019
	var showTinhTrangPttt = false; //HaNv_29012019: show cot tinh trang PTTT - L2PT-1567
	var isCheckSlNguoi = false;
	var isShowTabTd = false;
	var jsonNuoiCay = ''; //HaNv_12042019: chinh sua chuc nang dich vu co DK nuoi cay L2PT-3612
	var isConfigGyc = false;
	var isTachPttt = false;
	var reqChandoanbandau = false; //HaNv_07052019: hien thi chandoanbandau tu khambenh - L1PT-719
	var isTlQy15 = false;
	var isCddvCl = false;
	var cbSoLuongXN = false;//HaNv_10072019: Canh bao khi chi dinh dv xet nghiem so luong > 1 - L2PT-6748
	var isDisplayColGiaChenh = false;//HaNv_24072019: Hien thi cot gia chenh lech - L2PT-7281
	var usingReportCode = false;//HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
	var checkDichVuByICD = 0;//HaNv_12082019: gioi han thuc hien dichvu CLS theo ICD trong 1 dot dieu tri - L2PT-7077
	var checkKhongCungCd = 0;//HaNv_12082019: Check dichvuid khong cung chi dinh chung voi nhau - L2PT-7660
	var isPrintPhieuAoVang = false;//HaNv_20092019: Tích hợp js phiếu áo vàng theo yc HuongPv - L2PT-8810
	var isGomNhomXn = false;//HaNv_10102019: Gộp chung 1 số phiếu với các DV xét nghiệm khác nhóm - L2PT-9671
	var isKeGiuongVd2 = false;//HaNv_22102019: Cho phép nhập ngày giường của BN số thập phân lẻ đến 0.25 - L1PT-1936
	var isSoLuongPttt = false;//HaNv_25102019: Thêm số lượng phiếu cần nhân ra với loại phiếu PTTT - L2PT-10188
	var isTachTheoGiaChenhLech = false;//HaNv_08112019: Tính toán lại giá chênh khi tách phiếu cho DKLAN - L2PT-10994
	var isBacsiSearch = false;//HaNv_13112019: Cho phép tìm kiếm bác sỹ trong danh sách thay vì chọn - L2PT-11245
	var isNhapSoLuongMbp = false;//HaNv_26112019: Cho phép gen mẫu bệnh phẩm theo số lượng - L2PT-12306
	var cboBacsiVal = '';
	var notGiaChenhVp = false;//HaNv_09012020: Không tính giá chênh cho BN viện phí - L2PT-14904
	var isClDvk = false;//HungNt_15012020: Chinh sua gia chenh lech cho cac dich vu khac - L2PT-15225
	var cbKeGiuongDuoi4h = false;//HaNv_16012020: Cảnh báo kê giường với BN nhập viện dưới 4h - L2PT-15165
	var cboBacsiNgoaiPk = '';//HaNv_17012020: Cho phép chọn Bác Sĩ bên ngoài phòng khám để tính hoa hồng - L2PT-15119
	var isDVTheoGoi = false;//HaNv_24022020: Cho phép chia giá của gói dịch vụ theo số lượng phiếu - L2PT-16531
	var checkMaBs = 0;//HaNv_23032020: Kiểm tra Chứng chỉ hành nghề (Mã BS) với BN BHYT - L2PT-18389
	var isClDvLv = false;
	var cpCdKetThucBA = false;//HaNv_08042020: Vẫn cho phép chỉ định DV khi BN đã kết thúc BA - L2PT-20670
	var chanDaKeThuoc = false;//HaNv_19052020: Chặn chỉ định khi BN đã kê thuốc (khám bệnh) - L2PT-21220
	var cbSoLuong = 0;//HaNv_13062020: Cảnh báo khi sửa số lượng chỉ định CLS > 1 - L2PT-22791
	var cbVanChuyenMucHuong = false;//HaNv_06072020: Cảnh báo vận chuyển theo mức hưởng thẻ BH - L2PT-22942
	var cbTrungDVCD = 0;//HaNv_10082020: Cảnh báo, chặn chỉ định CLS đã chỉ định trong cùng một đợt điều trị - L2PT-25013
	var tienMucTranBh = 0;//HaNv_15082020: cảnh báo chỉ định vượt tiền mức trần BHYT đã cấu hình - L2PT-25620
	var slClsNguyenDuong = false;//HaNv_20082020: Chỉ cho phép kê lẻ CLS với số lượng nguyên dương - L2PT-25936
	var config_cdgdv = 0;
	var isHidePcd = false;//HaNv_08092020: L2PT-26552 - L2PT-29789
	var changeLoaiMbp = false;//HaNv_09092020: L2PT-26489
	var checkKiSoPDT = false;//HaNv_05102020: L2PT-28416
	var isMapGiuongVattu = false;//HaNv_09102020: L2PT-28565
	var isShowTabGoiDv = false;//HaNv_12102020: L2PT-28041
	var tgkegiuong_nhapkhoa = false;//HaNv_17102020: L2PT-28913
	function doLoad() {
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		loadLISConfig();
		//START Suat an
		var check_par = [];
		check_par.push({
			"name" : "[0]",
			"value" : "1"
		});
		tgSDLuuTiep = jsonrpc.AjaxJson.getOneValue("COM.SYSDATE_P", check_par);
		//END Suat an
		opt.hospitalId = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');
		_opt.hospitalId = opt.hospitalId;
		_initControl();
		_bindEvent();
	}
	function _initControl() {
		if (opt.hospitalId == '1014') {
			$('#btnLuuXem').show();
		}
		//START -- phan he dinh duong -- hongdq -- 20180418
		if (opt._loaidichvu == '12') {
			$('#divTgSD').show();
			if (opt.thoigiantiep == '') {
				$("#txtTGSUDUNG").val(tgSDLuuTiep);
			} else {
				$("#txtTGSUDUNG").val(opt.thoigiantiep);
			}
			$('#btnLuuIn').hide();
			$('#btnSaveTemp').hide();
			$('#btnPhieuMau').hide();
			$('#btnLuuTiep').show();
			$('#tabDanhSachDichVuDaChiDinh').hide();
		} else {
			$('#divTgSD').hide();
		}
		//END -- phan he dinh duong -- hongdq -- 20180418
		if (opt._loaidichvu == '19') {
			_grdCDHeader_dvk = _grdCDHeader_dvk.replace('ND trả,THANH_TIEN', 'Miễn giảm,THANH_TIEN');
		}
		validator = new DataValidator("divData");
		$('#txtLOAIDV').val('0');
		$('#cboLOAIDV').val('0');
		$('#txtLOAIDV').focus();
		currentTime = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		$('#txtTGCHIDINH').val(currentTime);
		$('#hidBENHNHANID').val(opt._benhnhanId);
		$('#hidDOITUONGBENHNHANID').val(opt._doituongbenhnhanId);
		$('#hidHOSOBENHANID').val(opt._hosobenhanId);
		$('#hidKHAMBENHID').val(opt._khambenhId);
		$('#hidTIEPNHANID').val(opt._tiepnhanId);
		$('#hidLOAITIEPNHANID').val(opt._loaitiepnhanId);
		var f3 = 114;
		var f4 = 115;
		var f6 = 117;
		var f7 = 118;
		var f8 = 119;
		$(document).unbind('keydown').keydown(function(e) {
			if (e.keyCode == f3) {
				$('#btnLuuIn').click();
			}
			if (e.keyCode == f4) {
				$('#btnLuu').click();
			}
		});
		var _colGroup = "Mã nhóm,MADICHVU,30,0,f,l;Tên nhóm,TENDICHVU,70,0,f,l;ID,DICHVUID,0,0,t,l";
		// cau hinh cho benh vien
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "HIS_EDIT_SOLUONG_GIUONG;HIS_IN_PHIEU_XET_NGIEM_CHUNG;HIS_HEN_XET_NGHIEM;HIS_CLS_BANGHI;HIS_PRINT_CLS_CHUNG;"
				+ "HIS_NHAP_CHUANDOAN;HIS_PL_DICHVU;HIS_CHIDINH_DICHVU;HIS_DUNGTUYEN_VC;HIS_COLSPAN_PARENT;HIS_SWAP_COLUMN_DV;HIS_DISPLAY_CDDV_COLUMN_TT37;NGT_GHICHU_BENHCHINH;"
				+ "HIS_NHOMDV_CHIDINH;HIS_EDIT_SL_DICHVU;HIS_SOLUONG_DICHVU;HIS_TIM_KIEM_GAN_DUNG;HIS_CK_COL_HIDE;HIS_DIEUTRI_THEO_PHACDO;HIS_CANHBAO_PHACDO;HIS_EDIT_PTTT;HIS_SEARCH_NHOM_CK;"
				+ "HIS_KEGIUONG_THEONGAY;HIS_BACSI_KE;HIS_CHIDINHCHUNG;HIS_TACH_CDHA;HIS_CANHBAO_DICHVU_NBH;HIS_CHAN_DICHVU_QUANGAY;HIS_NHOMDV_KHONGTP;HIS_LAYBENHPHAM;HIS_HIEN_THI_MAGIUONG;"
				+ "HIS_BO_CHECK_TRUNGDV;HIS_FILEEXPORT_TYPE;HIS_NHAPGIUONG_TP;HIS_PCD_PTH;HIS_CHAN_NGAY_DV;HIS_KELE_DICHVU_CLS;HIS_CHAN_MAGIUONG;MSG_CANHBAO_DT_TIENG_TU;PHONG_TUDONG_IN;HIS_CHON_PDT;"
				+ "HIS_GIUONG_DICHVU;HIS_BACSI_KE_PCK;HIS_CHON_PDT_ICD;CHAN_NGAYDV_CHOPHEP_VP;HIS_DS_NHOMDV_VISINH;HIS_CDDV_SHOW_SOLUONG_CLS;HIS_TT_NGOAI_HC;HIS_TT_NGOAI_HC_SANG_BD;"
				+ "HIS_TT_NGOAI_HC_SANG_KT;HIS_TT_NGOAI_HC_CHIEU_BD;HIS_TT_NGOAI_HC_CHIEU_KT;HIS_KHOA_YC;HIS_KHOA_CH_NGOAI_HC;HIS_DTBN_TT_HC;HIS_CDDV_TGKETTHUC_GIUONG;CDDV_TACHPHIEU_THEO_LOAIDT;"
				+ "SHOWMSG_VUOTTIEN_KB;CHANCUNG_VUOTTIEN_KB;HIS_BACSI_KE_NGT;HIS_CAR_VC;HIS_MAU_CLS_VD;SHOWMSG_VUOTTIEN;CHANCUNG_VUOTTIEN;HIS_TINH_LITX;GIA_XANG;HIS_KIEU_IN_MAU_CLS_VD;"
				+ "HIS_GROUP_DICHVU_CHENHLECH;HIS_DICHVU_CO_DK;CDDV_HIDE_BTNLUU;HIS_HANTHE_BHYT;HIS_DAUTHE_BHYT_MG;NTU_SAVEOK_NOTMSG;SHOWCOL_TINHTRANG_PTTT;HIS_CHECK_SOLUONG_NGUOI_GIUONG;"
				+ "HIS_SHOW_TAB_DVTHUONGDUNG;HIS_TINH_CHENHLECH_GYC;HIS_TACH_PTTT;BM2_CHANDOANBANDAU;HIS_QY_TLT_15;HIS_BACSI_KE_PNG;HIS_BACSI_KE_PVC;CDDV_CB_SOLUONG_XN;HIS_CDDV_CL;CDDV_CHAN_DTBN_DV;"
				+ "CDDV_DISPLAY_COL_GIACHENH;CDDV_USING_REPORT_CODE;CDDV_CB_ICD_DV;CDDV_CB_DV_KHONGCUNGCD;IS_PHIEU_AOVANG;CDDV_GOMNHOM_XN;CDDV_SHOW_SONGAY_THUKHAC;HIS_KEGIUONG_VD2;"
				+ "CDDV_SOLUONG_PTTT;VANCHUYEN_MACDINH_THUPHI;CDDV_DICHVU_GIACHENH;CDDV_BACSI_SEARCH;CDDV_NHAP_SOLUONGMBP_CLS;CDDV_CHECK_USERTYPE;CDDV_KO_GIACHENH_VP;CDDV_TINH_CL_DVK;"
				+ "CDDV_KEGIUONG_DUOI4H;CDDV_BACSI_NGOAIPK;CDDV_BACSI_DEFAULT;CDDV_CHECK_MABACSI;HIS_DICHVU_CHENHLECH_LV;CDDV_CHAN_DAKETHUOC;CDDV_SHOW_LICHSU_BENHAN;CDDV_CB_SOLUONG_CLS;"
				+ "CDDV_SHOW_SONGAY_CONGKHAM;CDDV_SHOW_TTLAYMAU_BP;CDDV_CB_VANCHUYEN_MUCHUONG;CDDV_HIDE_TEMP_DVKHAC;CDDV_THUKHAC_BOSUNG;CDDV_CB_DVCD_DIEUTRI;HIS_SOTIEN_MUCTRAN_BHYT;"
				+ "CDDV_SOLUONG_CLS_NGUYENDUONG;CDDV_CHUYEN_GIADICHVU_VP;CDDV_HIDE_VP_DV;CDDV_PHONGTH_HIDE_PCD;CDDV_CHANGE_LOAIMBP;HIS_CHECK_DIEUTRI_KISO;CDDV_MAP_GIUONG_VATTU;"
				+ "CDDV_BACSI_TOANVIEN_LNMBP;CDDV_SHOW_TAB_GOIDV;GIUONG_TGCD_NGAYNHAPKHOA;CDDV_HIDE_CHUYENDOITUONG_DV");
		if (data_ar != null && data_ar.length > 0) {
			if (typeof opt._chidinhdichvu == "undefined") {
				//Begin_HaNv_12102020: Chỉ định dịch vụ theo bộ dịch vụ - L2PT-28041
				if (data_ar[0].CDDV_SHOW_TAB_GOIDV != '0') {
					isShowTabGoiDv = true;
					$('#liGoiDv').show();
				}
				//End_HaNv_12102020
				//Begin_HaNv_09092020: Cho phép thay đổi loại Mbp khi chỉ định dịch vụ - L2PT-26489
				if (data_ar[0].CDDV_CHANGE_LOAIMBP == '1') {
					changeLoaiMbp = true;
				}
				//End_HaNv_09092020
				//Begin_HaNv_08092020: Ẩn phòng chỉ định vào danh sách phòng thực hiện DV PTTT - L2PT-26552 - L2PT-29789
				if (data_ar[0].CDDV_PHONGTH_HIDE_PCD == '1') {
					isHidePcd = true;
				}
				//End_HaNv_08092020
				if (data_ar[0].CDDV_HIDE_VP_DV != '0') {
					$('#changeVPYC').remove();
				}
				if (data_ar[0].CDDV_CHUYEN_GIADICHVU_VP != '0') {
					config_cdgdv = data_ar[0].CDDV_CHUYEN_GIADICHVU_VP;
				}
				//Begin_HaNv_20082020: Chỉ cho phép kê lẻ CLS với số lượng nguyên dương - L2PT-25936
				if (data_ar[0].CDDV_SOLUONG_CLS_NGUYENDUONG == '1') {
					slClsNguyenDuong = true;
				}
				//End_HaNv_20082020
				//Begin_HaNv_10082020: cảnh báo, chặn chỉ định CLS đã chỉ định trong cùng một đợt điều trị - L2PT-25013
				if (data_ar[0].CDDV_CB_DVCD_DIEUTRI != '0') {
					cbTrungDVCD = data_ar[0].CDDV_CB_DVCD_DIEUTRI;
				}
				//End_HaNv_10082020
				//Begin_HaNv_04072020: Bổ sung thông tin người lấy mẫu, thời gian lấy mẫu khi chỉ định - L2PT-23017
				if (data_ar[0].CDDV_SHOW_TTLAYMAU_BP != '0') {
					$('#divTTLAYMAU').show();
					$('#txtTGLAYMAU').val(currentTime);
					var _colNv = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USER_NAME,20,0,f,l;Tên điều dưỡng,OFFICER_NAME,30,0,f,l;Chức danh/Khoa phòng,CHUCDANH,50,0,f,l";
					var sql_par = [];
					sql_par.push({
						"name" : "[0]",
						"value" : "6"
					});
					ComboUtil.initComboGrid("txtNGUOILAYMAU", "CDDV.USERBYLOAINV", sql_par, "600px", _colNv, function(event, ui) {
						$("#txtNGUOILAYMAU").val("");
						var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.OFFICER_NAME + '</option>');
						$("#cboNGUOILAYMAU").empty();
						$("#cboNGUOILAYMAU").append(option);
						return false;
					});
				} else {
					$('#divTTLAYMAU').remove();
				}
				//End_HaNv_04072020
				//Begin_HaNv_10072019: Canh bao khi chi dinh dv xet nghiem so luong > 1 - L2PT-6748
				if (data_ar[0].CDDV_CB_SOLUONG_CLS != '0') {
					cbSoLuong = data_ar[0].CDDV_CB_SOLUONG_CLS;
				}
				//End_HaNv_10072019
				//Begin_HaNv_19052020: Chặn chỉ định khi BN đã kê thuốc (khám bệnh) - L2PT-21220
				if (data_ar[0].CDDV_CHAN_DAKETHUOC == '1' && $('#hidLOAITIEPNHANID').val() == 1) {
					var sql_par = [ {
						"name" : "[0]",
						"value" : $('#hidKHAMBENHID').val()
					} ];
					var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
					var rows = $.parseJSON(data);
					if (rows.length > 0) {
						DlgUtil.showMsg("Bệnh nhân đã kê thuốc. Không cho phép chỉ định dịch vụ cận lâm sàng!");
						$('#btnLuu').hide();
						$('#btnLuuIn').hide();
						return;
					}
				}
				//End_HaNv_19052020
				if (data_ar[0].HIS_DICHVU_CHENHLECH_LV != '0') {
					isClDvLv = true;
				}
				//Begin_HaNv_23032020: Kiểm tra Chứng chỉ hành nghề (Mã BS) với BN BHYT - L2PT-18389
				if ($('#hidDOITUONGBENHNHANID').val() == '1') {
					if (data_ar[0].CDDV_CHECK_MABACSI == '1') {
						checkMaBs = 1; //Canh bao
					} else if (data_ar[0].CDDV_CHECK_MABACSI == '2') {
						checkMaBs = 2; //Chan chi dinh
					}
				}
				//End_HaNv_23032020
				//Begin_HaNv_17022020: Fix giá trị cboBACSI theo giá trị cấu hình - L2PT-16383
				if (data_ar[0].CDDV_BACSI_DEFAULT != '0') {
					opt.bacsike = data_ar[0].CDDV_BACSI_DEFAULT;
				}
				//End_HaNv_17022020
				//Begin_HaNv_17012020: Cho phép chọn Bác Sĩ bên ngoài phòng khám để tính hoa hồng - L2PT-15119
				if (data_ar[0].CDDV_BACSI_NGOAIPK == '1') {
					$('#divBacSiNgoai').show();
					var _colBsNgoai = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USER_NAME,20,0,f,l;Tên bác sỹ,OFFICER_NAME,30,0,f,l;Chức danh/Khoa phòng,CHUCDANH,50,0,f,l";
					ComboUtil.initComboGrid("txtBACSINGOAIPK", "CDDV.BSNGOAIPK", [], "600px", _colBsNgoai, function(event, ui) {
						$("#txtBACSINGOAIPK").val("");
						var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.OFFICER_NAME + '</option>');
						$("#cboBACSINGOAIPK").empty();
						$("#cboBACSINGOAIPK").append(option);
						return false;
					});
				} else {
					$('#divBacSiNgoai').remove();
				}
				//End_HaNv_17012020
				//Begin_HaNv_08112019: Tính toán lại giá chênh khi tách phiếu cho DKLAN - L2PT-10994
				if (data_ar[0].CDDV_DICHVU_GIACHENH == '1') {
					isTachTheoGiaChenhLech = true;
				}
				//End_HaNv_08112019
				//Begin_HaNv_25102019: Thêm số lượng phiếu cần nhân ra với loại phiếu PTTT - L2PT-10188 - L2PT-21002
				if (data_ar[0].CDDV_SOLUONG_PTTT == '1') {
					$('#divSOLUONGPTTT').show();
					$('#txtSOLUONGPTTT').val(1);
					isSoLuongPttt = true;
				}
				//End_HaNv_25102019
				//Begin_HaNv_26112019: Cho phép gen mẫu bệnh phẩm theo số lượng - L2PT-12306
				if (data_ar[0].CDDV_NHAP_SOLUONGMBP_CLS == '1') {
					$('#divSOLUONGMBPCLS').show();
					$('#txtSOLUONGMBPCLS').val(1);
					isNhapSoLuongMbp = true;
				}
				//End_HaNv_26112019
				//Begin_HaNv_12072019: Chan CDDV voi benh nhan noi tru dich vu - L2PT-6576
				if (data_ar[0].CDDV_CHAN_DTBN_DV == '1' && $('#hidLOAITIEPNHANID').val() == 0 && $('#hidDOITUONGBENHNHANID').val() == '3') {
					DlgUtil.showMsg("Không cho phép chỉ định dịch vụ với bệnh nhân nội trú dịch vụ!");
					$('#btnLuu').hide();
					$('#btnLuuIn').hide();
					return;
				}
				//End_HaNv_12072019
				if (data_ar[0].HIS_TACH_PTTT != '0') {
					isTachPttt = true;
				}
				if (data_ar[0].HIS_TINH_CHENHLECH_GYC != '0') {
					isConfigGyc = true;
				}
				if (data_ar[0].HIS_SHOW_TAB_DVTHUONGDUNG != '0') {
					isShowTabTd = true;
					$('#liThuongDung').show();
				}
				if (data_ar[0].HIS_DTBN_TT_HC != '0') {
					if (data_ar[0].HIS_DTBN_TT_HC.includes(opt._doituongbenhnhanId)) {
						isChDt = true;
					}
				}
				if (data_ar[0].HIS_TT_NGOAI_HC == '1' && isChDt) {
					isCdNgoaigio = true;
				}
				if (data_ar[0].HIS_CDDV_CL == '1') {
					isCddvCl = true;
				}
				if (typeof (opt._subdeptId) != "undefined" && opt._subdeptId != "-1") {
					var dataDept = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D009.EV0011", RSUtil.buildParam("", [ opt._subdeptId ]));
					var dataDept_ar = $.parseJSON(dataDept);
					if (dataDept_ar != null && dataDept_ar.length > 0) {
						if (data_ar[0].HIS_KHOA_YC != '0') {
							if (data_ar[0].HIS_KHOA_YC == 'ALL' || data_ar[0].HIS_KHOA_YC.includes(dataDept_ar[0].ORG_CODE + ",")) {
								isKhoaYc = true;
								if (!isCddvCl) {
									opt._doituongbenhnhanId = '3';
								}
								isCdNgoaigio = false;
							}
						}
						if (data_ar[0].HIS_KHOA_CH_NGOAI_HC != '0' && isChDt && isCdNgoaigio) {
							if (data_ar[0].HIS_KHOA_CH_NGOAI_HC.includes(dataDept_ar[0].ORG_CODE + ",")) {
								isKhoaChHc = true;
								isCdNgoaigio = true;
							} else {
								isCdNgoaigio = false;
							}
						} else {
							isCdNgoaigio = false;
						}
					}
				}
				if (data_ar[0].HIS_TT_NGOAI_HC_SANG_BD != '0') {
					isHcSangBd = data_ar[0].HIS_TT_NGOAI_HC_SANG_BD;
				}
				if (data_ar[0].HIS_TT_NGOAI_HC_SANG_KT != '0') {
					isHcSangKt = data_ar[0].HIS_TT_NGOAI_HC_SANG_KT;
				}
				if (data_ar[0].HIS_TT_NGOAI_HC_CHIEU_BD != '0') {
					isHcChieuBd = data_ar[0].HIS_TT_NGOAI_HC_CHIEU_BD;
				}
				if (data_ar[0].HIS_TT_NGOAI_HC_CHIEU_KT != '0') {
					isHcChieuKt = data_ar[0].HIS_TT_NGOAI_HC_CHIEU_KT;
				}
			} else {
				//Begin_HaNv_17102020: Mặc định thời gian kê tiền giường là ngày nhập khoa - L2PT-28913
				if (opt._loaiphieumbp == '12' && data_ar[0].GIUONG_TGCD_NGAYNHAPKHOA == '1') {
					tgkegiuong_nhapkhoa = true;
				}
				//End_HaNv_17102020
				//Begin_HaNv_16072020: Bổ sung thông tin nhân viên đi kèm, Nơi đưa/rước chỉ định thu khác - L2PT-23725
				if (opt._loaidichvu == '1' && data_ar[0].CDDV_THUKHAC_BOSUNG == '1') {
					$('#divTHUKHAC_BOSUNG').show();
				} else {
					$('#divTHUKHAC_BOSUNG').hide();
				}
				//End_HaNv_16072020
				//Begin_HaNv_16072020: Bỏ div Phiếu mẫu với dịch vụ khác (không phải CLS) - L2PT-15165
				if (data_ar[0].CDDV_HIDE_TEMP_DVKHAC == '1') {
					$('#txtTEXT_TEMP').remove();
					$('#btnSaveTemp').remove();
					$('#btnPhieuMau').remove();
				}
				//End_HaNv_16072020
				//Begin_HaNv_16012020: Cảnh báo kê giường với BN nhập viện dưới 4h - L2PT-15165
				if (opt._loaidichvu == '13' && data_ar[0].CDDV_KEGIUONG_DUOI4H == '1') {
					cbKeGiuongDuoi4h = true;
				}
				//End_HaNv_16012020
				//Begin_HungNt_15012020: Chinh sua gia chenh lech cho cac dich vu khac - L2PT-15225
				if (data_ar[0].CDDV_DICHVU_GIACHENH == '1') {
					isTachTheoGiaChenhLech = true;
				}
				//End_HungNt_15012020
				if (typeof (opt._subdeptId) != "undefined" && opt._subdeptId != "-1" && opt._loaidichvu == '12') {
					var dataDept = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D009.EV0011", RSUtil.buildParam("", [ opt._subdeptId ]));
					var dataDept_ar = $.parseJSON(dataDept);
					if (dataDept_ar != null && dataDept_ar.length > 0) {
						if (data_ar[0].HIS_KHOA_YC != '0') {
							if (data_ar[0].HIS_KHOA_YC == 'ALL' || data_ar[0].HIS_KHOA_YC.includes(dataDept_ar[0].ORG_CODE + ",")) {
								isKhoaYc = true;
								opt._doituongbenhnhanId = '3';
							}
						}
					}
				}
				//Begin_HaNv_20092019: Tích hợp js phiếu áo vàng theo yc HuongPv - L2PT-8810
				if (opt._loaiphieumbp == '10' && data_ar[0].IS_PHIEU_AOVANG != '0') {
					isPrintPhieuAoVang = true;
				}
				//End_HaNv_20092019
				//Begin_HungNt_15012020: Chinh sua gia chenh lech cho cac dich vu khac - L2PT-15225
				if (data_ar[0].CDDV_TINH_CL_DVK != '0') {
					isClDvk = true;
					var dataDept = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D009.EV0011", RSUtil.buildParam("", [ opt._subdeptId ]));
					var dataDept_ar = $.parseJSON(dataDept);
					if (dataDept_ar != null && dataDept_ar.length > 0) {
						if (data_ar[0].HIS_KHOA_YC != '0') {
							if (data_ar[0].HIS_KHOA_YC == 'ALL' || data_ar[0].HIS_KHOA_YC.includes(dataDept_ar[0].ORG_CODE + ",")) {
								isKhoaYc = true;
							}
						}
					}
				}
				//End_HungNt_15012020
				//Begin_HaNv_09102020: Tao goi vat tu di kem ngay giuong - L2PT-9828 - L2PT-28565
				if (opt._loaiphieumbp == '12' && data_ar[0].CDDV_MAP_GIUONG_VATTU == '1') {
					isMapGiuongVattu = true;
				}
				//End_HaNv_09102020
			}
			if (data_ar[0].HIS_QY_TLT_15 != '0') {
				isTlQy15 = true;
			}
			if (data_ar[0].HIS_CHECK_SOLUONG_NGUOI_GIUONG != '0' && opt._loaidichvu == '13') {
				isCheckSlNguoi = true;
			}
			if (data_ar[0].HIS_HANTHE_BHYT != '0') {
				config_hanthe = data_ar[0].HIS_HANTHE_BHYT;
			}
			if (data_ar[0].HIS_DAUTHE_BHYT_MG != '0') {
				cf_dauthe = data_ar[0].HIS_DAUTHE_BHYT_MG;
			}
			if (data_ar[0].HIS_DICHVU_CO_DK != '0') {
				isDvDieuKien = true;
			}
			if (data_ar[0].GIA_XANG != '0') {
				isGiaxang = data_ar[0].GIA_XANG;
			}
			if (data_ar[0].HIS_GROUP_DICHVU_CHENHLECH != '0') {
				$('#changeBHYT').remove();
				$('#changeBHYTYC').remove();
				$('#changeBHYTYC').remove();
				$('#changeYC').remove();
			}
			if (data_ar[0].HIS_TINH_LITX == '1') {
				isTinhXang = true;
			}
			if (data_ar[0].HIS_CAR_VC == '1' && opt._loaidichvu == '14') {
				$('#divDANHSACHXE').show();
				isCar = true;
			}
			//Begin_HaNv_06072020: Cảnh báo vận chuyển theo mức hưởng thẻ BH - L2PT-22942
			if (data_ar[0].CDDV_CB_VANCHUYEN_MUCHUONG == '1' && $('#hidDOITUONGBENHNHANID').val() == '1' && opt._loaidichvu == '14') {
				cbVanChuyenMucHuong = true;
			}
			//End_HaNv_06072020
			if (data_ar[0].HIS_MAU_CLS_VD == '1') {
				isMauCls = true;
			} else {
				$('#changeTempData').remove();
				$('#changeNcData').remove();
			}
			if (data_ar[0].HIS_KIEU_IN_MAU_CLS_VD != '0') {
				isMauPrint = data_ar[0].HIS_KIEU_IN_MAU_CLS_VD;
			}
			if (data_ar[0].HIS_CHON_PDT_ICD == '1') {
				isIcdPd = true;
			}
			if (data_ar[0].HIS_GIUONG_DICHVU == '1') {
				isGiuongdv = true;
			}
			if (data_ar[0].HIS_CHON_PDT == '1' && $('#hidLOAITIEPNHANID').val() != 1) {
				isCPdt = true;
				if (typeof opt._chidinhdichvu == "undefined") {
					$("#divPhieuDT").addClass("required");
				}
			} else {
				$("#cboMAUBENHPHAMID").css("width", "100%");
			}
			if (data_ar[0].PHONG_TUDONG_IN != '' && data_ar[0].PHONG_TUDONG_IN != '0') {
				isAutoP = data_ar[0].PHONG_TUDONG_IN;
			}
			if (data_ar[0].HIS_CHAN_MAGIUONG == '1') {
				isChanMg = true;
			}
			if (data_ar[0].HIS_KELE_DICHVU_CLS == '1') {
				isKeleCls = true;
			}
			if (data_ar[0].HIS_CHAN_NGAY_DV == '1') {
				isNgaydv = true;
			}
			if (data_ar[0].HIS_PCD_PTH == '1') {
				isPhongcd = true;
			}
			if (data_ar[0].HIS_NHAPGIUONG_TP != '0') {
				isNhapGiuongTp = true;
			}
			if (data_ar[0].HIS_FILEEXPORT_TYPE != '-1') {
				_typePrint = data_ar[0].HIS_FILEEXPORT_TYPE;
			}
			if (data_ar[0].HIS_BO_CHECK_TRUNGDV != '0') {
				isUncheckDuplicate = true;
			}
			if (data_ar[0].HIS_HIEN_THI_MAGIUONG != '0') {
				isHienThiMaGiuong = true;
			}
			if (data_ar[0].HIS_LAYBENHPHAM != '0') {
				isLayBenhPham = true;
			}
			if (data_ar[0].HIS_NHOMDV_KHONGTP != '0') {
				isNhomDvKtp = data_ar[0].HIS_NHOMDV_KHONGTP;
			}
			if (data_ar[0].HIS_CHAN_DICHVU_QUANGAY != '0') {
				isCbQuaTg = true;
			}
			if (data_ar[0].HIS_CANHBAO_DICHVU_NBH != '0') {
				isCbNbh = true;
			}
			if (data_ar[0].HIS_EDIT_SOLUONG_GIUONG != '1') {
				noEdit = true;
			}
			if (data_ar[0].HIS_IN_PHIEU_XET_NGIEM_CHUNG == '1') {
				printPrivare = false;
			}
			if (data_ar[0].HIS_HEN_XET_NGHIEM == '1') {
				book = true;
			}
			if (data_ar[0].HIS_CLS_BANGHI != '0') {
				record = parseInt(data_ar[0].HIS_CLS_BANGHI);
			}
			if (data_ar[0].HIS_PRINT_CLS_CHUNG != '0') {
				printAll = parseInt(data_ar[0].HIS_PRINT_CLS_CHUNG);
			}
			// DatLT---------Start modify 21/06/2017---------
			if (data_ar[0].HIS_CHIDINH_DICHVU == '1') {
				existConfig = true;
			}
			// DatLT---------End modify 21/06/2017---------
			if (data_ar[0].HIS_DUNGTUYEN_VC == '1') {
				isTranspost = true;
			}
			if (data_ar[0].HIS_COLSPAN_PARENT == '1') {
				isColspan = true;
			}
			if (data_ar[0].HIS_SWAP_COLUMN_DV == '1') {
				isSwapColumn = true;
			}
			if (data_ar[0].HIS_DISPLAY_CDDV_COLUMN_TT37 == '1') {
				isDisplayColumnTt37 = true;
			}
			if (data_ar[0].HIS_NHOMDV_CHIDINH == '1') {
				isGroupService = true;
			}
			if (data_ar[0].HIS_PL_DICHVU == '1') {
				plDv = '1';
				$('#divAllService').show();
			} else {
				$('#divAllService').hide();
			}
			if (data_ar[0].HIS_EDIT_SL_DICHVU == '1') {
				isNotEdit = true;
			}
			if (data_ar[0].HIS_TIM_KIEM_GAN_DUNG == '1') {
				isSearchLike = true;
			}
			if (data_ar[0].HIS_CK_COL_HIDE == '1') {
				hideColCk = true;
			}
			if (data_ar[0].HIS_DIEUTRI_THEO_PHACDO == '1') {
				$('#btnPhacDo').show();
			} else {
				$('#btnPhacDo').hide();
			}
			if (data_ar[0].HIS_CANHBAO_PHACDO == '1') {
				isCbPd = true;
			}
			if (data_ar[0].HIS_EDIT_PTTT == '1') {
				isEditPttt = true;
			}
			//Begin_HaNv_26102019: Them cboBacsi ở ngoại trú (sắp xếp lại cấu hình hiển thị cboBacSi) - L2PT-10177
			if ($('#hidLOAITIEPNHANID').val() == 0 && typeof opt._chidinhdichvu == "undefined" && data_ar[0].HIS_BACSI_KE == '1') {
				isBacsiKe = true;
				//Begin_HaNv_13112019: Cho phép tìm kiếm bác sỹ trong danh sách thay vì chọn - L2PT-11245
				if (data_ar[0].CDDV_BACSI_SEARCH == '1') {
					isBacsiSearch = true;
					$('#divLbBacsi').remove();
					$('#divTxBacsi').remove();
					$('#divTkBacSi').show();
				} else {
					$('#divTkBacSi').remove();
				}
				//End_HaNv_13112019
			} else if ($('#hidLOAITIEPNHANID').val() == 1 && typeof opt._chidinhdichvu == "undefined" && data_ar[0].HIS_BACSI_KE_NGT != '0') {
				//Begin_HaNv_18122019: check user đăng nhập là bác sĩ thì không hiển thị danh sách bác sĩ - L2PT-13258
				var officerType = 0;
				if (data_ar[0].CDDV_CHECK_USERTYPE == '1' && opt.user_id != '') {
					officerType = jsonrpc.AjaxJson.getOneValue("GET_OFFICER_TYPE", [ {
						"name" : "[0]",
						"value" : opt.user_id
					} ]);
				}
				if (officerType != 4) {
					isBacsiKe = true;
					//Begin_HaNv_13112019: Cho phép tìm kiếm bác sỹ trong danh sách thay vì chọn - L2PT-11245
					if (data_ar[0].CDDV_BACSI_SEARCH == '1') {
						isBacsiSearch = true;
						$('#divLbBacsi').remove();
						$('#divTxBacsi').remove();
						$('#divTkBacSi').show();
						if (data_ar[0].HIS_BACSI_KE_NGT != '2') {
							$('#divLbTkBacsi').removeClass('required');
						}
					} else {
						$('#divTkBacSi').remove();
						if (data_ar[0].HIS_BACSI_KE_NGT != '2') {
							$('#divLbBacsi').removeClass('required');
						}
					}
					//End_HaNv_13112019
				} else {
					$('#divLbBacsi').hide();
					$('#divTxBacsi').hide();
				}
				//End_HaNv_18122019
			} else if (opt._chidinhdichvu == '1' && opt._loaiphieumbp == '3' && data_ar[0].HIS_BACSI_KE_PCK == '1') {
				isBacsiKe = true;
			}
			//Begin_HaNv_28062019: Them cboBacsi khi tạo phiếu ngày giường, phiếu vận chuyển L2PT-6222 và L2PT-6208
			else if (opt._chidinhdichvu == '1' && opt._loaiphieumbp == '12' && data_ar[0].HIS_BACSI_KE_PNG != '0') {
				isBacsiKe = true;
				if (data_ar[0].HIS_BACSI_KE_PNG != '2') {
					$('#divLbBacsi').removeClass('required');
				}
			} else if (opt._chidinhdichvu == '1' && opt._loaiphieumbp == '16' && data_ar[0].HIS_BACSI_KE_PVC != '0') {
				isBacsiKe = true;
				if (data_ar[0].HIS_BACSI_KE_PVC != '2') {
					$('#divLbBacsi').removeClass('required');
				}
			}
			//End_HaNv_28062019
			else {
				$('#divLbBacsi').hide();
				$('#divTxBacsi').hide();
			}
			if (isBacsiKe) {
				if (isBacsiSearch) {
					var _colUser = "USER_ID,USER_ID,0,0,t,l;Tài khoản,USER_NAME,20,0,f,l;Tên bác sỹ,OFFICER_NAME,30,0,f,l;Chức danh/Khoa phòng,CHUCDANH,50,0,f,l";
					ComboUtil.initComboGrid("txtTKBACSI", "CDDV.BACSI_SEARCH", [ {
						"name" : "[0]",
						"value" : opt._depId
					} ], "600px", _colUser, function(event, ui) {
						$("#txtTKBACSI").val("");
						var option = $('<option value="' + ui.item.USER_ID + '">' + ui.item.OFFICER_NAME + '</option>');
						$("#cboBACSIID").empty();
						$("#cboBACSIID").append(option);
						return false;
					});
				} else {
					//Begin_HaNv_12102020: Thiết lập hiển thị bác sĩ toàn viện theo loainhommaubenhpham - L2PT-28284
					if (data_ar[0].CDDV_BACSI_TOANVIEN_LNMBP != '0') {
						var bsByLnmbp = data_ar[0].CDDV_BACSI_TOANVIEN_LNMBP;
						if (bsByLnmbp.indexOf(opt._loaiphieumbp + ";") > -1) {
							ComboUtil.getComboTag("cboBACSIID", "NGT02K016.EV002.TV", [], "", {
								value : '',
								text : 'Chọn'
							}, "sql");
						} else {
							ComboUtil.getComboTag("cboBACSIID", "NGT02K016.EV002", [ {
								"name" : "[0]",
								"value" : opt._depId
							} ], "", {
								value : '',
								text : 'Chọn'
							}, "sql");
						}
					} else {
						ComboUtil.getComboTag("cboBACSIID", "NGT02K016.EV002", [ {
							"name" : "[0]",
							"value" : opt._depId
						} ], "", {
							value : '',
							text : 'Chọn'
						}, "sql");
					}
				}
			}
			//End_HaNv_26102019
			if (data_ar[0].HIS_CHIDINHCHUNG == '1') {
				isKeChung = true;
				$('#liXetNghiem a')[0].innerHTML = 'Dịch vụ CLS';
				$('#liCDHA').hide();
				$('#liChuyenKhoa').hide();
			}
			if (data_ar[0].HIS_SEARCH_NHOM_CK == '1') {
				ComboUtil.getComboTag("cboCHUYENKHOAID", "DMC.CBNDV.01", [ {
					"name" : "[0]",
					"value" : 5
				} ], "", {
					text : "--- Tất cả ---",
					value : -1
				}, 'sql', '');
				ComboUtil.initComboGrid("txtCHUYENKHOA", "DMC.CBNDV.02", [ {
					"name" : "[0]",
					"value" : 5
				} ], "600px", _colGroup, function(event, ui) {
					$("#cboCHUYENKHOAID").val(ui.item.DICHVUID);
					$('#cboCHUYENKHOAID').change();
					return;
				});
			} else {
				ComboUtil.getComboTag("cboCHUYENKHOAID", "DMC.CHUYENKHOA.01", [], "", {
					text : "--- Tất cả ---",
					value : -1
				}, 'sql', '');
				var _colChuyenKhoa = "Mã chuyên khoa,CHUYENKHOAID,30,0,f,l;Tên chuyên khoa,TENCHUYENKHOA,70,0,f,l";
				ComboUtil.initComboGrid("txtCHUYENKHOA", "DMC.CHUYENKHOA.02", [], "600px", _colChuyenKhoa, function(event, ui) {
					$("#cboCHUYENKHOAID").val(ui.item.CHUYENKHOAID);
					$('#cboCHUYENKHOAID').change();
					return;
				});
			}
			if (data_ar[0].HIS_KEGIUONG_THEONGAY == '1') {
				isKeGiuongTheoNgay = true;
			}
			if (data_ar[0].HIS_SOLUONG_DICHVU == '1') {
				if (opt._loaidichvu == '13') {
					$('#divLBSONGAY').hide();
					$('#divTXSONGAY').hide();
					$('#divLBSOLUONGDICHVU').show();
					$('#divTXSOLUONGDICHVU').show();
					$('#txtSOLUONGDICHVU').val(1);
					isShowNumService = true;
				}
			}
			if (data_ar[0].NGT_GHICHU_BENHCHINH == '1') {
				$("#divBc").removeClass("col-md-9");
				$("#divBc").addClass("col-md-7");
				$('#divSuaBc').css('display', '');
			}
			//Begin_HaNv_07052019: hien thi chandoanbandau tu khambenh - L1PT-719
			if (data_ar[0].BM2_CHANDOANBANDAU == '1') {
				reqChandoanbandau = true;
				$('#txtGHICHU_BENHCHINH').attr("disabled", "disabled");
			}
			//End_HaNv_07052019
			if (data_ar[0].HIS_NHAP_CHUANDOAN != '1') {
				$('#txtTKCHANDOAN').attr("disabled", "disabled");
				$('#cboMACHANDOAN').attr("disabled", "disabled");
				$('#txtTKCHANDOANKT').attr("disabled", "disabled");
				$('#btnCLEARCHANDOANKT').attr("disabled", "disabled");
			}
			if (data_ar[0].HIS_TACH_CDHA == '1') {
				isTachCdha = true;
			}
			if ($('#hidLOAITIEPNHANID').val() == '0') {
				//Begin_HaNv_26122018: show canh bao vuot tien cho BN điều trị nội trú theo cau hinh
				if (data_ar[0].SHOWMSG_VUOTTIEN == '1' && $('#hidDOITUONGBENHNHANID').val() != '1') {
					//Cấu hình cảnh báo vượt tiền tạm ứng với BN viện phí/ dịch vụ
					isShowMsgVuotTien = true;
				} else if (data_ar[0].SHOWMSG_VUOTTIEN == '2') {
					//Cấu hình cảnh báo vượt tiền tạm ứng với tất cả các loại đối tượng
					isShowMsgVuotTien = true;
				}
				//End_HaNv_26122018
				//Begin_HaNv_10042019: Chan cung vươt tien tam ung-ap dung cho BN dieu tri noi tru - L2PT-3935
				if (data_ar[0].CHANCUNG_VUOTTIEN == '1' && $('#hidDOITUONGBENHNHANID').val() != '1') {
					//Cấu hình chặn cứng vượt tiền tạm ứng với BN viện phí/ dịch vụ
					isChancbTu = true;
				} else if (data_ar[0].CHANCUNG_VUOTTIEN == '2') {
					//Cấu hình chặn cứng vượt tiền tạm ứng với tất cả các loại đối tượng
					isChancbTu = true;
				}
				//End_HaNv_10042019
			}
			if ($('#hidLOAITIEPNHANID').val() == '1') {
				//Begin_HaNv_23082018: show canh bao vuot tien cho BN kham benh theo cau hinh
				if (data_ar[0].SHOWMSG_VUOTTIEN_KB == '1' && $('#hidDOITUONGBENHNHANID').val() != '1') {
					//Cấu hình cảnh báo vượt tiền tạm ứng với BN viện phí/ dịch vụ
					isShowMsgVuotTienKB = true;
				} else if (data_ar[0].SHOWMSG_VUOTTIEN_KB == '2') {
					//Cấu hình cảnh báo vượt tiền tạm ứng với tất cả các loại đối tượng
					isShowMsgVuotTienKB = true;
				}
				//End_HaNv_23082018
				//Begin_HaNv_23102018: Chan cung vươt tien tam ung-ap dung cho BN kham benh - L2HOTRO-10404
				if (data_ar[0].CHANCUNG_VUOTTIEN_KB == '1' && $('#hidDOITUONGBENHNHANID').val() != '1') {
					//Cấu hình chặn cứng vượt tiền tạm ứng với BN viện phí/ dịch vụ
					isChancbTu = true;
				} else if (data_ar[0].CHANCUNG_VUOTTIEN_KB == '2') {
					//Cấu hình chặn cứng vượt tiền tạm ứng với tất cả các loại đối tượng
					isChancbTu = true;
				}
				//End_HaNv_23102018
			}
			if (data_ar[0].CHAN_NGAYDV_CHOPHEP_VP == '1') {
				isNgaydvVienphi = true;
			}
			//Begin_HaNv_21062018: Danh dau thuc hien dich vu vi sinh (phat hien hoac nuoi cay) - L2K74TW-582
			if (data_ar[0].HIS_DS_NHOMDV_VISINH != null && data_ar[0].HIS_DS_NHOMDV_VISINH != '') {
				isNhomDvViSinh = true;
			}
			//End_HaNv_21062018
			//Begin_HaNv_29012019: Tinh trang PTTT (binh thuong hoac cap cuu) - L2PT-1567
			if (data_ar[0].SHOWCOL_TINHTRANG_PTTT == '1') {
				showTinhTrangPttt = true;
			}
			//End_HaNv_21062018
			//Begin_HaNv_26062018: Thêm số lượng dịch vụ khi chỉ định - HISL2TK-634
			if (data_ar[0].HIS_CDDV_SHOW_SOLUONG_CLS == '1') {
				if (opt._loaidichvu != '13') {
					$('#divSOLUONGDICHVUCLS').show();
					$('#txtSOLUONGDICHVUCLS').val(1);
					isShowSoLuongCls = true;
				}
			}
			//End_HaNv_26062018
			//Begin_HaNv_14102019: Thêm số ngày khi chỉ định dịch vụ thu khác - L2PT-9589
			if (data_ar[0].CDDV_SHOW_SONGAY_THUKHAC == '1') {
				if (opt._loaidichvu == '1') {
					$('#divThongTinGiuong').show();
					$('#divLBTTDIEUTRI').remove();
					$('#divTTDIEUTRI').remove();
					$('#divLBSOLUONGDICHVU').remove();
					$('#divTXSOLUONGDICHVU').remove();
					$('#divLBSONGAY').removeClass("col-md-1");
					$('#divLBSONGAY').addClass("col-md-2");
					$('#divTXSONGAY').removeClass("col-md-1");
					$('#divTXSONGAY').addClass("col-md-2");
					$('#txtSONGAYGIUONG').val(1);
				}
			}
			//End_HaNv_14102019
			//Begin_HaNv_15062020: Thêm số ngày khi chỉ định công khám - L2PT-22318
			if (data_ar[0].CDDV_SHOW_SONGAY_CONGKHAM == '1' && opt._loaidichvu == '2') {
				$('#divThongTinGiuong').show();
				$('#divLBTTDIEUTRI').remove();
				$('#divTTDIEUTRI').remove();
				$('#divLBSOLUONGDICHVU').remove();
				$('#divTXSOLUONGDICHVU').remove();
				$('#divLBSONGAY').removeClass("col-md-1");
				$('#divLBSONGAY').addClass("col-md-2");
				$("#divLBSONGAY").html("Số lượng phiếu");
				$('#divTXSONGAY').removeClass("col-md-1");
				$('#divTXSONGAY').addClass("col-md-2");
				$('#txtSONGAYGIUONG').val(1);
			}
			//End_HaNv_15062020
			//Begin_HaNv_05072018: Thời gian kết thúc giường khi kê giường
			if (opt._loaidichvu == '13') {
				if (data_ar[0].HIS_CDDV_TGKETTHUC_GIUONG == '1' && data_ar[0].HIS_SOLUONG_DICHVU != '0') {
					$('#divTGKetThucGiuong').show();
					var _sys_date = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
					$("#txtTGKETTHUC").val(_sys_date + " 23:59:59");
					showTgKetThucGiuong = true;
				} else {
					$('#divTGKetThucGiuong').hide();
				}
			} else {
				$('#divTGKetThucGiuong').hide();
			}
			//End_HaNv_05072018
			//Begin_HaNv_06072018: Cau hinh de tach phieu theo doi tuong dịch vụ: BHYT va thu phi - L2DKBD-1342
			if (data_ar[0].CDDV_TACHPHIEU_THEO_LOAIDT == '1') {
				isTachPhieuTheoLoaiDT = true;
			}
			//End_HaNv_06072018
			//Begin_HaNv_26112018: Ẩn btnLuu theo cấu hình (BM2) L2HOTRO-12488
			if (data_ar[0].CDDV_HIDE_BTNLUU == '1') {
				$('#btnLuu').hide();
			} else {
				$('#btnLuu').show();
			}
			//End_HaNv_26112018
			//Begin_HaNv_05012019: Không hiển thị thông báo khi lưu chỉ định thành công - L2PT-487
			if (data_ar[0].NTU_SAVEOK_NOTMSG == '1') {
				isSaveNotMsg = true;
			}
			//End_HaNv_05012019
			//Begin_HaNv_10072019: Canh bao khi chi dinh dv xet nghiem so luong > 1 - L2PT-6748
			if (data_ar[0].CDDV_CB_SOLUONG_XN == '1') {
				cbSoLuongXN = true;
			}
			//End_HaNv_10072019
			//Begin_HaNv_24072019: Hien thi cot gia chenh lech - L2PT-7281
			if (data_ar[0].CDDV_DISPLAY_COL_GIACHENH == '1') {
				isDisplayColGiaChenh = true;
			}
			//End_HaNv_24072019
			//Begin_HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
			if (data_ar[0].CDDV_USING_REPORT_CODE == '1') {
				usingReportCode = true;
			}
			//End_HaNv_05082019
			//Begin_HaNv_12082019: gioi han thuc hien dichvu CLS theo ICD trong 1 dot dieu tri - L2PT-7077
			if (data_ar[0].CDDV_CB_ICD_DV == '1') {
				checkDichVuByICD = 1; //Canh bao
			} else if (data_ar[0].CDDV_CB_ICD_DV == '2') {
				checkDichVuByICD = 2; //Chan chi dinh
			}
			//End_HaNv_12082019
			//Begin_HaNv_12082019: Check dichvuid khong cung chi dinh chung voi nhau - L2PT-7660
			//Begin_HaNv_28112019: Chỉ chan voi benh nhan khong phai BHYT - L2PT-12047
			if ($('#hidDOITUONGBENHNHANID').val() == '1') {
				if (data_ar[0].CDDV_CB_DV_KHONGCUNGCD == '1') {
					checkKhongCungCd = 1; //Canh bao
				} else if (data_ar[0].CDDV_CB_DV_KHONGCUNGCD == '2') {
					checkKhongCungCd = 2; //Chan chi dinh
				}
			}
			//End_HaNv_28112019
			//End_HaNv_12082019
			//Begin_HaNv_10102019: Gộp chung 1 số phiếu với các DV xét nghiệm khác nhóm - L2PT-9671
			if (data_ar[0].CDDV_GOMNHOM_XN == '1') {
				isGomNhomXn = true;
			}
			//End_HaNv_10102019
			//Begin_HaNv_22102019: Cho phép nhập ngày giường của BN số thập phân lẻ đến 0.25 - L1PT-1936
			if (data_ar[0].HIS_KEGIUONG_VD2 == '1') {
				isKeGiuongVd2 = true;
			}
			//End_HaNv_22102019
			//Begin_HaNv_25102019: Mặc định tiền vận chuyển là thu phí - L2PT-10145
			if (opt._loaidichvu == '14' && data_ar[0].VANCHUYEN_MACDINH_THUPHI == '1') {
				opt._doituongbenhnhanId = '2';
				$('#hidDOITUONGBENHNHANID').val('2');
			}
			//End_HaNv_22102019
			//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
			if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
				$('#divThongTinCongKham').show();
			} else {
				$('#divThongTinCongKham').hide();
			}
			//End_HaNv_01012020
			//Begin_HaNv_09012020: Không tính giá chênh cho BN viện phí - L2PT-14904
			if (data_ar[0].CDDV_KO_GIACHENH_VP == '1') {
				notGiaChenhVp = true;
			}
			//End_HaNv_09012020
			//Begin_HaNv_25052020: Thêm btn xem lịch sử BA - L2PT-21525
			if (data_ar[0].CDDV_SHOW_LICHSU_BENHAN == '1') {
				$('#btnLichSuBA').show();
			} else {
				$('#btnLichSuBA').hide();
			}
			//End_HaNv_25052020
			//Begin_HaNv_15082020: cảnh báo chỉ định vượt tiền mức trần BHYT đã cấu hình - L2PT-25620
			if (data_ar[0].HIS_SOTIEN_MUCTRAN_BHYT != '0') {
				tienMucTranBh = parseFloat(data_ar[0].HIS_SOTIEN_MUCTRAN_BHYT);
			}
			//End_HaNv_15082020
			//Begin_HaNv_05102020: Ràng buộc chỉ định dịch vụ khi tờ điều trị kí số - L2PT-28416
			if (data_ar[0].HIS_CHECK_DIEUTRI_KISO == '1') {
				checkKiSoPDT = true;
			}
			//End_HaNv_05102020
			//Begin_HaNv_03112020: Cấu hình ẩn contextMenu chuyển giá đối tượng dịch vụ - L2PT-30124
			if (data_ar[0].CDDV_HIDE_CHUYENDOITUONG_DV != '0') {
				//$('#changeBHYT').remove();
				$('#changeBHYTYC').remove();
				$('#changeKSK').remove();
				//$('#changeVP').remove();
				$('#changeVPYC').remove();
				$('#changeYC').remove();
				$('#changeMP').remove();
				$('#changeTempData').remove();
				$('#changeNcData').remove();
				$('#changeDieuKien').remove();
				$('#changeHopDong').remove();
			}
			//End_HaNv_03112020
		}
		if (!isKeChung) {
			ComboUtil.getComboTag("cboNHOMXETNGHIEMID", "DMC.CBNDV.01", [ {
				"name" : "[0]",
				"value" : 3
			} ], "", {
				text : "--- Tất cả ---",
				value : -1
			}, 'sql', '');
			ComboUtil.getComboTag("cboNHOMCDHAID", "DMC.CBNDV.01", [ {
				"name" : "[0]",
				"value" : 4
			} ], "", {
				text : "--- Tất cả ---",
				value : -1
			}, 'sql', '');
			ComboUtil.initComboGrid("txtNHOMXETNGHIEM", "NGT02K016.EV006", [ {
				"name" : "[0]",
				"value" : 3
			} ], "600px", _colGroup, function(event, ui) {
				$("#cboNHOMXETNGHIEMID").val(ui.item.DICHVUID);
				$('#cboNHOMXETNGHIEMID').change();
				return;
			});
			ComboUtil.initComboGrid("txtNHOMCDHA", "NGT02K016.EV006", [ {
				"name" : "[0]",
				"value" : 4
			} ], "600px", _colGroup, function(event, ui) {
				$("#cboNHOMCDHAID").val(ui.item.DICHVUID);
				$('#cboNHOMCDHAID').change();
				return;
			});
		} else {
			ComboUtil.getComboTag("cboNHOMXETNGHIEMID", "NGT02K016.EV004", [], "", {
				text : "--- Tất cả ---",
				value : -1
			}, 'sql', '');
			ComboUtil.initComboGrid("txtNHOMXETNGHIEM", "NGT02K016.EV005", [], "600px", _colGroup, function(event, ui) {
				$("#cboNHOMXETNGHIEMID").val(ui.item.DICHVUID);
				$('#cboNHOMXETNGHIEMID').change();
				return;
			});
		}
		// cau hinh cho bac si
		data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH_ND", 'HIS_HEN_XET_NGHIEM;HIS_CANH_BAO_TRAN;NGT_PHIEUKHAM_TUDONGIN;HIS_BACSI_KE_THEOKHOA');
		if (data_ar != null && data_ar.length > 0) {
			if (data_ar[0].HIS_HEN_XET_NGHIEM == '1') {
				bookAll = true;
			}
			if (data_ar[0].HIS_CANH_BAO_TRAN == '1') {
				isShowMsgWr = true;
			}
			if (data_ar[0].NGT_PHIEUKHAM_TUDONGIN == '1') {
				isAutoPrint = true;
			}
			if (data_ar[0].HIS_BACSI_KE_THEOKHOA == '1' && opt.modekhoa == '1') {
				$('#divKhoaBacSi').show();
				$('#divLbBacsi').remove();
				$('#divTxBacsi').remove();
				$('#divTkBacSi').remove();
				isKhoabs = true;
				ComboUtil.getComboTag("cboKHOACHIDINHCHID", 'NTU02D037.EV001', [], '', {
					text : "---Chọn---",
					value : ''
				}, 'sql');
			} else {
				$('#divKhoaBacSi').hide();
			}
		}
		ComboUtil.initComboGrid("txtTKCHANDOAN", _sql, [], "600px", _col, function(event, ui) {
			var _ui = ui.item;
			var str = $("#txtCHANDOAN_KT").val();
			if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
				DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
				return false;
			}
			$("#cboMACHANDOAN").empty();
			$("#cboMACHANDOAN").append('<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10NAME + '</option>');
			if (isIcdPd) {
				var myVar = {
					machandoan : $('#cboMACHANDOAN').val(),
					loaidv : 0
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgPhacDoMau", "divPhieuMauCDDV", "manager.jsp?func=../noitru/NTU02D075_PhacDoMau", myVar, "Phác đồ mẫu", 1200, 550);
				DlgUtil.open("dlgPhacDoMau");
			}
		});
		ComboUtil.initComboGrid("txtTKCHANDOANKT", "NT.008", [], "600px", _colICD, function(event, ui) {
			var _ui = ui.item;
			var str = $("#txtCHANDOAN_KT").val();
			if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
				DlgUtil.showMsg("Bệnh kèm theo trùng");
				return false;
			}
			if (($("#cboMACHANDOAN").val() + "-").indexOf(_ui.ICD10CODE + "-") > -1) {
				DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
				return false;
			}
			$('#txtCHANDOAN_KT').val($("#txtCHANDOAN_KT").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtCHANDOAN_KT").val() + ";" + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
		});
		//Begin_HaNv_09042018: chinh lai cach lay subdept, dept -> khong lay tu session
		if (typeof (opt._subdeptId) != "undefined" && opt._subdeptId != "-1") {
			var dataDept = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D009.EV0011", RSUtil.buildParam("", [ opt._subdeptId ]));
			var dataDept_ar = $.parseJSON(dataDept);
			if (dataDept_ar != null && dataDept_ar.length > 0) {
				opt._depId = dataDept_ar[0].KHOAID;
				opt._subDeptName = dataDept_ar[0].PHONGNAME;
			}
		}
		//End_HaNv_09042018
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.LAYDL", this.opt._khambenhId + "$" + opt._subdeptId);
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			FormUtil.setObjectToForm("", "", row);
			//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
			if (typeof (row.HOPDONGID) != "undefined" && typeof (row.LOAIKHAM) != "undefined" && row.HOPDONGID != "0" && row.LOAIKHAM != "0") {
				if (row.LOAIKHAM == "2") {
					//Chỉ cho phép chuyển HĐ với loaikham = 2 (chăm sóc sức khỏe cán bộ)
					$('#changeHopDong').show();
				} else {
					$('#changeHopDong').remove();
				}
			}
			//End_HaNv_25072020
			//Begin_HaNv_17102020: Mặc định thời gian kê tiền giường là ngày nhập khoa - L2PT-28913
			if (tgkegiuong_nhapkhoa && row.NGAYVAOKHOA != '') {
				$('#txtTGCHIDINH').val(row.NGAYVAOKHOA);
			}
			//End_HaNv_17102020
			icdchinh = row.TKCHANDOAN_NEW;
			icdphu = row.CHANDOAN_KT_NEW;
			textchinh = row.CHANDOAN_NEW;
			isQn = row.SUB_DTBNID;
			isHtvv = row.HINHTHUCVAOVIENID;
			//Begin_HaNv_24082018: tinh lai tien VP khi load trang
			var vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K016.VPCD", opt._tiepnhanId);
			// var vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.06",opt._tiepnhanId);
			var tamung = 0;
			var miengiam = 0;
			var danop = 0;
			if (vp_ar != null && vp_ar.length > 0) {
				var data = vp_ar[0];
				tongtienbh = data.TONGTIENBH;
				$('#lblTAMUNG').html(formatNumber(data.TAMUNG) + "đ");
				$('#lblMIENGIAM').html(formatNumber(data.MIENGIAM) + "đ");
				$('#lblDANOP').html(formatNumber(data.DANOP) + "đ");
				tamung = data.TAMUNG;
				miengiam = data.MIENGIAM;
				danop = data.DANOP;
				$('#lblTONGCHIPHI').html(formatNumber(data.TONGTIENDV) + "đ");
				$('#lblBHTRA').html(formatNumber(data.BHYT_THANHTOAN) + "đ");
				$('#lblBNTRA').html(formatNumber((parseFloat(data.TONGTIENDV) - parseFloat(data.BHYT_THANHTOAN)).toFixed(2)) + "đ");
				tongbh = parseFloat(data.TONGTIENDV_BH);
				var nopthem = (parseFloat(tamung) - parseFloat(data.TONGTIENDV) + parseFloat(data.BHYT_THANHTOAN) + parseFloat(miengiam) + parseFloat(danop)).toFixed(2);
				if (parseFloat(nopthem) <= 0) {
					flagMsgMoney = true;
					$('#lblCHENHLECH').html(formatNumber(nopthem) + "đ");
					$('#lblNOPTHEM').html(formatNumber(-1 * nopthem) + "đ");
				} else {
					$('#lblCHENHLECH').html(formatNumber(nopthem) + "đ");
					$('#lblNOPTHEM').html(0 + "đ");
				}
				$('#hidQUYENLOI').val(data.TYLE_BHYT);
				$('#hidQUYENLOITHE').val(data.TYLE_THE);
				$('#hidTRAN_BHYT').val(data.TRAN_BHYT);
			}
			//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
			if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
				var sql_par = [];
				sql_par.push({
					"name" : "[0]",
					"value" : opt._hosobenhanId
				});
				var ret = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K016.EV011", sql_par);
				var rows = JSON.parse(ret);
				if (rows != null && rows.length > 0) {
					if (rows[0]["CONGKHAM"].length > 0) {
						$('#txtTHONGTINCONGKHAM').val(rows[0]["CONGKHAM"]);
					}
				}
			}
			//End_HaNv_01012020
		}
		var sql_par = [];
		// danh sach dich vu da chi dinh
		GridUtil.init("grdDSCDOld", "100%", "150", "", false, _grdCDHeaderOld, true);
		$("#grdDSCDOld")[0].toggleToolbar();
		// chi dinh dich vu khac
		if (typeof opt._chidinhdichvu != "undefined") {
			if (isHienThiMaGiuong && opt._loaidichvu == '13') {
				$('#divXepGiuong').show();
				$('#divChanDoanC').hide();
			}
			checkInthukhac = true;
			$('#divChanDoan').removeClass('required');
			if (opt._loaidichvu == '13') {
				$('#divChuanDoanKT').hide();
				$('#divThongTinGiuong').show();
				$('#txtSONGAYGIUONG').val(1);
			}
			sql_par = RSUtil.buildParam("", [ opt._tiepnhanId, opt._loaidichvu ]);
			GridUtil.loadGridBySqlPage("grdDSCDOld", "NTU02D009.EV009", sql_par);
			if (opt._loaidichvu != '13') {
				GridUtil.init("grdDSCD", "100%", "150", "", false, _grdCDHeader_dvk, false);
				$("#grdDSCD")[0].toggleToolbar();
				$("#grdDSCD").jqGrid('hideCol', 'TYLEDV');
			} else {
				GridUtil.init("grdDSCD", "100%", "150", "", false, _grdCDHeader_dvk, false);
				$("#grdDSCD")[0].toggleToolbar();
				if (noEdit) {
					$("#grdDSCD").jqGrid('setColProp', 'SOLUONG', {
						editable : false
					});
				}
			}
			if (typeof opt._maubenhphamId != "undefined") {
				sql_par = RSUtil.buildParam("", [ opt._maubenhphamId, opt._khambenhId ]);
				$('#txtSONGAYGIUONG').attr("disabled", "disabled");
				//START -- phan he dinh duong -- hongdq -- 20180418
				var dataObj;
				if (opt._loaidichvu == '12') {
					GridUtil.loadGridBySqlPage("grdDSCD", "NGT02K016.DSDVCD", sql_par);
					dataObj = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K016.UPD_SA", RSUtil.buildParam("", [ opt._maubenhphamId ]));
				} else {
					GridUtil.loadGridBySqlPage("grdDSCD", "NTU02D009.EV002", sql_par);
					dataObj = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D009.EV004", RSUtil.buildParam("", [ opt._maubenhphamId ]));
				}
				//END -- phan he dinh duong -- hongdq -- 20180418
				//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
				if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
					$('#cboTYLEDV_CK').prop("disabled", true);
				}
				//End_HaNv_01012020
				var data_ar = $.parseJSON(dataObj);
				if (data_ar != null && data_ar.length > 0) {
					data = data_ar[0];
					FormUtil.setObjectToForm("", "", data);
					if (isCar && opt._loaidichvu == '14') {
						ComboUtil.getComboTag("cboXEID", "NGT02K016.EV007", [], data.MAPXEID, {
							text : "--- Chọn ---",
							value : ''
						}, 'sql', '');
					}
					if (isBacsiKe) {
						//Begin_HaNv_13112019: Cho phép tìm kiếm bác sỹ trong danh sách thay vì chọn - L2PT-11245
						if (data.BACSYDIEUTRIID != null && data.BACSYDIEUTRIID != '') {
							cboBacsiVal = data.BACSYDIEUTRIID;
						}
						//End_HaNv_13112019
					}
					sql_par = RSUtil.buildParam("", [ this.opt._khambenhId, "4" ]);
					if (loaigiuongid == '') {
						loaigiuongid = data.LOAIGIUONGID;
					}
					if (magiuong == '') {
						magiuong = data.MAGIUONG;
					}
					ComboUtil.getComboTag("cboMAUBENHPHAMID", "COM.PHIEUDIEUTRI", sql_par, data.PHIEUDIEUTRIID == null ? '' : data.PHIEUDIEUTRIID, {
						value : '',
						text : 'Chưa có phiếu điều trị'
					}, 'sql', '', function() {
						//Begin_HaNv_05102020: Ràng buộc chỉ định dịch vụ khi tờ điều trị kí số - L2PT-28416
						if (checkKiSoPDT) {
							var checkKiSo = jsonrpc.AjaxJson.ajaxCALL_SP_I("PDT.CHECK_KISO", data.PHIEUDIEUTRIID);
							if (checkKiSo > 0) {
								DlgUtil.showMsg('Không thể cập nhật thông tin phiếu được gắn với phiếu điều trị đã được kí số!');
								$('#cboMAUBENHPHAMID').val('');
								return;
							}
						}
						//End_HaNv_05102020
						if (data.PHIEUDIEUTRIID != null && data.PHIEUDIEUTRIID != '') {
							var dataObj = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV008", data.PHIEUDIEUTRIID);
							if (dataObj != null && dataObj.length > 0) {
								var dataICd = dataObj[0];
								FormUtil.setObjectToForm("", "", dataICd);
							}
						} else {
							if (data.MACHANDOAN != null && data.MACHANDOAN != '') {
								$('#txtTKCHANDOAN').combogrid("setValue", data.MACHANDOAN);
							}
							if (data.CHANDOAN_KEMTHEO != null && data.CHANDOAN_KEMTHEO != '') {
								$('#txtCHANDOAN_KT').val(data.CHANDOAN_KEMTHEO);
							}
						}
					});
					//Begin_HaNv_07082019: load dữ liệu chkKhan khi cập nhật dv CLS - L2PT-7335
					if (data.LOAIMAUBENHPHAM == '2') {
						$('#chkKHAN').attr('checked', true);
					} else {
						$('#chkKHAN').attr('checked', false);
					}
					//End_HaNv_31072019
				} else {
					DlgUtil.showMsg('Không tìm thấy mẫu bệnh phẩm');
					return;
				}
			} else {
				if (isCar && opt._loaidichvu == '14') {
					ComboUtil.getComboTag("cboXEID", "NGT02K016.EV007", [], '', {
						text : "--- Chọn ---",
						value : ''
					}, 'sql', '');
				}
				if (isBacsiKe) {
					//Begin_HaNv_13112019: Cho phép tìm kiếm bác sỹ trong danh sách thay vì chọn - L2PT-11245
					if (opt.bacsike != null && opt.bacsike != '') {
						cboBacsiVal = opt.bacsike;
					}
					//End_HaNv_13112019
				}
				sql_par = RSUtil.buildParam("", [ this.opt._khambenhId, "4" ]);
				ComboUtil.getComboTag("cboMAUBENHPHAMID", "COM.PHIEUDIEUTRI", sql_par, "", {
					value : '',
					text : 'Chưa có phiếu điều trị'
				}, 'sql', '', function() {
					if (opt._phieudieutriId != '') {
						//Begin_HaNv_05102020: Ràng buộc chỉ định dịch vụ khi tờ điều trị kí số - L2PT-28416
						if (checkKiSoPDT) {
							var checkKiSo = jsonrpc.AjaxJson.ajaxCALL_SP_I("PDT.CHECK_KISO", opt._phieudieutriId);
							if (checkKiSo > 0) {
								DlgUtil.showMsg('Không thể cập nhật thông tin phiếu được gắn với phiếu điều trị đã được kí số!');
								$('#cboMAUBENHPHAMID').val('');
								return;
							}
						}
						//End_HaNv_05102020
						$('#cboMAUBENHPHAMID').val(opt._phieudieutriId).change();
					} else {
						$('#cboMACHANDOAN').empty();
						$('#cboMACHANDOAN').append('<option value="' + icdchinh + '">' + textchinh + '</option>');
						$('#txtCHANDOAN_KT').val(icdphu);
					}
				});
			}
			var _group = {
				groupField : [ 'TENNHOM' ],
				groupColumnShow : [ false ],
				groupText : [ '<b>{0}</b>' ]
			};
			if (isSwapColumn) {
				GridUtil.initGroup("grdDichVu", "100%", "453", "", true, _group, _gridHeader_NT, false);
			} else {
				GridUtil.initGroup("grdDichVu", "100%", "453", "", true, _group, _gridHeader, false);
			}
			sql_par = RSUtil.buildParam("", [ opt._loaidichvu, opt._khambenhId, $('#txtTGCHIDINH').val() ]);
			GridUtil.loadGridBySqlPage("grdDichVu", "NTU01H003.EV001", sql_par);
			if (!isDisplayColumnTt37) {
				$("#grdDichVu").jqGrid('hideCol', 'TEN_TT37');
			}
			//Begin_HaNv_24072019: Hien thi cot gia chenh lech - L2PT-7281
			if (!isDisplayColGiaChenh) {
				$("#grdDichVu").jqGrid('hideCol', 'GIACHENHLECH');
			}
			//End_HaNv_24072019
			if (opt._loaidichvu == '13' && isHienThiMaGiuong) {
				var maGiuongCheck = '';
				var dataGiuong = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D009.EV015", RSUtil.buildParam("", [ opt._khambenhId ]));
				var dataGiuongJson = $.parseJSON(dataGiuong);
				if (dataGiuongJson != null && dataGiuongJson.length > 0) {
					$('#hidKHOABNID').val(dataGiuongJson[0].KHOAID);
					if (dataGiuongJson[0].MAGIUONG != '' && dataGiuongJson[0].MAGIUONG != null) {
						maGiuongCheck = magiuong == '' ? dataGiuongJson[0].MAGIUONG : magiuong;
						ComboUtil.getComboTag("cboLOAIGIUONG", "NTU01H014.EV001", [], loaigiuongid == '' ? dataGiuongJson[0].LOAIGIUONG : loaigiuongid, {
							value : '',
							text : 'Chọn'
						}, "sql", '', function() {
							var sql_par_g = [];
							sql_par_g.push({
								"name" : "[0]",
								"value" : $('#hidKHOABNID').val()
							});
							sql_par_g.push({
								"name" : "[1]",
								"value" : nvl($('#cboLOAIGIUONG').val(), '-1')
							});
							ComboUtil.getComboTag("cboMAGIUONG", "NTU01H032.EV007", sql_par_g, "", {
								extval : true,
								value : '',
								text : 'Chọn giường'
							}, 'sql', '', function() {
								$('#cboMAGIUONG').val(magiuong == '' ? dataGiuongJson[0].MAGIUONG : magiuong);
							});
						});
					} else {
						maGiuongCheck = magiuong;
						ComboUtil.getComboTag("cboLOAIGIUONG", "NTU01H014.EV001", [], loaigiuongid, {
							value : '',
							text : 'Chọn'
						}, "sql", '', function() {
							var sql_par_g = [];
							sql_par_g.push({
								"name" : "[0]",
								"value" : $('#hidKHOABNID').val()
							});
							sql_par_g.push({
								"name" : "[1]",
								"value" : nvl($('#cboLOAIGIUONG').val(), '-1')
							});
							ComboUtil.getComboTag("cboMAGIUONG", "NTU01H032.EV007", sql_par_g, "", {
								extval : true,
								value : '',
								text : 'Chọn giường'
							}, 'sql', '', function() {
								$('#cboMAGIUONG').val(magiuong == '' ? '' : magiuong);
							});
						});
					}
				}
				//Begin_HaNv_14032018: check số người trên giường BN nằm và hiển thị cảnh báo tỉ lệ giường
				if (maGiuongCheck != '' && maGiuongCheck != null) {
					var _sql_par = [ {
						"name" : "[0]",
						"value" : opt._khambenhId
					}, {
						"name" : "[1]",
						"value" : maGiuongCheck
					} ];
					var songuoi = jsonrpc.AjaxJson.getOneValue("CHECK_SONGUOI", _sql_par);
					if (songuoi == '2') {
						DlgUtil.showMsg('Giường ' + maGiuongCheck + ' có số lượng BN nằm là 2 người. Tỉ lệ giường là 50%');
					} else if (songuoi == '3') {
						DlgUtil.showMsg('Giường ' + maGiuongCheck + ' có số lượng BN nằm là 3 người. Tỉ lệ giường là 30%');
					}
				}
				//End_HaNv_14032018
			}
			if (opt._loaidichvu == '19') {
				$("#grdDSCD").jqGrid('hideCol', 'MIENGIAM');
				$("#grdDSCD").jqGrid('hideCol', 'BHYT_TRA');
			}
			if (opt._loaidichvu != '12') {
				$("#grdDSCD").jqGrid('hideCol', 'GHICHU');
			}
		} else {
			config = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_CAUHINH_YEUCAU_CHUANDOAN_DICHVU');
			if (config == '0') {
				$('#divChanDoan').removeClass('required');
			} else {
				config = '1';
			}
			sql_par = RSUtil.buildParam("", [ opt._tiepnhanId, '-1' ]);
			GridUtil.loadGridBySqlPage("grdDSCDOld", "NTU02D009.EV009", sql_par);
			if (isLayBenhPham) {
				$('#divBenhPham').show();
				GridUtil.init("grdDSCD", "100%", "150", "", false, _grdCDHeader, false, {
					rowNum : 50,
					rowList : [ 50, 75, 100 ]
				});
			} else {
				GridUtil.init("grdDSCD", "100%", "150", "", false, _grdCDHeader, false, {
					rowNum : 50,
					rowList : [ 50, 75, 100 ]
				});
			}
			$("#grdDSCD")[0].toggleToolbar();
			if (!book) {
				$("#grdDSCD").jqGrid('hideCol', 'HEN');
			}
			if (!isNhomDvViSinh) {
				$("#grdDSCD").jqGrid('hideCol', 'THUCHIENVISINH');
			}
			//Begin_HaNv_29012019: Tinh trang PTTT (binh thuong hoac cap cuu) - L2PT-1567
			if (!showTinhTrangPttt) {
				$("#grdDSCD").jqGrid('hideCol', 'TINHTRANG_PTTT');
			}
			//End_HaNv_21062018
			if (isNotEdit) {
				$("#grdDSCD").jqGrid('setColProp', 'SOLUONG', {
					editable : false
				});
			}
			if (typeof (opt._loaiphieu) != 'undefined' && !isKeChung) {
				if (opt._loaiphieu == '2') {
					$('#liXetNghiem').removeClass("active");
					$('#liCDHA').addClass("active");
					$('#tabXetNghiem').removeClass("active");
					$('#tabCDHA').addClass("active");
				} else if (opt._loaiphieu == '5') {
					$('#liXetNghiem').removeClass("active");
					$('#liChuyenKhoa').addClass("active");
					$('#tabXetNghiem').removeClass("active");
					$('#tabChuyenKhoa').addClass("active");
				}
			}
			if (typeof opt._maubenhphamId != "undefined") {
				sql_par = RSUtil.buildParam("", [ opt._maubenhphamId, opt._khambenhId ]);
				//START -- phan he dinh duong -- hongdq -- 20180418
				var dataObj;
				if (opt._loaidichvu == '12') {
					GridUtil.loadGridBySqlPage("grdDSCD", "NGT02K016.DSDVCD", sql_par);
					dataObj = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K016.UPD_SA", RSUtil.buildParam("", [ opt._maubenhphamId ]));
				} else {
					GridUtil.loadGridBySqlPage("grdDSCD", "NTU02D009.EV002", sql_par);
					dataObj = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D009.EV004", RSUtil.buildParam("", [ opt._maubenhphamId ]));
				}
				//END -- phan he dinh duong -- hongdq -- 20180418
				var data_ar = $.parseJSON(dataObj);
				if (data_ar != null && data_ar.length > 0) {
					var data = data_ar[0];
					FormUtil.setObjectToForm("", "", data);
					if (isBacsiKe) {
						if (data.BACSYDIEUTRIID != null && data.BACSYDIEUTRIID != '') {
							cboBacsiVal = data.BACSYDIEUTRIID;
						}
					}
					//Begin_HaNv_17012020: Cho phép chọn Bác Sĩ bên ngoài phòng khám để tính hoa hồng - L2PT-15119
					if (data.BACSINGOAIPK != null && data.BACSINGOAIPK != '') {
						cboBacsiNgoaiPk = data.BACSINGOAIPK;
					}
					//End_HaNv_17012020
					//Begin_HaNv_04072020: Bổ sung thông tin người lấy mẫu, thời gian lấy mẫu khi chỉ định - L2PT-23017
					if (data.NGUOILAYMAU != null && data.NGUOILAYMAU != '') {
						var sql_par = [];
						sql_par.push({
							"name" : "[0]",
							"value" : "6"
						}, {
							"name" : "[1]",
							"value" : data.NGUOILAYMAU
						});
						var _row = jsonrpc.AjaxJson.getFirstRowO("CDDV.USER_LOAD", sql_par);
						if (_row != null && _row.length > 0) {
							$("#txtNGUOILAYMAU").val("");
							option = $('<option value="' + _row[0].USER_ID + '">' + _row[0].OFFICER_NAME + '</option>');
							$("#cboNGUOILAYMAU").empty();
							$("#cboNGUOILAYMAU").append(option);
						}
					}
					//End_HaNv_04072020
					sql_par = RSUtil.buildParam("", [ this.opt._khambenhId, "4" ]);
					ComboUtil.getComboTag("cboMAUBENHPHAMID", "COM.PHIEUDIEUTRI", sql_par, data.PHIEUDIEUTRIID == null ? '' : data.PHIEUDIEUTRIID, {
						value : '',
						text : 'Chưa có phiếu điều trị'
					}, 'sql', '', function() {
						if (data.PHIEUDIEUTRIID != null && data.PHIEUDIEUTRIID != '') {
							var dataObj = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV008", data.PHIEUDIEUTRIID);
							if (dataObj != null && dataObj.length > 0) {
								var dataICd = dataObj[0];
								FormUtil.setObjectToForm("", "", dataICd);
							}
						} else {
							if (data.MACHANDOAN != null && data.MACHANDOAN != '') {
								$('#cboMACHANDOAN').empty();
								$('#cboMACHANDOAN').append('<option value="' + data.MACHANDOAN + '">' + data.CHANDOAN + '</option>');
							}
							if (data.CHANDOAN_KEMTHEO != null && data.CHANDOAN_KEMTHEO != '') {
								$('#txtCHANDOAN_KT').val(data.CHANDOAN_KEMTHEO);
							}
						}
					});
					//Begin_HaNv_07082019: load dữ liệu chkKhan khi cập nhật dv CLS - L2PT-7335
					if (data.LOAIMAUBENHPHAM == '2') {
						$('#chkKHAN').attr('checked', true);
					} else {
						$('#chkKHAN').attr('checked', false);
					}
					//End_HaNv_31072019
				} else {
					DlgUtil.showMsg('Không tìm thấy mẫu bệnh phẩm');
					return;
				}
			} else {
				if (isBacsiKe) {
					//Begin_HaNv_13112019: Cho phép tìm kiếm bác sỹ trong danh sách thay vì chọn - L2PT-11245
					if (opt.bacsike != null && opt.bacsike != '') {
						cboBacsiVal = opt.bacsike;
					} else if (opt.user_id != '') {
						//Begin_HaNv_10102019: Mặc định hiển thị bác sỹ là user đăng nhập - L2PT-9544
						cboBacsiVal = opt.user_id;
						//End_HaNv_10102019
					}
					//End_HaNv_13112019
				}
				sql_par = RSUtil.buildParam("", [ this.opt._khambenhId, "4" ]);
				ComboUtil.getComboTag("cboMAUBENHPHAMID", "COM.PHIEUDIEUTRI", sql_par, "", {
					value : '',
					text : 'Chưa có phiếu điều trị'
				}, 'sql', '', function() {
					if (opt._phieudieutriId != '') {
						$('#cboMAUBENHPHAMID').val(opt._phieudieutriId).change();
					} else {
						if (icdchinh != null && icdchinh != '') {
							$('#cboMACHANDOAN').empty();
							$('#cboMACHANDOAN').append('<option value="' + icdchinh + '">' + textchinh + '</option>');
							$('#txtCHANDOAN_KT').val(icdphu);
						}
						if (icdphu != null && icdphu != '') {
							$('#txtCHANDOAN_KT').val(icdphu);
						}
					}
				});
			}
			var _group = {
				groupField : [ 'TENNHOM' ],
				groupColumnShow : [ false ],
				groupText : [ "<b>{0}</b>" ],
				groupCollapse : isColspan
			};
			GridUtil.initGroup("grdXetNghiem", "100%", "380", "", true, _group, _gridHeader, false, {
				rowNum : record,
				rowList : [ record, 200, 300 ]
			});
			// $("#grdXetNghiem").jqGrid('filterToolbar', { searchOnEnter: true, enableClear: false, autosearch: false });
			GridUtil.initGroup("grdCDHA", "100%", "380", "", true, _group, _gridHeader, false, {
				rowNum : record,
				rowList : [ record, 200, 300 ]
			});
			// $("#grdCDHA").jqGrid('filterToolbar', { searchOnEnter: true, enableClear: false, autosearch: false });
			GridUtil.initGroup("grdChuyenKhoa", "100%", "380", "", true, _group, _gridHeader, false, {
				rowNum : record,
				rowList : [ record, 200, 300 ]
			});
			// $("#grdChuyenKhoa").jqGrid('filterToolbar', { searchOnEnter: true, enableClear: false, autosearch: false });
			if (isShowTabTd) {
				GridUtil.initGroup("grdThuongDung", "95%", "350", "", true, _group, _gridHeader, false, {
					rowNum : record,
					rowList : [ record, 200, 300 ]
				});
				var sql_par_td = RSUtil.buildParam("", [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), '0' ]);
				GridUtil.loadGridBySqlPage("grdThuongDung", 'NTU02D009.EV018', sql_par_td);
				if (!isDisplayColumnTt37) {
					$("#grdThuongDung").jqGrid('hideCol', 'TEN_TT37');
				}
				//Begin_HaNv_24072019: Hien thi cot gia chenh lech - L2PT-7281
				if (!isDisplayColGiaChenh) {
					$("#grdThuongDung").jqGrid('hideCol', 'GIACHENHLECH');
				}
				//End_HaNv_24072019
			}
			var loadPlService = '0';
			if (plDv == '1') {
				if ($('#chkAllService').is(':checked')) {
					loadPlService = '0';
				} else {
					loadPlService = '1';
				}
			} else {
				loadPlService = '0';
			}
			if (!isKeChung) {
				sql_par = RSUtil.buildParam("", [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), loadPlService ]);
				GridUtil.loadGridBySqlPage("grdXetNghiem", lookup_sql, sql_par);
				sql_par[1].value = 4;
				GridUtil.loadGridBySqlPage("grdCDHA", lookup_sql, sql_par);
				sql_par[1].value = 5;
				GridUtil.loadGridBySqlPage("grdChuyenKhoa", "NTU02D009.EV006", sql_par);
			} else {
				sql_par = RSUtil.buildParam("", [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), loadPlService ]);
				GridUtil.loadGridBySqlPage("grdXetNghiem", 'NTU02D009.EV014', sql_par);
			}
			if (!isDisplayColumnTt37) {
				$("#grdXetNghiem").jqGrid('hideCol', 'TEN_TT37');
				$("#grdCDHA").jqGrid('hideCol', 'TEN_TT37');
				$("#grdChuyenKhoa").jqGrid('hideCol', 'TEN_TT37');
			}
			if (hideColCk) {
				$("#grdXetNghiem").jqGrid('hideCol', 'CHUYENKHOAID');
				$("#grdCDHA").jqGrid('hideCol', 'CHUYENKHOAID');
				$("#grdChuyenKhoa").jqGrid('hideCol', 'CHUYENKHOAID');
			}
			//Begin_HaNv_24072019: Hien thi cot gia chenh lech - L2PT-7281
			if (!isDisplayColGiaChenh) {
				$("#grdXetNghiem").jqGrid('hideCol', 'GIACHENHLECH');
				$("#grdCDHA").jqGrid('hideCol', 'GIACHENHLECH');
				$("#grdChuyenKhoa").jqGrid('hideCol', 'GIACHENHLECH');
			}
			//End_HaNv_24072019
		}
		$("#grdDSCD").setColProp('PHONG_TH', {
			sortable : false
		});
		//Begin_HaNv_01072019: Hien thi cac dich vu tiem chung da danh dau - L1PT-719
		if (opt.tiemchung == '1') {
			$('#liXetNghiem').hide();
			$('#liCDHA').hide();
			$('#liChuyenKhoa').hide();
			$('#liThuongDung').hide();
			$('#liTiemChung').show();
			$('#liCDHA').removeClass("active");
			$('#tabCDHA').removeClass("active");
			$('#liChuyenKhoa').removeClass("active");
			$('#tabChuyenKhoa').removeClass("active");
			$('#liXetNghiem').removeClass("active");
			$('#tabXetNghiem').removeClass("active");
			$('#liTiemChung').addClass("active");
			$('#tabTiemChung').addClass("active");
			$("#liTiemChung").click();
			GridUtil.initGroup("grdTiemChung", "95%", "350", "", true, _group, _gridHeader, false, {
				rowNum : record,
				rowList : [ record, 200, 300 ]
			});
			var sql_par_tc = RSUtil.buildParam("", [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), '0' ]);
			GridUtil.loadGridBySqlPage("grdTiemChung", 'NTU02D009.EV019', sql_par_tc);
			if (!isDisplayColumnTt37) {
				$("#grdTiemChung").jqGrid('hideCol', 'TEN_TT37');
			}
			//Begin_HaNv_24072019: Hien thi cot gia chenh lech - L2PT-7281
			if (!isDisplayColGiaChenh) {
				$("#grdTiemChung").jqGrid('hideCol', 'GIACHENHLECH');
			}
			//End_HaNv_24072019
		}
		//End_HaNv_01072019
		//Begin_HaNv_12102020: Chỉ định dịch vụ theo bộ dịch vụ - L2PT-28041
		if (isShowTabGoiDv) {
			GridUtil.init("grdGoiDv", "100%", "420", "", true, _gridGoiDvHeader);
			GridUtil.loadGridBySqlPage("grdGoiDv", "DMC142.DSMAY");
		}
		//End_HaNv_12102020
		//Begin_HaNv_13112019: Cho phép tìm kiếm bác sỹ trong danh sách thay vì chọn - L2PT-11245
		$(document).ready(function() {
			if (cboBacsiVal != null && cboBacsiVal != '') {
				if (isBacsiSearch) {
					var sql_par = [];
					sql_par.push({
						"name" : "[0]",
						"value" : opt._depId
					}, {
						"name" : "[1]",
						value : cboBacsiVal
					});
					var _row = jsonrpc.AjaxJson.getFirstRowO("CDDV.BACSI_LOAD", sql_par);
					if (_row != null && _row.length > 0) {
						option = $('<option value="' + _row[0].USER_ID + '">' + _row[0].OFFICER_NAME + '</option>');
						$("#cboBACSIID").empty();
						$("#cboBACSIID").append(option);
					}
				} else {
					$('#cboBACSIID').val(cboBacsiVal);
				}
			}
		});
		//End_HaNv_13112019
		//Begin_HaNv_17012020: Cho phép chọn Bác Sĩ bên ngoài phòng khám để tính hoa hồng - L2PT-15119
		if (cboBacsiNgoaiPk != null && cboBacsiNgoaiPk != '') {
			var sql_par = [];
			sql_par.push({
				"name" : "[0]",
				"value" : cboBacsiNgoaiPk
			});
			var _row = jsonrpc.AjaxJson.getFirstRowO("CDDV.BSNGOAI_LOAD", sql_par);
			if (_row != null && _row.length > 0) {
				$("#txtBACSINGOAIPK").val("");
				option = $('<option value="' + _row[0].USER_ID + '">' + _row[0].OFFICER_NAME + '</option>');
				$("#cboBACSINGOAIPK").empty();
				$("#cboBACSINGOAIPK").append(option);
			}
		}
		//End_HaNv_17012020
		//Begin_HaNv_04052020: Chuyển đổi khám sức khỏe theo đoàn - L2PT-20170
		if (opt.formCall != 'KSK') {
			$('#changeKSK').remove();
		}
		//End_HaNv_04052020
		//Begin_HaNv_08052020: Cho phép chỉ định thu khác từ thuvienphi khi BN đã kết thúc BA - L2PT-20670
		if (opt.formCall == 'VPI_TVP') {
			cpCdKetThucBA = true;
		}
		//End_HaNv_08052020
	}
	function _bindEvent() {
		$('#focusguard-2').on('focus', function() {
			$('#txtLOAIDV').focus();
		});
		$('#focusguard-1').on('focus', function() {
			$('#txtSOLUONG_KHA').focus();
		});
		$("#txtLOAIDV").focusout(function() {
			var loaidv = $("#txtLOAIDV").val();
			if (loaidv == '1') {
				$('#cboLOAIDV').val('3').change();
			} else if (loaidv == '2') {
				$('#cboLOAIDV').val('4').change();
			} else if (loaidv == '3') {
				$('#cboLOAIDV').val('5').change();
			} else {
				$('#cboLOAIDV').val('0').change();
			}
			$('#cboLOAIDV').change();
		});
		$("#cboLOAIDV").on('change', function(e) {
			var sql_par_dv = RSUtil.buildParam("", [ opt._subdeptId, $("#cboLOAIDV").val(), opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), '0' ]);
			ComboUtil.initComboGrid("txtMADICHVU", 'NTU02D009.EV001', sql_par_dv, "800px", _colDv, function(event, ui) {
				_objDichVu = ui.item;
				callbackDichVu(_objDichVu);
				return false;
			});
		});
		$('#txtSOLUONG_KHA').keydown(function(e) {
			if (e.which === 13) {
				if ($('#txtSOLUONG_KHA').val() == '' || $('#txtSOLUONG_KHA').val() == null) {
					$('#txtSOLUONG_KHA').val('1');
				}
				checkAddDichVu();
			}
			if (e.which === 38) {
				if ($('#txtSOLUONG_KHA').val() == '' || $('#txtSOLUONG_KHA').val() == null) {
					$('#txtSOLUONG_KHA').val('1');
				}
				$('#txtMADICHVU').focus();
			}
		});
		$('#txtMADICHVU').change(function() {
			if ($(this).val() == null || $(this).val().trim() == "") {
				$('#txtTENDICHVU').val('');
			}
		});
		$('#txtMADICHVU').keydown(function(e) {
			if (e.which === 46) {
				$('#txtTENDICHVU').val('');
			}
		});
		EventUtil.setEvent("CLS001X113_LUU", function(e) {
			var data = e;
			if (jsonNuoiCay == '') {
				jsonNuoiCay = JSON.stringify(data);
			}
			var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
			for (var k = 0; k < rowIds.length; k++) {
				var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
				if (rowData.DICHVUID == data.DICHVUID) {
					$('#grdDSCD').jqGrid('setCell', rowIds[k], 'JSON_NUOICAY', JSON.stringify(data));
					$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).JSON_NUOICAY = JSON.stringify(data);
					break;
				}
			}
		});
		$('#gbox_grdXetNghiem').find('.ui-search-input').find('#gs_TENDICHVU').focus();
		EventUtil.setEvent("assignSevice_saveTempData", function(e) {
			var data = e;
			var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
			for (var k = 0; k < rowIds.length; k++) {
				var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
				if (rowData.DICHVUID == data.DICHVUID) {
					$('#grdDSCD').jqGrid('setCell', rowIds[k], 'JSON_TEMP', JSON.stringify(data));
					$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).JSON_TEMP = JSON.stringify(data);
					break;
				}
			}
		});
		$("#liXetNghiem").on("click", function(e) {
			setTimeout(function() {
				$('#gbox_grdXetNghiem').find('.ui-search-input').find('#gs_TENDICHVU')[0].focus();
			}, 300);
		});
		$("#liCDHA").on("click", function(e) {
			setTimeout(function() {
				$('#gbox_grdCDHA').find('.ui-search-input').find('#gs_TENDICHVU')[0].focus();
			}, 300);
		});
		$("#liChuyenKhoa").on("click", function(e) {
			setTimeout(function() {
				$('#gbox_grdChuyenKhoa').find('.ui-search-input').find('#gs_TENDICHVU')[0].focus();
			}, 300);
		});
		$('#gbox_grdXetNghiem').find('.ui-search-input').find('#gs_TENDICHVU').keydown(function(e) {
			if ($(e.target).is("input")) {
				if (e.which === 13) {
					var marked_list = $('#grdXetNghiem').attr("marked");
					var idarray = $('#grdXetNghiem').jqGrid('getDataIDs');
					var marked = [];
					if (marked_list) {
						marked = marked_list.split(',');
					}
					var rowIdnext = '';
					var isBreak = false;
					if (marked.length == 0) {
						return;
					} else {
						for (var i1 = 0; i1 < idarray.length; i1++) {
							var rowid = idarray[i1];
							if (rowid) {
								var _color = '#FFFFFF';
								$('#grdXetNghiem').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
									if ($(element).hasClass('markedRow')) {
										$('#grdXetNghiem').setSelection(rowid, true);
										isBreak = true;
										// $('#gbox_grdXetNghiem').find('.ui-search-input').find('#gs_TENDICHVU').focus();
										$('#gbox_grdXetNghiem').find('.ui-search-input').find('#gs_TENDICHVU').select();
										return false;
									}
								});
								if (isBreak) {
									break;
								}
							}
						}
					}
				} else if (isSearchLike) {
					$(e.target).val($(e.target).val().replace(" ", "%"));
				}
			}
		});
		$("#gbox_grdXetNghiem").keydown(function(e) {
			switch (e.which) {
				case 40:
					var marked_list = $('#grdXetNghiem').attr("marked");
					var idarray = $('#grdXetNghiem').jqGrid('getDataIDs');
					var marked = [];
					if (marked_list) {
						marked = marked_list.split(',');
					}
					var rowIdnext = '';
					if (marked.length == 0 && idarray != null && idarray.length > 0) {
						rowIdnext = idarray[0];
					} else {
						for (var i1 = 0; i1 < idarray.length; i1++) {
							var rowid = idarray[i1];
							if (rowid) {
								var _color = '#FFFFFF';
								$('#grdXetNghiem').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
									if ($(element).hasClass('markedRow')) {
										$(element).removeClass(function(_, cl) {
											return cl.split(' ').filter(function(c) {
												var clsName = "markedRow";
												return c.substr(0, clsName.length) === clsName;
											}).join(' ');
										});
										if (i1 < idarray.length - 1) {
											rowIdnext = parseInt(rowid) + 1;
										}
									}
								});
							}
						}
					}
					$('#grdXetNghiem').attr("marked", '');
					if (rowIdnext != '') {
						GridUtil.markRow("grdXetNghiem", rowIdnext);
					}
				break;
				case 38:
					var marked_list = $('#grdXetNghiem').attr("marked");
					var idarray = $('#grdXetNghiem').jqGrid('getDataIDs');
					var marked = [];
					if (marked_list) {
						marked = marked_list.split(',');
					}
					var rowIdnext = '';
					if (marked.length == 0 && idarray != null && idarray.length > 0) {
						rowIdnext = idarray[0];
					} else {
						for (var i1 = 0; i1 < idarray.length; i1++) {
							var rowid = idarray[i1];
							if (rowid) {
								var _color = '#FFFFFF';
								$('#grdXetNghiem').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
									if ($(element).hasClass('markedRow')) {
										$(element).removeClass(function(_, cl) {
											return cl.split(' ').filter(function(c) {
												var clsName = "markedRow";
												return c.substr(0, clsName.length) === clsName;
											}).join(' ');
										});
										if (i1 != 0) {
											rowIdnext = parseInt(rowid) - 1;
										}
									}
								});
							}
						}
					}
					$('#grdXetNghiem').attr("marked", '');
					if (rowIdnext != '') {
						GridUtil.markRow("grdXetNghiem", rowIdnext);
					}
				break;
				default:
					return;
			}
		});
		$('#gbox_grdCDHA').find('.ui-search-input').find('#gs_TENDICHVU').keydown(function(e) {
			if ($(e.target).is("input")) {
				if (e.which === 13) {
					var marked_list = $('#grdCDHA').attr("marked");
					var idarray = $('#grdCDHA').jqGrid('getDataIDs');
					var marked = [];
					if (marked_list) {
						marked = marked_list.split(',');
					}
					var rowIdnext = '';
					var isBreak = false;
					if (marked.length == 0) {
						return;
					} else {
						for (var i1 = 0; i1 < idarray.length; i1++) {
							var rowid = idarray[i1];
							if (rowid) {
								var _color = '#FFFFFF';
								$('#grdCDHA').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
									if ($(element).hasClass('markedRow')) {
										$('#grdCDHA').setSelection(rowid, true);
										isBreak = true;
										// $('#gbox_grdCDHA').find('.ui-search-input').find('#gs_TENDICHVU').focus();
										$('#gbox_grdCDHA').find('.ui-search-input').find('#gs_TENDICHVU').select();
										return false;
									}
								});
								if (isBreak) {
									break;
								}
							}
						}
					}
				} else if (isSearchLike) {
					$(e.target).val($(e.target).val().replace(" ", "%"));
				}
			}
		});
		$("#gbox_grdCDHA").keydown(function(e) {
			switch (e.which) {
				case 40:
					var marked_list = $('#grdCDHA').attr("marked");
					var idarray = $('#grdCDHA').jqGrid('getDataIDs');
					var marked = [];
					if (marked_list) {
						marked = marked_list.split(',');
					}
					var rowIdnext = '';
					if (marked.length == 0 && idarray != null && idarray.length > 0) {
						rowIdnext = idarray[0];
					} else {
						for (var i1 = 0; i1 < idarray.length; i1++) {
							var rowid = idarray[i1];
							if (rowid) {
								var _color = '#FFFFFF';
								$('#grdCDHA').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
									if ($(element).hasClass('markedRow')) {
										$(element).removeClass(function(_, cl) {
											return cl.split(' ').filter(function(c) {
												var clsName = "markedRow";
												return c.substr(0, clsName.length) === clsName;
											}).join(' ');
										});
										if (i1 < idarray.length - 1) {
											rowIdnext = parseInt(rowid) + 1;
										}
									}
								});
							}
						}
					}
					$('#grdCDHA').attr("marked", '');
					if (rowIdnext != '') {
						GridUtil.markRow("grdCDHA", rowIdnext);
					}
				break;
				case 38:
					var marked_list = $('#grdCDHA').attr("marked");
					var idarray = $('#grdCDHA').jqGrid('getDataIDs');
					var marked = [];
					if (marked_list) {
						marked = marked_list.split(',');
					}
					var rowIdnext = '';
					if (marked.length == 0 && idarray != null && idarray.length > 0) {
						rowIdnext = idarray[0];
					} else {
						for (var i1 = 0; i1 < idarray.length; i1++) {
							var rowid = idarray[i1];
							if (rowid) {
								var _color = '#FFFFFF';
								$('#grdCDHA').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
									if ($(element).hasClass('markedRow')) {
										$(element).removeClass(function(_, cl) {
											return cl.split(' ').filter(function(c) {
												var clsName = "markedRow";
												return c.substr(0, clsName.length) === clsName;
											}).join(' ');
										});
										if (i1 != 0) {
											rowIdnext = parseInt(rowid) - 1;
										}
									}
								});
							}
						}
					}
					$('#grdCDHA').attr("marked", '');
					if (rowIdnext != '') {
						GridUtil.markRow("grdCDHA", rowIdnext);
					}
				break;
				default:
					return;
			}
		});
		$('#gbox_grdChuyenKhoa').find('.ui-search-input').find('#gs_TENDICHVU').keydown(function(e) {
			if ($(e.target).is("input")) {
				if (e.which === 13) {
					var marked_list = $('#grdChuyenKhoa').attr("marked");
					var idarray = $('#grdChuyenKhoa').jqGrid('getDataIDs');
					var marked = [];
					if (marked_list) {
						marked = marked_list.split(',');
					}
					var rowIdnext = '';
					var isBreak = false;
					if (marked.length == 0) {
						return;
					} else {
						for (var i1 = 0; i1 < idarray.length; i1++) {
							var rowid = idarray[i1];
							if (rowid) {
								var _color = '#FFFFFF';
								$('#grdChuyenKhoa').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
									if ($(element).hasClass('markedRow')) {
										$('#grdChuyenKhoa').setSelection(rowid, true);
										isBreak = true;
										// $('#gbox_grdChuyenKhoa').find('.ui-search-input').find('#gs_TENDICHVU').focus();
										$('#gbox_grdChuyenKhoa').find('.ui-search-input').find('#gs_TENDICHVU').select();
										return false;
									}
								});
								if (isBreak) {
									break;
								}
							}
						}
					}
				} else if (isSearchLike) {
					$(e.target).val($(e.target).val().replace(" ", "%"));
				}
			}
		});
		$("#gbox_grdChuyenKhoa").keydown(function(e) {
			switch (e.which) {
				case 40:
					var marked_list = $('#grdChuyenKhoa').attr("marked");
					var idarray = $('#grdChuyenKhoa').jqGrid('getDataIDs');
					var marked = [];
					if (marked_list) {
						marked = marked_list.split(',');
					}
					var rowIdnext = '';
					if (marked.length == 0 && idarray != null && idarray.length > 0) {
						rowIdnext = idarray[0];
					} else {
						for (var i1 = 0; i1 < idarray.length; i1++) {
							var rowid = idarray[i1];
							if (rowid) {
								var _color = '#FFFFFF';
								$('#grdChuyenKhoa').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
									if ($(element).hasClass('markedRow')) {
										$(element).removeClass(function(_, cl) {
											return cl.split(' ').filter(function(c) {
												var clsName = "markedRow";
												return c.substr(0, clsName.length) === clsName;
											}).join(' ');
										});
										if (i1 < idarray.length - 1) {
											rowIdnext = parseInt(rowid) + 1;
										}
									}
								});
							}
						}
					}
					$('#grdChuyenKhoa').attr("marked", '');
					if (rowIdnext != '') {
						GridUtil.markRow("grdChuyenKhoa", rowIdnext);
					}
				break;
				case 38:
					var marked_list = $('#grdChuyenKhoa').attr("marked");
					var idarray = $('#grdChuyenKhoa').jqGrid('getDataIDs');
					var marked = [];
					if (marked_list) {
						marked = marked_list.split(',');
					}
					var rowIdnext = '';
					if (marked.length == 0 && idarray != null && idarray.length > 0) {
						rowIdnext = idarray[0];
					} else {
						for (var i1 = 0; i1 < idarray.length; i1++) {
							var rowid = idarray[i1];
							if (rowid) {
								var _color = '#FFFFFF';
								$('#grdChuyenKhoa').find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
									if ($(element).hasClass('markedRow')) {
										$(element).removeClass(function(_, cl) {
											return cl.split(' ').filter(function(c) {
												var clsName = "markedRow";
												return c.substr(0, clsName.length) === clsName;
											}).join(' ');
										});
										if (i1 != 0) {
											rowIdnext = parseInt(rowid) - 1;
										}
									}
								});
							}
						}
					}
					$('#grdChuyenKhoa').attr("marked", '');
					if (rowIdnext != '') {
						GridUtil.markRow("grdChuyenKhoa", rowIdnext);
					}
				break;
				default:
					return;
			}
		});
		$("#chkAllService").change(function() {
			loadGridData();
		});
		$("#cboKHOACHIDINHCHID").change(function() {
			if ($("#cboKHOACHIDINHCHID").val() == '') {
				$('#cboBACSIKHOACDID').empty();
				$('#cboBACSIKHOACDID').append('<option value="">--Chọn--</option>');
				$('#cboPHONGCHIDINHCHID').empty();
				$('#cboPHONGCHIDINHCHID').append('<option value="">--Chọn--</option>');
			} else {
				ComboUtil.getComboTag("cboPHONGCHIDINHCHID", "COM.PHONGKHOA", [ {
					"name" : "[0]",
					"value" : "-1"
				}, {
					"name" : "[1]",
					"value" : "5"
				}, {
					"name" : "[2]",
					"value" : $("#cboKHOACHIDINHCHID").val()
				} ], '', {
					value : '',
					text : '---Chọn---'
				}, "sql");
				ComboUtil.getComboTag("cboBACSIKHOACDID", "NGT02K016.EV002", [ {
					"name" : "[0]",
					"value" : $("#cboKHOACHIDINHCHID").val()
				} ], '', {
					value : '',
					text : '---Chọn---'
				}, "sql");
			}
		});
		$('#cboLOAIGIUONG').on('change', function(e) {
			var sql_par_g = [];
			sql_par_g.push({
				"name" : "[0]",
				"value" : $('#hidKHOABNID').val()
			});
			sql_par_g.push({
				"name" : "[1]",
				"value" : nvl($('#cboLOAIGIUONG').val(), '-1')
			});
			ComboUtil.getComboTag("cboMAGIUONG", "NTU01H032.EV007", sql_par_g, giuongdv, {
				extval : true,
				value : '',
				text : 'Chọn giường'
			}, 'sql');
		});
		function loadGridData() {
			var loadPlService = '0';
			if (plDv == '1') {
				if ($('#chkAllService').is(':checked')) {
					loadPlService = '0';
				} else {
					loadPlService = '1';
				}
			} else {
				loadPlService = '0';
			}
			sql_par = RSUtil.buildParam("", [ opt._subdeptId, 3, opt._depId, nvl($('#cboNHOMXETNGHIEMID').val(), '-1'), opt._khambenhId, $('#txtTGCHIDINH').val(), loadPlService ]);
			if (isKeChung) {
				GridUtil.loadGridBySqlPage("grdXetNghiem", 'NTU02D009.EV014', sql_par);
			} else {
				GridUtil.loadGridBySqlPage("grdXetNghiem", lookup_sql, sql_par);
				sql_par[1].value = 4;
				sql_par[3].value = nvl($('#cboNHOMCDHAID').val(), -1);
				GridUtil.loadGridBySqlPage("grdCDHA", lookup_sql, sql_par);
				sql_par[1].value = 5;
				sql_par[3].value = nvl($('#cboCHUYENKHOAID').val(), -1);
				GridUtil.loadGridBySqlPage("grdChuyenKhoa", "NTU02D009.EV006", sql_par);
			}
		}
		$('#cboMAUBENHPHAMID').on('change', function(e) {
			if ($('#cboMAUBENHPHAMID').val() != '') {
				//Begin_HaNv_05102020: Ràng buộc chỉ định dịch vụ khi tờ điều trị kí số - L2PT-28416
				if (checkKiSoPDT) {
					var checkKiSo = jsonrpc.AjaxJson.ajaxCALL_SP_I("PDT.CHECK_KISO", $('#cboMAUBENHPHAMID').val());
					if (checkKiSo > 0) {
						DlgUtil.showMsg('Không thể chọn phiếu điều trị đã được kí số!');
						$('#cboMAUBENHPHAMID').val('');
						return;
					}
				}
				//End_HaNv_05102020
				var dataObj = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV008", $('#cboMAUBENHPHAMID').val());
				if (dataObj != null && dataObj.length > 0) {
					var dataICd = dataObj[0];
					//Begin_HaNv_05072019: Điều chỉnh mô tả mã bệnh khi chỉ định dịch vụ - L2PT-6491
					//FormUtil.setObjectToForm("", "", dataICd);
					$('#cboMACHANDOAN').empty();
					$('#cboMACHANDOAN').append('<option value="' + dataICd.TKCHANDOAN + '">' + dataICd.MACHANDOAN + '</option>');
					$('#txtCHANDOAN_KT').val(dataICd.CHANDOAN_KT);
					$('#txtTGCHIDINH').val(dataICd.TGDIEUTRI);
					//End_HaNv_05072019
				}
				//Begin_HaNv_17042019: load thông tin nuôi cấy của 1 dịch vụ trong phiếu điều trị - L2PT-4114
				if (isMauCls && jsonNuoiCay == '') {
					var dataNuoiCay = jsonrpc.AjaxJson.ajaxCALL_SP_O("CDDV.TTNC_PDT", $('#cboMAUBENHPHAMID').val());
					if (dataNuoiCay != null && dataNuoiCay.length > 0) {
						var data = dataNuoiCay[0];
						jsonNuoiCay = JSON.stringify(data);
					}
				}
				//End_HaNv_17042019
			} else {
				if (icdchinh != null && icdchinh != '') {
					$('#txtTKCHANDOAN').combogrid("setValue", icdchinh);
				}
				if (icdphu != null && icdphu != '') {
					$('#txtCHANDOAN_KT').val(icdphu);
				}
			}
		});
		$('#cboNHOMXETNGHIEMID').on('change', function(e) {
			var loadPlService = '0';
			if (plDv == '1') {
				if ($('#chkAllService').is(':checked')) {
					loadPlService = '0';
				} else {
					loadPlService = '1';
				}
			} else {
				loadPlService = '0';
			}
			sql_par = RSUtil.buildParam("", [ opt._subdeptId, 3, opt._depId, nvl($('#cboNHOMXETNGHIEMID').val(), '-1'), opt._khambenhId, $('#txtTGCHIDINH').val(), loadPlService ]);
			if (isKeChung) {
				GridUtil.loadGridBySqlPage("grdXetNghiem", 'NTU02D009.EV014', sql_par);
			} else {
				GridUtil.loadGridBySqlPage("grdXetNghiem", lookup_sql, sql_par);
			}
		});
		$('#cboNHOMCDHAID').on('change', function(e) {
			var loadPlService = '0';
			if (plDv == '1') {
				if ($('#chkAllService').is(':checked')) {
					loadPlService = '0';
				} else {
					loadPlService = '1';
				}
			} else {
				loadPlService = '0';
			}
			sql_par = RSUtil.buildParam("", [ opt._subdeptId, 4, opt._depId, nvl($('#cboNHOMCDHAID').val(), '-1'), opt._khambenhId, $('#txtTGCHIDINH').val(), loadPlService ]);
			GridUtil.loadGridBySqlPage("grdCDHA", lookup_sql, sql_par);
		});
		$('#cboCHUYENKHOAID').on('change', function(e) {
			var loadPlService = '0';
			if (plDv == '1') {
				if ($('#chkAllService').is(':checked')) {
					loadPlService = '0';
				} else {
					loadPlService = '1';
				}
			} else {
				loadPlService = '0';
			}
			sql_par = RSUtil.buildParam("", [ opt._subdeptId, 5, opt._depId, nvl($('#cboCHUYENKHOAID').val(), '-1'), opt._khambenhId, $('#txtTGCHIDINH').val(), loadPlService ]);
			GridUtil.loadGridBySqlPage("grdChuyenKhoa", "NTU02D009.EV006", sql_par);
		});
		$('#txtTGCHIDINH').on('change', function(e) {
			var dateStr = $('#txtTGCHIDINH').val().trim();
			var date1 = dateStr.substr(0, 2) + "/" + dateStr.substr(2, 2) + "/" + dateStr.substr(4, 4) + " " + dateStr.substr(8, 2) + ":" + dateStr.substr(10, 2);
			if (dateStr.length >= 12 && dateStr.length < 14 && dateStr.indexOf('/') == -1) {
				$('#txtTGCHIDINH').val(date1 + ":00");
			} else if (dateStr.length == 14 && dateStr.indexOf('/') == -1) {
				$('#txtTGCHIDINH').val(date1 + ":" + dateStr.substr(12, 2));
			}
			if ($('#txtTGCHIDINH').val().trim().length > 0 && !datetimeRegex.test($('#txtTGCHIDINH').val())) {
				DlgUtil.showMsg("TG chỉ định " + $.i18n("date_type_invalid"), function() {
					$('#txtTGCHIDINH').focus();
				});
				return false;
			};
			if (typeof opt._chidinhdichvu != "undefined") {
				var sql_par = RSUtil.buildParam("", [ opt._loaidichvu, opt._khambenhId, $('#txtTGCHIDINH').val() ]);
				GridUtil.loadGridBySqlPage("grdDichVu", "NTU01H003.EV001", sql_par);
			} else {
				var loadPlService = '0';
				if (plDv == '1') {
					if ($('#chkAllService').is(':checked')) {
						loadPlService = '0';
					} else {
						loadPlService = '1';
					}
				} else {
					loadPlService = '0';
				}
				var sql_par = RSUtil.buildParam("", [ opt._subdeptId, 3, opt._depId, nvl($('#cboNHOMXETNGHIEMID').val(), '-1'), opt._khambenhId, $('#txtTGCHIDINH').val(), loadPlService ]);
				if (isKeChung) {
					GridUtil.loadGridBySqlPage("grdXetNghiem", 'NTU02D009.EV014', sql_par);
				} else {
					GridUtil.loadGridBySqlPage("grdXetNghiem", lookup_sql, sql_par);
					sql_par[1].value = 4;
					sql_par[3].value = nvl($('#cboNHOMCDHAID').val(), '-1');
					GridUtil.loadGridBySqlPage("grdCDHA", lookup_sql, sql_par);
					sql_par[1].value = 5;
					sql_par[3].value = nvl($('#cboCHUYENKHOAID').val(), '-1');
					GridUtil.loadGridBySqlPage("grdChuyenKhoa", "NTU02D009.EV006", sql_par);
				}
			}
		});
		//START -- phan he dinh duong -- hongdq -- 20180418
		$('#txtTGSUDUNG').on('change', function(e) {
			var gtCauhinh = '16';
			if ($("#txtTGSUDUNG").val() == '') {
				return DlgUtil.showMsg("Thời gian Sử dụng không được để trống");
			}
			var _tg_chidinh_full = moment($("#txtTGCHIDINH").val().trim(), 'DD/MM/YYYY HH:mm:ss');
			var _tg_sudung_full = moment($("#txtTGSUDUNG").val().trim(), 'DD/MM/YYYY HH:mm:ss');
			tgSDLuuTiep = $("#txtTGSUDUNG").val();
		});
		//END -- phan he dinh duong -- hongdq -- 20180418
		$("#grdDSCD").bind(
				"jqGridAfterGridComplete",
				function(e, rowid, orgClickEvent) {
					if (isCdNgoaigio) {
						var rowCount = $('#grdDSCD').jqGrid('getDataIDs');
						if (rowCount != null && rowCount.length > 0) {
							$('#txtTGCHIDINH').prop('disabled', true);
						} else {
							$('#txtTGCHIDINH').prop('disabled', false);
						}
					}
					if (typeof opt._chidinhdichvu == "undefined" && flagLoad && typeof opt._maubenhphamId != "undefined") {
						var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
						for (var k = 0; k < rowIds.length; k++) {
							var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
							if (opt._doituongbenhnhanId == '1' && (rowData.LOAIDOITUONG == '1' || rowData.LOAIDOITUONG == '2')) {
								if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'BHYT_TRA', rowData.BHYT_TRAFINAL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).BHYT_TRA = rowData.BHYT_TRAFINAL;
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'THANH_TIEN', rowData.THANH_TIENFINAL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).THANH_TIEN = rowData.THANH_TIENFINAL;
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'MIENGIAM', rowData.MIENGIAMFINAL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).MIENGIAM = rowData.MIENGIAMFINAL;
								} else {
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'BHYT_TRA', rowData.BHYT_TRAFULL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).BHYT_TRA = rowData.BHYT_TRAFULL;
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'THANH_TIEN', rowData.THANH_TIENFULL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).THANH_TIEN = rowData.THANH_TIENFULL;
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'MIENGIAM', rowData.MIENGIAMFULL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).MIENGIAM = rowData.MIENGIAMFULL;
								}
							}
							if (rowData.PHONG_TH != null && rowData.PHONG_TH != '' && rowData.PHONG_TH.indexOf("select") < 0) {
								var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV003", rowData.PHONG_TH);
								if (data_ar != null && data_ar.length > 0) {
									var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboKHOACHIDINHID">';
									var option = '';
									var checkExistDept = false;
									var checkEqualDept = false;
									for (var i = 0; i < data_ar.length; i++) {
										if (data_ar[i].PHONGID == opt._subdeptId_login) {
											checkExistDept = true;
										}
										if (data_ar[i].PHONGID != rowData.PHONG_TH1) {
											option = option + '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
										} else {
											checkEqualDept = true;
											option = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>' + option;
										}
									}
									if (!checkExistDept && rowData.LOAINHOMDICHVU == '5') {
										if (!checkEqualDept) {
											option = '<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>' + option;
										} else {
											option = option + '<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>';
										}
									}
									html = html + option + '</select>';
									$("#grdDSCD").jqGrid('setCell', rowIds[k], 'PHONG_TH', html);
								}
							} else {
								if (rowData.PHONG_TH1 != null && rowData.PHONG_TH1 != '' && rowData.PHONG_TH.indexOf("select") < 0) {
									var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV003", rowData.PHONG_TH1);
									if (data_ar != null && data_ar.length > 0) {
										var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboKHOACHIDINHID">' + '<option value="' + data_ar[0].PHONGID + '">' +
												data_ar[0].PHONGNAME + '</option>' + '</select>';
										$("#grdDSCD").jqGrid('setCell', rowIds[k], 'PHONG_TH', html);
									}
								}
							}
							if (book) {
								var checked = rowData.PHIEUHEN == '1' ? 'checked="true"' : "";
								var htmlCheckbox = '<label class="control-label" for="' + (rowData.DICHVUID) +
										'_chkHEN" style="margin-left:5px; margin-bottom: 0px;"><input type="checkbox" class="mgb5" ' + checked + '  id="' + (rowData.DICHVUID) +
										'_chkHEN"> Hẹn</label>';
								$("#grdDSCD").jqGrid('setCell', rowIds[k], 'HEN', htmlCheckbox);
							}
							//Begin_HaNv_21062018: Danh dau thuc hien dich vu vi sinh (phat hien hoac nuoi cay) - L2K74TW-582
							if (isNhomDvViSinh) {
								var checkDvViSinh = jsonrpc.AjaxJson.getOneValue("CHECK_DV_VISINH", [ {
									"name" : "[0]",
									"value" : rowData.DICHVUID
								} ]);
								if (checkDvViSinh == '1') {
									var selected1 = rowData.THUCHIENVISINH == '1' ? 'selected="selected"' : "";
									var selected2 = rowData.THUCHIENVISINH == '2' ? 'selected="selected"' : "";
									var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboTHUCHIENVISINH">' + '<option value="">Chọn</option>' +
											'<option value="1" ' + selected1 + '>Phát hiện</option>' + '<option value="2" ' + selected2 + '>Theo dõi</option>' + '</select>';
									$("#grdDSCD").jqGrid('setCell', rowIds[k], 'ISVISINH', '1');
									$("#grdDSCD").jqGrid('setCell', rowIds[k], 'THUCHIENVISINH', html);
								} else {
									$("#grdDSCD").jqGrid('setCell', rowIds[k], 'ISVISINH', '0');
									$("#grdDSCD").jqGrid('setCell', rowIds[k], 'THUCHIENVISINH', null);
								}
							}
							//End_HaNv_21062018
							//Begin_HaNv_29012019: Tinh trang PTTT (binh thuong hoac cap cuu) - L2PT-1567
							if (showTinhTrangPttt) {
								if (rowData.LOAINHOMDICHVU == '5') {
									if (rowData.TINHTRANG_PTTT != '' && rowData.TINHTRANG_PTTT != null) {
										var selected1 = rowData.TINHTRANG_PTTT == '2' ? 'selected="selected"' : "";
										var selected2 = rowData.TINHTRANG_PTTT == '1' ? 'selected="selected"' : "";
										var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboTINHTRANG_PTTT">' + '<option value="2" ' + selected1 +
												'>Chủ động</option>' + '<option value="1" ' + selected2 + '>Cấp cứu</option>' + '</select>';
										$("#grdDSCD").jqGrid('setCell', rowIds[k], 'TINHTRANG_PTTT', html);
									} else {
										$("#grdDSCD").jqGrid('setCell', rowIds[k], 'TINHTRANG_PTTT', '2');
									}
								} else {
									$("#grdDSCD").jqGrid('setCell', rowIds[k], 'TINHTRANG_PTTT', null);
								}
							}
							//End_HaNv_21062018
							//Begin_HaNv_09092020: Cho phép thay đổi loại Mbp khi chỉ định dịch vụ - L2PT-26489
							if (changeLoaiMbp) {
								var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboLOAI_MBP">';
								html = html + '<option value=""' + (rowData.LOAI_MBP == '' || rowData.LOAI_MBP == null ? 'selected="selected"' : "") + '>Rỗng</option>';
								html = html + '<option value="1"' + (rowData.LOAI_MBP == '1' ? 'selected="selected"' : "") + '>Đàm</option>';
								html = html + '<option value="2"' + (rowData.LOAI_MBP == '2' ? 'selected="selected"' : "") + '>Máu</option>';
								html = html + '<option value="3"' + (rowData.LOAI_MBP == '3' ? 'selected="selected"' : "") + '>Nước tiểu</option>';
								html = html + '<option value="4"' + (rowData.LOAI_MBP == '4' ? 'selected="selected"' : "") + '>Phân</option>';
								html = html + '<option value="5"' + (rowData.LOAI_MBP == '5' ? 'selected="selected"' : "") + '>Dịch</option>';
								html = html + '<option value="6"' + (rowData.LOAI_MBP == '6' ? 'selected="selected"' : "") + '>Mủ</option>';
								html = html + '<option value="7"' + (rowData.LOAI_MBP == '7' ? 'selected="selected"' : "") + '>Khác</option></select>';
								$("#grdDSCD").jqGrid('setCell', rowIds[k], 'LOAIMAUBENHPHAM', html);
							}
							//End_HaNv_09092020
						}
						flagLoad = false;
					} else {
						var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
						var tyleCongKham = 0;
						for (var k = 0; k < rowIds.length; k++) {
							var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
							if (opt._doituongbenhnhanId == '1' && (rowData.LOAIDOITUONG == '1' || rowData.LOAIDOITUONG == '2')) {
								if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'BHYT_TRA', rowData.BHYT_TRAFINAL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).BHYT_TRA = rowData.BHYT_TRAFINAL;
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'THANH_TIEN', rowData.THANH_TIENFINAL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).THANH_TIEN = rowData.THANH_TIENFINAL;
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'MIENGIAM', rowData.MIENGIAMFINAL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).MIENGIAM = rowData.MIENGIAMFINAL;
								} else {
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'BHYT_TRA', rowData.BHYT_TRAFULL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).BHYT_TRA = rowData.BHYT_TRAFULL;
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'THANH_TIEN', rowData.THANH_TIENFULL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).THANH_TIEN = rowData.THANH_TIENFULL;
									$('#grdDSCD').jqGrid('setCell', rowIds[k], 'MIENGIAM', rowData.MIENGIAMFULL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).MIENGIAM = rowData.MIENGIAMFULL;
								}
							}
							if (rowData.SOLUONG % 1 !== 0) {
								$('#grdDSCD').jqGrid('setCell', rowIds[k], 'SOLUONG', parseFloat(rowData.SOLUONG));
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).SOLUONG = parseFloat(rowData.SOLUONG);
							}
							//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
							if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1' && typeof opt._maubenhphamId != "undefined") {
								if (rowData.TYLEDVTEMP != "" && tyleCongKham == 0) {
									tyleCongKham = rowData.TYLEDVTEMP;
								}
							}
							//End_HaNv_01012020
							if (opt._loaidichvu == '13') {
								var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboTYLEDV">' + '<option value="1">100%</option>' +
										'<option value="0.7">70%</option>' + '<option value="0.5">50%</option>' + '<option value="0.3">30%</option>' + '</select>';
								$("#grdDSCD").jqGrid('setCell', rowIds[k], 'TYLEDV', html);
								if (rowData.TYLEDVTEMP == "") {
									$('#' + rowData.DICHVUID + "_cboTYLEDV").val(1);
								} else {
									if ('0.3'.indexOf(rowData.TYLEDVTEMP) !== -1) {
										$('#' + rowData.DICHVUID + "_cboTYLEDV").val('0.3');
									} else if ('0.5'.indexOf(rowData.TYLEDVTEMP) !== -1) {
										$('#' + rowData.DICHVUID + "_cboTYLEDV").val('0.5');
									} else if ('0.7'.indexOf(rowData.TYLEDVTEMP) !== -1) {
										$('#' + rowData.DICHVUID + "_cboTYLEDV").val('0.7');
									} else {
										$('#' + rowData.DICHVUID + "_cboTYLEDV").val(1);
									}
								}
								$('#' + rowData.DICHVUID + "_cboTYLEDV").on('change', function(e) {
									console.log('rowid.' + $(e.target).parent().parent().attr('id'));
									var rowId = $(e.target).parent().parent().attr('id');
									var rowData = $("#grdDSCD").jqGrid('getRowData', rowId);
									changeTYLEDV(rowId);
									$('#grdDSCD').jqGrid('setCell', rowId, 'TYLEDVTEMP', $('#' + rowData.DICHVUID + "_cboTYLEDV").val());
									$('#grdDSCD').jqGrid('getLocalRow', rowId).TYLEDVTEMP = $('#' + rowData.DICHVUID + "_cboTYLEDV").val();
								});
							} else {
								$("#grdDSCD").jqGrid('hideCol', 'TYLEDV');
							}
						}
						//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
						if (tyleCongKham != 0) {
							if (tyleCongKham == '1') {
								$('#cboTYLEDV_CK').val(1);
							} else if ('0.1'.indexOf(tyleCongKham) !== -1) {
								$('#cboTYLEDV_CK').val('0.1');
							} else if ('0.3'.indexOf(tyleCongKham) !== -1) {
								$('#cboTYLEDV_CK').val('0.3');
							}
						}
						//End_HaNv_01012020
					}
				});
		//Begin_HaNv_15102018: Thêm contextMenu xóa nhiều dịch vụ
		$("#grdDSCD").bind("CustomAction", function(e, act, rid) {
			if (act == 'del') {
				delServiceGrid(rid);
			}
		});
		//End_HaNv_15102018
		if (typeof opt._maubenhphamId == "undefined") {
			dGridCddv.resolve();
		} else {
			$("#grdDSCD").bind("jqGridAfterLoadComplete", function(e, rowid, orgClickEvent) {
				dGridCddv.resolve();
				// loadAll();
			});
		}
		if (typeof opt._chidinhdichvu != "undefined") {
			$('#divCLS').hide();
			$('#grdDichVu').jqGrid('setGridParam', {
				onSelectRow : function(id, selected) {
					//Begin_HaNv_31072019: Chức năng duyệt thu khác - L2PT-6731
					var ret = $("#grdDichVu").jqGrid('getRowData', id);
					if (selected && opt._loaidichvu == '20') {
						var khoaDtk = jsonrpc.AjaxJson.ajaxCALL_SP_I("CDDV.CHECK_DTK", ret.DICHVUID);
						if (khoaDtk == 0) {
							DlgUtil.showMsg(ret.TENDICHVU + ' chưa được cấu hình khoa thực hiện (bắt buộc)');
							$('#grdDichVu').jqGrid('setSelection', id, false);
							return;
						} else {
							$('#grdDichVu').jqGrid('setCell', id, 'KHOA_DTK', khoaDtk);
							$('#grdDichVu').jqGrid('getLocalRow', id).KHOA_DTK = khoaDtk;
							onSelectRow("grdDichVu", id, selected);
						}
					} else {
						onSelectRow("grdDichVu", id, selected);
					}
					//End_HaNv_31072019
					//Begin_HaNv_16012020: Cảnh báo kê giường với BN nhập viện dưới 4h - L2PT-15165
					if (selected && cbKeGiuongDuoi4h) {
						var diff_m = diffDate(currentTime, $('#hidNGAYTIEPNHAN').val(), 'DD/MM/YYYY HH:mm', 'minutes');
						var diff_h = diff_m / 60;
						if (diff_h < 4) {
							DlgUtil.showMsg("Bệnh nhân nhập viện dưới 4 tiếng, không được phép cấp giường");
						}
					}
					//End_HaNv_16012020
				}
			});
			if (typeof opt._maubenhphamId != "undefined") {
				$("#grdDichVu").bind("jqGridAfterLoadComplete", function(e, rowid, orgClickEvent) {
					dGridDv.resolve();
				});
			} else {
				$("#grdDichVu").bind("jqGridAfterLoadComplete", function(e, rowid, orgClickEvent) {
					var cbs = $("tr.ui-jqgrid-labels > th > div > input.cbox");
					cbs.attr("disabled", "disabled");
				});
			}
		} else {
			$('#cboKHOATHUCHIENID').on('change', function(e) {
				var loadPlService = '0';
				if (plDv == '1') {
					if ($('#chkAllService').is(':checked')) {
						loadPlService = '0';
					} else {
						loadPlService = '1';
					}
				} else {
					loadPlService = '0';
				}
				var sql_par = RSUtil.buildParam("", [ opt._subdeptId, 3, opt._depId, nvl($('#cboKHOATHUCHIENID').val()), opt._khambenhId, $('#txtTGCHIDINH').val(), loadPlService ]);
				if (isKeChung) {
					GridUtil.loadGridBySqlPage("grdXetNghiem", 'NTU02D009.EV014', sql_par);
				} else {
					GridUtil.loadGridBySqlPage("grdXetNghiem", lookup_sql, sql_par);
					sql_par[1].value = 4;
					GridUtil.loadGridBySqlPage("grdCDHA", lookup_sql, sql_par);
					sql_par[1].value = 5;
					GridUtil.loadGridBySqlPage("grdChuyenKhoa", "NTU02D009.EV006", sql_par);
				}
			});
			$('#divDVK').hide();
			$('#grdXetNghiem').jqGrid('setGridParam', {
				onSelectRow : function(id, selected) {
					var ret = $("#grdXetNghiem").jqGrid('getRowData', id);
					if (selected) {
						//Begin_HaNv_07082019: Cảnh báo xuất toán khi kê dv CLS - L2PT-7409
						if (ret.CANHBAO_XUATTOAN != '' && ret.CANHBAO_XUATTOAN != null) {
							DlgUtil.showMsg(ret.CANHBAO_XUATTOAN);
						}
						//End_HaNv_07082019
						var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
						for (var k = 0; k < rowIds.length; k++) {
							var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
							if (ret.DICHVUID == rowData.DICHVUID) {
								DlgUtil.showMsg(ret.TENDICHVU + ' đã được chỉ định trong phiếu');
								$('#grdXetNghiem').jqGrid('setSelection', id, false);
								return;
							}
						}
					}
					// Hanv: check khong cung thanh toan
					check_kctt("grdXetNghiem", id, selected);
					// Hanv_end: check khong cung thanh toan
				},
				gridComplete : function(id) {
					if (isColspan) {
						if (typeof $('#grdXetNghiem').getGridParam("postData").filters != "undefined") {
							var jsonSearch = JSON.parse($('#grdXetNghiem').getGridParam("postData").filters);
							if (jsonSearch.rules.length == 0) {} else {
								var trFirst = $('#gbox_grdXetNghiem').find('tr[id^="grdXetNghiemghead"]')[0];
								$(trFirst).find('td span').click();
							}
						}
					}
					$('#gbox_grdXetNghiem').find('tr[id^="grdXetNghiemghead"]').each(function(i) {
						var controlid = $(this)[0].id;
						var text = $(this).find('td b b')[0].innerHTML;
						var tagHtml = "<b onclick=" + "jQuery('#grdXetNghiem').jqGrid('groupingToggle','" + controlid + "');return false;>";
						$(this).find('td b')[0].innerHTML = tagHtml + text + "</b>";
					});
					var ids = $("#grdXetNghiem").getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						var row = $("#grdXetNghiem").jqGrid('getRowData', id);
						if (parseFloat(row.GIABHYT) <= 0) {
							$("#grdXetNghiem").jqGrid('setRowData', id, "", {
								color : 'red'
							});
						}
					}
				}
			});
			$('#grdCDHA').jqGrid('setGridParam', {
				onSelectRow : function(id, selected) {
					var ret = $("#grdCDHA").jqGrid('getRowData', id);
					if (selected) {
						//Begin_HaNv_07082019: Cảnh báo xuất toán khi kê dv CLS - L2PT-7409
						if (ret.CANHBAO_XUATTOAN != '' && ret.CANHBAO_XUATTOAN != null) {
							DlgUtil.showMsg(ret.CANHBAO_XUATTOAN);
						}
						//End_HaNv_07082019
						var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
						for (var k = 0; k < rowIds.length; k++) {
							var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
							if (ret.DICHVUID == rowData.DICHVUID) {
								DlgUtil.showMsg(ret.TENDICHVU + ' đã được chỉ định trong phiếu');
								$('#grdCDHA').jqGrid('setSelection', id, false);
								return;
							}
						}
					}
					// Hanv: check khong cung thanh toan
					check_kctt("grdCDHA", id, selected);
					// Hanv_end: check khong cung thanh toan
				},
				gridComplete : function(id) {
					if (isColspan) {
						if (typeof $('#grdCDHA').getGridParam("postData").filters != "undefined") {
							var jsonSearch = JSON.parse($('#grdCDHA').getGridParam("postData").filters);
							if (jsonSearch.rules.length == 0) {} else {
								var trFirst = $('#gbox_grdCDHA').find('tr[id^="grdCDHAghead"]')[0];
								$(trFirst).find('td span').click();
							}
						}
					}
					$('#grdCDHA').find('tr[id^="grdCDHAghead"]').each(function(i) {
						var controlid = $(this)[0].id;
						var text = $(this).find('td b b')[0].innerHTML;
						var tagHtml = "<b onclick=" + "jQuery('#grdCDHA').jqGrid('groupingToggle','" + controlid + "');return false;>";
						$(this).find('td b')[0].innerHTML = tagHtml + text + "</b>";
					});
				}
			});
			$('#grdChuyenKhoa').jqGrid('setGridParam', {
				onSelectRow : function(id, selected) {
					var ret = $("#grdChuyenKhoa").jqGrid('getRowData', id);
					if (selected) {
						//Begin_HaNv_07082019: Cảnh báo xuất toán khi kê dv CLS - L2PT-7409
						if (ret.CANHBAO_XUATTOAN != '' && ret.CANHBAO_XUATTOAN != null) {
							DlgUtil.showMsg(ret.CANHBAO_XUATTOAN);
						}
						//End_HaNv_07082019
						var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
						for (var k = 0; k < rowIds.length; k++) {
							var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
							if (ret.DICHVUID == rowData.DICHVUID) {
								DlgUtil.showMsg(ret.TENDICHVU + ' đã được chỉ định trong phiếu');
								$('#grdChuyenKhoa').jqGrid('setSelection', id, false);
								return;
							}
						}
					}
					// Hanv: check khong cung thanh toan
					check_kctt("grdChuyenKhoa", id, selected);
					// Hanv_end: check khong cung thanh toan
				},
				gridComplete : function(id) {
					if (isColspan) {
						if (typeof $('#grdChuyenKhoa').getGridParam("postData").filters != "undefined") {
							var jsonSearch = JSON.parse($('#grdChuyenKhoa').getGridParam("postData").filters);
							if (jsonSearch.rules.length == 0) {} else {
								var trFirst = $('#gbox_grdChuyenKhoa').find('tr[id^="grdChuyenKhoaghead"]')[0];
								$(trFirst).find('td span').click();
							}
						}
					}
					$('#grdChuyenKhoa').find('tr[id^="grdChuyenKhoaghead"]').each(function(i) {
						var controlid = $(this)[0].id;
						var text = $(this).find('td b b')[0].innerHTML;
						var tagHtml = "<b onclick=" + "jQuery('#grdChuyenKhoa').jqGrid('groupingToggle','" + controlid + "');return false;>";
						$(this).find('td b')[0].innerHTML = tagHtml + text + "</b>";
					});
				}
			});
			//Begin_HaNv_01072019: Hien thi cac dich vu tiem chung da danh dau - L1PT-719
			$('#grdTiemChung').jqGrid('setGridParam', {
				onSelectRow : function(id, selected) {
					var ret = $("#grdTiemChung").jqGrid('getRowData', id);
					if (selected) {
						var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
						for (var k = 0; k < rowIds.length; k++) {
							var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
							if (ret.DICHVUID == rowData.DICHVUID) {
								DlgUtil.showMsg(ret.TENDICHVU + ' đã được chỉ định trong phiếu');
								$('#grdTiemChung').jqGrid('setSelection', id, false);
								return;
							}
						}
					}
					// Hanv: check khong cung thanh toan
					check_kctt("grdTiemChung", id, selected);
					// Hanv_end: check khong cung thanh toan
				},
				gridComplete : function(id) {
					if (isColspan) {
						if (typeof $('#grdTiemChung').getGridParam("postData").filters != "undefined") {
							var jsonSearch = JSON.parse($('#grdTiemChung').getGridParam("postData").filters);
							if (jsonSearch.rules.length == 0) {} else {
								var trFirst = $('#gbox_grdTiemChung').find('tr[id^="grdTiemChungghead"]')[0];
								$(trFirst).find('td span').click();
							}
						}
					}
					$('#gbox_grdTiemChung').find('tr[id^="grdTiemChungghead"]').each(function(i) {
						var controlid = $(this)[0].id;
						var text = $(this).find('td b b')[0].innerHTML;
						var tagHtml = "<b onclick=" + "jQuery('#grdTiemChung').jqGrid('groupingToggle','" + controlid + "');return false;>";
						$(this).find('td b')[0].innerHTML = tagHtml + text + "</b>";
					});
					var ids = $("#grdTiemChung").getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						var row = $("#grdTiemChung").jqGrid('getRowData', id);
						if (parseFloat(row.GIABHYT) <= 0) {
							$("#grdTiemChung").jqGrid('setRowData', id, "", {
								color : 'red'
							});
						}
					}
				}
			});
			//End_HaNv_01072019
			$('#grdThuongDung').jqGrid('setGridParam', {
				onSelectRow : function(id, selected) {
					var ret = $("#grdThuongDung").jqGrid('getRowData', id);
					if (selected) {
						var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
						for (var k = 0; k < rowIds.length; k++) {
							var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
							if (ret.DICHVUID == rowData.DICHVUID) {
								DlgUtil.showMsg(ret.TENDICHVU + ' đã được chỉ định trong phiếu');
								$('#grdThuongDung').jqGrid('setSelection', id, false);
								return;
							}
						}
					}
					// Hanv: check khong cung thanh toan
					check_kctt("grdThuongDung", id, selected);
					// Hanv_end: check khong cung thanh toan
				},
				gridComplete : function(id) {
					if (isColspan) {
						if (typeof $('#grdThuongDung').getGridParam("postData").filters != "undefined") {
							var jsonSearch = JSON.parse($('#grdThuongDung').getGridParam("postData").filters);
							if (jsonSearch.rules.length == 0) {} else {
								var trFirst = $('#gbox_grdThuongDung').find('tr[id^="grdThuongDungghead"]')[0];
								$(trFirst).find('td span').click();
							}
						}
					}
					$('#gbox_grdThuongDung').find('tr[id^="grdThuongDungghead"]').each(function(i) {
						var controlid = $(this)[0].id;
						var text = $(this).find('td b b')[0].innerHTML;
						var tagHtml = "<b onclick=" + "jQuery('#grdThuongDung').jqGrid('groupingToggle','" + controlid + "');return false;>";
						$(this).find('td b')[0].innerHTML = tagHtml + text + "</b>";
					});
					var ids = $("#grdThuongDung").getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						var row = $("#grdThuongDung").jqGrid('getRowData', id);
						if (parseFloat(row.GIABHYT) <= 0) {
							$("#grdThuongDung").jqGrid('setRowData', id, "", {
								color : 'red'
							});
						}
					}
				}
			});
			//Begin_HaNv_12102020: Chỉ định dịch vụ theo bộ dịch vụ - L2PT-28041
			$('#grdGoiDv').jqGrid('setGridParam', {
				onSelectRow : function(id, selected) {
					var ret = $("#grdGoiDv").jqGrid('getRowData', id);
					if (selected) {
						onSelectRow("grdXetNghiem", '0', true, '1', ret.bodvid, '5');
					}
				}
			});
			//End_HaNv_12102020
			$('#grdDSCD').jqGrid('setGridParam', {
				onSelectRow : function(id, selected) {
					GridUtil.unmarkAll("grdDSCD");
					GridUtil.markRow("grdDSCD", id);
				}
			});
			$("#grdXetNghiem").bind("jqGridAfterLoadComplete", function(e, rowid, orgClickEvent) {
				var cbs = $("tr.ui-jqgrid-labels > th > div > input.cbox");
				cbs.attr("disabled", "disabled");
				dGridXn.resolve();
			});
			$("#grdCDHA").bind("jqGridAfterLoadComplete", function(e, rowid, orgClickEvent) {
				var cbs = $("tr.ui-jqgrid-labels > th > div > input.cbox");
				cbs.attr("disabled", "disabled");
				dGridCdha.resolve();
			});
			$("#grdChuyenKhoa").bind("jqGridAfterLoadComplete", function(e, rowid, orgClickEvent) {
				var cbs = $("tr.ui-jqgrid-labels > th > div > input.cbox");
				cbs.attr("disabled", "disabled");
				dGridCk.resolve();
			});
		}
		var selectPercen = 1;
		$("#grdDSCD").bind("jqGridBeforeSelectRow", function(e, rowid, orgClickEvent) {
			var rowData = $('#grdDSCD').jqGrid('getRowData', rowid);
			if (opt._loaidichvu == '13') {
				selectPercen = $('#' + rowData.DICHVUID + "_cboTYLEDV").val();
			}
		});
		$("#grdDSCD").bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
			var rowData = $('#grdDSCD').jqGrid('getRowData', rowid);
			var value = $("#grdDSCD").jqGrid('getCell', rowid, 'SOLUONG');
			var oldValue = rowData.OLDVALUE;
			if (typeof opt._chidinhdichvu == "undefined" && !isKeleCls) {
				if (isEditPttt && rowData.LOAINHOMDICHVU != 5) {
					$('#grdDSCD').jqGrid('setCell', rowid, 'SOLUONG', oldValue);
					return;
				}
			}
			if (opt._loaidichvu == '13') {
				$('#' + rowData.DICHVUID + "_cboTYLEDV").val(selectPercen);
			}
			if (((/^\d+(\.\d{1,2})?$/i.test(value) && (opt._loaidichvu == '13' || isKeleCls)) || (opt._loaidichvu != '13' && /^\d+$/.test(value))) && parseFloat(value) > 0 && value != oldValue) {
				//Begin_HaNv_23102018: Số lượng = 1 với những dịch vụ chỉ định chưa đến hạn - L2HOTRO-10799
				if (isNgaydv && opt._doituongbenhnhanId == "1") {
					var _checkDv = jsonrpc.AjaxJson.getOneValue("CHECKDV_CHANNGAY", [ {
						"name" : "[0]",
						"value" : rowData.DICHVUID
					} ]);
					if (_checkDv !== "0") {
						DlgUtil.showMsg(rowData.TENDICHVU + ' có thiết lập hạn chỉ định tiếp theo. Số lượng mỗi lần chỉ định chỉ bằng 1');
						$('#grdDSCD').jqGrid('setCell', rowid, 'SOLUONG', 1);
						return;
					}
				}
				//End_HaNv_23102018
				//Begin_HaNv_10072019: Canh bao khi chi dinh dv xet nghiem so luong > 1 - L2PT-6748
				if (cbSoLuongXN && rowData.LOAINHOMDICHVU == '3' && value > 1) {
					DlgUtil.showMsg('Dịch vụ xét nghiệm vừa được chỉ định số lượng > 1!');
				}
				//End_HaNv_10072019
				//Begin_HaNv_13062020: Cảnh báo khi sửa số lượng chỉ định CLS > 1 - L2PT-22791
				else if (value > 1) {
					if (rowData.LOAINHOMDICHVU == '3' && (cbSoLuong == 1 || cbSoLuong == 4 || cbSoLuong == 5)) {
						DlgUtil.showMsg('Dịch vụ xét nghiệm vừa được chỉ định số lượng > 1!');
					} else if (rowData.LOAINHOMDICHVU == '4' && (cbSoLuong == 2 || cbSoLuong == 4 || cbSoLuong == 5)) {
						DlgUtil.showMsg('Dịch vụ CĐHA vừa được chỉ định số lượng > 1!');
					} else if (rowData.LOAINHOMDICHVU == '5' && (cbSoLuong == 3 || cbSoLuong == 5)) {
						DlgUtil.showMsg('Dịch vụ PTTT vừa được chỉ định số lượng > 1!');
					}
				}
				//End_HaNv_13062020
				//Begin_HaNv_22102019: Cho phép nhập ngày giường của BN số thập phân lẻ đến 0.25 - L1PT-1936
				if (opt._loaidichvu == '13' && !isNhapGiuongTp) {
					if (isKeGiuongVd2) {
						if (parseFloat(value) % 0.25 !== 0) {
							DlgUtil.showMsg('Thông tin số lượng giường chưa chính xác, yêu cầu nhập lại');
							$('#grdDSCD').jqGrid('setCell', rowid, 'SOLUONG', oldValue);
						} else {
							loadChange(oldValue, value, rowid);
							$('#grdDSCD').jqGrid('setCell', rowid, 'OLDVALUE', value);
						}
					} else if (isKeGiuongTheoNgay && parseFloat(value) != 1 && parseFloat(value) != 0.5 && parseFloat(value) != 0.7) {
						//Kê theo ngày: Chỉ cho phép kê giường với số lượng 0.5, 0.7 hoặc 1
						$('#grdDSCD').jqGrid('setCell', rowid, 'SOLUONG', oldValue);
					} else if (parseFloat(value) < 1 && parseFloat(value) != 0.5 && parseFloat(value) != 0.7) {
						$('#grdDSCD').jqGrid('setCell', rowid, 'SOLUONG', oldValue);
					} else {
						loadChange(oldValue, value, rowid);
						$('#grdDSCD').jqGrid('setCell', rowid, 'OLDVALUE', value);
					}
				} else {
					if (typeof opt._chidinhdichvu == "undefined") {
						if (!isKeleCls) {
							$('#grdDSCD').jqGrid('setCell', rowid, 'SOLUONG', oldValue);
						} else {
							//Begin_HaNv_20082020: Chỉ cho phép kê lẻ CLS với số lượng nguyên dương - L2PT-25936
							if (slClsNguyenDuong && !/^\d+$/.test(value)) {
								$('#grdDSCD').jqGrid('setCell', rowid, 'SOLUONG', oldValue);
								DlgUtil.showMsg('Chỉ cho phép kê lẻ CLS với số lượng nguyên dương!');
							} else {
								loadChange(oldValue, value, rowid);
								$('#grdDSCD').jqGrid('setCell', rowid, 'OLDVALUE', value);
							}
							//End_HaNv_20082020
						}
					} else {
						loadChange(oldValue, value, rowid);
						$('#grdDSCD').jqGrid('setCell', rowid, 'OLDVALUE', value);
					}
				}
				//End_HaNv_22102019
			} else {
				$('#grdDSCD').jqGrid('setCell', rowid, 'SOLUONG', oldValue);
			}
		});
		$.when(dGridCddv.promise(), dGridXn.promise(), dGridCdha.promise(), dGridCk.promise()).then(function() {
			if (typeof opt._maubenhphamId != "undefined") {
				var _gridIdLoop = "";
				if (opt._loaiphieu == '1') {
					_gridIdLoop = "grdXetNghiem";
				} else if (opt._loaiphieu == '2') {
					_gridIdLoop = "grdCDHA";
				} else if (opt._loaiphieu == '5') {
					_gridIdLoop = "grdChuyenKhoa";
				}
				loadDefaultGrid(_gridIdLoop);
			} else {
				//Begin_HaNv_23102018: Chan cung vươt tien tam ung-ap dung cho BN kham benh - L2HOTRO-10404
				if (flagMsgMoney && flagMsg) {
					if (isShowMsgVuotTienKB) {
						DlgUtil.showMsg('Bệnh nhân có chi phí phát sinh lớn hơn tiền tạm ứng', function() {
							$('#gbox_grdXetNghiem').find('.ui-search-input').find('#gs_TENDICHVU').focus();
						});
						flagMsg = false;
						if (isChancbTu && isQn != 4 && isHtvv != 2) {//BN là quân nhân hoặc BN cấp cứu -> không chặn cứng
							$('#btnLuuIn').hide();
							$('#btnLuu').hide();
						}
					} else if (isShowMsgVuotTien) {
						DlgUtil.showMsg('Bệnh nhân có chi phí phát sinh lớn hơn tiền tạm ứng', function() {
							$('#gbox_grdXetNghiem').find('.ui-search-input').find('#gs_TENDICHVU').focus();
						});
						flagMsg = false;
						if (isChancbTu && isQn != 4 && isHtvv != 2) {//BN là quân nhân hoặc BN cấp cứu -> không chặn cứng
							$('#btnLuuIn').hide();
							$('#btnLuu').hide();
						}
					}
				}
				//End_HaNv_23102018
			}
		});
		$.when(dGridCddv.promise(), dGridDv.promise()).then(function() {
			loadDefaultGrid("grdDichVu");
		});
		$("#btnLuu").bindOnce("click", function() {
			isLuuTiep = false;
			_checkThoiGianCD('0');//HaNv_23032020: L2PT-18389
		}, 1000);
		$("#btnLuuIn").bindOnce("click", function() {
			isLuuTiep = false;
			_checkThoiGianCD('1');//HaNv_23032020: L2PT-18389
		}, 1000);
		$("#btnLuuTiep").bindOnce("click", function() {
			isLuuTiep = true;
			if (!isCbQuaTg && !compareDate($('#txtTGCHIDINH').val().trim(), currentTime, 'DD/MM/YYYY HH:mm:ss')) {
				DlgUtil.showConfirm("Thời gian chỉ định lớn hơn thời gian hiện tại. Bạn có tiếp tục?", function(flag) {
					if (flag) {
						saveData('0');
					}
				});
			} else {
				saveData('0');
			}
		}, 1000);
		//START L2HOTRO-12397
		$("#btnLuuXem").bindOnce("click", function() {
			isLuuTiep = true;
			if (!isCbQuaTg && !compareDate($('#txtTGCHIDINH').val().trim(), currentTime, 'DD/MM/YYYY HH:mm:ss')) {
				DlgUtil.showConfirm("Thời gian chỉ định lớn hơn thời gian hiện tại. Bạn có tiếp tục?", function(flag) {
					if (flag) {
						isSaveView = true;
						saveData('1');
					}
				});
			} else {
				isSaveView = true;
				saveData('1');
			}
		}, 1000);
		//END L2HOTRO-12397
		$("#btnCLEARCHANDOANKT").on("click", function() {
			$('#txtCHANDOAN_KT').val('');
		});
		$("#btnCLEARXN").on("click", function() {
			$('#cboNHOMXETNGHIEMID').val('-1');
			$('#cboNHOMXETNGHIEMID').change();
		});
		$("#btnCLEARCDHA").on("click", function() {
			$('#cboNHOMCDHAID').val('-1');
			$('#cboNHOMCDHAID').change();
		});
		$("#btnCLEARCK").on("click", function() {
			$('#cboCHUYENKHOAID').val('-1');
			$('#cboCHUYENKHOAID').change();
		});
		//Begin_HaNv_13112019: Cho phép tìm kiếm bác sỹ trong danh sách thay vì chọn - L2PT-11245
		$("#btnCLEARBACSIID").on("click", function() {
			cboBacsiVal = '';
			$("#txtTKBACSI").val("");
			var option = $('<option value="">--Lựa chọn--</option>');
			$("#cboBACSIID").empty();
			$("#cboBACSIID").append(option);
		});
		//End_HaNv_13112019
		//Begin_HaNv_17012020: Cho phép chọn Bác Sĩ bên ngoài phòng khám để tính hoa hồng - L2PT-15119
		$("#btnCLEARBACSINGOAIPK").on("click", function() {
			cboBacsiNgoaiPk = '';
			$("#txtBACSINGOAIPK").val("");
			var option = $('<option value="">--Lựa chọn--</option>');
			$("#cboBACSINGOAIPK").empty();
			$("#cboBACSINGOAIPK").append(option);
		});
		//End_HaNv_17012020
		//Begin_HaNv_04072020: Bổ sung thông tin người lấy mẫu, thời gian lấy mẫu khi chỉ định - L2PT-23017
		$("#btnCLEARNGUOILAYMAU").on("click", function() {
			$("#txtNGUOILAYMAU").val("");
			var option = $('<option value="">--Lựa chọn--</option>');
			$("#cboNGUOILAYMAU").empty();
			$("#cboNGUOILAYMAU").append(option);
		});
		//End_HaNv_04072020
		$("#btnEDITCHANDOANKT").on("click", function(e) {
			var myVar = {
				benhphu : $('#txtCHANDOAN_KT').val(),
				chandoan_kt_bd : $('#hidCHANDOAN_KEMTHEO_BD').val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 500);
			DlgUtil.open("dlgBPKT");
		});
		EventUtil.setEvent("chinhsua_benhphu", function(e) {
			DlgUtil.close("dlgBPKT");
			if (e.benhphu.indexOf($("#cboMACHANDOAN").val() + "-") > -1) {
				DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
				return false;
			} else {
				$('#hidCHANDOAN_KEMTHEO_BD').val(e.chandoanKtBd);
				$('#txtCHANDOAN_KT').val(e.benhphu);
			}
		});
		$("#grdDSCD").bind("jqGridAfterInsertRow", function(e, rowid, orgClickEvent) {
			$(".jqgrow", '#grdDSCD').contextMenu('contextMenu', {
				bindings : {
					'delService' : function(t) {
						var idTemp = $(t).attr("id");
						delServiceGrid(idTemp);
					},
					'changeBHYT' : function(t) {
						changeObject($(t).attr("id"), '1');
					},
					'changeBHYTYC' : function(t) {
						changeObject($(t).attr("id"), '2');
					},
					//Begin_HaNv_04052020: Chuyển đổi khám sức khỏe theo đoàn - L2PT-20170
					'changeKSK' : function(t) {
						changeObject($(t).attr("id"), '30');
					},
					//End_HaNv_04052020
					'changeVP' : function(t) {
						changeObject($(t).attr("id"), '4');
					},
					'changeVPYC' : function(t) {
						changeObject($(t).attr("id"), '11');
					},
					'changeYC' : function(t) {
						changeObject($(t).attr("id"), '6');
					},
					'changeDK' : function(t) {
						changeObject($(t).attr("id"), '5');
					},
					'changeHPCK' : function(t) {
						changeObject($(t).attr("id"), '7');
					},
					'changeHPPTTT' : function(t) {
						changeObject($(t).attr("id"), '8');
					},
					'changeHPK' : function(t) {
						changeObject($(t).attr("id"), '9');
					},
					'changeTTK' : function(t) {
						changeObject($(t).attr("id"), '10');
					},
					//Begin_HaNv_16052018: them contextMenu Chuyen mien phi HISL2TK-468
					'changeMP' : function(t) {
						changeObject($(t).attr("id"), '15');
					},
					//End_HaNv_16052018
					'changeTempData' : function(t) {
						changeTempData($(t).attr("id"));
					},
					'changeNcData' : function(t) {
						changeNcData($(t).attr("id"));
					},
					'changeDieuKien' : function(t) {
						if (opt._doituongbenhnhanId == '1') {
							changeDieuKien($(t).attr("id"));
						} else {
							return DlgUtil.showMsg("Bệnh nhân không phải BHYT không được chọn");
						}
					},
					//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
					'changeHopDong' : function(t) {
						changeObject($(t).attr("id"), 'HD');
					}
				//End_HaNv_25072020
				},
				onContextMenu : function(event, menu) {
					var rowId = $(event.target).parent("tr").attr("id");
					var grid = $('#grdDSCD');
					grid.setSelection(rowId);
					GridUtil.unmarkAll("grdDSCD");
					GridUtil.markRow("grdDSCD", rowId);
					return true;
				},
			});
		});
		$("#btnHuy").on("click", function(e) {
			parent.DlgUtil.close("divDlgDichVu");
		});
		$("#btnPhieuMau").on("click", function(e) {
			var myVar = {
				loainhom_mau : _loainhom_mau
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgPhieuMauCDDV", "divPhieuMauCDDV", "manager.jsp?func=../ngoaitru/NGT02K041_PhieuChiDinhMau", myVar, "Phiếu chỉ định mẫu", 1200, 550);
			DlgUtil.open("dlgPhieuMauCDDV");
		});
		$("#btnPhacDo").on("click", function(e) {
			var myVar = {
				machandoan : $('#cboMACHANDOAN').val(),
				loaidv : 0
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgPhacDoMau", "divPhieuMauCDDV", "manager.jsp?func=../noitru/NTU02D075_PhacDoMau", myVar, "Phác đồ mẫu", 1200, 550);
			DlgUtil.open("dlgPhacDoMau");
		});
		$("#btnSaveTemp").bindOnce("click", function() {
			if ($("#txtTEXT_TEMP").val().trim() != "")
				doSavePhieuMau();
			else {
				$("#txtTEXT_TEMP").focus();
				return DlgUtil.showMsg("Bạn phải nhập tên phiếu mẫu!");
			}
		});
		EventUtil.setEvent("temp_presc_success", function(e) {
			var param = [ e.id, opt._khambenhId, $('#txtTGCHIDINH').val() ];
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K016.EV001", param.join('$'));
			if (typeof ret != 'undefined' && ret != '') {
				DlgUtil.showConfirm(ret + " đã được chỉ định trong ngày, có đồng ý load mẫu dịch vụ?", function(flag) {
					if (flag) {
						onSelectRow("grdXetNghiem", '0', true, '1', e.id);
					} else {
						DlgUtil.close("dlgPhieuMauCDDV");
						return;
					}
				});
			} else {
				onSelectRow("grdXetNghiem", '0', true, '1', e.id);
			}
			DlgUtil.close("dlgPhieuMauCDDV");
		});
		EventUtil.setEvent("pddt_presc_success", function(e) {
			var param = [ e.id, opt._khambenhId, $('#txtTGCHIDINH').val() ];
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D075.EV003", param.join('$'));
			if (typeof ret != 'undefined' && ret != '') {
				DlgUtil.showConfirm(ret + " đã được chỉ định trong ngày, có đồng ý load phác đồ mẫu không có chỉ định trùng?", function(flag) {
					if (flag) {
						if (isAddKHA) {
							addDichVu('1', e.id, '2');
						} else {
							onSelectRow("grdXetNghiem", '0', true, '1', e.id, '2');
						}
					} else {
						DlgUtil.close("dlgPhacDoMau");
						return;
					}
				});
			} else {
				onSelectRow("grdXetNghiem", '0', true, '1', e.id, '2');
			}
			DlgUtil.close("dlgPhacDoMau");
		});
		function doSavePhieuMau() {
			var jsonGridData = $("#grdDSCD").jqGrid('getRowData');
			if (jsonGridData.length > 0) {
				_jsonPhieuMau.TEN_MAUPHIEU = $("#txtTEXT_TEMP").val().trim();
				_jsonPhieuMau.KHAMBENHID = khambenhId;
				_jsonPhieuMau.LOAINHOM_MAU = _loainhom_mau;
				_jsonPhieuMau.DS_DICHVU = jsonGridData;
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K016.LUUMAU", JSON.stringify(_jsonPhieuMau));
				if (ret == 1) {
					DlgUtil.showMsg("Tạo phiếu mẫu thành công!");
				} else if (ret == 0) {
					$("#txtTEXT_TEMP").focus();
					DlgUtil.showMsg("Đã tồn tại tên phiếu mẫu này!");
				} else
					DlgUtil.showMsg("Tạo phiếu mẫu không thành công!");
			} else
				return DlgUtil.showMsg("Bạn phải chọn dịch vụ chỉ định trước!");
		}
		//START-- L2K74TW-605 -- hongdq
		$('#txtTGSEARCH').on('change', function(e) {
			$('#calSearch').prop('title', $('#txtTGSEARCH').val());
			sql_par = RSUtil.buildParam("", [ _opt._khambenhId, "4", $('#txtTGSEARCH').val() + ' 00:00:00', $('#txtTGSEARCH').val() + ' 23:59:59' ]);
			ComboUtil.getComboTag("cboMAUBENHPHAMID", "COM.PHIEUDIEUTRI_1", sql_par, '', 'sql', '');
		});
		$("#btnCancelSearch").click(function() {
			sql_par = RSUtil.buildParam("", [ _opt._khambenhId, "4" ]);
			ComboUtil.getComboTag("cboMAUBENHPHAMID", "COM.PHIEUDIEUTRI", sql_par, _opt._phieudieutriId == null ? '' : _opt._phieudieutriId, {
				value : '-1',
				text : 'Chưa có phiếu điều trị'
			}, 'sql', '', function() {});
		});
		//END-- L2K74TW-605 -- hongdq
		// laphm: cau hinh dich vu thuong dung
		$("#btnAddThuongDung").on("click", function(e) {
			var par = {
				khambenhId : opt._khambenhId,
				thoigianchidinh : $('#txtTGCHIDINH').val(),
				type : 0
			};
			dlgPopup = DlgUtil.buildPopupUrl("CauHinhDichVuThuongDung", "divCauHinhDichVu", "manager.jsp?func=../ngoaitru/NGT02K100_ChDichVuTd", par, "Cấu hình dịch vụ thường dùng", 1200, 480);
			dlgPopup.open();
		});
		EventUtil.setEvent("CauHinhDichVuThuongDung_onClose", function(e) {
			// load lai grid thuong dung
			var sql_par_td = RSUtil.buildParam("", [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), '0' ]);
			GridUtil.loadGridBySqlPage("grdThuongDung", 'NTU02D009.EV018', sql_par_td);
		});
		//Begin_HaNv_25052020: Thêm btn xem lịch sử BA - L2PT-21525
		$("#btnLichSuBA").on("click", function() {
			paramInput = {
				benhnhanId : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn", paramInput, "Lịch sử bệnh án", 1290, 590);
			DlgUtil.open("dlgLichSuBenhAn");
		});
		//End_HaNv_25052020
	}
	function callbackDichVu(_ui) {
		$("#txtMADICHVU").val(_ui.MADICHVU);
		$("#txtTENDICHVU").val(_ui.TENDICHVU);
		$("#cboPHONGTH_KHA").empty();
		var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), _ui.DICHVUID, '2' ];
		var dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
		if (dataArr == null || dataArr.length <= 0) {
			DlgUtil.showMsg('Chưa cấu hình phòng thực hiện cho dịch vụ');
			return;
		}
		var ret = dataArr[0];
		if (ret.PHONGID != null && ret.PHONGID != '') {
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV003", ret.PHONGID);
			if (data_ar != null && data_ar.length > 0) {
				var option = '';
				var checkExistDept = false;
				var checkEqualDept = false;
				//Begin_HaNv_12102020: Check ưu tiên phòng thực hiện của khoa hiện tại trong DS phòng - L2PT-27477
				var optionExistDept = '';
				for (var i = 0; i < data_ar.length; i++) {
					if (data_ar[i].PHONGID == opt._subdeptId_login) {
						checkExistDept = true;
						optionExistDept = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
					}
					//Begin_HaNv_12102020: Check ưu tiên phòng thực hiện của khoa hiện tại trong DS phòng - L2PT-27477
					else if (data_ar[i].KHOAID == opt._depId) {
						option = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>' + option;
					}
					//End_HaNv_12102020
					else {
						option = option + '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
					}
				}
				if (checkExistDept) {
					if (_ui.LOAINHOMDICHVU == '5') {
						option = optionExistDept + option;
					} else {
						if (_ui.HAS_ORDER == '-1') {
							option = optionExistDept + option;
						} else {
							option = option + optionExistDept;
						}
					}
				}
				//Begin_HaNv_08092020: Ẩn phòng chỉ định vào danh sách phòng thực hiện DV PTTT - L2PT-26552 - L2PT-29789
				if (!checkExistDept && _ui.LOAINHOMDICHVU == '5' && !isHidePcd) {
					option = option + '<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>';
				}
				//End_HaNv_08092020
				//End_HaNv_12102020
				$('#cboPHONGTH_KHA').append(option);
			}
		} else {
			$('#cboPHONGTH_KHA').append('<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>');
		}
	}
	function checkAddDichVu() {
		var ret = _objDichVu;
		//Begin_HaNv_07082019: Cảnh báo xuất toán khi kê dv CLS - L2PT-7409
		if (ret.CANHBAO_XUATTOAN != '' && ret.CANHBAO_XUATTOAN != null) {
			DlgUtil.showMsg(ret.CANHBAO_XUATTOAN);
		}
		//End_HaNv_07082019
		var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
		for (var k = 0; k < rowIds.length; k++) {
			var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
			if (ret.DICHVUID == rowData.DICHVUID) {
				DlgUtil.showMsg(ret.TENDICHVU + ' đã được chỉ định trong phiếu');
				return;
			}
		}
		check_kctt("grdXetNghiem", 1, true);
	}
	function addDichVu(template, templateid, fullTemp) {
		var rowCount = $("#grdDSCD").getGridParam("reccount");
		var ret = _objDichVu;
		var _insurance = 0;
		var _insurance_full = 0;
		var _insurance_final = 0;
		var dataArr = [];
		var soluong = parseFloat(nvl($('#txtSOLUONG_KHA').val(), 1));
		if (template == '1') {
			if (fullTemp == '1') {
				var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), templateid, '3' ];
				dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
			} else if (fullTemp == '2') {
				var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), templateid, '4' ];
				dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
			}
			//Begin_HaNv_12102020: Chỉ định dịch vụ theo bộ dịch vụ - L2PT-28041
			else if (fullTemp == '5') {
				var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), templateid, '5' ];
				dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
			}
			//End_HaNv_12102020
			else {
				var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), templateid, '1' ];
				dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
				// Begin_HaNv_08042019: Check bo dich vu co dieu kien tu phieu mau voi BN BHYT - L1PT-391
				if (isDvDieuKien && opt._doituongbenhnhanId == "1") {
					var dataArrAllow = [];
					var listDv = '';
					for (var h1 = 0; h1 < dataArr.length; h1++) {
						var resultDataDk = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K016.EV008", dataArr[h1].DICHVUID);
						if (resultDataDk > 0) {
							listDv = (listDv == '' ? '' : listDv + '; ') + dataArr[h1].TENDICHVU;
						} else {
							dataArrAllow.push(dataArr[h1]);
						}
					}
					if (listDv !== '') {
						DlgUtil.showMsg('Các dịch vụ trong phiếu mấu: ' + listDv + ' là các dịch vụ có điều kiện. Không được load ra từ phiếu mẫu!');
					}
					dataArr = dataArrAllow;
				}
				// End_HaNv_08042019
			}
			if (isNgaydv && opt._doituongbenhnhanId == "1") {
				for (var i8 = 0; i8 < dataArr.length; i8++) {
					//Begin_HaNv_09072019: check hạn sử dụng dịch vụ - L2PT-6517
					var par = [ dataArr[i8].DICHVUID, $('#txtMA_BHYT').val(), $('#hidBENHNHANID').val(), $('#txtTGCHIDINH').val().substr(0, 10) ];
					//End_HaNv_09072019
					var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D009.EV017", par.join('$'));
					if (resultCheck != '0') {
						DlgUtil.showMsg(dataArr[i8].TENDICHVU + ' đã được sử dụng vào ngày ' + resultCheck + '. Chưa đến hạn sử dụng tiếp theo của BHYT');
						return;
					}
				}
			}
		} else {
			if (ret.LDV == 1) {
				var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), ret.NHOMDICHVUID, '0' ];
				dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
				if (dataArr == null || dataArr.length <= 0) {
					DlgUtil.showMsg('Chưa cấu hình phòng thực hiện cho dịch vụ');
					return;
				}
			} else {
				var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), ret.DICHVUID, '2' ];
				dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
				if (dataArr == null || dataArr.length <= 0) {
					DlgUtil.showMsg('Chưa cấu hình phòng thực hiện cho dịch vụ');
					return;
				}
			}
		}
		if (dataArr == null || dataArr.length <= 0) {
			return;
		}
		var dataArrSub = [];
		var rowIdsSub = $('#grdDSCD').jqGrid('getDataIDs');
		for (var n2 = 0; n2 < dataArr.length; n2++) {
			var checkSub = false;
			for (var n1 = 0; n1 < rowIdsSub.length; n1++) {
				var rowDataSub = $('#grdDSCD').jqGrid('getRowData', rowIdsSub[n1]);
				if (rowDataSub.DICHVUID == dataArr[n2].DICHVUID) {
					checkSub = true;
					break;
				}
			}
			if (checkSub == false) {
				dataArrSub.push(dataArr[n2]);
				var soluongDv = 1;
				if (isShowNumService) {
					soluongDv = parseInt($('#txtSOLUONGDICHVU').val().trim());
				} else if (isShowSoLuongCls) {
					soluongDv = parseInt($('#txtSOLUONGDICHVUCLS').val().trim());
				}
				for (var n3 = 0; n3 < soluongDv - 1; n3++) {
					dataArrSub.push(dataArr[n2]);
				}
			}
		}
		for (var i12 = 0; i12 < dataArr.length; i12++) {
			ret = dataArr[i12];
			//Begin_HaNv_04042018: Chan/bo chan ngay dich vu BH. Neu ko chan co the chi dinh tiep DV theo gia vien phi
			if (isNgaydv && opt._doituongbenhnhanId == "1") {
				//Begin_HaNv_09072019: check hạn sử dụng dịch vụ - L2PT-6517
				var par = [ dataArr[i12].DICHVUID, $('#txtMA_BHYT').val(), $('#hidBENHNHANID').val(), $('#txtTGCHIDINH').val().substr(0, 10) ];
				//End_HaNv_09072019
				var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D009.EV017", par.join('$'));
				if (resultCheck != '0') {
					if (isNgaydvVienphi) {
						ret.GIABHYT = 0;
						DlgUtil.showMsg(ret.TENDICHVU + ' sẽ được chỉ định với giá viện phí do chưa đến hạn sử dụng tiếp theo của BHYT');
					} else {
						DlgUtil.showMsg(ret.TENDICHVU + ' đã được sử dụng vào ngày ' + resultCheck + '. Chưa đến hạn sử dụng tiếp theo của BHYT');
						return;
					}
				}
			}
			//End_HaNv_04042018
			if (opt._doituongbenhnhanId == '1' && parseFloat(ret.GIABHYT) > 0) {
				tongbh = parseFloat(tongbh) + parseFloat(ret.GIABHYT) * soluong;
				if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
					_insurance = nvl($('#hidQUYENLOITHE').val(), $('#txtQUYENLOI').val());
				} else {
					_insurance = nvl($('#hidQUYENLOI').val(), $('#txtQUYENLOI').val());
				}
				_insurance_full = nvl($('#hidQUYENLOI').val(), $('#txtQUYENLOI').val());
				_insurance_final = nvl($('#hidQUYENLOITHE').val(), $('#txtQUYENLOI').val());
				if (rowCount != 0 && _insurance_bf != _insurance) {
					var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
					for (var i1 = 0; i1 < rowIds.length; i1++) {
						var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[i1]);
						if (rowData.LOAIDOITUONG == '1' || rowData.LOAIDOITUONG == '2') {
							if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFINAL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFINAL;
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFINAL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFINAL;
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFINAL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFINAL;
								loadPay(0, parseFloat(rowData.BHYT_TRAFINAL) - parseFloat(rowData.BHYT_TRA));
							} else {
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFULL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFULL;
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFULL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFULL;
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFULL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFULL;
								loadPay(0, parseFloat(rowData.BHYT_TRAFULL) - parseFloat(rowData.BHYT_TRA));
							}
						}
					}
				}
				_insurance_bf = _insurance;
			}
			var _payStart = 0;
			var _payIns = 0;
			var _payEnd = 0;
			var _benhPhamGroup = '0';
			var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
			var r = null;
			var r_full = null;
			var r_final = null;
			var objTinhTien = new Object();
			if (opt._loaidichvu == '19') {
				objTinhTien.DOITUONGBENHNHANID = '2';
			} else {
				if (isCdNgoaigio) {
					var dayCd = $('#txtTGCHIDINH').val().substring(0, 10);
					var timeSbd = dayCd + " " + isHcSangBd;
					var timeSkt = dayCd + " " + isHcSangKt;
					var timeCbd = dayCd + " " + isHcChieuBd;
					var timeCkt = dayCd + " " + isHcChieuKt;
					var checkDay = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC.CHECK.NL", dayCd + "$" + '0');
					if (checkDay > 0) {
						objTinhTien.DOITUONGBENHNHANID = '3';
					} else {
						checkDay = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC.CHECK.NL", dayCd + "$" + '1');
						if (checkDay > 0) {
							if ((!compareDate($('#txtTGCHIDINH').val(), timeSbd, 'DD/MM/YYYY HH:mm:ss') && compareDate($('#txtTGCHIDINH').val(), timeSkt, 'DD/MM/YYYY HH:mm:ss')) ||
									(!compareDate($('#txtTGCHIDINH').val(), timeCbd, 'DD/MM/YYYY HH:mm:ss') && compareDate($('#txtTGCHIDINH').val(), timeCkt, 'DD/MM/YYYY HH:mm:ss'))) {
								objTinhTien.DOITUONGBENHNHANID = opt._doituongbenhnhanId;
							} else {
								objTinhTien.DOITUONGBENHNHANID = '3';
							}
						} else {
							if (moment(dayCd, 'DD/MM/YYYY HH:mm:ss').isoWeekday() == 6 || moment(dayCd, 'DD/MM/YYYY HH:mm:ss').isoWeekday() == 7) {
								objTinhTien.DOITUONGBENHNHANID = '3';
							} else {
								if ((!compareDate($('#txtTGCHIDINH').val(), timeSbd, 'DD/MM/YYYY HH:mm:ss') && compareDate($('#txtTGCHIDINH').val(), timeSkt, 'DD/MM/YYYY HH:mm:ss')) ||
										(!compareDate($('#txtTGCHIDINH').val(), timeCbd, 'DD/MM/YYYY HH:mm:ss') && compareDate($('#txtTGCHIDINH').val(), timeCkt, 'DD/MM/YYYY HH:mm:ss'))) {
									objTinhTien.DOITUONGBENHNHANID = opt._doituongbenhnhanId;
								} else {
									objTinhTien.DOITUONGBENHNHANID = '3';
								}
							}
						}
					}
				} else {
					objTinhTien.DOITUONGBENHNHANID = opt._doituongbenhnhanId;
				}
			}
			if (opt._loaidichvu == '14' && isTranspost) {
				objTinhTien.MUCHUONG = $('#hidTYLE_THEBHYT').val();
			} else {
				objTinhTien.MUCHUONG = parseFloat(_insurance);
			}
			if (isTinhXang && opt._loaidichvu == '14') {
				objTinhTien.GIABHYT = parseFloat(isGiaxang);
				objTinhTien.GIAND = parseFloat(isGiaxang);
				objTinhTien.GIADV = parseFloat(isGiaxang);
				objTinhTien.SOLUONG = nvl(ret.SO_LIT, '1');
			} else {
				objTinhTien.GIABHYT = parseFloat(ret.GIABHYT);
				objTinhTien.GIAND = parseFloat(ret.GIANHANDAN);
				objTinhTien.GIADV = parseFloat(ret.GIADICHVU);
				objTinhTien.SOLUONG = soluong;
			}
			objTinhTien.CAUHINH_CGDV = config_cdgdv;
			objTinhTien.GIATRANBH = parseFloat(ret.DICHVU_BHYT_DINHMUC);
			objTinhTien.GIANN = 0;
			objTinhTien.DOITUONGCHUYEN = 0;
			objTinhTien.GIADVKTC = opt._doituongbenhnhanId == '1' && parseFloat(ret.GIA_DVC) > 0 ? ret.GIA_DVC : 0;
			objTinhTien.MANHOMBHYT = ret.MANHOM_BHYT;
			objTinhTien.CANTRENDVKTC = $('#hidCANTRENKTC').val();
			objTinhTien.THEDUTHOIGIAN = $('#hidBHFULL').val();
			objTinhTien.DUOCVANCHUYEN = $('#hidDUOCVC').val();
			objTinhTien.TYLETHUOCVATTU = 100;
			objTinhTien.NHOMDOITUONG = $('#hidNHOMDOITUONG').val();
			objTinhTien.NGAYHANTHE = $('#hidNGAYHANTHE').val();
			objTinhTien.NGAYDICHVU = $('#txtTGCHIDINH').val().substr(0, 10);
			objTinhTien.TYLE_MIENGIAM = $('#hidTYLEMIENGIAM').val();
			objTinhTien.NGAYGIAHANTHE = config_hanthe;
			if (objTinhTien.DOITUONGCHUYEN == '0' || objTinhTien.DOITUONGCHUYEN == '1' || objTinhTien.DOITUONGCHUYEN == '2') {
				objTinhTien.DAUTHE = $('#txtMA_BHYT').val().substring(0, 3);
				objTinhTien.CHUOIDAUTHE = cf_dauthe;
			}
			if (typeof opt._chidinhdichvu == "undefined") {
				if ((_opt.hospitalId == '1217' || _opt.hospitalId == '1218' || _opt.hospitalId == '1219' || _opt.hospitalId == '1220' || _opt.hospitalId == '1221' || _opt.hospitalId == '1222' ||
						_opt.hospitalId == '1223' || _opt.hospitalId == '1224' || _opt.hospitalId == '1225' || _opt.hospitalId == '1226' || _opt.hospitalId == '1227') &&
						objTinhTien.DOITUONGBENHNHANID == '1') {
					var dauthebhyt = $('#txtMA_BHYT').val().substring(2, 5);
					var dauthebhyt2 = $('#txtMA_BHYT').val().substring(0, 2);
					var dauthebhyt3 = $('#txtMA_BHYT').val().substring(0, 5);
					if (dauthebhyt2 == 'TA' || (dauthebhyt != '497' && dauthebhyt3 != 'QN597' && dauthebhyt != '297') || isTlQy15) {
						objTinhTien.TYLEDV = 0.7;
					}
				}
			}
			//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
			if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
				objTinhTien.TYLEDV = typeof ($('#cboTYLEDV_CK').val()) != "undefined" ? $('#cboTYLEDV_CK').val() : 1;
			}
			//End_HaNv_01012020
			//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
			if ($('#hidLOAIKHAM').val() != '' && $('#hidLOAIKHAM').val() != '0') {
				objTinhTien.DOITUONGCHUYEN = '4';
				objTinhTien.LOAIKHAM = $('#hidLOAIKHAM').val();
			}
			//End_HaNv_25072020
			r = vienphi.tinhtien_dichvu(objTinhTien);
			if (r.bh_tra == -1 && r.nd_tra == -1 && r.tong_cp == -1) {
				DlgUtil.showMsg('Giá tiền dịch vụ của bệnh nhân không thể bằng 0');
				return;
			}
			var objTinhTienFull = $.extend({}, objTinhTien);
			objTinhTienFull.MUCHUONG = _insurance_full;
			r_full = vienphi.tinhtien_dichvu(objTinhTienFull);
			var objTinhTienFinal = $.extend({}, objTinhTien);
			if (opt._loaidichvu == '14' && isTranspost) {
				objTinhTienFinal.MUCHUONG = $('#hidTYLE_THEBHYT').val();
			} else {
				objTinhTienFinal.MUCHUONG = _insurance_final;
			}
			r_final = vienphi.tinhtien_dichvu(objTinhTienFinal);
			_payStart = r.tong_cp;
			_payIns = r.bh_tra;
			_payEnd = r.nd_tra;
			if (ret.LOAINHOMDICHVU == '3') {
				_benhPhamGroup = '1';
			} else {
				_benhPhamGroup = ret.LOAINHOMDICHVU == '4' ? '2' : '5';
			}
			var tendichvu_tmp = opt._loaidichvu == '12' ? ' (' + ret.MADICHVU + ') ' + ret.TENDICHVU : ret.TENDICHVU;
			var datarow = {
				NHOMDICHVUID : ret.NHOMDICHVUID,
				DICHVUID : ret.DICHVUID,
				TENDICHVU : tendichvu_tmp,
				SOLUONG : soluong,
				GIA_TIEN : _payStart / parseFloat(soluong),
				BHYT_TRA : _payIns,
				MIENGIAM : isNaN(r.mien_giam) ? '0' : r.mien_giam,
				//MIENGIAM : nvl(r.mien_giam,'0'),
				THANH_TIEN : _payEnd,
				LOAITT_CU : r.ten_loai_tt,
				PHONG_TH : ret.PHONGID,
				LOAINHOMDICHVU : ret.LOAINHOMDICHVU,
				NHOM_MABHYT_ID : ret.NHOM_MABHYT_ID,
				GIABHYT : ret.GIABHYT,
				GIANHANDAN : ret.GIANHANDAN,
				GIADICHVU : ret.GIADICHVU,
				LOAIDOITUONG : r.loai_dt,
				MAUBENHPHAMID : '',
				NHOMBENHPHAM : _benhPhamGroup,
				DICHVU_BHYT_DINHMUC : ret.DICHVU_BHYT_DINHMUC,
				LOAI_DT_MOI : '',
				GIA_CHENH : '',
				LOAITT_MOI : '',
				GIA_DVC : ret.GIA_DVC,
				MANHOM_BHYT : ret.MANHOM_BHYT,
				DONVI : ret.DONVI,
				KHOANMUCID : ret.KHOANMUCID,
				BHYT_TRAFULL : r_full.bh_tra,
				MIENGIAMFULL : r_full.mien_giam,
				THANH_TIENFULL : r_full.nd_tra,
				BHYT_TRAFINAL : r_final.bh_tra,
				MIENGIAMFINAL : r_final.mien_giam,
				THANH_TIENFINAL : r_final.nd_tra,
				DICHVUCHINH : ret.DICHVUCHINH,
				DICHVUDIKEM : ret.DICHVUDIKEM,
				OLDVALUE : soluong,
				HAS_ORDER : ret.HAS_ORDER,
				GHICHU : nvl(ret.GHICHU, ''),
				TONGTIEN : _payEnd + 0,
				TACHPHIEU : ret.TACHPHIEU,
				PHUTHU : ret.PHUTHU + '',
				LOAIMAUBENHPHAM : ret.LOAIMAUBENHPHAM, //L2PT-6394
				KHOA_DTK : ret.KHOA_DTK, //L2PT-6731
				GIACHENHLECH : ret.GIACHENHLECH
			//L2PT-10994
			};
			//Begin_HaNv_24022020: Cho phép chia giá của gói dịch vụ theo số lượng phiếu - L2PT-16531
			if (typeof opt._chidinhdichvu == "undefined" && !isDVTheoGoi && ret.DVTHEOGOI == '1' && (r.loai_dt == '4' || r.loai_dt == '6')) {
				isDVTheoGoi = true;
			}
			//End_HaNv_HaNv_24022020
			if ((isCddvCl && isKhoaYc && typeof opt._chidinhdichvu == "undefined") || isClDvLv) {
				var r_gia_chenhlech = typeof ret.GIACHENHLECH == "undefined" ? 0 : parseFloat(ret.GIACHENHLECH);
				if (r_gia_chenhlech > 0) {
					datarow.GIA_CHENH = r_gia_chenhlech;
					datarow.LOAI_DT_MOI = 6;
					if (r.loai_dt == '1') {
						datarow.LOAIDOITUONG = '2';
						datarow.LOAITT_MOI = 'BHYT + Dịch vụ';
						var nd_chenh_temp = typeof r.nd_tra_chenh == "undefined" ? 0 : parseFloat(r.nd_tra_chenh);
						datarow.TONGTIEN = parseFloat(r.nd_tra) + nd_chenh_temp;
						datarow.GIA_TIEN = datarow.GIABHYT;
					} else if (r.loai_dt == '4') {
						//Begin_HaNv_09012020: Không tính giá chênh cho BN viện phí - L2PT-14904
						if (notGiaChenhVp) {
							datarow.LOAI_DT_MOI = '';
							datarow.GIA_CHENH = 0;
						} else {
							datarow.LOAIDOITUONG = '11';
							datarow.LOAITT_MOI = 'Viện phí + Dịch vụ';
							var nd_chenh_temp = typeof r.nd_tra_chenh == "undefined" ? 0 : parseFloat(r.nd_tra_chenh);
							datarow.TONGTIEN = parseFloat(r.nd_tra) + nd_chenh_temp;
							datarow.GIA_TIEN = datarow.GIANHANDAN;
						}
						//End_HaNv_09012020
					}
				}
			}
			if (rowIds == null || rowIds.length <= 0) {
				$('#grdDSCD').jqGrid('addRowData', 1, datarow);
			} else {
				$('#grdDSCD').jqGrid('addRowData', Math.max.apply(null, rowIds) + 1, datarow);
			}
			// lay tam tong chi phi va bao hiem trong luc cho doi cong thuc
			loadPay(_payStart, _payIns);
			if (typeof opt._chidinhdichvu == "undefined") {
				var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
				var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[rowIds.length - 1]);
				if (rowData.PHONG_TH != null && rowData.PHONG_TH != '' && rowData.PHONG_TH.indexOf("select") < 0) {
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV003", rowData.PHONG_TH);
					if (data_ar != null && data_ar.length > 0) {
						var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboKHOACHIDINHID">';
						var option = '';
						var checkExistDept = false;
						var checkEqualDept = false;
						//Begin_HaNv_12102020: Check ưu tiên phòng thực hiện của khoa hiện tại trong DS phòng - L2PT-27477
						var optionExistDept = '';
						var optionSelectKHA = '';
						for (var i = 0; i < data_ar.length; i++) {
							if (typeof opt._maubenhphamId != "undefined") {
								if (data_ar[i].PHONGID == opt._subdeptId) {
									checkExistDept = true;
								}
								if (data_ar[i].PHONGID != rowData.PHONG_TH1) {
									option = option + '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
								} else {
									checkEqualDept = true;
									option = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>' + option;
								}
							} else {
								if (data_ar[i].PHONGID == $('#cboPHONGTH_KHA').val()) {
									optionSelectKHA = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
								} else if (data_ar[i].PHONGID == opt._subdeptId_login) {
									checkExistDept = true;
									optionExistDept = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
								}
								//Begin_HaNv_12102020: Check ưu tiên phòng thực hiện của khoa hiện tại trong DS phòng - L2PT-27477
								else if (data_ar[i].KHOAID == opt._depId) {
									option = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>' + option;
								}
								//End_HaNv_12102020
								else {
									option = option + '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
								}
							}
						}
						if (checkExistDept) {
							if (rowData.LOAINHOMDICHVU == '5') {
								option = optionExistDept + option;
							} else {
								if (rowData.HAS_ORDER == '-1') {
									option = optionExistDept + option;
								} else {
									option = option + optionExistDept;
								}
							}
						}
						//Begin_HaNv_08092020: Ẩn phòng chỉ định vào danh sách phòng thực hiện DV PTTT - L2PT-26552 - L2PT-29789
						if (!checkExistDept && rowData.LOAINHOMDICHVU == '5' && !isHidePcd) {
							if (typeof opt._maubenhphamId != "undefined") {
								if (!checkEqualDept) {
									option = '<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>' + option;
								} else {
									option = option + '<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>';
								}
							} else {
								option = option + '<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>';
							}
						}
						//End_HaNv_08092020
						//End_HaNv_12102020
						html = html + optionSelectKHA + option + '</select>';
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'PHONG_TH', html);
					}
				} else {
					var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboKHOACHIDINHID">' + '<option value="' + opt._subdeptId_login + '">' +
							opt._subdeptName_login + '</option>' + '</select>';
					$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'PHONG_TH', html);
				}
				if (book) {
					var checkedAll = "";
					if (bookAll) {
						var checkedAll = 'checked="true"';
					}
					var htmlCheckbox = '<label class="control-label" for="' + (rowData.DICHVUID) + '_chkHEN" style="margin-left:5px; margin-bottom: 0px;"><input type="checkbox" ' + checkedAll +
							' class="mgb5" id="' + (rowData.DICHVUID) + '_chkHEN"> Hẹn</label>';
					$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'HEN', htmlCheckbox);
				}
				//Begin_HaNv_21062018: Danh dau thuc hien dich vu vi sinh (phat hien hoac nuoi cay) - L2K74TW-582
				if (isNhomDvViSinh) {
					var checkDvViSinh = jsonrpc.AjaxJson.getOneValue("CHECK_DV_VISINH", [ {
						"name" : "[0]",
						"value" : rowData.DICHVUID
					} ]);
					if (checkDvViSinh == '1') {
						var html = '<select class="form-control input-sm" id="' + (ret.DICHVUID) + "_" + 'cboTHUCHIENVISINH">' + '<option value="">Chọn</option>' +
								'<option value="1">Phát hiện</option>' + '<option value="2">Theo dõi</option>' + '</select>';
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'ISVISINH', '1');
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'THUCHIENVISINH', html);
					} else {
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'ISVISINH', '0');
					}
				}
				//End_HaNv_21062018
				//Begin_HaNv_29012019: Tinh trang PTTT (binh thuong hoac cap cuu) - L2PT-1567
				if (showTinhTrangPttt) {
					if (rowData.LOAINHOMDICHVU == '5') {
						var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboTINHTRANG_PTTT">' + '<option value="2">Chủ động</option>' +
								'<option value="1">Cấp cứu</option>' + '</select>';
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'TINHTRANG_PTTT', html);
					}
				}
				//End_HaNv_29012019
				//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
				if ($('#hidLOAIKHAM').val() != '' && $('#hidLOAIKHAM').val() != '0') {
					$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'LOAIKHAM', $('#hidLOAIKHAM').val());
					$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'LOAITT_MOI', 'Hợp đồng');
				}
				//End_HaNv_25072020
				//Begin_HaNv_15082020: cảnh báo chỉ định vượt tiền mức trần BHYT đã cấu hình - L2PT-25620
				if (tienMucTranBh > 0 && $('#hidLOAITIEPNHANID').val() == 1 && tongbh > tienMucTranBh) {
					DlgUtil.showMsg('Tổng tiền dịch vụ bảo hiểm là ' + tongbh + ', đã vượt tiền mức trần cấu hình là ' + tienMucTranBh);
				}
				//End_HaNv_15082020
				//Begin_HaNv_09092020: Cho phép thay đổi loại Mbp khi chỉ định dịch vụ - L2PT-26489
				if (changeLoaiMbp) {
					var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboLOAI_MBP">';
					html = html + '<option value="">Rỗng</option>';
					html = html + '<option value="1">Đàm</option>';
					html = html + '<option value="2">Máu</option>';
					html = html + '<option value="3">Nước tiểu</option>';
					html = html + '<option value="4">Phân</option>';
					html = html + '<option value="5">Dịch</option>';
					html = html + '<option value="6">Mủ</option>';
					html = html + '<option value="7">Khác</option></select>';
					$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'LOAIMAUBENHPHAM', html);
					$('#' + ret.DICHVUID + "_cboLOAI_MBP").val(ret.LOAI_MBP);
				}
				//End_HaNv_09092020
			}
			if (isShowMsgWr && $('#hidLOAITIEPNHANID').val() == 1 && tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
				DlgUtil.showMsg('Tổng tiền dịch vụ bảo hiểm vượt trần');
			}
			$('#txtLOAIDV').focus();
		}
	}
	//Begin_HaNv_23032020: Kiểm tra Chứng chỉ hành nghề (Mã BS) với BN BHYT - L2PT-18389
	function _checkMaBs(type) {
		if (checkMaBs != '0') {
			var _sql_par = [];
			var _bsikham = $('#cboBACSIID').val();
			if (_bsikham == '-1' || _bsikham == undefined || _bsikham == "") {
				_bsikham = opt.user_id;
			}
			_sql_par.push({
				"name" : "[0]",
				value : _bsikham
			});
			var ret = jsonrpc.AjaxJson.getOneValue("NGT.MALOAIBS", _sql_par);
			if (ret == '0') {
				if (checkMaBs == 2) {
					DlgUtil.showMsg("Người dùng không phải bác sĩ hoặc không có chứng chỉ hành nghề!");
					return;
				}
				DlgUtil.showConfirm("Người dùng không phải bác sĩ hoặc không có chứng chỉ hành nghề. Bạn có muốn tiếp tục?", function(flag) {
					if (flag) {
						saveData(type);
					}
				});
			} else {
				saveData(type);
			}
		} else {
			saveData(type);
		}
	}
	//End_HaNv_23032020
	//Begin_HaNv_23032020: Kiểm tra Chứng chỉ hành nghề (Mã BS) với BN BHYT - L2PT-18389
	function _checkThoiGianCD(type) {
		if (!isCbQuaTg && !compareDate($('#txtTGCHIDINH').val().trim(), currentTime, 'DD/MM/YYYY HH:mm:ss')) {
			DlgUtil.showConfirm("Thời gian chỉ định lớn hơn thời gian hiện tại. Bạn có tiếp tục?", function(flag) {
				if (flag) {
					_checkMaBs(type);
				}
			});
		} else {
			_checkMaBs(type);
		}
	}
	//End_HaNv_23032020
	function getMinFieldByDept(rowDatas, phongStr) {
		var phong_arr = phongStr.split(';');
		var minDept = '';
		var minSL = 0;
		for (var i = 0; i < rowDatas.length; i++) {
			for (var j = 0; j < phong_arr.length; j++) {
				if (rowDatas[i].PHONGID == phong_arr[j]) {
					if (minDept == '') {
						minDept = rowDatas[i].PHONGID;
						minSL = rowDatas[i].SL;
					} else if (parseInt(minSL) > parseInt(rowDatas[i].SL)) {
						minDept = rowDatas[i].PHONGID;
						minSL = rowDatas[i].SL;
					}
				}
			}
		}
		return minDept;
	}
	function changeTempData(rowId) {
		var rowData = $("#grdDSCD").jqGrid('getRowData', rowId);
		paramInput = {
			dichvuid : rowData.DICHVUID,
			tendichvu : rowData.TENDICHVU,
			json_data : rowData.JSON_TEMP,
			maubenhphamid : typeof opt._maubenhphamId != "undefined" ? opt._maubenhphamId : ''
		};
		dlgPopup = DlgUtil.buildPopupUrl("divDlgSubPu", "divDlg", "manager.jsp?func=../noitru/NTU01H047_DichVuMauLS", paramInput, "Cập nhật", 700, 430);
		DlgUtil.open("divDlgSubPu");
	}
	function changeNcData(rowId) {
		var rowData = $("#grdDSCD").jqGrid('getRowData', rowId);
		paramInput = {
			dichvuid : rowData.DICHVUID,
			benhnhanid : $('#hidBENHNHANID').val(),
			json_data : rowData.JSON_NUOICAY,
			maubenhphamid : typeof opt._maubenhphamId != "undefined" ? opt._maubenhphamId : ''
		};
		var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapDKNC", "dlgNhapDKNC", "manager.jsp?func=../canlamsang/CLS001X113_DieuKienNuoiCay&type=update", paramInput, "Điều kiện cho dịch vụ nuôi cấy", 1080,
				500);
		dlgPopup.open("dlgNhapDKNC");
	}
	function changeDieuKien(rowId) {
		var rowData = $("#grdDSCD").jqGrid('getRowData', rowId);
		var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), rowData.DICHVUID, '2' ];
		dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
		if (dataArr == null || dataArr.length <= 0) {
			DlgUtil.showMsg('Chưa cấu hình phòng thực hiện cho dịch vụ');
			return;
		}
		var objDataDk = dataArr[0];
		var resultDataDk = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K016.EV008", objDataDk.DICHVUID);
		if (resultDataDk > 0) {
			var myVar = {
				thuocvattuid : objDataDk.DICHVUID,
				dieukienid : rowData.DIEUKIEN_DV,
				rowid : rowId,
				loai : 1,
				json_data : JSON.stringify(objDataDk),
				gridId : ""
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgTHUOCTYLE", "divDlg", "manager.jsp?func=../ngoaitru/NGT01T096_thuoctyle", myVar, "DỊCH VỤ CÓ ĐIỀU KIỆN", 600, 550, {
				closeButton : false
			});
			DlgUtil.open("dlgTHUOCTYLE");
			return;
		} else {
			DlgUtil.showMsg('Dịch vụ này không có điều kiện đi kèm');
			return;
		}
	}
	function changeObject(rowId, loaiDt) {
		//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
		if ($('#hidLOAIKHAM').val() == '1' && loaiDt != 'HD') {
			DlgUtil.showMsg('Không cho phép chuyển loại đối tượng với bệnh nhân hợp đồng gửi thực hiện CLS!');
			return;
		}
		//End_HaNv_25072020
		if (opt._loaidichvu == '19') {
			return;
		}
		if ($('#grdDSCD').find("input[id*='SOLUONG']").length > 0) {
			DlgUtil.showMsg('Tồn tại trường số lượng đang sửa');
			return false;
		}
		var _insurance = 0;
		var _insurance_full = 0;
		var _insurance_final = 0;
		var dtbnId = opt._doituongbenhnhanId;
		var rowData = $("#grdDSCD").jqGrid('getRowData', rowId);
		if (loaiDt != 15) {
			if (((dtbnId == 1 && loaiDt == '1') || (dtbnId == 2 && loaiDt == '4') || (dtbnId == 3 && loaiDt == '6')) && rowData.LOAI_DT_MOI == '' && rowData.LOAITT_MOI == '') {
				DlgUtil.showMsg('Không thể chuyển loại thanh toán trùng với loại của đối tượng bệnh nhân');
				return;
			}
		}
		if ((loaiDt == 1 || loaiDt == 2) && (rowData.GIABHYT == '' || parseFloat(rowData.GIABHYT) <= 0)) {
			DlgUtil.showMsg('Không thể chuyển loại đối tượng khi giá bảo hiểm bằng 0');
			return;
		}
		if ((loaiDt == 4 || loaiDt == 11) && (rowData.GIANHANDAN == '' || parseFloat(rowData.GIANHANDAN) <= 0)) {
			DlgUtil.showMsg('Không thể chuyển loại đối tượng khi giá nhân dân bằng 0');
			return;
		}
		if ((loaiDt == 6) && (rowData.GIADICHVU == '' || parseFloat(rowData.GIADICHVU) <= 0)) {
			DlgUtil.showMsg('Không thể chuyển loại đối tượng khi giá dịch vụ bằng 0');
			return;
		}
		if (dtbnId == '1' && parseFloat(rowData.GIABHYT) > 0) {
			if (rowData.LOAIDOITUONG == '1' || rowData.LOAIDOITUONG == '2') {
				if (loaiDt == '4' || loaiDt == '11' || loaiDt == '6') {
					tongbh = tongbh - parseFloat(rowData.BHYT_TRA) - parseFloat(rowData.THANH_TIEN);
				}
			}
			if (rowData.LOAIDOITUONG != '1' && rowData.LOAIDOITUONG != '2') {
				if (loaiDt == '1' || loaiDt == '2') {
					tongbh = tongbh + parseFloat(rowData.BHYT_TRA) + parseFloat(rowData.THANH_TIEN);
				}
			}
			if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
				_insurance = nvl($('#hidQUYENLOITHE').val(), $('#txtQUYENLOI').val());
			} else {
				_insurance = nvl($('#hidQUYENLOI').val(), $('#txtQUYENLOI').val());
			}
			_insurance_full = nvl($('#hidQUYENLOI').val(), $('#txtQUYENLOI').val());
			_insurance_final = nvl($('#hidQUYENLOITHE').val(), $('#txtQUYENLOI').val());
			var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
			for (var i1 = 0; i1 < rowIds.length; i1++) {
				var rowData1 = $('#grdDSCD').jqGrid('getRowData', rowIds[i1]);
				if ((rowData1.LOAIDOITUONG == '1' || rowData1.LOAIDOITUONG == '2') && rowIds[i1] != rowId) {
					if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData1.BHYT_TRAFINAL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData1.BHYT_TRAFINAL;
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData1.THANH_TIENFINAL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData1.THANH_TIENFINAL;
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFINAL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFINAL;
						loadPay(0, parseFloat(rowData1.BHYT_TRAFINAL) - parseFloat(rowData1.BHYT_TRA));
					} else {
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData1.BHYT_TRAFULL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData1.BHYT_TRAFULL;
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData1.THANH_TIENFULL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData1.THANH_TIENFULL;
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFULL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFULL;
						loadPay(0, parseFloat(rowData1.BHYT_TRAFULL) - parseFloat(rowData1.BHYT_TRA));
					}
				}
			}
		}
		var objTinhTien = new Object();
		objTinhTien.DOITUONGBENHNHANID = dtbnId;
		if (opt._loaidichvu == '14' && isTranspost) {
			objTinhTien.MUCHUONG = $('#hidTYLE_THEBHYT').val();
		} else {
			objTinhTien.MUCHUONG = parseFloat(_insurance);
		}
		objTinhTien.GIATRANBH = parseFloat(rowData.DICHVU_BHYT_DINHMUC);
		objTinhTien.GIABHYT = parseFloat(rowData.GIABHYT);
		objTinhTien.GIAND = parseFloat(rowData.GIANHANDAN);
		//Begin_HaNv_08112019: Tính toán lại giá chênh khi tách phiếu cho DKLAN - L2PT-10994
		//Cấu hình bât và loaiDt in 2,11: GIADICHVU = GIANHANDAN + GIACHENHLECH(dmc_dichvu). Hiện tại: GIACHENHLECH = GIADICHVU - GIANHANDAN
		if (isTachTheoGiaChenhLech && (loaiDt == 2 || loaiDt == 11)) {
			objTinhTien.GIADV = parseFloat(rowData.GIANHANDAN) + parseFloat(rowData.GIACHENHLECH);
		} else {
			objTinhTien.GIADV = parseFloat(rowData.GIADICHVU);
		}
		//End_HaNv_08112019
		objTinhTien.GIANN = 0;
		objTinhTien.DOITUONGCHUYEN = loaiDt;
		objTinhTien.GIADVKTC = opt._doituongbenhnhanId == '1' && parseFloat(rowData.GIA_DVC) > 0 ? rowData.GIA_DVC : 0;
		objTinhTien.MANHOMBHYT = rowData.MANHOM_BHYT;
		objTinhTien.SOLUONG = rowData.SOLUONG;
		objTinhTien.CANTRENDVKTC = $('#hidCANTRENKTC').val();
		objTinhTien.THEDUTHOIGIAN = $('#hidBHFULL').val();
		objTinhTien.DUOCVANCHUYEN = $('#hidDUOCVC').val();
		objTinhTien.TYLETHUOCVATTU = 100;
		objTinhTien.NHOMDOITUONG = $('#hidNHOMDOITUONG').val();
		objTinhTien.NGAYHANTHE = $('#hidNGAYHANTHE').val();
		objTinhTien.NGAYDICHVU = $('#txtTGCHIDINH').val().substr(0, 10);
		objTinhTien.TYLE_MIENGIAM = $('#hidTYLEMIENGIAM').val();
		objTinhTien.NGAYGIAHANTHE = config_hanthe;
		if (objTinhTien.DOITUONGCHUYEN == '0' || objTinhTien.DOITUONGCHUYEN == '1' || objTinhTien.DOITUONGCHUYEN == '2') {
			objTinhTien.DAUTHE = $('#txtMA_BHYT').val().substring(0, 3);
			objTinhTien.CHUOIDAUTHE = cf_dauthe;
		}
		if (opt._loaidichvu == '13') {
			objTinhTien.TYLEDV = $('#' + rowData.DICHVUID + '_cboTYLEDV').val();
		} else {
			if (typeof rowData.LOAIPTTT != 'undefined' && rowData.LOAIPTTT != '' && rowData.LOAIPTTT != null) {
				if (typeof rowData.TYLEDVTEMP != 'undefined' && rowData.TYLEDVTEMP != '' && rowData.TYLEDVTEMP != null) {
					objTinhTien.TYLEDV = rowData.TYLEDVTEMP;
				} else {
					objTinhTien.TYLEDV = 1;
				}
			} else {
				objTinhTien.TYLEDV = 1;
			}
		}
		//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
		if (loaiDt == 'HD') {
			objTinhTien.DOITUONGCHUYEN = '4';
			objTinhTien.LOAIKHAM = $('#hidLOAIKHAM').val();
		}
		//End_HaNv_25072020
		var objDataChange = vienphi.tinhtien_dichvu(objTinhTien);
		var objTinhTienFull = $.extend({}, objTinhTien);
		objTinhTienFull.MUCHUONG = _insurance_full;
		var objDataFull = vienphi.tinhtien_dichvu(objTinhTienFull);
		var objTinhTienFinal = $.extend({}, objTinhTien);
		if (opt._loaidichvu == '14' && isTranspost) {
			objTinhTienFinal.MUCHUONG = $('#hidTYLE_THEBHYT').val();
		} else {
			objTinhTienFinal.MUCHUONG = _insurance_final;
		}
		var objDataFinal = vienphi.tinhtien_dichvu(objTinhTienFinal);
		if (rowData.LOAIDOITUONG == objDataChange.loai_dt && rowData.LOAI_DT_MOI != '' && typeof objDataChange.loai_dt_moi != 'undefined' && rowData.LOAI_DT_MOI == objDataChange.loai_dt_moi) {
			DlgUtil.showMsg('Không thể chuyển loại thanh toán trùng với loại của đối tượng bệnh nhân');
			return;
		}
		if (objDataChange.bh_tra != -1 && objDataChange.bh_tra != -1 && objDataChange.nd_tra != -1) {
			var _oldBHYTDataRow = rowData.BHYT_TRA;
			var _oldTTDataRow = rowData.THANH_TIEN;
			var _newBHYTDataRow = parseFloat(objDataChange.bh_tra);
			var _newTTDataRow = parseFloat(objDataChange.tong_cp);
			var _payBHYTChange = _newBHYTDataRow - parseFloat(_oldBHYTDataRow);
			var _payTTChange = _newTTDataRow - parseFloat(_oldTTDataRow) - parseFloat(_oldBHYTDataRow) + parseFloat(nvl(objDataChange.nd_tra_chenh, "0"));
			loadPay(_payTTChange, _payBHYTChange);
			if (objDataChange.loai_dt == 4 || objDataChange.loai_dt == 6) {
				$("#grdDSCD").jqGrid('setRowData', rowId, "", {
					color : 'red'
				});
			}
			//Begin_HaNv_08112019: Tính toán lại giá chênh khi tách phiếu cho DKLAN - L2PT-10994
			if (isTachTheoGiaChenhLech && (loaiDt == 2 || loaiDt == 11) && nvl(rowData.GIACHENHLECH, '0') != '0') {
				if (loaiDt == 2) {
					$('#grdDSCD').jqGrid('setCell', rowId, 'GIA_TIEN', parseFloat(rowData.GIABHYT));
					$('#grdDSCD').jqGrid('getLocalRow', rowId).GIA_TIEN = parseFloat(rowData.GIABHYT);
				} else if (loaiDt == 11) {
					$('#grdDSCD').jqGrid('setCell', rowId, 'GIA_TIEN', parseFloat(rowData.GIANHANDAN));
					$('#grdDSCD').jqGrid('getLocalRow', rowId).GIA_TIEN = parseFloat(rowData.GIANHANDAN);
				}
			} else {
				$('#grdDSCD').jqGrid('setCell', rowId, 'GIA_TIEN',
						parseFloat(objDataChange.tong_cp) / parseFloat(rowData.SOLUONG) + parseFloat(typeof objDataChange.nd_tra_chenh == "undefined" ? '0' : objDataChange.nd_tra_chenh));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).GIA_TIEN = parseFloat(objDataChange.tong_cp) / parseFloat(rowData.SOLUONG) +
						parseFloat(typeof objDataChange.nd_tra_chenh == "undefined" ? '0' : objDataChange.nd_tra_chenh);
			}
			//End_HaNv_08112019
			$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRA', parseFloat(objDataChange.bh_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRA = parseFloat(objDataChange.bh_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAM', parseFloat(objDataChange.mien_giam));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAM = parseFloat(objDataChange.mien_giam);
			$('#grdDSCD').jqGrid('setCell', rowId, 'THANH_TIEN', parseFloat(objDataChange.nd_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIEN = parseFloat(objDataChange.nd_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRAFINAL', parseFloat(objDataFinal.bh_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRAFINAL = parseFloat(objDataFinal.bh_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAMFINAL', parseFloat(objDataFinal.mien_giam));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAMFINAL = parseFloat(objDataFinal.mien_giam);
			$('#grdDSCD').jqGrid('setCell', rowId, 'THANH_TIENFINAL', parseFloat(objDataFinal.nd_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIENFINAL = parseFloat(objDataFinal.nd_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRAFULL', parseFloat(objDataFull.bh_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRAFULL = parseFloat(objDataFull.bh_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAMFULL', parseFloat(objDataFull.mien_giam));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAMFULL = parseFloat(objDataFull.mien_giam);
			$('#grdDSCD').jqGrid('setCell', rowId, 'THANH_TIENFULL', parseFloat(objDataFull.nd_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIENFULL = parseFloat(objDataFull.nd_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'LOAIDOITUONG', objDataChange.loai_dt);
			$('#grdDSCD').jqGrid('getLocalRow', rowId).LOAIDOITUONG = objDataChange.loai_dt;
			$('#grdDSCD').jqGrid('setCell', rowId, 'GIA_CHENH', typeof objDataChange.nd_tra_chenh == "undefined" ? null : objDataChange.nd_tra_chenh);
			$('#grdDSCD').jqGrid('getLocalRow', rowId).GIA_CHENH = typeof objDataChange.nd_tra_chenh == "undefined" ? null : objDataChange.nd_tra_chenh;
			$('#grdDSCD').jqGrid('setCell', rowId, 'LOAI_DT_MOI', typeof objDataChange.loai_dt_moi == "undefined" ? null : objDataChange.loai_dt_moi);
			$('#grdDSCD').jqGrid('getLocalRow', rowId).LOAI_DT_MOI = typeof objDataChange.loai_dt_moi == "undefined" ? null : objDataChange.loai_dt_moi;
			$('#grdDSCD').jqGrid('setCell', rowId, 'LOAITT_MOI', typeof objDataChange.ten_loai_tt_moi == "undefined" ? null : objDataChange.ten_loai_tt_moi);
			$('#grdDSCD').jqGrid('getLocalRow', rowId).LOAITT_MOI = typeof objDataChange.ten_loai_tt_moi == "undefined" ? null : objDataChange.ten_loai_tt_moi;
			var nd_chenh_temp = typeof objDataChange.nd_tra_chenh == "undefined" ? 0 : parseFloat(objDataChange.nd_tra_chenh);
			$('#grdDSCD').jqGrid('setCell', rowId, 'TONGTIEN', parseFloat(objDataChange.nd_tra) + nd_chenh_temp);
			$('#grdDSCD').jqGrid('getLocalRow', rowId).TONGTIEN = parseFloat(objDataChange.nd_tra) + nd_chenh_temp;
			//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
			if (loaiDt == 'HD') {
				$('#grdDSCD').jqGrid('setCell', rowId, 'LOAIKHAM', $('#hidLOAIKHAM').val());
			} else {
				$('#grdDSCD').jqGrid('setCell', rowId, 'LOAIKHAM', '0');
			}
			//End_HaNv_25072020
		} else {
			DlgUtil.showMsg('Không thể chuyển loại thanh toán vừa chọn cho đối tượng bệnh nhân này');
		}
	}
	/* Hanv_20170717 begin: check dich vu khong cung thanh toan */
	function check_kctt(_grdId, _id, _selected) {
		dGridKctt = $.Deferred();
		checkKctt = true;
		var ret;
		var isAddKHA = false;
		if (!jQuery.isEmptyObject(_objDichVu)) {
			ret = _objDichVu;
			isAddKHA = true;
		} else {
			ret = $("#" + _grdId).jqGrid('getRowData', _id);
		}
		if (_selected) {
			var idSelected = '';
			var dscdIds = $('#grdDSCD').jqGrid('getDataIDs');
			for (var i = 0; i < dscdIds.length; i++) {
				var rowData = $('#grdDSCD').jqGrid('getRowData', dscdIds[i]);
				if (idSelected == '') {
					idSelected = rowData.DICHVUID;
				} else {
					idSelected = idSelected + ';' + rowData.DICHVUID;
				}
			}
			var _par = [ ret.DICHVUID, opt._tiepnhanId, $('#txtTGCHIDINH').val(), idSelected ];
			var res = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D009.KCTT", _par.join('$')); // check khong cung thanh toan
			if (res) { // co dich vu khong cung thanh toan da chon truoc
				DlgUtil.showConfirm("Dịch vụ " + res + " và " + ret.MADICHVU + " không được thanh toán cùng ngày. Bạn có muốn tiếp tục chỉ định?", function(flag) {
					if (flag) {
						checkKctt = true;
						dGridKctt.resolve();
					} else {
						$("#" + _grdId).jqGrid('setSelection', _id, false);
						GridUtil.unmarkRow(_grdId, _id);
						checkKctt = false;
						dGridKctt.resolve();
					}
				});
			} else {
				checkKctt = true;
				dGridKctt.resolve();
			}
		} else {
			checkKctt = true;
			dGridKctt.resolve();
		}
		$.when(dGridKctt.promise()).then(function() {
			if (checkKctt) {
				// start chan chi dinh dich vu trong 24h
				var resultCheck = 1;
				if (_selected && !isUncheckDuplicate) {
					var _par = [ $('#hidKHAMBENHID').val(), ret.DICHVUID, $('#txtTGCHIDINH').val() ];
					resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D009.EV007", _par.join('$'));
				}
				if (resultCheck == 0) {
					if (existConfig) {
						DlgUtil.showConfirm(ret.TENDICHVU + ' đã được kê tại phòng khám hiện tại hoặc phòng khám khác trong ngày, có tiếp tục chỉ định dịch vụ', function(flag) {
							if (flag) {
								if (!_selected || opt._doituongbenhnhanId != "1") {
									if (isAddKHA) {
										addDichVu();
									} else {
										onSelectRow(_grdId, _id, _selected);
									}
								} else {
									var resultCheck = 0;
									if (!isCbNbh) {
										resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K016.CHECK_CDDV", ret.DICHVUID);
									}
									if (resultCheck == 1) {
										DlgUtil.showConfirm("Có chắc chắn chỉ định dịch vụ nằm ngoài danh mục BHYT thanh toán cho bệnh nhân?", function(flag) {
											if (flag) {
												if (isAddKHA) {
													addDichVu();
												} else {
													onSelectRow(_grdId, _id, _selected);
												}
											} else {
												GridUtil.unmarkRow(_grdId, _id);
												$("#" + _grdId).jqGrid('setSelection', _id, false);
											}
										});
									} else if (isAddKHA) {
										addDichVu();
									} else {
										onSelectRow(_grdId, _id, _selected);
									}
								}
							} else {
								$("#" + _grdId).jqGrid('setSelection', _id, false);
							}
						});
					} else {
						DlgUtil.showMsg(ret.TENDICHVU + ' đã được kê tại phòng khám hiện tại hoặc phòng khám khác trong ngày, đề nghị lựa chọn dịch vụ khác');
						$("#" + _grdId).jqGrid('setSelection', _id, false);
						return;
					}
				} else {
					// start check doi tuong bhyt chi dinh dich vu ngoai danh muc
					if (!_selected || opt._doituongbenhnhanId != "1")
						if (isAddKHA) {
							addDichVu();
						} else {
							onSelectRow(_grdId, _id, _selected);
						}
					else {
						var resultCheck = 0;
						if (!isCbNbh) {
							resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K016.CHECK_CDDV", ret.DICHVUID);
						}
						if (resultCheck == 1) {
							DlgUtil.showConfirm("Có chắc chắn chỉ định dịch vụ nằm ngoài danh mục BHYT thanh toán cho bệnh nhân?", function(flag) {
								if (flag) {
									if (isAddKHA) {
										addDichVu();
									} else {
										onSelectRow(_grdId, _id, _selected);
									}
								} else {
									GridUtil.unmarkRow(_grdId, _id);
									$("#" + _grdId).jqGrid('setSelection', _id, false);
								}
							});
						} else {
							if (isAddKHA) {
								addDichVu();
							} else {
								onSelectRow(_grdId, _id, _selected);
							}
						}
					}
					// end check doi tuong bhyt chi dinh dich vu ngoai danh muc
				}
				// end
			}
		});
	}
	/* Hanv_20170717 end */
	/* Begin_HaNv_12082019: gioi han thuc hien dichvu CLS theo ICD trong 1 dot dieu tri - L2PT-7077 */
	function _checkDichVuByICD(_grdId, _id) {
		if ($('#txtCHANDOAN_KT').val() != '' || ($('#cboMACHANDOAN').val() != '' && $('#cboMACHANDOAN').val() != null)) {
			var objCheck = new Object();
			var ret = $("#" + _grdId).jqGrid('getRowData', _id);
			objCheck["TYPE"] = '1';
			objCheck["DICHVUID"] = ret.DICHVUID;
			objCheck["ICD"] = ($('#txtCHANDOAN_KT').val() != '') ? ($('#txtCHANDOAN_KT').val() + ';' + $('#cboMACHANDOAN').val() + '-') : $('#cboMACHANDOAN').val() + '-';
			objCheck["HOSOBENHANID"] = $('#hidHOSOBENHANID').val();
			var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("CDDV.CHECK_ICD_DV", JSON.stringify(objCheck));
			if (typeof resultCheck != 'undefined' && resultCheck != '') {
				DlgUtil.showMsg('Dịch vụ ' + ret.MADICHVU + ' đã vượt quá số lần được chỉ định với mã bệnh ' + resultCheck);
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}
	/* End_HaNv_12082019 */
	/* Begin_HaNv_12082019: Check dichvuid khong cung chi dinh chung voi nhau - L2PT-7660 */
	function _checkKhongCungCd(_grdId, _id) {
		var objCheck = new Object();
		listDvId = '';
		var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
		for (var i = 0; i < rowIds.length; i++) {
			var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[i]);
			listDvId = (listDvId == '') ? (rowData.DICHVUID) : (listDvId + ';' + rowData.DICHVUID);
		}
		var ret = $("#" + _grdId).jqGrid('getRowData', _id);
		objCheck["TYPE"] = '2';
		objCheck["DICHVUID"] = ret.DICHVUID;
		objCheck["DICHVUID_DACHON"] = listDvId;
		objCheck["HOSOBENHANID"] = $('#hidHOSOBENHANID').val();
		var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("CDDV.CHECK_ICD_DV", JSON.stringify(objCheck));
		if (typeof resultCheck != 'undefined' && resultCheck != '') {
			DlgUtil.showMsg('Dịch vụ ' + ret.MADICHVU + ' không cùng chỉ định với dịch vụ ' + resultCheck);
			return true;
		} else {
			return false;
		}
	}
	/* End_HaNv_12082019 */
	/* Begin_HaNv_10082020: cảnh báo, chặn chỉ định CLS đã chỉ định trong cùng một đợt điều trị - L2PT-25013 */
	function _checkTrungDVCD(_grdId, _id) {
		var objCheck = new Object();
		var ret = $("#" + _grdId).jqGrid('getRowData', _id);
		objCheck["DICHVUID"] = ret.DICHVUID;
		objCheck["HOSOBENHANID"] = $('#hidHOSOBENHANID').val();
		var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("CDDV.TRUNG_DVCD", JSON.stringify(objCheck));
		if (typeof resultCheck != 'undefined' && resultCheck != '') {
			DlgUtil.showMsg(ret.TENDICHVU + ' đã được kê tại phòng khám hiện tại hoặc phòng khám khác trong cùng đợt điều trị. Số phiếu đã kê: ' + resultCheck);
			return true;
		} else {
			return false;
		}
	}
	/* End_HaNv_10082020 */
	function onSelectRow(_grdId, _id, _selected, template, templateid, fullTemp) {
		var rowCount = $("#grdDSCD").getGridParam("reccount");
		var ret = $("#" + _grdId).jqGrid('getRowData', _id);
		if (_selected) {
			//Begin_HaNv_17092020: Kê bổ sung dịch vụ vào mbpid_bosungdv - L2PT-26516
			if (opt.mbpid_bosungdv != '' && ((opt._loaiphieu == '1' && ret.LOAINHOMDICHVU != '3') || (opt._loaiphieu == '2' && ret.LOAINHOMDICHVU != '4'))) {
				DlgUtil.showMsg('Dịch vụ được chọn không cùng loại phiếu với phiếu cần kê bổ sung!');
				$("#" + _grdId).jqGrid('setSelection', _id, false);
				return;
			}
			//End_HaNv_17092020
			//Begin_HaNv_10082020: cảnh báo, chặn chỉ định CLS đã chỉ định trong cùng một đợt điều trị - L2PT-25013
			if (ret.LOAINHOMDICHVU == '3' && cbTrungDVCD != 0) {
				var check = _checkTrungDVCD(_grdId, _id);
				if (check && cbTrungDVCD == '2') {
					return;
				}
			}
			//End_HaNv_10082020
			//Begin_HaNv_06072020: Cảnh báo vận chuyển theo mức hưởng thẻ BH - L2PT-22942
			if (cbVanChuyenMucHuong) {
				var muchuong = $('#txtMA_BHYT').val().substring(2, 3);
				if (((muchuong == 1 || muchuong == 2 || muchuong == 5) && ret.MADICHVU != 'VC.80001') || ((muchuong == 3 || muchuong == 4) && ret.MADICHVU == 'VC.80001')) {
					DlgUtil.showMsg('Đề nghị kiểm tra xem đã đúng quyền lợi BHYT hay không!');
				}
			}
			//End_HaNv_06072020
			if (opt._loaidichvu == '13') {
				$('#cboLOAIGIUONG').prop("disabled", true);
				$('#cboMAGIUONG').prop("disabled", true);
			}
			//Begin_HaNv_12082019: gioi han thuc hien dichvu CLS theo ICD trong 1 dot dieu tri - L2PT-7077
			if (checkDichVuByICD != '0') {
				var check = _checkDichVuByICD(_grdId, _id);
				if (check && checkDichVuByICD == '2') {
					return;
				}
			}
			//End_HaNv_12082019
			//Begin_HaNv_12082019: Check dichvuid khong cung chi dinh chung voi nhau - L2PT-7660
			if (checkKhongCungCd != '0') {
				var check = _checkKhongCungCd(_grdId, _id);
				if (check && checkKhongCungCd == '2') {
					return;
				}
			}
			//End_HaNv_12082019
			//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
			if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
				$('#cboTYLEDV_CK').prop("disabled", true);
			}
			//End_HaNv_01012020
			//Begin_HaNv_24022020: Cho phép chia giá của gói dịch vụ theo số lượng phiếu - L2PT-16531
			if (isDVTheoGoi && rowCount > 0) {
				DlgUtil.showMsg('Không được phép chỉ định nhiều hơn 1 dịch vụ khi đã chỉ định dịch vụ gói!');
				$("#" + _grdId).jqGrid('setSelection', _id, false);
				return;
			}
			//End_HaNv_HaNv_24022020
		} else {
			if (opt._loaidichvu == '13') {
				if (rowCount == 1) {
					$('#cboLOAIGIUONG').prop("disabled", false);
					$('#cboMAGIUONG').prop("disabled", false);
				}
			}
			//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
			if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
				if (rowCount == 1) {
					$('#cboTYLEDV_CK').prop("disabled", false);
				}
			}
			//End_HaNv_01012020
			//Begin_HaNv_24022020: Cho phép chia giá của gói dịch vụ theo số lượng phiếu - L2PT-16531
			if (isDVTheoGoi) {
				if (rowCount == 1) {
					isDVTheoGoi = false;
				}
			}
			//End_HaNv_HaNv_24022020
		}
		var _insurance = 0;
		var _insurance_full = 0;
		var _insurance_final = 0;
		var dataArr = [];
		if (typeof opt._chidinhdichvu != "undefined") {
			if (template == '1') {
				var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), templateid, '1' ];
				dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
			} else {
				dataArr.push(ret);
			}
			if (opt._loaidichvu == '13' && dataArr.length == 1 && isCheckSlNguoi) {
				var paramArr = [ $('#txtTGCHIDINH').val(), $('#cboMAGIUONG').val(), $('#cboLOAIGIUONG').val(), opt._khambenhId ];
				var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K016.EV010", paramArr.join('$'));
				if (resultCheck == 0) {
					DlgUtil.showMsg('Sử dụng giường quá số lượng bệnh nhân cho phép trong ngày chỉ định');
					return;
				}
			}
		} else {
			if (template == '1') {
				if (fullTemp == '1') {
					var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), templateid, '3' ];
					dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
				} else if (fullTemp == '2') {
					var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), templateid, '4' ];
					dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
				}
				//Begin_HaNv_12102020: Chỉ định dịch vụ theo bộ dịch vụ - L2PT-28041
				else if (fullTemp == '5') {
					var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), templateid, '5' ];
					dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
				}
				//End_HaNv_12102020
				else {
					var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), templateid, '1' ];
					dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
					// Begin_HaNv_08042019: Check bo dich vu co dieu kien tu phieu mau voi BN BHYT - L1PT-391
					if (isDvDieuKien && opt._doituongbenhnhanId == "1") {
						var dataArrAllow = [];
						var listDv = '';
						for (var h1 = 0; h1 < dataArr.length; h1++) {
							var resultDataDk = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K016.EV008", dataArr[h1].DICHVUID);
							if (resultDataDk > 0) {
								listDv = (listDv == '' ? '' : listDv + '; ') + dataArr[h1].TENDICHVU;
							} else {
								dataArrAllow.push(dataArr[h1]);
							}
						}
						if (listDv !== '') {
							DlgUtil.showMsg('Các dịch vụ trong phiếu mấu: ' + listDv + ' là các dịch vụ có điều kiện. Không được load ra từ phiếu mẫu!');
						}
						dataArr = dataArrAllow;
					}
					// End_HaNv_08042019
				}
				if (isNgaydv && opt._doituongbenhnhanId == "1") {
					for (var i8 = 0; i8 < dataArr.length; i8++) {
						//Begin_HaNv_09072019: check hạn sử dụng dịch vụ - L2PT-6517
						var par = [ dataArr[i8].DICHVUID, $('#txtMA_BHYT').val(), $('#hidBENHNHANID').val(), $('#txtTGCHIDINH').val().substr(0, 10) ];
						//End_HaNv_09072019
						var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D009.EV017", par.join('$'));
						if (resultCheck != '0') {
							DlgUtil.showMsg(dataArr[i8].TENDICHVU + ' đã được sử dụng vào ngày ' + resultCheck + '. Chưa đến hạn sử dụng tiếp theo của BHYT');
							return;
						}
					}
				}
			} else {
				if (ret.LDV == 1) {
					var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), ret.NHOMDICHVUID, '0' ];
					dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
					if (dataArr == null || dataArr.length <= 0) {
						DlgUtil.showMsg('Chưa cấu hình phòng thực hiện cho dịch vụ');
						return;
					}
				} else {
					var paramArr = [ opt._subdeptId, 3, opt._depId, '-1', opt._khambenhId, $('#txtTGCHIDINH').val(), ret.DICHVUID, '2' ];
					dataArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV0010", paramArr.join('$'));
					if (dataArr == null || dataArr.length <= 0) {
						DlgUtil.showMsg('Chưa cấu hình phòng thực hiện cho dịch vụ');
						return;
					}
				}
			}
			if (isPhongcd) {
				if (dataArr != null || dataArr.length >= 0) {
					for (var i5 = 0; i5 < dataArr.length; i5++) {
						if (dataArr[i5].PHONGID != null && dataArr[i5].PHONGID != '') {
							var par_arr = [ dataArr[i5].DICHVUID, opt._subdeptId ];
							var results = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV016", par_arr.join('$'));
							if (results != null && results.length > 0) {
								var str_arr_tmp = [];
								var str_arr = dataArr[i5].PHONGID.split(';');
								for (var i6 = 0; i6 < str_arr.length; i6++) {
									var check = false;
									for (var i7 = 0; i7 < results.length; i7++) {
										if (str_arr[i6] == results[i7].PHONGTHUCHIENID) {
											check = true;
										}
									}
									if (check) {
										str_arr_tmp.push(str_arr[i6]);
									}
								}
								if (str_arr_tmp.length > 0) {
									dataArr[i5].PHONGID = str_arr_tmp.join(';');
								} else {
									DlgUtil.showMsg('Chưa cấu hình phòng thực hiện cho dịch vụ');
									return;
								}
							} else {
								DlgUtil.showMsg('Chưa cấu hình phòng thực hiện cho dịch vụ');
								return;
							}
						}
					}
				}
			}
			if (dataArr == null || dataArr.length <= 0) {
				return;
			}
		}
		if (_selected) {
			var dataArrSub = [];
			var rowIdsSub = $('#grdDSCD').jqGrid('getDataIDs');
			for (var n2 = 0; n2 < dataArr.length; n2++) {
				var checkSub = false;
				for (var n1 = 0; n1 < rowIdsSub.length; n1++) {
					var rowDataSub = $('#grdDSCD').jqGrid('getRowData', rowIdsSub[n1]);
					if (rowDataSub.DICHVUID == dataArr[n2].DICHVUID) {
						checkSub = true;
						break;
					}
				}
				if (checkSub == false) {
					dataArrSub.push(dataArr[n2]);
					var soluongDv = 1;
					if (isShowNumService) {
						soluongDv = parseInt($('#txtSOLUONGDICHVU').val().trim());
					} else if (isShowSoLuongCls) {
						soluongDv = parseInt($('#txtSOLUONGDICHVUCLS').val().trim());
					}
					for (var n3 = 0; n3 < soluongDv - 1; n3++) {
						dataArrSub.push(dataArr[n2]);
					}
				}
			}
			dataArr = dataArrSub;
		}
		if (_selected && typeof opt._chidinhdichvu == "undefined" && isCbPd && $('#cboMACHANDOAN').val() != null && $('#cboMACHANDOAN').val() != '') {
			var dichvuidLst = '';
			for (var n4 = 0; n4 < dataArr.length; n4++) {
				if (n4 == 0) {
					dichvuidLst = dataArr[n4].DICHVUID;
				} else {
					dichvuidLst = dichvuidLst + "," + dataArr[n4].DICHVUID;
				}
			}
			var paramArrDvPd = [ dichvuidLst, $('#cboMACHANDOAN').val(), '1' ];
			var resultDvPd = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D009.EV013", paramArrDvPd.join('$'));
			if (typeof resultDvPd != 'undefined' && resultDvPd != '') {
				DlgUtil.showMsg('Dịch vụ ' + resultDvPd + ' không tồn tại trong phác đồ điều trị mã bệnh ' + $('#cboMACHANDOAN').val());
			}
		}
		if (dataArr.length == 1 && isDvDieuKien && opt._doituongbenhnhanId == '1') {
			var objDataDk = dataArr[0];
			var resultDataDk = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K016.EV008", objDataDk.DICHVUID);
			if (resultDataDk > 0) {
				var myVar = {
					thuocvattuid : objDataDk.DICHVUID,
					dieukienid : "",
					rowid : "",
					loai : 1,
					json_data : JSON.stringify(objDataDk),
					gridId : _grdId,
					hosobenhanid : opt._hosobenhanId,
					thoigianchidinh : $('#txtTGCHIDINH').val()
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgTHUOCTYLE", "divDlg", "manager.jsp?func=../ngoaitru/NGT01T096_thuoctyle", myVar, "DỊCH VỤ CÓ ĐIỀU KIỆN", 600, 550, {
					closeButton : false
				});
				DlgUtil.open("dlgTHUOCTYLE");
				return;
			}
		}
		for (var i12 = 0; i12 < dataArr.length; i12++) {
			ret = dataArr[i12];
			//Begin_HaNv_04042018: Chan/bo chan ngay dich vu BH. Neu ko chan co the chi dinh tiep DV theo gia vien phi
			if (_selected && isNgaydv && opt._doituongbenhnhanId == "1") {
				//Begin_HaNv_09072019: check hạn sử dụng dịch vụ - L2PT-6517
				var par = [ dataArr[i12].DICHVUID, $('#txtMA_BHYT').val(), $('#hidBENHNHANID').val(), $('#txtTGCHIDINH').val().substr(0, 10) ];
				//End_HaNv_09072019
				var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D009.EV017", par.join('$'));
				if (resultCheck != '0') {
					if (isNgaydvVienphi) {
						ret.GIABHYT = 0;
						DlgUtil.showMsg(ret.TENDICHVU + ' sẽ được chỉ định với giá viện phí do chưa đến hạn sử dụng tiếp theo của BHYT');
					} else {
						DlgUtil.showMsg(ret.TENDICHVU + ' đã được sử dụng vào ngày ' + resultCheck + '. Chưa đến hạn sử dụng tiếp theo của BHYT');
						return;
					}
				}
			}
			//End_HaNv_04042018
			//START -- phan he dinh duong -- hongdq -- 20180418
			if (opt._loaidichvu == '12') {
				var pars = [ 'NTU_SUATAN_TG_TRUOCAN' ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_SUATAN_TG_TRUOCAN;NTU_SUATAN_TGCD");
				var _sys_date = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
				var systemHour = _sys_date.substr(11, 2);
				var tgSD = $("#txtTGSUDUNG").val().trim().substr(0, 10)
				if (_sys_date.substr(0, 10) == tgSD) {
					if ((Number($("#txtTGSUDUNG").val().trim().substr(11, 2)) + Number(data_ar[0].NTU_SUATAN_TG_TRUOCAN)) >= Number(ret.GIO_SUATAN)) {
						if (_selected) {
							return DlgUtil.showMsg("Đã quá giờ cho phép, Không thể chỉ định cho mã " + ret.MADICHVU + " . Kiểm tra lại thời gian suất ăn!");
						}
					}
				}
				var check_par = [];
				check_par.push({
					"name" : "[0]",
					"value" : "1"
				});
				var _sys_date_add1 = jsonrpc.AjaxJson.getOneValue("COM.SYSDATE_P", check_par);
				var _sys_date_temp = _sys_date_add1.substr(0, 10);
				var tgSD = $("#txtTGSUDUNG").val().trim().substr(0, 10);
				//var tgSD_temp = moment(tgSD.substr(0,10), "DD/MM/YYYY");
				if ((ret.GIO_SUATAN == '6' || ret.GIO_SUATAN == '9') && tgSD == _sys_date_temp && (Number(systemHour) >= Number(data_ar[0].NTU_SUATAN_TGCD) && _selected)) {
					return DlgUtil.showMsg('Không được chỉ định dich vụ 6H và 9H ngày ' + tgSD);
				}
			}
			//END -- phan he dinh duong -- hongdq -- 20180418
			if (opt._doituongbenhnhanId == '1' && parseFloat(ret.GIABHYT) > 0) {
				if (_selected) {
					tongbh = parseFloat(tongbh) + parseFloat(ret.GIABHYT) * parseFloat(nvl(ret.SOLUONG, '1'));
					if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
						_insurance = nvl($('#hidQUYENLOITHE').val(), $('#txtQUYENLOI').val());
					} else {
						_insurance = nvl($('#hidQUYENLOI').val(), $('#txtQUYENLOI').val());
					}
					_insurance_full = nvl($('#hidQUYENLOI').val(), $('#txtQUYENLOI').val());
					_insurance_final = nvl($('#hidQUYENLOITHE').val(), $('#txtQUYENLOI').val());
					if (rowCount != 0 && _insurance_bf != _insurance) {
						var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
						for (var i1 = 0; i1 < rowIds.length; i1++) {
							var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[i1]);
							if (rowData.LOAIDOITUONG == '1' || rowData.LOAIDOITUONG == '2') {
								if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
									$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFINAL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFINAL;
									$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFINAL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFINAL;
									$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFINAL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFINAL;
									loadPay(0, parseFloat(rowData.BHYT_TRAFINAL) - parseFloat(rowData.BHYT_TRA));
								} else {
									$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFULL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFULL;
									$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFULL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFULL;
									$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFULL);
									$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFULL;
									loadPay(0, parseFloat(rowData.BHYT_TRAFULL) - parseFloat(rowData.BHYT_TRA));
								}
							}
						}
					}
					_insurance_bf = _insurance;
				}
			}
			var _payStart = 0;
			var _payIns = 0;
			var _payEnd = 0;
			var _benhPhamGroup = '0';
			if (_selected) {
				GridUtil.markRow(_grdId, _id);
				var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
				var r = null;
				var r_full = null;
				var r_final = null;
				var objTinhTien = new Object();
				if (opt._loaidichvu == '19') {
					objTinhTien.DOITUONGBENHNHANID = '2';
				} else {
					if (isCdNgoaigio) {
						var dayCd = $('#txtTGCHIDINH').val().substring(0, 10);
						var timeSbd = dayCd + " " + isHcSangBd;
						var timeSkt = dayCd + " " + isHcSangKt;
						var timeCbd = dayCd + " " + isHcChieuBd;
						var timeCkt = dayCd + " " + isHcChieuKt;
						var checkDay = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC.CHECK.NL", dayCd + "$" + '0');
						if (checkDay > 0) {
							objTinhTien.DOITUONGBENHNHANID = '3';
						} else {
							checkDay = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC.CHECK.NL", dayCd + "$" + '1');
							if (checkDay > 0) {
								if ((!compareDate($('#txtTGCHIDINH').val(), timeSbd, 'DD/MM/YYYY HH:mm:ss') && compareDate($('#txtTGCHIDINH').val(), timeSkt, 'DD/MM/YYYY HH:mm:ss')) ||
										(!compareDate($('#txtTGCHIDINH').val(), timeCbd, 'DD/MM/YYYY HH:mm:ss') && compareDate($('#txtTGCHIDINH').val(), timeCkt, 'DD/MM/YYYY HH:mm:ss'))) {
									objTinhTien.DOITUONGBENHNHANID = opt._doituongbenhnhanId;
								} else {
									objTinhTien.DOITUONGBENHNHANID = '3';
								}
							} else {
								if (moment(dayCd, 'DD/MM/YYYY HH:mm:ss').isoWeekday() == 6 || moment(dayCd, 'DD/MM/YYYY HH:mm:ss').isoWeekday() == 7) {
									objTinhTien.DOITUONGBENHNHANID = '3';
								} else {
									if ((!compareDate($('#txtTGCHIDINH').val(), timeSbd, 'DD/MM/YYYY HH:mm:ss') && compareDate($('#txtTGCHIDINH').val(), timeSkt, 'DD/MM/YYYY HH:mm:ss')) ||
											(!compareDate($('#txtTGCHIDINH').val(), timeCbd, 'DD/MM/YYYY HH:mm:ss') && compareDate($('#txtTGCHIDINH').val(), timeCkt, 'DD/MM/YYYY HH:mm:ss'))) {
										objTinhTien.DOITUONGBENHNHANID = opt._doituongbenhnhanId;
									} else {
										objTinhTien.DOITUONGBENHNHANID = '3';
									}
								}
							}
						}
					} else {
						objTinhTien.DOITUONGBENHNHANID = opt._doituongbenhnhanId;
					}
				}
				if (opt._loaidichvu == '14' && isTranspost) {
					objTinhTien.MUCHUONG = $('#hidTYLE_THEBHYT').val();
				} else {
					objTinhTien.MUCHUONG = parseFloat(_insurance);
				}
				if (isTinhXang && opt._loaidichvu == '14') {
					objTinhTien.GIABHYT = parseFloat(isGiaxang);
					objTinhTien.GIAND = parseFloat(isGiaxang);
					objTinhTien.GIADV = parseFloat(isGiaxang);
					objTinhTien.SOLUONG = nvl(ret.SO_LIT, '1');
				} else {
					objTinhTien.GIABHYT = parseFloat(ret.GIABHYT);
					objTinhTien.GIAND = parseFloat(ret.GIANHANDAN);
					objTinhTien.GIADV = parseFloat(ret.GIADICHVU);
					objTinhTien.SOLUONG = nvl(ret.SOLUONG, '1');
				}
				objTinhTien.CAUHINH_CGDV = config_cdgdv;
				objTinhTien.GIATRANBH = parseFloat(ret.DICHVU_BHYT_DINHMUC);
				objTinhTien.GIANN = 0;
				objTinhTien.DOITUONGCHUYEN = 0;
				objTinhTien.GIADVKTC = opt._doituongbenhnhanId == '1' && parseFloat(ret.GIA_DVC) > 0 ? ret.GIA_DVC : 0;
				objTinhTien.MANHOMBHYT = ret.MANHOM_BHYT;
				objTinhTien.CANTRENDVKTC = $('#hidCANTRENKTC').val();
				objTinhTien.THEDUTHOIGIAN = $('#hidBHFULL').val();
				objTinhTien.DUOCVANCHUYEN = $('#hidDUOCVC').val();
				objTinhTien.TYLETHUOCVATTU = 100;
				objTinhTien.NHOMDOITUONG = $('#hidNHOMDOITUONG').val();
				objTinhTien.NGAYHANTHE = $('#hidNGAYHANTHE').val();
				objTinhTien.NGAYDICHVU = $('#txtTGCHIDINH').val().substr(0, 10);
				objTinhTien.TYLE_MIENGIAM = $('#hidTYLEMIENGIAM').val();
				objTinhTien.NGAYGIAHANTHE = config_hanthe;
				if (objTinhTien.DOITUONGCHUYEN == '0' || objTinhTien.DOITUONGCHUYEN == '1' || objTinhTien.DOITUONGCHUYEN == '2') {
					objTinhTien.DAUTHE = $('#txtMA_BHYT').val().substring(0, 3);
					objTinhTien.CHUOIDAUTHE = cf_dauthe;
				}
				if (typeof opt._chidinhdichvu == "undefined") {
					if ((_opt.hospitalId == '1217' || _opt.hospitalId == '1218' || _opt.hospitalId == '1219' || _opt.hospitalId == '1220' || _opt.hospitalId == '1221' || _opt.hospitalId == '1222' ||
							_opt.hospitalId == '1223' || _opt.hospitalId == '1224' || _opt.hospitalId == '1225' || _opt.hospitalId == '1226' || _opt.hospitalId == '1227') &&
							objTinhTien.DOITUONGBENHNHANID == '1') {
						var dauthebhyt = $('#txtMA_BHYT').val().substring(2, 5);
						var dauthebhyt2 = $('#txtMA_BHYT').val().substring(0, 2);
						var dauthebhyt3 = $('#txtMA_BHYT').val().substring(0, 5);
						if (dauthebhyt2 == 'TA' || (dauthebhyt != '497' && dauthebhyt3 != 'QN597' && dauthebhyt != '297') || isTlQy15) {
							objTinhTien.TYLEDV = 0.7;
						}
					}
				}
				//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
				if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
					objTinhTien.TYLEDV = typeof ($('#cboTYLEDV_CK').val()) != "undefined" ? $('#cboTYLEDV_CK').val() : 1;
				}
				//End_HaNv_01012020
				//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
				if ($('#hidLOAIKHAM').val() != '' && $('#hidLOAIKHAM').val() != '0') {
					objTinhTien.DOITUONGCHUYEN = '4';
					objTinhTien.LOAIKHAM = $('#hidLOAIKHAM').val();
				}
				//End_HaNv_25072020
				var parJson = JSON.stringify(objTinhTien) + "@@" + JSON.stringify(opt) + "@@" + JSON.stringify(ret);
				var checkjson = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU.LOGS", parJson);
				r = vienphi.tinhtien_dichvu(objTinhTien);
				if (r.bh_tra == -1 && r.nd_tra == -1 && r.tong_cp == -1) {
					DlgUtil.showMsg('Giá tiền dịch vụ của bệnh nhân không thể bằng 0');
					return;
				}
				var objTinhTienFull = $.extend({}, objTinhTien);
				objTinhTienFull.MUCHUONG = _insurance_full;
				r_full = vienphi.tinhtien_dichvu(objTinhTienFull);
				var objTinhTienFinal = $.extend({}, objTinhTien);
				if (opt._loaidichvu == '14' && isTranspost) {
					objTinhTienFinal.MUCHUONG = $('#hidTYLE_THEBHYT').val();
				} else {
					objTinhTienFinal.MUCHUONG = _insurance_final;
				}
				r_final = vienphi.tinhtien_dichvu(objTinhTienFinal);
				_payStart = r.tong_cp;
				_payIns = r.bh_tra;
				_payEnd = r.nd_tra;
				if (ret.LOAINHOMDICHVU == '3') {
					_benhPhamGroup = '1';
				} else {
					_benhPhamGroup = ret.LOAINHOMDICHVU == '4' ? '2' : '5';
				}
				var soluong = 1;
				if (isTinhXang && opt._loaidichvu == '14') {
					soluong = nvl(ret.SO_LIT, '1');
				} else {
					soluong = nvl(ret.SOLUONG, '1');
				}
				var tendichvu_tmp = opt._loaidichvu == '12' ? ' (' + ret.MADICHVU + ') ' + ret.TENDICHVU : ret.TENDICHVU;
				var datarow = {
					NHOMDICHVUID : ret.NHOMDICHVUID,
					DICHVUID : ret.DICHVUID,
					TENDICHVU : tendichvu_tmp,
					SOLUONG : soluong,
					GIA_TIEN : _payStart / parseFloat(soluong),
					BHYT_TRA : _payIns,
					MIENGIAM : isNaN(r.mien_giam) ? '0' : r.mien_giam,
					//MIENGIAM : nvl(r.mien_giam,'0'),
					THANH_TIEN : _payEnd,
					LOAITT_CU : r.ten_loai_tt,
					PHONG_TH : ret.PHONGID,
					LOAINHOMDICHVU : ret.LOAINHOMDICHVU,
					NHOM_MABHYT_ID : ret.NHOM_MABHYT_ID,
					GIABHYT : ret.GIABHYT,
					GIANHANDAN : ret.GIANHANDAN,
					GIADICHVU : ret.GIADICHVU,
					LOAIDOITUONG : r.loai_dt,
					MAUBENHPHAMID : '',
					NHOMBENHPHAM : _benhPhamGroup,
					DICHVU_BHYT_DINHMUC : ret.DICHVU_BHYT_DINHMUC,
					LOAI_DT_MOI : '',
					GIA_CHENH : '',
					LOAITT_MOI : '',
					GIA_DVC : ret.GIA_DVC,
					MANHOM_BHYT : ret.MANHOM_BHYT,
					DONVI : ret.DONVI,
					KHOANMUCID : ret.KHOANMUCID,
					BHYT_TRAFULL : r_full.bh_tra,
					MIENGIAMFULL : r_full.mien_giam,
					THANH_TIENFULL : r_full.nd_tra,
					BHYT_TRAFINAL : r_final.bh_tra,
					MIENGIAMFINAL : r_final.mien_giam,
					THANH_TIENFINAL : r_final.nd_tra,
					DICHVUCHINH : ret.DICHVUCHINH,
					DICHVUDIKEM : ret.DICHVUDIKEM,
					OLDVALUE : soluong,
					HAS_ORDER : ret.HAS_ORDER,
					GHICHU : nvl(ret.GHICHU, ''),
					TONGTIEN : _payEnd + 0,
					TACHPHIEU : ret.TACHPHIEU,
					PHUTHU : ret.PHUTHU + '',
					LOAIMAUBENHPHAM : ret.LOAIMAUBENHPHAM, //L2PT-6394
					KHOA_DTK : ret.KHOA_DTK, //L2PT-6731
					GIACHENHLECH : ret.GIACHENHLECH
				//L2PT-10994
				};
				//Begin_HaNv_24022020: Cho phép chia giá của gói dịch vụ theo số lượng phiếu - L2PT-16531
				if (typeof opt._chidinhdichvu == "undefined" && !isDVTheoGoi && ret.DVTHEOGOI == '1' && (r.loai_dt == '4' || r.loai_dt == '6')) {
					isDVTheoGoi = true;
				}
				//End_HaNv_HaNv_24022020
				if ((isCddvCl && isKhoaYc && typeof opt._chidinhdichvu == "undefined") || isClDvLv) {
					var r_gia_chenhlech = typeof ret.GIACHENHLECH == "undefined" ? 0 : parseFloat(ret.GIACHENHLECH);
					if (r_gia_chenhlech > 0) {
						datarow.GIA_CHENH = r_gia_chenhlech;
						datarow.LOAI_DT_MOI = 6;
						if (r.loai_dt == '1') {
							datarow.LOAIDOITUONG = '2';
							datarow.LOAITT_MOI = 'BHYT + Dịch vụ';
							var nd_chenh_temp = typeof r.nd_tra_chenh == "undefined" ? 0 : parseFloat(r.nd_tra_chenh);
							datarow.TONGTIEN = parseFloat(r.nd_tra) + nd_chenh_temp;
							datarow.GIA_TIEN = datarow.GIABHYT;
						} else if (r.loai_dt == '4') {
							//Begin_HaNv_09012020: Không tính giá chênh cho BN viện phí - L2PT-14904
							if (notGiaChenhVp) {
								datarow.LOAI_DT_MOI = '';
								datarow.GIA_CHENH = 0;
							} else {
								datarow.LOAIDOITUONG = '11';
								datarow.LOAITT_MOI = 'Viện phí + Dịch vụ';
								var nd_chenh_temp = typeof r.nd_tra_chenh == "undefined" ? 0 : parseFloat(r.nd_tra_chenh);
								datarow.TONGTIEN = parseFloat(r.nd_tra) + nd_chenh_temp;
								datarow.GIA_TIEN = datarow.GIANHANDAN;
							}
							//End_HaNv_09012020
						}
					}
				}
				//Begin_HungNt_15012020: Chinh sua gia chenh lech cho cac dich vu khac - L2PT-15225
				if (typeof opt._chidinhdichvu != "undefined" && isClDvk && isKhoaYc) {
					var r_gia_chenhlech = typeof ret.GIACHENHLECH == "undefined" ? 0 : parseFloat(ret.GIACHENHLECH);
					if (r_gia_chenhlech > 0) {
						datarow.GIA_CHENH = r_gia_chenhlech;
						datarow.LOAI_DT_MOI = 6;
						if (r.loai_dt == '1') {
							datarow.LOAIDOITUONG = '2';
							datarow.LOAITT_MOI = 'BHYT + Dịch vụ';
							var nd_chenh_temp = typeof r.nd_tra_chenh == "undefined" ? 0 : parseFloat(r.nd_tra_chenh);
							datarow.TONGTIEN = parseFloat(r.nd_tra) + nd_chenh_temp;
							datarow.GIA_TIEN = datarow.GIABHYT;
						} else if (r.loai_dt == '4') {
							//Begin_HaNv_09012020: Không tính giá chênh cho BN viện phí - L2PT-14904
							if (notGiaChenhVp) {
								datarow.LOAI_DT_MOI = '';
								datarow.GIA_CHENH = 0;
							} else {
								datarow.LOAIDOITUONG = '11';
								datarow.LOAITT_MOI = 'Viện phí + Dịch vụ';
								var nd_chenh_temp = typeof r.nd_tra_chenh == "undefined" ? 0 : parseFloat(r.nd_tra_chenh);
								datarow.TONGTIEN = parseFloat(r.nd_tra) + nd_chenh_temp;
								datarow.GIA_TIEN = datarow.GIANHANDAN;
							}
							//End_HaNv_09012020
						}
					}
				}
				//End_HungNt_15012020
				if (opt._loaidichvu == '13' && isGiuongdv) {
					giuongdv = ret.MAGIUONG;
					$('#cboLOAIGIUONG').val(ret.LOAIGIUONGID).change();
				}
				if (rowIds == null || rowIds.length <= 0) {
					$('#grdDSCD').jqGrid('addRowData', 1, datarow);
				} else {
					$('#grdDSCD').jqGrid('addRowData', Math.max.apply(null, rowIds) + 1, datarow);
				}
				// lay tam tong chi phi va bao hiem trong luc cho doi cong thuc
				loadPay(_payStart, _payIns);
				if (typeof opt._chidinhdichvu == "undefined") {
					var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
					var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[rowIds.length - 1]);
					if (rowData.PHONG_TH != null && rowData.PHONG_TH != '' && rowData.PHONG_TH.indexOf("select") < 0) {
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D009.EV003", rowData.PHONG_TH);
						if (data_ar != null && data_ar.length > 0) {
							var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboKHOACHIDINHID">';
							var option = '';
							var checkExistDept = false;
							var checkEqualDept = false;
							//Begin_HaNv_12102020: Check ưu tiên phòng thực hiện của khoa hiện tại trong DS phòng - L2PT-27477
							var optionExistDept = '';
							for (var i = 0; i < data_ar.length; i++) {
								if (typeof opt._maubenhphamId != "undefined") {
									if (data_ar[i].PHONGID == opt._subdeptId) {
										checkExistDept = true;
									}
									if (data_ar[i].PHONGID != rowData.PHONG_TH1) {
										option = option + '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
									} else {
										checkEqualDept = true;
										option = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>' + option;
									}
								} else {
									if (data_ar[i].PHONGID == opt._subdeptId_login) {
										checkExistDept = true;
										optionExistDept = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
									}
									//Begin_HaNv_12102020: Check ưu tiên phòng thực hiện của khoa hiện tại trong DS phòng - L2PT-27477
									else if (data_ar[i].KHOAID == opt._depId) {
										option = '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>' + option;
									}
									//End_HaNv_12102020
									else {
										option = option + '<option value="' + data_ar[i].PHONGID + '">' + data_ar[i].PHONGNAME + '</option>';
									}
								}
							}
							if (checkExistDept) {
								if (rowData.LOAINHOMDICHVU == '5') {
									option = optionExistDept + option;
								} else {
									if (rowData.HAS_ORDER == '-1') {
										option = optionExistDept + option;
									} else {
										option = option + optionExistDept;
									}
								}
							}
							//Begin_HaNv_08092020: Ẩn phòng chỉ định vào danh sách phòng thực hiện DV PTTT - L2PT-26552 - L2PT-29789
							if (!checkExistDept && rowData.LOAINHOMDICHVU == '5' && !isHidePcd) {
								if (typeof opt._maubenhphamId != "undefined") {
									if (!checkEqualDept) {
										option = '<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>' + option;
									} else {
										option = option + '<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>';
									}
								} else {
									option = option + '<option value="' + opt._subdeptId_login + '">' + opt._subdeptName_login + '</option>';
								}
							}
							//End_HaNv_08092020
							//End_HaNv_12102020
							html = html + option + '</select>';
							$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'PHONG_TH', html);
						}
					} else {
						var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboKHOACHIDINHID">' + '<option value="' + opt._subdeptId_login + '">' +
								opt._subdeptName_login + '</option>' + '</select>';
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'PHONG_TH', html);
					}
					if (book) {
						var checkedAll = "";
						if (bookAll) {
							var checkedAll = 'checked="true"';
						}
						var htmlCheckbox = '<label class="control-label" for="' + (rowData.DICHVUID) + '_chkHEN" style="margin-left:5px; margin-bottom: 0px;"><input type="checkbox" ' + checkedAll +
								' class="mgb5" id="' + (rowData.DICHVUID) + '_chkHEN"> Hẹn</label>';
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'HEN', htmlCheckbox);
					}
					//Begin_HaNv_21062018: Danh dau thuc hien dich vu vi sinh (phat hien hoac nuoi cay) - L2K74TW-582
					if (isNhomDvViSinh) {
						var checkDvViSinh = jsonrpc.AjaxJson.getOneValue("CHECK_DV_VISINH", [ {
							"name" : "[0]",
							"value" : rowData.DICHVUID
						} ]);
						if (checkDvViSinh == '1') {
							var html = '<select class="form-control input-sm" id="' + (ret.DICHVUID) + "_" + 'cboTHUCHIENVISINH">' + '<option value="">Chọn</option>' +
									'<option value="1">Phát hiện</option>' + '<option value="2">Theo dõi</option>' + '</select>';
							$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'ISVISINH', '1');
							$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'THUCHIENVISINH', html);
						} else {
							$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'ISVISINH', '0');
						}
					}
					//End_HaNv_21062018
					//Begin_HaNv_29012019: Tinh trang PTTT (binh thuong hoac cap cuu) - L2PT-1567
					if (showTinhTrangPttt) {
						if (rowData.LOAINHOMDICHVU == '5') {
							var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboTINHTRANG_PTTT">' + '<option value="2">Chủ động</option>' +
									'<option value="1">Cấp cứu</option>' + '</select>';
							$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'TINHTRANG_PTTT', html);
						}
					}
					//End_HaNv_29012019
					//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
					if ($('#hidLOAIKHAM').val() != '' && $('#hidLOAIKHAM').val() != '0') {
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'LOAIKHAM', $('#hidLOAIKHAM').val());
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'LOAITT_MOI', 'Hợp đồng');
					}
					//End_HaNv_25072020
					//Begin_HaNv_15082020: cảnh báo chỉ định vượt tiền mức trần BHYT đã cấu hình - L2PT-25620
					if (tienMucTranBh > 0 && $('#hidLOAITIEPNHANID').val() == 1 && tongbh > tienMucTranBh) {
						DlgUtil.showMsg('Tổng tiền dịch vụ bảo hiểm là ' + tongbh + ', đã vượt tiền mức trần cấu hình là ' + tienMucTranBh);
					}
					//End_HaNv_15082020
					//Begin_HaNv_09092020: Cho phép thay đổi loại Mbp khi chỉ định dịch vụ - L2PT-26489
					if (changeLoaiMbp) {
						var html = '<select class="form-control input-sm" id="' + (rowData.DICHVUID) + "_" + 'cboLOAI_MBP">';
						html = html + '<option value="">Rỗng</option>';
						html = html + '<option value="1">Đàm</option>';
						html = html + '<option value="2">Máu</option>';
						html = html + '<option value="3">Nước tiểu</option>';
						html = html + '<option value="4">Phân</option>';
						html = html + '<option value="5">Dịch</option>';
						html = html + '<option value="6">Mủ</option>';
						html = html + '<option value="7">Khác</option></select>';
						$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'LOAIMAUBENHPHAM', html);
						$('#' + ret.DICHVUID + "_cboLOAI_MBP").val(ret.LOAI_MBP);
					}
					//End_HaNv_09092020
				} else if (opt._loaidichvu == '13') {
					var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
					var html = '<select class="form-control input-sm" id="' + (ret.DICHVUID) + "_" + 'cboTYLEDV">' + '<option value="1">100%</option>' + '<option value="0.7">70%</option>' +
							'<option value="0.5">50%</option>' + '<option value="0.3">30%</option>' + '</select>';
					$("#grdDSCD").jqGrid('setCell', rowIds[rowIds.length - 1], 'TYLEDV', html);
					$('#' + ret.DICHVUID + "_cboTYLEDV").val(1);
					$('#' + ret.DICHVUID + "_cboTYLEDV").on('change', function(e) {
						console.log('rowid.' + $(e.target).parent().parent().attr('id'));
						var rowId = $(e.target).parent().parent().attr('id');
						var rowData = $("#grdDSCD").jqGrid('getRowData', rowId);
						changeTYLEDV(rowId);
						$('#grdDSCD').jqGrid('setCell', rowId, 'TYLEDVTEMP', $('#' + rowData.DICHVUID + "_cboTYLEDV").val());
						$('#grdDSCD').jqGrid('getLocalRow', rowId).TYLEDVTEMP = $('#' + rowData.DICHVUID + "_cboTYLEDV").val();
					});
				} else {
					$("#grdDSCD").jqGrid('hideCol', 'TYLEDV');
				}
			} else {
				GridUtil.unmarkRow(_grdId, _id);
				var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
				for (var k = 0; k < rowIds.length; k++) {
					var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
					if (rowData.DICHVUID == ret.DICHVUID) {
						_payStart = parseFloat(rowData.GIA_TIEN) * parseFloat(rowData.SOLUONG);
						_payIns = parseFloat(rowData.BHYT_TRA);
						$('#grdDSCD').jqGrid('delRowData', rowIds[k]);
						break;
					}
				}
				loadPay(-_payStart, -_payIns);
				if (opt._doituongbenhnhanId == '1' && parseFloat(_payIns) > 0) {
					tongbh = tongbh - _payStart;
					if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
						_insurance = nvl($('#hidQUYENLOITHE').val(), $('#txtQUYENLOI').val());
					} else {
						_insurance = nvl($('#hidQUYENLOI').val(), $('#txtQUYENLOI').val());
					}
					_insurance_bf = _insurance;
					var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
					for (var i1 = 0; i1 < rowIds.length; i1++) {
						var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[i1]);
						if (rowData.LOAIDOITUONG == '1' || rowData.LOAIDOITUONG == '2') {
							if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFINAL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFINAL;
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFINAL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFINAL;
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFINAL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFINAL;
								loadPay(0, parseFloat(rowData.BHYT_TRAFINAL) - parseFloat(rowData.BHYT_TRA));
							} else {
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFULL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFULL;
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFULL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFULL;
								$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFULL);
								$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFULL;
								loadPay(0, parseFloat(rowData.BHYT_TRAFULL) - parseFloat(rowData.BHYT_TRA));
							}
						}
					}
				}
				var checkNuoiCay = false;
				var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
				if (jsonNuoiCay != '') {
					for (var k = 0; k < rowIds.length; k++) {
						var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
						if (rowData.JSON_NUOICAY != '' && rowData.JSON_NUOICAY != "undefined") {
							checkNuoiCay = true;
							break;
						}
					}
					if (!checkNuoiCay) {
						jsonNuoiCay = '';
					}
				}
			}
		}
		if (_selected && isShowMsgWr && $('#hidLOAITIEPNHANID').val() == 1 && tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
			DlgUtil.showMsg('Tổng tiền dịch vụ bảo hiểm vượt trần');
		}
		if (_selected && dataArr.length == 1) {
			if (dataArr[0].SHOWPOPUP == '1' && isMauCls) {
				paramInput = {
					dichvuid : dataArr[0].DICHVUID,
					tendichvu : dataArr[0].TENDICHVU
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgSubPu", "divDlg", "manager.jsp?func=../noitru/NTU01H047_DichVuMauLS", paramInput, "Cập nhật", 500, 330);
				DlgUtil.open("divDlgSubPu");
			}
			if (dataArr[0].NUOICAY == '1' && isMauCls) {
				if (jsonNuoiCay == '') {
					titleUrl = "Điều kiện cho dịch vụ nuôi cấy";
					paramInput = {
						dichvuid : dataArr[0].DICHVUID,
						benhnhanid : $('#hidBENHNHANID').val(),
						maubenhphamid : ''
					};
					var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapDKNC", "dlgNhapDKNC", "manager.jsp?func=../canlamsang/CLS001X113_DieuKienNuoiCay&type=update", paramInput, titleUrl, 1080, 500);
					dlgPopup.open("dlgNhapDKNC");
				} else {
					var jsonNuoiCayTemp = jsonNuoiCay;
					jsonNuoiCayTemp["DICHVUID"] = dataArr[0].DICHVUID;
					var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
					for (var k = 0; k < rowIds.length; k++) {
						var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
						if (rowData.DICHVUID == dataArr[0].DICHVUID) {
							$('#grdDSCD').jqGrid('setCell', rowIds[k], 'JSON_NUOICAY', jsonNuoiCayTemp);
							$('#grdDSCD').jqGrid('getLocalRow', rowIds[k]).JSON_NUOICAY = jsonNuoiCayTemp;
							break;
						}
					}
				}
			}
		}
	}
	function loadPay(_bd, _bh) {
		_bd = parseFloat(_bd);
		_bh = parseFloat(_bh);
		var tongcp = parseFloat($("#lblTONGCHIPHI").html().replaceAll('.', '').replaceAll(',', '.').replaceAll('đ', ''));
		var bhtra = parseFloat($("#lblBHTRA").html().replaceAll('.', '').replaceAll(',', '.').replaceAll('đ', ''));
		tongcp = (parseFloat(tongcp) + parseFloat(_bd)).toFixed(2);
		bhtra = (parseFloat(bhtra) + parseFloat(_bh)).toFixed(2);
		$("#lblTONGCHIPHI").html(formatNumber(tongcp) + ' đ');
		$("#lblBHTRA").html(formatNumber(bhtra) + ' đ');
		$("#lblBNTRA").html(formatNumber((tongcp - bhtra).toFixed(2)) + ' đ');
		var tamung = parseFloat($("#lblTAMUNG").html().replaceAll('.', '').replaceAll(',', '.').replaceAll('đ', ''));
		var miengiam = parseFloat($("#lblMIENGIAM").html().replaceAll('.', '').replaceAll(',', '.').replaceAll('đ', ''));
		var danop = parseFloat($("#lblDANOP").html().replaceAll('.', '').replaceAll(',', '.').replaceAll('đ', ''));
		var chenhlech = (parseFloat(tamung) - parseFloat(tongcp) + parseFloat(bhtra)).toFixed(2);
		var nopthem = (parseFloat(tamung) - parseFloat(tongcp) + parseFloat(bhtra) + parseFloat(miengiam) + parseFloat(danop)).toFixed(2);
		if (nopthem < 0) {
			//Begin_HaNv_23102018: Chan cung vươt tien tam ung-ap dung cho BN kham benh - L2HOTRO-10404
			if (flagMsg) {
				if (isShowMsgVuotTienKB) {
					DlgUtil.showMsg('Bệnh nhân có chi phí phát sinh lớn hơn tiền tạm ứng', function() {
						$('#gbox_grdXetNghiem').find('.ui-search-input').find('#gs_TENDICHVU').focus();
					});
					flagMsg = false;
					if (isChancbTu && isQn != 4 && isHtvv != 2) {//BN là quân nhân hoặc BN cấp cứu -> không chặn cứng
						$('#btnLuuIn').hide();
						$('#btnLuu').hide();
					}
				} else if (isShowMsgVuotTien) {
					DlgUtil.showMsg('Bệnh nhân có chi phí phát sinh lớn hơn tiền tạm ứng', function() {
						$('#gbox_grdXetNghiem').find('.ui-search-input').find('#gs_TENDICHVU').focus();
					});
					flagMsg = false;
					if (isChancbTu && isQn != 4 && isHtvv != 2) {//BN là quân nhân hoặc BN cấp cứu -> không chặn cứng
						$('#btnLuuIn').hide();
						$('#btnLuu').hide();
					}
				}
			}
			//End_HaNv_23102018
			$("#lblNOPTHEM").html(formatNumber(-1 * nopthem) + ' đ');
		} else {
			flagMsg = true;//đánh dấu lại chưa vượt tạm ứng
			$('#btnLuuIn').show();
			$('#btnLuu').show();
			$("#lblNOPTHEM").html('0đ');
		}
		$("#lblCHENHLECH").html(formatNumber(nopthem) + ' đ');
	}
	function loadChange(oldValue, newValue, rowId) {
		var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
		if (rowIds != null && rowIds.length > 0) {
			var _param;
			var _totalTemp = 0;
			var _insurranceTemp = 0;
			var bhyt_af = 0;
			var tyledv = 1;
			_param = $('#grdDSCD').jqGrid('getRowData', rowId);
			if (opt._loaidichvu == '13' && opt._doituongbenhnhanId == '1') {
				tyledv = $('#' + _param.DICHVUID + '_cboTYLEDV').val();
			}
			//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
			if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
				tyledv = typeof ($('#cboTYLEDV_CK').val()) != "undefined" ? $('#cboTYLEDV_CK').val() : 1;
			}
			//End_HaNv_01012020
			tongbh = parseFloat(tongbh) + parseFloat(_param.GIABHYT) * parseFloat(newValue - oldValue)
			_totalTemp = parseFloat(_param.GIA_TIEN) * parseFloat(newValue - oldValue);
			_insurranceTemp = parseFloat(_param.BHYT_TRA) / parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.BHYT_TRA);
			if (opt._doituongbenhnhanId == '1' && parseFloat(_param.GIABHYT) > 0) {
				var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
				for (var i1 = 0; i1 < rowIds.length; i1++) {
					var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[i1]);
					if ((rowData.LOAIDOITUONG == '1' || rowData.LOAIDOITUONG == '2') && rowIds[i1] != rowId) {
						if (parseFloat(tongbh) > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
							$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFINAL);
							$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFINAL;
							$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFINAL);
							$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFINAL;
							$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFINAL);
							$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFINAL;
							loadPay(0, parseFloat(rowData.BHYT_TRAFINAL) - parseFloat(rowData.BHYT_TRA));
						} else {
							$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFULL);
							$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFULL;
							$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFULL);
							$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFULL;
							$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFULL);
							$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFULL;
							loadPay(0, parseFloat(rowData.BHYT_TRAFULL) - parseFloat(rowData.BHYT_TRA));
						}
					}
				}
			}
			if (parseFloat(tongbh) > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
				bhyt_af = (parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRA = (parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRA', (parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAM = (parseFloat(_param.MIENGIAMFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAM', (parseFloat(_param.MIENGIAMFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIEN = (parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFINAL) /
						parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD')
						.jqGrid(
								'setCell',
								rowId,
								'THANH_TIEN',
								(parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFINAL) /
										parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRAFULL = (parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRAFULL', (parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAMFULL = (parseFloat(_param.MIENGIAMFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAMFULL', (parseFloat(_param.MIENGIAMFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIENFULL = (parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFULL) /
						parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD')
						.jqGrid(
								'setCell',
								rowId,
								'THANH_TIENFULL',
								(parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFULL) /
										parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRAFINAL = (parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRAFINAL', (parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAMFINAL = (parseFloat(_param.MIENGIAMFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAMFINAL', (parseFloat(_param.MIENGIAMFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIENFINAL = (parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFINAL) /
						parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD')
						.jqGrid(
								'setCell',
								rowId,
								'THANH_TIENFINAL',
								(parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFINAL) /
										parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
			} else {
				bhyt_af = (parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRA = (parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRA', (parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAM = (parseFloat(_param.MIENGIAMFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAM', (parseFloat(_param.MIENGIAMFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIEN = (parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFULL) /
						parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD')
						.jqGrid(
								'setCell',
								rowId,
								'THANH_TIEN',
								(parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFULL) /
										parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRAFULL = (parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRAFULL', (parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAMFULL = (parseFloat(_param.MIENGIAMFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAMFULL', (parseFloat(_param.MIENGIAMFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIENFULL = (parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFULL) /
						parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFULL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD')
						.jqGrid(
								'setCell',
								rowId,
								'THANH_TIENFULL',
								(parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFULL) / parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFULL) /
										parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRAFINAL = (parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRAFINAL', (parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAMFINAL = (parseFloat(_param.MIENGIAMFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAMFINAL', (parseFloat(_param.MIENGIAMFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
				$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIENFINAL = (parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFINAL) /
						parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFINAL) / parseFloat(oldValue) * parseFloat(newValue)).toFixed(2);
				$('#grdDSCD')
						.jqGrid(
								'setCell',
								rowId,
								'THANH_TIENFINAL',
								(parseFloat(_param.GIA_TIEN) * parseFloat(newValue) * parseFloat(tyledv) - parseFloat(_param.BHYT_TRAFINAL) / parseFloat(oldValue) * parseFloat(newValue) - parseFloat(_param.MIENGIAMFINAL) /
										parseFloat(oldValue) * parseFloat(newValue)).toFixed(2));
			}
			_insurranceTemp = bhyt_af - parseFloat(_param.BHYT_TRA);
			loadPay(_totalTemp, _insurranceTemp);
			$('#grdDSCD').jqGrid('setCell', rowId, 'TONGTIEN', parseFloat($('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIEN) + parseFloat($('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRA));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).TONGTIEN = parseFloat($('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIEN) + parseFloat($('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRA);
		}
	}
	function loadDefaultGrid(gridId) {
		var cdparam_arr = $("#grdDSCD").jqGrid('getRowData');
		var rowIds = $('#' + gridId).jqGrid('getDataIDs');
		for (var i = 0; i < cdparam_arr.length; i++) {
			var cdparam = cdparam_arr[i];
			for (var j = 0; j < rowIds.length; j++) {
				var rowData = $('#' + gridId).jqGrid('getRowData', rowIds[j]);
				if (cdparam.DICHVUID == rowData.DICHVUID) {
					$('#' + gridId).setSelection(rowIds[j], false);
					GridUtil.markRow(gridId, rowIds[j]);
				}
			}
		}
	}
	function loadGridAfterRemove(gridId, dichvuId) {
		var rowIds = $('#' + gridId).jqGrid('getDataIDs');
		var selRowIds = $("#" + gridId).jqGrid("getGridParam", "selarrrow");
		for (var j = 0; j < rowIds.length; j++) {
			var rowData = $('#' + gridId).jqGrid('getRowData', rowIds[j]);
			if (dichvuId == rowData.DICHVUID) {
				if ($.inArray(rowIds[j], selRowIds) >= 0) {
					$('#' + gridId).setSelection(rowIds[j], false);
					GridUtil.unmarkRow(gridId, rowIds[j]);
				}
			}
		}
	}
	function _validate() {
		if (typeof opt._chidinhdichvu == "undefined" && typeof (opt._modeFunction) == 'undefined' && config == '1') {
			if ($('#cboMACHANDOAN').val() == null || $('#cboMACHANDOAN').val() == '') {
				DlgUtil.showMsg("Chẩn đoán " + $.i18n("require"));
				return false;
			}
			/*if ($('#grdDSCD').find("input[id*='GHICHU']").length > 0) {
				DlgUtil.showMsg('Trường ghi chú đang sửa');
				return false;
			}*/
		}
		//Begin_HaNv_15082020: Fix - check GHICHU bị phụ thuộc vào cấu hình HIS_CAUHINH_YEUCAU_CHUANDOAN_DICHVU - L2PT-25893
		if (typeof opt._chidinhdichvu == "undefined" && $('#grdDSCD').find("input[id*='GHICHU']").length > 0) {
			DlgUtil.showMsg('Trường ghi chú đang sửa');
			return false;
		}
		//End_HaNv_15082020
		if (isKhoabs) {
			if ($('#cboKHOACHIDINHCHID').val() == '' || $('#cboKHOACHIDINHCHID').val() == null || $('#cboBACSIKHOACDID').val() == null || $('#cboBACSIKHOACDID').val() == '' ||
					$('#cboPHONGCHIDINHCHID').val() == null || $('#cboPHONGCHIDINHCHID').val() == '') {
				DlgUtil.showMsg("Chưa chọn khoa chỉ định, phòng, bác sĩ chỉ định");
				return false;
			}
		}
		if (typeof opt._chidinhdichvu == "undefined" && isCbQuaTg && !compareDate($('#txtTGCHIDINH').val().trim(), currentTime, 'DD/MM/YYYY HH:mm:ss')) {
			DlgUtil.showMsg("Thời gian chỉ định lớn hơn thời gian hiện tại");
			return false;
		}
		if (isBacsiKe && ($('#divLbBacsi').hasClass('required') || $('#divLbTkBacsi').hasClass('required')) && $('#cboBACSIID').val() == '') {
			DlgUtil.showMsg("Chưa chọn bác sĩ chỉ định");
			return false;
		}
		if (typeof opt._chidinhdichvu == "undefined" && isCPdt && $('#cboMAUBENHPHAMID').val() == '') {
			DlgUtil.showMsg("Chưa chọn phiếu điều trị");
			return false;
		}
		if ($('#txtTGCHIDINH').val().trim().length > 0 && !datetimeRegex.test($('#txtTGCHIDINH').val())) {
			DlgUtil.showMsg("TG chỉ định " + $.i18n("date_type_invalid"), function() {
				$('#txtTGCHIDINH').focus();
			});
			return false;
		}
		if (!compareDate($('#hidNGAYTIEPNHAN').val(), $('#txtTGCHIDINH').val().trim(), 'DD/MM/YYYY HH:mm:ss')) {
			DlgUtil.showMsg('Ngày chỉ định không được nhỏ hơn thời gian nhập viện');
			return false;
		}
		if ($('#grdDSCD').find("input[id*='SOLUONG']").length > 0) {
			DlgUtil.showMsg('Trường số lượng đang sửa');
			return false;
		}
		if (opt._loaidichvu == '13') {
			if (!/^[1-9][0-9]*$/i.test($('#txtSONGAYGIUONG').val())) {
				DlgUtil.showMsg('Trường số lượng ngày giường không hợp lệ', function() {
					$('#txtSONGAYGIUONG').focus();
				});
				return false;
			}
			if (isHienThiMaGiuong && isChanMg) {
				if ($('#cboLOAIGIUONG').val() == null || $('#cboLOAIGIUONG').val() == '') {
					DlgUtil.showMsg('Chưa chọn loại giường');
					return false;
				}
				if ($('#cboMAGIUONG').val() == null || $('#cboMAGIUONG').val() == '') {
					DlgUtil.showMsg('Chưa chọn mã giường');
					return false;
				}
			}
		}
		return true;
	}
	function changeTYLEDV(rowId) {
		if ($('#grdDSCD').find("input[id*='SOLUONG']").length > 0) {
			$('#grdDSCD').saveRow(rowId);
		}
		var _insurance = 0;
		var _insurance_full = 0;
		var _insurance_final = 0;
		var rowData = $("#grdDSCD").jqGrid('getRowData', rowId);
		if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
			_insurance = nvl($('#hidQUYENLOITHE').val(), $('#txtQUYENLOI').val());
		} else {
			_insurance = nvl($('#hidQUYENLOI').val(), $('#txtQUYENLOI').val());
		}
		_insurance_full = nvl($('#hidQUYENLOI').val(), $('#txtQUYENLOI').val());
		_insurance_final = nvl($('#hidQUYENLOITHE').val(), $('#txtQUYENLOI').val());
		var objTinhTien = new Object();
		objTinhTien.DOITUONGBENHNHANID = opt._doituongbenhnhanId;
		if (opt._loaidichvu == '14' && isTranspost) {
			objTinhTien.MUCHUONG = $('#hidTYLE_THEBHYT').val();
		} else {
			objTinhTien.MUCHUONG = parseFloat(_insurance);
		}
		objTinhTien.GIATRANBH = parseFloat(rowData.DICHVU_BHYT_DINHMUC);
		objTinhTien.GIABHYT = parseFloat(rowData.GIABHYT);
		objTinhTien.GIAND = parseFloat(rowData.GIANHANDAN);
		objTinhTien.GIADV = parseFloat(rowData.GIADICHVU);
		objTinhTien.GIANN = 0;
		objTinhTien.DOITUONGCHUYEN = 0;
		objTinhTien.GIADVKTC = opt._doituongbenhnhanId == '1' && parseFloat(rowData.GIA_DVC) > 0 ? rowData.GIA_DVC : 0;
		objTinhTien.MANHOMBHYT = rowData.MANHOM_BHYT;
		objTinhTien.SOLUONG = rowData.SOLUONG;
		objTinhTien.CANTRENDVKTC = $('#hidCANTRENKTC').val();
		objTinhTien.THEDUTHOIGIAN = $('#hidBHFULL').val();
		objTinhTien.DUOCVANCHUYEN = $('#hidDUOCVC').val();
		objTinhTien.TYLETHUOCVATTU = 100;
		objTinhTien.NHOMDOITUONG = $('#hidNHOMDOITUONG').val();
		objTinhTien.NGAYHANTHE = $('#hidNGAYHANTHE').val();
		objTinhTien.NGAYDICHVU = $('#txtTGCHIDINH').val().substr(0, 10);
		objTinhTien.TYLE_MIENGIAM = $('#hidTYLEMIENGIAM').val();
		objTinhTien.NGAYGIAHANTHE = config_hanthe;
		if (objTinhTien.DOITUONGCHUYEN == '0' || objTinhTien.DOITUONGCHUYEN == '1' || objTinhTien.DOITUONGCHUYEN == '2') {
			objTinhTien.DAUTHE = $('#txtMA_BHYT').val().substring(0, 3);
			objTinhTien.CHUOIDAUTHE = cf_dauthe;
		}
		if (opt._loaidichvu == '13') {
			objTinhTien.TYLEDV = $('#' + rowData.DICHVUID + '_cboTYLEDV').val();
		} else {
			objTinhTien.TYLEDV = 1;
		}
		var objDataChange = vienphi.tinhtien_dichvu(objTinhTien);
		var objTinhTienFull = $.extend({}, objTinhTien);
		objTinhTienFull.MUCHUONG = _insurance_full;
		var objDataFull = vienphi.tinhtien_dichvu(objTinhTienFull);
		var objTinhTienFinal = $.extend({}, objTinhTien);
		if (opt._loaidichvu == '14' && isTranspost) {
			objTinhTienFinal.MUCHUONG = $('#hidTYLE_THEBHYT').val();
		} else {
			objTinhTienFinal.MUCHUONG = _insurance_final;
		}
		var objDataFinal = vienphi.tinhtien_dichvu(objTinhTienFinal);
		if (objDataChange.bh_tra != -1 && objDataChange.bh_tra != -1 && objDataChange.nd_tra != -1) {
			var _oldBHYTDataRow = rowData.BHYT_TRA;
			var _oldTTDataRow = rowData.THANH_TIEN;
			var _newBHYTDataRow = parseFloat(objDataChange.bh_tra);
			var _newTTDataRow = parseFloat(objDataChange.tong_cp);
			var _payBHYTChange = _newBHYTDataRow - parseFloat(_oldBHYTDataRow);
			var _payTTChange = 0;
			loadPay(_payTTChange, _payBHYTChange);
			$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRA', parseFloat(objDataChange.bh_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRA = parseFloat(objDataChange.bh_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'THANH_TIEN', parseFloat(objDataChange.nd_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIEN = parseFloat(objDataChange.nd_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAM', parseFloat(objDataChange.mien_giam));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAM = parseFloat(objDataChange.mien_giam);
			$('#grdDSCD').jqGrid('setCell', rowId, 'TONGTIEN',
					parseFloat(objDataChange.nd_tra) * parseFloat(rowData.SOLUONG) - parseFloat(objDataChange.bh_tra) + parseFloat(rowData.SOLUONG) * parseFloat(nvl(rowData.GIACHENH, '0')));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).TONGTIEN = parseFloat(objDataChange.nd_tra) * parseFloat(rowData.SOLUONG) - parseFloat(objDataChange.bh_tra) + parseFloat(rowData.SOLUONG) *
					parseFloat(nvl(rowData.GIACHENH, '0'));
			$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRAFINAL', parseFloat(objDataFinal.bh_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRAFINAL = parseFloat(objDataFinal.bh_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'THANH_TIENFINAL', parseFloat(objDataFinal.nd_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIENFINAL = parseFloat(objDataFinal.nd_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAMFINAL', parseFloat(objDataFinal.mien_giam));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAMFINAL = parseFloat(objDataFinal.mien_giam);
			$('#grdDSCD').jqGrid('setCell', rowId, 'BHYT_TRAFULL', parseFloat(objDataFull.bh_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).BHYT_TRAFULL = parseFloat(objDataFull.bh_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'THANH_TIENFULL', parseFloat(objDataFull.nd_tra));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).THANH_TIENFULL = parseFloat(objDataFull.nd_tra);
			$('#grdDSCD').jqGrid('setCell', rowId, 'MIENGIAMFULL', parseFloat(objDataFull.mien_giam));
			$('#grdDSCD').jqGrid('getLocalRow', rowId).MIENGIAMFULL = parseFloat(objDataFull.mien_giam);
		} else {
			DlgUtil.showMsg('Không thể đổi tỷ lệ cho bệnh nhân này');
		}
	}
	function saveData(mode) {
		//Begin_HaNv_08052020: Cho phép chỉ định thu khác từ thuvienphi khi BN đã kết thúc BA - L2PT-20670
		if (!cpCdKetThucBA) {
			//Begin_HaNv_20022020: check Bệnh nhân đã đóng bệnh án khi chỉ định (trangthaitiepnhan = 1)
			var check_par = [];
			check_par.push({
				"name" : "[0]",
				"value" : opt._tiepnhanId
			});
			var check_trangthaitiepnhan = jsonrpc.AjaxJson.getOneValue("NTU02D049.CHECKKTBA", check_par);
			if (check_trangthaitiepnhan == "1") {
				return DlgUtil.showMsg("Bệnh nhân đã kết thúc bệnh án, không thể chỉ định dịch vụ!");
			}
			//End_HaNv_20022020
		}
		//End_HaNv_08052020
		//START -- phan he dinh duong -- hongdq -- 20180418
		if (opt._loaidichvu == '12') {
			var gtCauhinh = '';
			if ($("#txtTGSUDUNG").val() == '') {
				return DlgUtil.showMsg("Thời gian Sử dụng không được để trống");
			}
			var _tg_chidinh_full = moment($("#txtTGCHIDINH").val().trim(), 'DD/MM/YYYY HH:mm:ss');
			var _tg_sudung_full = moment($("#txtTGSUDUNG").val().trim(), 'DD/MM/YYYY HH:mm:ss');
			if (_tg_chidinh_full > _tg_sudung_full) {
				//$("#txtTGSUDUNG").focus();
				return DlgUtil.showMsg("Thời gian Chỉ định phải nhỏ hơn thời gian Sử dụng");
			}
			var pars = [ 'NTU_SUATAN_TG_TRUOCAN' ];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_SUATAN_TG_TRUOCAN;NTU_SUATAN_TGCD");
			var _sys_date = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			var _sysdate_full = moment(_sys_date, 'DD/MM/YYYY HH:mm:ss');
			if (_tg_sudung_full < _sysdate_full) {
				return DlgUtil.showMsg("Thời gian Sử dụng phải lớn hơn hiện tại");
			}
			var systemHour = _sys_date.substr(11, 2);
			var tgSD = $("#txtTGSUDUNG").val().trim().substr(0, 10)
			var selRowIds = $("#grdDichVu").jqGrid("getGridParam", "selarrrow");
			for (var j = 0; j < selRowIds.length; j++) {
				var rowData = $('#grdDichVu').jqGrid('getRowData', selRowIds[j]);
				if (_sys_date.substr(0, 10) == tgSD) {
					//if ((Number($("#txtTGSUDUNG").val().trim().substr(11, 2)) + Number(data_ar)) >= Number(rowData.GIO_SUATAN) && (Number(systemHour) >= Number(data_ar[0].NTU_SUATAN_TGCD))) {
					if ((Number($("#txtTGSUDUNG").val().trim().substr(11, 2)) + Number(data_ar[0].NTU_SUATAN_TG_TRUOCAN)) >= Number(rowData.GIO_SUATAN)) {
						return DlgUtil.showMsg("Đã quá giờ cho phép, Không thể chỉ định cho mã " + rowData.MADICHVU + " . Kiểm tra lại thời gian suất ăn!");
					}
				}
				var check_par = [];
				check_par.push({
					"name" : "[0]",
					"value" : "1"
				});
				var _sys_date_add1 = jsonrpc.AjaxJson.getOneValue("COM.SYSDATE_P", check_par);
				var _sys_date_temp = _sys_date_add1.substr(0, 10);
				var tgSD = $("#txtTGSUDUNG").val().trim().substr(0, 10);
				//var tgSD_temp = moment(tgSD.substr(0,10), "DD/MM/YYYY");
				if ((rowData.GIO_SUATAN == '6' || rowData.GIO_SUATAN == '9') && tgSD == _sys_date_temp && (Number(systemHour) >= Number(data_ar[0].NTU_SUATAN_TGCD))) {
					return DlgUtil.showMsg('Không được chỉ định dich vụ 6H và 9H ngày ' + tgSD);
				}
			}
		}
		//END -- phan he dinh duong -- hongdq -- 20180418
		//Begin_HaNv_05072018: Thời gian kết thúc giường khi kê giường
		if (showTgKetThucGiuong) {
			if ($("#txtTGKETTHUC").val() == '') {
				return DlgUtil.showMsg("Thời gian kết thúc giường không được để trống");
			}
			var timeChiDinh = moment($("#txtTGCHIDINH").val().trim(), 'DD/MM/YYYY HH:mm:ss');
			var timeKetThuc = moment($("#txtTGKETTHUC").val().trim(), 'DD/MM/YYYY HH:mm:ss');
			var tgcd = moment($("#txtTGCHIDINH").val().trim(), 'DD/MM/YYYY');
			var tgkt = moment($("#txtTGKETTHUC").val().trim(), 'DD/MM/YYYY');
			var diffDay = tgcd.diff(tgkt, 'days');
			if (timeChiDinh > timeKetThuc) {
				return DlgUtil.showMsg("Thời gian Kết thúc giường phải lớn hơn thời gian Chỉ định");
			}
			if (diffDay != 0) {
				return DlgUtil.showMsg("Thời gian Kết thúc giường phải cùng ngày với thời gian Chỉ định");
			}
		}
		//End_HaNv_05072018
		if (isCar && opt._loaidichvu == '14') {
			if ($('#cboXEID').val() == '') {
				return DlgUtil.showMsg("Bạn phải chọn xe vận chuyển");
			}
		}
		//Begin_HaNv_07052019: hien thi chandoanbandau tu khambenh - L1PT-719
		if (reqChandoanbandau) {
			if (($('#cboMACHANDOAN').val() == null || $('#cboMACHANDOAN').val() == '') && $('#txtGHICHU_BENHCHINH').val() == '') {
				DlgUtil.showMsg("Bắt buộc phải nhập dữ liệu chẩn đoán hoặc có dữ liệu chẩn đoán ban đầu");
				return false;
			}
		}
		//End_HaNv_07052019
		var param_arr = $("#grdDSCD").jqGrid('getRowData');
		//Begin_HaNv_25072020: Chỉ định dịch vụ cho BN hợp đồng - L2PT-24932
		if (typeof opt._chidinhdichvu == "undefined" && $('#hidLOAIKHAM').val() == '2') {
			var tiendv = 0;
			for (var i = 0; i < param_arr.length; i++) {
				var _param = param_arr[i];
				if (_param.LOAIKHAM == '2') {
					tiendv = tiendv + parseFloat(_param.MIENGIAM);
				}
			}
			var objData = new Object();
			objData["HOSOBENHANID"] = $('#hidHOSOBENHANID').val();
			objData["TIENDV"] = tiendv + '';
			var check = jsonrpc.AjaxJson.ajaxCALL_SP_I("CDDV.CHECK.TIENHD", JSON.stringify(objData));
			if (check == 0) {
				DlgUtil.showMsg("Số tiền BN chỉ định vượt mức cho phép tiền hợp đồng. Không được phép chỉ định!");
				return false;
			}
		}
		//End_HaNv_25072020
		//Begin_HaNv_25102019: Thêm số lượng phiếu cần nhân ra với loại phiếu PTTT - L2PT-10188 - L2PT-21002
		var data_arr = [];
		if (isSoLuongPttt && parseFloat($("#txtSOLUONGPTTT").val()) > 1) {
			//Begin_HaNv_17072020: Cho phép chỉ định nhiều ngày với nhiều DV PTTT trong 1 lần chỉ định - L2PT-24477
			for (var i = 0; i < param_arr.length; i++) {
				var _param = param_arr[i];
				_param.BHYT_TRA = _param.BHYT_TRAFINAL;
				if (param_arr[i].LOAINHOMDICHVU != 5) {
					return DlgUtil.showMsg("Chỉ được phép chỉ định dịch vụ PTTT khi nhập số lượng PTTT!");
				}
			}
			/*if (param_arr.length > 1) {
				return DlgUtil.showMsg("Chỉ được phép chỉ định 1 dịch vụ PTTT trong mỗi lần chỉ định!");
			}
			if (param_arr[0].LOAINHOMDICHVU != 5) {
				return DlgUtil.showMsg("Chỉ được phép chỉ định dịch vụ PTTT khi nhập số lượng PTTT!");
			}*/
			//End_HaNv_17072020
		}
		//End_HaNv_25102019
		if (param_arr != null && param_arr.length > 0) {
			var valid = validator.validateForm();
			if (valid) {
				if (_validate()) {
					//Begin_HaNv_22082019: Fix loi dupleClick btnLuu - L2PT-8202
					$('#btnLuuIn').hide();
					$('#btnLuu').hide();
					//End_HaNv_22082019
					var func = "";
					var map = new Object();
					var mapMain = new Object();
					if (typeof opt._chidinhdichvu != "undefined") {
						func = mode == '0' ? "NTU01H003.EV002" : "NTU01H003.EV003";
						delMbp = true;
						for (var i = 0; i < param_arr.length; i++) {
							var _param = param_arr[i];
							_param.BHYT_TRA = _param.BHYT_TRAFINAL;
							_param.MIENGIAM = _param.MIENGIAMFINAL;
							_param.PHONG_TH = '1';
							if (opt._loaidichvu == '12') {
								_param.PHONG_TH = i;
							}
							//Begin_HaNv_31072019: Chức năng duyệt thu khác (tách phiếu với từng dịch vụ) - L2PT-6731
							if (opt._loaidichvu == '20') {
								_param.PHONG_TH = i;
							}
							//End_HaNv_31072019
							if (opt._loaidichvu == '13') {
								_param.TYLEDV = $('#' + _param.DICHVUID + "_cboTYLEDV").val();
							} else {
								_param.TYLEDV = '1';
							}
							//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
							if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
								_param.TYLEDV = typeof ($('#cboTYLEDV_CK').val()) != "undefined" ? $('#cboTYLEDV_CK').val() : 1;
							}
							//End_HaNv_01012020
							var listGroup = [];
							if (typeof (map[_param.PHONG_TH || _param.PHONG_TH != '' ? _param.PHONG_TH + ',' : 'NaN,']) == "undefined") {
								listGroup.push(_param);
							} else {
								listGroup = map[_param.PHONG_TH || _param.PHONG_TH != '' ? _param.PHONG_TH + ',' : 'NaN,'];
								listGroup.push(_param);
							}
							map[_param.PHONG_TH || _param.PHONG_TH != '' ? _param.PHONG_TH + ',' : 'NaN,'] = listGroup;
							if (_param.MAUBENHPHAMID != '') {
								delMbp = false;
								mapMain[_param.PHONG_TH || _param.PHONG_TH != '' ? _param.PHONG_TH + ',' + _param.MAUBENHPHAMID : 'NaN,' + _param.MAUBENHPHAMID] = listGroup;
							}
						}
						if (typeof opt._maubenhphamId != "undefined") {
							if (delMbp) {
								map["-1," + opt._maubenhphamId] = [];
							}
						}
					} else {
						func = "NGT02K016.INS_PRT";
						delMbp = true;
						if (opt._doituongbenhnhanId == '1' || opt._doituongbenhnhanId == '2' || opt._doituongbenhnhanId == '3') {
							for (var i2 = 0; i2 < param_arr.length; i2++) {
								if (param_arr[i2].DICHVUCHINH != null && param_arr[i2].DICHVUCHINH != '') {
									var dvdikem = param_arr[i2].DICHVUDIKEM.split(';');
									for (var i3 = 0; i3 < dvdikem.length; i3++) {
										for (var i4 = 0; i4 < param_arr.length; i4++) {
											if (dvdikem[i3] == param_arr[i4].DICHVUID) {
												param_arr[i4].BHYT_TRA = '0';
												param_arr[i4].THANH_TIEN = '0';
												param_arr[i4].LOAIDOITUONG = '12';
												param_arr[i4].GIA_TIEN = '0';
												param_arr[i4].LOAI_DT_MOI = '';
											}
										}
									}
								}
							}
						}
						for (var i = 0; i < param_arr.length; i++) {
							var _param = param_arr[i];
							//Begin_HaNv_21062018: Danh dau thuc hien dich vu vi sinh (phat hien hoac nuoi cay) - L2K74TW-582
							if (_param.ISVISINH == '1' && $('#' + _param.DICHVUID + '_cboTHUCHIENVISINH').val() == '') {
								return DlgUtil.showMsg(_param.TENDICHVU + " chưa chọn loại thực hiện vi sinh");
							}
							_param.THUCHIENVISINH = $('#' + _param.DICHVUID + '_cboTHUCHIENVISINH').val();
							//End_HaNv_21062018
							//Begin_HaNv_29012019: Tinh trang PTTT (binh thuong hoac cap cuu) - L2PT-1567
							_param.TINHTRANG_PTTT = $('#' + _param.DICHVUID + '_cboTINHTRANG_PTTT').val();
							//End_HaNv_29012019
							//Begin_HaNv_09092020: Cho phép thay đổi loại Mbp khi chỉ định dịch vụ - L2PT-26489
							if (changeLoaiMbp) {
								_param.LOAI_MBP = $('#' + _param.DICHVUID + '_cboLOAI_MBP').val();
							}
							//End_HaNv_09092020
							_param.BHYT_TRA = _param.BHYT_TRAFINAL;
							_param.MIENGIAM = _param.MIENGIAMFINAL;
							_param.PHONG_TH = $('#' + _param.DICHVUID + '_cboKHOACHIDINHID').val();
							$('#' + _param.DICHVUID + '_chkHEN').is(':checked') ? '1' : '0';
							if (isGroupService) {
								_param.NHOMDICHVUID = '1';
							} else {
								if (isTachCdha && _param.LOAINHOMDICHVU == 4 && isNhomDvKtp.indexOf(_param.NHOMDICHVUID) == -1) {
									_param.NHOMDICHVUID = _param.NHOMDICHVUID + "_" + i;
								}
							}
							if (isTachPttt && _param.LOAINHOMDICHVU == 5) {
								_param.NHOMDICHVUID = _param.NHOMDICHVUID + "_" + i;
							}
							if (_param.TACHPHIEU == '1') {
								_param.NHOMDICHVUID = _param.NHOMDICHVUID + "_" + i;
							}
							//Begin_HaNv_06072018: Cau hinh de tach phieu theo doi tuong BN: BHYT va thu phi - L2DKBD-1342
							if (isTachPhieuTheoLoaiDT) {
								if (_param.LOAIDOITUONG == '1' || _param.LOAIDOITUONG == '2') { //Doi tuong BHYT
									_param.NHOMDICHVUID = _param.NHOMDICHVUID + "_1";
								} else { //Doi tuong thu phi
									_param.NHOMDICHVUID = _param.NHOMDICHVUID + "_2";
								}
							}
							//End_HaNv_06072018
							//Begin_HaNv_10102019: Gộp chung 1 số phiếu với các DV xét nghiệm khác nhóm - L2PT-9671
							if (isGomNhomXn && _param.LOAINHOMDICHVU == 3) {
								_param.NHOMDICHVUID = '1';
							}
							//End_HaNv_10102019
							//Begin_HaNv_04052020: Chuyển đổi khám sức khỏe theo đoàn - L2PT-20170
							if (opt.formCall == 'KSK' && _param.LOAIDOITUONG == '30') {
								_param.NHOMDICHVUID = _param.NHOMDICHVUID + "_1";
							}
							//End_HaNv_04052020
							if ($('#' + _param.DICHVUID + '_chkHEN').is(':checked')) {
								_param.HEN = '1';
								appointment = true;
							} else {
								_param.HEN = '0';
							}
							if (typeof _param.TYLEDVTEMP != "undefined" && _param.TYLEDVTEMP != '' && _param.TYLEDVTEMP != '1' && _param.TYLEDVTEMP != null) {
								_param.TYLEDV = _param.TYLEDVTEMP;
							} else {
								if (typeof _param.TYLEDV == "undefined" || _param.TYLEDV == "") {
									_param.TYLEDV = '1';
								} else {
									_param.TYLEDV = parseInt(_param.TYLEDV) / 100;
								}
							}
							var listGroup = [];
							if (typeof (map[_param.NHOMDICHVUID + "," + _param.PHONG_TH + "," + _param.NHOMBENHPHAM + ","]) == "undefined") {
								listGroup.push(_param);
							} else {
								listGroup = map[_param.NHOMDICHVUID + "," + _param.PHONG_TH + "," + _param.NHOMBENHPHAM + ","];
								listGroup.push(_param);
							}
							map[_param.NHOMDICHVUID + "," + _param.PHONG_TH + "," + _param.NHOMBENHPHAM + ","] = listGroup;
							if (_param.MAUBENHPHAMID != '') {
								delMbp = false;
								mapMain[_param.NHOMDICHVUID + "," + _param.PHONG_TH + "," + _param.NHOMBENHPHAM + "," + _param.MAUBENHPHAMID] = listGroup;
							}
						}
						if (typeof opt._maubenhphamId != "undefined") {
							if (delMbp) {
								map["-1," + "-1," + "-1," + opt._maubenhphamId] = [];
							}
						}
					}
					if (typeof opt._maubenhphamId != "undefined") {
						Object.keys(mapMain).forEach(function(keyM) {
							Object.keys(map).forEach(function(key) {
								if (keyM.indexOf(key) !== -1) {
									map[keyM] = map[key];
									delete map[key];
								}
							});
						});
					}
					//Begin_HaNv_26112019: Cho phép gen mẫu bệnh phẩm theo số lượng - L2PT-12306
					if (isNhapSoLuongMbp && parseFloat($("#txtSOLUONGMBPCLS").val()) > 1) {
						var mapMbp = new Object();
						for (var i = 1; i <= parseFloat($("#txtSOLUONGMBPCLS").val()); i++) {
							Object.keys(map).forEach(function(_key) {
								//Begin_HaNv_03022020: Tạo phiếu CLS nhiều ngày - L2PT-15476
								var timeCDNew = '';
								if (i > 1) {
									var timeCD = $("#txtTGCHIDINH").val();
									var timeAdd = moment(timeCD.substr(0, 10), "DD/MM/YYYY");
									timeAdd.add(i - 1, 'days');
									timeCDNew = moment(timeAdd).format('DD/MM/YYYY') + ' ' + timeCD.substr(11, 8);
								}
								//End_HaNv_03022020
								var _keyMbp = _key.split(',')[0] + i + _key.slice(_key.indexOf(',')) + ' ,' + timeCDNew;
								mapMbp[_keyMbp] = map[_key];
							});
						}
						map = mapMbp;
					}
					//End_HaNv_26112019
					//Begin_HaNv_18052020: Điều chỉnh gen mbp PTTT - L2PT-21002
					if (isSoLuongPttt && parseFloat($("#txtSOLUONGPTTT").val()) > 1) {
						var mapMbp = new Object();
						for (var i = 1; i <= parseFloat($("#txtSOLUONGPTTT").val()); i++) {
							Object.keys(map).forEach(function(_key) {
								var timeCDNew = '';
								if (i > 1) {
									if ($('#hidLOAITIEPNHANID').val() == 1) {
										timeCDNew = '';
									} else {
										var timeCD = $("#txtTGCHIDINH").val();
										var timeAdd = moment(timeCD.substr(0, 10), "DD/MM/YYYY");
										timeAdd.add(i - 1, 'days');
										timeCDNew = moment(timeAdd).format('DD/MM/YYYY') + ' ' + timeCD.substr(11, 8);
									}
								}
								//End_HaNv_03022020
								var _keyMbp = _key.split(',')[0] + i + _key.slice(_key.indexOf(',')) + ' ,' + timeCDNew;
								mapMbp[_keyMbp] = map[_key];
							});
						}
						map = mapMbp;
					}
					//End_HaNv_18052020
					var _msggg = "";
					if (!isSaveNotMsg) {
						_msggg = "Chỉ định dịch vụ thành công";
					}
					var objData = new Object();
					FormUtil.setFormToObject("divData", "", objData);
					if ($('#txtCHANDOAN_KT').val() != '') {
						objData.CHANDOAN_KT = $('#txtCHANDOAN_KT').val();
					}
					objData.HEN = appointment ? "1" : "0";
					objData.KHAN = objData.KHAN == '0' ? '1' : '2';
					objData.QUYENLOI = $('#txtQUYENLOI').val();
					objData.DICHVUKHAMBENHCHAID = typeof (opt._dichvukhambenhId) != "undefined" ? opt._dichvukhambenhId : "";
					objData.DICHVUIDKHAC = opt._dichvuidKhac;
					objData.DEPTID = opt._depId;
					objData.SUBDEPT = opt._subdeptId;
					objData.MODEDEPT = opt._subdeptId != "-1" ? "1" : "0";
					objData.LOAIPHIEUBP = typeof (opt._loaiphieumbp) != "undefined" ? opt._loaiphieumbp + '' : "-1";
					objData.SETKHOACD = isKhoabs ? "1" : "0";
					objData.TGKETTHUC = showTgKetThucGiuong ? $("#txtTGKETTHUC").val().trim() : "";//HaNv_05072018
					objData.MODETIEPDON = opt.modeTiepDon; //HaNv_26072018: Nhap benh nhan tu khu tiep don truoc khi vao khoa
					objData.DVTHEOGOI = isDVTheoGoi ? "1" : "0"; //HaNv_24022020: Cho phép chia giá của gói dịch vụ theo số lượng phiếu - L2PT-16531
					objData.MAUBENHPHAM_LINHMAU = opt.maubenhpham_linhmau;//HaNv_27052020: L2PT-21223
					objData.MBPID_BOSUNGDV = opt.mbpid_bosungdv;//HaNv_17092020: L2PT-26516
					var slCls = parseFloat($("#txtSOLUONGMBPCLS").val());
					objData.SOLUONGMBPCLS = (slCls > 1) ? $("#txtSOLUONGMBPCLS").val() : '1';
					objData.LOAIKHAM = $('#hidLOAIKHAM').val();//HaNv_25072020: L2PT-24932
					var _par = [ JSON.stringify(objData), JSON.stringify(map) ];
					if (mode == '0') {
						var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S(func, _par.join('$'));
						if (ret == 0) {
							DlgUtil.showMsg('Có lỗi khi chỉ định dịch vụ');
							//Begin_HaNv_22082019: Fix loi dupleClick btnLuu - L2PT-8202
							$('#btnLuuIn').show();
							$('#btnLuu').show();
							//End_HaNv_22082019
						} else {
							if (isLuuTiep) {
								var tgSD = $("#txtTGSUDUNG").val();
								var new_date = moment(tgSD.substr(0, 10), "DD/MM/YYYY");
								new_date.add(1, 'days');
								tgSDLuuTiep = moment(new_date).format('DD/MM/YYYY') + ' ' + tgSD.substr(11, 8);
								EventUtil.raiseEvent("assignSevice_saveChiDinhDichVuTiepOk", {
									msg : _msggg,
									tgtiep : tgSDLuuTiep
								});
								return;
							} else {
								var arr_mbp = '';
								if (typeof opt._chidinhdichvu == "undefined") {
									if (LIS_CONNECTION_TYPE == "1" || LIS_CONNECTION_TYPE == "2" && LIS_SERVICE_DOMAIN_NAME != "") {
										arr_mbp = ret.split(';');
										for (var i = 0; i < arr_mbp.length; i++) {
											if (arr_mbp[i] != '') {
												var param = arr_mbp[i].split('-');
												var funcName = '';
												if (param[1] == '1') {
													SendRequestToLab(param[2], param[0], param[3]);
												}
											}
										}
									}
								}
								//Begin_HaNv_09102020: Tao goi vat tu di kem ngay giuong - L2PT-9828 - L2PT-28565
								else {
									var arr_res = ret.split('@');
									arr_mbp = arr_res[0].split(';');
									var checkError = arr_res[1];
									if (isMapGiuongVattu) {
										var lstmbpid = '';
										for (var i = 0; i < arr_mbp.length; i++) {
											if (arr_mbp[i] != '') {
												var param = arr_mbp[i].split('-');
												lstmbpid = (lstmbpid == '') ? param[0] : (lstmbpid + ',' + param[0]);
											}
										}
										var rt = jsonrpc.AjaxJson.ajaxCALL_SP_S('CDDV.GIUONG_VATTU', lstmbpid);
										if (rt == '1') {
											_msggg = _msggg + '. Kê thuốc, vật tư đi kèm thành công';
										} else if (isNaN(rt)) {
											_msggg = _msggg + '. ' + rt;
										}
									}
								}
								//End_HaNv_09102020
								EventUtil.raiseEvent("assignSevice_saveChiDinhDichVuOk", {
									msg : _msggg,
									result : ret
								});
							}
						}
					} else {
						var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S(func, _par.join('$'));
						if (ret == 0) {
							DlgUtil.showMsg('Có lỗi khi chỉ định dịch vụ');
							//Begin_HaNv_22082019: Fix loi dupleClick btnLuu - L2PT-8202
							$('#btnLuuIn').show();
							$('#btnLuu').show();
							//End_HaNv_22082019
						} else {
							//Begin_HaNv_09102020: Tao goi vat tu di kem ngay giuong - L2PT-9828 - L2PT-28565
							if (typeof opt._chidinhdichvu == "undefined") {
								arr_mbp = ret.split(';');
							} else {
								var arr_res = ret.split('@');
								arr_mbp = arr_res[0].split(';');
								var checkError = arr_res[1];
								if (isMapGiuongVattu) {
									var lstmbpid = '';
									for (var i = 0; i < arr_mbp.length; i++) {
										if (arr_mbp[i] != '') {
											var param = arr_mbp[i].split('-');
											lstmbpid = (lstmbpid == '') ? param[0] : (lstmbpid + ',' + param[0]);
										}
									}
									var rt = jsonrpc.AjaxJson.ajaxCALL_SP_S('CDDV.GIUONG_VATTU', lstmbpid);
									if (rt == '1') {
										_msggg = _msggg + '. Kê thuốc, vật tư đi kèm thành công';
									} else if (isNaN(rt)) {
										_msggg = _msggg + '. ' + rt;
									}
								}
							}
							//End_HaNv_09102020
							//Begin_HaNv_20092019: Tích hợp js phiếu áo vàng theo yc HuongPv - L2PT-8810
							if (isPrintPhieuAoVang) {
								for (var i = 0; i < arr_mbp.length; i++) {
									if (arr_mbp[i] != '') {
										var param = arr_mbp[i].split('-');
										var par = [ {
											name : 'maubenhphamid',
											type : 'String',
											value : param[0]
										} ];
										openReport('window', 'PHIEU_AOVANG_A4', 'pdf', par);
									}
								}
							}
							//End_HaNv_20092019
							//START in phieu chi dinh suat an 
							if (typeof opt._chidinhdichvu != "undefined" && opt._loaiphieumbp == '11') {
								for (var i = 0; i < arr_mbp.length; i++) {
									if (arr_mbp[i] != '') {
										var param = arr_mbp[i].split('-');
										var par = [ {
											name : 'maubenhphamid',
											type : 'String',
											value : param[0]
										} ];
										openReport('window', 'PHIEU_CHIDINH_SUATAN', 'pdf', par);
									}
								}
								if (isLuuTiep) {
									var tgSD = $("#txtTGSUDUNG").val();
									var new_date = moment(tgSD.substr(0, 10), "DD/MM/YYYY");
									new_date.add(1, 'days');
									tgSDLuuTiep = moment(new_date).format('DD/MM/YYYY') + ' ' + tgSD.substr(11, 8);
									EventUtil.raiseEvent("assignSevice_saveChiDinhDichVuTiepOk", {
										msg : _msggg,
										tgtiep : tgSDLuuTiep
									});
									return;
								} else {
									EventUtil.raiseEvent("assignSevice_saveChiDinhDichVuOk", {
										msg : _msggg
									});
									return;
								}
							}
							//END in phieu chi dinh suat an 
							//START L2PT-1939
							if (typeof opt._chidinhdichvu != "undefined" && opt._loaiphieumbp == '16' && opt._loaidichvu == '14' && _opt.hospitalId == '957') {
								var par = [ {
									name : 'khambenhid',
									type : 'String',
									value : $("#hidKHAMBENHID").val()
								} ];
								openReport('window', 'GIAY_TUNGUYEN_THUEXE_A5', 'pdf', par);
							}
							//END L2PT-1939
							if (isMauCls) {
								var nhomMauBenhPhamId = '';
								for (var i = 0; i < arr_mbp.length; i++) {
									if (arr_mbp[i] != '') {
										var param = arr_mbp[i].split('-');
										nhomMauBenhPhamId = nhomMauBenhPhamId == '' ? param[0] : nhomMauBenhPhamId + "," + param[0];
									}
								}
								if (nhomMauBenhPhamId != '') {
									var checkMauCls = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H047.EV003", nhomMauBenhPhamId);
									if (checkMauCls > 0) {
										var parCheckMauCls = [ {
											name : 'maubenhphamid',
											type : 'String',
											value : nhomMauBenhPhamId
										} ];
										CommonUtil.inPhieu('window', 'PHIEU_CHINHDINH_CLS_1028', isMauPrint, parCheckMauCls, 'PHIEU_CHINHDINH_CLS_1028.' + isMauPrint);
										openReport('window', "PHIEU_CHINHDINH_CLS_1028", "pdf", parCheckMauCls);
									}
								}
							}
							if (printPrivare) {
								for (var i = 0; i < arr_mbp.length; i++) {
									if (arr_mbp[i] != '') {
										var param = arr_mbp[i].split('-');
										var funcName = '';
										var par = [ {
											name : 'maubenhphamid',
											type : 'String',
											value : param[0]
										}, {
											name : 'i_hid',
											type : 'String',
											value : _opt.hospitalId
										}, {
											name : 'i_sch',
											type : 'String',
											value : _opt.dbschema
										} ];
										if (param[1] == '1') {
											// tuonglt them in phieu thanh 2 loai doi tuong bhyt va thu phi
											if (_opt.hospitalId == 965) {
												if (opt._doituongbenhnhanId == '1') {
													// lay loai dich vu la bhyt hay thuong
													var _loaidichvu = 0;
													var _par_loai = param[0];
													var arr_loaidichvu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075_LOAIDICHVU", _par_loai);
													if (arr_loaidichvu != null && arr_loaidichvu.length > 0) {
														for (var i1 = 0; i1 < arr_loaidichvu.length; i1++) {
															_loaidichvu = arr_loaidichvu[i1].BHYT;
															if (_loaidichvu == 1) {
																if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
																	var rpName = "VNPTHIS_IN_A4_CLS_";
																	rpName += $('#txtMABENHNHAN').val();
																	rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
																	rpName += "." + _typePrint;
																	CommonUtil.inPhieu('window', 'PHIEU_XETNGHIEM_A4_965', _typePrint, par, rpName);
																} else {
																	openReport('window', "PHIEU_XETNGHIEM_A4_965", "pdf", par);
																}
															} else if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
																var rpName = "VNPTHIS_IN_A4_CLS_";
																rpName += $('#txtMABENHNHAN').val();
																rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
																rpName += "." + _typePrint;
																CommonUtil.inPhieu('window', 'PHIEU_XETNGHIEMDICHVU_A4_965', _typePrint, par, rpName);
															} else {
																openReport('window', "PHIEU_XETNGHIEMDICHVU_A4_965", "pdf", par);
															}
														}
													}
												} else {
													if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
														var rpName = "VNPTHIS_IN_A4_CLS_";
														rpName += $('#txtMABENHNHAN').val();
														rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
														rpName += "." + _typePrint;
														CommonUtil.inPhieu('window', 'PHIEU_XETNGHIEMDICHVU_A4_965', _typePrint, par, rpName);
													} else {
														openReport('window', "PHIEU_XETNGHIEMDICHVU_A4_965", "pdf", par);
													}
												}
											} else {
												if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
													var rpName = "VNPTHIS_IN_A5_CLS_";
													rpName += $('#txtMABENHNHAN').val();
													rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
													rpName += "." + _typePrint;
													CommonUtil.inPhieu('window', 'PHIEU_XETNGHIEM', _typePrint, par, rpName);
												} else {
													if (usingReportCode) {//HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
														var _par_code = [ param[0] ];
														var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE", _par_code.join('$'));
														if (i_report_code != null && i_report_code.length > 0) {
															for (var i1 = 0; i1 < i_report_code.length; i1++) {
																var _report_code = i_report_code[i1].REPORT_CODE;
																var par_rpt = [ {
																	name : 'maubenhphamid',
																	type : 'String',
																	value : param[0]
																}, {
																	name : 'report_code',
																	type : 'String',
																	value : _report_code
																} ];
																openReport('window', _report_code, "pdf", par_rpt);
															}
														} else {
															var par_rpt = [ {
																name : 'maubenhphamid',
																type : 'String',
																value : param[0]
															}, {
																name : 'report_code',
																type : 'String',
																value : 'PHIEU_XETNGHIEM_CHUNG'
															} ];
															openReport('window', "PHIEU_XETNGHIEM_CHUNG", "pdf", par_rpt);
														}
														var i_check_hiv = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.CHIDINH.HIV", _par_code.join('$'));
														if (i_check_hiv > 0) {
															openReport('window', "PHIEUDONGY_XETNGHIEMHIV_A4_951", "pdf", par);
														}
													}
													//Begin_HaNv_16052018: bo sung phan in tach phieu HISL2TK-423
													else if (_opt.hospitalId == 957) {
														var _par_code = [ param[0] ];
														var lstDoiTuong = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.LOAI.DTCLS", _par_code.join('$'));
														if (lstDoiTuong != null && lstDoiTuong.length > 0) {
															for (var i1 = 0; i1 < lstDoiTuong.length; i1++) {
																var _loaidoituong = lstDoiTuong[i1].LOAIDOITUONG;
																var par_rpt = [ {
																	name : 'maubenhphamid',
																	type : 'String',
																	value : param[0]
																}, {
																	name : 'i_loaidoituong',
																	type : 'String',
																	value : _loaidoituong
																} ];
																openReport('window', "PHIEU_XETNGHIEM", "pdf", par_rpt);
															}
														}
													}
													//End_HaNv_16052018
													//Begin_HaNv_24092019: Tich hop report CLS - nguoiphoihop: TuyenDv - L2PT-8688
													else if (_opt.hospitalId == 987) {
														if ($('#hidLOAITIEPNHANID').val() == '1') { //khambenh
															openReport('window', "NGT_PHIEU_XETNGHIEM_A4", "pdf", par);
														} else {
															openReport('window', "PHIEU_XETNGHIEM_A4", "pdf", par);
														}
													}
													//End_HaNv_24092019
													else {
														if (_opt.hospitalId != 960) {
															openReport('window', "PHIEU_XETNGHIEM", "pdf", par);
														}
													}
												}
											}
											if (LIS_CONNECTION_TYPE == "1" || LIS_CONNECTION_TYPE == "2" && LIS_SERVICE_DOMAIN_NAME != "") {
												SendRequestToLab(param[2], param[0], param[3]);
											}
										} else if (param[1] == '2') {
											// tuonglt them in phieu thanh 2 loai doi tuong bhyt va thu phi
											if (_opt.hospitalId == 965) {
												if (opt._doituongbenhnhanId == '1') {
													// lay loai dich vu la bhyt hay thuong
													var _loaidichvuc = 0;
													var _par_loaic = param[0];
													var arr_loaidichvuc = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075_LOAIDICHVU", _par_loaic);
													if (arr_loaidichvuc != null && arr_loaidichvuc.length > 0) {
														for (var i2 = 0; i2 < arr_loaidichvuc.length; i2++) {
															_loaidichvuc = arr_loaidichvuc[i2].BHYT;
															if (_loaidichvuc == 1) {
																if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
																	var rpName = "VNPTHIS_IN_A4_CLS_";
																	rpName += $('#txtMABENHNHAN').val();
																	rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
																	rpName += "." + _typePrint;
																	CommonUtil.inPhieu('window', 'PHIEU_CDHA_A4_965', _typePrint, par, rpName);
																} else {
																	openReport('window', "PHIEU_CDHA_A4_965", "pdf", par);
																}
															} else {
																if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
																	var rpName = "VNPTHIS_IN_A4_CLS_";
																	rpName += $('#txtMABENHNHAN').val();
																	rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
																	rpName += "." + _typePrint;
																	CommonUtil.inPhieu('window', 'PHIEU_CDHADICHVU_A4_965', _typePrint, par, rpName);
																} else {
																	openReport('window', "PHIEU_CDHADICHVU_A4_965", "pdf", par);
																}
															}
														}
													}
												} else {
													if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
														var rpName = "VNPTHIS_IN_A4_CLS_";
														rpName += $('#txtMABENHNHAN').val();
														rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
														rpName += "." + _typePrint;
														CommonUtil.inPhieu('window', 'PHIEU_CDHADICHVU_A4_965', _typePrint, par, rpName);
													} else {
														openReport('window', "PHIEU_CDHADICHVU_A4_965", "pdf", par);
													}
												}
											} else {
												if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
													var rpName = "VNPTHIS_IN_A5_CLS_";
													rpName += $('#txtMABENHNHAN').val();
													rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
													rpName += "." + _typePrint;
													//Begin_HaNv_24092019: Tich hop report CLS - nguoiphoihop: TuyenDv - L2PT-8688
													if (_opt.hospitalId == 987) {
														if ($('#hidLOAITIEPNHANID').val() == '1') { //khambenh
															if (opt._doituongbenhnhanId == '1') {
																CommonUtil.inPhieu('window', 'NGT_PHIEU_CDHA_BH', _typePrint, par, rpName);
															} else {
																CommonUtil.inPhieu('window', 'NGT_PHIEU_CDHA_DV', _typePrint, par, rpName);
															}
														} else {
															if (opt._doituongbenhnhanId == '1') {
																CommonUtil.inPhieu('window', 'PHIEU_CDHA_BH', _typePrint, par, rpName);
															} else {
																CommonUtil.inPhieu('window', 'PHIEU_CDHA_DV', _typePrint, par, rpName);
															}
														}
													} else {
														CommonUtil.inPhieu('window', 'PHIEU_CDHA', _typePrint, par, rpName);
													}
													//End_HaNv_24092019
												} else {
													if (usingReportCode) {//HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
														var _par_code = [ param[0] ];
														var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE", _par_code.join('$'));
														if (i_report_code != null && i_report_code.length > 0) {
															for (var i1 = 0; i1 < i_report_code.length; i1++) {
																var _report_code = i_report_code[i1].REPORT_CODE;
																var par_rpt = [ {
																	name : 'maubenhphamid',
																	type : 'String',
																	value : param[0]
																}, {
																	name : 'report_code',
																	type : 'String',
																	value : _report_code
																} ];
																openReport('window', _report_code, "pdf", par_rpt);
															}
														} else {
															var par_rpt = [ {
																name : 'maubenhphamid',
																type : 'String',
																value : param[0]
															}, {
																name : 'report_code',
																type : 'String',
																value : 'PHIEU_CDHA_A4'
															} ];
															openReport('window', "PHIEU_CDHA_A4", "pdf", par_rpt);
														}
													}
													//Begin_HaNv_16052018: bo sung phan in tach phieu HISL2TK-423
													else if (_opt.hospitalId == 957) {
														var _par_code = [ param[0] ];
														var lstDoiTuong = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.LOAI.DTCLS", _par_code.join('$'));
														if (lstDoiTuong != null && lstDoiTuong.length > 0) {
															for (var i1 = 0; i1 < lstDoiTuong.length; i1++) {
																var _loaidoituong = lstDoiTuong[i1].LOAIDOITUONG;
																var par_rpt = [ {
																	name : 'maubenhphamid',
																	type : 'String',
																	value : param[0]
																}, {
																	name : 'i_loaidoituong',
																	type : 'String',
																	value : _loaidoituong
																} ];
																openReport('window', "PHIEU_CDHA", "pdf", par_rpt);
															}
														}
													}
													//End_HaNv_16052018
													//START L2PT-4123
													else if (_opt.hospitalId == 987) {
														if (opt._doituongbenhnhanId == '1') {
															CommonUtil.inPhieu('window', 'PHIEU_CDHA_BH', _typePrint, par, rpName);
														} else {
															CommonUtil.inPhieu('window', 'PHIEU_CDHA_DV', _typePrint, par, rpName);
														}
													}
													//END L2PT-4123
													//Begin_HaNv_29102020: Tích hợp js mẫu phiếu chỉ định Điện Não - L2PT-29628
													else if (_opt.hospitalId == 10284) {
														var manhomdichvu = jsonrpc.AjaxJson.ajaxCALL_SP_S("GET.MA.NHOMDV", param[0]);
														if (typeof manhomdichvu != 'undefined' && manhomdichvu != '' && manhomdichvu.indexOf("DIENN") > -1) {
															var par = [ {
																name : 'maubenhphamid',
																type : 'String',
																value : param[0]
															} ];
															openReport('window', "PHIEU_CDHA_DIENN_10284", "pdf", par);
														}
													}
													//End_HaNv_29102020
													else {
														openReport('window', "PHIEU_CDHA", "pdf", par);
													}
												}
											}
											// funcName = 'PHIEU_CDHA';
										}
										// nghiant 20092017 L2DKBD-404
										else if (checkInthukhac) {
											if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
												var rpName = "VNPTHIS_IN_A5_CLS_";
												rpName += $('#txtMABENHNHAN').val();
												rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
												rpName += "." + _typePrint;
												//START L1PT-1470
												if (opt.hospitalId == '1077') {
													var par = [ {
														name : 'khambenhid',
														type : 'String',
														value : $("#hidKHAMBENHID").val()
													}, {
														name : 'maubenhphamid',
														type : 'String',
														value : param[0]
													} ];
													openReport('window', "PHIEU_SUDUNG_DVKT_1077", "pdf", par);
												} else {
													CommonUtil.inPhieu('window', 'DKBD_PCD_THEM_CONG_KHAM_A5', _typePrint, par, rpName);
												}
											} else {
												//START L1PT-1470
												if (opt.hospitalId == '1077') {
													var par = [ {
														name : 'khambenhid',
														type : 'String',
														value : $("#hidKHAMBENHID").val()
													}, {
														name : 'maubenhphamid',
														type : 'String',
														value : param[0]
													} ];
													openReport('window', "PHIEU_SUDUNG_DVKT_1077", "pdf", par);
												} else {
													openReport('window', "DKBD_PCD_THEM_CONG_KHAM_A5", "pdf", par);
												}
											}
										}
										// end nghiant 20092017 L2DKBD-404
										else {
											// tuonglt them in phieu thanh 2 loai doi tuong bhyt va thu phi
											if (_opt.hospitalId == 965) {
												if (opt._doituongbenhnhanId == '1') {
													// lay loai dich vu la bhyt hay thuong
													var _loaidichvud = 0;
													var _par_loaid = param[0];
													var arr_loaidichvud = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075_LOAIDICHVU", _par_loaid);
													if (arr_loaidichvud != null && arr_loaidichvud.length > 0) {
														for (var i3 = 0; i3 < arr_loaidichvud.length; i3++) {
															_loaidichvud = arr_loaidichvud[i3].BHYT;
															if (_loaidichvud == 1) {
																if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
																	var rpName = "VNPTHIS_IN_A4_CLS_";
																	rpName += $('#txtMABENHNHAN').val();
																	rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
																	rpName += "." + _typePrint;
																	CommonUtil.inPhieu('window', 'PHIEU_PTTT_A4_965', _typePrint, par, rpName);
																} else {
																	openReport('window', "PHIEU_PTTT_A4_965", "pdf", par);
																}
															} else {
																if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
																	var rpName = "VNPTHIS_IN_A4_CLS_";
																	rpName += $('#txtMABENHNHAN').val();
																	rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
																	rpName += "." + _typePrint;
																	CommonUtil.inPhieu('window', 'PHIEU_PTTTDICHVU_A4_965', _typePrint, par, rpName);
																} else {
																	openReport('window', "PHIEU_PTTTDICHVU_A4_965", "pdf", par);
																}
															}
														}
													}
												} else {
													if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
														var rpName = "VNPTHIS_IN_A4_CLS_";
														rpName += $('#txtMABENHNHAN').val();
														rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
														rpName += "." + _typePrint;
														CommonUtil.inPhieu('window', 'PHIEU_PTTTDICHVU_A4_965', _typePrint, par, rpName);
													} else {
														openReport('window', "PHIEU_PTTTDICHVU_A4_965", "pdf", par);
													}
												}
											} else {
												if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
													var rpName = "VNPTHIS_IN_A5_CLS_";
													rpName += $('#txtMABENHNHAN').val();
													rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
													rpName += "." + _typePrint;
													CommonUtil.inPhieu('window', 'PHIEU_PHAUTHUAT_A4', _typePrint, par, rpName);
												} else {
													if (usingReportCode) {//HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
														var _par_code = [ param[0] ];
														var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE", _par_code.join('$'));
														if (i_report_code != null && i_report_code.length > 0) {
															for (var i1 = 0; i1 < i_report_code.length; i1++) {
																var _report_code = i_report_code[i1].REPORT_CODE;
																var par_rpt = [ {
																	name : 'maubenhphamid',
																	type : 'String',
																	value : param[0]
																}, {
																	name : 'report_code',
																	type : 'String',
																	value : _report_code
																} ];
																openReport('window', _report_code, "pdf", par_rpt);
															}
														} else {
															var par_rpt = [ {
																name : 'maubenhphamid',
																type : 'String',
																value : param[0]
															}, {
																name : 'report_code',
																type : 'String',
																value : 'PHIEU_PHAUTHUAT_A4'
															} ];
															openReport('window', "PHIEU_PHAUTHUAT_A4", "pdf", par_rpt);
														}
													}
													//Begin_HaNv_16052018: bo sung phan in tach phieu HISL2TK-423
													else if (_opt.hospitalId == 957) {
														var _par_code = [ param[0] ];
														var lstDoiTuong = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.LOAI.DTCLS", _par_code.join('$'));
														if (lstDoiTuong != null && lstDoiTuong.length > 0) {
															for (var i1 = 0; i1 < lstDoiTuong.length; i1++) {
																var _loaidoituong = lstDoiTuong[i1].LOAIDOITUONG;
																var par_rpt = [ {
																	name : 'maubenhphamid',
																	type : 'String',
																	value : param[0]
																}, {
																	name : 'i_loaidoituong',
																	type : 'String',
																	value : _loaidoituong
																} ];
																openReport('window', "PHIEU_PHAUTHUAT_A4", "pdf", par_rpt);
															}
														}
													}
													//End_HaNv_16052018
													else {
														openReport('window', "PHIEU_PHAUTHUAT_A4", "pdf", par);
													}
												}
											}
										}
									}
								}
							} else {
								var nhomMauBenhPhamId = '';
								for (var i = 0; i < arr_mbp.length; i++) {
									if (arr_mbp[i] != '') {
										var param = arr_mbp[i].split('-');
										var funcName = '';
										if (param[1] == '1') {
											funcName = 'PHIEU_XETNGHIEM';
											if (LIS_CONNECTION_TYPE == "1" || LIS_CONNECTION_TYPE == "2" && LIS_SERVICE_DOMAIN_NAME != "") {
												SendRequestToLab(param[2], param[0], param[3]);
											}
											nhomMauBenhPhamId = nhomMauBenhPhamId == '' ? param[0] : nhomMauBenhPhamId + ";" + param[0];
										} else if (param[1] == '2') {
											funcName = 'PHIEU_CDHA';
											nhomMauBenhPhamId = nhomMauBenhPhamId == '' ? param[0] : nhomMauBenhPhamId + ";" + param[0];
										} else {
											funcName = 'PHIEU_PHAUTHUAT_A4';
											nhomMauBenhPhamId = nhomMauBenhPhamId == '' ? param[0] : nhomMauBenhPhamId + ";" + param[0];
										}
									}
								}
								//tuyendv 0406 1506
								var _par_code = [ nhomMauBenhPhamId ];
								var lstDoiTuong = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.DSLOAI.DTCLS", _par_code.join('$'));
								var _loaidoituong = '';
								if (lstDoiTuong != null && lstDoiTuong.length > 0) {
									for (var i1 = 0; i1 < lstDoiTuong.length; i1++) {
										_loaidoituong = _loaidoituong + lstDoiTuong[i1].LOAIDOITUONG + ';';
									}
								}
								var _par_code2 = [ nhomMauBenhPhamId ];
								var lstReport_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.DSREPORT.CODE", _par_code2.join('$'));
								var ds_report_code = '';
								if (lstReport_code != null && lstReport_code.length > 0) {
									for (var i2 = 0; i2 < lstReport_code.length; i2++) {
										ds_report_code = ds_report_code + lstReport_code[i2].REPORT_CODE + ',';
									}
								}
								var _check_hiv = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.HAVE.HIV", _par_code.join('$'));
								var par = [ {
									name : 'nhommaubenhphamid',
									type : 'String',
									value : nhomMauBenhPhamId
								}, {
									name : 'i_hid',
									type : 'String',
									value : _opt.hospitalId
								}, {
									name : 'i_sch',
									type : 'String',
									value : _opt.dbschema
								}, {
									name : 'nhomdoituong',
									type : 'String',
									value : _loaidoituong
								}, {
									name : 'nhomreportcode',
									type : 'String',
									value : ds_report_code
								}, {
									name : 'havexnhiv',
									type : 'String',
									value : _check_hiv
								} ];
								//end tuyendv 0406
								//START L2HOTRO-12397
								if (typeof opt._chidinhdichvu == "undefined") {
									var pars = [ 'NTU_BM2_12397_INTD' ];
									var data_arr_12397 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
									if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
										var rpName = "VNPTHIS_IN_A5_CLS_";
										rpName += $('#txtMABENHNHAN').val();
										rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
										rpName += "." + _typePrint;
										//START L2HOTRO-12397
										if (data_arr_12397 == '1') {
											if (isSaveView) {
												CommonUtil.inPhieu('window', 'PHIEU_CLS_ALL', _typePrint, par, null, false, true);
											} else {
												CommonUtil.inPhieu('window', 'PHIEU_CLS_ALL', _typePrint, par, null, true, true);
											}
										} else {
											CommonUtil.inPhieu('window', 'PHIEU_CLS_ALL', _typePrint, par, rpName);
										}
									} else {
										//START L2HOTRO-12397
										if (data_arr_12397 == '1') {
											if (isSaveView) {
												CommonUtil.inPhieu('window', 'PHIEU_CLS_ALL', 'pdf', par, null, false, true);
											} else {
												CommonUtil.inPhieu('window', 'PHIEU_CLS_ALL', 'pdf', par, null, true, true);
											}
										} else {
											openReport('window', 'PHIEU_CLS_ALL', "pdf", par);
										}
									}
								}
								//Begin_HaNv_29072019: in phiếu thu khác khi bật cấu hình in chung - L2PT-7051
								else {
									// nghiant 20092017 L2DKBD-404
									if (checkInthukhac) {
										if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
											var rpName = "VNPTHIS_IN_A5_CLS_";
											rpName += $('#txtMABENHNHAN').val();
											rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
											rpName += "." + _typePrint;
											//START L1PT-1470
											if (opt.hospitalId == '1077') {
												var par = [ {
													name : 'khambenhid',
													type : 'String',
													value : $("#hidKHAMBENHID").val()
												}, {
													name : 'maubenhphamid',
													type : 'String',
													value : param[0]
												} ];
												openReport('window', "PHIEU_SUDUNG_DVKT_1077", "pdf", par);
											} else {
												var par = [ {
													name : 'maubenhphamid',
													type : 'String',
													value : param[0]
												} ];
												CommonUtil.inPhieu('window', 'DKBD_PCD_THEM_CONG_KHAM_A5', _typePrint, par, rpName);
											}
										} else {
											//START L1PT-1470
											if (opt.hospitalId == '1077') {
												var par = [ {
													name : 'khambenhid',
													type : 'String',
													value : $("#hidKHAMBENHID").val()
												}, {
													name : 'maubenhphamid',
													type : 'String',
													value : param[0]
												} ];
												openReport('window', "PHIEU_SUDUNG_DVKT_1077", "pdf", par);
											} else {
												var par = [ {
													name : 'maubenhphamid',
													type : 'String',
													value : param[0]
												} ];
												openReport('window', "DKBD_PCD_THEM_CONG_KHAM_A5", "pdf", par);
											}
										}
									}
									// end nghiant 20092017 L2DKBD-404
								}
								//End_HaNv_29072019: in phiếu thu khác khi bật cấu hình in chung - L2PT-7051
							}
							if (printAll) {
								var par = [ {
									name : 'khambenhid',
									type : 'String',
									value : $("#hidKHAMBENHID").val()
								}, {
									name : 'i_hid',
									type : 'String',
									value : _opt.hospitalId
								}, {
									name : 'i_sch',
									type : 'String',
									value : _opt.dbschema
								} ];
								if (isAutoPrint || $.inArray(opt._subdeptId_login, isAutoP.split(',')) >= 0) {
									var rpName = "VNPTHIS_IN_A5_CLS_";
									rpName += $('#txtMABENHNHAN').val();
									rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
									rpName += "." + _typePrint;
									CommonUtil.inPhieu('window', 'PHIEU_CLSC', _typePrint, par, rpName);
								} else if (_opt.hospitalId != 951) {
									openReport('window', "PHIEU_CLSC", "pdf", par);
								}
							}
							EventUtil.raiseEvent("assignSevice_saveChiDinhDichVuOk", {
								msg : _msggg
							});
						}
					}
				} else {
					//Begin_HaNv_22082019: Fix loi dupleClick btnLuu - L2PT-8202
					$('#btnLuuIn').show();
					$('#btnLuu').show();
					//End_HaNv_22082019
				}
			}
		} else {
			DlgUtil.showMsg('Chưa chỉ định dịch vụ');
		}
	}
	function SendRequestToLab(sophieu, maubenhphamid, barcode) {
		var request_url = LIS_SERVICE_DOMAIN_NAME + LIS_SEND_REQUEST;
		console.log("request_url=" + request_url);
		var so_phieu = sophieu;
		refreshLabToken();
		var request = new LabRequestSet();
		if (LIS_PROVIDER) {
			request = LabRequestTGG();
		}
		if (LIS_AUTHENTICATION_GATE) {
			request = createLabRequest(maubenhphamid);
		} else {
			request.SID = so_phieu;
			request.Barcode = barcode;
			var param = [ maubenhphamid ];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
			for (var i = 0; i < data_ar.length; i++) {
				request.TestCodeList.push(data_ar[i]["MADICHVU"]);
			}
		}
		console.log(JSON.stringify(request));
		if (LIS_CONNECTION_TYPE == "1" || LIS_CONNECTION_TYPE == "2" && LIS_SERVICE_DOMAIN_NAME != "") {
			$.ajax({
				type : "POST",
				crossDomain : true,
				contentType : "application/json; charset=utf-8",
				headers : {
					'Username' : LIS_USERNAME,
					'Lis-Access-Hash' : getHash(so_phieu),
					'Identify-Code' : so_phieu,
					'SID' : so_phieu,
					'Token' : getLabToken(),
					'idToken' : getLabidToken(),
					'password' : getLabSecretKey(),
					'maTinh' : getLabProvinceCode(),
					'maCSKCB' : getLabHospitalCode()
				},
				data : JSON.stringify(request),
				url : request_url,
				success : function(data) {
					if (data)
						if (data.error_code != 0)
							refreshLabToken();
					console.log(JSON.stringify(data));
				},
				error : function(xhr) {
					console.log("send request fail: " + JSON.stringify(xhr));
					refreshLabToken();
				}
			});
		}
	}
	function delServiceGrid(id) {
		var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
		var rowData = $('#grdDSCD').jqGrid('getRowData', id);
		$('#grdDSCD').jqGrid('delRowData', id);
		if (typeof opt._chidinhdichvu != "undefined") {
			loadGridAfterRemove('grdDichVu', rowData.DICHVUID);
		} else {
			loadGridAfterRemove('grdXetNghiem', rowData.DICHVUID);
			loadGridAfterRemove('grdCDHA', rowData.DICHVUID);
			loadGridAfterRemove('grdChuyenKhoa', rowData.DICHVUID);
			loadGridAfterRemove('grdThuongDung', rowData.DICHVUID);
			loadGridAfterRemove('grdTiemChung', rowData.DICHVUID);
		}
		loadPay(-parseFloat(rowData.THANH_TIEN) - parseFloat(rowData.BHYT_TRA) - parseFloat(nvl(rowData.GIA_CHENH, '0')), -parseFloat(rowData.BHYT_TRA));
		if (opt._doituongbenhnhanId == '1' && parseFloat(rowData.BHYT_TRA) > 0) {
			if (parseFloat(rowData.BHYT_TRA) > 0) {
				tongbh = tongbh - (parseFloat(rowData.BHYT_TRA) - parseFloat(rowData.THANH_TIEN));
			}
			for (var i1 = 0; i1 < rowIds.length; i1++) {
				var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[i1]);
				if (rowData.LOAIDOITUONG == '1' || rowData.LOAIDOITUONG == '2') {
					if (tongbh > parseFloat(nvl($('#hidTRAN_BHYT').val(), "0"))) {
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFINAL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFINAL;
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFINAL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFINAL;
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFINAL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFINAL;
						loadPay(0, parseFloat(rowData.BHYT_TRAFINAL) - parseFloat(rowData.BHYT_TRA));
					} else {
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'BHYT_TRA', rowData.BHYT_TRAFULL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).BHYT_TRA = rowData.BHYT_TRAFULL;
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'THANH_TIEN', rowData.THANH_TIENFULL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).THANH_TIEN = rowData.THANH_TIENFULL;
						$('#grdDSCD').jqGrid('setCell', rowIds[i1], 'MIENGIAM', rowData.MIENGIAMFULL);
						$('#grdDSCD').jqGrid('getLocalRow', rowIds[i1]).MIENGIAM = rowData.MIENGIAMFULL;
						loadPay(0, parseFloat(rowData.BHYT_TRAFULL) - parseFloat(rowData.BHYT_TRA));
					}
				}
			}
		}
		var checkNuoiCay = false;
		var rowIds = $('#grdDSCD').jqGrid('getDataIDs');
		if (jsonNuoiCay != '') {
			for (var k = 0; k < rowIds.length; k++) {
				var rowData = $('#grdDSCD').jqGrid('getRowData', rowIds[k]);
				if (rowData.JSON_NUOICAY != '' && rowData.JSON_NUOICAY != "undefined") {
					checkNuoiCay = true;
					break;
				}
			}
			if (!checkNuoiCay) {
				jsonNuoiCay = '';
			}
		}
		//Begin_HaNv_01012020: Thêm thông tin công khám cũ vả tỉ lệ công khám của BN khi tạo phiếu công khám - L2PT-13921
		if (opt._loaidichvu == '2' && opt._doituongbenhnhanId == '1') {
			var rowCount = $("#grdDSCD").getGridParam("reccount");
			if (rowCount == 0) {
				$('#cboTYLEDV_CK').prop("disabled", false);
			}
		}
		//End_HaNv_01012020
		//Begin_HaNv_24022020: Cho phép chia giá của gói dịch vụ theo số lượng phiếu - L2PT-16531
		if (isDVTheoGoi) {
			var rowCount = $("#grdDSCD").getGridParam("reccount");
			if (rowCount == 0) {
				isDVTheoGoi = false;
			}
		}
		//End_HaNv_HaNv_24022020
	}
}

<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jquery-ui-1.12.1/jquery-ui.js"></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script type="text/javascript" src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script type="text/javascript" src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script type="text/javascript" src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../ngoaitru/NGT02K028_HienthiDanhSachKham.js?v=2211082"></script>

<style>
	.overlay {
	    height: 100%;
	    width: 100%;
	    position: fixed;
	    z-index: 1;
	    top: 0;
	    left: 0;
	    /* background-color: rgb(0,0,0);
	    background-color: rgba(0,0,0, 0.9); */
	    overflow-x: hidden;
	    transition: 0.5s;
	}
	
	table thead tr th {
		background-color: #1674B1;
		color:white;
	}
	
	table tbody tr td {
		background-color: #eee;
		/* height:15%; */
		font-size:35px;
	}
	
	table tbody tr {
		/* height:15%; */
	}
	
	.blink{
	  animation: blink-animation 1s steps(5, start) infinite;
	  -webkit-animation: blink-animation 1s steps(5, start) infinite;
	  text-align:center; 
	  align:center;
	}
	
	blinkUT{
	  animation: blink-animation 1s steps(5, start) infinite;
	  -webkit-animation: blink-animation 1s steps(5, start) infinite;
	  text-align:center; 
	  align:center;
	  color:blue;
	}
	
	@keyframes blink-animation {
	  to {
	    visibility: hidden;
	  }
	}
	@-webkit-keyframes blink-animation {
	  to {
	    visibility: hidden;
	  }
	}
	
	.highlightRow{
		color:blue;
		font-weight:bold;
	}
	
	.blink1 {
	  animation: blink-animation 1s steps(5, start) infinite;
	  -webkit-animation: blink-animation 1s steps(5, start) infinite;
	  
	  	color:red;
		font-weight:bold;
		text-align:center; 
		align:center;
	}
	
	.setFont {
		font-size: 55px !important;
		color: #000000;
	}
	.setFontUT {
		font-size: 55px !important;
		color:red; 
	}
	
</style>
<div class="overlay">
		<div style="width: 100%; background-color: #004B7C"
			align="center">
			<span
				style="font-size: 40px; color: Red; line-height: 2; text-transform: uppercase"
				id="phongkham"></span>
		</div>
		<div style="width: 100%; background-color: #004B7C" align="center" id="dvTENBS">
			<span style="font-size: 40px; color: white; line-height: 1;"
				id="tenbs"></span>
		</div>
		
		<div class="col-xs-12 low-padding" style="width: 100%; height: 8%; background-color: #004B7C" align="center">
			<div class="col-xs-6 low-padding" style="background-color: #004B7C" id="divtxtBs" >
				<label
					style="margin-top: 5px; font-size: 30px; color: white; line-height: 2;">Bác sĩ:</label> <span
					style="font-size: 30px; color: white; line-height: 2; margin-right: 20px;"
					id="tenbsythietlap"></span>
			</div>
			<div class="col-xs-6 low-padding" style="height: 18%; background-color: #004B7C; float:right" id="divtxtDd" >
				<label
					style="margin-top: 5px; font-size: 30px; color: white; line-height: 2;">Điều dưỡng:</label> <span
					style="font-size: 30px; color: white; line-height: 2; margin-right: 20px;"
					id="tenddthietlap"></span>
			</div>
		</div>
	
		<div id="divBsy" class="col-xs-12 low-padding" style="width: 100%; background-color: #ffffff" >
			<div class="col-xs-1 low-padding required"
				style="" align="right">
				<label style="margin-top: 5px;margin-right:5px;">Chọn bác sĩ</label>

			</div>
			<div class="col-xs-1 low-padding" style="margin-top: 2px;">
				<input class="form-control input-sm" id="txtBS_NAME_SEARCH"
					name="txtBS_NAME_SEARCH" title="">
			</div>
			<div class="col-xs-3 low-padding"
				style="padding-top: 2px !important; padding-bottom: 2px !important;">
				<select class="form-control input-sm isnotnull" id="cboBSYID"
					filterLike="txtBS_NAME_SEARCH"></select>
			</div>
			<div class="col-xs-2 low-padding required">
				<label style="margin-top: 5px;margin-left:20px;">Chọn điều dưỡng viên</label>
			</div>
			<div class="col-xs-1 low-padding" style="margin-top: 2px;">
				<input class="form-control input-sm" id="txtDD_NAME_SEARCH"
					   name="txtDD_NAME_SEARCH" title="" >
			</div>
			<div class="col-xs-3 low-padding"
				 style="padding-top: 2px !important; padding-bottom: 2px !important;">
				<select class="form-control input-sm isnotnull"
						id="cboDIEUDUONGID" filterLike="txtDD_NAME_SEARCH"></select>
			</div>
			<button type="button" class="btn btn-sm btn-primary heightbtn"
					id="btnHide" style="margin-top: 1px;margin-left:10px;">
				<span class="glyphicon glyphicon-repeat"></span> Ẩn
			</button>
		</div>
<!--		<div id="divDieuduong">-->
<!--			-->
<!--		</div>-->
<!--		<div class="col-xs-1 low-padding" id="divHide">-->
<!--			-->
<!--		</div>-->
<!--		-->
	    <div style="width: 100%;">
			<table border="1" style="width: 100%;border:#6BC0F4">
				<thead>
					<tr id="tr2" hidden="true">
						<th><h2><center><span style="font-size: 33px" id="lbSTT"></center></h2></th>
						<th><h2><center><span style="font-size: 33px" id="lbHoTen"></center></h2></th>
						<th><h2><center><span style="font-size: 33px" id="lbUT"></center></h2></th>
						<th><h2><center><span style="font-size: 33px" id="lbLG"></center></h2></th>
 						<th><h2><center><span style="font-size: 33px" id="lbGH"></center></h2></th>
					</tr> 
				</thead>
				
				<thead>
					<tr id="tr3" hidden="true">
						<th><h2><center><span style="font-size: 33px" id="lbSTT1"></span></center></h2></th>
						<th><h2><center><span style="font-size: 33px" id="lbHoVaTen"></span></center></h2></th>
						<th><h2><center><span style="font-size: 33px" id="lbTuoi"></span></center></h2></th>
						<th><h2><center><span style="font-size: 33px" id="lbGioiTinh"></span></center></h2></th>
						<th><h2><center><span style="font-size: 33px" id="lbLoaiDKKham"></span></center></h2></th>
					</tr>
				</thead>
				
				<tbody id="list" style="font-size: 23px;">
				</tbody>
			</table>
		</div>
	</div>
	
	<iframe id="main_frame" style="width:100%;height:100%;border:none;display:none"></iframe>
	
<script type="text/javascript">
	var uuid = '{uuid}';
	initRest(uuid,"/vnpthis");
	var mode = '{showMode}';	 
	var data;
	var ngaybd = '{ngaybd}';
	var ngaykt = '{ngaykt}';
	var phongid = '{subdept_id}';
	var subdept_name = '{subdept_name}';
	var fname = '{fname}';
	var hid = '{hospital_id}';
	var _opts = new Object();
	_opts.ngaybd = ngaybd;
	_opts.ngaykt = ngaykt;
	_opts.phongid = phongid;
	_opts.subdept_name = subdept_name;
	_opts.fname = fname;
	_opts.hid = hid;

	var tt = new ThongBaoBenhNhan(_opts);
	tt.load();
</script>
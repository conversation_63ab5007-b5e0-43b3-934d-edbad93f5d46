function ThongBaoBenhNhan(opt) {
    this.load = doLoad;
    var _opt = opt;

    function doLoad() {
        window.getScreenDetails().then(function(data) {
                const primaryScreen = data.screens.find(s => s.isPrimary);
                if (window.screen.isExtended) {
                    // Xử lý khi có 2 màn hình
                    DlgUtil.showConfirm("Mở chế độ full màn hình thứ 2?", function(flag){
                        if(flag) {
                            const otherScreen = data.screens.find(s => s !== primaryScreen);
                            document.documentElement.requestFullscreen({screen : otherScreen});
                        }
                    });
                } else {
                    // Xử lý khi chỉ có 1 màn hình
                    DlgUtil.showConfirm("Mở chế độ full màn hình?", function(flag){
                        if(flag) {
                            document.documentElement.requestFullscreen({screen : primaryScreen});
                        }
                    });
                }
            });
        _initControl();
    }

    function _initControl() {
        var par = ['NGT02K028_COLUMN_NAME'];
        var dt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
        if (dt == "" || dt == "0") {
            dt = "SỐ KHÁM;HỌ TÊN;ƯU TIÊN;LẦN GỌI";
        }


        par = ['HIS_DSK_SHOW_BSYDIEUDUONG'];
        var _showBsyDieuduong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
        if (_showBsyDieuduong == '1') {
            document.onwebkitfullscreenchange = fullscreenChanged;
            
            $("#main_frame").attr("src", "manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham2&showMode=dlg");
            $('#phongkham').text(_opt.subdept_name);
            $("#divBsy").show();
            $("#divDieuduong").show();
            $("#divHide").show();
            $("#divtxtBs").show();
            $("#divtxtDd").show();
            $("#dvTENBS").hide();
            ComboUtil.getComboTag("cboBSYID", "NGT02K028.TENBSY", [], 0, {value: 0, text: 'Chọn'}, "sql", "", "");
            ComboUtil.getComboTag("cboDIEUDUONGID", "NGT02K028.TENDD", [], 0, {value: 0, text: 'Chọn'}, "sql", "", "");
            $('#cboBSYID').change(function () {
                var rets = ($('#cboBSYID :selected').text()).split('(');
                $('#tenbsythietlap').text(rets[0]);
            });
            $('#cboDIEUDUONGID').change(function () {
                var rets = ($('#cboDIEUDUONGID :selected').text()).split('(');
                $('#tenddthietlap').text(rets[0]);
            });
            $('#btnHide').on("click", function () {
                if ($('#cboBSYID').val() == '0') {
                    DlgUtil.showMsg('Hãy chọn bác sĩ khám bệnh.');
                    return false;
                }
                ;
                if ($('#cboDIEUDUONGID').val() == '0') {
                    DlgUtil.showMsg('Hãy chọn điều dưỡng viên.');
                    return false;
                }
                ;
                $("#divBsy").hide();
                $("#divDieuduong").hide();
                $("#divHide").hide();

            });
        } else {
            document.onwebkitfullscreenchange = fullscreenChanged;
            document.documentElement.onclick = goFullscreen;
            document.onkeydown = goFullscreen;
            $("#main_frame").attr("src", "manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham2&showMode=dlg");
            $('#phongkham').text(_opt.subdept_name);
            $("#divBsy").hide();
            $("#divDieuduong").hide();
            $("#divHide").hide();
            $("#divtxtBs").hide();
            $("#divtxtDd").hide();
            $("#dvTENBS").show();
            var strr = '';
            var par_bs = [];
            var data_bs = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K028.BACSI", par_bs);
            var row = JSON.parse(data_bs);
            if (row != null && row.length > 0 && row[0].HOC_HAM != '') strr = row[0].HOC_HAM + '.' + row[0].OFFICER_NAME;
            $('#tenbs').text(strr);
        }
        if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT_LCD_KB_NANPKANPHUOC') == '1'){  
            $("#dvCHINH").hide();
            $("#dvNANAP").show();
        }

        var array = dt.split(";");
        var arraylbl = ["lbSTT","lbHoTen","lbUT","lbLG"]
        var arraylbl2 = ["lbSTT1","lbHoTen1","lbUT1","lbLG1"]
        $('#lbSTT').text(array[0]);
        $('#lbHoTen').text(array[1]);
        $('#lbUT').text(array[2]);
        $('#lbLG').text(array[3]);
        $('#lbSTT1').text(array[0]);
        $('#lbHoTen1').text(array[1]);
        $('#lbUT1').text(array[2]);
        $('#lbLG1').text(array[3]);
        _loadData();
        setInterval(function () {
            _loadData();
        }, 3000);
        if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT_LCD_KB_DANGGOIKHAM') == '1'){	
			setInterval(function () {
				$('[id^=lbDangKham]').fadeOut(200)
				$('[id^=lbDangKham]').fadeIn(200)
			},100)
		}
    }

    function _loadData() {
        var th = 0;
        var html = '';
        var html1 = '';
        var html2 = '';
        var sql_par = [_opt.phongid];
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K001.LCD", sql_par.join('$'));
        var logo = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_BM2_URL');

        var imgTag = document.getElementById('imgLogo');
        imgTag.src = logo;
        if (data_ar != null && data_ar.length > 0) {

            for (var i = 0; i < data_ar.length; i++) {
                html += '<tr style="height:115px;">';
                if (data_ar[i].UUTIENKHAMID != "0") {
                    html += '<td style="width:15%;text-align: center;background-color:#005AAB "><span class="setFontUT" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham1"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></th>';
                    html += '<td style="width:60%;background-color:#005AAB "><span class="setFontUT" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham2"+i+"\">"): ">") + data_ar[i].TENBENHNHAN + '</span></th>';
                    html += '<td style="width:15%;text-align: center;background-color:#005AAB "><span class="setNSLG" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham3"+i+"\">"): ">") + (data_ar[i].UUTIENKHAMID != "0" ? "CÓ" : "KHÔNG") + '</span></th>';
                    html += '<td style="width:10%;text-align: center;background-color:#005AAB "><span class="setNSLG" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham4"+i+"\">"): ">") + data_ar[i].LANGOIKHAM + '</span></th>';
                    html2 += '<tr style="height:115px; ">';
                    html2 += '<td style="width:20%;text-align: center;background-color:#005AAB "><span class="setFontUT" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham1"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></th>';
                    html2 += '<td style="width:80%;background-color:#005AAB "><span class="setFontUT" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham2"+i+"\">"): ">") + data_ar[i].TENBENHNHAN + '</span></th>';
                    html2 += '</tr>';
                } else {
                    html += '<td style="width:15%;text-align: center;background-color:#005AAB "><span class="setFont" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham1"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></th>';
                    html += '<td style="width:60%;background-color:#005AAB "><span class="setFont" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham2"+i+"\">"): ">") + data_ar[i].TENBENHNHAN + '</span></th>';
                    html += '<td style="width:15%;text-align: center;background-color:#005AAB "><span class="setNSLG" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham3"+i+"\">"): ">") + (data_ar[i].UUTIENKHAMID != "0" ? "CÓ" : "KHÔNG") + '</span></th>';
                    html += '<td style="width:10%;text-align: center;background-color:#005AAB "><span class="setNSLG" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham4"+i+"\">"): ">") + data_ar[i].LANGOIKHAM + '</span></th>';
                    html1 += '<tr style="height:115px; ">';
                    html1 += '<td style="width:15%;text-align: center;background-color:#005AAB "><span class="setFont" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham1"+i+"\">"): ">") + data_ar[i].SOTHUTU + '</span></th>';
                    html1 += '<td style="width:60%;background-color:#005AAB "><span class="setFont" ' + (data_ar[i].DANGGOIKHAM == "1" ? ("id=\"lbDangKham2"+i+"\">"): ">") + data_ar[i].TENBENHNHAN + '</span></th>';
                    html1 += '</tr>';
                }
                html += '</tr>';
            }
        }
        $('#list').html(html);
        $('#list1').html(html1);
        $('#list2').html(html2);
        var constant = parseFloat(getSpeedFromCauHinh()); // cang lon thi cang nhanh chong mat
        if(constant != 0){
            var x = $('#list').prop('scrollHeight') -  $('#list').height();
            var speed = Math.round(x/constant*130);
            if(x>0){
                scroll(speed,x);
            }
        }
    }

    function goFullscreen() {
        var isFirefox = typeof InstallTrigger !== 'undefined';
        mf = document.getElementById("main_frame");
        if (!isFirefox) {
            mf.webkitRequestFullscreen();
        } else {
            mf.mozRequestFullScreen();
        }
    }

    function fullscreenChanged() {
        if (document.webkitFullscreenElement == null) {
            mf = document.getElementById("main_frame");
            mf.style.display = "none";
        }
    }
    function scroll(speed, x) {
        $('#list').animate(
            {scrollTop: x}
            , speed
            , function() {
                $(this).animate({ scrollTop: 0 }, speed, function(){
                   window.scrollTo(0, 0);
                });

            }
        );
    }

    function getSpeedFromCauHinh(){
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH",'NTU02D021_LCD_SPEED_SCROLL');
        if (data_ar != null && data_ar.length > 0) {
            return data_ar[0].NTU02D021_LCD_SPEED_SCROLL;
        } else {
            return 1; // default
        }
    }
}


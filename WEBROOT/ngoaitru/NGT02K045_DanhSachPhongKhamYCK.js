function NGT02K045_DANHSACHPHONGKHAM(opt) {
    this.load = doLoad;
    var _opt = opt;

    function doLoad() {
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        if(jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','NGT_HIENTHISLBN_DSPKYCK')=='0') {
            $('#dvGHICHU').show();
        }
        $('#lblYeuCauKham').text(_opt.tenyeucaukham);
        _loadData();
    }

    function _bindEvent() {
        $("#btnDong").on("click", function (e) {
            parent.DlgUtil.close("dlgCV");
        });

        $('.btnButtonPK').on('click', function (e) {
            EventUtil.raiseEvent("assignsetphongkham", {option: $(this).attr('id')});
        });
    }

    function _loadData() {
        var dem = 0;
        var shtml = '';
        $('#btndspk').html(shtml);
        var sql_par = [_opt.yeucaukhamid];
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("DS.PK.YEUCAUKHAM", sql_par.join('$'));
        if (data_ar != null && data_ar.length > 0) {
            shtml += '<tr>';
            if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'NGT_HIENTHISLBN_DSPKYCK') == 1) {
                for (var i = 0; i < data_ar.length; i++) {
                    dem += 1;
                    shtml += '<td> <button type="button" class="btnButtonPK" style="background-color:#ffffff" id="' + data_ar[i].ORG_ID + '">' + data_ar[i].ORG_NAME;
                    shtml += '<br><span style="background-color: grey;font-size: 15px;padding: 2px 3px 2px 3px;margin-right: 2px">' + data_ar[i].SLCHOKHAM + '</span><span style="background-color: yellow;font-size: 15px;padding: 2px 3px 2px 3px;margin-right: 2px">' + data_ar[i].SLDANGKHAM ;
                    shtml += '</span><span style="background-color: green;font-size: 15px;padding: 2px 3px 2px 3px;margin-right: 2px">' + data_ar[i].SLDAKHAM + '</span><span style="background-color: red;font-size: 15px;padding: 2px 3px 2px 3px;margin-right: 2px">' + data_ar[i].SLKHAM + '</span></button></td>';
                    if (dem == 7) {
                        shtml += '</tr>';
                        shtml += '<tr>';
                        dem = 0;
                    }
                }
            } else {
                for (var i = 0; i < data_ar.length; i++) {
                    dem += 1;
                    if (parseInt(data_ar[i].SLDANGKHAM) >= parseInt(data_ar[i].SLKHAM)) {
                        shtml += '<td> <button type="button" class="btnButtonPK" style="background-color:#8E8E8E"  id="' + data_ar[i].ORG_ID + '" disabled>' + data_ar[i].ORG_NAME + '(' + data_ar[i].SLDANGKHAM + '/' + data_ar[i].SLKHAM + ')' + '</button></td>';
                    } else {
                        shtml += '<td> <button type="button" class="btnButtonPK" style="background-color:#' + data_ar[i].MAMAU + '" id="' + data_ar[i].ORG_ID + '">' + data_ar[i].ORG_NAME + '</br>(' + data_ar[i].SLDANGKHAM + '/' + data_ar[i].SLKHAM + ')' + '</button></td>';
                    }

                    if (dem == 7) {
                        shtml += '</tr>';
                        shtml += '<tr>';
                        dem = 0;
                    }
                }
            }

        }
        $('#btndspk').html(shtml);
    }
}


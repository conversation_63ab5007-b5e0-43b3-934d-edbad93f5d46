<!-- 
 	MUC DICH: HIEN THI CHI TIET THONG TIN LICH SU KHAM CHUA BENH BAO HIEM; 
 	
 	DU LIEU TRUYEN LA 1 JSON DUOI DANG SAU: 
 		- USERBH; 
 		- PASSBH; 
 		- MAHOSO CAN HIEN THI; 
 	
 	NEU KHONG LAY RA DU LIEU THI HIEN THI THONG BAO VA DE DU LIEU TRANG CHO FORM; 
 	NEU CO DU LIEU THI FILL VAO TEXTBOX; 
 	
 	NGUOI TAO		NGAY TAO		NOI DUNG SUA DOI; 
 	SONDN			10/05/2017			TAO MOI; 
 	
 -->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<script type="text/javascript" src="../common/script/AjaxService.js"></script>

<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">    
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>  
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../ngoaitru/NGT02K048_LichSuKCB_CT.js?v=20170525"></script>
<script type="text/javascript" src="..//noitru/cominf.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>

<script src="https://cdn.rawgit.com/SheetJS/js-xlsx/v0.8.0/dist/xlsx.full.min.js"></script>
<script src="https://cdn.rawgit.com/SheetJS/js-xlsx/v0.8.0/dist/ods.js"></script>
<div class="container" id="" style="width: 100%;">
	<div class="" id="dvPhieuChamSoc">
		<div class="col-xs-12 low-padding">
			<span><b style="color:blue">Thông tin chi tiết khám chữa bệnh</b></span>
		</div>
		<div class="col-xs-12 low-padding" id="dvLichSuKCB">
			
			<div class="col-xs-12 low-padding ">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Họ tên</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtHoTen" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="mgl5">Cha mẹ/ Người GH</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTenChame" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="mgl5">Cân nặng</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtCanNang" style="width:100%;">
					</div>
				</div>
			</div>
			
			<div class="col-xs-12 low-padding ">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Mã bệnh nhân</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtMaBn" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="mgl5">Mã khoa</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtMaKhoa" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="mgl5">TT Ra viện</label>
					</div>
					<div class="col-xs-8 low-padding">
						<!-- <input class="form-control input-sm i-col-m_fl" id="txtTinhTrangRv" style="width:100%;"> -->
						<select class="form-control input-sm" id="cboTinhTrangRv" style="width:100%;">
							<option value="1">Ra viện</option>
							<option value="2">Chuyển viện</option>
							<option value="3">Trốn viện</option>
							<option value="4">Xin ra viện</option>
						</select>
					</div>
				</div>
			</div>
			
			<div class="col-xs-12 low-padding ">
				<div class="col-xs-8 low-padding">
					<div class="col-xs-2 low-padding">
						<label class="">Mã bệnh</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtMaBenh" style="width:100%;">
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTenBenh" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="mgl5">Mã bệnh khác</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtMaBenhkhac" style="width:100%;">
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Mã PTTT QT</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtMaPtttQt" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="mgl5">Mã TNTT</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtMaTaiNan" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="mgl5">Số ngày Đ.Trị</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtSoNgayDtri" style="width:100%;">
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Ngày vào</label>
					</div>
					<div class="col-xs-8 low-padding">
						<div class="input-group">
							<input class="form-control input-sm" id="txtNgayVao" name="txtNgayVao" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy"  readonly="readonly">
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtNgayVao','ddMMyyyy','dropdown',false,'24',false)"></span>
						</div>
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="mgl5">Ngày ra</label>
					</div>
					<div class="col-xs-8 low-padding">
						<div class="input-group">
							<input class="form-control input-sm" id="txtNgayRa" name="txtNgayRa" valrule="Ngày hợp đồng,trim_required" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy"  readonly="readonly">
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtNgayRa','ddMMyyyy','dropdown',false,'24',false)"></span>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Ngày nhập</label>
					</div>
					<div class="col-xs-8 low-padding">
						<div class="input-group">
							<input class="form-control input-sm" id="txtNgaynhap" name="txtNgaynhap" valrule="Ngày hợp đồng,trim_required" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy"  readonly="readonly">
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtNgaynhap','ddMMyyyy','dropdown',false,'24',false)"></span>
						</div>
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="mgl5">Ngày TT</label>
					</div>
					<div class="col-xs-8 low-padding">
						<div class="input-group">
							<input class="form-control input-sm" id="txtNgaythanhtoan" name="txtNgaythanhtoan" valrule="Ngày hợp đồng,trim_required" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy"  readonly="readonly">
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtNgaythanhtoan','ddMMyyyy','dropdown',false,'24',false)"></span>
						</div>
					</div>
				</div>
			</div>

			<div class="col-xs-12 low-padding">
				<span><b style="color:blue">Thông tin thẻ BHYT</b></span>
			</div>
			
			<div class="col-xs-12 low-padding">
				<div class="col-xs-4 low-padding">
					<div class="col-xs-4 low-padding">
						<label class="">Mã thẻ</label>
					</div>
					<div class="col-xs-8 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtMaThe" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-8 low-padding">
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Từ ngày</label>
					</div>
					<div class="col-xs-2 low-padding">
						<div class="input-group">
							<input class="form-control input-sm" id="txtGtTheTu" name="txtGtTheTu" valrule="Ngày hợp đồng,trim_required" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy"  readonly="readonly">
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtGtTheTu','ddMMyyyy','dropdown',false,'24',false)"></span>
						</div>
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Đến ngày</label>
					</div>
					<div class="col-xs-2 low-padding">
						<div class="input-group">
							<input class="form-control input-sm" id="txtGtTheDen" name="txtGtTheDen" valrule="Ngày hợp đồng,trim_required" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy"  readonly="readonly">
							<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtGtTheDen','ddMMyyyy','dropdown',false,'24',false)"></span>
						</div>
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Mức hưởng</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtMucHuong" style="width:100%;">
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding ">
				<span><b style="color:blue">Thông tin thanh toán</b></span>
			</div>
			<div class="col-xs-12 low-padding">
				<div class="col-xs-8 low-padding">
					<div class="col-xs-2 low-padding">
						<label class="">Tổng TT</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTTongchi" style="width:100%;">
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl5">BH Trả</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTBhtt" style="width:100%;">
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl5">BN Trả</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTBntt" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-3 low-padding">
						<label class="mgl5">Nguồn khác</label>
					</div>
					<div class="col-xs-3 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTNguonkhac" style="width:100%;">
					</div>
					<div class="col-xs-3 low-padding">
						<label class="mgl5">Ngoài DS</label>
					</div>
					<div class="col-xs-3 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTNgoaids" style="width:100%;">
					</div>
				</div>
				
			</div>
			<div class="col-xs-12 low-padding">
				<div class="col-xs-8 low-padding">
					<div class="col-xs-2 low-padding">
						<label class="">Tiền Thuốc</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTThuoc" style="width:100%;">
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Tiền khám</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTienkham" style="width:100%;">
					</div>
					<div class="col-xs-2 low-padding">
						<label class="mgl5">Tiền VTYT</label>
					</div>
					<div class="col-xs-2 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTVtyt" style="width:100%;">
					</div>
				</div>
				<div class="col-xs-4 low-padding">
					<div class="col-xs-3 low-padding">
						<label class="mgl5">Tiền giường</label>
					</div>
					<div class="col-xs-3 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTiengiuong" style="width:100%;">
					</div>
					<div class="col-xs-3 low-padding">
						<label class="mgl5">Vận chuyển</label>
					</div>
					<div class="col-xs-3 low-padding">
						<input class="form-control input-sm i-col-m_fl" id="txtTienvanchuyen" style="width:100%;">
					</div>
				</div>
			</div>
			<div class="col-xs-12 low-padding">
				<div class="col-xs-5 low-padding" style="padding-right: 15px !important;">
					<table id="grdKham"></table>
					<div id="pager_grdKham"></div>
				</div>
				<div class="col-xs-7 low-padding">
					<table id="grdThuoc"></table>
					<div id="pager_grdThuoc"></div>
				</div>
				
			</div>
			<div class="col-xs-12 low-padding mgt5" style="text-align: center;">
				<button type="submit" id="btnClose" class="btn btn-default btn-primary">
				<span class="glyphicon glyphicon-remove-circle"></span> Đóng</button>
			</div>
			
		</div>
		
	</div>
</div>
<script>
	var paramInfo = CommonUtil.decode('{paramData}');

	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var uuid = '{uuid}';
	var url ='{url}';
	var session_par=[];
	
	initRest(uuid, "/vnpthis");
	initAjax("/vnpthis");
	ajaxSvc.register("PortalWS");
	ajaxSvc.register("InsrWS");

	var _opts={
		lang: lang,
		_param:session_par,
		_uuid:uuid,
		_hospital_id: hospital_id
	}
	
	parent.DlgUtil.tunnel(DlgUtil.moveEvent);
	var data = EventUtil.getVar("dlgVar");

	_opts.I_U = data.I_U; 
	_opts.I_P = data.I_P;
	_opts.MAHOSO = data.MAHOSO;
	
	var tba = new lichsukcbct(_opts);
	tba.load();	
</script>
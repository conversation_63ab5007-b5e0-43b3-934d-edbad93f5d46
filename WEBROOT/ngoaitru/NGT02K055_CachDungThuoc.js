function NGT02K055_CachDungThuoc(_opts) {
	
	var _gridDSCHId="grdDanhSachCH"; 
	var _gridDSCHHeader="ID,ID,50,0,t,l,1,2;" +
				"LOAI,LOAI,50,0,t,l,1,2;" +
				"<PERSON><PERSON><PERSON> d<PERSON>,CACHDUNG,80,0,f,l,1,2;" +
				"Mã,MA_CD,30,0,f,l,1,2;" +
				"Khoa,KHOA,50,0,f,l,1,2;" + 
				"Phòng,PHONG,50,0,f,l,1,2;" + 
				"Ng<PERSON><PERSON><PERSON> tạo,NGUOITAO,30,0,f,l,1,2;" + 
				"Ngày tạo,NGAY,30,0,f,c,1,2";

	flagLoading = false;
	_param=[];
	
	var validator = null;
	var valid_ar = [];
	this.load=doLoad;
	
	function doLoad(_hosp_id) {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
		validator = new DataValidator("inputForm");
		var _options=$.extend({},_opts);
		var _param=_options._param;
		_hospital_id = _param[0];
		_user_id = _param[1];
		_initControl();
		_bindEvent();	

	}
	function _initControl(){
    	GridUtil.init(_gridDSCHId,"100%","425","",false,_gridDSCHHeader);
		loadGridData();	
	};
	
	function _bindEvent() {
		
		GridUtil.setGridParam(_gridDSCHId,{
			onSelectRow: function(id) {
				GridUtil.unmarkAll(_gridDSCHId);
	    		GridUtil.markRow(_gridDSCHId,id);
	    		
	            if (id) {
	            	if(flagLoading) return;
	            	var _row = $("#"+_gridDSCHId).jqGrid('getRowData', id);
	            	
	            	FormUtil.clearForm("inputForm","");
				    FormUtil.setObjectToForm("inputForm","",_row);
				       
				    setEnabled(["btnThem", "btnSua", "btnXoa",],["btnLuu", "btnHuy"]);
	    			
	            }
	        }
		});

		$("#btnThem").click(function(){
			
			isEdit = false;
			flagLoading = true;
			FormUtil.clearForm('inputForm',"");
			$("#cboLOAI").val(1);
			
			setEnabled(['btnLuu', 'btnHuy','txtID', 'txtCACHDUNG','txtGHICHU','txtMA_CD','cboLOAI'], 
					['btnThem', 'btnSua', 'btnXoa']);
		});
		
		$("#btnLuu").click(function(){
			var valid = validator.validateForm();
			if(valid){
				var _validator=new DataValidator([{region:'divMain',fields:valid_ar}]);	
				var valid= _validator.validateForm();
				if(!valid){
				return false;
				}
				if(_opts.dept_id == '0' || _opts.subdept_id == '0'){
					DlgUtil.showMsg("Chưa thiết lập thông tin khoa phòng!");
					return false;
				}
				
				objData = new Object();
				FormUtil.setFormToObject("inputForm", "", objData); 
				objData.KHOAID = _opts.dept_id;
				objData.PHONGID = _opts.subdept_id;
				
				if(objData.ID == ""){
					// them moi; 
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC55.THEM", JSON.stringify(objData));
					flagLoading = false;	
					
					if(fl > 0){
						DlgUtil.showMsg("Thêm mới thành công !");
						loadGridData();
						setEnabled(["btnThem"], ['btnSua', 'btnXoa','btnLuu', 'btnHuy','txtID', 'txtCACHDUNG', 'txtGHICHU','txtMA_CD','cboLOAI']);
					
					}else{
						DlgUtil.showMsg("Lỗi thêm mới cách dùng !");
					}
				}else{
					// sua thong tin; 
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC55.SUA", JSON.stringify(objData));
					flagLoading = false;
					
					if(fl > 0){
						DlgUtil.showMsg("Cập nhật thành công !");
						loadGridData();
						setEnabled(["btnThem"], ['btnSua', 'btnXoa','btnLuu', 'btnHuy','txtID', 'txtCACHDUNG', 'txtGHICHU','txtMA_CD','cboLOAI']);
					
					}else{
						DlgUtil.showMsg("Lỗi cập nhật thông tin cách dùng!");
					}
				}
			}
		});
		
		$("#btnSua").click(function(){
			flagLoading = true;
			isEdit = true;
			setEnabled(['btnLuu', 'btnHuy','txtID', 'txtCACHDUNG', 'txtGHICHU','txtMA_CD','cboLOAI'], 
					['btnThem', 'btnSua', 'btnXoa']);
		});
		
		$("#btnXoa").click(function(){
			DlgUtil.showConfirm("Bạn có muốn xóa chi tiết cách dùng này ?",function(flag) {
				if(flag){		
					var obj = new Object(); 
					FormUtil.setFormToObject("inputForm", "", obj);
					
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("DMC55.XOA", obj);
					if(fl==1){
						DlgUtil.showMsg("Xóa thành công !");
						loadGridData();
						
						FormUtil.clearForm('inputForm',"");
					    setEnabled(['btnThem'], ['btnSua', 'btnXoa', 'btnHuy','txtID', 'txtCACHDUNG', 'txtGHICHU','txtMA_CD','cboLOAI']);
						
					}else{
						DlgUtil.showMsg("Lỗi xóa chi tiết cách dùng !");
					}
				 } 
			});
		});

		
		$("#btnHuy").click(function(){
			$("#txtCACHDUNG").val("");
			$("#txtGHICHU").val("");
			if(isEdit)
				setEnabled(['btnThem','btnSua', 'btnXoa'], ['btnLuu', 'btnHuy','txtID', 'txtCACHDUNG', 'txtGHICHU','txtMA_CD','cboLOAI']);
			else
				setEnabled(['btnThem'], 
						['btnSua', 'btnXoa','btnLuu', 'btnHuy','txtID', 'txtCACHDUNG', 'txtGHICHU','txtMA_CD','cboLOAI']);
			flagLoading = false;
		});
		
	}
	
	function loadGridData() {
		if (flagLoading)
			return;
		
		var _sql_par =[];

		_sql_par = RSUtil.setSysParam(_sql_par, _param);
		GridUtil.loadGridBySqlPage(_gridDSCHId,"DMC55.LAYDS",_sql_par);
	}
	
	function setEnabled(_ena, _dis) {
		for (var i =0; i<_ena.length; i++) {
			$("#"+_ena[i]).attr('disabled', false);
		}
		for (var i =0; i<_dis.length; i++) {
			$("#"+_dis[i]).attr('disabled', true);
		}
	}
}

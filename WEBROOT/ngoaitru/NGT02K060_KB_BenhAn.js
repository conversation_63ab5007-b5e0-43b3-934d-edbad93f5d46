var ctl_ar=[{type:'buttongroup',id:'btnPrint',icon:'print',text:'In ấn'
	,children:[
		{id:'group_0',icon:'print',text:'IN PHIẾU',hlink:'#',group:true}
		,{id:'group_0_1',icon:'print',text:'Gi<PERSON>y ra viện',hlink:'#'}
		,{id:'group_0_2',icon:'print',text:'Gi<PERSON>y chuyển viện',hlink:'#'}
		,{id:'group_0_3',icon:'print',text:'Giấy hẹn khám',hlink:'#'}
		,{id:'group_0_4',icon:'print',text:'Bảng kê',hlink:'#'}
		,{id:'group_2_1',icon:'print',text:'Phiếu chỉ định CLS chung',hlink:'#'}
		,{id:'group_2_2',icon:'print',text:'Đơn thuốc',hlink:'#'}
		,{id:'group_2_5',icon:'print',text:'Phiếu khám bệnh vào viện',hlink:'#'}
		,{id:'group_2_6',icon:'print',text:'In tờ điều trị',hlink:'#'}
	]}
	,{type:'button',id:'btnDSKham',icon:'dskham',text:'DS Khám'},
	{type:'buttongroup',id:'btnBANGT',icon:'benhan',text:'BA NGT'
		,children:[
			{id:'btnBANGT_0',icon:'benhan',text:'Bệnh án',hlink:'#'}
			,{id:'btnBANGT_1',icon:'benhan',text:'Chọn bệnh án',hlink:'#'}
			,{id:'btnBANGT_2',icon:'benhan',text:'Mở bệnh án',hlink:'#'}
			,{id:'btnBANGT_3',icon:'benhan',text:'Đóng bệnh án',hlink:'#'}
			,{id:'btnBANGT_4',icon:'benhan',text:'Đưa ra khỏi bệnh án',hlink:'#'}
		]}
	,{type:'button',id:'btnCall',icon:'volume-up',text:'Gọi khám'}
	,{type:'button',id:'btnStart',icon:'khac',text:'Bắt đầu'}
	,{type:'button',id:'btnExam',icon:'kham',text:'Khám bệnh'}
	/*,{type:'buttongroup',id:'btnTreat',icon:'edit',text:'Điều trị'
		,children:[
			        {id:'treat_1',icon:'edit',text:'Tạo phiếu điều trị',hlink:'#'}
			        ]}*/
	,{type:'button',id:'btnService',icon:'dichvu',text:'Dịch vụ',hlink:'#'}
	,{type:'buttongroup',id:'btndrug',icon:'thuoc',text:'Thuốc',hlink:'#'
		,children:[
			{id:'drug_thuoc',icon:'thuoc',text:'THUỐC',hlink:'#',group:true},
			{id:'drug_1',icon:'thuoc',text:'Tạo đơn thuốc',hlink:'#'},
			/*{id:'drug_2',icon:'thuoc',text:'Tạo phiếu trả thuốc',hlink:'#'},	*/
			{id:'drug_mn',icon:'thuoc',text:'Tạo đơn thuốc mua ngoài',hlink:'#'},
			{id:'drug_1kt',icon:'thuoc',text:'Tạo đơn thuốc không thuốc',hlink:'#'},

			{id:'drug_thuocdy',icon:'thuoc',text:'THUỐC ĐÔNG Y',hlink:'#',group:true},
			{id:'drug_1dy',icon:'thuoc',text:'Tạo đơn thuốc đông y',hlink:'#'},
			{id:'drug_2dy',icon:'thuoc',text:'Tạo phiếu trả thuốc đông y',hlink:'#'},

			{id:'drug_vattu',icon:'thuoc',text:'VẬT TƯ',hlink:'#',group:true},
			{id:'drug_3',icon:'thuoc',text:'Tạo phiếu vật tư',hlink:'#'}
			/*,{id:'drug_4',icon:'thuoc',text:'Tạo phiếu trả vật tư',hlink:'#'}*/
		]}
	/*,{type:'buttongroup',id:'btnhandling',icon:'bell',text:'Xử trí khác',hlink:'#'  
		,children:[
		           {id:'handling_1',icon:'bell',text:'Chuyển phòng khám',hlink:'#'},
		           {id:'handling_2',icon:'bell',text:'Khám thêm phòng',hlink:'#'},
		           {id:'handling_3',icon:'bell',text:'Trả bệnh nhân (không khám)',hlink:'#'},
		           {id:'handling_4',icon:'bell',text:'Kết thúc khám',hlink:'#'},
		           {id:'handling_5',icon:'bell',text:'Xử trí khám bệnh',hlink:'#'}
		           ]}*/
	,{type:'button',id:'btnPhieuKham',icon:'xutri',text:'Xử trí KB'}
	,{type:'buttongroup',id:'btnKHAC',icon:'goikham',text:'Khác',hlink:'#'
		,children:[
			{id:'group_0',icon:'goikham',text:'KHÁC',hlink:'#',group:true},
			{id:'handling_1',icon:'goikham',text:'Chuyển phòng khám',hlink:'#'},
			{id:'handling_4',icon:'goikham',text:'Hồ sơ quản lý sức khỏe cá nhân',hlink:'#'},//nghiant 14062017*/
			/*{id:'handling_3',icon:'goikham',text:'Trả bệnh nhân (không khám)',hlink:'#'},*/
			{id:'btnKHAC_3',icon:'goikham',text:'Tai nạn thương tích',hlink:'#'},
			{id:'btnKHAC_8',icon:'goikham',text:'Phiếu vận chuyển',hlink:'#'},
			{id:'handling_2',icon:'goikham',text:'Đổi phòng khám',hlink:'#'},
			{id:'btnKHAC_9',icon:'goikham',text:'Nghỉ hưởng BHXH',hlink:'#'},
			/*{id:'btnKHAC_10',icon:'goikham',text:'Chỉ định thu khác',hlink:'#'},*/
			//Lập phiếu tạm ứng
			{id:'bntKHAC_ptu',icon:'goikham',text:'Lập phiếu tạm ứng',hlink:'#'},
			{id:'btnKHAC_10',icon:'goikham',text:'Nhập bệnh án',hlink:'#'},
			{id:'group_0',icon:'print',text:'XỬ TRÍ',hlink:'#',group:true},
			{id:'btnKHAC_0', arr:'1',icon:'goikham',text:'Thông tin tử vong',hlink:'#'},
			/*  {id:'btnKHAC_1',icon:'bell',text:'Biên bản tử vong',hlink:'#'},*/
			{id:'btnKHAC_2',icon:'goikham',text:'Kiểm điểm tử vong',hlink:'#'},
			{id:'btnKHAC_4',icon:'goikham',text:'Thông tin ra viện',hlink:'#'},
			{id:'btnKHAC_5',icon:'goikham',text:'Chuyển viện',hlink:'#'},
			{id:'btnKHAC_6',icon:'goikham',text:'Hẹn khám mới',hlink:'#'},
			{id:'btnKHAC_7',icon:'goikham',text:'Hẹn khám tiếp',hlink:'#'}
		]}
	/*,{type:'button',id:'handling_1',icon:'share',text:'Chuyển PK',hlink:'#'}*/
	,{type:'button',id:'btnKTKH',icon:'ketthuc',text:'Kết thúc khám',hlink:'#'},
];
//var toolbar=new JsToolbar('toolbarId');
//toolbar.buildToolbar(ctl_ar);
var toolbar=ToolbarUtil.build('toolbarId',ctl_ar);
toolbar.addEvent("btnStart","click",function(e){
	console.log('btnStart_click');
	console.log('txtFromDate='+toolbar.getValue("txtFromDate"));
});
var _toolbar=ToolbarUtil.getToolbar('toolbarId');

_toolbar.addEvent("print_1","click",function(e){
	console.log('print_1_click');
	console.log('txtToDate='+toolbar.getValue("txtToDate"));
});
toolbar.addEvent("txtSearch","click",function(e){
	console.log('txtSearch_click');
	console.log('txtFromDate='+toolbar.getValue("txtFromDate"));
});

function benhnhanList(opt) {

	var LNMBP_XetNghiem=1;
	var LNMBP_CDHA=2;
	var LNMBP_ChuyenKhoa=5;
	var LNMBP_DieuTri=4;
	var LNMBP_Phieuvattu = 8;
	var LNMBP_Phieuthuoc = 7;
	var _flgModeView='0';
	var _tsmobenhan = '0';
	var _enable_mobenhan = '0';
	var _checktienkhiketthuckham = '0';
	var _idkhambenh = 0;
	var _sophongkham = "0";
	var _trangthaikhambenh ='';
	var _phongdkid = 0;
	var htmlDlgThoiGian = ""; // fix lỗi chọn thời gian khi chuyển tab
	_grdDieuTri="grdDieuTri";
	$grdDieuTri = $("#" + _grdDieuTri);
	var _type = "";
	var _opt = opt;
	var _gridId="grdDSBenhNhan";
	this._gridHeader=" ,ICON,30,0,ns,l; ,ICONCLS,30,0,ns,l;KQCLS,KQCLS,0,0,t,l;khambenhid,KHAMBENHID,0,0,t,l;hosobenhanid,HOSOBENHANID,0,0,t,l;phongkhamdangkyid,PHONGKHAMDANGKYID,0,0,t,l;benhnhanid,BENHNHANID,0,0,t,l;doituongbenhnhanid,DOITUONGBENHNHANID,0,0,t,l;tiepnhanid,TIEPNHANID,0,0,t,l;loaitiepnhanid,LOAITIEPNHANID,0,0,t,l;trangthaikhambenh,TRANGTHAIKHAMBENH,10,0,t,l;xutrikhambenhid,XUTRIKHAMBENHID,10,0,t,l;SỐ HSBA,SOTHUTU,150,0,f,l;LẦN GỌI,LANGOI,70,0,t,l;MÃ BA,MAHOSOBENHAN,70,0,f,l;MÃ BN,MABENHNHAN,80,0,f,l;HỌ TÊN,TENBENHNHAN,245,0,f,l;MÃ BHYT,MA_BHYT,120,0,f,l;TRẠNG THÁI,TENTRANGTHAIKB,110,0,t,l;madichvu,MADICHVU,0,0,t,l;PHONGID,PHONGID,0,0,t,l;uutienkhamid,UUTIENKHAMID,0,0,t,l;LOAIBENHANID,LOAIBENHANID,0,0,t,l";
	var _SQL=["NGT02K060.EV001", "NGT02K001.EV002", "NGT02K001.EV003","NGT02K001.EV004", "NGT02K009.RV005", "NGT02K009.RV002"];
	this.load=doLoad;

	function doLoad() {
		_type = getParameterByName('type',window.location.search.substring(1));
		$('#txtMABENHNHANTK').focus();
		$('#toolbarIdbtnhandling').css('width','105px');
		$('#toolbarIdtxtFromDate').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));
		$('#toolbarIdtxtToDate').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'));

		$("#toolbarIdtxtFromDate").attr("valrule", "Từ ngày,date|max_length[10]");
		$("#toolbarIdtxtToDate").attr("valrule", "Đến ngày,date|max_length[10]");

		$("#toolbarIdbtndrug").attr("disabled", true);
		$("#toolbarIdbtnStart").attr("disabled", true);

		var sql_par=[];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("LAY.CAUHINH",sql_par.join('$'));
		if (data_ar != null && data_ar.length > 0) {
			/*if(data_ar[0].CH_KETTHUCKHAM == "1"){
				$('#toolbarIdbtnKTKH').css("display", 'none');
			}*/
			if(data_ar[0].DK_MOBENHAN == "1"){
				_tsmobenhan = 1;
			}

			if(data_ar[0].NGAYPK == "0"){
				$('#toolbarIdtxtFromDate').attr("disabled", true);
				$('#lblTuNgay').attr("disabled", true);
				$('#toolbarIdtxtToDate').attr("disabled", true);
				$('#lblDenNgay').attr("disabled", true);
			}else{
				$('#toolbarIdtxtFromDate').attr("disabled", false);
				$('#lblTuNgay').attr("disabled", false);
				$('#lblTuNgay').attr("disabled", false);
				$('#lblDenNgay').attr("disabled", false);
			}

			if(data_ar[0].HIDE_BTN_MO_BA == '1'){
				_enable_mobenhan = '1';
			}

			if(data_ar[0].CHUPANH == '1'){
				$('#divIMG').css("display", "");
			}

			if(data_ar[0].ANBANGT == '0'){
				$("#toolbarIdbtnBANGT").hide();
			}else{
				$("#toolbarIdbtnBANGT").show();
			}

			if(data_ar[0].HIDEDONTHUOCKT == "0"){
				$("#toolbarIddrug_1kt").hide();
			}else{
				$("#toolbarIddrug_1kt").show();
			}
		}

		ComboUtil.getComboTag("cboDOITUONG", "NGT02K060.LOADDT",[],"",{value:-1,text:'--Tất cả--'},'sql','','');

		var sql_par=[];
		sql_par.push({"name":"[0]","value": 87});
		ComboUtil.getComboTag("cboTRANGTHAI",_SQL[5],sql_par, "49",{value:0, text:'Tất cả'},"sql","","");

		$('#lblKHOA').text(_opt.dept_name);
		$('#lblPHONG').text(_opt.subdept_name);
		$("#hidHisId").val(opt.hospital_id);
		$("#hidDbSchema").val(opt.db_schema);
		$("#hidUserID").val(opt.user_id);
		$('#toolbarIdbtnKTKH').css('width','120px');
		$("#hidPHONGID").val(opt.phongid);
		$("#toolbarIdgroup_0_4").addClass("disabled");
		GridUtil.init(_gridId,"100%","360px","Danh sách bệnh nhân",false,this._gridHeader,false, { rowNum: 100,rowList: [100, 200, 300]});
		GridUtil.addExcelButton(_gridId,'Xuất excel',true);

		_loadGridData(_opt.phongid);
		_setButton(true);

		// lay số phòng gọi tiep don
		var sql_par=[];
		sql_par.push({"name":"[0]","value":_opt.khoaid});
		sql_par.push({"name":"[1]","value":_opt.phongid});

		var vsophong = jsonrpc.AjaxJson.ajaxExecuteQueryO("SOPHONG.TIEPDON", sql_par);
		vphong = JSON.parse(vsophong);
		_sophongkham = vphong[0].SOPHONG;
//		if (vphong != null && vphong.length > 0 && !isNaN(vphong[0].SOPHONG) && vphong[0].SOPHONG != ""){
//			_sophongkham = JSON.parse(vphong[0].SOPHONG);
//		}
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT_TAOPDT_FORMNHAPBA') == '1'){
			$("#toolbarIdbtnTaoPDT").show();
			$("#dvInPhieu").hide();
		}
		_bindEvent();
		htmlDlgThoiGian = $('#dlgWRAP_P').html();
	}

	function _bindEvent() {
		$.jMaskGlobals = {
			maskElements: 'input,td,span,div',
			dataMaskAttr: '*[data-mask]',
			dataMask: true,
			watchInterval: 300,
			watchInputs: true,
			watchDataMask: true,
			byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
			translation: {
				'0': {pattern: /\d/},
				'9': {pattern: /\d/, optional: true},
				'#': {pattern: /\d/, recursive: true},
				'A': {pattern: /[a-zA-Z0-9]/},
				'S': {pattern: /[a-zA-Z]/}
			}
		};

		// click button tim kiem
		$("#toolbarIdbtnSearchDate").on("click", function() {
			_loadTabHanhChinh(-1);
			_setButton(true);
			_loadGridData(_opt.phongid);
		});

		// click view anh lon
		$("#imgBN").on("click",function(e){
			var paramInput = {
				url : $('#imgBN').attr('src')
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgTTA","divDlg","manager.jsp?func=../ngoaitru/NGT02K059_show_img",paramInput,'THÔNG TIN ẢNH', 650,540);
			DlgUtil.open("dlgTTA");
		});

		$('#txtMABENHNHANTK').change(function(){
			if($(this).val().length > 9 || $(this).val().length <= 0){
				_loadGridData(_opt.phongid);
			}
		});

		// click thay doi trang thai tim kiem
		$('#cboTRANGTHAI').change(function(){
			_loadGridData(_opt.phongid);
		});

		$('#toolbarIdtxtFromDate').change(function(){
			_loadGridData(_opt.phongid);
		});

		$('#toolbarIdtxtToDate').change(function(){
			_loadGridData(_opt.phongid);
		});

		// click thay doi tu ngay
		$('#toolbarIdtxtFromDate').change(function(){
			var from = $('#toolbarIdtxtFromDate').val().substr(6,4) + $('#toolbarIdtxtFromDate').val().substr(3,2) + $('#toolbarIdtxtFromDate').val().substr(0,2)
				+ $('#toolbarIdtxtFromDate').val().substr(11,2) + $('#toolbarIdtxtFromDate').val().toString().substr(14,2);

			var sdate = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');
			if(from < sdate && _type != "1"){
				$("#toolbarId button").addClass('disabled');
				$("#toolbarIdbtnPrint").removeClass('disabled');
			}else{
				$("#toolbarId button").removeClass('disabled');
			}
		});

		// click chon benh nhan trong danh sach
		GridUtil.setGridParam(_gridId,{
			onSelectRow: function(index, selected) {
				if(selected === false){
					FormUtil.clearForm('divContentHC');
					$("#toolbarIdbtnLS").attr("disabled", true);
				}else{
					_selectedRow(index);
					$("#toolbarIdbtnLS").attr("disabled", false);
				}
				GridUtil.unmarkAll(_gridId);
				GridUtil.markRow(_gridId,index);
			},
			gridComplete: function(id){
				$(".jqgrow", '#' + _gridId).contextMenu( 'contextMenu', {
					bindings: {
						// nvangoc start L2PT-6142
						'xuatlogchinhsua': function (t) {
							xuatLogChinhSua();
						},
						// nvangoc end L2PT-6142
						'yeucaumolaibenhan': function (t) {
							_molaibenhan(0, "YÊU CẦU MỞ LẠI BỆNH ÁN");
						},
						'molaibenhan': function (t) {
							_molaibenhan(1, "MỞ LẠI BỆNH ÁN");
						},
						'goilaibnchuyenphong': function (t) {
							_goilaibnchuyenkham();
						},
						'btnLS_1': function (t) {
							paramInput={
								benhnhanId : $("#hidBENHNHANID").val()
							};
							dlgPopup=DlgUtil.buildPopupUrl("dlgLichSuDieuTri","divDlg","manager.jsp?func=../ngoaitru/NGT02K025_LichSuDieuTri",paramInput,"LỊCH SỬ ĐIỀU TRỊ",1320,610);
							DlgUtil.open("dlgLichSuDieuTri");
						},
						'btnLS_2': function (t) {
							paramInput={
								benhnhanId : $("#hidBENHNHANID").val()
							};
							dlgPopup=DlgUtil.buildPopupUrl("dlgLichSuBA","divDlg","manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn",paramInput,"LỊCH SỬ BỆNH ÁN",1320,610);
							DlgUtil.open("dlgLichSuBA");
						},

						'btnLSCongBHYT': function (t) {
							var paramInput={
								MABHYT : $('#txtSOTHEBHYT').val(),
								TENBENHNHAN : $('#txtTENBENHNHAN').val(),
								NGAYSINH : $('#txtNGAYSINH').val(),
								QRCODE : '',
								TUNGAY : '',
								DENNGAY : ''
							};

							dlgPopup=DlgUtil.buildPopupUrl(
								"divDlgDDT","divDlg","manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB",
								paramInput,"Thông tin lịch sử điều trị bệnh nhân",window.innerWidth*0.95,window.innerHeight*0.95);

							DlgUtil.open("divDlgDDT");
						},

						'btnLSKCB': function (t) {
							var paramInput={
								MABHYT : $('#txtSOTHEBHYT').val(),
								TENBENHNHAN : $('#txtTENBENHNHAN').val(),
								NGAYSINH : $('#txtNGAYSINH').val(),
								QRCODE : '',
								TUNGAY : '',
								DENNGAY : ''
							};

							dlgPopup=DlgUtil.buildPopupUrl(
								"divDlgDDT","divDlg","manager.jsp?func=../ngoaitru/NGT02K049_TraCuuCongBYT",
								paramInput,"Tra cứu thông tin Bộ Y Tế",window.innerWidth*0.95,window.innerHeight*0.95);
							DlgUtil.open("divDlgDDT");
						},

						'thanhtoanvp': function (t) {
							paramInput={
								tiepnhanid : $("#hidTIEPNHANID").val()
							};
							dlgPopup=DlgUtil.buildPopupUrl("dlgTTVP","divDlg","manager.jsp?func=../vienphi/VPI01T006_thanhtoanvienphi",paramInput,"THANH TOÁN VIỆN PHÍ",1250,600);
							DlgUtil.open("dlgTTVP");
						}
					},
					onContextMenu: function (event, menu) {
						var rowId = $(event.target).parent("tr").attr("id");
						var grid = $('#' + _gridId);
						grid.setSelection(rowId);
						return true;
					}
				});

				var ids = $("#"+_gridId).getDataIDs();
				for(var i=0;i<ids.length;i++){
					var id = ids[i];
					var row = $("#"+_gridId).jqGrid('getRowData',id);
					var _icon = '';
					var _iconcls = '';
					if(row.TRANGTHAIKHAMBENH == 1){
						_icon = '<center><img src="'+ _opt.imgPath[0] +'" width="15px"></center>';
					}else if(row.TRANGTHAIKHAMBENH == 4){
						_icon = '<center><img src="'+ _opt.imgPath[1] +'" width="15px"></center>';
					}else if(row.TRANGTHAIKHAMBENH == 9){
						_icon = '<center><img src="'+ _opt.imgPath[2] +'" width="15px"></center>';
					}

					if(row.KQCLS == "1"){
						_iconcls = '<center><img src="'+ _opt.imgPath[3] +'" width="15px"></center>';
					}else if(row.KQCLS == "2"){
						_iconcls = '<center><img src="'+ _opt.imgPath[4] +'" width="15px"></center>';
					}

					$("#"+_gridId).jqGrid ('setCell', id, 1, _icon);
					$("#"+_gridId).jqGrid ('setCell', id, 2, _iconcls);

					if(row.UUTIENKHAMID != "0"){
						$("#"+_gridId).jqGrid('setRowData', id, "", {color : 'blue'});
					}

					if(row.KHAMBENHID == $("#hidKHAMBENHID").val() && row.TRANGTHAIKHAMBENH != 1){
						GridUtil.unmarkAll(_gridId);
						GridUtil.markRow(_gridId,id);
						$('#'+_gridId).find("tr[id='" + id + "']").find('td').trigger( "click" );
					}
				}
			}
		});

		// open popup yeu cau mo lai/mo lai benh an
		function _molaibenhan(_kieu, _title){
			if($('#hidDUYETKETOAN').val() == "1" || $('#hidDUYETBH').val() == "2"){
				DlgUtil.showMsg('Đã duyệt kế toán/bảo hiểm ko thể mở lại bệnh án');
				return false;
			}
			if(_tsmobenhan != "1"){
				DlgUtil.showMsg('Bệnh nhân đã nhập viện không thể mở bệnh án');
				return false;
			}

			$('#toolbarIdbtnSearchDate').focus();
			var myVar={
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
				tiepnhanid : $('#hidTIEPNHANID').val(),
				hosobenhanid : $('#hidHOSOBENHANID').val(),
				kieu : _kieu // 1: mo benh an, 0: yêu cầu mở.
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgMOLAIBA","divDlg","manager.jsp?func=../ngoaitru/NGT02K029_YeuCauMoBenhAn",myVar,_title,500,220);
			DlgUtil.open("dlgMOLAIBA");

			//tuyennx_edit_20170713_start
			EventUtil.setEvent("assignSevice_mobenhan",function(e){
				if(e.msg == "1"){
					DlgUtil.showMsg('Bệnh nhân đã mở bệnh án');

					DlgUtil.close("dlgMOLAIBA");
					_loadGridData(_opt.phongid);
				}
				else if(e.msg == "2"){
					DlgUtil.showMsg('Đã gửi yêu cầu mở lại bệnh án');

					DlgUtil.close("dlgMOLAIBA");
					_loadGridData(_opt.phongid);
				}
				else{
					DlgUtil.showMsg('Cập nhật thông tin không thành công');
				}
			});
			//tuyennx_edit_20170713_end
		}

		// click button bắt đầu trên toolbar
		$("#toolbarIdbtnStart").on("click", function() {
			var objData = new Object();
			objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
			objData['KHAMBENHID'] = $("#hidKHAMBENHID").val();
			objData['TIEPNHANID'] = $("#hidTIEPNHANID").val();
			objData['DOITUONGID'] = $("#hidDOITUONGBENHNHANID").val();
			objData['PHONGID'] = _opt.phongid;

			// kiem tra con thuoc va xac nhan truoc khi vao kham:
			var b_MaBenhNhan = $("#txtSOTHEBHYT").val();
			var b_Loai = "2"; 			// ?
			var _objData = new Object();
			_objData["tenbenhnhan"] = $("#txtTENBENHNHAN").val();
			_objData["ngaysinh"] = $("#txtNGAYSINH").val();
			_objData["gioitinhid"] = $("#cboGIOITINH").val() == "Nam" ? "1" : "2";
			_objData["benhnhanid"] = $("#hidBENHNHANID").val();

			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT01T002.TKBN1",b_MaBenhNhan+'$'+b_Loai+'$'+JSON.stringify(_objData));

			if (data_ar != null && data_ar.length > 0){
				if(data_ar[0].NGAYTHUOC != "" && data_ar[0].NGAYTHUOC != null){
					var t_ngaythuoc = data_ar[0].NGAYTHUOC;
					var ngay = t_ngaythuoc.split('/');
					var ngaythuoc = ngay[2].substring(0,4)+ngay[1]+ngay[0];
					var ngaytn = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');
					var ngaykedon = data_ar[0].NGAYMAUBENHPHAM;

					if(parseInt(ngaykedon) != parseInt(ngaytn) && parseInt(ngaythuoc) > parseInt(ngaytn)){
						DlgUtil.showConfirm('Bệnh nhân đang còn thuốc của lần khám trước, có tiếp tục bắt đầu khám', function(flag){
							if(flag){
								_batdaukhamm(objData);
							}
						});
					}else{
						_batdaukhamm(objData);
					}
				}else{
					_batdaukhamm(objData);
				}
			}else{
				_batdaukhamm(objData);
			}
		});

		function _batdaukhamm(objData){
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I(_SQL[3],JSON.stringify(objData));
			if(ret == 1){
				_setButton(false);
				/*	var item_id = $("#hidINDEX").val();
                    $("#"+_gridId).jqGrid ('setCell', item_id, 8, 4);*/

				$("#toolbarIdbtnStart").attr("disabled", true);

				var index = $('#hidINDEX').val();
				_icon = '<center><img src="'+ _opt.imgPath[1] +'" width="15px"></center>';

				$("#"+_gridId).jqGrid ('setCell', index, 1, _icon);
				$("#"+_gridId).jqGrid ('setCell', index, 11, 4);

				var objj = new Object();
				var ttkb = "4";				// dang kham;
				objj._xutrikhambenh = "0"; 			// khong co xu tri;
				loadTabStatus(ttkb, objj);

			}else if(ret == 200){
				DlgUtil.showMsg('Bệnh nhân đối tượng BHYT + DV nhưng chưa thanh toán tiền DV');
			}else if(ret == 300){
				DlgUtil.showMsg('Bệnh nhân chưa thanh toán tiền công khám');
			}
			//_loadGridData(_opt.phongid);
		}

		// click button gọi khám
		$("#toolbarIdbtnCall").on("click", function() {
			var stt = $('#hidSOTHUTU').val();
			if(stt != ""){
				call.goivaokham(stt, _sophongkham);
				var objData = new Object();
				objData['PHONGKHAMDANGKYID'] = $("#hidPHONGKHAMDANGKYID").val();
				jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.GOIKHAM",JSON.stringify(objData));
			}
			_loadGridData(_opt.phongid);
		});

		// thong tin tai nan thuong tich
		$("#toolbarIdbtnKHAC_3").on("click", function() {
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				hosobenhanid: $("#hidHOSOBENHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgTaiNanThuongTich","divDlg","manager.jsp?func=../noitru/NTU02D035_Thongtintainanthuongtich",paramInput,"THÔNG TIN TAI NẠN THƯƠNG TÍCH",1000,470);
			DlgUtil.open("dlgTaiNanThuongTich");
		});

		//callback cho tai nan thuong tich
		EventUtil.setEvent("assignSevice_saveTNTT", function(e) {
			DlgUtil.showMsg(e.msg);
			DlgUtil.close("dlgTaiNanThuongTich");
		});

		// click button khám trên toolbar
		$("#toolbarIdbtnExam").on("click", function() {
			var myVar={
				khambenhId : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				madichvu : $("#hidMADICHVU").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				mabenhnhan : $("#txtMABENHNHAN").val()
			};

			dlgPopup=DlgUtil.buildPopupUrl("dlgKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K002_KhamBenhHoiBenh",myVar,"Khám hỏi bệnh",1300,630);

			//dlgPopup.open();
			DlgUtil.open("dlgKham");
		});

		// mo benh an da co ra de cap nhat
		$("#toolbarIdbtnBANGT_0").on("click", function() {
			_mobenhan_daingay();
		});

		//mo benh an moi
		$("#toolbarIdbtnBANGT_2").on("click", function() {
			DlgUtil.showConfirm("Bạn có chắc chắn mở bệnh án ngoai trú dài ngày cho bệnh nhân không?",function(flag) {
				if (flag) {
					// xu tri dieu tri ngoai tru
					// kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
					var _par_ins = [ $('#hidBENHNHANID').val(),36, $("#hidKHAMBENHID").val(),""];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY",_par_ins.join('$'));
					if(_return==-1){
						DlgUtil.showMsg('Bệnh chính của bệnh nhân không thuộc nhóm bệnh dài ngày nên không thể mở bệnh án');
					}else if(_return==-2){
						DlgUtil.showMsg('Bệnh nhân đã được mở bệnh án ngoại trú dài ngày, vào menu Bệnh an để cập nhật thông tin');
					}else if(_return==1){
						DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thành công');
					}else if(_return==0){
						DlgUtil.showMsg('Mở bệnh án ngoại trú dài ngày thất bại');
					}else if(_return==-3){
						DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán bệnh chính');
					}
				}
			});
		});

		//Chọn bệnh án
		$("#toolbarIdbtnBANGT_1").on("click", function() {
			DlgUtil.showConfirm("Bạn có muốn chọn bệnh án đang mở cho bệnh nhân không?",function(flag) {
				if (flag) {
					// xu tri dieu tri ngoai tru
					// kiem tra neu thuoc loai icd dai ngay va chua co benh an dai ngay thi mo benh an
					var _par_ins = [ $('#hidBENHNHANID').val(),36, $("#hidKHAMBENHID").val(),""];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.INS.DAINGAY",_par_ins.join('$'));
					if(_return==-1){
						DlgUtil.showMsg('Bệnh chính của bệnh nhân không thuộc nhóm bệnh dài ngày nên không thể chọn bệnh án');
					}else if(_return==-2){
						DlgUtil.showMsg('Bệnh nhân đã được mở bệnh án ngoại trú dài ngày, vào menu Bệnh an để cập nhật thông tin');
					}else if(_return==1){
						DlgUtil.showMsg('Chọn bệnh án ngoại trú dài ngày thành công');
					}else if(_return==0){
						DlgUtil.showMsg('Chọn bệnh án ngoại trú dài ngày thất bại');
					}else if(_return==-3){
						DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán bệnh chính');
					}
				}
			});
		});

		// click button khám dich vu trên toolbar
		$("#toolbarIdbtnService").on("click", function() {
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV"+ "&loaidichvu=" + 5,myVar,"Tạo phiếu chỉ định dịch vụ",1300,600);
			DlgUtil.open("divDlgDichVu");
		});

		// click button khám > tạo phiếu khám trên toolbar
		$("#toolbarIdtreat_1").on("click", function() {
			var myVar={
				khambenhId : $("#hidKHAMBENHID").val(),
				maubenhphamId:-1,
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				hosobenhanId : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanId : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanId : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid

			};
			EventUtil.setEvent("assignSevice_cancelP",function(e){
				DlgUtil.close("dlgPhieukham");
			});

			EventUtil.setEvent("treatment_cancel",function(e){
				$('#tabDieuTri').ntu02d027_dt({
					_grdDieuTri : 'grdDieuTri',
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid:  $("#hidBENHNHANID").val(),
					_lnmbp:	LNMBP_DieuTri,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
				DlgUtil.close("dlgPhieukham");
				DlgUtil.close("divDlgPhieuDieuTri");
			});

			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieukham","divDlg1","manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT",myVar,"Phiếu điều trị",1330,620);

			//dlgPopup.open();
			DlgUtil.open("dlgPhieukham");
			EventUtil.setEvent("assignSevice_cancel",function(e){
				DlgUtil.close("dlgCDDV");
			});
		});

		// click button Thuốc > Tạo phiếu thuốc.
		$("#toolbarIddrug_1").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			_openDialogThuoc('02D010', 0, "Chỉ định thuốc");
		});

		// click button Thuốc > Tạo phiếu thuốc không thuốc.
		$("#toolbarIddrug_1kt").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			_openDialogThuocK('02K044', 0, "Chỉ định thuốc không thuốc");
		});

		// click button Thuốc > Tạo phiếu thuốc đông y.
		$("#toolbarIddrug_1dy").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			_openDialogThuoc('02D017', 1, "Chỉ định thuốc YHCT");
		});

		// click button Thuốc > Tạo phiếu trả thuốc đông y.
		$("#toolbarIddrug_2dy").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			_openDialogThuoc('02D018', 1, "Trả thuốc YHCT");
		});

		// click button Thuốc > Tạo phiếu huy thuốc.
		$("#toolbarIddrug_2").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});

			_openDialogThuoc('02D014', 0, "Tạo phiếu trả thuốc");
		});

		//Tao phieu don thuoc mua ngoai
		$("#toolbarIddrug_mn").on("click", function() {
			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon: 1,
				dichvuchaid: "",
				opt : "02D011"
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D011","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,"Tạo đơn thuốc mua ngoài",1300,590);
			DlgUtil.open("divDlgTaoPhieuThuoc"+"02D011");
		});

		// click button Thuốc > Tạo phiếu vat tu.
		$("#toolbarIddrug_3").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});
			_openDialogThuoc('02D015', 1, "Chỉ định vật tư");
		});

		// click button Thuốc > Tạo phiếu huy vat tu.
		$("#toolbarIddrug_4").on("click",function(e){
			EventUtil.setEvent("assignDrug_fail",function(e){
				DlgUtil.close("dlgCDT");
			});
			_openDialogThuoc('02D016', 1, "Tạo phiếu trả vật tư");
		});

		//tao phieu van chuyen
		$("#toolbarIdbtnKHAC_8").on("click", function() {
			paramInput={
				chidinhdichvu : '1',
				loaidichvu : '14',
				loaiphieumbp: '16',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV"+ "&loaidichvu=" + 14,paramInput,"Phiếu vận chuyển",1300,600);
			DlgUtil.open("divDlgDichVu");
		});


		//nhap benh an
		$("#toolbarIdbtnBenhAn").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn một bệnh nhân.');
				return false;
			}
			if(_trangthaikhambenh == "1"){
				DlgUtil.showMsg('Chỉ thao tác với bệnh nhân đang khám  hoạc kết thúc khám.');
				return false;
			}
			var loaibenhanid = $("#hidLOAIBENHANID").val();
			var sovaovien = $("#hidSOTHUTU").val();

			var _sql_par1 = RSUtil.buildParam("", [loaibenhanid]);
			var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
			var _rows1 = JSON.parse(_data1);
			var _sreenName = _rows1[0].URL;
			var _tenloaibenhan = _rows1[0].TENLOAIBENHAN;
			var _maloaibenhan = _rows1[0].MALOAIBENHAN;

			if (_sreenName != '' &&  ((jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT_DONTHUOC_BADN') == 2) || (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT_DONTHUOC_BADN') == 3)) ) {
				paramInput = {
					khambenhid: $("#hidKHAMBENHID").val(),
					hosobenhanid: $("#hidHOSOBENHANID").val(),
					benhnhanid: $("#hidBENHNHANID").val(),
					loaibenhanid: loaibenhanid,
					maloaibenhan: _maloaibenhan,
					// nvangoc start L2PT-6142
					sovaovien: sovaovien
					// nvangoc end L2PT-6142
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../benhan/" + _sreenName, paramInput, "Cập nhật " + _tenloaibenhan, 1300, 610);
				DlgUtil.open("divDlgBenhAnDetail");
			} else {
				paramInput = {
					chidinhdichvu: '1',
					loaidichvu: '14',
					loaiphieumbp: '16',
					benhnhanid: $("#hidBENHNHANID").val(),
					khambenhid: $("#hidKHAMBENHID").val(),
					hosobenhanid: $("#hidHOSOBENHANID").val(),
					tiepnhanid: $("#hidTIEPNHANID").val(),
					doituongbenhnhanid: $("#hidDOITUONGBENHNHANID").val(),
					loaitiepnhanid: $("#hidLOAITIEPNHANID").val(),
//					subDeptId : _opt.phongid
					phongkhamdangkyid: $('#hidPHONGKHAMDANGKYID').val(),//L2PT-9165
					subDeptId: _phongdkid
				};

				dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU01H031_NhapBenhAn", paramInput, "Nhập bệnh án", 500, 250);
				DlgUtil.open("divDlgNhapBenhAn");
			}
		});

		EventUtil.setEvent("openBa", function (e) {
			DlgUtil.close("divDlgNhapBenhAn");
			_loadGridData(_opt.phongid);
			var _sql_par1 = RSUtil.buildParam("", [e.loaibenhanid]);
			var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
			var _rows1 = JSON.parse(_data1);
			var _sreenName=_rows1[0].URL;
			var _tenloaibenhan=_rows1[0].TENLOAIBENHAN;
			var _maloaibenhan=_rows1[0].MALOAIBENHAN;

			if(_sreenName != ''){
				paramInput={
					khambenhid: $("#hidKHAMBENHID").val(),
					hosobenhanid: $("#hidHOSOBENHANID").val(),
					benhnhanid: $("#hidBENHNHANID").val(),
					loaibenhanid: e.loaibenhanid,
					maloaibenhan: _maloaibenhan,
					// nvangoc start L2PT-6142
					sovaovien: e.sovaovien
					// nvangoc end L2PT-6142
				};
				dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../benhan/"+_sreenName,paramInput,"Cập nhật " +_tenloaibenhan,1300,610);
				DlgUtil.open("divDlgBenhAnDetail");
			} else {
				DlgUtil.showMsg('Không tồn tại loại bệnh án này trong dữ liệu');
				return;
			}


		});

		// click button Phiếu khám
		$("#toolbarIdbtnPhieuKham").on("click",function(e){
			_phieukham();
			_loadGridData(_opt.phongid);
		});

		// click button xu tri > xu tri kham benh
		$("#toolbarIdhandling_5").on("click",function(e){
			_phieukham();
			_loadGridData(_opt.phongid);
		});

		// open popup phieu kham benh
		function _phieukham(){
			var myVar={
				benhnhanId : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				mabenhnhan : $("#txtMABENHNHAN").val(),
				phongid:_opt.phongid,
				khoaid:_opt.khoaid,
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKB","divDlg","manager.jsp?func=../ngoaitru/NGT02K005_phieukhambenh",myVar,"Phiếu khám bệnh",1300,500);
			DlgUtil.open("dlgPhieuKB");
		}

		// Danh sach kham
		$("#toolbarIdbtnDSKham").on("click",function(e){
			var param = "&ngaybd="+$("#toolbarIdtxtFromDate").val()+"&ngaykt="+$("#toolbarIdtxtToDate").val();
			window.open('manager.jsp?func=../ngoaitru/NGT02K028_HienthiDanhSachKham&showMode=dlg','','width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		});

		$("#toolbarIdhandling_1").on("click",function(e){
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn chuyển phòng khám.');
				return false;
			}
			//tuyennx_add_20170906_start yc  HISL2BVDKHN-425
			if($('#hidXUTRIKHAMBENHID').val() == "6"){
				DlgUtil.showMsg('Bệnh nhân đã xử trí nhập viện không thể chuyển phòng khám.');
				return false;
			}
			//tuyennx_add_20170906_end

			var myVar={
				kieu : 1, //thêm phòng
				khambenhid : $('#hidKHAMBENHID').val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				dichvuid : $('#hidDICHVUID').val(),
				phongid : _opt._phongid,
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				phongkhamdangkyid : $('#hidPHONGKHAMDANGKYID').val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K046_them_chuyenphongkham",myVar,"Chuyển phòng khám",700,300);
			//dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham",myVar,"Chuyển phòng khám",700,300);
			DlgUtil.open("dlgPhieuKham");
		});

		$("#toolbarIdhandling_2").on("click",function(e){
			//tuyennx_add_20170906_start yc  HISL2BVDKHN-425
			if($('#hidXUTRIKHAMBENHID').val() == "6"){
				DlgUtil.showMsg('Bệnh nhân đã xử trí nhập viện không thể đổi phòng khám.');
				return false;
			}
			//tuyennx_add_20170906_end
			var myVar={
				kieu : 0, //chuyen
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				phongid : _opt._phongid,
				dichvuid : $('#hidDICHVUID').val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val()
			};
			//dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K046_them_chuyenphongkham",myVar,"Thêm phòng khám",700,400);
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieuKham","divDlg","manager.jsp?func=../ngoaitru/NGT01T004_tiepnhan_chuyenphongkham",myVar,"Chuyển phòng khám",700,300);
			DlgUtil.open("dlgPhieuKham");
		});

		//hanv_20170712:
		// cau hinh: Hien thi/an lap phieu tam ung
		var showPTU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT02K001_SHOW_PTU');
		if(showPTU=='0') {
			$("#toolbarIdbntKHAC_ptu").remove();
		}
		//Lap phieu tam ung
		$("#toolbarIdbntKHAC_ptu").on("click",function(e){
			var paramInput={
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid: $("#hidKHAMBENHID").val(),
				khoaid: opt.khoaid,
				thoigianvaovien: $('#txtDENKHAMLUC').val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgPhieuTamUng","divDlg","manager.jsp?func=../noitru/NTU01H021_PhieuTamUngBenhNhan",paramInput,"Lập phiếu tạm ứng",900,460);
			DlgUtil.open("divDlgPhieuTamUng");
		});
		//end hanv

		//nghiant 14062017
		$("#toolbarIdhandling_4").on("click",function(e){
			var myVar={
				benhnhanId : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				mabenhnhan : $("#txtMABENHNHAN").val(),
				phongid:_opt.phongid,
				khoaid:_opt.khoaid
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgHoSoSkCaNhan","divDlg","manager.jsp?func=../benhan/HOSO_QUANLYSUCKHOECANHAN",myVar,"Hồ sơ quản lý sức khỏe cá nhân",1300,600);
			DlgUtil.open("dlgHoSoSkCaNhan");
		});
		//end nghiant 14062017

		//Phiếu ra viện.
		$("#toolbarIdgroup_0_1").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy ra viện.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 1 && $('#hidXUTRIKHAMBENHID').val() != 9 && $('#hidXUTRIKHAMBENHID').val() != 3){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí ra viện, In phiếu chỉ với những bệnh nhân ra viện/khác');
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}, {
				name : 'i_hid',
				type : 'String',
				value : opt.hospital_id
			}, {
				name : 'i_sch',
				type : 'String',

				value : opt.db_schema
			}];
			var pars = ['NTU_PRINT_DOC_DKTNN'];
			var data_CH_DH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
			if(_opt.hospital_id == "939" && data_CH_DH != null && data_CH_DH == '1'){			// L2PT-2615 DKDHTNN: XUAT docx
				var rpName= "NTU009_GIAYRAVIEN_01BV01_QD4069_A5_939" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'docx';
				CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5_939", 'docx', par, rpName);
			}else{
				openReport('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", "pdf", par);
			}
		});

		//Phiếu hẹn khám.
		$("#toolbarIdgroup_0_3").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
				return;
			}

//			if($('#hidXUTRIKHAMBENHID').val() != 4 && $('#hidXUTRIKHAMBENHID').val() != 5){
//				DlgUtil.showMsg('Bệnh nhân không phải xử trí hẹn khám, In phiếu chỉ với những bệnh nhân hẹn khám');
//				return;
//			}

			var obj = new Object();
			obj.KHAMBENHID = $("#hidKHAMBENHID").val();
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.HK", JSON.stringify(obj));

			if(ret > 0){
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}];
				openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
			}else{
				DlgUtil.showMsg("Không có thông tin hẹn khám của bệnh nhân này.");
			}

		});

		//In Don thuoc.
		$("#toolbarIdgroup_2_2").on("click", function() {

			var sql_par=[];
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
				return false;
			}
			sql_par.push({"name":"[0]","value":$('#hidKHAMBENHID').val()});

			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
			var rows=$.parseJSON(data);
			if(rows.length == 1){
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rows[0].MAUBENHPHAMID
				}];
				openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
			}else if(rows.length > 1){
				paramInput={
					data : rows
				};
				dlgPopup=DlgUtil.buildPopupUrl("dlgInDonThuoc","divDlg","manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc",paramInput,"IN ĐƠN THUỐC",420,260);
				DlgUtil.open("dlgInDonThuoc");
			}else{
				DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
			}
		});

		//Phoi thanh toan
		$("#toolbarIdgroup_0_4").on("click", function() {
			var flag = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T004.10",$("#hidTIEPNHANID").val());
			var _dtbnid=$("#hidDOITUONGBENHNHANID").val();
			var _tiepnhanid=$("#hidTIEPNHANID").val();
			if(_dtbnid == 1) {
				vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_A4');
				if(flag==1)
					vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT035_BKCPKCBTUTUCNGOAITRU_A4');
			} else {
				if(flag==1)
					vienphi_tinhtien.inPhoiVP('1', _tiepnhanid, 'NGT035_BKCPKCBTUTUCNGOAITRU_A4');
			}
		});

		//Phiếu chuyển viện.
		$("#toolbarIdgroup_0_2").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy chuyển viện.');
				return;
			}

			if($('#hidXUTRIKHAMBENHID').val() != 7){
				DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
				return;
			}

			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];
			openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
		});

		//report phieu chi dinh CLS
		$("#toolbarIdgroup_2_1").on("click", function() {
			var par = [];
			if(_opt.hospital_id==922){
				_openReportClsLan(par, "PHIEU_CLSC_922",1); // in xet nghiem rieng
				_openReportClsLan(par, "PHIEU_CLSC_922",2); // in cdha rieng
				_openReportClsLan(par, "PHIEU_CLSC_922",5);//in pttt rieng
			}else{
				_openReport(par, "PHIEU_CLSC");
			}

		});

		//huongpv them
		function _openReportClsLan(param, reportName,i_loainhommaubenhpham){
			param.push({name : 'khambenhid', type : 'String',value : $("#hidKHAMBENHID").val()});
			param.push({name : 'i_loainhommaubenhpham', type : 'String',value : i_loainhommaubenhpham});
			CommonUtil.openReportGet('window', reportName, "pdf", param);
		}

		function _openReport(param, reportName){
			if($("#hidKHAMBENHID").val() == "-1" ){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
				return;
			}

			param.push({name : 'i_hid', type : 'String',value : _opt.hospital_id});
			param.push({name : 'i_sch', type : 'String',value : _opt.db_schema});
			param.push({name : 'khambenhid', type : 'String',value : $("#hidKHAMBENHID").val()});

			openReport('window', reportName, "pdf", param);
		}

		//Phiếu khám bệnh vào viện
		$("#toolbarIdgroup_2_5").on("click", function() {
			var par = [];
			if(_opt.hospital_id == "965"){			// BDHCM : xuat excel chuc nang nay;
				par = [ {
					name : 'i_hid',
					type : 'String',
					value : _opt.hospital_id
				}, {
					name : 'i_sch',
					type : 'String',
					value : _opt.db_schema
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				} ];


				if($("#hidDOITUONGBENHNHANID").val()== 3){
					var rpName= "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
					CommonUtil.inPhieu('window', "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'xlsx', par, rpName);
				}else{
					var rpName= "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
					CommonUtil.inPhieu('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'xlsx', par, rpName);
				}
			}else{
				if($("#hidDOITUONGBENHNHANID").val()== 3){
					_openReport(par, "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'pdf');
				}else{
					_openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
				}
			}
		});

		//In  tờ điều trị
		$("#toolbarIdbtnIn").on("click", function () {
			if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','NGT_INDIEUTRI_KHOANGTG') == 1){
				var _benhnhanid = $("#hidBENHNHANID").val();
				var _khambenhid = $("#hidKHAMBENHID").val();
				var _khoaid =  _opt.khoaid;
				$('.popup_remove').remove();
				$('#dlgWRAP_P').append($(htmlDlgThoiGian));
				var dlgINTHOIGIAN = DlgUtil.buildPopup("dlgINTHOIGIAN", "dlgTHOIGIAN", "In tờ điều trị theo khoảng thời gian", 800, 110, {
					"zIndex" : 998
				}); // L2PT-10648 duonghn
				DlgUtil.open("dlgINTHOIGIAN");
				var btnIN = $('#btn_IN');
				var btnClose = $('#btn_CLOSE');
				btnIN.click(function() {
					// L2PT-10648 duonghn start
					var tungay = $('#txtTUNGAY').val();
					var denngay = $('#txtDENNGAY').val();
					if (tungay && denngay && compareDate(denngay, tungay, 'DD/MM/YYYY HH:mm:ss')) {
						setErrValidate('toolbarIdtxtFromDate');
						DlgUtil.showMsg('Từ ngày không thể lớn hơn đến ngày');
						return false;
					}
					var par = [ {
						name : 'benhnhanid',
						type : 'String',
						value : $("#hidBENHNHANID").val()
					}, {
						name : 'tungay',
						type : 'String',
						value : tungay
					}, {
						name : 'denngay',
						type : 'String',
						value : denngay
					}, {
						name: 'phongid',
						type: 'String',
						value: $("#hidPHONGID").val() //L2PT-89064
					} ];
					openReport('window', "TODIEUTRI_MANTINH_NGT", "pdf", par);

				});
				btnClose.click(function() {
					dlgINTHOIGIAN.close();
				});
			}else {
				if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
					DlgUtil.showMsg('Hãy chọn một bệnh nhân.');
					return false;
				}
				var _benhnhanid = $("#hidBENHNHANID").val();
				var _khoaid = _opt.khoaid;
				var par = [{
					name: 'i_benhnhanid',
					type: 'String',
					value: _benhnhanid
				}, {
					name: 'i_khambenhid',
					type: 'String',
					value: $("#hidKHAMBENHID").val()
				}, {
					name: 'i_phongid',
					type: 'String',
					value: $("#hidPHONGID").val()
				}];

				var _type = "docx";			// theo yeu cau cua dev HUONGPV
				var rpName = "NGT020_TODIEUTRI_39BV01_QD4069_A4_" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + _type;
				CommonUtil.inPhieu('window', 'NGT020_TODIEUTRI_39BV01_QD4069_A4_965', _type, par, rpName);
				//openReport('window', "NGT020_TODIEUTRI_39BV01_QD4069_A4_965", "rtf", par);
			}
		});

		$("#printCA").on("click", function () {
			if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
				DlgUtil.showMsg('Hãy chọn một bệnh nhân.');
				return false;
			}
			var _param = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			}, {
				name: 'i_benhnhanid',
				type: 'String',
				value: $("#hidBENHNHANID").val()
			}, {
				name: 'i_khambenhid',
				type: 'String',
				value: $("#hidKHAMBENHID").val()
			}, {
				name: 'i_phongid',
				type: 'String',
				value: $("#hidPHONGID").val()
			}, {
				name: 'RPT_CODE',
				type: 'String',
				value: 'NGT020_TODIEUTRI_39BV01_QD4069_A4_965'
			}];
			_kyCaRpt(_param);
		});

		$("#printPDF").on("click", function() {
			if($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null){
				DlgUtil.showMsg('Hãy chọn một bệnh nhân.');
				return false;
			}
			var _benhnhanid = $("#hidBENHNHANID").val();
			var _khoaid = _opt.khoaid;
			var par = [{
				name : 'i_benhnhanid',
				type : 'String',
				value : _benhnhanid
			},{
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			},{
				name : 'i_phongid',
				type : 'String',
				value : $("#hidPHONGID").val()
			}];
			_openReport(par, "NGT020_TODIEUTRI_39BV01_QD4069_A4_965", 'pdf');
		});
		$("#toolbarIdbtnTaoPDT").on("click", function() {
			var myVar={
				khambenhId : $("#hidKHAMBENHID").val(),
				maubenhphamId:-1,
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				hosobenhanId : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanId : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanId : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid

			};
			EventUtil.setEvent("assignSevice_cancelP",function(e){
				DlgUtil.close("dlgPhieukham");
			});

			EventUtil.setEvent("treatment_cancel",function(e){
				$('#tcDieuTri').ntu02d027_dt({
					_grdDieuTri : 'grdDieuTri',
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid:  $("#hidBENHNHANID").val(),
					_lnmbp:	LNMBP_DieuTri,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
				DlgUtil.close("dlgPhieukham");
				DlgUtil.close("divDlgPhieuDieuTri");
			});

			dlgPopup=DlgUtil.buildPopupUrl("dlgPhieukham","divDlg1","manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT",myVar,"Phiếu điều trị",1330,620);

			//dlgPopup.open();
			DlgUtil.open("dlgPhieukham");
			EventUtil.setEvent("assignSevice_cancel",function(e){
				DlgUtil.close("dlgCDDV");
			});

		});
		EventUtil.setEvent("treatment_print", function(e) {
			//widget phieu dieu tri

			DlgUtil.close("divDlgPhieuDieuTri");

			var _benhnhanid = $("#hidBENHNHANID").val();
			var _khoaid = _opt.khoaid;
			var _maubenhphamid = e.msg;
			var par = [ {
				name : 'i_benhnhanid',
				type : 'String',
				value : _benhnhanid
			},
				{
					name : 'i_maubenhphamid',
					type : 'String',
					value : _maubenhphamid
				},{
					name : 'i_khoaid',
					type : 'String',
					value : _khoaid
				}];
			openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", "pdf", par);

		});
		// click button xu tri > trả bệnh nhân (không khám)
		$("#toolbarIdhandling_3").on("click",function(e){
			_xutribenhnhan(1);
		});

		// click button xu tri > ket thuc kham
		$("#toolbarIdbtnKTKH").on("click",function(e){
			_xutribenhnhan(2);
		});

		//Kiểm điểm tử vong
		$("#toolbarIdbtnKHAC_2").on("click", function() {
			paramInput={
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				diachi : $('#txtDIACHI').val(),
				namsinh : $('#hidNAMSINH').val(),
				khambenhid : $('#hidKHAMBENHID').val(),
				ngaytn : $('#hidNGAYTN').val(),
				nghenghiep : $("#hidTENNGHENGHIEP").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgKDTV","divDlg","manager.jsp?func=../ngoaitru/NGT02K033_KiemdiemTuVong",paramInput,"KIỂM ĐIỂM TỬ VONG",900,430);
			DlgUtil.open("dlgKDTV");
		});

		//Biên bản tử vong
		$("#toolbarIdbtnKHAC_1").on("click", function() {
			paramInput={
				mabenhnhan : $('#txtMABENHNHAN').val(),
				tenbenhnhan : $('#txtTENBENHNHAN').val(),
				diachi : $('#txtDIACHI').val(),
				namsinh : $('#hidNAMSINH').val(),
				khambenhid : $('#hidKHAMBENHID').val(),
				ngaytn : $('#hidNGAYTN').val(),
				nghenghiep : $("#hidTENNGHENGHIEP").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgBBTV","divDlg","manager.jsp?func=../ngoaitru/NGT02K032_BienbanTuVong",paramInput,"BIÊN BẢN TỬ VONG",900,430);
			DlgUtil.open("dlgBBTV");
		});

		// thong tin tu vong
		$("#toolbarIdbtnKHAC_0").on("click",function(e){
			_changexutri("8");
		});

		// thong tin ra viện
		$("#toolbarIdbtnKHAC_4").on("click",function(e){
			_changexutri("1");
		});

		// click tab lay ds
		$("#tabHanhChinhTab").on("click",function(e){
			height_window = $(window).height();   // returns height of browser viewport
			height_dsbn = $('#tabHanhChinh').height();
			height_divMain = $('#hidDocumentHeight').val();
			console.log('height_window1:' + height_window );
			console.log('height_divMain1:' + height_divMain );
			if(height_dsbn + 110 < height_window){
				$('#divMain').css('height',height_dsbn + 110);
			} else if(height_window < height_dsbn + 110){
				$("#divMain").css('height',height_dsbn + 110);
			} else if(height_dsbn + 110 == height_window) {
				$('#divMain').css('height',height_dsbn + 110);
			}
			_loadGridData(_opt.phongid);
		});

		// thong tin hen kham tiep
		$("#toolbarIdbtnKHAC_6").on("click",function(e){
			_changexutri("4");
		});

		// thong tin hen kham moi
		$("#toolbarIdbtnKHAC_7").on("click",function(e){
			_changexutri("5");
		});

		// thong tin hen kham moi
		$("#toolbarIdbtnKHAC_9").on("click",function(e){
			var param = {
				khambenhid : $('#hidKHAMBENHID').val()
			};
			_showDialog("NGT02K058_Thongtin_nghiduong", param, 'Thông tin nghỉ hưởng BHXH',800,380);
		});

		// thong tin chuyển viện
		$("#toolbarIdbtnKHAC_5").on("click",function(e){
			_changexutri("7");
		});

		// click tab benh an
		$("#tabBenhAnTab").on("click",function(e){
			//widget thong tin benh an
			$('#tabBenhAn').ntu02d022_ttba({
				_khambenhid: $("#hidKHAMBENHID").val()
			});
		});

		// click tab dieu tri
		$("#tabDieuTriTab").on("click",function(e){
			//widget khoi tao grid dieu tri
			$('#tabDieuTri').ntu02d027_dt({
				_grdDieuTri : 'grdDieuTri',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_DieuTri,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		//calback cho dong man hinh phieu dieu tri
		EventUtil.setEvent("treatment_cancel", function(e) {
			//widget phieu dieu tri
			$('#tabDieuTri').ntu02d027_dt({
				_grdDieuTri : 'grdDieuTri',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_DieuTri,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
			DlgUtil.close("divDlgPhieuDieuTri");
		});

		// click tab xet nghiem
		$("#tabXetNghiemTab").on("click",function(e){
			//widget khoi tao grid danh sach xet nghiem
			$('#tabXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_XetNghiem,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		// click tab CDHA
		$("#tabCDHATab").on("click",function(e){
			//widget khoi tao grid danh sach CDHA
			$('#tabCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_CDHA,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		// click tab chuyen khoa
		$("#tabChuyenKhoaTab").on("click",function(e){
			//widget khoi tao grid danh sach chuyen khoa
			$('#tabChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua  : 'grdCKketQua',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_ChuyenKhoa,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		// click tab thuoc
		$("#tabThuocTab").on("click",function(e){
			//widget cho tab thong tin thuoc
			$('#tabThuoc').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_Phieuthuoc,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		// click tab vat tu
		$("#tabVatTuTab").on("click",function(e){
			//widget cho tab thong tin vat tu
			$('#tabVatTu').ntu02d034_pvt({
				_grdVatTu : 'grdVatTu',
				_gridVatTuDetail : 'grdChiTietVatTu',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_Phieuvattu,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		// click tab viện phí
		$("#tabVienPhiTab").on("click",function(e){
			$('#tabVienPhi').vpi01t006_ttvp({
				_tiepnhanid : $("#hidTIEPNHANID").val(),
				_dept_id : _opt.khoaid
			});
		});

		//widget cho tab phieu van chuyen
		$("#tabPhieuVanChuyenTab").on("click",function(e){
			$('#tabPhieuVanChuyen').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid: $("#hidBENHNHANID").val(),
				_lnmbp:	'16',
				_loaidichvu: "14",
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		});

		function _openDialogThuoc(_opt,_loaikedon, _title){
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				mabenhnhan : $("#txtMABENHNHAN").val(),
				maubenhphamid : "",
				opt : _opt,
				loaikedon : _loaikedon,
				dichvuchaid : ''
			};

			dlgPopup=DlgUtil.buildPopupUrl("dlgCDT","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",myVar,_title,1300,600);
			DlgUtil.open("dlgCDT");
		}

		// mo popup don thuoc khong thuoc
		function _openDialogThuocK(_opt,_loaikedon, _title){
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				mabenhnhan : $("#txtMABENHNHAN").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				maubenhphamid : "",
				action : "Add"

			};

			dlgPopup=DlgUtil.buildPopupUrl("dlgCDT","divDlg","manager.jsp?func=../ngoaitru/NGT02K044_CapThuocK",myVar,_title,800,520);
			DlgUtil.open("dlgCDT");
		}

		function _ketthuckham(vkieu){
			//=========== check dich vu thanh toan dong thoi; chi check khi CLICK KET THUC KHAM;
			if(vkieu == 2){
				var par11 = ['HIS_CANHBAO_KHONG_TTDT'];
				var checkTt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par11.join('$'));

				var msgCheckTt = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV018",$("#hidTIEPNHANID").val());
				if(msgCheckTt && msgCheckTt != ''){
					DlgUtil.showMsg('Các dịch vụ ' + msgCheckTt + ' miễn giảm thanh toán đồng thời');
					if(checkTt == '1'){
						return;
					}
				}
			}
			//===========

			var myVar={
				kieu : vkieu,
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val()
			};
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K001.KTKHAM", JSON.stringify(myVar));
			var rets = ret.split(',');
			if(rets[0] == '1'){
				if(typeof rets[1] != 'undefined' && rets[1] != '' && rets[1] != '0'){
					DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?",function(flag) {
						if (flag) {
							_mobenhan_daingay();
							_loadGridData(_opt.phongid);
							_setButton(true);
							$("#toolbarIdbtndrug").attr("disabled", true);
							DlgUtil.showMsg('Cập nhật thông tin thành công');
							_loadGridData(_opt.phongid);
						}
					});
				}else{
					_loadGridData(_opt.phongid);
					_setButton(true);
					$("#toolbarIdbtndrug").attr("disabled", true);
					DlgUtil.showMsg('Cập nhật thông tin thành công');
					_loadGridData(_opt.phongid);
				}
			}else if(ret == 'kocoxutri'){
				DlgUtil.showMsg('Bệnh nhân hiện chưa có xử trí.');
			}else if(ret == 'coxutri'){
				DlgUtil.showMsg('Bệnh nhân có xử trí không trả bệnh nhân.');
			}else if(ret == 'codvcls'){
				DlgUtil.showMsg('Bệnh nhân đang có chỉ định dịch vụ CLS hoặc đã kê đơn thuốc');
			} else if(ret == 'connotien'){
				DlgUtil.showMsg('Bệnh nhân còn nợ tiền, phải thanh toán mới kết thúc khám.');
			} else if(ret == 'cophieudangsua'){
				DlgUtil.showMsg('Bệnh nhân có phiếu CLS/Đơn thuốc đang sửa, không kết thúc khám được.');
			}else if(ret == 'chuacochandoan'){
				DlgUtil.showMsg('Bệnh nhân chưa có chẩn đoán.');
			}
			else{
				DlgUtil.showMsg('Cập nhật thông tin không thành công');
			}
		}

		function _xutribenhnhan(vkieu){
			var myVar={
				khambenhid : $("#hidKHAMBENHID").val()
			};
			var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KTKHAM", JSON.stringify(myVar));
			if(check == 'codvcls'){
				DlgUtil.showMsg('Bệnh nhân đang có chỉ định dịch vụ trạng thái đang chờ tiếp nhận, có thể hủy phiếu để kết thúc khám');
				return false;
			}
			//tuyennx_add_start_20170727  y/c HISL2BVDKHN-247
			else if(check == 'ngaydichvu'){
				DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện không thể kết thúc khám');
				return false;
			}
			//tuyennx_add_end_20170727
			else if(check == 'pasdvcls'){
				DlgUtil.showConfirm("Bệnh nhân có dịch vụ đang thực hiện, bạn có muốn kết thúc khám không.",function(flag) {
					if (flag) {
						_ketthuckham(vkieu);
					}
				});
			}else if(check == '1'){
				_ketthuckham(vkieu);
			}
		}

		//calback cho man hinh chuyen mo thanh cong
		EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function(e) {
			DlgUtil.showMsg(e.msg);
			//widget khoi tao grid danh sach xet nghiem
			$('#tabXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_XetNghiem,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});

			//widget khoi tao grid danh sach CDHA
			$('#tabCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_CDHA,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});

			//widget khoi tao grid danh sach chuyen khoa
			$('#tabChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua  : 'grdCKketQua',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_ChuyenKhoa,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
			DlgUtil.close("divDlgDichVu");

			_loadGridData(_opt.phongid);
		});

		// calback cho man hinh tao phieu thuoc, vat tu
		EventUtil.setEvent("assignSevice_saveTaoPhieuThuoc", function(e) {
			DlgUtil.showMsg(e.msg);
			// reload lai widget cho tung man hinh
			if (e.option == "02D010" || e.option == "02D014") {
				// widget cho tab thong tin thuoc
				$('#tabThuoc').ntu02d033_pt({
					_grdPhieuthuoc : 'grdThuoc',
					_gridPhieuthuocDetail : 'grdChiTietThuoc',
					_khambenhid : $("#hidKHAMBENHID").val(),
					_benhnhanid : $("#hidBENHNHANID").val(),
					_lnmbp : LNMBP_Phieuthuoc,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if (e.option == "02D015" || e.option == "02D016") {
				// widget cho tab thong tin vat tu
				$('#tabVatTu').ntu02d034_pvt({
					_grdVatTu : 'grdVatTu',
					_gridVatTuDetail : 'grdChiTietVatTu',
					_khambenhid : $("#hidKHAMBENHID").val(),
					_benhnhanid : $("#hidBENHNHANID").val(),
					_lnmbp : LNMBP_Phieuvattu,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			}
			//DlgUtil.close("dlgCDT");
			//DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
			_loadGridData(_opt.phongid);
		});

		EventUtil.setEvent("assignSevice_closechuyenphong", function(e) {
			_loadGridData(_opt.phongid);
		});

		EventUtil.setEvent("assignDrug_cancel", function(e) {
			if(typeof e.badaingay != 'undefined' && e.badaingay != '' && e.badaingay != '0'){
				DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?",function(flag) {
					if (flag) {
						_mobenhan_daingay();

						var HIS_KOKTKHAM_KHICODONTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_I('his_common.com_lay_cauhinh_nd','HIS_KOKTKHAM_KHICODONTHUOC');
						var HIS_KETTHUCKHAM_KHICODONTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','HIS_KETTHUCKHAM_KHICODONTHUOC');
						if(e.type == '1' && HIS_KOKTKHAM_KHICODONTHUOC == '0' && HIS_KETTHUCKHAM_KHICODONTHUOC == '1'){
							_loadTabHanhChinh(-1);
							_loadGridData(_opt.phongid);
							_setButton(true);

							$('#tabXetNghiemTab').text("Xét nghiệm");
							$('#tabCDHATab').text("CĐHA");
							$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
							$('#tabThuocTab').text("Thuốc");
							$('#tabVatTuTab').text("Vật tư");
							$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
							$('#lblSTT').html('');
						}
					}
				});
			}

			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
		});

		//callback don thuoc khong thuoc
		EventUtil.setEvent("assignDrugK_cancel", function(e) {
			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
			DlgUtil.close("divDlgTaoPhieuThuoc");
		});

		//callback cho ho so benh an
		EventUtil.setEvent("assignSevice_saveHSBADetail", function(e) {
			DlgUtil.showMsg(e.msg);
			DlgUtil.close("divDlgBenhAnDetail");
		});

		EventUtil.setEvent("assignDrug_loisaibn", function(e) {
			DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');

			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
		});

		EventUtil.setEvent("assignDrug_khacbn", function(e) {
			DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');

			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
		});

		EventUtil.setEvent("assignDrug_xutri", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			DlgUtil.close("dlgCDT");
			DlgUtil.close("divDlgTaoPhieuThuoc"+e.option);
			_phieukham();
		});

		EventUtil.setEvent("assignSevice_savePTTT", function(e) {
			DlgUtil.showMsg(e.msg);
			$('#tabChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua  : 'grdCKketQua',
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid: $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_ChuyenKhoa,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
			DlgUtil.close("dlgPTTT");
		});

		EventUtil.setEvent("assignSevice_closephieukham", function(e) {
			if(e.msg == "6" || e.msg == "8" || e.msg == "0"){
				$("#toolbarIdbtndrug").attr("disabled", true);
			}else{
				$("#toolbarIdbtndrug").attr("disabled", false);
			}

			if(typeof e.badaingay != 'undefined' && e.badaingay != '' && e.badaingay != '0'){
				DlgUtil.showConfirm("Bệnh nhân có ICD bệnh án dài ngày, bạn muốn nhập thông tin bệnh án?",function(flag) {
					if (flag) {
						_mobenhan_daingay();

						if(e.type == '1'){
							_loadTabHanhChinh(-1);
							_loadGridData(_opt.phongid);
							_setButton(true);

							$('#tabXetNghiemTab').text("Xét nghiệm");
							$('#tabCDHATab').text("CĐHA");
							$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
							$('#tabThuocTab').text("Thuốc");
							$('#tabVatTuTab').text("Vật tư");
							$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
							$('#lblSTT').html('');
						}
					}else{
						if(e.type == '1'){
							_loadTabHanhChinh(-1);
							_loadGridData(_opt.phongid);
							_setButton(true);

							$('#tabXetNghiemTab').text("Xét nghiệm");
							$('#tabCDHATab').text("CĐHA");
							$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
							$('#tabThuocTab').text("Thuốc");
							$('#tabVatTuTab').text("Vật tư");
							$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
							$('#lblSTT').html('');
						}
					}
				});
			}else{
				if(e.type == '1'){
					_loadTabHanhChinh(-1);
					_loadGridData(_opt.phongid);
					_setButton(true);

					$('#tabXetNghiemTab').text("Xét nghiệm");
					$('#tabCDHATab').text("CĐHA");
					$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
					$('#tabThuocTab').text("Thuốc");
					$('#tabVatTuTab').text("Vật tư");
					$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
					$('#lblSTT').html('');
				}
			}
			DlgUtil.close("dlgPhieuKB");
		});

		EventUtil.setEvent("assignSevice_khacbnxutri", function(e) {
			DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');
			DlgUtil.close("dlgPhieuKB");
		});

		EventUtil.setEvent("assignSevice_capthuoc", function(e) {
			DlgUtil.close("dlgPhieuKB");
			_openDialogThuoc('02D010', 0, "Chỉ định thuốc");
		});

		EventUtil.setEvent("exam_cancel", function(e) {
			$('#tabBenhAn').ntu02d022_ttba({
				_khambenhid: $("#hidKHAMBENHID").val()
			});
			_loadTabHanhChinh($("#hidKHAMBENHID").val());
			//_loadGridData(_opt.phongid); tạm bỏ
			DlgUtil.close("dlgKham");
		});

		EventUtil.setEvent("close_chuyenphongkham", function(e) {
			if(e.type == '1'){
				_loadTabHanhChinh(-1);
				_loadGridData(_opt.phongid);
				_setButton(true);
			}
			DlgUtil.close("dlgPhieuKham");
		});

		EventUtil.setEvent("chidinhthukhac", function(e) {

			paramInput={
				chidinhdichvu : '1',
				loaidichvu : '1',
				loaiphieumbp: '17',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : e.phongchidinh
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV"+ "&loaidichvu=" + 1,paramInput,"Phiếu thu khác",1300,600);
			DlgUtil.open("divDlgDichVu");

			DlgUtil.close("dlgPhieuKham");
		});



		EventUtil.setEvent("exam_khacbnkbhb", function(e) {
			DlgUtil.showMsg('Hãy chọn lại bệnh nhân.');
			DlgUtil.close("dlgKham");
		});

		EventUtil.setEvent("exam_cddv", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			DlgUtil.close("dlgKham");

			var myVar={
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : _opt.phongid
			};

			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV"+ "&loaidichvu=" + 5,myVar,"Tạo phiếu chỉ định dịch vụ",1300,600);
			DlgUtil.open("divDlgDichVu");
		});

		EventUtil.setEvent("exam_capthuoc", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			DlgUtil.close("dlgKham");
			DlgUtil.close("dlgPhieuKB");
			_openDialogThuoc('02D010', 0, "Chỉ định thuốc");
		});

		EventUtil.setEvent("exam_xutri", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			DlgUtil.close("dlgKham");

			_phieukham();
		});

		EventUtil.setEvent("exam_save", function(e) {
			$("#hidKHAMBENHID").val(e.khambenhid);
			//widget thong tin benh an
			$('#tabBenhAn').ntu02d022_ttba({
				_khambenhid: $("#hidKHAMBENHID").val()
			});
			_loadGridData(_opt.phongid);
			//DlgUtil.close("dlgKham");
		});

		EventUtil.setEvent("assignSevice_savethemchuyenphong", function(e) {
			// load du lieu cho tab Hanh Chinh
			_loadTabHanhChinh($('#hidKHAMBENHID').val());
			DlgUtil.close("dlgPhong");
		});

		//calback cho MAN HINH TAO BAN SAO DON THUOC
		EventUtil.setEvent("assignSevice_SaveCopyMbp", function(e) {
			DlgUtil.showMsg(e.msg);
			if(e.type == '7'){
				//widget cho tab thong tin thuoc
				$('#tabThuoc').ntu02d033_pt({
					_grdPhieuthuoc : 'grdThuoc',
					_gridPhieuthuocDetail : 'grdChiTietThuoc',
					_khambenhid : $("#hidKHAMBENHID").val(),
					_benhnhanid : $("#hidBENHNHANID").val(),
					_lnmbp : LNMBP_Phieuthuoc,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if(e.type == '8'){
				//widget cho tab thong tin thuoc
				$('#tabVatTu').ntu02d034_pvt({
					_grdVatTu : 'grdVatTu',
					_gridVatTuDetail : 'grdChiTietVatTu',
					_khambenhid : $("#hidKHAMBENHID").val(),
					_benhnhanid : $("#hidBENHNHANID").val(),
					_lnmbp : LNMBP_Phieuvattu,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if(e.type == '1'){
				$('#tabXetNghiem').ntu02d024_ttxn({
					_gridXnId : "grdXetNghiem",
					_gridXnDetailId : "grdXetNghiemChiTiet",
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid:  $("#hidBENHNHANID").val(),
					_lnmbp:	LNMBP_XetNghiem,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if(e.type == '2'){
				$('#tabCDHA').ntu02d025_cdha({
					_gridCDHA : "grdCDHA",
					_gridCDHADetail : "grdCDHAChiTiet",
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid:  $("#hidBENHNHANID").val(),
					_lnmbp:	LNMBP_CDHA,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			} else if(e.type == '5'){
				$('#tabChuyenKhoa').ntu02d026_ck({
					_gridCK : 'grdCK',
					_gridCKDetail : 'grdCKChitiet',
					_grdCKketQua  : 'grdCKketQua',
					_khambenhid: $("#hidKHAMBENHID").val(),
					_benhnhanid:  $("#hidBENHNHANID").val(),
					_lnmbp:	LNMBP_ChuyenKhoa,
					_modeView: _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid: ""
				});
			}
			_loadGridData(_opt.phongid);
			DlgUtil.close("divDlgCopyMbp");
		});
	}

	function _mobenhan_daingay(){
		// var _par=[];
		// var _hosobenhanid=null;
		// _par=RSUtil.buildParam("",[ $("#hidHOSOBENHANID").val()]);
		// var dataDaingay=jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.CHECK_DAINGAY", _par);
		// _rowsDaingay= JSON.parse(dataDaingay);
		// var _loaibadaingay = -1;
		// if(_rowsDaingay!=null && _rowsDaingay.length>0){
		//    _loaibadaingay=_rowsDaingay[0].LOAIBENHANID;
		//    _hosobenhanid = _rowsDaingay[0].HOSOBENHANID;
		// }

		var object = {};
		FormUtil.setFormToObject('divContentHC', '', object);
		var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
		var dataDaingay = {};
		if (dataDaingays && dataDaingays.length > 0) {
			dataDaingay = dataDaingays[0];
		}
		if (!dataDaingay) {
			DlgUtil.showMsg("Bệnh nhân chưa có bệnh án dài ngày!");
			return;
		}
		var _hosobenhanid = dataDaingay.HOSOBENHANID;
		var _loaibadaingay = dataDaingay.LOAIBENHANID;

		if(_loaibadaingay==36){
			var _sql_par1 = [];
			_sql_par1=RSUtil.buildParam("",[36]);
			var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
			var _rows1 = JSON.parse(_data1);
			var _sreenName=_rows1[0].URL;
			var _tenloaibenhan=_rows1[0].TENLOAIBENHAN
			var _maloaibenhan=_rows1[0].MALOAIBENHAN;

			paramInput={
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : _hosobenhanid,
				benhnhanid :  $("#hidBENHNHANID").val(),
				loaibenhanid : 36,
				maloaibenhan : _maloaibenhan
			};
			dlgPopup=DlgUtil.buildPopupUrl("divDlgBenhAnDetail","divDlg","manager.jsp?func=../benhan/"+_sreenName,paramInput,"Cập nhật " +_tenloaibenhan,1300,610);
			DlgUtil.open("divDlgBenhAnDetail");//
		}else{
			DlgUtil.showMsg('Bệnh nhân chưa được mở bệnh án dài ngày để cập nhật!');
		}
	}

	function _loadGridData(phongid) {
		var from = $('#toolbarIdtxtFromDate').val().substr(6,4) + $('#toolbarIdtxtFromDate').val().substr(3,2) + $('#toolbarIdtxtFromDate').val().substr(0,2)
			+ $('#toolbarIdtxtFromDate').val().substr(11,2) + $('#toolbarIdtxtFromDate').val().toString().substr(14,2);

		var to = $('#toolbarIdtxtToDate').val().substr(6,4) + $('#toolbarIdtxtToDate').val().substr(3,2) + $('#toolbarIdtxtToDate').val().substr(0,2)
			+ $('#toolbarIdtxtToDate').val().substr(11,2) + $('#toolbarIdtxtToDate').val().toString().substr(14,2);
		if(from > to){
			setErrValidate('toolbarIdtxtFromDate');
			DlgUtil.showMsg('Sai điều kiện tìm kiếm, từ ngày không thể lớn hơn đến ngày');
			return false;
		}

		var sql_par=[];
		sql_par.push({"name":"[0]","value":$("#toolbarIdtxtFromDate").val()});
		sql_par.push({"name":"[1]","value":$("#toolbarIdtxtToDate").val()});
		sql_par.push({"name":"[2]","value": phongid});
		sql_par.push({"name":"[3]","value": $("#cboTRANGTHAI").val()});
		sql_par.push({"name":"[4]","value": $("#txtMABENHNHANTK").val()});
		sql_par.push({"name":"[5]","value": $("#cboDOITUONG").val()});
		sql_par.push({"name":"[6]","value": $("#cboLOAI").val()});
		GridUtil.loadGridBySqlPage(_gridId,_SQL[0],sql_par);
	}

	function _goilaibnchuyenkham(){
		var myVar={
			phongkhamdangkyid : $("#hidPHONGKHAMDANGKYID").val(),
			tiepnhanid : $("#hidTIEPNHANID").val(),
			doituongbtid : $("#hidDOITUONGBENHNHANID").val()
		};
		var check = jsonrpc.AjaxJson.ajaxCALL_SP_S("GOILAI.CHUYENKHAM", JSON.stringify(myVar));
		if(check == 'kochuyenphong'){
			DlgUtil.showMsg('Bệnh nhân chưa chuyển sang phòng khám khác từ phòng này');
		}else if(check == 'dakham'){
			DlgUtil.showMsg('Phòng chuyển tới đã hoặc đang khám, không gọi lại được');
		}else if(check == 'dathutien'){
			DlgUtil.showMsg('Phòng chuyển tới bệnh nhân đã đóng tiền, không hủy được');
		}else if(check == 'dathuphi'){
			DlgUtil.showMsg('Bệnh nhân đã đóng tiền không hủy được, muốn hủy phải hủy hóa đơn trước');
		}else if(check == '1'){
			DlgUtil.showMsg('Hủy chuyển phòng khám thành công');
		}
	}

	//======= SU DUNG TRONG TRUONG HOP CHO PHEP
	function loadTabStatus(_trangthaikhambenh, obj){
		var _xutrikhambenh = obj._xutrikhambenh;

		if(_trangthaikhambenh=='9'){
			_flgModeView='1';
			$("#toolbarIdgroup_0_4").removeClass("disabled");
			$("#goilaibnchuyenphong").addClass("disabled");
			_setButton(true);
			$("#toolbarIdbtndrug").attr("disabled", true);
			$("#toolbarIdbtnStart").attr("disabled", true);

			if(_enable_mobenhan != '1'){
				$("#yeucaumolaibenhan").addClass('disabled');
				$("#molaibenhan").remove();
			}else{
				$("#yeucaumolaibenhan").removeClass('disabled');
			}
		}else{
			_flgModeView='0';
			$("#toolbarIdgroup_0_4").addClass("disabled");
			$("#yeucaumolaibenhan").addClass('disabled');
			if(_trangthaikhambenh == "4"){
				$("#goilaibnchuyenphong").removeClass("disabled");
				_setButton(false);
				/*if(_xutrikhambenh == "6" || _xutrikhambenh == "8" || _xutrikhambenh == "0"){
					$("#toolbarIdbtndrug").attr("disabled", true);
				}else{
					$("#toolbarIdbtndrug").attr("disabled", false);
				}*/
				_disableMenuXuTri(_xutrikhambenh);

				$("#toolbarIdbtnStart").attr("disabled", true);
				$("#toolbarIdbtnKHAC_3").removeClass("disabled");
				$("#toolbarIdbtnKHAC_8").removeClass("disabled");
				$("#toolbarIdhandling_1").removeClass("disabled");
			}else if(_trangthaikhambenh == 1){
				$("#toolbarIdbtnStart").attr("disabled", false);
				$("#toolbarIdbtndrug").attr("disabled", true);
				$("#goilaibnchuyenphong").addClass("disabled");
				_setButton(true);
				$("#toolbarIdbtnKHAC").attr("disabled", false);
				$("#toolbarIdbtnKHAC_3").addClass("disabled");
				$("#toolbarIdbtnKHAC_8").addClass("disabled");
				$("#toolbarIdhandling_1").addClass("disabled");
				_disableMenuXuTri(_xutrikhambenh);
			}
		}
	}


	// ======= SU DUNG TRONG TRUONG HOP CHO PHEP
	function _selectedRow(item_id){
		if (item_id != null && item_id.length > 0) {
			FormUtil.clearForm('divContentHC');

			var selRowId = $('#' + _gridId).jqGrid ('getGridParam', 'selrow');
			var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);

			$("#hidKHAMBENHID").val('-1');
			_phongdkid = rowData.PHONGID;
			var _khambenhid = rowData.KHAMBENHID;
			var _hosobenhanid = rowData.HOSOBENHANID;
			var _phongkhamdangkyid = rowData.PHONGKHAMDANGKYID;
			var _benhnhanid = rowData.BENHNHANID;
			var _doituongbenhnhanid = rowData.DOITUONGBENHNHANID;
			var _tiepnhanid = rowData.TIEPNHANID;
			var _loaitiepnhanid = rowData.LOAITIEPNHANID;
			_trangthaikhambenh = rowData.TRANGTHAIKHAMBENH;
			var _xutrikhambenh = rowData.XUTRIKHAMBENHID;
			var _sothutu = rowData.SOTHUTU;

			$("#hidKHAMBENHID").val(_khambenhid);
			$("#hidHOSOBENHANID").val(_hosobenhanid);
			$("#hidPHONGKHAMDANGKYID").val(_phongkhamdangkyid);
			$("#hidBENHNHANID").val(_benhnhanid);
			$("#hidDOITUONGBENHNHANID").val(_doituongbenhnhanid);
			$("#hidTIEPNHANID").val(_tiepnhanid);
			$("#hidLOAITIEPNHANID").val(_loaitiepnhanid);
			$('#hidXUTRIKHAMBENHID').val(_xutrikhambenh);
			$("#hidINDEX").val(item_id);
			$("#hidSOTHUTU").val(_sothutu);
			$("#hidHisId").val(opt.hospital_id);
			$("#hidUserID").val(opt.user_id);
			$("#hidMADICHVU").val(rowData.MADICHVU);
			$('#lblSTT').html(_sothutu);
			$('#hidLOAIBENHANID').val(rowData.LOAIBENHANID);

			var objj = new Object();
			objj._xutrikhambenh = _xutrikhambenh;
			loadTabStatus(_trangthaikhambenh, objj);

//			if(_trangthaikhambenh=='9'){
//				_flgModeView='1';
//				$("#toolbarIdgroup_0_4").removeClass("disabled");
//				$("#goilaibnchuyenphong").addClass("disabled");
//				_setButton(true);
//				$("#toolbarIdbtndrug").attr("disabled", true);
//				$("#toolbarIdbtnStart").attr("disabled", true);
//				
//				if(_enable_mobenhan != '1'){
//					$("#yeucaumolaibenhan").addClass('disabled');
//					$("#molaibenhan").remove();
//				}else{
//					$("#yeucaumolaibenhan").removeClass('disabled');
//				}
//			}else{
//				_flgModeView='0';
//				$("#toolbarIdgroup_0_4").addClass("disabled");
//				$("#yeucaumolaibenhan").addClass('disabled');
//				if(_trangthaikhambenh == "4"){
//					$("#goilaibnchuyenphong").removeClass("disabled");
//					_setButton(false);
//					/*if(_xutrikhambenh == "6" || _xutrikhambenh == "8" || _xutrikhambenh == "0"){
//						$("#toolbarIdbtndrug").attr("disabled", true);
//					}else{
//						$("#toolbarIdbtndrug").attr("disabled", false);
//					}*/
//					_disableMenuXuTri(_xutrikhambenh);
//					
//					$("#toolbarIdbtnStart").attr("disabled", true);
//					$("#toolbarIdbtnKHAC_3").removeClass("disabled");
//					$("#toolbarIdbtnKHAC_8").removeClass("disabled");
//					$("#toolbarIdhandling_1").removeClass("disabled");
//				}else if(_trangthaikhambenh == 1){
//					$("#toolbarIdbtnStart").attr("disabled", false);
//					$("#toolbarIdbtndrug").attr("disabled", true);
//					$("#goilaibnchuyenphong").addClass("disabled");
//					_setButton(true);
//					$("#toolbarIdbtnKHAC").attr("disabled", false);
//					$("#toolbarIdbtnKHAC_3").addClass("disabled");
//					$("#toolbarIdbtnKHAC_8").addClass("disabled");
//					$("#toolbarIdhandling_1").addClass("disabled");
//					_disableMenuXuTri(_xutrikhambenh);
//				}
//			}

			// load du lieu cho tab Hanh Chinh
			_loadTabHanhChinh(_khambenhid);

			var vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.VIENPHI",rowData.TIEPNHANID);
			if(vp_ar != null && vp_ar.length > 0){
				var data = vp_ar[0];
				$('#txtTIENTAMUNG').val(formatNumber(data.TAMUNG) + " đ");
				$('#txtTONGCHIPHI').val(formatNumber(data.TONGTIENDV) + " đ");
			}
			/*
			/*vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.05",rowData.TIEPNHANID);
			if(vp_ar != null && vp_ar.length > 0){
				var data = vp_ar[0];
				$('#txtTONGCHIPHI').val(formatNumber(data.TONGTIENDV) + " đ");
			}*/
		}else{
			FormUtil.clearForm("divContentHC", "");
			FormUtil.clearForm("tabBenhAn", "");
			$("#hidKHAMBENHID").val(-1);
			$("#toolbarIdbtnStart").attr("disabled", true);
			$('#lblTAMUNG').html('');
			$('#lblTONGCHIPHI').html('');
			$('#lblBAOHIEMTHANHTOAN').html('');
			$('#hidXUTRIKHAMBENHID').val('');
		}
	}

	//calback cho MA HINH SUA PHONG THUC HIEN
	EventUtil.setEvent("assignSevice_SavePhongThucHien", function(e) {
		DlgUtil.showMsg(e.msg);
		//reload danh sach xet nghiem
		if(e.loaiPhieu==LNMBP_XetNghiem){
			$('#tabXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_XetNghiem,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		}else if(e.loaiPhieu==LNMBP_CDHA){
			$('#tabCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid: $("#hidKHAMBENHID").val(),
				_benhnhanid:  $("#hidBENHNHANID").val(),
				_lnmbp:	LNMBP_CDHA,
				_modeView: _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid: ""
			});
		}

		DlgUtil.close("divDlgEditOrgDone");
	});

	function _loadTabHanhChinh(khambenhid){
		var sql_par=[khambenhid, _phongdkid];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O(_SQL[1],sql_par.join('$'));
		if (data_ar != null && data_ar.length > 0) {
			var row=data_ar[0];
			FormUtil.clearForm("subdivContentHC","");
			FormUtil.setObjectToForm("divContentHC","",row);

			if (row.ANHBENHNHAN != null){
				var imgTag = document.getElementById('imgBN');
				imgTag.src = row.ANHBENHNHAN;
			}else{
				$('#imgBN').removeAttr('src');
			}

			if (row.BTNTHUOC == "1" && row.TRANGTHAI_STT == "4"){
				$("#toolbarIdbtndrug").attr("disabled", false);
			}

			/*if (row.TRANGTHAI_STT == "9"){
				$("#toolbarIdhandling_1").attr("disabled", true);
			}else{
				$("#toolbarIdhandling_1").attr("disabled", false);
			}*/

			if(data_ar[0].SLXN != 0){
				$('#tabXetNghiemTab').text("Xét nghiệm("+data_ar[0].SLXN+")");
			}else{
				$('#tabXetNghiemTab').text("Xét nghiệm");
			}

			if(data_ar[0].SLCDHA != 0){
				$('#tabCDHATab').text("CĐHA("+data_ar[0].SLCDHA+")");
			}else{
				$('#tabCDHATab').text("CĐHA");
			}

			if(data_ar[0].SLCHUYENKHOA != 0){
				$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật("+data_ar[0].SLCHUYENKHOA+")");
			}else{
				$('#tabChuyenKhoaTab').text("Phẫu thuật thủ thuật");
			}

			if(data_ar[0].SLTHUOC != 0){
				$('#tabThuocTab').text("Thuốc("+data_ar[0].SLTHUOC+")");
			}else{
				$('#tabThuocTab').text("Thuốc");
			}

			if(data_ar[0].SLVATTU != 0){
				$('#tabVatTuTab').text("Vật tư("+data_ar[0].SLVATTU+")");
			}else{
				$('#tabVatTuTab').text("Vật tư");
			}

			if(data_ar[0].SLVANCHUYEN != 0){
				$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển("+data_ar[0].SLVANCHUYEN+")");
			}else{
				$('#tabPhieuVanChuyenTab').text("Phiếu vận chuyển");
			}
		}else{
			FormUtil.clearForm("divContentHC","");
		}
	}

	function _formatRow(_rId, _fIndex)
	{
		var _icon = '';
		if(_opt.imgPath[_fIndex] != '')
			_icon = '<center><img src="../common/img/' + _opt.imgPath[_fIndex] + '" width="15px"></center>';
		$("#"+_grdDieuTri).setRowData(_rId,{icon:_icon});
		$("#"+_grdDieuTri).find("tr[id='" + _rId + "']").find('td').each(function(index, element) {
			$(element).css({'color':_opt.foreColor[_fIndex]});
		});
	}

	function _setButton(value){
		$("#toolbarIdbtnExam").attr("disabled", value);
		$("#toolbarIdbtnTreat").attr("disabled", value);
		$("#toolbarIdbtnService").attr("disabled", value);
		//$("#toolbarIdbtndrug").attr("disabled", value);
		$("#toolbarIdbtnhandling").attr("disabled", value);
		$("#toolbarIdbtnPhieuKham").attr("disabled", value);
		$("#toolbarIdbtnHoaHong").attr("disabled", value);
		//$("#toolbarIdbtnStart").attr("disabled", value);
		$("#toolbarIdbtnKTKH").attr("disabled", value);
		//$("#toolbarIdbtnCall").attr("disabled", value);
		$("#toolbarIdbtnKHAC").attr("disabled", value);
		$("#toolbarIdbtndrug").attr("disabled", value);
		$("#toolbarIdhandling_1").attr("disabled", value);
	}

	function _disableMenuXuTri(xutriid){
		var arrID = ['toolbarIdbtnKHAC_0', 'toolbarIdbtnKHAC_1', 'toolbarIdbtnKHAC_2', 'toolbarIdbtnKHAC_4', 'toolbarIdbtnKHAC_5', 'toolbarIdbtnKHAC_6', 'toolbarIdbtnKHAC_7'];

		for(var i = 0; i < arrID.length; i++){
			$("#"+arrID[i]).addClass("disabled");
		}

		if(xutriid == "8"){
			$('#toolbarIdbtnKHAC_0').removeClass("disabled");
			$('#toolbarIdbtnKHAC_1').removeClass("disabled");
			$('#toolbarIdbtnKHAC_2').removeClass("disabled");
		}else if(xutriid == "1" || xutriid == "3" || xutriid == "9"){
			$('#toolbarIdbtnKHAC_4').removeClass("disabled");
		}else if(xutriid == "4"){// hẹn kham tiep
			$('#toolbarIdbtnKHAC_7').removeClass("disabled");
		}
		else if(xutriid == "7"){
			$('#toolbarIdbtnKHAC_5').removeClass("disabled");
		}else if(xutriid == "5"){ // hẹn kham moi
			$('#toolbarIdbtnKHAC_6').removeClass("disabled");
		}
	}

	// hiển thị dialog xử trí khám bệnh
	function _showDialog(url, param, title, w, h){
		_objData = {};
		dlgPopup=DlgUtil.buildPopupUrl("dlgXuTri","divDlg","manager.jsp?func=../ngoaitru/"+url,param,title,w,h);
		DlgUtil.open("dlgXuTri");
	}

	function _changexutri(value){
		var param = {
			mabenhnhan : $('#txtMABENHNHAN').val(),
			tenbenhnhan : $('#txtTENBENHNHAN').val(),
			nghenghiep : $("#hidTENNGHENGHIEP").val(),
			diachi : $('#txtDIACHI').val(),
			namsinh : $('#hidNAMSINH').val(),
			khambenhid : $('#hidKHAMBENHID').val(),
			benhnhanid : $('#hidBENHNHANID').val(),
			chandoan : $('#txtCDC').val(),
			ngaytiepnhan : $('#txtDENKHAMLUC').val(),
			capnhat : '1',
			hosobenhanid : $('#hidHOSOBENHANID').val()
		};

		if(value === "1" || value === "3" || value === "9"){ // Cấp toa cho về, hẹn, khác
			_showDialog("NGT02K007_Thongtin_Ravien", param, 'Thông tin ra viện',1000,380);
		}else if(value === "4" || value === "5"){ // hẹn khám tiếp, khám mới
			param['xutri'] = value;
			_showDialog("NGT02K008_Thongtin_Lichkham", param, 'Thông tin lịch hẹn',900,300);
		}else if(value === "7"){ // chuyển viện
			_showDialog("NGT02K009_Chuyenvien", param, 'Thông tin chuyển viện',1200,440);
		}else if(value === "8"){ // tử vong
			_showDialog("NGT02K010_Tuvong", param, 'Thông tin tử vong',900,375);
		}
	}

	// nvangoc start L2PT-6142
	function xuatLogChinhSua() {
		var par = [{
			name : 'mahosobenhan',
			type : 'String',
			value : $('#hidMABENHAN').val()
		}];
		var rpName= "LOG_BENH_AN_NGT" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xlsx';
		CommonUtil.inPhieu('window', "NGT_LOG_BAN_GET", 'xlsx', par, rpName);
	}
	// nvangoc end L2PT-6142

	// Xử lý sự kiện liên quan ký CA => START
	function _kyCaRpt(_params) {
		//check ky cap hay khong
		var _rptCode = _params.find(element => element.name == 'RPT_CODE')['value'];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			var loaiky = row.LOAIKY;
		}
		if (loaiky == '1') {
			CommonUtil.kyCA(_params, '', '', '', '', '1');
		} else {
			CommonUtil.kyCA(_params);
		}
		EventUtil.setEvent("eventKyCA", function(e) {
			DlgUtil.showMsg(e.res);
		});
	}
	// Xử lý sự kiện liên quan ký CA => END
}


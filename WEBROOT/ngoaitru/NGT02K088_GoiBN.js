
function NGT02K088_GOIBENHNHAN(opt) {
	this.load = doLoad;

	function doLoad() {
		$("#btnStop").prop('disabled', true);
		_bindEvent();
	}

	function _bindEvent() {
		if (opt.phongid == "0" || opt.phongid == null || opt.phongid == 'null' || opt.phongid == ""){
			DlgUtil.showMsg("Yêu cầu thực hiện thiết lập phòng.", function(){
				window.location.replace("/vnpthis/main/manager.jsp?func=../admin/SelDept");
			});
			return; 
		}
		
		var par = ['NGT_GOIBENHNHAN']; 
		var chedo = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', par.join('$'));
		var _thutugoikham = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_THUTU_GOIKHAM');				// sondn L2PT-5957
		var _thutugoikhamtg = Number(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_THUTU_GOIKHAMTG')); 
		_thutugoikhamtg = _thutugoikhamtg == 0 ? 6000 : _thutugoikhamtg; 
		var intervalCall = null;
		
		var obj = new Object(); 
		obj.PHONGID = opt.phongid + "";
		obj.STRGOIKHAM = "";													// sondn L2PT-5957
		obj.MODE = "1"; 														// sondn L2PT-5957 : CHE DO SELECT DU LIEU; 
		
		$("#btnStart").on(
				"click",
				function(e) {
					$("#btnStart").prop('disabled', true);
					$("#btnStop").prop('disabled', false);
					
					if (_thutugoikham == "0"){
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GOIBN", JSON.stringify(obj) );
						if (data_ar != null && data_ar.length > 0) {
							call.goi_mot_bn_dangky(parseInt(data_ar[0].STT),parseInt(data_ar[0].SOPHONG), chedo);
						}
						intervalCall = setInterval(function() {
							var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GOIBN", JSON.stringify(obj));
							if (data_ar != null && data_ar.length > 0) {
								call.goi_mot_bn_dangky(parseInt(data_ar[0].STT),parseInt(data_ar[0].SOPHONG), chedo);
							}

						}, _thutugoikhamtg);			// mac dinh 6s; 
					}else{																										// sondn L2PT-5957
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GOIBNHD", JSON.stringify(obj) );
						if (data_ar != null && data_ar.length > 0 && data_ar[0].STRGOIKHAM != "") {
							goiKhamGG(data_ar[0].STRGOIKHAM, "0", 5000);										
						}
						intervalCall = setInterval(function() {
							var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GOIBNHD", JSON.stringify(obj));
							if (data_ar != null && data_ar.length > 0 && data_ar[0].STRGOIKHAM != "") {
								goiKhamGG(data_ar[0].STRGOIKHAM, "0", 5000);		
							}
						}, _thutugoikhamtg);				// mac dinh 6s
					}
				});

		$("#btnStop").on("click", function(e) {
			clearInterval(intervalCall);
			$("#btnStart").prop('disabled', false);
			$("#btnStop").prop('disabled', true);
			interval = 0;
		});
	}
}
/*
update - version 64
 */
function benhNhanForm(opt) {
    var _CAUHINH = {};
    var _CAUHINH_NBN;
    var _MODE; // THEM MOI/ CAP NHAT/ XU TRI
    var _TYPE; // NOI TRU/ DIEU TRI NGOAI TRU
    var _KHAMBENHID = ''; // MODE THEM MOI THI = ''
    var _TIEPNHANID = ''; // MODE THEM MOI THI = ''
    var _HOSOBENHANID = ''; // MODE THEM MOI THI = ''
    this.load = doLoad;
    var _objData = null;
    var configBacSi = '0';
    var dateRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4}$/;
    var flag_ban_the = false;
    var config_ndkkcbbd = "";
    var i_u = "";
    var i_p = "";
    //start DoanPV_20210630 xử lý sự kiện ký số/điện tử
    var causer = '';
    var capassword = '';
    var checkKyCa = false;
    var isKyCa = false;
    var printCaRv = false;
    var printCaCv = false;
    var printCaTv = false;
    var printCaHen = false;
    var xutri_cur = null;
    var dkkcbbd_nothe = "";//HaNv_12032019 - L2PT-2293
    var _colICD = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
    var _colDKKCBBD = "Mã BV,BENHVIENKCBBD,10,0,f,l;Bệnh viện,TENBENHVIEN,40,0,f,l;Địa chỉ,DIACHI,50,0,f,l";
    var tainantt = ''; //L2PT-27682
    var _checkkhoa = '0'; //L2PT-75282
    var loadtuyen = 0;
    var showyhct4750 = 0;//L2PT-116259
    function doLoad() {
        _CAUHINH = getDSCauHinh('NHAPBENHNHAN');
        _CAUHINH_NBN = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "HIS_CHECKMALOAIKCB_10;NTU_NBN_CHAN_TRUNG_TN;NTU_NBN_CHECK_GIAYTOCT;NTU_NBN_CK_EDIT_LOAIBA;NTU_NBN_VALIDATE_LANHDAO;NTU_NBN_SHOW_NTDVV;NTU_NBN_SHOWLYDOVV_NGT" +
            ';XUTRI_KYSO_XMLBHYT;NTU_NBN_LOAIBAMD;NTU_NBN_SHOW_KHOACHUYENDEN;NTU_NBN_CANNANG;NTU_NBN_TENICD_YHCT');// L2PT-126791 L2PT-126791 
        $.i18n().load(i18n_his.CtlSql);
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
        $.jMaskGlobals = {
            maskElements: 'input,td,span,div',
            dataMaskAttr: '*[data-mask]',
            dataMask: true,
            watchInterval: 300,
            watchInputs: true,
            watchDataMask: true,
            byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
            translation: {
                '0': {
                    pattern: /\d/
                },
                '9': {
                    pattern: /\d/,
                    optional: true
                },
                '#': {
                    pattern: /\d/,
                    recursive: true
                },
                'A': {
                    pattern: /[a-zA-Z0-9]/
                },
                'S': {
                    pattern: /[a-zA-Z]/
                }
            }
        };
        _initControl();
        _bindEvent();
    }

    $('document').ready(function () {
        //start DoanPV_20210630 xử lý sự kiện ký số/điện tử
        if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU') == "1" && opt._mode == '2') {
            $("#btnKyCa").show();
            $("#btnHuyCa").show();
            //check ky CA
            var _rptCode = '';
            var _par = [{
                name: 'hosobenhanid',
                type: 'String',
                value: $("#hidHOSOBENHANID").val()
            }, {
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            if (xutri_cur == '1' || xutri_cur == '2') {
                _rptCode = 'NTU009_GIAYRAVIEN_01BV01_QD4069_A5';
            } else if (xutri_cur == '6') {
                if (opt.hospital_id == "902") {
                    if (opt.doituongbenhnhanid == "1") {
                        _rptCode = 'NGT003_GIAYCHUYENTUYEN_BHYT_A4_902';
                    } else {
                        _rptCode = 'NGT003_GIAYCHUYENTUYEN_TT14_A4';
                    }
                } else if (opt.hospital_id == "932") {
                    _rptCode = 'NGT003_GIAYCHUYENTUYEN_BHYT_A4_932';
                } else {
                    _rptCode = 'NGT003_GIAYCHUYENTUYEN_TT14_A4';
                }
            } else if (xutri_cur == '7') {
                _rptCode = 'NGT022_GIAYBAOTU_04BV99';
            } else if (xutri_cur == '8') {
                _rptCode = 'NGT014_GIAYHENKHAMLAI_TT402015_A4';
            }
            _par.push({
                name: 'RPT_CODE',
                type: 'String',
                value: _rptCode
            });
            var _check = CommonUtil.checkKyCaByParam(_par);
            if (_check > 0) {
                $("#btnKyCa").prop('disabled', true);
                $("#btnSave").prop('disabled', true);
                $("#btnSaveRv").prop('disabled', true);
                $("#btnSaveCv").prop('disabled', true);
            }
        }
        //end DoanPV_20210630 xử lý sự kiện ký số/điện tử
    });

    function _initControl() {
        $('#txtTENBENHNHAN').focus();
        //L2PT-55802 start
        if (_CAUHINH.NBN_BTN_CHUYENTUYEN == "1") {
            $('#btnChuyenTuyen').hide();
        }
        //L2PT-55802 end
        //L2PT-27845 start
        if (_CAUHINH.NBN_DEFAULT_TKDIAPHUONG != '0' && opt._mode == '0') {
            $('#txtTKDIAPHUONG').val(_CAUHINH.NBN_DEFAULT_TKDIAPHUONG);
        }
        //L2PT-27845 end

        if (_CAUHINH.HIS_NGAY_TIEPNHAN == "0") {
            $('#btnNgayTN').attr("disabled", true);
        } else {
            $('#btnNgayTN').attr("disabled", false);
        }
        // Start QuyHTA L2PT-129066
        if (_CAUHINH.HIS_NGAY_NHAPVIEN == "0") {
            $('#btnNgayNV').attr("disabled", true);
        } else {
            $('#btnNgayNV').attr("disabled", false);
            $('#txtNGAYNHAPVIEN').removeAttr("disabled");
        }
        // End QuyHTA L2PT-129066
        //Begin_HaNv_13032018: LAY THONG TIN CHECK CONG BHYT
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK", ["1"].join('$'));
        if (data_ar != null && data_ar.length > 0) {
            i_u = data_ar[0].I_U;
            i_p = data_ar[0].I_P;
        }
        //End_HaNv_13032018
        if (_CAUHINH.NGT_NHAP_SOVAOVIEN == '1' && opt._mode == '0') {
            $('#txtSOVAOVIEN').prop("disabled", false);
            $('#divsvv').addClass("required");
        }
        //L2PT-58377 start
        if (_CAUHINH.HIS_NBN_SDT == '1' && opt._mode == '0') {
            $('#divSDT').addClass("required");
        }
        //L2PT-58377 end
        $('#divGHICHU_NHAPVIEN').hide();
        //Beg_HaNv_130223: Nhập tay số lưu trữ - L2PT-34568
        if (_CAUHINH.NBN_NHAP_SOLUUTRU == '1' && opt._mode == '1') {
            $('#txtSOLUUTRU').prop("disabled", false);
        }
        //End_HaNv_130223
        if (_CAUHINH.HIS_KHONGCHON_BA == '1') {
            // start jira 9346
            if (_CAUHINH.HIS_KHONGCHON_BA_NOITRU == '0') {
                $('#cboLOAIBENHANID').prop("disabled", true);
            }
            // end jira 9346
        }
        //L2PT-135215
        if (_CAUHINH.NTU_PDT_EDIT_TENBENHPHU_YHCT == '1') {
            $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").prop("disabled", false);
        }
        if (_CAUHINH.HIS_BAC_SI_DIEU_TRI == '1') {
            configBacSi = '1';
        } else {
            $('#divBacSiDt').removeClass('required');
        }

        if (_CAUHINH.HIS_DIEU_DUONG == '1') {
            $('.dieuduong').show();
        }
        config_ndkkcbbd = _CAUHINH.HIS_NOI_KHAM_CHUA_BENH_BAN_DAU;
        dkkcbbd_nothe = _CAUHINH.DEFAULT_DKKCBBD_NOTHE;//HaNv_12032019
        if (_CAUHINH.CHECK_THE_BHYT_TUCONG == '1') {
            $('#chkCHECKCONG').attr('checked', true);
            $('#divCHECKCONG').show();
        } else {
            $('#chkCHECKCONG').attr('checked', false);
            $('#divCHECKCONG').hide();
        }
        // start L2PT-23489
        // bệnh án điều trị ngoại trú mãn tính cho bình thuận
        if (_CAUHINH.NTU_BADTNT_MANTINH == '1' && opt._type == '3') {
            $('#divBADTNTMANTINH').show();
        }
        // end L2PT-23489
        // start jira 27282
        // validate them 1 so thu cho binh thuan khi tiep nhan benh nhan tu kham benh
        if (_CAUHINH.NBN_VALBINHTHUAN == '1') {
            $('#titleLoaiBA').addClass('required');
            $('#divVAOPHONG').addClass('required');
            $('#divBacSiDt').addClass('required');
            $('#cboLOAIBENHANID').attr('valrule', 'Loại bệnh án,required');
            $('#cboKHOAID').attr('valrule', 'Phòng,required');
            $('#cboBACSIID').attr('valrule', 'Bác sĩ,required');
        }
        // end jira 27282
        //dannd_start_L2PT-3962
        if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_SHOW_VSSID') == '1') {
            $("#dvVAOLAN").addClass("col-xs-1 low-padding");
            $('#dvVSSID').show();
            if (opt._mode == '0') {
                $('#cboVSSID').prop("disabled", false);
            }
        }
        //dannd_end
        // start jira L2PT-5143
        if (_CAUHINH.NBN_BENHNHANNGUOINHA == '1') {
            $('#divNGUOINHA').show();
            if (opt._mode == '0') {
                $('#chkISNGUOINHA').attr('checked', false);
                $('#txtTKBENHNHANNGUOINHA').prop("disabled", true);
            }
        }
        //dannd_start_L2PT-42563
        if (_CAUHINH.NBN_SHOW_THONGTINCHAME == '1') {
            $('#divBOME').show();
        }

        if (_CAUHINH.NBN_CHUNGMINHTHU == '1') {
            $('#divCHUNGMINHTHU').show();
        }
        // end jira L2PT-5143
        // start jira L2PT-9926
        if (_CAUHINH.NBN_BENHNHANNANG == '1') {
            $('#divBENHNHANNANG').show();
        }
        // end jira L2PT-9926
        // start jira L2PT-14071
        if (_CAUHINH.NBN_LOAIKHAMHOPDONG == '1') {
            $('#divLOAIKHAMHOPDONG').show();
            ComboUtil.getComboTag("cboHOPDONGID", "DS.HOPDONG1", [{
                "name": "[0]",
                "value": "0"
            }], "", {
                value: 0,
                text: 'Chọn hợp đồng'
            }, "sql", "", "");
        }
        // end jira L2PT-14071
        // start jira L2PT-17288
        if (_CAUHINH.NBN_XT_YHCT == '1') {
            $('#divBENHCHINHYHCT').show();
            $('#divBENHPHUYHCT').show();
        }
        // end jira L2PT-17288
        //L2PT-102966
        if (_CAUHINH_NBN[0].NTU_NBN_CK_EDIT_LOAIBA == '1') {
            var check_ck = jsonrpc.AjaxJson.getOneValue("NTU01H002.CHECK.CK", [{
                "name": "[0]",
                "value": opt._khambenhId
            }]);
            if (Number(check_ck) > 0) {
                $('#cboLOAIBENHANID').prop("disabled", true);
            }
        }
        //L2PT-53368
        if (_CAUHINH.HIS_QD130 == '1') {
            //$('#divDOITUONGKCB').show();
            $("#txtTKNGHENGHIEP").hide();
            $("#cboNGHENGHIEPID").removeAttr("style")
            $("#cboNGHENGHIEPID").attr('style', 'width: 100% !important');
            $("#cboNGHENGHIEPID").select2({
                language: {
                    noResults: function (params) {
                        return "Không có kết quả";
                    }
                }
            });
            $('#lblCHANDOANVAOVIEN').addClass('required');
            $("#txtCHANDOANVAOVIEN").attr("valrule", "Địa chỉ hóa đơn,required|max_length[1000]");
        }
        if (_CAUHINH.HIS_SHOWLANHDAO == '1') {
            $('#divLANHDAO').show();
            ComboUtil.getComboTag("cboLANHDAOKHOAID", "DMC.GETLANHDAOKHOA", [{
                "name": "[1]",
                "value": opt._deptId
            }], "", {
                value: '',
                text: 'Chọn'
            }, "sql", "", "");
            ComboUtil.getComboTag("cboLANHDAOVIENID", "DMC.GETLANHDAOBV", [{
                "name": "[0]",
                "value": "0"
            }], "", {
                value: '',
                text: 'Chọn'
            }, "sql", "", "");
        }
        if (_CAUHINH.NTU_NBN_SHOWLYDOVV == '1' && opt._type != '3') {
            $('#dvLYDOVAONOITRU').show();
        }
        if (_CAUHINH_NBN[0].NTU_NBN_SHOWLYDOVV_NGT == '1' && opt._type == '3') {
            $('#dvLYDOVAONOITRU').show();
        }
        $('#txtBHYT_BD').val('01/01/' + new Date().getFullYear());
        $('#txtBHYT_KT').val('31/12/' + new Date().getFullYear());
        var data = new Object();
        $("#h3BenhAn").html(opt._type == '0' ? 'BỆNH ÁN NỘI TRÚ' : 'BỆNH ÁN NGOẠI TRÚ');
        // 0:mode Add,1:mode Edit,2:mode Xu tri
        if (opt._mode != '0') {
            $('#cboKHOAID').prop("disabled", true);
            $('#chkDT_BANNGAY').prop("disabled", true);
            $("#strSugest").hide();
            $("#btnNext").hide();
            $('#txtMABENHNHAN').prop("disabled", true);
            data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H002.EV001", opt._khambenhId);
            if (data_ar != null && data_ar.length > 0) {
                data = data_ar[0];
                xutri_cur = data.XUTRIID;
                $('#hidKHAMBENHID').val(opt._khambenhId);
                $('#hidHOSOBENHANID').val(opt._hosobenhanId);
                $('#hidBENHNHANID').val(opt._benhnhanId);
                $('#hidTIEPNHANID').val(opt._tiepnhanId);
                tainantt = data.TAINAN_NGUYENNHANID; //L2PT-27682
                if (data.TRANGTHAIKHAMBENH == '1') {
                    $('#cboKHOAID').prop("disabled", false);
                    $('#chkDT_BANNGAY').prop("disabled", false);
                }
                // start jira L2PT-14071
                _loadHopDong(data_ar[0].LOAIKHAM, data_ar[0].HOPDONGID);
                // start jira L2PT-14071
            } else {
                DlgUtil.showMsg('Không tìm thấy bệnh nhân');
                return;
            }
        }
        var sqlLoadLoaiBA = "COM.LOAIBENHAN";
        var paramLoadLoaiBA = [{
            "name": "[0]",
            "value": opt._type == '0' ? '0' : '1'
        }];
        var loaiBAMacDinh = "";
        if (opt._mode == '0' || (opt._mode == '1' && opt._trangthaikhambenh == '1')) {
            if (_CAUHINH.NBN_MALOAIBAMACDINH != '0') { // Không hiển thị header
                loaiBAMacDinh = getBAIDFromMaBA(_CAUHINH.NBN_MALOAIBAMACDINH);
            }
        } else {
            loaiBAMacDinh = setDefaultValueUndefined(data.LOAIBENHANID, "");
        }
        // start jira 9601
        // neu bat cau hinh HIS_QLKHOA_NHIEUBA va khoa da duoc cau hinh loai benh an
        // se chi hien thi cac loai benh an da duoc cau hinh
        var soLoaiBACuaKhoa = jsonrpc.AjaxJson.getOneValue("NTU01H002.SOLOAIBA", [{ // Hiển thị bệnh án tăng dần
            "name": "[0]",
            "value": opt._deptId
        }]);
        if (_CAUHINH.HIS_QLKHOA_NHIEUBA == '1' && soLoaiBACuaKhoa != 0) {
            sqlLoadLoaiBA = "COM.LOAIBA.KHOA";
            paramLoadLoaiBA = [{
                "name": "[0]",
                "value": opt._type == '0' ? '0' : '1'
            }, {
                "name": "[1]",
                "value": opt._deptId
            }];
            if (opt._mode == '0' || (opt._mode == '1' && opt._trangthaikhambenh == '1')) {
                loaiBAMacDinh = jsonrpc.AjaxJson.getOneValue("COM.LOAIBA.KHOA1", [{
                    "name": "[0]",
                    "value": opt._deptId
                }]);
            } else {
                loaiBAMacDinh = setDefaultValueUndefined(data.LOAIBENHANID, "");
            }
        }
        ComboUtil.getComboTag("cboLOAIBENHANID", sqlLoadLoaiBA, paramLoadLoaiBA, loaiBAMacDinh, "", "sql", '', function () {
            // Start L2PT-132353 QuyHTA
            if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_BATCHONLOAIBENHAN') == '1') {
                $('#cboLOAIBENHANID').prepend('<option value="0">-- Chọn loại bệnh án --</option>');
                $('#cboLOAIBENHANID').val('0');
            } else {
                callbackLoadLoaiBA(loaiBAMacDinh);
            }
            // End L2PT-132353 QuyHTA
            //callbackLoadLoaiBA(loaiBAMacDinh);
        });
        // start jira 9601
        if (_CAUHINH.HIS_NBN_CHVLBA == '1') {
            setTimeout(function () {
                // Start L2PT-132353 QuyHTA
                //$('#h3BenhAn').text($("#cboLOAIBENHANID option:selected").text());
                if ($('#cboLOAIBENHANID').val() == '0' && jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_BATCHONLOAIBENHAN') == '1') {
                    $('#h3BenhAn').text('');
                } else {
                    $('#h3BenhAn').text($("#cboLOAIBENHANID option:selected").text());
                }
                // End L2PT-132353 QuyHTA
            }, 1000);
        }
        // end jira 9601
        //L2PT-24558 ttlinh start
        var _nghenghiepdef = '1';
        if (typeof data.NGHENGHIEPID != 'undefined') {
            _nghenghiepdef = data.NGHENGHIEPID;
        }
        var sql_par = [uuid, opt.dbschema, "1.1.1.1", opt.hospitalId, "0"];
        ComboUtil.getComboTag("cboNGHENGHIEPID", "NGTNN.090", sql_par.join('$'), "", {
            extval: true
        }, "sp", "", function () {
            $("#cboNGHENGHIEPID").val(_nghenghiepdef);
            var _vl = $('#cboNGHENGHIEPID' + " option:selected").attr('extval0');
            $("#txtTKNGHENGHIEP").val(_vl);
        });
        //L2PT-24558 end
        ComboUtil.getComboTag("cboDANTOCID", "COM.DANTOC", [], setDefaultValueUndefined(data.DANTOCID, "25"), "", "sql", '', function () {
            $('#txtTKDANTOC').val($('#cboDANTOCID').val());
        });
        ComboUtil.getComboTag("cboQUOCGIAID", "NGTQG.002", [], setDefaultValueUndefined(data.QUOCGIAID, ""), "", "sql", 'quocgia', function () {
            $('#txtTKQUOCGIA').val($('#cboQUOCGIAID').val());
        });
        ComboUtil.getComboTag("cboHC_TINHID", "NGTTI.002", [], "", {
            extval: true,
            value: '',
            text: '-- Chọn --'
        }, "sql", "", function () {
            if (opt._mode != '0') {
                // tu thong tin dia phuong (diaphuongid trong dmc_benhnhan - cboHC_XAID khi luu)
                // (tra ve tinh huyen xa thon) set vao cac combobox tinh huyen xa va cap nhat dia chi
                getDiaChi(data.DIAPHUONGID, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID', data.DIABANID, 'txtTKHC_TINH', 'txtTKHC_HUYEN', 'txtTKHC_XA', 'txtTKDIABAN',
                    "cboDOITUONGBENHNHANID", true, false);
            }
        });
        // START SONDN 010118
        // if (_CAUHINH.HIS_SUDUNG_DOITUONG_KHAIBAO == '1') {
        //     ComboUtil.getComboTag("cboDOITUONGBENHNHANID", "NT.007.01", [], "", {extval: true}, "sql", '', function () {
        //         if (opt._mode == "0") {
        //             $("#cboDOITUONGBENHNHANID").find("option[extval0='6']").attr("selected", "selected");//Begin_HaNv_03122018: mặc định thu phí - L2HOTRO-12590
        //             $('#cboDOITUONGBENHNHANID').change();
        //
        //         } else {
        //             $('#cboDOITUONGBENHNHANID option').removeAttr('selected').filter('[value=val1]').attr('selected', true);
        //             $("#cboDOITUONGBENHNHANID").find("option[extval0='" + data.SUB_DTBNID + "']").prop("selected", true);
        //         }
        //     });
        // } else {
        //     ComboUtil.getComboTag("cboDOITUONGBENHNHANID", "NT.007", [], opt._mode != "0" ? data.DOITUONGBENHNHANID : $('#cboDOITUONGBENHNHANID').val(), "", "sql", '', function () {
        //         if (opt._mode == "1") {
        //             $('#txtTKDOITUONGBENHNHAN').val(data.DOITUONGBENHNHANID);
        //         } else {
        //             $('#txtTKDOITUONGBENHNHAN').val($('#cboDOITUONGBENHNHANID').val());
        //         }
        //     });
        // }
        // END SONDN 010118
        loadDataComboDoiTuongBN(data);
        ComboUtil.getComboTag("cboMA_DOITUONG_KCB", "NGT.GET.DOITUONGKBCB", [], "", {
            value: 0,
            text: '--Chọn--'
        }, "sql", '', ''); //L2PT-53368
        ComboUtil.getComboTag("cboGIOITINHID", "NT.0010", [{
            "name": "[0]",
            "value": "1"
        }], setDefaultValueUndefined(data.GIOITINHID, ""), "", "sql", 'gioitinh', function () {
            $('#txtTKGIOITINH').val($('#cboGIOITINHID').val());
        });
        ComboUtil.getComboTag("cboHINHTHUCVAOVIENID", "NT.0010", [{
            "name": "[0]",
            "value": "4"
        }], setDefaultValueUndefined(data.HINHTHUCVAOVIENID, ""), "", "sql");
        ComboUtil.getComboTag("cboKETQUADIEUTRIID", "NT.0010_1", [{ //L2PT-58677
            "name": "[0]",
            "value": "6"
        }], setDefaultValueUndefined(data.KETQUADIEUTRIID, ""), {
            value: '',
            text: 'Chọn'
        }, "sql", "", function () {
            $('#txtTKKETQUADIEUTRI').val($('#cboKETQUADIEUTRIID').val());
            //L2PT-114286
            if (_CAUHINH.QD_3176 == '1') {
                $("#cboKETQUADIEUTRIID option[value=5]").text("Tử vong tại cơ sở KBCB");
            }
        });
        loadDataComboXuTri(data.XUTRIID);
        ComboUtil.getComboTag("cboMAVUNGID", "NT.0010", [{
            "name": "[0]",
            "value": "76"
        }], setDefaultValueUndefined(data.MAVUNGID, ""), {
            value: '',
            text: ''
        }, "sql");
        var promiseCboPhong = generateComboBoxPhong();
        promiseCboPhong.done(function (str) {
            if (opt._mode == '0') {
                // jira L2PT-8342
                // tu dong chon phong hien tai neu la dieu tri ngoai tru
                if (opt._type == 3 && _CAUHINH.HIS_NBN_DTNT_PHONGHT == '1' && isPhongHienTaiLaDieuTriNgoaiTru()) {
                    $("#cboKHOAID").val(opt._subdept_id);
                } else {
                    $("#cboKHOAID").prop('selectedIndex', 1);
                }
            } else {
                if (!data.KHOAID || data.KHOAID == 0) {
                    // chờ nhập khoa
                    $("#cboKHOAID").prop('selectedIndex', 1);
                } else {
                    $('#cboKHOAID').val(data.KHOAID);
                }
            }
        });
        ComboUtil.getComboTag("cboGIAIPHAUBENHID", "NT.0010", [{
            "name": "[0]",
            "value": "85"
        }], setDefaultValueUndefined(data.GIAIPHAUBENHID, ""), {
            value: '',
            text: 'Chọn'
        }, "sql");
        // start jira 9601. truong hop nhap benh nhan thi chon mac dinh la dung tuyen
        loadTuyen(data.TUYENID);
        // start jira 33632
        if (_CAUHINH.NBN_XOATHONGTUYEN == '1') {
            setTimeout(function () {
                $("#cboTUYENID option[value='5']").remove();
            }, 2000)
        }
        // end jira 33632
        /*
		        if (_CAUHINH.NBN_LAYALLBS == '1') {
		            ComboUtil.getComboTag("cboBACSIID", "NTU01H002.EV014", [{
		                "name": "[0]",
		                "value": opt._deptId
		            }], setDefaultValueUndefined(data.BACSYDIEUTRIID, ""), {
		                value: '',
		                text: 'Chọn'
		            }, "sql");
		        } else {
		            ComboUtil.getComboTag("cboBACSIID", "NGT02K016.EV002", [{
		                "name": "[0]",
		                "value": opt._deptId
		            }], setDefaultValueUndefined(data.BACSYDIEUTRIID, ""), {
		                value: '',
		                text: 'Chọn'
		            }, "sql");
		        }
		*/
        var sqlIdLayBacSi = 'NGT02K016.EV002.2';
        if (_CAUHINH.NBN_LAYALLBS == '1') { // cau hinh lay all bac si
            sqlIdLayBacSi = 'NTU01H002.EV014.2';
        } else if (_CAUHINH.NBN_LAYCCHN_BSYS == '1') { // cau hinh lay bac si y si co CCHN L2PT-21888
            sqlIdLayBacSi = 'NTU01H002.EV014.3';
        }
        var colBacSi = "Mã bác sĩ,USER_NAME,30,0,f,l" + ";Tên bác sĩ,OFFICER_NAME,70,0,f,l" + ";USER_ID,USER_ID,0,0,t,l";
        ComboUtil.initComboGrid("txtTKBACSI", sqlIdLayBacSi, [{
            "name": "[0]",
            "value": opt._deptId
        }], "600px", colBacSi, function (event, ui) {
            $("#cboBACSIID").empty();
            $("#cboBACSIID").append('<option value="' + ui.item.USER_ID + '">' + ui.item.OFFICER_NAME + '</option>');
        });

        //dieu duong
        var colDieuDuong = "Mã điều dưỡng,USER_NAME,30,0,f,l" + ";Tên điều dưỡng,OFFICER_NAME,70,0,f,l" + ";USER_ID,USER_ID,0,0,t,l";
        ComboUtil.initComboGrid("txtTKDIEUDUONG", 'NTU01H002.EV014.02', [{
            "name": "[0]",
            "value": opt._deptId
        }], "600px", colDieuDuong, function (event, ui) {
            $("#cboDIEUDUONGID").empty();
            $("#cboDIEUDUONGID").append('<option value="' + ui.item.USER_ID + '">' + ui.item.OFFICER_NAME + '</option>');
        });

        var colNguoiNhaBN = "Mã bệnh án,MAHOSOBENHAN,30,0,f,l" + ";Tên bệnh nhân,TENBENHNHAN,70,0,f,l" + ";HOSOBENHANID,HOSOBENHANID,0,0,t,l";
        var pa1 = [];
        pa1.push({
            "name": "[0]",
            "value": opt._deptId + ""
        });
        ComboUtil.initComboGrid("txtTKBENHNHANNGUOINHA", "NTU01H002.L33.2", pa1, "600px", colNguoiNhaBN, function (event, ui) {
            $('#txtBENHNHANNGUOINHA').val(ui.item.MAHOSOBENHAN + ' - ' + ui.item.TENBENHNHAN);
            $('#txtBENHNHANNGUOINHAHSBAID').val(ui.item.HOSOBENHANID);
        });
        ComboUtil.initComboGrid("txtTKCHUANDOANGIOITHIEUID", "NT.008", [], "600px", _colICD, function (event, ui) {
            $('#txtCHUANDOANGIOITHIEU').val(
                $("#txtCHUANDOANGIOITHIEU").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtCHUANDOANGIOITHIEU").val() + ";" + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
            //Begin_HaNv_26092018: Chỉnh sửa màn hình tiếp đón vào viện - L2HOTRO-10246
            if (_CAUHINH.HIS_MOTHONGTIN_NHAPKHOA != '0') {
                $('#txtCHANDOANVAOVIEN').val($('#txtCHUANDOANGIOITHIEU').val());
            }
            //End_HaNv_26092018:
        });
        ComboUtil.initComboGrid("txtTKCHANDOANVAOKHOA", "NT.008", [], "600px", _colICD, function (event, ui) {
            $("#cboMACHANDOANVAOKHOA").empty();
            var optionText = '<option value="' + ui.item.ICD10CODE + '">' + ui.item.ICD10NAME + '</option>';
            // start jira L2PT-6953
            if (opt.hospitalId == '30360') {
                optionText = '<option value="' + ui.item.ICD10CODE + '">' + ui.item.ICD10CODE + ' - ' + ui.item.ICD10NAME + '</option>';
            }
            // end jira L2PT-6953
            $("#cboMACHANDOANVAOKHOA").append(optionText);
        });
        var sqlCHANDOANRAVIEN = "NT.008";
        var _colICDCHANDOANRAVIEN = _colICD;
        // start jira L2PT-17288
        if (_CAUHINH.NBN_XT_YHCT == '1') {
            sqlCHANDOANRAVIEN = "NT.008.YHCTV4";
            _colICDCHANDOANRAVIEN = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
        }
        // end jira L2PT-17288
        var sqlCHANDOANRAVIENKEMTHEO = "NT.008";
        var _colICDCHANDOANRAVIENKEMTHEO = _colICD;
        // start jira L2PT-17288
        if (_CAUHINH.NBN_XT_YHCT == '1') {
            sqlCHANDOANRAVIENKEMTHEO = "NT.008.YHCTV4";
            _colICDCHANDOANRAVIENKEMTHEO = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
        }
        // end jira L2PT-17288
        // L2PT-89923 HungND
        var sql_par_k = [];
        sql_par_k.push({"name": "[0]", value: opt._deptId});
        var check_k = jsonrpc.AjaxJson.getOneValue('CHECK.KHOAYHCT', sql_par_k);
        var sql_par_p = [];
        sql_par_p.push({"name": "[0]", value: opt._subdept_id});
        var check_p = jsonrpc.AjaxJson.getOneValue('CHECK.PHONGYHCT', sql_par_p);
        var HIS_SHOW_ICDYHCT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_SHOW_ICDYHCT')
        if (HIS_SHOW_ICDYHCT == '1' && (check_k != '0' || check_p != '0')) {
            $('#divBENHCHINHYHCT').show();
            $('#divBENHPHUYHCT').show();
            //L2PT-116259
            showyhct4750 = 1;
            //$("#btnEDITCHANDOANRAVIENKT").hide();
            sqlCHANDOANRAVIEN = "NT.008.YHCTV4";
            _colICDCHANDOANRAVIEN = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
            ComboUtil.initComboGrid("txtMA_CHANDOAN_RA_CHINH_YHCT", sqlCHANDOANRAVIEN, [], "600px", _colICDCHANDOANRAVIEN, function (event, ui) {
                var check = _checkCBBN(ui.item.ICD10CODE, "1");
                if (check == 1)
                    return false;
                var _ui = ui.item;
                var str = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
                if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
                    DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
                    return false;
                }
                $("#txtTKCHANDOANRAVIENID").val(_ui.ICD10CODE);
                $("#txtMA_CHANDOAN_RA_CHINH_YHCT").val(_ui.YHCTCODE);
                $("#cboCHANDOANRAVIENID").empty();
                var optionText = '<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10NAME + '</option>';
                $("#cboCHANDOANRAVIENID").append(optionText);
                if(_CAUHINH_NBN[0].NTU_NBN_TENICD_YHCT == '0')
					$("#txtTEN_CHANDOAN_RA_CHINH_YHCT").val(_ui.YHCTNAME + ' [' + _ui.ICD10NAME + '] ');
				else 
					$("#txtTEN_CHANDOAN_RA_CHINH_YHCT").val(_ui.YHCTNAME);
                return false;
            });
            sqlCHANDOANRAVIENKEMTHEO = "NT.008.YHCTV4";
            _colICDCHANDOANRAVIENKEMTHEO = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
            ComboUtil.initComboGrid("txtMA_CHANDOAN_RA_KEMTHEO_YHCT", sqlCHANDOANRAVIENKEMTHEO, [], "600px", _colICDCHANDOANRAVIENKEMTHEO, function (event, ui) {
                var check = _checkCBBN(ui.item.ICD10CODE, "0");
                if (check == 1)
                    return false;
                var _ui = ui.item;
                var str = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
                if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
                    DlgUtil.showMsg("Bệnh kèm theo trùng");
                    return false;
                }
                if (($("#cboCHANDOANRAVIENID").val() + "-").indexOf(_ui.ICD10CODE + "-") > -1) {
                    DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
                    return false;
                }
                if (ui.item.ICD10CODE != '') {
                    $('#txtCHANDOANRAVIEN_KEMTHEO').val(
                        $("#txtCHANDOANRAVIEN_KEMTHEO").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtCHANDOANRAVIEN_KEMTHEO").val() + ";" + ui.item.ICD10CODE + "-" +
                            ui.item.ICD10NAME);
                }
                if (ui.item.YHCTCODE != '') {
                    $('#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT').val(
                        $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").val() == '' ? ui.item.YHCTCODE + "-" + ui.item.YHCTNAME : $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").val() + ";" + ui.item.YHCTCODE + "-" +
                            ui.item.YHCTNAME);
                }
            });
        }
        // L2PT-89923 HungND END
        ComboUtil.initComboGrid("txtTKCHANDOANRAVIENID", sqlCHANDOANRAVIEN, [], "600px", _colICDCHANDOANRAVIEN, function (event, ui) {
            //HungND - L2PT-74405 - 240304
            var check = _checkCBBN(ui.item.ICD10CODE, "1");
            if (check == 1)
                return false;
            //HungND - L2PT-74405 - 240304 END
            var _ui = ui.item;
            var str = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
            if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
                DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
                return false;
            }
            $("#txtTKCHANDOANRAVIENID").val(_ui.ICD10CODE);
            $("#cboCHANDOANRAVIENID").empty();
            var optionText = '<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10NAME + '</option>';
            // start jira L2PT-6953
            if (opt.hospitalId == '30360') {
                optionText = '<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10CODE + ' - ' + _ui.ICD10NAME + '</option>';
            }
            // end jira L2PT-6953
            $("#cboCHANDOANRAVIENID").append(optionText);
            // start jira L2PT-17288
            if (_CAUHINH.NBN_XT_YHCT == '1' || (HIS_SHOW_ICDYHCT == '1' && check_k != '0')) {
                if (_ui.YHCTCODE == '') {
                    $("#txtMA_CHANDOAN_RA_CHINH_YHCT").val('');
                    $("#txtTEN_CHANDOAN_RA_CHINH_YHCT").val('');
                } else {
                    $("#txtMA_CHANDOAN_RA_CHINH_YHCT").val(_ui.YHCTCODE);
                    if(_CAUHINH_NBN[0].NTU_NBN_TENICD_YHCT == '0')
						$("#txtTEN_CHANDOAN_RA_CHINH_YHCT").val(_ui.YHCTNAME + ' [' + _ui.ICD10NAME + '] ');
					else 
						$("#txtTEN_CHANDOAN_RA_CHINH_YHCT").val(_ui.YHCTNAME);
                }
            }
            // end jira L2PT-17288
            return false;
        });
        ComboUtil.initComboGrid("txtTKCHANDOANVAOKHOA_KEMTHEO", "NT.008", [], "600px", _colICD, function (event, ui) {
            var _ui = ui.item;
            var str = $("#txtCHANDOANVAOKHOA_KEMTHEO").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtCHANDOANVAOKHOA_KEMTHEO").val() + ";" + ui.item.ICD10CODE + "-" +
                ui.item.ICD10NAME;
            $('#txtCHANDOANVAOKHOA_KEMTHEO').val(str);
        });
        ComboUtil.initComboGrid("txtTKCHANDOANRAVIEN_KEMTHEO", sqlCHANDOANRAVIENKEMTHEO, [], "600px", _colICDCHANDOANRAVIENKEMTHEO, function (event, ui) {
            //HungND - L2PT-74405 - 240304
            var check = _checkCBBN(ui.item.ICD10CODE, "0");
            if (check == 1)
                return false;
            //HungND - L2PT-74405 - 240304 END
            var _ui = ui.item;
            var str = $("#txtCHANDOANRAVIEN_KEMTHEO").val();
            if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
                DlgUtil.showMsg("Bệnh kèm theo trùng");
                return false;
            }
            if (($("#cboCHANDOANRAVIENID").val() + "-").indexOf(_ui.ICD10CODE + "-") > -1) {
                DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
                return false;
            }
            if (ui.item.ICD10CODE != '') {
                $('#txtCHANDOANRAVIEN_KEMTHEO').val(
                    $("#txtCHANDOANRAVIEN_KEMTHEO").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtCHANDOANRAVIEN_KEMTHEO").val() + ";" + ui.item.ICD10CODE + "-" +
                        ui.item.ICD10NAME);
            }
            // start jira L2PT-17288
            if (_CAUHINH.NBN_XT_YHCT == '1' || (HIS_SHOW_ICDYHCT == '1' && check_k != '0')) {
                var str1 = $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").val();
                if (_ui.YHCTCODE != '') {
                    if (str1 != '') {
                        str1 += ";";
                    }
                    if(_CAUHINH_NBN[0].NTU_NBN_TENICD_YHCT == '0')
						$("#txtTEN_CHANDOAN_RA_CHINH_YHCT").val(_ui.YHCTNAME + ' [' + _ui.ICD10NAME + '] ');
					else 
						$("#txtTEN_CHANDOAN_RA_CHINH_YHCT").val(_ui.YHCTNAME);
                }
            }
            // end jira L2PT-17288
        });
        var _colDIAPHUONG = "Viết tắt,TENVIETTATDAYDU,30,0,f,l;Địa phương,NAME,70,0,f,l";
        ComboUtil.initComboGrid("txtTKDIAPHUONG", "DMDP.002", [], "600px", _colDIAPHUONG, function (event, ui) {
            if ($('#txtTKDIAPHUONG').val() != null && $('#txtTKDIAPHUONG').val() != '') {
                $("#txtTKDIAPHUONG").val(ui.item.TENVIETTATDAYDU);
                var option = $('<option value="' + ui.item.VALUE + '">' + ui.item.NAME + '</option>');
                $("#cboDIAPHUONGID").empty();
                $("#cboDIAPHUONGID").append(option);
                getDiaChi(ui.item.VALUE, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID', "", 'txtTKHC_TINH', 'txtTKHC_HUYEN', 'txtTKHC_XA', 'txtTKDIABAN', "cboDOITUONGBENHNHANID",
                    flag_ban_the, true);
                $('#txtDIACHI').val(($('#txtSONHA').val() == "" ? "" : $('#txtSONHA').val() + "-") + ui.item.NAME);
                $('#txtDIACHI_BHYT').val($('#txtDIACHI').val());
                return false;
            }
        });
        //L2PT-27682 start
        if (_CAUHINH.HIS_NBN_TNTT == '1') {
            $('#divNGUYENNHAN_TNTT').show();
            sql_par = [];
            sql_par.push({
                "name": "[0]",
                "value": 49
            });
            ComboUtil.getComboTag("cboTAINAN_NGUYENNHANID", "NTU035.TRANGTHAI", sql_par, '', {
                value: '',
                text: ' Chọn'
            }, "sql", "", function () {
                var _themgiatri = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'NGT_CBO_TNTT_NGUYENHAN');
                if (_themgiatri == 0) {
                    $("#cboTAINAN_NGUYENNHANID option[value='11']").remove();
                    $("#cboTAINAN_NGUYENNHANID option[value='12']").remove();
                    $("#cboTAINAN_NGUYENNHANID option[value='13']").remove();
                }
                if (tainantt != '') {
                    $("#txtTKNGUYENNHAN").val(tainantt);
                    $("#cboTAINAN_NGUYENNHANID").val(tainantt);
                }
                var checkTNTT = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'NGT_CC_BB_TNTT');
                if (checkTNTT == 0) {
                    $("#cboTAINAN_NGUYENNHANID option[value='14']").remove();
                }
            });
        }
        //L2PT-27682 end
        ComboUtil.initComboGrid("txtTKDKKBBD", "NT.009", [], "800px", _colDKKCBBD, function (event, ui) {
            $("#txtTKDKKBBD").val(ui.item.BENHVIENKCBBD);
            var option = '<option value="' + ui.item.BENHVIENKCBBD + '">' + ui.item.TENBENHVIEN + '</option>';
            $("#cboDKKBBDID").empty();
            $("#cboDKKBBDID").append(option);
            // Begin_laphm_12082019: bo sung hien thi gia tri text di kem combogrid - L2PT-7700
            ui.item.value = ui.item.BENHVIENKCBBD;
            // End_laphm_12082019
            // start jira L2PT-21321
            if (_CAUHINH.NTU_NBN_LAN_ATDT == '1') {
                if (ui.item.BENHVIENKCBBD.substr(0, 2) == '80' && $('#cboMAVUNGID').val()) {
                    $('#cboTUYENID').val(1);
                }
            }
            // end jira L2PT-21321
        });
        ComboUtil.initComboGrid("txtTKNOICD", "NT.009", [], "800px", _colDKKCBBD, "txtTKNOICD=BENHVIENKCBBD,cboNOICDID=BENHVIENKCBBD:TENBENHVIEN");
        ComboUtil.getComboTag("cboDOITUONGDB", "DMC.DTDACBIET", [], "", {
            extval: true,
            value: 0,
            text: ''
        }, "sql", "", "");

        if (opt._mode != '0') {
            FormUtil.setObjectToForm("divNhapBenhNhan", "", data);
            $('#cboKHOAID').change();//L2PT-117155
            // L2PT-74675 start dong bo voi form chuyen doi tuong
            //$("#hidCV_SOCHUYENVIEN1").val(data.SOCHUYENTUYEN);
            $("#hidCV_SOCHUYENVIEN1").val(data.CV_SOCHUYENVIEN);
            // L2PT-74675 end
            //L2PT-63088 start
            if (typeof data.MANOIGIOITHIEU1 != 'undefined' && data.MANOIGIOITHIEU1 != null && data.MANOIGIOITHIEU1 != '') {
                $('#txtTKNOICD').val(data.MANOIGIOITHIEU1);
                $('#txtTKNOICD').combogrid("setValue", data.MANOIGIOITHIEU1);
            }
            //L2PT-63088 end
            // start jira L2PT-5143
            if (_CAUHINH.NBN_BENHNHANNGUOINHA == '1' && data.ISNGUOINHA == '1') {
                var obj = getBenhNhanNguoiNha(opt._hosobenhanId);
                $('#txtBENHNHANNGUOINHA').val(obj.BENHNHANNGUOINHA);
                $('#txtBENHNHANNGUOINHAHSBAID').val(obj.BENHNHANNGUOINHAHSBAID);
            }
            // end jira L2PT-5143
            if (typeof data.MACHANDOANRAVIEN != 'undefined' && data.MACHANDOANRAVIEN != null && data.MACHANDOANRAVIEN != '') {
                var maChanDoanRaVien = data.MACHANDOANRAVIEN;
                var chanDoanRaVien = data.CHANDOANRAVIEN;
                $('#txtTKCHANDOANRAVIENID').val(maChanDoanRaVien);
                $('#cboCHANDOANRAVIENID').empty();
                $('#cboCHANDOANRAVIENID').append('<option value="' + maChanDoanRaVien + '">' + chanDoanRaVien + '</option>');
            }
            if (typeof data.MACHANDOANVAOKHOA != 'undefined' && data.MACHANDOANVAOKHOA != null && data.MACHANDOANVAOKHOA != '') {
                var maChanDoanVaoKhoa = data.MACHANDOANVAOKHOA;
                var chanDoanVaoKhoa = data.CHANDOANVAOKHOA;
                // start jira L2PT-6953
                // da khoa quang tri yeu cau hien thi ma ben canh ten chan doan vao khoa va benh chinh
                if (opt.hospitalId == '30360') {
                    if (!chanDoanVaoKhoa.includes(maChanDoanVaoKhoa)) {
                        chanDoanVaoKhoa = maChanDoanVaoKhoa + '-' + chanDoanVaoKhoa;
                    }
                }
                // end jira L2PT-6953
                $('#cboMACHANDOANVAOKHOA').empty();
                $('#cboMACHANDOANVAOKHOA').append('<option value="' + maChanDoanVaoKhoa + '">' + chanDoanVaoKhoa + '</option>');
            }
            if (data.BACSYDIEUTRIID) {
                $('#cboBACSIID').empty();
                $('#cboBACSIID').append('<option value="' + data.BACSYDIEUTRIID + '">' + data.TENBACSI + '</option>');
            }
            if (data.DIEUDUONGID) {
                $('#cboDIEUDUONGID').empty();
                $('#cboDIEUDUONGID').append('<option value="' + data.DIEUDUONGID + '">' + data.TENDIEUDUONG + '</option>');
            }
            if (data.XUTRIID == '3') {
                $('#divTGTVTV').show();
            }
            if (data.XUTRIID == '6') {
                $('#divChuyenVien').show();
            }
            if (data.NOIGIOITHIEU != null && data.NOIGIOITHIEU != '') {
                var radioList = $('input:radio[name=NOIGIOITHIEU]');
                if (radioList.is(':checked') === false) {
                    radioList.filter('[value=' + data.NOIGIOITHIEU + ']').prop('checked', true);
                }
            } else {
                if (opt._mode == '1' && data.TUYENID == 2) { //L2PT-60428
                    $('input:radio[name=NOIGIOITHIEU]').filter('[value=1]').prop('checked', true);
                } else {
                    $('input:radio[name=NOIGIOITHIEU]').filter('[value=2]').prop('checked', true);
                }
            }
            if (data.DOITUONGBENHNHANID != '1') {
                _clearObjectBHYT(true);
            }
            // start jira 9664
            if (_CAUHINH.HIS_NBN_SOCHUYENTUYEN == '1' && data.TUYENID == 2) {
                $('#divSoChuyenTuyen').show();
            }
            // end jira 9664
            if (opt._mode != '1') {
                $('div.required').each(function (index) {
                    $(this).removeClass("required");
                });
                $('div[attrSh]').each(function (index) {
                    $(this).addClass("required");
                });
                $('[modeDisHC]').each(function (index) {
                    $(this).prop("disabled", true);
                });
                $('[modeDisBH]').each(function (index) {
                    $(this).prop("disabled", true);
                    //Beg_HaNv_150922: disable span chọn ngày - L2PT-25307
                    $('#spNGAYDU5NAM').attr("disabled", "disabled");
                    $('#btnSelectNGAYHUONG5NAM6THANG').attr("disabled", "disabled");
                    //End_HaNv_150922
                });
                $('[modeDisXT]').each(function (index) {
                    $(this).prop("disabled", false);
                });
            } else {
                if (typeof opt._submode != 'undefined') {
                    // cap nhat thong tin hanh chinh
                    if (opt._submode == '0') {
                        $('[modeDisHC]').each(function (index) {
                            $(this).prop("disabled", false);
                        });
                        $('[modeDisBH]').each(function (index) {
                            $(this).prop("disabled", true);
                            //Beg_HaNv_150922: disable span chọn ngày - L2PT-25307
                            $('#spNGAYDU5NAM').attr("disabled", "disabled");
                            $('#btnSelectNGAYHUONG5NAM6THANG').attr("disabled", "disabled");
                            //End_HaNv_150922
                        });
                    }
                    // cap nhat thong tin bao hiem
                    if (opt._submode == '1') {
                        $('[modeDisHC]').each(function (index) {
                            $(this).prop("disabled", true);
                        });
                        $('[modeDisBH]').each(function (index) {
                            $(this).prop("disabled", false);
                        });
                        if (data.DOITUONGBENHNHANID != '1') {
                            _clearObjectBHYT(true);
                        }
                    }
                    // cap nhat khoa phong
                    if (opt._submode == '2') {
                        $('[modeDisHC]').each(function (index) {
                            $(this).prop("disabled", true);
                            if (_CAUHINH.NBN_EDIT_VAOLANTHU == '1') {
                                $('#txtVAOLAN').prop("disabled", false);
                            }
                        });
                        $('[modeDisBH]').each(function (index) {
                            $(this).prop("disabled", true);
                            //Beg_HaNv_150922: disable span chọn ngày - L2PT-25307
                            $('#spNGAYDU5NAM').attr("disabled", "disabled");
                            $('#btnSelectNGAYHUONG5NAM6THANG').attr("disabled", "disabled");
                            //End_HaNv_150922
                        });
                        $('[modeDisXP]').each(function (index) {
                            $(this).prop("disabled", false);
                        });
                        if (data.DOITUONGBENHNHANID != '1') {
                            _clearObjectBHYT(true);
                        }
                    }
                    $('[modeDisXT]').each(function (index) {
                        $(this).prop("disabled", true);
                    });
                } else {
                    $('[modeDisHC]').each(function (index) {
                        $(this).prop("disabled", true);
                    });
                    $('[modeDisBH]').each(function (index) {
                        $(this).prop("disabled", true);
                        //Beg_HaNv_150922: disable span chọn ngày - L2PT-25307
                        $('#spNGAYDU5NAM').attr("disabled", "disabled");
                        $('#btnSelectNGAYHUONG5NAM6THANG').attr("disabled", "disabled");
                        //End_HaNv_150922
                    });
                    $('[modeDisXT]').each(function (index) {
                        $(this).prop("disabled", true);
                    });
                    $('#btnSave').hide();
                }
            }
        } else {
            $('[modeDisXT]').each(function (index) {
                $(this).prop("disabled", true);
            });
            if (_CAUHINH.HIS_MOTHONGTIN_NHAPKHOA != '0') {
                $('#txtCHANDOANVAOVIEN').prop('disabled', false);
                $('#txtCHANDOANVAOVIEN_KEMTHEO').prop('disabled', false);
                $('#txtTKCHANDOANVAOKHOA').prop('disabled', false);
                $('#cboCHANDOANVAOKHOA').prop('disabled', false);
                if (_CAUHINH.NBN_EDIT_CHANDOANVAOKHOA != '1') { //L2PT-19260 ttlinh
                    $('#txtCHANDOANVAOKHOA_KEMTHEO').prop('disabled', false);
                }
                $('#txtTKCHANDOANVAOKHOA_KEMTHEO').prop('disabled', false);
                $('#txtCHANDOANVAOVIEN').prop('disabled', false);
                $('#txtTKCHANDOANRAVIENID').prop('disabled', false);
                $('#txtTKCHANDOANRAVIEN_KEMTHEO').prop('disabled', false);
            }
            $("#txtVAOKHOA").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
            $('input:radio[name=NOIGIOITHIEU]').filter('[value=2]').prop('checked', true);
        }
        //tuyennx_add_start_20170724 check an hien nut sua ghi chu benh chinh
        var NGT_GHICHU_BENHCHINH = _CAUHINH.NGT_GHICHU_BENHCHINH;
        if (NGT_GHICHU_BENHCHINH == '1') {
            $("#divBc").addClass("col-xs-8");
            $('#divSuaBc').css('display', '');
        }
        //tuyennx_add_end_20170724
        //L2PT-75282
        if (_CAUHINH.HIS_KHOA_SOLIEU == '1' && opt._mode == '2' && data.XUTRIID && data.XUTRIID != '5') {
            // bo ve, dua ve, khac, tien luong tu vong thi lay txtTHOIGIANTVTV
            // con lai lay txtTHOIGIANKETTHUC
            var timeXuTri = $('#txtTHOIGIANKETTHUC').val();
            var xuTriId = $('#cboXUTRIID').val();
            if (['3', '4', '9', '11'].includes(xuTriId)) {
                timeXuTri = $('#txtTHOIGIANTVTV').val();
            }
            var sql_par = [$("#hidTIEPNHANID").val(), timeXuTri];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DMC32.KSL.05", sql_par.join('$'));
            var rets = ret.split(';');
            if (rets[0] > 0) {
                $("#txtTHOIGIANKETTHUC").prop('disabled', true);
                $("#spTHOIGIANKETTHUC").attr('disabled', true);
                _checkkhoa = '1';
            }
        }

        if (_CAUHINH.HIS_QD130 == '1') {
            $('#lblCHANDOANVAOVIEN').addClass('required');
            $("#txtCHANDOANVAOVIEN").attr("valrule", "Chẩn đoán vào viện,trim_required|max_length[500]");
            $('#txtCHANDOANVAOVIEN').prop('disabled', false);
            $('#txtLY_DO_VNT').prop('disabled', false);
            if (opt._mode == '2' && $('#cboXUTRIID').val() != '5') {
                $('#lblLANHDAOKHOA').addClass('required');
                $("#cboLANHDAOKHOAID").attr("valrule", "Lãnh đạo khoa,required");
                $('#lblLANHDAOVIEN').addClass('required');
                $("#cboLANHDAOVIENID").attr("valrule", "Lãnh đạo viện,required");
            }
            $('#lblSINHTHETE').html('Thẻ tạm');//L2PT-114300
        }

        //Begin_HaNv_26072018: Nhap benh nhan tu khu tiep don truoc khi vao khoa - L2DKBD-1312
        $(document).ready(function () {
            if (opt._modeTiepDon == '1') {
                $("#divVAOPHONG").removeClass("required");
                $("#cboKHOAID").empty();
                $('#cboKHOAID').prop('disabled', true);
                if (configBacSi == '0') {
                    configBacSi = '1';
                    $('#divBacSiDt').addClass('required');
                }
            }
        });
        //End_HaNv_26072018
        var f3 = 114;
        var f4 = 115;
        var f6 = 117;
        $(document).unbind('keydown').keydown(function (e) {
            if (e.keyCode == f3) {
                var focused = $(':focus');
                focused.blur();
                e.preventDefault();
                if (opt._mode == '0') {
                    _searchPatient();
                }
            }
            if (e.keyCode == f6) {
                getIcd(e.target);
            }
            if (e.keyCode == f4) {
                $('#btnSave').click();
            }
        });
        // Begin_laphm_08082019: Cho phep chinh sua chan doan vao khoa - L2PT-7658
        if (_CAUHINH.HIS_NBN_ENABLE_CDKT == '1') {
            // cap nhat thong tin cho benh nhan cho nhap vien
            if (opt._mode == '1' && opt._trangthaikhambenh == '1') {
                $('#cboMACHANDOANVAOKHOA').prop("disabled", false);
                $('#txtTKCHANDOANVAOKHOA').prop("disabled", false);
                if (_CAUHINH.NBN_EDIT_CHANDOANVAOKHOA != '1') { //L2PT-19260 ttlinh
                    $('#txtCHANDOANVAOKHOA_KEMTHEO').prop('disabled', false);
                }
                $('#txtTKCHANDOANVAOKHOA_KEMTHEO').prop("disabled", false);
            }
        }
        // End_laphm_08082019
        // start jira L2PT-22229
        // nếu là xử trí lần đầu
        // 1. Bệnh chính lấy bệnh chính trong Bệnh án chung của khoa hiện tại
        // 2. Bệnh kèm theo là tổng hợp mã bệnh chính và bệnh kèm theo trong Bệnh án chung ở các khoa điều trị trước đó,
        // trừ mã bệnh trong phần bệnh chính ở trên
        if (_CAUHINH.NTU_NBN_XT_THMB == '1') {
            if (opt._mode == '2') {
                // chưa có kết quả điều trị nào thì là xử trí lần đầu
                if (chuaCoKetQuaDieuTriNao()) {
                    var obj = getTHMaBenhTuCacKhoa();
                    if (obj.MABENHCHINH != '' && obj.TENBENHCHINH != '') {
                        $('#txtTKCHANDOANRAVIENID').val(obj.MABENHCHINH);
                        $('#cboCHANDOANRAVIENID').empty();
                        $('#cboCHANDOANRAVIENID').append('<option value="' + obj.MABENHCHINH + '">' + obj.TENBENHCHINH + '</option>');
                    }
                    $('#txtCHANDOANRAVIEN_KEMTHEO').val(obj.LISTBENHPHU);
                    $('#txtGHICHU_BENHCHINH_RAVIEN').val(obj.GHICHUBENHCHINH);
                }
            }
        }
        // end jira L2PT-22229
        // start jira L2PT-19626
        // nếu là xử trí lần đầu
        // 1. Bệnh chính và bệnh kèm theo lấy từ phiếu điều trị
        if (_CAUHINH.NTU_NBN_XT_CDTPDT == '1' && opt._mode == '2') {
            // chưa có kết quả điều trị nào thì là xử trí lần đầu
            if (chuaCoKetQuaDieuTriNao()) {
                var obj = getChanDoanTuPhieuDieuTri();
                if (obj.MABENHCHINH != '' && obj.TENBENHCHINH != '') {
                    $('#txtTKCHANDOANRAVIENID').val(obj.MABENHCHINH);
                    $('#cboCHANDOANRAVIENID').empty();
                    $('#cboCHANDOANRAVIENID').append('<option value="' + obj.MABENHCHINH + '">' + obj.TENBENHCHINH + '</option>');
                }
                $('#txtCHANDOANRAVIEN_KEMTHEO').val(obj.LISTBENHPHU);
            }
        }
        // end jira L2PT-22229
        // start L2PT-23489
        // chỉ cho sửa combobox badtntmantinh khi nhập bệnh nhân hoặc tiếp nhận
        if (_CAUHINH.NTU_BADTNT_MANTINH == '1' && opt._type == '3') {
            if (opt._mode == '0' || (opt._mode == '1' && opt._trangthaikhambenh == '1')) {
                $('#cboBADTNTMANTINH').prop("disabled", false);
            } else {
                $('#cboBADTNTMANTINH').prop("disabled", true);
            }
            // bệnh án đtnt mãn tính thì khi xử trí, fix số ngày điều trị = 1 và xử trí = 9 (kết thúc đợt đtnt)
            if (opt._mode == '2' && $('#cboBADTNTMANTINH').val() == '1') {
                setTimeout(function () {
                    $('#txtSONGAYDIEUTRI').val("1");
                    $('#txtSONGAYDIEUTRI').prop("disabled", true);
                    $('#cboXUTRIID').val(9);
                    $('#txtTKXUTRI').val(9);
                }, 2000)
            }
        }
        // end L2PT-23489
        $('#txtVAOKHOA').mask('00/00/0000 00:00:00');
        // start jira L2PT-6953
        // da khoa quang tri cho phep nhap tay ngay vao khoa va khong nhap giay
        if (opt.hospitalId == '30360' && (opt._mode == '0' || opt._mode == '1')) {
            $('#txtVAOKHOA').prop("disabled", false);
            //$('#txtVAOKHOA').mask('00/00/0000');
        }
        // end jira L2PT-6953
        // START JIRA L2PT-10866
        if (_CAUHINH.NBN_DISABLE_SNDT == '1') {
            $('#txtSONGAYDIEUTRI').prop("disabled", true);
        }
        // END JIRA L2PT-10866
        // L2PT-19260 ttlinh start
        if (_CAUHINH.NBN_EDIT_CHANDOANVAOKHOA == '1') {
            $('#divEditCDVaoKhoa').show();
            $('#divEDITCHANDOANVAOKHOAKT').show();
        }
        // L2PT-19260 end
        //Beg_HaNv_200722: Thêm thông tin nhận thuốc theo hẹn, đáp ứng TT36 - L2PT-23050
        if (_CAUHINH.NTU_NHANTHUOCTHEOHEN == '1' && (opt._mode == '0' || opt._mode == '1')) {
            $('#divNhanThuoc').show();
            ComboUtil.initComboGrid("txtDV_CAPGIAY", "NT.009", [], "800px", _colDKKCBBD, function (event, ui) {
                $("#txtDV_CAPGIAY").val(ui.item.BENHVIENKCBBD);
                var option = '<option value="' + ui.item.BENHVIENKCBBD + '">' + ui.item.TENBENHVIEN + '</option>';
                $("#cboTEN_DV_CAPGIAY").empty();
                $("#cboTEN_DV_CAPGIAY").append(option);
                return false;
            });
            if (data.DV_CAPGIAY) {
                $('#txtDV_CAPGIAY').combogrid("setValue", data.DV_CAPGIAY);
            }
        } else {
            $('#divNhanThuoc').hide();
        }
        //End_HaNv_200722
        //Beg_HaNv_140323: Check giờ sinh nếu BN chưa đủ 28 ngày (QD130) - L2PT-35188
        if (_CAUHINH.HIS_QD130 == '1') {
            $("#divNgaySinh").removeClass("col-xs-8");
            $("#divNgaySinh").addClass("col-xs-5");
            $("#divGioSinh").css('display', '');
        } else {
            $("#divGioSinh").remove();
        }
        //End_HaNv_140323
        //Beg_HaNv_220523: Ẩn chức năng sinh thẻ TE - L2PT-42890
        if (_CAUHINH.NBN_HIDE_SINHTHETE == '1') {
            $("#divSINHTHETE").hide();
        }
        //End_HaNv_220523
        if (_CAUHINH_NBN[0].NTU_NBN_SHOW_NTDVV == '1') {
            $("#divNGAYTDVV").show();
        }
        if (_CAUHINH_NBN[0].NTU_NBN_SHOW_KHOACHUYENDEN == '1') {
            $("#dvKHOACHUYENDEN").show();
        }
        // L2PT-122253 start
        // L2PT-126791 start
        if (opt._mode == '0' || opt._mode == '2') {
            $("#divCanNang").show();
            if (_CAUHINH_NBN[0].NTU_NBN_CANNANG == '1' && opt._type == 3) {
                $("#divCanNang").addClass("required");
                $('#txtKHAMBENH_CANNANG').attr('valrule', 'Cân nặng,required|max_length[6]|greater_than[0]');
            }
        }
        // L2PT-126791 end
        // L2PT-122253 end
    }

    function saveData() {
        var msgCheck = checkTheBhyt();
        if (msgCheck != '') {
            DlgUtil.showMsg(msgCheck);
            return;
        }
        //Beg_HaNv_140323: Check giờ sinh nếu BN chưa đủ 28 ngày (QD130) - L2PT-35188
        if (opt._mode == '0' && $('#cboDVTUOI').val() == "3") {
            var songaybatbuoc = _CAUHINH.HIS_BATBUOCGIOSINH_SONGAY;
            if (_CAUHINH.HIS_QD130 == '1' && parseInt($('#txtTUOI').val()) <= songaybatbuoc && $('#txtGIO_SINH').val().trim() == '') {
                DlgUtil.showMsg('Bệnh nhân nhỏ hơn ' + songaybatbuoc + ' ngày bắt buộc nhập giờ sinh ');
                $('#txtGIO_SINH').focus();
                return;
            }
        }

        //check mã loại kcb = 10
        if (opt._mode == '2' && $("#cboDOITUONGBENHNHANID").val() == '1' && _CAUHINH_NBN.HIS_CHECKMALOAIKCB_10 != '0' && $('#cboXUTRIID').val() != '5') {
            var maloaikcb = jsonrpc.AjaxJson.ajaxCALL_SP_S("HIS.GETMALOAIKCB", $("#hidTIEPNHANID").val());
            if (maloaikcb == '10') {
                if (cfObj.HIS_CHECKMALOAIKCB_10 == '1') {
                    if (!confirm("Mã loại khám chữa bệnh của bệnh nhân là 10 chưa được cổng BHXH chấp nhận khi đẩy cổng, có muốn tiếp tục?")) {
                        return -1;
                    }
                } else {
                    DlgUtil.showMsg("Mã loại khám chữa bệnh của bệnh nhân là 10 chưa được cổng BHXH chấp nhận khi đẩy cổng!");
                    return -1;
                }
            }
        }
        //End_HaNv_140323
        if (_CAUHINH.HIS_CHECK_THE_BHYT == '1' && $('#cboDOITUONGBENHNHANID').val() == "1" && $('#cboXUTRIID').val() == '5') {
            msgCheck = "";
            var resultThe = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H001.EV021", $("#hidTIEPNHANID").val());
            if (resultThe != null && resultThe.length > 0 && resultThe[0].SINHTHEBHYT != '1') {
                var mathe = resultThe[0].MA_BHYT;
                var tenbn = resultThe[0].TENBENHNHAN;
                var namsinh = resultThe[0].NGAYSINH;
                var gioitinhid = resultThe[0].GIOITINHID;
                var noidk = resultThe[0].MA_KCBBD;
                var ngaybd = resultThe[0].BHYT_BD;
                var ngaykt = resultThe[0].BHYT_KT;
                var ret1 = _checkCongBHYT(i_u, i_p, mathe.trim(), tenbn, namsinh.trim(), gioitinhid, noidk, ngaybd, ngaykt, "0");
                msgCheck = ret1.noiDungKetQua;
            }
            if (msgCheck != '') {
                msgCheck = msgCheck + " Vui lòng kiểm tra và cập nhật lại thẻ để đảm bảo dữ liệu đẩy BH được chính xác";
            }
        }
        // L2PT-12015 duonghn start: kiểm tra địa chỉ thẻ BHYT
        var dia_chi_bhyt_check = $("#txtDIACHI_BHYT").val();
        var check_control_character = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU.CHECK.CTR.CHR", dia_chi_bhyt_check);
        if (check_control_character != '1') {
            DlgUtil.showMsg('Địa chỉ BHYT ' + check_control_character);
            return;
        }
        // L2PT-12015 duonghn end
        // sau khi nhập trực tiếp hoặc gọi hàm _checkCongBHYT sẽ update lại ten, dia chi
        // validate ky tu dac biet
        if (opt._mode == '0') {
            if (!checkErrorChar($('#txtTENBENHNHAN').val())) {
                DlgUtil.showMsg("Tên bệnh nhân chứa ký tự đặc biệt. Vui lòng chỉnh sửa", function () {
                    $('#txtTENBENHNHAN').focus();
                });
                return;
            }
            if (!checkErrorChar($('#txtDIACHI').val())) {
                DlgUtil.showMsg("Địa chỉ chứa ký tự đặc biệt. Vui lòng chỉnh sửa", function () {
                    $('#txtDIACHI').focus();
                });
                return;
            }
            if (!checkErrorChar($('#txtDIACHI_BHYT').val())) {
                DlgUtil.showMsg("Địa chỉ BHYT chứa ký tự đặc biệt. Vui lòng chỉnh sửa", function () {
                    $('#txtDIACHI_BHYT').focus();
                });
                return;
            }
        }
        // Begin_laphm_16092019: mo ba lan 2 trong ngay - L2PT-7879
        if (opt._mode == '0' && $('#cboDOITUONGBENHNHANID').val() == '1') {
            var cauHinhMoBALan2 = _CAUHINH.HIS_NTN_MOBALAN2;
            if (cauHinhMoBALan2 != '0') {
                //var hsbaid = daKhamTrongNgayTheoBHYT();
                var hsbaid = daChiKhamNgoaiTruTrongNgayTheoBHYT();
                if (hsbaid != '-1' && hsbaid != '0') {
                    if (cauHinhMoBALan2 == '1') {
                        moBALan2(hsbaid);
                        return;
                    } else if (cauHinhMoBALan2 == '2') {
                        var r = confirm("Bệnh nhân đã khám trong ngày, Bạn có muốn mở lại bệnh án cũ không?");
                        if (r == true) {
                            moBALan2(hsbaid);
                            return;
                        }
                    }
                }
            }
        }
        // End_laphm_16092019
        // start jira 19889
        // khi xu tri, kiem tra xem con phieu nao thieu chi dinh icd khong
        var cauHinhCheckICDPhieuChiDinh = _CAUHINH.HIS_NBN_THIEUICD_PCD;
        if (cauHinhCheckICDPhieuChiDinh != '0') {
            if (opt._mode == '2') {
                var phieuChuaChiDinh = getPhieuChuaChiDinhICD();
                if (phieuChuaChiDinh != "") {
                    DlgUtil.showMsg('Còn phiếu chỉ định thiếu ICD: ' + phieuChuaChiDinh);
                    if (cauHinhCheckICDPhieuChiDinh == '2') {
                        return;
                    }
                }
            }
        }
        // end jira 19889
        // start jira 21222
        // (chức năng duyệt mổ - khám mê)
        // nếu bật cấu hình HIS_QUYTRINH_DUYETMO_LCI thì khi xử trí khác chuyển khoa sẽ tự động kết thúc ở khoa gây mê trước đó
        // (nếu còn phiếu ở khoa gây mê thì sẽ cảnh báo)
        if (_CAUHINH.HIS_QUYTRINH_DUYETMO_LCI == '1') {
            if (opt._mode == '2' && $('#cboXUTRIID').val() != '5') {
                if (checkPhieuChuaXuLyXong() == -1) {
                    return;
                } else {
                    // update trang thai kham benh o khoa kham me
                    if (ketThucKhoaGMHS() == -1) {
                        return;
                    }
                }
            }
        }
        // end jira 21222
        // start jira 26484
        // khi xử trí mà có khám chuyên khoa chưa kết thúc (KHÔNG PHẢI NỘI VIỆN) mà các bản ghi bên maubenhpham
        // đã hoàn thành hết rồi thì tự động kết thúc cái khám chuyên khoa đó luôn
        if (_CAUHINH.KCK_TDKTKHTMBP == '1') {
            tuDongKetThucKCKNeuHoanThanhPhieu();
        }
        // end jira 26484
        // start jira 31700
        // canh bao dich vu cls con khi chuyen khoa cho bn noi tru
        if (_CAUHINH.NBN_WARNDVCKCK != '0' && opt._mode == '2' && $('#cboXUTRIID').val() == '5' && opt._type == '0') {
            var phieuCLSChuaKT = checkPhieuChuaXuLyXong2();
            if (phieuCLSChuaKT != '-1') {
                if (_CAUHINH.NBN_WARNDVCKCK == '2') {
                    DlgUtil.showMsg(phieuCLSChuaKT);
                    return;
                }
                var r = confirm(phieuCLSChuaKT + ". Bạn có muốn tiếp tục?");
                if (r == false) {
                    return;
                }
            }
        }
        // end jira 31700
        // start jira 496
        // với trường hợp chờ nhập khoa của quy trình duyệt mổ
        // mà còn phiếu PTTT ở phòng mổ mà chưa hoàn thành
        // thì không cho tiếp nhận
        if (_CAUHINH.NBN_QTDM_CHECKPTTTCHT == '1' && opt._mode == '1' && opt._trangthaikhambenh == 1) {
            var phieuPTTT = conPhieuPTTTChuaHTQTDM($('#hidKHAMBENHID').val());
            if (phieuPTTT != '0') {
                DlgUtil.showMsg('Bệnh nhân có phiếu thực hiện phẫu thuật thủ thuật chưa hoàn thành: ' + phieuPTTT);
                return;
            }
        }
        // end jira 496
        // start jira BVTM-935
        // canh bao bn ra vien co thoi gian nam vien duoi 8 tieng
        if (_CAUHINH.NBN_CANHBAONVDUOI8TIENG == '1' && opt._mode == '2' && $('#cboXUTRIID').val() == '1') {
            var tgVao = $('#txtVAOKHOA').val();
            var tgRa = $('#txtTHOIGIANKETTHUC').val();
            var diff_h = diffDate(tgRa, tgVao, 'DD/MM/YYYY HH:mm:ss', 'hours');
            if (diff_h < 8) {
                DlgUtil.showMsg('Bệnh nhân có thời gian nằm viện dưới 8 tiếng');
            }
        }
        // end jira BVTM-935
        // L2PT-21308 thoi gian xu tri nam trong khoang cau hinh SO_NGAY_XUTRI_DIEUTRI thiet lap
        if (_CAUHINH.SO_NGAY_XUTRI_DIEUTRI != '0' && opt._mode == '2' && $('#cboXUTRIID').val() != '5') {
            //HaNv_201223: Chuyển cấu hình số ngày thành số giờ - L2PT-57552
            var songaydt = parseInt(_CAUHINH.SO_NGAY_XUTRI_DIEUTRI);
            var ngayHientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MM:SS');
            var ngayRa = $('#txtTHOIGIANKETTHUC').val();
            var t_truoc = diffDate(ngayHientai, ngayRa, 'DD/MM/YYYY HH:mm', 'minutes') / 60;
            var t_sau = diffDate(ngayRa, ngayHientai, 'DD/MM/YYYY HH:mm', 'minutes') / 60;
            if (t_truoc > songaydt || t_sau > songaydt) {
                return DlgUtil.showMsg("Thời gian ra viện của bệnh nhân không nằm trong khoảng với cấu hình SO_NGAY_XUTRI_DIEUTRI đã thiết lập");
            }
        }
        // end jira L2PT-21308
        // start jira 2520
        if (_CAUHINH.NBN_CHECKTYLETHEVATAMUNG == '1' && opt._mode == '1' && $('#cboDOITUONGBENHNHANID').val() == '1' && !isDieuTriNgoaiTruKhoaKhamBenh()) {
            var mucHuong = $('#txtMUCHUONG').val();
            if (mucHuong != '') {
                var n = mucHuong.indexOf("%");
                var mucHuong2 = mucHuong.substr(0, n);
                if (mucHuong2 != '100') {
                    if (isChuaCoTienTamUng($('#hidTIEPNHANID').val())) {
                        DlgUtil.showMsg('Bệnh nhân chưa có tiền tạm ứng');
                        return;
                    }
                }
            }
        }
        //Beg_HaNv_221222: check tạm ứng (cảnh báo, chặn) theo loại tiếp nhận, đối tượng BN - L2PT-30826
        if (opt._mode == '1' && _CAUHINH.NBN_CBTAMUNG != '0') {
            var arr = _CAUHINH.NBN_CBTAMUNG.split(';');
            var tpcb = arr[0];//1: cảnh báo, 2: cảnh báo và chặn
            var loaitnid = arr[1];
            var dtbnid = arr[2];
            if (dtbnid.indexOf($('#cboDOITUONGBENHNHANID').val()) > -1 && loaitnid.indexOf(opt._type) > -1) {
                if (isChuaCoTienTamUng($('#hidTIEPNHANID').val())) {
                    DlgUtil.showMsg('Bệnh nhân chưa có tiền tạm ứng');
                    if (tpcb == '2') {
                        return;
                    }
                }
            }
        }
        //End_HaNv_221222
        // end jira 2520
        // start jira L2PT-3306
        // chan khi thoi gian thuc hien PTTT sau thoi gian xu tri
        if (_CAUHINH.NBN_CKTHPTTTSTGXT == '1' && opt._mode == '2') {
            var phieu = getPhieuPTTTTHSauTimeRv();
            if (phieu != '') {
                DlgUtil.showMsg('Phiếu PTTT: ' + phieu + ' có thời gian thực hiện sau thời gian xử trí');
                return;
            }
        }
        // end jira L2PT-3306
        //L2PT-60547 start
        if (_CAUHINH.HIS_CHECK_TOMTATBA == '1' && opt._mode == '2' && $('#cboXUTRIID').val() != '5') {
            var _sql_par = RSUtil.buildParam("", [$("#hidHOSOBENHANID").val()]);
            var _check = jsonrpc.AjaxJson.getOneValue("NTU01H001.EV041", _sql_par);
            if (_check == 0) {
                DlgUtil.showMsg('Chưa nhập thông tin tóm tắt bệnh án, không thể tiếp tục xử trí.');
                return;
            }
        }
        if (_CAUHINH.HIS_CHECK_TTDV == '1' && opt._mode == '2') { // check ton tai ma y te, BHXH cua thu truong don vi trong he thong tai khoan cong
            var _sql_par = RSUtil.buildParam("", [$("#hidHOSOBENHANID").val()]);
            var _check = jsonrpc.AjaxJson.getOneValue("NTU01H001.TTDV", _sql_par);
            if (_check == 0) {
                DlgUtil.showMsg('Chưa nhập thông tin mã TTDV trong danh mục tài khoản cổng, không thể tiếp tục xử trí.');
                return;
            }
        }
        //L2PT-60547 end
        //DoanPV_20220712 L2PT-22463 START
        //check thời gian trả kết quả CLS,PTTT vs ngay_ravien
        if (opt._mode == '2' && _CAUHINH.XUTRI_CHECK_NGAYTRAKETQUA_CLS == '1') {
            var obj = new Object();
            obj.HOSOBENHANID = $("#hidHOSOBENHANID").val();
            obj.TIEPNHANID = $("#hidTIEPNHANID").val();
            obj.KHAMBENHID = $("#hidKHAMBENHID").val();
            var result_tg = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV032", JSON.stringify(obj));
            if (result_tg != '1') {
                DlgUtil.showMsg('Tồn tại ' + result_tg + ' có thời gian trả kết quả trước ngày nhập viện hoặc sau ngày ra viện. Kiểm tra lại dữ liệu');
                return;
            }
        }
        //check thời gian trả kết quả CLS,PTTT vs ngaymaubenhpham
        if (opt._mode == '2' && _CAUHINH.XUTRI_CHECK_NGAYYLENH_NGAYKQ == '1') {
            var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV066", $("#hidHOSOBENHANID").val());
            if (result_ct != '1') {
                DlgUtil.showMsg('Còn phiếu XN/CDHA/PTTT có ngày KQ nhỏ hơn ngày YL: ' + result_ct);
                return;
            }
        }
        //DoanPV_20220712 L2PT-22463 END
        // start jira 5143
        if (_CAUHINH.NBN_BENHNHANNGUOINHA == '1' && opt._mode == '0' && $('#chkISNGUOINHA').is(":checked")) {
            if ($('#txtBENHNHANNGUOINHAHSBAID').val() == '') {
                DlgUtil.showMsg('Chưa chọn bệnh nhân của người nhà!', function () {
                    $('#txtTKBENHNHANNGUOINHA').focus();
                });
                return;
            }
            if (isBenhNhanQuaSoLuongNguoiNha($('#txtBENHNHANNGUOINHAHSBAID').val())) {
                DlgUtil.showMsg('Bệnh nhân được chọn đã quá số lượng người nhà!', function () {
                    $('#txtTKBENHNHANNGUOINHA').focus();
                });
                return;
            }
        }
        // end jira 5143
        // start jira 17907, 9924
        // cau hinh khong cho tiep nhan bn tư kham benh vao khi khong co so vao vien
        var cauHinhBatNhapSVV = _CAUHINH.HIS_NBN_KO_NK_KKCSVV;
        if (cauHinhBatNhapSVV != '0' && $('#txtSOVAOVIEN').val().trim() == '' && opt._mode == '1') {
            if (cauHinhBatNhapSVV == '1' || (cauHinhBatNhapSVV == '2' && opt._type == '0') || (cauHinhBatNhapSVV == '3' && opt._type == '3')) {
                DlgUtil.showMsg('Chưa có số vào viện!', function () {
                });
                return;
            }
        }
        // end jira 17907, 9924
        // start jira L2PT-11048
        // khong cho chuyen khoa neu con thuoc vat tu chua duyet
        if (_CAUHINH.NBN_XT_CKTCD == '1' && opt._mode == '2' && $('#cboXUTRIID').val() == '5') {
            var donThuocVatTuChuaDuyet = getDonThuocVatTuChuaDuyet($('#hidKHAMBENHID').val());
            if (donThuocVatTuChuaDuyet) {
                DlgUtil.showMsg('Còn thuốc vật tư chưa duyệt ở khoa hiện tại: ' + donThuocVatTuChuaDuyet, function () {
                });
                return;
            }
        }
        // end jira L2PT-11048
        // Kiểm tra tổng số ICD khi xử trí.
        if (opt._mode == '2') {
            var _checkIcd = checkSoLuongICDKemTheo($('#txtCHANDOANRAVIEN_KEMTHEO').val());
            if (_checkIcd > '0') {
                DlgUtil.showMsg('Tổng số mã ICD trong một điều trị vượt quá số lượng quy định của BHXH');
                return;
            }
            //check thời gian ngày giường
            if (_CAUHINH.NBN_XT_CHECK_NGAYGIUONG == '1' && $('#cboXUTRIID').val() == '5') {
                var obj = new Object();
                obj.KHAMBENHID = $('#hidKHAMBENHID').val();
                obj.THOIGIANKETTHUC = $('#txtTHOIGIANKETTHUC').val();
                var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV023", JSON.stringify(obj));
                if (result_ct != '0') {
                    DlgUtil.showMsg('Phiếu ngày giường: ' + result_ct + ' có thời gian vượt quá thời gian xử trí');
                    return;
                }
            }
            //check time ra vien
            if (_CAUHINH.NBN_XT_CHECK_TIME_RAVIEN != '0') {
                var obj = new Object();
                obj.KHOAID = opt._deptId
                obj.THOIGIANKETTHUC = $('#txtTHOIGIANKETTHUC').val();
                var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV024", JSON.stringify(obj));
                if (result_ct != '0') {
                    if (_CAUHINH.NBN_XT_CHECK_TIME_RAVIEN == '1') {
                        DlgUtil.showMsg('Tồn tại bệnh nhân: ' + result_ct + ' có thời gian ra viện tại cùng 1 thời gian');
                        return;
                    } else {
                        DlgUtil.showConfirm('Tồn tại bệnh nhân: ' + result_ct + ' có thời gian ra viện tại cùng 1 thời gian. Tiếp tục xử trí cho bệnh nhân?', function (flag) {
                            if (flag) {
                                saveData2(msgCheck);
                            }
                        });
                        return;
                    }
                }
            }
            if ($('#cboXUTRIID').val() == '6' && $('#cboLOAIRAVIEN').val() == '0') {
                DlgUtil.showMsg('Chưa chọn loại ra viện');
                return;
            }
        }
        //check tron vien
        if (opt._mode == '0') {
            if (_CAUHINH.NGT_CHECK_TRONVIEN == '2') {
                var _maba = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L09", $("#hidBENHNHANID").val());
                if (_maba != '0' && _maba != 'EXCEPTION_SQL') {
                    DlgUtil.showMsg('Bệnh nhân trốn viện theo bệnh án :' + _maba);
                    return;
                }
            }
        }
        saveData2(msgCheck);
    }

    function saveData2(msgCheck) {
        var objData = new Object();
        FormUtil.setFormToObject("divNhapBenhNhan", "", objData);
        if ($.find("[name='NOIGIOITHIEU']:checked").length > 0) {
            objData.NOIGIOITHIEU = $.find("[name='NOIGIOITHIEU']:checked")[0].value;
        }
        if ($("#cboHC_TINHID").val().trim() != "") {
            objData.HC_XAID = $("#cboHC_TINHID").val();
        }
        if ($("#cboHC_HUYENID").val().trim() != "") {
            objData.HC_XAID = $("#cboHC_HUYENID").val();
        }
        if ($("#cboHC_XAID").val().trim() != "") {
            objData.HC_XAID = $("#cboHC_XAID").val();
        }
        objData.MODE = opt._mode.toString();
        objData["TRANGTHAIKHAMBENH"] = opt._trangthaikhambenh + "";
        if (opt._type == "0") {
            objData.LOAITIEPNHAN = "0";
        } else {
            objData.LOAITIEPNHAN = "3";
        }
        if (typeof opt._submode != 'undefined') {
            objData.SUBMODE = opt._submode;
        }
        if (_CAUHINH.HIS_SUDUNG_DOITUONG_KHAIBAO == '1') {
            objData.SUB_DTBNID = $("#cboDOITUONGBENHNHANID").find(':selected').attr('extval0');
        } else {
            objData.SUB_DTBNID = '0';
        }
        if ($('#hidCV_CHUYENDUNGTUYEN1').val() == "1") {
            objData["CV_CHUYENDUNGTUYEN1"] = "1";
            objData["CV_CHUYENVUOTTUYEN1"] = "0";
        } else {
            objData["CV_CHUYENDUNGTUYEN1"] = "0";
            objData["CV_CHUYENVUOTTUYEN1"] = "1";
        }
        //Begin_HaNv_26072018: Nhap benh nhan tu khu tiep don truoc khi vao khoa - L2DKBD-1312
        if (opt._modeTiepDon == '1') {
            var _phongid = jsonrpc.AjaxJson.getOneValue("NTUH043.TGTD", [{
                "name": "[0]",
                "value": opt._deptId
            }]);
            if (_phongid != null && _phongid != 'null') {
                objData["KHOAID"] = _phongid;
            } else {
                DlgUtil.showMsg('Chưa thiết lập phòng trung gian tiếp đón của khoa đã chọn');
                return;
            }
            objData["NHAPTUTIEPDON"] = "1";
        } else {
            objData["NHAPTUTIEPDON"] = "0";
        }
        //End_HaNv_26072018
        //Begin_HaNv_31102018: Bo sung thong tin mien giam - L2HOTRO-12106
        var tyle = $('#cboDOITUONGDB option:selected').attr('extval0');
        if (tyle != undefined) {
            objData["TYLE_MIENGIAM"] = tyle;
        }
        //End_HaNv_3110201
        //Begin_HaNv_13052019: Bo sung thong tin ma doi tuong ngheo - L2PT-4494
        objData["MADOITUONGNGHEO"] = $("#txtMADOITUONGNGHEO").val().trim();
        //End_HaNv_13052019
        objData["MA_DOITUONG_KCB"] = $("#cboMA_DOITUONG_KCB").val(); //L2PT-53368
        /////////////////////////////////////////////////////
        // save data

        var result = '0';
        var maBenhAn = '';
        if (opt._mode == '0') {
            objData.XUTRIID = '';
            var _par = [opt._deptId, JSON.stringify(objData)];
            var result2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV003", _par.join('$'));
            var arr = result2.split(';');
            result = arr[0];
            if (arr.length > 1) {
                maBenhAn = arr[1];
            }
        } else {
            objData.XUTRIID = opt._mode == '1' ? "" : objData.XUTRIID;
            objData.TENXUTRI = $("#cboXUTRIID option:selected").text();
            objData.TENKQDT = $("#cboKETQUADIEUTRIID option:selected").text();
            _objData = $.extend(_objData, objData);
            //loai ra vien
            if (objData.XUTRIID == '1') {
                objData.LOAIRAVIEN = '1';
            } else if (objData.XUTRIID == '2') {
                objData.LOAIRAVIEN = '4';
            } else if (objData.XUTRIID == '3') {
                objData.LOAIRAVIEN = '3';
            } else if (objData.XUTRIID == '6') {
                objData.LOAIRAVIEN = objData.LOAIRAVIEN;
            } else {
                objData.LOAIRAVIEN = '0';
            }
            var _par = [opt._deptId, JSON.stringify(objData), JSON.stringify(_objData)];
            result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.EV004", _par.join('$'));
        }
        switch (result + "") {
            case "1":
                if (opt._mode == '0') {
                    themMoiThanhCong();
                } else if (opt._mode == '1') {
                    capNhatThanhCong();
                } else if (opt._mode == '2') {
                    if (!isKyCa) {
                        xuTriThanhCong(msgCheck);
                        //doanpv_L2PT-65549
                        if (_CAUHINH.HIS_XUTRI_AUTO_EKIPKYCA == '1') {
                            _caAutoEkipCa();
                        }
                    } else {
                        _caRpt('1', msgCheck);
                    }
                }
                break;
            case "0":
                DlgUtil.showMsg("Thêm/Cập nhật bệnh nhân lỗi");
                break;
            // nhapbenhnhan
            case "2":
                DlgUtil.showMsg("Thêm/Cập nhật bệnh nhân lỗi do tên bệnh nhân chứa ký tự đặc biệt");
                break;
            case "3":
                DlgUtil.showMsg("Thêm/Cập nhật bệnh nhân lỗi do địa chỉ bệnh nhân chứa ký tự đặc biệt");
                break;
            case "4":
                DlgUtil.showMsg("Thêm/Cập nhật bệnh nhân lỗi do địa chỉ BHYT bệnh nhân chứa ký tự đặc biệt");
                break;
            case "5":
                DlgUtil.showMsg("Thêm/Cập nhật bệnh nhân lỗi do số vào viện bị trùng với bệnh nhân có mã bệnh án " + maBenhAn);
                break;
            case "6":
                DlgUtil.showMsg("Thêm/Cập nhật bệnh nhân lỗi, thông tin bệnh nhân không đúng theo mã " + $('#txtMABENHNHAN').val());
                break;
            case "7":
                DlgUtil.showMsg("Mã thẻ BHYT trùng với BN khác, vui lòng kiểm tra lại!");
                break;
            //capnhatbenhnhan
            case "10":
                DlgUtil.showMsg("Không thể xử trí khi bệnh nhân ở trạng thái này");
                break;
            case "11":
                DlgUtil.showMsg("Không thể xử trí khi bệnh nhân đã chuyển khoa khác điều trị");
                break;
            case "12":
                DlgUtil.showMsg("Không thể xử trí khi bệnh nhân được chuyển từ khoa khác đến điều trị đồng thời");
                break;
            case "22":
                var khoaMoBA = getKhoaMoBA($('#hidTIEPNHANID').val(), opt._deptId);
                DlgUtil.showMsg("Không thể xử trí khi bệnh nhân đang được mở bệnh án để cập nhật, khoa đang mở bệnh án: " + khoaMoBA, function () {
                });
                break;
            case "31":
                DlgUtil.showMsg("Không thể xử trí khi bệnh nhân khi đã tạo đợt điều trị mới");
                break;
            case "41":
                DlgUtil.showMsg("Không thể xét ngày ra viện quá giá trị cấu hình cho phép");
                break;
            case "42":
                DlgUtil.showMsg("Thời gian kết thúc PTTT bằng hoặc sau thời gian ra viện");
                break;
            case "51":
                DlgUtil.showMsg("Không thể ra viện trong ngày chốt dữ liệu bảo hiểm");
                break;
            case "61":
                var donThuoc = getDonThuocDangSua($('#hidTIEPNHANID').val());
                DlgUtil.showMsg("Còn đơn thuốc/ vật tư đang sửa. Vui lòng thực hiện xóa hoặc hoàn thành đơn: " + donThuoc);
                break;
            case "612":
                DlgUtil.showMsg("Còn phiếu xét nghiệm, CDHA đang sửa hoặc đang thực hiện. Vui lòng thực hiện xóa hoặc hoàn thành phiếu");
                break;
            case "71":
                DlgUtil.showMsg("Bệnh nhân chưa tạm ứng, không thể nhập viện");
                break;
            case "81":
                DlgUtil.showMsg("Bệnh nhân chưa có thông tin cân nặng, không thể xử trí!");
                break;
            case "99":
                DlgUtil.showMsg("Bệnh nhân chưa kết thúc khám ở khoa gây mê. Không thể xử trí");
                break;
            default:
                break;
        }
    }

    function _bindEvent() {
        // L2PT-19260 ttlinh start
        EventUtil.setEvent("chinhsua_benhphu", function (e) {
            if (e.type == "CHANDOANVAOKHOA_KEMTHEO") { // L2PT-19260
                $('#txtCHANDOANVAOKHOA_KEMTHEO').val(e.benhphu);
            } else if (e.type == "CHANDOANRAVIEN_KEMTHEO") {
                $('#txtCHANDOANRAVIEN_KEMTHEO').val(e.benhphu);
            }
            DlgUtil.close("dlgBPKT");
        });
        // L2PT-19260
        EventUtil.setEvent("assignSevice_cancelTK", function (e) {
            DlgUtil.close("divDlgTKBN");
        });
        EventUtil.setEvent("assignSevice_savetv", function (e) {
            var data = e.msg;
            $('#txtTHOIGIANKETTHUC').val(data.TUVONGLUC);
            _objData = $.extend(_objData, data);
            DlgUtil.close("dlgXuTri");
            $('#txtSONGAYDIEUTRI').val(tinhngayDt($('#hidNGAYTIEPNHAN').val(), data.TUVONGLUC, $('#cboKETQUADIEUTRIID').val()));
        });
        EventUtil.setEvent("assignSevice_saverv", function (e) {
            var data = e.msg;
            if ($('#cboXUTRIID').val() == '3') {
                $('#txtTHOIGIANTVTV').val(data.PTHOIGIANRAVIEN);
                $("#txtTHOIGIANTVTV").attr("disabled", true);
            } else {
                $('#txtTHOIGIANKETTHUC').val(data.PTHOIGIANRAVIEN);
            }
            _objData = $.extend(_objData, data);
            DlgUtil.close("dlgXuTri");
            $('#txtSONGAYDIEUTRI').val(tinhngayDt($('#hidNGAYTIEPNHAN').val(), data.PTHOIGIANRAVIEN, $('#cboKETQUADIEUTRIID').val()));
        });
        EventUtil.setEvent("assignSevice_loadBenhNhan", function (e) {
            if (typeof (e) != 'undefined') {
                DlgUtil.close("divDlgTKBN");
                loadDetail(e.mabenhnhan);
            }
        });
        EventUtil.setEvent("assignSevice_savehk", function (e) {
            var data = e.msg;
            _objData = $.extend(_objData, data);
            DlgUtil.close("dlgXuTri");
            if ($('#txtTHOIGIANKETTHUC').val() == null || $('#txtTHOIGIANKETTHUC').val() == '') {
                $('#txtTHOIGIANKETTHUC').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
            }
            $('#txtSONGAYDIEUTRI').val(tinhngayDt($('#hidNGAYTIEPNHAN').val(), $('#txtTHOIGIANKETTHUC').val(), $('#cboKETQUADIEUTRIID').val()));
        });
        EventUtil.setEvent("assignSevice_resultTK", function (e) {
            if (e.mode == '0') {
                $('#' + e.ctrId).combogrid("setValue", e.text);
            } else if (e.mode == '1') {
                $('#' + e.ctrTargetId).val($('#' + e.ctrTargetId).val() == '' ? "" + e.text : $('#' + e.ctrTargetId).val() + ";" + e.text);
            }
            DlgUtil.close(e.popupId);
        });
        EventUtil.setEvent("assignSevice_savecv", function (e) {
            var data = e.msg;
            $("#divVien").css("display", "");
            $('#txtMAVIEN').val(data.MACHUYENVIEN);
            $('#txtTENVIEN').val(data.TENCHUYENVIENDEN);
            $('#txtTHOIGIANKETTHUC').val(data.THOIGIANCHUYENVIEN);
            _objData = $.extend(_objData, data);
            DlgUtil.close("dlgXuTri");
            $('#txtSONGAYDIEUTRI').val(tinhngayDt($('#hidNGAYTIEPNHAN').val(), data.THOIGIANCHUYENVIEN, $('#cboKETQUADIEUTRIID').val()));
            $('#btnSave').click();
        });
        EventUtil.setEvent("assignSevice_saveCv", function (e) {
            var objChuyenVien = e.msg;
            $('#txtCHUANDOANGIOITHIEU').val(objChuyenVien.CHANDOANTUYENDUOI);
            $('#txtTKMANOIGIOITHIEU1').val(objChuyenVien.MANOIGIOITHIEU);
            $('#hidCV_CHUYENVIEN_HINHTHUCID1').val(objChuyenVien.CV_CHUYENVIEN_HINHTHUCID);
            $('#hidCV_CHUYENVIEN_LYDOID1').val(objChuyenVien.CV_CHUYENVIEN_LYDOID);
            $('#hidCV_CHUYENDUNGTUYEN1').val(objChuyenVien.CV_CHUYENDUNGTUYEN);
            $('#hidCV_SOCHUYENVIEN1').val(objChuyenVien.CV_SOCHUYENVIEN);
            $("#txtTKNOICD").val(objChuyenVien.MANOIGIOITHIEU);
            $('#txtTKNOICD').combogrid("setValue", objChuyenVien.MANOIGIOITHIEU);
            DlgUtil.close("dlgCV");
        });
        $("#txtSOCMTND").focusout(function () {
            // nhap so cmt thi tu dong lay ra thong tin tuong ung va fill vao
            var cmt = $("#txtSOCMTND").val();
            if (cmt && cmt != '') {
                var _cccd = $("#txtSOCMTND").val().split("|");
                //Beg_HaNv_081222: fix auto fill CCCD - L2PT-29642
                if (_cccd.length > 1) {
                    $("#txtSOCMTND").val(_cccd[0]);
                    $("#txtTENBENHNHAN").val(_cccd[2]);
                    if (_cccd[3] && _cccd[3].length == "4") {
                        $("#txtNAMSINH").val(_cccd[3]);
                        $("#txtNGAYSINH").val("");
                        $("#txtNAMSINH").trigger("change");
                    } else {
                        $("#txtNGAYSINH").trigger("focusin");
                        $("#txtNGAYSINH").val(_cccd[3].substring(0, 2) + "/" + _cccd[3].substring(2, 4) + "/" + _cccd[3].substring(4, 8));
                        $("#txtNGAYSINH").change();
                    }
                    $("#txtTKGIOITINH").val(_cccd[4] == "Nam" ? 1 : 2);
                    $("#cboGIOITINHID").val(_cccd[4] == "Nam" ? 1 : 2);
                    $("#txtDIACHI").val(_cccd[5]);
                }
                //End_HaNv_081222
                var ret1 = _callCheckCong($("#txtSOCMTND").val());
                if (ret1.hasOwnProperty("maKetQua") && ret1.hasOwnProperty("gtTheDen") && ret1.hasOwnProperty("gtTheTu") && ret1.maThe != null && ret1.maThe != 'null' && ret1.maThe != '') {
                    // end sondn L2PT-1038
                    $("#txtMABHYT").val(ret1.maThe);
                    //Beg_HaNv_290822: Quét căn cước công dân tìm kiếm được Mã BN - L2PT-24227
                    _muchuong_bhyt(opt._deptId, $("#txtMABHYT").val().substr(0, 3).toUpperCase(), $("#cboTUYENID").val());
                    var obj = new Object();
                    obj.ten = '-1';
                    obj.ngaysinh = '-1';
                    obj.gioitinh = '-1';
                    obj.mabhyt = $('#txtMABHYT').val().trim();
                    var _par = [JSON.stringify(obj)];
                    var resultCount = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV008", _par.join('$'));
                    if (resultCount != '0') {
                        var resultCode = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV012", $('#txtMABHYT').val().trim());
                        loadDetail(resultCode);
                    }
                    //End_HaNv_290822
                    if (ret1.gioiTinh == null || ret1.gioiTinh == 'null' || ret1.gioiTinh == '') {
                        $("#txtTKGIOITINH").val(-1);
                        $("#cboGIOITINHID").val(-1);
                    } else {
                        $("#txtTKGIOITINH").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                        $("#cboGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                    }
                    $('#txtBHYT_BD').val(ret1.gtTheTu);
                    $('#txtBHYT_KT').val(ret1.gtTheDen);
                    $("#txtNGAYDU5NAM").val(ret1.ngayDu5Nam);
                    $("#txtNGAYDU5NAM").change();
                    $("#txtDIACHI_BHYT").val(ret1.diaChi);
                    if ($("#txtDIACHI").val() == "") {
                        $("#txtDIACHI").val(ret1.diaChi); // sondn L2PT-28917
                    }
                    // SONDN 19/03/2020 L2PT-17643
                    for (i = 1; i <= 3; i++) {
                        if (ret1.maKV == "K" + i) {
                            $("#cboMAVUNGID").val(i);
                        }
                    }
                    // END SONDN 19/03/2020 L2PT-17643
                    $("#txtTENBENHNHAN").val(ret1.hoTen.toUpperCase());
                    $("#txtTKDKKBBD").val(ret1.maDKBD);
                    $("#cboDKKBBDID").empty();
                    $("#cboDKKBBDID").append('<option value="' + ret1.maDKBD + '" selected>' + ret1.tenDKBDMoi + '</option>');
                    if (ret1.ngaySinh.length == "4") {
                        $("#txtNAMSINH").val(ret1.ngaySinh);
                        $("#txtNGAYSINH").val("");
                        $("#txtNAMSINH").change();
                    } else {
                        $("#txtNGAYSINH").val(ret1.ngaySinh);
                        $("#txtNGAYSINH").change();
                    }
                    // sondn L2PT-27548
                    // 003: Thẻ cũ hết giá trị sử dụng nhưng đã được cấp thẻ mới.
                    if (ret1.maKetQua == "003") {
                        if (ret1.hasOwnProperty("maTheMoi")) {
                            $("#txtMABHYT").val(ret1.maTheMoi);
                            $('#txtBHYT_BD').val(ret1.gtTheTuMoi);
                            $('#txtBHYT_KT').val(ret1.gtTheDenMoi);
                            $("#cboDKKBBDID").val(ret1.maDKBDMoi);
                            $("#txtTKDKKBBD").val(ret1.maDKBDMoi);
                            _muchuong_bhyt(opt._deptId, $("#txtMABHYT").val().replace(/[-]/g, "").substr(0, 3).toUpperCase(), $("#cboTUYENID").val());
                            DlgUtil.showMsg("Bệnh nhân đã được cấp thẻ mới, mã thẻ là: " + ret1.maTheMoi);
                        } else {
                            DlgUtil.showMsg("Trong dữ liệu trả về mã thẻ mới không tồn tại, yêu cầu kiểm tra lại thông tin cổng BHXH. ");
                        }
                    }
                    // end sondn L2PT-27548
                } else if (ret1 == "0") {
                    // DlgUtil.showMsg("Thiếu thông tin họ tên / mã BHYT / ngày sinh, yêu cầu bổ sung để check cổng");
                    return;
                } else {
                    DlgUtil.showMsg("Không kết nối được với cổng BHXH, yêu cầu kiểm tra lại");
                    return;
                }
            }
        });
        // start jira L2PT-14071
        $("#cboLOAIKHAM").on('change', function (e) {
            _loadHopDong($(this).val(), 0);
        });
        // start jira L2PT-14071
        //L2PT-117155
        $("#cboKHOAID").on('change', function (e) {
            if (_CAUHINH_NBN[0].NTU_NBN_LOAIBAMD == '1') {
                var check = jsonrpc.AjaxJson.getOneValue("NTU01H002.GETLBA", [{
                    "name": "[0]",
                    "value": $("#hidKHAMBENHID").val()
                }, {
                    "name": "[1]",
                    "value": $("#cboKHOAID").val()
                }]);
                if (check && check != 'null') {
                    $('#cboLOAIBENHANID').val(check);
                }
            }
        });
        $('#chkISNGUOINHA').on('change', function (e) {
            $('#txtBENHNHANNGUOINHA').val('');
            $('#txtBENHNHANNGUOINHAHSBAID').val('');
            if ($('#chkISNGUOINHA').is(":checked")) {
                $('#txtTKBENHNHANNGUOINHA').prop("disabled", false);
            } else {
                $('#txtTKBENHNHANNGUOINHA').prop("disabled", true);
            }
        });
        $("#txtTKBENHNHANNGUOINHA").keyup(function () {
            setTimeout(function () {
                $(".combogrid").css({
                    "z-index": 2
                });
            }, 500);
        });
        $("#btnEditBc").on("click", function (e) {
            if (!$('#cboCHANDOANRAVIENID').val()) {
                DlgUtil.showMsg("Chưa chọn bệnh chính để sửa");
                return;
            }
            $('#cboCHANDOANRAVIENID').hide();
            $('#txtBCEDIT').val($('#cboCHANDOANRAVIENID option:selected').text());
            $('#txtBCEDIT').show();
            $('#btnSaveEditBc').show();
            $("#btnEditBc").hide();
        });
        $("#btnSaveEditBc").on("click", function (e) {
            if ($.trim($('#txtBCEDIT').val()) != '') {
                var icd = $("#cboCHANDOANRAVIENID").val();
                $("#cboCHANDOANRAVIENID").empty();
                $("#cboCHANDOANRAVIENID").append('<option value="' + icd + '">' + $('#txtBCEDIT').val().trim() + '</option>');
                $('#txtBCEDIT').hide();
                $('#btnSaveEditBc').hide();
                $('#cboCHANDOANRAVIENID').show();
                $("#btnEditBc").show();
            } else {
                $('#txtBCEDIT').focus();
                return;
            }
        });
        $("#txtBCEDIT").focusout(function () {
            if ($.trim($('#txtBCEDIT').val()) != '') {
                var icd = $("#cboCHANDOANRAVIENID").val();
                $("#cboCHANDOANRAVIENID").empty();
                $("#cboCHANDOANRAVIENID").append('<option value="' + icd + '">' + $('#txtBCEDIT').val().trim() + '</option>');
                $('#txtBCEDIT').hide();
                $('#btnSaveEditBc').hide();
                $('#cboCHANDOANRAVIENID').show();
                $("#btnEditBc").show();
            } else {
                $('#txtBCEDIT').focus();
                return;
            }
        });
        $("#btnEDITCHANDOANRAVIENKT").on("click", function (e) {
            //L2PT-116259
            if (showyhct4750 == '1') {
                var myVar = {
                    benhphu: $('#txtCHANDOANRAVIEN_KEMTHEO').val(),
                    chandoan_kt_bd: "",
                    type: "CHANDOANRAVIEN_KEMTHEO",
                    benhchinh: $('#txtTKCHANDOANRAVIENID').val(),
                    yhct: showyhct4750,
                    benhphu1: $('#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT').val()
                };
                DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 500);
                DlgUtil.open("dlgBPKT");
            } else {
                var myVar = {
                    benhphu: $('#txtCHANDOANRAVIEN_KEMTHEO').val(),
                    chandoan_kt_bd: "",
                    type: "CHANDOANRAVIEN_KEMTHEO",
                    benhchinh: $('#txtTKCHANDOANRAVIENID').val()
                };
                DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 500);
                DlgUtil.open("dlgBPKT");
            }
        });
        // L2PT-19260 ttlinh start
        $("#btnEditCDVaoKhoa").on("click", function (e) {
            if (!$('#cboMACHANDOANVAOKHOA').val()) {
                DlgUtil.showMsg("Chưa chọn chẩn đoán chính để sửa");
                return;
            }
            $('#cboMACHANDOANVAOKHOA').hide();
            $('#txtEditCDVaoKhoa').val($('#cboMACHANDOANVAOKHOA option:selected').text());
            $('#txtEditCDVaoKhoa').show();
            $('#btnSaveEditCDVaoKhoa').show();
            $("#btnEditCDVaoKhoa").hide();
        });
        $("#btnSaveEditCDVaoKhoa").on("click", function (e) {
            if ($.trim($('#txtEditCDVaoKhoa').val()) != '') {
                var icd = $("#cboMACHANDOANVAOKHOA").val();
                $("#cboMACHANDOANVAOKHOA").empty();
                $("#cboMACHANDOANVAOKHOA").append('<option value="' + icd + '">' + $('#txtEditCDVaoKhoa').val().trim() + '</option>');
                $('#txtEditCDVaoKhoa').hide();
                $('#btnSaveEditCDVaoKhoa').hide();
                $('#cboMACHANDOANVAOKHOA').show();
                $("#btnEditCDVaoKhoa").show();
            } else {
                $('#txtEditCDVaoKhoa').focus();
                return;
            }
        });
        $("#txtEditCDVaoKhoa").focusout(function () {
            if ($.trim($('#txtEditCDVaoKhoa').val()) != '') {
                var icd = $("#cboMACHANDOANVAOKHOA").val();
                $("#cboMACHANDOANVAOKHOA").empty();
                $("#cboMACHANDOANVAOKHOA").append('<option value="' + icd + '">' + $('#txtEditCDVaoKhoa').val().trim() + '</option>');
                $('#txtEditCDVaoKhoa').hide();
                $('#btnSaveEditCDVaoKhoa').hide();
                $('#cboMACHANDOANVAOKHOA').show();
                $("#btnEditCDVaoKhoa").show();
            } else {
                $('#txtEditCDVaoKhoa').focus();
                return;
            }
        });
        $("#btnEDITCHANDOANVAOKHOAKT").on("click", function (e) {
            if (!$('#txtCHANDOANVAOKHOA_KEMTHEO').val()) {
                DlgUtil.showMsg("Chưa chọn chẩn đoán phụ để sửa");
                return;
            }
            //L2PT-116259
            if (showyhct4750 == '1') {
                var myVar = {
                    benhphu: $('#txtCHANDOANVAOKHOA_KEMTHEO').val(),
                    chandoan_kt_bd: "",
                    type: "CHANDOANVAOKHOA_KEMTHEO",
                    yhct: showyhct4750,
                    benhphu1: $('#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT').val()
                };
                DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 500);
                DlgUtil.open("dlgBPKT");
            } else {
                var myVar = {
                    benhphu: $('#txtCHANDOANVAOKHOA_KEMTHEO').val(),
                    chandoan_kt_bd: "",
                    type: "CHANDOANVAOKHOA_KEMTHEO" // L2PT-19260
                };
                DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 500);
                DlgUtil.open("dlgBPKT");
            }
        });
        $('#btnCLEARCHANDOANVAOKHOA').on("click", function () {
            $('#txtCHANDOANVAOKHOA_KEMTHEO').val('');
        });
        // L2PT-19260 ttlinh end
        $(':input').keydown(function (e) {
            var id = $(this).attr('id');
            if (e.which === 13) {
                var index = $(':input').index(this) + 1;
                if (!$(':input').eq(index).is(":button")) {
                    $(':input').eq(index).focus();
                }
                if (id == "txtSOCMTND") {
                    $('#txtSOCMTND').blur();
                }
            }
        });
        $('#txtNGAYSINH').on('change', function (e) {
            var ngaysinh = tinhTuoi($('#txtNGAYSINH').val(), 'txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');
            if (_CAUHINH.HIS_HIDENGHENGHIEP_QD342020 == '0') {//L2PT-138024
				if ($('#cboDVTUOI').val() != "1") {
					$('#cboNGHENGHIEPID').val(3);
					$('#cboNGHENGHIEPID').change();
				} else {
					if (parseInt($('#txtTUOI').val()) < 7) {
						$('#cboNGHENGHIEPID').val(3);
						$('#cboNGHENGHIEPID').change();
					}
				}
			}
            //Beg_HaNv_140323: Check giờ sinh nếu BN chưa đủ 28 ngày (QD130) - L2PT-35188
            if ($('#cboDVTUOI').val() == "3") {
                var songaybatbuoc = _CAUHINH.HIS_BATBUOCGIOSINH_SONGAY;
                if (_CAUHINH.HIS_QD130 == '1' && parseInt($('#txtTUOI').val()) <= songaybatbuoc) {
                    DlgUtil.showMsg('Bệnh nhân nhỏ hơn ' + songaybatbuoc + ' ngày bắt buộc nhập giờ sinh ');
                    $('#txtGIO_SINH').focus();
                    // Start L2PT-131093 QuyHTA
                    var maKhoaID = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_MACDINHLOAIBENHAN_SOSINH');
                    if (maKhoaId != '0') {
                        par = [];
                        par.push({
                            "name": "[0]",
                            "value": maKhoaId
                        });
                        var getIdKhoa = jsonrpc.AjaxJson.getOneValue('NGT.GETMAKHOA', opt._deptId);
                        if (getIdKhoa == maKhoaId) {
                            $('#cboLOAIBENHANID').val('6');
                            $('#cboLOAIBENHANID').change();
                            $('#h3BenhAn').text($("#cboLOAIBENHANID option:selected").text());
                        }
                    }
                    // End L2PT-131093 QuyHTA
                }
            }
            //End_HaNv_140323

        });
        $('#txtNAMSINH').on('change', function (e) {
            var b_namsinh = $('#txtNAMSINH').val();
            if (isNaN(b_namsinh)) {
                DlgUtil.showMsg("Năm sinh " + $.i18n("integer"), function () {
                    $('#txtNAMSINH').val("");
                    $('#txtTUOI').val("");
                });
                return;
            }
            if (b_namsinh.length == 4) {
                var today = new Date();
                var bNamHT = today.getFullYear();
                if (b_namsinh > bNamHT) {
                    DlgUtil.showMsg("Năm sinh không được lớn hơn năm hiện tại", function () {
                        $('#txtNAMSINH').val("");
                    });
                    return;
                } else {
                    $('#txtTUOI').val(bNamHT - b_namsinh);
                }
                $('#cboDVTUOI').val("1");
            }
            if (_CAUHINH.HIS_HIDENGHENGHIEP_QD342020 == '0') {//L2PT-138024
				if ($('#cboDVTUOI').val() != "1") {
					$('#cboNGHENGHIEPID').val(3);
					$('#cboNGHENGHIEPID').change();
				} else {
					if (parseInt($('#txtTUOI').val()) < 7) {
						$('#cboNGHENGHIEPID').val(3);
						$('#cboNGHENGHIEPID').change();
					}
				}
			}
        });
        $('#chkNOTHE').on('change', function (e) {
            if ($('#chkNOTHE').is(":checked")) {
                if (dkkcbbd_nothe.length > 0) {
                    config_ndkkcbbd = dkkcbbd_nothe;
                } else if (config_ndkkcbbd <= 0) {
                    config_ndkkcbbd = '35148';
                }
                _clearObjectBHYT(false);
                $("#chkNOTHE").prop("checked", true);
                $("#chkNOTHE").attr("disabled", false);
                $('#txtMABHYT').val(_sinhSoNoThe());
                $('#txtMABHYT').prop("disabled", true);
                $('#txtTKDKKBBD').combogrid("setValue", config_ndkkcbbd);
                $('#cboTUYENID').val(1);
                $('#txtDIACHI_BHYT').val($('#txtDIACHI').val());
                // start jira  L2PT-32246
                if (_CAUHINH.NBN_STTNGAYKT6THANG == '1') {
                    var _ngayhientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
                    $("#txtBHYT_BD").val(_ngayhientai);
                    $("#txtBHYT_KT").val(_ngayhientai.substring(0, 6) + (Number(_ngayhientai.substring(6)) + 1));
                }
                // end jira L2PT-32246
            } else {
                $('#txtMABHYT').prop("disabled", false);
                $('#txtMABHYT').val("");
            }
        });
        $('#txtTUOI').on('change', function (e) {
            tinhNgaySinh('txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');
            //Beg_HaNv_140323: Check giờ sinh nếu BN chưa đủ 28 ngày (QD130) - L2PT-35188
            if ($('#cboDVTUOI').val() == "3") {
                var songaybatbuoc = _CAUHINH.HIS_BATBUOCGIOSINH_SONGAY;
                if (_CAUHINH.HIS_QD130 == '1' && parseInt($('#txtTUOI').val()) <= songaybatbuoc) {
                    DlgUtil.showMsg('Bệnh nhân nhỏ hơn ' + songaybatbuoc + ' ngày bắt buộc nhập giờ sinh ');
                    $('#txtGIO_SINH').focus();
                }
            }
            //End_HaNv_140323
        });
        //Beg_HaNv_140323: Check giờ sinh nếu BN chưa đủ 28 ngày (QD130) - L2PT-35188
        $('#txtGIO_SINH').on('change', function (e) {
            if ($('#txtGIO_SINH').val().trim().length > 0) {
                var _tgs = $('#txtGIO_SINH').val().split(':');
                var _hh = _tgs[0];
                var _mi = _tgs[1];
                if (parseInt(_hh) > 24) {
                    DlgUtil.showMsg("Giờ sinh không được vượt quá 24 giờ");
                    $('#txtGIO_SINH').val("");
                    $('#txtGIO_SINH').focus();
                    return false;
                }
                if (parseInt(_mi) > 59) {
                    DlgUtil.showMsg("Phút sinh không được vượt quá 59 phút");
                    $('#txtGIO_SINH').val("");
                    $('#txtGIO_SINH').focus();
                    return false;
                }
            }
        });
        //End_HaNv_140323
        var previous;
        $("#cboDOITUONGBENHNHANID").on('focus', function () {
            previous = this.value;
        }).change(function () {
            if (previous != $("#cboDOITUONGBENHNHANID").val()) {
                _clearObjectBHYT(this.value != '1');
            }
            $('#txtTKDOITUONGBENHNHAN').val($('#cboDOITUONGBENHNHANID').val());
            previous = this.value;
        });
        $('#txtBHYT_BD').on('change', function (e) {
            $('#txtBHYT_BD').val(stringToDateFormat($('#txtBHYT_BD').val()));
        });
        $('#txtBHYT_KT').on('change', function (e) {
            $('#txtBHYT_KT').val(stringToDateFormat($('#txtBHYT_KT').val()));
        });
        $('#cboHC_TINHID').on('change', function (e) {
            changeTinh($('#cboHC_TINHID').val(), true);
        });
        $('#cboHC_HUYENID').on('change', function (e) {
            changeHuyen($('#cboHC_HUYENID').val(), true);
        });
        $('#cboHC_XAID').on('change', function (e) {
            changeXa($('#cboHC_XAID').val(), true);
        });
        $('#cboDIABANID').on('change', function (e) {
            $('#txtTKDIABAN').val($('#cboDIABANID').val());
        });
        $('#cboGIOITINHID').on('change', function (e) {
            $('#txtTKGIOITINH').val($('#cboGIOITINHID').val());
        });
        $('#cboNGHENGHIEPID').on('change', function (e) {
            $('#txtTKNGHENGHIEP').val($('#cboNGHENGHIEPID').val());
        });
        $('#cboDANTOCID').on('change', function (e) {
            $('#txtTKDANTOC').val($('#cboDANTOCID').val());
        });
        $('#cboQUOCGIAID').on('change', function (e) {
            $('#txtTKQUOCGIA').val($('#cboQUOCGIAID').val());
        });
        $('#cboMAVUNGID').on('change', function (e) {
            if ($('#cboTUYENID').val() != '' && $("#txtMABHYT").val() != '') {
                /*var objBH = new Object();
				objBH.MATHE = $("#txtMABHYT").val();
				objBH.DOITUONGSONG = $('#cboMAVUNGID').val();
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.MUCHUONG.BHYT", opt._deptId + '$' + $("#txtMABHYT").val().substr(0, 3).toUpperCase() + '$' + $('#cboTUYENID').val() + '$' +
						JSON.stringify(objBH) + '$' + $('#cboHINHTHUCVAOVIENID').val());
				if (data_ar != null && data_ar.length > 0) {
					if (data_ar[0].MUCHUONG_NOI == '-1') {
						DlgUtil.showMsg('Mã đầu thẻ đã bị khóa');
						return;
					} else if (data_ar[0].MUCHUONG_NOI == '-2') {
						DlgUtil.showMsg('Mã đầu thẻ không tồn tại');
						return;
					}
					var _bstr = data_ar[0].MUCHUONG_NOI + "%";
					$("#txtMUCHUONG").val(_bstr);
				}*/
                _muchuong_bhyt(opt._deptId, $("#txtMABHYT").val().substr(0, 3).toUpperCase(), $("#cboTUYENID").val());
            }
            // start jira L2PT-21321
            if (_CAUHINH.NTU_NBN_LAN_ATDT == '1') {
                if ($('#txtTKDKKBBD').val().substr(0, 2) == '80' && $('#cboMAVUNGID').val()) {
                    $('#cboTUYENID').val(1);
                }
            }
            // end jira L2PT-21321
        });
        $('#cboTUYENID').on('change', function (e) {
            $('#dvBHYT_LOAIID1').hide();
            $("#dvBHYT_LOAIID").removeClass("col-xs-2 low-padding");
            $("#dvBHYT_LOAIID").addClass("col-xs-4 low-padding");
            if ($('#cboTUYENID').val() != '' && $("#txtMABHYT").val() != '') {
                /*var objBH = new Object();
				objBH.MATHE = $("#txtMABHYT").val();
				objBH.DOITUONGSONG = $('#cboMAVUNGID').val();
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.MUCHUONG.BHYT", opt._deptId + '$' + $("#txtMABHYT").val().substr(0, 3).toUpperCase() + '$' + $('#cboTUYENID').val() + '$' +
						JSON.stringify(objBH) + '$' + $('#cboHINHTHUCVAOVIENID').val());
				if (data_ar != null && data_ar.length > 0) {
					if (data_ar[0].MUCHUONG_NOI == '-1') {
						DlgUtil.showMsg('Mã đầu thẻ đã bị khóa');
						return;
					} else if (data_ar[0].MUCHUONG_NOI == '-2') {
						DlgUtil.showMsg('Mã đầu thẻ không tồn tại');
						return;
					}
					var _bstr = data_ar[0].MUCHUONG_NOI + "%";
					$("#txtMUCHUONG").val(_bstr);
				}*/
                _muchuong_bhyt(opt._deptId, $("#txtMABHYT").val().substr(0, 3).toUpperCase(), $("#cboTUYENID").val());
                if ($('#cboTUYENID').val() == '3') {
                    $('#cboHINHTHUCVAOVIENID').val('2').change();
                    $('#cboHINHTHUCVAOVIENID').prop("disabled", true);
                } else {
                    $('#cboHINHTHUCVAOVIENID').prop("disabled", false);
                }
            }
            if ($('#cboTUYENID').val() == '4') {
                $("#chkBH5NAM").prop("checked", false);
                $("#chkBH5NAM").attr("disabled", true);
                $("#chkBH6THANG").prop("checked", false);
                $("#chkBH6THANG").attr("disabled", true);
            } else {
                $("#chkBH5NAM").attr("disabled", false);
                $("#chkBH6THANG").attr("disabled", false);
            }
            // START SONDN 020118
            if ($('#cboTUYENID').val() == '2') {
                $('#btnChuyenTuyen').prop('disabled', false);
                // start jira 9664
                if (_CAUHINH.HIS_NBN_SOCHUYENTUYEN == '1') {
                    $('#divSoChuyenTuyen').show();
                }
                // end jira 9664
                if (_CAUHINH_NBN[0].NTU_NBN_CHECK_GIAYTOCT && _CAUHINH_NBN[0].NTU_NBN_CHECK_GIAYTOCT == '1') {
                    $('#dvBHYT_LOAIID1').show();
                    $("#dvBHYT_LOAIID").removeClass("col-xs-4 low-padding");
                    $("#dvBHYT_LOAIID").addClass("col-xs-2 low-padding");
                }
            } else {
                $('#btnChuyenTuyen').prop('disabled', true);
                // start jira 9664
                if (_CAUHINH.HIS_NBN_SOCHUYENTUYEN == '1') {
                    $('#divSoChuyenTuyen').hide();
                }
                // end jira 9664
            }
            // END SONDN 020118
        });
        $('#cboHINHTHUCVAOVIENID').on('change', function (e) {
            if ($('#cboTUYENID').val() != '' && $("#txtMABHYT").val() != '') {
                /*var objBH = new Object();
				objBH.MATHE = $("#txtMABHYT").val();
				objBH.DOITUONGSONG = $('#cboMAVUNGID').val();
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.MUCHUONG.BHYT", opt._deptId + '$' + $("#txtMABHYT").val().substr(0, 3).toUpperCase() + '$' + $('#cboTUYENID').val() + '$' +
						JSON.stringify(objBH) + '$' + $('#cboHINHTHUCVAOVIENID').val());
				if (data_ar != null && data_ar.length > 0) {
					if (data_ar[0].MUCHUONG_NOI == '-1') {
						DlgUtil.showMsg('Mã đầu thẻ đã bị khóa');
						return;
					} else if (data_ar[0].MUCHUONG_NOI == '-2') {
						DlgUtil.showMsg('Mã đầu thẻ không tồn tại');
						return;
					}
					var _bstr = data_ar[0].MUCHUONG_NOI + "%";
					$("#txtMUCHUONG").val(_bstr);
				}*/
                _muchuong_bhyt(opt._deptId, $("#txtMABHYT").val().substr(0, 3).toUpperCase(), $("#cboTUYENID").val());
            } else {
                $("#txtMUCHUONG").val('');
            }
        });
        $('#cboKETQUADIEUTRIID').on('change', function (e) {
            $('#txtTKKETQUADIEUTRI').val($('#cboKETQUADIEUTRIID').val());
            $('#cboXUTRIID').val('');
            $('#txtTKXUTRI').val('');
            // start jira 26488
            if (_CAUHINH.NBN_MAPKQXT == '1') {
                ComboUtil.getComboTag("cboXUTRIID", "NTU01H002.L19.2", [{
                    "name": "[0]",
                    "value": $('#cboKETQUADIEUTRIID').val()
                }, {
                    "name": "[1]",
                    "value": opt._type
                }], "", {
                    value: '',
                    text: 'Chọn'
                }, "sql", "", function () {
                    $('#txtTKXUTRI').val("");
                });
            }
            // end jira 26488
        });
        $('#txtMABENHNHAN').on('change', function (e) {
            if (opt._mode == '0') {
                flag_ban_the = false;
                loadDetail($('#txtMABENHNHAN').val().trim());
            }
        });
        $('#txtTKGIOITINH').on('change', function (e) {
            fillValueDisplay('txtTKGIOITINH', 'cboGIOITINHID');
        });
        $('#txtTKNGHENGHIEP').on('change', function (e) {
            fillValueDisplay('txtTKNGHENGHIEP', 'cboNGHENGHIEPID');
        });
        $('#txtTKDANTOC').on('change', function (e) {
            fillValueDisplay('txtTKDANTOC', 'cboDANTOCID');
        });
        $('#txtTKQUOCGIA').on('change', function (e) {
            fillValueDisplay('txtTKQUOCGIA', 'cboQUOCGIAID');
        });
        $('#txtSONHA').on('change', function (e) {
            updateDiaChiFromTTHC();
        });
        $('#txtTKHC_TINH').on('change', function (e) {
            changeTinh($('#txtTKHC_TINH').val());
        });
        $('#txtTKHC_HUYEN').on('change', function (e) {
            changeHuyen($('#txtTKHC_HUYEN').val());
        });
        $('#txtTKHC_XA').on('change', function (e) {
            changeXa($('#txtTKHC_XA').val());
        });
        $('#txtTKDIABAN').on('change', function (e) {
            fillValueDisplay('txtTKDIABAN', 'cboDIABANID');
        });
        $('#txtTKDOITUONGBENHNHAN').on('change', function (e) {
            fillValueDisplay('txtTKDOITUONGBENHNHAN', 'cboDOITUONGBENHNHANID');
            _clearObjectBHYT(this.value != '1');
        });
        $('#txtTKKETQUADIEUTRI').on('change', function (e) {
            fillValueDisplay('txtTKKETQUADIEUTRI', 'cboKETQUADIEUTRIID');
        });
        $('#txtTKXUTRI').on('change', function (e) {
            fillValueDisplay('txtTKXUTRI', 'cboXUTRIID');
        });
        //L2PT-27682
        $('#txtTKNGUYENNHAN').on('change', function (e) {
            fillValueDisplay('txtTKNGUYENNHAN', 'cboTAINAN_NGUYENNHANID');
        });
        $('#btnCLEARCHANDOANRAVIEN').on("click", function () {
            $('#txtCHANDOANRAVIEN_KEMTHEO').val('');
            $('#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT').val('');
        });
        $('#btnCLEARCHUANDOANGIOITHIEU').on("click", function () {
            $('#txtCHUANDOANGIOITHIEU').val('');
        });
        $('#btnChuyenTuyen').on(
            'click',
            function (e) {
                var param = {
                    chandoantuyenduoi: $('#txtCHUANDOANGIOITHIEU').val(),
                    tkmanoigioithieu: $('#txtTKNOICD').val(),
                    cv_chuyenvien_hinhthucid: $('#hidCV_CHUYENVIEN_HINHTHUCID1').val(),
                    cv_chuyenvien_lydoid: $('#hidCV_CHUYENVIEN_LYDOID1').val(),
                    cv_chuyenvien_sochuyenvien: $('#hidCV_SOCHUYENVIEN1').val(),
                    cv_chuyendungtuyen: $('#hidCV_CHUYENDUNGTUYEN1').val() == null || $('#hidCV_CHUYENDUNGTUYEN1').val() == 'null' || $('#hidCV_CHUYENDUNGTUYEN1').val() == '' ? "1" : $(
                        '#hidCV_CHUYENDUNGTUYEN1').val()
                };
                DlgUtil.buildPopupUrl("dlgCV", "divDlg", "manager.jsp?func=../ngoaitru/NGT01T001_chuyenvien", param, 'THÔNG TIN CHUYỂN TUYẾN', 820, 280);
                DlgUtil.open("dlgCV");
            });
        $('#btnSaveCv').bindOnce("click", function () {
            if (opt._mode == '2') {
                if ($('#cboXUTRIID').val() == '6') {
                    $('#btnSave').click();
                }
            }
        });
        $('#btnSaveRv').bindOnce("click", function () {
            if (opt._mode == '2') {
                if ($('#cboXUTRIID').val() != '6' && $('#cboXUTRIID').val() != '5') {
                    $('#btnSave').click();
                }
            }
        });
        $('#btnSave').bindOnce("click", function () {
            // Start L2PT-132353 QuyHTA: Kiểm tra xem đã chọn loại bệnh án hay chưa
            if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_BATCHONLOAIBENHAN') == '1' && $('#cboLOAIBENHANID').val() == '0') {
                DlgUtil.showMsg('Vui lòng chọn loại bệnh án trước khi lưu!');
                $('#cboLOAIBENHANID').focus();
                return;
            } else {
                saveThongTin();
            }
            // End L2PT-132353 QuyHTA: Kiểm tra xem đã chọn loại bệnh án hay chưa
            // saveThongTin();
        }, 1000);
        $('#btnNext').on("click", function (e) {
            clearSave();
        });
        $('#btnClose').on("click", function (e) {
            parent.DlgUtil.close("divDlgNhapBenhNhan");
        });
        //L2PT-27682 start
        $("#btnChange_TNTT").on("click", function (e) {
            $('#cboTAINAN_NGUYENNHANID').change();
        });
        $('#cboTAINAN_NGUYENNHANID').on('change', function (e) {
            $('#txtTKNGUYENNHAN').val($('#cboTAINAN_NGUYENNHANID').val());
            if ($('#cboTAINAN_NGUYENNHANID').val() != 19 && $('#cboTAINAN_NGUYENNHANID').val() != null && $('#cboTAINAN_NGUYENNHANID').val() != '') {
                var param = {
                    khambenhid: $("#hidKHAMBENHID").val(),
                    benhnhanId: $("#hidBENHNHANID").val(),
                    hosobenhanid: $("#hidHOSOBENHANID").val(),
                    cboTNTT: $('#cboTAINAN_NGUYENNHANID').val()
                };
                dlgPopup = DlgUtil.buildPopupUrl("dlgTaiNanThuongTich", "divDlg", "manager.jsp?func=../noitru/NTU02D035_Thongtintainanthuongtich", param, "Thông tin tai nạn thương tích", 1000, 460);
                DlgUtil.open("dlgTaiNanThuongTich");
            }
        });
        //L2PT-27682 end
        $("#txtMABHYT")
            .focusout(
                function () {
                    if (opt._mode == '0' && !$('#chkSINHTHETE').is(":checked")) {
                        var sobhyt = $("#txtMABHYT").val().trim().toUpperCase();
                        //$("#tlmiengiam").val(0);
                        // start jira L2PT-1281
                        // the 10 ky tu - QĐ1666
                        if ((sobhyt.length == 10) && (sobhyt.indexOf("_") < 0)) {
                            // neu bat cau hinh NBN_FILLTTHE1666 thi dua vao mathebhyt + ten + nam sinh, lay thong tin bn tu cong
                            if ((_CAUHINH.NBN_FILLTTHE1666 == '1') && $('#txtTENBENHNHAN').val() != '' && $('txtNAMSINH').val() != '') {
                                var ttTheTuCong = getTTBNTuCong($('#txtTENBENHNHAN').val(), $('#txtNAMSINH').val(), sobhyt);
                                fillDataVaoForm(ttTheTuCong);
                            }
                            // lay thong tin tu DB voi 10 ky tu the (su dung ham ben ngoai tru)
                            else {
                                var maBn = getMaBNFromThe10So(sobhyt);
                                if (maBn) {
                                    loadDetail(maBn);
                                }
                            }
                        }
                            // end jira L2PT-1281
                        // the binh thuong (15 ky tu)
                        else if ((sobhyt.length == 15) && (sobhyt.indexOf("_") < 0)) {
                            var obj = new Object();
                            obj.ten = '-1';
                            obj.ngaysinh = '-1';
                            obj.gioitinh = '-1';
                            obj.mabhyt = sobhyt;
                            var _par = [JSON.stringify(obj)];
                            var resultCount = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV008", _par.join('$'));
                            if (resultCount != '0') {
                                var resultCode = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV012", $('#txtMABHYT').val().trim());
                                loadDetail(resultCode);
                                //L2PT-28075 start
                                if ((_CAUHINH.NBN_FILLTTHE_AUTO == '1')) {
                                    var ttTheTuCong = getTTBNTuCong($('#txtTENBENHNHAN').val(), $('#txtNAMSINH').val(), sobhyt);
                                    fillDataVaoForm(ttTheTuCong);
                                }
                                //L2PT-28075 end
                            }
                        }
                        // ma qrcode
                        else if ((sobhyt.length > 15) && (sobhyt.indexOf("|") > -1)) {
                            var sobhyt_catchuoi = sobhyt.split("|");
                            // start jira L2PT-1281
                            // the 10 ky tu - QĐ1666
                            if (sobhyt_catchuoi[0].trim().length == 10 && _CAUHINH.NBN_FILLTTHE1666 == '1') {
                                var tenBenhNhan = convert_utf8totext(sobhyt_catchuoi[1].trim());
                                var namSinh = sobhyt_catchuoi[2].trim();
                                var ttTheTuCong = getTTBNTuCong(tenBenhNhan, namSinh, sobhyt_catchuoi[0].trim());
                                fillDataVaoForm(ttTheTuCong);
                                return;
                            }
                            // end jira L2PT-1281
                            $("#txtMABHYT").val(sobhyt_catchuoi[0].trim());
                            var obj = new Object();
                            obj.ten = '-1';
                            obj.ngaysinh = '-1';
                            obj.gioitinh = '-1';
                            obj.mabhyt = sobhyt_catchuoi[0].trim();
                            var _par = [JSON.stringify(obj)];
                            var resultCount = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV008", _par.join('$'));
                            if (resultCount != '0') {
                                var resultCode = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV012", $('#txtMABHYT').val().trim());
                                loadDetail(resultCode);
                                tinhTuoi(sobhyt_catchuoi[2], 'txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');
                            } else {
                                $("#txtTENBENHNHAN").val(convert_utf8totext(sobhyt_catchuoi[1]));
                                if (sobhyt_catchuoi[2].trim().length > 4) {
                                    $("#txtNGAYSINH").val(sobhyt_catchuoi[2].trim());
                                    $("#txtNGAYSINH").trigger("change");
                                } else {
                                    $("#txtNAMSINH").val(sobhyt_catchuoi[2].trim());
                                    $("#txtNAMSINH").trigger("change");
                                }
                                tinhTuoi(sobhyt_catchuoi[2], 'txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');
                                $("#cboGIOITINHID").val(sobhyt_catchuoi[3] == "1" ? "1" : "2");
                                try {
                                    $("#txtDIACHI_BHYT").val(convert_utf8totext(sobhyt_catchuoi[4]));
                                } catch (e) {
                                    $("#txtDIACHI_BHYT").val("");
                                }
                                $("#txtDIACHI").val($("#txtDIACHI_BHYT").val());
                                var noidk = sobhyt_catchuoi[5].trim().replace(" – ", "").replace("-", "").replace(" ", "").replace(" ", "");
                                $("#txtTKDKKBBD").val(noidk);
                                $('#txtTKDKKBBD').combogrid("setValue", noidk);
                                $("#txtBHYT_BD").val(sobhyt_catchuoi[6]);
                                $("#txtBHYT_KT").val(sobhyt_catchuoi[7]);
                                var matinh = sobhyt_catchuoi[0].trim().substring(3, 5);
                                $("#cboHC_TINHID").find("option[extval1='" + matinh + "']").attr("selected", "selected");
                                $("#txtTKHC_TINH").val($('#cboHC_TINHID' + " option:selected").attr('extval0'));
                                var sql_par1 = [];
                                sql_par1.push({
                                    "name": "[0]",
                                    "value": $("#cboHC_TINHID").val()
                                });
                                ComboUtil.getComboTag("cboHC_HUYENID", "DMDP.001", sql_par1, "", {
                                    extval: true,
                                    value: '',
                                    text: 'Chọn'
                                }, "sql", "", function () {
                                    var mahuyen = sobhyt_catchuoi[0].trim().substring(5, 7);
                                    $("#cboHC_HUYENID").find("option[extval1='" + mahuyen + "']").attr("selected", "selected");
                                    $("#txtTKHC_HUYEN").val($('#cboHC_HUYENID' + " option:selected").attr('extval0'));
                                    sql_par1 = [];
                                    sql_par1.push({
                                        "name": "[0]",
                                        "value": $("#cboHC_HUYENID").val()
                                    });
                                    ComboUtil.getComboTag("cboHC_XAID", "DMDP.001", sql_par1, "", {
                                        extval: true,
                                        value: '',
                                        text: 'Chọn'
                                    }, "sql", "", function () {
                                        $("#cboHC_XAID option").filter(function () {
                                            var diachi = convert_utf8totext(sobhyt_catchuoi[4]);
                                            var sd = diachi.split('-');
                                            return this.text == sd[sd.length - 3].trim();
                                        }).attr('selected', true);
                                        $("#txtTKHC_XA").val($('#cboHC_XAID' + " option:selected").attr('extval0'));
                                    });
                                });
                            }
                            flag_ban_the = true;
                        }
                        //Begin_HaNv_26062019: Mo form check lich su kham chua benh nhu tiepnhan_ngt - L2PT-5921
                        if (_CAUHINH.NGT_SHOW_CHECKCONGBHXH == '1' && $("#cboDOITUONGBENHNHANID").val() == "1" && $("#txtMABHYT").val().trim() != "" &&
                            $("#txtTENBENHNHAN").val().trim() != "" && ($("#txtNGAYSINH").val() != "" || $("#txtNAMSINH").val() != "")) {
                            openPopUpCheckLSKCB(0.85, 0.83);
                        }
                        //End_HaNv_26062019
                        // clone từ bên ngoại trú và sửa cho phù hợp
                        if (_CAUHINH.NGT_CHECKCONG_QUETTHE == "1" && ($("#cboDOITUONGBENHNHANID").val() == "1" || $("#cboDOITUONGBENHNHANID").val() == "6")
                            // && ($('#hidSINHTHEBHYT').val().trim() != "1" || ($('#hidSINHTHEBHYT').val().trim() == "1" && _sinhthete == "3"))
                        ) {
                            var ret1 = _callCheckCong();
                            if (ret1.hasOwnProperty("maKetQua") && ret1.hasOwnProperty("gtTheDen") && ret1.hasOwnProperty("gtTheTu") && ret1.maThe != null && ret1.maThe != 'null' &&
                                ret1.maThe != '') {
                                // sondn L2PT-1038 : Neu ma the 10 ky tu, check cong xong lay ve the 15 ky tu fill lai;
                                if ($("#txtMABHYT").val().length == 10) {
                                    DlgUtil.showMsg("Mã số:" + ret1.maSoBHXH + ". Check từ cổng mã BHYT: " + ret1.maThe);
                                    $("#txtMABHYT").val(ret1.maThe);
                                    // _timKiemBenhNhan($("#txtMABHYT").val().toUpperCase(), '2');
                                    _muchuong_bhyt(opt._deptId, $("#txtMABHYT").val().substr(0, 3).toUpperCase(), $("#cboTUYENID").val());
                                }
                                // end sondn L2PT-1038
                                if (ret1.gioiTinh == null || ret1.gioiTinh == 'null' || ret1.gioiTinh == '') {
                                    $("#txtTKGIOITINH").val(-1);
                                    $("#cboGIOITINHID").val(-1);
                                } else {
                                    $("#txtTKGIOITINH").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                                    $("#cboGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                                }
                                $('#txtBHYT_BD').val(ret1.gtTheTu);
                                $('#txtBHYT_KT').val(ret1.gtTheDen);
                                $("#txtNGAYDU5NAM").val(ret1.ngayDu5Nam);
                                $("#txtDIACHI_BHYT").val(ret1.diaChi);
                                if ($("#txtDIACHI").val() == "") {
                                    $("#txtDIACHI").val(ret1.diaChi); // sondn L2PT-28917
                                }
                                // SONDN 19/03/2020 L2PT-17643
                                for (i = 1; i <= 3; i++) {
                                    if (ret1.maKV == "K" + i) {
                                        $("#cboMAVUNGID").val(i);
                                    }
                                }
                                // END SONDN 19/03/2020 L2PT-17643
                                $("#txtTENBENHNHAN").val(ret1.hoTen.toUpperCase());
                                $("#txtTKDKKBBD").val(ret1.maDKBD);
                                $('#txtTKDKKBBD').combogrid("setValue", ret1.maDKBD);
                                if (ret1.ngaySinh.length == "4") {
                                    $("#txtNAMSINH").val(ret1.ngaySinh);
                                    $("#txtNGAYSINH").val("");
                                    $("#txtNAMSINH").change();
                                } else {
                                    $("#txtNGAYSINH").val(ret1.ngaySinh);
                                    $("#txtNGAYSINH").change();
                                }
                                // sondn L2PT-27548
                                // 003: Thẻ cũ hết giá trị sử dụng nhưng đã được cấp thẻ mới.
                                if (ret1.maKetQua == "003") {
                                    if (ret1.hasOwnProperty("maTheMoi")) {
                                        $("#txtMABHYT").val(ret1.maTheMoi);
                                        $('#txtBHYT_BD').val(ret1.gtTheTuMoi);
                                        $('#txtBHYT_KT').val(ret1.gtTheDenMoi);
                                        $("#txtTKDKKBBD").val(ret1.maDKBDMoi);
                                        $('#txtTKDKKBBD').combogrid("setValue", ret1.maDKBDMoi);
                                        _muchuong_bhyt(opt._deptId, $("#txtMABHYT").val().substr(0, 3).toUpperCase(), $("#cboTUYENID").val());
                                        DlgUtil.showMsg("Bệnh nhân đã được cấp thẻ mới, mã thẻ là: " + ret1.maTheMoi);
                                    } else {
                                        DlgUtil.showMsg("Trong dữ liệu trả về mã thẻ mới không tồn tại, yêu cầu kiểm tra lại thông tin cổng BHXH. ");
                                    }
                                }
                                // end sondn L2PT-27548
                            } else if (ret1 == "0") {
                                // DlgUtil.showMsg("Thiếu thông tin họ tên / mã BHYT / ngày sinh, yêu cầu bổ sung để check cổng");
                                return;
                            } else {
                                DlgUtil.showMsg("BHXH: Không kết nối được với cổng BHXH, yêu cầu kiểm tra lại");
                                return;
                            }
                        }
                    }
                });
        $("#btnChange").on("click", function (e) {
            $('#cboXUTRIID').change();
        });

        $('#cboXUTRIID').on(
            'change',
            function (e) {
                if ($('#cboXUTRIID').val() != '' && ($('#cboKETQUADIEUTRIID').val() == null || $('#cboKETQUADIEUTRIID').val() == '')) {
                    DlgUtil.showMsg('Vui lòng chọn kết quả điều trị trước khi xử trí');
                    $('#cboXUTRIID').val('');
                    return;
                }
                if (_CAUHINH_NBN[0].NTU_NBN_VALIDATE_LANHDAO == '1') {
                    if ($('#cboXUTRIID').val() != '' && ($('#cboLANHDAOKHOAID').val() == null || $('#cboLANHDAOKHOAID').val() == '')) {
                        DlgUtil.showMsg('Vui lòng chọn lãnh đạo khoa trước khi xử trí');
                        $('#cboXUTRIID').val('');
                        return;
                    }
                    if ($('#cboXUTRIID').val() != '' && ($('#cboLANHDAOVIENID').val() == null || $('#cboLANHDAOVIENID').val() == '')) {
                        DlgUtil.showMsg('Vui lòng chọn lãnh đạo bệnh viện trước khi xử trí');
                        $('#cboXUTRIID').val('');
                        return;
                    }
                }
                $('#txtTKXUTRI').val($('#cboXUTRIID').val());
                if ($('#cboXUTRIID').val() == '') {
                    $('#txtTHOIGIANKETTHUC').val('');
                } else {
                    if ($('#txtTHOIGIANKETTHUC').val() == null || $('#txtTHOIGIANKETTHUC').val() == '') {
                        $('#txtTHOIGIANKETTHUC').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
                    }
                }
                $('#divChuyenKhoa').hide();
                $('#divTGTVTV').hide();
                $('#divChuyenVien').hide();
                //START L2PT-4796
                var xtid = $('#cboXUTRIID').val();
                //START L2PT-4796
                var xtid = $('#cboXUTRIID').val();
                if (xtid == '7' && _CAUHINH.XUTRI_KHONGNHAP_TUVONG == '1') {
                    return;
                }
                var popupArray = [["1", "NGT02K007_Thongtin_Ravien:Thông tin ra viện"], ["2", "NGT02K007_Thongtin_Ravien:Thông tin ra viện"], ["3", ""], ["4", ""], ["5", ""],
                    ["6", "NGT02K009_Chuyenvien:Chuyển viện"], ["7", "NGT02K010_Tuvong:Thông tin tử vong"], ["8", "NGT02K008_Thongtin_Lichkham:Thông tin lịch hẹn"], ["9", ""],
                    ["10", "NGT02K008_Thongtin_Lichkham:Thông tin lịch hẹn"]];
                var popupMap = new Map(popupArray);
                var url = popupMap.get($('#cboXUTRIID').val());
                //END L2PT-4796
                //START L2PT-18912
                if (_CAUHINH.HIS_DONGBO_ICD == '1' && xtid != '5' && xtid != '') {
                    var _par = [$('#hidKHAMBENHID').val(), $('#hidHOSOBENHANID').val(), xtid, $('#cboCHANDOANRAVIENID').val()];
                    var cd_th = jsonrpc.AjaxJson.ajaxCALL_SP_S('NTU01H002.EV016', _par.join('$'));
                    if ($('#txtCHANDOANRAVIEN_KEMTHEO').val() == '' && cd_th != '' && cd_th != '-1' && cd_th != '0') {
                        $('#txtCHANDOANRAVIEN_KEMTHEO').val(cd_th);
                    }
                }
                //END L2PT-18912
                if (typeof url != "undefined" && url != "") {
                    var url_arr = url.split(":");
                    var _chandoan = $('#cboCHANDOANRAVIENID').val() + '-' + $('#cboCHANDOANRAVIENID option:selected').text() + ($('#txtGHICHU_BENHCHINH_RAVIEN').val() == "" ? "" : "(") +
                        $('#txtGHICHU_BENHCHINH_RAVIEN').val() + ($('#txtGHICHU_BENHCHINH_RAVIEN').val() == "" ? "" : ")") + ($('#txtCHANDOANRAVIEN_KEMTHEO').val() == "" ? "" : ";") +
                        $('#txtCHANDOANRAVIEN_KEMTHEO').val();
                    var param = {
                        mabenhnhan: $('#txtMABENHNHAN').val(),
                        tenbenhnhan: $('#txtTENBENHNHAN').val(),
                        nghenghiep: $("#cboNGHENGHIEPID option:selected").text(),
                        diachi: _createAddress(["txtSONHA", "cboDIABANID", "cboHC_XAID", "cboHC_HUYENID", "cboHC_TINHID"], ","),
                        namsinh: $('#txtNAMSINH').val(),
                        khambenhid: $('#hidKHAMBENHID').val(),
                        chandoan: $('#CHANDOANRAVIEN').val(),
                        ngaytiepnhan: $('#txtVAOKHOA').val(),
                        //tuyennx_add_start_20190128 L2PT-1114
                        benhnhanid: $('#hidBENHNHANID').val(),
                        hosobenhanid: $('#hidHOSOBENHANID').val(),
                        //tuyennx_add_end_20190128
                        checkkhoa: _checkkhoa, //L2PT-75282
                        chandoan: _chandoan,
                        doituongbenhnhanid: $("#cboDOITUONGBENHNHANID").val(),
                        capnhat: '0'
                    };
                    _objData = {};
                    if ($('#cboXUTRIID').val() == '6' && opt.hospitalId != '32260') {
                        $('#divChuyenVien').show();
                        DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/" + url_arr[0], param, url_arr[1], 1200, 440, false);
                    } else if ($('#cboXUTRIID').val() == '6' && opt.hospitalId == '32260') {
                        $('#divChuyenVien').show();
                        DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/" + url_arr[0], param, url_arr[1], window.innerWidth * 0.97, window.innerHeight * 0.97, false);
                    } else {
                        DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/" + url_arr[0], param, url_arr[1], 1200, 500, false);
                    }
                    DlgUtil.open("dlgXuTri");
                } else {
                    if ($('#cboXUTRIID').val() == '5') {
                        $('#divChuyenKhoa').show();
                        // HaNv_20170807 : Xử trí chuyển khoa -> không load ra khoa hiện tại
                        if ($.isNumeric(opt._type) && opt._type == 3) {
                            if (getCauHinhChiHienThiPhongDTNTUKhiXuTri() == 1) {
                                // chi load khoa dieu tri ngoai tru
                                ComboUtil.getComboTag("cboCHUYENKHOA", "KHOA.DTNGT2", [{
                                    "name": "[0]",
                                    "value": opt._deptId
                                }], "", {
                                    value: 0,
                                    text: ''
                                }, "sql"); //L2PT-24773
                            } else {
                                // load khoa dieu tri ngoai tru va noi tru hoac tat ca (phu thuoc cau hinh HIS_LOAIKHOA_DTNGT)
                                generateCombBoxKhoaDTNT();
                            }
                        } else {
                            generateCombBoxKhoaNoitru();
                        }
                        // End HaNv_20170807
                        $('#txtTKKHOA').focus();
                    }
                    if (['3', '4', '9', '11'].includes($('#cboXUTRIID').val() + "")) {
                        $('#divTGTVTV').show();
                        //Begin_HaNv_20032018: show popup thông tin ra viện với xử trí bỏ về
                        if ($('#cboXUTRIID').val() == '3' && _CAUHINH.XUTRI_BOVE_SHOW_POPUP == '1') {
                            var param = {
                                mabenhnhan: $('#txtMABENHNHAN').val(),
                                tenbenhnhan: $('#txtTENBENHNHAN').val(),
                                nghenghiep: $("#cboNGHENGHIEPID option:selected").text(),
                                diachi: _createAddress(["txtSONHA", "cboDIABANID", "cboHC_XAID", "cboHC_HUYENID", "cboHC_TINHID"], ","),
                                namsinh: $('#txtNAMSINH').val(),
                                khambenhid: $('#hidKHAMBENHID').val(),
                                chandoan: $('#CHANDOANRAVIEN').val(),
                                //tuyennx_add_start_20190128 L2PT-1114
                                benhnhanid: $('#hidBENHNHANID').val(),
                                hosobenhanid: $('#hidHOSOBENHANID').val(),
                                //tuyennx_add_end_20190128
                                checkkhoa: _checkkhoa, //L2PT-75282
                                ngaytiepnhan: $('#txtVAOKHOA').val()
                            };
                            DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K007_Thongtin_Ravien", param, "Thông tin bỏ về", 1200, 440, false);
                            DlgUtil.open("dlgXuTri");
                        } else {
                            if (_CAUHINH.NTU_NBN_GPURVKXTK == '1') {
                                $('#divTGTVTV').hide();
                                goiPopupXuTriRaVien();
                            } else {
                                $('#txtTHOIGIANTVTV').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
                                $('#txtSONGAYDIEUTRI').val(tinhngayDt($('#hidNGAYTIEPNHAN').val(), $('#txtTHOIGIANTVTV').val(), $('#cboKETQUADIEUTRIID').val()));
                            }
                        }
                        //End_HaNv_20032018
                    }
                }
                // start jira L2PT-8406
                // khi chọn chuyển khoa, có thể cho phép chuyển từ điều trị ngoại trú sang nội trú
                if (_CAUHINH.NBN_CHUYENKHOA_DTNTNOITRU == '1' && opt._type == '3' && $('#cboXUTRIID').val() == '5') {
                    $('#divCHUYENKHOADTNTNOITRU').show();
                    $("#chkISCHUYENKHOADTNTNOITRU").prop("checked", false);
                } else {
                    $('#divCHUYENKHOADTNTNOITRU').hide();
                    $("#chkISCHUYENKHOADTNTNOITRU").prop("checked", false);
                }
                // end jira L2PT-8406

                if ($('#cboXUTRIID').val() == '5' || $('#cboXUTRIID').val() == '') {
                    $('#lblLANHDAOKHOA').removeClass('required');
                    $("#cboLANHDAOKHOAID").removeAttr("valrule");
                    $('#lblLANHDAOVIEN').removeClass('required');
                    $("#cboLANHDAOVIENID").removeAttr("valrule");
                } else {
                    $('#lblLANHDAOKHOA').addClass('required');
                    $("#cboLANHDAOKHOAID").attr("valrule", "Lãnh đạo khoa,required");
                    $('#lblLANHDAOVIEN').addClass('required');
                    $("#cboLANHDAOVIENID").attr("valrule", "Lãnh đạo viện,required");
                }
            });
        $('#txtTHOIGIANTVTV').on('change', function (e) {
            $('#txtTHOIGIANKETTHUC').val($('#txtTHOIGIANTVTV').val());
            $('#txtSONGAYDIEUTRI').val(tinhngayDt($('#hidNGAYTIEPNHAN').val(), $('#txtTHOIGIANTVTV').val(), $('#cboKETQUADIEUTRIID').val()));
            return;
        });
        $('#chkISCHUYENKHOADTNTNOITRU').on('change', function (e) {
            if ($('#chkISCHUYENKHOADTNTNOITRU').is(":checked")) {
                generateCombBoxKhoaNoitru(1);
            } else {
                generateCombBoxKhoaDTNT();
            }
        });
        //L2PT-114300
        $('#cboLOAITHETAM').on('change', function (e) {
            if ($('#cboLOAITHETAM').val() == 1) {
                if (_kiemTraSinhSoTheBHYT()) {
                    var matinh = (typeof $('#cboHC_TINHID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_TINHID' + " option:selected").attr('extval1') : "");
                    $('#txtMABHYT').val('TE1' + matinh);
                    $('#cboBHYT_LoaiID').change();
                    var kcbbd = $('#cboHC_TINHID' + " option:selected").attr('extval1') + "000"; 			// ma tinh + 000;
                    $('#txtMA_KCBBD').val(kcbbd);
                    $('#txtMA_KCBBD').combogrid("setValue", kcbbd);
                    $('#hidSINHTHEBHYT').val('1');
                    $('#txtDIACHI_BHYT').val($('#txtDIACHI').val());
                } else {
                    $("#cboLOAITHETAM").val('');
                    $("#txtMABHYT").val('');
                }
            } else if ($('#cboLOAITHETAM').val() == 2) {
                var matinh = (typeof $('#cboHC_TINHID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_TINHID' + " option:selected").attr('extval1') : "");
                $('#txtMABHYT').val('HG4' + matinh);
            } else {
                $("#txtMABHYT").val('');
            }
        });
        $('#chkSINHTHETE').on('change', function (e) {
            if ($('#chkSINHTHETE').is(":checked")) {
                //Beg_HaNv_270622: Cấu hình thiết lập không tạo thẻ tạm cho BN khi check sinh thẻ TE - L2PT-21461
                if (_CAUHINH.NBN_NOT_SINHTHE_TE == '1') {
                    return;
                }
                if (_CAUHINH.HIS_QD130 == '1') {
                    $('#cboLOAITHETAM').prop("disabled", false);
                    $("#cboLOAITHETAM").val(1);//L2PT-114300
                    $("#cboLOAITHETAM").change();
                    return;
                }
                //End_HaNv_270622
                if (_kiemTraSinhSoTheBHYT()) {
                    $('#txtMABHYT').val(_sinhSoTheBHYT());
                    var kcbbd = $('#cboHC_TINHID' + " option:selected").attr('extval1') + "000";
                    $('#txtTKDKKBBD').val(kcbbd);
                    $('#txtTKDKKBBD').combogrid("setValue", kcbbd);
                    var comp = $('#txtNGAYSINH').val().split('/');
                    var d = parseInt(comp[0], 10);
                    var m = parseInt(comp[1], 10);
                    var y = parseInt(comp[2], 10);
                    var dateFrom = new Date(y, m - 1, d);
                    var dateTo = new Date(y + 6, m - 1, d - 1);
                    $('#txtBHYT_BD').val(moment(dateFrom).format('DD/MM/YYYY'));
                    $('#txtBHYT_KT').val(moment(dateTo).format('DD/MM/YYYY'));
                    $('#txtMABHYT').prop("disabled", true);
                    // start jira 20732
                    if (_CAUHINH.NGT_THOIGIAN_THETE == '1') {
                        var ngayHienTai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
                        $('#txtBHYT_BD').val(ngayHienTai);
                        $('#txtBHYT_KT').val(ngayHienTai);
                    }
                    // end jira 20732
                } else {
                    $("#chkSINHTHETE").prop("checked", false);
                    $('#txtMABHYT').prop("disabled", false);
                    $('#txtMABHYT').val("");
                }
            } else {
                $('#txtMABHYT').prop("disabled", false);
                $('#txtMABHYT').val("");
                $('#txtBHYT_BD').val("");
                $('#txtBHYT_KT').val("");
            }
        });
        $('#btnLichsuTheoBHYT').on(
            'click',
            function (e) {
                var paramInput = {
                    MABHYT: $('#txtMABHYT').val(),
                    TENBENHNHAN: $('#txtTENBENHNHAN').val(),
                    NGAYSINH: $('#txtNGAYSINH').val() == "" ? $("#txtNAMSINH").val().trim() : $('#txtNGAYSINH').val(),
                    QRCODE: '',
                    GIOITINH: $("#cboGIOITINHID").val(),
                    MAKCBBD: $("#cboMAKCBBD").val() == null || $("#cboMAKCBBD").val() == 'null' ? "" : $("#cboMAKCBBD").val(),
                    TUNGAY: $('#txtBHYT_BD').val(),
                    DENNGAY: $('#txtBHYT_KT').val()
                };
                DlgUtil.buildPopupUrl("divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB", paramInput, "Thông tin lịch sử điều trị bệnh nhân", window.innerWidth * 0.95,
                    window.innerHeight * 0.93);
                var parent = DlgUtil.open("divDlgDDT");
            });
        $('#chkBH5NAM').on('change', function (e) {
            if ($('#chkBH5NAM').is(":checked")) {
                $('#chkBH6THANG').prop("disabled", false);
            } else {
                $('#chkBH6THANG').prop("disabled", true);
                $('#chkBH6THANG').prop('checked', false);
            }
        });
        $('#chkCHECKCONG').on('change', function (e) {
            if ($('#chkCHECKCONG').is(":checked") && _CAUHINH.NGT_SHOW_CHECKCONGBHXH == '1') {
                openPopUpCheckLSKCB(0.95, 0.93);
            }
        });
        // start jira 9601
        $('#cboLOAIBENHANID').change(function () {
            // cau hinh cho phep thay doi header form theo loai benh an
            if (_CAUHINH.HIS_NBN_CHVLBA == '1') {
                // Start L2PT-132353 QuyHTA
                if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_BATCHONLOAIBENHAN') == '1' && $('#cboLOAIBENHANID').val() == '0') {
                    $('#h3BenhAn').text('');
                } else {
                    $('#h3BenhAn').text($("#cboLOAIBENHANID option:selected").text());
                }
                // End L2PT-132353 QuyHTA
                //$('#h3BenhAn').text($("#cboLOAIBENHANID option:selected").text());
            }
        });
        // end jira 9601
        $('#txtNGAYDU5NAM').on('change', function (e) {
            // start jira 3100
            if (_CAUHINH.NBN_CKNDCTKCC5N == '1' && !checkDate($('#txtNGAYDU5NAM').val())) {
                $("#txtNGAYHUONG_5NAM6THANG").val("");
                $("#txtNGAYHUONG_5NAM6THANG").blur();
                return false;
            }
            // end jira 3100
        });
        $('#txtNGAYHUONG_5NAM6THANG').on('keyup', function (e) {
            // start jira 3100
            if (_CAUHINH.NBN_CKNDCTKCC5N == '1' && !checkDate($('#txtNGAYDU5NAM').val())) {
                DlgUtil.showMsg('Phải nhập TG đủ 5 năm trước khi nhập TG hưởng ĐCT', function () {
                });
                $("#txtNGAYHUONG_5NAM6THANG").val("");
                $("#txtNGAYHUONG_5NAM6THANG").blur();
                return false;
            }
            // end jira 3100
        });
        $('#btnSelectNGAYHUONG5NAM6THANG').on('click', function (e) {
            // start jira 3100
            if (_CAUHINH.NBN_CKNDCTKCC5N == '1' && !checkDate($('#txtNGAYDU5NAM').val())) {
                DlgUtil.showMsg('Phải nhập TG đủ 5 năm trước khi nhập TG hưởng ĐCT');
                return false;
            } else {
                NewCssCal('txtNGAYHUONG_5NAM6THANG', 'ddMMyyyy', 'dropdown', false, '24', false);
            }
            // end jira 3100
        });
        //start DoanPV_20210630 xử lý sự kiện ký số/điện tử
        $('#btnKyCa').bindOnce("click", function () {
            isKyCa = true;
            printCaRv = false;
            printCaCv = false;
            printCaTv = false;
            printCaHen = false;
            if ($('#cboXUTRIID').val() == '1' || $('#cboXUTRIID').val() == '2') {
                printCaRv = true;
            } else if ($('#cboXUTRIID').val() == '6') {
                printCaCv = true;
            } else if ($('#cboXUTRIID').val() == '7') {
                printCaTv = true;
            } else if ($('#cboXUTRIID').val() == '8') {
                printCaHen = true;
            }
            $('#btnSave').click();
        });
        $("#btnHuyCa").on("click", function (e) {
            isKyCa = true;
            printCaRv = false;
            printCaCv = false;
            printCaTv = false;
            printCaHen = false;
            if ($('#cboXUTRIID').val() == '1' || $('#cboXUTRIID').val() == '2') {
                printCaRv = true;
            } else if ($('#cboXUTRIID').val() == '6') {
                printCaCv = true;
            } else if ($('#cboXUTRIID').val() == '7') {
                printCaTv = true;
            } else if ($('#cboXUTRIID').val() == '8') {
                printCaHen = true;
            }
            _caRpt('2');
        });
    }

    function goiPopupXuTriRaVien() {
        var _chandoan = $('#cboCHANDOANRAVIENID').val() + '-' + $('#cboCHANDOANRAVIENID option:selected').text() + ($('#txtGHICHU_BENHCHINH_RAVIEN').val() == "" ? "" : "(") +
            $('#txtGHICHU_BENHCHINH_RAVIEN').val() + ($('#txtGHICHU_BENHCHINH_RAVIEN').val() == "" ? "" : ")") + ($('#txtCHANDOANRAVIEN_KEMTHEO').val() == "" ? "" : ";") +
            $('#txtCHANDOANRAVIEN_KEMTHEO').val();
        var param = {
            mabenhnhan: $('#txtMABENHNHAN').val(),
            tenbenhnhan: $('#txtTENBENHNHAN').val(),
            nghenghiep: $("#cboNGHENGHIEPID option:selected").text(),
            diachi: _createAddress(["txtSONHA", "cboDIABANID", "cboHC_XAID", "cboHC_HUYENID", "cboHC_TINHID"], ","),
            namsinh: $('#txtNAMSINH').val(),
            khambenhid: $('#hidKHAMBENHID').val(),
            chandoan: $('#CHANDOANRAVIEN').val(),
            ngaytiepnhan: $('#txtVAOKHOA').val(),
            //tuyennx_add_start_20190128 L2PT-1114
            benhnhanid: $('#hidBENHNHANID').val(),
            hosobenhanid: $('#hidHOSOBENHANID').val(),
            //tuyennx_add_end_20190128
            chandoan: _chandoan,
            checkkhoa: _checkkhoa, //L2PT-75282
            doituongbenhnhanid: $("#cboDOITUONGBENHNHANID").val()
        };
        _objData = {};
        DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K007_Thongtin_Ravien", param, "Thông tin ra viện", 1200, 500, false);
        DlgUtil.open("dlgXuTri");
    }

    // kiểm tra control trước khi lưu thông tin
    function _kiemTra() {
        var isDoiTuongBhyt = false;
        if ($('#cboDOITUONGBENHNHANID').val() == '1') {
            isDoiTuongBhyt = true;
        }
        var b_tu = '';
        var b_den = '';
        var b_ngaykham = '';
        var b_ngaysinh = '';
        var b_ngaykham_daydu = '';
        if ($('#txtBHYT_BD').val().length >= 10) {
            b_tu = $('#txtBHYT_BD').val().substr(6, 4) + $('#txtBHYT_BD').val().substr(3, 2) + $('#txtBHYT_BD').val().substr(0, 2);
        }
        if ($('#txtBHYT_KT').val().length >= 10) {
            b_den = $('#txtBHYT_KT').val().substr(6, 4) + $('#txtBHYT_KT').val().substr(3, 2) + $('#txtBHYT_KT').val().substr(0, 2);
        }
        if ($('#txtVAOKHOA').val().length >= 10) {
            b_ngaykham = $('#txtVAOKHOA').val().substr(6, 4) + $('#txtVAOKHOA').val().substr(3, 2) + $('#txtVAOKHOA').val().substr(0, 2);
            b_ngaykham_daydu = $('#txtVAOKHOA').val().substr(6, 4) + $('#txtVAOKHOA').val().substr(3, 2) + $('#txtVAOKHOA').val().substr(0, 2) + $('#txtVAOKHOA').val().substr(11, 2) +
                $('#txtVAOKHOA').val().toString().substr(14, 2);
        }
        if ($('#txtNGAYSINH').val().trim().length > 0 && !checkDate($('#txtNGAYSINH').val())) {
            DlgUtil.showMsg("Ngày sinh " + $.i18n("date_type_invalid"), function () {
                $('#txtNGAYSINH').focus();
            });
            return false;
        }
        if ($('#txtNGAYSINH').val().trim().length > 0 && b_ngaysinh > b_ngaykham) {
            DlgUtil.showMsg($.i18n("ngay_sinh_nho_hon_ngay_kham"), function () {
                $('#txtNGAYSINH').focus();
            });
            return false;
        }
        if ($('#cboDANTOCID').val() == null) {
            DlgUtil.showMsg("Dân tộc " + $.i18n("require"), function () {
                $('#cboDANTOCID').focus();
            });
            return false;
        }
        if ($('#cboQUOCGIAID').val() == null) {
            DlgUtil.showMsg("Quốc tịch " + $.i18n("require"), function () {
                $('#cboQUOCGIAID').focus();
            });
            return false;
        }
        if ($('#cboHC_TINHID').val() == null || $('#cboHC_TINHID').val() == "") {
            DlgUtil.showMsg("Tỉnh " + $.i18n("require"), function () {
                $('#cboHC_TINHID').focus();
            });
            return false;
        }
        if (_CAUHINH.HIS_NBN_REQUIRE_HUYENXA == '1') {
            if ($('#cboHC_HUYENID').val() == null || $('#cboHC_HUYENID').val() == "") {
                DlgUtil.showMsg("Huyện " + $.i18n("require"), function () {
                    $('#cboHC_HUYENID').focus();
                });
                return false;
            }
            if ($('#cboHC_XAID').val() == null || $('#cboHC_XAID').val() == "") {
                DlgUtil.showMsg("Xã " + $.i18n("require"), function () {
                    $('#cboHC_XAID').focus();
                });
                return false;
            }
        }
        //L2PT-58377 start
        if (_CAUHINH.HIS_NBN_SDT == '1') {
            if ($("#txtDIENTHOAI").val().trim() == "") {
                DlgUtil.showMsg("Số điện thoại bệnh nhân không được để trống!");
                $("#txtDIENTHOAI").focus();
                return false;
            }
            if ($("#txtDIENTHOAI").val().trim().length > 11 || $("#txtDIENTHOAI").val().trim().length < 8 || isNaN($("#txtDIENTHOAI").val().trim())) {
                DlgUtil.showMsg("Số điện thoại bệnh nhân không đúng yêu cầu nhập lại!");
                $("#txtDIENTHOAI").focus();
                return false;
            }
        }
        //L2PT-58377 end
        if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'QD_4750') == 1) {
            if ($("#txtSOCMTND").val().trim() != "" && !checkCMND($('#txtSOCMTND').val())) {
                DlgUtil.showMsg("Số CMTND/CCCD không đúng ký tự hoặc có chứa ký tự đặc biệt, vui lòng kiểm tra lại! ");
                $("#txtSOCMTND").focus();
                return false;
            }

            if ($("#txtDIENTHOAI").val().trim() != "" && ($("#txtDIENTHOAI").val().trim().length > 15 || $("#txtDIENTHOAI").val().trim().length < 8 || isNaN($("#txtDIENTHOAI").val().trim()))) {
                DlgUtil.showMsg("Số điện thoại không đúng định dạng ");
                $("#txtDIENTHOAI").focus();
                return false;
            }

            $('#lblSINHTHETE').html('Thẻ tạm');//L2PT-114300
        }
        if (_CAUHINH.NTU_NBN_SHOWLYDOVV == '1' && opt._mode == '0' && opt._type != '3') {
            if ($('#txtLY_DO_VNT').val() == "") {
                DlgUtil.showMsg("Lý do vào viện không được để trống!");
                $("#txtLY_DO_VNT").focus();
                return false;
            }
            if ($("#txtLY_DO_VNT").val().length > parseInt(_CAUHINH.HIS_GIOIHANKYTU_LYDOVNT)) {
                DlgUtil.showMsg('Lý do vào viện vượt quá ' + parseInt(_CAUHINH.HIS_GIOIHANKYTU_LYDOVNT) + ' ký tự!');
                $("#txtLY_DO_VNT").focus();
                return false;
            }
        }
        if (isDoiTuongBhyt && $('#txtMABHYT').val().trim().length == 0) {
            DlgUtil.showMsg("Mã thẻ BHYT " + $.i18n("require"), function () {
                $('#txtMABHYT').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt == "1" && $('#txtBHYT_BD').val().trim().length == 0) {
            DlgUtil.showMsg("Từ ngày BHYT " + $.i18n("require"), function () {
                $('#txtBHYT_BD').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && $('#txtBHYT_KT').val().trim().length == 0 && !$("#chkCHECKCONG").prop('checked')) {
            DlgUtil.showMsg("Đến ngày BHYT " + $.i18n("require"), function () {
                $('#txtBHYT_KT').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && $('#txtBHYT_BD').val().trim().length > 0 && !checkDate($('#txtBHYT_BD').val())) {
            DlgUtil.showMsg("Từ ngày BHYT " + $.i18n("date_type_invalid"), function () {
                $('#txtBHYT_BD').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && $('#txtBHYT_KT').val().trim().length > 9 && !checkDate($('#txtBHYT_KT').val())) {
            DlgUtil.showMsg("Đến ngày BHYT " + $.i18n("date_type_invalid"), function () {
                $('#txtBHYT_KT').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && opt._mode == '0' && $('#txtBHYT_KT').val().trim().length > 9 && b_den < b_ngaykham) {
            DlgUtil.showMsg("Thẻ BHYT hết hạn", function () {
                $('#txtBHYT_KT').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && $('#txtBHYT_KT').val().trim().length > 9 && !compareDate($('#txtBHYT_BD').val(), $('#txtBHYT_KT').val(), 'DD/MM/YYYY')) {
            DlgUtil.showMsg("Từ ngày " + $.i18n("smaller_date") + " đến ngày", function () {
                $('#txtBHYT_BD').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && ($('#cboDKKBBDID').val() == "" || $('#cboDKKBBDID').val() == null)) {
            DlgUtil.showMsg("Nơi ĐKKCB " + $.i18n("require"), function () {
                $('#txtTKDKKBBD').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && $('#cboTUYENID').val() == "") {
            DlgUtil.showMsg("Tuyến " + $.i18n("require"), function () {
                $('#cboTUYENID').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && $('#txtDIACHI_BHYT').val().trim() == "") {
            DlgUtil.showMsg("Địa chỉ BHYT " + $.i18n("require"), function () {
                $('#txtDIACHI_BHYT').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && $('#txtMABHYT').val().trim().length != 15) {
            DlgUtil.showMsg("Thẻ bảo hiểm không đủ 15 ký tự", function () {
                $('#txtMABHYT').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && $('#txtMABHYT').val().trim().length == 15) {
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.EV005", $('#txtMABHYT').val().trim().substring(0, 3));
            if (result == '0') {
                DlgUtil.showMsg("Mã đối tượng bảo hiểm " + $.i18n("date_type_invalid"), function () {
                    $('#txtMABHYT').focus();
                });
                return false;
            }
            if (opt._mode == '0' && $('#cboHINHTHUCVAOVIENID').val() != '2') {
                var obj = new Object();
                obj.ten = '-1';
                obj.ngaysinh = '-1';
                obj.gioitinh = '-1';
                obj.mabhyt = nvl($('#txtMABHYT').val(), '-1');
                var _par = [JSON.stringify(obj)];
                var resultCount = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV008", _par.join('$'));
                if (resultCount != '0') {
                    //Begin_HaNv_06072018: thong bao mabenhan trung the BHYT - L2DKHN-846
                    var param = [nvl($('#hidBENHNHANID').val(), '-1'), $('#txtMABHYT').val().trim()];
                    var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV010", param.join('$'));
                    if (result != '0' && result != '') {
                        DlgUtil.showMsg('Đã tồn tại bệnh nhân có mã bệnh án ' + result + ' sử dụng thẻ này');
                        return false;
                    }
                    //End_HaNv_06072018
                }
            } else if (opt._mode == '1') {
                //Begin_HaNv_06072018: thong bao mabenhan trung the BHYT - L2DKHN-846
                var param = [nvl($('#hidBENHNHANID').val(), '-1'), $('#txtMABHYT').val().trim()];
                var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV010", param.join('$'));
                if (result != '0' && result != '') {
                    DlgUtil.showMsg('Đã tồn tại bệnh nhân có mã bệnh án ' + result + ' sử dụng thẻ này');
                    return false;
                }
                //End_HaNv_06072018
            }
            var _parKhoa = [$('#txtMABHYT').val().substring(0, 3)];
            var resultKhoa = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CHECK.THE.KHOA", _parKhoa.join('$'));
            if (resultKhoa == '1') {
                DlgUtil.showMsg('Mã đầu thẻ đã bị khóa');
                return false;
            } else if (resultKhoa == '2') {
                DlgUtil.showMsg('Mã đầu thẻ không tồn tại');
                return false;
            }

            if (opt._mode == '0' && $("#cboTUYENID").val() == "2" && _CAUHINH_NBN[0].NTU_NBN_CHECK_GIAYTOCT && _CAUHINH_NBN[0].NTU_NBN_CHECK_GIAYTOCT == '1') {
                if ($('#txtSOCHUYENTUYEN').val() == '') {
                    DlgUtil.showMsg('Thiếu thông tin số chuyển tuyến đối với BN nhập viện đúng tuyến giới thiệu');
                    return false;
                }
            }
        }
        if ((typeof opt._submode != 'undefined' && opt._submode == '2') || opt._mode == '0') {
            var modeFunc = opt._mode == '0' ? '0' : '1';
            // START L2PT-29894 không check với trường hợp bn đang điều trị ngoại trú
            //if (isDoiTuongBhyt) {
            if (isDoiTuongBhyt && !isDangDTNTCheckMaBA()) {
                // END L2PT-29894
                var _parCheck = [$('#hidTIEPNHANID').val(), $('#txtMABHYT').val(), $("#hidHOSOBENHANID").val(), $('#txtVAOKHOA').val(), modeFunc];
                var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV015", _parCheck.join('$'));
                if (resultCheck != '1' && resultCheck != '-1') {
                    DlgUtil.showMsg("Có mã BA " + resultCheck + " sử dụng thẻ trong thời gian tiếp nhận bệnh nhân");
                    return false;
                }
            }
        }
        //Begin_HaNv_26072018: Nhap benh nhan tu khu tiep don truoc khi vao khoa - L2DKBD-1312
        if (opt._modeTiepDon != '1') {
            if ($('#cboKHOAID').val() == null || $('#cboKHOAID').val() == '') {
                DlgUtil.showMsg('Chưa chọn buồng phòng');
                $('#cboKHOAID').focus();
                return false;
            }
        }
        //End_HaNv_26072018
        if ($.find("[name='NOIGIOITHIEU']:checked").length <= 0) {
            DlgUtil.showMsg('Chưa chọn nơi giới thiệu');
            return false;
        }
        if (configBacSi == '1' && ($('#cboBACSIID').val() == null || $('#cboBACSIID').val() == '')) {
            DlgUtil.showMsg('Chưa chọn bác sĩ điều trị');
            $('#cboBACSIID').focus();
            return false;
        }
        if (opt._mode == '2') {
            if ($('#cboCHANDOANRAVIENID').val() == null || $('#cboCHANDOANRAVIENID').val() == '') {
                DlgUtil.showMsg("Chưa chọn bệnh chính");
                return false;
            }
            if ($('#txtCHANDOANRAVIEN_KEMTHEO').val() == null || $('#txtCHANDOANRAVIEN_KEMTHEO').val().length > 2000) {
                DlgUtil.showMsg("Bệnh kèm theo vượt quá giới hạn 2000 ký tự", function () {
                    $('#txtCHANDOANRAVIEN_KEMTHEO').focus();
                });
                return false;
            }
            if (!['3', '4', '5', '9', '11'].includes($('#cboXUTRIID').val() + "") && _objData != null) {
                if (typeof _objData.SAVE == "undefined" || _objData.SAVE == null || _objData.SAVE == "") {
                    DlgUtil.showMsg("Chưa lưu thông tin xử trí");
                    return false;
                }
            }
            if (!['3', '4', '5', '9', '11'].includes($('#cboXUTRIID').val() + "") && $.isEmptyObject(_objData)) {
                if (!($('#cboXUTRIID').val() == '7' && _CAUHINH.XUTRI_KHONGNHAP_TUVONG == '1')) {
                    DlgUtil.showMsg("Chưa có dữ liệu xử trí");
                    return false;
                }
            }
            //L2PT-27682 start
            if (_CAUHINH.HIS_NBN_TNTT == '1') {
                if ($('#cboTAINAN_NGUYENNHANID').val() == null || $('#cboTAINAN_NGUYENNHANID').val() == '') {
                    DlgUtil.showMsg("Chưa chọn nguyên nhân tai nạn thương tích");
                    return false;
                }
                if ($('#cboTAINAN_NGUYENNHANID').val() != '19') {
                    sql_par = [];
                    sql_par.push({
                        "name": "[0]",
                        "value": opt._hosobenhanId
                    });
                    data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU035.TNTT.TT1", sql_par);
                    rows = JSON.parse(data);
                    if (rows.length <= 0) {
                        DlgUtil.showMsg("Chưa lưu thông tin tai nạn thương tích");
                        $('#txtTKNGUYENNHAN').val('');
                        $('#cboTAINAN_NGUYENNHANID').val('');
                        return false;
                    }
                }
            }
            //L2PT-27682 end
            if (['3', '4', '9', '11'].includes($('#cboXUTRIID').val() + "") && _CAUHINH.NTU_NBN_GPURVKXTK != '1') {
                if ($('#txtTHOIGIANTVTV').val().trim() == '') {
                    DlgUtil.showMsg('Thời gian về không được để trống', function () {
                        $('#txtTHOIGIANTVTV').focus();
                    });
                    return false;
                }
                if (!compareDate($('#txtVAOKHOA').val(), $('#txtTHOIGIANTVTV').val().trim(), 'DD/MM/YYYY HH:mm:ss')) {
                    DlgUtil.showMsg('Thời gian về trước ngày vào viện');
                    return false;
                }
            }
            if ($('#cboXUTRIID').val() == '5') {
                if ($('#cboCHUYENKHOA').val().trim() == '0') {
                    DlgUtil.showMsg('Vui lòng chọn khoa để chuyển', function () {
                        $('#cboCHUYENKHOA').focus();
                    });
                    return false;
                }
            }
            if ($('#cboKETQUADIEUTRIID').val() == null || $('#cboKETQUADIEUTRIID').val() == '') {
                DlgUtil.showMsg("Chưa chọn kết quả điều trị");
                return false;
            }
            if ($('#txtTHOIGIANKETTHUC').val() == null || $('#txtTHOIGIANKETTHUC').val() == '') {
                DlgUtil.showMsg("Chưa chọn thời gian ra khoa/viện");
                return false;
            }
            //DoanPV_20211221 BVTM-7690
            if (getCauHinh('HIS_XUTRI_CHAN_DIEUKIEN_RAVIEN_CLS') == '1') {
                var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV006", $("#hidKHAMBENHID").val());
                if (result_ct != '0') {
                    if (result_ct == '-1') {
                        DlgUtil.showMsg('Có lỗi khi xử lý');
                        return;
                    } else {
                        if (getCauHinh('HIS_CHAN_DIEUKIEN_RAVIEN_CLS') == '1' || getCauHinh('NGT_CHECK_DV_CLS') == '1') {
                            DlgUtil.showMsg("Còn phiếu XN/CDHA/PTTT chưa xử lý xong ở khoa " + result_ct);
                            return false;
                        }
                    }
                }
            }
        }
        if (isDoiTuongBhyt) {
            if (typeof $('#txtNGAYSINH').val() != 'undefined' && $('#txtNGAYSINH').val() != null && $('#txtNGAYSINH').val().trim().length > 0) {
                var birthDate = moment($('#txtNGAYSINH').val().trim(), 'DD/MM/YYYY');
                var sysDate = moment();
                var diff = sysDate.diff(birthDate, 'months');
                if (diff <= 36) {
                    if ($('#txtTENNGUOITHAN').val().trim() == '') {
                        DlgUtil.showMsg("Bệnh nhân dưới 6 tuổi. Chưa nhập tên người thân", function () {
                            $('#txtTENNGUOITHAN').focus();
                        });
                        return false;
                    }
                }
            } else {
                var today = new Date();
                var bNamHT = today.getFullYear();
                var b_namsinh = $('#txtNAMSINH').val();
                var tuoi = parseInt(bNamHT) - parseInt(b_namsinh);
                if (tuoi <= 6) {
                    if ($('#txtTENNGUOITHAN').val().trim() == '') {
                        DlgUtil.showMsg("Bệnh nhân dưới 6 tuổi. Chưa nhập tên người thân", function () {
                            $('#txtTENNGUOITHAN').focus();
                        });
                        return false;
                    }
                }
            }
        }
        //L2PT-60785 start
        if (_CAUHINH.NBN_CHECK_NGUOIGIAMHO_TRE_16TUOI == '1' && opt._type == '0' && opt._mode == '0') {
            if (typeof $('#txtNGAYSINH').val() != 'undefined' && $('#txtNGAYSINH').val() != null && $('#txtNGAYSINH').val().trim().length > 0) {
                var birthDate = moment($('#txtNGAYSINH').val().trim(), 'DD/MM/YYYY');
                var sysDate = moment();
                var diff = sysDate.diff(birthDate, 'months');
                if (diff < 192) {
                    if ($('#txtTENNGUOITHAN').val().trim() == '') {
                        DlgUtil.showMsg("Bệnh nhân dưới 16 tuổi. Chưa nhập tên người thân", function () {
                            $('#txtTENNGUOITHAN').focus();
                        });
                        return false;
                    }
                }
            } else {
                var today = new Date();
                var bNamHT = today.getFullYear();
                var b_namsinh = $('#txtNAMSINH').val();
                var tuoi = parseInt(bNamHT) - parseInt(b_namsinh);
                if (tuoi < 16) {
                    if ($('#txtTENNGUOITHAN').val().trim() == '') {
                        DlgUtil.showMsg("Bệnh nhân dưới 16 tuổi. Chưa nhập tên người thân", function () {
                            $('#txtTENNGUOITHAN').focus();
                        });
                        return false;
                    }
                }
            }
        }
        //L2PT-60785 end
        //Begin_HaNv_28122018: Thêm trường ngày hưởng đồng chi trả 5 năm 6 tháng - L2HOTRO-13785
        if (isDoiTuongBhyt && $('#txtNGAYDU5NAM').val().trim().length > 0 && !checkDate($('#txtNGAYDU5NAM').val())) {
            DlgUtil.showMsg("Ngày đủ 5 năm 6 tháng " + $.i18n("date_type_invalid"), function () {
                $('#txtNGAYDU5NAM').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && $('#txtNGAYHUONG_5NAM6THANG').val().trim().length > 0 && !checkDate($('#txtNGAYHUONG_5NAM6THANG').val())) {
            DlgUtil.showMsg("Ngày hưởng 5 năm 6 tháng đồng chi trả " + $.i18n("date_type_invalid"), function () {
                $('#txtNGAYHUONG_5NAM6THANG').focus();
            });
            return false;
        }
        if (isDoiTuongBhyt && !compareDate($('#txtNGAYDU5NAM').val(), $('#txtNGAYHUONG_5NAM6THANG').val(), 'DD/MM/YYYY')) {
            DlgUtil.showMsg("Ngày đủ 5 năm 6 tháng " + $.i18n("smaller_date") + " Ngày hưởng đồng chi trả", function () {
                $('#txtNGAYHUONG_5NAM6THANG').focus();
            });
            return false;
        }
        //End_HaNv_28122018
        if (!dateRegEx($('#txtVAOKHOA').val())) {
            DlgUtil.showMsg("Giá trị vào khoa lúc phải có định dạng dd/mm/yyy hh:mm:ss", function () {
                $('#txtVAOKHOA').focus();
            });
            return false;
        }
        // start jira 18925
        // cau hinh bat buoc chon bac si khi tiep nhan benh nhan vao noi tru
        if (_CAUHINH.NTU_NBN_BB_BS == '1') {
            if (opt._mode == '1' && opt._trangthaikhambenh == 1 && !$('#cboBACSIID').val()) {
                DlgUtil.showMsg("Chưa chọn Bác sĩ ĐT", function () {
                    $('#cboBACSIID').focus();
                });
                return false;
            }
        }
        // end jira 18925
        // START SONDN 030118
        if (_CAUHINH.HIS_NHAP_CHUYENTUYEN == "1" && $("#cboTUYENID").val() == "2" && opt._mode == '0') {
            if ($('#txtCHUANDOANGIOITHIEU').val() == "" || $('#hidCV_CHUYENVIEN_HINHTHUCID1').val() == "" || $('#hidCV_CHUYENVIEN_LYDOID1').val() == "" || $('#cboNOICDID').val() == "") {
                DlgUtil.showMsg("Yêu cầu nhập thông tin chuyển tuyến với bệnh nhân đúng tuyến giới thiệu. ");
                return false;
            }
        }
        // END SONDN 030118
        //check Bổ sung thông tin trẻ sơ sinh trong bện án Sản khoa
        if (_CAUHINH.NTU_CHECK_TT_TRESOSINH == '1') {
            if (opt._mode == '2' && $("#cboLOAIBENHANID").val() == '5' && $("#cboXUTRIID").val() != '5') {
                var _sql_par = RSUtil.buildParam("", [opt._hosobenhanId]);
                var _result = jsonrpc.AjaxJson.getOneValue('NTU01H002.L04', _sql_par);
                if (_result == 0) {
                    DlgUtil.showMsg("Chưa nhập thông tin Bệnh án chi tiết Sản khoa!");
                    return false;
                }
            }
        }
        // End_laphm_12072019
        if (isDoiTuongBhyt && !dateRegex.test($('#txtBHYT_KT').val())) {
            DlgUtil.showMsg('Hạn thẻ BHYT không hợp lệ');
            return false;
        }
        // star jira L2PT-16779
        // xu tri khac chuyen khoa
        if (opt._mode == '2' && $('#cboXUTRIID').val() != 5) {
            if ($('#txtSONGAYDIEUTRI').val().trim() == '') {
                DlgUtil.showMsg('Số ngày điều trị không được để trống!', function () {
                });
                return false;
            }
        }
        // end jira jira L2PT-16779
        // start jira L2PT-17056
        // chặn xử trí nếu không có số ngày điều trị
        // if (opt._mode == '2' && _CAUHINH.NBN_XUTRI_CHANKHIKOCOSNDT == '1' && $('#txtSONGAYDIEUTRI').val().trim() == '') {
        //     DlgUtil.showMsg('Số ngày điều trị không được để trống!', function () {
        //     });
        //     return false;
        // }
        // end jira L2PT-17056
        //Begin_HaNv_05112018: Cho phép nhập tay số vào viện theo cấu hình ngoại trú - L2HOTRO-10246
        if (opt._mode == '0' && _CAUHINH.NGT_NHAP_SOVAOVIEN == '1' && ($("#txtSOVAOVIEN").val().trim() == "" || isNaN($("#txtSOVAOVIEN").val().trim()))) {
            DlgUtil.showMsg('Số vào viện không hợp lệ. ', function () {
                $("#txtSOVAOVIEN").focus();
            });
            return false;
        }
        //End_HaNv_05112018
        // start jira 32547
        // neu chon chuyen vien thi chi duoc phep luu voi ket qua nang hon hoac khong thay doi
        if (_CAUHINH.NBN_XTCVCCNHHKTD == '1' && opt._mode == '2' && $('#cboXUTRIID').val() == '6' && $('#cboKETQUADIEUTRIID').val() != '4' && $('#cboKETQUADIEUTRIID').val() != '3') {
            DlgUtil.showMsg('Xử trí Chuyển viện chỉ có thể chọn kết quả Nặng hơn hoặc Không thay đổi', function () {
            });
            return false;
        }
        // end jira 32547
        // start jira 4315
        if (_CAUHINH.HIS_KHOA_SOLIEU == '1' && opt._mode == '2') {
            // bo ve, dua ve, khac, tien luong tu vong thi lay txtTHOIGIANTVTV
            // con lai lay txtTHOIGIANKETTHUC
            var timeXuTri = $('#txtTHOIGIANKETTHUC').val();
            var xuTriId = $('#cboXUTRIID').val();
            if (['3', '4', '9', '11'].includes(xuTriId)) {
                timeXuTri = $('#txtTHOIGIANTVTV').val();
            }
            var sql_par = [$("#hidTIEPNHANID").val(), timeXuTri];
            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DMC32.KSL.05", sql_par.join('$'));
            var rets = ret.split(';');
            if (rets[0] > 0) {
                DlgUtil.showMsg(rets[1]);
                return false;
            }
        }
        // end jira 4315
        // Begin_laphm_12072019: Bổ sung cấu hình kiểm tra tiếp nhận bệnh nhân nội trú - L2PT-6676
        //if (conNoThanhToan()) {
        //	DlgUtil.showMsg("Bệnh nhân chưa thanh toán hết");
        //	return false;
        //}
        // End_laphm_12072019
        //Beg_HaNv_291122: Check chặn nếu ngày vào khoa < ngày tiếp nhận - L2PT-29474
        if (_CAUHINH.NBN_CHECK_VAOKHOA_NGAYTN == '1' && opt._mode == '1') {
            if ($('#txtVAOKHOA').val() == '') {
                DlgUtil.showMsg('Thời gian vào khoa không được để trống!');
                return false;
            }
            if ($("#cboHINHTHUCVAOVIENID").val() == '1') {
                if (!compareDate($('#txtNGAYTIEPDON').val(), $('#txtNGAYTIEPDON').val().trim(), 'DD/MM/YYYY HH:mm:ss')) {
                    DlgUtil.showMsg('Ngày vào khoa không được nhỏ hơn thời gian tiếp đón!');
                    return false;
                }
            } else {
                if (!compareDate($('#txtNGAYNHAPVIEN').val(), $('#txtVAOKHOA').val().trim(), 'DD/MM/YYYY HH:mm:ss')) {
                    DlgUtil.showMsg('Ngày vào khoa không được nhỏ hơn thời gian nhập viện!');
                    return false;
                }
            }
        }
        //End_HaNv_291122
        //Beg_HaNv_071223: Cảnh báo dịch vụ cận lâm sàng, PTTT chưa hoàn thành - L2PT-59404
        if (_CAUHINH.NBN_CB_DVCLS_CHUAHT != '0' && opt._mode == '1' && opt._trangthaikhambenh == '1') {
            var msgchk = '';
            var reschk = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV006.1", $("#hidKHAMBENHID").val());
            if (reschk != '0') {
                if (reschk == '-1') {
                    DlgUtil.showMsg('Có lỗi khi xử lý');
                    return false;
                } else {
                    msgchk = 'Chưa hoàn thành dịch vụ CLS \n' + reschk + '.';
                    if (_CAUHINH.NBN_CB_DVCLS_CHUAHT == '2') {
                        if (!confirm(msgchk + '\nCó tiếp tục?')) {
                            return false;
                        }
                    } else {
                        DlgUtil.showMsg(msgchk);
                        return false;
                    }
                }
            }
        }
        //End_HaNv_071223

        //HungND - L2PT-L2PT-78539
        var NTU_XUTRI_CHECKKHOA_NTU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_XUTRI_CHECKKHOA_NTU');
        if (NTU_XUTRI_CHECKKHOA_NTU == '1' && opt._mode == 0 && opt._type == '0') {
            var obj = {
                maBenhNhan: $('#txtMABENHNHAN').val().trim(),
                khoaHienTai: opt._deptId + ""
            }
            var param = JSON.stringify(obj);
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L29", param);
            if (result != '0') {
                DlgUtil.showMsg("Lưu không thành công! Bệnh nhân đang điều trị nội trú tại khoa " + result);
                return false;
            }
        }
        //HungND - L2PT-L2PT-78539

        //DoanPV - L2PT-99969
        var NTU_NBN_CHECK_BENHAN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_NBN_CHECK_BENHAN');
        if (NTU_NBN_CHECK_BENHAN == '1' && opt._mode == '1') {
            var obj = new Object();
            obj.HOSOBENHANID = $('#hidHOSOBENHANID').val();
            obj.LOAIBENHANID = $("#cboLOAIBENHANID").val();
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.E01", JSON.stringify(obj));
            if (result != '0') {
                DlgUtil.showMsg("Tồn tại bệnh án: " + result + " đã thực hiện ký. Hủy ký để thay đổi loại bệnh án.");
                return false;
            }
        }
        //DoanPV - L2PT-99969
        return true;
    }

    function loadDetail(code) {
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV009", code);
        if (result != '1' && opt._mode == '0') {
            // jira 29894 + trao doi a SyHN: van cho tiep nhan neu dang dieu tri ngoai tru o khoa khac
            //HungND - L2PT-82796
            var NTU_NBN_DDTNGT = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'NTU_NBN_DDTNGT')
            if (NTU_NBN_DDTNGT == '1') {
                DlgUtil.showMsg('Bệnh nhân đang điều trị  ngoại trú tại khoa: ' + result + '. Không thể tiếp nhận lại')
                return;
            } else if (NTU_NBN_DDTNGT == '2') {
                if (!confirm('Bệnh nhân đang điều trị tại ngoại trú khoa : ' + result + '. Có tiếp tục tiếp nhận?')) {
                    return;
                }
            }
            //HungND - L2PT-82796
            // HungND - L2PT-62183
            if (isDangDieuTriNgoaiTruKhoaKhac(code)) {
                if (_CAUHINH.NTU_TN_DDTNT == '0') {
                    DlgUtil.showMsg('Bệnh nhân đang điều trị tại khoa: ' + result + '. Không thể tiếp nhận lại');
                    return;
                } else if (_CAUHINH.NTU_TN_DDTNT == '2') {
                    if (!confirm('Bệnh nhân đang điều trị tại khoa: ' + result + '. Có tiếp tục tiếp nhận?')) {
                        return;
                    }
                }
            }
            // HungND - L2PT-62183 END

        }

        // HungND - L2PT-73623
        var cauHinhCheckThanhToan = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_NBN_CHECK_THANHTOAN');
        if (cauHinhCheckThanhToan == '1' && kiemTraThanhToan() > 0) {
            if (!confirm('Bệnh nhân chưa thanh toán của lần khám trước. Có tiếp tục tiếp nhận?')) {
                return;
            }
        }
        // HungND - L2PT-73623 END

        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H002.EV006", code);
        if (data_ar != null && data_ar.length > 0) {
            FormUtil.setObjectToForm("divNhapBenhNhan", "", data_ar[0]);
            // load tinh huyen xa
            getDiaChi(data_ar[0].HC_XAID, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID', "", 'txtTKHC_TINH', 'txtTKHC_HUYEN', 'txtTKHC_XA', 'txtTKDIABAN', "cboDOITUONGBENHNHANID",
                true, false);
            //Begin_HaNv_03122018: Bổ dung sub_dtbnid - L2HOTRO-12562
            if (_CAUHINH.HIS_SUDUNG_DOITUONG_KHAIBAO == '1') {
                $("#cboDOITUONGBENHNHANID").find("option[extval0='" + data_ar[0].SUB_DTBNID + "']").prop("selected", true);
            }
            //End_HaNv_03122018
            if ($('#txtNGAYSINH').val() != '') {
                tinhTuoi($('#txtNGAYSINH').val(), 'txtNGAYSINH', 'txtNAMSINH', 'txtTUOI', 'cboDVTUOI');
            } else {
                var today = new Date();
                var bNamHT = today.getFullYear();
                var b_namsinh = $('#txtNAMSINH').val();
                if (b_namsinh > bNamHT) {
                    DlgUtil.showMsg("Năm sinh không được lớn hơn năm hiện tại", function () {
                        $('#txtNAMSINH').val("");
                    });
                    return;
                } else {
                    $('#txtTUOI').val(bNamHT - b_namsinh);
                }
                $('#cboDVTUOI').val("1");
            }
            if (data_ar[0].DOITUONGBENHNHANID != '1') {
                _clearObjectBHYT(true);
            }
            if (opt._mode == '0') {
                var msgAll = '';
                var endDate = moment(data_ar[0].NGAY_SD, 'DD/MM/YYYY');
                var sysDate = moment(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'), 'DD/MM/YYYY');
                var diff = sysDate.diff(endDate, 'days');
                if (diff < 15 && diff > 0) {
                    msgAll = 'Bệnh nhân mới tới điều trị cách đây 15 ngày';
                } else if (diff == 0) {
                    msgAll = 'Bệnh nhân đã khám trong ngày';
                }

                //check trốn viện.
                var _maba = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV025", $("#hidBENHNHANID").val());
                if (_CAUHINH.NGT_CHECK_TRONVIEN == '3' && _maba != '0') {
                    if (_maba.substring(0, 1) == '0') {
                        msgAll = msgAll + '</br>' + 'Bệnh nhân có thông tin trốn viện theo BA: ' + _maba.substring(1, _maba.length);
                    } else if (_maba.substring(0, 1) == '1') {
                        msgAll = msgAll + '</br>' + 'Bệnh nhân có thông tin trốn viện nhưng đã hủy theo BA: ' + _maba.substring(1, _maba.length);
                    } else {
                        msgAll = msgAll + '</br>' + 'Bệnh nhân đã hoàn tất thanh toán theo BA: ' + _maba;
                    }
                }

                if (msgAll != '') {
                    DlgUtil.showMsg(msgAll);
                }

                //HungND - L2PT-99603
                if (_CAUHINH_NBN[0].NTU_NBN_CHAN_TRUNG_TN && _CAUHINH_NBN[0].NTU_NBN_CHAN_TRUNG_TN == '1') {
                    $('#txtTENBENHNHAN').prop("disabled", true);
                    $('#txtNGAYSINH').prop("disabled", true);
                    $('#txtGIO_SINH').prop("disabled", true);
                    $('#txtNAMSINH').prop("disabled", true);
                    $('#txtTUOI').prop("disabled", true);
                    $('#cboDVTUOI').prop("disabled", true);
                    $('#txtTKGIOITINH').prop("disabled", true);
                    $('#cboGIOITINHID').prop("disabled", true);
                }
                //HungND - L2PT-99603 END
            }
        }
    }

    function _clearAndDisable(ctrId, value, status) {
        $('#' + ctrId).val(value);
        $('#' + ctrId).prop("disabled", status);
    }

    function _clearObjectBHYT(value) {
        _clearAndDisable("txtMABHYT", "", value);
        _clearAndDisable("txtBHYT_BD", "", value);
        _clearAndDisable("txtBHYT_KT", "", value);
        _clearAndDisable("txtTKDKKBBD", "", value);
        $("#cboDKKBBDID").empty();
        $("#cboDKKBBDID").text('');
        _clearAndDisable("cboDKKBBDID", "", value);
        _clearAndDisable("cboTUYENID", "", value);
        _clearAndDisable("txtTKNOICD", "", value);
        $("#cboNOICDID").empty();
        $("#cboNOICDID").text('');
        _clearAndDisable("cboNOICDID", "", value);
        _clearAndDisable("txtDIACHI_BHYT", "", value);
        _clearAndDisable("cboMAVUNGID", "", value);
        $("#chkBH5NAM").prop("checked", false);
        $("#chkBH5NAM").attr("disabled", value);
        $("#chkNOTHE").prop("checked", false);
        $("#chkNOTHE").attr("disabled", value);
        if (!value) {
            $('[attrview]').each(function (index) {
                $(this).addClass("required");
            });
            $('#txtBHYT_BD').val('01/01/' + new Date().getFullYear());
            $('#txtBHYT_KT').val('31/12/' + new Date().getFullYear());
            $('#txtDIACHI_BHYT').val($('#txtDIACHI').val());
        } else {
            $('[attrview]').each(function (index) {
                $(this).removeClass("required");
            });
        }
        $("#cboDOITUONGDB").val('0');
        _clearAndDisable("cboDOITUONGDB", "", value);
        _clearAndDisable("txtMADOITUONGNGHEO", "", value);
        // start jira 9664
        $('#divSoChuyenTuyen').hide();
        // end jira 9664
    }

    // kiem tra sinh so the tre em
    function _kiemTraSinhSoTheBHYT() {
        if ($("#txtNGAYSINH").val() == "") {
            DlgUtil.showMsg("Bệnh nhân chưa có ngày sinh", function () {
                $("#txtNGAYSINH").focus();
            });
        } else if ($('#txtNGAYSINH').val().trim().length > 0 && !dateRegex.test($('#txtNGAYSINH').val())) {
            DlgUtil.showMsg("Ngày sinh " + $.i18n("datetime"), function () {
                $('#txtNGAYSINH').focus();
            });
            return false;
        } else if ($('#txtNGAYSINH').val().trim().length > 0 && !checkDate($('#txtNGAYSINH').val())) {
            DlgUtil.showMsg("Ngày sinh " + $.i18n("date_type_invalid"), function () {
                $('#txtNGAYSINH').focus();
            });
            return false;
        } else if ($("#cboHC_TINHID").val() == "") {
            DlgUtil.showMsg("Bệnh nhân chưa có mã tỉnh", function () {
                $('#cboHC_TINHID').focus();
            });
            return false;
        } else if ($("#cboHC_HUYENID").val() == "") {
            DlgUtil.showMsg("Bệnh nhân chưa có mã huyện", function () {
                $('#cboHC_HUYENID').focus();
            });
            return false;
        } else if ($("#cboDOITUONGBENHNHANID").val() != "1") {
            DlgUtil.showMsg("Bệnh nhân phải là đối tượng bảo hiểm y tế", function () {
                $('#cboDOITUONGBENHNHANID').focus();
            });
            return false;
        } else {
            var ageObj = moment().diff(moment($('#txtNGAYSINH').val(), 'DD/MM/YYYY'), 'months');
            if (ageObj > 72) {
                DlgUtil.showMsg("Bệnh nhân đã quá 6 tuổi");
                return false;
            }
        }
        return true;
    }

    // sinh so the bao hiem tre em
    function _sinhSoTheBHYT() {
        var matinh = (typeof $('#cboHC_TINHID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_TINHID' + " option:selected").attr('extval1') : "");
        var mahuyen = (typeof $('#cboHC_HUYENID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_HUYENID' + " option:selected").attr('extval1') : "");
        var namsinh = $('#txtNGAYSINH').val();
        namsinh = namsinh.substring(0, 2) + namsinh.substring(3, 5) + namsinh.substring(namsinh.length - 1, namsinh.length);
        var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.SINHTHE.BHYT", matinh + '$' + mahuyen + '$' + namsinh);
        return ret;
    }

    // sinh so the bao hiem no cho benh nhan
    function _sinhSoNoThe() {
        var matinh = (typeof $('#cboHC_TINHID' + " option:selected").attr('extval1') != 'undefined' ? $('#cboHC_TINHID' + " option:selected").attr('extval1') : "");
        if (matinh == '') {
            DlgUtil.showMsg('Chưa chọn tỉnh');
            $("#chkNOTHE").prop("checked", false);
            return;
        }
        var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.SINHTHE.NO", matinh);
        return ret;
    }

    function _clearOption(strId, _defOpt, ctrTk) {
        $("#" + strId).empty();
        if (typeof _defOpt.value !== "undefined") {
            $("#" + strId).append('<option value="' + _defOpt.value + '">' + _defOpt.text + '</option>');
        }
        if (typeof ctrTk !== "undefined") {
            $("#" + ctrTk).val('');
        }
    }

    function _createAddress(arrCtr, charJoin) {
        var strJoin = '';
        for (var i = 0; i < arrCtr.length; i++) {
            if ($('#' + arrCtr[i]).val() != "") {
                strJoin = strJoin + (strJoin == '' ? '' : charJoin) + (arrCtr[i].indexOf('cbo') != -1 ? $('#' + arrCtr[i] + " option:selected").text() : $('#' + arrCtr[i]).val());
            }
        }
        return strJoin;
    }

    function _searchPatient() {
        flag_ban_the = false;
        var obj = new Object();
        obj.ten = nvl($('#txtTENBENHNHAN').val(), '-1');
        obj.ngaysinh = nvl($('#txtNGAYSINH').val(), '-1');
        obj.gioitinh = '-1'; // L2PT-76626: không tìm theo giới tính
        obj.mabhyt = nvl($('#txtMABHYT').val(), '-1');
        var _par = [JSON.stringify(obj)];
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.EV008", _par.join('$'));
        if (result == '0') {
            DlgUtil.showMsg('Không tìm thấy bệnh nhân');
        } else {
            var paramInput = {
                ten: obj.ten,
                ngaysinh: obj.ngaysinh,
                gioitinh: obj.gioitinh,
                mabhyt: obj.mabhyt
            };
            DlgUtil.buildPopupUrl("divDlgTKBN", "divDlg", "manager.jsp?func=../noitru/NTU01H013_TimKiemBenhNhan", paramInput, "Tìm kiếm bệnh nhân", 965, 510);
            DlgUtil.open("divDlgTKBN");
        }
    }

    function clearSave() {
        FormUtil.clearForm("divNhapBenhNhan", "");
        setValueDefaultCbo("divNhapBenhNhan", "cbo");
        $('#cboDANTOCID').val('25').change();
        $("#txtVAOKHOA").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
        $("#txtVAOLAN").val('1');
        _clearOption('cboHC_HUYENID', {
            value: '',
            text: 'Chọn'
        }, 'txtTKHC_HUYEN');
        _clearOption('cboHC_XAID', {
            value: '',
            text: 'Chọn'
        }, 'txtTKHC_XA');
        _clearOption('cboDIABANID', {
            value: '',
            text: 'Chọn'
        });
        flag_ban_the = false;
        $('#cboHINHTHUCVAOVIENID').prop("disabled", false);
        $('#txtTENBENHNHAN').focus();
        // start jira L2PT-17048
        // đa khoa hà nam mặc định viện phí
        if (opt._mode == "0" && opt.hospitalId == '915') {
            $('#cboDOITUONGBENHNHANID').val("2").change();
        }
        // end jira L2PT-17048
        loadTuyen();
    }

    function checkTheBhyt() {
        var fillTTSuccess = false;
        var checkCongBhyt = $("#chkCHECKCONG").prop('checked');
        if (checkCongBhyt == true && opt._mode == '0' && $("#cboDOITUONGBENHNHANID").val() == "1" && $('#txtMABHYT').val().substr(0, 3) != 'NTH' && $("#chkSINHTHETE").prop('checked') == false) {
            var mathe = $("#txtMABHYT").val().trim();
            var tenbn = $("#txtTENBENHNHAN").val().trim();
            var namsinh = $("#txtNGAYSINH").val().trim() != "" ? $("#txtNGAYSINH").val().trim() : $("#txtNAMSINH").val().trim();
            var gioitinhid = $("#cboGIOITINHID").val().trim();
            var noidk = $("#cboDKKBBDID").val() == null ? "" : $("#cboDKKBBDID").val();
            var ngaybd = $("#txtBHYT_BD").val().trim();
            var ngaykt = $("#txtBHYT_KT").val().trim();
            var ret1 = _checkCongBHYT(i_u, i_p, mathe.trim(), tenbn, namsinh.trim(), gioitinhid, noidk, ngaybd, ngaykt, "0");
            if (_CAUHINH.NGT_TUDONGFILL_BHXH == '1' && ret1.maKetQua != "" && ret1.gtTheDen != "" && ret1.gtTheDen != 'null' && ret1.gtTheDen != null) {
                // confirm xac nhan fill 2 thong tin sau: hoten, ngaysinh
                // tu dong fill cac thong tin sau: gioitinh, makv, ngaydu5nam, ngaytu, ngayden
                $("#txtTKGIOITINH").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                $("#cboGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
                $('#txtBHYT_BD').val(ret1.gtTheTu);
                $('#txtBHYT_KT').val(ret1.gtTheDen);
                $("#txtNGAYDU5NAM").val(ret1.ngayDu5Nam);
                $("#txtDIACHI_BHYT").val(ret1.diaChi);
                $("#txtTKDKKBBD").val(ret1.maDKBD);
                $("#cboDKKBBDID").empty();
                $("#cboDKKBBDID").append('<option value="' + ret1.maDKBD + '" selected>' + ret1.tenDKBDMoi + '</option>');
                for (i = 1; i <= 3; i++) {
                    if (ret1.maKV == ("K" + i)) {
                        $("#cboMAVUNGID").val(i);
                    }
                }
                $("#txtTENBENHNHAN").val(ret1.hoTen.toUpperCase());
                if (ret1.ngaySinh.length == "4") {
                    $("#txtNAMSINH").val(ret1.ngaySinh);
                    $("#txtNGAYSINH").val("");
                    $("#txtNAMSINH").change();
                } else {
                    $("#txtNGAYSINH").val(ret1.ngaySinh);
                    $("#txtNGAYSINH").change();
                }
                fillTTSuccess = true;
            }
            var ketQuaBHXH = ret1.maKetQua == "004" ? "" : ret1.noiDungKetQua;
            var ghiChuBHXH = ret1.ghiChu;
            if (ketQuaBHXH == '') {
                if (ret1.maKetQua == "004") {
                    DlgUtil.showMsg(ghiChuBHXH + ". Bệnh nhân sẽ được tiếp đón bình thường.");
                }
                // start jira 21904
                // bình thường chỉ cần ret1.noiDungKetQua = '' là cho pass nhưng giờ muốn
                // validate thêm 1 số nội dung như nơi đkcbbđ, giới tính so với thông tin nhập vào
                if (_CAUHINH.NTU_NBN_VALTHEMBHYT == '1') {
                    return validateThemTheBHYT(ret1);
                }
                // end jira 21904
                return '';
            } else {
                if (fillTTSuccess) {
                    return "";
                } else {
                    return ghiChuBHXH;
                }
            }
        }
        return '';
    }

    function tinhngayDt(ngaybd, ngaykt, kqdt) {
        // số ngày điều trị = ngay ra - ngay vao + 1
        if (_CAUHINH.NBN_NGAYDIEUTRI_6556 == '1') {
            $('#txtSONGAYDIEUTRI').prop("disabled", true);
            var diff = diffDate(ngaykt, ngaybd, 'DD/MM/YYYY', 'days');
            return diff + 1;
        } else if (_CAUHINH.NBN_NGAYDIEUTRI_6556 == '2') {
            $('#txtSONGAYDIEUTRI').prop("disabled", true);
            var diff_h = diffDate(ngaykt, ngaybd, 'DD/MM/YYYY HH:mm', 'hours');
            if (diff_h < 8) {
                return 1;
            } else {
                var diffNgay = diffDate(ngaykt, ngaybd, 'DD/MM/YYYY', 'days');
                return diffNgay + 1;
            }
        }
        // end BVTM-37
        // start jira 34356
        //if (getCauHinh('NBN_TNDT_YDCTVP') == '1') {
        if (["26440", "30900"].includes(opt.hospitalId + "")) {
            var diff_h = diffDate(ngaykt, ngaybd, 'DD/MM/YYYY HH:mm', 'hours');
            if (diff_h < 8) {
                return 1;
            } else {
                var diffNgay = diffDate(ngaykt, ngaybd, 'DD/MM/YYYY', 'days');
                return diffNgay + 1;
            }
        }
        // end jira 34356
        // start BVTM-37
        // buu dien ha noi, xu tri chuyen vien thì fix số ngày điều trị = ngay ra - ngay vao + 1
        if (opt.hospitalId == '10284' && opt._mode == '2' && $('#cboXUTRIID').val() == '6') {
            $('#txtSONGAYDIEUTRI').prop("disabled", true);
            var diff = diffDate(ngaykt, ngaybd, 'DD/MM/YYYY', 'days');
            return diff + 1;
        }
        // end BVTM-37
        // start L2PT-23489
        // bệnh án đtnt mãn tính thì fix số ngày điều trị = 1
        if (_CAUHINH.NTU_BADTNT_MANTINH == '1' && opt._type == '3') {
            if ($('#cboBADTNTMANTINH').val() == '1') {
                $('#txtSONGAYDIEUTRI').prop("disabled", true);
                return 1;
            }
        }
        // end L2PT-23489
        // start L2PT-24333
        // so ngay dieu tri chi tinh cho cac khoa noi tru
        if (_CAUHINH.NBN_SNDT_ONTU == '1') {
            var ngaybd_ntu = $('#txtVAOKHOA').val();
            var diff = diffDate(ngaykt, ngaybd_ntu, 'DD/MM/YYYY', 'days');
            return diff + 1;
        }
        // end L2PT-24333
        var diff = diffDate(ngaykt, ngaybd, 'DD/MM/YYYY', 'days');
        if ($.isNumeric(opt._type) && opt._type == 3) {
            // L2PT-20181
            if (_CAUHINH.HIS_TT3739_TINHNGAYDT == '1' && _CAUHINH.NBN_SNDT_DTNT == '1') {
                var diffNgay = diffDate(ngaykt, ngaybd, 'DD/MM/YYYY', 'days');
                return diffNgay + 1;
            } else {
                return diff;
            }
        }
        if (diff == 0) {
            if (_CAUHINH.HIS_TINHNGAYDT_THEOGIO == '1') {
                return 1;
            }
            var diff_h = diffDate(ngaykt, ngaybd, 'DD/MM/YYYY HH:mm', 'hours');
            if (diff_h < 4) {
                return diff;
            } else {
                return 1;
            }
        } else {
            //Begin_HaNv_10012019: Tính ngày điều trị theo TT37 và TT39 (cho tất cả đối tượng) - L2PT-566
            if (_CAUHINH.HIS_TT3739_TINHNGAYDT == '1') {
                if (kqdt == '3' || kqdt == '4' || kqdt == '5' || $('#cboXUTRIID').val() == '2' || $('#cboXUTRIID').val() == '6') {
                    return diff + 1;
                } else {
                    return diff;
                }
            }
            //Beg_HaNv_230922: Tính ngày điều trị theo TT39 QUANY15 - L2PT-26098
            else if (_CAUHINH.HIS_TT3739_TINHNGAYDT == '2') {
                var xt = $('#cboXUTRIID').val();
                if (((kqdt == '3' || kqdt == '4') && (xt == '2' || xt == '6')) || (kqdt == '5' && (xt == '2' || xt == '4' || xt == '7'))) {
                    return diff + 1;
                } else {
                    return diff;
                }
            }
            //Beg_HaNv_230922: Tính ngày điều trị theo nhi nam định
            else if (_CAUHINH.HIS_TT3739_TINHNGAYDT == '3') {
                var xt = $('#cboXUTRIID').val();
                if (xt == '6' || xt == '7') {
                    return diff + 1;
                } else {
                    return diff;
                }
            }
            //End_HaNv_230922
            return diff;
        }
    }

    function openPopUpCheckLSKCB(ngang, doc) {
        var paramInput = {
            MABHYT: $('#txtMABHYT').val(),
            TENBENHNHAN: $('#txtTENBENHNHAN').val(),
            NGAYSINH: $('#txtNGAYSINH').val() == "" ? $("#txtNAMSINH").val().trim() : $('#txtNGAYSINH').val(),
            QRCODE: '',
            GIOITINH: $("#cboGIOITINHID").val(),
            MAKCBBD: $("#cboMAKCBBD").val() == null || $("#cboMAKCBBD").val() == 'null' ? "" : $("#cboMAKCBBD").val(),
            TUNGAY: $('#txtBHYT_BD').val(),
            DENNGAY: $('#txtBHYT_KT').val()
        };
        DlgUtil.buildPopupUrl("divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB", paramInput, "Thông tin lịch sử điều trị bệnh nhân", window.innerWidth * ngang,
            window.innerHeight * doc);
        var parent = DlgUtil.open("divDlgDDT");
    }

    function moBALan2(hsbaid) {
        var objData = new Object();
        // noi tru thi mode = 6, dieu tri ngoai tru thi = 2
        objData.MODE = (opt._type == 0 ? '6' : '2');
        objData.HSBAID = "" + hsbaid;
        objData.KHOAID = opt._deptId;
        var par = JSON.stringify(objData);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.L07", par);
        var data = $.parseJSON(result);
        var message = '';
        if (data == 1) {
            message = 'Bệnh nhân đã khám trong ngày, đã mở lại bệnh án cũ!';
        } else {
            message = 'Bệnh nhân đã khám trong ngày, có lỗi khi mở lại bệnh án cũ! ' + data;
        }
        DlgUtil.showMsg(message, function () {
            clearSave();
        });
    }

    function daChiKhamNgoaiTruTrongNgayTheoBHYT() {
        // neu trong ngay da chi kham benh ngoai tru (khong nhap noi tru) roi thi tra ve hsbaid, neu khong tra ve rong
        var objData = new Object();
        objData.MABHYT = $('#txtMABHYT').val().trim();
        var par = JSON.stringify(objData);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L06.2", par);
        var data = $.parseJSON(result);
        return data;
    }

    function dateRegEx(date) {
        var pattern = new RegExp("^(3[01]|[12][0-9]|0[1-9])/(1[0-2]|0[1-9])/[0-9]{4} (2[0-3]|[01]?[0-9]):([0-5]?[0-9]):([0-5]?[0-9])$");
        if (date.search(pattern) === 0) {
            return true;
        } else {
            return false;
        }
    }

    function getPhieuChuaChiDinhICD() {
        var dsPhieuChuaChiDinhICD = "";
        var obj = {
            hosobenhanid: opt._hosobenhanId + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L15", param);
        if (result != '' && result != undefined && result != 'EXCEPTION_SQL') {
            dsPhieuChuaChiDinhICD = result;
        }
        return dsPhieuChuaChiDinhICD;
    }

    // L2PT-24773 start
    function getCauHinhChiHienThiPhongDTNTUKhiXuTri() {
        var res = 0;
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'NTU_NBN_CPDTNTU_DTNTU');
        if (data_ar != null && data_ar.length > 0) {
            if (data_ar[0].NTU_NBN_CPDTNTU_DTNTU == '1') {
                res = 1;
            }
        }
        return res;
    }

    //L2PT-24773 end
    function getCauHinh(tenCauHinh) {
        var giaTriCauHinh = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", tenCauHinh);
        return giaTriCauHinh;
    }

    // hàm a Đoàn cung cấp
    function checkPhieuChuaXuLyXong() {
        var _checkDuyetDuoc = _CAUHINH.NTU_DONG_BA_CHECK_DUYET_DUOC;
        if (_checkDuyetDuoc != '1') {
            var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV005", opt._khambenhId);
            if (result_ct != '0') {
                if (result_ct == '-1') {
                    DlgUtil.showMsg('Có lỗi khi xử lý');
                    return -1;
                } else {
                    DlgUtil.showMsg('Còn đơn thuốc/phiếu vật tư chưa duyệt ở khoa' + result_ct);
                    return -1;
                }
            }
        }
        var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV008", opt._khambenhId);
        if (result_ct != '0') {
            if (result_ct == '-1') {
                DlgUtil.showMsg('Có lỗi khi xử lý');
                return -1;
            } else {
                DlgUtil.showMsg('Còn phiếu phẫu thuật thủ thuật chưa xử lý xong ở khoa ' + result_ct);
                return -1;
            }
        }
        var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV006", opt._khambenhId);
        if (result_ct != '0') {
            if (result_ct == '-1') {
                DlgUtil.showMsg('Có lỗi khi xử lý');
                return -1;
            } else {
                DlgUtil.showMsg('Còn phiếu xét nghiệm hoặc chuẩn đoán hình ảnh chưa xử lý xong ở khoa ' + result_ct);
                return -1;
            }
        }
        return 1;
    }

    function checkPhieuChuaXuLyXong2() {
        var _checkDuyetDuoc = _CAUHINH.NTU_DONG_BA_CHECK_DUYET_DUOC;
        if (_checkDuyetDuoc != '1') {
            var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV005", opt._khambenhId);
            if (result_ct != '0' && result_ct !== '-1') {
                return 'Còn đơn thuốc/phiếu vật tư chưa duyệt ở khoa' + result_ct;
            }
        }
        var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV008", opt._khambenhId);
        if (result_ct != '0' && result_ct !== '-1') {
            return 'Còn phiếu phẫu thuật thủ thuật chưa xử lý xong ở khoa ' + result_ct;
        }
        var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV006", opt._khambenhId);
        if (result_ct != '0' && result_ct !== '-1') {
            return 'Còn phiếu xét nghiệm hoặc chuẩn đoán hình ảnh chưa xử lý xong ở khoa ' + result_ct;
        }
        return -1;
    }

    function ketThucKhoaGMHS() {
        var obj = {
            hosobenhanid: $('#hidHOSOBENHANID').val()
        };
        var par = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.KTGMHS", par);
        var data = $.parseJSON(result);
        if (data == -1) {
            DlgUtil.showMsg('Có lỗi khi xử lý');
            return -1;
        } else {
            console.log('kết thúc khoa gây mê hồi sức thành công');
            return 1;
        }
    }

    function validateThemTheBHYT(ret1) {
        if (ret1.errKCBBD != "") {
            return "Nơi KCBBĐ không hợp lệ.";
        }
        if (ret1.gioiTinh != $('#cboGIOITINHID option:selected').text()) {
            return "Giới tính không đúng";
        }
        var ngaybd = $("#txtBHYT_BD").val().trim();
        var ngaykt = $("#txtBHYT_KT").val().trim();
        if (ret1.gtTheTu != ngaybd) {
            return "Giá trị thẻ từ không hợp lệ";
        }
        if (ret1.gtTheDen != ngaykt) {
            return "Giá trị thẻ đến không hợp lệ";
        }
        return "";
    }

    // dua vao khambenhid kiem tra xem co ket qua dieu tri nao chua
    function chuaCoKetQuaDieuTriNao() {
        var obj = {
            khamBenhID: opt._khambenhId + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.L14", param);
        if (result == 1) {
            return true;
        } else {
            return false;
        }
    }

    function getTHMaBenhTuCacKhoa() {
        var ttMaBenh = {
            MABENHCHINH: "",
            TENBENHCHINH: "",
            LISTBENHPHU: "",
            GHICHUBENHCHINH: ""
        }
        var obj = {
            khamBenhID: opt._khambenhId + "",
            hoSoBenhAnID: opt._hosobenhanId + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H002.L13", param);
        if (result.length > 0) {
            ttMaBenh = result[0];
            ttMaBenh = xoaBenhTrung(ttMaBenh);
        }
        return ttMaBenh;
    }

    function xoaBenhTrung(ttMaBenh) {
        var benhChinh = ttMaBenh.MABENHCHINH + "-" + ttMaBenh.TENBENHCHINH;
        var arrBenhPhu = ttMaBenh.LISTBENHPHU.split(";");
        var arrBenhPhuNew = [];
        // xoa benh trung
        for (var i = 0; i < arrBenhPhu.length; i++) {
            var index = $.inArray(arrBenhPhu[i], arrBenhPhuNew);
            if (index < 0) {
                arrBenhPhuNew.push(arrBenhPhu[i]);
            }
        }
        var arrBenhPhuNew2 = [];
        // xoa benh chinh khoi benh phu
        for (var i = 0; i < arrBenhPhuNew.length; i++) {
            if (arrBenhPhuNew[i] != benhChinh) {
                arrBenhPhuNew2.push(arrBenhPhuNew[i]);
            }
        }
        return {
            MABENHCHINH: ttMaBenh.MABENHCHINH,
            TENBENHCHINH: ttMaBenh.TENBENHCHINH,
            LISTBENHPHU: arrBenhPhuNew2.join(";"),
            GHICHUBENHCHINH: ttMaBenh.GHICHUBENHCHINH
        }
    }

    function getChanDoanTuPhieuDieuTri() {
        var ttMaBenh = {
            MABENHCHINH: "",
            TENBENHCHINH: "",
            LISTBENHPHU: "",
            GHICHUBENHCHINH: ""
        }
        var obj = {
            khamBenhID: opt._khambenhId + "",
            hoSoBenhAnID: opt._hosobenhanId + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H002.L45", param);
        if (result && result.length > 0) {
            ttMaBenh = result[0];
        }
        return ttMaBenh;
    }

    function callbackLoadLoaiBA(loaiBAMacDinh) {
        // start jira 26063
        // bv mắt bình thuận: chỉ để lại các bệnh án mắt khi tiep nhan noi tru
        if (_CAUHINH.CHIDELAIBAMAT == '1' && opt._type == '0') {
            $("#cboLOAIBENHANID").empty();
            $("#cboLOAIBENHANID").append('<option value="30">Bệnh án đáy mắt</option>');
            $("#cboLOAIBENHANID").append('<option value="31">Bệnh án mắt lác</option>');
            $("#cboLOAIBENHANID").append('<option value="32">Bệnh án mắt trẻ em</option>');
            $("#cboLOAIBENHANID").append('<option value="33">Bệnh án chấn thương mắt</option>');
            $("#cboLOAIBENHANID").append('<option value="34">Bệnh án mắt - bán phần trước</option>');
            $("#cboLOAIBENHANID").append('<option value="35">Bệnh án mắt glocom</option>');
            $("#cboLOAIBENHANID").append('<option value="38">Bệnh án Mắt</option>');
            if (['30', '31', '32', '33', '34', '35', '38'].includes(loaiBAMacDinh)) {
                $("#cboLOAIBENHANID").val(loaiBAMacDinh);
            }
        }
        // end jira 26063
        $("#cboLOAIBENHANID option[value='23']").each(function () {
            $(this).remove();
        });

    }

    function tuDongKetThucKCKNeuHoanThanhPhieu() {
        var obj = {
            hosobenhanid: opt._hosobenhanId + "",
            khambenhid: opt._khambenhId + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L18", param);
        if (result == '1') {
            console.log("Kết thúc khám chuyên khoa thành công");
        } else if (result == '2') {
            console.log("khám chuyên khoa nhưng không kết thúc vì còn mbp chưa hoàn thành");
        } else if (result == '3') {
            console.log("không tồn tại khám chuyên khoa");
        } else {
            console.log("đã xảy ra lỗi");
        }
    }

    function soBnTrungTT() {
        var obj = new Object();
        obj.ten = $('#txtTENBENHNHAN').val().trim();
        obj.ngaysinh = nvl($('#txtNGAYSINH').val().trim(), '-1');
        obj.gioitinh = $('#cboGIOITINHID').val();
        obj.mabhyt = '-1';
        var _par = [JSON.stringify(obj)];
        var resultCount = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.EV008", _par.join('$'));
        return resultCount;
    }

    function isNhapBnTTDuLieuTuyenKhongHopLe() {
        if (opt._mode == '0' && $('#cboDOITUONGBENHNHANID').val() == '1' &&
            ((opt._hospital_code != $('#cboDKKBBDID').val() && $('#cboTUYENID').val() != '4') || (opt._hospital_code == $('#cboDKKBBDID').val() && $('#cboTUYENID').val() == '4'))) {
            return true;
        } else {
            return false;
        }
    }

    function getMsgEnd() {
        var msgEnd = '';
        var currentTime = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
        if (!compareDate(currentTime, $('#txtBHYT_KT').val(), 'DD/MM/YYYY')) {
            msgEnd = 'Thẻ BHYT đã hết hạn\n';
        }
        var countBed = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.EV013", $('#hidKHAMBENHID').val());
        if (countBed == 0) {
            msgEnd = msgEnd + 'Bệnh nhân chưa kê giường\n';
        }
        return msgEnd;
    }

    function saveThongTin() {
        //HungND - L2PT-51531
        // var trangthaitiepnhan = jsonrpc.AjaxJson.getOneValue("NGT.GETCHECKKTBA", [{"name":"[0]", "value":$('#hidTIEPNHANID').val()}]);
        // if (trangthaitiepnhan != 0 && trangthaitiepnhan != null && trangthaitiepnhan != "null" ) {
        // 	DlgUtil.showMsg("Bệnh án đã kết thúc không thể thao tác, yêu cầu tải lại trang để cập nhật lại trang thái của bệnh nhân!");
        // 	return ;
        // }
        //HungND - L2PT-51531 END
        var validator = new DataValidator("divNhapBenhNhan");
        var valid = validator.validateForm();
        if (valid) {
            if (_kiemTra()) {
                var _checkCls = getCauHinh('HIS_XUTRI_CHAN_DIEUKIEN_RAVIEN_CLS');
                if (opt._mode == '2' && _checkCls != '0') {
                    var _msg = '';
                    var result_ct = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.EV006", $("#hidKHAMBENHID").val());
                    if (result_ct != '0') {
                        if (result_ct == '-1') {
                            DlgUtil.showMsg('Có lỗi khi xử lý');
                            return;
                        } else {
                            if (getCauHinh('HIS_CHAN_DIEUKIEN_RAVIEN_CLS') == '1' || getCauHinh('NGT_CHECK_DV_CLS') == '1') {
                                _msg = 'Còn phiếu XN/CDHA/PTTT chưa xử lý xong ở khoa ' + result_ct + '\n';
                            }
                        }
                    }
                    if (isNhapBnTTDuLieuTuyenKhongHopLe()) {
                        if (!confirm(_msg + 'Dữ liệu đúng tuyến/trái tuyến không hợp lệ. Có tiếp tục?')) {
                            return;
                        }
                        saveData();
                        return;
                    }
                    if ($('#cboXUTRIID').val() == '' || $('#cboDOITUONGBENHNHANID').val() != '1') {
                        saveData();
                        return;
                    }
                    var msgEnd = getMsgEnd();
                    if (msgEnd != '') {
                        if (!confirm(msgEnd + _msg + "Bạn có tiếp tục?")) {
                            return;
                        }
                    }
                    saveData();
                } else {
                    if (opt._mode == '0' && $('#cboDOITUONGBENHNHANID').val() != 1) {
                        if (soBnTrungTT() != '0') {
                            if (!confirm('Có bệnh nhân trùng thông tin (có thể quay lại ấn F3 để kiểm tra), bạn có tiếp tục nhập bệnh nhân')) {
                                return;
                            }
                        }
                        saveData();
                        return;
                    }

                    if (isNhapBnTTDuLieuTuyenKhongHopLe()) {
                        if (!confirm('Dữ liệu đúng tuyến/trái tuyến không hợp lệ. Có tiếp tục?')) {
                            return;
                        }
                        saveData();
                        return;
                    }
                    if ($('#cboXUTRIID').val() == '' || $('#cboDOITUONGBENHNHANID').val() != '1') {
                        saveData();
                        return;
                    }
                    var msgEnd = getMsgEnd();
                    if (msgEnd != '') {
                        if (!confirm(msgEnd + "Bạn có tiếp tục?")) {
                            return;
                        }
                    }
                    saveData();
                }
            }
        }
    }

    function xuTriThanhCong(msgCheck) {
        var xuTriId = $('#cboXUTRIID').val();
        if (xuTriId == '6') {
            DlgUtil.showMsg("Lưu thông tin thành công! ");
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $('#hidKHAMBENHID').val()
            }];
            //L2PT-79812 HungND
            var NTU_NBN_AUTO_IGRV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_NBN_AUTO_IGRV')
            if (NTU_NBN_AUTO_IGRV == '1') {
                if (_CAUHINH.NTU_NBN_CHUYENVIEN_RTF == '1') {
                    var rpName = "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
                    CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'rtf', par, rpName);
                } else {
                    openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
                }
            }

            //L2PT-79812 HungND END
        }
        // neu xu tri ra vien va bat cau hinh NTU_NBN_AUTO_IGRV thi tu dong in luon giay ra vien
        else if (xuTriId == '1' && _CAUHINH.NTU_NBN_AUTO_IGRV == '1') {
            DlgUtil.showMsg("Lưu thông tin thành công! ");
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }, {
                name: 'i_hid',
                type: 'String',
                value: opt.hospitalId
            }, {
                name: 'i_sch',
                type: 'String',
                value: opt.dbschema
            }];
            openReport('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", "pdf", par);
        }
        // neu xu tri hen kham va bat cau hinh NTU_NBN_AUTO_IGHK thi tu dong in luon giay hen kham
        else if (xuTriId == '8' && _CAUHINH.NTU_NBN_AUTO_IGHK == '1') {
            DlgUtil.showMsg("Lưu thông tin thành công! ");
            var par = [{
                name: 'khambenhid',
                type: 'String',
                value: $("#hidKHAMBENHID").val()
            }];
            openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
        } else {
            if (_CAUHINH.NTU_XUTRIKOTHOATFORM == '1') {
                DlgUtil.showMsg("Xử trí thành công! ");
            } else {
                EventUtil.raiseEvent("assignSevice_updateNhapBenhNhan", {
                    msg: 'Xử trí thành công' + '</br>' + msgCheck,
                    mode: opt._mode
                });
            }
        }
    }

    function themMoiThanhCong() {
        DlgUtil.showMsg("Thêm bệnh nhân thành công. Vui lòng xếp giường cho bệnh nhân", function () {
            clearSave();
        });
        gw_tiepnhankham($('#hidTIEPNHANID').val());//L2PT-40477
        //gw_batdaukham($('#hidTIEPNHANID').val());//L2PT-40477
    }

    function capNhatThanhCong() {
        EventUtil.raiseEvent("assignSevice_updateNhapBenhNhan", {
            msg: 'Cập nhật bệnh nhân thành công',
            mode: opt._mode,
            phongid: $("#cboKHOAID").val()
            //HaNv_160622: L2PT-21257
        });
        if (opt._mode == '1') {
            //gửi EMR khi thêm BN thành công
            var _dayTong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'EMR_DAYTONG_NHAPBENHNHAN');
            if (_dayTong !== '0') {
                var callback = function () {
                    if ($.active !== 0) {
                        setTimeout(callback, '100');
                        return;
                    }
                    var obj = new Object();
                    obj.hosobenhanid = $("#hidHOSOBENHANID").val();
                    obj.khambenhid = $("#hidKHAMBENHID").val();
                    obj.rpt_code = 'RPT_BIABENHAN';
                    obj.loai = '2';
                    var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H028.EV003", JSON.stringify(obj));
                    if (fl == '1') {
                        CommonUtil.sendAllEmr2($("#hidHOSOBENHANID").val(), _dayTong);
                    }
                };
                callback();
            }

            //xoa cac phieu kham benh. NGT_GIAYXACNHAN_TINHTRANGBENHTAT
            var obj = new Object();
            obj.hosobenhanid = $("#hidHOSOBENHANID").val();
            obj.khambenhid = $("#hidKHAMBENHID").val();
            jsonrpc.AjaxJson.ajaxCALL_SP_I("HIS.XOAPHIEU.NGT", JSON.stringify(obj));
        }
    }

    function isDangDieuTriNgoaiTruKhoaKhac(maBenhNhan) {
        var obj = {
            maBenhNhan: maBenhNhan + "",
            khoaHienTai: opt._deptId + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.L22", param);
        if (result == '1') {
            return true;
        } else {
            return false;
        }
    }

    // HungND - L2PT-62183
    function isDangDieuTriNoiTru(maBenhNhan) {
        var obj = {
            maBenhNhan: maBenhNhan + "",
            khoaHienTai: opt._deptId + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.L29", param);
        if (result == '1') {
            return true;
        } else {
            return false;
        }
    }

    // HungND - L2PT-62183 END

    function getBAIDFromMaBA(maLoaiBA) {
        var obj = {
            maLoaiBA: maLoaiBA + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L23", param);
        if (result !== '' && result != undefined && result != 'EXCEPTION_SQL') {
            return result;
        } else {
            return "";
        }
    }

    function isDangDTNTCheckMaBA() {
        if (_CAUHINH.NTU_NBN_KOCHECKMABADDTNT == '1') {
            var obj = {
                hosobenhanid: opt._hosobenhanId + "",
            }
            var param = JSON.stringify(obj);
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L21", param);
            if (result == '1') {
                return true;
            }
        }
        return false;
    }

    function conPhieuPTTTChuaHTQTDM(khambenhid) {
        var obj = {
            khambenhid: khambenhid + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L24", param);
        if (result !== '' && result != undefined && result != 'EXCEPTION_SQL') {
            return result;
        } else {
            return '0';
        }
    }

    function getTTBNTuCong(tenBenhNhan, namSinh, soThe) {
        var ret1 = _checkCongBHYT(i_u, i_p, soThe, tenBenhNhan, namSinh);
        return ret1;
    }

    function fillDataVaoForm(ret1) {
        $("#txtTENBENHNHAN").val(ret1.hoTen.toUpperCase());
        if (ret1.ngaySinh.length == "4") {
            $("#txtNAMSINH").val(ret1.ngaySinh);
            $("#txtNGAYSINH").val("");
            $("#txtNAMSINH").change();
        } else {
            $("#txtNGAYSINH").val(ret1.ngaySinh);
            $("#txtNGAYSINH").change();
        }
        $("#txtTKGIOITINH").val(ret1.gioiTinh == "Nam" ? 1 : 2);
        $("#cboGIOITINHID").val(ret1.gioiTinh == "Nam" ? 1 : 2);
        $('#txtMABHYT').val(ret1.maThe);
        $('#txtBHYT_BD').val(ret1.gtTheTu);
        $('#txtBHYT_KT').val(ret1.gtTheDen);
        $("#txtNGAYDU5NAM").val(ret1.ngayDu5Nam);
        $("#txtDIACHI_BHYT").val(ret1.diaChi);
        $("#txtTKDKKBBD").val(ret1.maDKBD);
        $("#cboDKKBBDID").empty();
        $("#cboDKKBBDID").append('<option value="' + ret1.maDKBD + '" selected>' + ret1.tenDKBDMoi + '</option>');
        for (i = 1; i <= 3; i++) {
            if (ret1.maKV == ("K" + i)) {
                $("#cboMAVUNGID").val(i);
            }
        }
    }

    function isChuaCoTienTamUng(tiepNhanID) {
        var obj = {
            tiepNhanID: tiepNhanID + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.L25", param);
        if (result == '1') {
            return true;
        } else {
            return false;
        }
    }

    function isDieuTriNgoaiTruKhoaKhamBenh() {
        if (opt._type == '3' && (_CAUHINH.NTU_NBN_IDKHOAKHAMBENH.includes(opt._deptId + ""))) {//L2PT-58388
            return true;
        } else {
            return false;
        }
    }

    function changeTinh(value, isID) {
        var valueID;
        var valueExt;
        if (isID) {
            valueID = value;
            valueExt = $("#cboHC_TINHID").find("option[value='" + value + "']").attr("extval0");
        } else {
            valueID = $("#cboHC_TINHID").find("option[extval0='" + value + "']").attr("value");
            valueExt = value;
        }
        $('#txtTKHC_TINH').val(valueExt);
        $("#cboHC_TINHID").val(valueID);
        resetInput(["txtTKHC_HUYEN", "txtTKHC_XA", "cboHC_XAID", "txtDIACHI", "txtDIACHI_BHYT"]);
        loadDataComboTHX(valueID, 'cboHC_HUYENID');
    }

    function changeHuyen(value, isID) {
        var valueID;
        var valueExt;
        if (isID) {
            valueID = value;
            valueExt = $("#cboHC_HUYENID").find("option[value='" + value + "']").attr("extval0");
        } else {
            valueID = $("#cboHC_HUYENID").find("option[extval0='" + value + "']").attr("value");
            valueExt = value;
        }
        $('#txtTKHC_HUYEN').val(valueExt);
        $("#cboHC_HUYENID").val(valueID);
        resetInput(["txtTKHC_XA", "cboHC_XAID", "txtDIACHI", "txtDIACHI_BHYT"]);
        loadDataComboTHX(valueID, 'cboHC_XAID');
    }

    function changeXa(value, isID) {
        var valueID;
        var valueExt;
        if (isID) {
            valueID = value;
            valueExt = $("#cboHC_XAID").find("option[value='" + value + "']").attr("extval0");
        } else {
            valueID = $("#cboHC_XAID").find("option[extval0='" + value + "']").attr("value");
            valueExt = value;
        }
        $('#txtTKHC_XA').val(valueExt);
        $("#cboHC_XAID").val(valueID);
        updateDiaChiFromTTHC();
    }

    function updateDiaChiFromTTHC() {
        var diaChiArr = []; // chi push khi co gia tri
        if ($('#txtSONHA').val().trim() != "") {
            diaChiArr.push($('#txtSONHA').val());
        }
        if ($('#cboHC_XAID').val() && $('#cboHC_XAID').val().trim() != "") {
            diaChiArr.push($('#cboHC_XAID option:selected').text());
        }
        if ($('#cboHC_HUYENID').val() && $('#cboHC_HUYENID').val().trim() != "") {
            diaChiArr.push($('#cboHC_HUYENID option:selected').text());
        }
        if ($('#cboHC_TINHID').val() && $('#cboHC_TINHID').val().trim() != "") {
            diaChiArr.push($('#cboHC_TINHID option:selected').text());
        }
        var diaChiStr = diaChiArr.join("-");
        $('#txtDIACHI').val(diaChiStr);
        if ($('#cboDOITUONGBENHNHANID').val() == '1') {
            $('#txtDIACHI_BHYT').val(diaChiStr);
        }
    }

    function loadDataComboTHX(chaID, cboID) {
        var par = chaID + "$";
        // clone from DMDP.001
        ComboUtil.getComboTag2(cboID, "NTU01H002.L26", par, "", {
            extval: true,
            value: '',
            text: '-- Chọn --'
        }, "sp", "", function (data) {
        });
    }

    function resetInput(arrID) {
        for (var i = 0; i < arrID.length; i++) {
            var ele = arrID[i];
            var type = ele.substr(0, 3);
            if (type == 'txt') {
                $('#' + ele).val("");
            } else if (type == 'cbo') {
                $("#" + ele).empty();
                $("#" + ele).append('<option value="">-- Chọn --</option>');
            }
        }
    }

    function getPhieuPTTTTHSauTimeRv() {
        var obj = {
            khambenhid: $("#hidKHAMBENHID").val() + "",
            thoigianravien: $('#txtTHOIGIANKETTHUC').val() + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L27", param);
        if (result != '' && result != undefined && result != 'EXCEPTION_SQL') {
            return result;
        } else {
            return '';
        }
    }

    function getKhoaMoBA(tiepNhanID, khoaHienTai) {
        var obj = {
            tiepNhanID: tiepNhanID + "",
            khoaHienTai: khoaHienTai + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L31", param);
        if (result != '' && result != undefined && result != 'EXCEPTION_SQL') {
            return result;
        } else {
            return '';
        }
    }

    function _caAutoEkipCa() {
        var obj = new Object();
        obj.KHOAID = opt._deptId;
        obj.HOSOBENHANID = $("#hidHOSOBENHANID").val();
        obj.KHAMBENHID = $("#hidKHAMBENHID").val();
        obj.RPT_CODE = 'NTU009_GIAYRAVIEN_01BV01_QD4069_A5';
        var _par = [{
            name: 'hosobenhanid',
            type: 'String',
            value: $("#hidHOSOBENHANID").val()
        }, {
            name: 'khambenhid',
            type: 'String',
            value: $("#hidKHAMBENHID").val()
        }, {
            name: 'rpt_code',
            type: 'String',
            value: 'NTU009_GIAYRAVIEN_01BV01_QD4069_A5'
        }];
        obj.PARAMHASHED = CryptoJS.MD5(JSON.stringify(_par).toUpperCase()).toString().toUpperCase();
        jsonrpc.AjaxJson.ajaxCALL_SP_I("HIS.EKIP.RAVIEN", JSON.stringify(obj));
    }

    function _caRpt(signType, _msgCheck) {
        var _rptCode = '';
        var _par = [{
            name: 'hosobenhanid',
            type: 'String',
            value: $("#hidHOSOBENHANID").val()
        }, {
            name: 'khambenhid',
            type: 'String',
            value: $("#hidKHAMBENHID").val()
        }];
        if (printCaRv) {
            _rptCode = 'NTU009_GIAYRAVIEN_01BV01_QD4069_A5';
        } else if (printCaCv) {
            if (opt.hospital_id == "902") {
                if (opt.doituongbenhnhanid == "1") {
                    _rptCode = 'NGT003_GIAYCHUYENTUYEN_BHYT_A4_902';
                } else {
                    _rptCode = 'NGT003_GIAYCHUYENTUYEN_TT14_A4';
                }
            } else if (opt.hospital_id == "932") {
                _rptCode = 'NGT003_GIAYCHUYENTUYEN_BHYT_A4_932';
            } else {
                _rptCode = 'NGT003_GIAYCHUYENTUYEN_TT14_A4';
            }
        } else if (printCaTv) {
            _rptCode = 'NGT022_GIAYBAOTU_04BV99';
        } else if (printCaHen) {
            _rptCode = 'NGT014_GIAYHENKHAMLAI_TT402015_A4';
        }
        _par.push({
            name: 'RPT_CODE',
            type: 'String',
            value: _rptCode
        });
        //trình ky
        var check = jsonrpc.AjaxJson.ajaxCALL_SP_I("EMR.GET.PTRINHKY", _rptCode);
        if (check == '1') {
            EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                DlgUtil.showMsg("Trình ký thành công! ");
                DlgUtil.close("divCALOGIN");
            });

            var _paramInput = {params: _par};
            var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM_KYCAP";
            var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, 'Trình ký số', 650, 400);
            popup.open("divCALOGIN");
        } else {
            CommonUtil.kyCA(_par, signType, true, true);
            EventUtil.setEvent("eventKyCA", function (e) {
                var _code = e.res.split("|")[0];
                var _msg = e.res.split("|")[1];
                if (_code == '0') {
                    if (signType == '1') {
                        EventUtil.raiseEvent("assignSevice_updateNhapBenhNhan", {
                            msg: 'Xử trí thành công' + '</br>' + _msgCheck + '</br>' + _msg,
                            mode: opt._mode
                        });
                        //L2PT-112020 luu chu ky
                        if (_CAUHINH_NBN[0].XUTRI_KYSO_XMLBHYT == '1') {
                            var xml_kyso = CommonUtil.getXmlCa(_par);
                            var signImg = xml_kyso.substring(xml_kyso.indexOf('<SignatureImage>'), xml_kyso.indexOf('</SignatureImage>')) + '</SignatureImage>';
                            var signData = xml_kyso.substring(xml_kyso.indexOf('<Signature '), xml_kyso.indexOf('</Signature>')) + '</Signature>';
                            var base64Sign = btoa(unescape(encodeURIComponent(signData)));
                            var base64Img = btoa(unescape(encodeURIComponent(signImg)));
                            var obj = new Object()
                            obj.HOSOBENHANID = $("#hidHOSOBENHANID").val();
                            obj.KHAMBENHID = $("#hidKHAMBENHID").val();
                            obj.XUTRIID = $('#cboXUTRIID').val()
                            obj.RPT_CODE = _rptCode;
                            obj.LOAI = '0';
                            obj.SIGN_DATA = base64Sign;
                            obj.SIGN_IMG = base64Img;
                            jsonrpc.AjaxJson.ajaxCALL_SP_I("HIS.SAVE.SIGNATURE", JSON.stringify(obj));
                        }
                    } else {
                        $("#btnKyCa").prop('disabled', false);
                        $("#btnSave").prop('disabled', false);
                        $("#btnSaveRv").prop('disabled', false);
                        $("#btnSaveCv").prop('disabled', false);
                    }
                }
                DlgUtil.showMsg(_msg);
            });
        }
    }

    // ham nay se tra ve thong tin bn de san sang setObjectToForm
    // nhap the bhyt thi lay ra ma bn roi goi ham nay
    function getTTBNFromMaBN(maBN) {
        var obj = {
            maBN: maBN + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("L7T.G03", param);
        if (result && result.length > 0) {
            return result[0];
        } else {
            return null;
        }
    }

    function getMaBNFromThe10So(maThe10So) {
        var obj = {
            maThe10So: maThe10So + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L37", param);
        if (result && result != 'EXCEPTION_SQL') {
            return result;
        } else {
            return '';
        }
    }

    function getBenhNhanNguoiNha(hosobenhanid) {
        var result = {
            BENHNHANNGUOINHA: "",
            BENHNHANNGUOINHAHSBAID: ""
        };
        var obj = {
            hosobenhanid: hosobenhanid + "",
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H002.L34", param);
        if (result && result.length > 0) {
            result = result[0];
        }
        return result;
    }

    // phong nhung id html lai lai cboKHOAID
    function generateComboBoxPhong() {
        var d = new $.Deferred;
        var obj = {
            loaitiepnhanid: opt._type + "",
            khoaid: opt._deptId + "",
            phongid: opt._subdept_id + "",
        }
        var param = JSON.stringify(obj);
        ComboUtil.getComboTag2("cboKHOAID", "NTU01H002.L39", param, "", {
            extval: true,
            value: '',
            text: '-- Chọn --'
        }, "sp", "", function (str) {
            d.resolve(str);
        });
        return d.promise();
    }

    function isPhongHienTaiLaDieuTriNgoaiTru() {
        var count = jsonrpc.AjaxJson.getOneValue("NTU01H002.L05", [{
            "name": "[0]",
            "value": opt._subdept_id
        }]);
        if (count > 0) {
            return true;
        } else {
            return false;
        }
    }

    function generateCombBoxKhoaDTNT() {
        ComboUtil.getComboTag("cboCHUYENKHOA", "KHOA.DTNGT1", [{
            "name": "[0]",
            "value": opt._deptId
        }], "", {
            value: 0,
            text: ''
        }, "sql");
    }

    function generateCombBoxKhoaNoitru(isChuyenTuDTNTVaoNoiTru) {
        var par = JSON.stringify({
            khoaid: opt._deptId + "",
            isChuyenTuDTNTVaoNoiTru: isChuyenTuDTNTVaoNoiTru + ""
        });
        ComboUtil.getComboTag2('cboCHUYENKHOA', "NTU01H002.L44", par, '', {
            value: 0,
            text: ''
        }, "sp", "", function (data) {
        });
    }

    function getDonThuocDangSua(tiepnhanid) {
        var obj = {
            tiepnhanid: tiepnhanid + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H002.L38", param);
        if (result && result != 'EXCEPTION_SQL') {
            return result;
        } else {
            return '';
        }
    }

    // HungND - L2PT-73623
    function kiemTraThanhToan() {
        var par = [];
        par.push({
            "name": "[0]",
            "value": $('#txtMABENHNHAN').val()
        });
        var count = jsonrpc.AjaxJson.getOneValue("NTU01H002.L03", par);
        return count;
    }

    // HungND - L2PT-73623 END
    // start jira L2PT-14071
    // sondn L2PT-24929
    function _loadHopDong(loaikhamid, hopdongid) {
        ComboUtil.getComboTag("cboHOPDONGID", "DS.HOPDONG1", [{
            "name": "[0]",
            "value": loaikhamid
        }], "", {
            value: 0,
            text: 'Chọn hợp đồng'
        }, "sql", "", function () {
            $("#cboHOPDONGID").val(hopdongid);
        });
    }

    // start jira L2PT-14071
    function loadDataComboXuTri(xuTriID) {
        var par = JSON.stringify({
            loaiTiepNhanID: opt._type + ""
        });
        ComboUtil.getComboTag2('cboXUTRIID', "NTU01H002.L42", par, setDefaultValueUndefined(xuTriID, ""), {
            extval: true,
            value: '',
            text: '-- Chọn --'
        }, "sp", "", function (data) {
            // jria L2PT-8034: dak nong đổi tên Bỏ về thành Trốn viện
            if (opt.hospitalId == '948') {
                $('#cboXUTRIID option:contains("Bỏ về")').text('Trốn viện');
            }
            if (_CAUHINH.NBN_CHANGE_NOIDUNG_XUTRI == '1') {
                $('#cboXUTRIID option:contains("Xin về")').text(' Xin ra viện');
                $('#cboXUTRIID option:contains("Bỏ về")').text('Trốn viện');
            }
            if (opt.hospitalId == '42240') { // Binh Dien HUE bo xu tri ĐƯA VỀ L2PT-121313
                $("#cboXUTRIID option[value=4]").remove();
            }
            $('#txtTKXUTRI').val($('#cboXUTRIID').val());
        });
    }

    function loadDataComboDoiTuongBN(data) {
        var par = JSON.stringify({
            loaiTiepNhanID: opt._type + ""
        });
        ComboUtil.getComboTag2('cboDOITUONGBENHNHANID', "NTU01H002.L43", par, setDefaultValueUndefined(data.DOITUONGBENHNHANID, ""), {
            extval: true
        }, "sp", "", function (data2) {
            // bật cấu hình này thì hiển thị theo sub đối tượng
            if (_CAUHINH.HIS_SUDUNG_DOITUONG_KHAIBAO == '1') {
                if (opt._mode == "0") {
                    $("#cboDOITUONGBENHNHANID").find("option[extval0='6']").attr("selected", "selected");//Begin_HaNv_03122018: thêm mới nếu có thu phí (6) thì mặc định thu phí - L2HOTRO-12590
                    $('#cboDOITUONGBENHNHANID').change();
                } else {
                    $('#cboDOITUONGBENHNHANID option').removeAttr('selected').filter('[value=val1]').attr('selected', true);
                    $("#cboDOITUONGBENHNHANID").find("option[extval0='" + data.SUB_DTBNID + "']").prop("selected", true);
                }
            } else {
                // start jira L2PT-17048
                // đa khoa hà nam mặc định viện phí
                if (opt._mode == "0" && opt.hospitalId == '915') {
                    $('#cboDOITUONGBENHNHANID').val("2").change();
                }
                // end jira L2PT-17048
                $('#txtTKDOITUONGBENHNHAN').val($('#cboDOITUONGBENHNHANID').val());
            }
        });
    }

    // clone từ màn NGT01T001_tiepnhan_ngt.js
    function _callCheckCong(cccd) {
        //L2PT-16155
        var mathe;
        if (cccd) {
            mathe = cccd;
        } else {
            mathe = $("#txtMABHYT").val().replace(/[-]/g, "").trim();
        }
        var tenbn = $("#txtTENBENHNHAN").val().trim();
        var namsinh = $("#txtNGAYSINH").val().trim() != "" ? $("#txtNGAYSINH").val().trim() : $("#txtNAMSINH").val().trim();
        var gioitinhid = $("#cboGIOITINHID").val();
        var noidk = $("#cboDKKBBDID").val() == null ? "" : $("#cboDKKBBDID").val();
        var ngaybd = $("#txtBHYT_BD").val().trim();
        var ngaykt = $("#txtBHYT_KT").val().trim();
        if (mathe == "" || tenbn == "" || namsinh == "" || gioitinhid == null || gioitinhid == "") {
            return "0";
        }
        var ret1 = _checkCongBHYT(i_u, i_p, mathe.trim(), tenbn, namsinh.trim(), gioitinhid, noidk, ngaybd, ngaykt, "0");
        return ret1;
    }

    function _muchuong_bhyt(i_phongid, i_madoituong, i_tuyen) {
        var hinhthucvaovienid = $("#cboHINHTHUCVAOVIENID").val();
        var objBH = new Object();
        objBH.MATHE = $("#txtMABHYT").val();
        objBH.DOITUONGSONG = $('#cboMAVUNGID').val();
        // WAIT FOR SYNCHRONIZATION BVNT
        var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.MUCHUONG.BHYT", i_phongid + '$' + i_madoituong.toUpperCase() + '$' + i_tuyen + '$' + JSON.stringify(objBH) + '$' + hinhthucvaovienid);
        // END WAIT FOR SYNCHRONIZATION BVNT
        if (data_ar != null && data_ar.length > 0) {
            var _bstr = "Ngoại (" + data_ar[0].MUCHUONG_NGOAI + "%)-Nội (" + data_ar[0].MUCHUONG_NOI + "%)";
            $("#txtMUCHUONG").val(_bstr);
            $("#hidMUCHUONG_NGT").val(data_ar[0].MUCHUONG_NGOAI);
            $("#txtQUYENLOI").val(i_madoituong.substr(2, 1));
            $("#hidBHYT_DOITUONG_ID").val(data_ar[0].BHYT_DOITUONG_ID);
        }
    }

    function loadTuyen(tuyenid) {
        if (tuyenid == 2 && _CAUHINH_NBN[0].NTU_NBN_CHECK_GIAYTOCT && _CAUHINH_NBN[0].NTU_NBN_CHECK_GIAYTOCT == '1') {
            $('#dvBHYT_LOAIID1').show();
            $("#dvBHYT_LOAIID").removeClass("col-xs-4 low-padding");
            $("#dvBHYT_LOAIID").addClass("col-xs-2 low-padding");
            $("#cboBHYT_LOAIID1").prop('disabled', true);
        }
        // start jira 9601. truong hop nhap benh nhan thi chon mac dinh la dung tuyen
        var _tuyen = '1';
        //L2PT-26681 START khong chon mac dinh la dung tuyen
        if (_CAUHINH.NBN_KHONGMACDINH_TUYEN == '1') {
            _tuyen = '';
        }
        //L2PT-26681 end
        if (_CAUHINH.NBN_CHONTUYEN_THEOKHOA != '0') {
            _tuyen = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H002.EV020", opt._deptId);
            //Beg_HaNv_181022: Đúng tuyến câp cứu thì mặc định vào từ khoa cấp cứu -
            if (_tuyen == '3') {
                ComboUtil.getComboTag("cboHINHTHUCVAOVIENID", "NT.0010", [{
                    "name": "[0]",
                    "value": "4"
                }], setDefaultValueUndefined("2", ""), "", "sql");
            }
            //End_HaNv_181022
        }
        if (opt._mode == '0') {
            ComboUtil.getComboTag("cboTUYENID", "COM.LOAIBHYT", [], setDefaultValueUndefined(_tuyen, ""), {
                value: '',
                text: 'Chọn'
            }, "sql");
            // end jira 9601
        } else {
            ComboUtil.getComboTag("cboTUYENID", "COM.LOAIBHYT", [], setDefaultValueUndefined(tuyenid, ""), {
                value: '',
                text: 'Chọn'
            }, "sql");
        }
    }

    //HungND - L2PT-74405 - 240304
    function _checkCBBN(ICD, _type) {
        //if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'HIS_CANHBAO_BN') == '1') {
        var objCheck = new Object();
        objCheck["TIEPNHANID"] = $("#hidTIEPNHANID").val();
        objCheck["ICD10"] = nvl(ICD, '0');
        objCheck["TYPE"] = _type;
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("CDDV.CBBN", JSON.stringify(objCheck));
        var obj = JSON.parse(result);
        if (obj.KETQUA && obj.KETQUA != '0' && obj.KETQUA != '1') {
            DlgUtil.showMsg(obj.GHICHU);
            if (obj.KETQUA == '3') {
                return 1;
            }
        }
        return 0;
        //}
    }

    //HungND - L2PT-74405 - 240304 END
    function checkCMND(str) {
        var length = str.length;
        if (length !== 8 && length !== 9 && length !== 12) {
            return false;
        }
        var specialCharPattern = /[!@#$%^&*()<>?:,]/;
        if (specialCharPattern.test(str)) {
            return false;
        }
        if (length === 8) {
            var firstChar = str.charAt(0);
            var remainingChars = str.slice(1);
            var numberPattern = /^[0-9]+$/;
            if (!firstChar.match(/[A-Z]/) || !numberPattern.test(remainingChars)) {
                return false;
            }
        }
        if (length === 9 || length === 12) {
            var numberPattern = /^[0-9]+$/;
            if (!numberPattern.test(str)) {
                return false;
            }
        }
        return true;
    }
};

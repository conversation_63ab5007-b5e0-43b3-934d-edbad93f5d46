<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}"/>
<input type="hidden" name="user_id" id="user_id" value="{user_id}"/>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js?20111113"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>

<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>

<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../noitru/NTU01H104_KyCap.js?v=000425"></script>
<script type="text/javascript" src="../common/script/EmrUtils.js"></script>
<link href="../common/script/select2/dist/css/select2.css" rel="stylesheet" />
<script src="../common/script/select2/dist/js/select2.full.js"></script>

<div width="100%" id="divMain" class="container">
    <div class="col-md-12 low-padding mgt5">
        <ul id="tabs" class="nav nav-tabs mgl15" style="margin-top: 10px;" data-tabs="tabs">
            <li id="btnList1" class="active">
                <a href="#divPhieuKyCap" data-toggle="tab" id="tabPhieuKyCap">Phiếu Ký Cấp</a>
            </li>
            <li id="btnList2">
                <a href="#divPhieuDaTao" data-toggle="tab" id="tabPhieuDaTao">Phiếu Đã Tạo</a>
            </li>
        </ul>
        <div id="divSearch" class="col-md-4 low-padding mgt-5 tab-content">
            <div class="form-inline border-group-1 mgr-1 mgt5 mgl15">
                <div class=" form-inline mgt5">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input class="form-control input-sm" id="txtTUNGAY" name="txtTUNGAY"
                                   title="" data-mask="00/00/0000" placeholder="Từ ngày">
                            <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                  type="sCal" id="btnTUNGAY"
                                  onclick="NewCssCal('txtTUNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input class="form-control input-sm" id="txtDENNGAY" name="txtDENNGAY"
                                   title="" data-mask="00/00/0000" placeholder="Đến ngày">
                            <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                  type="sCal"
                                  id="btnDENNGAY"
                                  onclick="NewCssCal('txtDENNGAY','ddMMyyyy','dropdown',false,'24',true)"></span>
                        </div>
                    </div>
                </div>
                <div class=" form-inline mgt5" id="div_search1" style="display: none">
                    <div class="col-md-6">
                        <select class="form-control input-sm" id="cboKHOA" style="width: 100%;">
                        </select>
                    </div>
                    <div class="col-md-6">
                        <select class="form-control input-sm" id="cboLOAIPHIEUID" style="width: 100%;">
                        </select>
                    </div>
                </div>
                <div class=" form-inline mgt5" id="div_search2" style="display: none">
                    <div class="col-md-6">
                        <select class="form-control input-sm" id="cboBRANCHID" style="width: 100%;">
                            <option value="-1" selected>-- CƠ SỞ --</option>
                            <option value="1">CƠ SỞ I</option>
                            <option value="2">CƠ SỞ II</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <select class="form-control input-sm" id="cboKHOA2" style="width: 100%;">
                        </select>
                    </div>
                </div>
                <div class=" form-inline">
                    <div class="col-md-6 mgt5 mgb5">
                        <select class="form-control input-sm" id="cboTRANGTHAI" style="width: 100%;">
                            <option value="-1">-- Chọn --</option>
                            <option value="1" selected>Chưa ký</option>
                            <option value="2">Đã ký</option>
                            <option value="3">Đã hủy</option>
                            <option value="4">Từ chối</option>
                        </select>
                    </div>
                    <div class="col-md-6 mgt5 mgb5">
                        <select class="form-control input-sm" id="cboPHIEUID" style="width: 100%;">
                        </select>
                    </div>
                </div>
                <div class=" form-inline">
                    <div class="col-md-8 mgt3 mgb3">
                        <button type="button" class="btn btn-sm btn-primary" id="btnTIMKIEM"
                                style="width: 100%; float: right;">
                            <span class="glyphicon glyphicon-search"></span> Search
                        </button>
                    </div>
                    <div class="col-md-4 mgt3 mgb3">
                        <button type="button" class="btn btn-sm btn-primary" id="btnMORONG"
                                style="width: 120%; float: right;">
                            <span class="glyphicon"></span>Mở rộng >>
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" id="btnTHUNHO"
                                style="width: 100%; float: right; display: none">
                            <span class="glyphicon"></span> Thủ nhỏ <<
                        </button>
                    </div>
                </div>
            </div>
            <div id="divPhieuKyCap" class="tab active">
                <div class="mgl15 mgt-5">
                    <table id="gridPhieu"></table>
                    <div id="pager_gridPhieu"></div>
                </div>
                <div class="mgl15 mgt-5">
                    <label style="color: red">double click để xem danh sách trình ký, và cấu hình người ký</label>
                </div>
                <div class="mgl15 mgt-5" id="div_duyetmo" style="display: none">
                    <label style="color: blue">BN đã duyệt mổ</label>
                </div>
            </div>
            <div id="divPhieuDaTao" style="display: none">
                <div class="mgl15">
                    <table id="gridPhieuDatao"></table>
                    <div id="pager_gridPhieuDatao"></div>
                </div>
            </div>
        </div>
        <div class="col-md-8 low-padding" style="padding-left: 15px !important;" id="divTT">
            <div id="inputForm" class="panel panel-default">
                <div class="panel-heading">Thông tin phiếu</div>
                <div class="panel-body">
                    <div class="col-md-12 low-padding mgt5" id="div_default">
                        <div class="col-md-12" id="divTTphieu">
                            <div class="col-md-12 ">
                                <div class="col-md-2 low-padding">
                                    <label class="">Số phiếu : </label>
                                </div>
                                <div class="col-md-10 low-padding">
                                    <label class="notW" id="lblSOPHIEU"></label>
                                </div>
                            </div>
                            <div class="col-md-12 ">
                                <div class="col-md-2 low-padding">
                                    <label class="">Tên phiếu : </label>
                                </div>
                                <div class="col-md-10 low-padding">
                                    <label class="notW" id="lblTENPHIEU"></label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="col-md-2 low-padding">
                                    <label class="">Ngày tạo : </label>
                                </div>
                                <div class="col-md-10 low-padding">
                                    <label class="notW" id="lblNGAYTAO"></label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12 mgt10" id="divduyetky" style="display: none">
                            <div class="col-md-12 ">
                                <div class="col-md-2 low-padding">
                                    <strong>Xác nhận ký </strong>
                                </div>
                            </div>
                            <div class="col-md-12 mgt5">
                                <div class="col-md-2 low-padding">
                                    <label class="">Ngày xác nhận : </label>
                                </div>
                                <div class="col-md-4 low-padding">
                                    <div class="input-group">
                                        <input class="form-control input-sm" id="txtNGAYXACNHAN" name="txtNGAYXACNHAN"
                                               title="" data-mask="00/00/0000" placeholder="dd/mm/yyyy hh24:mi:ss">
                                        <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                              type="sCal" id="btnNGAYXACNHAN"
                                              onclick="NewCssCal('txtNGAYXACNHAN','ddMMyyyy','dropdown',false,'24',true)"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 ">
                                <div class="col-md-5 low-padding">
                                    <label>Thống nhất sử dụng kháng sinh như trên : </label>
                                </div>
                                <div class="col-xs-2 low-padding">
                                    <input type="radio" name="radDUYETKY" value="1" checked="checked"> Có
                                </div>
                                <div class="col-xs-2 low-padding">
                                    <input type="radio" name="radDUYETKY" value="0"> Không
                                </div>
                            </div>
                            <div class="col-md-12 ">
                                <div class="col-md-2 low-padding">
                                    <label >Ý kiến khác : </label>
                                </div>
                                <div class="col-md-10 low-padding">
                                <textarea class="form-control input-md" id="txtYKIENKHAC" rows="2"
                                          style="width: 96%"></textarea>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="col-md-12 low-padding mgt5 mgb10" style="text-align: center;">
                            <button type="button" class="btn btn-sm btn-primary" id="btnKySo">
                                <span class="glyphicon glyphicon-pencil"></span> Ký số
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnHuyKy">
                                <span class="glyphicon glyphicon-edit"></span> Hủy ký
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnInKy">
                                <span class="glyphicon glyphicon-print"></span> In ký
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnTuChoi" style="display: none">
                                <span class="glyphicon glyphicon-edit"></span> Từ chối
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnXuatPDF">
                                <span class="glyphicon glyphicon-ok"></span> Xuất PDF BA
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnBAEMR">
                                <span class="glyphicon glyphicon-ok"></span> HSBA EMR
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="btnLichSu">
                                <span class="glyphicon glyphicon-eye-close"></span> Lịch sử điều trị
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div id="viewContainer">
                <div id="loading" class="hide">
                    <span class="spinner"></span>
                </div>
                <iframe id="viewIframe"></iframe>
            </div>
        </div>
    </div>
</div>
<style>
    #viewContainer {
        position: relative;
        width: 100%;
        overflow: hidden;
        padding-top: 56.25%;
    }

    @keyframes spinner {
        to {
            transform: rotate(360deg);
        }
    }

    #viewIframe {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
        border: none;
    }

    #loading.show {
        display: block;
        z-index: 999;
    }

    #loading.hide {
        display: none;
        z-index: -1;
    }

    .spinner:before {
        content: '';
        box-sizing: border-box;
        position: absolute;
        top: 50%;
        left: 50%;
        width: 40px;
        height: 40px;
        margin-top: -20px;
        margin-left: -20px;
        border-radius: 50%;
        border: 4px solid #000;
        border-top-color: #fff;
        animation: spinner 1s linear infinite;
    }

    #viewContainer #loading {
        position: absolute;
        z-index: 999;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #cacaca;
    }

    .ui-jqgrid-htable,
    .ui-jqgrid-btable,
    .ui-jqgrid-pager,
    .ui-jqgrid-view,
    .ui-jqgrid-bdiv,
    .ui-jqgrid-hdiv,
    .ui-jqgrid-hbox,
    .ui-jqgrid {
        max-width: 100% !important;
        width: 100% !important;
    }
</style>
<script>
    var opt = [];
    var hospital_id = '{hospital_id}';
    var user_id = '{user_id}';
    var user_type = '{user_type}';
    var province_id = '{province_id}';
    var dept_id = '{dept_id}';
    var subdept_id = '{subdept_id}';
    var db_schema = '{db_schema}';
    var uuid = '{uuid}';
    var lang = "vn";
    console.log('hospital_id=' + hospital_id + ' user_type=' + user_type);
    var session_par = [];
    session_par[0] = hospital_id;
    session_par[1] = user_id;
    session_par[2] = user_type;
    session_par[3] = province_id;
    session_par[4] = dept_id;
    session_par[5] = subdept_id;
    session_par[6] = db_schema;
    var table_name = '{table}';

    initRest(uuid, "/vnpthis");

    var _opts = new Object();
    _opts.lang = lang;
    _opts._param = session_par;
    _opts._uuid = uuid;
    _opts.dept_id = dept_id;

    var DS = new dsKyCap(_opts);
    DS.load(hospital_id);
</script>
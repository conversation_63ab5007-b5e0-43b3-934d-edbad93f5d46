/*
 Mã màn hình  : NTU02D001
 File mã nguồn : NTU02D001_TaoBenhAn.js
 <PERSON><PERSON><PERSON> đích  : <PERSON><PERSON><PERSON> diện màn hình
 	+ <PERSON>h<PERSON>p thông tin bệnh án
 	+ <PERSON><PERSON><PERSON><PERSON> thông tin hỏi bệnh
 Tham số vào : 
 	khambenhid 		: <PERSON><PERSON>m bệnh ID 
	
 Người lập tr<PERSON><PERSON> cập nhật  	<PERSON>hi chú
 linhvv				 5/9/2016			Sửa bổ xung tính năng
*/
function NT02D001(_opts) {
	this.load = doLoad;
	var _options = $.extend({}, _opts);
	var uuid = _options._uuid;
	var _param = _options._param;
	function doLoad() {
		_initControl(_opts);
		_bindEvent();
	}
	function _initControl(_options) {
		// L2PT-76863 start
    	if (_opts.trangthaikhambenh == 9){
			$("#divMain").find("button").not("#btnIn").remove();
		}
		// L2PT-76863 end
		var tab_ba = new tab_BenhAn(_options);
		tab_ba.load();
		var tab_kb = new tab_KhamBenh(_options);
		tab_kb.load();
		//$("#txtMA_CHANDOAN_VAO_CHINH").focus();
	}
	function _bindEvent() {
		var sql_par = [];
		if (typeof _param !== "undefined") {
			sql_par = RSUtil.setSysParam(sql_par, _param);
		}
	}
}
function tab_BenhAn(_opts) {
	var _options = $.extend({}, _opts);
	var _company_id = _options._hospital_id;
	var _user_id;
	var _data = [];
	var _rowKHAMBENH;
	var _rowDACDIEM;
	var _mode = 'edit';
	var _editMode = 'NEW';
	var _lstObj = "SOTHANG_DIUNG,SOTHANG_THUOCLA,SOTHANG_MATUY,SOTHANG_THUOCLAO,SOTHANG_RUOUBIA,SOTHANG_KHAC,COTHAI";//START L1PT-913
	var _arr;
	var _url;
	var _khambenh_id = _opts.khambenh_id;
	var _hosobenhanid = _opts.hosobenhanid;
	var _loaibenhanid = _opts.loaibenhanid;
	var sql_par = [];
	var cfObj = new Object(); //L2PT-91925
	_colICD = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
	_colPhacdo = "PĐ id,PHACDODTID,30,0,t,l;Mã PĐ,MAPHACDODT,30,0,f,l;Tên Phác đồ,TENPHACDODT,70,0,f,l";
	phacdodtID = "-1";
	var controlActive = "";
	var _numberFormat = {
		prefix : '',
		thousandsSeparator : '.',
		centsLimit : 0
	};
	var _enter = $.Event('keypress');
	_enter.which = 13;
	var _param = [ _company_id ];
	var _col = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
	var load_icd_yhct_sl = false;
	//var _sql="select icd10code,icd10name from dmc_icd where upper(icd10code) like upper('%[FILTER]%')";
	var valid_ar = [ {
		"name" : "txtICD10_CODE",
		"display" : "Mã ICD10",
		"rules" : "trim_required"
	}, {
		"name" : "cboICD10_NAME",
		"display" : "Tên bệnh",
		"rules" : "trim_required"
	}, {
		"name" : "txtLY_DO",
		"display" : "Lý do",
		"rules" : "trim_required"
	}, {
		"name" : "txtQT_BENHLY",
		"display" : "Quá trình bệnh lý",
		"rules" : "trim_required"
	}, {
		"name" : "txtTIENSU_BANTHAN",
		"display" : "Tiền sử bản thân",
		"rules" : "trim_required"
	}, {
		"name" : "txtSOTHANG_DIUNG",
		"display" : "Số tháng dị ứng",
		"rules" : "trim_required"
	}, {
		"name" : "txtSOTHANG_THUOCLA",
		"display" : "Số tháng hút thuốc lá",
		"rules" : "trim_required"
	}, {
		"name" : "txtSOTHANG_MATUY",
		"display" : "Số tháng sử dụng ma túy",
		"rules" : "trim_required"
	}, {
		"name" : "txtSOTHANG_THUOCLAO",
		"display" : "Số tháng hút thuốc lào",
		"rules" : "trim_required"
	}, {
		"name" : "txtSOTHANG_RUOUBIA",
		"display" : "Số tháng uống rượu, bia",
		"rules" : "trim_required"
	}, {
		"name" : "txtSOTHANG_KHAC",
		"display" : "Khác",
		"rules" : "trim_required"
	}, {
		"name" : "txtCOTHAI",
		"display" : "Có thai",
		"rules" : "trim_required"
	} ];//START L1PT-913
	var that = this;
	this.load = doLoad;
	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		_arr = _lstObj.split(",");
		this.validator = new DataValidator("divHOIBENH");
		//Load benh ICD10
		//doLoadCombo("txtMACHANDOANVAOVIEN", "txtCHANDOANVAOVIEN");
		$("#txtLYDOVAOVIEN").focus();
		//Lay thong tin 
		var data_ar;
		//doResetForm();
		data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.01", _khambenh_id, []);
		//data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("{?=call Noitru_Duoc.Ntu02d001_Taobenhan_get('[UID]','[SCH]','[IP]','[HID]',?2L)}",_khambenh_id,[]);
		if (data_ar != null && data_ar.length > 0) {
			_rowDACDIEM = data_ar[0];
			FormUtil.setObjectToForm("", "", _rowDACDIEM);
			for (var i = 0; i < _arr.length; i++) {
				setCheckBoxState(_rowDACDIEM, _arr[i]);
			}
		}
		data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.02.1", _khambenh_id, []);
		if (data_ar != null && data_ar.length > 0) {
			_rowKHAMBENH = data_ar[0];
			FormUtil.setObjectToForm("divHOIBENH", "", _rowKHAMBENH);
			$('#txtGHICHU_BENHCHINH').val(_rowKHAMBENH.GHICHU_BENHCHINH);
			$('#txtGHICHU_BENHCHINH_RAVIEN').val(_rowKHAMBENH.GHICHU_BENHCHINH_RAVIEN);//hongdq-- HISL2TK-520 --18052018
			$('#txtGHICHU_BENH_KEMTHEO').val(_rowKHAMBENH.GHICHU_BENHKEMTHEO);//L2PT-8467
			$('#txtHO_TEN_CHA').val(_rowKHAMBENH.HO_TEN_CHA);//L2PT-29909
			$('#txtHO_TEN_ME').val(_rowKHAMBENH.HO_TEN_ME);//L2PT-29909
		}
		bindEvent();
		//START L1PT-913
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","OPT_HOSPITAL_ID;DKLSO_SUAICD_BENHAN;" +
				"NTU_BA_SHOW_CHAME;KB_ANGHICHUBENHCHINH;NTU_BAC_LOAD_YHCT_SELECT;QD_4750");//L2PT-7751 L2PT-91925
		if(data_ar != null && data_ar.length > 0){
			cfObj = data_ar[0]; //L2PT-91925
			if(data_ar[0].OPT_HOSPITAL_ID == '1014'){
				$("#divCOTHAI").show();
			}else{
				$("#divCOTHAI").hide();
			}
			
			if(data_ar[0].DKLSO_SUAICD_BENHAN == '1' || data_ar[0].QD_4750 == '1' ){ //L2PT-91925
				$("#txtMACHANDOANVAOVIEN").removeAttr('readonly');
				$("#txtCHANDOANVAOVIEN").removeAttr('readonly');
				//L2PT-25475
				$("#txtMACHANDOANVAOVIEN_KT").removeAttr('readonly');
				$("#txtCHANDOANVAOVIEN_KT").removeAttr('readonly');
			}
			//L2PT-29909
			if(data_ar[0].NTU_BA_SHOW_CHAME == '1'){
				$("#dvhoten_chame").show();
			}
			//L2PT-1059
			if(data_ar[0].KB_ANGHICHUBENHCHINH == '1'){
				$("#txtGHICHU_BENHCHINH").hide();
				$("#txtGHICHU_BENHCHINH_RAVIEN").hide();
			}
			//L2PT-8701 load icd yhct
			if(data_ar[0].NTU_BAC_LOAD_YHCT_SELECT == '1'){
				load_icd_yhct_sl = true;
			}
		}
		//Load benh ICD10
		doLoadCombo("txtMACHANDOANVAOVIEN", "txtCHANDOANVAOVIEN");
		//L2PT-25475
		doLoadCombo("txtMACHANDOANVAOVIEN_KT", "txtCHANDOANVAOVIEN_KT");

		// Xử lý sự kiện liên quan ký CA => START
		var kyso_kydientu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU');
		if (kyso_kydientu == "1") {
			$("#btnKyCa").show();
			$("#btnHuyCa").show();
			$("#btnExportCa").show();
			$("#div_button").removeClass("mgb10");
			$("#div_button").addClass("mgb5");
		}
		// Xử lý sự kiện liên quan ký CA => END
	}
	function doIns() {
		var objData = new Object();
		FormUtil.setFormToObject("divHOIBENH", "", objData);
		objData.KHAMBENH_ID = _khambenh_id;
		objData.SOTHANG_DIUNG = $("#txtSOTHANG_DIUNG").val();
		objData.SOTHANG_THUOCLA = $("#txtSOTHANG_THUOCLA").val();
		objData.SOTHANG_MATUY = $("#txtSOTHANG_MATUY").val();
		objData.SOTHANG_THUOCLAO = $("#txtSOTHANG_THUOCLAO").val();
		objData.SOTHANG_RUOUBIA = $("#txtSOTHANG_RUOUBIA").val();
		objData.SOTHANG_KHAC = $("#txtSOTHANG_KHAC").val();
		objData.COTHAI = $("#txtCOTHAI").val();//START L1PT-913
		if (cfObj.QD_4750 && ($('#txtLYDOVAOVIEN').val() == '' || $('#txtLYDOVAOVIEN').val() == null)) {
			DlgUtil.showMsg("Bệnh nhân chưa nhập lý do vào viện!");
			$('#txtLYDOVAOVIEN').focus();
			return false;
		}
		var valid = that.validator.validateForm();
		if (valid) {
			if (checkInt()) {
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D001.03.1", JSON.stringify(objData));
				if (ret == 1) {
					var _par = [ _hosobenhanid, _khambenh_id, _loaibenhanid, phacdodtID ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONGBO.LEFT", _par.join('$'));
					if (_return == 1) {
						//HaNv_240723: L2PT-47559
						DlgUtil.showMsg("Cập nhật bệnh án thành công",undefined,jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_TIMEOUT_THONGBAO'));
					} else {
						DlgUtil.showMsg("Có lỗi khi thực hiện!");
					}
				} else if (ret == -1) {
					DlgUtil.showMsg("Có lỗi khi thực hiện!");
				} else if (ret == -2) {
					DlgUtil.showMsg("Lỗi do nhập sai mã ICD10!");
					$("#txtMACHANDOANVAOVIEN").focus();
				}
			}
		}
		//return DlgUtil.showMsg("Nhập đúng định dạng dữ liệu!");
	}
	function doInsClose() {
		var objData = new Object();
		FormUtil.setFormToObject("divHOIBENH", "", objData);
		objData.KHAMBENH_ID = _khambenh_id;
		objData.SOTHANG_DIUNG = $("#txtSOTHANG_DIUNG").val();
		objData.SOTHANG_THUOCLA = $("#txtSOTHANG_THUOCLA").val();
		objData.SOTHANG_MATUY = $("#txtSOTHANG_MATUY").val();
		objData.SOTHANG_THUOCLAO = $("#txtSOTHANG_THUOCLAO").val();
		objData.SOTHANG_RUOUBIA = $("#txtSOTHANG_RUOUBIA").val();
		objData.SOTHANG_KHAC = $("#txtSOTHANG_KHAC").val();
		objData.COTHAI = $("#txtCOTHAI").val();//START L1PT-913
		objData.GHICHU_BENHCHINH = $('#txtGHICHU_BENHCHINH').val();
		objData.GHICHU_BENH_KEMTHEO = $('#txtGHICHU_BENH_KEMTHEO').val();//L2PT-8467
		var valid = that.validator.validateForm();
		if (valid) {
			if (checkInt()) {
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D001.03.1", JSON.stringify(objData));
				//alert(ret)
				if (ret == 1) {
					var _par = [ _hosobenhanid, _khambenh_id, _loaibenhanid, phacdodtID ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONGBO.LEFT", _par.join('$'));
					if (_return == 1) {
						var evFunc = EventUtil.getEvent("assignSevice_saveBenhAn");
						if (typeof evFunc === 'function') {
							evFunc({
								msg : "Cập nhật bệnh án thành công"
							});
						} else {
							console.log('evFunc not a function');
						}
					} else {
						DlgUtil.showMsg("Có lỗi khi thực hiện!");
					}
				} else if (ret == -1) {
					DlgUtil.showMsg("Có lỗi khi thực hiện!");
				} else if (ret == -2) {
					DlgUtil.showMsg("Lỗi do nhập sai mã ICD10!");
					$("#txtMACHANDOANVAOVIEN").focus();
				}
			}
		}
		//return DlgUtil.showMsg("Nhập đúng định dạng dữ liệu!");
	}
	function doResetForm() {
		$("#txtMACHANDOANVAOVIEN").val("");
		$("#txtCHANDOANVAOVIEN").val("");
		$("#txtLYDOVAOVIEN").val("");
		$("#txtQUATRINHBENHLY").val("");
		$("#txtTIENSUBENH_BANTHAN").val("");
		$("#txtTIENSUBENH_GIADINH").val("");
		for (var i = 0; i < _arr.length; i++) {
			setCheckBoxState([], _arr[i]);
		}
	}
	function bindEvent() {
		$("#chkSOTHANG_DIUNG").on("click", function(e) {
			onClickCheckBox(_rowDACDIEM, "SOTHANG_DIUNG");
		});
		$("#chkSOTHANG_THUOCLA").on("click", function(e) {
			onClickCheckBox(_rowDACDIEM, "SOTHANG_THUOCLA");
		});
		$("#chkSOTHANG_MATUY").on("click", function(e) {
			onClickCheckBox(_rowDACDIEM, "SOTHANG_MATUY");
		});
		$("#chkSOTHANG_THUOCLAO").on("click", function(e) {
			onClickCheckBox(_rowDACDIEM, "SOTHANG_THUOCLAO");
		});
		$("#chkSOTHANG_RUOUBIA").on("click", function(e) {
			onClickCheckBox(_rowDACDIEM, "SOTHANG_RUOUBIA");
		});
		$("#chkSOTHANG_KHAC").on("click", function(e) {
			onClickCheckBox(_rowDACDIEM, "SOTHANG_KHAC");
		});
		//START L1PT-913
		$("#chkCOTHAI").on("click", function(e) {
			onClickCheckBox(_rowDACDIEM, "COTHAI");
		});
		$("#btnSave").on("click", function(e) {
			doInsClose();
		});
		$("#btnLuuHoiBenh").on("click", function(e) {
			doIns();
		});
		$("#btnDongHoiBenh").on("click", function(e) {
			closeHoiBenh();
		});
	}
	function closeHoiBenh() {
		var evFunc = EventUtil.getEvent("assignSevice_closeBenhAn");
		if (typeof evFunc === 'function') {
			evFunc({
				msg : ""
			});
		}
	}
	function onClickCheckBox(row, _chkName) {
		if ($('#chk' + _chkName).is(":checked")) {
			$("#chk" + _chkName).prop('checked', true);
			if (row[_chkName]) {
				$("#txt" + _chkName).val(row[_chkName]);
				if ($("#txt" + _chkName).val() == "0")
					$("#txt" + _chkName).val("");
			} else
				$("#txt" + _chkName).val("0");
			$("#txt" + _chkName).prop('readonly', false);
			$("#txt" + _chkName).focus();
		} else {
			$("#chk" + _chkName).prop('checked', false);
			//if(parseInt($("#txt"+_chkName).val()) > 0)
			$("#txt" + _chkName).val("0");
			$("#txt" + _chkName).prop('readonly', true);
		}
	}
	function checkInt() {
		var _lstObj = 'SOTHANG_DIUNG,SOTHANG_THUOCLA,SOTHANG_MATUY,SOTHANG_THUOCLAO,SOTHANG_RUOUBIA,SOTHANG_KHAC,COTHAI';//START L1PT-913
		var _lstText = 'Dị ứng,Thuốc lá,Ma túy,Thuốc lào,Rượu bia,Khác';
		var _arr = _lstObj.split(",");
		var _arrText = _lstText.split(",");
		for (var i = 0; i < _arr.length; i++) {
			if ($('#chk' + _arr[i]).is(":checked")) {
				if (parseInt($("#txt" + _arr[i]).val()) < 0) {
					DlgUtil.showMsg(_arrText[i] + " luôn là số nguyên dương!");
					$("#txt" + _arr[i]).focus();
					return false;
				}
			}
		}
		return true;
	}
	function setCheckBoxState(row, _key) {
		if (row[_key] != undefined && parseInt(row[_key]) > 0) {
			$("#chk" + _key).prop('checked', true);
			$("#txt" + _key).prop('readonly', false);
		} else {
			$("#" + _key).prop('checked', false);
			$("#txt" + _key).prop('readonly', true);
			$("#txt" + _key).val("0");
		}
	}
	function doLoadCombo(_txt, _txtDst) {
		var _selfnc = function(event, ui) {
			//L2PT-9382
			if(load_icd_yhct_sl){
				$("#" + _txt).val(ui.item.ICD10CODE);
				$("#" + _txtDst).val(ui.item.YHCTNAME +' ['+ ui.item.ICD10NAME + '] ');
			}else{
				//L2PT-25475
				if(_txt == 'txtMACHANDOANVAOVIEN_KT'){
					var str = $("#"+_txtDst).val();
					if (str != '')
						str += ";";
					$("#"+_txtDst).val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
				}else{
					$("#" + _txt).val(ui.item.ICD10CODE);
					$("#" + _txtDst).val(ui.item.ICD10NAME);
				}
				
			}
			
			return false;
		};
		//L2PT-9382
		if(load_icd_yhct_sl){
			var sql_icd= 'NT.008.YHCTV3';
			_col = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
		}else{
			var sql_icd= 'NT.SEARCH.ICD10';
		}
		ComboUtil.initComboGrid(_txt, sql_icd , sql_par, "900px", _col, _selfnc);
	};
	function validateCheckbox(_chkName) {
		if ($('#chk' + _chkName).is(":checked")) {
			if ($("#txt" + _chkName).val() == "" || $("#txt" + _chkName).val() == "0")
				return false;
			else
				return true;
		} else
			return true;
	}
	$('.input-sm').keydown(function(e) {
		if (e.which === 13) {
			var index = $('.input-sm').index(this) + 1;
			$('.input-sm').eq(index).focus();
		}
	});
}
/*-------------------------------TAB KHAM BENH-----------------------------------------------------*/
function tab_KhamBenh(_opts) {
	var _user_id;
	var _options = $.extend({}, _opts);
	var _khambenh_id = _options.khambenh_id;
	var _hosobenhanid = _options.hosobenhanid;
	var _loaibenhanid = _options.loaibenhanid;
	var _col = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
	var kobatbuoc_benhchinh;//BVTM-2546
	var yhctid_rv, yhctid_vk, cauhinh_icdyhct ;//START L2PT-16663
	var load_icd_yhct_sl = false;//L2PT-8701 load icd yhct
	var load_nhommau_rh =false;//BVTM-7586
	var chuyenkhoaid; 
	var his_qd130 = false; //L2PT-60364
	var showyhct4750 = '0'; //L2PT-93075
	//var _sql="select icd10code,icd10name from dmc_icd where upper(icd10code) like upper('%[FILTER]%')";
	var that = this;
	this.load = doLoad;
	var nhommau ;
	var rh ;
	var HIS_CHAN_THAYDOI_NHOMMAU;
	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		//ductx -bvtm-7792
		if(hospital_id == "965"){
			$('#divSP').show();
		}else{
			$('#divSP').hide();
		}
		//end bvtm-7792
		//START L2PT-16663
		var data_cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","HIS_CHAN_THAYDOI_NHOMMAU;NTU_CAUHINH_ICD_YHCT;" +
				"HIS_SHOW_ICDYHCT;" +//L2PT-93075
				"NTU_BENHANCHUNG_CDBD;" +//L2PT-118207
				"BENHAN_CHANDOAN_PB;" +//L2PT-95834
				"NTU_BENHANCHUNG_BOCHANDOAN;KBH_PARA;NTU_BAC_LOAD_YHCT_SELECT;NTU_BAC_LOADNHOMMAU_RH;NGT_KHAMCHUYENKHOA;HIS_QD130;NTU_BAC_SAVE_CHANDOAN;NTU_PDT_EDIT_TENBENHPHU_YHCT");//L2PT-7751 L2PT-60364 L2PT-61683
		if(data_cauhinh != null && data_cauhinh.length > 0){
			//BVTM-7586
			if(data_cauhinh[0].NTU_BAC_LOADNHOMMAU_RH == '1'){
				load_nhommau_rh = true;
			}
			HIS_CHAN_THAYDOI_NHOMMAU = data_cauhinh[0].NTU_BAC_LOADNHOMMAU_RH;
			if(data_cauhinh[0].NTU_CAUHINH_ICD_YHCT == '1'){
				cauhinh_icdyhct = 1;
				$("#btnEdit_ICDYHCT_VK").show();
				$("#btnEdit_ICDYHCT_RV").show();
			}
			//L2PT-118207
			if(data_cauhinh[0].NTU_BENHANCHUNG_CDBD == '1'){
				$("#divCHANDOANBANDAU").show();
			}
			//L2PT-135215
			if (data_cauhinh[0].NTU_PDT_EDIT_TENBENHPHU_YHCT == '1') {
				$("#txtCHANDOANYHCTPHU").prop("disabled", false);
				$("#txtCHANDOANRVYHCTPHU").prop("disabled", false);
			}
			
			//L2PT-95834
			if(data_cauhinh[0].BENHAN_CHANDOAN_PB == '1'){
				$("#divChanDoanPB").show();
			}
			//BVTM-2546
			if(data_cauhinh[0].NTU_BENHANCHUNG_BOCHANDOAN == '1'){
				$('#divBenhchinh_vao').removeClass('required');
				kobatbuoc_benhchinh = true;
			}
			//L2PT-60364 start
			if(data_cauhinh[0].HIS_QD130 == '1'){
				$('#divCANNANG').addClass('required');
				his_qd130 = true;
			}
			//L2PT-60364 end
			if(data_cauhinh[0].KBH_PARA == '1'){
				$("#divPARA").show();
			}
			//L2PT-8701 load icd yhct
			if(data_cauhinh[0].NTU_BAC_LOAD_YHCT_SELECT == '1'){
				load_icd_yhct_sl = true;
			}
			//L2PT-34421
			if(data_cauhinh[0].NGT_KHAMCHUYENKHOA == '1') {
				$("#btnChuyenKhoa").show();
			}
			//L2PT-61683 start
			if(data_cauhinh[0].NTU_BAC_SAVE_CHANDOAN == '1') {
				$("#divChanDoanVK").show();
				$("#divChanDoanRV").show();
			}
			//L2PT-61683 end
		}
		$("#txtMA_CHANDOAN_VAO_CHINH").focus();
		//Lay thong tin kham benh
		var data_ar;
		data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.04", _khambenh_id, []);
		if (data_ar != null && data_ar.length > 0) {
			var _row = data_ar[0];
			FormUtil.setObjectToForm("dvCoQuan", "", _row);
		}
		data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.05.V2", _khambenh_id, []);
		//ductx bvtm-7792
		if(hospital_id == "965"){
		var csp = jsonrpc.AjaxJson.getOneValue("NTU.GETSPO", [{ "name": "[0]", "value": _khambenh_id }]);
		if(csp != "null"){
			$('#txtKHAMBENH_SPO2').val(csp);
		}else{
			$('#txtKHAMBENH_SPO2').val('');
		}
		
		}
		//end bvtm-7792
		if (data_ar != null && data_ar.length > 0) {
			var _row = data_ar[0];
			FormUtil.setObjectToForm("dvKhamBenh1", "", _row);
			FormUtil.setObjectToForm("dvKhamBenh2", "", _row);
			nhommau = _row.NHOMMAU;
			rh = _row.RH;
			$('#txtHIDMA_CHANDOAN_VAO_CHINH').val(_row.MA_CHANDOAN_VAO_CHINH);
			$('#txtTKMA_CHANDOAN_VAO_CHINH').val(_row.MA_CHANDOAN_VAO_CHINH);
			$('#txtTKMA_CHANDOAN_RA_CHINH').val(_row.MA_CHANDOAN_RA_CHINH);
			if (_row.TEN_CHANDOAN_VAO_CHINH != null && _row.TEN_CHANDOAN_VAO_CHINH != '') {
				$("#cboMA_CHANDOAN_VAO_CHINH").empty();
				$("#cboMA_CHANDOAN_VAO_CHINH").append('<option value="' + _row.MA_CHANDOAN_VAO_CHINH + '">' + _row.TEN_CHANDOAN_VAO_CHINH + '</option>');
			}
			if (_row.MA_CHANDOAN_RA_CHINH != null && _row.MA_CHANDOAN_RA_CHINH != '') {
				$("#cboMA_CHANDOAN_RA_CHINH").empty();
				$("#cboMA_CHANDOAN_RA_CHINH").append('<option value="' + _row.MA_CHANDOAN_RA_CHINH + '">' + _row.TEN_CHANDOAN_RA_CHINH + '</option>');
			}
			
			//L2PT-93075
			if (_row.TKMACHANDOANYHCT != null && _row.TKMACHANDOANYHCT != '') {
				$("#cboMACHANDOANYHCT").empty();
				$("#cboMACHANDOANYHCT").append('<option value="'+_row.TKMACHANDOANYHCT+'">'+_row.CHANDOANYHCT+'</option>');
			}
			if (_row.TKMACHANDOANRVYHCT != null && _row.TKMACHANDOANRVYHCT != '') {
				$("#cboMACHANDOANRVYHCT").empty();
				$("#cboMACHANDOANRVYHCT").append('<option value="'+_row.TKMACHANDOANRVYHCT+'">'+_row.CHANDOANRVYHCT+'</option>');
			}
			//START L2PT-16663
			if(cauhinh_icdyhct == 1){
				yhctid_rv = _row.ICDID_YHCT_RAVIEN;
				yhctid_vk = _row.ICDID_YHCT_VAOKHOA;
			}
			//L2PT-8701 load icd yhct
			if(load_icd_yhct_sl == 1){
				if (_row.TEN_CHANDOAN_VAO_CHINH_YHCT != null && _row.TEN_CHANDOAN_VAO_CHINH_YHCT != '') {
					$("#cboMA_CHANDOAN_VAO_CHINH_YHCT").empty();
					$("#cboMA_CHANDOAN_VAO_CHINH_YHCT").append('<option value="' + _row.MA_CHANDOAN_VAO_CHINH_YHCT + '">' + _row.TEN_CHANDOAN_VAO_CHINH_YHCT + '</option>');
				}
				if (_row.MA_CHANDOAN_RA_CHINH_YHCT != null && _row.MA_CHANDOAN_RA_CHINH_YHCT != '') {
					$("#cboMA_CHANDOAN_RA_CHINH_YHCT").empty();
					$("#cboMA_CHANDOAN_RA_CHINH_YHCT").append('<option value="' + _row.MA_CHANDOAN_RA_CHINH_YHCT + '">' + _row.TEN_CHANDOAN_RA_CHINH_YHCT + '</option>');
				}

			}
			// if(load_nhommau_rh){
			// 	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.LOADRH", _khambenh_id+'$'+'1');
	        //     if (data_ar != null && data_ar.length > 0) {
	        //         var row = data_ar[0];
	        //         if (row.NHOMMAU){
	        //         	$('#cboNHOMMAU').val(row.NHOMMAU);
	        //         }
	        //         if (row.NHOMMAU_RH){
	        //         	switch (row_nhommau.NHOMMAU_RH) {
	        //         		case '+':
	        //         			  $('#cboRH').val('1');
	        //         		    break;
	        //         		case '-':
	        //         			  $('#cboRH').val('2');
	        //         			break;
	        //         	}
	        //         }
	        //     }
			// }
		}
		this.validator = new DataValidator("divKHAMBENH");
		//start load phac do - hongdq
		data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.07", _khambenh_id, []);
		if (data_ar != null && data_ar.length > 0) {
			var _row = data_ar[0];
			$('#txtTEN_PHACDO_DIEUTRI').val('');
			$('#txtTEN_PHACDO_DIEUTRI').val(_row.TENPHACDODT);
			//L2PT-30739
			phacdodtID = _row.PHACDOID;
			
		}
		//end load phac do - hongdq
		//L2PT-8701 load icd yhct
		if(load_icd_yhct_sl){
			var sql_icd= 'NT.008.YHCTV3';
			_col = "Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l";
			 $("#cboMA_CHANDOAN_VAO_CHINH_YHCT").show();
			 $("#txtTEN_CHANDOAN_VAO_KEMTHEO_YHCT").show();
			 $("#cboMA_CHANDOAN_RA_CHINH_YHCT").show();
			 $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").show();
			 
			 $("#btnEditBcVk").hide();
			 //L2PT-21075
			 //$("#btnEDITCHANDOANVAOVIENKT").hide();
			 $("#btnEditBc").hide();
			 //L2PT-21075
			 //$("#btnEDITCHANDOANRAVIENKT").hide();
			 
			 $("#cboMA_CHANDOAN_VAO_CHINH").hide();
			 $("#txtTEN_CHANDOAN_VAO_KEMTHEO").hide();
			 $("#cboMA_CHANDOAN_RA_CHINH").hide();
			 $("#txtTEN_CHANDOAN_RA_KEMTHEO").hide();
		}else{
			var sql_icd= 'NT.008';
		}
		
		//L2PT-93075
		var sql_par_k = [];
		sql_par_k.push({"name":"[0]", value: _options.khoaId});
		var check_k = jsonrpc.AjaxJson.getOneValue('CHECK.KHOAYHCT', sql_par_k);
		//dannd_L2PT-98513
		var sql_par_p = [];
        sql_par_p.push({"name": "[0]", value: _options.phongId});
        var check_p = jsonrpc.AjaxJson.getOneValue('CHECK.PHONGYHCT', sql_par_p);
		if(data_cauhinh[0].HIS_SHOW_ICDYHCT == '1' && (check_k != '0' || check_p != 0) ) {
			showyhct4750 = '1';
//			$("#btnEDITCHANDOANVAOVIENKT").hide(); //L2PT-96824
//			$("#btnEDITCHANDOANRAVIENKT").hide();
			$("#divICDYHTC").css("display", "");
			$("#divICDYHTC_RV").css("display", "");
			sql_icd = 'NT.008.YHCTV4';
			var _col = 'Mã bệnh,ICD10CODE,10,0,f,l;Tên bệnh,ICD10NAME,40,0,f,l;Mã bệnh YHCT,YHCTCODE,10,0,f,l;Tên bệnh YHCT,YHCTNAME,40,0,f,l';
			ComboUtil.initComboGrid("txtTKMACHANDOANYHCT", sql_icd, [], "900px", _col, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtCHANDOANYHCTPHU").val();
				if (str.indexOf(_ui.YHCTCODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
					return false;
				}
				$("#txtTKMACHANDOANYHCT").val(_ui.YHCTCODE);// L2PT-34106
				$("#cboMACHANDOANYHCT").empty();
				$("#cboMACHANDOANYHCT").append('<option value="' + _ui.YHCTCODE + '">' + _ui.YHCTNAME + '</option>');
				
				$("#txtTKMA_CHANDOAN_VAO_CHINH").val(_ui.ICD10CODE);// L2PT-34106
				$("#cboMA_CHANDOAN_VAO_CHINH").empty();
				$("#cboMA_CHANDOAN_VAO_CHINH").append('<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10NAME + '</option>');
				
				return false;
			});
			ComboUtil.init("txtTKMACHANDOANYHCTPHU", sql_icd, [], "900px", _col, function(event, ui) {
				if (ui.item.YHCTCODE == $("#cboMACHANDOANYHCT").val()) {
					DlgUtil.showMsg("Bệnh kèm theo vừa nhập không được trùng với bệnh chính.");
					return false;
				}
				var stryhct = $("#txtCHANDOANYHCTPHU").val();
				
				if(stryhct.indexOf(ui.item.YHCTCODE+'-') > -1){
				DlgUtil.showMsg("Bệnh kèm theo đã được nhập");
				    return false;
				}
				
				if(ui.item.YHCTCODE){
					if (stryhct != '')
						stryhct += ";";
					$("#txtCHANDOANYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
				}
				
				var str = $("#txtTEN_CHANDOAN_VAO_KEMTHEO").val();
				if (str != '')
					str += ";";
				$("#txtTEN_CHANDOAN_VAO_KEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
				
				return false;
			});
			
			ComboUtil.initComboGrid("txtTKMA_CHANDOAN_VAO_CHINH", sql_icd, [], "900px", _col, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtTEN_CHANDOAN_VAO_KEMTHEO").val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
					return false;
				}
				$("#cboMA_CHANDOAN_VAO_CHINH").empty();
				$("#cboMA_CHANDOAN_VAO_CHINH").append('<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10NAME + '</option>');
				
				$("#txtTKMACHANDOANYHCT").val(_ui.YHCTCODE);// L2PT-34106
				$("#cboMACHANDOANYHCT").empty();
				$("#cboMACHANDOANYHCT").append('<option value="' + _ui.YHCTCODE + '">' + _ui.YHCTNAME + '</option>');

			});
			ComboUtil.initComboGrid("txtMA_CHANDOAN_VAO_KEMTHEO", sql_icd, [], "900px", _col, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtTEN_CHANDOAN_VAO_KEMTHEO").val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng");
					return false;
				}
				if (($("#cboMA_CHANDOAN_VAO_CHINH").val() + "-").indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
					return false;
				}
				$('#txtTEN_CHANDOAN_VAO_KEMTHEO').val(
						$("#txtTEN_CHANDOAN_VAO_KEMTHEO").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtTEN_CHANDOAN_VAO_KEMTHEO").val() + ";" + ui.item.ICD10CODE + "-" +
								ui.item.ICD10NAME);
				
				var stryhct = $("#txtCHANDOANYHCTPHU").val();
				if(ui.item.YHCTCODE){
					if (stryhct != '')
						stryhct += ";";
					$("#txtCHANDOANYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
				}
				

			});
			ComboUtil.initComboGrid("txtTKMACHANDOANRVYHCT", sql_icd, [], "900px", _col, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtCHANDOANRVYHCTPHU").val();
				if (str.indexOf(_ui.YHCTCODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
					return false;
				}
				$("#txtTKMACHANDOANRVYHCT").val(_ui.YHCTCODE);// L2PT-34106
				$("#cboMACHANDOANRVYHCT").empty();
				$("#cboMACHANDOANRVYHCT").append('<option value="' + _ui.YHCTCODE + '">' + _ui.YHCTNAME + '</option>');
				
				$("#txtTKMA_CHANDOAN_RA_CHINH").val(_ui.ICD10CODE);// L2PT-34106
				$("#cboMA_CHANDOAN_RA_CHINH").empty();
				$("#cboMA_CHANDOAN_RA_CHINH").append('<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10NAME + '</option>');
				
				return false;
			});
			ComboUtil.init("txtTKMACHANDOANRVYHCTPHU", sql_icd, [], "900px", _col, function(event, ui) {
				if (ui.item.YHCTCODE == $("#cboMACHANDOANRVYHCT").val()) {
					DlgUtil.showMsg("Bệnh kèm theo vừa nhập không được trùng với bệnh chính.");
					return false;
				}
				var stryhct = $("#txtCHANDOANRVYHCTPHU").val();
				
				if(stryhct.indexOf(ui.item.YHCTCODE+'-') > -1){
				DlgUtil.showMsg("Bệnh kèm theo đã được nhập");
				    return false;
				}
				
				if(ui.item.YHCTCODE){
					if (stryhct != '')
						stryhct += ";";
					$("#txtCHANDOANRVYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
				}
				
				var str = $("#txtTEN_CHANDOAN_RA_KEMTHEO").val();
				if (str != '')
					str += ";";
				$("#txtTEN_CHANDOAN_RA_KEMTHEO").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
				
				return false;
			});
			ComboUtil.initComboGrid("txtTKMA_CHANDOAN_RA_CHINH", sql_icd, [], "900px", _col, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtTEN_CHANDOAN_RA_KEMTHEO").val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
					return false;
				}
				$("#cboMA_CHANDOAN_RA_CHINH").empty();
				$("#cboMA_CHANDOAN_RA_CHINH").append('<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10NAME + '</option>');
				$("#txtTKMACHANDOANRVYHCT").val(_ui.YHCTCODE);// L2PT-34106
				$("#cboMACHANDOANRVYHCT").empty();
				$("#cboMACHANDOANRVYHCT").append('<option value="' + _ui.YHCTCODE + '">' + _ui.YHCTNAME + '</option>');

			});
			ComboUtil.initComboGrid("txtMA_CHANDOAN_RA_KEMTHEO", sql_icd, [], "900px", _col, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtTEN_CHANDOAN_RA_KEMTHEO").val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng");
					return false;
				}
				if (($("#cboMA_CHANDOAN_RA_CHINH").val() + "-").indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
					return false;
				}
				$('#txtTEN_CHANDOAN_RA_KEMTHEO').val(
						$("#txtTEN_CHANDOAN_RA_KEMTHEO").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtTEN_CHANDOAN_RA_KEMTHEO").val() + ";" + ui.item.ICD10CODE + "-" +
								ui.item.ICD10NAME);
				var stryhct = $("#txtCHANDOANRVYHCTPHU").val();
				if(ui.item.YHCTCODE){
					if (stryhct != '')
						stryhct += ";";
					$("#txtCHANDOANRVYHCTPHU").val(stryhct + ui.item.YHCTCODE + "-" + ui.item.YHCTNAME);
				}
			});
			
		}else{
			ComboUtil.initComboGrid("txtTKMA_CHANDOAN_VAO_CHINH", sql_icd, [], "900px", _colICD, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtTEN_CHANDOAN_VAO_KEMTHEO").val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
					return false;
				}
				$("#cboMA_CHANDOAN_VAO_CHINH").empty();
				$("#cboMA_CHANDOAN_VAO_CHINH").append('<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10NAME + '</option>');

			});
			ComboUtil.initComboGrid("txtMA_CHANDOAN_VAO_KEMTHEO", sql_icd, [], "900px", _colICD, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtTEN_CHANDOAN_VAO_KEMTHEO").val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng");
					return false;
				}
				if (($("#cboMA_CHANDOAN_VAO_CHINH").val() + "-").indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
					return false;
				}
				$('#txtTEN_CHANDOAN_VAO_KEMTHEO').val(
						$("#txtTEN_CHANDOAN_VAO_KEMTHEO").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtTEN_CHANDOAN_VAO_KEMTHEO").val() + ";" + ui.item.ICD10CODE + "-" +
								ui.item.ICD10NAME);

			});
			ComboUtil.initComboGrid("txtTKMA_CHANDOAN_RA_CHINH", sql_icd, [], "900px", _colICD, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtTEN_CHANDOAN_RA_KEMTHEO").val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
					return false;
				}
				$("#cboMA_CHANDOAN_RA_CHINH").empty();
				$("#cboMA_CHANDOAN_RA_CHINH").append('<option value="' + _ui.ICD10CODE + '">' + _ui.ICD10NAME + '</option>');

			});
			ComboUtil.initComboGrid("txtMA_CHANDOAN_RA_KEMTHEO", sql_icd, [], "900px", _colICD, function(event, ui) {
				var _ui = ui.item;
				var str = $("#txtTEN_CHANDOAN_RA_KEMTHEO").val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng");
					return false;
				}
				if (($("#cboMA_CHANDOAN_RA_CHINH").val() + "-").indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
					return false;
				}
				$('#txtTEN_CHANDOAN_RA_KEMTHEO').val(
						$("#txtTEN_CHANDOAN_RA_KEMTHEO").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtTEN_CHANDOAN_RA_KEMTHEO").val() + ";" + ui.item.ICD10CODE + "-" +
								ui.item.ICD10NAME);
		
			});
			doLoadCombo("txtTKMA_CHANDOAN_VAO_CHINH","cboMA_CHANDOAN_VAO_CHINH", 0, "txtTEN_CHANDOAN_VAO_KEMTHEO");
		  	doLoadCombo("txtMA_CHANDOAN_VAO_KEMTHEO","txtTEN_CHANDOAN_VAO_KEMTHEO", 1, "txtTEN_CHANDOAN_VAO_KEMTHEO", "txtTKMA_CHANDOAN_VAO_CHINH");
			doLoadCombo("txtTKMA_CHANDOAN_RA_CHINH","cboMA_CHANDOAN_RA_CHINH", 0, "txtTEN_CHANDOAN_RA_KEMTHEO");
			doLoadCombo("txtMA_CHANDOAN_RA_KEMTHEO","txtTEN_CHANDOAN_RA_KEMTHEO", 1,"txtTEN_CHANDOAN_RA_KEMTHEO", "txtTKMA_CHANDOAN_RA_CHINH" );
		}
		
		//L2PT-95834
		ComboUtil.initComboGrid("txtMA_CHANDOAN_PHANBIET", sql_icd, [], "900px", _colICD, function(event, ui) {
			var _ui = ui.item;
			var _ui = ui.item;
			var str = $("#txtTEN_CHANDOAN_PHANBIET").val();
			if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
				DlgUtil.showMsg("Chẩn đoán phân biệt trùng");
				return false;
			}
			$('#txtTEN_CHANDOAN_PHANBIET').val(
					$("#txtTEN_CHANDOAN_PHANBIET").val() == '' ? ui.item.ICD10CODE + "-" + ui.item.ICD10NAME : $("#txtTEN_CHANDOAN_PHANBIET").val() + ";" + ui.item.ICD10CODE + "-" +
							ui.item.ICD10NAME);
			return false;
		});
		
		
		ComboUtil.initComboGrid("txtMA_PHACDO_DIEUTRI", "NTU02D075.EV007", [], "600px", _colPhacdo, function(event, ui) {
			var _ui = ui.item;
			//START L2PT-30739
			if(phacdodtID != '-1'){
				phacdodtID = phacdodtID + ',' + ui.item.PHACDODTID
			}else{
				phacdodtID = ui.item.PHACDODTID;
			}
			var txtPDDT = '';
			if(txtPDDT = ''){
				txtPDDT = ui.item.MAPHACDODT + "-" + ui.item.TENPHACDODT;
			}else{
				txtPDDT = ',' + ui.item.MAPHACDODT + "-" + ui.item.TENPHACDODT
			}
			//END 2PT-30739
			$('#txtTEN_PHACDO_DIEUTRI').val('');
			//$('#txtTEN_PHACDO_DIEUTRI').val($("#txtTEN_PHACDO_DIEUTRI").val() == '' ? ui.item.MAPHACDODT +"-"+ ui.item.TENPHACDODT : $("#txtTEN_PHACDO_DIEUTRI").val() + ";" + ui.item.MAPHACDODT +"-"+ ui.item.TENPHACDODT);

			$('#txtTEN_PHACDO_DIEUTRI').val(txtPDDT)
		});
		
		loadCboMauBenhAn();
		bindEvent();
	}
	function loadCboMauBenhAn() {
		ComboUtil.getComboTag("cboMAUBENHAN", "NTU02D001.08", _options.khoaId, "", {
			value : '-1',
			text : '-- Chọn mẫu --'
		}, 'sp');
	}
	function loadBenhAn() {
		var mauBenhAnID = $("#cboMAUBENHAN").val();
		if (mauBenhAnID != '-1') {
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.11", mauBenhAnID);
			if (data_ar != null && data_ar.length > 0) {
				var row = data_ar[0];
				FormUtil.clearForm();
				//L2PT-119690
				$('#txtMA_CHANDOAN_VAO_KEMTHEO').val(row.MA_CHANDOAN_VAO_KEMTHEO);
				$('#txtTEN_CHANDOAN_VAO_KEMTHEO').val(row.TEN_CHANDOAN_VAO_KEMTHEO);
				delete row.MA_CHANDOAN_VAO_KEMTHEO;
				delete row.TEN_CHANDOAN_VAO_KEMTHEO;
				
				FormUtil.setObjectToForm("", "", row);
				$("#cboMA_CHANDOAN_VAO_CHINH").empty();
				$("#cboMA_CHANDOAN_VAO_CHINH").append('<option value="' + row.MA_CHANDOAN_VAO_CHINH + '">' + row.TEN_CHANDOAN_VAO_CHINH + '</option>');
				$("#cboMA_CHANDOAN_RA_CHINH").empty();
				$("#cboMA_CHANDOAN_RA_CHINH").append('<option value="' + row.MA_CHANDOAN_RA_CHINH + '">' + row.TEN_CHANDOAN_RA_CHINH + '</option>');
				setCheckBoxState(row, 'SOTHANG_DIUNG');
				setCheckBoxState(row, 'SOTHANG_THUOCLA');
				setCheckBoxState(row, 'SOTHANG_MATUY');
				setCheckBoxState(row, 'SOTHANG_THUOCLAO');
				setCheckBoxState(row, 'SOTHANG_RUOUBIA');
				setCheckBoxState(row, 'SOTHANG_KHAC');
				setCheckBoxState(row, 'COTHAI');//START L1PT-913

				DlgUtil.showMsg("Load mẫu thành công");

			}
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.13", _khambenh_id);
			if (data_ar != null && data_ar.length > 0) {
				var _row = data_ar[0];

				$('#txtHIDMA_CHANDOAN_VAO_CHINH').val(_row.MA_CHANDOAN_VAO_CHINH);
				$('#txtTKMA_CHANDOAN_VAO_CHINH').val(_row.MA_CHANDOAN_VAO_CHINH);

				if (_row.TEN_CHANDOAN_VAO_CHINH != null && _row.TEN_CHANDOAN_VAO_CHINH != '') {
					$("#cboMA_CHANDOAN_VAO_CHINH").empty();
					$("#cboMA_CHANDOAN_VAO_CHINH").append('<option value="' + _row.MA_CHANDOAN_VAO_CHINH + '">' + _row.TEN_CHANDOAN_VAO_CHINH + '</option>');
				}
				$('#txtMA_CHANDOAN_VAO_KEMTHEO').val(_row.MA_CHANDOAN_VAO_KEMTHEO).change();
				$('#txtTEN_CHANDOAN_VAO_KEMTHEO').val(_row.TEN_CHANDOAN_VAO_KEMTHEO);
			}
		}
	}
	function setCheckBoxState(row, _key) {
		if (row[_key] != undefined && parseInt(row[_key]) > 0) {
			$("#chk" + _key).prop('checked', true);
			$("#txt" + _key).prop('readonly', false);
		} else {
			$("#" + _key).prop('checked', false);
			$("#txt" + _key).prop('readonly', true);
			$("#txt" + _key).val("0");
		}
	}
	function saveMauBenhAn() {
		var _validator = new DataValidator("divMain");
		var valid = _validator.validateForm();
		if (!valid) {
			return false;
		}
		if ($("#txtTENMAUBENHAN").val().trim() == "") {
			DlgUtil.showMsg("Tên mẫu không được để trống");
			$('#txtTENMAUBENHAN').focus();
			return false;
		}
		var objData = new Object();
		FormUtil.setFormToObject("", "", objData);
		objData.SOTHANG_DIUNG = $("#txtSOTHANG_DIUNG").val();
		objData.SOTHANG_THUOCLA = $("#txtSOTHANG_THUOCLA").val();
		objData.SOTHANG_MATUY = $("#txtSOTHANG_MATUY").val();
		objData.SOTHANG_THUOCLAO = $("#txtSOTHANG_THUOCLAO").val();
		objData.SOTHANG_RUOUBIA = $("#txtSOTHANG_RUOUBIA").val();
		objData.SOTHANG_KHAC = $("#txtSOTHANG_KHAC").val();
		objData["KHOAID"] = _options.khoaId;
		objData["PHONGID"] = _options.phongId;
		var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D001.09", JSON.stringify(objData));
		if (ret == -1) {
			DlgUtil.showMsg("Lưu mẫu không thành công");
		} else {
			DlgUtil.showMsg("Lưu mẫu thành công");
			loadCboMauBenhAn();
		}
	}
	function xoaMauBenhAn() {
		var tmp_id_xoa = $("#cboMAUBENHAN").val();
		var ten_mau_xoa = $("#cboMAUBENHAN option:selected").text();
		if (tmp_id_xoa == "-1") {
			DlgUtil.showMsg("Chưa chọn mẫu để xóa");
			$('#cboMAUBENHAN').focus();
			return false;
		}
		DlgUtil.showConfirm("Bạn có muốn xóa mẫu " + ten_mau_xoa + " ko?", function(flag) {
			if (flag) {
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D001.10", tmp_id_xoa);
				if (result == -1) {
					DlgUtil.showMsg("Xóa mẫu " + ten_mau_xoa + " không thành công");
				} else {
					DlgUtil.showMsg("Xóa mẫu " + ten_mau_xoa + " thành công");
					loadCboMauBenhAn();
				}
			}
		});
	}
	function closeKhamBenh() {
		var evFunc = EventUtil.getEvent("assignSevice_closeBenhAn");
		if (typeof evFunc === 'function') {
			evFunc({
				msg : ""
			});
		}
	}
	function doIns() {
		if (load_nhommau_rh && HIS_CHAN_THAYDOI_NHOMMAU != '0'  && ( $('#cboNHOMMAU').val() != '0' || $('#cboRH').val() != 0 )  && (nhommau != '0' || rh != '0' ) ) {
			if ( $('#cboNHOMMAU').val() != nhommau || $('#cboRH').val() != rh) {
				if (HIS_CHAN_THAYDOI_NHOMMAU == '1'){
					if (!confirm('Thông tin nhóm máu hoặc RH đã bị thay đổi không giống với lần khám trước,có muốn tiếp tục?')) {
						return false;
					}
				}else {
					DlgUtil.showMsg('Thông tin nhóm máu hoặc RH đã bị thay đổi không giống với lần khám trước, vui lòng kiểm tra lại!');
					return false;
				}
			}
		}
		var objCoQuan = new Object();
		var objKhamBenh = new Object();
		FormUtil.setFormToObject("dvCoQuan", "", objCoQuan);
		FormUtil.setFormToObject("dvKhamBenh1", "", objKhamBenh);
		FormUtil.setFormToObject("dvKhamBenh2", "", objKhamBenh);
		objCoQuan.KHAMBENHID = _khambenh_id;
		objKhamBenh.KHAMBENHID = _khambenh_id;
		objKhamBenh.PHACDODTID = phacdodtID;
		objKhamBenh.GHICHU_BENHCHINH = $('#txtGHICHU_BENHCHINH').val();
		objKhamBenh.GHICHU_BENHCHINH_RAVIEN = $('#txtGHICHU_BENHCHINH_RAVIEN').val();//hongdq-- HISL2TK-520 --18052018
		objKhamBenh.GHICHU_BENH_KEMTHEO = $('#txtGHICHU_BENH_KEMTHEO').val();//L2PT-8467
		objKhamBenh.HO_TEN_CHA = $('#txtHO_TEN_CHA').val();//L2PT-29909
		objKhamBenh.HO_TEN_ME = $('#txtHO_TEN_ME').val();//L2PT-29909
		//START L2PT-16663
		if(cauhinh_icdyhct == '1' || load_icd_yhct_sl){
			//L2PT-8701 thêm đk load icd yhct
			if(yhctid_rv == null && yhctid_vk == null && !load_icd_yhct_sl){
				return DlgUtil.showMsg("Chưa chọn Mã bệnh YHCT");
			}
			if(load_icd_yhct_sl && ($('#cboMA_CHANDOAN_VAO_CHINH_YHCT').val() == '' 
					|| $('#cboMA_CHANDOAN_VAO_CHINH_YHCT').val() == null)){
				return DlgUtil.showMsg("Chưa nhập chẩn đoán chính");
			}
			objKhamBenh.ICDID_YHCT_VAOKHOA = yhctid_vk; 
			objKhamBenh.ICDID_YHCT_RAVIEN = yhctid_rv;
			
			objKhamBenh.ICDCODE_YHCT_VAOKHOA = $('#cboMA_CHANDOAN_VAO_CHINH_YHCT').val();
			objKhamBenh.ICDCODE_YHCT_KT_VAOKHOA = $('#txtTEN_CHANDOAN_VAO_KEMTHEO_YHCT').val();
			objKhamBenh.ICDCODE_YHCT_RAVIEN = $('#cboMA_CHANDOAN_RA_CHINH_YHCT').val();
			objKhamBenh.ICDCODE_YHCT_KT_RAVIEN = $('#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT').val();
		}
		//START L2HOTRO-10747
		var HIS_GIOIHAN_CANNANG = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'HIS_GIOIHAN_CANNANG');
		//L2PT-61864 start
//		if (_opts.namsinh != null && _opts.namsinh != "") {
//			var sysDate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
//			sysDate = sysDate.substr(6, 4);
//			var tuoi = Number(sysDate) - Number(_opts.namsinh);
//			if (tuoi < 1 && HIS_GIOIHAN_CANNANG != null && HIS_GIOIHAN_CANNANG != "" && $('#txtKHAMBENH_CANNANG').val() != "" && Number($('#txtKHAMBENH_CANNANG').val()) > Number(HIS_GIOIHAN_CANNANG)) {
//				$('#txtKHAMBENH_CANNANG').focus();
//				return DlgUtil.showMsg("Bệnh nhân < 1 tuổi, cân nặng phải nhỏ hơn hoặc bằng " + HIS_GIOIHAN_CANNANG);;
//			}
//		}
		
		if(HIS_GIOIHAN_CANNANG != null && HIS_GIOIHAN_CANNANG != "" && $('#txtKHAMBENH_CANNANG').val() != "" && Number($('#txtKHAMBENH_CANNANG').val()) > Number(HIS_GIOIHAN_CANNANG)){
			DlgUtil.showMsg("Cân nặng phải nhỏ hơn hoặc bằng "+ HIS_GIOIHAN_CANNANG);
			$('#txtKHAMBENH_CANNANG').focus();
			return false;
		}
		//L2PT-61864 end
		//END L2HOTRO-10747
		//L2PT-60364 start ttlinh
		if (his_qd130 && ($('#txtKHAMBENH_CANNANG').val() == '' || $('#txtKHAMBENH_CANNANG').val() == null)) {
			return DlgUtil.showMsg("Bệnh nhân chưa nhập thông tin cân nặng!");
		}
		//L2PT-60364 end
		//var valid = validator.validateForm();
		var valid = that.validator.validateForm();
		if (valid) {
			if (checkInt()) {
				objKhamBenh.YHCT4750 =  showyhct4750;//L2PT-93075
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D001.06.1", JSON.stringify(objCoQuan) + "$" + JSON.stringify(objKhamBenh));
				if (ret == 1) {
					var _par = [ _hosobenhanid, _khambenh_id, _loaibenhanid, phacdodtID ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONGBO.LEFT", _par.join('$'));
					if (_return == 1) {
						DlgUtil.showMsg("Cập nhật bệnh án thành công");
					} else {
						DlgUtil.showMsg("Có lỗi khi thực hiện!");
					}
				} else if (ret == -1) {
					DlgUtil.showMsg("Có lỗi xảy ra trên hệ thống!!");
				} else if (ret == -5) {
					$("#txtMA_CHANDOAN_VAO_CHINH").focus();
					DlgUtil.showMsg("Lỗi do nhập sai mã ICD10 của bệnh chính khi vào khoa!");
				} else if (ret == -6) {
					$("#txtMA_CHANDOAN_RA_CHINH").focus();
					DlgUtil.showMsg("Lỗi do nhập sai mã ICD10 của bệnh chính khi ra viện!");
				}
			}
		}
	}
	function doInsClose() {
		var objCoQuan = new Object();
		var objKhamBenh = new Object();
		FormUtil.setFormToObject("dvCoQuan", "", objCoQuan);
		FormUtil.setFormToObject("dvKhamBenh1", "", objKhamBenh);
		FormUtil.setFormToObject("dvKhamBenh2", "", objKhamBenh);
		objCoQuan.KHAMBENHID = _khambenh_id;
		objKhamBenh.KHAMBENHID = _khambenh_id;
		objKhamBenh.GHICHU_BENHCHINH = $('#txtGHICHU_BENHCHINH').val();
		objKhamBenh.GHICHU_BENHCHINH_RAVIEN = $('#txtGHICHU_BENHCHINH_RAVIEN').val();//hongdq-- HISL2TK-520 --18052018
		objKhamBenh.GHICHU_BENH_KEMTHEO = $('#txtGHICHU_BENH_KEMTHEO').val();//L2PT-8467
		objKhamBenh.HO_TEN_CHA = $('#txtHO_TEN_CHA').val();//L2PT-29909
		objKhamBenh.HO_TEN_ME = $('#txtHO_TEN_ME').val();//L2PT-29909
    if (load_nhommau_rh && HIS_CHAN_THAYDOI_NHOMMAU != '0'  && ( $('#cboNHOMMAU').val() != '0' || $('#cboRH').val() != 0 )  && (nhommau != '0' || rh != '0' ) ) {
			if ( $('#cboNHOMMAU').val() != nhommau || $('#cboRH').val() != rh) {
				if (HIS_CHAN_THAYDOI_NHOMMAU == '1'){
					if (!confirm('Thông tin nhóm máu hoặc RH đã bị thay đổi không giống với lần khám trước,có muốn tiếp tục?')) {
						return false;
					}
				}else {
					DlgUtil.showMsg('Thông tin nhóm máu hoặc RH đã bị thay đổi không giống với lần khám trước, vui lòng kiểm tra lại!');
					return false;
				}
			}
		}
		//START L2HOTRO-10747
		var HIS_GIOIHAN_CANNANG = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'HIS_GIOIHAN_CANNANG');
		//L2PT-61864 start
//		if (_opts.namsinh != null && _opts.namsinh != "") {
//			var sysDate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
//			sysDate = sysDate.substr(6, 4);
//			var tuoi = Number(sysDate) - Number(_opts.namsinh);
//			if (tuoi < 1 && HIS_GIOIHAN_CANNANG != null && HIS_GIOIHAN_CANNANG != "" && $('#txtKHAMBENH_CANNANG').val() != "" && Number($('#txtKHAMBENH_CANNANG').val()) > Number(HIS_GIOIHAN_CANNANG)) {
//				$('#txtKHAMBENH_CANNANG').focus();
//				return DlgUtil.showMsg("Bệnh nhân < 1 tuổi, cân nặng phải nhỏ hơn hoặc bằng " + HIS_GIOIHAN_CANNANG);;
//			}
//		}
		
		if(HIS_GIOIHAN_CANNANG != null && HIS_GIOIHAN_CANNANG != "" && $('#txtKHAMBENH_CANNANG').val() != "" && Number($('#txtKHAMBENH_CANNANG').val()) > Number(HIS_GIOIHAN_CANNANG)){
			DlgUtil.showMsg("Cân nặng phải nhỏ hơn hoặc bằng "+ HIS_GIOIHAN_CANNANG);
			$('#txtKHAMBENH_CANNANG').focus();
			return false;
		}
		//L2PT-61864 end
		//END L2HOTRO-10747
		//L2PT-60364 start ttlinh
		if (his_qd130 && ($('#txtKHAMBENH_CANNANG').val() == '' || $('#txtKHAMBENH_CANNANG').val() == null)) {
			return DlgUtil.showMsg("Bệnh nhân chưa nhập thông tin cân nặng!");
		}
		//L2PT-60364 end
		//var valid = validator.validateForm();
		var valid = that.validator.validateForm();
		if (valid) {
			if (checkInt()) {
				objKhamBenh.YHCT4750 =  showyhct4750;//L2PT-93075
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D001.06.1", JSON.stringify(objCoQuan) + "$" + JSON.stringify(objKhamBenh));
				if (ret == 1) {
					var _par = [ _hosobenhanid, _khambenh_id, _loaibenhanid, phacdodtID ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("BAN.DONGBO.LEFT", _par.join('$'));
					//DlgUtil.showMsg("Thêm mới thành công!");
					var evFunc = EventUtil.getEvent("assignSevice_saveBenhAn");
					if (typeof evFunc === 'function') {
						evFunc({
							msg : "Cập nhật bệnh án thành công"
						});
					} else {
						console.log('evFunc not a function');
					}
				} else if (ret == -1) {
					DlgUtil.showMsg("Có lỗi khi thực hiện!");
				} else if (ret == -5) {
					$("#txtMA_CHANDOAN_VAO_CHINH").focus();
					DlgUtil.showMsg("Lỗi do nhập sai mã ICD10 của bệnh chính khi vào khoa!");
				} else if (ret == -6) {
					$("#txtMA_CHANDOAN_RA_CHINH").focus();
					DlgUtil.showMsg("Lỗi do nhập sai mã ICD10 của bệnh chính khi ra viện!");
				}
			}
		}
	}
	//L2PT-61683 start
	function doInsChanDoan(mode) {
		var objKhamBenh = new Object();
		FormUtil.setFormToObject("dvKhamBenh2", "", objKhamBenh);
		objKhamBenh.KHAMBENHID = _khambenh_id;
		objKhamBenh.MODE = mode + "";
		var valid = that.validator.validateForm();
		if (valid) {
			if (checkInt()) {
				var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D001.CHANDOAN", JSON.stringify(objKhamBenh));
				if (ret == 1) {
					DlgUtil.showMsg("Cập nhật chẩn đoán thành công");
				} else if (ret == -1) {
					DlgUtil.showMsg("Có lỗi xảy ra trên hệ thống!!");
				} else if (ret == -5) {
					$("#txtMA_CHANDOAN_VAO_CHINH").focus();
					DlgUtil.showMsg("Lỗi do nhập sai mã ICD10 của bệnh chính khi vào khoa!");
				} else if (ret == -6) {
					$("#txtMA_CHANDOAN_RA_CHINH").focus();
					DlgUtil.showMsg("Lỗi do nhập sai mã ICD10 của bệnh chính khi ra viện!");
				}
			}
		}
	}
	//L2PT-61683 end
	function doLoadCombo(_txt, _txtDst, i_mode, txtSubCheck, txtMain) {
		var _selfnc = function(event, ui) {
			var _text = "";
			if (i_mode == 1 && typeof txtSubCheck != "undefined" && typeof txtMain != "undefined") {
				var _ui = ui.item;
				var str = $("#" + txtSubCheck).val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng");
					return false;
				}
				if (($("#" + txtMain).val() + "-").indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
					return false;
				}
				//L2PT-81379
				if(load_icd_yhct_sl){
					var stryhct = $("#" + txtSubCheck+ "_YHCT").val();
					if (stryhct.indexOf(_ui.ICD10CODE + "-") > -1) {
						DlgUtil.showMsg("Bệnh kèm theo trùng");
						return false;
					}
					if (($("#" + txtMain + "_YHCT").val() + "-").indexOf(_ui.ICD10CODE + "-") > -1) {
						DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
						return false;
					}
				}
			}
			if (i_mode == 0 && typeof txtSubCheck != "undefined") {
				var _ui = ui.item;
				var str = $("#" + txtSubCheck).val();
				if (str.indexOf(_ui.ICD10CODE + "-") > -1) {
					DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
					if (_txt == 'txtMA_CHANDOAN_VAO_CHINH') {
						$('#txtMA_CHANDOAN_VAO_CHINH').val($('#txtHIDMA_CHANDOAN_VAO_CHINH').val());
					}
					return false;
				}
				//L2PT-81379
				if(load_icd_yhct_sl){
					var stryhct = $("#" + txtSubCheck+ "_YHCT").val();
					if (stryhct.indexOf(_ui.ICD10CODE + "-") > -1) {
						DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
						if (_txt == 'txtMA_CHANDOAN_VAO_CHINH') {
							$('#txtMA_CHANDOAN_VAO_CHINH').val($('#txtHIDMA_CHANDOAN_VAO_CHINH').val());
						}
						return false;
					}
				}
			}
			if (_txt == 'txtMA_CHANDOAN_VAO_CHINH') {
				$('#txtHIDMA_CHANDOAN_VAO_CHINH').val(ui.item.ICD10CODE);
			}
			$("#" + _txt).val(ui.item.ICD10CODE);
			var code = _ui.YHCTCODE == '' ? _ui.ICD10CODE : _ui.YHCTCODE;
			var name = _ui.YHCTNAME == '' ? _ui.ICD10NAME : _ui.YHCTNAME + ' [' + _ui.ICD10NAME + '] ';
			if (i_mode == 0) {
				$("#" + _txtDst).empty();
				$("#" + _txtDst).append('<option value="' + ui.item.ICD10CODE + '">' + ui.item.ICD10NAME+ '</option>');
				//L2PT-8701 load icd yhct
				if(load_icd_yhct_sl){
					if(_txt == 'txtTKMA_CHANDOAN_VAO_CHINH'){
						$("#cboMA_CHANDOAN_VAO_CHINH_YHCT").empty();
						$("#cboMA_CHANDOAN_VAO_CHINH_YHCT").append('<option value="' + code + '">' + name + '</option>');
					}
					if(_txt == 'txtTKMA_CHANDOAN_RA_CHINH'){
						$("#cboMA_CHANDOAN_RA_CHINH_YHCT").empty();
						$("#cboMA_CHANDOAN_RA_CHINH_YHCT").append('<option value="' + code + '">' + name + '</option>');
					}
				}
			} else {
				if ($("#" + _txtDst).val() == '')
					_text = ui.item.ICD10CODE + "-" + ui.item.ICD10NAME;
				else
					_text = $("#" + _txtDst).val() + ";" + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME;
				$("#" + _txtDst).val(_text);
				//L2PT-8701 load icd yhct
				if(load_icd_yhct_sl){
					if(_txt == 'txtMA_CHANDOAN_VAO_KEMTHEO'){
						$('#txtTEN_CHANDOAN_VAO_KEMTHEO_YHCT').val(
						$("#txtTEN_CHANDOAN_VAO_KEMTHEO_YHCT").val() == '' ? code + "-" + name : $("#txtTEN_CHANDOAN_VAO_KEMTHEO_YHCT").val() + ";" + code + "-" + name);
					}
					if(_txt == 'txtMA_CHANDOAN_RA_KEMTHEO'){
						$('#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT').val(
						$("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").val() == '' ? code + "-" + name : $("#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT").val() + ";" + code + "-" + name);
					}
				}
			}

			return false;
		};
		//L2PT-8701 load icd yhct
		if(load_icd_yhct_sl){
			var sql_icd= 'NT.008.YHCTV3';
		}else{
			var sql_icd= 'NT.SEARCH.ICD10';
		}
		ComboUtil.initComboGrid(_txt, sql_icd , [], "1200px", _col, _selfnc);
	};

	function checkInt() {
		var _lstObj = 'txtKHAMBENH_MACH,txtKHAMBENH_NHIETDO,txtKHAMBENH_HUYETAP_HIGH,txtKHAMBENH_HUYETAP_LOW,txtKHAMBENH_NHIPTHO,txtKHAMBENH_CANNANG,txtKHAMBENH_CHIEUCAO';
		var _lstText = 'Mạch(lần/phút),Nhiệt độ,Huyết áp(mmHG),Huyết áp(mmHG),Nhịp thở,Cân nặng,Chiều cao';
		var _arr = _lstObj.split(",");
		var _arrText = _lstText.split(",");
		for (var i = 0; i < _arr.length; i++) {
			if (parseFloat($("#" + _arr[i]).val()) < 0) {
				DlgUtil.showMsg(_arrText[i] + " luôn là số nguyên!");
				$("#" + _arr[i]).focus();
				return false;
			}
		}
		if ($('#cboMA_CHANDOAN_VAO_CHINH').val() == null || $('#cboMA_CHANDOAN_VAO_CHINH').val() == '') {
			DlgUtil.showMsg('Vui lòng nhập chẩn đoán vào khoa');
			return false;
		}
		return true;
	}
	function bindEvent() {
		$("#btnSaveKhamBenhDong").on("click", function(e) {
			doInsClose();
		});
		$("#btnCLEARCHANDOANVAOVIEN").on("click", function(e) {
			$('#txtTEN_CHANDOAN_VAO_KEMTHEO').val('');
			$('#txtCHANDOANYHCTPHU').val(''); //L2PT-93075
			if(load_icd_yhct_sl){//L2PT-8701 load icd yhct
				$('#txtTEN_CHANDOAN_VAO_KEMTHEO_YHCT').val('');
			}
		});
		$("#btnCLEARCHANDOANRAVIEN").on("click", function(e) {
			$('#txtTEN_CHANDOAN_RA_KEMTHEO').val('');
			$('#txtCHANDOANRVYHCTPHU').val(''); //L2PT-93075
			if(load_icd_yhct_sl){//L2PT-8701 load icd yhct
				$('#txtTEN_CHANDOAN_RA_KEMTHEO_YHCT').val('');
			}
		});
		$("#btnEditBc").on("click", function(e) {
			if (!$('#cboMA_CHANDOAN_RA_CHINH').val()) {
				DlgUtil.showMsg("Chưa chọn bệnh chính để sửa");
				return;
			}
			$('#cboMA_CHANDOAN_RA_CHINH').hide();
			$('#txtBCEDIT').val($('#cboMA_CHANDOAN_RA_CHINH option:selected').text());
			$('#txtBCEDIT').show();
			$('#btnSaveEditBc').show();
			$("#btnEditBc").hide();
		});
		$("#btnSaveEditBc").on("click", function(e) {
			if ($.trim($('#txtBCEDIT').val()) != '') {
				var icd = $("#cboMA_CHANDOAN_RA_CHINH").val();
				$("#cboMA_CHANDOAN_RA_CHINH").empty();
				$("#cboMA_CHANDOAN_RA_CHINH").append('<option value="' + icd + '">' + $('#txtBCEDIT').val().trim() + '</option>');
				$('#txtBCEDIT').hide();
				$('#btnSaveEditBc').hide();
				$('#cboMA_CHANDOAN_RA_CHINH').show();
				$("#btnEditBc").show();
			} else {
				$('#txtBCEDIT').focus();
				return;
			}
		});
		$("#txtBCEDIT").focusout(function() {
			if ($.trim($('#txtBCEDIT').val()) != '') {
				var icd = $("#cboMA_CHANDOAN_RA_CHINH").val();
				$("#cboMA_CHANDOAN_RA_CHINH").empty();
				$("#cboMA_CHANDOAN_RA_CHINH").append('<option value="' + icd + '">' + $('#txtBCEDIT').val().trim() + '</option>');
				$('#txtBCEDIT').hide();
				$('#btnSaveEditBc').hide();
				$('#cboMA_CHANDOAN_RA_CHINH').show();
				$("#btnEditBc").show();
			} else {
				$('#txtBCEDIT').focus();
				return;
			}
		});
		$("#btnEditBcVk").on("click", function(e) {
			if (!$('#cboMA_CHANDOAN_VAO_CHINH').val()) {
				DlgUtil.showMsg("Chưa chọn bệnh chính để sửa");
				return;
			}
			$('#cboMA_CHANDOAN_VAO_CHINH').hide();
			$('#txtBCEDITVK').val($('#cboMA_CHANDOAN_VAO_CHINH option:selected').text());
			$('#txtBCEDITVK').show();
			$('#btnSaveEditBcVk').show();
			$("#btnEditBcVk").hide();
		});
		$("#btnSaveEditBcVk").on("click", function(e) {
			if ($.trim($('#txtBCEDITVK').val()) != '') {
				var icd = $("#cboMA_CHANDOAN_VAO_CHINH").val();
				$("#cboMA_CHANDOAN_VAO_CHINH").empty();
				$("#cboMA_CHANDOAN_VAO_CHINH").append('<option value="' + icd + '">' + $('#txtBCEDITVK').val().trim() + '</option>');
				$('#txtBCEDITVK').hide();
				$('#btnSaveEditBcVk').hide();
				$('#cboMA_CHANDOAN_VAO_CHINH').show();
				$("#btnEditBcVk").show();
			} else {
				$('#txtBCEDITVK').focus();
				return;
			}
		});
		$("#txtBCEDITVK").focusout(function() {
			//BVTM-2546
			if(!kobatbuoc_benhchinh){
    			if ($.trim($('#txtBCEDITVK').val()) != '') {
    				var icd = $("#cboMA_CHANDOAN_VAO_CHINH").val();
    				$("#cboMA_CHANDOAN_VAO_CHINH").empty();
    				$("#cboMA_CHANDOAN_VAO_CHINH").append('<option value="' + icd + '">' + $('#txtBCEDITVK').val().trim() + '</option>');
    				$('#txtBCEDITVK').hide();
    				$('#btnSaveEditBcVk').hide();
    				$('#cboMA_CHANDOAN_VAO_CHINH').show();
    				$("#btnEditBcVk").show();
    			} else {
    				$('#txtBCEDITVK').focus();
    				return;
    			}
			}else{
				$("#cboMA_CHANDOAN_VAO_CHINH").empty();
				$("#cboMA_CHANDOAN_VAO_CHINH").append($('#txtBCEDITVK').val().trim());
			}
		});
		//START L2PT-16663
		$("#btnEdit_ICDYHCT_VK").on("click", function(e) {
			var myVar = {
					icdcode : $('#cboMA_CHANDOAN_VAO_CHINH').val(),
					mode: 0,
					yhctid : yhctid_vk
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgYHCT", "divDlg", "manager.jsp?func=../noitru/NTU02D149_Chinhsua_MaBHYT", myVar, "Chỉnh sửa ICD YHCT", 400, 250);
			DlgUtil.open("dlgYHCT");
		});
		$("#btnEdit_ICDYHCT_RV").on("click", function(e) {
			var myVar = {
					icdcode : $('#cboMA_CHANDOAN_RA_CHINH').val(),
					mode: 1,
					yhctid : yhctid_rv
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgYHCT", "divDlg", "manager.jsp?func=../noitru/NTU02D149_Chinhsua_MaBHYT", myVar, "Chỉnh sửa ICD YHCT", 400, 250);
			DlgUtil.open("dlgYHCT");
		});
		EventUtil.setEvent("chinhsua_icdyhct_rv", function(e) {
			yhctid_rv = e.yhct_id;
			DlgUtil.close("dlgYHCT");
		});
		EventUtil.setEvent("chinhsua_icdyhct_vk", function(e) {
			yhctid_vk = e.yhct_id;
			DlgUtil.close("dlgYHCT");
		});
		
		//END L2PT-16663
		$("#btnEDITCHANDOANVAOVIENKT").on("click", function(e) {
			//L2PT-21075
			if(load_icd_yhct_sl){
				controlActive = "txtTEN_CHANDOAN_VAO_KEMTHEO_YHCT";
				controlActive1 = "txtTEN_CHANDOAN_VAO_KEMTHEO";
			}else{
				controlActive = "txtTEN_CHANDOAN_VAO_KEMTHEO";
			}
			//L2PT-96824
			if(showyhct4750 == '1'){
				controlActive1 = "txtCHANDOANYHCTPHU";
				var myVar = {
						benhphu : $('#' + controlActive).val(),
						yhct : showyhct4750,
						benhphu1 : $('#txtCHANDOANYHCTPHU').val(),
						chandoan_kt_bd : ""
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
					DlgUtil.open("dlgBPKT");
			}
			else{
				var myVar = {
						benhphu : $('#' + controlActive).val(),
						benhphu1 : $('#txtTEN_CHANDOAN_VAO_KEMTHEO').val(),
						chandoan_kt_bd : ""
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
					DlgUtil.open("dlgBPKT");
			}
			
		});
		
		//L2PT-95834
		$("#btnEDITCHANDOANVAOPB").on("click", function(e) {
			controlActive = "txtTEN_CHANDOAN_PHANBIET";
			var myVar = {
				benhphu : $('#txtTEN_CHANDOAN_PHANBIET').val(),
				benhphu1 : $('#txtTEN_CHANDOAN_PHANBIET').val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
			DlgUtil.open("dlgBPKT");
		});
		$("#btnCLEARCHANDOANVAOPB").on("click", function(e) {
			$('#txtTEN_CHANDOAN_PHANBIET').val('');
			$('#txtMA_CHANDOAN_PHANBIET').val('');
		});
		
		
		$("#btnEDITCHANDOANRAVIENKT").on("click", function(e) {
			//L2PT-21075
			if(load_icd_yhct_sl){
				controlActive = "txtTEN_CHANDOAN_RA_KEMTHEO_YHCT";
				controlActive1 = "txtTEN_CHANDOAN_RA_KEMTHEO";
			}else{
				controlActive = "txtTEN_CHANDOAN_RA_KEMTHEO";
			}
			//L2PT-96824
			if(showyhct4750 == '1'){
				controlActive1 = "txtCHANDOANRVYHCTPHU";
				var myVar = {
						benhphu : $('#' + controlActive).val(),
						yhct : showyhct4750,
						benhphu1 : $('#txtCHANDOANRVYHCTPHU').val(),
						chandoan_kt_bd : ""
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
					DlgUtil.open("dlgBPKT");
			}
			else{
				var myVar = {
						benhphu : $('#' + controlActive).val(),
						benhphu1 : $('#txtTEN_CHANDOAN_RA_KEMTHEO').val(),
						chandoan_kt_bd : ""
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgBPKT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu", myVar, "Chỉnh sửa bệnh kèm theo", 600, 420);
					DlgUtil.open("dlgBPKT");
			}			
		});
		EventUtil.setEvent("chinhsua_benhphu", function(e) {
			$('#' + controlActive).val(e.benhphu);
			if(load_icd_yhct_sl || showyhct4750 == '1'){//L2PT-96824
				$('#' + controlActive1).val(e.benhphu1);
			}
			DlgUtil.close("dlgBPKT");
		});
		$("#btnDongKhamBenh").on("click", function(e) {
			closeKhamBenh();
		});
		$("#btnLuuKhamBenh").on("click", function(e) {
			doIns();
		});
		//L2PT-61683 start 
		$("#btnLuuChanDoanRV").on("click", function(e) {
			doInsChanDoan(2);
		});
		$("#btnLuuChanDoanVK").on("click", function(e) {
			doInsChanDoan(1);
		});
		//L2PT-61683 end
		$("#btnCLEARPHACDO").on("click", function(e) {
			$('#txtTEN_PHACDO_DIEUTRI').val('');
			//L2PT-30739
			phacdodtID = '-1';
		});
		var f2 = 113;
		$(document).unbind('keydown').keydown(function(e) {
			if (e.keyCode == f2) {
				getIcd(e.target);
			}
		});
		EventUtil.setEvent("assignSevice_resultTK", function(e) {
			if (e.mode == '0') {
				$('#' + e.ctrId).combogrid("setValue", e.text);
			} else if (e.mode == '1') {
				$('#' + e.ctrTargetId).val(e.text);
			}
			DlgUtil.close(e.popupId);
		});
		$('#cboMAUBENHAN').change(function() {
			loadBenhAn();
		});
		$("#btnSAVEMAUBENHAN").on("click", function(e) {
			saveMauBenhAn();
		});
		$("#btnXOAMAUBENHAN").on("click", function(e) {
			xoaMauBenhAn();
		});

		$("#btnKyCa").on("click", function(e) {
			doIns();
			_caRpt('1');
		});
		$("#btnHuyCa").on("click", function(e) {
			_caRpt('2');
		});
		$("#btnExportCa").on("click", function(e) {
			_caRpt('0');
		});

		$("#btnChuyenKhoa").on("click", function (e) {
			if (_options.khoaId == '46594') {
				chuyenkhoaid = '14'; //Mắt
			} else if (_options.khoaId == '46592') {
				chuyenkhoaid = '15';//TMH
			}
			if (_options.khoaId == '46593') {
				chuyenkhoaid = '16';//RHM
			}
			paramInput = {
				khambenhid: _khambenh_id,
				chuyenkhoaid: chuyenkhoaid
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgKhamCK", "divDlg", "manager.jsp?func=../ngoaitru/NGT03K005_KhamChuyenKhoa", paramInput
				, "Khám chuyên khoa", 900, 480);
			DlgUtil.open("dlgKhamCK");
		});

	}
	$('.input-sm').keydown(function(e) {
		if (e.which === 13) {
			var index = $('.input-sm').index(this) + 1;
			$('.input-sm').eq(index).focus();
		}
	});
	//L2PT-23855
	$('#txtKHAMBENH_CANNANG').keypress(function(e) {
	        if(e.keyCode == '44' || e.charCode == '44') {
	            if(document.selection) {
	                var range = document.selection.createRange();
	                range.text = '.';
	            }else if(this.selectionStart || this.selectionStart == '0') {
	                var start = this.selectionStart;
	                var end = this.selectionEnd;
	                $(this).val($(this).val().substring(0, start) + '.' + $(this).val().substring(end, $(this).val().length));
	                this.selectionStart = start + 1;
	                this.selectionEnd = start +1;
	            } else {
	                $(this).val($(this).val() + '.');             
	            }
	            return false;
	        }
	 });
	$('#txtKHAMBENH_CANNANG').on("cut copy paste",function(e) {
	      e.preventDefault();
	});

	function _caRpt(signType) {
		var _params = [ {
			name: 'hosobenhanid',
			type: 'String',
			value: _hosobenhanid
		}, {
			name : 'khambenhid',
			type : 'String',
			value : _khambenh_id
		}, {
			name : 'RPT_CODE',
			type : 'String',
			value : 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4'
		} ];
		if(signType == '0') {
			CommonUtil.openReportGetCA2(_params, false);
		} else {
			var msg = CommonUtil.kyCA(_params, signType, true);
			EventUtil.setEvent("eventKyCA",function(e){
				DlgUtil.showMsg(e.res);
			});
		}
	}
}
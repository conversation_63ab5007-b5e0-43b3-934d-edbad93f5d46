

/*
 Mã màn hình  : NTU02D010
 File mã nguồn : NTU02D010_CapThuoc.js
 <PERSON><PERSON><PERSON> đích  : <PERSON><PERSON><PERSON> diện màn hình
 	+ Chỉ định thuốc
 	+ Chỉ định vật tư
 	+ <PERSON><PERSON><PERSON>
 	+ <PERSON>r<PERSON> vật tư
 	....(các chức năng liên quan chỉ định thuốc, vật tư)
 Tham số vào : 
 	khambenhid 		: M<PERSON> khám bệnh ID 
	maubenhphamid 	: Mẫu bệnh phẩm ID, để load thông tin về phiếu đã được chỉ định
	opt 			: Tham số định nghĩa loại màn hình tương ứng
		+ 	opt 	= '02D010' -> M<PERSON>n hình chỉ định thuốc
		+ 	opt 	= '02D014' -> M<PERSON>n hình phiếu trả thuốc
		+ 	opt 	= '02D015' -> <PERSON><PERSON><PERSON> hình phiếu vật tư
		+ 	opt 	= '02D016' -> <PERSON><PERSON><PERSON> hình phiếu trả vật tư
	loaikedon 		: <PERSON><PERSON><PERSON> số định nghĩa loại kê đơn thuốc
		+	loaikedon = 1 -> <PERSON><PERSON> số lượng thuốc tổng hợp
		+	loaikedon = 0 -> Kê đơn chi tiết (sáng, trưa, chiều, tối)
 
 Người lập trình	 Ngày cập nhật  	Ghi chú
 linhvv				 5/9/2016			Sửa bổ xung tính năng
*/
function chiDinhThuoc(_opts) {
	var gridInfo;
	var _gridCaption			= ""; 
	var _company_id 			= _opts.company_id;
	var _khambenhid 			= _opts.khambenh_id;
	var _user_id 				= _opts.user_id;
	var _dept_id 				= _opts.dept_id;
	var _loaikedon 				= _opts.loaikedon;
	var _khoaId 				= _opts.khoaId;
	var _phongId 				= _opts.phongId;
	var _dichvucha_id 			= _opts.dichvuchaid;
	var _maubenhpham_id 		= _opts.maubenhpham_id;
	var _phieudieutri_id		= _opts.phieudieutriid;
	var _macdinh_hao_phi		= _opts.macdinh_hao_phi;
	var _kieutra				= _opts.kieutra;
	var _loadkhotheo			= _opts.loadkhotheo;	
	var _dinhduong			    = _opts.dinhduong;
	var _tiepnhanid				= "";
	var _srch_hoatchat			= 0;
	var _maubenhpham_temp_id 	= "";
	var _option 				= _opts.option;
	var type_tc 				= 0;
	var i_action 				= "";
	var _first_load 			= 0;
	var _loainhommaubenhpham_id = "";
	var _tyle_bhyt 				= "";
	var _ma_bhyt 				= "";
	var _doituongbenhnhanid 	= "";
	var _ten_doituong_benhnhan 	= "";
	var _icd10_code				= "";
	var _icd10_text				= "";
	var _objDrugTemp 			= [];
	var _objDrug 				= [];
	var oldValue 				= "";
	var _benhnhanid 			= "";
	var _list_thuoc_dake 		= "";
	var _tuongtacthuoc			= "";
	var _loaitiepnhanid			= "";
	var _hinhthuc_kho			= "";
	var _loai_tvt_kho			= "";
	var _grdRowSelect			= "";
	var _nhom_mabhyt_id			= "";
	var _muc_tran_bhyt			= "";
	var _lbl_kho				= "";
	var _lbl_text				= "";
	var _ten_donvitinh			= "";
	var _khoanmucid				= "";
	var _thoigian_vaovien		= "";
	var _loai_doituong_moi		= "";
	var _TRAN_BHYT 				= "";
	var _TYLE_BHYT_TT 			= "";
	var _TONGTIENDV_BH 			= "";
	var _tradu6thangluongcoban	= "";
	var _duoc_van_chuyen		= "";
	var _ngay_bhyt_kt			= "";
	var _ngay_bhyt_bd			= "";//L2PT-3717
	var _GLB_CACH_TINH_TIEN		= 1;//0: Tinh binh thuong; 1: Tinh duoi gia tran
	var _tong_tien_dv			= "";
	var _tyle_bhyt_tt_tvt		= "";
	var _indonthuoc_noitru		= "";
	var _songaykemax     		= "";
	var _soluongthuocmax   		= "";
	var _phieutraid   			= "";
	var _dichvukbcha_id 		= "";
	var _ngaytiepnhan			= "";
	var _indonthuoc_huongthan	= "";
	var _makhomacdinh			= "";
	var _tyle_miengiam			= 0;
	var _thoihanthe				= 0;
	var _chanhoatchat			= 0;
	var _kechungthuocvt			= 0;
	var _objTmpThuoc 			= [];
	var _ngayhanthe				= 0;
	var _luu     				= 0;
	var _cachdung_sangchieu		= 0;
	var _ngaykethuocmax			= 0;
	var _badaingay				= 0;
	var _kieucheck				= 0;
	var _checkdiung				= 1;
	var _botimcachdung			= 1;
	var _canhbaophacdo 			= 0;
	var _sudungphacdo			= 0;
	var _checktrunghoatchat	    = 0;
	var _bacsi_ke				= 0;
	var _format_cd				= 0;
	var _dsthuocvattuid 		= "";
	var _ke_tunhieu_kho 		= 0;
	var _sudung_lieudung 		= 0;
	var _kieucanhbaotientamung  = 0;
	var _hopdongid				= 0;
	var _sudung_dvqd_kethuoc	= 0;
	var _ds_id_loaitvt			= "";
	var _phieudtri_kedon		= "0";
	var _loaikedonthuoc			= "";
	var _phieudtri_trathuoc		= "";
	var _lbllieudung			= "";
	var _checkngaykedonntu		= "1";
	var _ngt_lamtron_kedon		= 1;
	var _an_menu_phai_kedon		= 0;
	var _chonkho_kedon			= 0;
	var _ds_khoid				= "0";
	var _sudungthuoc12sao		= '0';
	var _timkiem_bn_kedon_ntu 	= '0'; 
	var _autoKyCA = false;//L2PT-124731
	var lstParamPrintCa = [];
	var causer = -1; //L2PT-30069
	var capassword = -1;
	var _smartcauser = -1;//L2PT-124731
	var loadfirt = 0; // check khi load don lan dau mac dinh lay kho nha thuoc bv L2PT-3517
	var _ten_tvt = '';	var _jsonThuoc24h;
	//tuyennx_add_start_20190507 L2PT-4556 L2PT-8767
	var _donvitinhid_qd 		= "";
	var _tyle_qd 		= "";
	//var KETHUOC_TINH_DVQUYDOI = 0;
	var _ten_dvt_qd 		= "";
	//tuyennx_add_end_20190507 L2PT-4556
	//tuyennx_add_start_20190507 L2PT-5526
	var data_ar 				= [];
	//tuyennx_add_end_20190507 L2PT-5526
	
	//tuyennx_add_start_20190507 L2PT-5682
	var KT_GOPCHUNG_CANHBAO = 0;
	var KT_SUDUNG_KHANGSINH = 0;
	var _thuockhangsinh;
	var _chuy = '';
	var _solo = '';
	var _hsd = '';
	//tuyennx_add_end_20190507 L2PT-5682
	//tuyennx_add_start L2PT-7416
	var sl_max = 500;
	var canhbaoke_max = 0;
	//tuyennx_add_end L2PT-7416
	//tuyennx_add_start L2PT-8635
	var sd_doncu_donmau = 0;
	var r_maubenhphamid = 0;
	var checkclose = 0;
	//tuyennx_add_end L2PT-8635
	
	var _songay_max = 0; //tuyennx_add_20190926 L2PT-8938
	var _check_close_popup = 0; //L2PT-21290
	var _MACHANDOAN = ""; 
	var list_thuoc_trongngay = "0"; //L2PT-23918
	//tuyennx_add_start_20200825 L2PT-26711
	var mySetLstTuongtac = new Set();
	//var KETHUOC_TUONGTAC_THUOC = 0;
	var listIdTuongtac = "0"; //L2PT-32754	
	var _objLstTT = []; //L2PT-8978
	//tuyennx_add_end_20200825 L2PT-26711
	
	var _check_canhbao = 0; //L2PT-29871
	var _tuoi = "";	
	var BHYT_LOAIID = ''; //L2PT-34335	
	var _HDSD 		= "";
	var check_c = 0;
	var _lieuluong 		= ""; //L2PT-7588
	
	var _ketugoi = "0"; //L2PT-409
	var _mbp_temp = "0";
	var checkky_Ca = 0; //BVTM-1487
	var mahoatchat = "";//L2PT-5948

	var _jsonDonThuoc 			= 
								{
									DS_THUOC:[],
									BENHNHANID:"",
									MA_CHANDOAN:"",
									CHANDOAN:"",
									CHANDOAN_KT : "",
									DUONG_DUNG:"",
									NGUOIDUNG_ID:"",
									CSYT_ID:"",
									KHO_THUOCID:"",
									INS_TYPE:"",
									I_ACTION:"",
									MAUBENHPHAMID:"",
									PHIEUDIEUTRI_ID:"",
									NGAYMAUBENHPHAM:"",
									NGAYMAUBENHPHAM_SUDUNG:"",
									DICHVUCHA_ID:"",
									DOITUONG_BN_ID:"",
									TYLE_BHYT:"",
									NGAY_BHYT_KT:"",
									HINH_THUC_KE:"",
									SONGAY_KE : "",
									MAUBENHPHAMCHA_ID:"",
									KIEUKEDON : ""
								};
	var _url;
	var _kho_id;
	var _loaithuocid;
	
	var _numberFormat = {
		    prefix: '',		    
		    thousandsSeparator: '.',
		    centsLimit: 0
		};
	var _enter = $.Event('keypress');
	_enter.which = 13;
	 var _loai_khothuoc = "";
	 var _loai_don = "";
	var _param=[_company_id];
	var sql_par=[];
	var _tableName="";
	var _keyField="";
	var _gridComboDonThuoc_TRA = "NTU02D010.01";
	var _gridComboMuaNgoai = "NTU02D010.02";
	_gridComboMuaNgoai_HoatChat ="NTU02D010.16";
	var _gridComboKhoThuoc = "NTU02D010.03";
	var _gridComboKhoThuoc_HoatChat = "NTU02D010.15";
	var _gridDonThuoc = "grdDONTHUOC";
	var	_gridDonThuocHeader = "Tên thuốc,TEN_THUOC,100,0,f,l;Hoạt chất,HOATCHAT,60,0,f,l;Đơn vị,TEN_DVT,60,0,f,l,0;SL khả dụng,SLKHADUNG,50,0,f,l,0;Mã thuốc,MA_THUOC,50,0,f,c,0;Giá DV,GIA_BAN,50,0,f,c,0;Biệt dược,BIETDUOC,50,0,f,c,0";
	
	var _gridDonThuocSQL = "NTU02D010.04.MN";
	var _gridDonThuocTemp = "NTU02D010.05";
	var r_dichvu_id_diff = "-1";
	var r_dichvu_id_dtc_diff = "-1";
	var r_checkicd = "0";
	var r_loaicheck = "0";
	var _tudongindt = "0";
	var _kieucheck_hoatchat = "0";
	var _tongtien = 0;
	var _tientu = 0;
	var _danop = 0;
	//tuyennx_add_start_20180806
	var soluong_kele = "";
	var an_dongia_thanhtien = "0";
	//tuyennx_add_end_20180806
	var tenthuoc_temp = "";//L2PT-135545
	var _configArr =  new Object();//tuyennx_add
	var _dstvt_tmp = [];
	
	
	var valid_ar =[{"name" : "txtTENTHUOC","display" : "Tên thuốc","rules" : "trim_required"},					
					{"name" : "txtSOLUONG_TONG","display" : "Số lượng","rules" : "trim_required|min_length[15]"},
					{"name" : "cboDUONG_DUNG","display" : "Giá trị thẻ từ","rules" : "trim_required"}];
	var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
	var _colloidanbs="Lời dặn bác sỹ,LOIDANBS,100,0,f,l";
	var that=this;
	this.load=doLoad;

	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		console.log("-------------_macdinh_hao_phi: "+_macdinh_hao_phi);
		
		//tuyennx_add_start_20190424 L1PT-664
		var hopital=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID'); 
		if(hopital!= null && hopital!=0) {
			_opts.hospital_id = hopital;
			_opts._hospital_id = hopital;
		} 
		//tuyennx_add_end_20190424 L1PT-664
		
		//tuyennx_add viet ham lay cau hinh
		var _lstConfig = jsonrpc.AjaxJson.ajaxCALL_SP_O("KETHUOC.DS.CAUHINH", '$');
		_configArr = _lstConfig[0];
		
		var objThuocDV = new Object();
		//L2PT-29871
		if(_configArr.KETHUOC_GHICHU_CANHBAO == '1'){
			$('#dvlGHICHUCANHBAO').css('display','');
			$('#dvGHICHUCANHBAO').css('display','');
		}
		//L2PT-124731
		if (_configArr.KYSO_TUDONG_KYDIENTU == '1') {
			_autoKyCA = true;
		}
//		DTDT_DAY_DONTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'DTDT_DAY_DONTHUOC'); //L2PT-30504
		var sys_date = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		$("#txtTHOI_GIAN").val(sys_date);
		$("#txtTG_DUNG").val(sys_date);
		$("#txtTHOI_GIANSMS").val(sys_date);
		$("#btnAllPhieu").addClass("disabled");
		
		// phòng khám chính
		// start manhnv L2HOTRO-11801
		if(_opts.khamchinhphu == '1'){
			$('#btnDTPhu').css('display','');
		}
		// end manhnv L2HOTRO-11801
		
		//tuyennx_add_start_20190318 lay phong cua bn khi chi dinh di kem
		if(_dichvucha_id != ""){
			var sql_par = [];
			sql_par.push({"name":"[0]", value:_dichvucha_id});
			_phongId = jsonrpc.AjaxJson.getOneValue('GET.PHONGBN', sql_par);
		}
		//tuyennx_add_end_20190318
		_list_thuoc_dake = "";
		an_dongia_thanhtien = _configArr.KETHUOC_AN_DONGIA;
		//$("#divDONGY").hide();
		
		//tuyennx_add_start_20190507 L2PT-5526
		if(_maubenhpham_id != "" && _maubenhpham_id != undefined ){
			data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.11.1", _maubenhpham_id,[]);//L2PT-4698
			if(data_ar != null && _option != '02D016' && _option != '02D014'){
	    		var _row = data_ar[0];
	    		if(_row.OPTIONS != "" && _row.OPTIONS != undefined)
	    			_option = _row.OPTIONS;
	    		if(_row.DICHVUCHA_ID) //L2PT-596
	    			_dichvucha_id = _row.DICHVUCHA_ID;
	    		
	    		//L2PT-4698
	    		if(_row.CHECK_HP == '0' )
	    			_macdinh_hao_phi = '9';
			}
		}
		//tuyennx_add_end_20190507 L2PT-5526
		
		if(_option == '02D010'){//Phieu thuoc 
			r_checkicd = "1";
			_loai_khothuoc 			= "2,3,8,10";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			//_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#dvLOAI_THUOC").show();
			$("#divSLTHANG").hide();		
		}else if(_option == '02D010_1'){
			r_checkicd = "1";
			_loai_khothuoc 			= "2,3,8,10";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			//_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#dvLOAI_THUOC").show();
			$("#divSLTHANG").hide();	
		}else if(_option == '02D011'){//Don thuoc mua ngoai 
			_loai_khothuoc 			= "4";
			_loai_don 				= "1";
			//_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_loainhommaubenhpham_id = "7";
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			$("#dvHeaderName").text("Đơn thuốc mua ngoài");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#grMenuRight").css("display", 'none');
			$("#divDonThuocVT").hide();
			
			if(_configArr.KETHUOC_PDT_MUANGOAI == '0') //tuyennx L2PT-2549
				$("#divPhieuDT").hide(); 
			
			$("#btnTraAllPhieu").hide();
			$("#divSLTHANG").hide();
			
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#dvQĐ").hide();
			//Start manhnv 04/10/2018 ADD
			/*$("#dvlTylethuoc").hide();
			$("#dvTYLETHUOC").hide();*/
			//End manhnv 04/10/2018 ADD
		}
		else if(_option == '02D014'){//Phieu tra thuoc
			_loai_khothuoc 			= "2,3,8,10";
			_loai_don 				= "2";
			_loainhommaubenhpham_id = "7";
			_srch_hoatchat			= 1;
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			//Truong hop phieu tra chi hien thi la ke tong
			//_loaikedon = 1;
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu trả thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu trả thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			$("#grMenuRight").css("display", 'none');
			
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			
			$("#btnDTMau").hide();
			$("#btnDTCu").hide();
			$("#btnTConSD").hide();	
			//tuyennx_add_start_20170816 yc L2DKBD-195
			$('#btnTDiUng').hide();
			//tuyennx_add_end_20170816 yc L2DKBD-195
			$("#txtTEXT_TEMP").hide();
			$("#btnSaveTemp").hide();
			$("#lblLoaiTra").text("Đơn thuốc trả");			
			$("#divDonThuocVT").show();
			$("#divPhieuDT").hide();
			$("#btnTraAllPhieu").show();
			$("#dvLOAI_THUOC").hide();
			$("#divSLTHANG").hide();
			$("#btnXuTri").hide();
			
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#dvQĐ").hide();
			//Start manhnv 04/10/2018 ADD
			/*$("#dvlTylethuoc").hide();
			$("#dvTYLETHUOC").hide();*/
			//End manhnv 04/10/2018 ADD
		}else if(_option == '02D015'){//Phieu vat tu 
			 r_checkicd = "1";
			_loai_khothuoc 			= "7, 9, 11";
			_loai_don 				= "3";
			_loainhommaubenhpham_id = "8";
			//_gridCaption			= "Danh sách vật tư";
			_gridCaption			= "";
			_lbl_kho				= "Kho vật tư";
			_lbl_text				= " vật tư";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp vật tư đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp vật tư");
			$("#lbSearchName").text("Tên vật tư");
			$("#lbTenThuoc").text("Tên vật tư");
			setTextToButton("btnAdd", "Thêm vật tư(F3)", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 1;
			
			$("#btnDTMau").text("Mẫu VT");
			$("#btnDTCu").text("Mẫu VT Cũ");
			
			$("#btnDTMau").show();
			$("#btnDTCu").show();
			$("#btnTConSD").hide();	
			//tuyennx_add_start_20170816 yc L2DKBD-195
			 $('#btnTDiUng').hide();
			 //tuyennx_add_end_20170816 yc L2DKBD-195
			$("#txtTEXT_TEMP").show();
			$("#btnSaveTemp").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();
			$("#lblloaithuoc").text("Loại vật tư");
			$("#dvLOAI_THUOC").show();
			$("#divSLTHANG").hide();
			$("#btnXuTri").hide();
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			//$("#divPhieuDT").hide();
		}else if(_option == '02D016'){//Phieu tra vat tu
			_loai_khothuoc 			= "7, 9, 11";
			_loai_don 				= "4";
			_loainhommaubenhpham_id = "8";
			//_gridCaption			= "Danh sách vật tư";
			_gridCaption			= "";
			//Truong hop phieu tra chi hien thi la ke tong
			_loaikedon 				= 1;
			_lbl_kho				= "Kho vật tư";
			_lbl_text				= " vật tư";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu trả vật tư đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu trả vật tư");
			$("#lbSearchName").text("Tên vật tư");
			$("#lbTenThuoc").text("Tên vật tư");
			setTextToButton("btnAdd", "Thêm vật tư(F3)", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 1;
			$("#grMenuRight").css("display", 'none');
			
			$("#btnDTMau").hide();
			$("#btnDTCu").hide();
			$("#btnTConSD").hide();	
			//tuyennx_add_start_20170816 yc L2DKBD-195 L2PT-5527
			   $('#btnTDiUng').hide();
			   //tuyennx_add_end_20170816 yc L2DKBD-195
			$("#txtTEXT_TEMP").hide();
			$("#btnSaveTemp").hide();
			$("#lblLoaiTra").text("Phiếu VT trả");
			$("#divDonThuocVT").show();
			$("#divPhieuDT").hide();
			$("#btnTraAllPhieu").show();
			$("#divSLTHANG").hide();
			$("#btnXuTri").hide();
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#dvQĐ").hide();
			//Start manhnv 04/10/2018 ADD
			/*$("#dvlTylethuoc").hide();
			$("#dvTYLETHUOC").hide();*/
			//End manhnv 04/10/2018 ADD
		}else if(_option == '02D017'){//Cấp thuốc đông y
			r_checkicd = "1";
			_loai_khothuoc 			= "5";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			_gridCaption			= "";
			//tuyennx_edit_start_20170816 yc L2PT-5527
			//_loaikedon 				= 1;
			//tuyennx_edit_end_20170816 yc L2PT-5527
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " vị thuốc YHCT";
			$("#lbSearchName").text("Tên vị thuốc YHCT");
			
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#lblcachdung").text("Cách dùng");
			$("#dvLOAI_THUOC").show();
			
			$("#lbTenThuoc").hide();
			$("#dvTENTHUOC").css('display','none');
			$("#divSLTHANG").show();
			$("#divDONGY").show();
			//L2PT-4950
			if(_configArr.KETHUOC_DONGY_MACDINH == '1'){
				$("#txtCACHSACTHUOC").val("Sắc ngày 1 thang");
	    		$("#txtCACHUONG").val("Uống sáng - chiều");	
			}
			$("#lblDUONGDUNG").css('display','none');
			$("#cboDUONG_DUNG").css('display','none');
			$("#dvTenThuoc").css('display','none');
			$("#dvDUONG_DUNG").css('display','none');			
			$("#txtTG_HENKHAM").val($("#txtSLTHANG").val());
			$("#dvQĐ").hide();
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			//Start manhnv 04/10/2018 ADD
			/*$("#dvlTylethuoc").hide();
			$("#dvTYLETHUOC").hide();*/
			//End manhnv 04/10/2018 ADD
		}else if(_option == '02D018'){//trả thuốc đông y
			_loai_khothuoc 			= "5";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách vật tư";
			_gridCaption			= "";
			//Truong hop phieu tra chi hien thi la ke tong
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " vị thuốc YHCT";
			$("#lbSearchName").text("Tên vị thuốc YHCT");
			setTextToButton("btnAdd", "Thêm thuốc trả", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#btnDTMau").hide();
			$("#btnDTCu").hide();
			$("#btnTConSD").hide();	
			//tuyennx_add_start_20170816 yc L2DKBD-195
		    $('#btnTDiUng').hide();
		    //tuyennx_add_end_20170816 yc L2DKBD-195
		    $('#btnPdDt').hide();
			$("#txtTEXT_TEMP").hide();
			$("#btnSaveTemp").hide();
			$("#lblLoaiTra").text("Đơn thuốc trả");			
			$("#divDonThuocVT").show();
			$("#divPhieuDT").hide();
			$("#btnTraAllPhieu").show();
			$("#dvLOAI_THUOC").hide();
			$("#divSLTHANG").hide();
			
			$("#lbTenThuoc").hide();
			$("#dvTENTHUOC").css('display','none');
			$("#lblDUONGDUNG").css('display','none');
			$("#cboDUONG_DUNG").css('display','none');
			$("#dvTenThuoc").css('display','none');
			$("#dvDUONG_DUNG").css('display','none');
			$("#btnXuTri").hide();
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#dvQĐ").hide();
			//Start manhnv 04/10/2018 ADD
			/*$("#dvlTylethuoc").hide();
			$("#dvTYLETHUOC").hide();*/
			//End manhnv 04/10/2018 ADD
		}else if(_option == '02D019'){//phiếu thuốc mua từ nhà thuốc 
			r_checkicd = "1";
			_loai_khothuoc 			= "4";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			//_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").hide();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#dvLOAI_THUOC").hide();
			$("#divSLTHANG").hide();
			
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#dvQĐ").hide();
			//Start manhnv 04/10/2018 ADD
			/*$("#dvlTylethuoc").hide();
			$("#dvTYLETHUOC").hide();*/
			//End manhnv 04/10/2018 ADD
		}else if(_option == '02D020'){//Phieu thuoc kê BN nội trú ra viện 
			r_checkicd = "1";
			_loai_khothuoc 			= "2,3,8,10";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu(F3)", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#dvLOAI_THUOC").show();
			$("#divSLTHANG").hide();			
		}
		// BVTM-6723
		$("#hidID_KE").val(jsonrpc.AjaxJson.getSystemDate('yyMMddHH24MISS'));		
		
		//BVTM-852
		var htdtmau = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIENTHI_BTN_DTMAU');
		if(_opts.macdinh_hao_phi != 9 && htdtmau == 0 && _dichvucha_id != ""){		
			$('#btnDTMau').css('display','none');
		} 
		
		//tuyennx_add_start_20200904_L2PT-26841
		if(_opts.ravien == '1' && _loainhommaubenhpham_id == 7)
			_loaikedon = _configArr.HIS_KEDONTHUOC_CHITIET_RAVIEN
		//tuyennx_add_end_20200904_L2PT-26841
		
		//BVTM-1487
		if(_configArr.KETHUOC_KYCA == '1'){
			$('#btnSaveCA').css('display','');
		}

		if(_configArr.KETHUOC_MN_CK_NHATHUOC == '1'){
			$("#chkChiTimTheoNT").prop('checked', true);
		}
		
		
		$("#lblKho").text(_lbl_kho);
		doLoadCombo("txtMACHANDOANICD","txtTENCHANDOANICD");		
		ComboUtil.initComboGrid("txtMATENCHANDOANICD_KT","NT.SEARCH.ICD10",[], "600px", _col, function(event, ui) {			
			var str = $("#txtTENCHANDOANICD_KT").val();
				if(str.indexOf(ui.item.ICD10CODE+'-') > -1){
				DlgUtil.showMsg("Bệnh kèm theo đã được nhập");
				    return false;
		    }
		   
		    if((ui.item.ICD10CODE.indexOf($('#txtMACHANDOANICD').val()) > -1) && $('#txtMACHANDOANICD').val() != ''){
		    	DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
		    	return false;
		    }
		   //tuyennx_edit_start_20170731 mở comment check trung ma benh phu
		    var _par=[_khambenhid,_phongId,ui.item.ICD10CODE,"1"];
			var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.ICD.TR",_par.join('$'));
			if(resultCheck == '0'){
				DlgUtil.showMsg("Đã tồn tại mã bệnh kèm theo trùng với phòng khám khác");
				return false;
			}
			//tuyennx_edit_start_20170731	   
			if(str != '') 
			str += ";";
			$("#txtTENCHANDOANICD_KT").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
			$("#txtMATENCHANDOANICD_KT").val('');
			$("#txtMATENCHANDOANICD_KT").trigger("focus");
			return false;
		});
		
		var _colan="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN,40,0,f,l;Mã,MA,10,0,f,c;Đường dùng,DUONG_DUNG,10,0,f,c;Hoạt chất,HOATCHAT,10,0,f,l;HDSD,HDSD,10,0,f,l;Liều lượng,LIEULUONG,10,0,f,l;Đơn vị,TEN_DVT,10,0,f,l";
		doLoadComboTVT("txtTHUOCVT",_colan);
		
		
		//L2PT-31821
		ComboUtil.init("txtTKLOIDANBS",'DMC.LOIDANBS',sql_par,"600px",_colloidanbs,function(event, ui) {
			if(_configArr.KETHUOC_GOP_LOIDAN == "1")
				$("#txtLOIDANBS").val($("#txtLOIDANBS").val() == "" ? ui.item.LOIDANBS : ($("#txtLOIDANBS").val() +";" + ui.item.LOIDANBS));
			else 
				$("#txtLOIDANBS").val(ui.item.LOIDANBS);
		});
		
		// cách dùng
		sql_par = [];
		sql_par.push({"name":"[0]","value":1});
		ComboUtil.initComboGrid("txtSearchCD","DMC.CACHDUNG.THUOC",sql_par, "300px", "Cách dùng,CACHDUNG,100,0,f,l", function(event, ui) {
			$("#txtSearchCD").val(ui.item.CACHDUNG);
			doInputDrugType(_loaikedon, 1);
			return false;
		});
		
		//tuyennx_add_start_L2PT-21372
		var KETHUOC_LOAD_DMCACHDUNG = _configArr.KETHUOC_LOAD_DMCACHDUNG;
		if(KETHUOC_LOAD_DMCACHDUNG == '2' || KETHUOC_LOAD_DMCACHDUNG == _loaitiepnhanid)
		ComboUtil.initComboGrid("txtGHICHU","DMC.CACHDUNG.THUOC",sql_par, "300px", "Cách dùng,CACHDUNG,100,0,f,l", function(event, ui) {
			$("#txtGHICHU").val(ui.item.CACHDUNG);
			return false;
		});
		//tuyennx_add_end_L2PT-21372
		
		// tim ma BA, Ma BN, TEN
		sql_par = [];
		sql_par.push({"name":"[0]","value":_khoaId});
		s_column = "Mã BN,MABENHNHAN,20,0,f,l;Mã BA,MAHOSOBENHAN,20,0,f,l;Tên,TENBENHNHAN,40,0,f,l;Năm sinh,NGAYSINH,20,0,f,l";
		ComboUtil.initComboGrid("lblPATIENTCODE","DS.BN.DTRI",sql_par, "530px", s_column, function(event, ui) {
			$("#lblPATIENTNAME").val(ui.item.TENBENHNHAN);
			$("#lblPATIENTCODE").val(ui.item.TENBENHNHAN);
			_khambenhid = ui.item.KHAMBENHID;  
			layThongTinBenhNhan();
			if(_configArr.KETHUOC_LOAD_GRID_BN=='1')
			{
				jQuery("#grdDONTHUOC").jqGrid("clearGridData");
				$('#btnSave').prop('disabled', false);
			}
			$("#txtDS_THUOC").focus();			
			return false;
		});
		
		// tg dùng
		sql_par = [];
		sql_par.push({"name":"[0]","value":2});		
		ComboUtil.initComboGrid("txtTGSD","DMC.CACHDUNG.THUOC",sql_par, "300px", "Cách dùng,CACHDUNG,100,0,f,l", function(event, ui) {			
			$("#txtTGSD").val(ui.item.CACHDUNG);
			doInputDrugType(_loaikedon, 1);
			return false;
		});
				
		//tuyennx_add_start_20190318 L2PT-8597
		if(_dichvucha_id != ""){
			var sql_par_dvchar = [];
			sql_par_dvchar.push({"name":"[0]", value:_dichvucha_id});
			var loaidichvu = jsonrpc.AjaxJson.getOneValue('GET.LOAIDV', sql_par_dvchar);
			if(loaidichvu == '1' || loaidichvu == '2'){
				_loadkhotheo = _configArr.LOAD_KHO_KETHUOC_DIKEM;
			}
		}
		//tuyennx_add_end_20190318
		
		//tuyennx_add_start_20190318 L2PT-8597 chuyen len dau ham
		if(_configArr.KETHUOC_TUTRUC_TONGHOP == '1' && _loadkhotheo == '2'){
			_loaikedon = 1;
		}
		//tuyennx_add_end_20190318 L2PT-9324
		
		if(_loaikedon == 1){
			$("#dvKE_TONG_TXT").show();
			if(_option == '02D010' || _option == '02D015'){
				$("#dvSONGAY_KE_TXT").show();
				$("#dvSONGAY_KE_LBL").show();
				$('#txtSONGAY_KE').val('1');
			}else {
				$("#dvSONGAY_KE_TXT").hide();
				$("#dvSONGAY_KE_LBL").hide();
			}			
			$("#dvGhiChu").removeClass("col-xs-3 low-padding").addClass("col-xs-5 low-padding");
			$("#dvKE_TONG_LB").show();
			$("#dvKE_CHITIET_TXT").hide();
			$("#dvKE_CHITIET_LB").hide();
			$("#dvSearchCD").hide();
			
			$('#dvlLIEU_DUNG').removeClass("col-xs-2");
			$('#dvlLIEU_DUNG').addClass("col-xs-3");
			
			$('#dvLIEU_DUNG').removeClass("col-xs-2");
			$('#dvLIEU_DUNG').addClass("col-xs-3");
		}else{
			$("#dvKE_TONG_TXT").hide();
			$("#dvKE_TONG_LB").hide();
			$("#dvSONGAY_KE_TXT").hide();
			$("#dvSONGAY_KE_LBL").hide();
			$("#dvKE_CHITIET_TXT").show();
			$("#dvKE_CHITIET_LB").show();
		}
		if(_srch_hoatchat == 1){
			$("#txtTENTHUOC").prop('readonly', false);
		}else{
			$("#txtTENTHUOC").prop('readonly', true);
		}
		
		//Load thong tin BN
		layThongTinBenhNhan();
    	//0$12$50$2$0$
		if(_option == '02D019'){
			_loai_tvt_kho = '0';
			_hinhthuc_kho = '9';
			_doituongbenhnhanid = '0';			
		}
		
		//tuyennx_add_start_L2PT-34007	
		if(_option == '02D017' && _loaitiepnhanid == '1' &&
				_configArr.KETHUOC_HINHTHUC_DONGY == '1'){
			_hinhthuc_kho = '4';		
		}
		//tuyennx_add_end_L2PT-34007	
		//tuyennx_add_start_L2PT-34007	
		if(_option == '02D017' && _loaitiepnhanid == '0' &&
				_configArr.KETHUOC_HINHTHUC_DONGY_NTU == '1'){
			_hinhthuc_kho = '5';		
		}
		//tuyennx_add_end_L2PT-34007	
		//tuyennx_add_start_L2PT-34007	
		if(_option == '02D017' && _loaitiepnhanid == '3' &&
				_configArr.KETHUOC_HINHTHUC_DONGY_DTNT == '1'){
			_hinhthuc_kho = '6';		
		}
		//tuyennx_add_end_L2PT-34007
		
		// 0: kho và tủ trực, 1: kho, 2: tủ trực.
		var _filter = "";
		if(_loadkhotheo == '1' || _option == '02D010_1'){ 
			_filter = "loaikho NOT IN (8,9,13)";
		}else if(_loadkhotheo == '2'){
			//tuyennx_edit_start 20180105 L2PT-34 L2PT-39 L2PT-42 L2PT-45
			if(_loai_tvt_kho == '0'){ //neu ke thuoc ko lay tu truc vat tu loaikho = 9
				_filter = "loaikho IN (8,13)";
			}
			else if(_loai_tvt_kho == '1'){ //neu ke vat tu ko lay tu truc thuoc loaikho = 8
				_filter = "loaikho IN (9,13)";
			}
			//_filter = "loaikho IN (8,9,13)";
		}
		else{
			//tuyennx_edit_start 20180105 L2PT-34 L2PT-39 L2PT-42 L2PT-45
			if(_loai_tvt_kho == '0'){ //neu ke thuoc ko lay tu truc vat tu loaikho = 9
				_filter = "loaikho  not IN (9)";
			}
			else if(_loai_tvt_kho == '1'){ //neu ke vat tu ko lay tu truc thuoc loaikho = 8
				_filter = "loaikho not IN (8)";
			}
			//tuyennx_edit_end 20180105
		}
		//tuyennx_add_add_start_20190807 L2PT-7643 L2PT-8479 L2PT-12549
		var ds_khoa = _configArr.DS_KHOA_KHONGTHEOPHONG;
		var ds_khoas = ds_khoa== undefined ? [] : ds_khoa.split(',');
		var _sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			value : _khoaId
		});
		var makhoa = jsonrpc.AjaxJson.getOneValue("NGT.GETMAKHOA", _sql_par);
		
		if(_configArr.LOAD_KHO_THEOPHONG == '1' 
			&& $.inArray( makhoa, ds_khoas) < 0
			&& (_loaitiepnhanid == '1' || (_dichvucha_id != "" && parseInt(_dichvucha_id) > 0))){
			_filter = _filter + (_filter==""? '':' and ') +  ' nvl(phongid,-1) =( CASE WHEN loaikho in(8,9,13)'+
			' THEN '+ _phongId + ' ELSE  nvl(phongid,-1) END)' ;
		}
		//tuyennx_add_end_start_20190807 L2PT-7643
		
		//tuyennx_add_add_start_20190807 L2PT-8073 
		if(_opts._hospital_id == '957') 
			_filter = _filter + (_filter==""? '':' and ') +  '  upper(TENKHO) not like \'%GHÉP%\''  ;
		//tuyennx_add_end_start_20190807 L2PT-8073
		
		//tuyennx_add_add_start_20190807 L2PT-30892
		if(_opts.loaikho){
			if(_opts.loaikho == 1)
				_filter = _filter + (_filter==""? '':' and ') +  '  loaikho = 2'  ;
			else
				_filter = _filter + (_filter==""? '':' and ') +  '  loaikho <> 2'  ;
		} 
		//tuyennx_add_end_start_20190807 L2PT-30892
		
		
    	var _par_kho = _loai_tvt_kho+"$"+_hinhthuc_kho+"$"+_khoaId+"$"+_doituongbenhnhanid+"$0$"+_filter;
    	//tuyennx_add_add_start_20190807 L2PT-34335 L2PT-3826
    	if((_loaitiepnhanid == '1' || _loaitiepnhanid == '3') &&  _configArr.KETHUOC_KHO_TRAITUYEN=='1'
    		&&  BHYT_LOAIID == '4'){ // check ngoai tru
    		_par_kho = _loai_tvt_kho+"$"+_hinhthuc_kho+"$"+_khoaId+"$"+"2"+"$0$"+_filter;
    	}
    	//tuyennx_add_add_end_20190807 L2PT-34335
    	
    	var _macdinh = {extval: true, value:'0',text:'-- Chọn --'};
		if(_option == '02D011'){ //Don thuoc mua ngoai
			_macdinh = {extval: true, value:'0',text:'--Mua ngoài--'};
		}
		
		var ch_par = ['KHO_THEO_SUB_DTBNID']; 
		var _sub_dtbnid = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', ch_par.join('$'));
		if(_opts.sub_dtbnid == _sub_dtbnid){
			var ch_par = ['KHO_BHYT_VIP'];
			sql_par=[];
	    	sql_par.push({"name":"[0]","value":jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', ch_par.join('$'))});
	    	ComboUtil.getComboTag("cboMA_KHO", "NTU02D010.17_1" , sql_par, [], "","sql",'','');
		}else{
			//tuyennx_edit_start_L2PT-6265
			var _selfnc=function() {
				var values = $.map($('#cboMA_KHO option '), function(e) { 
					if(e.value != 0){
						return e.attributes[1].nodeValue;
					}
				})      
				var khos = _makhomacdinh.split(';');
				var _rkhoid = 0;
				if(khos.length > 1){          
					for(var i = 0; i < khos.length; i++){
						if(values.includes(khos[i])){
							_rkhoid = khos[i];
							break;
						}
					}
				}else{
					_rkhoid = _makhomacdinh;
				}
				
				ComboUtil.findByExtra("cboMA_KHO",_rkhoid,0);
				
				//fix cho lac viet truong hop cap nhat k load dc kho L2PT-2589
				if(data_ar != null){
		    		var _row = data_ar[0];
		    		if(_row.KHOTHUOCID){//L2PT-4812
		    			$("#cboMA_KHO").val(_row.KHOTHUOCID);
		    		}
		    		$("#cboMA_KHO").change();//L2PT-4060
				}
			 };
			if(_configArr.PHARMA_CHONKHOTHEOPHONG == '1'){
				_par_kho = _loai_tvt_kho+"$"+_hinhthuc_kho+"$"+_khoaId+"$"+_phongId+"$"+_doituongbenhnhanid+"$0$"+_filter;
				ComboUtil.getComboTag("cboMA_KHO", "NTU02D010.17_2" ,_par_kho, "", _macdinh,"sp","", _selfnc);
			}
			else 
				ComboUtil.getComboTag("cboMA_KHO", "NTU02D010.17" ,_par_kho, "", _macdinh,"sp","", _selfnc);
			//tuyennx_edit_end_L2PT-6265
			
		}	
		//tuyennx_add_start_20200205_L2PT-15725
		if(_option == '02D011' && _configArr.AN_OPTION_MUANGOAI=='1' && _opts.dongy!='1'){ //L2PT-17921
			$("#cboMA_KHO option[value='0']").hide();
		}
		//tuyennx_add_end_20200205_L2PT-15725
		var values = $.map($('#cboMA_KHO option'), function(e) { return e.value; });   
	  	_ds_khoid = values.join(',');
		
		var sql_loaithuoc = "";
		var _kieu = '0';		
		if(_kechungthuocvt == '1'
			&&  _configArr.KETHUOC_CHUNGVT_DOITUONG.includes(_loaitiepnhanid)){ //L2PT-23998
			sql_loaithuoc = "LOAITHUOCVATTU.01";
		}else{
			if(_loainhommaubenhpham_id == '7'){
				sql_loaithuoc = "LOAITHUOC.01";
				_kieu = 1;
			}
			if(_loainhommaubenhpham_id == '8'){
				sql_loaithuoc = "LOAIVATTU.01";
			}
		}
		
		ComboUtil.getComboTag("cboLOAITHUOC", sql_loaithuoc ,[], "", {value:'-1',text:'-- Tất cả --'},"sql","", false);	
		//tuyennx_edit_start_20191007 L2PT-9546
		//ComboUtil.getComboTag("cboDICHVUID","NGTDV.009",sql_par,_opt.dichvuid,{value:'',text:'Chọn yêu cầu khám'},'sql','', '');
		
		/*ComboUtil.getComboTag("cboBACSIID","NGT02K016.EV002",[{"name":"[0]", "value":_opts.khoaId}], "", {value:'-1',text:'Chọn'},"sql","",
		function() {
			if(_configArr.LOAD_BSI_THEO_USER =='1'){
				$("#cboBACSIID").val(_user_id);
			}
		});	*/
		//tuyennx_edit_end_20191007 L2PT-9546
        var sql_par = [];
        sql_par.push({
            "name": "[0]",
            value: _opts.khambenhid
        });
        var _chandoan = jsonrpc.AjaxJson.getOneValue('BACT.GET_CHANDOAN', sql_par);
		
		if(_option == '02D014' || _option == '02D016' || _option == '02D024'){
			var _par_phieutra=[];
	    	_par_phieutra.push({"name":"[0]","value":_loainhommaubenhpham_id});
	    	_par_phieutra.push({"name":"[1]","value":_khambenhid});
	    	_par_phieutra.push({"name":"[2]","value":_kieu});
			ComboUtil.getComboTag("cboDONTHUOCVT", "NTU02D010.PHIEUTRA" , _par_phieutra, "0", {extval: true,value:'0',text:'--Chọn--'},"sql","",false);
			_phieutraid = $('#cboDONTHUOCVT').val();
		}
		
		ComboUtil.getComboTag("cboDUONG_DUNG","NTU02D010.07",[], "", {extval: true,value:'0',text:''},"sql","",false);
		
    	var NGT_GHICHU_BENHCHINH = _configArr.NGT_GHICHU_BENHCHINH;
		if(NGT_GHICHU_BENHCHINH=='1'){
			$("#divBc").removeClass("col-md-8");
			$("#divBc").addClass("col-md-6");
			$('#divSuaBc').css('display','');
		}
		//tuyennx_add_end_20170724 
		
		//L2PT-3550
		if(_configArr.KETHUOC_CORONA == '0'){
			$("#changeCorona").remove();
			$("#changeBHYTCorona").remove();
		}
		
		//tuyennx_add_start_20170724 L2PT-20192
		var data_icd = jsonrpc.AjaxJson.ajaxCALL_SP_O("KETHUOC.GETICD", _khambenhid+"$"+ _opts.phongkhamdangkyid,[]);
    	if(data_icd != null && data_icd.length > 0){
    		$("#txtMACHANDOANICD").val(data_icd[0].MACHANDOAN);
    		_MACHANDOAN = data_icd[0].MACHANDOAN; //L2PT-23947
    		$("#txtTENCHANDOANICD").val(data_icd[0].CHANDOAN);
    		$("#txtTENCHANDOANICD_KT").val(data_icd[0].CHANDOAN_KEMTHEO);
    		$("#txtGHICHU_BENHCHINH").val(data_icd[0].GHICHU_BENHCHINH); //L2PT-33640
    	}
		//tuyennx_add_end
    	
    	//Load thong tin phieu dieu tri
    	if(_phieudieutri_id != ""){
    		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.09", _phieudieutri_id,[]);
        	if(data_ar != null && data_ar.length > 0){
        		var _row = data_ar[0];
        		_icd10_code = _row.MACHANDOAN;
        		_icd10_text = _row.CHANDOAN;
        		
        		$("#lblICD10").val(_icd10_code);
        		$("#lblCHI_DINH").val(_icd10_text);
        		$("#txtTHOI_GIAN").val(_row.NGAYMAUBENHPHAM);
	    		$("#txtTG_DUNG").val(_row.NGAYMAUBENHPHAM);        		
        		$("#lblPHIEU_DT").val(_row.SOPHIEU);
        		if(_phieudieutri_id != ""){
        			_MACHANDOAN = _row.MACHANDOAN; //L2PT-23947
        			$("#txtMACHANDOANICD").val(_row.MACHANDOAN);
            		$("#txtTENCHANDOANICD").val(_row.CHANDOAN);
            		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOAN_KEMTHEO);
        		}
        	}
    	}
    	bindEvent();
		if(_maubenhpham_id != "" && _maubenhpham_id != undefined){
			i_action = "Upd";
			//tuyennx_edit_start_20190507 L2PT-5526
			//data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.11", _maubenhpham_id,[]);
			//tuyennx_edit_end_20190507 L2PT-5526
	    	if(data_ar != null){
	    		var _row = data_ar[0];
	    		$("#txtTHOI_GIAN").val(_row.NGAYMAUBENHPHAM);
	    		$("#txtTG_DUNG").val(_row.NGAYMAUBENHPHAM_SUDUNG);
	        	$("#txtMACHANDOANICD").val(_row.MACHANDOAN);
	        	_MACHANDOAN = _row.MACHANDOAN; //L2PT-23947
	    		$("#txtTENCHANDOANICD").val(_row.CHANDOAN);
	    		$("#cboPHIEU_CD").val(_row.PHIEUDIEUTRIID);
	    		$("#cboMA_KHO").val(_row.KHOTHUOCID);
	    		if(_row.KHOTHUOCID == "0")
	    			$("#chkChiTimTheoNT").prop('checked', false);
	    		else
	    			$("#chkChiTimTheoNT").prop('checked', true);
	    		$("#chkChiTimTheoNT").prop('disabled', true)
	    		//tuyennx_add_start_20190125 L2PT-899
	    		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOAN_KEMTHEO);
	    		//tuyennx_add_end_20190125 
	    		$('#txtDS_THUOC').trigger("focus");
	    		$('#txtSONGAY_KE').val('1');
	    		
	    		$("#txtLOIDANBS").val(_row.LOIDANBACSI);
	    		$("#txtTG_HENKHAM").val(_row.SONGAYHEN);
	    		if(_row.PHIEUHEN == '1'){
	    			$("#chkCapPhieuHenKham").prop('checked', true);
	    		}
	    		$('#txtSLTHANG').val(_row.SLTHANG);
	    		//tuyennx_add_start 20190328 L2PT-2948
	    		$("#txtCACHSACTHUOC").val(_row.CACHSACTHUOC);
	    		$("#txtCACHUONG").val(_row.CACHUONG);	
				//tuyennx_add_end 20190328
	    		$("#cboDONTHUOCVT").val(_row.MAUBENHPHAMCHA_ID);	    		
	    		//_loaikedon = _row.LOAIKEDON;
	    		//$('#txtSONGAY_KE').attr("readonly", true);
	    		$("#cboNGUOI_TC").val(_row.NGUOI_TC);
	    		$("#txtNGAY_TC").val(_row.NGAY_TC);
	    	}else
	    		$('#txtMACHANDOANICD').trigger("focus");
	    	
	    	// kiểu trả = 1 là trả theo tiện ích
	    	if(_kieutra != '1'){
	    		loadGridDonThuoc('DONTHUOC', _maubenhpham_id);
	    	}			
			
			var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			for(var i = 0; i < jsonGridData.length; i ++){
				//doAddDrugToJson(ui, _objDrug, 1);
				 var objRowData = jQuery("#"+_gridDonThuoc).getRowData(jsonGridData[i]);
				 doAddDrugToJson(objRowData, _objDrug, 1);
			}			
			$("#cboMA_KHO").addClass("disabled");
			$("#cboMA_KHO").change();
			$("#cboDONTHUOCVT").addClass("disabled");
			if(_option == '02D014' || _option == '02D016'){
				$("#cboDONTHUOCVT").change();
			}
			
			if(_loaikedon == 1){
				$("#dvKE_TONG_TXT").show();
				if(_option == '02D010' || _option == '02D015'){
					$("#dvSONGAY_KE_TXT").show();
					$("#dvSONGAY_KE_LBL").show();
					$('#txtSONGAY_KE').val('1');
				}else {
					$("#dvSONGAY_KE_TXT").hide();
					$("#dvSONGAY_KE_LBL").hide();
				}			
				$("#dvGhiChu").removeClass("col-xs-3 low-padding").addClass("col-xs-5 low-padding");
				$("#dvKE_TONG_LB").show();
				$("#dvKE_CHITIET_TXT").hide();
				$("#dvKE_CHITIET_LB").hide();
				$("#dvSearchCD").hide();
			}else{
				$("#dvKE_TONG_TXT").hide();
				$("#dvKE_TONG_LB").hide();
				$("#dvSONGAY_KE_TXT").hide();
				$("#dvSONGAY_KE_LBL").hide();
				$("#dvKE_CHITIET_TXT").show();
				$("#dvKE_CHITIET_LB").show();
			}			
		}else{
			i_action = "Add";
			_first_load = 1;
			if($("#txtMACHANDOANICD").val() != "")
				$("#txtDS_THUOC").trigger("focus");
			else
				$('#txtMACHANDOANICD').trigger("focus");
		}
		this.validator = new DataValidator("dvMain");
		//dsThuocKe24h(_khambenhid);
		_jsonThuoc24h = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.19", _khambenhid,[]);
		doLoadPrescription();
		//doCalDrugTicket();
		if(_maubenhpham_id != "" && _maubenhpham_id != undefined){
			var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			tinhTruocTien(_jsonGridData);
			loadAll("","");
			loadComboGrid(1);
			//loadComboGrid(0);
		}		
		
		$('#cboMA_KHO').change();
		
		if(_kieutra == '1' && (_option == '02D014' || _option == '02D016') && _maubenhpham_id != "" && _maubenhpham_id != undefined){
			i_action = "Add";
			$("#txtTHOI_GIAN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
			$('#cboDONTHUOCVT').val(_maubenhpham_id);					
			$('#cboDONTHUOCVT').change();
		}
		//tuyennx_add_start_20180605
//		$('#jqgh_grdDONTHUOC_cb').css('display','none');
//		GridUtil.setGridParam(_gridDonThuoc, {
//			onSelectRow : function(id, status) {				
//				if (!status) {
//					var valueold = $("#"+_gridDonThuoc).jqGrid ('getCell', id, 'SO_LUONG');
//					$("#"+_gridDonThuoc).jqGrid ('setCell', id, 'SO_LUONG', Math.ceil(valueold));
//					
//					var value = $("#"+_gridDonThuoc).jqGrid ('getCell', id, 'SO_LUONG');
//					var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
//					tinhTruocTien(_jsonGridData);
//					loadAll("","");
//					$('#'+_gridDonThuoc).jqGrid('setCell',id,'OLDVALUE',value);
//				}
//			}
//		});
		//tuyennx_add_end_20180605
		
		if(_dichvucha_id > 0){
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
		}
		//tuyennx_add_start_20181206  L2HOTRO-13088
		$("#dvSONGAY_KE_TXT").show();
		$("#dvSONGAY_KE_LBL").show();
		$('#txtSONGAY_KE').val('1');
//		if(jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','HIS_KEDONTHUOC_CHITIET_NTU') == '0'){
//			$("#dvSONGAY_KE_TXT").show();
//			$("#dvSONGAY_KE_LBL").show();
//			$('#txtSONGAY_KE').val('1');
//		}else {
//			$("#dvSONGAY_KE_TXT").hide();
//			$("#dvSONGAY_KE_LBL").hide();
//		}
		//tuyennx_add_end_20181206 
		//tuyennx_add_start_20180919 L2HOTRO-10337
		if(_configArr.LOAD_TGDUNG_THEO_TGCD == '1')
			$('#spTG_DUNG').prop("onclick", null).off("click");
		//tuyennx_add_end_20180919 
		
		//L2PT-9684
		if(_configArr.KETHUOC_SUA_NGAYKE_DTRI  == '1' && _phieudieutri_id){
			$('#spTG_DUNG').prop("onclick", null).off("click");
			$('#spTHOI_GIAN').prop("onclick", null).off("click");
		}
		
		
		//tuyennx_add_start_20180919 L2HOTRO-13865 L2PT-26896
		var parttt = ['KIEU_LOAD_DIKEM_DVKT']; 
		var _kieu_load = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', parttt.join('$'));		
		if(_kieu_load == '1'){
			if(_dichvucha_id != "" && _macdinh_hao_phi == '9'){
				var objThuocDV = new Object();
				objThuocDV.LOAINHOMMAUBENHPHAM = _loainhommaubenhpham_id;
				
				//BVTM-1789
				var _objDSThuoc ;
				if(_configArr.KE_THUOC_DV_KEM_MAU == '1'){				
					_objDSThuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("DSTHUOC.KEMDV.MAU", _dichvucha_id+"$"+_ds_khoid+"$"+JSON.stringify(objThuocDV),[]);
					if(_objDSThuoc+'' == 'undefined'){
						//$("#btnSave").hide();
						DlgUtil.showMsg('Chưa cấu hình gói thuốc/VT cho dịch vụ hoặc dịch vụ đã được kê.');
					}
					/*
					else{
						window.onload = function onload(){
							$("#btnDTMau").trigger("click");
						};
					}*/
				}else{
					_objDSThuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.DSTHUOC.KEMDV1", _dichvucha_id+"$"+_ds_khoid+"$"+JSON.stringify(objThuocDV),[]);
					
					for(var i = 0; i< _objDSThuoc.length;i++){
						if(dsThem.indexOf(_objDSThuoc[i].THUOCVATTUID) == -1  ){
							callbackthuoc(_objDSThuoc[i]);
							$("#cboDUONG_DUNG").val(_objDSThuoc[i].DUONGDUNGID);
							$("#txtSOLUONG_TONG").val(_objDSThuoc[i].SO_LUONG);
							$("#txtGHICHU").val(_objDSThuoc[i].CACHDUNG);
							$("#txtLIEUDUNG").val(_objDSThuoc[i].CACHDUNG);
							dsThem.push(_objDSThuoc[i].THUOCVATTUID);
							addThuoc();
						}
						_ketugoi = '1';//L2PT-409
						if(parseInt(check_c) > 1){
							window.onload = function onload(){
								$("#btnDTMau").trigger("click");
							};
						}
					}
				}
			}
		}else{
			if(_dichvucha_id != ""){
				var objThuocDV = new Object();
				objThuocDV.LOAINHOMMAUBENHPHAM = _loainhommaubenhpham_id;
				
				//L2PT-34136
				var _objDSThuoc ;
				if(KE_THUOC_DV_KEM_MAU == '1'){
					_objDSThuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("DSTHUOC.KEMDV.MAU", _dichvucha_id+"$"+_ds_khoid+"$"+JSON.stringify(objThuocDV),[]);
					if(_objDSThuoc && _objDSThuoc.length > 0 && _objDSThuoc[0].CHECK_C != '1'){
						check_c = _objDSThuoc[0].CHECK_C;
						_objDSThuoc =[];	
					}
					//L2PT-409
					else if(_objDSThuoc && _objDSThuoc.length > 0 && _objDSThuoc[0].CHECK_C == '1')
						_mbp_temp = _objDSThuoc[0].DICHVUTEMPID; 
						
					
				}			
				else
					_objDSThuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.DSTHUOC.KEMDV1", _dichvucha_id+"$"+_ds_khoid+"$"+JSON.stringify(objThuocDV),[]);
				if(parseInt(check_c) > 1){
					window.onload = function onload(){
						$("#btnDTMau").trigger("click");
					};
				}
				
				var dsThem = [];
				for(var i = 0; i< _objDSThuoc.length;i++){
					if(dsThem.indexOf(_objDSThuoc[i].THUOCVATTUID) == -1  ){
						callbackthuoc(_objDSThuoc[i]);
						$("#cboDUONG_DUNG").val(_objDSThuoc[i].DUONGDUNGID);
						$("#txtSOLUONG_TONG").val(_objDSThuoc[i].SO_LUONG);
						$("#txtGHICHU").val(_objDSThuoc[i].CACHDUNG);
						$("#txtLIEUDUNG").val(_objDSThuoc[i].CACHDUNG);
						dsThem.push(_objDSThuoc[i].THUOCVATTUID);
						addThuoc();
					}
					if(_macdinh_hao_phi == '9')
						_ketugoi = '1';//L2PT-409
				}
			}
			//tuyennx_add_end_20180919 
		}		
		
		//tuyennx_add_start_20200722 L2PT-23918
		if(_loaitiepnhanid == '1'){ // check ngoai tru
			var param = [_khambenhid];
			list_thuoc_trongngay = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU.GETTHUOC.CR", param.join('$'));
		}
		//tuyennx_add_end_20200722 L2PT-23918
		
		//tuyennx_add_start_20200722 L2PT-25085
		//KETHUOC_ANNGAYKEDON = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','KETHUOC_ANNGAYKEDON') 
		if(_configArr.KETHUOC_ANNGAYKEDON == '1'){
			$("#divSoNgay").hide();
			$("#divSoNgayLB").hide();
			$("#txtSO_NGAY").val("1");
			$("#divSoLuong").removeClass("col-xs-2").addClass("col-xs-4");
		}
		//tuyennx_add_end_20200722 L2PT-25085
		
		initPopupTuongTacThuoc(); //tuyennx_add_start_20200825 L2PT-26711
		
		//L2PT-29871
		var tuoi = $('#lblBIRTHDAY_YEAR').val().includes('/') ? $('#lblBIRTHDAY_YEAR').val().split('/')[2]:$('#lblBIRTHDAY_YEAR').val()  ;
		_tuoi =  parseInt(jsonrpc.AjaxJson.getSystemDate('YYYY'))- parseInt(tuoi) ;
		
		//L2PT-32063
		if(_configArr.KE_THUOC_HAOPHI_THEOGOI == '1' && _macdinh_hao_phi == '9' && (_dichvucha_id != "" && parseInt(_dichvucha_id) > 0)){
			$("#txtDS_THUOC").prop('readonly', true);
			$("#txtDS_HOATCHAT").prop('readonly', true);
			$('#btnSave').focus();
		}   
		
		//BVTM-1288
		if(_dichvucha_id && _configArr.KE_THUOC_THOIGIAN_PTTT == '1'){
			_objPTTT = jsonrpc.AjaxJson.ajaxCALL_SP_O("KETHUOC.PTTT", _dichvucha_id,[]);
			if(_objPTTT && _objPTTT.length > 0){
				$('#txtTHOI_GIAN').val(_objPTTT[0].TGBATDAU);
				$('#txtTG_DUNG').val(_objPTTT[0].TGKETTHUC);
				
				/*//BVTM-3184
				if(_objPTTT[0].MA_CHANDOANSAUPHAUTHUAT != "" && _objPTTT[0].CHANDOANSAUPHAUTHUAT != ""){
					$('#txtMACHANDOANICD').val(_objPTTT[0].MA_CHANDOANSAUPHAUTHUAT);
					$('#txtTENCHANDOANICD').val(_objPTTT[0].CHANDOANSAUPHAUTHUAT);
				}*/
			}
		}  
		
		//BVTM-3194
		if(_configArr.KE_THUOC_THOIGIAN_VAOKHOA == '1' && _loaitiepnhanid == '0' && _dichvucha_id == ""){
			_tgVaoKhoa = jsonrpc.AjaxJson.ajaxCALL_SP_O("KETHUOC.TG_VAOKHOA", _khambenhid,[]);
			if(_tgVaoKhoa && _tgVaoKhoa.length > 0){
				$('#txtTHOI_GIAN').val(_tgVaoKhoa[0].TGVAOKHOA);
				$('#txtTG_DUNG').val(_tgVaoKhoa[0].TGVAOKHOA);
			}
		}  
		
		// ẩn các control trên form thiết lập id trong biến cấu hình.
		hiddenControl(_configArr.KE_THUOC_HIDE_CONTROL);
		
		//disableControl(_configArr.KE_THUOC_DISABLE_CONTROL);
	}
	
	function setTextToButton(_btnId, _btnText, _className){
		$("#"+_btnId+"").append("<span class='"+_className+"'></span> " + _btnText);
	}
	
	function loadDSPhieuTra(){
		if(_option == '02D014' || _option == '02D016' || _option == '02D024'){
			var _par_phieutra=[];
	    	_par_phieutra.push({"name":"[0]","value":_loainhommaubenhpham_id});
	    	_par_phieutra.push({"name":"[1]","value":_khambenhid});
	    	_par_phieutra.push({"name":"[2]","value":_kieu});
			ComboUtil.getComboTag("cboDONTHUOCVT", "NTU02D010.PHIEUTRA" , _par_phieutra, "0", {extval: true,value:'0',text:'--Chọn--'},"sql","",false);
			_phieutraid = $('#cboDONTHUOCVT').val();
		} 
	}
	
	function layThongTinBenhNhan(){
		_tyle_miengiam = 0;
		var _par_loai = [_khambenhid,_phongId];
		data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.10", _par_loai.join('$'),[]);
		_thoigian_vaovien = "";
    	if(data_ar != null && data_ar.length > 0){
    		var _row = data_ar[0];
    		
			//thaiph - L2PT-4340
			var checkBC = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','NGT_CHINHSUABENHCHINH');
			if(checkBC =='1' && _row.DOITUONGBENHNHANID=='2'){
				$("#txtTENCHANDOANICD").removeAttr("readonly");
			}
			if(checkBC =='2'){
				$("#txtTENCHANDOANICD").removeAttr("readonly");
			}
			if(checkBC =='0'){
				$("#txtTENCHANDOANICD").attr("readonly", "readonly");
			}
			//end thaiph - L2PT-4340
    		
    		//mbp.chandoan, mbp.machandoan, mbp.phieudieutriid
    		
    		/*if(_row.TRANGTHAIKHAMBENH == '1'){
    			EventUtil.raiseEvent("assignDrug_loisaibn",{option:_opts.option});
    		}*/
    		
			/*if(_opts._mabenhnhan != undefined &&_row.MABENHNHAN !== _opts._mabenhnhan){
    			EventUtil.raiseEvent("assignDrug_khacbn",{option:_opts.option});
    		}*/
    		
    		_thoigian_vaovien = _row.THOIGIANVAOVIEN;
    		$('#lblPATIENTCODE').val(FormUtil.unescape(_row.MABENHNHAN));
        	$('#lblPATIENTNAME').val(FormUtil.unescape(_row.TENBENHNHAN));
        	$('#lblBIRTHDAY_YEAR').val(FormUtil.unescape(_row.NAMSINH));
        	$('#lblGIOITINHCODE').val(FormUtil.unescape(_row.GIOITINH));
        	$('#txtDIACHI').val(FormUtil.unescape(_row.DIACHI));
        	$('#lblMA_BHYT').val(FormUtil.unescape(_row.MA_BHYT));
        	$('#lblDT_THANHTOAN').val(FormUtil.unescape(_row.TEN_DTBN));
        	$('#lblMUCHUONG_BHYT').val(FormUtil.unescape(_row.TYLE_BHYT));        	
        	$('#hidDIACHI').val(FormUtil.unescape(_row.DIACHI));
        	$('#hidBENHNHANID').val(FormUtil.unescape(_row.BENHNHANID));
        	$('#hidHOSOBENHANID').val(FormUtil.unescape(_row.HOSOBENHANID));
        	$('#hidSDTBENHNHAN').val(FormUtil.unescape(_row.SDTBENHNHAN));
        	$('#txtSDT_BENHNHAN').val(FormUtil.unescape(_row.SDTBENHNHAN)); // SMS
        	// tuyennx day congboyte add _start
        	$('#hidMAHOSOBENHAN').val(FormUtil.unescape(_row.MAHOSOBENHAN));
        	$('#hidGIOITINHID').val(FormUtil.unescape(_row.GIOITINHID));
        	$('#hidNGAYSINH').val(FormUtil.unescape(_row.NGAYSINH));
        	// tuyennx day congboyte add end
        	$('#lblKHAMBENH_CANNANG').val(FormUtil.unescape(_row.KHAMBENH_CANNANG));
        	if(_row.HANTHE)
        		$('#lblSO_NGAY_BHYT').val(FormUtil.unescape(_row.HANTHE)); //L2PT-7091
        	
        	$('#hidNGHENGHIEP').val(FormUtil.unescape(_row.NGHENGHIEP));        	
        	_ngaytiepnhan = _row.NGAYTIEPNHAN;
        	_indonthuoc_huongthan = _row.INHUONGTHAN;
        	_ngay_bhyt_kt = _row.BHYT_KT;
        	_ngay_bhyt_bd = _row.BHYT_BD; //L2PT-3717
        	_tradu6thangluongcoban	= _row.TRADU6THANGLUONGCOBAN;
        	_duoc_van_chuyen		= _row.DUOC_VAN_CHUYEN;
        	_tiepnhanid 	= _row.TIEPNHANID;
        	_loaitiepnhanid = _row.LOAITIEPNHANID;
        	_indonthuoc_noitru = _row.INDONTHUOC;
        	_songaykemax = _row.SONGAYKEMAX;
        	_soluongthuocmax = _row.SOLUONGTHUOCMAX;
        	_canhbaophacdo = _row.CANHBAOPHACDO;
        	_hopdongid     = _row.HOPDONGID;     
        	//tuyennx_add_start_20170724 
    		$("#txtGHICHU_BENHCHINH").val(_row.GHICHU_BENHCHINH);
    		//tuyennx_add_end_20170724 
        	if(_loaitiepnhanid == 0 || _loaitiepnhanid == 5){ //L2PT-28787
        		_hinhthuc_kho = 12;
        		$("#txtMACHANDOANICD").val(_row.MACHANDOANVAOKHOA);
        		_MACHANDOAN = _row.MACHANDOANVAOKHOA; //L2PT-23947
        		$("#txtTENCHANDOANICD").val(_row.CHANDOANVAOKHOA);
        		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOANVAOKHOA_KEMTHEO);
        	}
        	else if(_loaitiepnhanid == 1){
        		_hinhthuc_kho = 13;
        		$("#txtMACHANDOANICD").val(_row.MACHANDOANRAVIEN);
        		_MACHANDOAN = _row.MACHANDOANVAOKHOA; //L2PT-23947
        		$("#txtTENCHANDOANICD").val(_row.CHANDOANRAVIEN);
        		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOANRAVIEN_KEMTHEO);
        	}else if(_loaitiepnhanid == 3){
        		_hinhthuc_kho = 10;
        		$("#txtMACHANDOANICD").val(_row.MACHANDOANVAOKHOA);
        		_MACHANDOAN = _row.MACHANDOANVAOKHOA; //L2PT-23947
        		$("#txtTENCHANDOANICD").val(_row.CHANDOANVAOKHOA);
        		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOANVAOKHOA_KEMTHEO);
        	}
        	if(_option == '02D011')//Nếu là mua ngoài -> băt buộc = 9
        		//_hinhthuc_kho = 9;
				_hinhthuc_kho = 15; //L2PT-4180
        	_ma_bhyt = _row.MA_BHYT;
        	_ten_doituong_benhnhan = _row.TEN_DTBN;
        	_tyle_bhyt = _row.TYLE_BHYT;
        	_doituongbenhnhanid = _row.DOITUONGBENHNHANID;
        	_benhnhanid = _row.BENHNHANID;
    		
        	if(_benhnhanid != ""){
        		$("#btnTConSD").prop('disabled', false);
        	}else{
        		$("#btnTConSD").prop('disabled', true);
        	} 
        	
        	// tham sô cho phép kê nhiều ngày ko  L2PT-29403
        	//if(_row.KENHIEUNGAY != '1'){
        	if(_row.KENHIEUNGAY == '0' || _row.KENHIEUNGAY.includes(_loaitiepnhanid) 
        			|| (_loaitiepnhanid == '0' && _row.KENHIEUNGAY == '2')){
        		$("#txtSONGAY_KE").prop('disabled', false);
        	}else{
        		$("#txtSONGAY_KE").prop('disabled', true);
        	}  
        	
        	// tham số cho phép kê thuốc lẻ hay ko
        	if(_row.CAPTHUOCLE == '1'){
        		$("#txtSOLUONG_TONG").removeClass('clsnumber');
        		$("#txtSOLUONG_CHITIET").removeClass('clsnumber');
        		$("#txtSANG").removeClass('clsnumber');
        		$("#txtTRUA").removeClass('clsnumber');
        		$("#txtCHIEU").removeClass('clsnumber');
        		$("#txtTOI").removeClass('clsnumber');        		
        		//$("#txtSOLUONG_TONG").addClass('clsfloat');
        		$("#txtSOLUONG_CHITIET").addClass('clsfloat');
        		$("#txtSANG").addClass('clsfloat');
        		$("#txtTRUA").addClass('clsfloat');
        		$("#txtCHIEU").addClass('clsfloat');
        		$("#txtTOI").addClass('clsfloat');
        	} 
        	
        	_makhomacdinh = _row.MAKHO;        	
        	r_loaicheck = _row.LOAICHECK;
        	_cachdung_sangchieu = _row.CACHDUNG;
        	_checkdiung = _row.CHECKDIUNGTHUOC;
        	
        	if(_checkdiung != '1'){
        		$('#btnTDiUng').remove();
        	}
        	
        	_botimcachdung = _row.ANTIMCACHDUNGDT;
        	if(_botimcachdung != '1'){
        		$('#dvSearchCD').remove();
        		$('#dvGhiChu').removeClass('col-xs-2');
        		$('#dvGhiChu').addClass('col-xs-3');
        	}
        	
        	_sudungphacdo = _row.SUDUNGPHACDO;
        	if(_sudungphacdo != '1'){
        		$('#btnPdDt').remove();        		
        	}        	
        	
        	if(_row.LOAITIEPNHANID != '1'){
        		$("#btnXuTri").hide();
        	}
        	
        	if(_row.KTCODONTHUOC == '1' && _row.KOKTKHICODONTHUOC != '1'){
        		$('#btnXuTri').css('display','none');
        	}
        	
        	_tyle_miengiam = _row.TYLE_MIENGIAM;   
        	_chanhoatchat = _row.CHANHOATCHAT;
        	_kechungthuocvt = _row.KECHUNGTHUOCVT;
        	if(_row.DOITUONGBENHNHANID == "1"){
        		_ngayhanthe = _row.HANTHE;
        	}else{
        		_ngayhanthe = 365;
        	}
        	
        	// chỉ check với cấp thuốc - vật tư - đông y
        	if(_option == '02D010' || _option == '02D015' || _option == '02D017') {
        		if(_row.DOITUONGBENHNHANID == "1" && (parseInt(_row.HANTHE) <= parseInt(_row.NGAYTHE))){
            		DlgUtil.showMsg('Thẻ của bệnh nhân sắp hết hạn, còn ' + _row.HANTHE + ' ngày sử dụng');
            	}
        	}       	        	
        	
        	if(_cachdung_sangchieu == '1'){
        		$('#divhenkham').css('display','');
        	}
        	//lay danh sach a hoat chat thuoc da ke trong dot dieu trị ngoai tru
        	_objTmpThuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET_MAHOATCHAT", _tiepnhanid,[]);
        	
        	_ngaykethuocmax = _row.NGAYKEMAX;        	
        	_kieucheck = _row.KIEUCHECK;
        	
        	_checktrunghoatchat = _row.CHECKTRUNGHOATCHAT;
        	
        	if(_row.AN_CBO_LOAITHUOC == '1'){
        		$('#cboLOAITHUOC').addClass("disabled");
        	}else{
        		$('#cboLOAITHUOC').removeClass("disabled");
        	}
        	
        	_bacsi_ke = _row.BACSI_KE;
        	//if(_bacsi_ke == '1' && _loaikedon == '1'){
        	//tuyennx_edit_start_20191007 L2PT-9546 L2PT-9545
        	if(_bacsi_ke == '1' && _configArr.KETHUOC_BSKE_DOITUONG.includes(_loaitiepnhanid)){
        		$('#divbske').css('display','');
        	}     
        	//tuyennx_edit_end_20191007 L2PT-9546 L2PT-9545
        	
        	_format_cd = _row.FORMAT_CD;
        	_tudongindt  = _row.TUDONGIN;
        	_kieucheck_hoatchat = _row.KIEUCHECK_HOATCHAT;
        	_ke_tunhieu_kho = _row.KE_TUNHIEU_KHO;
        	_sudung_lieudung = _row.SUDUNG_LIEUDUNG;
        	
        	BHYT_LOAIID =  _row.BHYT_LOAIID; //L2PT-34335
        	
        	/*if(_loaitiepnhanid == '0'){
        		$('#dvSearchName').removeClass("col-xs-2");
        		$('#dvSearchName').addClass("col-xs-3");
        		
        		$('#dvDS_THUOC').removeClass("col-xs-2");
        		$('#dvDS_THUOC').addClass("col-xs-3");
        		
        		$('#dvGhiChu').removeClass("col-xs-2");
        		$('#dvGhiChu').addClass("col-xs-3");
        		
        		$('#dvlLIEU_DUNG').removeClass("col-xs-2");
        		$('#dvlLIEU_DUNG').addClass("col-xs-3");
        		
        		$('#dvLIEU_DUNG').removeClass("col-xs-2");
        		$('#dvLIEU_DUNG').addClass("col-xs-3");
        	}else if(_loaitiepnhanid == '3'){
        		$('#dvlGhiChu').removeClass("col-xs-3");
        		$('#dvlGhiChu').addClass("col-xs-2");
        	}*/
        	
        	//if(_sudung_lieudung == '1' &&  $.inArray(_option, ['02D010', '02D017']) >= 0) {
        	if(_sudung_lieudung == '1' &&  $.inArray(_option, ['02D010']) >= 0) {
        		$('#dvlLIEU_DUNG').css('display','');
        		$('#dvLIEU_DUNG').css('display','');
        		
        	}
        	/*	_lbllieudung = "250,0,f,c;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c;";
        	}else{
        		_lbllieudung = "350,0,f,c;";
        	}
        	
*/        	// lấy thông tin thanh toán
        	/*var vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.VIENPHI",_tiepnhanid);
    		if(vp_ar != null && vp_ar.length > 0){
    			var data = vp_ar[0];
    			
    			_tongtien = data.TONGTIENDV;
    			_tientu = data.TAMUNG;
    			_danop = data.DANOP;				
    		}*/
    		
    		_kieucanhbaotientamung = _row.KIEUCANHBAOTIENTAMUNG;
    		_sudung_dvqd_kethuoc =  _row.SUDUNG_DVQD_KETHUOC;
    		
    		if(_sudung_dvqd_kethuoc == '0'){ //L2PT-5948
    			$("#dvQĐ").hide();
    			$("#dvlDVQD").hide();
    		}
    		
    		//Start manhnv 04/10/2018 ADD
    		/*_sudung_tyle_kieukien =  _row.NGT_CHECKTHUOC_TYLE_DIEUKIEN;
    		if(_sudung_tyle_kieukien != '1'){
    			$("#dvTYLETHUOC").hide();
    			$("#dvlTylethuoc").hide();
    		}*/
    		//END manhnv 04/10/2018 ADD
    		
    		_ds_id_loaitvt = _row.DS_ID_LOAITVT.split(',');
    		_phieudtri_kedon = _row.PHIEUDTRI_KEDON;
    		
    		if(_macdinh_hao_phi == '9' || (_dichvucha_id != "" && parseInt(_dichvucha_id) > 0)){
    			_phieudtri_kedon = 0;
    		}  

    		_phieudtri_trathuoc = _row.PHIEUDTRI_TRATHUOC;
    		_loaikedonthuoc = _row.PHIEUDTRI_LOAIKEDON.split(',');
    		if( $.inArray(_option, _loaikedonthuoc) >= 0 && $.inArray(_loaitiepnhanid, _configArr.KETHUOC_LOAITN_PDIEUTRI) >= 0&& _phieudtri_kedon == "1"){
    			$('#divPhieuDT').css('display','');
    			if(_option != '02D011'){
    				$('#lblpdtri').addClass('required');
    			}
    		}/*else{
    			$('#divPhieuDT').css('display','');
    			$('#lblpdtri').addClass('required');
    		}*/
    		
    		//_checkngaykedonntu = _row.CHECKNGAYKEDONNTU;

    		_ngt_lamtron_kedon = _row.NGT_LAMTRON_KETHUOC;
    		_an_menu_phai_kedon = _row.AN_MENU_PHAI_KEDON;
    		_chonkho_kedon = _row.KETHUOC_CHONKHO;
    		_sudungthuoc12sao = _row.SUDUNGTHUOC12SAO;
    		
    		//tuyennx_add_start_20190507 L2PT-5682
    		KT_GOPCHUNG_CANHBAO = _row.KT_GOPCHUNG_CANHBAO;
    		KT_SUDUNG_KHANGSINH = _row.KT_SUDUNG_KHANGSINH;
    		//tuyennx_add_end_20190507 L2PT-5682
    		
    		//tuyennx_add_start L2PT-7416
    		sl_max = _row.SOLUONGKETHUOC_MAX;
    		canhbaoke_max = _row.CANHBAOKETHUOC_MAX; 
    		//tuyennx_add_end L2PT-7416
    		
    		_timkiem_bn_kedon_ntu = _row.TIMKIEM_BN_KEDON_NTU;
    		if(_timkiem_bn_kedon_ntu == '1' && (_loaitiepnhanid == 0 || _loaitiepnhanid == 3)){ //L2PT-26713
    			$("#lblPATIENTCODE").removeAttr('readonly');
    		}else{
    			$("#btnNhapMoi").remove();
    		}
    		
    		if(_row.SOTHANG_DONGY > 0) {
    			$('#txtSLTHANG').val(_row.SOTHANG_DONGY);
    		}
    		if(_row.TIEM_CHUNG == 1){
    			type_tc = 1;
    			$("#divTiemChung").css("display","");
    			ComboUtil.getComboTag("cboNGUOI_TC","NGT02K016.EV002",[{"name":"[0]", "value":_opts.khoaId}], "", {value:'-1',text:'Chọn'},"sql","",
    					function() {
    						if(_configArr.LOAD_BSI_THEO_USER =='1'){
    							$("#cboNGUOI_TC").val(_user_id);
    						}
    					});
    		}
    	}else{    		
    		_thoigian_vaovien = "";
    		$('#lblPATIENTCODE').val(FormUtil.unescape(""));
        	$('#lblPATIENTNAME').val(FormUtil.unescape(""));
        	$('#lblBIRTHDAY_YEAR').val(FormUtil.unescape(""));
        	$('#lblGIOITINHCODE').val(FormUtil.unescape(""));
        	$('#txtDIACHI').val(FormUtil.unescape(""));
        	$('#lblMA_BHYT').val(FormUtil.unescape(""));
        	$('#lblDT_THANHTOAN').val(FormUtil.unescape(""));
        	$('#lblMUCHUONG_BHYT').val(FormUtil.unescape(""));        	
        	$('#hidDIACHI').val(FormUtil.unescape(""));
        	$('#hidBENHNHANID').val(FormUtil.unescape(""));
        	$('#hidHOSOBENHANID').val(FormUtil.unescape(""));
        	// tuyennx day congboyte add _start
        	$('#hidMAHOSOBENHAN').val(FormUtil.unescape(""));
        	$('#hidGIOITINHID').val(FormUtil.unescape(""));
        	$('#hidNGAYSINH').val(FormUtil.unescape(""));
        	// tuyennx day congboyte add end
        	
        	$('#hidNGHENGHIEP').val("");        	
        	_ngaytiepnhan = "";
        	_indonthuoc_huongthan = "";
        	_ngay_bhyt_kt = 0;
        	_tradu6thangluongcoban	= "";
        	_duoc_van_chuyen		= "";
        	_tiepnhanid 	= 0;
        	_loaitiepnhanid = -1;
        	_indonthuoc_noitru = 0;
        	_songaykemax = 0;
        	_soluongthuocmax = 0;
        	_canhbaophacdo = 0;
        	_hopdongid     = 0;       	

    		_hinhthuc_kho = 12;
    		$("#txtMACHANDOANICD").val("");
    		_MACHANDOAN = ""; //L2PT-23947
    		$("#txtTENCHANDOANICD").val("");
    		$("#txtTENCHANDOANICD_KT").val("");
        	
        	_ma_bhyt = "";
        	_ten_doituong_benhnhan = 0;
        	_tyle_bhyt = 0;
        	_doituongbenhnhanid = 0;
        	_benhnhanid = 0      	;
        	_tyle_miengiam = 0;
        	_objTmpThuoc = [];
			_tongtien = 0;
			_tientu = 0;
			_danop = 0;	
			$('#msgCNKQ').text("");
			jQuery("#grdDONTHUOC").jqGrid("clearGridData");
    	}
    	//tuyennx_edit_start_20200801_L2PT-25490 cho vào ham lay ttbn
    	sql_par=[];
    	sql_par.push({"name":"[0]","value":_khambenhid});
    	ComboUtil.getComboTag("cboPHIEU_CD", "NTU02D010.08" , sql_par, _phieudieutri_id, {value:'',text:'-- Chọn --'},"sql",'','');
    	//tuyennx_edit_end_20200801_L2PT-25490
    	//L2PT-7589
    	if(_phieudieutri_id && jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KETHUOC_EDIT_PDT')=='1')
    		$('#cboPHIEU_CD').addClass("disabled");
	}
	
	EventUtil.setEvent("saveHDSD",function(e){
		$('#'+_gridDonThuoc).jqGrid('setCell', e.rowid, 'HUONGDAN_SD', e.text);
		$('#'+_gridDonThuoc).jqGrid('getLocalRow', e.rowid).HUONGDAN_SD = e.text;
		
		$('#'+_gridDonThuoc).jqGrid('setCell', e.rowid, 'DUONG_DUNG', e.duongdung);
		$('#'+_gridDonThuoc).jqGrid('getLocalRow', e.rowid).DUONG_DUNG = e.duongdung;
		
		//L2PT-29773
		$('#'+_gridDonThuoc).jqGrid('setCell', e.rowid, 'DUONGDUNGID', e.duongdungid);
		$('#'+_gridDonThuoc).jqGrid('getLocalRow', e.rowid).DUONGDUNGID = e.duongdung;
		
		// SONDN 27/03/2018
		$('#'+_gridDonThuoc).jqGrid('setCell', e.rowid, 'LIEUDUNG', e.lieudung);
		$('#'+_gridDonThuoc).jqGrid('getLocalRow', e.rowid).LIEUDUNG = e.lieudung;
		// END SONDN 27/03/2018
		
		var _paramTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', e.rowid);
		$("#btnTHUOC_" + _paramTmp.THUOCVATTUID).text(splitDd(e.text));
		DlgUtil.close("dlgDuongDung");
	});
	
	function updateJson(jsonObj, _key, _valueComp, _noteUpdate, _value){
		for (var i=0; i<jsonObj.length; i++) {
			if (jsonObj[i][_key] == _valueComp && i == (jsonObj.length -1)) {
				jsonObj[i][_noteUpdate] = _value;
				jsonObj[i][_noteUpdate] = _value;
				if(_noteUpdate == "SO_LUONG"){
					jsonObj[i]['OLDVALUE'] = _value;
					var _so_luong = jsonObj[i].SO_LUONG;
					_don_gia = parseInt(jsonObj[i].DON_GIA.replaceAll(',',''));
				    var ngay_dv = $("#txtTHOI_GIAN").val().trim();
				    //alert(ngay_dv.substring(0, 10));
					var objTinhTien = new Object();
			    	objTinhTien.DOITUONGBENHNHANID 	= _doituongbenhnhanid;
			    	objTinhTien.MUCHUONG 			= parseFloat(_tyle_bhyt);
			    	objTinhTien.GIATRANBH 			= parseFloat(jsonObj[i].GIATRANBHYT);
			    	objTinhTien.GIABHYT 			= parseFloat(_don_gia);
			    	objTinhTien.GIAND 				= parseFloat(_don_gia);
			    	objTinhTien.GIADV 				= parseFloat(_don_gia);
			    	objTinhTien.GIANN 				= parseFloat(_don_gia);
			    	objTinhTien.DOITUONGCHUYEN 		= jsonObj[i].ID_DT_MOI;
			    	objTinhTien.GIADVKTC 			= 0;
			    	objTinhTien.MANHOMBHYT 			= _nhom_mabhyt_id;
			    	objTinhTien.SOLUONG 			= _so_luong;
			    	objTinhTien.CANTRENDVKTC 		= 0;
			    	objTinhTien.THEDUTHOIGIAN 		= _tradu6thangluongcoban;
			    	objTinhTien.DUOCVANCHUYEN 		= _duoc_van_chuyen;
			    	objTinhTien.TYLETHUOCVATTU 		= _tyle_bhyt_tt_tvt;
			    	objTinhTien.NGAYHANTHE			= _ngay_bhyt_kt;
			    	objTinhTien.NGAYHANTHE_BD = _ngay_bhyt_bd; //L2PT-3717
			    	objTinhTien.NGAYDICHVU			= ngay_dv.substring(0, 10);
			    	objTinhTien.TYLE_MIENGIAM 		= _tyle_miengiam;
			    	
			    	if (_configArr.HIS_HANTHE_BHYT != '0') {
			    		objTinhTien.NGAYGIAHANTHE = _configArr.HIS_HANTHE_BHYT;
			        }
			    	var r = vienphi.tinhtien_dichvu(objTinhTien);
					jsonObj[i].THANH_TIEN = r.tong_cp;//""+_thanh_tien.formatMoney(0);
					jsonObj[i].BH_TRA = r.bh_tra;//""+_bh_tra.formatMoney(0);
					jsonObj[i].ND_TRA = r.nd_tra;//""+_nd_tra.formatMoney(0);
					//tuyennx_add_start_20180813 L2PT-5527
					if($.inArray( _option, ['02D011','02D019']) >= 0){
						jsonObj[i].THANH_TIEN = 0;
						jsonObj[i].BH_TRA = 0;
						jsonObj[i].ND_TRA = 0;
					}
					//tuyennx_add_end_20180813 L2PT-5527
				}
				break;
		  	}
		}
	}
	
	function _openformhenkham(){
		var _songayhen = parseInt($('#txtTG_HENKHAM').val());
		var _ngayht = jsonrpc.AjaxJson.getSystemDate('DD-MM-YYYY');
		var _ngayhen = moment(_ngayht,"DD-MM-YYYY").add('days', _songayhen).format('DD/MM/YYYY');
		var param = {
			mabenhnhan : $('#lblPATIENTCODE').val(),
			tenbenhnhan : $('#lblPATIENTNAME').val(), 
			nghenghiep : $('#hidNGHENGHIEP').val(),
			diachi : $('#hidDIACHI').val(),
			namsinh : $('#lblBIRTHDAY_YEAR').val(),
			khambenhid : _khambenhid,
			benhnhanid : $('#hidBENHNHANID').val(),
			chandoan : $('#txtTENCHANDOANICD').val(),
			ngaytiepnhan : _ngayhen,
			capnhat : '1',
			hosobenhanid : $('#hidHOSOBENHANID').val()
		};
		
		dlgPopup=DlgUtil.buildPopupUrl("dlgXuTri","divDlg","manager.jsp?func=../ngoaitru/NGT02K008_Thongtin_Lichkham",param,'Thông tin lịch hẹn',900,500);
		DlgUtil.open("dlgXuTri");
	}
	
	function tinhTruocTien(jsonObj){
		var _tong_tien_dv_tt = 0;
		var _data_ar 	= jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.05", _tiepnhanid,[]);
	    if(_data_ar != null){
    		var _row = _data_ar[0];
    		_TRAN_BHYT 		= _row.TRAN_BHYT;
    		_TYLE_BHYT_TT 	= _row.TYLE_BHYT;
    		_TONGTIENDV_BH 	= _row.TONGTIENDV_BH;
	    }
	    _tong_tien_dv_tt = parseFloat(_TONGTIENDV_BH);
	    var _obj_new, _so_luong, _don_gia;
		//_tong_tien_dv
	    //_GLB_CACH_TINH_TIEN
		for (var i=0; i<jsonObj.length; i++) {
			_so_luong = jsonObj[i].SO_LUONG;
			_don_gia = parseInt(jsonObj[i].DON_GIA.replaceAll(',',''));
			_obj_new = jsonObj[i].ID_DT_MOI;
			
			var objTinhTien = new Object();
	    	objTinhTien.DOITUONGBENHNHANID 	= _doituongbenhnhanid;
	    	objTinhTien.MUCHUONG 			= parseFloat(_TYLE_BHYT_TT);
	    	objTinhTien.GIATRANBH 			= parseFloat(jsonObj[i].GIATRANBHYT);
	    	objTinhTien.GIABHYT 			= parseFloat(_don_gia);
	    	objTinhTien.GIAND 				= parseFloat(_don_gia);
	    	objTinhTien.GIADV 				= parseFloat(_don_gia);
	    	objTinhTien.GIANN 				= _don_gia;
	    	objTinhTien.DOITUONGCHUYEN 		= _obj_new;
	    	objTinhTien.GIADVKTC 			= 0;
	    	objTinhTien.MANHOMBHYT 			= _nhom_mabhyt_id;
	    	objTinhTien.SOLUONG 			= _so_luong;
	    	objTinhTien.CANTRENDVKTC 		= 0;
	    	objTinhTien.THEDUTHOIGIAN 		= _tradu6thangluongcoban;
	    	objTinhTien.DUOCVANCHUYEN 		= _duoc_van_chuyen;
	    	objTinhTien.TYLETHUOCVATTU 		= _tyle_bhyt_tt_tvt;
	    	objTinhTien.NGAYHANTHE			= _ngay_bhyt_kt;
	    	objTinhTien.NGAYHANTHE_BD = _ngay_bhyt_bd; //L2PT-3717
	    	objTinhTien.NGAYDICHVU			= moment($("#txtTHOI_GIAN").val().trim()).format('DD/MM/YYYY');
	    	objTinhTien.TYLE_MIENGIAM 		= _tyle_miengiam;
	    	if (_configArr.HIS_HANTHE_BHYT != '0') {
	    		objTinhTien.NGAYGIAHANTHE = _configArr.HIS_HANTHE_BHYT;
	        }
	    	var r = vienphi.tinhtien_dichvu(objTinhTien);
		    row_Price 	= parseFloat(r.tong_cp);
		    row_Insr 	=  parseFloat(r.bh_tra);
		    row_End 	=  parseFloat(r.nd_tra);
		    _tong_tien_dv_tt = parseFloat(_tong_tien_dv_tt) + parseFloat(row_Price);		    
		    
//		    var parttt = ['NGT_CHECK_TU']; 
//			var _ngt_check_tu = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', parttt.join('$'));
			//tuyennx_edit_start_20181227 L2HOTRO-13220
			var lstDoituong = _configArr.DOITUONG_CANHBAO_TU;
			var lstTiepnhan = _configArr.LOAITN_CANHBAO_TU;
		    
		    // check BN khác BHYT, BN là NGT và BN nằm ngoài HĐ
		    if(_configArr.NGT_CHECK_TU == '1' && lstDoituong.includes(_doituongbenhnhanid)  && lstTiepnhan.includes(_loaitiepnhanid) && $.inArray( _option, ['02D010','02D015','02D017']) >= 0 && parseInt(_hopdongid) == 0){	
		    //tuyennx_edit_end_20181227 
		    	var checktien = parseFloat(_tongtien) + parseFloat(_tong_tien_dv_tt) - parseFloat(_danop);
		    	if( parseFloat(_tientu) <  parseFloat(checktien)){
		    		DlgUtil.showMsg('Tổng tiền thanh toán > tiền tạm ứng');
		    		if(_kieucanhbaotientamung == "1"){
		    			$('#btnSave').prop('disabled', true);
		    		}else{
		    			$('#btnSave').prop('disabled', false);
		    		}
		    	}
		    }
		    
		    if(parseFloat(_TRAN_BHYT) < _tong_tien_dv_tt){
		    	_GLB_CACH_TINH_TIEN = 0;
		    	break;
		    }else{
		    	_GLB_CACH_TINH_TIEN = 1;
		    }				    	
		}
	}
	
	function ktTongSoLuong(jsonObj, _key, _val){
		_tongso = 0;
		for (var i in jsonObj){
			if(jsonObj[i].THUOCVATTUID == _val)
				_tongso += parseFloat(jsonObj[i][_key]);
		}
		console.log("------------------_tongso: "+ _tongso);
		return _tongso;
	}
	//Tim kiem json
	function findObjectJson(jsonObj, _key, _valueComp){
		var _ret = false;
		if(jsonObj.length > 0){
			for (var i=0; i<jsonObj.length; i++) {
				if (jsonObj[i][_key] == _valueComp) {
					_ret = true;
					break;
			  	}else
			  		_ret = false;
			}
		}else
			_ret = false;
		return _ret;
	}
	
	function removeItemJson(jsonObj, _key, _valueComp){
		for (var i=0; i<jsonObj.length; i++) {
			if (jsonObj[i][_key] == _valueComp) {
				delete jsonObj[i][_key];
				break;
		  	}
		}
	}
	
	function bindEvent(){
		
		//L2PT-31691
		$(document).unbind('keydown').keydown(function(e) {
			if (e.keyCode == 114) {
				var focused = $(':focus');
					focused.blur();
					e.preventDefault();
				 $("#btnSave").trigger("click");
			} 
			if (e.keyCode == 115) {
				var focused = $(':focus');
					focused.blur();
					e.preventDefault();
				 $("#btnXuTri").trigger("click");
			} 
			if (e.keyCode == 27) {
				var focused = $(':focus');
					focused.blur();
					e.preventDefault();
				 $("#btnClose").trigger("click");
			} 
		});
		$("#cboMA_KHO").on('change', function (e) {
			if(parseInt(_loai_don) == 2 || parseInt(_loai_don) == 4)
				loadComboGrid(1);
			else{
				loadComboGrid(1);
				var e = jQuery.Event("keydown");
				e.which = 50; // # Some key code value
				$("#txtDS_THUOC").trigger(e);
			}
			//loadComboGrid(0);//Tim theo hoat chat thuoc
			//BVTM-1637
			if(_configArr.KETHUOC_HIENTHI_SLKD == '1'){
				var rowIds = $('#'+_gridDonThuoc).jqGrid('getDataIDs');
				for (var k = 0; k < rowIds.length; k++) {
					var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rowIds[k]);
					var sql_par = row.THUOCVATTUID + '$' +$('#cboMA_KHO').val();
					var sudung_ks  = jsonrpc.AjaxJson.ajaxCALL_SP_I('KETHUOC.GET.SLKD', sql_par);
					if(sudung_ks != '-1'){
						$('#'+_gridDonThuoc).jqGrid('setCell', rowIds[k], 'SLKHADUNG', sudung_ks);
						$('#'+_gridDonThuoc).jqGrid('getLocalRow', rowIds[k]).SLKHADUNG = sudung_ks;
					}
					if(_opts.option!='02D011' && ($('#cboMA_KHO').val() != '0') && $('#cboMA_KHO').val() != null){ 
						$('#'+_gridDonThuoc).jqGrid('setCell', rowIds[k], 'KHO_THUOCID', $('#cboMA_KHO').val());
						$('#'+_gridDonThuoc).jqGrid('getLocalRow', rowIds[k]).KHO_THUOCID = $('#cboMA_KHO').val();
					}
				}
			}
			$("#txtDS_THUOC").focus();
			
		});
		
		$('#txtMATENCHANDOANICD_KT').keydown(function (e) {
			if (e.keyCode == 9 && $('#txtMATENCHANDOANICD_KT').val() == '') {
				$("#chkCapPhieuHenKham").focus();
			}
		});
		
		$("#cboLOAITHUOC").on('change', function (e) {
			if(parseInt(_loai_don) == 2 || parseInt(_loai_don) == 4)
				loadComboGrid(1);
			else{
				loadComboGrid(1);
				var e = jQuery.Event("keydown");
				e.which = 50; // # Some key code value
				$("#txtDS_THUOC").trigger(e);
			}
			//loadComboGrid(0);//Tim theo hoat chat thuoc
			$("#txtDS_THUOC").focus();
		});
		
		$("#cboDONTHUOCVT").on('change', function (e) {
			_phieutraid = $('#cboDONTHUOCVT').val();
			if(parseInt(_loai_don) == 2 || parseInt(_loai_don) == 4)
				loadComboGrid(1);
			else{
				loadComboGrid(1);
				var e = jQuery.Event("keydown");
				e.which = 50; // # Some key code value
				$("#txtDS_THUOC").trigger(e);
			}
			//loadComboGrid(0);//Tim theo hoat chat thuoc
			
			$("#txtDS_THUOC").focus();
			if(_phieutraid == '0'){
				$("#btnAllPhieu").addClass("disabled");
			}else{
				$("#btnAllPhieu").removeClass("disabled");
			}			
			
			$('#cboMA_KHO').val($('#cboDONTHUOCVT'+ " option:selected").attr('extval0'));
			//tuyennx_add_start_20180919 L2HOTRO-4549
			$('#txtTHOI_GIAN').val($('#cboDONTHUOCVT'+ " option:selected").text().split('-')[1]);
			//tuyennx_add_end__20180919
		});
		
		$("#txtDS_THUOC").on('keypress', function (e) {
			_ten_tvt = $('#txtDS_THUOC').val();
			if(_option == '02D014' || _option == '02D016'){
				var phieutraid = $('#cboDONTHUOCVT').val();
				if(phieutraid == '0'){
					$("#cboDONTHUOCVT").focus();
					DlgUtil.showMsg('Hãy chọn đơn thuốc/Vật tư cần trả');
					return;
				}
			}
			
			if(_option == '02D010' || _option == '02D015'){
				
				var khothuocid = $('#cboMA_KHO').val();
				if(khothuocid == '0' && _chonkho_kedon == '1'){
					$("#cboMA_KHO").focus();
				     setTimeout(
				      function(){ 
				       DlgUtil.showMsg('Hãy chọn kho thuốc/vật tư');
				      }
				     , 500);
				}
			}
			//tuyennx_add_start_20201116_L2PT-29871
			if (e.which === 13 && $("#txtDS_THUOC").val()){
				if(_loaikedon == 1){
			        $("#txtSOLUONG_TONG").trigger("focus");
		        }else{
	        		if(_configArr.KETHUOC_ANNGAYKEDON == '1') //chu y upcode L2PT-29871
	        			$("#txtSANG").trigger("focus");
	        		else{ //L2PT-7616
	        			if(_configArr.KETHUOC_MACDINH_NGAYNTU != '0' && _loaitiepnhanid == '0')
	        				$("#txtSO_NGAY").val(_configArr.KETHUOC_MACDINH_NGAYNTU);
	        			$("#txtSO_NGAY").trigger("focus");
	        		}
		        }	
			}	
			//tuyennx_add_end_20201116_L2PT-29871
		});
		
		$("#txtTHUOCVT").on('keypress', function (e) {			
			if (e.which === 13 && $("#txtTHUOCVT").val()){
	        	$("#cboDUONG_DUNG").trigger("focus");
			}
		});
		
		$("#txtSONGAY_KE").on('change', function (e) {
			if(parseInt($(this).val()) > parseInt(_songaykemax)){
				$("#txtSONGAY_KE").focus();
				DlgUtil.showMsg('Số ngày kê thuốc không lớn hơn '+_songaykemax+' ngày');
				return;
			}
		});
		
		$("#chkCapPhieuHenKham").on('change', function (e) {
			if($('#chkCapPhieuHenKham').is(':checked')){
				_openformhenkham();
			}
		});

		$("#chkSMSLichHen").on('change', function(){
			if($('#chkSMSLichHen').is(':checked')){
				$(".sms-info-group").show();
			} else {
				$(".sms-info-group").hide();
			}
		});
		
		$("#btnEDITBP").on("click",function(e){
			var myVar={
				benhphu : $('#txtTENCHANDOANICD_KT').val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgBPKT","divDlg","manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu",myVar,"Chỉnh sửa bệnh kèm theo",600,420);
			DlgUtil.open("dlgBPKT");
		});	
		
		EventUtil.setEvent("chinhsua_benhphu",function(e){
			$('#txtTENCHANDOANICD_KT').val(e.benhphu);
			DlgUtil.close("dlgBPKT");
		});
		
		EventUtil.setEvent("pddt_presc_success",function(e){
			_maubenhpham_temp_id = e.id;
			var ret = '';
			var param = [];
			if(_loaitiepnhanid != 0){
				param = [e.id,_khambenhid,$('#txtTG_DUNG').val()];
				ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D075.EV005", param.join('$'));
			} else {
				ret = '';
			}
			
			if(typeof ret != 'undefined' && ret != ''){
				DlgUtil.showConfirm(ret + " đã được chỉ định trong ngày, có đồng ý load mẫu không có chỉ định trùng?",function(flag) {
					if(flag){
						_maubenhpham_temp_id = e.id;
						if(_maubenhpham_temp_id != ""){
							loadGridDonThuoc('TEMP', _maubenhpham_temp_id,'2');
						}
					} else {
						DlgUtil.close("dlgPhacDoMau");
						return;
					}
				});
			} else {
				_maubenhpham_temp_id = e.id;
				if(_maubenhpham_temp_id != ""){
					loadGridDonThuoc('TEMP', _maubenhpham_temp_id,'2');
				}
			}
			
			if(e.khoid != "" && e.khoid != '' && _maubenhpham_temp_id != ''){
				$('#cboMA_KHO').val(e.khoid);
				$("#cboMA_KHO").addClass("disabled");
				$("#cboMA_KHO").change();
			}
				
			DlgUtil.close("dlgPhacDoMau");
		});
		
		$("#txtSLTHANG").focusout(function(){
			$('#txtTG_HENKHAM').val($('#txtSLTHANG').val());
		});
		
		$("#cboPHIEU_CD").on('change', function (e) {
			var _sel = this.value;
			if(_sel != ""){
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.09", _sel,[]);
				if(data_ar != null && data_ar.length > 0){
					var _row = data_ar[0];
					if(_phieudtri_kedon == '1'){
						$("#txtTHOI_GIAN").val(_row.NGAYMAUBENHPHAM);
			    		$("#txtTG_DUNG").val(_row.NGAYMAUBENHPHAM);
					}					
					$("#txtMACHANDOANICD").val(_row.MACHANDOAN);
					_MACHANDOAN = _row.MACHANDOAN; //L2PT-23947
		    		$("#txtTENCHANDOANICD").val(_row.CHANDOAN);
		    		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOAN_KEMTHEO);
				}
				//tuyennx_add_start_20180919 L2HOTRO-11765
				$('#txtTHOI_GIAN').val($('#cboPHIEU_CD'+ " option:selected").text().split('-')[1].trim());
				$('#txtTG_DUNG').val($('#cboPHIEU_CD'+ " option:selected").text().split('-')[1].trim());
				//tuyennx_add_end_20180919 
			}else{
				layThongTinBenhNhan();
			}
		});
		
		//tuyennx_add_start_L2PT-23947
		$("#txtMACHANDOANICD").focusout(function(){
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : $("#txtMACHANDOANICD").val()
			});
			var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECKICD", _sql_par);
			if(ret == '0')
				$("#txtTENCHANDOANICD").val("");
		});
		//tuyennx_add_end_L2PT-23947
		
		//Trả cả đơn thuốc
		$("#btnAllPhieu").on("click",function(e){
			var _tongtien = 0, _bhtra = 0, _ndtra = 0;
			var sql_par=[];
			_objDrug = [];
			sql_par.push(
				{"name":"[0]","value":$('#cboDONTHUOCVT').val()},
				{"name":"[1]","value":_khambenhid},
				{"name":"[2]","value":_loainhommaubenhpham_id}
			 );			
			var vthuoc = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D010.TRADON", sql_par);
			_objDrug = JSON.parse(vthuoc);		
			jQuery("#grdDONTHUOC").jqGrid("clearGridData");
			for(var i=0;i<_objDrug.length;i++) {
				jQuery("#grdDONTHUOC").jqGrid('addRowData', i, _objDrug[i]);
				loadAll(i, _objDrug[i].ID_DT_MOI);
				_tongtien += parseFloat(_objDrug[i].THANH_TIEN);
				_bhtra += parseFloat(_objDrug[i].BH_TRA);
				_ndtra += parseFloat(_objDrug[i].ND_TRA);
            }
			
			$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { THANH_TIEN	: _tongtien.formatMoney(0) });
			$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { BH_TRA		: _bhtra.formatMoney(0) });
			$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { ND_TRA		: _ndtra.formatMoney(0) });
			
			$('#cboDONTHUOCVT').prop('disabled', true);
		});
		
		//Thêm thuốc vào Grid
		//Thêm thuốc vào Grid
		$("#btnAdd").on("click",function(e){
			//tuyennx_edit_start_20180919 L2HOTRO-13865
			var now = new Date();
			var ngayhen = addDays(now ,  parseInt($("#txtSO_NGAY").val()));
			var ngayhientai = ngayhen.getDay();

			if(_configArr.NGT_CANHBAOHENKHAM == '1'){
				if(ngayhientai == 0 || ngayhientai == 6){
					DlgUtil.showConfirm("Thuốc được kê có ngày tái khám là Thứ 7 hoặc Chủ nhật, bạn có muốn tiếp tục?", function (flag) {
						if(flag){
							addThuoc();
						}
					});
				}else {
					addThuoc();
				}
			}else {
				addThuoc();
			}
			//tuyennx_edit_end_20180919 L2HOTRO-13865
		});

		$("#"+_gridDonThuoc).bind("jqGridInlineAfterSaveRow", function (e, rowid, orgClickEvent) {
			var value = $("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'SO_LUONG');
			var oldValue = $("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'OLDVALUE');
			if(_loaitiepnhanid != 0){
				if(/^\d+$/.test(value)){
					var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
					tinhTruocTien(_jsonGridData);
					loadAll("","");
					$('#'+_gridDonThuoc).jqGrid('setCell',rowid,'OLDVALUE',value);
				} else {
					$('#'+_gridDonThuoc).jqGrid('setCell',rowid,'SO_LUONG',oldValue);
					DlgUtil.showMsg("Số lượng thuốc phải là số nguyên dương!");
					return;
				}
			} else {
				var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				tinhTruocTien(_jsonGridData);
				loadAll("","");
				$('#'+_gridDonThuoc).jqGrid('setCell',rowid,'OLDVALUE',value);
			}
			//tuyennx_add_start_20190507 L2PT-4556
			var sl_ke = $("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'SO_LUONG_KE');
			if(sl_ke!= null || sl_ke!= ''){
				var sl_ke_new = value*(sl_ke/oldValue);
				$('#'+_gridDonThuoc).jqGrid('setCell',rowid,'SO_LUONG_KE',sl_ke_new);
			}
			//tuyennx_add_end_20190507 L2PT-4556
			
			//tuyennx_add_start_20180806 yc L2PT-7456
//			var lieudung = $("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'SO_LUONG');
//		    if(lieudung == ''){
//		    	if(!Number.isInteger(parseFloat(_param.SOLAN_SD_KHANGSINH)))
//		    		return DlgUtil.showMsg("Liều dùng không được để tr!");
//		    }
		    //tuyennx_add_end_20180806 yc L2PT-7456
		    
			/*}
			else{
				$("#"+_gridDonThuoc).jqGrid('setCell',rowid,7,oldValue);
				DlgUtil.showMsg("Số lượng"+_lbl_text+" phải là số nguyên dương!");
			}*/
		});
		//jqGridLoadComplete
		$("#"+_gridDonThuoc).bind("jqGridLoadComplete", function (e, rowid, orgClickEvent) {
			//BVTM-1789
//			if(_configArr.KE_THUOC_GOPTHONGBAO == '0'){
//				if(_configArr.KE_THUOC_LOADDS_TVT == '1' && _dstvt_tmp.length > 0){//if(_configArr.KE_THUOC_LOADDS_TVT == '1' && _dichvucha_id != "" && _macdinh_hao_phi == '9'){
//					var msg = "";
//					var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
//					jQuery("#grdDONTHUOC").jqGrid("clearGridData");			
//					if(_dstvt_tmp.length > 0 && jsonGridData.length > 0){
//						for(var k = 0; k < jsonGridData.length; k++){
//							for(var i = 0; i < _dstvt_tmp.length; i++){
//								var dem = 0;
//								if(jsonGridData[k].THUOCVATTUID ==_dstvt_tmp[i].DSTHUOCVATTUID){
//									param = [_dstvt_tmp[i].DSTHUOCVATTUID, $('#cboMA_KHO').val(), _dstvt_tmp[i].SOLUONG];
//									var checkdt = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.042", param.join('$'));
//									if(checkdt == '-1'){
//										DlgUtil.showMsg("Thuốc/VT "+jsonGridData[k].TEN_THUOC+" không đủ để cấp");
//										dem = 1;
//									}
//									
//									if(checkdt == '-2'){
//										DlgUtil.showMsg("Thuốc/VT "+jsonGridData[k].TEN_THUOC+" không có trong kho/tủ trực");
//										dem = 1;
//									}
//									
//									if(dem == 0){
//										$("#"+_gridDonThuoc).jqGrid('addRowData', i, jsonGridData[k]);
//										var _sltemp_s = _dstvt_tmp[i].SOLUONG.split('.');
//										var _sltemp;
//										if(_sltemp_s[0] == ''){
//											_sltemp = '0.'+_sltemp_s[1];
//										}else{
//											_sltemp = _dstvt_tmp[i].SOLUONG;
//										}
//										$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'SO_LUONG', _sltemp);
//									}
//								}
//							}
//						}
//					}
//				}		
//			}			
//			
//			// Gop thong bao thuoc và set thay the thuoc vat tu
//			if(_configArr.KE_THUOC_GOPTHONGBAO != '0'){
//				if(_configArr.KE_THUOC_LOADDS_TVT == '1' && _dstvt_tmp.length > 0){//if(_configArr.KE_THUOC_LOADDS_TVT == '1' && _dichvucha_id != "" && _macdinh_hao_phi == '9'){
//					var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
//					jQuery("#grdDONTHUOC").jqGrid("clearGridData");			
//					if(_dstvt_tmp.length > 0 && jsonGridData.length > 0){
//						var _msg = "";
//						for(var k = 0; k < jsonGridData.length; k++){
//							for(var i = 0; i < _dstvt_tmp.length; i++){
//								var dem = 0;
//								if(jsonGridData[k].THUOCVATTUID ==_dstvt_tmp[i].DSTHUOCVATTUID){
//									//BVTM-4964								
//									if(_dstvt_tmp[i].TVTTHAYTHE != ""){
//										jsonGridData[k].THUOCVATTUID = _dstvt_tmp[i].TVTTHAYTHE;
//										jsonGridData[k].TEN_THUOC = _dstvt_tmp[i].TEN_TVT_MOI;
//										jsonGridData[k].SLKHADUNG = _dstvt_tmp[i].SLKD;
//										jsonGridData[k].DON_GIA = _dstvt_tmp[i].DONGIA;
//										jsonGridData[k].HOATCHAT = _dstvt_tmp[i].HOATCHAT;
//									}
//									param = [jsonGridData[k].THUOCVATTUID, $('#cboMA_KHO').val(), _dstvt_tmp[i].SOLUONG];
//									var checkdt = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.042", param.join('$'));
//									if(checkdt == '-1'){																		
//										_msg = _msg + jsonGridData[k].TEN_THUOC+" không đủ để cấp.@";
//										dem = 1;
//									}
//									
//									if(checkdt == '-2'){
//										_msg = _msg + jsonGridData[k].TEN_THUOC+" không có trong kho/tủ trực.@";
//										dem = 1;
//									}
//									
//									if(dem == 0){										
//										var _sltemp_s = _dstvt_tmp[i].SOLUONG.split('.');
//										var _sltemp;
//										if(_sltemp_s[0] == ''){
//											_sltemp = '0.'+_sltemp_s[1];
//										}else{
//											_sltemp = _dstvt_tmp[i].SOLUONG;
//										}
//										//$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'SO_LUONG', _sltemp);
//										jsonGridData[k].SO_LUONG = _sltemp;
//										
//										//BVTM-4964
//										if(_dstvt_tmp[i].TVTTHAYTHE != ""){
//											jsonGridData[k].THANH_TIEN = parseFloat(_sltemp) * parseFloat(_dstvt_tmp[i].DONGIA);
//										}
//										$("#"+_gridDonThuoc).jqGrid('addRowData', i, jsonGridData[k]);																														
//									}
//								}
//							}
//						}
//						if(_msg != ""){
//							//DlgUtil.showMsg(_msg);
//							var myVar={
//								thongbao : _msg
//							};
//							dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","dlgDuongDung","manager.jsp?func=../ngoaitru/NGT04K006_ThongBao",myVar,"Thông báo",600,420);
//							DlgUtil.open("divDlgDichVu");
//						}
//					}
//				}			
//			}

			var rowIds = $("#"+_gridDonThuoc).jqGrid('getDataIDs');
			if(rowIds != null && rowIds.length > 0){
				if(_first_load == 0){
					for (var i = 0; i < rowIds.length; i++) { 
					    _param =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIds[i]);
					    doAddItemGridToJson(_param, _objDrug, (i + 1));
					}
					_first_load = 1;
				}
				
				for (var i = 0; i < rowIds.length; i++) { 
					_param =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIds[i]);
					var html = '<button type="button" class="btn btn-link" style="white-space: normal;" id="'+'btnTHUOC_'+_param.THUOCVATTUID+'">'+splitDd(_param.HUONGDAN_SD)+'</span></button>';
					$("#"+_gridDonThuoc).jqGrid ('setCell', rowIds[i], 'DUONGDUNGE', html);
					
					$('#btnTHUOC_' + _param.THUOCVATTUID).on("click",function(e) {
						if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
							var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
							var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);
							
							//tuyennx_add_start_20200710 yc L2PT-24136
							if($('#'+_gridDonThuoc).find("input[id*='SO_LUONG']").length > 0){
								DlgUtil.showMsg('Dữ liệu đang sửa không thể thao tác!');
								return false;
							}
							//tuyennx_add_end_20200710 yc L2PT-24136
							
							//tuyennx_edit_start_20180810 yc L2DKHN-882
							var sql_par=[];
							sql_par.push({"name":"[0]","value": dataTmp.THUOCVATTUID});
							var chuy = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO('NGT.GETCHUYTHUOC', sql_par));
							var myVar={
									HDSD:dataTmp.HUONGDAN_SD,
									rowId:rowIdTmp,
									chuy:chuy[0].CHUY,
									lieudung: dataTmp.LIEUDUNG,
									chidinh:chuy[0].CHIDINH
							};
							//tuyennx_edit_end_20180810 yc L2DKHN-882
							dlgPopup=DlgUtil.buildPopupUrl("dlgDuongDung","dlgDuongDung","manager.jsp?func=../ngoaitru/NGT02K043_DUONGDUNG",myVar,"Đường dùng",700,350);
							DlgUtil.open("dlgDuongDung");
						}
					});
					
					var html = '<button type="button"  class="btn btn-sm btn-primary" style="height: 24px;" id="'+'btnTHUOCLT_'+_param.THUOCVATTUID+'">'+'<span class="glyphicon glyphicon-upload"></span></button>';
					$('#btnTHUOCLT_' +  _param.THUOCVATTUID).on("click",function(e) {
						if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
							var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
							var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);						
							var valueold = dataTmp.SO_LUONG;
							$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'SO_LUONG', Math.ceil(valueold));
						}
					});
					
					$("#"+_gridDonThuoc).jqGrid ('setCell', rowIds[i], 'LAMTRON', html);
					
//					var pars = ['SET_THANHTOAN_HAOPHI']; 
//					var data_tt = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
					if(_macdinh_hao_phi == '9' && _configArr.SET_THANHTOAN_HAOPHI == '1'){
						$("#"+_gridDonThuoc).jqGrid ('setCell', rowIds[i], 'LOAI_DT_MOI', 'Hao phí');
						$("#"+_gridDonThuoc).jqGrid ('setCell', rowIds[i], 'ID_DT_MOI', _macdinh_hao_phi);
					}
					//BVTM-5408
					if(_doituongbenhnhanid == '2' && $("#"+_gridDonThuoc).jqGrid ('getCell', rowIds[i], 'ID_DT_MOI') == '1'){
						$("#"+_gridDonThuoc).jqGrid ('setCell', rowIds[i], 'LOAI_DT_MOI', 'Viện phí');
						$("#"+_gridDonThuoc).jqGrid ('setCell', rowIds[i], 'ID_DT_MOI', '4');
					}
					
				}
			}
			var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			tinhTruocTien(_jsonGridData);
			loadAll("","");
			
		});
		
		$("#"+_gridDonThuoc).bind("jqGridBeforeSelectRow", function (e, rowid, orgClickEvent) {
			if(/^\d+$/.test($("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'SO_LUONG')))
				oldValue = $("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'SO_LUONG');
			GridUtil.unmarkAll(_gridDonThuoc);
            GridUtil.markRow(_gridDonThuoc,rowid);
		});
		
		$("#"+ _gridDonThuoc).bind("CustomActionCompleted", function(e, rowid){
			var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			_objDrug = jsonGridData;
			_list_thuoc_dake = "";
			if(_objDrug.length > 0){
				for(var i = 0; i < _objDrug.length; i ++){
					if(_list_thuoc_dake == "")
						_list_thuoc_dake = _objDrug[i].THUOCVATTUID;
					else
						_list_thuoc_dake = _list_thuoc_dake +","+ _objDrug[i].THUOCVATTUID;
				}
			}else
				$('#cboMA_KHO').prop('disabled', false);
			//Cap nhat lai thong tin tien chi tra cho phieu thuoc
			
			var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			tinhTruocTien(_jsonGridData);
			
			loadAll("","");
		});
		//Thuoc con su dung
		$("#btnTConSD").on("click",function(e){
			EventUtil.setEvent("rrugusing_cancel",function(e){
				DlgUtil.close("dlgCDDV");
			});
			var myVar={
					benhnhanId : _benhnhanid
				};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTCu","manager.jsp?func=../ngoaitru/NGT02K020_ThuocConSuDung",myVar,"Thuốc còn sử dụng",1200,550);
			DlgUtil.open("dlgCDDV");
		});
		
		//tuyennx_add_start_20170816 yc L2DKBD-195
		//Thuoc di ung
		$("#btnTDiUng").on("click",function(e){
			//L2PT-4416
			EventUtil.setEvent("diungthuoc_cancel",function(e){
				DlgUtil.close("divDlgDiungThuoc");
			});
//			var myVar={
//					benhnhanId : _benhnhanid,
//					khoaId : _khoaId,
//					phongId : _phongId
//				};
//			dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTCu","manager.jsp?func=../ngoaitru/NGT02K057_ThuocDiUng",myVar,"Dị ứng thuốc",1200,550);
//			DlgUtil.open("dlgCDDV");
			paramInput={
					benhnhanid : $("#hidBENHNHANID").val(),
					khambenhid : _khambenhid,
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					tiepnhanid : _tiepnhanid,
					mabenhan : $("#hidMAHOSOBENHAN").val(),
					tenbenhnhan : $("#lblPATIENTNAME").val(),
					mabenhnhan : $('#lblPATIENTCODE').val()
			};	
					
			dlgPopup=DlgUtil.buildPopupUrl("divDlgDiungThuoc","divDlg","manager.jsp?func=../noitru/NTU02D083_DiUngThuoc",paramInput,"Dị ứng thuốc",1000,550);
			DlgUtil.open("divDlgDiungThuoc");	
		});
		//tuyennx_add_end_20170816 yc L2DKBD-195
		
		$("#btnPdDt").on("click",function(e){
			var myVar={
					machandoan:$('#txtMACHANDOANICD').val(),
					loaidv:1,
					khoid: $('#cboMA_KHO').val()
			};
			DlgUtil.open("dlgPhacDoMau");
		});
		
		$("#btnSave").bindOnce("click",function(e){
			//tuyennx_edit_start_20200810_L2PT-25176
			if(_configArr.KETHUOC_CANHBAO_THUOCLE == '1'){
				var rowIds = $('#'+_gridDonThuoc).jqGrid('getDataIDs');
				var check_thuocle = 0;
				for (var k = 0; k < rowIds.length; k++) {
					var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rowIds[k]);
					if(row.SO_LUONG.includes('.') ){
						check_thuocle = 1;
						break;
			    	}
				}
				if(check_thuocle == '1'){
					DlgUtil.showConfirm("Đơn thuốc có chỉ định thuốc lẻ, bạn có đồng ý lưu?",function(flag) {
						if(flag){
							_kiemtra_mabacsi();
						}
					});
				}else{
					_kiemtra_mabacsi();
				}
			}else{
				_kiemtra_mabacsi();
			}		
			//tuyennx_edit_start_20200810 L2PT-25176
		},5000);
		
		//BVTM-1487
		$("#btnSaveCA").bindOnce("click",function(e){
			checkky_Ca = 1;
			//L2PT-124731 start
			//$("#btnSave").trigger("click");
			var _rptCode = 'NGT006_DONTHUOC_MUANGOAI_A5';
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
			let userCaConf = CaUtils.getCACachingConfig(_rptCode);
			var catype = '';
			var kieuky = '';
			var _title = '';
	        if (data_ar != null && data_ar.length > 0) {
	            var row = data_ar[0];
	            var loaiky = row.LOAIKY;
	            catype = row.CA_TYPE;
	            var loaiphieu = row.LOAIPHIEU;
	            _title = row.KIEUKY;
				kieuky = row.KIEUKY;
	        }
			if(catype == '3' || catype == '6' ) {
	            var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
	            let _paramInput = {params: null, smartca_method: 0};
	            EventUtil.setEvent("dlgCaLogin_confirm", function () {
	                DlgUtil.close("divCALOGIN");
	                let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
	                causer = _hisl2SmartCa.token.refresh_token;
	                capassword = _hisl2SmartCa.token.access_token;
	                _smartcauser = _hisl2SmartCa.user.uid;
	                $("#btnSave").trigger("click");
					 if(_configArr.DTDT_DAY_DONTHUOC == '2' || _configArr.DTDT_DAY_DONTHUOC == '3')
	                	_dayDonThuocOnline_new('2',maubenhphamId,_opts.option,_loaitiepnhanid,"", i_action,causer,capassword,catype);
	                
	            });

	            let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
	            if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
	                _paramInput.smartca_method = 1;
					if (_autoKyCA) {
								let
								_hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
								causer = _hisl2SmartCa.token.refresh_token;
								capassword = _hisl2SmartCa.token.access_token;
								smartcauser = _hisl2SmartCa.user.uid;
								$("#btnSave").trigger("click");
							} else {
								let
								_popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
								_popup.open("divCALOGIN");
								return;
							}
	            } else {
	                EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function(e) {
	                    if (e.data && e.data.token && e.data.token.access_token) {
	                        _paramInput.smartca_method = 1;
	                    }
	                    DlgUtil.close("dlgCA_SMARTCA_LOGIN");
	                    let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
	                    _popup.open("divCALOGIN");
	                    return;
	                });
	                DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {isSignPopup: true}, "Smart Ca Login", 500, 650);
	                DlgUtil.open("dlgCA_SMARTCA_LOGIN");
	                return;
	            }
	        } 
			else if(catype == '5'){
				
				$("#btnSave").trigger("click");
			}
			else if (userCaConf && _autoKyCA) {//L2PT-30069
				causer = userCaConf.USER_NAME,
				capassword = userCaConf.PASS_WORD,
				$("#btnSave").trigger("click");
	        }else {
	        	EventUtil.setEvent("dlgCaLogin_confirm", function(e) {
	    			causer = e.username;
	    			capassword = e.password;
	    			_smartcauser ="";
	    			DlgUtil.close("divCALOGIN");
	    			$("#btnSave").trigger("click");
					if(_configArr.DTDT_DAY_DONTHUOC == '2' || _configArr.DTDT_DAY_DONTHUOC == '3')
	    				_dayDonThuocOnline_new('2',maubenhphamId,_opts.option,_loaitiepnhanid,"", i_action,causer,capassword,catype);
	    		});
	    		EventUtil.setEvent("dlgCaLogin_close", function(e) {
	    			DlgUtil.close("divCALOGIN");
	    		});
	    		var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
	    		var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", "CA - LOGIN", 505, 268);
	    		popup.open("divCALOGIN");
	        }	
			//L2PT-124731 end
		},5000);
		//tuyennx_edit_start_20191209 L2PT-12390
		function _kiemtra_kham5p(){
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : _tiepnhanid
			});
			var ret = jsonrpc.AjaxJson.getOneValue("NGT.CHECKKHAM5P", _sql_par);
			if(_loaitiepnhanid == '1' && ret != '0' && _configArr.HIS_KETTHUCKHAM_KHICODONTHUOC == '1' ){
				if(_configArr.HIS_CANHBAO_KHAM5P == '2'){  //chặn  L2PT-13836
					DlgUtil.showMsg("BN có thời gian khám bệnh dưới " + _configArr.HIS_CANHBAO_KHAM5P_TL +" phút không thể thao tác!");
					return ;
				}
				DlgUtil.showConfirm("BN có thời gian khám bệnh dưới " + _configArr.HIS_CANHBAO_KHAM5P_TL +" phút bạn có muốn tiếp tục?",function(flag) {
					if(flag){
						_canhbao_traituyen_vp();//L2PT-6070
					}
				});
			}
			else{
				_canhbao_traituyen_vp();//L2PT-6070
			}
		}
		//tuyennx_edit_end_20191209 L2PT-12390
		
		//L2PT-6070
		function _canhbao_traituyen_vp(){
			if(_loaitiepnhanid == '1' && _configArr.KETHUOC_CANHBAO_TRAITUYEN  == '1'
				&& (BHYT_LOAIID == '4' || _doituongbenhnhanid == '2' )){
				DlgUtil.showConfirm("Bạn có muốn kê thuốc cho BN trái tuyến, viện phí?",function(flag) {
					if(flag){
						$('#btnSave').prop('disabled', true);
						doInsDonThuoc(i_action);
					}
				});
			}
			else{
				$('#btnSave').prop('disabled', true);
				doInsDonThuoc(i_action);
			}
		}
		//tuyennx_add_start L2PT-18390
		function _kiemtra_mabacsi(){
			if(_configArr.HIS_CANHBAO_MABACSI == '1' && _doituongbenhnhanid == "1"){
				var _sql_par = [];
				var _bsikham = $('#cboBACSIID').val();
				if( _bsikham == '-1' || _bsikham == undefined || _bsikham ==""){
					_bsikham = _user_id;
				}
				_sql_par.push({	"name" : "[0]",	value : _bsikham});
				var ret = jsonrpc.AjaxJson.getOneValue("NGT.MALOAIBS", _sql_par);
				if(ret == '0'){
					if(_configArr.NGT_CHAN_KHAMBENH == '1'){  //chặn  L2PT-13836
						DlgUtil.showMsg("User không phải loại bác sỹ hoặc chưa có mã bác sỹ!");
						return ;
					}
					DlgUtil.showConfirm("User đang sử dụng ko phải bác sỹ hoặc chưa có mã bác sỹ bạn có muốn tiếp tục?",function(flag) {
						if(flag){
							_kiemtra_kham5p();
						}
					});
				}else{
					_kiemtra_kham5p();
				}
			}else{
				_kiemtra_kham5p();
			}		
		}
		//tuyennx_add_end L2PT-18390
		
		$("#btnNhapMoi").bindOnce("click",function(e){
			$('#btnSave').prop('disabled', false);
			_khambenhid = -1;
			layThongTinBenhNhan();
			$('#lblPATIENTCODE').focus();
		},5000);
		
		$("#btnDTMau").on("click", function(e) {
			//tuyennx_add_start L2PT-8635
			sd_doncu_donmau = 1;
			//tuyennx_add_end L2PT-8635
			
			//L2PT-617
//			if(_configArr.KETHUOC_CHONKHO_DONMAU == '1' && ($('#cboMA_KHO').val() == '0' || $('#cboMA_KHO').val() == null)){
//				DlgUtil.showMsg('Chọn kho thuốc trước khi chọn đơn mẫu');
//				return;
//			}
			EventUtil.setEvent("temp_presc_success",function(e){
				_maubenhpham_temp_id = e.id;
				_dstvt_tmp = e.dsthuocvattuid;
				var ret = '';
				var param = [];
				if(_loaitiepnhanid != 0){
					param = [e.id,_khambenhid,$('#txtTG_DUNG').val()];
					ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.21", param.join('$'));
				} else {
					ret = '';
				}				
				
				if(_dichvucha_id && _configArr.KE_THUOC_CHECKTRUNG_DIKEM == '1'){
					ret = '';
				}				
				
				if(typeof ret != 'undefined' && ret != '' && (_configArr.CHAN_KE_TRUNG_THUOC==1 || _configArr.CHAN_KE_TRUNG_THUOC == 2)){ //L2PT-1865
					DlgUtil.showConfirm(ret + " đã được chỉ định trong ngày, có đồng ý load mẫu không có chỉ định trùng?",function(flag) {
						if(flag){
//							if(_maubenhpham_temp_id != ""){
//								loadGridDonThuoc('TEMP', _maubenhpham_temp_id);
//							}
							_maubenhpham_temp_id = e.id;
							if(_maubenhpham_temp_id != ""){
								param = [e.id,_khambenhid,$('#txtTG_DUNG').val()];
								ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.23", param.join('$'));
								r_dichvu_id_diff = ret;
								loadGridDonThuoc('TEMP', _maubenhpham_temp_id,'1');
							}
						} else {
							DlgUtil.close("dlgCDDV");
							return;
						}
					});
				} else {
					_maubenhpham_temp_id = e.id;
					if(_maubenhpham_temp_id != ""){
						loadGridDonThuoc('TEMP', _maubenhpham_temp_id);
					}
				}
				
				if(e.khoid != "" && e.khoid != '' && e.khoid != "0" && _maubenhpham_temp_id != ''){ //L2PT-617 khong set lại gia tri kho khi khoid = 0
					$('#cboMA_KHO').val(e.khoid);
					//tuyennx_edit_start L2PT-6436 check don thuoc mau khong chon kho
					//$("#cboMA_KHO").addClass("disabled");
					//tuyennx_edit_end
					$("#cboMA_KHO").change();
				}
				//tuyennx_add_start_20181012  L2HOTRO-11197
				if(_configArr.LOAD_KHO_ALL_KETHUOCMAU=='1'){
					//$('#cboMA_KHO').val('0');
					$("#cboMA_KHO").removeClass("disabled");
					$("#cboMA_KHO").change();
				}
				//tuyennx_add_end_20181012 

					
				DlgUtil.close("dlgCDDV");
			});
			var myVar={
				loainhom : _loainhommaubenhpham_id,
				//tuyennx_add_start_20180924  L2HOTRO-10386
				loadkhotheo: _loadkhotheo,
				option: _opts.option,
				//tuyennx_add_end_20180924  L2HOTRO-10386
				dichvuchaid: _dichvucha_id,//L2PT-34136
				loaitiepnhanid : _loaitiepnhanid,
				khothuocid : $("#cboMA_KHO").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTCu","manager.jsp?func=../ngoaitru/NGT02K018_DonThuocMau",myVar,"Đơn thuốc/vật tư mẫu",1200,550);
			DlgUtil.open("dlgCDDV");
		});
		
		$("#btnDTCu").on("click", function(e) {
			//tuyennx_add_start L2PT-8635
			sd_doncu_donmau = 1;
			//tuyennx_add_end L2PT-8635
			EventUtil.setEvent("old_presc_success",function(e){
				_maubenhpham_id = e.id;
				
				//tuyennx_add_start_20190515 L2PT-4826
				var sql_par=[];
				sql_par.push(//{"name":"[0]","value":_tyle_bhyt},
									{"name":"[0]","value":_loainhommaubenhpham_id},
									{"name":"[1]","value":_company_id},
									{"name":"[2]","value":_maubenhpham_id});
				var vtmp = jsonrpc.AjaxJson.ajaxExecuteQueryO('NTU02D010.04', sql_par);
				vtmp = JSON.parse(vtmp);
				if(vtmp.length > 0) {
					for(var k = 0; k <vtmp.length; k++){
						if(vtmp[k].CHOLANHDAODUYET == '1' && vtmp[k].CHUY != ''){
							DlgUtil.showMsg("Chú ý: Thuốc ["+ vtmp[k].TEN_THUOC +"] " + vtmp[k].CHUY);
						}
					}
				}
				//tuyennx_add_end_20190515 L2PT-4826
				
				var ret = '';
				var param = [];
				if(_loaitiepnhanid != 0 && _option != '02D011'){
					param = [_maubenhpham_id,_tiepnhanid];
					ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.24", param.join('$'));
				} else {
					ret = '';
				}
				
				if(typeof ret != 'undefined' && ret != ''){
					DlgUtil.showConfirm(ret + " đã tồn tại trong các đơn thuốc của bệnh nhân, có đồng ý load đơn không trùng hoạt chất?",function(flag) {
						if(flag){
							if(_maubenhpham_id != ""){
								param = [_maubenhpham_id,_tiepnhanid];
								ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.25", param.join('$'));
								r_dichvu_id_dtc_diff = ret;
								loadGridDonThuoc('DTCU', _maubenhpham_id,'1'); //L2PT-15996
							}
						} else {
							DlgUtil.close("dlgCDDV");
							return;
						}
					});
				} else {
					loadGridDonThuoc('DTCU', _maubenhpham_id); //L2PT-15996
				}

				DlgUtil.close("dlgCDDV");
			});
			var myVar={
				benhnhanId : _benhnhanid,
				khothuocId : $("#cboMA_KHO").val(),
				//loainhommbp : _option == '02D015'?'8':'7', // L2PT-9975
				loainhommbp : _loainhommaubenhpham_id, //L2PT-26834
				khoaid : _khoaId
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTCu","manager.jsp?func=../ngoaitru/NGT02K019_DonThuocCu",myVar,"Đơn thuốc/VT cũ",1200,550);
			DlgUtil.open("dlgCDDV");
		});
		
		// start manhnv L2HOTRO-11801
		$("#btnDTPhu").on("click", function(e) {
			EventUtil.setEvent("assignSevice_saveThuocPhu",function(e){
				var _dsthuocphu = e;	
				GridUtil.fetchGridData(_gridDonThuoc, _dsthuocphu);
				DlgUtil.close("dlgTHUOCPHU");
			});
			var myVar={
					_khambenhid : _khambenhid
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCPHU","divDTPhu","manager.jsp?func=../ngoaitru/NGT02K097_DS_ThuocPhu",myVar,"DANH SÁCH ĐƠN THUỐC PHỤ",1200,550);
			DlgUtil.open("dlgTHUOCPHU");
		});	
		// end manhnv L2HOTRO-11801
		
		$("#btnClose").on("click", function(e) {
			EventUtil.raiseEvent("assignDrug_cancel",{option:_opts.option,type:_luu, badaingay:_badaingay});
			if(_opts.openKBHB == 1){
				EventUtil.raiseEvent("exam_closedlg1");
			}
		});
		
		//SMS
		$("#SendSMSHK").on("click", function(e) {
			if($('#txtSDT_BENHNHAN').val() == ""){
				$('#txtSDT_BENHNHAN').focus();
				DlgUtil.showMsg('Bệnh nhân chưa có số điện thoại');
				return false;
			}
			
			var _ngayhk = $("#txtTHOI_GIAN").val().split(' ');
			const _ngayhen = new Date(moment(_ngayhk[0],"DD/MM/YYYY"));
			var _songayket = $("#hidNGAYSMS").val();
			_ngayhen.setDate(_ngayhen.getDate() + parseInt(_songayket));
			
			var _loidanbsi = _configArr.KE_THUOC_LOIDAN_BSI_MD + " " + _ngayhen.getDate() + "/" + (_ngayhen.getMonth()+1) + "/" + _ngayhen.getFullYear();			
			var myVar={
					sodienthoai : $('#txtSDT_BENHNHAN').val(),
					noidungtn   : _loidanbsi,
					songay    	: 1,
					khambenhid  : $('#hidKHAMBENHID').val(),
					loai 		: '2'
				};
				dlgPopup=DlgUtil.buildPopupUrl("dlgGuiSMS","divDlg","manager.jsp?func=../ngoaitru/GuiSMS_BenhNhan",myVar,"Gửi tin nhắn cho bệnh nhân",700,450);
				dlgPopup.open("dlgGuiSMS");
		});
		
		$("#btnXuTri").on("click", function(e) {
			EventUtil.raiseEvent("assignDrug_xutri",{khambenhid:_khambenhid,option:_opts.option});
		});
		$("#btnInDon").on("click", function(e) {
			indonthuoc(r_maubenhphamid.split(';'),true, r_maubenhphamid); //L2PT-16981
		});
		
		$("#btnSaveClose").bindOnce("click",function(e){
			checkclose = 1;
			$("#btnSave").trigger("click");
		},5000);
		$("#btnSaveTemp").on("click", function(e) {
			if($("#txtTEXT_TEMP").val().trim() != "")
				doInsDonThuoc("SAVE_TEMP");
			else
				return DlgUtil.showMsg("Bạn phải nhập tên đơn thuốc mẫu!");
		});
		$('#txtTHOI_GIAN').on('change', function (e) {
			var bstr = $('#txtTHOI_GIAN').val();			
			$('#txtTHOI_GIAN').val(stringToDateFormat(bstr));
			//tuyennx_add_start_20180919 L2HOTRO-10337
			if(_configArr.LOAD_TGDUNG_THEO_TGCD =='1')
				$('#txtTG_DUNG').val(stringToDateFormat(bstr));
			//tuyennx_add_end_20180919 	

		});
		$('#txtTG_DUNG').on('change', function (e) {
			var bstr = $('#txtTG_DUNG').val();			
			$('#txtTG_DUNG').val(stringToDateFormat(bstr));
		});
		
		$("#"+_gridDonThuoc).bind("jqGridAfterInsertRow", function (e, rowid, orgClickEvent) {
			//tuyennx_edit_start L2PT-1475
			var loaitn = _configArr.LOAI_THANHTOAN_HIENTHIMENU;
			if(_an_menu_phai_kedon != "1" && (loaitn == '-1' || loaitn.includes(_loaitiepnhanid))){
			//tuyennx_edit_end
				$(".jqgrow", '#'+_gridDonThuoc).contextMenu('contextMenu', {
					bindings: {
	                    'changeBHYT': function (t) {
	                    	loadAll($(t).attr("id"),'1');	
	                    },
	                    'changeVP': function (t) {
	                    	loadAll($(t).attr("id"),'4');	
	                    },
	                    'changeYC': function (t) {
	                    	loadAll($(t).attr("id"),'6'); 
	                    },
						'changeMP': function (t) {
							loadAll($(t).attr("id"),'15');
						},
	                    'changeHPK': function (t) {
	                    	loadAll($(t).attr("id"),'9');
	                    },
	                    'changeCorona': function (t) {
	                    	loadAll($(t).attr("id"),'17');
	                    },
	                    'changeBHYTCorona': function (t) {
	                    	loadAll($(t).attr("id"),'18');
	                    },
	                    'changeTyle': function (t) { // add manhnv 23/10/2018
	                    	var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rowid);
	                    	//tuyennx_add_start_20190108 L2PT-949
	                		var DS_ICD = $('#txtMACHANDOANICD').val();
	                		if($('#txtTENCHANDOANICD_KT').val() != ''){
	                			var array = $('#txtTENCHANDOANICD_KT').val().split(';');
	                			for(var i=0;i<array.length;i++){
	                				DS_ICD = DS_ICD+','+ array[i].split('-')[0];
	                			}
	                		}
	                		//tuyennx_edit_start_20180806
	                    	var myVar={
            					thuocvattuid : row.THUOCVATTUID,
            					dsicd : DS_ICD,
            					dieukienid : row.DIEUKIENID,
            					rowid	   : rowid,
            					loai : 0
            				};
            				dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCTYLE","divDlg","manager.jsp?func=../ngoaitru/NGT01T096_thuoctyle",myVar,"TỶ LỆ THUỐC CÓ ĐIỀU KIỆN",600,500, { closeButton: false });
            				DlgUtil.open("dlgTHUOCTYLE");
            				
            				EventUtil.setEvent("assignSevice_savetylethuoc",function(e){
            					$('#hidTYLEDK').val(e.msg);
            					$('#hidDIEUKIENID').val(e.dieukienid);
            					if(e.rowid > 0 ){
            						$('#'+_gridDonThuoc).jqGrid('setCell', e.rowid, 'TYLEDK', e.msg);
            						$('#'+_gridDonThuoc).jqGrid('setCell', e.rowid, 'DIEUKIENID', e.dieukienid);
            					}else{
            						_addthuocgrid(valid, _soluong_thuoc);
            					}
            					DlgUtil.close("dlgTHUOCTYLE");
            				});
	                    } // end manhnv 23/10/2018
	                },
	                onContextMenu: function (event, menu) {
	                    var rowId = $(event.target).parent("tr").attr("id");
	                    var grid = $('#'+_gridDonThuoc);
	                    grid.setSelection(rowId);
	                    GridUtil.unmarkAll(_gridDonThuoc);
	                    GridUtil.markRow(_gridDonThuoc,rowId);
	                    return true;
	                },
	            });
				
			}
		});

		var f6 = 117;
		//tuyennx_edit_start_20170721 chi hien thi thuoc thuong dung khi click vao ten thuoc 
		$("#txtDS_THUOC").unbind('keydown').keydown(function(e) {
			//var _jsonThuoc;
			pressF6(e);
		});
		$("#txtTENTHUOC").unbind('keydown').keydown(function(e) {
			//var _jsonThuoc;
			pressF6(e);
		});
		
		function pressF6(e){
			if (e.keyCode == f6) {
				EventUtil.setEvent("always_presc_success",function(e){
					if(e != null){
						//var _jsonThuoc = e.jsonThuoc;
						//alert(JSON.stringify(e.jsonThuoc));
						var _item = e.jsonThuoc;
						$("#txtDS_THUOC").val(_item.TEN_THUOC);
				        if(_srch_hoatchat == 1)
				        	$("#txtTENTHUOC").val(_item.HOATCHAT);
				        else
				        	$("#txtTENTHUOC").val(_item.TEN_THUOC);
				        $("#cboDUONG_DUNG").val(_item.DUONGDUNGID);
				        _tuongtacthuoc = ','+ _item.TUONGTACTHUOC+',';
				        _nhom_mabhyt_id = _item.NHOM_MABHYT_ID;
				        if(_loai_don =="2" || _loai_don =="4")
				        	$("#cboMA_KHO").val(_item.KHOID);
				        if(_option != '02D011')//Don mua ngoai
				        	$("#hdSOLUONGKHADUNG").val(_item.SLKHADUNG);
				        else
				        	$("#hdSOLUONGKHADUNG").val(100000);
				        $("#txtGHICHU").val("");
				        $("#hdHUONGDANTHUCHIEN").val("");
				        if(_loaikedon == 1){
					        $("#txtSOLUONG_TONG").trigger("focus");
				        }else{
				        	//tuyennx_edit_start_20200722 L2PT-25085
			        		if(_configArr.KETHUOC_ANNGAYKEDON == '1')
			        			$("#txtSANG").trigger("focus");
			        		else{ //L2PT-7616
			        			if(_configArr.KETHUOC_MACDINH_NGAYNTU != '0' && _loaitiepnhanid == '0')
			        				$("#txtSO_NGAY").val(_configArr.KETHUOC_MACDINH_NGAYNTU);
			        			$("#txtSO_NGAY").trigger("focus");
			        		}
			        		//tuyennx_edit_end_20200722 L2PT-25085
				        }	
				        _objDrugTemp = [];
				        _objDrug = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
						doAddDrugToJson(_item, _objDrug, 1);
						doAddDrugToJson(_item, _objDrugTemp, 1);
						DlgUtil.close("dlgCDDV");
					}
					else{
						$("#txtDS_THUOC").trigger("focus");
						DlgUtil.close("dlgCDDV");
					}
				});
				var myVar={
						option : _option,
						khoid : $("#cboMA_KHO").val()
					};
				dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTTD","manager.jsp?func=../noitru/NTU02D056_ThuocThuongDung",myVar,"Thuốc thường dùng",1200,550);
				DlgUtil.open("dlgCDDV");
			}
			//tuyennx_edit_end_20170721 
		}
		//tuyennx_add_start_20180605
		$("#"+_gridDonThuoc).bind("jqGridAfterLoadComplete", function (e, rowid, orgClickEvent) {
//			var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
//			_objDrug = [];
//			var _list_thuoc_err = "";
//			if(vtmp && jsonGridData){
//				for(var i = 0; i < jsonGridData.length; i ++){
//					for(var k = 0; k <vtmp.length; k++){
//						if(jsonGridData[i].THUOCVATTUID == vtmp[k].THUOCVATTUID && vtmp[k].KTTONKHO && vtmp[k].KTTONKHO > 0)
//							_objDrug.push(jsonGridData[i]);
//						if(jsonGridData[i].THUOCVATTUID == vtmp[k].THUOCVATTUID && vtmp[k].KTTONKHO && vtmp[k].KTTONKHO < 0)
//							_list_thuoc_err = _list_thuoc_err + jsonGridData[i].TEN_THUOC +";";
//					}
//				}
//			}
//			$("#"+_gridDonThuoc).jqGrid("clearGridData", true);
//			GridUtil.fetchGridData(_gridDonThuoc,_objDrug);
//			if(_list_thuoc_err)
//				DlgUtil.showMsg("Có thuốc hết hạn trong kho: " +_list_thuoc_err);
			var cms = $("#"+_gridDonThuoc).jqGrid('getGridParam','colModel'); 
			for(var j = 0; j < cms.length; j++){
				if(cms[j].name == "cb"){
					var ids=$("#"+_gridDonThuoc).getGridParam("reccount");
					for(var i = 1; i <= ids; i++){
						var _row = $("#"+_gridDonThuoc).jqGrid('getRowData', i);
						if(_row.SO_LUONG.includes(".")){
							$("#"+_gridDonThuoc).jqGrid('setSelection', i, false);
						}
					}
				}
			}
			
			//tuyennx_add_start_20190418 L1PT-464 
			 var data_ar = _configArr.HIS_BACSY_YCHUY_DV;
				if(data_ar != null){
					if(data_ar == '1'){
						var ids = $('#' +_gridDonThuoc).getDataIDs();
		        	 for(var i=0;i<ids.length;i++){
		        		  var id = ids[i];
		        		var rowDataCd = $('#' +_gridDonThuoc).jqGrid('getRowData', id);
		        		if(rowDataCd.YC_HOAN == '1'){
		        			$('#' +_gridDonThuoc).jqGrid('setRowData', id, "", {
								color : 'red'
							});
		        			$('#' +_gridDonThuoc).setCell(id,'TEN_THUOC','',{'text-decoration': 'line-through'});
		        			$('#' +_gridDonThuoc).setCell(id,'HOATCHAT','',{'text-decoration': 'line-through'});
		        		}
		        	 }
					}
				}
			//tuyennx_add_end_20190418 L1PT-464 
			//if(cm[rowid].name == "rn")
			 //  {
			 //      alert('111111111111111');
			 //  }
			/*var ids=$("#"+_gridDonThuoc).getGridParam("reccount");
				for(var i = 1; i <= ids; i++){
					var _row = $("#"+_gridDonThuoc).jqGrid('getRowData', i);
					if(_row.SO_LUONG.includes(".")){
						$("#"+_gridDonThuoc).jqGrid('setSelection', i, false);
					}
			}*/
		 });
		
		//tuyennx_add_end_20180605
		
		//START-- L2K74TW-605 -- hongdq
		$('#txtTGSEARCH').on(
				'change',
				function(e) {
					$('#calSearch').prop('title', $('#txtTGSEARCH').val());
					sql_par = RSUtil.buildParam("", [ _opts.khambenh_id, "4", $('#txtTGSEARCH').val()+ ' 00:00:00', $('#txtTGSEARCH').val()+ ' 23:59:59']);
					ComboUtil.getComboTag("cboPHIEU_CD",
							"COM.PHIEUDIEUTRI_1", sql_par,'', 'sql', '');
		});
		
		$("#btnCancelSearch").click(function(){
			sql_par = RSUtil.buildParam("", [ _opts.khambenh_id, "4" ]);
			ComboUtil.getComboTag("cboPHIEU_CD",
				"COM.PHIEUDIEUTRI", sql_par,
				_opts.phieudieutriid == null ? ''
						:_opts.phieudieutriid, {
					value : '-1',
					text : 'Chưa có phiếu điều trị'
				}, 'sql', '', function() {

				});
		});
		//END-- L2K74TW-605 -- hongdq
		
		// SMS
		$("#btnCNSDT").click(function(){
			ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CN_SDT_BN", $('#hidHOSOBENHANID').val() + "$" + $('#txtSDT_BENHNHAN').val());
			DlgUtil.showMsg(ret);
		});
	}
	
	function _addthuocgrid(valid, _soluong_thuoc){
		if(valid){
			//tuyennx_add_start_20201116_L2PT-29871
			if(_check_canhbao == 1 && !$("#txtGHICHUCANHBAO").val() && _configArr.KETHUOC_GHICHU_CANHBAO == 1){
				$("#txtGHICHUCANHBAO").focus();
				return DlgUtil.showMsg("Nhập ghi chú dùng thuốc đã có cảnh báo");
			}
			//tuyennx_add_end_20201116_L2PT-29871
			
			//L2PT-4274
			if(_configArr.KETHUOC_CHECK_SOLUONG == '1' && _loaikedon == '0'){
				if((parseFloat($("#txtSANG").val()) + parseFloat($("#txtTRUA").val())
						+ parseFloat($("#txtCHIEU").val()) + parseFloat($("#txtTOI").val()))*
						 parseFloat($("#txtSO_NGAY").val()) != parseFloat($("#txtSOLUONG_CHITIET").val())){
					$("#txtSOLUONG_CHITIET").focus();
					DlgUtil.showMsg("Tổng số lượng sáng, trưa , chiều, tối x Số ngày không bằng với số lượng tổng của thuốc!");
				}
			}

			var _objTVTTuongtac = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			if(_objTVTTuongtac.length > 0){
				for(var i = 0; i < _objTVTTuongtac.length; i ++){
					if(_dsthuocvattuid != ""){
						_dsthuocvattuid = _dsthuocvattuid +","+ _objTVTTuongtac[i].THUOCVATTUID;
					}else{
						_dsthuocvattuid = _objTVTTuongtac[i].THUOCVATTUID;
					}						
				}
			}
			
			if(_dsthuocvattuid != "" && _configArr.KETHUOC_TUONGTAC_THUOC != "1"){ //L2PT-32754
				var param = ['',_objDrugTemp[0].THUOCVATTUID, _dsthuocvattuid];
				var _msgTuongtac = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.TUONGTAC.TVT", param.join('$'));
				if(_msgTuongtac != undefined && _msgTuongtac != ""){
					DlgUtil.showMsg(_msgTuongtac+"");
				}					
			}	
			
			//BVTM-3263
			if(_configArr.KETHUOC_TUONGTAC_ATC == '1'){
				var _jsonthuoc = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				if(_jsonthuoc.length > 0){
					for(var i = 0; i <_jsonthuoc.length; i++){
						var object = jsonrpc.AjaxJson.ajaxCALL_SP_O("KETHUOC.TUONGTAC", _jsonthuoc[i].THUOCVATTUID+"$"+_objDrugTemp[0].THUOCVATTUID,[]);
						if(object && object.length > 0){
							if(object[0].TUONGTAC_ATC == '1'){
								//var ghichu = '<font color="' + object[0].MA_MAU_ATC +'">';
								DlgUtil.showMsg('<font color=' + object[0].MA_MAU_ATC +'">' +
										object[0].GHICHU_ATC+ '</font>' + "");
								if(object[0].LOAI == '4')
									return ;
							}
						}						
					}
				}
			}
			
			//tuyennx_add_start_L2PT-34660
			if(_configArr.KETHUOC_TUONGTAC_MIMS == "1" && _dsthuocvattuid != ""){
				
				//Chua co bang nen fix tam du lieu
				var dsthuoc =  _dsthuocvattuid +","+ _objDrugTemp[0].THUOCVATTUID
				var xmlTuongtac = "<Request>"
					+"<Interaction>"
					+"<Prescribing>";
				var dt_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("MIMS.TUONGTAC",dsthuoc);
				if(dt_ar != null && dt_ar.length > 0){
					for(var i = 0; i< dt_ar.length; i++){
						xmlTuongtac = xmlTuongtac +"<"+ dt_ar[i].MIMS_TYPE+" reference=\"{"+ dt_ar[i].MIMS_GUID+"}\"/>";
					}
				}
				xmlTuongtac = xmlTuongtac +"</Prescribing>"
				+"<References/>"
				+"</Interaction>"
				+"</Request>";
				if(dt_ar != null && dt_ar.length > 0){
					var resultTT = ajaxSvc.ThuocMIMSWS.htmlresult(xmlTuongtac); 	
					if(resultTT.includes("ERR_")){
						DlgUtil.showMsg("Lỗi kiểm tra tương tác thuốc MIMS ");
					}
					else if(!resultTT.includes('No results found.')){
						var randomnumber = Math.floor((Math.random() * 100) + 1);
						var win = window.open('', "_blank", 'PopUp', randomnumber, 'scrollbars=1,menubar=0,resizable=1,width=850,height=500');
						win.document.write(resultTT);
					}
				}
			}
			//tuyennx_add_end_L2PT-34660
			
			
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DIEUKIENID", $("#hidDIEUKIENID").val()); // manhnh 05/10/2018 ADD
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "TYLEDK", $("#hidTYLEDK").val()); // manhnh 05/10/2018 ADD
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "THUOCSAO", $("#hidTHUOCSAO").val());
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "KHO_THUOCID", $("#hidKHOTHUOCTHEOTHUOC").val()); //L2PT-7738
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "TENKHO", $("#hidTENKHOTHUOC").val()); 
			//tuyennx_edit_start_20190507 L2PT-4556  L2PT-8767
			if(_donvitinhid_qd && _configArr.KETHUOC_TINH_DVQUYDOI == '1'){
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "SO_LUONG", (_soluong_thuoc/_tyle_qd).toFixed(3)); //L2PT-16883
				
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "SO_LUONG_KE", _soluong_thuoc);
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DONVI_TINH_QD", _ten_dvt_qd);
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DON_GIA_QD", (parseFloat(_objDrugTemp[0].DON_GIA.replaceAll(',',''))/_tyle_qd).toFixed(3));
//				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DONVI_TINH", _ten_dvt_qd);
//				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DON_GIA", (parseFloat(_objDrugTemp[0].DON_GIA.replaceAll(',',''))*_tyle_qd).formatMoney(0));
			}
			else
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "SO_LUONG", _soluong_thuoc);
			
			if((_sudung_dvqd_kethuoc == "1"  && $("#hidMABYT").val() == "40.17") || (_sudung_dvqd_kethuoc == "2"  && mahoatchat == "40.17")){//L2PT-5948
				var sql_par=[];
				sql_par.push({"name":"[0]","value": $("#cboDVQUYDOI").val()});
				var quydoi_oxy = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO('NGT.QUYDOI.OXY', sql_par));
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "SO_LUONG_KE", (parseFloat(_objDrugTemp[0].DON_GIA.replaceAll(',',''))*_soluong_thuoc/quydoi_oxy[0].DON_GIA).toFixed(3));
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DONVI_TINH_QD", quydoi_oxy[0].TEN_DVT);
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DON_GIA_QD", quydoi_oxy[0].DON_GIA);
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "TEN_QD", quydoi_oxy[0].TEN_QD);
			}
			//tuyennx_edit_end_20190507 L2PT-4556
			var _huongdan_sd = "";
			if(_loaikedon == 1)
				//tuyennx_edit_start_20190507 L2PT-6691
				if(_option == '02D017' && _configArr.SONGAYBANGSOTHANG ==1 && $('#txtSLTHANG').val() != '' )
					_huongdan_sd = $('#txtSLTHANG').val()+ "@"+ $("#cboDUONG_DUNG option:selected").text() +"@"+$("#txtGHICHU").val()+"@"+_soluong_thuoc+"@@@@";
				else 
					_huongdan_sd = "1@"+ $("#cboDUONG_DUNG option:selected").text() +"@"+$("#txtGHICHU").val()+"@"+_soluong_thuoc+"@@@@";
				//tuyennx_edit_start_20190507 L2PT-6691
			else
				_huongdan_sd = $("#hdHUONGDANTHUCHIEN").val().replace("_param_huongdan", $("#txtGHICHU").val());
			
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "HUONGDAN_SD", _huongdan_sd);
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DUONG_DUNG", $("#cboDUONG_DUNG option:selected").text());
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DUONGDUNGID", $("#cboDUONG_DUNG").val());				
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "LOAITVTID", $("#hidLOAITVTID").val());
			//tuyennx_add_start_20180806 yc L2DKBD-1189
			if((_option == '02D017' || _option == '02D018' )&& soluong_kele==""){
				soluong_kele = _soluong_thuoc;
			}
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "SL_KELE", soluong_kele);
			soluong_kele ="";
			//tuyennx_add_end_20180806
			
			//tuyennx_add_start_20180806 yc L2PT-5687 L2PT-28228
			
			var sql_par = _tiepnhanid + '$' + _objDrugTemp[0].THUOCVATTUID + '$' +$('#cboPHIEU_CD').val()+ '$' +$('#txtTHOI_GIAN').val();
			var sudung_ks  = jsonrpc.AjaxJson.ajaxCALL_SP_I('GET.SL.SDKS.DT', sql_par);
			if(sudung_ks != '-1')
				updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "SOLAN_SD_KHANGSINH", parseInt(sudung_ks) +1);
			//tuyennx_add_end_20180806
			
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "SLKHADUNG", $("#hdSOLUONGKHADUNG").val()); //L2PT-617		
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "CHUY", _chuy); //L2PT-885
			if(type_tc == 1){
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "SOLO", _solo);			
			var sql_par = $("#hidBENHNHANID").val() + '$' + _objDrugTemp[0].THUOCVATTUID;
			var muitiem  = jsonrpc.AjaxJson.ajaxCALL_SP_I('GET.MUI.TIEM', sql_par);
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "MUITIEM", "Mũi "+muitiem);
			
			}
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "HANSUDUNG", _hsd == "-"?"":_hsd);//L2PT-3791
			//tuyennx_edit_start 	L2PT-5527 mo comment
			var r_lieudung = "";
			if(_sudung_lieudung == '1'){
				r_lieudung = $("#txtLIEUDUNG").val();
				if(r_lieudung == "" && $.inArray(_option, ['02D010']) >= 0){
					$("#txtLIEUDUNG").focus();
					DlgUtil.showMsg("Hãy nhập liều dùng");
					return;
				}
			}else{					
				if(_option == '02D017' ){
					r_lieudung = $("#hidLIEUDUNGBD").val();
				}
				var _format = _configArr.FORMAT_LIEUDUNG_4210;
			    if(_format == '1' && _option != '02D017'){
			    	r_lieudung = $("#hidLIEUDUNGBD").val();
			    }else{
			    	r_lieudung = $("#txtGHICHU").val();
			    }
			}
			//tuyennx_edit_end 	L2PT-5527 mo comment
			
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "LIEUDUNG", r_lieudung);
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DVQD", $("#cboDVQUYDOI").val());
			
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "GHICHUCANHBAO", $("#txtGHICHUCANHBAO").val()); //L2PT-29871
			
			if(ktTongSoLuong(_objDrug, "SO_LUONG", _objDrugTemp[0].THUOCVATTUID) > parseFloat($("#hdSOLUONGKHADUNG").val())){
				if(_loaikedon == 1)
					$("#txtSOLUONG_TONG").trigger("focus");
				else
					$("#txtSOLUONG_CHITIET").trigger("focus");
				return DlgUtil.showMsg("Tổng số lượng kê"+_lbl_text+" lớn hơn số lượng tồn kho khả dụng!");
			}
			jQuery("#grdDONTHUOC").jqGrid("clearGridData");
			
			for(var i=1;i<=_objDrug.length;i++) {
				jQuery("#grdDONTHUOC").jqGrid('addRowData', i, _objDrug[i-1]);
				$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'SO_LUONG', '',{'color':'red', 'font-weight':'bold'}); // BVTM-3352
				$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'CHUY', '',{'color':'red', 'font-weight':'bold'}); // L2PT-7114
				
				if(Math.ceil(_objDrug[i-1].SO_LUONG)-_objDrug[i-1].SO_LUONG > 0){
					$("#"+_gridDonThuoc).jqGrid('setSelection', i, false);
				}
				
				//tuyennx_add_start_20190418 L2PT-3791
				if(_configArr.KETHUOC_TOMAU_HSD == '1'){
					var ids = $('#' +_gridDonThuoc).getDataIDs();
					var date = new Date($('#txtTHOI_GIAN').val().substr(3,2)+ '/' + $('#txtTHOI_GIAN').val().substr(0,2) +'/' + $('#txtTHOI_GIAN').val().substr(6,4)) ;
					date.setMonth(date.getMonth() + 3); 
		       		var rowDataCd = $('#' +_gridDonThuoc).jqGrid('getRowData', i);
		       		_hsd = rowDataCd.HANSUDUNG;
		       		if(_hsd && _hsd != '-' && _hsd != ""){
		    			var date1 = new Date(_hsd.split(';')[0].substr(3,2)+ '/' + _hsd.split(';')[0].substr(0,2) +'/' + _hsd.split(';')[0].substr(6,4)) ;
		    			if(date1 < date){
		    				$('#' +_gridDonThuoc).jqGrid('setRowData', i, "", {
								color : 'red'
							});
		    			}
			       	 }
				}
				 //tuyennx_add_end_20190418 L2PT-3791
				
				var html = '<button type="button" class="btn btn-link" style="white-space: normal;" id="'+'btnTHUOC_'+_objDrug[i-1].THUOCVATTUID+'">'+splitDd(_objDrug[i-1].HUONGDAN_SD)+'</button>';
				$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'DUONGDUNGE', html);
				$('#btnTHUOC_' + _objDrug[i-1].THUOCVATTUID).on("click",function(e) {
					if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
						var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
						var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);
						
						//tuyennx_add_start_20200710 yc L2PT-24136
						if($('#'+_gridDonThuoc).find("input[id*='SO_LUONG']").length > 0){
							DlgUtil.showMsg('Dữ liệu đang sửa không thể thao tác!');
							return false;
						}
						//tuyennx_add_end_20200710 yc L2PT-24136
						
						//tuyennx_edit_start_20180810 yc L2DKHN-882
						var sql_par=[];
						sql_par.push({"name":"[0]","value": dataTmp.THUOCVATTUID});
						var chuy = JSON.parse(jsonrpc.AjaxJson.ajaxExecuteQueryO('NGT.GETCHUYTHUOC', sql_par));
						var myVar={
								HDSD:dataTmp.HUONGDAN_SD,
								rowId:rowIdTmp,
								chuy:chuy[0].CHUY,
								lieudung: dataTmp.LIEUDUNG,
								chidinh:chuy[0].CHIDINH
						};
						//tuyennx_edit_end_20180810 yc L2DKHN-882
						dlgPopup=DlgUtil.buildPopupUrl("dlgDuongDung","dlgDuongDung","manager.jsp?func=../ngoaitru/NGT02K043_DUONGDUNG",myVar,"Đường dùng",700,350);
						DlgUtil.open("dlgDuongDung");
					}
				});
				
				// SMS
				var html = '<button type="button" class="btn btn-link" style="white-space: normal;" id="'+'btnSMS_'+_objDrug[i-1].THUOCVATTUID+'">SMS</button>';
				$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'SMS', html);
				$('#btnSMS_' + _objDrug[i-1].THUOCVATTUID).on("click",function(e) {
					if($('#txtSDT_BENHNHAN').val() == ""){
						$('#txtSDT_BENHNHAN').focus();
						DlgUtil.showMsg('Bệnh nhân chưa có số điện thoại');
						return false;
					}
					
					if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
						var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
						var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);
						var _cachdung = dataTmp.HUONGDAN_SD.split('@');
						var myVar={
							sodienthoai : $('#txtSDT_BENHNHAN').val(),
							noidungtn   : dataTmp.TEN_THUOC + "("+_cachdung[2]+")",
							songay    	: _cachdung[0],
							khambenhid  : $('#hidKHAMBENHID').val(),
							loai 		: '1'
						};
						dlgPopup=DlgUtil.buildPopupUrl("dlgGuiSMS","divDlg","manager.jsp?func=../ngoaitru/GuiSMS_BenhNhan",myVar,"Gửi tin nhắn cho bệnh nhân",700,450);
						dlgPopup.open("dlgGuiSMS");
					}
				});			
				
				//tuyennx_add_start_20180810 
				var html = '<button type="button" class="btn btn-sm btn-primary" style="height: 24px;" id="'+'btnTHUOCLT_'+_objDrug[i-1].THUOCVATTUID+'">'+'<span class="glyphicon glyphicon-upload"></span></button>';
				$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'LAMTRON', html);
				$('#btnTHUOCLT_' + _objDrug[i-1].THUOCVATTUID).on("click",function(e) {
					if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
						var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
						var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);						
						var valueold = dataTmp.SO_LUONG;
						$("#"+_gridDonThuoc).jqGrid ('setCell', rowIdTmp, 'SO_LUONG', Math.ceil(valueold));
					}
				});
				//tuyennx_add_end_20180810 
            }
			if(_GLB_CACH_TINH_TIEN == 1)
				tinhTruocTien(_objDrug);
			cleanForm();
			$('#txtDS_THUOC').trigger("focus");
			if(_ke_tunhieu_kho != '1'){
				$('#cboMA_KHO').prop('disabled', true);
			}
			$('#cboDONTHUOCVT').prop('disabled', true);
			loadAll("","");
			
			// đẩy ma hoat chat vao array temp
			_objTmpThuoc.push({
				"THUOCVATTUID" : _objDrugTemp[0].THUOCVATTUID,
				"MAHOATCHAT" : _objDrugTemp[0].MAHOATCHAT,
				"KETRUNGHOATCHAT" : _objDrugTemp[0].KETRUNGHOATCHAT
			});
			
			//tuyennx_add_start_20190926 L2PT-8938
			if(parseFloat($("#txtSO_NGAY").val()) > _songay_max)
				_songay_max = parseFloat($("#txtSO_NGAY").val());
			//tuyennx_add_end_20190926 L2PT-8938
		}
		$('#txtGHICHU').val('');
		$('#txtSearchCD').val('');
		
		$('#txtSLSOLAN').val('');
		$('#txtSOLANSONGAY').val('');
		$('#txtLIEUDUNG').val('');
		$("#hidLOAITVTID").val('');
		$("#hidTHUOCSAO").val('');		
		$("#hidTYLEDK").val(''); // manhnv 05/10/2018 add
		$('#hidDIEUKIENID').val(""); // manhnv 05/10/2018 add
		//tuyennx_add_start_20190507 L2PT-4556 L2PT-8767
		 _donvitinhid_qd 		= "";
	     _tyle_qd 		= "";
	     _ten_dvt_qd ="";
		//tuyennx_add_end_20190507 L2PT-4556
	     _HDSD 		= ""; //L2PT-34642
	     _lieuluong 		= ""; //L2PT-7588
	     
	     //L2PT-29871
	     $('#txtGHICHUCANHBAO').val('');
	     _check_canhbao = 0;
	}
	
	/*function changeObject(rowId,loaiDt){
		if($('#'+_gridDonThuoc).find("input[id*='SO_LUONG']").length > 0){
    		DlgUtil.showMsg('Tồn tại trường số lượng đang sửa');
			return false;
    	}
		
		var rowData = $("#grdDSCD").jqGrid('getRowData',rowId); 
		var objDataChange = vienphi.tinh_phi_dv(_doituongbenhnhanid, parseFloat(_tyle_bhyt),parseFloat(rowData.DICHVU_BHYT_DINHMUC),parseFloat(rowData.GIABHYT),parseFloat(rowData.GIANHANDAN),parseFloat(rowData.GIADICHVU),'0',loaiDt);
		if(objDataChange.bh_tra != -1 && objDataChange.bh_tra != -1 && objDataChange.nd_tra != -1){
			var _oldBHYTDataRow = rowData.BHYT_TRA;
			var _oldTTDataRow =  rowData.THANH_TIEN;
			
			var _newBHYTDataRow = parseFloat(objDataChange.bh_tra)*parseFloat(rowData.SOLUONG);
			var _newTTDataRow = (parseFloat(objDataChange.nd_tra)+parseFloat(objDataChange.bh_tra))*parseFloat(rowData.SOLUONG);
			
			var _payBHYTChange = _newBHYTDataRow - parseFloat(_oldBHYTDataRow);
			var _payTTChange = _newTTDataRow - parseFloat(_oldTTDataRow) - parseFloat(_oldBHYTDataRow);
			loadPay(_payTTChange, _payBHYTChange);
//			$('#grdDSCD').jqGrid('setCell',rowId,6,parseFloat(objDataChange.tong_cp) + parseFloat(typeof objDataChange.nd_tra_chenh == "undefined" ? '0':objDataChange.nd_tra_chenh));
//			$('#grdDSCD').jqGrid('setCell',rowId,7,parseFloat(objDataChange.bh_tra)*parseFloat(rowData.SOLUONG));
//			$('#grdDSCD').jqGrid('setCell',rowId,8,parseFloat(objDataChange.nd_tra)*parseFloat(rowData.SOLUONG));
//			$('#grdDSCD').jqGrid('setCell',rowId,15,objDataChange.loai_dt);
//			$('#grdDSCD').jqGrid('setCell',rowId,20,typeof objDataChange.nd_tra_chenh == "undefined" ? '':objDataChange.nd_tra_chenh);
//			$('#grdDSCD').jqGrid('setCell',rowId,19,typeof objDataChange.loai_dt_moi == "undefined" ? '':objDataChange.loai_dt_moi);
//			$('#grdDSCD').jqGrid('setCell',rowId,22,typeof objDataChange.ten_loai_tt_moi == "undefined" ? '':objDataChange.ten_loai_tt_moi);
			$('#grdDSCD').jqGrid('setCell',rowId,7,parseFloat(objDataChange.tong_cp) + parseFloat(typeof objDataChange.nd_tra_chenh == "undefined" ? '0':objDataChange.nd_tra_chenh));
			$('#grdDSCD').jqGrid('setCell',rowId,8,parseFloat(objDataChange.bh_tra)*parseFloat(rowData.SOLUONG));
			$('#grdDSCD').jqGrid('setCell',rowId,9,parseFloat(objDataChange.nd_tra)*parseFloat(rowData.SOLUONG));
			$('#grdDSCD').jqGrid('setCell',rowId,16,objDataChange.loai_dt);
			$('#grdDSCD').jqGrid('setCell',rowId,21,typeof objDataChange.nd_tra_chenh == "undefined" ? '':objDataChange.nd_tra_chenh);
			$('#grdDSCD').jqGrid('setCell',rowId,20,typeof objDataChange.loai_dt_moi == "undefined" ? '':objDataChange.loai_dt_moi);
			$('#grdDSCD').jqGrid('setCell',rowId,23,typeof objDataChange.ten_loai_tt_moi == "undefined" ? '':objDataChange.ten_loai_tt_moi);
		} else {
			DlgUtil.showMsg('Không thể chuyển loại thanh toán cho đối tượng bệnh nhân này');
		}
	}*/
	
	function loadAll(_rowId, _loadDTMoi){
		var rowIds = $("#"+_gridDonThuoc).jqGrid('getDataIDs');
		//tuyennx_add_start L2PT-7416
		if(_loaitiepnhanid == "1" && canhbaoke_max == 1 && rowIds.length > sl_max){
			DlgUtil.showMsg('Số lượng thuốc trong đơn vượt quá ' + sl_max);
		}
		//tuyennx_add_end L2PT-7416
		var _obj_new = "";
		var _totalPrice = 0, _totalIns = 0, _totalEnd = 0;
		var row_Price = 0, row_Insr = 0, row_End = 0;
		var data_ar;
		if(rowIds != null && rowIds.length > 0){
			var _param, _number;
			var _don_gia, _tong_tra;
			for (var i = 0; i < rowIds.length; i++) { 
			    _param =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIds[i]);
			    if(_rowId == rowIds[i]){
			    	_obj_new = _loadDTMoi;
			    }else{
			    	_obj_new = _param.ID_DT_MOI;
			    }
			    _number = parseFloat(_param.SO_LUONG) || 0;
			    //alert("_number :"+ _number +" --_obj_new: "+_obj_new);
			    
			   //tuyennx_add_start_20180806 yc L2PT-5687 L2PT-8635
			    if(_param.SOLAN_SD_KHANGSINH.trim() != ''){
			    	if(!Number.isInteger(parseFloat(_param.SOLAN_SD_KHANGSINH)))
			    		return DlgUtil.showMsg("Số ngày sử dụng kháng sinh phải là số nguyên dương!");
			    }
			    //L2PT-28228
			    var sql_par = _tiepnhanid + '$' + _param.THUOCVATTUID + '$' +$('#cboPHIEU_CD').val()+ '$' +$('#txtTHOI_GIAN').val();
				var sudung_ks  = jsonrpc.AjaxJson.ajaxCALL_SP_I('GET.SL.SDKS.DT', sql_par);
				if(_param.SOLAN_SD_KHANGSINH.trim() != '' && sudung_ks == '-1'
					&& _configArr.KETHUOC_STT_KHANGSINH_CHECK == '0')
					 DlgUtil.showMsg("Thuốc chưa đánh dấu ngày sử dụng hoạc chưa có mã hoạt chất!");
				else{
					if(sd_doncu_donmau == 1 && sudung_ks!= -1)
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'SOLAN_SD_KHANGSINH', parseInt(sudung_ks) +1);
				}
				$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'STT_KE', i +1);//L2PT-10076
			   //tuyennx_add_end_20180806 yc L2PT-5687 L2PT-8635
			    
			    if(_number > 0 || ( _configArr.KETHUOC_SOLUONG_0 == '1' && _number == 0)){ //L2PT-617
			    	
			    	_don_gia = _param.DON_GIA.replaceAll(',','');
			    	
			    	var objTinhTien = new Object();
			    	objTinhTien.DOITUONGBENHNHANID 	= _doituongbenhnhanid;
			    	objTinhTien.MUCHUONG 			= parseFloat(_tyle_bhyt);			    	
			    	objTinhTien.GIATRANBH 			= parseFloat(_param.GIATRANBHYT);
			    	objTinhTien.GIABHYT 			= parseFloat(_don_gia);
			    	objTinhTien.GIAND 				= parseFloat(_don_gia);
			    	objTinhTien.GIADV 				= parseFloat(_don_gia);
			    	objTinhTien.GIANN 				= parseFloat(_don_gia);
			    	//objTinhTien.DOITUONGCHUYEN 		= _doituongbenhnhanid == 2 ? 0:_obj_new;
			    	objTinhTien.DOITUONGCHUYEN 		= _obj_new;
			    	
			    	//L2PT-3826
		    		if(_loaitiepnhanid == '3' &&  _configArr.KETHUOC_DT_TRAITUYEN=='1'
		        		&&  BHYT_LOAIID == '4'){ 
		    			objTinhTien.DOITUONGCHUYEN = 4
		        	}
			    	//L2PT-3550
			    	if(_obj_new == 17)
			    		objTinhTien.DOITUONGCHUYEN = 4
		    		if(_obj_new == 18)
			    		objTinhTien.DOITUONGCHUYEN = 1
			    		
			    	objTinhTien.GIADVKTC 			= 0;
			    	//tuyennx_edit_start 20190613 L2PT-6068
			    	if(i == rowIds.length -1)
			    		objTinhTien.MANHOMBHYT 			= _nhom_mabhyt_id;
			    	else
			    		objTinhTien.MANHOMBHYT 			= _param.NHOM_MABHYT_ID;
			    	//tuyennx_edit_end 20190613 L2PT-6068
			    	objTinhTien.SOLUONG 			= _number;
			    	objTinhTien.CANTRENDVKTC 		= 0;
			    	objTinhTien.THEDUTHOIGIAN 		= _tradu6thangluongcoban;
			    	objTinhTien.DUOCVANCHUYEN 		= _duoc_van_chuyen;
			    	objTinhTien.TYLETHUOCVATTU 		= _tyle_bhyt_tt_tvt;
			    	objTinhTien.NGAYHANTHE			= _ngay_bhyt_kt;
			    	objTinhTien.NGAYHANTHE_BD = _ngay_bhyt_bd; //L2PT-3717
			    	objTinhTien.NGAYDICHVU			= moment($("#txtTHOI_GIAN").val().trim(),'DD/MM/YYYY');
			    	objTinhTien.TYLE_MIENGIAM 		= _tyle_miengiam;
			    	
			    	if (_configArr.HIS_HANTHE_BHYT != '0') {
			    		objTinhTien.NGAYGIAHANTHE = _configArr.HIS_HANTHE_BHYT;
			        }
			    	
			    	console.log("------------------------------objTinhTien :"+ JSON.stringify(objTinhTien));
			    	var r = vienphi.tinhtien_dichvu(objTinhTien);
				    if(parseFloat(r.tong_cp) == - 1){
						if(_loadDTMoi != "")
							DlgUtil.showMsg("Chuyển đối tượng thanh toán không hợp lệ!");
					}else{
						//console.log("------------------------------r.tong_cp:"+ r.tong_cp);
						_loai_doituong_moi = _loadDTMoi;
						row_Price 	= parseFloat(r.tong_cp);
					    row_Insr 	=  parseFloat(r.bh_tra);
					    row_End 	=  parseFloat(r.nd_tra);
					    
			    		if(_GLB_CACH_TINH_TIEN == 1){
			    			row_Insr = (parseFloat(_don_gia)*_number*_TYLE_BHYT_TT)/100;
			    			if(row_Price > 0)
			    				row_End = parseFloat(row_Price) - parseFloat(row_Insr);
			    			else
			    				row_End = 0;
			    		}			    			
					    
					    _totalPrice = _totalPrice + row_Price;	
					    _totalIns = _totalIns + row_Insr;
					    _totalEnd = _totalEnd + row_End;
					    
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],8,row_Price.formatMoney(0));
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],9,row_Insr.formatMoney(0));
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],10,row_End.formatMoney(0));
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],11,_ten_doituong_benhnhan);
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],12,r.ten_loai_tt_moi);
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],13,_doituongbenhnhanid);
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],14,_obj_new);
					    $("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'THANH_TIEN',row_Price.formatMoney(0));
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'BH_TRA',row_Insr.formatMoney(0));
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ND_TRA',row_End.formatMoney(0));
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_CU',_ten_doituong_benhnhan);
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_MOI',r.ten_loai_tt_moi);
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ID_DT_CU',_doituongbenhnhanid);
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ID_DT_MOI',r.loai_dt);
						//$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ID_DT_MOI',_obj_new);
						
						//L2PT-3550
				    	if(_obj_new == 17){
				    		$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_MOI','Corona');
				    		$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ID_DT_MOI',_obj_new);
				    	}	
			    		if(_obj_new == 18){
			    			$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_MOI','BHYT+Corona');
				    		$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ID_DT_MOI',_obj_new);
			    		}
						
						/*//tuyennx_add_start 20180815 HISL2TK-825 L2PT-5682 L2PT-7348
						if(_loaitiepnhanid == 0 && (_sudungthuoc12sao == '1' || KT_SUDUNG_KHANGSINH == '1') && _param.DVKBID>0){
					        	var dt_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU.UDGETTHUOCSAO",_param.DVKBID);
					        	if(dt_ar != undefined && dt_ar.length>0){
					        		$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'THUOCSAO',JSON.stringify(dt_ar[0])); //L2PT-25783
					        	}
						}*/
						//tuyennx_add_end 20180815
						
						//cap nhat lai doi tuong moi cho tung thuoc
					}
			    }else{
//			    	$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],8, 0);
//					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],9, 0);
//					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],10, 0);
//					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],11, _ten_doituong_benhnhan);
//					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],12, "");
			    	$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'THANH_TIEN', 0);
					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'BH_TRA', 0);
					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ND_TRA', 0);
					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_CU', _ten_doituong_benhnhan);
					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_MOI', "");
			    	return DlgUtil.showMsg("Số lượng phải là số nguyên dương!");
			    }
			}
		}else{
			_totalStart = 0;
			_totalIns =  0;
			_totalEnd =  0;
			oldValue = "";
		}
		
		var _tt_sothang = _configArr.THANHTIEN_DONGY_SOTHANG;
	    if(_tt_sothang == '1' && _option == '02D017'){
	    	_totalPrice = _totalPrice * parseFloat($('#txtSLTHANG').val()); 
	    }
		
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { THANH_TIEN	: _totalPrice.formatMoney(0) });
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { BH_TRA		: _totalIns.formatMoney(0) });
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { ND_TRA		: _totalEnd.formatMoney(0) });
		//Cap nhat lai tien don thuoc khi sua so luong
		$('#lblMUCHUONG_BHYT').val(_tyle_bhyt +"%");
		$('#lblMA_BHYT').val(_ma_bhyt);
		$('#lblDT_THANHTOAN').val(_ten_doituong_benhnhan);
	}
	
	function doLoadPrescription(){//Load don thuoc
//		if(_sudung_lieudung == '1'){
//			if(an_dongia_thanhtien == '1')
//				_gridDonThuocHeader="THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,300,0,f,l;Hoạt chất,HOATCHAT,100,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,t,r;SL,SO_LUONG,40,0,e,c;SLTK,SLKHADUNG,40,0,e,c;Thành tiền,THANH_TIEN,80,0,t,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,60,0,f,l;Loại TT mới,LOAI_DT_MOI,70,0,f,l;Chú ý,CHUY,100,0,f,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,150,0,f,c,ES;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c; ,ACTION,50,udb,f,l; ,ACTION,30,d,f,l;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l"; // manhnh 05/10/2018 upd //OldValue,OLDVALUE,0,0,f,t,0
//			else
//				_gridDonThuocHeader="THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,200,0,f,l;Hoạt chất,HOATCHAT,100,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,f,r;SL,SO_LUONG,40,0,e,c;SLTK,SLKHADUNG,40,0,e,c;Thành tiền,THANH_TIEN,80,0,f,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,60,0,f,l;Loại TT mới,LOAI_DT_MOI,70,0,f,l;Chú ý,CHUY,100,0,f,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,150,0,f,c,ES;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c; ,ACTION,50,udb,f,l; ,ACTION,30,d,f,l;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l"; // manhnh 05/10/2018 upd //OldValue,OLDVALUE,0,0,f,t,0
//		}else{
//			if(an_dongia_thanhtien == '1')
//				_gridDonThuocHeader="THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,300,0,f,l;Hoạt chất,HOATCHAT,100,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,t,r;SL,SO_LUONG,40,0,e,c;SLTK,SLKHADUNG,40,0,e,c;Thành tiền,THANH_TIEN,80,0,t,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,60,0,f,l;Loại TT mới,LOAI_DT_MOI,70,0,f,l;Chú ý,CHUY,100,0,f,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,150,0,f,c,ES;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c; ,ACTION,50,udb,f,l; ,ACTION,30,d,f,l;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l"; // manhnh 05/10/2018 upd //OldValue,OLDVALUE,0,0,f,t,0
//			else
//				_gridDonThuocHeader="THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,200,0,f,l;Hoạt chất,HOATCHAT,100,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,f,r;SL,SO_LUONG,40,0,e,c;SLTK,SLKHADUNG,40,0,e,c;Thành tiền,THANH_TIEN,80,0,f,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,60,0,f,l;Loại TT mới,LOAI_DT_MOI,70,0,f,l;Chú ý,CHUY,100,0,f,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,150,0,f,c,ES;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c; ,ACTION,50,udb,f,l; ,ACTION,30,d,f,l;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l"; // manhnh 05/10/2018 upd //OldValue,OLDVALUE,0,0,f,t,0
//		}
		//L2PT-2098
		_gridDonThuocHeader="THUOCVATTUID,THUOCVATTUID,0,0,t,l;STT_KE,STT_KE,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+ _configArr.KETHUOC_HEADER_GRID_MN;//L2PT-10076
		//tuyennx_add_start_20190610 L2PT-5922
		if(_option == '02D014' || _option == '02D018' || _option == '02D016'){
			_gridDonThuocHeader="THUOCVATTUID,THUOCVATTUID,0,0,t,l;STT_KE,STT_KE,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,200,0,f,l;Hoạt chất,HOATCHAT,100,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,f,r;SL,SO_LUONG,40,0,f,c;Thành tiền,THANH_TIEN,80,0,f,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,60,0,f,l;Loại TT mới,LOAI_DT_MOI,70,0,f,l;Chú ý,CHUY,100,0,f,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,150,0,f,c,ES;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c; ,ACTION,50,udb,f,l; ,ACTION,30,d,f,l;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l"; // manhnh 05/10/2018 upd //OldValue,OLDVALUE,0,0,f,t,0 L2PT-10076
		}
		//tuyennx_add_start_20190610
		
		
		var opt_ext={footerrow: true, rowNum: 200,rowList: [200]};
		if(type_tc == 1){
			_gridDonThuocHeader = _gridDonThuocHeader + ";Số lô,SOLO,100,0,f,l;Mũi tiêm,MUITIEM,100,0,f,l;Hạn sử dụng,HANSUDUNG,0,0,t,l"
		}
		
		// SMS
		$('#SendSMSHK').hide();
		if(_configArr.KE_THUOC_GUI_SMS == '1' && $.inArray(_loaitiepnhanid, [_configArr.LOAIBN_GUI_SMS]) >= 0){ // 0: noi; 1: ngoai; 3: dieutri ngt
			_gridDonThuocHeader = "SMS,SMS,50,0,f,l,ES;"+_gridDonThuocHeader;
			$('#SendSMSHK').show();
		}
		
		GridUtil.init(_gridDonThuoc,"1278","200",_gridCaption,false, _gridDonThuocHeader,false,opt_ext);
//		if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TACH_PHIEUTHUOC_KE_SOLUONGLE')==1){
//			GridUtil.init(_gridDonThuoc,"1278","200",_gridCaption,true, _gridDonThuocHeader,false,opt_ext);
//		}else{
//			GridUtil.init(_gridDonThuoc,"1278","200",_gridCaption,false, _gridDonThuocHeader,false,opt_ext);
//		}
		
		if(_configArr.TACH_PHIEUTHUOC_KE_SOLUONGLE==1)
			jQuery("#grdDONTHUOC").jqGrid('showCol', "LAMTRON");
		else
			jQuery("#grdDONTHUOC").jqGrid('hideCol', "LAMTRON");
		
		//tuyennx_add_start 20180928 L2PT-617
		if(_configArr.KETHUOC_HIENTHI_SLKD == '1')
			jQuery("#grdDONTHUOC").jqGrid('showCol', "SLKHADUNG");
		else
			jQuery("#grdDONTHUOC").jqGrid('hideCol', "SLKHADUNG");
		//tuyennx_add_end 20180928 L2PT-617
		//tuyennx_add_start 20180928 L2PT-885
//		if(_configArr.KETHUOC_HIENTHI_CHUY == '1'){
//			jQuery("#grdDONTHUOC").jqGrid('showCol', "CHUY");
//			jQuery("#grdDONTHUOC").jqGrid('hideCol', "LOAI_DT_CU");
//			jQuery("#grdDONTHUOC").jqGrid('hideCol', "LOAI_DT_MOI");
//		}
//		else{
//			jQuery("#grdDONTHUOC").jqGrid('hideCol', "CHUY");
//			jQuery("#grdDONTHUOC").jqGrid('showCol', "LOAI_DT_CU");
//			jQuery("#grdDONTHUOC").jqGrid('showCol', "LOAI_DT_MOI");
//		}	
		//tuyennx_add_end 20180928 L2PT-885
		
		$("#"+_gridDonThuoc)[0].toggleToolbar();
		//$("#"+_gridDonThuoc).jqGrid('navGrid',{edit:false,add:false,del:true});
		//Xoa 1 row = keypress
		
		$("#"+_gridDonThuoc).bind("CustomAction", function(e,act,rid){ 
			if(act == 'del'){
				var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rid);
				//tuyennx_add_start_20180727 HISL2CORE-1158
				_dsthuocvattuid = _dsthuocvattuid.replaceAll(row.THUOCVATTUID, "-1");
				//tuyennx_add_end_20180727
				$("#"+_gridDonThuoc).jqGrid('delRowData',rid);
				var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				_objDrug = jsonGridData;
				_list_thuoc_dake = "";
				if(_objDrug.length > 0){
					for(var i = 0; i < _objDrug.length; i ++){
						if(_list_thuoc_dake == "")
							_list_thuoc_dake = _objDrug[i].THUOCVATTUID;
						else
							_list_thuoc_dake = _list_thuoc_dake +","+ _objDrug[i].THUOCVATTUID;
					}
				}
				
				var ts = ['KIEU_LOAD_GRID_KHI_XOATHUOC']; 
		    	var gt_ts = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', ts.join('$'));				
		    	if(gt_ts == '1' && _dichvucha_id == "" && _macdinh_hao_phi != '9') {
		    		$("#"+_gridDonThuoc).jqGrid("clearGridData", true);
					GridUtil.fetchGridData(_gridDonThuoc,jsonGridData);
					loadAll("","");
		    	}
				
				var tmp = [];
				for(var k = 0; k <_objTmpThuoc.length; k++){
					if(_objTmpThuoc[k].THUOCVATTUID != row.THUOCVATTUID){
						tmp.push({
							"THUOCVATTUID" : _objTmpThuoc[k].THUOCVATTUID,
							"MAHOATCHAT" : _objTmpThuoc[k].MAHOATCHAT,
							"KETRUNGHOATCHAT" : _objTmpThuoc[k].KETRUNGHOATCHAT
						});
						
						//tmp.push(_objTmpThuoc[k]);
					}
				}
				_objTmpThuoc = tmp;
			}else{
				//console.log("CustomAction act="+act+" rid="+rid);
				var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				var rowIds = $("#"+_gridDonThuoc).jqGrid('getDataIDs');
				var _rowUp;
				var _ridDown;
				if(act == "up"){
					_rowUp = rid;
					_rowDown = rid - 1;
					if(rid == 0)
						return false;
				}else{
					if(_jsonGridData.length > 2)
						_rowUp = rid;
					else
						_rowUp = parseInt(rid)+ 1;
					if(_jsonGridData.length > 2)
						_rowDown = parseInt(rid) + 1;
					else
						_rowDown = parseInt(rid);
					if(rid == rowIds.length)
						return false;
				}
				doSwap(_rowUp, _rowDown);
				var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				//jQuery("#"+_gridDonThuoc).jqGrid("clearGridData");
				GridUtil.fetchGridData(_gridDonThuoc, jsonGridData);
			}
			
			return false;
		});
		$("#"+_gridDonThuoc).jqGrid('bindKeys', {"onKeyDelete":function( rowid ) {
			$("#"+_gridDonThuoc).jqGrid('delRowData',rowid);
			var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			_objDrug = jsonGridData;
			_list_thuoc_dake = "";
			if(_objDrug.length > 0){
				for(var i = 0; i < _objDrug.length; i ++){
					if(_list_thuoc_dake == "")
						_list_thuoc_dake = _objDrug[i].THUOCVATTUID;
					else
						_list_thuoc_dake = _list_thuoc_dake +","+ _objDrug[i].THUOCVATTUID;
				}
			}
			$("#"+_gridDonThuoc).jqGrid("clearGridData", true);
			GridUtil.fetchGridData(_gridDonThuoc,jsonGridData);
			loadAll("","");
		}});
	}
	
	function doSwap(_rowUp, _rowDown){
		var $row1 = $("#"+_rowUp), $row2 = $("#"+_rowDown),
		$next1 = $row1.next(".jqgrow"), $prev1 = $row1.prev(".jqgrow"),
        $next2, $prev2, doOneMove = false, movedOnce = false;

	    if ($row2.is($next1) || $prev1.is($row2)) {
	        doOneMove = true;
	    }
	
	    if ($prev1.length > 0 && !$prev1.is($row2)) {
	        $row2.detach().insertAfter($prev1);
	        movedOnce = true;
	    } else if ($next1.length > 0 && !$next1.is($row2)) {
	        $row2.detach().insertBefore($next1);
	        movedOnce = true;
	    }
	    
	    if (doOneMove && movedOnce) {
	        return;
	    }
	
	    $next2 = $row2.next();
	    $prev2 = $row2.prev();
	    if ($prev2.length > 0 && !$prev2.is($row1)) {
	        $row1.detach().insertAfter($prev2);
	    } else if ($next2.length > 0 && !$next2.is($row1)) {
	        $row1.detach().insertBefore($next2);
	    }
	}
	
	function doAddDrugToJson(_itemDrug, _obj, _opt){
		var _stt;
		if( _opt == 0){
			_stt = 1;
			_obj = []; 
		}
		else{
			_stt = parseInt(_obj.length + 1);
		}
		
		if(_option == '02D014' || _option == '02D016' || _option == '02D018'){
			_macdinh_hao_phi = _itemDrug.ID_DT_MOI;
		}
		
		var r_khothuocid = $("#cboMA_KHO").val();
		if(r_khothuocid = '0' && _option != '02D011' && _chonkho_kedon != '1'){
			r_khothuocid = $("#hidKHOTHUOCTHEOTHUOC").val();
		}
			
		_obj.push({
			"THUOCVATTUID"	: _itemDrug.THUOCVATTUID,
			"STT"			: _stt,
	        "TEN_THUOC" 	: _itemDrug.TEN_THUOC,
	        "HOATCHAT"		: _itemDrug.HOATCHAT,
	        "SO_LUONG"  	: $("#txtSOLUONG_CHITIET").val(),
	        "DONVI_TINH"	: _itemDrug.TEN_DVT,
	        "KHOANMUCID"	: _itemDrug.KHOANMUCID,
	        "DON_GIA"		: _itemDrug.GIA_BAN,
	        "THANH_TIEN"	: "",
	        "BH_TRA"		: "",
	        "ND_TRA"		: "",
	        "DUONG_DUNG"	: _itemDrug.DUONG_DUNG,
	        "DUONGDUNGID" 	: _itemDrug.DUONGDUNGID,
	        "MA_THUOC"		: _itemDrug.MA_THUOC,
	        "NHOM_MABHYT_ID": _itemDrug.NHOM_MABHYT_ID,
	        "GIATRANBHYT"	: _itemDrug.GIATRANBHYT,
	        "ID_DT_CU"		: _doituongbenhnhanid,
	        "ID_DT_MOI"		: _macdinh_hao_phi, 
	        "HUONGDAN_SD"   : $("#hdHUONGDANTHUCHIEN").val(),
	        "TYLEBHYT_TVT"	: _itemDrug.TYLEBHYT_TVT,
	        "DICHVUKHAMBENHID" : _itemDrug.DICHVUKHAMBENHID,
	        "MAHOATCHAT" 	: _itemDrug.MAHOATCHAT,
	        "OLDVALUE"		: $("#txtSOLUONG_CHITIET").val(),
	        "KETRUNGHOATCHAT" : _itemDrug.KETRUNGHOATCHAT,
	        "KHO_THUOCID" : r_khothuocid //L2PT-7738
	    });
		$("#hdHUONGDANTHUCHIEN").val("");
	}
	
	function doAddItemGridToJson(_item, _obj, i){		
		_obj.push({
			"THUOCVATTUID"	: _item.THUOCVATTUID,
			"STT"			: i,
	        "TEN_THUOC" 	: _item.TEN_THUOC,
	        "HOATCHAT"		: _item.HOATCHAT,
	        "SO_LUONG"  	: _item.SO_LUONG,
	        "DONVI_TINH"	: _item.DONVI_TINH,
	        "KHOANMUCID"	: _item.KHOANMUCID,
	        "DON_GIA"		: _item.DON_GIA,
	        "THANH_TIEN"	: _item.THANH_TIEN,
	        "BH_TRA"		: _item.BH_TRA,
	        "ND_TRA"		: _item.ND_TRA,
	        "DUONG_DUNG"	: _item.DUONG_DUNG,
	        "DUONGDUNGID" 	: _item.DUONGDUNGID,
	        "MA_THUOC"		: _item.MA_THUOC,
	        "NHOM_MABHYT_ID": _item.NHOM_MABHYT_ID,
	        "GIATRANBHYT"	: _item.GIATRANBHYT,
	        "ID_DT_CU"		: _doituongbenhnhanid,
	        "ID_DT_MOI"		: _macdinh_hao_phi,
	        "HUONGDAN_SD"   : $("#hdHUONGDANTHUCHIEN").val(),
	        "TYLEBHYT_TVT"	: _item.TYLEBHYT_TVT,
	        "DICHVUKHAMBENHID" : _item.DICHVUKHAMBENHID,
	        "MAHOATCHAT"	: _item.MAHOATCHAT,
	        "OLDVALUE"		: _item.SO_LUONG,
	        "KETRUNGHOATCHAT" : _item.KETRUNGHOATCHAT,
	        "KHO_THUOCID" : _item.KHO_THUOCID //L2PT-7738
	    });
		$("#hdHUONGDANTHUCHIEN").val("");
	}
	
	function doCalDrugTicket(){
		var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
		//console.log("-----------doCalDrugTicket: "+JSON.stringify(jsonGridData));
		var _totalStart = 0, _totalIns = 0, _totalEnd = 0;
		var _soluong = 0;
		_list_thuoc_dake = "";
		var row_totalStart = 0, row_totalIns = 0, row_totalEnd = 0;
		if(jsonGridData.length > 0){
			for(var i = 0; i < jsonGridData.length; i ++){
				if(_list_thuoc_dake == "")
					_list_thuoc_dake = jsonGridData[i].THUOCVATTUID;
				else
					_list_thuoc_dake = _list_thuoc_dake +","+ jsonGridData[i].THUOCVATTUID;
				var _don_gia = jsonGridData[i].DON_GIA.replace(/,/g,'');
				//console.log(_tyle_bhyt +"--"+ _don_gia+"--"+_doituongbenhnhanid);
				var r = vienphi.tinh_phi_dv(_doituongbenhnhanid,_tyle_bhyt, "0",_don_gia,_don_gia,_don_gia, _don_gia, "0");
				_soluong = parseFloat(jsonGridData[i].SO_LUONG);
				row_totalStart = _soluong * parseInt(r.tong_cp);
				row_totalIns = _soluong*parseInt(r.bh_tra);
				row_totalEnd = _soluong*parseInt(r.nd_tra);
				
				_totalStart = _totalStart + row_totalStart;
				_totalIns =  _totalIns + row_totalIns;
				_totalEnd =  _totalEnd + row_totalEnd;
				
				//console.log(_tyle_bhyt +"--"+ _don_gia)
			}
		}else{
			_totalStart = 0;
			_totalIns =  0;
			_totalEnd =  0;
		}
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { THANH_TIEN	: _totalStart.formatMoney(0) });
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { BH_TRA		: _totalIns.formatMoney(0) });
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { ND_TRA		: _totalEnd.formatMoney(0) });

		$('#lblMUCHUONG_BHYT').val(_tyle_bhyt +"%");
		$('#lblMA_BHYT').val(_ma_bhyt);
		$('#lblDT_THANHTOAN').val(_ten_doituong_benhnhan);
	}
	function doInsDonThuoc(r_action){
		if($('#cboMA_KHO').val() == '0' && _option != '02D011' && _chonkho_kedon == '1'){
			DlgUtil.showMsg('Chưa chọn kho thuốc');
			$('#btnSave').prop('disabled', false);
			return;
		}
		
		if(_opts.khamchinhphu != '1' && _configArr.KETHUOC_DONPHU_ICD == '1' && _macdinh_hao_phi != '9'){
			if($('#txtMACHANDOANICD').val() == '' && $('#txtTENCHANDOANICD_KT').val() == ''){
				DlgUtil.showMsg('Bạn phải nhập ICD10');			
				$('#btnSave').prop('disabled', false);
				$('#txtMACHANDOANICD').focus();
				return false;
			}
		}else{
			if(_configArr.KETHUOC_DIKEM_ICD == '0' || !_dichvucha_id){//L2PT-28560
				if((r_action == "Upd" || r_action == "Add") && $('#txtMACHANDOANICD').val() == '' && r_checkicd == "1"){
					DlgUtil.showMsg('Bạn phải nhập ICD10');			
					$('#btnSave').prop('disabled', false);
					$('#txtMACHANDOANICD').focus();
					return false;
				}
			}
		}
		
		if(type_tc == 1){
			if($("#cboNGUOI_TC").val() == "" || $("#cboNGUOI_TC").val() == -1){
				DlgUtil.showMsg('Hãy chọn người tiêm!');
				$('#cboNGUOI_TC').focus;
				$('#btnSave').prop('disabled', false);
				return false;
			}
			if($("#txtNGAY_TC").val() == null || $("#txtNGAY_TC").val() == ""){
				DlgUtil.showMsg('Hãy chọn ngày tiêm!');
				$('#txtNGAY_TC').focus;
				$('#btnSave').prop('disabled', false);
				return false;
			}
		}
		
		if(_option != '02D011' && $.inArray(_loaitiepnhanid, _configArr.KETHUOC_LOAITN_PDIEUTRI) >= 0 && _phieudtri_kedon == "1" && $.inArray(_option, _loaikedonthuoc) >= 0 && ($('#cboPHIEU_CD').val() == "" || $('#cboPHIEU_CD').val() == "-1" || typeof $('#cboPHIEU_CD').val() == 'undefined')){
			DlgUtil.showMsg('Hãy chọn phiếu điều trị');
			$('#btnSave').prop('disabled', false);
			$('#cboPHIEU_CD').focus;
			return false;
		}	
		
		//Begin_HaNv_05102020: Ràng buộc chỉ định dịch vụ khi tờ điều trị kí số - L2PT-28416
		if (_configArr.HIS_CHECK_DIEUTRI_KISO == '1') {
			var checkKiSo = jsonrpc.AjaxJson.ajaxCALL_SP_I("PDT.CHECK_KISO", $('#cboPHIEU_CD').val());
			if (checkKiSo > 0) {
				DlgUtil.showMsg('Không thể cập nhật thông tin phiếu được gắn với phiếu điều trị đã được kí số!');
				$('#btnSave').prop('disabled', false);
				$('#cboPHIEU_CD').focus;
				return false;
			}
		}
		//End_HaNv_05102020
		
		var b_ngaytiepnhan = _ngaytiepnhan.substr(6,4) + _ngaytiepnhan.substr(3,2) + _ngaytiepnhan.substr(0,2) + _ngaytiepnhan.substring(11,13);
		var b_ngaycapthuoc = $('#txtTHOI_GIAN').val().substr(6,4) + $('#txtTHOI_GIAN').val().substr(3,2) + $('#txtTHOI_GIAN').val().substr(0,2) + $('#txtTHOI_GIAN').val().substring(11,13);
		var b_ngaykham_tn = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY').substr(6,4) + jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY').substr(3,2) + jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY').substr(0,2);
		var r_ngaycapthuoc = $('#txtTHOI_GIAN').val().substr(6,4) + $('#txtTHOI_GIAN').val().substr(3,2) + $('#txtTHOI_GIAN').val().substr(0,2);

		// check ngày cấp thuốc với ngày hiện tại
		if(_loaitiepnhanid == 1 && parseInt(r_ngaycapthuoc) > parseInt(b_ngaykham_tn)){
			$('#txtTHOI_GIAN').select();
			$('#txtTHOI_GIAN').focus;
			$('#btnSave').prop('disabled', false);
			DlgUtil.showMsg('Ngày chỉ định không lớn hơn ngày hiện tại');
			return;
		}
		
		if(_loaitiepnhanid == 0 && (parseInt(_ngaykethuocmax.substring(0, 8)) < parseInt(b_ngaycapthuoc.substring(0, 8)))){
			DlgUtil.showMsg('Thời gian chỉ định không được vượt quá số ngày cho phép');
			$('#btnSave').prop('disabled', false);
			return;
		}
		//tuyennx_edit_start_20191007 L2PT-9546 L2PT-9545
		if(_configArr.KETHUOC_BSKE_DOITUONG.includes(_loaitiepnhanid) &&_bacsi_ke == '1' 
			&& ($('#cboBACSIID').val() == '-1' || $('#cboBACSIID').val() == undefined)){
		//tuyennx_edit_end_20191007 L2PT-9546 L2PT-9545
			DlgUtil.showMsg('Chọn bác sỹ kê đơn');
			$('#cboBACSIID').focus();
			$('#btnSave').prop('disabled', false);
			return;
		}
		
		// check ngày cấp thuốc với ngày tiep nhan
	    var b_ngaytiepnhan = _ngaytiepnhan.substr(6,4) + _ngaytiepnhan.substr(3,2) + _ngaytiepnhan.substr(0,2) + _ngaytiepnhan.substring(11,13)+ _ngaytiepnhan.substring(14,16)+_ngaytiepnhan.substring(17,19);
	    var b_ngaycapthuoc = $('#txtTHOI_GIAN').val().substr(6,4) + $('#txtTHOI_GIAN').val().substr(3,2) + $('#txtTHOI_GIAN').val().substr(0,2) + $('#txtTHOI_GIAN').val().substring(11,13)+ $('#txtTHOI_GIAN').val().substring(14,16)+$('#txtTHOI_GIAN').val().substring(17,19);
		if(parseInt(b_ngaycapthuoc) < parseInt(b_ngaytiepnhan)){
			$('#txtTHOI_GIAN').select();
			$('#txtTHOI_GIAN').focus;
			DlgUtil.showMsg('Ngày giờ chỉ định không nhỏ hơn ngày giờ tiếp nhận');
			$('#btnSave').prop('disabled', false);
			return;
		}
		
		//tuyennx_add_start_20190702 L2PT-6609
//		if($("#cboDONTHUOCVT").val() != 0 && $("#cboDONTHUOCVT").val() != null && (_option == '02D014' || _option == '02D016')){
//			//var _tg_ke = moment($("#txtTHOI_GIAN").val().trim(),'DD/MM/YYYY HH24:MI:SS');
//			var _tg_tra = $('#txtTG_DUNG').val().substr(6,4) + $('#txtTG_DUNG').val().substr(3,2) + $('#txtTG_DUNG').val().substr(0,2) + $('#txtTG_DUNG').val().substring(11,13)+ $('#txtTG_DUNG').val().substring(14,16)+$('#txtTG_DUNG').val().substring(17,19);
//			
//			var sql_par = [];
//			sql_par.push({"name":"[0]", value:$('#cboDONTHUOCVT').val()});
//			_tg_kethuoc = jsonrpc.AjaxJson.getOneValue('GET.THOIGIAN.LINH', sql_par);
//			
////			var _tg_ke = $('#cboDONTHUOCVT'+ " option:selected").text().split('-')[1] ;
//			var i_ngaycapthuoc = $('#txtTHOI_GIAN').val().substr(6,4) + $('#txtTHOI_GIAN').val().substr(3,2) + $('#txtTHOI_GIAN').val().substr(0,2) + $('#txtTHOI_GIAN').val().substring(11,13)+ $('#txtTHOI_GIAN').val().substring(14,16)+$('#txtTHOI_GIAN').val().substring(17,19);
//			if(parseInt(i_ngaycapthuoc) < parseInt(_tg_kethuoc) || parseInt(_tg_tra) < parseInt(_tg_kethuoc)){
//				//$("#txtTHOI_GIAN").focus();
//				DlgUtil.showMsg("Thời gian trả đơn phải lớn hơn thời gian lĩnh thuốc!");
//				$('#btnSave').prop('disabled', false);
//				return false;
//			}
//		}
		//tuyennx_add_end_20190702 L2PT-6609

		if(parseInt($('#txtSONGAY_KE').val()) > parseInt(_songaykemax)){
			$('#txtSONGAY_KE').select();
			$('#txtSONGAY_KE').focus;
			DlgUtil.showMsg('Số ngày kê thuốc không lớn hơn '+_songaykemax+' ngày');
			$('#btnSave').prop('disabled', false);
			return;
		}
		
		var isCheckSL = 0;
		var isCheckLOAIDT = 0;
		var _print = false;
		var isCheckBHYT =0; //L2PT-5712
		//var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');//tuyennx_edit_start L2PT-6436 check don thuoc mau khong chon kho
		var rowIds = $('#'+_gridDonThuoc).jqGrid('getDataIDs');
		for (var k = 0; k < rowIds.length; k++) {
			var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rowIds[k]);
			
			if($('#'+_gridDonThuoc).find("input[id*='SO_LUONG']").length > 0){
				isCheckSL = 1;
	    	}
			
			if(row.ID_DT_MOI == null || row.ID_DT_MOI == ''){
				isCheckLOAIDT = 1;
			}
			//L2PT-5712
			if((row.ID_DT_MOI == '1' || row.ID_DT_MOI == '2' || row.ID_DT_MOI == '3')
					|| (!row.LOAI_DT_MOI.includes("Viện phí") && (row.ID_DT_CU == '1' || row.ID_DT_CU == '2' || row.ID_DT_CU == '3')) ){
				isCheckBHYT = 1;
			}
			//tuyennx_add_start_20190821 L2PT-27340
			if(parseFloat(row.SO_LUONG)  < 0){
				DlgUtil.showMsg('Tồn tại thuốc kê có số lượng âm!');
				$('#btnSave').prop('disabled', false);
	    		return false;
			}
			//tuyennx_add_end_20190821 L2PT-27340
		}
		
		//L2PT-4392 //L2PT-5712
		if(_doituongbenhnhanid == "1" && _configArr.KETHUOC_NGAYKE_HANTHE =='1' && isCheckBHYT == '1'){
			var date = new Date($('#txtTHOI_GIAN').val().substr(3,2)+ '/' + $('#txtTHOI_GIAN').val().substr(0,2) +'/' + $('#txtTHOI_GIAN').val().substr(6,4)) ;
			var date_bh_bd = new Date(_ngay_bhyt_bd.substr(3,2)+ '/' + _ngay_bhyt_bd.substr(0,2) +'/' + _ngay_bhyt_bd.substr(6,4)) ;
			//var date_bh_kt = new Date(_ngay_bhyt_kt.substr(3,2)+ '/' + _ngay_bhyt_kt.substr(0,2) +'/' + _ngay_bhyt_kt.substr(6,4)) ;
			if(date_bh_bd > date ){
				DlgUtil.showMsg("Thời gian kê phải nằm trong thời gian của thẻ BHYT từ: " +  _ngay_bhyt_bd + " đến: " + _ngay_bhyt_kt);
				$('#btnSave').prop('disabled', false);
				$('#txtTHOI_GIAN').focus;
				return false;
			}
		}
		
		
		//tuyennx_add_start_20190821 L2PT-7753
		var DS_ICD = '\'' +$('#txtMACHANDOANICD').val() +'\'';
		if($('#txtTENCHANDOANICD_KT').val() != ''){
			var array = $('#txtTENCHANDOANICD_KT').val().split(';');
			for(var i=0;i<array.length;i++){
				DS_ICD = DS_ICD+','+ '\''+array[i].split('-')[0] +'\'';
			}
		}
		//tuyennx_add_end_20190821 L2PT-7753
		for (var k = 0; k < rowIds.length; k++) {
			var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rowIds[k]);	
			
			// BVTM-2931
			if(_configArr.KE_THUOC_KIEUCHECK_CACHDUNG == '1') { 
				var _khoid =  $("#cboMA_KHO").val();
				if(_khoid == 0 && _option != '02D011'){
					$("#cboMA_KHO").focus();
					DlgUtil.showMsg("Hãy chọn kho");				
					return;
				}
				var temp = row.HUONGDAN_SD.split('@');
				_loaikho = $('#cboMA_KHO'+ " option:selected").attr('extval1')			
				var _ch_loaikh = _configArr.KE_THUOC_DSKHOCHECK_CACHDUNG.split(',');
				if($.inArray(_loaikho, _ch_loaikh) < 0 && temp[2] == ""){
					DlgUtil.showMsg("Hãy nhập cách dùng thuốc");				
					return;
				}
			}else{			
				if($.inArray(row.LOAITVTID, ["0","3","6","7","8","9"]) >= 0 && _option == '02D010'){ // BVTM-2931
					if(_sudung_lieudung == '1' && row.LIEUDUNG == ""){
						DlgUtil.showMsg('Thuốc kê trong danh sách không có liều dùng');
						$('#btnSave').prop('disabled', false);
			    		return false;
					}else{
						var temp = row.HUONGDAN_SD.split('@');
						if(temp[2] == ""){
							
							//tuyennx_edit_start_L2PT-34197 bo qua check bat buoc cach dung voi vi thuoc YHCT ke di kem
							var _check_thuoc = 0;
							if(_configArr.KE_THUOC_CHECK_DUONGDUNG == 1 && _dichvucha_id){
								var sql_par1 = [];
								sql_par1.push({"name":"[0]", value:row.THUOCVATTUID});
								_check_thuoc = jsonrpc.AjaxJson.getOneValue('KETHUOC.VITHUOC', sql_par1);
							}
							if(_check_thuoc == 0){
								//BVTM-1637
								if(_configArr.KETHUOC_CACHDUNG_DIKEM == '1' && _dichvucha_id !== "")
									continue;
								DlgUtil.showMsg('Thuốc kê trong danh sách không có cách dùng');
								$('#btnSave').prop('disabled', false);
					    		return false;
							}
							//tuyennx_edit_end_L2PT-34197
							
						}
					}
				}
			}
			//tuyennx_add_start_20190821 L2PT-7753
			var _par_check = [row.THUOCVATTUID,DS_ICD,'1'];					
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KE.ICD",_par_check.join('$'));
			var _rs = ret.split(';');
			if(parseInt(_rs[0]) > 0){
				DlgUtil.showMsg("Thuốc ["+ row.TEN_THUOC +"] kê cho BN phải có những ICD được chỉ định trong danh mục! Danh sách ICD: "+_rs[1]);
				if(_configArr.CHANKETHUOC_THEOICD == '0'){
					$('#btnSave').prop('disabled', false);
		    		return false;
				}
			}
			
			_par_check = [row.THUOCVATTUID,DS_ICD,'0'];					
			ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KE.ICD",_par_check.join('$'));
			_rs = ret.split(';');
			if(parseInt(_rs[0]) > 0){
				DlgUtil.showMsg("Thuốc ["+ row.TEN_THUOC +"] chỉ định cho BN có ICD không được phép kê trong danh mục! Danh sách ICD: "+_rs[1]);
				if(_configArr.CHANKETHUOC_THEOICD == '0'){
					$('#btnSave').prop('disabled', false);
		    		return false;
				}
			}
			
			//L2PT-1479
			_par_check = [row.THUOCVATTUID,DS_ICD,'2'];					
			ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KE.ICD",_par_check.join('$'));
			_rs = ret.split(';');
			if(parseInt(_rs[0]) > 0){
				DlgUtil.showMsg("Chú ý thuốc ["+ row.TEN_THUOC +"] cảnh báo ICD: "+_rs[1]);
				if(_configArr.CHANKETHUOC_THEOICD == '0'){
					$('#btnSave').prop('disabled', false);
		    		return false;
				}
			}
			//L2PT-126334
			_par_check = [row.THUOCVATTUID,DS_ICD,'4',JSON.stringify(objData)];					
			ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KETVT.ICD",_par_check.join('$'));
			_rs = ret.split(';');
			if(parseInt(_rs[0]) > 0){
				DlgUtil.showMsg("Chú ý thuốc ["+ row.TEN_THUOC +"] cảnh báo thiếu ICD: "+_rs[1]);
			}
			//tuyennx_add_end_20190821 L2PT-7753
			
			//tuyennx_add_start_20190821 L2PT-34366
			_par_check = [$("#hidHOSOBENHANID").val(),row.THUOCVATTUID,DS_ICD,'1'];					
			ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("KE.ICD.TUOI",_par_check.join('$'));
			_rs = ret.split(';');
			if(parseInt(_rs[0]) > 0){
				DlgUtil.showMsg("Thuốc ["+ row.TEN_THUOC +"] kê cho BN phải có ICD và độ tuổi tương ứng chỉ định trong danh mục! Danh sách ICD: "+_rs[1]);
				if(_configArr.CHANKETHUOC_THEOICD == '0'){
					$('#btnSave').prop('disabled', false);
		    		return false;
				}
			}
			
			_par_check = [$("#hidHOSOBENHANID").val(),row.THUOCVATTUID,DS_ICD,'0'];						
			ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("KE.ICD.TUOI",_par_check.join('$'));
			_rs = ret.split(';');
			if(parseInt(_rs[0]) > 0){
				DlgUtil.showMsg("Thuốc ["+ row.TEN_THUOC +"] chỉ định cho BN có ICD không được phép kê với độ tuổi của BN trong danh mục! Danh sách ICD: "+_rs[1]);
				if(_configArr.CHANKETHUOC_THEOICD == '0'){
					$('#btnSave').prop('disabled', false);
		    		return false;
				}
			}
			
			_par_check = [$("#hidHOSOBENHANID").val(),row.THUOCVATTUID,DS_ICD,'2'];						
			ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("KE.ICD.TUOI",_par_check.join('$'));
			_rs = ret.split(';');
			if(parseInt(_rs[0]) > 0){
				DlgUtil.showMsg("Chú ý thuốc ["+ row.TEN_THUOC +"] cảnh báo tuổi theo ICD: "+_rs[1]);
				if(_configArr.CHANKETHUOC_THEOICD == '0'){
					$('#btnSave').prop('disabled', false);
		    		return false;
				}
			}
			//tuyennx_add_end_20190821 L2PT-34366
			
			//L2PT-126334
			_par_check = [$("#hidHOSOBENHANID").val(),row.THUOCVATTUID,DS_ICD,'4'];						
			ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("KE.ICD.TUOI",_par_check.join('$'));
			_rs = ret.split(';');
			if(parseInt(_rs[0]) > 0){
				DlgUtil.showMsg("Thuốc ["+ row.TEN_THUOC +"] chỉ định cho BN có cảnh báo thiếu ICD với độ tuổi của BN trong danh mục! Danh sách ICD: "+_rs[1]);
				//L2PT-126334	
//				if(_configArr.CHANKETHUOC_THEOICD == '0'){
//				if(_configArr.CHANKETHUOC_THEOICD == '0'){
//					$('#btnSave').prop('disabled', false);
//		    		return false;
//				}
			}
		}
		
		//tuyennx_add_start L2PT-6436 check don thuoc mau khong chon kho
		if($('#cboMA_KHO').val() != '0' && $('#cboMA_KHO').val() != null){
			for (var k = 0; k < rowIds.length; k++) {
				if($("#"+_gridDonThuoc).jqGrid ('getCell', rowIds[k], 'KHO_THUOCID') == 0 || 
						$("#"+_gridDonThuoc).jqGrid ('getCell', rowIds[k], 'KHO_THUOCID') == ''){
							$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[k],'KHO_THUOCID',$("#cboMA_KHO").val());
						}
			}
		}
		
		for (var k = 0; k < rowIds.length; k++) {
			var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rowIds[k]);			
			if(_opts.option!='02D011' && ((row.KHO_THUOCID == 0|| row.KHO_THUOCID == '' || row.KHO_THUOCID == null) 
					&& ($('#cboMA_KHO').val() == '0') || $('#cboMA_KHO').val() == null)){
				DlgUtil.showMsg('Thuốc trong danh sách chưa chọn kho!');
				$('#btnSave').prop('disabled', false);
	    		return false;
			}
			//
	    	//L2PT-32063 L2PT-409
    		if((_configArr.KE_THUOC_HAOPHI_THEOGOI == '1' || _configArr.KE_THUOC_DV_KEM_MAU == "1")
    			&& _macdinh_hao_phi == '9' && (_dichvucha_id != "" && parseInt(_dichvucha_id) > 0)){
	    		 var sql_par = [];
				 sql_par.push({"name":"[0]", value:row.THUOCVATTUID});
				 sql_par.push({"name":"[1]", value:_dichvucha_id});
				 sql_par.push({"name":"[2]", value:row.SO_LUONG});
				 var _check_sl_hp = 0;
				 if(_configArr.KE_THUOC_DV_KEM_MAU == "1"){
					 sql_par.push({"name":"[3]", value:_mbp_temp});
					 _check_sl_hp = jsonrpc.AjaxJson.getOneValue('KETHUOC.SLMAU', sql_par);
				 } 
				 else 
					 _check_sl_hp = jsonrpc.AjaxJson.getOneValue('KETHUOC.SLDIKEM', sql_par);
				 if(_check_sl_hp > 0){
					 $('#btnSave').prop('disabled', false);
					 return DlgUtil.showMsg("Số lượng thuốc ["+ row.TEN_THUOC +"] nhập phải nhỏ hơn số lượng trong gói thiết lập!");
				 }
					 
	    	}
		}
		var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
		//tuyennx_add_end
		
		if(isCheckSL == '1'){
			DlgUtil.showMsg('Tồn tại trường số lượng đang sửa trong đơn'+_lbl_text+'!');
    		$('#btnSave').prop('disabled', false);
    		return false;
		}
		
		if(isCheckLOAIDT == '1'){
			DlgUtil.showMsg('Thuốc chưa có loại đối tượng thanh toán');
			//$('#btnSave').prop('disabled', false); //tuyennx L2PT-5527
    		return false;
		}
		//tuyennx_add_start_20180605
		var rowKeys_select = $("#"+_gridDonThuoc).getGridParam('selarrrow');
		for (var k = 0; k < rowKeys_select.length; k++) {
			var rowObject = $("#"+_gridDonThuoc).getRowData(rowKeys_select[k]);
			if(!rowObject.SO_LUONG.includes(".")){
				DlgUtil.showMsg('Tồn tại thuốc được check nhưng số lượng kê chẵn');
				$('#btnSave').prop('disabled', false);
	    		return false;
			}
		}
		//tuyennx_add_end_20180605
		
		if(jsonGridData.length > 0){
			_jsonDonThuoc.DS_THUOC 			= jsonGridData;
			_jsonDonThuoc.KHAMBENHID 		= _khambenhid;
			_jsonDonThuoc.KHOAID 			= _khoaId;
			_jsonDonThuoc.PHONGID 			= _phongId;
			_jsonDonThuoc.MA_CHANDOAN		= $("#txtMACHANDOANICD").val();
			//tuyennx_add_start_20170724 
			_jsonDonThuoc.GHICHU_BENHCHINH		= $("#txtGHICHU_BENHCHINH").val();
			//tuyennx_add_end_20170724 
			_jsonDonThuoc.CHANDOAN 			= $("#txtTENCHANDOANICD").val();
			_jsonDonThuoc.CHANDOAN_KT 		= $("#txtTENCHANDOANICD_KT").val();
			_jsonDonThuoc.DUONG_DUNG 		= $("#cboDUONG_DUNG").val();
			_jsonDonThuoc.KIEUKEDON			= _loaikedon+"";
			
			if(_bacsi_ke == '1' && $("#cboBACSIID").val() != '-1' && $("#cboBACSIID").val() != null){ //L2PT-22232
				_jsonDonThuoc.NGUOIDUNG_ID 		= $("#cboBACSIID").val();
			}else{
				_jsonDonThuoc.NGUOIDUNG_ID 		= _user_id;
			}
			
			_jsonDonThuoc.TEMP_CODE 		= $("#txtTEXT_TEMP").val();
			_jsonDonThuoc.DICHVUCHA_ID 		= _dichvucha_id;
			_jsonDonThuoc.DOITUONG_BN_ID 	= _doituongbenhnhanid;
			_jsonDonThuoc.TYLE_BHYT 		= _tyle_bhyt;
			_jsonDonThuoc.NGAY_BHYT_KT 		= _ngay_bhyt_kt;
			_jsonDonThuoc.DUONGDUNGE 		= '';
			_jsonDonThuoc.OPTION 			= _option;

			//_jsonDonThuoc.HINH_THUC_KE		= _option;
			//_jsonDonThuoc.NHOM_MABHYT_ID 	= _nhom_mabhyt_id;
			if(_option != '02D011') 
				_jsonDonThuoc.KHO_THUOCID = $("#cboMA_KHO").val();
			else
				_jsonDonThuoc.KHO_THUOCID 	= "";
			_jsonDonThuoc.NGAYMAUBENHPHAM 	= $("#txtTHOI_GIAN").val();
			_jsonDonThuoc.NGAYMAUBENHPHAM_SUDUNG = $("#txtTG_DUNG").val();
			if($("#cboPHIEU_CD").val() !== null)
				_jsonDonThuoc.PHIEUDIEUTRI_ID = $("#cboPHIEU_CD").val();
			else
				_jsonDonThuoc.PHIEUDIEUTRI_ID = "0";
			_jsonDonThuoc.INS_TYPE 			= _loai_don;
			_jsonDonThuoc.I_ACTION 			= r_action;
			if(sd_doncu_donmau ==0)
				_jsonDonThuoc.MAUBENHPHAMID 	= _maubenhpham_id;
			_jsonDonThuoc.SONGAY_KE 	= $("#txtSONGAY_KE").val();
			_jsonDonThuoc.YKIENBACSY 	= $("#txtLOIDANBS").val();	
			//tuyennx_add_start 20190328 L2PT-2948
			_jsonDonThuoc.CACHSACTHUOC 	= $("#txtCACHSACTHUOC").val();	
			_jsonDonThuoc.CACHUONG 	= $("#txtCACHUONG").val();	
			//tuyennx_add_end 20190328
			if(_option == '02D017' || _option == '02D018'){
				_jsonDonThuoc.SLTHANG 		= $("#txtSLTHANG").val();
			}else{
				_jsonDonThuoc.SLTHANG 		= '1';
			}			
			_jsonDonThuoc.NGAYHEN 		= $("#txtTG_HENKHAM").val();
			
			if($('#chkCapPhieuHenKham').is(':checked')){
				_jsonDonThuoc.PHIEUHEN 		= '1';
			}else{
				_jsonDonThuoc.PHIEUHEN 		= '0';
			}
			
			_jsonDonThuoc.MAUBENHPHAMCHA_ID = _phieutraid;
			
			//_thoigian_vaovien
			var _TG_KEDON = moment($("#txtTHOI_GIAN").val().trim(),'DD/MM/YYYY');
			var _TG_SUDUNG_THUOC = moment($("#txtTG_DUNG").val().trim(),'DD/MM/YYYY');
			var _tg_vaovien = moment(_thoigian_vaovien,'DD/MM/YYYY');
			if(_tg_vaovien > _TG_KEDON){
				$("#txtTHOI_GIAN").focus();
				DlgUtil.showMsg("Thời gian kê đơn phải lớn hơn thời gian vào viện!");
				$('#btnSave').prop('disabled', false);
				return false;
			}
			if(_TG_KEDON > _TG_SUDUNG_THUOC){
				$("#txtTG_DUNG").focus();
				DlgUtil.showMsg("Thời gian kê đơn phải nhỏ hơn thời gian vào viện!");
				$('#btnSave').prop('disabled', false);
				return false;
			}
			
			// SONDN 06/03/2020 L2PT-23947
			if(_MACHANDOAN.toUpperCase() != $("#txtMACHANDOANICD").val() && ($("#txtMACHANDOANICD").val() || $("#txtTENCHANDOANICD").val())){ //L2PT-28560
				DlgUtil.showMsg("Trường ICD chính đang chưa đúng, đang nhập là [" + $("#txtMACHANDOANICD").val() 
								+ "], đang ghi nhận là [" + _MACHANDOAN + "], yêu cầu kiểm tra lại. ", function(){
					$('#txtMACHANDOANICD').focus();
				});
				$('#btnSave').prop('disabled', false);
				return false;
			}
			// END SONDN 06/03/2020 L2PT-23947
			
			// uppercase ma chan doan de duyet ke toan khong bi loi;
			_jsonDonThuoc.MA_CHANDOAN = _jsonDonThuoc.MA_CHANDOAN.toUpperCase(); 
			
			var rsldt = checkCongLamDungThuocBYT(jsonGridData);
			if(rsldt ==1)
				return; 
			var rs = dayCongBYT();
			if(rs ==1)
				return; 
			//tuyennx_add_start_20190319 L2PT-2864
			_jsonDonThuoc.DINHDUONG = _dinhduong+''; 
			//tuyennx_add_end_20190319
			_jsonDonThuoc.SONGAYMAX = _songay_max+'';
			//tuyennx_edit_start_20180802 HISL2TK-923
			_jsonDonThuoc.RAVIEN = _opts.ravien; //tuyennx L2PT-9849
			
			_jsonDonThuoc.KETUGOI = _ketugoi; //tuyennx L2PT-409
			_jsonDonThuoc.HAOPHI = _macdinh_hao_phi+''; //tuyennx BVTM-1651
			if(type_tc == 1){
				_jsonDonThuoc.TIEMCHUNG = '1';
				_jsonDonThuoc.NGUOI_TC = $("#cboNGUOI_TC").val();
				_jsonDonThuoc.NGAY_TC = $("#txtNGAY_TC").val();
			}
			//BVTM-4316
			_jsonDonThuoc.NGAY_CK = $("#txtNGAY_CK").val();
			_jsonDonThuoc.LOAITIEPNHANID = _loaitiepnhanid+'';//L2PT-9923
			_jsonDonThuoc.ID_KE = $("#hidID_KE").val();	// BVTM-6723
			_jsonDonThuoc.ID_GOIKE = _maubenhpham_temp_id;//BVTM-6348
			
			var ret;
			var _close_p = 0;
			if(r_action != "SAVE_TEMP"){
				ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.12.MN", JSON.stringify(_jsonDonThuoc));
			}else{
				ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.27", JSON.stringify(_jsonDonThuoc));
			}
				
			//tuyennx_edit_end_20180802
			var rets = ret.split(',');
			_badaingay = rets[1];
			if(rets[0] >= 1){
			   _ketugoi = "0"; //L2PT-409
			   _mbp_temp = "0";
			   var msg="";
			   if(r_action != "SAVE_TEMP"){
				   if(_opts.option=='02D010'){
					   _print = true;
					   msg= "Tạo phiếu thuốc thành công";
					   $('#btnInDon').prop('disabled', false);
					   r_maubenhphamid = rets[0];
	
					   //tuyennx_add_start
					   if(r_action !== "Upd"){
						   	_daybtkd(rets[2].split(';'),r_action);
					   		//_xacnhanbanthuoc(rets[0],r_action);
						   	_dayDonThuocOnline(_configArr.DTDT_DAY_DONTHUOC,rets[0],_opts.option,_loaitiepnhanid, 0); //L2PT-30504
					   }
       		   			//tuyennx_add_end
					   //START L2PT-1283
					   var pars = ['NTU_KHOA_INCAMKET_TM']; 
			    	   var data_CH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
			    	   //tuyennx_edit_start_20190301 L2PT-2048
			    	   objData = new Object();
						objData.MAUBENHPHAMID = rets[0];
						var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.INCAMKET", JSON.stringify(objData)); 
						if(fl > 0){
			    	   //if(data_CH.indexOf($("#cboMA_KHO").val()) != -1){
						//tuyennx_edit_end
			    		   var par = [{
			      				name : 'i_tiepnhanid',
			      				type : 'String',
			      				value : _tiepnhanid
			      				}];
							openReport('window', "RPT_GIAYCAMKET_TRUYENMAU_951", "pdf", par);
			    	   }
						if(checkclose == 1)
							 $("#btnClose").trigger("click");
						EventUtil.raiseEvent("reloadTabThuocVT",{msg:"Tạo phiếu thuốc thành công",maubpid:rets[0]});
					   //END L2PT-1283
				   }else if(_opts.option=='02D014'){
					   _print = false;
					   msg= "Tạo phiếu trả thuốc thành công";
				   }else if(_opts.option=='02D015'){
					   _print = true;
					   msg= "Tạo phiếu vật tư thành công";
				   }else if(_opts.option=='02D016'){
					   _print = false;
					   msg= "Tạo phiếu trả vật tư thành công";
				   }else if(_opts.option=='02D011'){
					   _print = true;
					   msg= "Tạo đơn thuốc thành công";
					   $('#btnInDon').prop('disabled', false);
					   r_maubenhphamid = rets[0];
					   //tuyennx_add_start
					   _daybtkd(rets[2].split(';'),r_action);
       		   			//tuyennx_add_end
					   _dayDonThuocOnline(_configArr.DTDT_DAY_DONTHUOC,rets[0],_opts.option,_loaitiepnhanid, 0); //L2PT-30504
					   if(checkclose == 1)
							 $("#btnClose").trigger("click");
						EventUtil.raiseEvent("reloadTabThuocVT",{msg:"Tạo phiếu thuốc thành công",maubpid:rets[0]});
				   }else if(_opts.option=='02D017'){
					   _print = true;
					   msg= "Tạo đơn thuốc YHCT thành công";
					   //tuyennx_add_start
					   if(r_action !== "Upd"){
						   	_daybtkd(rets[2].split(';'),r_action);
					   		//_xacnhanbanthuoc(rets[0],r_action);
						   	_dayDonThuocOnline(_configArr.DTDT_DAY_DONTHUOC,rets[0],_opts.option,_loaitiepnhanid, 0); //L2PT-30504
					   }
       		   			//tuyennx_add_end
				   }else if(_opts.option=='02D018'){
					   _print = true;
					   msg= "Tạo đơn thuốc trả YHCT thành công";
				   }else if(_opts.option=='02D019'){
					   _print = true;
					   msg= "Tạo đơn thuốc nhà thuốc thành công";
					 //tuyennx_add_start
					   _daybtkd(rets[2].split(';'),r_action);
       		   			//tuyennx_add_end
					   _dayDonThuocOnline(_configArr.DTDT_DAY_DONTHUOC,rets[0],_opts.option,_loaitiepnhanid, 0); //L2PT-30504
				   }
				   
				   //BVTM-1487
				   if(checkky_Ca == 1){
					   //L2PT-124731 ky nhieu phieu
					   //_kySo(rets[0]);
					   var dsmbp = rets[2].split(';');
			        	for(var j = 0; j < dsmbp.length; j++){
			    			var rets = dsmbp[j];
			    			_kySo(rets);
			        	}
				   } 
				   
				   //BVTM-4843
				   var show = _configArr.NGT_MO_POPUP_LUUDONTHUOC;
				   if(show == '1' && checkky_Ca == 0) {
					   $("#btnXuTri").trigger("click");
				   }
				   
				   //L2PT-32754
				   if(_configArr.KETHUOC_TUONGTAC_THUOC == '1'){
						//L2PT-8978
						if(_configArr.KE_THUOC_LUU_TUONTAC == '1'){
							var objtuongtac = {};
							objtuongtac.MAUBENHPHAMID = rets[0];
							objtuongtac.DANHSACHTT = _objLstTT;
							var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("THUOCTT.THEMTHUOC", JSON.stringify(objtuongtac));
							if(parseInt(result) < 0){
								DlgUtil.showMsg("Có lỗi lưu thuốc tương tác!");
							}
							_objLstTT =[];
						}else{
							var sql_par = [];
							   sql_par.push({
									"name" : "[0]",
									"value" : rets[0]
								});	
							   sql_par.push({
									"name" : "[1]",
									"value" : listIdTuongtac
								});	
								jsonrpc.AjaxJson.execute("KETHUOC.UD.TT", sql_par);
								listIdTuongtac = "0";
						}
						
				   }
				   if(checkky_Ca == 0)
					   indonthuoc(rets[0].split(';'),_print, rets[0]); //L2PT-16981
				   
				   //openChidinhDV(rets[2].split(';')); //L2PT-21290
				   
				   $('#btnSave').prop('disabled', true);
				   $('#btnSaveClose').prop('disabled', true);
				   $('#btnSaveCA').prop('disabled', true);
				   $('#btnAdd').prop('disabled', true);
				   $('#btnDTMau').prop('disabled', true);
				   $('#btnDTCu').prop('disabled', true);
				   $('#btnTConSD').prop('disabled', true);
				   //tuyennx_add_start_20170816 yc L2DKBD-195
				   $('#btnTDiUng').prop('disabled', true);
				   //tuyennx_add_end_20170816 yc L2DKBD-195
				 //tuyennx_add_start day hssk
					var HIS_KETTHUCKHAM_KHICODONTHUOC = _configArr.HIS_KETTHUCKHAM_KHICODONTHUOC;
					if(HIS_KETTHUCKHAM_KHICODONTHUOC == 1){
						dayCongHSSK(_khambenhid);
						_dayDonThuocOnline(_configArr.DTDT_DAY_DONTHUOC,rets[0],_opts.option,_loaitiepnhanid, 0); //L2PT-30504
					}
					//tuyennx_add_end
				   _luu = 1;
				   //BVTM-1487
				   if(_configArr.KETHUOC_RELOAD_THUOC == '1' && _loaitiepnhanid == 0 && _dichvucha_id == ""){
						var _dsmbpid = rets[2].split(';')
						var _demau = 0;
						for(var i = 0; i < _dsmbpid.length; i++){
							var rets = _dsmbpid[i];
							var sql_par = [];
							sql_par.push({"name":"[0]", value:rets});
							var _checkMau = jsonrpc.AjaxJson.getOneValue('KETHUOC.CHECK.KEMAU', sql_par);
							if(parseInt(_checkMau) <= 0){
								_demau = 1;
							}
						}		
						if(_demau > 0){
							//location.reload();
							jQuery("#grdDONTHUOC").jqGrid("clearGridData");
							$('#btnSave').prop('disabled', false);
							$('#btnAdd').prop('disabled', false);
						    $('#btnDTMau').prop('disabled', false);
						    $('#btnDTCu').prop('disabled', false);
						    $('#btnTConSD').prop('disabled', false);
							var opt_ext={footerrow: true, rowNum: 200,rowList: [200]};
							GridUtil.init(_gridDonThuoc,"1278","200",_gridCaption,false, _gridDonThuocHeader,false,opt_ext);
						}
						DlgUtil.showMsg(msg,undefined,_configArr.HIS_TIMEOUT_THONGBAO);
					}else{				   
					   if(_timkiem_bn_kedon_ntu != '1' && _loaitiepnhanid == 0 && _check_close_popup == 0 && checkky_Ca == 0){		//L2PT-21290				   
						   EventUtil.raiseEvent("assignSevice_saveTaoPhieuThuoc",{msg:msg,option:_opts.option});
					   }else{
						   checkky_Ca = 0;
						   $('#btnSave').prop('disabled', true);
						   $('#btnSaveClose').prop('disabled', true);
						   $('#btnAdd').prop('disabled', false);
						   $('#btnDTMau').prop('disabled', false);
						   $('#btnDTCu').prop('disabled', false);
						   $('#btnTConSD').prop('disabled', false);
						   //tuyennx_add_start_20170816 yc L2DKBD-195
						   $('#btnTDiUng').prop('disabled', false);
						   $('#msgCNKQ').text("Tạo đơn thuốc thành công mã BA: "+$('#lblPATIENTCODE').val());
						   
						   DlgUtil.showMsg(msg,undefined,_configArr.HIS_TIMEOUT_THONGBAO); //L2PT-30583
						   //DlgUtil.showMsg("Tạo đơn thuốc thành công");
					   }	
					}
					if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NOTIFY_APP_VNCARE') == 1){
						var _sophongkham = "";
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.BN.VNCARE",$("#hidBENHNHANID").val()+'$'+$("#hidHOSOBENHANID").val()+'$'+rets[0]+'$'+"-1");
						if(data_ar != null && data_ar.length > 0){
							var sql_par=[];
							sql_par.push({"name":"[0]","value":_khoaId});
							sql_par.push({"name":"[1]","value":_opts.phongId});
							var vsophong = jsonrpc.AjaxJson.ajaxExecuteQueryO("SOPHONG.TIEPDON", sql_par);
							vphong = JSON.parse(vsophong);
							if(vphong != null && vphong.length > 0 && vphong[0].SOPHONG != ""){
									_sophongkham = vphong[0].SOPHONG;
							}
						var _objThongtin = new Object();
						_objThongtin["maCSYT"] = _opts.hospital_code;
						_objThongtin["tenCSYT"] = _opts.hospital_name;
						_objThongtin["maBN"] = data_ar[0].MABENHNHAN;
						_objThongtin["tenBN"] = data_ar[0].TENBENHNHAN;
						_objThongtin["phone"] = data_ar[0].TK_LIENKET;
						_objThongtin["soPhong"] = _sophongkham == '0'?"":_sophongkham;
						_objThongtin["tenPhong"] = _opts._subdept_name;
						_objThongtin["donThuoc"] = _opts.hospital_code+'.'+data_ar[0].SOPHIEU;
						_objThongtin["tenBS"] = data_ar[0].FULL_NAME;
						_objThongtin["maLuotKham"] = data_ar[0].MAHOSOBENHAN;
						_objThongtin["mauBenhPhamID"] = data_ar[0].MAUBENHPHAMID;
						_objThongtin["soPhieu"] = data_ar[0].SOPHIEU;
						sendNotify(4,JSON.stringify(_objThongtin));
						}
					}	
					
//					if(_check_close_popup == 0){	
//						$("#btnClose").trigger("click");
//						//parent.DlgUtil.close($('[id^="divDlgTaoPhieuThuoc"]'));
//					}				
			   }else{
				   DlgUtil.showMsg("Tạo đơn thuốc mẫu thành công"); //tuyennx 	L2PT-5527
			   }   
			}
			else if (ret == '-1'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lưu không thành công!");
			}
			//tuyennx_add_start_20170727  y/c HISL2BVDKHN-247
			else if (ret == 'ngaydichvu'){
				DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện đã cấp thuốc thành công nhưng chưa kết thúc khám');
			}
			//tuyennx_add_end_20170727 
			else if (ret == '-2'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Có lỗi khi chuyển phiếu!");
			}/*else if (ret == -3){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lỗi khi yêu cầu cấp"+_lbl_text+"!");
			}*/else if (ret == '-4'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lỗi khi hủy"+_lbl_text+"!");
			}else if (ret == '-5'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lỗi khi tính giá dịch vụ cao vượt mức trần Bảo hiểm!");
			}else if (ret == '-6'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lỗi khi yêu cầu trả"+_lbl_text+"!");
			}else if (ret == '-7'){
				DlgUtil.showMsg("Lỗi do nhập sai mã ICD10!");
				$('#btnSave').prop('disabled', false);
				$("#txtMACHANDOANICD").trigger("focus");
			}else if (ret == 'cophieudangsua'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg('Bệnh nhân có phiếu CLS/Đơn thuốc đang sửa, không kết thúc khám được.');
			}else if (ret == 'daduyetvp'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg('Bệnh nhân đã duyệt thanh toán, không kê đơn được.');
			} 
			//tuyennx_add_start_20181116 kiem tra neu khoa phong lan session
			else if (ret == 'loikhoaphong'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg('Thiết lập lại khoa phòng khi kê thuốc!');
			} 
			//tuyennx_add_end
			//tuyennx_add_start_20190129
			else if(ret == 'saibenhphu'){
				DlgUtil.showMsg('Định dạng mã bệnh kèm theo không đúng.');
			}
			//tuyennx_add_end_20180927 
			else{
				$('#btnSave').prop('disabled', false);
				//tuyennx_add_start_20200908_L2PT-27371
				if(ret.toUpperCase().includes('THÀNH CÔNG')){
					 $('#btnSave').prop('disabled', true);
				}
				//tuyennx_add_end_20200908_L2PT-27371
				DlgUtil.showMsg(ret);
			}
		}else{
			$('#btnSave').prop('disabled', false);
			DlgUtil.showMsg("Bạn chưa nhập"+_lbl_text+" cho đơn thuốc!");
		}
	}	
	
	//L2PT-16981
	function indonthuoc(dsmbp,_print, lst_mbp){
		//L2PT-7182
		if(_configArr.KETHUOC_TAT_TUDONGIN == '1'){ 
			return;
		}
		
		//L2PT-4359 in gop don thuoc nha thuoc
	    if(_option == '02D019' && _configArr.KETHUOC_INTACH_NHATHUOC == '1'){ 	    	
	    	var par1=[];	
	    	if(_configArr.KETHUOC_NHATHUOC_INGOP1DON == '1'){
	    		par1.push({
					name : 'list_maubenhphamid',
					type : 'String',
					value : lst_mbp.replaceAll(';',',')
				});
	    	}else{
	    		par1.push({
					name : 'maubenhphamid',
					type : 'String',
					value : dsmbp[0]
				});
	    	}
			openReport('window', "NGT006_DONTHUOC_NHATHUOC", "pdf", par1);
			return;
	    }
	    
		for(var j = 0; j < dsmbp.length; j++){
			var rets = dsmbp[j];
			if(_dinhduong == '1'){
				   var rpcode = "NGT006_DONTUVAN_17DBV01_TT052016_A5";
				   var par = [ {
						name : 'maubenhphamid',
						type : 'String',
						value : rets
					}];
					
				   if(_opts._hospital_id=='951'){
						rpcode = "PHIEUTUVAN_A5_951";
					}
				   openReport('window', rpcode, "pdf", par);
			   //tuyennx_add_start_20191015_L2PT-9849
			   }else if(_opts.ravien == '1'){
				   var par = [ {
						name : 'maubenhphamid',
						type : 'String',
						value : rets
					 }];
				   	 var _par_loai = [rets];						
			 		 var _loaithuoc=0;    								
					 var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
					 if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
						////tuyennx_edit_start_20181113 L2PT-6425 comment do goi ben tren
						var pars = ['PHONG_TUDONG_IN']; 
						var dc_phong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
						var dc_phongs = dc_phong.split(',');
						for(var i=0;i< arr_loaithuoc.length;i++){
							_loaithuoc=arr_loaithuoc[i].LOAI;
							   if(_loaithuoc==3){
									//thuoc dong y --DONTHUOCTHANG_NGOAITRU
								   if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( _opts.phongId, dc_phongs) >= 0){
									   var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
										rpName += $('#lblPATIENTCODE').val(); 
										rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
										rpName += "."+_type; 
										CommonUtil.inPhieu('window', 'NGT020_DONTHUOCTHANG_RAVIEN_NGOAITRU', _type, par,rpName); //L2PT-21948
								   }else{
									   openReport('window', "NGT020_DONTHUOCTHANG_RAVIEN_NGOAITRU", "pdf", par); //L2PT-21948
								   }
								 }else if(_loaithuoc==6){
									 //thuoc huong than 
									 if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( _opts.phongId, dc_phongs) >= 0){
										   var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
											rpName += $('#lblPATIENTCODE').val(); 
											rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
											rpName += "."+_type;  
											CommonUtil.inPhieu('window', 'NGT013_DONTHUOCHUONGTHAN_TT052016_A5', _type, par,rpName);
									   }else{
										   openReport('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par); 
									   }
								 }else if(_loaithuoc==7){
									 //don thuoc gay nghien
									if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( _opts.phongId, dc_phongs) >= 0){
										var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
										rpName += $('#lblPATIENTCODE').val(); 
										rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
										rpName += "."+_type;
										CommonUtil.inPhieu('window', 'NGT013_DONTHUOCGAYNGHIEN_TT052016_A5', _type, par,rpName);
								   }else{
									   openReport('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par);
								   }
								 }
							   	//tuyennx_add_start L2PT-13903
								 else if(_loaithuoc==16 || _loaithuoc==19 ){
									if(_configArr.IN_TACHDON_MP_TPCN == 1){ //L2PT-30631
										if(_loaithuoc==16){
											//don my pham
											if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( _opts.phongId, dc_phongs) >= 0){
												var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
												rpName += $('#lblPATIENTCODE').val(); 
												rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
												rpName += "."+_type;
												CommonUtil.inPhieu('window', 'NGT013_MYPHAM_TT052016_A5', _type, par,rpName);
										   }else{
											   openReport('window', "NGT013_MYPHAM_TT052016_A5", "pdf", par);
										   }
										}
										else{
											//don thuc pham chuc nang
											if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( _opts.phongId, dc_phongs) >= 0){
												var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
												rpName += $('#lblPATIENTCODE').val(); 
												rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
												rpName += "."+_type;
												CommonUtil.inPhieu('window', 'NGT013_TPCN_TT052016_A5', _type, par,rpName);
										   }else{
											   openReport('window', "NGT013_TPCN_TT052016_A5", "pdf", par);
										   }
										}									
									}else{
										 //don my pham, thuc pham chuc nang
										if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( _opts.phongId, dc_phongs) >= 0){
											var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
											rpName += $('#lblPATIENTCODE').val(); 
											rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
											rpName += "."+_type;
											CommonUtil.inPhieu('window', 'NGT013_DONMYPHAM_TPCN_TT052016_A5', _type, par,rpName);
									   }else{
										   openReport('window', "NGT013_DONMYPHAM_TPCN_TT052016_A5", "pdf", par);
									   }
									}
								 }
							   //tuyennx_add_end L2PT-13903
								//tuyennx_add_start L2PT-21559
								 else if(_loaithuoc==-1 ){
									 //don thuoc chuong trinh
									if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( _opts.phongId, dc_phongs) >= 0){
										var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
										rpName += $('#lblPATIENTCODE').val(); 
										rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
										rpName += "."+_type;
										CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5', _type, par,rpName);
								   }else{
									   openReport('window', "NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5", "pdf", par);
								   }
								 }
							   //tuyennx_add_end L2PT-21559
								 else{ //L2PT-18977 L2PT-30499
									 var sql_par1 = [];
									 sql_par1.push({"name":"[0]", value:rets});
									 var _lnmbp = jsonrpc.AjaxJson.getOneValue('KETHUOC.GETLNMBP', sql_par1);
									 if(_lnmbp == 7){
										 var rpcode = "NGT006_DONRAVIEN_17DBV01_TT052016_A5";
										   openReport('window', rpcode, "pdf", par);
									 }else{
										 var rpcode = "NGT006_DONVATTURAVIEN_17DBV01_TT052016_A5";
										   openReport('window', rpcode, "pdf", par);
									 }
									   
								 }
						}
					 }
			   }
			 //tuyennx_add_end_20191015_L2PT-9849
			   else{
				   if(_print){
					   var sql_par1 = [];
					   sql_par1.push({"name":"[0]", value:rets});
						var _lnmbp = jsonrpc.AjaxJson.getOneValue('KETHUOC.GETLNMBP', sql_par1);
					   if($.inArray(_loaitiepnhanid, [1,5]) !== '-1' && (_loainhommaubenhpham_id == "7" || _loainhommaubenhpham_id == '-1' ||_loainhommaubenhpham_id == "8")){
						   		if(_lnmbp == '8'){
						   			_inDonVatTu(rets, _opts.phongId);
						   		}else
						   			_inDonThuoc(rets, _opts.phongId);						   
					   }else if(_indonthuoc_noitru == "1" && (_loainhommaubenhpham_id == "7" || _loainhommaubenhpham_id == '-1' || _loainhommaubenhpham_id == "8")){
						   if(_lnmbp == '8'){
					   			_inDonVatTu(rets, _opts.phongId);
					   		}else
					   			_inDonThuoc(rets, _opts.phongId);	
					   }
					   //L2PT-34418_start
					   else if(_loaitiepnhanid == "3" && _opts.loaikho != 0 && //L2PT-8671 them dieu kien ko in khi ke tu menu Tạo phiếu thuốc từ kho dược
							   (_loainhommaubenhpham_id == "7" || _loainhommaubenhpham_id == '-1' || _loainhommaubenhpham_id == "8")){
						   var _sql_par = [];
							_sql_par.push({
								"name" : "[0]",
								value : _khoaId
							});
							var makhoa = jsonrpc.AjaxJson.getOneValue("NGT.GETMAKHOA", _sql_par);
							var ds_khoa = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH','HIS_KHOA_KHONG_TONGHOP_PHIEULINH');
							var ds_khoas = ds_khoa== undefined ? [] : ds_khoa.replaceAll('\'','').split(',');
							if( $.inArray( makhoa, ds_khoas) >= 0){
								if(_lnmbp == '8'){
						   			_inDonVatTu(rets, _opts.phongId);
						   		}else
						   			_inDonThuoc(rets, _opts.phongId);	
							}
					   }
					 //L2PT-34418_end
				   }	
			   }
		}		
	}
	
	//tuyennx_add_start_L2PT-21290
	function openChidinhDV(dsmbp){
		if(_configArr.KETHUOC_LINHMAU_CDDV == '1'){
			for(var i = 0; i < dsmbp.length; i++){
				var rets = dsmbp[i];
				var sql_par = [];
				sql_par.push({"name":"[0]", value:rets});
				var _checkMau = jsonrpc.AjaxJson.getOneValue('KETHUOC.CHECK.KEMAU', sql_par);
				if(parseInt(_checkMau) >0){
					_check_close_popup = 1;
					paramInput={
							benhnhanid : $("#hidBENHNHANID").val(),
							mabenhnhan : $('#lblPATIENTCODE').val(),
							khambenhid : _khambenhid,
							tiepnhanid : _tiepnhanid,
							hosobenhanid : $("#hidHOSOBENHANID").val(),
							doituongbenhnhanid : _doituongbenhnhanid,
							loaitiepnhanid : _loaitiepnhanid,
							subDeptId : _phongId,
							maubenhpham_linhmau : rets
					};
					var w = $( document ).width()-50;
				    var h = $( document ).height()-50;
					dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5,paramInput,"Tạo phiếu chỉ định dịch vụ",w,h);
					DlgUtil.open("divDlgDichVu");
				}
			}		
		}
	}
	//tuyennx_add_end_L2PT-21290
	
	//tuyennx_add_start tích hợp cổng dữ liệu y tế
	function dayCongBYT(){
		var sql_par=[];
		sql_par.push({"name":"[0]","value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')});
		var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT",sql_par);
		vsothutu = JSON.parse(vsothutu);
		if (vsothutu[0].BYTDAYDL == "1"){
			var _parram = ["1"];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",_parram.join('$'));
			var data_bv = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETTT_BV",[]);
			
			var objCHECKIN = new Object(); 
			objCHECKIN.MA_LK = $("#hidHOSOBENHANID").val();
			objCHECKIN.Sender_Code = _opts.hospital_code;
			objCHECKIN.Sender_Name = "";
			objCHECKIN.Action_Type = "1";				// 0: bắt đầu khám, 1: kết thúc khám
			objCHECKIN.Transaction_Type = "M0001";
			objCHECKIN.MABENHVIEN = data_ar[0].I_U1;
			objCHECKIN.MA_THE = $("#lblMA_BHYT").val();
			objCHECKIN.MA_LOAI_KCB = "1";
			objCHECKIN.TEN_BENH = $("#txtTENCHANDOANICD").val();
			objCHECKIN.MA_BENH = $("#txtMACHANDOANICD").val();
			objCHECKIN.HO_TEN = $("#lblPATIENTNAME").val();
			objCHECKIN.NAM_SINH = $("#lblBIRTHDAY_YEAR").val();
//			objCHECKIN.GIOI_TINH ="2";
//			objCHECKIN.DIA_CHI ="Xã Phú Sơn-Huyện Tân Kỳ-Nghệ An";
//			objCHECKIN.NGAYGIOVAO = "201803121530";
//			objCHECKIN.TINHTRANGVAOVIEN = "1";
			objCHECKIN.NGAYHETTHUOC = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');

			objCHECKIN.NGAYGIORA = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');
			var objHeader = XML_BYT_TaoHeaderKTK(objCHECKIN,"1"); 										// tao doi tuong header; 
			var objIn = XML_BYT_TaoTheCHECKIN(objCHECKIN); 									// tao the 
			var obj3 = XML_BYT_TaoKhung(objHeader, objIn, "1"); 											// tao JSON full => XML

			var resultCongBYT = ajaxSvc.CongDLYTWS.guiTTKTK(
					vsothutu[0].BYTURL
					, data_ar[0].I_U1
					, data_ar[0].I_P1
					, data_ar[0].I_U1						// csytid
					, data_bv[0].MADIAPHUONG							// ma tinh 
					, "1", obj3);		
			var rets = resultCongBYT.split(';');
			if(rets[0] != '0'){
				DlgUtil.showMsg("Lỗi đẩy dữ liệu cổng y tế: "+rets[1]);
				if(vsothutu[0].BYTSTOPCHUCNANG == "1")
					return 1;
			}			
		}
		return 0;
	}
	
	function checkCongLamDungThuocBYT(jsonGridData){
		var sql_par=[];
		sql_par.push({"name":"[0]","value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')});
		var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT",sql_par);
		vsothutu = JSON.parse(vsothutu);
		if (vsothutu[0].BYTDAYDL == "1"){
			var _parram = ["1"];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",_parram.join('$'));
			
			var objCHECKIN = new Object(); 
			objCHECKIN.MA_LK = $("#hidHOSOBENHANID").val();
			objCHECKIN.Sender_Code = _opts.hospital_code;
			objCHECKIN.Sender_Name = "";
			objCHECKIN.Action_Type = "1";				// 0: bắt đầu khám, 1: kết thúc khám
			objCHECKIN.Transaction_Type = "M0001";
			objCHECKIN.MABENHVIEN = data_ar[0].I_U1;
			objCHECKIN.MA_THE = $("#lblMA_BHYT").val();
			objCHECKIN.MA_LOAI_KCB = "1";
			objCHECKIN.TEN_BENH = $("#txtTENCHANDOANICD").val();
			objCHECKIN.MA_BENH = $("#txtMACHANDOANICD").val();
			objCHECKIN.NGAYHETTHUOC = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');
			objCHECKIN.NGAYGIORA = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');			
			var objTTChung = new Object();
			objTTChung.MA_LK = $("#hidMAHOSOBENHAN").val();
			objTTChung.MABENHVIEN = data_ar[0].I_U1;
			objTTChung.MA_THE = $("#lblMA_BHYT").val();
			objTTChung.NGAY_SINH = $("#hidNGAYSINH").val();
			objTTChung.GIOI_TINH = $("#hidGIOITINHID").val();
			
			var objDSThuoc = [];
			for(var i=0;i< jsonGridData.length;i++){
				var objThuoc = new Object();
				objThuoc.STT = i+1;
				objThuoc.MA_THUOC = jsonGridData[i].MA_THUOC;
				objThuoc.MA_NHOM = jsonGridData[i].NHOM_MABHYT_ID;
				objDSThuoc.push(objThuoc);
			}

			var objIn = new Object();
			var objThuoc = new Object();
			objThuoc.CHI_TIET_THUOC = objDSThuoc;
			objIn.THONGTINCHUNG = objTTChung;
			objIn.DSACH_CHI_TIET_THUOC = objThuoc;
			var objHeader = XML_BYT_TaoHeader(objCHECKIN); 										// tao doi tuong header; 
			var obj3 = XML_BYT_TaoKhung(objHeader, objIn, "8"); 											// tao JSON full => XML

			var resultCongBYT = ajaxSvc.CongDLYTWS.lamdungthuoc(
					vsothutu[0].BYTURL
					, data_ar[0].I_U1
					, data_ar[0].I_P1
					, obj3);		
			var rets = resultCongBYT.split(';');
			if(rets[0] != '0'){
				DlgUtil.showMsg("Thông tin check cổng y tế: "+rets[1]);
				if(vsothutu[0].BYTSTOPCHUCNANG == "1")
					return 1;
			}			
		}
		return 0;
	}
	//đẩy cổng bộ y tế với đơn mua ngoài, đơn thuốc nhà thuốc
	function _daybtkd(dsmbp, r_action){	
		var BTKD_DAY_BYT = _configArr.BTKD_DAY_BYT;
		var ds_option = _configArr.BTKD_MA_OPTION_DAY;
		var ds_loaitiepnhan = _configArr.BTKD_LOAITIEPNHAN_DAY;
		var ds_options = ds_option.split(',');

		if(BTKD_DAY_BYT == 1 && (ds_option == '0' || $.inArray( _option, ds_options) >= 0)
				&& (ds_loaitiepnhan.includes(_loaitiepnhanid) )){
			for(var j = 0; j < dsmbp.length; j++){
//				var sql_par = [];
//				sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
//				jsonrpc.AjaxJson.ajaxExecuteQuery("BTKD.UPDATE.MADT", sql_par);
				var ret_maubenhpham_id = dsmbp[j];
				var sql_par1 = [];
				sql_par1.push({"name":"[0]", value:ret_maubenhpham_id});
				var _lnmbp = jsonrpc.AjaxJson.getOneValue('KETHUOC.GETLNMBP', sql_par1);
				if(_lnmbp == '7' || _configArr.BTKD_DAYDON_VT == '1'){
					var objSend = new Object(); 
			   		var objData = new Object(); 
			   		var objHeader = new Object(); 
			   		var objBody = new Object();
			   		var objSeccurity = new Object();
			   		var objDonThuoc = new Object();
			   		var objThongTinBN = new Object();
			   		var objDSChiTietThuoc = new Object(); 
			   		
			   		var BTKD_WS_URL = _configArr.BTKD_WS_URL;
			   		var BTKD_WS_USER = _configArr.BTKD_WS_USER;
			   		var BTKD_WS_PASS = _configArr.BTKD_WS_PASS;
			   		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",[ret_maubenhpham_id].join('$'));
			   		
			   		var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD",[ret_maubenhpham_id].join('$'));
			   		//tao header
			   		objHeader.SENDER_CODE = _opts.hospital_code;
			   		objHeader.SENDER_NAME = data_ar[0].TEN_CSYT;
			   		objHeader.TRANSACTION_TYPE = "M0002";
			   		objHeader.TRANSACTION_NAME = "FTP";
			   		objHeader.TRANSACTION_ID = "";
			   		objHeader.PROVIDER ="12";
			   		if(r_action == "Upd")
			   			objHeader.ACTION_TYPE = "1";
			   		else
			   			objHeader.ACTION_TYPE = "0";
			   		//tao don thuoc
			   		objDonThuoc.DON_THUOC_ID =data_btkd[0].MAUBENHPHAMID;
			   		objDonThuoc.MA_DON_THUOC =_opts.hospital_code+"."+ data_btkd[0].MA_DON_THUOC;
			   		objDonThuoc.MA_LUOT_KHAM =data_btkd[0].HOSOBENHANID;
			   		objDonThuoc.LOAI_DON_VI ="1";
			   		objDonThuoc.MA_XAC_THUC ="";
			   		objDonThuoc.MA_DON_VI =_opts.hospital_code;
			   		objDonThuoc.MA_DINH_DANH =data_btkd[0].HSSK_MADD;
			   		objDonThuoc.HO_TEN_BN =data_btkd[0].TENBENHNHAN;
			   		objDonThuoc.NGAY_SINH =data_btkd[0].NGAYSINH;
			   		objDonThuoc.GIOI_TINH =data_btkd[0].GIOITINHID;
			   		objDonThuoc.MA_THE =data_btkd[0].MA_BHYT;
			   		objDonThuoc.MA_BENH =data_btkd[0].MACHANDOAN;
			   		objDonThuoc.TEN_BENH =data_btkd[0].CHANDOAN;
			   		objDonThuoc.SD_TU_NGAY =data_btkd[0].NGAYMAUBENHPHAM_SUDUNG;
			   		objDonThuoc.SD_DEN_NGAY =data_btkd[0].SD_DEN_NGAY;
			   		objDonThuoc.HD_SD =data_btkd[0].LOIDANBACSI;
			   		objDonThuoc.SO_THANG =data_btkd[0].SLTHANG;
			   		objDonThuoc.NGAY_CAP =data_btkd[0].NGAYMAUBENHPHAM;
			   		objDonThuoc.MA_BAC_SY =data_btkd[0].MA_BAC_SI;
			   		objDonThuoc.TEN_BAC_SY =data_btkd[0].OFFICER_NAME;
			   		objDonThuoc.SO_GPHN ="";
			   		objDonThuoc.MA_KHOA =data_btkd[0].KHOAID;
			   		objDonThuoc.NHA_THUOC_ID ="";
			   		objDonThuoc.MA_NHA_THUOC ="";
			   		objDonThuoc.TEN_NHA_THUOC ="";
			   		objDonThuoc.TEN_DUOC_SY ="";
			   		objDonThuoc.NGAY_BAN ="";
			   		objDonThuoc.MA_TINH =data_btkd[0].MA_TINH;
			   		
			   	    //tao obj benh nhan
			   		objThongTinBN.MABN = data_btkd[0].MABENHNHAN;
			   		objThongTinBN.MATINH_KHAISINH = data_btkd[0].MATINH_KHAISINH;
			   		objThongTinBN.SOCMND = data_btkd[0].SOCMTND;
			   		objThongTinBN.NGAYCAP = data_btkd[0].NGAYCAPCMND;
			   		objThongTinBN.NOICAP = "";
			   		objThongTinBN.DIACHI_THUONGTRU = "";
			   		objThongTinBN.MATINH_THUONGTRU = "";
			   		objThongTinBN.MAHUYEN_THUONGTRU = "";
			   		objThongTinBN.MAXA_THUONGTRU = "";
			   		objThongTinBN.MATHONXOM_THUONGTRU = "";
			   		objThongTinBN.DIACHI_HIENTAI = data_btkd[0].DIACHI;
			   		objThongTinBN.MATINH_HIENTAI = data_btkd[0].MATINH_KHAISINH;
			   		objThongTinBN.MAHUYEN_HIENTAI = data_btkd[0].MAHUYEN_HIENTAI;
			   		objThongTinBN.MAXA_HIENTAI = data_btkd[0].MAXA_HIENTAI;
			   		objThongTinBN.MATHONXOM_HIENTAI = "";
			   		objThongTinBN.DIENTHOAI_CD = data_btkd[0].SDTBENHNHAN;
			   		objThongTinBN.DIENTHOAI_DD = "";
			   		objThongTinBN.EMAIL = "";
			   		
			   		
			   		var objChiTietThuoc = [];
					for(var i=0;i< data_btkd.length;i++){
						var objThuoc = new Object();
						objThuoc.DON_THUOC_ID = data_btkd[i].MAUBENHPHAMID;
						objThuoc.STT = data_btkd[i].THUTU;
						 if(_opts.option=='02D010' || _opts.option=='02D017')
							 objThuoc.CHI_TIET_ID = data_btkd[i].DICHVUKHAMBENHID;
						 else
							 objThuoc.CHI_TIET_ID = "";
						objThuoc.CHI_TIET_ID_CLIENT = data_btkd[i].DICHVUKHAMBENHID;
						objThuoc.MA_NHOM = "";
						objThuoc.SO_DANG_KY = data_btkd[i].SODANGKY;
						objThuoc.MA_THUOC = data_btkd[i].MA;
						objThuoc.TEN_THUOC = data_btkd[i].TEN;
						objThuoc.DON_VI_TINH = data_btkd[i].TEN_DVT;
						objThuoc.HAM_LUONG = data_btkd[i].LIEULUONG;
						objThuoc.DUONG_DUNG = data_btkd[i].DUONGDUNG;
						objThuoc.LIEU_DUNG = data_btkd[i].LIEUDUNG;
						objThuoc.DONG_GOI = data_btkd[i].DONGGOI;
						objThuoc.SO_LUONG = data_btkd[i].SOLUONG;
						objThuoc.DON_GIA = data_btkd[i].TIEN_CHITRA;
						objThuoc.TYLE_TT = "100";
						objThuoc.MUC_HUONG = "0";
						objThuoc.THANH_TIEN = data_btkd[i].THANH_TIEN;
						objThuoc.T_NGUON_KHAC = "0";
						objThuoc.T_BNTT = "0";
						objThuoc.GHI_CHU = data_btkd[i].GHI_CHU;
						objChiTietThuoc.push(objThuoc);
		
					}
					objDSChiTietThuoc.CHI_TIET_THUOC = objChiTietThuoc;
					
			   		objBody.DON_THUOC = objDonThuoc;
			   		objBody.THONGTINBENHNHAN = objThongTinBN;
			   		objBody.DSACH_CHI_TIET_THUOC = objDSChiTietThuoc;
			   		
			   		objData.HEADER = objHeader;
			   		objData.BODY = objBody;
			   		objData.SECURITY = objSeccurity;
			   		objData.HEADER = objHeader;
			   		objSend.DATA = objData;
			   	
					var x2js = new X2JS();
					objSend = JSON.stringify(objSend);
					var obj =  x2js.json2xml_str($.parseJSON(objSend));
					obj = obj.replace(/&lt;/g,'<').replace(/&gt;/g,'>').replace(/&amp;/g,'&');
					obj = btoa(unescape(encodeURIComponent(obj)));//atob(obj);
					
					//gui lay ma dinh danh va MK
					var resultCongBTKD = ajaxSvc.CongBTKDWS.tiepNhanDonThuoc_new(_company_id
							, BTKD_WS_USER
							, BTKD_WS_PASS,
							obj); 	
					var rs = resultCongBTKD.split(';');
					if(rs[0] != 0){
						DlgUtil.showMsg("Lỗi đẩy dữ liệu bán thuốc kê đơn cổng bộ y tế: " +rs[1]);
						setTimeout(function(){
							 }, 2000);
						
						//return 1;
					}
					
					else{//L2PT-16266
						var sql_par = [];
						sql_par.push({
							"name" : "[0]",
							"value" : ret_maubenhpham_id
						});				
						jsonrpc.AjaxJson.execute("NGT.UD.BTKD", sql_par);
					}
				}
			}
		}
	}
	//đẩy cổng bộ y tế với đơn mua ngoài, đơn thuốc nhà thuốc
	function _xacnhanbanthuoc(ret_maubenhpham_id, r_action){	
		var BTKD_DAY_BYT = _configArr.BTKD_DAY_BYT;
		if(BTKD_DAY_BYT == 1 && r_action !== "Upd"){
	   		var objSend = new Object(); 
	   		var objData = new Object(); 
	   		var objHeader = new Object(); 
	   		var objBody = new Object();
	   		var objSeccurity = new Object();
	   		var objDonThuoc = new Object();
	   		var objDSChiTietThuoc = new Object(); 
	   		
	   		var BTKD_WS_URL = _configArr.BTKD_WS_URL;
	   		var BTKD_WS_USER = _configArr.BTKD_WS_USER;
	   		var BTKD_WS_PASS = _configArr.BTKD_WS_PASS;
	   		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",[ret_maubenhpham_id].join('$'));
	   		
	   		var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD",[ret_maubenhpham_id].join('$'));
	   		//tao header
	   		objHeader.SENDER_CODE = _opts.hospital_code;
	   		objHeader.SENDER_NAME = data_ar[0].TEN_CSYT;
	   		objHeader.TRANSACTION_TYPE = "M0001";
	   		objHeader.TRANSACTION_NAME = "FTP";
	   		objHeader.TRANSACTION_ID = "";
	   		objHeader.REQUEST_ID ="";
	   		objHeader.ACTION_TYPE = "1";
	   		//tao don thuoc
	   		objDonThuoc.LUOT_BAN_ID ="";
	   		objDonThuoc.MA_DON_THUOC =_opts.hospital_code+"."+ data_btkd[0].MA_DON_THUOC;
	   		objDonThuoc.NHA_THUOC_ID ="";
	   		objDonThuoc.MA_NHA_THUOC =_opts.hospital_code;
	   		objDonThuoc.TEN_NHA_THUOC ="";
	   		objDonThuoc.TEN_DUOC_SY ="";
	   		objDonThuoc.NGAY_BAN =jsonrpc.AjaxJson.getSystemDate('YYYY-MM-DD');
	   		objDonThuoc.TINH_TRANG =3;
	   		objDonThuoc.SO_PHIEU =data_btkd[0].SO_PHIEU;
	   		
	   		var objChiTietThuoc = [];
			for(var i=0;i< data_btkd.length;i++){
				var objThuoc = new Object();
				objThuoc.LUOT_BAN_ID = "";
				objThuoc.CHI_TIET_ID = data_btkd[i].DICHVUKHAMBENHID;
				objThuoc.CHI_TIET_ID_CLIENT = data_btkd[i].DICHVUKHAMBENHID;
				objThuoc.DON_THUOC_ID = data_btkd[i].MAUBENHPHAMID;
				objThuoc.SO_DANG_KY = data_btkd[i].SODANGKY;
				objThuoc.MA_THUOC = data_btkd[i].MA;
				objThuoc.TEN_THUOC = data_btkd[i].TEN;
				objThuoc.DON_VI_TINH = data_btkd[i].TEN_DVT;
				objThuoc.HAM_LUONG = data_btkd[i].LIEULUONG;
				objThuoc.DUONG_DUNG = data_btkd[i].DUONGDUNG;
				objThuoc.LIEU_DUNG = data_btkd[i].LIEUDUNG;
				objThuoc.DONG_GOI = data_btkd[i].DONGGOI;
				objThuoc.SO_LUONG = data_btkd[i].SOLUONG;
				objThuoc.SL_TUONG_DUONG = data_btkd[i].SOLUONG;
				objThuoc.DON_GIA = data_btkd[i].TIEN_CHITRA;
				objThuoc.THANH_TIEN = data_btkd[i].THANH_TIEN;
				objThuoc.GHI_CHU = data_btkd[i].GHI_CHU;
				objThuoc.HAN_DUNG = data_btkd[i].HAN_DUNG;
				objThuoc.SO_LO = data_btkd[i].SO_LO;
				
				objChiTietThuoc.push(objThuoc);

			}
			objDSChiTietThuoc.CHI_TIET_THUOC = objChiTietThuoc;
			
	   		objBody.DON_THUOC = objDonThuoc;
	   		objBody.DSACH_CHI_TIET_THUOC = objDSChiTietThuoc;
	   		
	   		objData.HEADER = objHeader;
	   		objData.BODY = objBody;
	   		objData.SECURITY = objSeccurity;
	   		objData.HEADER = objHeader;
	   		objSend.DATA = objData;
	   		
	   		
	   	
			var x2js = new X2JS();
			objSend = JSON.stringify(objSend);
			var obj =  x2js.json2xml_str($.parseJSON(objSend));
			obj = obj.replace(/&lt;/g,'<').replace(/&gt;/g,'>').replace(/&amp;/g,'&');
			obj = btoa(unescape(encodeURIComponent(obj)));//atob(obj);
			
			//gui lay ma dinh danh va MK
			var resultCongBTKD = ajaxSvc.CongBTKDWS.xacNhanBanThuoc_new(_company_id
					, BTKD_WS_USER
					, BTKD_WS_PASS,
					obj); 	
			var rs = resultCongBTKD.includes("Gửi thông tin thành công");
			if(!rs){
				DlgUtil.showMsg("Lỗi đẩy dữ liệu bán thuốc kê đơn cổng bộ y tế");
				setTimeout(function(){
					 }, 2000);
				
				//return 1;
			}
		}
	}
	//tuyennx_add_end
	//tuyennx_add_L2PT-16981
	function _inDonVatTu(ret_maubenhpham_id, phongid){	
		if(_configArr.TUDONGINDONVT == '1')
			return;
		var _type = 'pdf';        	
    	var pars = ['HIS_FILEEXPORT_TYPE']; 
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
		if(data_ar != '-1'){
			_type = data_ar;
		}
    	
	    var par = [ {
			name : 'maubenhphamid',
			type : 'String',
			value : ret_maubenhpham_id
		}];
	  //tuyennx_add_start_20181113 L2HOTRO-11946
	    if(_dichvucha_id != "")
			return;
	  //tuyennx_add_end_20181113
	    
	   //tuyennx_add_start_20181113 L2PT-10958
	    var sql_par = [];
		sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
		var _checkTutruc = jsonrpc.AjaxJson.getOneValue('KETHUOC.TUTRUC', sql_par);
		if(parseInt(_checkTutruc) >0)
			return;
	  //tuyennx_add_end_20181113
	   
	    
	  //tuyennx_add_start_20181113 L2HOTRO-11841
	    if(_opts.khamchinhphu!='1' && _opts.khamchinhphu!= undefined && _configArr.KETHUOC_TACHDON_PHU == 0){ 
	    	 if(_configArr.INLUON_DONTHUOC == '1')
				 CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5_KP', 'pdf', par, null,true,false);
	         else
	        	 openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_KP", "pdf", par); 
	   		return;
	    }
	  //tuyennx_add_end_20181113
	    
	    //tuyennx_add_start_20181113 L2PT-6425
	    var pars = ['PHONG_TUDONG_IN']; 
		var dc_phong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
		var dc_phongs = dc_phong.split(',');
	    if(_option == '02D011' && _configArr.IN_TACHDONMUA_NGOAI == '1'){ 
	    	 if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
				   var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
					rpName += $('#lblPATIENTCODE').val(); 
					rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
					rpName += "."+_type; 
					CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_MUANGOAI_A5', _type, par,rpName);
			   }else{
				   CommonUtil.openReportGet('window', "NGT006_DONTHUOC_MUANGOAI_A5", "pdf", par);
				   //openReport('window', "NGT006_DONTHUOC_MUANGOAI_A5", "pdf", par);
			   }
	   		return;
	    }
	  //tuyennx_add_end_20181113	    
	    if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
			var rpName = "VNPTHIS_IN_PHIEU_VATTU";
			rpName += $('#lblPATIENTCODE').val(); 
			rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
			rpName += "."+_type; 
			CommonUtil.inPhieu('window', 'PHIEU_VATTU_A4', _type, par,rpName);
	   }else{
		   openReport('window', "PHIEU_VATTU_A4", "pdf", par);
	   } 
	}
	function _inDonThuoc(ret_maubenhpham_id, phongid){	
			var _type = 'pdf';        	
	    	var pars = ['HIS_FILEEXPORT_TYPE']; 
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
			if(data_ar != '-1'){
				_type = data_ar;
			}
	    	
		    var par = [ {
				name : 'maubenhphamid',
				type : 'String',
				value : ret_maubenhpham_id
			}];
		  //tuyennx_add_start_20181113 L2HOTRO-11946
		    if(_dichvucha_id != "")
				return;
		  //tuyennx_add_end_20181113
		    
		   //tuyennx_add_start_20181113 L2PT-10958
		    var sql_par = [];
			sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
			var _checkTutruc = jsonrpc.AjaxJson.getOneValue('KETHUOC.TUTRUC', sql_par);
			if(parseInt(_checkTutruc) >0)
				return;
		  //tuyennx_add_end_20181113
		   
		    
		  //tuyennx_add_start_20181113 L2HOTRO-11841
		    if(_opts.khamchinhphu!='1' && _opts.khamchinhphu!= undefined && _configArr.KETHUOC_TACHDON_PHU == 0){ 
		    	 if(_configArr.INLUON_DONTHUOC == '1')
					 CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5_KP_1014', 'pdf', par, null,true,false);
		         else
		        	 openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_KP_1014", "pdf", par); 
		   		return;
		    }
		  //tuyennx_add_end_20181113
		    
		    //tuyennx_add_start_20181113 L2PT-6425
		    var pars = ['PHONG_TUDONG_IN']; 
			var dc_phong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
			var dc_phongs = dc_phong.split(',');
		    if(_option == '02D011' && _configArr.IN_TACHDONMUA_NGOAI == '1'){ 
		    	 if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
					   var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
						rpName += $('#lblPATIENTCODE').val(); 
						rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
						rpName += "."+_type; 
						CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_MUANGOAI_A5', _type, par,rpName);
				   }else{
					   CommonUtil.openReportGet('window', "NGT006_DONTHUOC_MUANGOAI_A5", "pdf", par);
					   //openReport('window', "NGT006_DONTHUOC_MUANGOAI_A5", "pdf", par);
				   }
		   		return;
		    }
		  //tuyennx_add_end_20181113
		    
		    	 if(_opts._hospital_id=='913'){
				    	openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_913", "pdf", par);
			      }else{
		 		 //lay loai thuoc
		 		 var _par_loai = [ret_maubenhpham_id];						
		 		 var _loaithuoc=0;    								
				 var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
				 if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
					////tuyennx_edit_start_20181113 L2PT-6425 comment do goi ben tren
//					var pars = ['PHONG_TUDONG_IN']; 
//					var dc_phong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
//					var dc_phongs = dc_phong.split(',');
					for(var i=0;i< arr_loaithuoc.length;i++){
						_loaithuoc=arr_loaithuoc[i].LOAI;
						   if(_loaithuoc==3){
								//thuoc dong y --DONTHUOCTHANG_NGOAITRU
							   if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
								   var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
									rpName += $('#lblPATIENTCODE').val(); 
									rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
									rpName += "."+_type; 
									CommonUtil.inPhieu('window', 'NGT020_DONTHUOCTHANGNGOAITRU', _type, par,rpName);
							   }else{
								   openReport('window', "NGT020_DONTHUOCTHANGNGOAITRU", "pdf", par);
							   }
							 }else if(_loaithuoc==6){
								 //thuoc huong than 
								 if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
									   var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
										rpName += $('#lblPATIENTCODE').val(); 
										rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
										rpName += "."+_type;  
										CommonUtil.inPhieu('window', 'NGT013_DONTHUOCHUONGTHAN_TT052016_A5', _type, par,rpName);
								   }else{
									   openReport('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par); 
								   }
							 }else if(_loaithuoc==7){
								 //don thuoc gay nghien
								if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
									var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
									rpName += $('#lblPATIENTCODE').val(); 
									rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
									rpName += "."+_type;
									CommonUtil.inPhieu('window', 'NGT013_DONTHUOCGAYNGHIEN_TT052016_A5', _type, par,rpName);
							   }else{
								   openReport('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par);
							   }
							 }
						   //tuyennx_add_start L2PT-13903
							 else if(_loaithuoc==16 || _loaithuoc==19 ){
								 if(_configArr.IN_TACHDON_MP_TPCN == 1){ //L2PT-30631
										if(_loaithuoc==16){
											//don my pham
											if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
												var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
												rpName += $('#lblPATIENTCODE').val(); 
												rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
												rpName += "."+_type;
												CommonUtil.inPhieu('window', 'NGT013_MYPHAM_TT052016_A5', _type, par,rpName);
										   }else{
											   openReport('window', "NGT013_MYPHAM_TT052016_A5", "pdf", par);
										   }
										}
										else{
											//don thuc pham chuc nang
											if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
												var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
												rpName += $('#lblPATIENTCODE').val(); 
												rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
												rpName += "."+_type;
												CommonUtil.inPhieu('window', 'NGT013_TPCN_TT052016_A5', _type, par,rpName);
										   }else{
											   openReport('window', "NGT013_TPCN_TT052016_A5", "pdf", par);
										   }
										}									
									}else{
										 //don my pham, thuc pham chuc nang
										if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
											var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
											rpName += $('#lblPATIENTCODE').val(); 
											rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
											rpName += "."+_type;
											CommonUtil.inPhieu('window', 'NGT013_DONMYPHAM_TPCN_TT052016_A5', _type, par,rpName);
									   }else{
										   openReport('window', "NGT013_DONMYPHAM_TPCN_TT052016_A5", "pdf", par);
									   }
									}
							 }
						 //tuyennx_add_end L2PT-13903
						 //tuyennx_add_start L2PT-21559
							 else if(_loaithuoc==-1 ){
								//don my pham, thuc pham chuc nang
								if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
									var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
									rpName += $('#lblPATIENTCODE').val(); 
									rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
									rpName += "."+_type;
									CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5', _type, par,rpName);
							   }else{
								   openReport('window', "NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5", "pdf", par);
							   }
							 }
						 //tuyennx_add_end L2PT-21559
							 else{
								 // don thuoc 1014
								 if(_opts._hospital_id == '1014'){								 
									 	var par = [ {
											name : 'maubenhphamid',
											type : 'String',
											value : ret_maubenhpham_id
										 }]; 
									     var _khothuoc=0;
										 var arr_khothuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC",_par_loai.join('$'));
										 if (arr_khothuoc != null && arr_khothuoc.length > 0) {
											 for(var i2=0;i2< arr_khothuoc.length;i2++){
												 _khothuoc=arr_khothuoc[i2].KHOTHUOCID;
												 if(_khothuoc==1){
													 //tuyennx_edit_start_20181119 L2HOTRO-12491
													 if(_configArr.INLUON_DONTHUOC == '1')
														 CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5_NGT_1014', 'pdf', par, null,true,false);
											         else
											        	 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_NGT_1014", "pdf", par); 
												 }
												 else
													 if(_configArr.INLUON_DONTHUOC == '1')
														 CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5_1014', 'pdf', par, null,true,false);
											         else
											        	 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_1014", "pdf", par);
												 //tuyennx_edit_end_20181119 
											 }
										 }								     							 
								   }
								 // don thuoc 1077
								 else if(_opts._hospital_id == '1077'){								 
									 	var par = [ {
											name : 'maubenhphamid',
											type : 'String',
											value : ret_maubenhpham_id
										 }]; 
									     var _khothuoc=0;
										 var arr_khothuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC",_par_loai.join('$'));
										 if (arr_khothuoc != null && arr_khothuoc.length > 0) {
											 for(var i2=0;i2< arr_khothuoc.length;i2++){
												 _khothuoc=arr_khothuoc[i2].KHOTHUOCID;
												 if(_khothuoc==1){
													 //tuyennx_edit_start_20181119 L2HOTRO-12491
													 if(_configArr.INLUON_DONTHUOC == '1')
														 CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5_MN', 'pdf', par, null,true,false);
											         else
											        	 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_MN", "pdf", par); 
												 }
												 else
													 if(_configArr.INLUON_DONTHUOC == '1')
														 CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5_BH', 'pdf', par, null,true,false);
											         else
											        	 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_BH", "pdf", par);
												 //tuyennx_edit_end_20181119 
											 }
										 }								     							 
								   }
								 else if( _opts._hospital_id=='944'){
									 var _khothuoc=0;
									 var arr_khothuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC",_par_loai.join('$'));
									 if (arr_khothuoc != null && arr_khothuoc.length > 0) {
										 for(var i2=0;i2< arr_khothuoc.length;i2++){
											 _khothuoc=arr_khothuoc[i2].KHOTHUOCID;
											 if(_khothuoc==1){
												 if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
														var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
														rpName += $('#lblPATIENTCODE').val(); 
														rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
														rpName += "."+_type; 
														CommonUtil.inPhieu('window', 'NGT006_DONTHUOC1L_17DBV01_TT052016_A5_944', _type, par,rpName);
												   }else{
													   openReport('window', "NGT006_DONTHUOC1L_17DBV01_TT052016_A5_944", "pdf", par);
												   } 
											 }
											 else {
												 if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
														var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
														rpName += $('#lblPATIENTCODE').val(); 
														rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
														rpName += "."+_type; 
														//tuyennx_edit_start_20180813 HISL2TK-1159
														if(_opts._hospital_id == '919' && _doituongbenhnhanid==1){
															CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_3L17DBV01_TT052016_A5_919', _type, par,rpName);
														}
														else CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5', _type, par,rpName);
														//tuyennx_edit_end_20180813
												   }else{
													 //tuyennx_edit_start_20180813 HISL2TK-1159
														if(_opts._hospital_id == '919' && _doituongbenhnhanid==1){
															openReport('window', "NGT006_DONTHUOC_3L17DBV01_TT052016_A5_919", "pdf", par);
														}
														else openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
														//tuyennx_edit_end_20180813
												   }
											 }
										 }
									 }
								}
								//tuyennx_add_start_20180813 L2PT-5527
								//sua lai don thuoc mua ngoai chỉ in 1 liên cho bệnh viện bưu điện
									else if (_opts._hospital_id=='965'){
										 var _khothuoc=0;
										 var arr_khothuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC",_par_loai.join('$'));
										 if (arr_khothuoc != null && arr_khothuoc.length > 0) {
											 for(var i2=0;i2< arr_khothuoc.length;i2++){
												 _khothuoc=arr_khothuoc[i2].KHOTHUOCID;
												 if(_khothuoc==1){
													 if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
															var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
															rpName += $('#lblPATIENTCODE').val(); 
															rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
															rpName += "."+_type; 
															CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_MUANGOAI_A5_965', _type, par,rpName);
													   }else{
														   openReport('window', "NGT006_DONTHUOC_MUANGOAI_A5_965", "pdf", par);
													   } 
												 }
												 else{
													 if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
															var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
															rpName += $('#lblPATIENTCODE').val(); 
															rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
															rpName += "."+_type; 
															CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5_965', _type, par,rpName);
													   }else{
														   openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_965", "pdf", par);
													   } 
												 } 	
											 }
										 }								
									}
								//tuyennx_add_end_20180813 L2PT-5527
									else{
								 if(_tudongindt == '1' || _configArr.KETHUOC_TAI_FILE == '1'|| $.inArray( phongid, dc_phongs) >= 0){
										var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
										rpName += $('#lblPATIENTCODE').val(); 
										rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
										rpName += "."+_type; 
										//tuyennx_edit_start_20180813 HISL2TK-1159
										if(_opts._hospital_id == '919' && _doituongbenhnhanid==1){
											CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_3L17DBV01_TT052016_A5_919', _type, par,rpName);
										}
										else{ //L2PT-16403
//											var KETHUOC_MAKHO_NGUONCT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KETHUOC_MAKHO_NGUONCT');
//											var _ds_kho = KETHUOC_MAKHO_NGUONCT.split(',');
//											var sql_par = [];
//											sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
//											var makho = jsonrpc.AjaxJson.getOneValue('KETHUOC.THUOCCT', sql_par);
//											if($.inArray( makho, _ds_kho) >= 0 )
//												CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5', _type, par,rpName);
//											else
												CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5', _type, par,rpName);
										}
											
										//tuyennx_edit_end_20180813
								   }else{
									   //tuyennx_edit_start_20180813 HISL2TK-1159
										if(_opts._hospital_id == '919' && _doituongbenhnhanid==1){
											openReport('window', "NGT006_DONTHUOC_3L17DBV01_TT052016_A5_919", "pdf", par);
										}
										else{ //L2PT-16403
//											var KETHUOC_MAKHO_NGUONCT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KETHUOC_MAKHO_NGUONCT');
//											var _ds_kho = KETHUOC_MAKHO_NGUONCT.split(',');
//											var sql_par = [];
//											sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
//											var makho = jsonrpc.AjaxJson.getOneValue('KETHUOC.THUOCCT', sql_par);
//											if($.inArray( makho, _ds_kho) >= 0 )
//												openReport('window', "NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5", "pdf", par);
//											else
												openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
										}
										//tuyennx_edit_end_20180813
								   }
								}
							 }  
					}
					//tuyennx_edit_start_20180813 L2HOTRO-11735
					 if(_opts._hospital_id == '902' || _opts._hospital_id == '996'){
						//tuyennx_edit_end_20180813 
						 if($('#chkCapPhieuHenKham').is(':checked')){
						     var par = [ {
								name : 'khambenhid',
								type : 'String',
								value : _khambenhid
							 }];
						     if(_tudongindt == '1'){
						    	var rpName = "VNPTHIS_IN_A4_DONTHUOC_";
								rpName += $('#lblPATIENTCODE').val(); 
								rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
								rpName += "."+_type; 
								CommonUtil.inPhieu('window', 'NGT014_GIAYHENKHAMLAI_TT402015_A4', _type, par,rpName);
						     }else{
						    	 openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par); 
						     }
						 }
					   }
					 }
				 }  
		   
		    //tuyennx_add_end_20181113 L2HOTRO-11841
		    
		    //L2PT-30303	 
		    if(_configArr.KETHUOC_INPHIEULINH_NGT == '1' && _loaitiepnhanid == '1'){
		    	var sql_par = [];
				sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
				var _nhapxuatId = jsonrpc.AjaxJson.getOneValue('GET.NHAPXUATID', sql_par);
				if(_nhapxuatId){
					var par = [ 
				   				{
				   				name : 'nhapxuatid',//nhapxuatid
				   				type : 'String',
				   				value : _nhapxuatId
				   			}];
				   			openReport('window', 'DUC004_PHIEUCAPTHUOCNGOAITRU',
				   					'pdf', par);
				}
		    }
	    }	

	/*param	: _itype
	 * 		= 1 -> Search theo ten thuoc
	 * 		= 0 -> Search theo hoat chat
	 * */	
	function loadComboGrid(_srchType){
		var _col = "";
		var _sql = "";
		var _sql_par=[];
		if(_option == '02D011'){//Mua ngoai
			if(_srchType == 1)
				_sql = _gridComboMuaNgoai;
			else
				_sql = _gridComboMuaNgoai_HoatChat;
			//tuyennx_edit_start_L2PT-34608
			var _jsonThuoc = new Object();
			_jsonThuoc.LOAI = _opts.dongy;
			_jsonThuoc.DINHDUONG  = _dinhduong + "";
			_sql_par.push({"name":"[0]","value":JSON.stringify(_jsonThuoc)}); 
			_col="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN_THUOC,20,0,f,l;Nồng độ,NONGDO,10,0,f,c;Hoạt chất,HOATCHAT,15,0,f,l;Hàm lượng,LIEULUONG,10,0,f,l;Đơn vị,TEN_DVT,10,0,f,l;" +
			"Mã"+_lbl_text+ _configArr.KETHUOC_HEADER_MN;
		}else if(_option == '02D014' || _option == '02D016'){//Phieu tra thuoc
			_sql = _gridComboDonThuoc_TRA;

			_sql_par.push({"name":"[0]","value":_khambenhid},
						{"name":"[1]","value":_company_id},
						{"name":"[2]","value":_loainhommaubenhpham_id},
						{"name":"[3]","value":_phieutraid}
			 );
			_col="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN_THUOC,20,0,f,l;Hoạt chất,HOATCHAT,10,0,f,c;Liều lượng,LIEULUONG,10,0,f,l;" +
					"Đơn vị,TEN_DVT,5,0,f,l;Mã"+_lbl_text + _configArr.HEADER_KEDONTHUOC; 
		}else if(_option == '02D017' || _option == '02D018'){//Phieu tra thuoc
			_col="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên Tên vị thuốc YHCT,TEN_THUOC,25,0,f,l;Đơn vị,TEN_DVT,10,0,f,l;" +
					"Mã"+_lbl_text+",MA_THUOC,15,0,f,l;SL khả dụng,SLKHADUNG,10,0,f,l;Giá DV,GIA_BAN,12,0,f,r;Hạn sử dụng,HANSUDUNG,12,0,f,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;" +
					"TENKHO,TENKHO,0,0,t,c;KHOID,KHOID,0,0,t,c;TUONGTACTHUOC,TUONGTACTHUOC,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;" +
					"GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;TYLEBHYT_TVT,TYLEBHYT_TVT,0,0,t,c;" +
					"DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;KHO_THUOCID,KHO_THUOCID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l"; // manhnh 05/10/2018 upd
			if(_srchType == 1)
				_sql = "NTU02D010.131";
			else
				//_sql = "NTU02D010.14";
				_sql = "NTU02D010.131";
			//_kho_id = $('#cboMA_KHO').val();
			//_ds_khoid = _kho_id;
			_loaithuocid = $('#cboLOAITHUOC').val();
			//tuyennx_add_start_L2PT-17166
			var _type ="0";
			if ($('#chkChiTimTheoTen').is(":checked"))
				_type ="1";
			//tuyennx_add_end_L2PT-17166
			
			//tuyennx_add_start_L2PT-18176
			var ds_loaitn = _configArr.KETHUOC_LOAITN_THEOPHONG;
			var ds_loaitns = ds_loaitn== undefined ? [] : ds_loaitn.split(',');
			var thuoc_theo_phong = "";	
			if($.inArray( _loaitiepnhanid, ds_loaitns) >= 0)
				thuoc_theo_phong = _phongId;
			//tuyennx_add_end_L2PT-18176
			
			//tuyennx_edit_start_L2PT-34608
			var _jsonThuoc = new Object();
			_jsonThuoc.TYPE = _type;
			_jsonThuoc.DINHDUONG  = _dinhduong + "";
			_sql_par.push(
					{"name":"[0]","value":_ds_khoid},				
					{"name":"[1]","value":_loainhommaubenhpham_id},
					{"name":"[2]","value":_loaithuocid},
					{"name":"[3]","value": thuoc_theo_phong},//L2PT-7766 L2PT-18176
					{"name":"[4]","value":JSON.stringify(_jsonThuoc)} //L2PT-17166
			);
			//tuyennx_edit_end_L2PT-34608
		}
		else{
			//tuyennx_edit_start_20181122 L2HOTRO-12687 L2PT-590 chỉnh lại độ rộng các cột cho hợp lý
			_col="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN_THUOC,20,0,f,l;Hoạt chất,HOATCHAT,10,0,f,c;Liều lượng,LIEULUONG,10,0,f,l;" +
					"Đơn vị,TEN_DVT,5,0,f,l;Mã"+_lbl_text + _configArr.HEADER_KEDONTHUOC; //OldValue,OLDVALUE,0,0,f,t,0	// manhnh 05/10/2018 upd	
			//tuyennx_edit_end_20181122
			//_kho_id = $('#cboMA_KHO').val();
			_loaithuocid = $('#cboLOAITHUOC').val();
			//Ko cho ke thuoc da co trong danh sach
			if(_srchType == 1)
				_sql = "NTU02D010.131";
			else
				//_sql = "NTU02D010.14";
				_sql = "NTU02D010.131";
			
			if(_kechungthuocvt == '1'
				&&  _configArr.KETHUOC_CHUNGVT_DOITUONG.includes(_loaitiepnhanid)){ //L2PT-23998
				_loainhommaubenhpham_id = '-1';
			}
			//_ds_khoid = _kho_id;
			//tuyennx_add_start_L2PT-17166
			var _type ="0";
			if ($('#chkChiTimTheoTen').is(":checked"))
				_type ="1";
			//tuyennx_add_end_L2PT-17166
			
			//tuyennx_add_start_L2PT-18176
			var ds_loaitn = _configArr.KETHUOC_LOAITN_THEOPHONG;
			var ds_loaitns = ds_loaitn== undefined ? [] : ds_loaitn.split(',');
			var thuoc_theo_phong = "";	
			if($.inArray( _loaitiepnhanid, ds_loaitns) >= 0)
				thuoc_theo_phong = _phongId;
			//tuyennx_add_end_L2PT-18176
			
			//tuyennx_edit_start_L2PT-34608
			var _jsonThuoc = new Object();
			_jsonThuoc.TYPE = _type;
			_jsonThuoc.DINHDUONG  = _dinhduong + "";
			_sql_par.push(
					{"name":"[0]","value":_ds_khoid},				
					{"name":"[1]","value":_loainhommaubenhpham_id},
					{"name":"[2]","value":_loaithuocid},
					{"name":"[3]","value": thuoc_theo_phong},//L2PT-7766 L2PT-18176
					{"name":"[4]","value":JSON.stringify(_jsonThuoc)} //L2PT-17166
			);
			//tuyennx_edit_end_L2PT-34608	
		}
		//_ten_donvitinh, _khoanmucid
		/*var _selfnc=function(event, ui) {
			var _ui = ui.item;
			var _tenthuoc = $("#txtDS_THUOC").val();
			if(_tenthuoc != _ui.TEN_THUOC){
				alert('11111');
			}
			//callbackthuoc(_ui);
			return false;
	    };*/

	    if(_srchType == 1){
	    	if(type_tc == 1){
	    		_sql = "NTU02D010.131.2";
	    		_col = _col + ";Số lô,SOLO,10,0,f,c";
	    	}
	    	ComboUtil.initComboGrid("txtDS_THUOC1", _sql, _sql_par,"1200px", _col, function(event, ui) {
	    		if(ui.item){
	    			alert('11111:'+ui.item.TEN_THUOC);
	    		}
				//callbackthuoc(_ui);
				return false;
		    });
	    	
	    }else{
	    	ComboUtil.initComboGrid("txtTENTHUOC", _sql, _sql_par,"1200px", _col, _selfnc);
	    }
	};
	
	//tuyennx_edit_start_20180919 L2HOTRO-13865
	function callbackthuoc(_ui){
		var _time = 2000000;
		if($.inArray( _option, ['02D011','02D019']) >= 0){
        	_ui.GIATRANBHYT = "0"; 
        	_ui.GIA_BAN = "0";
        }
		
		//tuyennx_add_start_20181126 L2HOTRO-12173
		var tuoi = $('#lblBIRTHDAY_YEAR').val().includes('/') ? $('#lblBIRTHDAY_YEAR').val().split('/')[2]:$('#lblBIRTHDAY_YEAR').val()  ;
		var mahc = _configArr.CHAN_MAHOATCHAT_KETHUOC.split(';');
		var check =  parseInt(jsonrpc.AjaxJson.getSystemDate('YYYY'))- parseInt(tuoi) ;
		
		var hc = _ui.MAHOATCHAT == undefined ? "":_ui.MAHOATCHAT.split(';');
		for(var i = 0; i< hc.length; i++){
			if($.inArray( hc[i], mahc) >= 0 
					&& _doituongbenhnhanid == "1" && parseInt(check)>= 65 ){
				DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] có hoạt chất không thể kê đối với bệnh nhân BHYT trên 65 tuổi!");
				return;

	        }
			
			//tuyennx_L2PT-18747 ;L2PT-26568_add
			var KE_THUOC_CHECK_HC_THEOBN = _configArr.KE_THUOC_CHECK_HC_THEOBN;
			if(KE_THUOC_CHECK_HC_THEOBN !=0){
				var sql_par = [];
				sql_par.push({"name":"[0]", value:_benhnhanid});
				sql_par.push({"name":"[1]", value:hc[i]});
				var checkhc = jsonrpc.AjaxJson.getOneValue('CHECK.HCBN', sql_par);
				if(checkhc > 0){
					DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] có hoạt chất đã kê vẫn còn sử dụng");
					//L2PT-4593
					if(KE_THUOC_CHECK_HC_THEOBN == 2
							|| (KE_THUOC_CHECK_HC_THEOBN == 3 && ( _loaitiepnhanid == '0' || _loaitiepnhanid == '3' ) )
							|| (KE_THUOC_CHECK_HC_THEOBN == 4 && _loaitiepnhanid == '1') )
						return;	
				}
			}
			
			//BVTM-6045
			if(_configArr.KE_THUOC_CANHBAO_HC ==  '1'){
				var sql_par = [];
				sql_par.push({"name":"[0]", value:hc[i]});
				var canhbao_hc = jsonrpc.AjaxJson.getOneValue('KETHUOC.CHECKHC', sql_par);
				if(canhbao_hc && canhbao_hc != 'null'){
					DlgUtil.showMsg("Chú ý: "+ canhbao_hc);
				}
			}
		}
		//tuyennx_add_end_20181126
		//tuyennx_add_start_20181126 L2PT-643
		if(_doituongbenhnhanid == "1" && _ui.TYLEBHYT == '0' && _loaitiepnhanid == '1' 
			&& _ui.NHOMMABHYT == '10' && _loadkhotheo == '2'){
				DlgUtil.showMsg("Cảnh báo: Thuốc ["+ _ui.TEN_THUOC +"] có tỷ lệ BHYT trả 0%! "+ _ui.CHUY);
		}
		//tuyennx_add_end_20181126
		
		$("#hidCHOLANHDAODUYET").val("");
		_thuockhangsinh = 0;
		_chuy = '';
		_solo = '';
		//tuyennx_add_end_20190507 L2PT-5682
		//tuyennx_edit_start_L2PT-34744
		if(_checktrunghoatchat == '1' && (_dichvucha_id == "" || _configArr.KE_THUOC_HOATCHAT_DIKEM == '1')){ // voi thuoc vat di kem ko check 
			if(_configArr.KE_THUOC_HOATCHAT_TN.includes(_loaitiepnhanid)){ // check ngoai tru
		//tuyennx_edit_end_L2PT-34744
				for(var i = 0; i <_objTmpThuoc.length; i++){
					if(_option != '02D015' && _ui.KETRUNGHOATCHAT != '1' && _objTmpThuoc[i].KETRUNGHOATCHAT != '1' && _ui.MAHOATCHAT != undefined && _ui.MAHOATCHAT != ''  && _objTmpThuoc[i].MAHOATCHAT != undefined && $(_objTmpThuoc[i].MAHOATCHAT.split('+')).filter(_ui.MAHOATCHAT.split('+')).length > 0){
						DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] có hoạt chất trùng với thuốc đã kê tại phòng khám hiện tại hoặc phòng khám khác trong ngày");
						if(_chanhoatchat == '1'){
							$('#btnSave').prop('disabled', true);
							return;
						}
					}else{
						$('#btnSave').prop('disabled', false);
					}
				}
			}else{
				if(_kieucheck_hoatchat == '1'){ // check hoat chat va duong dung
					var _jsonthuoc = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
					if(_jsonthuoc.length > 0){
						for(var i = 0; i <_jsonthuoc.length; i++){
							if(_option != '02D015' && _ui.MAHOATCHAT != undefined && _ui.MAHOATCHAT !='' && _jsonthuoc[i].MAHOATCHAT != undefined && _jsonthuoc[i].MAHOATCHAT == _ui.MAHOATCHAT && _jsonthuoc[i].DUONGDUNGID == _ui.DUONGDUNGID){
								DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] có hoạt chất và đường dùng trùng với thuốc đã kê trong đơn");
								if(_chanhoatchat == '1'){
									$('#btnSave').prop('disabled', true);
									return;
								}
							}else{
								$('#btnSave').prop('disabled', false);
							}
						}
					}
				}
			}
		}
		//tuyennx_add_start_20200722 L2PT-23918
		var _jsonthuoc = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
		var list_thuoc ="0";
		var list_thuoc_tong = "";
		if(_jsonthuoc.length > 0){
			for(var i = 0; i <_jsonthuoc.length; i++){
				list_thuoc = list_thuoc + "," + _jsonthuoc[i].THUOCVATTUID;
			}
		}
		list_thuoc_tong = list_thuoc_trongngay + "," + list_thuoc;
 		if(list_thuoc_tong != '0,0'){
			var param = [list_thuoc_tong,_ui.THUOCVATTUID];
			var rs_tuongduong = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.THUOC.TD", param.join('$'));
			if(rs_tuongduong != '1')
				DlgUtil.showMsg("Cảnh báo thuốc tương đương: " +rs_tuongduong);
		}
		//tuyennx_add_end_20200722 L2PT-23918
 		
 		//checkTuongTacThuoc(_ui.THUOCVATTUID); //tuyennx_add_start_20200825 L2PT-26711
		
		//tuyennx_add_start_20190821 L2PT-7753 L2PT-29871
		var DS_ICD = '\'' +$('#txtMACHANDOANICD').val() +'\'';
		if($('#txtTENCHANDOANICD_KT').val() != ''){
			var array = $('#txtTENCHANDOANICD_KT').val().split(';');
			for(var i=0;i<array.length;i++){
				DS_ICD = DS_ICD+','+ '\''+array[i].split('-')[0] +'\'';
			}
		}
		var _par_check = [_ui.THUOCVATTUID,DS_ICD,'1'];					
		var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KE.ICD",_par_check.join('$'));
		var _rs = ret.split(';');
		if(parseInt(_rs[0]) > 0){
			//if(_check_canhbao == 0){
				_check_canhbao = 1;
				DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] kê cho BN phải có những ICD được chỉ định trong danh mục! Danh sách ICD: " +_rs[1]);
			//}
		}
		
		_par_check = [_ui.THUOCVATTUID,DS_ICD,'0'];					
		ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KE.ICD",_par_check.join('$'));
		_rs = ret.split(';');
		if(parseInt(_rs[0]) > 0){
			//if(_check_canhbao == 0){
				_check_canhbao = 1;
				DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] chỉ định cho BN có ICD không được phép kê trong danh mục! Danh sách ICD: " +_rs[1]);
			//}
		}
		
		//L2PT-1479
		_par_check = [_ui.THUOCVATTUID,DS_ICD,'2'];					
		ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KE.ICD",_par_check.join('$'));
		_rs = ret.split(';');
		if(parseInt(_rs[0]) > 0){
			//if(_check_canhbao == 0){
				_check_canhbao = 1;
				DlgUtil.showMsg("Chú ý thuốc ["+ _ui.TEN_THUOC +"] cảnh báo ICD: "+_rs[1]);
			//}
		}

		_par_check = [$("#hidHOSOBENHANID").val(),_ui.THUOCVATTUID,DS_ICD,'2'];						
		ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("KE.ICD.TUOI",_par_check.join('$'));
		_rs = ret.split(';');
		if(parseInt(_rs[0]) > 0){
			//if(_check_canhbao == 0){
				_check_canhbao = 1;
				DlgUtil.showMsg("Chú ý thuốc ["+ row.TEN_THUOC +"] cảnh báo tuổi theo ICD: "+_rs[1]);
			//}
		}
		
		//tuyennx_add_end_20190821 L2PT-7753
		//tuyennx_add_start_20190821 L2PT-34366
		_par_check = [$("#hidHOSOBENHANID").val(),_ui.THUOCVATTUID,DS_ICD,'1'];						
		ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("KE.ICD.TUOI",_par_check.join('$'));
		_rs = ret.split(';');
		if(parseInt(_rs[0]) > 0){
			//if(_check_canhbao == 0){
				_check_canhbao = 1;
				DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] kê cho BN phải có ICD và độ tuổi tương ứng chỉ định trong danh mục! Danh sách ICD: "+_rs[1]);
			//}
		}
		
		_par_check = [$("#hidHOSOBENHANID").val(),_ui.THUOCVATTUID,DS_ICD,'0'];					
		ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("KE.ICD.TUOI",_par_check.join('$'));
		_rs = ret.split(';');
		if(parseInt(_rs[0]) > 0){
			//if(_check_canhbao == 0){
				_check_canhbao = 1;
				DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] chỉ định cho BN có ICD không được phép kê với độ tuổi của BN trong danh mục! Danh sách ICD: "+_rs[1]);
			//}
		}
		//tuyennx_add_end_20190821 L2PT-34366
		
		//tuyennx_edit_start_20201116_L2PT-29871
		//L2PT-119029
		if(_ui.TUOIMIN && parseFloat(_ui.TUOIMIN) > 0 && (parseFloat(_tuoi * (_dvtuoi == '1' ? 12 : 1)) < parseFloat(_ui.TUOIMIN * (_ui.TUOITHANGMIN == '0' ? 12 : 1))) 
				//&& ((_ui.TUOITHANGMIN == '0' && _dvtuoi == '1') ||  (_ui.TUOITHANGMIN == '1' && _dvtuoi == '2') )
		){
			var thang = _ui.TUOITHANGMIN == '1' ? " tháng":'';
			if(_configArr.KETHUOC_CHI_CANHBAO == '1'){
				//if(_check_canhbao == 0){
					_check_canhbao = 1;
					DlgUtil.showMsg("Thuốc/Vật tư kê cho BN không được nhỏ hơn "  + _ui.TUOIMIN + thang + " tuổi");
				//}
			}
			else
				return DlgUtil.showMsg("Thuốc/Vật tư kê cho BN không được nhỏ hơn "  + _ui.TUOIMIN + thang + " tuổi");
		}
		
		if(_ui.TUOIMAX && parseFloat(_ui.TUOIMAX) > 0 && (parseFloat(_tuoi * (_dvtuoi == '1' ? 12 : 1)) > parseFloat(_ui.TUOIMAX * (_ui.TUOITHANGMAX == '0' ? 12 : 1))) 
				//&& ((_ui.TUOITHANGMAX == '0' && _dvtuoi == '1') ||  (_ui.TUOITHANGMAX == '1' && _dvtuoi == '2') )
			){
			var thang = _ui.TUOITHANGMAX == '1' ? " tháng":'';
			if(_configArr.KETHUOC_CHI_CANHBAO == '1'){
				//if(_check_canhbao == 0){
					_check_canhbao = 1;
					DlgUtil.showMsg("Thuốc/Vật tư kê cho BN không được lớn hơn "  + _ui.TUOIMAX + thang + " tuổi");
				//}
			}
			else
				return DlgUtil.showMsg("Thuốc/Vật tư kê cho BN không được lớn hơn "  + _ui.TUOIMAX + thang + " tuổi");
		}
		
		//tuyennx_edit_end_20201116_L2PT-29871
		var _par = [_tiepnhanid, _ui.THUOCVATTUID];					
		var _return = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KE.KHANGSINH",_par.join('$'));
		if(parseInt(_return) > 0){
			var _ischeck
			_showConfirm = DlgUtil.showConfirm("Bệnh nhân đã sử dụng kháng sinh vượt quá ngày quy định, có sử dung tiếp không?", function(_ret){
				if(!_ret){
					return false;
				}
			}, _time);
		}
		
		if(_ui.MABYT == "40.17" || _ui.MAHOATCHAT == "40.17"){ //L2PT-5948
			var _par_quydoi=[];
			_par_quydoi.push({"name":"[0]","value":_ui.THUOCVATTUID});
			ComboUtil.getComboTag("cboDVQUYDOI","DMC_QUYDOIOXY",_par_quydoi, "", {extval: true,value:'0',text:''},"sql");	
			
			$('#cboDVQUYDOI').prop("disabled", false);
			$("#cboDVQUYDOI").addClass("notnullandlonhon0");
		}else{
			ComboUtil.getComboTag("cboDVQUYDOI","DMC_QUYDOIOXY",_par_quydoi, "", {extval: true,value:'0',text:_ui.TEN_DVTQD},"sql"); //L2PT-16895
			//ComboUtil.getComboTag("cboLOAITHUOC", sql_loaithuoc ,[], "", {value:'-1',text:'-- Tất cả --'},"sql","", false);
			$('#cboDVQUYDOI').prop("disabled", true);
		}
		// Start tuyennx edit  15/10/2018 L2PT-1192  L2PT-30259
		if(_configArr.KE_THUOC_SOTAY_THUOC == '1'){
			var _x_par = [];
			_x_par.push({"name":"[0]","value":_ui.THUOCVATTUID});
			if(_configArr.KE_THUOC_SOTAY_GHICHU == '1'){
				var _columnthuoc = "Tên thuốc,TEN,40,0,f,l;Số ngày,SONGAY,7,0,t,l;Sáng,SANG,5,0,t,l;Trưa,TRUA,5,0,t,l;Chiều,CHIEU,5,0,t,l;Tối,TOI,5,0,t,l;SL tổng,SLTONG,10,0,f,l;Cách dùng,CACHDUNGTHUOC,20,0,f,l";
				ComboUtil.initComboGrid("txtGHICHU","DS_CACH_DUNG_KE",_x_par, "930px", _columnthuoc, function(event, uithuoc) {
					$("#txtGHICHU").val(uithuoc.item.CACHDUNGTHUOC);
					$("#txtGHICHU").focus();
					return false;
				});
			}else{
				if(_loaikedon == 0){
					// Start manhnv 15/10/2018 ADD
					// DM cách dùng thuốc chi tiết.
					var _columnthuoc = "Tên thuốc,TEN,40,0,f,l;Số ngày,SONGAY,7,0,f,l;Sáng,SANG,5,0,f,l;Trưa,TRUA,5,0,f,l;Chiều,CHIEU,5,0,f,l;Tối,TOI,5,0,f,l;SL tổng,SLTONG,10,0,f,l;Cách dùng,CACHDUNGTHUOC,20,0,f,l";
					ComboUtil.initComboGrid("txtSO_NGAY","DS_CACH_DUNG_KE",_x_par, "930px", _columnthuoc, function(event, uithuoc) {
						$("#txtSO_NGAY").val(uithuoc.item.SONGAY);
						$("#txtSANG").val(uithuoc.item.SANG);
						$("#txtTRUA").val(uithuoc.item.TRUA);
						$("#txtCHIEU").val(uithuoc.item.CHIEU);
						$("#txtTOI").val(uithuoc.item.TOI);
						$("#txtSOLUONG_CHITIET").val(uithuoc.item.SLTONG);
						$("#txtGHICHU").val(uithuoc.item.CACHDUNGTHUOC);
						$("#txtGHICHU").focus();
						return false;
					});
					var e = jQuery.Event("keydown", {
					    keyCode: 32
					});
					$("#txtSO_NGAY").trigger(e);
					// End manhnv 15/10/2018 ADD
				}else{
					
					// DM cách dùng thuốc tong hop L2PT-1192.
					var _columnthuoc = "Tên thuốc,TEN,40,0,f,l;Số ngày,SONGAY,7,0,t,l;Sáng,SANG,5,0,t,l;Trưa,TRUA,5,0,t,l;Chiều,CHIEU,5,0,t,l;Tối,TOI,5,0,t,l;SL tổng,SLTONG,10,0,f,l;Cách dùng,CACHDUNGTHUOC,20,0,f,l";
					ComboUtil.initComboGrid("txtSOLUONG_TONG","DS_CACH_DUNG_KE",_x_par, "930px", _columnthuoc, function(event, uithuoc) {
						//$("#txtSO_NGAY").val(uithuoc.item.SONGAY);
						$("#txtSANG").val(uithuoc.item.SANG);
						$("#txtTRUA").val(uithuoc.item.TRUA);
						$("#txtCHIEU").val(uithuoc.item.CHIEU);
						$("#txtTOI").val(uithuoc.item.TOI);
						$("#txtSOLUONG_TONG").val(uithuoc.item.SLTONG);
						$("#txtGHICHU").val(uithuoc.item.CACHDUNGTHUOC);
						
						$("#txtGHICHU").focus();
						return false;
					});
					var e = jQuery.Event("keydown", {
					    keyCode: 32
					});
					$("#txtSOLUONG_TONG").trigger(e);
					
				}
			}
		}
		// End tuyennx 15/10/2018 ADD
		
		//tuyennx_add_start_20190507 L2PT-4556 L2PT-8767
		 _donvitinhid_qd 		= _ui.DONVITINHID_QD;
		 _ten_dvt_qd = _ui.TEN_DVTQD;
	     _tyle_qd 		= _ui.TYLE_QD;
		//tuyennx_add_end_20190507 L2PT-4556
	     _lieuluong = _ui.LIEULUONG; //L2PT-7588
	     _HDSD = _ui.HUONGDAN_SD //L2PT-34642
		
		
		var _ret = findObjectJson(_jsonThuoc24h, "THUOCVATTUID", _ui.THUOCVATTUID);
		if(_ret == true && (_option == '02D010' || _option == '02D011')){//Neu thuoc dc ke -> phai confirm
			var _msgInfo = "Thuốc "+_ui.TEN_THUOC +" đã được kê khoảng 24h trước, bạn có muốn kê đơn với thuốc này nữa không?";
			var _showConfirm = DlgUtil.showConfirm(_msgInfo, function(_ret){
				if(_ret){
					$("#txtDS_THUOC").val(_ui.TEN_THUOC);
			        if(_srch_hoatchat == 1)
			        	$("#txtTENTHUOC").val(_ui.HOATCHAT);
			        else
			        	$("#txtTENTHUOC").val(_ui.TEN_THUOC);
			        
			        $("#txtGHICHU").val(_ui.HUONGDAN_SD);
			        $("#cboDUONG_DUNG").val(_ui.DUONGDUNGID);
			        _tuongtacthuoc 		= ','+ _ui.TUONGTACTHUOC+',';
			        _nhom_mabhyt_id 	= _ui.NHOM_MABHYT_ID;
			        _ten_donvitinh 		= _ui.TEN_DVT;
			        _khoanmucid 		= _ui.KHOANMUCID;
			        _tyle_bhyt_tt_tvt	= _ui.TYLEBHYT_TVT;
			        
			        $("#hidLOAITVTID").val(_ui.LOAI);
			        $("#hidMABYT").val(_ui.MABYT);
			        mahoatchat = _ui.MAHOATCHAT;//L2PT-5948
			        
			        if(_loai_don =="2" || _loai_don =="4")
			        	$("#cboMA_KHO").val(_ui.KHOID);
			        if(_option != '02D011')//Don mua ngoai
			        	$("#hdSOLUONGKHADUNG").val(_ui.SLKHADUNG);
			        else
			        	$("#hdSOLUONGKHADUNG").val(100000);
			        $("#txtGHICHU").val("");
			        $("#hdHUONGDANTHUCHIEN").val("");
			        $("#hidCANHBAOSOLUONG").val(_ui.CANHBAOSOLUONG);
			        
			        //tuyennx_add_start L2PT-295
			        $("#hidCHOLANHDAODUYET").val(_ui.CHOLANHDAODUYET);
			        //tuyennx_add_end
			      //tuyennx_add_start_20190507 L2PT-5682
					_thuockhangsinh = _ui.THUOCKHANGSINH;
					_chuy = _ui.CHUY;
					//if(type_tc == 1){
						_solo = _ui.SOLO;
						_hsd = _ui.HANSUDUNG;
					//}
					//tuyennx_add_end_20190507 L2PT-5682
			        
			        if(_loaikedon == 1)
			        	if(_configArr.KE_THUOC_FOCUS_DUONGDUNG == '1')
			        		$("#cboDUONG_DUNG").trigger("focus");
			        	else
			        		$("#txtSOLUONG_TONG").trigger("focus");
			        else{
			        	if(_configArr.KE_THUOC_FOCUS_DUONGDUNG == '1')
			        		$("#cboDUONG_DUNG").trigger("focus");
			        	else
			        		//tuyennx_edit_start_20200722 L2PT-25085
			        		if(_configArr.KETHUOC_ANNGAYKEDON == '1')
			        			$("#txtSANG").trigger("focus");
			        		else{ //L2PT-7616
			        			if(_configArr.KETHUOC_MACDINH_NGAYNTU != '0' && _loaitiepnhanid == '0')
			        				$("#txtSO_NGAY").val(_configArr.KETHUOC_MACDINH_NGAYNTU);
			        			$("#txtSO_NGAY").trigger("focus");
			        		}
			        		//tuyennx_edit_end_20200722 L2PT-25085
			        }
			        //tuyennx_edit_start_20190507 L2PT-4556  L2PT-8860
			        if(_donvitinhid_qd && _configArr.KETHUOC_TINH_DVQUYDOI == '1')
			        	$("#hdDONVI").val(_ui.TEN_DVTQD);
			        else
			        	$("#hdDONVI").val(_ui.TEN_DVT);
			        //tuyennx_edit_end_20190507 L2PT-4556
			        $("#hidKHOTHUOCTHEOTHUOC").val(_ui.KHOID);
			        $('#hidTHUOCVTID').val(_ui.THUOCVATTUID);
			        
			        _objDrugTemp = [];
			        _objDrug = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
					doAddDrugToJson(_ui, _objDrug, 1);
					doAddDrugToJson(_ui, _objDrugTemp, 1);
			        return false;
				}
				return true;
			}, _time);
		}else {//Neu thuoc chua dc ke -> Cu the ma ke
			// check thuốc tồn tại trong đơn
			var data = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			//tuyennx_edit_start 20190121 L2PT-1229 L2PT-34643
			var CHAN_KE_TRUNG_THUOC = _configArr.CHAN_KE_TRUNG_THUOC;
			if(CHAN_KE_TRUNG_THUOC==1 || CHAN_KE_TRUNG_THUOC == 2)
			{
				for(var i = 0; i < data.length; i++){
					if(_ui.THUOCVATTUID == data[i].THUOCVATTUID){
						DlgUtil.showMsg('Thuốc/vật tư đã có trong đơn');
						$('#txtDS_THUOC').select();
						$('#txtDS_THUOC').focus();
						if(CHAN_KE_TRUNG_THUOC == 1)
							return;
					}
				}
			}
			//tuyennx_edit_end_20190121 
			//BVTM-6667
			if($('#'+_gridDonThuoc).find("input[id*='SO_LUONG']").length > 0){
				DlgUtil.showMsg('Tồn tại trường số lượng đang sửa');
				cleanForm()
				return false;
			}
			
			objData = new Object();
			objData.THUOCVATTUID = _ui.THUOCVATTUID;
			objData.BENHNHANID = $('#hidBENHNHANID').val();
			objData.MAHOATCHAT = _ui.MAHOATCHAT;
			
			if(_canhbaophacdo == '1' && $('#txtMACHANDOANICD').val() != null && $('#txtMACHANDOANICD').val() != ''){
				var paramArrDvPd = [_ui.THUOCVATTUID, _opts.khambenh_id,$('#txtMACHANDOANICD').val()];
				var resultDvPd = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D009.EV0131",paramArrDvPd.join('$'));
				if(typeof resultDvPd != 'undefined' && resultDvPd != '1'){
					DlgUtil.showMsg(resultDvPd);
					//DlgUtil.showMsg('Thuốc ' + resultDvPd + ' có hoạt chất không tồn tại trong phác đồ điều trị mã bệnh ' + $('#txtMACHANDOANICD').val());
				}
			}
			
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DIUNGTHUOC", JSON.stringify(objData)); 
			if(fl > 0 && _checkdiung == '1'){
				DlgUtil.showMsg('Thuốc [' + _ui.TEN_THUOC + '] nằm trong danh sách thuốc dị ứng của bệnh nhân');
				$('#txtDS_THUOC').select();
				$('#txtDS_THUOC').focus();
				//return;
			}			
			$("#hidCHOLANHDAODUYET").val(_ui.CHOLANHDAODUYET);
			//tuyennx_edit_start_20190507 L2PT-5682
			_thuockhangsinh = _ui.THUOCKHANGSINH;
			_chuy = _ui.CHUY;
			//if(type_tc == 1){
				_solo = _ui.SOLO;
				_hsd = _ui.HANSUDUNG;
			//}
			
			if(_ui.CHOLANHDAODUYET == '1' && KT_GOPCHUNG_CANHBAO != '1'){
				DlgUtil.showMsg(_ui.CHUY);
			}
			//tuyennx_edit_end_20190507 L2PT-5682
			
			$("#txtDS_THUOC").val(_ui.TEN_THUOC);
	        if(_srch_hoatchat == 1)
	        	$("#txtTENTHUOC").val(_ui.HOATCHAT);
	        else
	        	$("#txtTENTHUOC").val(_ui.TEN_THUOC);
	        
	        $("#hidLOAITVTID").val(_ui.LOAI);
	        $("#hidMABYT").val(_ui.MABYT);
	        mahoatchat = _ui.MAHOATCHAT;//L2PT-5948
	        $("#txtGHICHU").val(_ui.HUONGDAN_SD);
	        $("#cboDUONG_DUNG").val(_ui.DUONGDUNGID);
	        _tuongtacthuoc 		= ','+ _ui.TUONGTACTHUOC+',';
	        _nhom_mabhyt_id 	= _ui.NHOM_MABHYT_ID;
	        _ten_donvitinh 		= _ui.TEN_DVT;
	        _khoanmucid 		= _ui.KHOANMUCID;
	        _tyle_bhyt_tt_tvt	= _ui.TYLEBHYT_TVT;
	        _dichvukbcha_id		= _ui.DICHVUKHAMBENHID;
	        if(_loai_don =="2" || _loai_don =="4")
	        	$("#cboMA_KHO").val(_ui.KHOID);
	        if(_option != '02D011')//Don mua ngoai
	        	$("#hdSOLUONGKHADUNG").val(_ui.SLKHADUNG);
	        else
	        	$("#hdSOLUONGKHADUNG").val(100000);
	        $("#txtGHICHU").val("");
	        $("#hdHUONGDANTHUCHIEN").val("");
	        $("#hidCANHBAOSOLUONG").val(_ui.CANHBAOSOLUONG);
	        
	        if(_loaikedon == 1){
	        	if(_configArr.KE_THUOC_FOCUS_DUONGDUNG == '1')
	        		$("#cboDUONG_DUNG").trigger("focus");
	        	else
	        		$("#txtSOLUONG_TONG").trigger("focus");
	        	
	        }else{
	        	if(_configArr.KE_THUOC_FOCUS_DUONGDUNG == '1')
	        		$("#cboDUONG_DUNG").trigger("focus");
	        	else
	        		//tuyennx_edit_start_20200722 L2PT-25085
	        		if(_configArr.KETHUOC_ANNGAYKEDON == '1')
	        			$("#txtSANG").trigger("focus");
	        		else{ //L2PT-7616
	        			if(_configArr.KETHUOC_MACDINH_NGAYNTU != '0' && _loaitiepnhanid == '0')
	        				$("#txtSO_NGAY").val(_configArr.KETHUOC_MACDINH_NGAYNTU);
	        			$("#txtSO_NGAY").trigger("focus");
	        		}
	        		//tuyennx_edit_end_20200722 L2PT-25085
	        }
	        
	      //tuyennx_edit_start_20190507 L2PT-4556
	        if(_donvitinhid_qd && _configArr.KETHUOC_TINH_DVQUYDOI == '1')
	        	$("#hdDONVI").val(_ui.TEN_DVTQD);
	        else
	        	$("#hdDONVI").val(_ui.TEN_DVT);
	        //tuyennx_edit_end_20190507 L2PT-4556
	        $("#hidKHOTHUOCTHEOTHUOC").val(_ui.KHOID);
	        $('#hidTHUOCVTID').val(_ui.THUOCVATTUID);
	        //tuyennx_add_start_20180919 L2PT-23529
	        if(_configArr.KETHUOC_TIMHOATCHAT == '1'){
	        	 $('#txtDS_HOATCHAT').val(_ui.HOATCHAT);
		    }
	        //tuyennx_add_end_20180919 L2PT-23529
	        
	        _objDrugTemp = [];
	        _objDrug = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			doAddDrugToJson(_ui, _objDrug, 1);
			doAddDrugToJson(_ui, _objDrugTemp, 1);
	        return false;
		}
	};
	function addDays(dateObj, numDays) {
		dateObj.setDate(dateObj.getDate() + numDays);
		return dateObj;
	}
	function addThuoc(){	
		if (tenthuoc_temp.trim() != ($("#txtTHUOCVT").val()).trim()) {//L2PT-135545
			$("#hidKHOTHUOCTHEOTHUOC").val("0");
			$("#hidTENKHOTHUOC").val("");
			$("#hidTHUOCVTID").val("");
		}
			objData = new Object();		
			objData.DON_GIA = "0";
			objData.DUONGDUNGID = $("#cboDUONG_DUNG").val();
			objData.DUONG_DUNG = $("#cboDUONG_DUNG option:selected").text();
			var _huongdan_sd = "";
			var _soluong_thuoc = $("#txtSOLUONG_CHITIET").val();
			if(_loaikedon == 1)
				//tuyennx_edit_start_20190507 L2PT-6691
				if(_option == '02D017' && _configArr.SONGAYBANGSOTHANG ==1 && $('#txtSLTHANG').val() != '' )
					_huongdan_sd = $('#txtSLTHANG').val()+ "@"+ $("#cboDUONG_DUNG option:selected").text() +"@"+$("#txtGHICHU").val()+"@"+_soluong_thuoc+"@@@@";
				else 
					_huongdan_sd = "1@"+ $("#cboDUONG_DUNG option:selected").text() +"@"+$("#txtGHICHU").val()+"@"+_soluong_thuoc+"@@@@";
				//tuyennx_edit_start_20190507 L2PT-6691
			else
				_huongdan_sd = $("#hdHUONGDANTHUCHIEN").val().replace("_param_huongdan", $("#txtGHICHU").val());
			objData.HUONGDAN_SD = _huongdan_sd;
			objData.ID_DT_CU = "4";
			objData.ID_DT_MOI = "4";
			objData.KHO_THUOCID = $("#hidKHOTHUOCTHEOTHUOC").val();
			objData.TENKHO = $("#hidTENKHOTHUOC").val();
			objData.SO_LUONG = $("#txtSOLUONG_CHITIET").val();	
			objData.TEN_THUOC = $("#txtTHUOCVT").val();
			objData.THUOCVTID = $("#hidTHUOCVTID").val();
			objData.THANH_TIEN = "0";
			objData.TYLEBHYT_TVT = "100";		
			objData.KHOANMUCID = "0";
			objData.DUONGDUNGE = $("#txtGHICHU").val();
			objData.THUOCVATTUID = $("#hidTHUOCVTID").val();
			
			
			var _jsonthuoc = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			jQuery("#grdDONTHUOC").jqGrid("clearGridData");
			
			
			if(_jsonthuoc.length > 0){
				for(var i = 0; i <_jsonthuoc.length; i++){
					jQuery("#grdDONTHUOC").jqGrid('addRowData', i+1, _jsonthuoc[i]);
					
				}
			}
		
			jQuery("#grdDONTHUOC").jqGrid('addRowData', _jsonthuoc.length + 1, objData);
//			for(var i=0;i<_objDrug.length;i++) {
//				jQuery("#grdDONTHUOC").jqGrid('addRowData', i, _objDrug[i]);
//				loadAll(i, _objDrug[i].ID_DT_MOI);
//				_tongtien += parseFloat(_objDrug[i].THANH_TIEN);
//				_bhtra += parseFloat(_objDrug[i].BH_TRA);
//				_ndtra += parseFloat(_objDrug[i].ND_TRA);
//            }
//			
			
			
			$("#cboDUONG_DUNG").val(0);
			$("#txtGHICHU").val("");
			$("#hidTHUOCVTID").val("");	
			$("#hidKHOTHUOCTHEOTHUOC").val("0")
			$("#txtSO_NGAY").val("");
			$("#txtSANG").val("");
			$("#txtTRUA").val("");
			$("#txtCHIEU").val("");
			$("#txtTOI").val("");
			$("#txtSOLUONG_CHITIET").val("");			
			$("#txtTHUOCVT").val("");			
			
			$("#txtTHUOCVT").focus();
	};
	//tuyennx_edit_end_20180919 L2HOTRO-13865

	/*
	 _ten_donvitinh = _ui.TEN_DVT;
	    _khoanmucid = _ui.KHOANMUCID;
	 */
	$("#cboMA_KHO" ).change(function () {
		_kho_id = $('#cboMA_KHO :selected').val();
		if(_kho_id != "0"){			
		    _ds_khoid = _kho_id;
		}else{
			var values = $.map($('#cboMA_KHO option'), function(e) { return e.value; });   
		  	_ds_khoid = values.join(',');
		}
		var _colan="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN,40,0,f,l;Mã,MA,10,0,f,c;Đường dùng,DUONG_DUNG,10,0,f,c;Hoạt chất,HOATCHAT,10,0,f,l;HDSD,HDSD,10,0,f,l;Liều lượng,LIEULUONG,10,0,f,l;Đơn vị,TEN_DVT,10,0,f,l";
		doLoadComboTVT("txtTHUOCVT",_colan);
	});
	
	$("#chkChiTimTheoNT" ).change(function () {
		var _colan="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN,40,0,f,l;Mã,MA,10,0,f,c;Đường dùng,DUONG_DUNG,10,0,f,c;Hoạt chất,HOATCHAT,10,0,f,l;HDSD,HDSD,10,0,f,l;Liều lượng,LIEULUONG,10,0,f,l;Đơn vị,TEN_DVT,10,0,f,l";
		doLoadComboTVT("txtTHUOCVT",_colan);
	});

	
	$("#btnBP").on("click",function(e){
		$('#txtMATENCHANDOANICD_KT').val('');
		$('#txtTENCHANDOANICD_KT').val('');
	});
	
	$("#cboLOAITHUOC" ).change(function () {
		_loaithuocid = $('#cboLOAITHUOC').val();
	});
	
	function loadGridDonThuoc(iType, _maubenhpham_id,diff) {
		var sql_par=[];
		var vtmp1 = []; //L2PT-30164
		
		//tuyennx_add_start_20181126 L2PT-30241
		var tuoi = $('#lblBIRTHDAY_YEAR').val().includes('/') ? $('#lblBIRTHDAY_YEAR').val().split('/')[2]:$('#lblBIRTHDAY_YEAR').val()  ;
		var mahc = _configArr.CHAN_MAHOATCHAT_KETHUOC.split(';');
		var check =  parseInt(jsonrpc.AjaxJson.getSystemDate('YYYY'))- parseInt(tuoi) ;
		
		if(_maubenhpham_id != "" && _maubenhpham_id !="undefined"){
			var _sqlGrid = "";
			if(iType == 'TEMP'){
				sql_par.push(//{"name":"[0]","value":_tyle_bhyt},
							{"name":"[0]","value":_company_id},
							{"name":"[1]","value":_loainhommaubenhpham_id},
							{"name":"[2]","value":_maubenhpham_id});
				
				if(diff == '1'){
					sql_par.push({"name":"[3]","value":r_dichvu_id_diff});
					_gridDonThuocTemp = 'NTU02D010.22';
				} else if(diff == '2'){
					sql_par = [];
					sql_par.push(//{"name":"[0]","value":_tyle_bhyt},
							{"name":"[0]","value":_doituongbenhnhanid},
							{"name":"[1]","value":_maubenhpham_id});
					_gridDonThuocTemp = 'NTU02D075.EV006';
				} else {
					_gridDonThuocTemp = 'NTU02D010.05';
				}
				
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.20",_maubenhpham_id);
				if(data_ar != null && data_ar.length > 0){
					$('#txtLOIDANBS').val(data_ar[0].LOIDANBS);
				}
				_sqlGrid = _gridDonThuocTemp;
				
				vtmp1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D010.051", sql_par); //L2PT-30164
				//tuyennx_add_start_20190515 L2PT-4826
				var vtmp = jsonrpc.AjaxJson.ajaxExecuteQueryO(_sqlGrid, sql_par);
				vtmp = JSON.parse(vtmp);
				if(vtmp.length > 0) {
					for(var k = 0; k <vtmp.length; k++){
						if(vtmp[k].CHOLANHDAODUYET == '1' && vtmp[k].CHUY != ''){
							DlgUtil.showMsg("Chú ý: Thuốc ["+ vtmp[k].TEN_THUOC +"] " + vtmp[k].CHUY);
						}
						
						//tuyennx_add_start_20181126 L2PT-30241
						var hc = vtmp[k].MAHOATCHAT == undefined ? "":vtmp[k].MAHOATCHAT.split(';');
						for(var i = 0; i< hc.length; i++){
							if($.inArray( hc[i], mahc) >= 0 
									&& _doituongbenhnhanid == "1" && parseInt(check)>= 65 ){
								DlgUtil.showMsg("Thuốc ["+ vtmp[k].TEN_THUOC +"] có hoạt chất không thể kê đối với bệnh nhân BHYT trên 65 tuổi!");
								return;
					        }
						}
						
						//add L2PT-18941 L2PT-30164
//						if(vtmp[k].KTTONKHO && vtmp[k].KTTONKHO < 0){
//							DlgUtil.showMsg("Chú ý: Thuốc ["+ vtmp[k].TEN_THUOC +"] số lượng trong kho không đủ để cấp!");
//						}
					}
				}
				//tuyennx_add_end_20190515 L2PT-4826
				
				// check trung hoat chat
				if(_loaitiepnhanid == '1'){
					var ischeckhc = '0';
					//tuyennx_edit_start_20190515 L2PT-4826 comment do goi o tren roi
//					var vtmp = jsonrpc.AjaxJson.ajaxExecuteQueryO(_sqlGrid, sql_par);
//					vtmp = JSON.parse(vtmp);
					//tuyennx_edit_end_20190515 L2PT-4826
					if(vtmp.length > 0) {
						for(var k = 0; k <vtmp.length; k++){
							for(var i = 0; i < _objTmpThuoc.length; i++){
								if(vtmp[k].KETRUNGHOATCHAT != "1" && _objTmpThuoc[i].KETRUNGHOATCHAT != "1" && vtmp[k].MAHOATCHAT && _objTmpThuoc[i].MAHOATCHAT 
										&& (vtmp[k].MAHOATCHAT == _objTmpThuoc[i].MAHOATCHAT)){ //L2PT-16381 
									ischeckhc = '1';
									break;
								}							
							}
							
							if(ischeckhc == '1'){
								DlgUtil.showMsg("Thuốc ["+ vtmp[k].TEN_THUOC +"] có hoạt chất trùng với thuốc đã kê tại phòng khám hiện tại hoặc phòng khám khác trong ngày");
								if(_chanhoatchat == '1'){
									return;
								}
							}
							//rao lai do neu chon lại don thi se bat trung hoat chat vs don da chon truoc day L2PT-617
//							else{
//								_objTmpThuoc.push({
//									"THUOCVATTUID" : vtmp[k].THUOCVATTUID,
//									"MAHOATCHAT" : vtmp[k].MAHOATCHAT,
//									"KETRUNGHOATCHAT" : vtmp[k].KETRUNGHOATCHAT
//								});
//							}
						}
					}
				}
			}else{
				sql_par.push({"name":"[0]","value":_loainhommaubenhpham_id},
						 	{"name":"[1]","value":iType =="DTCU"?"-1":_company_id}, //L2PT-15996
						 	{"name":"[2]","value":_maubenhpham_id});
				if(diff == '1'){
					sql_par.push({"name":"[3]","value":r_dichvu_id_dtc_diff});	
					_sqlGrid = 'NTU02D010.26';
				} else {
					_sqlGrid = _gridDonThuocSQL;
				}
				vtmp1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D010.041", sql_par); //L2PT-30164
				
				//tuyennx_add_start_20190515 L2PT-30241
				var vtmp = jsonrpc.AjaxJson.ajaxExecuteQueryO(_sqlGrid, sql_par);
				vtmp = JSON.parse(vtmp);
				if(vtmp.length > 0) {
					for(var k = 0; k <vtmp.length; k++){
						if(vtmp[k].CHOLANHDAODUYET == '1' && vtmp[k].CHUY != ''){
							DlgUtil.showMsg("Chú ý: Thuốc ["+ vtmp[k].TEN_THUOC +"] " + vtmp[k].CHUY);
						}
						var hc = vtmp[k].MAHOATCHAT == undefined ? "":vtmp[k].MAHOATCHAT.split(';');
						for(var i = 0; i< hc.length; i++){
							if($.inArray( hc[i], mahc) >= 0 
									&& _doituongbenhnhanid == "1" && parseInt(check)>= 65 ){
								DlgUtil.showMsg("Thuốc ["+ vtmp[k].TEN_THUOC +"] có hoạt chất không thể kê đối với bệnh nhân BHYT trên 65 tuổi!");
								return;
					        }
						}
					}
				}
				
			}
			sql_par=setSysParam(sql_par);
			var _gridDonThuocHeader = "";
//			if(_option != '02D017' || _option != '02D018'){
//				if(an_dongia_thanhtien == '1')
//					_gridDonThuocHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,300,0,f,l;Hoạt chất,HOATCHAT,130,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,t,r;SL,SO_LUONG,40,0,e,c;SLTK,SLKHADUNG,40,0,e,c;Thành tiền,THANH_TIEN,80,0,t,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,60,0,f,l;Loại TT mới,LOAI_DT_MOI,70,0,f,l;Chú ý,CHUY,100,0,f,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,150,0,f,c,ES;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c; ,ACTION,50,udb,f,c; ,ACTION,30,d,f,c;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l"; // manhnh 05/10/2018 upd
//				else
//					_gridDonThuocHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,200,0,f,l;Hoạt chất,HOATCHAT,130,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,f,r;SL,SO_LUONG,40,0,e,c;SLTK,SLKHADUNG,40,0,e,c;Thành tiền,THANH_TIEN,80,0,f,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,60,0,f,l;Loại TT mới,LOAI_DT_MOI,70,0,f,l;Chú ý,CHUY,100,0,f,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,150,0,f,c,ES;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c; ,ACTION,50,udb,f,c; ,ACTION,30,d,f,c;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l"; // manhnh 05/10/2018 upd
//			}else{
//				if(an_dongia_thanhtien == '1')
//					_gridDonThuocHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,300,0,f,l;Đơn vị tính,DONVI_TINH,100,0,f,l;Đơn giá,DON_GIA,100,0,t,r;Số lượng,SO_LUONG,100,0,e,c;SLTK,SLKHADUNG,40,0,e,c;Thành tiền,THANH_TIEN,100,0,t,r;BH trả,BH_TRA,100,0,t,l;ND trả,ND_TRA,100,0,t,l;Loại TT cũ,LOAI_DT_CU,100,0,f,l;Loại TT mới,LOAI_DT_MOI,100,0,f,l;Chú ý,CHUY,100,0,f,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,150,0,f,c,ES;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c; ,ACTION,70,udb,f,c; ,ACTION,50,d,f,c;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l";	//OldValue,OLDVALUE,0,0,f,t,0 // manhnh 05/10/2018 upd
//				else
//					_gridDonThuocHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,200,0,f,l;Đơn vị tính,DONVI_TINH,100,0,f,l;Đơn giá,DON_GIA,100,0,f,r;Số lượng,SO_LUONG,100,0,e,c;SLTK,SLKHADUNG,40,0,e,c;Thành tiền,THANH_TIEN,100,0,f,r;BH trả,BH_TRA,100,0,t,l;ND trả,ND_TRA,100,0,t,l;Loại TT cũ,LOAI_DT_CU,100,0,f,l;Loại TT mới,LOAI_DT_MOI,100,0,f,l;Chú ý,CHUY,100,0,f,l;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;Cách dùng,DUONGDUNGE,150,0,f,c,ES;Liều dùng,LIEUDUNG,130,0,e,c;NSD,SOLAN_SD_KHANGSINH,30,0,e,c; ,ACTION,70,udb,f,c; ,ACTION,50,d,f,c;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l;SL_KELE,SL_KELE,0,0,t,l;DVKBID,DVKBID,0,0,t,l;TYLEDK,TYLEDK,0,0,t,l;DIEUKIENID,DIEUKIENID,0,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;SO_LUONG_KE,SO_LUONG_KE,0,0,t,l;DONVI_TINH_QD,DONVI_TINH_QD,0,0,t,l;DON_GIA_QD,DON_GIA_QD,0,0,t,l;TEN_QD,TEN_QD,0,0,t,l;GHICHUCANHBAO,GHICHUCANHBAO,0,0,t,l";	//OldValue,OLDVALUE,0,0,f,t,0 // manhnh 05/10/2018 upd
//			}
			//L2PT-2098
			_gridDonThuocHeader="THUOCVATTUID,THUOCVATTUID,0,0,t,l;STT_KE,STT_KE,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+ _configArr.KETHUOC_HEADER_GRID_MN;//L2PT-10076
			
			var opt_ext={footerrow: true, rowNum: 200,rowList: [200]};
			
			GridUtil.init(_gridDonThuoc,"1278","245",_gridCaption,false, _gridDonThuocHeader, false, opt_ext);
			
			//tuyennx_add_start 20180928 fix
			if(_configArr.TACH_PHIEUTHUOC_KE_SOLUONGLE == '1')
				jQuery("#grdDONTHUOC").jqGrid('showCol', "LAMTRON");
			else
				jQuery("#grdDONTHUOC").jqGrid('hideCol', "LAMTRON");
			//tuyennx_add_end 20180928 fix
			
			//tuyennx_add_start 20180928 L2PT-617
			if(_configArr.KETHUOC_HIENTHI_SLKD == '1')
				jQuery("#grdDONTHUOC").jqGrid('showCol', "SLKHADUNG");
			else
				jQuery("#grdDONTHUOC").jqGrid('hideCol', "SLKHADUNG");
			//tuyennx_add_end 20180928 L2PT-617
			
			//tuyennx_add_start 20180928 L2PT-885
//			if(_configArr.KETHUOC_HIENTHI_CHUY == '1'){
//				jQuery("#grdDONTHUOC").jqGrid('showCol', "CHUY");
//				jQuery("#grdDONTHUOC").jqGrid('hideCol', "LOAI_DT_CU");
//				jQuery("#grdDONTHUOC").jqGrid('hideCol', "LOAI_DT_MOI");
//			}
//			else{
//				jQuery("#grdDONTHUOC").jqGrid('hideCol', "CHUY");
//				jQuery("#grdDONTHUOC").jqGrid('showCol', "LOAI_DT_CU");
//				jQuery("#grdDONTHUOC").jqGrid('showCol', "LOAI_DT_MOI");
//			}	
			//tuyennx_add_end 20180928 L2PT-885
			
//			if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TACH_PHIEUTHUOC_KE_SOLUONGLE')==1){
//				GridUtil.init(_gridDonThuoc,"1278","245",_gridCaption,true, _gridDonThuocHeader, false, opt_ext);
//			}else{
//				GridUtil.init(_gridDonThuoc,"1278","245",_gridCaption,false, _gridDonThuocHeader, false, opt_ext);
//			}
			
			//L2PT-30164
			if(vtmp1){
				vtmp1 = JSON.parse(vtmp1);
				if(vtmp1[0].TENTHUOC)
					DlgUtil.showMsg("Có thuốc trong kho không đủ để cấp: " +vtmp1[0].TENTHUOC);
			}
			sql_par.push({"name":"[4]","value":$('#cboMA_KHO').val()});	 //L2PT-617
			GridUtil.loadGridBySqlPage(_gridDonThuoc, _sqlGrid, sql_par);
			
			//L2PT-409
			if(iType == 'TEMP'){
				_mbp_temp = _maubenhpham_id;
				if(_macdinh_hao_phi == '9')
					_ketugoi = '1';
			}
			
			_nhom_mabhyt_id			= ""; //L2PT-34568 reset lại nhom mau bhyt truong hop su dung don mau va don cu, ko bi lay theo loai cua thuoc truoc day
			loadAll("","");
		}
	}

	//Start TuyenNX BVTM-1487
	function _kySo(maubenhphamId) {
		_caRpt('1', maubenhphamId);
	}

	function _caRpt(signType, maubenhphamId) {
		if(_option == '02D011'){
			_exportKyCA("NGT006_DONTHUOC_MUANGOAI_A5",maubenhphamId, signType);
			return;
		}
		 var _par_loai = [maubenhphamId];						
 		 var _loaithuoc=0;    								
		 var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
		 if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
			for(var i=0;i< arr_loaithuoc.length;i++){
				_loaithuoc=arr_loaithuoc[i].LOAI;
			   if(_loaithuoc==3){
					//thuoc dong y --DONTHUOCTHANG_NGOAITRU
				   _exportKyCA("NGT020_DONTHUOCTHANGNGOAITRU",maubenhphamId, signType);
			   }else if(_loaithuoc==6){
				   _exportKyCA("NGT013_DONTHUOCHUONGTHAN_TT052016_A5",maubenhphamId, signType);
			   }else if(_loaithuoc==7){
				   _exportKyCA("NGT013_DONTHUOCGAYNGHIEN_TT052016_A5",maubenhphamId, signType);
			   }
//				   //don my pham thuc pham chuc nang
//				   else if(_loaithuoc==16 || _loaithuoc==19 ){   
//				   }
//				   //don thuoc nguon
//				   else if(_loaithuoc==-1 ){
//				   }
			   //don thuoc thuong
			   else{
				   _exportKyCA("NGT006_DONTHUOC_17DBV01_TT052016_A5",maubenhphamId, signType);
			   }
			}
		 }
	}
	//L2PT-124731
	function _exportKyCA(_rpt_code_kyso, maubenhphamId, signType, catype, _title){
		var par_rpt_KySo = [];
		par_rpt_KySo = [ {
			name : 'HOSOBENHANID',
			type : 'String',
			value : $("#hidHOSOBENHANID").val()
		}];
		par_rpt_KySo.push({
			name : 'rpt_code',
			type : 'String',
			value : _rpt_code_kyso
		});
		par_rpt_KySo.push({
			name : 'maubenhphamid',
			type : 'String',
			value : maubenhphamId
		});
		
		//L2PT-43073
		if (_configArr.KETHUOC_IN_KYSO_CHUNG == '1') {
			lstParamPrintCa.push(par_rpt_KySo);
		}
		
		//ky
		if(catype == '5') {
			//L2PT-28091
			lstParamKyCa.push(par_rpt_KySo);
			//CommonUtil.kyCA(par_rpt_KySo, signType, true);
		} 
		else{
			var oData = {
				sign_type : signType,
				causer : causer,
				capassword : capassword,
				params : par_rpt_KySo,
				smartcauser: _smartcauser //L2PT-17577
			};
			//xu ly ky don trong nganh chi thong bao neu ky that bai va khong in L2PT-15355
			if(_rpt_code_kyso == 'NGT006_DONTHUOCGOP_17DBV01_TT052016_A5'){
				
				//var msg = CommonUtil.caRpt(oData, _rpt_code_kyso, false);
				var msg = CommonUtil.caRpt(oData, _rpt_code_kyso, false, '', true, _title, catype);
				if(!msg.toUpperCase().includes('THÀNH CÔNG') || msg.toUpperCase().includes('KHÔNG THÀNH CÔNG'))
					DlgUtil.showMsg('Lỗi ký đơn trong ngành:' + msg);
		//			var msg = CommonUtil.kyCA(par_rpt_KySo, signType, false);
		//			EventUtil.setEvent("eventKyCA",function(e){
		//				if(!msg.includes('THÀNH CÔNG'))
		//					DlgUtil.showMsg('Lỗi ký đơn trong ngành:' + e.res);
		//			});
			}
			else {
				//var msg = CommonUtil.caRpt(oData, _rpt_code_kyso, true);
				//L2PT-43073
				var msg ="";
				if (_configArr.KETHUOC_IN_KYSO_CHUNG == '1' && lstParamPrintCa.length > 0) 
					msg = CommonUtil.caRpt(oData, _rpt_code_kyso, false, '', true, _title, catype);
				else
					msg = CommonUtil.caRpt(oData, _rpt_code_kyso, true, '', true, _title, catype);
				
				var _code = msg.split("|")[0];
				var _msg = msg.split("|")[1];
				var _caid = msg.split("|")[2];
				
				if (_code == '0' || _code == '7' || _code == '8') {
					$.bootstrapGrowl(_msg,{
		                type: 'success',
		                delay: 2000,
		            });
					if (catype == '3') {
						var intervalId = null;
						var smartCaLoaderFunction = function () {
							console.log("smartCaLoaderFunction is running!")
							var _sql_par = [];
							_sql_par.push({"name": "[0]", value: _caid});
							var fl = jsonrpc.AjaxJson.getOneValue("SMARTCA.GET.STATUS", _sql_par);
							if (fl == 1) {
								// bat phieu in
								CommonUtil.openReportGetCA2(par_rpt_KySo, false);
								clearInterval(intervalId);
							}
						};
						intervalId = setInterval(smartCaLoaderFunction, 4000);
					} 
					//L2PT-88450
					if(_configArr.NGT_MO_POPUP_LUUDONTHUOC == '1'){
						$("#btnXuTri").trigger("click");
					}  
				}
				else{
					DlgUtil.showMsg(_msg);
				}
				
			}
		}			
	}
	//End TuyenNX BVTM-1487
	
	function setSysParam(_par_ar) {
		var v_par=_par_ar;
		for(var i1=0;i1<_param.length;i1++) {
			v_par.push({"name":"[S"+i1+"]","value":_param[i1]});
		}
		return v_par;
	}
	
	function cleanForm(){
		$("#txtDS_THUOC").val("");
		$("#txtDS_HOATCHAT").val(""); // L2PT-23529
		$("#txtTENTHUOC").val("");
		$("#txtSOLUONG_TONG").val("");
		$("#txtGHICHU").val("");
		$("#txtGHICHUCANHBAO").val(""); //L2PT-29871
		$("#txtSOLUONG_CHITIET").val("");
		$("#txtTGSD").val("");
		$("#txtSearchCD").val("");
		
		//$("#txtSO_NGAY").val("");
		$("#txtSANG").val("");
		$("#txtTRUA").val("");
		$("#txtCHIEU").val("");
		$("#txtTOI").val("");
		$("#hdHUONGDANTHUCHIEN").val("");
		
		$("#txtSLSOLAN").val("");
		$("#txtSOLANSONGAY").val("");
		$("#txtLIEUDUNG").val("");
		//txtSOLUONG_CHITIET,txtSO_NGAY,txtSANG,txtTRUA,txtCHIEU,txtTOI
	}
	
	$('#txtSOLUONG_TONG').keydown(function (e) {
		if (e.which === 13){
			var num = parseFloat($('#txtSOLUONG_TONG').val()) || 0;
			// Start tuyennx edit  15/10/2018 L2PT-1192
		    if(($('#txtSOLUONG_TONG').val() == '' || num == 0) && 
		    		_configArr.KE_THUOC_SOTAY_THUOC !=1){
		    	// end tuyennx edit  15/10/2018
		    	DlgUtil.showMsg("Số lượng kê đơn phải là số nguyên dương và lớn hơn 0");
		    }	
		    else{
		    	//tuyennx_add_start_20201112 L2PT-30259
				var e = jQuery.Event("keydown", {
				    keyCode: 32
				});
				$("#txtGHICHU").trigger(e);
				//tuyennx_add_end_20201112 L2PT-30259
		    	$('#txtGHICHU').trigger("focus");
		    
		    	if($('#txtSOLUONG_TONG').val().indexOf('/') != -1){
		    		soluong_kele = $('#txtSOLUONG_TONG').val();
		    		var array = $('#txtSOLUONG_TONG').val().split('/');
		    		//tuyennx_edit_start_20180806
		    		var value = (array[0]/array[1]).toFixed(3);
		    		//tuyennx_edit_end_20180806
		    		$('#txtSOLUONG_TONG').val(value);
		    	}
		    	
		    	if(isNaN($('#txtSOLUONG_TONG').val())){
		    		$('#txtSOLUONG_TONG').trigger("focus");
			    	DlgUtil.showMsg("Số lượng phải là kiểu số");
			    }
		    	
		    	//tuyennx_add_start_20201116_L2PT-29871
		    	var _soluong_thuoc = $("#txtSOLUONG_TONG").val();
				
				if(r_loaicheck == "1" && parseFloat($("#hidCANHBAOSOLUONG").val()) > 0 && (parseFloat(_soluong_thuoc) > parseFloat($("#hidCANHBAOSOLUONG").val()))){
					//tuyennx_edit_start_20201116_L2PT-29871
					if(_configArr.KETHUOC_CHI_CANHBAO == '1'){
						//if(_check_canhbao == 0){
							_check_canhbao = 1;
							DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + $("#hidCANHBAOSOLUONG").val());
						//}
					}
					else
						return DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + $("#hidCANHBAOSOLUONG").val());
					
				}
				//tuyennx_add_end_20201116_L2PT-29871
				
				//tuyennx_edit_start_20201116_L2PT-33947
				if(r_loaicheck == "1"){
					//L2PT-7599
					var sql_par_slthuoc;
					if(parseInt($("#txtSO_NGAY").val()) > 0)
						sql_par_slthuoc = _tiepnhanid + '$' + $('#hidTHUOCVTID').val() + '$' +(_soluong_thuoc/parseInt($("#txtSO_NGAY").val())).toFixed(3)+ '$' +$('#txtTHOI_GIAN').val();
					else
						sql_par_slthuoc = _tiepnhanid + '$' + $('#hidTHUOCVTID').val() + '$' +_soluong_thuoc+ '$' +$('#txtTHOI_GIAN').val();
					
					var check_sl_thuoc  = jsonrpc.AjaxJson.ajaxCALL_SP_I('KETHUOC.SOLUONG', sql_par_slthuoc);
					if(_configArr.KETHUOC_CHI_CANHBAO == '1' && check_sl_thuoc > 0){
							_check_canhbao = 1;
							DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + check_sl_thuoc + " trong 1 ngày");
					}
					else if(check_sl_thuoc > 0){
						$('#txtSOLUONG_TONG').val('');
						$('#txtSOLUONG_TONG').trigger("focus");
						return DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + check_sl_thuoc + " trong 1 ngày");
					}
						
					
					sql_par_slthuoc = _tiepnhanid + '$' + $('#hidTHUOCVTID').val() + '$' +_soluong_thuoc+ '$' +"";
					check_sl_thuoc  = jsonrpc.AjaxJson.ajaxCALL_SP_I('KETHUOC.SOLUONG', sql_par_slthuoc);
					if(_configArr.KETHUOC_CHI_CANHBAO == '1' && check_sl_thuoc > 0){
							_check_canhbao = 1;
							DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + check_sl_thuoc + " trong 1 đợt điều trị");
					}
					else if(check_sl_thuoc > 0){
						$('#txtSOLUONG_TONG').val('');
						$('#txtSOLUONG_TONG').trigger("focus");
						return DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + check_sl_thuoc + " trong 1 đợt điều trị");
					}
				}
				//tuyennx_add_end_20201116_L2PT-33947
		    	
		    	$('#SLSOLAN').trigger("focus");
		    	if(_sudung_lieudung == '1' && (_option == '02D010')){
		    		$('#txtSLSOLAN').trigger("focus");
				}else{
					//tuyennx_add_start_20201112 L2PT-30259
					var e = jQuery.Event("keydown", {
					    keyCode: 32
					});
					$("#txtGHICHU").trigger(e);
					//tuyennx_add_end_20201112 L2PT-30259
					$('#txtGHICHU').trigger("focus");
				}
		    }
		 // Start tuyennx edit  L2PT-32407
			if(_configArr.KE_THUOC_GHICHU_DONGY == '1' && _option == '02D017')
				$("#txtGHICHU").val($("#txtCACHSACTHUOC").val());
			// end tuyennx edit  L2PT-32407
		
			//tuyennx_add_start_L2PT-34197
			if(_configArr.KE_THUOC_CHECK_DUONGDUNG == '1' && $("#txtGHICHU").val() == '' && _dichvucha_id)
				$("#txtGHICHU").val($("#cboDUONG_DUNG option:selected").text());
			//tuyennx_add_end_L2PT-34197
			
			//tuyennx_add_start_L2PT-34642 L2PT-6526
			if(_configArr.KE_THUOC_LAY_DUONGDUNG == '1' && _HDSD)
				$("#txtGHICHU").val(_HDSD);
			//tuyennx_add_end_L2PT-34642
			
			
		}
	});
	
	$('#cboDUONG_DUNG').keydown(function (e) {
		if (e.which == 13)
			if(_loaikedon == 1){
				$('#txtSOLUONG_TONG').trigger("focus");
			}else{
				$('#txtSO_NGAY').trigger("focus");
			}
	});
	
	$('#txtGHICHU').keydown(function (e) {
		if (e.which === 13){
			//tuyennx_edit_start L2PT-29871
			if(_configArr.KETHUOC_GHICHU_CANHBAO == '1'){
				$('#txtGHICHUCANHBAO').trigger("focus");
			}else{
				$('#btnAdd').trigger("focus");
			}
			//tuyennx_edit_end L2PT-29871
			/*if(_sudung_lieudung == '1' && (_option == '02D010' || _option == '02D017')){
				$('#txtSLSOLAN').trigger("focus");
			}else{
				$('#btnAdd').trigger("focus");
			}*/
		}	
	});
	//tuyennx_add_start_20201116_L2PT-29871
	$('#txtGHICHUCANHBAO').keydown(function (e) {
		if (e.which === 13){
			$('#btnAdd').trigger("focus");
		}	
	});
	//tuyennx_add_end_20201116_L2PT-29871
	
	$('#txtSLSOLAN').keydown(function (e) {
		if (e.which === 13){ 
			$('#txtSOLANSONGAY').trigger("focus");
		}	
	});
	
	$('#txtSOLANSONGAY').keydown(function (e) {
		if (e.which === 13){
			var lieudung = $('#txtSLSOLAN').val()+ " " + $('#hdDONVI').val() + "/Lần * " + $('#txtSOLANSONGAY').val() + "lần/Ngày";
			//L2PT-4594
			if(_configArr.KE_THUOC_KONHAP_LIEUDUNG == '1' && (parseInt($('#txtSLSOLAN').val()) + parseInt($('#txtSOLANSONGAY').val())) == 0 )
				$("#txtLIEUDUNG").val("");
			else
				$("#txtLIEUDUNG").val(lieudung);
			$('#txtLIEUDUNG').trigger("focus");
		}	
	});
	$('#txtLIEUDUNG').keydown(function (e) {
		if (e.which === 13){
			$('#txtGHICHU').trigger("focus");
			//$('#btnAdd').trigger("focus");
		}	
	});
	
	$('#txtSOLUONG_CHITIET').keydown(function (e) {
		if (e.which === 13 || e.which === 9){
			var num = parseFloat($('#txtSOLUONG_CHITIET').val()) || 0;
		    if($('#txtSOLUONG_CHITIET').val() == '' || num == 0){
		    	DlgUtil.showMsg("Số lượng kê đơn phải là số nguyên dương và lớn hơn 0");
		    }	
		    else{
		    	
		    	//$('#txtGHICHU').trigger("focus");
		    	//tuyennx_add_start_20201112 L2PT-30259
				var e = jQuery.Event("keydown", {
				    keyCode: 32
				});
				$("#txtGHICHU").trigger(e);
				//tuyennx_add_end_20201112 L2PT-30259
		    	$('#SLSOLAN').trigger("focus");
		    	//tuyennx_add_start_20201116_L2PT-29871
		    	var _soluong_thuoc = $("#txtSOLUONG_CHITIET").val();
				if(r_loaicheck == "1" && parseFloat($("#hidCANHBAOSOLUONG").val()) > 0 && (parseFloat(_soluong_thuoc) > parseFloat($("#hidCANHBAOSOLUONG").val()))){
					//tuyennx_edit_start_20201116_L2PT-29871
					if(_configArr.KETHUOC_CHI_CANHBAO == '1'){
						//if(_check_canhbao == 0){
							_check_canhbao = 1;
							DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + $("#hidCANHBAOSOLUONG").val());
						//}
					}
					else
						return DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + $("#hidCANHBAOSOLUONG").val());
					
				}
				//tuyennx_add_end_20201116_L2PT-29871
				
				//tuyennx_edit_start_20201116_L2PT-33947
				if(r_loaicheck == "1"){
					var sql_par_slthuoc = _tiepnhanid + '$' + $('#hidTHUOCVTID').val() + '$' +_soluong_thuoc+ '$' +$('#txtTHOI_GIAN').val();
					var check_sl_thuoc  = jsonrpc.AjaxJson.ajaxCALL_SP_I('KETHUOC.SOLUONG', sql_par_slthuoc);
					if(_configArr.KETHUOC_CHI_CANHBAO == '1' && check_sl_thuoc > 0){
							_check_canhbao = 1;
							DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + check_sl_thuoc + " trong 1 ngày");
					}
					else if(check_sl_thuoc > 0){
						$('#txtSOLUONG_CHITIET').val('');
						$('#txtSOLUONG_CHITIET').trigger("focus");
						return DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + check_sl_thuoc + " trong 1 ngày");
					}
						
					
					sql_par_slthuoc = _tiepnhanid + '$' + $('#hidTHUOCVTID').val() + '$' +_soluong_thuoc+ '$' +"";
					check_sl_thuoc  = jsonrpc.AjaxJson.ajaxCALL_SP_I('KETHUOC.SOLUONG', sql_par_slthuoc);
					if(_configArr.KETHUOC_CHI_CANHBAO == '1' && check_sl_thuoc > 0){
							_check_canhbao = 1;
							DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + check_sl_thuoc + " trong 1 đợt điều trị");
					}
					else if(check_sl_thuoc > 0){
						$('#txtSOLUONG_CHITIET').val('');
						$('#txtSOLUONG_CHITIET').trigger("focus");
						return DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + check_sl_thuoc + " trong 1 đợt điều trị");
					}
				}
				//tuyennx_add_end_20201116_L2PT-33947
		    	if(_sudung_lieudung == '1' && (_option == '02D010' || _option == '02D017')){
		    		$('#txtSLSOLAN').trigger("focus");
				}else if(_botimcachdung == '0'){ //L2PT-20418 
					$('#txtGHICHU').trigger("focus");
				}else{
					$('#txtSearchCD').trigger("focus");
				}
		    }
		    //tuyennx_add_start_20181030 L2HOTRO-11748
		    if($('#txtSOLUONG_CHITIET').val().includes('.') 
		    		&& _configArr.NTU_CANHBAO_KETHUOCLE =='1'
		    		&& _loadkhotheo == '1' && _loaitiepnhanid=='0'){
		    	DlgUtil.showMsg("Chú ý số lượng kê là lẻ");
		    }
		    //tuyennx_add_end_20181030
		  //tuyennx_add_start_L2PT-34642 L2PT-6526
			if(_configArr.KE_THUOC_LAY_DUONGDUNG == '1'  && _HDSD)
				$("#txtGHICHU").val(_HDSD);
			//tuyennx_add_end_L2PT-34642
		}
		/*if (e.which == 13 && _botimcachdung == '1'){
			//$('#txtSearchCD').trigger("focus");
			$('#txtTGSD').trigger("focus");
		}else if(e.which == 13){
			$('#txtGHICHU').trigger("focus");
		}*/
	});
	
	$('#txtTGSD').keydown(function (e) {
		if (e.which == 13){
			$('#txtSearchCD').trigger("focus");
		}
	});
	
	$('#txtSearchCD').keydown(function (e) {
		if (e.which == 13){
			$('#txtGHICHU').trigger("focus");
		}
	});
	
	$('#txtSO_NGAY').keydown(function (e) {
		if (e.which == 13){
			$('#txtSANG').trigger("focus");
		}
	});
	$('#txtSANG').keydown(function (e) {
		if (e.which == 13){
			$('#txtTRUA').trigger("focus");
		}
	});
	$('#txtTRUA').keydown(function (e) {
		if (e.which == 13){
			var _trua = parseFloat($('#txtTRUA').val()) || 0;
			if(_trua == 0)
				$('#txtTRUA').val("0");
			$('#txtCHIEU').trigger("focus");
		}
	});
	$('#txtCHIEU').keydown(function (e) {
		if (e.which == 13){
			var _chieu = parseFloat($('#txtCHIEU').val()) || 0;
			if(_chieu == 0)
				$('#txtCHIEU').val("0");
			//doInputDrugType(_loaikedon);
			$('#txtTOI').trigger("focus");
		}
	});
	$('#txtTOI').keydown(function (e) {
		if (e.which == 13){
			var _chieu = parseFloat($('#txtTOI').val()) || 0;
			if(_chieu == 0)
				$('#txtTOI').val("0");
			//doInputDrugType(_loaikedon);
			$('#txtSOLUONG_CHITIET').trigger("focus");
		}
	});
	
	$('#txtSONGAY_KE').keydown(function (e) {
		if (e.which == 13){
			$('#txtSOLUONG_TONG').trigger("focus");
		}
	});
	
	$('#txtSANG').focusin(function() { $(this).select(); });
	$('#txtTRUA').focusin(function() { $(this).select(); } );
	$('#txtCHIEU').focusin(function() { $(this).select(); } );
	$('#txtTOI').focusout(function() { doInputDrugType(_loaikedon);});
	//$('#txtSOLUONG_TONG').focusin(function() { $(this).select(); });
	$('#txtSOLUONG_CHITIET').focusout(function() { doInputDrugType(_loaikedon, 1);});
	$('#txtTGSD').focusout(function() { doInputDrugType(_loaikedon, 1);});
	$('#txtSearchCD').focusout(function() { doInputDrugType(_loaikedon, 1);});
	$('#txtSOLUONG_TONG').focusout(function() { doInputDrugType(_loaikedon, 1); });
	
	String.prototype.replaceAll = function(search, replacement) {
	    var target = this;
	    return target.replace(new RegExp(search, 'g'), replacement);
	};
	
	Number.prototype.formatMoney = function(c, d, t){
		var n = this, 
		    c = isNaN(c = Math.abs(c)) ? 2 : c, 
		    d = d == undefined ? "." : d, 
		    t = t == undefined ? "," : t, 
		    s = n < 0 ? "-" : "", 
		    i = parseInt(n = Math.abs(+n || 0).toFixed(c)) + "", 
		    j = (j = i.length) > 3 ? j % 3 : 0;
		   return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
		 };
	function doLoadCombo(_txt,_txtDst){
		var _selfnc=function(event, ui) {
		   var str = $("#txtTENCHANDOANICD_KT").val();
		   if(str.indexOf(ui.item.ICD10CODE) > -1){
		    DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
		    return false;
		   }
			   
	        $("#"+_txt).val(ui.item.ICD10CODE);
	        _MACHANDOAN = ui.item.ICD10CODE; //L2PT-23947
	        $("#"+_txtDst).val(ui.item.ICD10NAME);
	        $("#txtMATENCHANDOANICD_KT").trigger("focus");
	        return false;
	    };
	    ComboUtil.initComboGrid(_txt,"NT.SEARCH.ICD10",sql_par,"600px",_col,_selfnc);
	};
	
	function doLoadComboTVT(_txt,_col){
	    if ($('#chkChiTimTheoNT').is(":checked")){
	    	var _selfnc=function(event, ui) {
				$("#hidTHUOCVTID").val(ui.item.THUOCVATTUID);
				$("#hidKHOTHUOCTHEOTHUOC").val(ui.item.KHOID);
				$("#hidTENKHOTHUOC").val(ui.item.TENKHO);
				$("#"+_txt).val(ui.item.TEN_THUOC);
				tenthuoc_temp = ui.item.TEN_THUOC;// L2PT-135545
				$("#cboDUONG_DUNG").val(ui.item.DUONGDUNGID);
				$("#cboDUONG_DUNG").focus();
				
		        return false;
		    };
			_sql_par =[];
			_loaithuocid = $('#cboLOAITHUOC').val();
			var _jsonThuoc = new Object();
			_jsonThuoc.TYPE = "0";
			_jsonThuoc.DINHDUONG  =  "";
			_sql_par.push(
					{"name":"[0]","value":_ds_khoid},				
					{"name":"[1]","value":"-1"},
					{"name":"[2]","value":_loaithuocid},
					{"name":"[3]","value": ""},//L2PT-7766 L2PT-18176
					{"name":"[4]","value":JSON.stringify(_jsonThuoc)} //L2PT-17166
			);
			_col="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN_THUOC,20,0,f,l;Hoạt chất,HOATCHAT,10,0,f,c;Liều lượng,LIEULUONG,10,0,f,l;" +
			"Đơn vị,TEN_DVT,5,0,f,l;Mã"+_lbl_text + _configArr.HEADER_KEDONTHUOC; //OldValue,OLDVALUE,0,0,f,t,0	// manhnh 05/10/2018 upd	
			//tuyennx_edit_end_20181122 
			ComboUtil.initComboGrid(_txt,"NTU02D010.131",_sql_par,"1200px",_col,_selfnc);
		}else{
			var _selfnc=function(event, ui) {
				$("#hidTHUOCVTID").val(ui.item.THUOCVATTUID);
				$("#"+_txt).val(ui.item.TEN);
				tenthuoc_temp = ui.item.TEN;// L2PT-135545
				$("#hidTENKHOTHUOC").val('Mua ngoài');
				$("#cboDUONG_DUNG").val(ui.item.DUONGDUNGID);
				$("#cboDUONG_DUNG").focus();
				
		        return false;
		    };
		    ComboUtil.initComboGrid(_txt,"DMC.TVT",sql_par,"1200px",_col,_selfnc);
		}
		
	};
	
	function doInputDrugType(_loaikedon, kieu){
		var _total = 0;
		var _text = "";
		var _lan_sang = 0;
		var _lan_trua = 0;
		var _lan_chieu = 0;
		var _lan_toi = 0;
		var _tong_lan = 0;
		var _trung_binh = 0;
		var _soluong = 0;
		var _solan = 0;
		var _lieudung = "";
		var _slmax = 0;
		
		$("#hidLIEUDUNGBD").val("");
		var _text_duongdung = $("#cboDUONG_DUNG option:selected").text();
			var _songay = parseFloat($('#txtSO_NGAY').val()) || 0;
			
			var _sang = parseFloat($('#txtSANG').val()) || 0;
			var _trua = parseFloat($('#txtTRUA').val()) || 0;
			var _chieu = parseFloat($('#txtCHIEU').val()) || 0;
			var _toi = parseFloat($('#txtTOI').val()) || 0;
			
			_slmax = _sang;
			if(_slmax < _trua)
				_slmax = _trua;
			if(_slmax < _chieu)
				_slmax = _chieu;
			if(_slmax < _toi)
				_slmax = _toi;
			
			if(_sang > 0){
				_tong_lan = parseInt(_tong_lan) + 1;
				_soluong = parseFloat(_soluong) + _sang;
				
			}else{
				$('#txtSANG').val("0");
			}
			
			if(_trua > 0){
				_tong_lan = parseInt(_tong_lan) + 1;
				_soluong = parseFloat(_soluong) + _trua;
			}else{
				$('#txtTRUA').val("0");
			}				
			
			if(_chieu > 0){
				_tong_lan = parseInt(_tong_lan) + 1;
				_soluong = parseFloat(_soluong) + _chieu;
			}else{
				$('#txtCHIEU').val("0");
			}
			
			if(_toi > 0){
				_tong_lan = parseInt(_tong_lan) + 1;
				_soluong = parseFloat(_soluong) + _toi;
			}else{
				$('#txtTOI').val("0");
			}

			_trung_binh = parseFloat(_sang + _trua + _chieu + _toi).toPrecision(12)/_tong_lan; //L2PT-8136
			
			if(_songay > 0){				
				_total = Number(parseFloat(_sang + _trua + _chieu + _toi).toPrecision(12)*_songay);//L2PT-8136
				if(kieu != '1'){// set lai so luong
					if(_ngt_lamtron_kedon == '1'){
						$("#txtSOLUONG_CHITIET").val(Math.round(_total));
					}else{
						$("#txtSOLUONG_CHITIET").val(_total);
					}
				}				
				// SONDN START 20180111
				var _tgsd1 = typeof $("#txtTGSD").val() === 'undefined' ? "" : $("#txtTGSD").val(); 
				var _searchcd1 = typeof $("#txtSearchCD").val() === 'undefined' ? "" : $("#txtSearchCD").val();
				var _donvi1 = typeof $("#hdDONVI").val() === 'undefined' ? "" : $("#hdDONVI").val();
				
				_text = _songay +"@"+$("#cboDUONG_DUNG option:selected").text()+"@_param_huongdan@"
							+ _total +"@"+ _sang +"@"+ _trua +"@"+ _chieu +"@"+ _toi 
							+ "@" + _tgsd1 + "@" + _searchcd1 + "@" + _donvi1;
				
				// SONDN END 20180111
				$("#hdHUONGDANTHUCHIEN").val(_text);
				//tuyennx_edit_start_20200722 L2PT-25085
				if(_configArr.KETHUOC_ANNGAYKEDON == '1')
					$("#txtGHICHU").val("Ngày "+ (_total/_songay) + " " + $('#hdDONVI').val() +" chia "+ _tong_lan);
				else
					$("#txtGHICHU").val(_songay + " ngày, " + "Ngày "+ (_total/_songay) + " " + $('#hdDONVI').val() +" chia "+ _tong_lan);
				//tuyennx_edit_end_20200722 L2PT-25085
				
				// tạm check với HCM
				if(_cachdung_sangchieu == '1'){
					$("#txtGHICHU").val("");
					var dvt = "";
					var _ghichu = "";
					var sang = "";
					var trua = "";
					var chieu = "";
					var toi = "";
					var _tgsd = "";
					var _cachdung = "";
					var _duongdung = "";
					dvt = $("#hdDONVI").val();
					
					if(parseFloat(_sang) != 0){
						_ghichu += "Sáng " + _sang + " " + dvt;
						sang = "Sáng " + _sang + " " + dvt;
					}
					
					if(parseFloat(_trua) != 0){						
						if(_ghichu != ""){
							_ghichu += ", Trưa " + _trua + " " + dvt;
							trua = ", Trưa " + _trua + " " + dvt;
						} else {
							_ghichu += "Trưa " + _trua + " " + dvt;
							trua = "Trưa " + _trua + " " + dvt;
						}
					}
					
					if(parseFloat(_chieu) != 0){
						if(_ghichu != ""){
							_ghichu += ", Chiều " + _chieu + " " + dvt;
							chieu = ", Chiều " + _chieu + " " + dvt;
						} else {
							_ghichu += "Chiều " + _chieu + " " + dvt;
							chieu = "Chiều " + _chieu + " " + dvt;
						}
					}
					
					if(parseFloat(_toi) != 0){						
						if(_ghichu != ""){
							_ghichu += ", Tối " + _toi + " " + dvt;
							toi = ", Tối " + _toi + " " + dvt;
						} else {
							_ghichu += "Tối " + _toi + " " + dvt;
							toi = "Tối " + _toi + " " + dvt;
						}
					}
					
					if(_format_cd == '1'){ // đường dùng + thời gian dùng + cách dùng + sang/trua/chieu/toi. 
						_ghichu = "";
						_tgsd = ($("#txtTGSD").val() == undefined)?'':$("#txtTGSD").val();
						_cachdung = ($("#txtSearchCD").val() == undefined)?'':$("#txtSearchCD").val();
						_duongdung = $("#cboDUONG_DUNG option:selected").text();
						
						if(_duongdung != ''){
							_duongdung += ", ";
						}
						else
							_duongdung ="";
						
						if(_tgsd != ''){
							_tgsd += ", ";
						}
						else
							_tgsd="";
						
						if(_cachdung != ''){
							_cachdung += ", ";
						}	
						else
							_cachdung="";
						_ghichu = _duongdung + _tgsd + _cachdung + sang + trua + chieu + toi;
					}else if(_format_cd == '2'){ // đường dùng + ngày + sl + DVT + thời gian dùng + cách dùng + sang/trua/chieu/toi. 
						var _soluong = 0;
						_ghichu = "";
						_tgsd = ($("#txtTGSD").val() == undefined)?'':$("#txtTGSD").val();
						_cachdung = ($("#txtSearchCD").val() == undefined)?'':$("#txtSearchCD").val();
						_duongdung = $("#cboDUONG_DUNG option:selected").text();
						
						if(_duongdung != ""){
							_duongdung += ", ";
						}
						
						if(_tgsd != ""){
							_tgsd += ", ";
						}
						
						if(_cachdung != ""){
							_cachdung += ", ";
						}					
						_soluong = Number(parseFloat(_sang + _trua + _chieu + _toi).toPrecision(12));//L2PT-8136
						
						if(parseFloat(_soluong) <= 0){
							_soluong = parseFloat(Number($("#txtSOLUONG_CHITIET").val())/_songay);
							_duongdung = $("#cboDUONG_DUNG option:selected").text();
							if(_duongdung.toUpperCase() != "UỐNG"){
								//L2PT-5849
								if(_configArr.KE_THUOC_CACHDUNG_BODUONGDUNG == '1')
									_ghichu =  " Ngày " + _tgsd + " " + _cachdung;
								else
									_ghichu = _duongdung + ", Ngày " + _tgsd + " " + _cachdung;
							}else{
								//L2PT-5849
								if(_configArr.KE_THUOC_CACHDUNG_BODUONGDUNG == '1')
									_ghichu = " Ngày " + _soluong + " " + dvt + " " + _tgsd + " " + _cachdung;
								else
									_ghichu = _duongdung + ", Ngày " + _soluong + " " + dvt + " " + _tgsd + " " + _cachdung;
							}							
						}else{
							//L2PT-31822
							if(_configArr.KE_THUOC_SOLAN_CACHDUNG == '1')
								_ghichu = _duongdung + "Ngày " + _soluong + " " + dvt + ", Chia làm "  + _tong_lan + " lần" + " " + _tgsd + " " + _cachdung + ", " + sang + trua + chieu + toi;
							else
								//L2PT-5849 //L2PT-7841 them trim neu cac gia tri ko co
								if(_configArr.KE_THUOC_CACHDUNG_BODUONGDUNG == '1')
									_ghichu = "Ngày " + (_soluong + " " + dvt + " " + _tgsd + " " + _cachdung).trim() + ", " + sang + trua + chieu + toi;
								else
									_ghichu = _duongdung + "Ngày " + (_soluong + " " + dvt + " " + _tgsd + " " + _cachdung).trim() + ", " + sang + trua + chieu + toi;
						}						
					}else if(_format_cd == '3'){ // manhnv BVTM-902
						if(parseFloat(_sang) != 0){							
							sang = "Sáng " + _sang;
						}
						
						if(parseFloat(_trua) != 0){		
							trua = ", Trưa " + _trua;
						}
						
						if(parseFloat(_chieu) != 0){
							chieu = ", Chiều " + _chieu;
						}
						
						if(parseFloat(_toi) != 0){	
							toi = ", Tối " + _toi;
						}
						_duongdung = $("#cboDUONG_DUNG option:selected").text()
						if(_duongdung)
							_duongdung = _duongdung +', '
						_ghichu = _duongdung + _songay + " ngày, " +  " ngày " +  _soluong + " " + dvt + ", " + sang + trua + chieu + toi;
					}else {
						if(_songay != null && _songay != '' && _configArr.KETHUOC_ANNGAYKEDON !== '1'){  //L2PT-25085
							_ghichu = _songay + " ngày, " + _ghichu;
						} 
					}
					//tuyennx_add_start_20190617 L2PT-6075
					if(_configArr.KE_THUOC_DUONGDUNG == '1' 
							&& $("#txtSearchCD").val().trim()!="" && _loaitiepnhanid == '0'){
						_ghichu = _duongdung + _tgsd + $("#txtSearchCD").val(); 
					}
					//tuyennx_add_end_20190617 L2PT-6075
					$("#txtGHICHU").val(_ghichu);
				}
				$('#txtSOLUONG_CHITIET').focusin(function() { $(this).select(); });
			}else{
				_total = 0;
				// SONDN START 20180111 : bo sung them 2 chu @
				var _donvi1 = typeof $("#hdDONVI").val() === 'undefined' ? "" : $("#hdDONVI").val();
				_text = "@"+$("#cboDUONG_DUNG option:selected").text()+"@_param_huongdan@"+ _total +"@@@@@@"  + "@" + _donvi1;
				// SONDN END 20180111
				
				$("#hdHUONGDANTHUCHIEN").val(_text);
				// Start tuyennx edit  15/10/2018 L2PT-1192
				if(_configArr.KE_THUOC_SOTAY_THUOC != '1')
					$("#txtGHICHU").val("");
				// end tuyennx edit  15/10/2018 
				$("#txtSOLUONG_CHITIET").val("");
				$('#txtSOLUONG_CHITIET').focusin(function() { $(this).select(); });
			}
			
/*			if(_option == '02D010'){
				_lieudung = _soluong + "/lần * " + _tong_lan + "/ ngày";
			}else */
				
			if(_option == '02D017'){
				_lieudung = $("#txtSOLUONG_TONG").val() + "g * 1 thang * " + $("#txtSLTHANG").val() + " ngày";
			}
			
			if(_option == '02D010'){
				$("#txtSLSOLAN").val(_slmax);
				$("#txtSOLANSONGAY").val(_tong_lan);
				_lieudung = _slmax + " " + $("#hdDONVI").val() + "/lần * " + _tong_lan + "lần/ ngày";
				
				//L2PT-4594
				if(_configArr.KE_THUOC_KONHAP_LIEUDUNG == '1' && (parseInt(_slmax) + parseInt(_tong_lan)) == 0 )
					$("#txtLIEUDUNG").val("");
				else
					$("#txtLIEUDUNG").val(_lieudung);
				
			}
			$("#hidLIEUDUNGBD").val(_lieudung);
			
			// Start tuyennx edit  L2PT-32407
			if(_configArr.KE_THUOC_GHICHU_DONGY == '1' && _option == '02D017')
				$("#txtGHICHU").val($("#txtCACHSACTHUOC").val());
			// end tuyennx edit  L2PT-32407
			
			// Start tuyennx edit  15/10/2018 L2PT-31704
			if(_configArr.KE_THUOC_GHICHU == '1')
				$("#txtGHICHU").val($("#txtGHICHU").val() + " " + $("#txtSearchCD").val());
			// end tuyennx edit  15/10/2018 
			
			// Start tuyennx edit  15/10/2018 L2PT-25160
			if(_configArr.KE_THUOC_DUONGDUNG_CHAI == '1' 
					&& _ten_donvitinh.toUpperCase().includes('CHAI'))
				$("#txtGHICHU").val($("#txtGHICHU").val().replaceAll(_ten_donvitinh,'ml'));
			// end tuyennx edit  15/10/2018 
			// Start tuyennx edit  15/10/2018 L2PT-23837
			if(_configArr.KE_THUOC_TUNHAPGHICHU == '1')
				$("#txtGHICHU").val("");
			// end tuyennx edit  15/10/2018 
			
		return _total;
	}
	//tuyennx_add_start_20200825 L2PT-26711
	function checkTuongTacThuoc(_thuocvattuid) {
		if(_configArr.KETHUOC_TUONGTAC_THUOC == '1'){
			var _jsonthuoc = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			if(_jsonthuoc.length > 0){
				for(var i = 0; i <_jsonthuoc.length; i++){
					//if(!mySetLstTuongtac.has(_thuocvattuid +'-' +_jsonthuoc[i].THUOCVATTUID)){ //L2PT-32754
						var objThuocDV = new Object();
						objThuocDV.LOAINHOMMAUBENHPHAM = _loainhommaubenhpham_id;
						var object = jsonrpc.AjaxJson.ajaxCALL_SP_O("KETHUOC.TUONGTAC", _jsonthuoc[i].THUOCVATTUID+"$"+_thuocvattuid,[]);
						if(object && object.length > 0){
							object[0].THUOCVATTUID1 = _jsonthuoc[i].THUOCVATTUID;
							object[0].THUOCVATTUID2 = _thuocvattuid;
							//L2PT-7842
							if(object[0].TUONGTAC_THUONG == '1'){
								$('#divTT_THUONG').css('display','');
								if(object[0].LOAIHC == '1'){
									$("#lbllLOAITUONGTAC").text('Loại tương tác HC');
									//$("#lbllHAUQUA").val('Hậu quả HC');
									$("#lbllXUTRI").text('Xử trí HC');
									$("#lbllGHICHU_THUONG").text('Ghi chú HC');
									$("#lbllTAILIEU").text('Tài liệu tham khảo HC');
								}
							}
							if(object[0].TUONGTAC_ATC == '1')
								$('#divTT_ATC').css('display','');
							FormUtil.setObjectToForm('dlgPOPUP_TUONGTAC', '', object[0]);
							if(_configArr.KETHUOC_BSKE_DOITUONG.includes(_loaitiepnhanid) &&_bacsi_ke == '1' 
								&& ($('#cboBACSIID').val() == '-1' || $('#cboBACSIID').val() == undefined)){
								$("#txtDS_THUOC").val('');
								$("#txtDS_HOATCHAT").val('');
								$('#cboBACSIID').focus();
								DlgUtil.showMsg('Chưa chọn bác sỹ kê đơn');
								$('#btnSave').prop('disabled', false);
								return;
							}
							DlgUtil.open("dlgTUONGTAC");
							$('#dlgKHONGDONGY').css('display','none');
							$('#lblLOAITUONGTAC').css('color',object[0].MA_MAU);
						}						
					//}
				}
			}
		}
	}
	function initPopupTuongTacThuoc(){
		var dlgPOPUP_TUONGTAC = DlgUtil.buildPopup("dlgTUONGTAC", "dlgPOPUP_TUONGTAC", "Cảnh báo tương tác thuốc", 700, 560, {"zIndex":998});
		var _gridLYDOId="grdLYDO";
		var _gridLYDOHeader=
			"Lý do không đồng ý với khuyến cáo,LYDO,580,0,f,l,1,2;"+
			"LYDOID,LYDOID,650,0,t,l,1,2";
		GridUtil.init(_gridLYDOId,"650","200","",true,_gridLYDOHeader,false);
		$("#"+_gridLYDOId)[0].toggleToolbar();
		var btnOK = $('#btn_TUONGTAC_OK');
		var btnNotOK = $('#btn_TUONGTAC_NOTOK');
		btnOK.click(function() {	
			var object = {};
			FormUtil.setFormToObject('dlgPOPUP_TUONGTAC', '', object);
			if(mySetLstTuongtac.has(object.THUOCVATTUID1 + '-' + object.THUOCVATTUID2 + '-0' )){ //L2PT-32754
				dlgPOPUP_TUONGTAC.close();
				$("#hidTHUOCVTID").val('');
				$("#txtDS_THUOC").val('');
				$("#txtDS_HOATCHAT").val('');
				$("#txtDS_THUOC").trigger("focus");
				return DlgUtil.showMsg("Không thể lưu. Đã đồng ý khuyến cáo trước đấy!");
			}
			if(_bacsi_ke == '1' && $("#cboBACSIID").val() != '-1' && $("#cboBACSIID").val() != null){ //L2PT-22232
				object.NGUOIDUNG_ID 		= $("#cboBACSIID").val();
			}else{
				object.NGUOIDUNG_ID 		= _user_id;
			}
			object.HOSOBENHANID = $("#hidHOSOBENHANID").val();
			object.BENHNHANID = _benhnhanid;
			object.KHAMBENHID = _khambenhid;
			object.LOAIID = '0';
			object.DS_LYDO = [];
			object.KHOAID = _khoaId;
			object.PHONGID = _phongId;
			
			//L2PT-8978
			if(_configArr.KE_THUOC_LUU_TUONTAC == '1'){
				_objLstTT.push(object); 
				dlgPOPUP_TUONGTAC.close();
			}else{
				var objtuongtac = {};
				objtuongtac.MAUBENHPHAMID = '';
				objtuongtac.DANHSACHTT = [object];
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("THUOCTT.THEMTHUOC", JSON.stringify(objtuongtac));
				if(parseInt(result) > 0){
					dlgPOPUP_TUONGTAC.close();
					$("#hidTHUOCVTID").val('');
					$("#txtDS_THUOC").val('');
					$("#txtDS_HOATCHAT").val('');
					$("#txtDS_THUOC").trigger("focus");
					DlgUtil.showMsg("Đã lưu thông tin đồng ý khuyến cáo!");
					mySetLstTuongtac.add(object.THUOCVATTUID1 +'-' +object.THUOCVATTUID2+ '-0');
					mySetLstTuongtac.add(object.THUOCVATTUID2 +'-' +object.THUOCVATTUID1+ '-0');
					listIdTuongtac = listIdTuongtac + "," + result;
				}
				else{
					DlgUtil.showMsg("Có lỗi lưu thuốc tương tác!");
				}
			}
		});
		btnNotOK.click(function() {
			$('#dlgKHONGDONGY').css('display','');
			var _sql_par = [];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("THUOCTT.LYDO", _sql_par.join('$'));
			GridUtil.fetchGridData(_gridLYDOId, data_ar);

		});
		$('#btn_XACNHAN').click(function() {
			var object = {};
			FormUtil.setFormToObject('dlgPOPUP_TUONGTAC', '', object);
			if(mySetLstTuongtac.has(object.THUOCVATTUID1 + '-' + object.THUOCVATTUID2 + '-1' )){ //L2PT-32754
				dlgPOPUP_TUONGTAC.close();
				$("#txtSOLUONG_TONG").trigger("focus");
				return DlgUtil.showMsg("Không thể lưu. Đã từ chối khuyến cáo trước đấy!");
			}
			if(_bacsi_ke == '1' && $("#cboBACSIID").val() != '-1' && $("#cboBACSIID").val() != null){ //L2PT-22232
				object.NGUOIDUNG_ID 		= $("#cboBACSIID").val();
			}else{
				object.NGUOIDUNG_ID 		= _user_id;
			}
			object.HOSOBENHANID = $("#hidHOSOBENHANID").val();
			object.BENHNHANID = _benhnhanid;
			object.KHAMBENHID = _khambenhid;
			var rowKeys_select = $("#grdLYDO").getGridParam('selarrrow');
			if(rowKeys_select.length == 0 && !$("#txtNDKHAC").val()){
				DlgUtil.showMsg("Bạn phải chọn ít nhất 1 lý do!");
				return;
			}
			var _objLydo = [];
			if(rowKeys_select.length > 0){
				for(var j = 0; j <rowKeys_select.length; j++){
					var lydo  = {};
					var rowObject = $('#grdLYDO').getRowData(rowKeys_select[j]);
					lydo.LYDOID = rowObject.LYDOID
					_objLydo.push(lydo);
				}
			}
			object.LOAIID = '1';
			object.NDKHAC = $("#txtNDKHAC").val();
			object.DS_LYDO = _objLydo;
			object.KHOAID = _khoaId;
			object.PHONGID = _phongId;
			//L2PT-8978
			if(_configArr.KE_THUOC_LUU_TUONTAC == '1'){
				_objLstTT.push(object); 
				dlgPOPUP_TUONGTAC.close();
			}else{
				var objtuongtac = {};
				objtuongtac.MAUBENHPHAMID = '';
				objtuongtac.DANHSACHTT = [object];
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("THUOCTT.THEMTHUOC", JSON.stringify(objtuongtac));
				if(parseInt(result) > 0){
					dlgPOPUP_TUONGTAC.close();
					$("#txtSOLUONG_TONG").trigger("focus");
					//DlgUtil.showMsg("Đã lưu thông tin từ chối khuyến cáo!");
					mySetLstTuongtac.add(object.THUOCVATTUID1 +'-' +object.THUOCVATTUID2 + '-1');
					mySetLstTuongtac.add(object.THUOCVATTUID2 +'-' +object.THUOCVATTUID1 + '-1');
					listIdTuongtac = listIdTuongtac + "," + result;
				}
				else{
					DlgUtil.showMsg("Có lỗi lưu thuốc tương tác!");
				}
			}
		});
	}
	//tuyennx_add_end_20200825 L2PT-26711
	
	function isInt(n){
	    return Number(n) === n && n % 1 === 0;
	}

	function isFloat(n){
	    return Number(n) === n && n % 1 !== 0;
	}
	
	function splitDd(n){
		var duongdung = '...';
		if(typeof n !== "undefined" && n != null && n != ''){
			var hdsd = n.split("@");
			var songay = '';
			if(hdsd != null && hdsd.length > 6){
				duongdung = hdsd[2];
			}
			if(duongdung == ''){
				duongdung = '...';
			}
		}
		return duongdung;
	}
	
	function hiddenControl(str) {		
		var arSTR = str.split(';');
		for (var i =0; i<arSTR.length; i++) {
			$("#"+arSTR[i]).remove();
		}
	}
	
	function disableControl(str) {		
		var arSTR = str.split(';');
		for (var i =0; i<arSTR.length; i++) {
			$("#"+arSTR[i]).prop('disabled', true);
		}
	}
}
/*
Mã màn hình  : NTU02D021
File mã nguồn : NTU02D021_BuongDieuTri.js
<PERSON><PERSON><PERSON> đích  : Xu ly nghiep vu cho man hinh buong dieu tri
Tham số vào : 1
Người lập trình	<PERSON> cập nhật  <PERSON>hi chú
HUONGPV	- 1/8/2016 - Comment
*/
//menu cho man hinh ngoai tru
var ctl_ar_ngoaitru = [ {
	type : 'buttongroup',
	id : 'btnPrintCa',
	icon : 'print',
	text : 'In ký số',
	children : [ {
		id : 'printca_11',
		icon : 'print',
		text : 'Giấy hẹn khám',
		hlink : '#'
	}, {
		id : 'printca_153',
		icon : 'print',
		text : 'In bệnh án chi tiết',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnPrint',
	icon : 'print',
	text : 'In ấn',
	children : [ {
		id : 'print_1',
		icon : 'print',
		text : '<PERSON>ếu chỉ định CLS chung',
		hlink : '#'
	}, {
		id : 'print_135',
		icon : 'print',
		text : 'S<PERSON> bệnh án ngoại trú_YHCT_1941',
		hlink : '#'
	} // L2PT-5320
	, {
		id : 'print_3',
		icon : 'print',
		text : 'Đơn thuốc',
		hlink : '#'
	}, {
		id : 'print_4',
		icon : 'print',
		text : 'Bảng kê',
		hlink : '#'
	}, {
		id : 'print_bhyt',
		icon : 'print',
		text : 'Bảng kê bhyt',
		hlink : '#'
	}, {
		id : 'print_vp',
		icon : 'print',
		text : 'Bảng kê viện phí',
		hlink : '#'
	}, {
		id : 'print_ntmp',
		icon : 'print',
		text : 'Bảng kê miễn phí',
		hlink : '#'
	} // L2PT-9972 duonghn
	, {
		id : 'print_33',
		icon : 'print',
		text : 'Bảng kê chưa thanh toán',
		hlink : '#'
	}, {
		id : 'print_34',
		icon : 'print',
		text : 'Bảng kê 3455',
		hlink : '#'
	}//START L2PT-452
	, {
		id : 'print_10',
		icon : 'print',
		text : 'Bảng kê VT hao phí',
		hlink : '#'
	}, {
		id : 'print_5',
		icon : 'print',
		text : 'Giấy ra viện',
		hlink : '#'
	}, {
		id : 'print_5_doc',
		icon : 'print',
		text : 'Giấy ra viện dọc',
		hlink : '#'
	}, {
		id : 'print_8',
		icon : 'print',
		text : 'Phiếu công khai thuốc',
		hlink : '#'
	}, {
		id : 'print_22',
		icon : 'print',
		text : 'Phiếu công khai vật tư',
		hlink : '#'
	}, {
		id : 'print_ckyltn',
		icon : 'print',
		text : 'Phiếu công khai y lệnh theo ngày',
		hlink : '#'
	}, {
		id : 'print_9',
		icon : 'print',
		text : 'In tờ điều trị',
		hlink : '#'
	}, {
		id : 'print_11',
		icon : 'print',
		text : 'Giấy hẹn khám',
		hlink : '#'
	}, {
		id : 'print_12',
		icon : 'print',
		text : 'Phiếu công khai dịch vụ',
		hlink : '#'
	},  {
		id : 'print_ckdvtdg2',
		icon : 'print',
		text : 'Phiếu CKDV treo đầu giường',
		hlink : '#'
	}, {
		id : 'print_15',
		icon : 'print',
		text : 'Phiếu theo dõi thủ thuật',
		hlink : '#'
	}, {
		id : 'print_16',
		icon : 'print',
		text : 'Phiếu tổng hợp y lệnh',
		hlink : '#'
	}, {
		id: 'print_17',
		icon: 'print',
		text: 'In xét nghiệm chung',
		hlink: '#'
	}, {
		id : 'print_182',
		icon : 'print',
		text : 'In CĐHA chung',
		hlink : '#'
	}, {
		id : 'print_18',
		icon : 'print',
		text : 'In tách bảng kê theo khoa',
		hlink : '#'
	}, {
		id : 'print_30',
		icon : 'print',
		text : 'Giấy chuyển viện',
		hlink : '#'
	}, {
		id : 'print_160',
		icon : 'print',
		text : 'Giấy chuyển viện .doc',
		hlink : '#'
	}, {
		id : 'print_13',
		icon : 'print',
		text : 'In phiếu khám bệnh',
		hlink : '#'
	}, {
		id : 'print_13_word',
		icon : 'print',
		text : 'In phiếu khám bệnh .doc',
		hlink : '#'
	}, {
		id : 'print_14',
		icon : 'print',
		text : 'In giấy chứng nhận nghỉ việc hưởng BHXH',
		hlink : '#'
	}, {
		id : 'print_151',
		icon : 'print',
		text : 'In tóm tắt bệnh án Quân nhân',
		hlink : '#'
	}, {
		id : 'print_152',
		icon : 'print',
		text : 'In giấy cam kết truyền máu',
		hlink : '#'
	}, {
		id : 'print_153',
		icon : 'print',
		text : 'In bệnh án chi tiết',
		hlink : '#'
	}, {
		id : 'print_154',
		icon : 'print',
		text : 'Phiếu sàng lọc đánh giá dinh dưỡng',
		hlink : '#'
	}, {
		id : 'print_155',
		icon : 'print',
		text : 'Nghỉ hưởng BHXH',
		hlink : '#'
	}, {
		id : 'print_156',
		icon : 'print',
		text : 'Phiếu theo dõi điều trị ngoại trú',
		hlink : '#'
	}, {
		id : 'print_159',
		icon : 'print',
		text : 'Đơn thuốc không thuốc',
		hlink : '#'
	}, {
		id : 'print_162',
		icon : 'print',
		text : 'Phiếu lọc máu',
		hlink : '#'
	}, {
		id : 'print_164',
		icon : 'print',
		text : 'Phiếu tổng hợp thanh toán viện phí',
		hlink : '#'
	}, {
		id : 'print_210',
		icon : 'print',
		text : 'Giấy cam kết chạy thận nhân tạo',
		hlink : '#'
	}, {
		id : 'print_bkdsdv',
		icon : 'print',
		text : 'Bảng kê đối soát dịch vụ',
		hlink : '#'
	}, {
		id : 'print_165',
		icon : 'print',
		text : 'Phiếu khám và chỉ định phục hồi chức năng',
		hlink : '#'
	}, {
		id : 'print_178',
		icon : 'print',
		text : 'In phiếu thực hiện kỹ thuật PHCN',
		hlink : '#'
	} //L2PT-10835
	, {
		id : 'print_203',
		icon : 'print',
		text : 'In phiếu ăn',
		hlink : '#'
	}, {
		id : 'print_CAMKETPTTT',
		icon : 'print',
		text : 'Giấy cam kế phẫu thuật thủ thuật',
		hlink : '#'
	}, {
		id : 'print_201',
		icon : 'print',
		text : 'Thông tin lịch hẹn',
		hlink : '#'
	}, {
		id : 'print_180',
		icon : 'print',
		text : 'CN In giấy cam đoan phụ thu CT Scanner',
		hlink : '#'
	}, {
		id : 'print_181',
		icon : 'print',
		text : 'Sổ thuốc tủ trực',
		hlink : '#'
	}, { //L2PT-26520
		id : 'print_hen',
		icon : 'print',
		text : 'Đơn thuốc HEN',
		hlink : '#'
	}, {
		id : 'print_copd',
		icon : 'print',
		text : 'Đơn thuốc COPD',
		hlink : '#'
	}, {
		id : 'print_tdsdgb',
		icon : 'print',
		text : 'Bảng theo dõi sử dụng giường bệnh',
		hlink : '#'
	}  ]
}, {
	type : 'button',
	id : 'btnStart',
	icon : 'dsbenhnhan',
	text : 'DS BN'
}, {
	type : 'button',
	id : 'btnCall',
	icon : 'volume-up',
	text : 'Gọi khám'
}, {
	type : 'buttongroup',
	id : 'btnBan',
	icon : 'benhan',
	text : 'Bệnh án',
	children : [ {
		id : 'ban_1',
		icon : 'benhan',
		text : 'Bệnh án chung',
		hlink : '#'
	}, {
		id : 'ban_2',
		icon : 'benhan',
		text : 'Bệnh án điều trị ngoại trú',
		hlink : '#'
	}, {
		id : 'ban_3',
		icon : 'benhan',
		text : 'Sơ kết điều trị',
		hlink : '#'
	}, {
		id : 'ban_4',
		icon : 'benhan',
		text : 'Tóm tắt bệnh án',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnTreat',
	icon : 'dieutri',
	text : 'Điều trị',
	children : [ {
		id : 'treat_1',
		icon : 'dieutri',
		text : 'Tạo phiếu điều trị',
		hlink : '#'
	}, {
		id : 'treat_4',
		icon : 'dieutri',
		text : 'Tạo phiếu truyền dịch',
		hlink : '#'
	}, {
		id : 'treat_5',
		icon : 'dieutri',
		text : 'Tạo phiếu thử phản ứng thuốc',
		hlink : '#'
	}, {
		id : 'treat_6',
		icon : 'dieutri',
		text : 'Tạo biên bản hội chẩn',
		hlink : '#'
	}, {
		id : 'treat_9',
		icon : 'dichvu',
		text : 'Điều trị cắt cơn nghiện',
		hlink : '#'
	}, {
		id : 'treat_cc',
		icon : 'dichvu',
		text : 'Giấy xác nhận cấp cứu',
		hlink : '#'
	}, {
		id : 'treat_ttdd',
		icon : 'dieutri',
		text : 'Đánh giá tình trạng dinh dưỡng(Trưởng thành)',
		hlink : '#'
	}, {
		id : 'treat_ttdd_nhi',
		icon : 'dieutri',
		text : 'Đánh giá tình trạng dinh dưỡng(Nhi)',
		hlink : '#'
	}, {
		id : 'treatdt_18_1',
		icon : 'dieutri',
		text : 'Đánh giá tình trạng dinh dưỡng >=18 tuổi, không mang thai',
		hlink : '#'
	}//Begin_HaNv_301220: L2PT-31735
	, {
		id : 'treat_bgtpt',
		icon : 'dieutri',
		text : 'Bàn giao người bệnh trước phẫu thuật',
		hlink : '#'
	}, {
		id : 'treat_26',
		icon : 'dieutri',
		text : 'Phiếu theo dõi chạy thận nhân tạo',
		hlink : '#'
	}//DoanPV_20210414 - BVTM-1012
	, {
		id : 'treat_21',
		icon : 'dieutri',
		text : 'Tạo phiếu khai thác tiền sử dị ứng',
		hlink : '#'
	}//L2PT-4999
	, {
		id : 'treat_themphieu',
		icon : 'dieutri',
		text : 'Thêm phiếu',
		hlink : '#'
	}
	, { 
		id : 'treatdt_pktm',
		icon : 'dieutri',
		text : 'Phiếu khám tiền mê',
		hlink : '#'
	}, { //L2PT-23979
		id : 'treat_dgbd_d155',
		icon : 'dieutri',
		text : 'Phiếu đánh giá ban đầu',
		hlink : '#'
	}
	]
}, {
	type : 'button',
	id : 'btnService',
	icon : 'dichvu',
	text : 'Dịch vụ',
	hlink : '#'
}, {
	type : 'buttongroup',
	id : 'btndrug',
	icon : 'thuoc',
	text : 'Thuốc',
	hlink : '#',
	children : [ {
		id : 'group_0',
		icon : '',
		text : 'Thuốc',
		hlink : '#',
		group : true
	}, {
		id : 'drug_khothuoc',
		icon : 'thuoc',
		text : 'Tạo phiếu thuốc từ kho',
		hlink : '#'
	}, {
		id : 'drug_khothuoc1',
		icon : 'thuoc',
		text : 'Kê đơn thuốc kho BHYT ngoại trú',
		hlink : '#'
	}, {
		id : 'drug_khothuoc2',
		icon : 'thuoc',
		text : 'Tạo phiếu thuốc từ kho dược ',
		hlink : '#'
	}, {
		id : 'drug_tutruc',
		icon : 'thuoc',
		text : 'Tạo phiếu thuốc tủ trực',
		hlink : '#'
	}, {
		id : 'drug_2',
		icon : 'thuoc',
		text : 'Tạo phiếu trả thuốc',
		hlink : '#'
	}, {
		id : 'drug_dtnhathuoc',
		icon : 'thuoc',
		text : 'Mua thuốc nhà thuốc',
		hlink : '#'
	}, {
		id : 'drug_phieuđinhuong',
		icon : 'thuoc',
		text : 'Phiếu tư vấn dinh dưỡng (Kê)',
		hlink : '#'
	} //HaNv_220521: L2PT-3147
	, {
		id : 'group_1',
		icon : '',
		text : 'Vật tư',
		hlink : '#',
		group : true
	}, {
		id : 'drug_3',
		icon : 'thuoc',
		text : 'Tạo phiếu vật tư',
		hlink : '#'
	}, {
		id : 'drug_4',
		icon : 'thuoc',
		text : 'Tạo phiếu trả vật tư',
		hlink : '#'
	}, {
		id : 'group_2',
		icon : '',
		text : 'Khác',
		hlink : '#',
		group : true
	}, {
		id : 'drug_6',
		icon : 'thuoc',
		text : 'Đơn thuốc mua ngoài',
		hlink : '#'
	}, {
		id : 'drug_8',
		icon : 'thuoc',
		text : 'Tạo phiếu hao phí',
		hlink : '#'
	}, {
		id : 'drug_hpvt',
		icon : 'thuoc',
		text : 'Tạo phiếu VT hao phí',
		hlink : '#'
	}, {
		id : 'drug_thuocdy',
		icon : 'thuoc',
		text : 'THUỐC ĐÔNG Y',
		hlink : '#',
		group : true
	}, {
		id : 'drug_1dy',
		icon : 'thuoc',
		text : 'Tạo đơn thuốc đông y',
		hlink : '#'
	}, {
		id : 'drug_2dy',
		icon : 'thuoc',
		text : 'Tạo phiếu trả thuốc đông y',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnhandling',
	icon : 'xutri',
	text : 'Xử trí',
	hlink : '#',
	children : [ {
		id : 'handling_3',
		icon : 'xutri',
		text : 'Xử trí',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnkh',
	icon : 'xutrikethop',
	text : 'Chuyên khoa',
	hlink : '#',
	cssClass : 'wd120',
	children : [ {
		id : 'handling_7',
		icon : 'xutri',
		text : 'Khám chuyên khoa (nội viện)',
		hlink : '#'
	}, {
		id : 'handling_8',
		icon : 'xutri',
		text : 'Kết thúc Khám chuyên khoa (nội viện)',
		hlink : '#'
	}, {
		id : 'handling_9',
		icon : 'xutri',
		text : 'Tra cứu Khám chuyên khoa (nội viện)',
		hlink : '#'
	}, {
		id : 'handling_10',
		icon : 'xutri',
		text : 'Khám chuyên khoa phòng khám',
		hlink : '#'
	}, {
		id : 'handling_11',
		icon : 'xutri',
		text : 'Lịch sử khám chuyên khoa',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnhistory',
	icon : 'lichsu',
	text : 'Lịch sử',
	hlink : '#',
	children : [ {
		id : 'history_1',
		icon : 'lichsu',
		text : 'Lịch sử bệnh án',
		hlink : '#'
	}, {
		id : 'history_2',
		icon : 'lichsu',
		text : 'Lịch sử điều trị',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnhospitalfee',
	icon : 'thutien',
	text : 'Viện phí',
	hlink : '#',
	children : [ {
		id : 'hospitalfee_1',
		icon : 'thutien',
		text : 'Thanh toán viện phí',
		hlink : '#'
	}, {
		id : 'hospitalfee_2',
		icon : 'thutien',
		text : 'Thông tin viện phí',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnfunction',
	icon : 'khac',
	text : 'Khác',
	hlink : '#',
	children : [ {
		id : 'function_5',
		icon : 'khac',
		text : 'Tai nạn thương tích',
		hlink : '#'
	}, {
		id : 'function_7',
		icon : 'khac',
		text : 'Ra viện, lịch hẹn',
		hlink : '#'
	}, {
		id : 'function_8',
		icon : 'khac',
		text : 'Chỉ định dịch vụ cho khoa',
		hlink : '#'
	}, {
		id : 'function_13',
		icon : 'khac',
		text : 'Phiếu duyệt dược CLS',
		hlink : '#'
	}, {
		id : 'function_14',
		icon : 'khac',
		text : 'Nghỉ hưởng BHXH',
		hlink : '#'
	}, {
		id : 'function_15',
		icon : 'khac',
		text : 'Biểu đồ chuyển dạ',
		hlink : '#'
	}, {
		id : 'function_17',
		icon : 'khac',
		text : 'Phiếu gây mê hồi sức',
		hlink : '#'
	}, {
		id : 'function_16',
		icon : 'khac',
		text : 'Thông tin dị ứng',
		hlink : '#'
	}, {
		id : 'function_18',
		icon : 'khac',
		text : 'Tạo phiếu khám sinh sản',
		hlink : '#'
	}, {
		id : 'function_21',
		icon : 'khac',
		text : 'Chuyển viện',
		hlink : '#'
	}, {
		id : 'function_22',
		icon : 'khac',
		text : 'Sửa tỷ lệ thẻ',
		hlink : '#'
	}, {
		id : 'function_25',
		icon : 'khac',
		text : 'Phiếu yêu cầu sử dụng kháng sinh ưu tiên quản lý',
		hlink : '#'
	}, {
		id : 'function_33',
		icon : 'khac',
		text : 'QL bệnh án',
		hlink : '#'
	}, {//HaNv_111022: L2PT-27457
		id : 'function_19',
		icon : 'khac',
		text : 'Nghỉ dưỡng thai',
		hlink : '#'
	}, {
		id : 'ban_QLHIV',
		icon : 'khac',
		text : 'QL bệnh nhân HIV',
		hlink : '#'
	}, {//HaNv_261023: L2PT-57867
		id : 'f_dbbenh',
		icon : 'khac',
		text : 'Diễn biến bệnh nhân',
		hlink : '#'
	} ]
}, {
	type : 'button',
	id : 'btnSKDT',
	icon : '',
	text : 'SKĐT',
	hlink : '#'
} ];
//menu cho man hinh noi tru
var ctl_ar_noitru = 
	[ {
	type : 'buttongroup',
	id : 'btnPrintCa',
	icon : 'print',
	text : 'In ký số',
	children : [ {
		id : 'printca_11',
		icon : 'print',
		text : 'Giấy hẹn khám',
		hlink : '#'
	}, {
		id : 'printca_12',
		icon : 'print',
		text : 'Phiếu công khai dịch vụ',
		hlink : '#'
	}, {
		id : 'printca_13',
		icon : 'print',
		text : 'Phiếu khám bệnh',
		hlink : '#'
	}, {
		id : 'printca_153',
		icon : 'print',
		text : 'In bệnh án chi tiết',
		hlink : '#'
	}, {
		id : 'printca_169',
		icon : 'print',
		text : 'Phiếu xác nhận xét nghiệm HIV',
		hlink : '#'
	}, {
		id : 'printca_179',
		icon : 'print',
		text : 'In phiếu cam kết xét nghiệm HCG',
		hlink : '#'
	}]
	}, 
	{
	type : 'buttongroup',
	id : 'btnPrint',
	icon : 'print',
	text : 'In ấn',
	children : [ {
		id : 'print_1',
		icon : 'print',
		text : 'Phiếu chỉ định CLS chung',
		hlink : '#'
	}, {
		id : 'print_136',
		icon : 'print',
		text : 'Sổ bệnh án nội trú_YHCT_1941',
		hlink : '#'
	} //L2PT-5320
	, {
		id : 'print_137',
		icon : 'print',
		text : 'Sổ bệnh án nội trú nhi_YHCT_1941',
		hlink : '#'
	} // L2PT-5320
	, {
		id : 'print_138',
		icon : 'print',
		text : 'Sổ bệnh án PHCN_YHCT_3730',
		hlink : '#'
	} // L2PT-10960
	, {
		id : 'print_139',
		icon : 'print',
		text : 'Sổ bệnh án PHCN_YHCT_3730 CBG',
		hlink : '#'
	} // L2PT-10960
	, {
		id : 'print_3',
		icon : 'print',
		text : 'Đơn thuốc',
		hlink : '#'
	}
	//ductx - BVTM-963 in phieu su dung tu truc
	, {
		id : 'print_99',
		icon : 'print',
		text : 'Phiếu sử dụng thuốc tủ trực',
		hlink : '#'
	}, {
		id : 'print_4',
		icon : 'print',
		text : 'Bảng kê',
		hlink : '#'
	}, {
		id : 'print_bhyt',
		icon : 'print',
		text : 'Bảng kê bhyt',
		hlink : '#'
	}, {
		id : 'print_vp',
		icon : 'print',
		text : 'Bảng kê viện phí',
		hlink : '#'
	}, {
		id : 'print_dvkhac',
		icon : 'print',
		text : 'Bảng kê dịch vụ khác',
		hlink : '#'
	}, {
		id : 'print_ntmp',
		icon : 'print',
		text : 'Bảng kê chi phí điều trị nội trú miễn phí',
		hlink : '#'
	}, {
		id : 'print_33',
		icon : 'print',
		text : 'Bảng kê chưa thanh toán',
		hlink : '#'
	}, {
		id : 'print_34',
		icon : 'print',
		text : 'Bảng kê 3455',
		hlink : '#'
	}//START L2PT-452
	, {
		id : 'print_10',
		icon : 'print',
		text : 'Bảng kê VT hao phí',
		hlink : '#'
	}, {
		id : 'print_5',
		icon : 'print',
		text : 'Giấy ra viện',
		hlink : '#'
	}, {
		id : 'print_5_doc',
		icon : 'print',
		text : 'Giấy ra viện dọc',
		hlink : '#'
	}, {
		id : 'print_172',
		icon : 'print',
		text : 'Giấy ra viện Dã Chiến',
		hlink : '#'
	}, {
		id : 'print_8',
		icon : 'print',
		text : 'Phiếu công khai thuốc',
		hlink : '#'
	}, {
		id : 'print_22',
		icon : 'print',
		text : 'Phiếu công khai vật tư',
		hlink : '#'
	}, {
		id : 'print_ckyltn',
		icon : 'print',
		text : 'Phiếu công khai y lệnh theo ngày',
		hlink : '#'
	}, {
		id : 'print_9',
		icon : 'print',
		text : 'In tờ điều trị',
		hlink : '#'
	}, {
		id : 'print_12',
		icon : 'print',
		text : 'Phiếu công khai dịch vụ',
		hlink : '#'
	},
	{
		id : 'print_ckdvtdg2',
		icon : 'print',
		text : 'Phiếu CKDV treo đầu giường',
		hlink : '#'
	}, {
		id : 'print_15',
		icon : 'print',
		text : 'Phiếu theo dõi thủ thuật',
		hlink : '#'
	}, {
		id : 'print_16',
		icon : 'print',
		text : 'Phiếu tổng hợp y lệnh',
		hlink : '#'
	}, {
		id : 'print_171',
		icon : 'print',
		text : 'Phiếu tổng hợp y lệnh tổng',
		hlink : '#'
	} //L2PT-6463
	, {
		id : 'print_17',
		icon : 'print',
		text : 'In xét nghiệm chung',
		hlink : '#'
	}, {
		id : 'print_18',
		icon : 'print',
		text : 'In tách bảng kê theo khoa',
		hlink : '#'
	}, {
		id : 'print_19',
		icon : 'print',
		text : 'In sơ kết điều trị ',
		hlink : '#'
	} //HaNv_11092018: L2HOTRO-5885
	, {
		id : 'print_20',
		icon : 'print',
		text : 'Phiếu đăng ký điều trị Lao',
		hlink : '#'
	}, {
		id : 'print_30',
		icon : 'print',
		text : 'Giấy chuyển viện',
		hlink : '#'
	}, {
		id : 'print_160',
		icon : 'print',
		text : 'Giấy chuyển viện .doc',
		hlink : '#'
	}, {
		id : 'print_31',
		icon : 'print',
		text : 'Giấy tóm tắt bệnh án',
		hlink : '#'
	}//nghiant 24102018 L2HOTRO-11256
	, {
		id : 'print_21',
		icon : 'print',
		text : 'In phiếu bệnh nhân Lao',
		hlink : '#'
	}, {
		id : 'print_13',
		icon : 'print',
		text : 'In phiếu khám bệnh',
		hlink : '#'
	}, {
		id : 'print_13_mat',
		icon : 'print',
		text : 'In phiếu khám bệnh chuyên khoa mắt',
		hlink : '#'
	}, {
		id : 'print_13_tmh',
		icon : 'print',
		text : 'In phiếu khám bệnh chuyên khoa tai mũi họng',
		hlink : '#'
	}, {
		id : 'print_13_rhm',
		icon : 'print',
		text : 'In phiếu khám bệnh chuyên khoa răng hàm mặt',
		hlink : '#'
	}, {
		id : 'print_13_word',
		icon : 'print',
		text : 'In phiếu khám bệnh .doc',
		hlink : '#'
	}, {
		id : 'print_14',
		icon : 'print',
		text : 'In giấy chứng nhận nghỉ việc hưởng BHXH',
		hlink : '#'
	}, {
		id : 'print_151',
		icon : 'print',
		text : 'In tóm tắt bệnh án quân nhân',
		hlink : '#'
	}, {
		id : 'print_152',
		icon : 'print',
		text : 'In giấy cam kết truyền máu',
		hlink : '#'
	}, {
		id : 'print_153',
		icon : 'print',
		text : 'In bệnh án chi tiết',
		hlink : '#'
	}, {
		id : 'print_154',
		icon : 'print',
		text : 'Phiếu sàng lọc kiểm soát bệnh nhân trước đánh giá dinh dưỡng',
		hlink : '#'
	}, {
		id : 'print_155',
		icon : 'print',
		text : 'Phiếu huỷ thuốc tại khoa',
		hlink : '#'
	}, {
		id : 'print_156',
		icon : 'print',
		text : 'Phiếu theo dõi điều trị nội trú',
		hlink : '#'
	}, {
		id : 'print_157',
		icon : 'print',
		text : 'Nghỉ hưởng BHXH',
		hlink : '#'
	}, {
		id : 'print_158',
		icon : 'print',
		text : 'Giấy hẹn khám doc',
		hlink : '#'
	}, {
		id : 'print_rvbnct',
		icon : 'print',
		text : 'Giấy ra viện cho bn chuyển tuyến',
		hlink : '#'
	}, {
		id : 'print_159',
		icon : 'print',
		text : 'Đơn thuốc không thuốc',
		hlink : '#'
	}, {
		id : 'print_161',
		icon : 'print',
		text : 'In phiếu thực hiện y lệnh',
		hlink : '#'
	}, {
		id : 'print_162',
		icon : 'print',
		text : 'Phiếu lọc máu',
		hlink : '#'
	}, {
		id : 'print_163',
		icon : 'print',
		text : 'Giấy chứng nhận phẫu thuật',
		hlink : '#'
	}, {
		id : 'print_164',
		icon : 'print',
		text : 'Phiếu tổng hợp thanh toán viện phí',
		hlink : '#'
	}, {
		id : 'print_202',
		icon : 'print',
		text : 'In giấy xác nhận bệnh nhân đang nằm viện',
		hlink : '#'
	}, {
		id : 'print_165',
		icon : 'print',
		text : 'Phiếu đánh giá tình trạng cấp cứu nhi khoa ',
		hlink : '#'
	}, {
		id : 'print_166',
		icon : 'print',
		text : 'In phiếu điều trị các khoa',
		hlink : '#'
	}, {
		id : 'print_phieuduoccatcom',
		icon : 'print',
		text : 'Phiếu được cắt cơm',
		hlink : '#'
	}, {
		id : 'print_168',
		icon : 'print',
		text : 'Phiếu được cắt cơm',
		hlink : '#'
	}, {
		id : 'print_gcntt',
		icon : 'print',
		text : 'Giấy chứng nhận thương tích',
		hlink : '#'
	}, {
		id : 'print_169',
		icon : 'print',
		text : 'Phiếu công khai thuốc HTSS',
		hlink : '#'
	}, {
		id : 'print_170',
		icon : 'print',
		text : 'Thẻ theo dõi dị ứng',
		hlink : '#'
	}, {
		id : 'print_bkdsdv',
		icon : 'print',
		text : 'Bảng kê đối soát dịch vụ',
		hlink : '#'
	}, {
		id : 'print_174',
		icon : 'print',
		text : 'PHIẾU THEO DÕI Xác định tình trạng nghiện ma túy tổng hợp chất dạng Amphetamine (ATS)',
		hlink : '#'
	}, {
		id : 'print_175',
		icon : 'print',
		text : 'BẢNG KIỂM TRƯỚC TIÊM CHỦNG ĐỐI VỚI TRẺ SƠ SINH',
		hlink : '#'
	}, {
		id : 'print_176',
		icon : 'print',
		text : 'PHIẾU TRẢ LỜI KẾT QUẢ Về việc xác định tình trạng nghiện ma túy',
		hlink : '#'
	}, {
		id : 'print_177',
		icon : 'print',
		text : 'PHIẾU THEO DÕI Xác định tình trạng nghiện ma túy nhóm Opiats (các chất dạng thuốc phiện',
		hlink : '#'
	}, {
		id : 'print_178',
		icon : 'print',
		text : 'In phiếu thực hiện kỹ thuật PHCN',
		hlink : '#'
	}, {
		id : 'print_gbt',
		icon : 'print',
		text : 'In giấy báo tử',
		hlink : '#'
	}, {
		id : 'print_bbkdtv',
		icon : 'print',
		text : 'Biên bản kiểm điểm tử vong',
		hlink : '#'
	}, {
		id : 'print_congkhaithuoctt',
		icon : 'print',
		text : 'Phiếu công khai thuốc và thủ thuật',
		hlink : '#'
	}, {
		id : 'print_179',
		icon : 'print',
		text : 'In giấy cam kết XN beta HCG',
		hlink : '#'
	}, {
		id : 'print_ttdd', //L2PT-26426 start
		icon : 'print',
		text : 'Đánh giá tình trạng dinh dưỡng(Trưởng thành)',
		hlink : '#'
	}, {
		id : 'print_ttdd_nhi',
		icon : 'print',
		text : 'Đánh giá tình trạng dinh dưỡng(Nhi)',
		hlink : '#'
	}, {
		id : 'print_pnmt',
		icon : 'print',
		text : 'Sàng lọc dinh dưỡng cho phụ nữ mang thai',
		hlink : '#'
	}, {
		id : 'print_18_1',
		icon : 'print',
		text : 'Đánh giá tình trạng dinh dưỡng >=18 tuổi, không mang thai',
		hlink : '#' //L2PT-26426 end
	}, {
		id : 'print_pkck',
		icon : 'print',
		text : 'Phiếu khám chuyên khoa',
		hlink : '#'
	}, {
		id : 'print_gmhs',
		icon : 'print',
		text : 'Phiếu gây mê hồi sức',
		hlink : '#'
	}, {
		id : 'print_thtvt',
		icon : 'print',
		text : 'Phiếu tổng hợp thuốc vật tư',
		hlink : '#'
	}, { //L2PT-23661 start
		id : 'print_nntv',
		icon : 'print',
		text : 'Phiếu chẩn đoán nguyên nhân tử vong',
		hlink : '#'
	}, {
		id : 'print_xinve',
		icon : 'print',
		text : 'Phiếu tóm tắt thông tin người bệnh nặng xin về',
		hlink : '#'
	}, {
		id : 'print_11',
		icon : 'print',
		text : 'Giấy hẹn khám lại',
		hlink : '#'
	}, {
		id : 'print_bktc',
		icon : 'print',
		text : 'Bảng kiểm tiêm chủng đối với trẻ sơ sinh',
		hlink : '#' //L2PT-23661 end
	}, { //L2PT-26520
		id : 'print_hen',
		icon : 'print',
		text : 'Đơn thuốc HEN',
		hlink : '#'
	}, {
		id : 'print_copd',
		icon : 'print',
		text : 'Đơn thuốc COPD',
		hlink : '#'
	}, { //L2PT-27520
		id : 'print_dmmm',
		icon : 'print',
		text : 'In phiếu XN đường máu mao mạch tại giường',
		hlink : '#'
	}, { //L2PT-27520
		id : 'print_ckdtnt',
		icon : 'print',
		text : 'Bản cam kết về việc điều trị nội trú',
		hlink : '#'
	}, { //L2PT-68634
		id : 'print_gcdcnntcl',
		icon : 'print',
		text : 'Giấy cam đoan chấp nhận nộp tiền chênh lệch',
		hlink : '#'
	}, { //L2PT-68635
		id : 'print_gcdtphdtnt',
		icon : 'print',
		text : 'Giấy cam đoan',
		hlink : '#'
	}, { //L2PT-68636
		id : 'print_gcdcnptvgm',
		icon : 'print',
		text : 'Giấy cam đoan chấp nhận phẫu thuật và gây mê',
		hlink : '#'
	}, { //L2PT-68637
		id : 'print_ptvvgtcbn',
		icon : 'print',
		text : 'Phiếu tư vấn và giải thích cho bệnh nhân',
		hlink : '#'
	}, { //L2PT-68638
		id : 'print_bvctsddv',
		icon : 'print',
		text : 'Văn bản chấp thuận sử dụng dịch vụ',
		hlink : '#'
	}      
	]
}, {
	type : 'button',
	id : 'btnStart',
	icon : 'dsbenhnhan',
	text : 'DS BN'
}, {
	type : 'buttongroup',
	id : 'btnBan',
	icon : 'benhan',
	text : 'Bệnh án',
	children : [ {
		id : 'ban_1',
		icon : 'benhan',
		text : 'Bệnh án chung',
		hlink : '#'
	}, {
		id : 'ban_2',
		icon : 'benhan',
		text : 'Bệnh án chi tiết',
		hlink : '#'
	}, {
		id : 'ban_3',
		icon : 'benhan',
		text : 'Sơ kết điều trị',
		hlink : '#'
	}, {
		id : 'ban_4',
		icon : 'benhan',
		text : 'Tóm tắt bệnh án',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnTreat',
	icon : 'dieutri',
	text : 'Điều trị',
	children : [ {
		id : 'treat_1',
		icon : 'dieutri',
		text : 'Tạo phiếu điều trị',
		hlink : '#'
	}, {
		id : 'treat_5',
		icon : 'dieutri',
		text : 'Tạo phiếu thử phản ứng thuốc',
		hlink : '#'
	}, {
		id : 'treat_6',
		icon : 'dieutri',
		text : 'Tạo biên bản hội chẩn',
		hlink : '#'
	}, {
		id : 'treat_6_1',
		icon : 'dieutri',
		text : 'Tạo biên bản hội chẩn trước PT',
		hlink : '#'
	}, {
		id : 'treat_14',
		icon : 'dieutri',
		text : 'Tạo phiếu hội chẩn, tư vấn dinh dưỡng',
		hlink : '#'
	}, {
		id : 'treat_7',
		icon : 'dieutri',
		text : 'Tạo phiếu truyền máu',
		hlink : '#'
	}, {
		id : 'treat_8',
		icon : 'dichvu',
		text : 'Tạo phiếu công khám',
		hlink : '#'
	}, {
		id : 'treat_9',
		icon : 'dichvu',
		text : 'Điều trị cắt cơn nghiện',
		hlink : '#'
	}, {
		id : 'treat_10',
		icon : 'dichvu',
		text : 'Dị ứng thuốc',
		hlink : '#'
	}, {
		id : 'treat_13',
		icon : 'dichvu',
		text : 'Sàng lọc và đánh giá dinh dưỡng',
		hlink : '#'
	}, {
		id : 'treatdt_tdlm',
		icon : 'dieutri',
		text : 'Tạo phiếu chăm theo dõi lọc máu',
		hlink : '#'
	}, {
		id : 'treat_15',
		icon : 'dichvu',
		text : 'Tạo phiếu suất ăn',
		hlink : '#'
	}
	//tuyennx_add_start_20190108 L2PT-295
	, {
		id : 'treat_17',
		icon : 'dichvu',
		text : 'Tạo hội chẩn thuốc duyệt lãnh đạo',
		hlink : '#'
	}
	//tuyennx_add_end_20190108
	, {
		id : 'treat_bbhctbvbd',
		icon : 'dichvu',
		text : 'Tạo biên bản hội chuẩn thuốc BVBĐ',
		hlink : '#'
	}
	, {
		id : 'treat_18',
		icon : 'dichvu',
		text : 'Chuyển viện',
		hlink : '#'
	}, {
		id : 'treat_19',
		icon : 'dieutri',
		text : 'Tạo biên bản kiểm điểm tử vong',
		hlink : '#'
	}, {
		id : 'treat_20',
		icon : 'dieutri',
		text : 'Tạo phiếu sơ sinh',
		hlink : '#'
	}, {
		id : 'treat_cc',
		icon : 'dichvu',
		text : 'Giấy xác nhận cấp cứu',
		hlink : '#'
	}, {
		id : 'treat_21',
		icon : 'dieutri',
		text : 'Tạo phiếu khai thác tiền sử dị ứng',
		hlink : '#'
	}, {
		id : 'treat_22',
		icon : 'dieutri',
		text : 'Lên lịch mổ',
		hlink : '#'
	}, {
		id : 'treat_ttdd',
		icon : 'dieutri',
		text : 'Đánh giá tình trạng dinh dưỡng(Trưởng thành)',
		hlink : '#'
	}, {
		id : 'treat_ttdd_nhi',
		icon : 'dieutri',
		text : 'Đánh giá tình trạng dinh dưỡng(Nhi)',
		hlink : '#'
	}, {
		id : 'treatdt_18_1',
		icon : 'dieutri',
		text : 'Đánh giá tình trạng dinh dưỡng >=18 tuổi, không mang thai',
		hlink : '#'
	}//Begin_HaNv_301220: L2PT-31735
	// L2PT-11223 duonghn start: chuyen sldd tu hanh chinh ve dieu tri
	, {
		id : 'treatdt_18',
		icon : 'dieutri',
		text : 'Đánh giá tình trạng dinh dưỡng cho phụ nữ mang thai',
		hlink : '#'
	}
	// L2PT-11223 duonghn end
	, {
		id : 'treat_bgtpt',
		icon : 'dieutri',
		text : 'Bàn giao người bệnh trước phẫu thuật',
		hlink : '#'
	}, {
		id : 'treat_qlpt',
		icon : 'dieutri',
		text : 'Tạo phiếu quản lý phôi thai',
		hlink : '#'
	}, {
		id : 'treat_ttqtcm',
		icon : 'dieutri',
		text : 'TT quy trình chuyên môn',
		hlink : '#'
	}, {
		id : 'treat_dgbd',
		icon : 'dieutri',
		text : 'Phiếu đánh giá ban đầu',
		hlink : '#'
	}, {
		id : 'treat_23',
		icon : 'dieutri',
		text : 'Tạo phiếu xét nghiệm nồng độ cồn',
		hlink : '#'
	}, {
		id : 'treat_24',
		icon : 'dieutri',
		text : 'Thông tin tử vong',
		hlink : '#'
	}, {
		id : 'treat_25',
		icon : 'dieutri',
		text : 'Chuyển đổi săn sóc tích cực',
		hlink : '#'
	}//HaNv_01072020 - L2PT-22747
	, {
		id : 'treat_26',
		icon : 'dieutri',
		text : 'Phiếu theo dõi chạy thận nhân tạo',
		hlink : '#'
	}//DoanPV_20210414 - BVTM-1012
	, {
		id : 'treat_27',
		icon : 'dieutri',
		text : 'Phiếu hướng dẫn chế độ ăn bệnh lý',
		hlink : '#'
	}//DoanPV_20210414 - BVTM-708
	, {
		id : 'treat_tpdm',
		icon : 'dieutri',
		text : 'Tạo phiếu duyệt mổ',
		hlink : '#'
	}, {
		id : 'treatdt_pkcdphcn',
		icon : 'dieutri',
		text : 'Phiếu khám và chỉ định phục hồi chức năng',
		hlink : '#'
	}, {
		id : 'treatdt_plghdcntg',
		icon : 'dieutri',
		text : 'Phiếu lượng giá hoạt động chức năng và sự tham gia',
		hlink : '#'
	}, {
		id : 'treat_28',
		icon : 'dieutri',
		text : 'Đánh giá sàng lọc dinh dưỡng',
		hlink : '#'
	}//BVTM-6402
	, {
		id : 'treat_29',
		icon : 'dieutri',
		text : 'Tạo phiếu khám gây mê hồi sức',
		hlink : '#'
	}//BVTM-6402
	, {
		id : 'treat_themphieu',
		icon : 'dieutri',
		text : 'Thêm phiếu',
		hlink : '#'
	}
	, {
		id : 'treatdt_pktm',
		icon : 'dieutri',
		text : 'Phiếu khám tiền mê',
		hlink : '#'
	}, { //L2PT-23570
		id : 'treat_bcyt',
		icon : 'dieutri',
		text : 'Tạo báo cáo y tế',
		hlink : '#'
	}
	]
}, {
	type : 'button',
	id : 'btnService',
	icon : 'dichvu',
	text : 'Dịch vụ',
	hlink : '#'
}, {
	type : 'buttongroup',
	id : 'btndrug',
	icon : 'thuoc',
	text : 'Thuốc',
	hlink : '#',
	children : [ {
		id : 'group_0',
		icon : '',
		text : 'Thuốc',
		hlink : '#',
		group : true
	}, {
		id : 'drug_khothuoc',
		icon : 'thuoc',
		text : 'Tạo phiếu thuốc từ kho',
		hlink : '#'
	}, {
		id : 'drug_tutruc',
		icon : 'thuoc',
		text : 'Tạo phiếu thuốc tủ trực',
		hlink : '#'
	}, {
		id : 'drug_2',
		icon : 'thuoc',
		text : 'Tạo phiếu trả thuốc',
		hlink : '#'
	}, {
		id : 'drug_dtnhathuoc',
		icon : 'thuoc',
		text : 'Mua thuốc nhà thuốc',
		hlink : '#'
	}, {
		id : 'drug_phieuđinhuong',
		icon : 'thuoc',
		text : 'Phiếu tư vấn dinh dưỡng (Kê)',
		hlink : '#'
	} //HaNv_220521: L2PT-3147
	, {
		id : 'group_1',
		icon : '',
		text : 'Vật tư',
		hlink : '#',
		group : true
	}, {
		id : 'drug_3',
		icon : 'thuoc',
		text : 'Tạo phiếu vật tư',
		hlink : '#'
	}, {
		id : 'drug_3_tt',
		icon : 'thuoc',
		text : 'Tạo phiếu vật tư tủ trực',
		hlink : '#'
	} //tuyennx L2PT-14596
	, {
		id : 'drug_4',
		icon : 'thuoc',
		text : 'Tạo phiếu trả vật tư',
		hlink : '#'
	}, {
		id : 'group_2',
		icon : '',
		text : 'Khác',
		hlink : '#',
		group : true
	}, {
		id : 'drug_6',
		icon : 'thuoc',
		text : 'Đơn thuốc mua ngoài',
		hlink : '#'
	}, {
		id : 'drug_8',
		icon : 'thuoc',
		text : 'Tạo phiếu hao phí',
		hlink : '#'
	}, {
		id : 'drug_hpvt',
		icon : 'thuoc',
		text : 'Tạo phiếu VT hao phí',
		hlink : '#'
	}, {
		id : 'drug_1dy',
		icon : 'thuoc',
		text : 'Tạo đơn thuốc đông y',
		hlink : '#'
	}, {
		id : 'drug_1rv',
		icon : 'thuoc',
		text : 'Tạo đơn thuốc ra viện',
		hlink : '#'
	} //tuyennx L2PT-9849
	, {
		id : 'drug_1dyrv',
		icon : 'thuoc',
		text : 'Tạo đơn thuốc đông y ra viện',
		hlink : '#'
	} //laphm L2PT-21951
	, {
		id : 'drug_vtrv',
		icon : 'thuoc',
		text : 'Tạo đơn vật tư ra viện',
		hlink : '#'
	} //tuyennx L2PT-18977
	, {
		id : 'drug_1put',
		icon : 'thuoc',
		text : 'Tạo thông tin phản ứng thuốc ADR',
		hlink : '#'
	} //tuyennx L2PT-12313
//	           ,{id:'drug_7',icon:'thuoc',text:'Đơn thuốc tủ trực',hlink:'#'}
	]
}, {
	type : 'button',
	id : 'handling_3',
	icon : 'xutri',
	text : 'Xử trí',
	hlink : '#'
}, {
	type : 'buttongroup',
	id : 'btnkh',
	icon : 'xutrikethop',
	text : 'Chuyên khoa',
	hlink : '#',
	cssClass : 'wd120',
	children : [ {
		id : 'handling_7',
		icon : 'xutri',
		text : 'Khám chuyên khoa (nội viện)',
		hlink : '#'
	}, {
		id : 'handling_8',
		icon : 'xutri',
		text : 'Kết thúc Khám chuyên khoa (nội viện)',
		hlink : '#'
	}, {
		id : 'handling_9',
		icon : 'xutri',
		text : 'Tra cứu Khám chuyên khoa (nội viện)',
		hlink : '#'
	}, {
		id : 'handling_10',
		icon : 'xutri',
		text : 'Khám chuyên khoa phòng khám',
		hlink : '#'
	}, {
		id : 'handling_11',
		icon : 'xutri',
		text : 'Lịch sử khám chuyên khoa',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnhistory',
	icon : 'lichsu',
	text : 'Lịch sử',
	hlink : '#',
	children : [ {
		id : 'history_1',
		icon : 'lichsu',
		text : 'Lịch sử bệnh án',
		hlink : '#'
	}, {
		id : 'history_2',
		icon : 'lichsu',
		text : 'Lịch sử điều trị',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnhospitalfee',
	icon : 'thutien',
	text : 'Viện phí',
	hlink : '#',
	children : [ {
		id : 'hospitalfee_1',
		icon : 'thutien',
		text : 'Thanh toán viện phí',
		hlink : '#'
	}, {
		id : 'hospitalfee_2',
		icon : 'thutien',
		text : 'Thông tin viện phí',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnfunction',
	icon : 'khac',
	text : 'Khác',
	hlink : '#',
	children : [ {
		id : 'function_5',
		icon : 'khac',
		text : 'Tai nạn thương tích',
		hlink : '#'
	}, {
		id : 'function_30',
		icon : 'khac',
		text : 'Kê tiền giường',
		hlink : '#'
	}//HaiNM BVTM-1951 Tạo menu:"Biên bản kiểm điểm tử vong".
	, {
		id : 'function_7',
		icon : 'khac',
		text : 'Ra viện, lịch hẹn',
		hlink : '#'
	}, {
		id : 'function_9',
		icon : 'khac',
		text : 'Cấp giấy chứng sinh',
		hlink : '#'
	}
	//ductx bvtm-7061
	, {
		id : 'function_96',
		icon : 'khac',
		text : 'Phiếu khám sơ sinh',
		hlink : '#'
	}, {
		id : 'function_11',
		icon : 'khac',
		text : 'Lịch sử phác đồ ĐT',
		hlink : '#'
	}, {
		id : 'function_8',
		icon : 'khac',
		text : 'Chỉ định dịch vụ cho khoa',
		hlink : '#'
	}
	//,{id:'function_10',icon:'khac',text:'Theo dõi sản phụ 6 giờ sau đẻ',hlink:'#'}
	, {
		id : 'function_12',
		icon : 'khac',
		text : 'Trả lời tư vấn dinh dưỡng',
		hlink : '#'
	}, {
		id : 'function_13',
		icon : 'khac',
		text : 'Phiếu duyệt dược CLS',
		hlink : '#'
	}, {
		id : 'function_14',
		icon : 'khac',
		text : 'Nghỉ hưởng BHXH',
		hlink : '#'
	}, {
		id : 'function_15',
		icon : 'khac',
		text : 'Biểu đồ chuyển dạ',
		hlink : '#'
	}, {
		id : 'function_17',
		icon : 'khac',
		text : 'Phiếu gây mê hồi sức',
		hlink : '#'
	}, {
		id : 'function_16',
		icon : 'khac',
		text : 'Thông tin dị ứng',
		hlink : '#'
	}, {
		id : 'function_18',
		icon : 'khac',
		text : 'Tạo phiếu khám sinh sản',
		hlink : '#'
	}, {
		id : 'function_19',
		icon : 'khac',
		text : 'Nghỉ dưỡng thai',
		hlink : '#'
	}, {
		id : 'function_20',
		icon : 'khac',
		text : 'Hành chính BN',
		hlink : '#'
	}, {
		id : 'function_21',
		icon : 'khac',
		text : 'Chuyển viện',
		hlink : '#'
	}, {
		id : 'function_22',
		icon : 'khac',
		text : 'Sửa tỷ lệ thẻ',
		hlink : '#'
	}, {
		id : 'function_23',
		icon : 'khac',
		text : 'Phiếu chỉ định xét nghiệm (ngoài viện)',
		hlink : '#'
	}, {
		id : 'function_24',
		icon : 'khac',
		text : 'Phiếu tư vấn - giải thích trong mổ',
		hlink : '#'
	}, {
		id : 'function_25',
		icon : 'khac',
		text : 'Phiếu yêu cầu sử dụng kháng sinh ưu tiên quản lý',
		hlink : '#'
	}, {
		id : 'function_26',
		icon : 'khac',
		text : 'Phiếu xác nhận xét nghiệm HIV',
		hlink : '#'
	}, {
		id : 'function_27',
		icon : 'khac',
		text : 'Nhập thông tin bệnh án',
		hlink : '#'
	}, {
		id : 'function_28',
		icon : 'khac',
		text : 'Thêm gói khám chung',
		hlink : '#'
	}, {
		id : 'function_29',
		icon : 'khac',
		text : 'Bảng kiểm tiêm chủng đối với trẻ sơ sinh',
		hlink : '#'
	}, {
		id : 'function_30',
		icon : 'khac',
		text : 'Kê tiền giường',
		hlink : '#'
	}, {
		id : 'function_31',
		icon : 'khac',
		text : 'Tạo phiếu dịch vụ khác',
		hlink : '#'
	}, { //L2PT-21249
		id : 'function_32',
		icon : 'khac',
		text : 'Thông tin tử vong',
		hlink : '#'
	}, {
		id : 'function_33',
		icon : 'khac',
		text : 'QL bệnh án',
		hlink : '#'
	}, {
		id : 'ban_QLHIV',
		icon : 'khac',
		text : 'QL bệnh nhân HIV',
		hlink : '#'
	}, {
		id : 'f_dbbenh',
		icon : 'khac',
		text : 'Diễn biến bệnh nhân',
		hlink : '#'
	} ]
}, {
	type : 'button',
	id : 'btnSKDT',
	icon : '',
	text : 'SKĐT',
	hlink : '#'
} ];
//menu cho man hinh noi tru full
var ctl_ar_noitru_full = [ {
	type : 'buttongroup',
	id : 'btnPrint',
	icon : 'print',
	text : 'In ấn',
	children : [ {
		id : 'print_1',
		icon : 'print',
		text : 'Phiếu chỉ định CLS chung',
		hlink : '#'
	}, {
		id : 'print_3',
		icon : 'print',
		text : 'Đơn thuốc',
		hlink : '#'
	}, {
		id : 'print_4',
		icon : 'print',
		text : 'Bảng kê',
		hlink : '#'
	}, {
		id : 'print_bhyt',
		icon : 'print',
		text : 'Bảng kê bhyt',
		hlink : '#'
	}, {
		id : 'print_33',
		icon : 'print',
		text : 'Bảng kê chưa thanh toán',
		hlink : '#'
	}, {
		id : 'print_34',
		icon : 'print',
		text : 'Bảng kê 3455',
		hlink : '#'
	}//START L2PT-452
	, {
		id : 'print_10',
		icon : 'print',
		text : 'Bảng kê VT hao phí',
		hlink : '#'
	}, {
		id : 'print_5',
		icon : 'print',
		text : 'Giấy ra viện',
		hlink : '#'
	}, {
		id : 'print_5_doc',
		icon : 'print',
		text : 'Giấy ra viện dọc',
		hlink : '#'
	}, {
		id : 'print_8',
		icon : 'print',
		text : 'Phiếu công khai thuốc',
		hlink : '#'
	}, {
		id : 'print_8_2',
		icon : 'print',
		text : 'Phiếu công khai thuốc mẫu 2',
		hlink : '#'
	}, {
		id : 'print_9',
		icon : 'print',
		text : 'In tờ điều trị',
		hlink : '#'
	}, {
		id : 'print_12',
		icon : 'print',
		text : 'Phiếu công khai dịch vụ',
		hlink : '#'
	},
	{
		id : 'print_ckdvtdg2',
		icon : 'print',
		text : 'Phiếu CKDV treo đầu giường',
		hlink : '#'
	}, {
		id : 'print_16',
		icon : 'print',
		text : 'Phiếu tổng hợp y lệnh',
		hlink : '#'
	}, {
		id : 'print_17',
		icon : 'print',
		text : 'In xét nghiệm chung',
		hlink : '#'
	}, {
		id : 'print_182',
		icon : 'print',
		text : 'In CĐHA chung',
		hlink : '#'
	}, {
		id : 'print_18',
		icon : 'print',
		text : 'In tách bảng kê theo khoa',
		hlink : '#'
	}, {
		id : 'print_20',
		icon : 'print',
		text : 'Phiếu đăng ký điều trị Lao',
		hlink : '#'
	}, {
		id : 'print_30',
		icon : 'print',
		text : 'Giấy chuyển viện',
		hlink : '#'
	}, {
		id : 'print_160',
		icon : 'print',
		text : 'Giấy chuyển viện .doc',
		hlink : '#'
	}, {
		id : 'print_13',
		icon : 'print',
		text : 'In phiếu khám bệnh',
		hlink : '#'
	}, {
		id : 'print_13_word',
		icon : 'print',
		text : 'In phiếu khám bệnh .doc',
		hlink : '#'
	}, {
		id : 'print_14',
		icon : 'print',
		text : 'In giấy chứng nhận nghỉ việc hưởng BHXH',
		hlink : '#'
	}, {
		id : 'print_151',
		icon : 'print',
		text : 'In tóm tắt bệnh án quân nhân',
		hlink : '#'
	}, {
		id : 'print_152',
		icon : 'print',
		text : 'In giấy cam kết truyền máu',
		hlink : '#'
	}, {
		id : 'print_153',
		icon : 'print',
		text : 'In bệnh án chi tiết',
		hlink : '#'
	}, {
		id : 'print_154',
		icon : 'print',
		text : 'Phiếu sàng lọc đánh giá dinh dưỡng',
		hlink : '#'
	}, {
		id : 'print_156',
		icon : 'print',
		text : 'Phiếu theo dõi điều trị nội trú',
		hlink : '#'
	}, {
		id : 'print_157',
		icon : 'print',
		text : 'Nghỉ hưởng BHXH',
		hlink : '#'
	}, {
		id : 'print_158',
		icon : 'print',
		text : 'Giấy hẹn khám doc',
		hlink : '#'
	}, {
		id : 'print_159',
		icon : 'print',
		text : 'Đơn thuốc không thuốc',
		hlink : '#'
	}, { //L2PT-26520
		id : 'print_hen',
		icon : 'print',
		text : 'Đơn thuốc HEN',
		hlink : '#'
	}, {
		id : 'print_copd',
		icon : 'print',
		text : 'Đơn thuốc COPD',
		hlink : '#'
	}  ]
}, {
	type : 'button',
	id : 'btnStart',
	icon : 'dsbenhnhan',
	text : 'DS BN'
}, {
	type : 'buttongroup',
	id : 'btnBan',
	icon : 'benhan',
	text : 'Bệnh án',
	children : [ {
		id : 'ban_1',
		icon : 'benhan',
		text : 'Bệnh án chung',
		hlink : '#'
	}, {
		id : 'ban_2',
		icon : 'benhan',
		text : 'Bệnh án chi tiết',
		hlink : '#'
	}, {
		id : 'ban_3',
		icon : 'benhan',
		text : 'Sơ kết điều trị',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnTreat',
	icon : 'dieutri',
	text : 'Điều trị',
	children : [ {
		id : 'treat_1',
		icon : 'dieutri',
		text : 'Tạo phiếu điều trị',
		hlink : '#'
	}, {
		id : 'treat_5',
		icon : 'dieutri',
		text : 'Tạo phiếu thử phản ứng thuốc',
		hlink : '#'
	}, {
		id : 'treat_6',
		icon : 'dieutri',
		text : 'Tạo biên bản hội chẩn',
		hlink : '#'
	}, {
		id : 'treat_9',
		icon : 'dichvu',
		text : 'Điều trị cắt cơn nghiện',
		hlink : '#'
	}, {
		id : 'treat_cc',
		icon : 'dichvu',
		text : 'Giấy xác nhận cấp cứu',
		hlink : '#'
	}
	]
}, {
	type : 'button',
	id : 'btnService',
	icon : 'dichvu',
	text : 'Dịch vụ',
	hlink : '#'
}, {
	type : 'buttongroup',
	id : 'btndrug',
	icon : 'thuoc',
	text : 'Thuốc',
	hlink : '#',
	children : [ {
		id : 'group_0',
		icon : '',
		text : 'Thuốc',
		hlink : '#',
		group : true
	}, {
		id : 'drug_khothuoc',
		icon : 'thuoc',
		text : 'Tạo phiếu thuốc từ kho',
		hlink : '#'
	}, {
		id : 'drug_tutruc',
		icon : 'thuoc',
		text : 'Tạo phiếu thuốc tủ trực',
		hlink : '#'
	}, {
		id : 'drug_2',
		icon : 'thuoc',
		text : 'Tạo phiếu trả thuốc',
		hlink : '#'
	}, {
		id : 'drug_dtnhathuoc',
		icon : 'thuoc',
		text : 'Mua thuốc nhà thuốc',
		hlink : '#'
	}, {
		id : 'group_1',
		icon : '',
		text : 'Vật tư',
		hlink : '#',
		group : true
	}, {
		id : 'drug_3',
		icon : 'thuoc',
		text : 'Tạo phiếu vật tư',
		hlink : '#'
	}, {
		id : 'drug_4',
		icon : 'thuoc',
		text : 'Tạo phiếu trả vật tư',
		hlink : '#'
	}, {
		id : 'group_2',
		icon : '',
		text : 'Khác',
		hlink : '#',
		group : true
	}, {
		id : 'drug_6',
		icon : 'thuoc',
		text : 'Đơn thuốc mua ngoài',
		hlink : '#'
	}, {
		id : 'drug_8',
		icon : 'thuoc',
		text : 'Tạo phiếu hao phí',
		hlink : '#'
	}, {
		id : 'drug_hpvt',
		icon : 'thuoc',
		text : 'Tạo phiếu VT hao phí',
		hlink : '#'
	}, {
		id : 'drug_1dy',
		icon : 'thuoc',
		text : 'Tạo đơn thuốc đông y',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnhandling',
	icon : 'xutri',
	text : 'Xử trí',
	hlink : '#',
	children : [ {
		id : 'group_0',
		icon : '',
		text : 'Xử trí',
		hlink : '#',
		group : true
	}, {
		id : 'handling_1',
		icon : 'xutri',
		text : 'Chuyển phòng (buồng),giường',
		hlink : '#'
	}, {
		id : 'handling_2',
		icon : 'xutri',
		text : 'Chuyển phòng (không chuyển giường)',
		hlink : '#'
	}, {
		id : 'handling_3',
		icon : 'xutri',
		text : 'Xử trí',
		hlink : '#'
	}, {
		id : 'group_1',
		icon : '',
		text : 'Chuyển mổ',
		hlink : '#',
		group : true
	}, {
		id : 'handling_4',
		icon : 'xutri',
		text : 'Chuyển mổ cấp cứu',
		hlink : '#'
	}, {
		id : 'handling_5',
		icon : 'xutri',
		text : 'Gửi duyệt mổ phiên',
		hlink : '#'
	}, {
		id : 'handling_0',
		icon : '',
		text : 'Khoa điều trị kết hợp',
		hlink : '#',
		group : true
	}, {
		id : 'handling_6',
		icon : 'xutri',
		text : 'Trả về khoa điều trị sau mổ',
		hlink : '#'
	}, {
		id : 'handling_7',
		icon : 'xutri',
		text : 'Chuyển khoa điều trị kết hợp',
		hlink : '#'
	}, {
		id : 'handling_8',
		icon : 'xutri',
		text : 'Kết thúc điều trị kết hợp',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnhistory',
	icon : 'lichsu',
	text : 'Lịch sử',
	hlink : '#',
	children : [ {
		id : 'history_1',
		icon : 'lichsu',
		text : 'Lịch sử bệnh án',
		hlink : '#'
	}, {
		id : 'history_2',
		icon : 'lichsu',
		text : 'Lịch sử điều trị',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnhospitalfee',
	icon : 'thutien',
	text : 'Viện phí',
	hlink : '#',
	children : [ {
		id : 'hospitalfee_1',
		icon : 'thutien',
		text : 'Thanh toán viện phí',
		hlink : '#'
	}, {
		id : 'hospitalfee_2',
		icon : 'thutien',
		text : 'Thông tin viện phí',
		hlink : '#'
	} ]
}, {
	type : 'buttongroup',
	id : 'btnfunction',
	icon : 'khac',
	text : 'Khác',
	hlink : '#',
	children : [ {
		id : 'function_5',
		icon : 'khac',
		text : 'Tai nạn thương tích',
		hlink : '#'
	}, {
		id : 'function_7',
		icon : 'khac',
		text : 'Ra viện, lịch hẹn',
		hlink : '#'
	}, {
		id : 'function_9',
		icon : 'khac',
		text : 'Cấp giấy chứng sinh',
		hlink : '#'
	}
	//ductx bvtm-7061
	, {
		id : 'function_96',
		icon : 'khac',
		text : 'Phiếu khám sơ sinh',
		hlink : '#'
	}, {
		id : 'function_11',
		icon : 'khac',
		text : 'Lịch sử phác đồ ĐT',
		hlink : '#'
	}, {
		id : 'function_8',
		icon : 'khac',
		text : 'Chỉ định dịch vụ cho khoa',
		hlink : '#'
	}, {
		id : 'function_12',
		icon : 'khac',
		text : 'Trả lời tư vấn dinh dưỡng',
		hlink : '#'
	}, {
		id : 'function_13',
		icon : 'khac',
		text : 'Phiếu duyệt dược CLS',
		hlink : '#'
	}, {
		id : 'function_14',
		icon : 'khac',
		text : 'Nghỉ hưởng BHXH',
		hlink : '#'
	}, {
		id : 'function_15',
		icon : 'khac',
		text : 'Biểu đồ chuyển dạ',
		hlink : '#'
	}, {
		id : 'function_17',
		icon : 'khac',
		text : 'Phiếu gây mê hồi sức',
		hlink : '#'
	}, {
		id : 'function_16',
		icon : 'khac',
		text : 'Thông tin dị ứng',
		hlink : '#'
	}, {
		id : 'function_18',
		icon : 'khac',
		text : 'Tạo phiếu khám sinh sản',
		hlink : '#'
	}, {
		id : 'function_23',
		icon : 'khac',
		text : 'Phiếu chỉ định xét nghiệm (ngoài viện)',
		hlink : '#'
	}, {
		id : 'function_24',
		icon : 'khac',
		text : 'Phiếu tư vấn - giải thích trong mổ',
		hlink : '#'
	}, {
		id : 'ban_QLHIV',
		icon : 'khac',
		text : 'QL bệnh nhân HIV',
		hlink : '#'
	}, {
		id : 'f_dbbenh',
		icon : 'khac',
		text : 'Diễn biến bệnh nhân',
		hlink : '#'
	} ]
}, {
	type : 'button',
	id : 'btnSKDT',
	icon : '',
	text : 'SKĐT',
	hlink : '#'
} ];
function NTU02D021_BDT(opt) {
	/*-------------------------------------------------------------------------------
	 * Khai bao danh sach bien
	 *------------------------------------------------------------------------------ */
	//danh sach benh nhan
	_gridId = "grdBenhNhan";
	$grid = $("#" + _gridId);
	var datetimeRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/]\d{4}$/;
	var uuid = opt._uuid;
	var _param = opt._param;
	//khai bao bien danh sach dieu tri
	_grdDieuTri = "grdDieuTri";
	$grdDieuTri = $("#" + _grdDieuTri);
	// mở rộng popup tạo đơn thuốc
	var _width = $(document).width() - 150;
	var _height = $(window).height() - 150;
	var LNMBP_XetNghiem = 1;
	var LNMBP_CDHA = 2;
	var LNMBP_ChuyenKhoa = 5;
	var LNMBP_DieuTri = 4;
	var LNMBP_ChamSoc = 9;
	var LNMBP_TruyenDich = 13;
	var LNMBP_TaoPhanUngThuoc = 14;
	var LNMBP_HoiChan = 15;
	var LNMBP_Phieuvattu = 8;
	var LNMBP_Phieuthuoc = 7;
	var LNMBP_Phieusuatan = 11;
	var LNMBP_Phieuphuthu = 6;
	var LNMBP_Phieuvanchuyen = 16;
	var LNMBP_PhieuSoSinh = 31;
	var paramInput;
	var type = "";
	var _flgModeView = '0';
	var _loaikedon = 0;
	var _loadDtkh = false;
	var checkCbHanThe = false;
	var songayCbHanThe = 0;
	var cbVienPhi = false;
	var isDsKhoa = false;
	var isTruyenmau = false;
	var isDtcc = false;
	var isDdcls = false;
	var is_tiensu_diungthuoc = false; //L2PT-59003
	var is_tt_tenga = false; //L2PT-59003
	var isPrintBk = false;
	var ssid = "";
	var isSelBs = false;
	var isKetThucBenhAn = false;
	var inPHieuKBChung = false;
	var dtkhLci = false;
	var _kbdtkhid = -1;
	var showDtkh = false;
	var isHideThuocDichVu = false;
	_gridSQL = "NTU02D021.EV001";
	// _gridSQL = "NTU02D021.DSBN";
	var loadToolbarCustom = true;
	var isRequiredDinhDuong = false;
	var maxNgayDt = -1; //HaNv_23082018: HISL2TK-884
	var hideMenuCkNoiVien = false; //HaNv_02102018: L2HOTRO-11019
	var showTheoDoiDieuTri = false;
	var showLichMo = true;
	var allowICU = false; //HaNv_01072020: L2PT-22747
	var cddvDLKHA = false;//HaNv_30112020
	var showSoluutru = false;
	var showBenhan = false;
	var showBenhanCT = false;
	var showBenhanALL = '';
	var showBenhanSKDT = false;
	var showDonThuoc = false;
	var isTachKhoNt = false;
	var gridType = 0;
	var showAllGrid = false;
	var nguoiNha = false;
	var timTheoDTBN = false;
	var inYLTONG = false;//L2PT-6463
	var inPhieuTheoDoi = false; // L2PT-8630
	var inPhieuKTPHCN = false; //L2PT-10835
	var hidePhieuVT = false;
	var tachtabthuoc = false;
	var searchByMaBA = false;
	var show_btn_skdt = false;//L2PT-8575
	var check_ktba_giaycs = false;//L2PT-9093
	var NGT_HIENTHIMAU_CAPCUU = '';
	var NGT_HIENTHIMAU_DATLICH = '';
	var kegiuongDs = false;
	var mauCovid = false;
	var showTTBNWidget = false;
	// L2PT-11973;L2PT-10784 duonghn start: tạo toolbar
	var form_phieudt_lv = false;//L2PT-26150
	var HIS_SEARCH_BY_TTKB;
	var duoi4h = false;
	var _showTabUx2023 = '';
	var cf = new Object();
	var _loaikedonvt = 1;//L2PT-51355
	var thongtinbn = null; //L2PT-57870
	var NTU_THUOC_RAVIEN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_THUOC_RAVIEN'); //L2PT-60299
	var isSearchEnter = false;
	var cccd = '';
	var ma_bhyt = '';

	 ctl_ar_noitru = getToolBar(ctl_ar_noitru, 'NTU_BDT_TOOLBAR_NTU');
	 ctl_ar_ngoaitru = getToolBar(ctl_ar_ngoaitru, 'NTU_BDT_TOOLBAR_NGT');

	// L2PT-11973;L2PT-10784 duonghn end
	//L2PT-31000 dongpt 20201127
	var NTU_DOITEN_KCK = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_DOITEN_KCK');
	ctl_ar_noitru[8].children[0].text = NTU_DOITEN_KCK == 1 ? 'Chuyển BN PTTT' : 'Khám chuyên khoa (nội viện)';
	ctl_ar_noitru[8].children[1].text = NTU_DOITEN_KCK == 1 ? 'Kết thúc BN PTTT' : 'Kết thúc Khám chuyên khoa (nội viện)';
	/* L2PT-10784 duonghn: code lại toolbar
	// L2PT-8408 duonghn start: menu thuốc
	var NTU_MENU_BDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH.CLOB', 'NTU_MENU_BDT');
	if (NTU_MENU_BDT && NTU_MENU_BDT != 0) {
		try {
			var chMenuBDT = FormUtil.unescape(NTU_MENU_BDT);
			var menuBDT = JSON.parse(chMenuBDT);
			for (var i = 0 ; i < ctl_ar_noitru.length; i++){
				if(ctl_ar_noitru[i].id == "btndrug"){
					ctl_ar_noitru[i].children = menuBDT;
					break;
				}
			}
		} catch (err) {
			console.log(err.message);
		}
	}
	// L2PT-8408 duonghn end
	*/
	var NTU_BOIMAU_TAMUNGAM = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_BOIMAU_TAMUNGAM'); // L2PT-9693 duonghn
	/*-------------------------------------------------------------------------------
	* Thong tin danh sach benh nhan
	*------------------------------------------------------------------------------ */
	//tao grid danh sach benh nhan L2PT-57870 L2PT-59003 L2PT-51878
	var _gridHeader = " ,ICON,20,0,ns,l,ES; ,ICON,20,0,ns,l,ES; ,ICON1,20,0,ns,l,ES; ,ICON_THUOCTRONGNGAY,20,0,ns,l,ES;THUOCTRONGNGAY,THUOCTRONGNGAY,0,0,t,l; ,PDT_CDCHAMSOC,20,0,ns,c,ES;PDT_CDCHAMSOC,PDT_CDCHAMSOC,0,0,t,l;TT_BA_CHITIET,TT_BA_CHITIET,90,0,f,c;Mã BA,MABENHAN,90,0,f,l;Mã BN,MABENHNHAN,90,0,f,l;Vào khoa,NGAYVAOKHOA,80,0,f,l;Họ tên,TENBENHNHAN,180,0,f,l;Số lưu trữ,SOLUUTRU,100,0,f,l; ,ICON2,15,0,ns,l,ES;"
			+ "Chẩn đoán,CHANDOANVAOKHOA,250,0,f,l;Mã BHYT,MA_BHYT,110,0,f,l;Mã điều trị,MAKHAMBENH,90,0,t,l;Trạng thái BN,TEN_TRANGTHAI,130,0,f,l;"
			+ "THOIGIANVAOVIEN,THOIGIANVAOVIEN,0,0,t,l;TENPHONG,TENPHONG,0,0,t,l;PHONGID,PHONGID,0,0,t,l;KHOAID,KHOAID,0,0,t,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
			+ "LOAIBENHANID,LOAIBENHANID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;NGAYSINH,NGAYSINH,0,0,t,l;DIACHI,DIACHI,0,0,t,l;"
			+ "TENNGHENGHIEP,TENNGHENGHIEP,0,0,t,l;TRANGTHAIKHAMBENH,TRANGTHAIKHAMBENH,10,0,t,l;KHOACHUYENDENID,KHOACHUYENDENID,10,0,t,l;BACSYDIEUTRIID,BACSYDIEUTRIID,10,0,t,l;CHECK_KQ_CLS,CHECK_KQ_CLS,0,0,t,l;"
			+ "NAMSINH,NAMSINH,0,0,t,l;BHYT_LOAIID,BHYT_LOAIID,0,0,t,l;GIOITINH,GIOITINH,0,0,t,l;"
			+ "HINHTHUCRAVIENID,HINHTHUCRAVIENID,0,0,t,l;SONGAYBHYT,SONGAYBHYT,0,0,t,l;"
			+ "GIUONGID,GIUONGID,0,0,t,l;NGAYTIEPNHAN,NGAYTIEPNHAN,0,0,t,l;TRANGTHAITIEPNHAN,TRANGTHAITIEPNHAN,0,0,t,l;"
			+ "SONGAYDIEUTRI,SONGAYDIEUTRI,0,0,t,l;Đối tượng,TENDOITUONGBENHNHAN,150,0,f,l;"
			+ "Tỷ lệ,TYLE_BHYT,60,0,f,l;Tuyến,TENLOAIBHYT,100,0,f,l;Chuẩn đoán ra viện,CHANDOANRAVIEN,100,0,t,l;KHAMBENHDTKHID,KHAMBENHDTKHID,80,0,t,l" + ";ISNGUOINHA,ISNGUOINHA,0,0,t,l;DUOI4H,DUOI4H,0,0,t,l" + ";KETQUADIEUTRIID,KETQUADIEUTRIID,100,0,t,l";
	//Begin_HaNv14052019: đưa mã BA, mã BN ở grid xuống dưới cùng - L2PT-4610
	var _gridHeader1 = " ,ICON,20,0,ns,l,ES; ,ICON,20,0,ns,l,ES; ,ICON1,20,0,ns,l,ES; ,ICON_THUOCTRONGNGAY,20,0,ns,l,ES;THUOCTRONGNGAY,THUOCTRONGNGAY,0,0,t,l; ,PDT_CDCHAMSOC,20,0,ns,c,ES;PDT_CDCHAMSOC,PDT_CDCHAMSOC,0,0,t,l;TT_BA_CHITIET,TT_BA_CHITIET,90,0,f,c;Vào khoa,NGAYVAOKHOA,80,0,f,l;Họ tên,TENBENHNHAN,180,0,f,l; ,ICON2,15,0,ns,l,ES;Mã BHYT,MA_BHYT,110,0,f,l;Mã điều trị,MAKHAMBENH,90,0,t,l;Trạng thái BN,TEN_TRANGTHAI,130,0,f,l;"
			+ "THOIGIANVAOVIEN,THOIGIANVAOVIEN,0,0,t,l;TENPHONG,TENPHONG,0,0,t,l;PHONGID,PHONGID,0,0,t,l;KHOAID,KHOAID,0,0,t,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
			+ "LOAIBENHANID,LOAIBENHANID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;NGAYSINH,NGAYSINH,0,0,t,l;DIACHI,DIACHI,0,0,t,l;"
			+ "TENNGHENGHIEP,TENNGHENGHIEP,0,0,t,l;TRANGTHAIKHAMBENH,TRANGTHAIKHAMBENH,10,0,t,l;KHOACHUYENDENID,KHOACHUYENDENID,10,0,t,l;BACSYDIEUTRIID,BACSYDIEUTRIID,10,0,t,l;CHECK_KQ_CLS,CHECK_KQ_CLS,0,0,t,l;"
			+ "NAMSINH,NAMSINH,0,0,t,l;BHYT_LOAIID,BHYT_LOAIID,0,0,t,l;GIOITINH,GIOITINH,0,0,t,l;"
			+ "HINHTHUCRAVIENID,HINHTHUCRAVIENID,0,0,t,l;SONGAYBHYT,SONGAYBHYT,0,0,t,l;"
			+ "GIUONGID,GIUONGID,0,0,t,l;NGAYTIEPNHAN,NGAYTIEPNHAN,0,0,t,l;TRANGTHAITIEPNHAN,TRANGTHAITIEPNHAN,0,0,t,l;"
			+ "SONGAYDIEUTRI,SONGAYDIEUTRI,0,0,t,l;Đối tượng,TENDOITUONGBENHNHAN,150,0,f,l;"
			+ "Tỷ lệ,TYLE_BHYT,60,0,f,l;Tuyến,TENLOAIBHYT,100,0,f,l;Chuẩn đoán ra viện,CHANDOANRAVIEN,100,0,t,l;"
			+ "Mã BA,MABENHAN,90,0,f,l;Mã BN,MABENHNHAN,90,0,f,l"+ ";KETQUADIEUTRIID,KETQUADIEUTRIID,100,0,t,l";
	//End_HaNv_14052019
	// Begin_laphm_05062019: sửa 1 số giao diện - L2PT-5597
	var _gridHeader2 = " ,ICON,20,0,ns,l; ,ICON,20,0,ns,l; ,ICON1,20,0,ns,l; ,ICON_THUOCTRONGNGAY,20,0,ns,l,ES;THUOCTRONGNGAY,THUOCTRONGNGAY,0,0,t,l; ,PDT_CDCHAMSOC,20,0,ns,c,ES;PDT_CDCHAMSOC,PDT_CDCHAMSOC,0,0,t,l;TT_BA_CHITIET,TT_BA_CHITIET,90,0,f,c;Mã BA,MABENHAN,90,0,f,l;"
			+ "Mã BN,MABENHNHAN,90,0,f,l;Vào khoa,NGAYVAOKHOA,80,0,f,l;Họ tên,TENBENHNHAN,180,0,f,l; ,ICON2,15,0,ns,l;"
			+ "Mã BHYT,MA_BHYT,110,0,f,l;Mã ICD,MABENHCHINH,50,0,f,l;Mã điều trị,MAKHAMBENH,90,0,t,l;Trạng thái BN,TEN_TRANGTHAI,130,0,f,l;"
			+ "THOIGIANVAOVIEN,THOIGIANVAOVIEN,0,0,t,l;TENPHONG,TENPHONG,0,0,t,l;PHONGID,PHONGID,0,0,t,l;"
			+ "KHOAID,KHOAID,0,0,t,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
			+ "LOAIBENHANID,LOAIBENHANID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;"
			+ "TIEPNHANID,TIEPNHANID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;NGAYSINH,NGAYSINH,0,0,t,l;DIACHI,DIACHI,0,0,t,l;"
			+ "TENNGHENGHIEP,TENNGHENGHIEP,0,0,t,l;TRANGTHAIKHAMBENH,TRANGTHAIKHAMBENH,10,0,t,l;" + "KHOACHUYENDENID,KHOACHUYENDENID,10,0,t,l;BACSYDIEUTRIID,BACSYDIEUTRIID,10,0,t,l;"
			+ "CHECK_KQ_CLS,CHECK_KQ_CLS,0,0,t,l;NAMSINH,NAMSINH,0,0,t,l;BHYT_LOAIID,BHYT_LOAIID,0,0,t,l;GIOITINH,GIOITINH,0,0,t,l;" + "HINHTHUCRAVIENID,HINHTHUCRAVIENID,0,0,t,l;SONGAYBHYT,SONGAYBHYT,0,0,t,l;"
			+ "GIUONGID,GIUONGID,0,0,t,l;NGAYTIEPNHAN,NGAYTIEPNHAN,0,0,t,l;TRANGTHAITIEPNHAN,TRANGTHAITIEPNHAN,0,0,t,l;"
			+ "SONGAYDIEUTRI,SONGAYDIEUTRI,0,0,t,l;Đối tượng,TENDOITUONGBENHNHAN,150,0,f,l;" + "Tỷ lệ,TYLE_BHYT,60,0,f,l;Tuyến,TENLOAIBHYT,100,0,f,l;Chuẩn đoán ra viện,CHANDOANRAVIEN,100,0,t,l"+ ";KETQUADIEUTRIID,KETQUADIEUTRIID,100,0,t,l";
	// End_laphm_10062019
	var _gridHeader_BDHN = " ,ICON,20,0,ns,l,ES; ,ICON,20,0,ns,l,ES; ,ICON1,20,0,ns,l,ES; ,ICON_THUOC,20,0,ns,l,ES; ,PDT_CDCHAMSOC,20,0,ns,c,ES; ,ICON_THUOCTRONGNGAY,20,0,ns,l,ES;THUOCTRONGNGAY,THUOCTRONGNGAY,0,0,t,l;PDT_CDCHAMSOC,PDT_CDCHAMSOC,0,0,t,l;TT_BA_CHITIET,TT_BA_CHITIET,90,0,f,c;"
			+ "Mã Phòng/giường,PHONG_GIUONG,110,0,f,l;"
			+ "Mã BA,MABENHAN,90,0,f,l;Mã BN,MABENHNHAN,90,0,f,l;Vào khoa,NGAYVAOKHOA,80,0,f,l;Họ tên,TENBENHNHAN,180,0,f,l;Số lưu trữ,SOLUUTRU,100,0,f,l; ,ICON2,15,0,ns,l,ES;"
			+ "Ngày sinh,NGAYSINH,80,0,t,l;Chẩn đoán,CHANDOANVAOKHOA,250,0,f,l;Mã BHYT,MA_BHYT,110,0,f,l;Mã điều trị,MAKHAMBENH,90,0,t,l;Trạng thái BN,TEN_TRANGTHAI,130,0,f,l;"
			+ "THOIGIANVAOVIEN,THOIGIANVAOVIEN,0,0,t,l;TENPHONG,TENPHONG,0,0,t,l;PHONGID,PHONGID,0,0,t,l;KHOAID,KHOAID,0,0,t,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
			+ "LOAIBENHANID,LOAIBENHANID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;DIACHI,DIACHI,0,0,t,l;"
			+ "TENNGHENGHIEP,TENNGHENGHIEP,0,0,t,l;TRANGTHAIKHAMBENH,TRANGTHAIKHAMBENH,10,0,t,l;KHOACHUYENDENID,KHOACHUYENDENID,10,0,t,l;BACSYDIEUTRIID,BACSYDIEUTRIID,10,0,t,l;CHECK_KQ_CLS,CHECK_KQ_CLS,0,0,t,l;"
			+ "NAMSINH,NAMSINH,0,0,t,l;BHYT_LOAIID,BHYT_LOAIID,0,0,t,l;GIOITINH,GIOITINH,0,0,t,l;" + "HINHTHUCRAVIENID,HINHTHUCRAVIENID,0,0,t,l;SONGAYBHYT,SONGAYBHYT,0,0,t,l;"
			+ "GIUONGID,GIUONGID,0,0,t,l;NGAYTIEPNHAN,NGAYTIEPNHAN,0,0,t,l;TRANGTHAITIEPNHAN,TRANGTHAITIEPNHAN,0,0,t,l;" + "SONGAYDIEUTRI,SONGAYDIEUTRI,0,0,t,l;"
			+ "Tỷ lệ,TYLE_BHYT,60,0,f,l;Tuyến,TENLOAIBHYT,100,0,f,l;Chuẩn đoán ra viện,CHANDOANRAVIEN,100,0,t,l;KHAMBENHDTKHID,KHAMBENHDTKHID,80,0,t,l;" + "Đối tượng,TENDOITUONGBENHNHAN_BD,90,0,f,l;"
			+ "Đơn vị công tác,TENDONVI,170,0,f,l;" + "Loại khám,LOAIKHAM,100,0,f,l;" + "TONGTIENDICHVUCORE,TONGTIENDICHVUCORE,80,0,t,l;" + "Tổng chi phí,TONGCHIPHI,80,0,f,r;"
			+ "Tạm ứng,TAMUNG,80,0,f,r;" + "Đã nộp,DANOP,80,0,f,r;" + "Còn nợ,CONNO,80,0,f,r;" + "TT Thanh toán,TRANGTHAI_THANHTOAN,100,0,f,l;" + "Xử trí,XUTRI,100,0,f,l;"
			+ "Kết quả điều trị,KETQUADIEUTRI,100,0,f,l;" + "THANNHAN,THANNHAN,100,0,t,l;" + "Trạng thái PTTT,TRANGTHAIPTTT,100,0,f,l" + ";ISCHUYENDTKH,ISCHUYENDTKH,0,0,t,l"+ ";THUCHIENTHUOC,THUCHIENTHUOC,0,0,t,l"
			+ ";KETQUADIEUTRIID,KETQUADIEUTRIID,100,0,t,l"; //L2PT-27125
	// jira L2PT-11949
	var _gridHeaderYHCTLS = " ,ICON,20,0,ns,l; ,ICON,20,0,ns,l; ,ICON1,20,0,ns,l; ,ICON_THUOCTRONGNGAY,20,0,ns,l,ES;THUOCTRONGNGAY,THUOCTRONGNGAY,0,0,t,l; ,PDT_CDCHAMSOC,20,0,ns,c,ES;PDT_CDCHAMSOC,PDT_CDCHAMSOC,0,0,t,l;TT_BA_CHITIET,TT_BA_CHITIET,90,0,f,c;Mã BA,MABENHAN,90,0,f,l;Mã BN,MABENHNHAN,90,0,f,l;Vào khoa,NGAYVAOKHOA,80,0,f,l;Họ tên,TENBENHNHAN,180,0,f,l;Số lưu trữ,SOLUUTRU,100,0,f,l; ,ICON2,15,0,ns,l;"
			+ "Chẩn đoán,CHANDOANVAOKHOA,250,0,f,l;Mã BHYT,MA_BHYT,110,0,f,l;Mã điều trị,MAKHAMBENH,90,0,t,l;Trạng thái BN,TEN_TRANGTHAI,130,0,f,l;"
			+ "THOIGIANVAOVIEN,THOIGIANVAOVIEN,0,0,t,l;TENPHONG,TENPHONG,0,0,t,l;PHONGID,PHONGID,0,0,t,l;KHOAID,KHOAID,0,0,t,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
			+ "LOAIBENHANID,LOAIBENHANID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;NGAYSINH,NGAYSINH,0,0,t,l;DIACHI,DIACHI,0,0,t,l;"
			+ "TENNGHENGHIEP,TENNGHENGHIEP,0,0,t,l;TRANGTHAIKHAMBENH,TRANGTHAIKHAMBENH,10,0,t,l;KHOACHUYENDENID,KHOACHUYENDENID,10,0,t,l;BACSYDIEUTRIID,BACSYDIEUTRIID,10,0,t,l;CHECK_KQ_CLS,CHECK_KQ_CLS,0,0,t,l;"
			+ "NAMSINH,NAMSINH,0,0,t,l;BHYT_LOAIID,BHYT_LOAIID,0,0,t,l;GIOITINH,GIOITINH,0,0,t,l;"
			+ "HINHTHUCRAVIENID,HINHTHUCRAVIENID,0,0,t,l;SONGAYBHYT,SONGAYBHYT,0,0,t,l;"
			+ "GIUONGID,GIUONGID,0,0,t,l;NGAYTIEPNHAN,NGAYTIEPNHAN,0,0,t,l;TRANGTHAITIEPNHAN,TRANGTHAITIEPNHAN,0,0,t,l;"
			+ "SONGAYDIEUTRI,SONGAYDIEUTRI,0,0,t,l;Đối tượng,TENDOITUONGBENHNHAN,150,0,f,l;"
			+ "Tỷ lệ,TYLE_BHYT,60,0,f,l;Tuyến,TENLOAIBHYT,100,0,f,l;Xử trí,XUTRI,100,0,f,l;Chuẩn đoán ra viện,CHANDOANRAVIEN,100,0,t,l;KHAMBENHDTKHID,KHAMBENHDTKHID,80,0,t,l"
			+ ";ISNGUOINHA,ISNGUOINHA,0,0,t,l"+ ";KETQUADIEUTRIID,KETQUADIEUTRIID,100,0,t,l";
	//L2PT-5548 start
	// L2PT-10784: thêm cột MABENHCHINH
	var NTU_DSBN_LOAD_CHIPHI = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_DSBN_LOAD_CHIPHI'); // L2PT-10784
	var _gridHeader_DKQTI = " ,ICON,20,0,ns,l,ES; ,ICON,20,0,ns,l,ES; ,ICON1,20,0,ns,l,ES; ,ICON_THUOCTRONGNGAY,20,0,ns,l,ES;THUOCTRONGNGAY,THUOCTRONGNGAY,0,0,t,l; ,PDT_CDCHAMSOC,20,0,ns,c,ES; ,TT_BENHAN,20,0,ns,c,ES;PDT_CDCHAMSOC,PDT_CDCHAMSOC,0,0,t,l;TT_BA_CHITIET,TT_BA_CHITIET,90,0,f,c;" +
			"Mã Phòng/giường,PHONG_GIUONG,110,0,f,l;" +
			"Mã BA,MABENHAN,90,0,f,l;Mã BN,MABENHNHAN,90,0,f,l;Vào khoa,NGAYVAOKHOA,80,0,f,l;Họ tên,TENBENHNHAN,180,0,f,l;Số lưu trữ,SOLUUTRU,100,0,f,l; ,ICON2,15,0,ns,l,ES;" +
			"Chẩn đoán,CHANDOANVAOKHOA,250,0,f,l;Mã BHYT,MA_BHYT,110,0,f,l;Mã ICD,MABENHCHINH,50,0,f,l;Mã điều trị,MAKHAMBENH,90,0,t,l;Trạng thái BN,TEN_TRANGTHAI,130,0,f,l;" +
			"THOIGIANVAOVIEN,THOIGIANVAOVIEN,0,0,t,l;TENPHONG,TENPHONG,0,0,t,l;PHONGID,PHONGID,0,0,t,l;KHOAID,KHOAID,0,0,t,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;" +
			"LOAIBENHANID,LOAIBENHANID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;NGAYSINH,NGAYSINH,0,0,t,l;DIACHI,DIACHI,0,0,t,l;" +
			"TENNGHENGHIEP,TENNGHENGHIEP,0,0,t,l;TRANGTHAIKHAMBENH,TRANGTHAIKHAMBENH,10,0,t,l;KHOACHUYENDENID,KHOACHUYENDENID,10,0,t,l;BACSYDIEUTRIID,BACSYDIEUTRIID,10,0,t,l;CHECK_KQ_CLS,CHECK_KQ_CLS,0,0,t,l;" +
			"NAMSINH,NAMSINH,0,0,t,l;BHYT_LOAIID,BHYT_LOAIID,0,0,t,l;GIOITINH,GIOITINH,0,0,t,l;" + "HINHTHUCRAVIENID,HINHTHUCRAVIENID,0,0,t,l;SONGAYBHYT,SONGAYBHYT,0,0,t,l;" +
			"GIUONGID,GIUONGID,0,0,t,l;NGAYTIEPNHAN,NGAYTIEPNHAN,0,0,t,l;TRANGTHAITIEPNHAN,TRANGTHAITIEPNHAN,0,0,t,l;" + "SONGAYDIEUTRI,SONGAYDIEUTRI,0,0,t,l;" +
			"Tỷ lệ,TYLE_BHYT,60,0,f,l;Tuyến,TENLOAIBHYT,100,0,f,l;Chuẩn đoán ra viện,CHANDOANRAVIEN,100,0,t,l;KHAMBENHDTKHID,KHAMBENHDTKHID,80,0,t,l;" + "Đối tượng,TENDOITUONGBENHNHAN_BD,90,0,f,l;" +
			"Loại khám,LOAIKHAM,100,0,f,l;" + "TONGTIENDICHVUCORE,TONGTIENDICHVUCORE,80,0,t,l;" +
			//HungND - L2PT-67343
			"SERI,SERI,0,0,t,l;" + "TONGTIENDICHVUCORE,TONGTIENDICHVUCORE,80,0,t,l;" +
			//End HungND - L2PT-67343
			// L2PT-10784 start: cấu hình load chi phí trên danh sách bệnh nhân
			(NTU_DSBN_LOAD_CHIPHI == '0' ? "Tổng chi phí,TONGCHIPHI,80,0,f,r;" + "BN trả,BNTRA,80,0,f,r;" + // L2PT-5548
			"Tạm ứng,TAMUNG,80,0,f,r;" + "Đã nộp,DANOP,80,0,f,r;" + "Tạm ứng CL,CONNO,80,0,f,r;" : "") + // L2PT-5548
			// L2PT-10784 end
			"TT Thanh toán,TRANGTHAI_THANHTOAN,100,0,f,l;" + "Xử trí,XUTRI,100,0,f,l;" + "Kết quả điều trị,KETQUADIEUTRI,100,0,f,l;" + "THANNHAN,THANNHAN,100,0,t,l;" +
			"Trạng thái PTTT,TRANGTHAIPTTT,100,0,f,l" + ";DADUYETMO,DADUYETMO,0,0,t,l" + ";ISCHUYENDTKH,ISCHUYENDTKH,0,0,t,l"+ ";KETQUADIEUTRIID,KETQUADIEUTRIID,100,0,t,l";
	//L2PT-5548 end
	var _gridHeader_DKHTH = " ,ICON,20,0,ns,l,ES; ,ICON,20,0,ns,l,ES; ,ICON1,20,0,ns,l,ES; ,ICON_THUOCTRONGNGAY,20,0,ns,l,ES;THUOCTRONGNGAY,THUOCTRONGNGAY,0,0,t,l; ,PDT_CDCHAMSOC,20,0,ns,c,ES;PDT_CDCHAMSOC,PDT_CDCHAMSOC,0,0,t,l;TT_BA_CHITIET,TT_BA_CHITIET,90,0,f,c;Mã BA,MABENHAN,90,0,f,l;Mã BN,MABENHNHAN,90,0,f,l;Vào khoa,NGAYVAOKHOA,80,0,f,l;Họ tên,TENBENHNHAN,180,0,f,l;"
			+ "Năm sinh,NAMSINH,50,0,f,l;Số lưu trữ,SOLUUTRU,100,0,f,l; ,ICON2,15,0,ns,l,ES;"
			+ "Chẩn đoán,CHANDOANVAOKHOA,250,0,f,l;Mã BHYT,MA_BHYT,110,0,f,l;Mã điều trị,MAKHAMBENH,90,0,t,l;Trạng thái BN,TEN_TRANGTHAI,130,0,f,l;"
			+ "THOIGIANVAOVIEN,THOIGIANVAOVIEN,0,0,t,l;TENPHONG,TENPHONG,0,0,t,l;PHONGID,PHONGID,0,0,t,l;KHOAID,KHOAID,0,0,t,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
			+ "LOAIBENHANID,LOAIBENHANID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;NGAYSINH,NGAYSINH,0,0,t,l;DIACHI,DIACHI,0,0,t,l;"
			+ "TENNGHENGHIEP,TENNGHENGHIEP,0,0,t,l;TRANGTHAIKHAMBENH,TRANGTHAIKHAMBENH,10,0,t,l;KHOACHUYENDENID,KHOACHUYENDENID,10,0,t,l;BACSYDIEUTRIID,BACSYDIEUTRIID,10,0,t,l;CHECK_KQ_CLS,CHECK_KQ_CLS,0,0,t,l;"
			+ "BHYT_LOAIID,BHYT_LOAIID,0,0,t,l;GIOITINH,GIOITINH,0,0,t,l;"
			+ "HINHTHUCRAVIENID,HINHTHUCRAVIENID,0,0,t,l;SONGAYBHYT,SONGAYBHYT,0,0,t,l;"
			+ "GIUONGID,GIUONGID,0,0,t,l;NGAYTIEPNHAN,NGAYTIEPNHAN,0,0,t,l;TRANGTHAITIEPNHAN,TRANGTHAITIEPNHAN,0,0,t,l;"
			+ "SONGAYDIEUTRI,SONGAYDIEUTRI,0,0,t,l;Đối tượng,TENDOITUONGBENHNHAN,150,0,f,l;"
			+ "Tỷ lệ,TYLE_BHYT,60,0,f,l;Tuyến,TENLOAIBHYT,100,0,f,l;Chuẩn đoán ra viện,CHANDOANRAVIEN,100,0,t,l;KHAMBENHDTKHID,KHAMBENHDTKHID,80,0,t,l;"
			+ "Xử trí,XUTRI,100,0,f,l"+ ";KETQUADIEUTRIID,KETQUADIEUTRIID,100,0,t,l";
	this.load = doLoad;
	//load khoi tao man hinh buong dieu tri
	function doLoad() {
		//load ngon ngu
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		_showTabUx2023 = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "HIS_SHOW_TAB_UX2023");
		if (_showTabUx2023 == "1"){
			$('#idTabCDHA').hide();
			$('#idTabChuyenKhoa').hide();
			$('#idTabXetNghiem').hide();
			$('#idTabPhieuTruyenMau').hide();
			$('#idTabThuoc').hide();
			$('#idTabMau').hide();
			$('#idTabDichVuKhac').hide();
			$('#idTabVatTu').hide();
			$('#idTabVC').hide();
			$('#idTabChamSoc').hide();
			$('#idTabSuatAn').hide();
			$('#idTabTruyenDich').hide();
			$('#idTabPhanUngThuoc').hide();
			$('#idTabHoiChan').hide();
			$('#idTabDieuTri').hide();
			$('#idPhieuSoSinh').hide();
			$('#idTabNgayGiuong').hide();
		}

		//HungND - L2PT-63849
		_showTabKham = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "NTU_BDT_SHOW_TABKHAM");
		if(_showTabKham == '0' || !_showTabKham) {
			$('#idTabCk').hide();
		}
		//HungND - L2PT-63849 End


		initControl();
		bindEvent();
		$('#hidPHONGDNID').val(opt._subdeptId);
		
		//L2PT-36760
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "CAUHINH_MENU_MAUPHIEU") == "1"){
			$('<li data-external="{}" id="toolbarId_DanhSachPhieu"><a href="#"><i class="glyphicon glyphicon-dieutri"></i>&nbsp;&nbsp;Danh sách phiếu</a></li>').insertBefore(
				$('#toolbarIdbtnTreat').next('.dropdown-menu').children().first()
			); 
			$("#toolbarId_DanhSachPhieu").click(function() { 
					myVar = {
						KHAMBENHID : $("#hidKHAMBENHID").val(),
						BENHNHANID : $("#hidBENHNHANID").val(),
						TIEPNHANID : $("#hidTIEPNHANID").val(),
						MABENHNHAN : $("#hidMABENHNHAN").val(),
						HOSOBENHANID : $("#hidHOSOBENHANID").val() 
					}
					dlgPopup = DlgUtil.buildPopupUrl("divDlgThemPhieu", "divDlg", "manager.jsp?func=../danhmuc/MAUPHIEU_DS", myVar, "Danh sách phiếu " + (thongtinbn==null?"":"(" + thongtinbn + ")")
						, window.innerWidth * 0.95, window.innerHeight * 0.95);
					DlgUtil.open("divDlgThemPhieu");
				});
		}
		
		//L2PT-39886
		CH_MENU_KHAC();
		//L2PT-39399
		MENU_INAN_ADD();
	}
	//L2PT-39399
	function MENU_INAN_ADD(){ 
		try{
			var str_menu = jsonrpc.AjaxJson.ajaxCALL_SP_X('COM.CAUHINH.CLOB', 'NTU02D021_BUONGDIEUTRI_MENU_INAN_ADD');  
			if (str_menu && str_menu.length >10){
				var _list = JSON.parse(str_menu); 
				for (var i=0; i<_list.length; i++){
					var _itemMenu = _list[i]; 
					var _id = 'Id_InAn_CreateAuto' + i; 
					var _name = _itemMenu.NAME;  
					var _type = _itemMenu.LOAITIEPNHAN ? _itemMenu.LOAITIEPNHAN : '';
					if (_type == '' || _type == type){
						var _strMenu = 
							'<input type="hidden" id="Data_' + _id + '" value="" />' 
							+ '<li data-external="{}" id="' + _id + '"><a href="#"><i class="glyphicon glyphicon-print"></i>&nbsp;&nbsp;'+_name+'</a></li>';
						
						$(_strMenu).insertAfter( $('#toolbarIdbtnPrint').next('.dropdown-menu').children().last() ); 
						
						$('#Data_' + _id).val( JSON.stringify(_itemMenu) );
						
						$("#"+_id).click(function() {						
							if (!checkBeforeClickOnMenu()) return;
							
							var _itemStr = $('#Data_' + $(this).attr('id')).val();
							var _item = JSON.parse(_itemStr);
							 
							var par = [ 
								{ name : 'hosobenhanid', type : 'String', value : $("#hidHOSOBENHANID").val() }
								, { name : 'khambenhid', type : 'String', value : $("#hidKHAMBENHID").val() } 
								, { name : 'mabenhnhan', type : 'String', value : $("#hidMABENHNHAN").val() }
								, { name : 'loaibenhanid', type : 'String', value : $("#hidLOAIBENHANID").val() }
								, { name : 'benhnhanid', type : 'String', value : $("#hidBENHNHANID").val() }
								, { name : 'tiepnhanid', type : 'String', value : $("#hidTIEPNHANID").val() }
								, { name : 'phongid', type : 'String', value : $("#hidPHONGID").val() }
								, { name : 'khoaid', type : 'String', value : opt._deptId }
								, { name : 'i_hosobenhanid', type : 'String', value : $("#hidHOSOBENHANID").val() }
								, { name : 'i_khambenhid', type : 'String', value : $("#hidKHAMBENHID").val() } 
								, { name : 'i_mabenhnhan', type : 'String', value : $("#hidMABENHNHAN").val() }
								, { name : 'i_loaibenhanid', type : 'String', value : $("#hidLOAIBENHANID").val() }
								, { name : 'i_benhnhanid', type : 'String', value : $("#hidBENHNHANID").val() }
								, { name : 'i_tiepnhanid', type : 'String', value : $("#hidTIEPNHANID").val() }
								, { name : 'i_phongid', type : 'String', value : $("#hidPHONGID").val() }
								, { name : 'i_khoaid', type : 'String', value : opt._deptId }
							] 
							if (_item.TYPE == 'DOC'){
								var rpName = _item.RPT_CODE + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
								CommonUtil.inPhieu('window', _item.RPT_CODE, 'docx', par, rpName);
							}
							else if (_item.TYPE == 'PDF'){
								openReport('window', _item.RPT_CODE, "pdf", par);
							} 
						}); 
					}
				} 
			}
		}catch(ex){} 
	}
	//L2PT-39886
	function CH_MENU_KHAC(){  
		try{
			var str_menu = jsonrpc.AjaxJson.ajaxCALL_SP_X('COM.CAUHINH.CLOB', 'NTU02D021_BUONGDIEUTRI_MENU_KHAC');  
			if (str_menu != '0'){
				var _list = JSON.parse(str_menu); 
				for (var i=0; i<_list.length; i++){
					var _itemMenu = _list[i];
					var _id = 'toolbarId_createAuto' + i; 
					var _name = _itemMenu.NAME;  
					var _strMenu = 
						'<input type="hidden" id="Data_' + _id + '" value="" />'
						+ '<li data-external="{}" id="'+_id+'"><a href="#"><i class="glyphicon glyphicon-khac"></i>&nbsp;&nbsp;'+_name+'</a></li>';
					
					$(_strMenu).insertAfter(
						$('#toolbarIdbtnfunction').next('.dropdown-menu').children().last()
					); 
					
					$('#Data_' + _id).val( JSON.stringify(_itemMenu) );
					
					$("#"+_id).click(function() {						
						if (!checkBeforeClickOnMenu()) return;
						
						var _itemStr = $('#Data_' + $(this).attr('id')).val();
						var _item = JSON.parse(_itemStr);
						var name;
						if (_item.URL.indexOf('NTU02D170_FORMPHIEU') >= 0){ 
							name = _item.CHUCNANG + '@' +  $("#hidKHAMBENHID").val() + '@' + $("#hidTRANGTHAIKHAMBENH").val() ;
						}
						else{ 
							name = {
								KHAMBENHID : $("#hidKHAMBENHID").val(),
								BENHNHANID : $("#hidBENHNHANID").val(),
								TIEPNHANID : $("#hidTIEPNHANID").val(),
								MABENHNHAN : $("#hidMABENHNHAN").val(),
								HOSOBENHANID : $("#hidHOSOBENHANID").val() 
							};
						}
						
						if (_item.TYPE == 'POPUP'){
							dlgPopup = DlgUtil.buildPopupUrl("divDlgPopup", "divDlg", "manager.jsp?func=../" + _item.URL, name, _item.NAME + " " +  thongtinbn
								, window.innerWidth * 0.95, window.innerHeight * 0.95);
							DlgUtil.open("divDlgPopup"); 
						}
						else{  //    noitru/NTU02D170_FORMPHIEU&showMode=dlg
							var dlgWindow = window.open('manager.jsp?func=../' + _item.URL , name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
							screen.height + ',width=' + screen.width);
							dlgWindow.moveTo(0, 0); 
						}
					});
				} 
			}
		}catch(ex){} 
	}
	
	function onChangeTimTheongay() {
		$('#chkTIMTHEONGAY').click(function() {
			$("#chkTIMTHEONGAYRA").prop("checked", false);
			if ($("#chkTIMTHEONGAY").is(':checked')) {
				$("#txtTG_NHAPVIEN_TU").val(null);
				$("#txtTG_NHAPVIEN_TU").attr("disabled", false);
				$("#txtTG_NHAPVIEN_DEN").val(null);
				$("#txtTG_NHAPVIEN_DEN").attr("disabled", false);
				$("#btnTG_NHAPVIEN_TU").attr("disabled", false);
				$("#btnTG_NHAPVIEN_DEN").attr("disabled", false);
			} else {
				$("#txtTG_NHAPVIEN_TU").val(null);
				$("#txtTG_NHAPVIEN_TU").attr("disabled", true);
				$("#txtTG_NHAPVIEN_DEN").val(null);
				$("#txtTG_NHAPVIEN_DEN").attr("disabled", true);
				$("#btnTG_NHAPVIEN_TU").attr("disabled", true);
				$("#btnTG_NHAPVIEN_DEN").attr("disabled", true);
			}
		});
		$('#chkTIMTHEONGAYRA').click(function() {
			$("#chkTIMTHEONGAY").prop("checked", false);
			if ($("#chkTIMTHEONGAYRA").is(':checked')) {
				$("#txtTG_NHAPVIEN_TU").val(null);
				$("#txtTG_NHAPVIEN_TU").attr("disabled", false);
				$("#txtTG_NHAPVIEN_DEN").val(null);
				$("#txtTG_NHAPVIEN_DEN").attr("disabled", false);
				$("#btnTG_NHAPVIEN_TU").attr("disabled", false);
				$("#btnTG_NHAPVIEN_DEN").attr("disabled", false);
			} else {
				$("#txtTG_NHAPVIEN_TU").val(null);
				$("#txtTG_NHAPVIEN_TU").attr("disabled", true);
				$("#txtTG_NHAPVIEN_DEN").val(null);
				$("#txtTG_NHAPVIEN_DEN").attr("disabled", true);
				$("#btnTG_NHAPVIEN_TU").attr("disabled", true);
				$("#btnTG_NHAPVIEN_DEN").attr("disabled", true);
			}
		});
	}
	//khoi tao control
	function initControl() {
		$("#cboTRANGTHAIKHAMBENH").focus();
		onChangeTimTheongay();
		type = getParameterByName('loaitiepnhan', window.location.search.substring(1));
		ssid = getParameterByName('ssid', window.location.search.substring(1));
		var sql_par = [];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("LAY.CAUHINH", sql_par.join('$'));
		if (data_ar != null && data_ar.length > 0) {
			if (data_ar[0].CONFIGBACSI != '1') {
				$("#chkBACSYDIEUTRI").prop("checked", false);
			} else {
				$("#chkBACSYDIEUTRI").prop("checked", true);
				$('#divCboBacSi').show();
			}
			//tuyennx_edit_start_20190319 L2PT-2841
			if (type == '0')
				_loaikedon = data_ar[0].KEDONTHUOC_CHITIET_NTU;
			else{//L2PT-51355
				_loaikedon = data_ar[0].KEDONTHUOC_CHITIET_DTNGT;
				_loaikedonvt = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'KEDONVT_CHITIET_DTNGT');
			}
				
			//tuyennx_edit_end_20190319
		}
		//GridUtil.addExcelButton(_gridId,"Export all",false);
		//khoi tao du lieu danh sach phong trong khoa
		var _sql_par_phong = [];
		_sql_par_phong.push({
			"name" : "[0]",
			"value" : opt._deptId
		});
		if (getCauHinhLayTatCaPhong() == 1) {
			// lay tat ca cac phong
			ComboUtil.getComboTag("cboPHONGID", "NT.021.DSPHONG.ALL", _sql_par_phong, "", {
				value : '-1',
				text : '--Tất cả--'
			}, "sql");
		} else {
			// lay cac phong la noi tru
			if (type == 3) {
				ComboUtil.getComboTag("cboPHONGID", "NT.DSPHONG.NT", [ {
					"name" : "[0]",
					"value" : 5
				}, {
					"name" : "[1]",
					"value" : opt._deptId
				}, {
					"name" : "[2]",
					"value" : "ORG_ID"
				}, {
					"name" : "[3]",
					"value" : "PARENT_ID"
				} ], "", {
					value : '-1',
					text : 'Chọn buồng,phòng'
				}, "sql");
			} else {
				ComboUtil.getComboTag("cboPHONGID", "NT.021.DSPHONG", _sql_par_phong, "", {
					value : '-1',
					text : 'Chọn buồng,phòng'
				}, "sql");
			}
		}
		// L2PT-66453 duonghn: them NTU_TIMKIEM_LOAIDTRINTU
		// L2PT-85558 duonghn: them NTU_LOAI_GIAYRAVIEN_DOC
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'HIS_FULL_MENU_HC' + ";" + "HIS_DIEUTRI_KETHOP" + ";" + "HIS_CANHBAO_HANTHE" + ";" + "HIS_CANHBAO_VIENPHI" + ";"
				+ "HIS_DANHSACH_KHOA" + ";" + "HIS_DANHSACH_KHOA_DTNT" + ";" + "HIS_AN_PHIEUMAU" + ";" + "HIS_DT_CAT_CON" + ";" + "HIS_NOTIFICE_DUYET_DUOC_CLS;HIS_REQUIRED_SANGLOC_DINHDUONG;"
				+ "VP_IN_PHOI_KHI_DONG_BENHAN;HIS_MAX_NGAYDIEUTRI;HIS_DOI_BACSI_DT;HIDE_MENU_CHUYENKHOA_NOIVIEN;HIS_BDT_NGAYRAVIEN;" + "NTU_IN_THEODOI_DIEUTRINOITRU;" + "HIS_INPHIEUKHAMBENH_CHUNG;"
				+ "HIS_DIEUTRI_KETHOP_LCI;" + "HIS_SEARCH_DUYETKETOAN;" + "HIS_LENLICHMO;" + "HIS_SHOW_SOLUUTRU;" + "HIS_SHOW_TOMTAT_BENHAN;" + "HIS_SHOW_BN_DIEUTRIKETHOP;" + "HIS_SHOW_NOISONG;"
				+ "HIS_SHOW_CHANDOAN;" + "HIS_TACH_KHO_NT;" + "HIS_SHOW_GRID_BVBDHN;" + "NTU_DSBN_FULL;" + "HIS_SHOW_MA_BUONG;" + "NBN_BENHNHANNGUOINHA;" + "BDT_DOITUONGBENHNHAN;"
				+ "HIS_HIDE_PHIEU_VATTU;" + "NTU_HIDETHUOCDICHVU;" + "NTU_TACH_TAB_THUOC;" + "NTU_SEARCH_BY_MABENHAN;HIS_CONFIG_KEGIUONG_DANHSACH;"
				+ "NT_GRID_HEADER_TYPE;NTU_PHC_TYPE_TTDT;NTU_CHECK_KETTHUC_BA;NTU_ALLOW_CHUYENDOI_ICU;CDDV_GIAODIEN_KHA;NTU_SUATAN_QTI;NTU_IN_YLTONG;"
				+ "NTU_IN_KIEMDIEM_TUVONG;NTU_IN_MATUY_TCSOSINH_LCI;NTU_QTI_SHOW_SKDT;NTU_UPD_GCS_KTBA;NTU_IN_THUCHIEN_KTPHCN;HIS_GRID_TT_THUOCTRONGNGAY" //L2PT-28613
				+ ";HIS_CANHBAO_TTST;HIS_SHOW_THUOC_MUANGOAI;HIS_SHOW_BENHAN_CHITIET;HIS_SHOW_TTBENHAN_WIDGET;HIS_SHOW_SCROLL;NTU_WIDGET_PCHN_SHOW;HIS_NTU_SHOWSDTBN;HIS_SHOW_BENHAN_ALL;HIS_GRID_TT_BACHITIET" //L2PT-25316
			    + ";HIS_SHOW_BENHAN_SKDT;HIS_PHIEUDT_FORMLV;HIS_NTU_SHOWSDTBN_NTU;HIS_IN_XN_DUONGMAUMAOMACH;HIS_SEARCH_BY_TTKB;HIS_BNRAVIEN_DUOI4H" //L2PT-27520
				+";HIS_SHOW_NGAY_TIEPNHAN;HIS_SHOW_THONGTIN_TUVONG;HIS_SHOW_THONGTIN_TONGPHIEU;HIS_TACHBANGKE_DIEUTRINOITRU;NTU02D129_PHIEUDINHDUONG_NHI"
				+";NTU_BBHC_THUOC_LD" //L2PT-82196
				+";HIS_SHOWTAB_WIDGET_DIEUTRI;BDT_SEARCH_CHKTAOPHIEU;HIS_FIXED_TOOLBAR_NOITRU;HIS_SOPHIEU_TOOLBAR_NOITRU;HIS_SHOW_TTBENHAN_BS;HIS_SHOW_DTKH_KHOACHUYENDEN"
				+";HIS_TIMKIEM_BNNOITRU;HIS_TIMKIEM_BNNOITRU_KETTHUC;HIS_GETGIUONG_BNKETTHUC;NTU02D170_FORMPHIEU_CSNB;HIS_DHYTB_INPDT_KHOAYHCT;HIS_DHYTB_INPDT_KHOAID;PDT_OPEN_UPD_AFTER_COPY"
				+";HIS_GRID_PDT_CDCHAMSOC;HIS_NOTIFY_TIENSU_DIUNGTHUOC;HIS_NOTIFY_TT_TENGA;NTU_TIMKIEM_LOAIDTRINTU;HIS_SHOW_TTBENHAN_NGAYGIUONG" //L2PT-59003
				+";NTU_INBA_HSDBA" // L2PT-76863
				+";NTU_LOAI_GIAYRAVIEN_DOC;NTU_CSCT_QTI" // L2PT-85558
				+";NTU_CHANGE_CBO_TT;HIS_TIMKIEM_NOITRU_SONGAY_MAX" //250312
		);
		if (data_ar != null && data_ar.length > 0) {
			cf = data_ar[0];
			if(data_ar[0].HIS_FIXED_TOOLBAR_NOITRU == '1') {
				$("#dvMsg").show();
				$("#toolbarId").css('position', 'fixed');
				$("#toolbarId").css('left', $("#divMain").offset().left + 16);
				$("#toolbarId").css('right', window.innerWidth - $("#divMain").width() - $("#divMain").offset().left - 30);
				$("#toolbarId").css('z-index', 2);
				$("#toolbarId").css('background', '#FFF');
				$("#dvTab").css('position', 'fixed');
				$("#dvTab").css('left', $("#divMain").offset().left + 16);
				$("#dvTab").css('right', window.innerWidth - $("#divMain").width() - $("#divMain").offset().left - 32);
				$("#dvTab").css('z-index', 1);
				$("#dvTab").css('margin-top', ($("#toolbarId").height() + 10) + 'px');
				$("#dvTab").css('background', '#FFF');
				$("#dvMsg").css('position', 'fixed');
				$("#dvMsg").css('left', $("#divMain").offset().left + 16);
				$("#dvMsg").css('right', window.innerWidth - $("#divMain").width() - $("#divMain").offset().left - 23);
				$("#dvMsg").css('z-index', 1);
				$("#dvMsg").css('margin-top', ($("#toolbarId").height() + $("#dvTab").height() + 10) + 'px');
				$("#dvMsg").css('background', '#FFF');
				$("#dvTHONGTIN").css('margin-top', ($("#toolbarId").height() + $("#dvTab").height() + $("#dvMsg").height() + 10) + 'px');
			}
			//L2PT-26150
			if (data_ar[0].HIS_SHOW_SCROLL == '0') {
				$('#divTTChiTiet').addClass('TTChiTiet');
			}
			if (data_ar[0].HIS_SHOW_NGAY_TIEPNHAN == '1') {
				$('#div_ngaytiepnhan').show();
			}
			HIS_SEARCH_BY_TTKB = data_ar[0].HIS_SEARCH_BY_TTKB;
			if (data_ar[0].HIS_BNRAVIEN_DUOI4H == '1') {
				duoi4h = true;
			}
			if (data_ar[0].HIS_PHIEUDT_FORMLV == '1') {
				form_phieudt_lv = true;
			}
			if (data_ar[0].HIS_SHOW_TTBENHAN_WIDGET == '1') {
				showTTBNWidget = true;
			}
			if (data_ar[0].NTU_UPD_GCS_KTBA == '1') {
				check_ktba_giaycs = true;
			}
			if (data_ar[0].HIS_SEARCH_ENTER == '1') {
				isSearchEnter = true;
			}
			//L2PT-24731 start
			if ((data_ar[0].HIS_NTU_SHOWSDTBN == '1' && type == '3') || (data_ar[0].HIS_NTU_SHOWSDTBN_NTU == '1' && type == '0')) {
				$('#divSDTBN').show();
			}
			//L2PT-24731 end
			//Beg_HaNv_100821: Quy trình suất ăn QTI - L2PT-6138
			if (data_ar[0].NTU_SUATAN_QTI == '1') {
				$('#tabSuatAn').remove();
				$('#toolbarIdtreat_15').remove();
			}
			//End_HaNv_100821
			//L2PT-23578 start thêm thanh cuốn dọc cho thong tin chi tiết BN
			//L2PT-23578 end
			//Begin_HaNv_30112020: Xây dựng giao diện chỉ định DV mới cho DLKHA - L2PT-30889
			if (data_ar[0].CDDV_GIAODIEN_KHA == '1') {
				cddvDLKHA = true;
			}
			//End_HaNv_30112020
			//Begin_HaNv_01072020: Chuyển đổi săn sóc tích cực - L2PT-22747
			if (data_ar[0].NTU_ALLOW_CHUYENDOI_ICU == '1') {
				allowICU = true;
			}
			//End_HaNv_01072020
			//Begin_HaNv_14062019: Cấu hình việc hiển thị thông tin điều trị của BN - L2PT-5509
			if (data_ar[0].NTU_PHC_TYPE_TTDT != '0') {
				$('#divTTDT1').hide();
				$('#divTTDT2').show();
			} else {
				$('#divTTDT1').show();
				$('#divTTDT2').hide();
			}
			//End_HaNv_14062019
			//Begin_HaNv_02102018: dua theo cau hinh de an/hien menu chuyen khoa noi vien - L2HOTRO-11019
			if (data_ar[0].HIDE_MENU_CHUYENKHOA_NOIVIEN == '1') {
				hideMenuCkNoiVien = true;
			}
			//End_HaNv_02102018
			if (data_ar[0].HIS_DOI_BACSI_DT == '1') {
				isSelBs = true;
				$('#divBsOld').hide();
				$('#divBsNew').show();
			} else {
				$('#divBsOld').show();
				$('#divBsNew').hide();
			}
			if (data_ar[0].VP_IN_PHOI_KHI_DONG_BENHAN == '0') {
				isPrintBk = true;
			}
			if (data_ar[0].HIS_NOTIFICE_DUYET_DUOC_CLS == '1') {
				isDdcls = true;
			}
			//L2PT-59003 start
			if (data_ar[0].HIS_NOTIFY_TIENSU_DIUNGTHUOC == '1') {
				is_tiensu_diungthuoc = true;
			}
			if (data_ar[0].HIS_NOTIFY_TT_TENGA == '1') {
				is_tt_tenga = true;
			}
			//L2PT-59003 end
			//Begin_HaNv_22052018: Cau hinh bat buoc khai bao sang loc dinh duong
			if (data_ar[0].HIS_REQUIRED_SANGLOC_DINHDUONG == '1') {
				isRequiredDinhDuong = true;
			}
			//End_HaNv_22052018
			if (data_ar[0].HIS_DT_CAT_CON == '1') {
				isDtcc = true;
			}
			if (data_ar[0].HIS_FULL_MENU_HC == '1') {
				loadToolbarCustom = false;
			}
			if (data_ar[0].HIS_DIEUTRI_KETHOP == '1') {
				_loadDtkh = true;
			} else {
				_loadDtkh = false;
			}
			if (data_ar[0].HIS_INPHIEUKHAMBENH_CHUNG == '1') {
				inPHieuKBChung = true;
			}
			if (data_ar[0].HIS_DIEUTRI_KETHOP_LCI == '1') {
				dtkhLci = true;
			}
			if (data_ar[0].HIS_CANHBAO_HANTHE != '0') {
				checkCbHanThe = true;
				songayCbHanThe = parseInt(data_ar[0].HIS_CANHBAO_HANTHE);
			}
			if (data_ar[0].HIS_CANHBAO_VIENPHI != '0') {
				cbVienPhi = true;
			}
			// jira L2PT-10589: nhi hai duong yeu cau cau hinh rieng cho dieu tri ngoai tru
			if (data_ar[0].HIS_DANHSACH_KHOA != '0' || (data_ar[0].HIS_DANHSACH_KHOA_DTNT != '0' && type == '3')) {
				isDsKhoa = true;
				$('#cboPHONGID').attr("disabled", false);
			}
			if (data_ar[0].HIS_AN_PHIEUMAU != '0') {
				isTruyenmau = true;
			}
			if (data_ar[0].HIS_SHOW_BN_DIEUTRIKETHOP == '1') {
				showDtkh = true;
			}
			//Begin_DoanPV_20201202 L2PT-31107: Hiệu chỉnh hiển thị ở màn hình danh sách bệnh nhận nội trú
			if (data_ar[0].NTU_DSBN_FULL == '1') {
				showAllGrid = true;
			}
			// L2PT-6463
			if (data_ar[0].NTU_IN_YLTONG == '1') {
				inYLTONG = true;
			}
			// L2PT-8630
			if (data_ar[0].NTU_IN_MATUY_TCSOSINH_LCI == '1') {
				inPhieuTheoDoi = true;
			}
			//L2PT-10835
			if (data_ar[0].NTU_IN_THUCHIEN_KTPHCN == '1') {
				inPhieuKTPHCN = true;
			}
			if (data_ar[0].HIS_CONFIG_KEGIUONG_DANHSACH == '1') {
				kegiuongDs = true;
			}
			if (data_ar[0].NBN_BENHNHANNGUOINHA == '1') {
				nguoiNha = true;
			}
			if (data_ar[0].NTU_SEARCH_BY_MABENHAN == '1') {
				searchByMaBA = true;
			}
			
			//Begin_HaNv_23082018: Cấu hình số ngày điều trị tối đa bệnh nhân được điều trị - HISL2TK-884
			if (data_ar[0].HIS_MAX_NGAYDIEUTRI != '' && data_ar[0].HIS_MAX_NGAYDIEUTRI != '0' && !isNaN(parseInt(data_ar[0].HIS_MAX_NGAYDIEUTRI))) {
				maxNgayDt = parseInt(data_ar[0].HIS_MAX_NGAYDIEUTRI);
			} else {
				$("#" + _gridId).jqGrid('hideCol', 'ICON2');
			}
			//End_HaNv_23082018
			//Begin_HaNv14052019: đưa mã BA, mã BN ở grid xuống dưới cùng - L2PT-4610
			gridType = data_ar[0].NT_GRID_HEADER_TYPE;
			var gridHeaderCall = '';
			if (gridType == '1') {
				gridHeaderCall = _gridHeader1;
			}
			// Begin_laphm_05062019: sửa 1 số giao diện - L2PT-5597
			// mở rộng grid, hiển thị thêm mã ICD sau mã BHYT, bổ sung icd chính vào phụ vào thông tin điều trị
			else if (gridType == '2') {
				$('#tabBenhNhanDiv1').removeClass().addClass('col-md-8 pdl0');
				$('#tabBenhNhanDiv2').removeClass().addClass('col-md-4 low-padding');
				gridHeaderCall = _gridHeader2;
			}
			// End_laphm_05062019 L2PT-5597
			// Begin_DoanPV_20201202 L2PT-31107: grid bv bưu điện HN
			else if (gridType == '3') {
				gridHeaderCall = _gridHeader_BDHN;
			}
			// End_DoanPV_20201202
			else if (gridType == '4') {//L2PT-971
				gridHeaderCall = _gridHeader_DKHTH;
			}
			// L2PT-5548 start
			else if (gridType == '5') {
				// L2PT-9324;L2PT-10784 duonghn start
				$('#divTUCL').show();
				if (NTU_DSBN_LOAD_CHIPHI == 1) {
					$('#divKTTU_QTI').show();
				}
				// L2PT-9324;L2PT-10784 duonghn end
				gridHeaderCall = _gridHeader_DKQTI;
			}
			// L2PT-5548 end
			// L2PT-11949 start
			else if(gridType == '6'){
				gridHeaderCall = _gridHeaderYHCTLS;
			}
			// L2PT-11949 end		
			else if (gridType == '100') {
				var par_ctl = ['GRID_TABLE_BDT'];
				var data_table = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', par_ctl.join('$'));
				if(data_table != undefined && data_table.length > 100){
					gridHeaderCall = data_table;
				} else {
					gridHeaderCall = _gridHeader;
				}
				// L2PT-123320 duonghn start
				if (NTU_DSBN_LOAD_CHIPHI == 1) {
					$('#divKTTU_QTI').show();
				}
				// L2PT-123320 duonghn end
			}			
			else {
				gridHeaderCall = _gridHeader;
			}
			//End_HaNv_14052019
			
			if (mauCovid) {
				gridHeaderCall += ';MAUCOVID,MAUCOVID,0,0,t,l';
			}
				
			GridUtil.init(_gridId, "100%", "330px", "Danh sách bệnh nhân", false, gridHeaderCall, false, {
				rowNum : 200,
				rowList : [ 200, 250, 300 ]
			});
			
			if (gridType == '3' && isSearchEnter) {
				GridUtil.init(_gridId, "100%", "330px", "Danh sách bệnh nhân", false, gridHeaderCall, false, {
					rowNum : 200,
					rowList : [ 200, 250, 300 ]
				}, "", true);
			}
				
			if (showAllGrid) {
				$('#tabBenhNhanDiv1').removeClass().addClass('col-md-12 pdl0');
				$('#tabBenhNhanDiv2').hide();
				$('#btnTHUNHO').show();
			}
			//Begin_HaNv_23082018: Cấu hình số ngày điều trị tối đa bệnh nhân được điều trị - HISL2TK-884
			if (data_ar[0].HIS_MAX_NGAYDIEUTRI != '' && data_ar[0].HIS_MAX_NGAYDIEUTRI != '0' && !isNaN(parseInt(data_ar[0].HIS_MAX_NGAYDIEUTRI))) {
				maxNgayDt = parseInt(data_ar[0].HIS_MAX_NGAYDIEUTRI);
			} else {
				$("#" + _gridId).jqGrid('hideCol', 'ICON2');
			}
			//End_HaNv_23082018
			//L2PT-25316 START
			if (data_ar[0].HIS_GRID_TT_BACHITIET != '1') {
				$("#" + _gridId).jqGrid('hideCol', 'TT_BA_CHITIET');
			}
			//L2PT-25316 END
			// begin laphm
			if (data_ar[0].HIS_BDT_NGAYRAVIEN == '1') {
				$('#divTimNgayRa').show();
			} else {
				$('#divTimNgayRa').hide();
			}
			// end laphm
			// START DoanPV_20200813 L2PT-25947
			if (data_ar[0].HIS_SEARCH_DUYETKETOAN == '1') {
				$('#divDuyetKeToan').show();
			} else {
				$('#divDuyetKeToan').hide();
			}
			// END DoanPV_20200813 L2PT-25947
			// L2PT-66453 duonghn start
			if (data_ar[0].NTU_TIMKIEM_LOAIDTRINTU == '1') {
				$('#divLoaiDtriNTU').show();
			} else {
				$('#divLoaiDtriNTU').hide();
			}
			// L2PT-66453 duonghn end
			// end laphm
			if (data_ar[0].NTU_CHECK_KETTHUC_BA == '1') {
				isKetThucBenhAn = true;
			}
			if (data_ar[0].NTU_IN_THEODOI_DIEUTRINOITRU == '1') {
				showTheoDoiDieuTri = true;
			}
			if (data_ar[0].HIS_LENLICHMO == '1') {
				showLichMo = false;
			}
			if (data_ar[0].HIS_SHOW_SOLUUTRU == '1') {
				showSoluutru = true;
			}
			if (data_ar[0].HIS_SHOW_TOMTAT_BENHAN == '1') {
				showBenhan = true;
			}
			if (data_ar[0].HIS_SHOW_BENHAN_CHITIET == '1') {
				showBenhanCT = true;
			}
			if (data_ar[0].HIS_SHOW_BENHAN_ALL != '0') {
				showBenhanALL = data_ar[0].HIS_SHOW_BENHAN_ALL;
			}
			if (data_ar[0].HIS_SHOW_BENHAN_SKDT == '1') {
				showBenhanSKDT = true;
			}
			if (data_ar[0].HIS_SHOW_THUOC_MUANGOAI == '1') {
				showDonThuoc = true;
			}
			if (showDtkh) {
				$('#divBnDtkh').show();
			} else {
				$('#divBnDtkh').hide();
			}
			if (data_ar[0].HIS_SHOW_NOISONG == '1') {
				$('#div_noisong').show();
			}
			if (data_ar[0].HIS_SHOW_CHANDOAN != '1') {
				$("#" + _gridId).hideCol('CHANDOANVAOKHOA');
			}
			if (data_ar[0].HIS_TACH_KHO_NT == '1') {
				isTachKhoNt = true;
			}
			if (data_ar[0].HIS_SHOW_MA_BUONG != '1') {
				$("#" + _gridId).hideCol('PHONG_GIUONG');
			} else {
				$("#" + _gridId).hideCol('ICON1');
			}
			if (data_ar[0].BDT_DOITUONGBENHNHAN == '1') {
				timTheoDTBN = true;
				$('#divDOITUONGBENHNHAN').show();
			} else {
				$('#divDOITUONGBENHNHAN').hide();
			}
			if (data_ar[0].HIS_HIDE_PHIEU_VATTU == '1') {
				hidePhieuVT = true;
			}
			if (data_ar[0].NTU_HIDETHUOCDICHVU == '1') {
				isHideThuocDichVu = true;
			}
			if (data_ar[0].NTU_TACH_TAB_THUOC == '1') {
				tachtabthuoc = true;
			}
			//L2PT-8575
			if (data_ar[0].NTU_QTI_SHOW_SKDT == '1' && type == '0') {
				show_btn_skdt = true;
			}

			if (data_ar[0].HIS_CANHBAO_TTST == '1') {
				mauCovid = true;
			}
			//L2PT-23216, L2PT-23217, L2PT-23218
			if (data_ar[0].NTU_WIDGET_PCHN_SHOW == '1'){
				$('#tabKythuat_PHCN').show();
			}
			
			//L2PT-26520
			if (opt.hospital_id != '924'){
				$('#toolbarIdprint_copd').hide();
				$('#toolbarIdprint_hen').hide();
			}
			//L2PT-28613
			if (data_ar[0].HIS_GRID_TT_THUOCTRONGNGAY == '0') {
				$("#" + _gridId).jqGrid('hideCol', 'ICON_THUOCTRONGNGAY');
			}
			//L2PT-59003 start  ttlinh
			if (data_ar[0].HIS_GRID_PDT_CDCHAMSOC == '0') {
				$("#" + _gridId).jqGrid('hideCol', 'PDT_CDCHAMSOC');
			}
			//L2PT-59003 end
			//L2PT-27520
			if (data_ar[0].HIS_IN_XN_DUONGMAUMAOMACH != '1'){
				$('#toolbarIdprint_dmmm').hide();
			}
			//Beg_HaNv_150523: check phiếu điều trị và đơn thuốc trong ngày - L2PT-40227
			if (cf.BDT_SEARCH_CHKTAOPHIEU == '1') {
				$('#divChkPDT').show();
				$('#divChkThuoc').show();
			} else {
				$('#divChkPDT').hide();
				$('#divChkThuoc').hide();
			}
			//End_HaNv_150523
		}
		//khoi tao toolbar
		var toolbar = null;
		if (type == '0') {
			if (loadToolbarCustom) {
				toolbar = ToolbarUtil.build('toolbarId', ctl_ar_noitru);
				//start BVTM-336 tuyendv
				if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_SUDUNG_KYSO_KYDIENTU') == "1") {
					$(".wd100").css("width", "90");
				} else {
					$('#toolbarIdbtnPrintCa').hide()
				}
				//end BVTM-336 tuyendv
			} else {
				toolbar = ToolbarUtil.build('toolbarId', ctl_ar_noitru_full);
			}
		} else if (type == '3') {
			toolbar = ToolbarUtil.build('toolbarId', ctl_ar_ngoaitru);
			$(".wd100").css("width", "90");
			$("#tabSuatAn").hide();
			$("#tabChamSoc").hide();
			$("#tabHoiChuan").hide();
			$("#tabNgayGiuong").hide();
			//L2PT-12591
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'HIS_NTU_GLI_BBHOICHAN');
			if (data_ar != null) {
				if (data_ar[0].HIS_NTU_GLI_BBHOICHAN == '1') {
					$("#tabHoiChuan").show();
				}
			}
			//L2PT-65801
			if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_BDT_TABNGAYGIUONG') == "1") {
				$("#tabNgayGiuong").show();
			}
		}
		_setButtonToolBar('0');
		//L2PT-8575
		if (show_btn_skdt) {
			$("#toolbarIdbtnSKDT").show();
			$("#toolbarIdbtnSKDT").empty();
			$("#toolbarIdbtnSKDT").css("width", "5%");
			$("#toolbarIdbtnSKDT").css("font-weight", "Bold");
			$("#toolbarIdbtnSKDT").css("color", "Red");
			$("#toolbarIdbtnSKDT").text('SKĐT')
			$('#toolbarIdbtnSKDT').prop('title', 'SƠ KẾT ĐIỀU TRỊ');
		} else {
			$("#toolbarIdbtnSKDT").hide();
		}
		if (_loadDtkh) {
			$('#toolbarIdbtnkh').show();
		} else {
			$('#toolbarIdbtnkh').hide();
		}
		if (isTruyenmau) {
			$("#toolbarIdtreat_7").hide();
		}
		if (!isDtcc) {
			$("#toolbarIdtreat_9").hide();
		}
		if (!showTheoDoiDieuTri) {
			$("#toolbarIdprint_156").hide();
		}
		if (!showLichMo) {
			$("#toolbarIdtreat_22").hide();
		}
		// L2PT-8630
		if (!inPhieuTheoDoi) {
			$("#toolbarIdprint_174").hide();
			$("#toolbarIdprint_175").hide();
			$("#toolbarIdprint_176").hide();
			$("#toolbarIdprint_177").hide();
		}
		//L2PT-10835
		if (!inPhieuKTPHCN) {
			$("#toolbarIdprint_178").hide();
		}
		if (!showSoluutru) {
			$("#" + _gridId).hideCol('SOLUUTRU');
			$("#divSoLuuTru").hide();
		}
		// L2PT-5548 start
		if (gridType == '3' || gridType == '5') {
			$("#dvGHICHU").show();
			if (gridType == '3') {
				$("#divMauchu").hide();
				$("#divMauchuHNI").show();
			} else {
				$("#divMauchu").show();
				$("#divMauchuHNI").hide();
			}
		}
		//L2PT-5548 end
		//Begin_HaNv_02102018: dua theo cau hinh de an/hien menu chuyen khoa noi vien - L2HOTRO-11019
		if (hideMenuCkNoiVien) {
			$("#toolbarIdhandling_7").hide();
			$("#toolbarIdhandling_8").hide();
			$("#toolbarIdhandling_9").hide();
		}
		//End_HaNv_02102018
		//Begin_HaNv_01072020: Chuyển đổi săn sóc tích cực - L2PT-22747
		if (!allowICU) {
			$("#toolbarIdtreat_25").hide();
		}
		//End_HaNv_01072020
		//Begin_DoanPV_20201201 L2PT-31476 : Tách Menu kê thuốc ngoại trú
		if (isTachKhoNt && type == '3') {
			$("#toolbarIddrug_khothuoc").hide();
		} else {
			$("#toolbarIddrug_khothuoc1").hide();
			$("#toolbarIddrug_khothuoc2").hide();
		}
		// start jira L2PT-7172
		// an menu thuoc va dich vu
		if (isHideThuocDichVu) {
			// $('#toolbarIdbtndrug').hide();
			$('#toolbarIdbtnService').hide();
		}
		// end jira L2PT-7172
		// giay ra vien da chien LPKHA L2PT-7970
		if (opt.hospital_id != 26780) {
			$("#toolbarIdprint_172").hide();
		}
		//load du lieu danh sach benh nhan
		var sql_ttkb = 'COM.TRANGTHAIKB';
		if(HIS_SEARCH_BY_TTKB == '1') {
			sql_ttkb = 'COM.TRANGTHAIKB.1';
		}
		ComboUtil.getComboTag("cboTRANGTHAIKHAMBENH", sql_ttkb, [ {
			"name" : "[0]",
			"value" : "TEN_NOITRU"
		} ], "", {
			value : '',
			text : 'Chọn'
		}, "sql", "", function() {
			setValTrangThai(4);
			loadGridData();
		});
		if (isSelBs) {
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H046.EV003", ssid);
			if (data_ar != null && data_ar.length > 0) {
				data = data_ar[0];
				$("#labelBACSIDIEUTRIMS").html(data.OFFICER_NAME + ' (' + data.USER_NAME + ')');
				$("#labelHidBACSYKE").val(data.USER_ID);
			}
		}
		if (NTU_DOITEN_KCK == '1') {
			$("#toolbarIdhandling_9").hide();
			$("#toolbarIdhandling_10").hide();
			$("#toolbarIdhandling_11").hide();
		}
		if (nguoiNha) {
			$('#divTIMNGUOINHA').show();
			$('#divBENHNHANNGUOINHA').show();
		}
		//L2PT-114638 start
		if (cf.NTU_TTBN_SHOW_CCCD == '1') {
			$('#divCCCD').show();
		}
		//L2PT-114638 end
		NGT_HIENTHIMAU_CAPCUU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_HIENTHIMAU_CAPCUU'); //L2PT-27662
		if (NGT_HIENTHIMAU_CAPCUU != "" && NGT_HIENTHIMAU_CAPCUU != "0") {
			$("#spGhichuCapCuu").css("color", NGT_HIENTHIMAU_CAPCUU);
		}
		NGT_HIENTHIMAU_DATLICH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_HIENTHIMAU_DATLICH');
		if (NGT_HIENTHIMAU_DATLICH != "" && NGT_HIENTHIMAU_DATLICH != "0") {
			$("#spGhichuDatLich").css("color", NGT_HIENTHIMAU_DATLICH);
		}
		//set up lai phieu in theo user dang nhap
		checkRolePhieuIn();
		// clone tu việc lấy ds bác sĩ của màn chỉ định dịch vụ
		ComboUtil.getComboTag("cboBACSIID", "NGT02K016.EV002", [ {
			"name" : "[0]",
			"value" : _opts._deptId
		} ], "", {
			value : '0',
			text : '-- Chọn --'
		}, "sql");
		var csytid = opt.hospital_id;
		// phiếu được cắt cơm
		// hoc vien an ninh
		// site that: 10307, site testl2: 20524
		if (csytid != 10307 && csytid != 20524) {
			$('#toolbarIdprint_phieuduoccatcom').hide();
		}
		// start jira BVTM-856
		// site buu dien yeu cau an chuc nang sang loc dinh duong va phieu suat an
		if (csytid == 10284) {
			$('#toolbarIdtreat_13').hide();
			$('#toolbarIdtreat_15').hide();
		} else {
			// $('#toolbarIdtreat_29').hide();
		}
		// end jira BVTM-856
		// Hưng nhân
		if (csytid != 32660) {
			$('#toolbarIdprint_13_word').hide();
		}
		// start jira L2PT-2113
		// nhap thong tin benh an
		if (csytid != 29040) {
			$('#toolbarIdfunction_27').hide();
		}
		// end jira L2PT-2113
		// start jira L2PT-3573
		// giay chung nhan thuong tich - quan y 15 - 1216
		if (csytid != 1216) {
			$('#toolbarIdprint_gcntt').hide();
		}
		// end jira L2PT-3573
		// start jira 3371
		// phieu duyet mo - da khoa lao cai 923
		/*if (csytid != 923) {
			$('#toolbarIdtreat_tpdm').hide();
		}*/
		// end jira 3371
		//Beg_HaNv_260721: L2PT-5102 - L2PT-6011
		if (csytid == 947) {
			$("#toolbarIddrug_6").remove();
		}
		//End_HaNv_260721
		// in so benh an CBG-YHCT L2PT-5320
		if (csytid != 1111) {
			$('#toolbarIdprint_135').hide();
			$('#toolbarIdprint_136').hide();
			$('#toolbarIdprint_137').hide();
			$('#toolbarIdprint_138').hide(); // L2PT-10960
		}
		// start jira L2PT-5902
		// bang ke doi soat dich vu - yhct lang son - 30680
		if (csytid != 30680) {
			$('#toolbarIdprint_bkdsdv').hide();
		}
		// end jira L2PT-5902
		// start jira L2PT-6077
		if (csytid == 30252) {
			$('#divRightBenhChinh').show();
		}
		// end jira L2PT-6077
		// start jria L2PT-6189
		// nut goi kham cho yhct quang ngai (rut gon code, chi lay code tuong ung cau hinh KBH_CHEDO_GOIKHAM = 2) tu man NGT02K001_KB_MHC
		if (getCauHinh("KBH_CHEDO_GOIKHAM") == "2" && csytid == 1059) {
			$('#toolbarIdbtnCall').show();
		} else {
			$('#toolbarIdbtnCall').hide();
		}
		// start jria L2PT-6189
		// start jira L2PT-6686
		// đổi tên 1 số menu
		if (csytid == 30360) {
			$('#toolbarIddrug_khothuoc a').html('&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-thuoc"></i>&nbsp;&nbsp;Tạo phiếu THUỐC (BHYT, viện phí, yêu cầu…)');
			$('#toolbarIddrug_3 a').html('&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-thuoc"></i>&nbsp;&nbsp;Tạo phiếu VẬT TƯ (BHYT, viện phí, yêu cầu…)');
			$('#toolbarIddrug_8 a').html('&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-thuoc"></i>&nbsp;&nbsp;Tạo phiếu THUỐC hao phí');
			$('#toolbarIddrug_hpvt a').html('&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-thuoc"></i>&nbsp;&nbsp;Tạo phiếu VẬT TƯ hao phí');
		}
		// end jira L2PT-6686
		// start jira L2PT-9487
		// đổi tên 1 số menu
		if (csytid == 5926) {
			$('#toolbarIddrug_8 a').html('&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-thuoc"></i>&nbsp;&nbsp;Tạo phiếu trọn gói');
			$('#toolbarIddrug_hpvt a').html('&nbsp;&nbsp;&nbsp;&nbsp;<i class="glyphicon glyphicon-thuoc"></i>&nbsp;&nbsp;Tạo phiếu VT trọn gói');
		}
		// end jira L2PT-9487
		// start jira BVTM-5517
		// them goi kham chung
		if (csytid != 10284) {
			$('#toolbarIdfunction_28').hide();
		}
		// end jira BVTM-5517
		// start jira BVTM-5585
		if (hidePhieuVT) {
			$("#toolbarIddrug_3").hide();
		}
		// end jira BVTM-5585
		// start jira L2PT-5949
		// phieu phan ung adr - da khoa ha tinh
		if (csytid != 26320) {
			$('#toolbarIddrug_1put').hide();
			$('#toolbarIdprint_201').hide();
		}
		// end jira L2PT-5949
		// cong van 3730
		// tth ha tinh, phcn vinh phuc, hung ha, phcn lang son, nhi hai duong, bac son lang son, lao phoi lang son, yhct cao bang, dinh lap lang son
		// if (![ "947", "27040", "30252", "28600", "30680", "957", "26720", "924", "30900", "32240" ].includes(csytid + "")) {
		// 	$('#toolbarIdtreatdt_pkcdphcn').hide();
		// 	$('#toolbarIdtreatdt_plghdcntg').hide();
		// }

		// phieu cong khai y lenh theo ngay
		// da khoa lao cai
		if (![ "923" ].includes(csytid + "")) {
			$('#toolbarIdprint_ckyltn').hide();
		}
		// dakhoa quảng trị
		if (csytid != 30360) {
			$('#toolbarIdprint_CAMKETPTTT').hide();
			$('#toolbarIdprint_180').hide();
			$('#toolbarIdfunction_29').hide();
			$('#toolbarIdfunction_31').hide();
		}
		
		//L2PT-40952
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'IN_AN_PRINT_CAMKETPTTT') == "1") { 
			$('#toolbarIdprint_CAMKETPTTT').show();
		}
		
		//Beg_HaNv_051022: L2PT-27156 (VPC-LV1,LV2) DBN-BINHAN: L2PT-48489;QTI-L2PT-50088
		if ([ "1133", "24750", "63380", "32940", "35600", "28660" ].includes(csytid)) {
			$('#toolbarIdfunction_31').show();
		}
		//End_HaNv_051022
		//L2PT-21249 start	
		
		//L2PT-41126
		//if (csytid != 30180) {
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'MENU_KHAC_THONGTIN_TUVONG') == "1") { 
			$('#toolbarIdfunction_32').show();
		}
		else{
			$('#toolbarIdfunction_32').hide();
		} 
		//L2PT-21249 end
		// Phiếu công khai thuốc và thủ thuật - L2PT-12085
		if (csytid != 30900) {
			$('#toolbarIdprint_congkhaithuoctt').hide();
            $('#toolbarIdprint_139').hide();
		}

		// L2PT-12775, L2PT-12776, L2PT-12777, L2PT-26426 csytid != 26780 && csytid != 30360
		if (getCauHinh("NTU_BDT_IN_DGDINHDUONG") != "1") {
			$('#toolbarIdprint_ttdd').hide();
			$('#toolbarIdprint_ttdd_nhi').hide();
			$('#toolbarIdprint_pnmt').hide();
			$('#toolbarIdprint_18_1').hide();
		}

		// L2PT-16059
		if (csytid != 10284) {
			$('#toolbarIdtreat_bbhctbvbd').hide();
		}

		// L2PT-12582
		if (![ "26060", "26320", "33560", "35020", "37280", "37820" ].includes(csytid + "")) {
			$('#toolbarIdprint_pkck').hide();
		}

		// L2PT-15676
		if (csytid != 960) {
			$('#toolbarIdprint_gmhs').hide();
		}
		
		//L2PT-23661 start
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_IN_QD1921') == '1') {
			$('#toolbarIdprint_nntv').hide();
			$('#toolbarIdprint_xinve').hide();
		}
		//L2PT-23661 end
		
		//L2PT-23979 start
		if (csytid != 987) {
			$('#toolbarIdtreat_dgbd_d155').hide();
		}
		//L2PT-23979 end
		
		//L2PT-23570 start
		if (csytid != 1133) {
			$('#toolbarIdtreat_bcyt').hide();
		}
		//L2PT-23570 end

		if (!tachtabthuoc) {
			$("#tabMau").hide();
		}
		if (searchByMaBA) {
			$('#div_mabenhan').show();
			$('#div_tenbenhnhan').show();
			$('#div_mabhyt').show();
		}

		// L2PT-15640
		// L2PT-21158 start
		if (getCauHinh("NTU_BDT_SHOW_PTDCNS") == "1") {
			$('#tabPTDCNS').show();
		}
		// L2PT-21158 end

		// phiếu khám tiền mê
		// if (![ "902"].includes(csytid + "")) {
		// 	$('#toolbarIdtreatdt_pktm').hide();
		// }
		if(cf.HIS_SHOWTAB_WIDGET_DIEUTRI != 0) {
			var _tabHide = cf.HIS_SHOWTAB_WIDGET_DIEUTRI.split(',');
			_tabHide.forEach(function (el) {
				if (el == '1') {
					$('#idTabXetNghiem').hide();
				} else if (el == '2') {
					$('#idTabCDHA').hide();
				} else if (el == '3') {
					$('#idTabChuyenKhoa').hide();
				} else if (el == '4') {
					$('#idTabSuatAn').hide();
				} else if (el == '5') {
					$('#idTabPhieuTruyenMau').hide();
				} else if (el == '6') {
					$('#idTabDieuTri').hide();
				} else if (el == '7') {
					$('#idTabMau').hide();
				} else if (el == '8') {
					$('#idTabVC').hide();
				} else if (el == '9') {
					$('#idTabChamSoc').hide();
				} else if (el == '10') {
					$('#idTabThuoc').hide();
				} else if (el == '11') {
					$('#idTabTruyenDich').hide();
				} else if (el == '12') {
					$('#idTabPhanUngThuoc').hide();
				} else if (el == '13') {
					$('#idTabHoiChan').hide();
				} else if (el == '14') {
					$('#idTabVatTu').hide();
				} else if (el == '15') {
					$('#idTabPTDCNS').hide();
				} else if (el == '16') {
					$('#idTabNgayGiuong').hide();
				} else if (el == '17') {
					$('#idTabPTDCNS').hide();
				}
			});
		}
	}
	//set mac dinh tim kiem trang thai dang dieu tri
	function setValTrangThai(_trangthai) {
		$("#cboTRANGTHAIKHAMBENH").val(4);
		if (cf.HIS_TIMKIEM_BNNOITRU != '0') {
			$("#txtTG_NHAPVIEN_DEN").attr("disabled", false);
			$("#txtTG_NHAPVIEN_TU").attr("disabled", false);
			$("#chkTIMTHEONGAY").prop('checked', true);
			var denngay = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
			$("#txtTG_NHAPVIEN_DEN").val(denngay);
			var tungay = moment($('#txtTG_NHAPVIEN_DEN').val(), "DD/MM/YYYY").add(-30, 'days').format('DD/MM/YYYY');
			$("#txtTG_NHAPVIEN_TU").val(tungay);
		}
	}
	//xu ly cho su kien click len row danh sach benh nhan
	function bindEvent() {
		//khoi tao luoi danh sach
		GridUtil.setGridParam(_gridId, {
			onSelectRow : function(id, selected) {
				viewDetail(id);
				GridUtil.unmarkAll(_gridId);
				GridUtil.markRow(_gridId, id);
				var _trangthaikhambenh = $("#hidTRANGTHAIKHAMBENH").val();
				var _trangthaitiepnhan = $("#hidTRANGTHAITIEPNHAN").val();
				var rowData = $('#' + _gridId).jqGrid('getRowData', id);
				//L2PT-57870 start ttlinh
				var _ch_popuptitle = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", "HIS_POPUP_SHOWTTBN");
				if (_ch_popuptitle && _ch_popuptitle != 0) {
					var _ch_data = _ch_popuptitle.split(';');
					var data = [];
					for (var i = 0; i < _ch_data.length; i++) {
						var x = _ch_data[i];
						data.push(rowData[x]);
					}
					thongtinbn = data.join('/ ');
				}
				$("#hidTHONGTINBN").val(thongtinbn); //L2PT-59003
				//L2PT-57870 end
				_kbdtkhid = rowData.KHAMBENHDTKHID;
				//truong hop ket thuc kham
				if (_trangthaikhambenh == '9' || _trangthaikhambenh == '1') {
					_setButtonToolBar('2');
					// L2PT-76863 start
					if(cf.NTU_INBA_HSDBA == '1' && type == '3' && _trangthaikhambenh == '9') {
						setEnabled(['toolbarIdbtnBan'],[]);
					}
					// L2PT-76863 end
					_flgModeView = '1';
				} else if (_trangthaikhambenh == '8') {
					_setButtonToolBar('3');
					_flgModeView = '1';
				} else {
					//Begin_HaNv_22052018: Cau hinh bat buoc khai bao sang loc dinh duong
					var rowData = $('#' + _gridId).jqGrid('getRowData', id);
					if (isRequiredDinhDuong && _trangthaikhambenh == '4') {
						var _sql_par = [ {
							"name" : "[0]",
							"value" : $("#hidKHAMBENHID").val()
						}, {
							"name" : "[1]",
							"value" : "23"
						} ];
						var _res = jsonrpc.AjaxJson.ajaxExecuteQueryO("CHECK.DINHDUONG", _sql_par);
						var data = JSON.parse(_res);
						if (data.length > 0 && data[0].MAUBENHPHAMID != '' && data[0].MAUBENHPHAMID != null) {
							_setButtonToolBar('1');
						} else {
							_setButtonToolBar('0');
							paramInput = {
								type : 3,
								khambenhid : $("#hidKHAMBENHID").val(),
								modeView : 2
							};

							//L2PT-42569
							var _form = '';
							if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'NTU02D086_SANGLOCDINHDUONG40782') == '1') _form = '40782';

							dlgPopup = DlgUtil.buildPopupUrl("divDlgSangLocNguyCo", "divDlg", "manager.jsp?func=../noitru/NTU02D086_SangLocDinhDuong"
							+ _form //L2PT-42569
							, paramInput, "Sàng lọc và đánh giá dinh dưỡng " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
									1000, 500);
							DlgUtil.open("divDlgSangLocNguyCo");
						}
					} else {
						_setButtonToolBar('1');
					}
					//End_HaNv_22052018
					_flgModeView = '0';
				}
				//check ko cho chi dinh cls khi chua cap nhat ba chung
				var NTU_CHECK_CAPNHAT_BACHUNG = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'NTU_CHECK_CAPNHAT_BACHUNG');
				if (NTU_CHECK_CAPNHAT_BACHUNG == '1') {
					var sql_par = [];
					sql_par.push({
						"name" : "[0]",
						value : $("#hidKHAMBENHID").val()
					});
					var _capnhat_bachung = jsonrpc.AjaxJson.getOneValue('NTU02D021.UPD.BA', sql_par);
					if (_capnhat_bachung == '1') {
						$("#toolbarIdbtnService").attr("disabled", false);
						$("#toolbarIdbtndrug").attr("disabled", false);
					} else {
						$("#toolbarIdbtnService").attr("disabled", true);
						$("#toolbarIdbtndrug").attr("disabled", true);
					}
				}
				//L2PT-9359 DoanPV-20191011 chan ko cho in giay ra vien khi chưa ket thuc BA
				if (isKetThucBenhAn) {
					if (_trangthaitiepnhan == 0) {
						$("#toolbarIdprint_5").hide();
						$("#toolbarIdprint_5_doc").hide();
						$("#toolbarIdprint_172").hide();
					} else {
						$("#toolbarIdprint_5").show();
						$("#toolbarIdprint_5_doc").show();
						$("#toolbarIdprint_172").show();
					}
				}
			},
			ondblClickRow : function(id) {
				var rowData = $('#' + _gridId).jqGrid('getRowData', id);
				if (rowData != null) {
					if (rowData.TRANGTHAIKHAMBENH == '9') {
						DlgUtil.showMsg("Bệnh án đã đóng");
						return;
					}
					//mo popup nhap benh nhan khi dbclick on row
					var paramInput = {
						mode : 1,
						benhnhanid : rowData.BENHNHANID,
						khambenhid : rowData.KHAMBENHID,
						hosobenhanid : rowData.HOSOBENHANID,
						tiepnhanid : rowData.TIEPNHANID,
						type : type
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhNhan", "divDlg", "manager.jsp?func=../noitru/NTU01H002_NhapBenhNhan", paramInput, "HIS - Cập nhật bệnh nhân  " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
							window.innerWidth * 0.95, window.innerHeight * 0.95);
					DlgUtil.open("divDlgNhapBenhNhan");
				}
			},
			loadComplete : function(id) {
				var ids = $("#" + _gridId).getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var row = $("#" + _gridId).jqGrid('getRowData', id);
					//set icon trang thai  benh nhan
					var iconCell1 = getIconCell1(row);
					$("#" + _gridId).jqGrid('setCell', id, 1, iconCell1);
					if (cf.HIS_GETGIUONG_BNKETTHUC == '1' && row.TRANGTHAIKHAMBENH == '9') {
						$("#" + _gridId).jqGrid('setCell', id, 'TENGIUONG', row.TENGIUONG_KT);
					}
					//Beg_HaNv_190523: Điều chỉnh icon giường giống Hành chính NTU - L2PT-42876
					if (row.GIUONGID != 0) {
						if (row.GIUONGID == '1') {
							$("#" + _gridId).jqGrid('setCell', id, 3, '<center><i class="fa fa-bed"	style="font-size:15px;color:green"></i> </center>');
						} else if (row.GIUONGID == '2') {
							$("#" + _gridId).jqGrid('setCell', id, 3, '<center><i class="fa fa-bed"	style="font-size:15px;color:yellow"></i> </center>');
						} else if (row.GIUONGID == '3') {
							$("#" + _gridId).jqGrid('setCell', id, 3, '<center><i class="fa fa-bed"	style="font-size:15px;color:red"></i> </center>');
						} else {
							$("#" + _gridId).jqGrid('setCell', id, 3, '<center><img src="../common/image/Bed.png" width="15px"></center>');
						}
					}
					//End_HaNv_190523
					//HungND - L2PT-67343
					if(row.SERI != ""){
						$("#" + _gridId).jqGrid('setCell', id, 'TT_BENHAN', '<center><i class="fa fa-text-width" style="font-size:15px;color:green"></i> </center>');
					}
					//End HungND - L2PT-67343
					// set icon trang thai cls
					var _iconCls = '';
					var _trangthaiCls = parseInt(row.CHECK_KQ_CLS);
					if (_trangthaiCls == 1) {
						_iconCls = '<center><img src="../common/image/Flag_Red_New.png" width="15px"></center>';
						$("#" + _gridId).jqGrid('setCell', id, 'TRANGTHAIPTTT', 'Chưa hoàn thành');
					} else if (_trangthaiCls == 2) {
						_iconCls = '<center><img src="../common/image/True.png" width="15px"></center>';
						$("#" + _gridId).jqGrid('setCell', id, 'TRANGTHAIPTTT', 'Đã hoàn thành');
					}
					$("#" + _gridId).jqGrid('setCell', id, 2, _iconCls);
					//Begin_HaNv_23082018: Cấu hình số ngày điều trị tối đa bệnh nhân được điều trị - HISL2TK-884
					//set icon dau hieu canh bao qua ngay dieu tri
					var _iconSndt = '<center><img src="../common/image/Pointer_waiting.png" width="15px"></center>';
					if (maxNgayDt != -1 && (parseFloat(row.SONGAYDIEUTRI)) > maxNgayDt) {
						$("#" + _gridId).jqGrid('setCell', id, 'ICON2', _iconSndt);
					}
					//End_HaNv_23082018
					//L2PT-27125 set cảnh báo thực hiện thuốc BDHNI
					var _iconTTT = '';
					if (row.THUCHIENTHUOC == '1') {
						_iconTTT = '<center><img src="../common/image/Drug_Red.png" width="15px"></center>';
					} else if (row.THUCHIENTHUOC == '0') {
						_iconTTT = '<center><img src="../common/image/Drug_Green.png" width="15px"></center>';
					}
					$("#" + _gridId).jqGrid('setCell', id, 'ICON_THUOC', _iconTTT);
					//L2PT-27125 end
					//L2PT-28613
					var _iconTTT = '';
					if (row.THUOCTRONGNGAY == '1') {
						_iconTTT = '<center><img src="../common/image/Drug_Red.png" width="15px"></center>';
					}
					$("#" + _gridId).jqGrid('setCell', id, 'ICON_THUOCTRONGNGAY', _iconTTT);
					//L2PT-59003 start
					if (row.PDT_CDCHAMSOC) {
						$("#" + _gridId).setCell(id, 'PDT_CDCHAMSOC', '', {
							'font-weight' : 'bold'
						});
					}
					//L2PT-59003 end
					//set màu chữ
					if (opt.hospital_id == 10284) {
						if (row.MADANGKY != 'undefined' && row.MADANGKY != '' && row.MADANGKY != '0') {
							$("#" + _gridId).jqGrid('setRowData', id, "", {
								color : NGT_HIENTHIMAU_DATLICH
							});
						}
						if (row.THANNHAN != '0') {
							$("#" + _gridId).jqGrid('setRowData', id, "", {
								color : '#008000'
							});
						}
						if (row.HINHTHUCVAOVIENID == '2') {
							$("#" + _gridId).jqGrid('setRowData', id, "", {
								color : NGT_HIENTHIMAU_CAPCUU
							});
						}
						if (row.UUTIENKHAMID != '' && row.UUTIENKHAMID != '0') {
							$("#" + _gridId).jqGrid('setRowData', id, "", {
								color : '#0000FF'
							});
						}
						var tongtien = row.TONGTIENDICHVUCORE.split(";");
						$("#" + _gridId).jqGrid('setCell', id, 'TONGCHIPHI', formatNumber(tongtien[0]));
						$("#" + _gridId).jqGrid('setCell', id, 'TAMUNG', formatNumber(tongtien[4]));
						$("#" + _gridId).jqGrid('setCell', id, 'DANOP', formatNumber(tongtien[2]));
						$("#" + _gridId).jqGrid('setCell', id, 'CONNO', formatNumber(tongtien[8]));
						// L2PT-9324;L2PT-10784 duonghn end
						if (tongtien[8] > '0') { // L2PT-9324
							$("#" + _gridId).setCell(id, 'CONNO', '', {
								'background-color' : 'red'
							});
							if (row.HINHTHUCVAOVIENID == '2') {
								$("#" + _gridId).setCell(id, 'CONNO', '', {
									'color' : '#FFFFFF'
								});
							} else if (row.MADANGKY != 'undefined' && row.MADANGKY != '' && row.MADANGKY != '0') {
								$("#" + _gridId).setCell(id, 'CONNO', '', {
									'color' : '#FFFFFF'
								});
							}
						}
					} else {
						if (row.BHYT_LOAIID == 4) {
							$("#" + _gridId).jqGrid('setRowData', id, "", {
								color : 'red'
							});
						}
						if (checkCbHanThe && row.DOITUONGBENHNHANID == '1') {
							if ((parseInt(row.SONGAYBHYT) > 0 && songayCbHanThe >= parseInt(row.SONGAYBHYT)) || (parseInt(row.SONGAYBHYT) <= 0)) {
								$("#" + _gridId).jqGrid('setRowData', id, "", {
									color : '#DF01D7'
								});
								$("#" + _gridId).setCell(id, 'TENBENHNHAN', '', {
									'font-weight' : 'bold'
								});
							}
						}
						if (cbVienPhi && row.DOITUONGBENHNHANID == '2') {
							$("#" + _gridId).jqGrid('setRowData', id, "", {
								color : 'blue'
							});
							$("#" + _gridId).setCell(id, 'TENBENHNHAN', '', {
								'font-weight' : 'bold'
							});
						}
						// start jira L2PT-5133
						if (nguoiNha && row.ISNGUOINHA == '1') {
							$("#" + _gridId).jqGrid('setRowData', id, "", {
								color : '#0f9d58'
							});
						}

						if (duoi4h && row.DUOI4H == '1') {
							$("#" + _gridId).jqGrid('setRowData', id, "", {
								color : '#00bfbf'
							});
						}
						// end jira L2PT-5133
						// L2PT-9324;L2PT-10784 duonghn start: bỏ tính chi phí trên grid voi DKQTI
						// L2PT-5548 START
						if (gridType == '5' || gridType == '100') { //L2PT-51404
							var tongtien_text = row.TONGTIENDICHVUCORE;
							if (tongtien_text && tongtien_text.length > 0) {
								var tongtien = row.TONGTIENDICHVUCORE.split(";");
								if (tongtien.length > 3) {
									$("#" + _gridId).jqGrid('setCell', id, 'TONGCHIPHI', formatNumber(tongtien[0]));
									$("#" + _gridId).jqGrid('setCell', id, 'TAMUNG', formatNumber(tongtien[4]));
									$("#" + _gridId).jqGrid('setCell', id, 'DANOP', formatNumber(tongtien[2]));
									$("#" + _gridId).jqGrid('setCell', id, 'BNTRA', formatNumber(tongtien[1]));
									var conNo = parseFloat(tongtien[4]) - parseFloat(tongtien[1]);
									if(opt.hospital_id == 64960) {//HaNv_290524: L2PT-86897
										conNo = conNo + parseFloat(tongtien[2]);
									}
									conNo = parseFloat(conNo.toFixed(2));
									$("#" + _gridId).jqGrid('setCell', id, 'CONNO', formatNumber(conNo));
									//L2PT-9693 duonghn: bôi màu cho những BN có tạm ứng còn lại < 0
									if (NTU_BOIMAU_TAMUNGAM == '1' && parseFloat(conNo) < 0) {
										$("#" + _gridId).jqGrid('setRowData', id, "", {
											color : '#fc5e03'
										});
									}
								}
							}
						}
						// L2PT-5548 END
						// L2PT-9324;L2PT-10784 duonghn end
					}

					if (row.MAUCOVID && row.MAUCOVID != '0') {
						$("#" + _gridId).setCell(id, 'MABENHAN', '', {
							'background-color' : row.MAUCOVID
						});
					}
				}
			}
		});
		//Begin_HaNv_22052018: Load lai button khi sang loc dinh duong thanh cong
		EventUtil.setEvent("sangLocDinhDuong_success", function(e) {
			if (typeof (e) != 'undefined') {
				DlgUtil.showMsg(e.msg);
			}
			DlgUtil.close("divDlgSangLocNguyCo");
			_setButtonToolBar('1');
		});
		//End_HaNv_22052018
		//Tim kiem benh nhan
		searchBenhNhan();
		//xu ly su kien click tren toolbar
		processToolBar();
		//xu ly su kien load data widget tren cac tab
		loadDataTabs();
		//thong bao message
		if (isDdcls) {
			var results = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D021.001", opt._deptId);
			if (results > 0) {
				myLoopMessage();
			}
		}
		$("#btnMORONG").on("click", function() {
			$('#tabBenhNhanDiv1').removeClass().addClass('col-md-12 pdl0');
			$('#tabBenhNhanDiv2').hide();
			$('#btnMORONG').hide();
			$('#btnTHUNHO').show();
		});
		$("#btnTHUNHO").on("click", function() {
			$('#tabBenhNhanDiv1').removeClass().addClass('col-md-7 pdl0');
			$('#tabBenhNhanDiv2').show();
			$('#btnMORONG').show();
			$('#btnTHUNHO').hide();
		});
		$("#txtMABENHAN").unbind('keydown').keydown(function(e) {
			if (e.keyCode == '13') {
				loadGridData();
			}
		});
		$("#txtMABENHNHAN").unbind('keydown').keydown(function(e) {
			if (e.keyCode == '13') {
				loadGridData();
			}
		});
		$("#txtTENBENHNHAN").unbind('keydown').keydown(function(e) {
			if (e.keyCode == '13') {
				loadGridData();
			}
		});
		$("#txtMABHYT").unbind('keydown').keydown(function(e) {
			if (e.keyCode == '13') {
				loadGridData();
			}
		});
		// L2PT-10784 mở pop up kiểm tra tạm ứng
		$("#btnKiemTraTU").click(function(e) {
			var myVar = new Object();
			myVar.type = type;
			DlgUtil.buildPopupUrl("dlgDSBN_CP", "dlgDSBN_CHIPHI", "manager.jsp?func=../noitru/NTU02D021_DSBN_ChiPhi", myVar, "Danh sách bệnh nhân - chi phí " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1250, 600);
			DlgUtil.open("dlgDSBN_CP");
		});
		// L2PT-10784 end
		// L2PT-28917 duonghn start
		//Begin_HaNv_03042020: Tạo chức năng Hẹn trả Kết quả CLS cho viện phí - L2PT-18983
		$("#toolbarIdbtnHenTraKQCLS").click(function() {
			if ($("#hidTIEPNHANID").val() != null && $("#hidTIEPNHANID").val() != '') {
				paramInput = {
					tiepnhanid : $("#hidTIEPNHANID").val()
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../vienphi/VPI02G006_HenTraKQCLS", paramInput, "Danh sách phiếu CLS " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 500);
				DlgUtil.open("divDlgDichVu");
			} else {
				DlgUtil.showMsg("Chưa chọn bệnh nhân");
			}
		});
		//End_HaNv_03042020
		// L2PT-28917 duonghn end
	}
	var i1 = 0;
	function myLoopMessage() {
		setTimeout(function() {
			$.bootstrapGrowl('Tồn tại bệnh nhân trong khoa bị từ chối duyệt dược CLS', {
				type : 'warning',
				delay : 3000,
			});
			i1++;
			if (i1 < 100) {
				myLoopMessage();
			}
		}, 6000);
	}
	//START L2HOTRO-13545
	var i2 = 0;
	function LoopMessageTamung() {
		setTimeout(function() {
			$.bootstrapGrowl('Bệnh nhân sắp hết tiền ký quỹ', {
				type : 'warning',
				delay : 3000,
			});
			i2++;
			if (i2 < 100) {
				LoopMessageTamung();
			}
		}, 6000);
	}
	//END L2HOTRO-13545
	function _validateParam() {
		if ($('#txtTG_NHAPVIEN_TU').val().trim().length > 0 && (!datetimeRegex.test($('#txtTG_NHAPVIEN_TU').val()) || !checkDate($('#txtTG_NHAPVIEN_TU').val()))) {
			DlgUtil.showMsg("Từ ngày " + $.i18n("date_type_invalid"));
			$('#txtTG_NHAPVIEN_TU').focus();
			return false;
		}
		if ($('#txtTG_NHAPVIEN_DEN').val().trim().length > 0 && (!datetimeRegex.test($('#txtTG_NHAPVIEN_DEN').val()) || !checkDate($('#txtTG_NHAPVIEN_DEN').val()))) {
			DlgUtil.showMsg("Đến ngày " + $.i18n("date_type_invalid"));
			$('#txtTG_NHAPVIEN_DEN').focus();
			return false;
		}
		//250312 ttlinh start tối ưu số ngày tối đa cho phép tìm kiếm bn nội trú
		var diffdate = diffDate($("#txtTG_NHAPVIEN_DEN").val(), $('#txtTG_NHAPVIEN_TU').val(), 'DD/MM/YYYY', 'days');
		if (parseInt(diffdate) > cf.HIS_TIMKIEM_NOITRU_SONGAY_MAX) {
			DlgUtil.showMsg('Khoảng ngày tìm kiếm vượt quá giới hạn ' + cf.HIS_TIMKIEM_NOITRU_SONGAY_MAX + ' ngày trong cấu hình: HIS_TIMKIEM_NOITRU_SONGAY_MAX. Đề nghị chỉ tìm kiếm trong khoảng ngày kiến nghị!');
			return false;
		}
		//250312 ttlinh end
		return true;
	}
	$("#btnDOIBACSI").on("click", function() {
		paramInput = {
			ssid : ssid
		};
		dlgPopup = DlgUtil.buildPopupUrl("divDlgBacSyChiDinh", "divDlg", "manager.jsp?func=../noitru/NTU01H046_ChonBacsi", paramInput, "Gán bác sỹ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 600, 230);
		DlgUtil.open("divDlgBacSyChiDinh");
	});
	EventUtil.setEvent("assignSevice_saveChangeBSDT", function(e) {
		DlgUtil.showMsg(e.msg);
		$('#labelBACSIDIEUTRIMS').html(e.name);
		$("#labelHidBACSYKE").val(e.user_id);
		DlgUtil.close(e.divId);
	});
	//xu ly su kien click tren toolbar
	function processToolBar() {
		// click button chuyen benh nhan vao phong, giuong trên toolbar
		$("#toolbarIdhandling_1").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					paramInput = {
						benhnhanid : $("#hidBENHNHANID").val(),
						khambenhid : $("#hidKHAMBENHID").val(),
						tiepnhanid : $("#hidTIEPNHANID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val(),
						khoaid : opt._deptId,
						type : 1
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgCBNVaoBuongPhong", "divDlg", "manager.jsp?func=../noitru/NTU02D017_ChuyenBenhNhanVaoBuongPhong", paramInput, "Chuyển phòng (buồng) giường " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
							600, 230);
					DlgUtil.open("divDlgCBNVaoBuongPhong");
				});
		//click button chuyen benh nhan vao phong khong chuyen giuong
		$("#toolbarIdhandling_2").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					paramInput = {
						benhnhanid : $("#hidBENHNHANID").val(),
						khambenhid : $("#hidKHAMBENHID").val(),
						tiepnhanid : $("#hidTIEPNHANID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val(),
						khoaid : opt._deptId
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgCBNVaoPhong", "divDlg", "manager.jsp?func=../noitru/NTU02D018_ChuyenBenhNhanVaoBuongPhongKhongGiuong", paramInput,
							"Chuyển phòng (không chuyển giường) " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 600, 150);
					DlgUtil.open("divDlgCBNVaoPhong");
				});
		if (type == '0') {
			//click button nhap benh an tong chung
			//HaNv_051222: Thao tác menu trên mobie - L2PT-29886
			//$("#toolbarIdban_1").on("click", function() {
			$("#toolbarIdban_1").hammer().bind("tap", function() {
				if (!checkBeforeClickOnMenu())
					return;
				paramInput = {
					khambenh_id : $("#hidKHAMBENHID").val(),
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					loaibenhanid : $("#hidLOAIBENHANID").val(),
					namsinh : $("#hidNAMSINH").val()
				};
				//L2PT-29547
				var _sql_par1 = [];
				var _loaibadaingay = null;
				var _loaibenhanid = null;
				var _hosobenhanid = null;
				if (_loaibadaingay != null && _loaibadaingay == 36) {
					_sql_par1 = RSUtil.buildParam("", [ 36 ]);
					_hosobenhanid = dataDaingay.HOSOBENHANID;
					_loaibenhanid = _loaibadaingay;
				} else {
					_sql_par1 = RSUtil.buildParam("", [ $("#hidLOAIBENHANID").val() ]);
					_hosobenhanid = $("#hidHOSOBENHANID").val();
					_loaibenhanid = $("#hidLOAIBENHANID").val();
				}
				var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
				var _rows1 = JSON.parse(_data1);
				var _tenloaibenhan = _rows1[0].TENLOAIBENHAN

				dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU02D001_TaoBenhAn", paramInput, "Tạo "+ _tenloaibenhan + " " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 500);
				DlgUtil.open("divDlgBenhAn");
			});
		} else if (type == '3') {
			// click button khám trên toolbar
			//HaNv_051222: Thao tác menu trên mobie - L2PT-29886
			//$("#toolbarIdban_1").on("click", function() {
			$("#toolbarIdban_1").hammer().bind("tap", function() {
				if (!checkBeforeClickOnMenu())
					return;
				paramInput = {
					khambenh_id : $("#hidKHAMBENHID").val(),
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					loaibenhanid : $("#hidLOAIBENHANID").val(),
					// L2PT-76863 start
					loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
					trangthaikhambenh : $("#hidTRANGTHAIKHAMBENH").val(),
					NTU_INBA_HSDBA : cf.NTU_INBA_HSDBA
					// L2PT-76863 end
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU02D001_TaoBenhAn", paramInput, "KHÁM BỆNH HỎI BỆNH " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 500);
				DlgUtil.open("divDlgBenhAn");
//				var myVar={
//						khambenhId : $("#hidKHAMBENHID").val(),
//						benhnhanId : $("#hidBENHNHANID").val(),
//						tiepnhanId : $("#hidTIEPNHANID").val(),
//						hosobenhanId : $("#hidHOSOBENHANID").val(),
//						loaibenhanId : $("#hidLOAIBENHANID").val()
//				};
//
//				dlgPopup=DlgUtil.buildPopupUrl("dlgKham","divDlg","manager.jsp?func=../ngoaitru/NGT02K002_KhamBenhHoiBenh",myVar,"KHÁM BỆNH HỎI BỆNH",1200,600);
//				dlgPopup.open();
//				DlgUtil.open("dlgKham");
			});
		}
		//click button nhap benh an chi tiet
		//HaNv_051222: Thao tác menu trên mobie - L2PT-29886
		//$("#toolbarIdban_2").on("click", function() {
		$("#toolbarIdban_2").hammer().bind("tap", function() {
			if (!checkBeforeClickOnMenu())
				return;
			//kiem tra dai ngay
			var _loaibadaingay = null;
			var _loaibenhanid = null;
			var _hosobenhanid = null;
			// var _par=[];
			// _par=RSUtil.buildParam("",[ $("#hidHOSOBENHANID").val()]);
			// var dataDaingay=jsonrpc.AjaxJson.ajaxExecuteQueryO("BAN.CHECK_DAINGAY", _par);
			// _rowsDaingay= JSON.parse(dataDaingay);
			// if(_rowsDaingay!=null && _rowsDaingay.length>0){
			//    _loaibadaingay=_rowsDaingay[0].LOAIBENHANID;
			// }
			var object = {};
			FormUtil.setFormToObject('divContentHC', '', object);
			var dataDaingays = jsonrpc.AjaxJson.ajaxCALL_SP_O("BAN.CHECK_DAINGAY", JSON.stringify(object));
			// if (!dataDaingay) {
			// 	DlgUtil.showMsg("Bệnh nhân chưa có bệnh án dài ngày!");
			// 	return;
			// }
			// _hosobenhanid = dataDaingay.HOSOBENHANID;
			var dataDaingay = {};
			if (dataDaingays && dataDaingays.length > 0) {
				dataDaingay = dataDaingays[0];
				_loaibadaingay = dataDaingay.LOAIBENHANID;
			}
			var _sql_par1 = [];
			if (_loaibadaingay != null && _loaibadaingay == 36) {
				_sql_par1 = RSUtil.buildParam("", [ 36 ]);
				_hosobenhanid = dataDaingay.HOSOBENHANID;
				_loaibenhanid = _loaibadaingay;
			} else {
				_sql_par1 = RSUtil.buildParam("", [ $("#hidLOAIBENHANID").val() ]);
				_hosobenhanid = $("#hidHOSOBENHANID").val();
				_loaibenhanid = $("#hidLOAIBENHANID").val();
			}
			var _data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.021.LOAI.BA", _sql_par1);
			var _rows1 = JSON.parse(_data1);
			var _sreenName = _rows1[0].URL;
			var _tenloaibenhan = _rows1[0].TENLOAIBENHAN
			var _maloaibenhan = _rows1[0].MALOAIBENHAN;
			// L2PT-76863: truyen tham so loaitiepnhanid
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : _hosobenhanid,
				benhnhanid : $("#hidBENHNHANID").val(),
				loaibenhanid : _loaibenhanid,
				maloaibenhan : _maloaibenhan,
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				trangthaikhambenh : $("#hidTRANGTHAIKHAMBENH").val(),
				NTU_INBA_HSDBA : cf.NTU_INBA_HSDBA
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgBenhAnDetail", "divDlg", "manager.jsp?func=../benhan/" + _sreenName, paramInput, "Cập nhật " + _tenloaibenhan + " " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 700);
			DlgUtil.open("divDlgBenhAnDetail");//
		});
		//click tab nhap thong tin so ket dieu tri
		//HaNv_051222: Thao tác menu trên mobie - L2PT-29886
		//$("#toolbarIdban_3").on("click", function() {
		$("#toolbarIdban_3").hammer().bind("tap", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			// L2PT-76863: truyen tham so loaitiepnhanid
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				bacsydieutriid : rowData.BACSYDIEUTRIID,
				benhnhanid : rowData.BENHNHANID,
				thoigianvaovien : $("#hidNGAYTIEPNHAN").val(),//START -- L2K74TW-663 -- hongdq
				tiepnhanid : rowData.TIEPNHANID,
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				trangthaikhambenh : $("#hidTRANGTHAIKHAMBENH").val(),
				NTU_INBA_HSDBA : cf.NTU_INBA_HSDBA
			//L2PT-2509
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgSoKetDieuTri", "divDlg", "manager.jsp?func=../noitru/NTU02D055_SoKetDieuTri", paramInput, "Sơ kết điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
			DlgUtil.open("divDlgSoKetDieuTri");//
		});
		//START L2PT-52392
		$("#toolbarIdban_QLHIV").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				bacsydieutriid : rowData.BACSYDIEUTRIID,
				benhnhanid : rowData.BENHNHANID,
				thoigianvaovien : $("#hidNGAYTIEPNHAN").val(),
				tiepnhanid : rowData.TIEPNHANID,
				mabenhnhan: rowData.MABENHNHAN,
				tenbenhnhan: rowData.TENBENHNHAN,
				cccd: cccd,
				ma_bhyt: ma_bhyt
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgHIV", "divDlg", "manager.jsp?func=../noitru/NTU02D228_QLBN_HIV", paramInput, "QL bệnh nhân HIV " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgHIV");
		});
		//L2PT-52392 end
		$("#toolbarIdban_QLLAO").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				bacsydieutriid : rowData.BACSYDIEUTRIID,
				benhnhanid : rowData.BENHNHANID,
				thoigianvaovien : $("#hidNGAYTIEPNHAN").val(),
				tiepnhanid : rowData.TIEPNHANID,
				mabenhnhan: rowData.MABENHNHAN,
				tenbenhnhan: rowData.TENBENHNHAN
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgHIV", "divDlg", "manager.jsp?func=../noitru/NTU02D232_QLBN_LAO", paramInput, "QL bệnh nhân LAO " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgHIV");
		});
		//L2PT-8575
		$("#toolbarIdbtnSKDT").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				bacsydieutriid : rowData.BACSYDIEUTRIID,
				benhnhanid : rowData.BENHNHANID,
				thoigianvaovien : $("#hidNGAYTIEPNHAN").val(),//START -- L2K74TW-663 -- hongdq
				tiepnhanid : rowData.TIEPNHANID
			//L2PT-2509
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgSoKetDieuTri", "divDlg", "manager.jsp?func=../noitru/NTU02D055_SoKetDieuTri", paramInput, "Sơ kết điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
			DlgUtil.open("divDlgSoKetDieuTri");//
		});
		//Doanpv_20200727 L2PT-25202 START
		//HaNv_051222: Thao tác menu trên mobie - L2PT-29886
		//$("#toolbarIdban_4").on("click", function() {
		$("#toolbarIdban_4").hammer().bind("tap", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			// L2PT-76863: truyen tham so loaitiepnhanid
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				benhnhanid : rowData.BENHNHANID,
				hosobenhanid : rowData.HOSOBENHANID,
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				trangthaikhambenh : $("#hidTRANGTHAIKHAMBENH").val(),
				NTU_INBA_HSDBA : cf.NTU_INBA_HSDBA
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTomTatBA", "divDlg", "manager.jsp?func=../noitru/NTU02D0113_DLG_TONGKETBENHAN", paramInput, "Tóm tắt bệnh án " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 650);
			DlgUtil.open("divDlgTomTatBA");//
		});
		//Doanpv_20200727 L2PT-25202 END
		$("#toolbarIdtreat_2").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				maubenhpham_id : "",
				khambenh_id : $("#hidKHAMBENHID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgChamSoc", "divDlg", "manager.jsp?func=../noitru/NTU02D004_PhieuChamSoc", paramInput, "Tạo phiếu chăm sóc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 750, 330);
			DlgUtil.open("divDlgChamSoc");
		});
		//tao phieu chi dinh dich vu
		$("#toolbarIdbtnService").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					// start jira L2PT-9615
					// neu chua xep giuong thi khong cho chi dinh dich vu, thuoc - HaNv_251022: L2PT-27502
					var cauHinhXepGiuong = getCauHinh('NTU_CHIDINHDVKHIXEPGIUONG');
					if (cauHinhXepGiuong != 0) {
						if (isChuaXepGiuong($("#hidKHAMBENHID").val())) {
							if (cauHinhXepGiuong == '1') {
								DlgUtil.showMsg("Bệnh nhân chưa xếp giường!");
							} else if (cauHinhXepGiuong == '2') {
								DlgUtil.showMsg("Bệnh nhân chưa xếp giường!");
								return;
							} else if (cauHinhXepGiuong == '3' && $("#hidLOAITIEPNHANID").val() == '0') {
								DlgUtil.showMsg("Bệnh nhân chưa xếp giường!");
							} else if (cauHinhXepGiuong == '4' && $("#hidLOAITIEPNHANID").val() == '0') {
								DlgUtil.showMsg("Bệnh nhân chưa xếp giường!");
								return;
							}
						}
					}
					// end jira L2PT-9615
					if (!checkDoiTuongBN())
						return;
					paramInput = {
						benhnhanid : $("#hidBENHNHANID").val(),
						mabenhnhan : $("#hidMABENHNHAN").val(),
						khambenhid : $("#hidKHAMBENHID").val(),
						tiepnhanid : $("#hidTIEPNHANID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val(),
						doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
						loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
						subDeptId : $("#hidPHONGID").val(),
						bacsike : $("#labelHidBACSYKE").val()
					};
					//Begin_HaNv_30112020: Xây dựng giao diện chỉ định DV mới cho DLKHA - L2PT-30889
					if (cddvDLKHA) {
						dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV_KHA" + "&loaidichvu=" + 5, paramInput,
								"Tạo phiếu chỉ định dịch vụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 600);
					} else {
						dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, paramInput, "Tạo phiếu chỉ định dịch vụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
								_width, _height);
					}
					//End_HaNv_30112020
					DlgUtil.open("divDlgDichVu");
				});
		//tao phieu chi dinh dich vu
		$("#toolbarIdfunction_8").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			//Begin_HaNv_09052018: Chan truoc khi mo form
			var _trangthaikhambenh = $("#hidTRANGTHAIKHAMBENH").val();
			if (_trangthaikhambenh != '4') {
				DlgUtil.showMsg("Bệnh nhân không ở trạng thái đang điều trị, không thể chỉ định dịch vụ!");
				return false;
			}
			if (!checkDoiTuongBN())
				return;
			paramInput = {
				benhnhanid : $("#hidBENHNHANID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $("#hidPHONGID").val(),
				modekhoa : '1',
				bacsike : $("#labelHidBACSYKE").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, paramInput, "Tạo phiếu chỉ định dịch vụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgDichVu");
		});
		//Begin_HaNv_08052018: them menu tra loi tu van dinh duong
		$("#toolbarIdfunction_12").on("click", function() {
			paramInput = {
				khambenhid : '',
				type : 0
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgHoiChanDD", "divDlg", "manager.jsp?func=../noitru/NTU02D089_HoiChanDinhDuong", paramInput, "Trả lời tư vấn dinh dưỡng " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1330, 650);
			DlgUtil.open("divDlgHoiChanDD");
		});
		//End_HaNv_08052018
		//Begin_HaNv_08052018: them menu tra loi tu van dinh duong
		$("#toolbarIdfunction_13").on("click", function() {
			paramInput = {
				mode : '1'
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDDCLS", "divDlg", "manager.jsp?func=../noitru/NTU02D091_TonghopDuocLamsang", paramInput, "Phiếu duyệt dược CLS " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 600);
			DlgUtil.open("divDlgDDCLS");
		});
		//End_HaNv_08052018
		//START L2HOTRO-12568
		$("#toolbarIdfunction_14").on("click", function() {
			var param = {
				khambenhid : $('#hidKHAMBENHID').val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K058_Thongtin_nghiduong", param, 'Thông tin nghỉ hưởng BHXH ' + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("dlgXuTri");
		});
		//END L2HOTRO-12568
		//START L2PT-4645
		$("#toolbarIdfunction_15").on("click", function() {
			var hosobenhanid = $("#hidHOSOBENHANID").val();
			if (hosobenhanid == '') {
				DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				checkBieuDoChuyenDa();
				window.open('manager.jsp?func=../noitru/NTU02D113_BieuDoChuyenDa&showMode=dlg', hosobenhanid, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			}
		});
		//HungND - L2PT-62162
		$('#toolbarIdprint_12b').on("click", function () {
			if($("#hidMAUBENHPHAMID").val() == '' || $("#hidMAUBENHPHAMID").val() == null) {
				DlgUtil.showMsg("Chưa chọn dịch vụ!");
				return;
			}
			if($("#hidDICHVUKHAMBENHID").val() == '' || $("#hidDICHVUKHAMBENHID").val() == null) {
				DlgUtil.showMsg("Chưa chọn chi tiết dịch vụ!");
				return;
			}
			var myVar = {
				hosobenhanId: $("#hidHOSOBENHANID").val(),
				tiepnhanId: $("#hidTIEPNHANID").val(),
				khambenhId: $("#hidKHAMBENHID").val(),
				maubenhphamId: $("#hidMAUBENHPHAMID").val(),
				dichvukhambenhId: $("#hidDICHVUKHAMBENHID").val(),
				tenbenhnhan : $("#hidTENBENHNHAN").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				mabenhan : $("#hidMABENHAN").val()
			};

			dlgPopup = DlgUtil.buildPopupUrl("dlgTomTatMo", "divDlg", "manager.jsp?func=../noitru/NTU02D132_DuyetMo_SNVPC", myVar, "Phiếu tóm tắt duyệt mổ", 1300, 600);
			DlgUtil.open("dlgTomTatMo");
		});
		//HungND - L2PT-62162 END
		function checkBieuDoChuyenDa() {
			var ttBenhVien = getTTBenhVien();
			var obj = {
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tenbenhnhan : $("#hidTENBENHNHAN").val(),
				tuoi : getTuoiBdcd(),
				sovaovien : $('#lblSOVAOVIEN').text(),
				ngayvao : getNgayVaoBdcd(),
				khoa : opt._dept_name,
				tenBenhVien : ttBenhVien.TENBENHVIEN,
				soyte : ttBenhVien.TENSOYTE
			}
			if (opt.hospital_id == 993) {
				obj.ngayvao = "";
			}
			var _par = JSON.stringify(obj);
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D113.03", _par);
			var data = $.parseJSON(result);
			if (data != '-1') {
				console.log('-- checkBieuDoChuyenDa success');
			} else {
				console.log('-- checkBieuDoChuyenDa fail');
			}
		}
		function getTTBenhVien() {
			var ttBenhVien = {
				TENBENHVIEN : "Bệnh viện ......................",
				TENSOYTE : "Sở y tế ......................"
			};
			var obj = new Object();
			obj.hospital_id = opt.hospital_id;
			var _par = JSON.stringify(obj);
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D113.04", _par);
			if (result.length > 0) {
				ttBenhVien = result[0];
			}
			return ttBenhVien;
		}
		function getBuong() {
			return opt._subdept_name;
		}
		function getGiuong() {
			return $('#lblGIUONG').text();
		}
		function getTuoiBdcd() {
			var namsinh = $('#hidNAMSINH').val();
			if (namsinh !== '') {
				var today = new Date();
				var year = today.getFullYear();
				return (year - namsinh) + '';
			} else {
				return "";
			}
		}
		function getNgayVaoBdcd() {
			var thoigianvaovien = $('#hidTHOIGIANVAOVIEN').val();
			if (thoigianvaovien.length > 9) {
				return thoigianvaovien.substring(0, 10);
			} else {
				return "";
			}
		}
		//END L2PT-4645
		//START L2PT-7181
		$("#toolbarIdfunction_17").on(
				"click",
				function() {
					var hosobenhanid = $("#hidHOSOBENHANID").val();
					var khambenhid = $("#hidKHAMBENHID").val();
					if (hosobenhanid == '') {
						DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
					} else {
						var idGayMeHoiSuc = checkPhieuGayMeHoiSuc(); // dua vao hosobenhanid,kiem tra xem co phieu gay me hoi suc tuong ung chua, neu chua co thi insert, tra ve idGayMeHoiSuc
						var name = hosobenhanid + '@' + idGayMeHoiSuc + '@' + khambenhid;
						// L2PT-30319 duonghn: thêm cấu hình sử dụng form GMHS
						if (opt.hospital_id == 10284 || jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NTU_CHON_FORM_GMHS') == '1') {
							var popup = window.open('manager.jsp?func=../noitru/NTU02D125_PhieuGayMeHoiSuc_BDHN&showMode=dlg', name,
									'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' + screen.height + ',width=' + screen.width);
							popup.moveTo(0, 0);
						 } else {
						 	window.open('manager.jsp?func=../noitru/NTU02D125_PhieuGayMeHoiSuc&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
						 }
					}
				});
		$("#toolbarIdfunction_18").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			EventUtil.setEvent("cancelKhamSinhSan", function() {
				DlgUtil.close("dlgKhamSinhSan");
			});
			var myVar = {
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				khambenhId : $("#hidKHAMBENHID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgKhamSinhSan", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K011_KhamSinhSan", myVar, "Khám sinh sản " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 880, 650);
			DlgUtil.open("dlgKhamSinhSan");
		});
		//START Doanpv_2020/07/23 L2PT-25026
		$("#toolbarIdfunction_19").on("click", function() {
			var _hosobenhanid = $("#hidHOSOBENHANID").val();
			if (_hosobenhanid != null && _hosobenhanid != -1) {
				var paramInput = {
					hosobenhanid : _hosobenhanid,
					khambenhid : $("#hidKHAMBENHID").val()
				};
				var url = "manager.jsp?func=../noitru/NTU02D157_NghiDuongThai";
				var popup = DlgUtil.buildPopupUrl("divDlgNDT", "divDlg", url, paramInput, "Phiếu nghỉ dưỡng thai " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
				popup.open("divDlgNDT");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//END Doanpv_2020/07/23 L2PT-25026
		//START Doanpv_20200805 L2PT-25513
		$("#toolbarIdfunction_20").on("click", function() {
			var _trangthaitiepnhan = $('#hidTRANGTHAITIEPNHAN').val();
			var _tiepnhanid = $("#hidTIEPNHANID").val();
			if (_tiepnhanid != null && _tiepnhanid != -1) {
				if (_trangthaitiepnhan == '2') {
					DlgUtil.showMsg('Vui lòng hủy duyệt bảo hiểm để cập nhật thông tin hành chính cho bệnh nhân');
					return;
				}
				var paramInput = {
					tiepnhanid : _tiepnhanid
				};
				var url = "manager.jsp?func=../noitru/NTU01H020_ThongTinBenhNhan";
				dlgPopup = DlgUtil.buildPopupUrl("divDlgSuaBenhNhan", "divDlg", "manager.jsp?func=../noitru/NTU01H020_ThongTinBenhNhan", paramInput, "HIS - Cập nhật bệnh nhân " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 580);
				DlgUtil.open("divDlgSuaBenhNhan");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//END Doanpv_20200805 L2PT-25513
		//START Doanpv_20201019 L2PT-29241
		$("#toolbarIdfunction_21").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			var paramInput = {
				mabenhnhan : $("#hidMABENHNHAN").val(),
				tenbenhnhan : $("#hidTENBENHNHAN").val(),
				nghenghiep : rowData.TENNGHENGHIEP,
				diachi : rowData.DIACHI,
				namsinh : rowData.NAMSINH,
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
				chandoan : rowData.CHANDOANRAVIEN,
				ngaytiepnhan : $("#hidTHOIGIANVAOVIEN").val(),
				capnhat : '1',
				hosobenhanid : $('#hidHOSOBENHANID').val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K009_Chuyenvien", paramInput, "Thông tin chuyển viện " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
			DlgUtil.open("dlgXuTri");
		});
		//END Doanpv_20200805 L2PT-25513
		function checkPhieuGayMeHoiSuc() {
			var ttBenhVien = getTTBenhVien();
			var ttKhac = getTTKhacGmhs();
			var obj = {
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				soyte : ttBenhVien.TENSOYTE,
				tendv : ttBenhVien.TENBENHVIEN,
				khoa : opt._dept_name,
				svv : $('#lblSOVAOVIEN').text(),
				ten : $("#hidTENBENHNHAN").val(),
				tuoi : getTuoiBdcd(),
				gioitinh : $('#hidGIOITINH').val() == "Nữ" ? "0" : "1",
				buong : getBuong(),
				giuong : getGiuong(),
				cannang : ttKhac.CANNANG,
				chieucao : ttKhac.CHIEUCAO,
				nhommau : ttKhac.NHOMMAU,
				ppvocam : "",
				bsphauthuat : "",
				ngaygayme : "",
				bsgayme : "",
				tuthe : "",
				tienme : "",
				tacdung : "",
				chandoan : ttKhac.CHANDOAN
			}
			var _par = JSON.stringify(obj);
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D125.01", _par);
			var data = $.parseJSON(result);
			if (data != '-1') {
				console.log('-- checkPhieuGayMeHoiSuc success');
			} else {
				console.log('-- checkPhieuGayMeHoiSuc fail');
			}
			return data; // r_id_pgmhs
		}
		function getTTKhacGmhs() {
			var ttKhac = {
				CANNANG : "",
				CHIEUCAO : "",
				CHANDOAN : "",
				NHOMMAU : ""
			};
			var obj = new Object();
			obj.khambenhid = $("#hidKHAMBENHID").val();
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.08", JSON.stringify(obj));
			if (result.length > 0) {
				ttKhac = result[0];
			}
			return ttKhac;
		}
		//END L2PT-7181
		// L2PT-83705 start merge upcode
		// START L1PT-916
		$("#toolbarIdfunction_16").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgThongTinDiUng", "divDlg", "manager.jsp?func=../noitru/NTU02D114_ThongTinDiUng", paramInput, "Thông tin dị ứng " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 250);
			dlgPopup.open();
		});
		// END L1PT-916
		// L2PT-83705 end
		// START L2PT-16200
		$("#toolbarIdfunction_18").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			EventUtil.setEvent("cancelKhamSinhSan", function() {
				DlgUtil.close("dlgKhamSinhSan");
			});
			var myVar = {
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				khambenhId : $("#hidKHAMBENHID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgKhamSinhSan", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K011_KhamSinhSan", myVar, "Khám sinh sản " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 880, 650);
			DlgUtil.open("dlgKhamSinhSan");
		});
		// END L2PT-16200
		//START Doanpv_2020/07/23 L2PT-25026
		$("#toolbarIdfunction_19").on("click", function() {
			var _hosobenhanid = $("#hidHOSOBENHANID").val();
			if (_hosobenhanid != null && _hosobenhanid != -1) {
				var paramInput = {
					hosobenhanid : _hosobenhanid,
					khambenhid : $("#hidKHAMBENHID").val()
				};
				var url = "manager.jsp?func=../noitru/NTU02D157_NghiDuongThai";
				var popup = DlgUtil.buildPopupUrl("divDlgNDT", "divDlg", url, paramInput, "Phiếu nghỉ dưỡng thai " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
				popup.open("divDlgNDT");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//END Doanpv_2020/07/23 L2PT-25026
		//START Doanpv_20200805 L2PT-25513
		$("#toolbarIdfunction_20").on("click", function() {
			var _trangthaitiepnhan = $('#hidTRANGTHAITIEPNHAN').val();
			var _tiepnhanid = $("#hidTIEPNHANID").val();
			if (_tiepnhanid != null && _tiepnhanid != -1) {
				if (_trangthaitiepnhan == '2') {
					DlgUtil.showMsg('Vui lòng hủy duyệt bảo hiểm để cập nhật thông tin hành chính cho bệnh nhân');
					return;
				}
				var paramInput = {
					tiepnhanid : _tiepnhanid
				};
				var url = "manager.jsp?func=../noitru/NTU01H020_ThongTinBenhNhan";
				dlgPopup = DlgUtil.buildPopupUrl("divDlgSuaBenhNhan", "divDlg", "manager.jsp?func=../noitru/NTU01H020_ThongTinBenhNhan", paramInput, "HIS - Cập nhật bệnh nhân " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 580);
				DlgUtil.open("divDlgSuaBenhNhan");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//END Doanpv_20200805 L2PT-25513
		//START Doanpv_20201019 L2PT-29241
		$("#toolbarIdfunction_21").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			if (rowData.HINHTHUCRAVIENID != '6' && opt.hospital_id == '10284') {
				DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, tạo phiếu chuyển viện chỉ với những bệnh nhân chuyển viện');
				return;
			} else {
				var paramInput = {
						mabenhnhan : $("#hidMABENHNHAN").val(),
						tenbenhnhan : $("#hidTENBENHNHAN").val(),
						nghenghiep : rowData.TENNGHENGHIEP,
						diachi : rowData.DIACHI,
						namsinh : rowData.NAMSINH,
						khambenhid : $('#hidKHAMBENHID').val(),
						benhnhanid : $('#hidBENHNHANID').val(),
						chandoan : rowData.CHANDOANRAVIEN,
						ngaytiepnhan : $("#hidTHOIGIANVAOVIEN").val(),
						capnhat : '1',
						hosobenhanid : $('#hidHOSOBENHANID').val()
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K009_Chuyenvien", paramInput, "Thông tin chuyển viện " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
					DlgUtil.open("dlgXuTri");
			}
		});
		//END Doanpv_20200805 L2PT-25513
		//START tuyendv
		$("#toolbarIdfunction_22").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			var paramInput = {
				mabenhnhan : $("#hidMABENHNHAN").val(),
				tenbenhnhan : $("#hidTENBENHNHAN").val(),
				nghenghiep : rowData.TENNGHENGHIEP,
				diachi : rowData.DIACHI,
				namsinh : rowData.NAMSINH,
				khambenhid : $('#hidKHAMBENHID').val(),
				benhnhanid : $('#hidBENHNHANID').val(),
				chandoan : rowData.CHANDOANRAVIEN,
				ngaytiepnhan : $("#hidTHOIGIANVAOVIEN").val(),
				capnhat : '1',
				hosobenhanid : $('#hidHOSOBENHANID').val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgSuaTyLeThe", "divDlg", "manager.jsp?func=../noitru/NTU02D167_SuaTyLeThe", paramInput, "Thông tin tỷ lệ thẻ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
			DlgUtil.open("dlgSuaTyLeThe");
		});
		//END tuyendv
		//START tuyendv BVTM-1413
		$("#toolbarIdfunction_25").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					var paramInput = {
						mabenhnhan : $("#hidMABENHNHAN").val(),
						tenbenhnhan : $("#hidTENBENHNHAN").val(),
						nghenghiep : rowData.TENNGHENGHIEP,
						diachi : rowData.DIACHI,
						namsinh : rowData.NAMSINH,
						khambenhid : $('#hidKHAMBENHID').val(),
						benhnhanid : $('#hidBENHNHANID').val(),
						chandoan : rowData.CHANDOANRAVIEN,
						ngaytiepnhan : $("#hidTHOIGIANVAOVIEN").val(),
						capnhat : '1',
						hosobenhanid : $('#hidHOSOBENHANID').val()
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgYCSuDungKhangSinh", "divDlg", "manager.jsp?func=../noitru/NTU02D185_YCSuDungKhangSinh", paramInput,
							"Phiếu yêu cầu sử dụng kháng sinh ưu tiên quản lý" + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 600);
					DlgUtil.open("dlgYCSuDungKhangSinh");
				});
		//END tuyendv BVTM-1413
		//START DoanPV_20210426 BVTM-996
		$("#toolbarIdfunction_26").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			CommonUtil.openReportGet('window', "REPORT_PHIEU_XACNHAN_HIV", "pdf", par);
		});
		//END DoanPV_20210426 BVTM-996
		// start jira 2113
		// nhap thong tin benh an
		$("#toolbarIdfunction_27").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var khambenhid = $("#hidKHAMBENHID").val();
					var chucNang = 'NTTBA';
					var name = chucNang + '@' + khambenhid;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
							screen.height + ',width=' + screen.width);
					popup.moveTo(0, 0);
					popup.onbeforeunload = function() {
						loadGrid();
					}
				});
		// end jira 2113
		// start jira BVTM-5517
		// them goi kham chung
		$("#toolbarIdfunction_28").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var myVar = {
				mabenhnhan : $("#hidMABENHNHAN").val(),
				tenbenhnhan : $("#hidTENBENHNHAN").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
			};
			DlgUtil.buildPopupUrl("NTU02D192_GoikhamBenhnhan", "NTU02D192_GoikhamBenhnhan", "manager.jsp?func=../noitru/NTU02D192_GoikhamBenhnhan", myVar, "Chỉ định gói KCB " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 400);
			DlgUtil.open("NTU02D192_GoikhamBenhnhan");
		});
		// end jira 2113
		$("#toolbarIdfunction_30").on("click", function() {
			if (!kegiuongDs) {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (rowData.TRANGTHAIKHAMBENH != 4) {
					DlgUtil.showMsg('Bệnh nhân không ở trạng thái đang điều trị');
					return;
				}
				if (selRowId != null && selRowId > 0) {
					if (!checkDoiTuongBN())
						return;
					paramInput = {
						chidinhdichvu : '1',
						loaidichvu : '13',
						loaiphieumbp : '12',
						benhnhanid : $("#hidBENHNHANID").val(),
						khambenhid : $("#hidKHAMBENHID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val(),
						tiepnhanid : $("#hidTIEPNHANID").val(),
						doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
						hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
						loaibenhanid : $("#hidLOAIBENHANID").val(),
						loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
						subDeptId : $('#hidPHONGID').val()
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 13, paramInput, "Phiếu ngày giường " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
					DlgUtil.open("divDlgDichVu");
				} else {
					DlgUtil.showMsg('Chưa chọn bệnh nhân');
				}
			} else {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (rowData.TRANGTHAIKHAMBENH != 4) {
					DlgUtil.showMsg('Bệnh nhân không ở trạng thái đang điều trị');
					return;
				}
				paramInput = {
					khambenhid : $("#hidKHAMBENHID").val(),
					tiepnhanid : $("#hidTIEPNHANID").val()
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVuGiuong", "divDlg", "manager.jsp?func=../noitru/NTU01H034_BenhNhanGiuong", paramInput, "Ngày giường " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 600);
				DlgUtil.open("divDlgDichVuGiuong");
			}
		});

		$("#toolbarIdfunction_31").on("click", function() {
			if (!checkDoiTuongBN())
				return;
			paramInput = {
				chidinhdichvu : '1',
				loaidichvu : '1',
				loaiphieumbp : '17',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
				loaibenhanid : $("#hidLOAIBENHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $('#hidPHONGID').val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 1, paramInput, "Phiếu thu khác " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgDichVu");
		});
		
		// L2PT-21249 ttlinh start
		$("#toolbarIdfunction_32").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				mabenhnhan : rowData.MABENHNHAN,
				tenbenhnhan : rowData.TENBENHNHAN,
				nghenghiep : rowData.TENNGHENGHIEP,
				diachi : rowData.DIACHI,
				namsinh : rowData.NAMSINH,
				khambenhid : rowData.KHAMBENHID,
				benhnhanid : rowData.BENHNHANID,
				chandoan : rowData.CHANDOANRAVIEN,
				ngaytiepnhan : rowData.NGAYTIEPNHAN,
				capnhat : '1',
				hosobenhanid : rowData.HOSOBENHANID
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K010_Tuvong", paramInput, "Thông tin tử vong " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1050, 575);
			DlgUtil.open("dlgXuTri");
		});
		// L2PT-21249 end
		
		// L2PT-21249 ttlinh start
		$("#toolbarIdfunction_33").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			var paramInput = {
				khambenhid: $("#hidKHAMBENHID").val(),
				tiepnhanid: $("#hidTIEPNHANID").val(),
				hosobenhanid: $("#hidHOSOBENHANID").val(),
				mabenhan: $("#hidMABENHNHAN").val(),
				tenbenhnhan: rowData.TENBENHNHAN,
				tuoi: rowData.TUOI,
				diachi: rowData.DIACHI
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../noitru/NTU01H101_DayLaiBenhAn", paramInput, "HIS - Quản lý bệnh án " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgBA");
		});
		$("#toolbarIdprint_pkcdphcn").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					var khambenhid = rowData.KHAMBENHID;
					var chucNang = 'PKCDPHCN';
					var mode = 1;
					var name = chucNang + '@' + khambenhid + '@' + mode;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=800,width=1450');
					popup.moveTo(0, 0);
					popup.onbeforeunload = function() {
						loadGrid();
					}
				});
		// cong van 3730 - phieu kham chi dinh phuc hoi chuc nang
		$("#toolbarIdtreatdt_pkcdphcn").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					var khambenhid = rowData.KHAMBENHID;
					var chucNang = 'PKCDPHCN';
					var name = chucNang + '@' + khambenhid;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=800,width=1450');
					popup.moveTo(0, 0);
					popup.onbeforeunload = function() {
						loadGrid();
					}
				});
		// cong van 3730 - phieu luong gia hoat dong chuc nang va tham gia
		$("#toolbarIdtreatdt_plghdcntg").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					var khambenhid = rowData.KHAMBENHID;
					var chucNang = 'PLGHDCNTG';
					var name = chucNang + '@' + khambenhid;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=800,width=1450');
					popup.moveTo(0, 0);
					popup.onbeforeunload = function() {
						loadGrid();
					}
				});
		// start jira 3371
		// bo sung chuc nang tao phieu duyet mo
		$("#toolbarIdtreat_tpdm").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var khambenhid = $("#hidKHAMBENHID").val();
					var chucNang = 'PDM';
					var name = chucNang + '@' + khambenhid;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
							screen.height + ',width=' + screen.width);
					popup.moveTo(0, 0);
					popup.onbeforeunload = function() {
						loadGrid();
					}
				});
		// end jira 3371
		//tao phieu suat an
		$("#toolbarIdtreat_3").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			if (!checkDoiTuongBN())
				return;
			paramInput = {
				chidinhdichvu : '1',
				loaidichvu : '12',
				loaiphieumbp : LNMBP_Phieusuatan.toString(),
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
				loaibenhanid : $("#hidLOAIBENHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $("#hidPHONGID").val(),
				bacsike : $("#labelHidBACSYKE").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 12, paramInput, "Phiếu suất ăn " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgDichVu");
		});
		//tao bien ban hoi chan
		$("#toolbarIdtreat_6").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenh_id : $("#hidKHAMBENHID").val(),
				maubenhpham_id : "",
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgHoiChan", "divDlg", "manager.jsp?func=../noitru/NTU02D008_BienBanHoiChuan", paramInput, "Tạo biên bản hội chẩn " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 550);
			DlgUtil.open("divDlgHoiChan");
		});
		//tao bien ban hoi chan lao kt
		$("#toolbarIdtreat_laokt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenh_id : $("#hidKHAMBENHID").val(),
				maubenhpham_id : "",
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgHoiChan", "divDlg", "manager.jsp?func=../noitru/NTU02D008_BienBanHoiLaoKT", paramInput, "Tạo biên bản hội chẩn lao kháng thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 550);
			DlgUtil.open("divDlgHoiChan");
		});
		//L2PT-2578
		$("#toolbarIdtreat_6_1").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				mabenhan : $("#hidMABENHAN").val(),
				tenbenhnhan : $("#hidTENBENHNHAN").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				maubenhpham_id : ""
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgHoiChan", "divDlg", "manager.jsp?func=../noitru/NTU02D189_BB_Hoichan_PT", paramInput, "Biên bản hội chẩn trước PT " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 600);
			DlgUtil.open("divDlgHoiChan");
		});
		//tuyennx_add_start_20190108 L2PT-295
		$("#toolbarIdtreat_17").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					//L2PT-82196
					if(cf.NTU_BBHC_THUOC_LD == '0'){
						paramInput = {
								KHAMBENHID : $("#hidKHAMBENHID").val(),
								TIEPNHANID : $("#hidTIEPNHANID").val(),
								maubenhpham_id : ""
							};
							dlgPopup = DlgUtil.buildPopupUrl("divDlgHoiChanLD", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K070_HoiChanLanhDao", paramInput, "Tạo biên bản hội chẩn thuốc duyệt lãnh đạo " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
									1000, 550);
							DlgUtil.open("divDlgHoiChanLD");
					}
					if(cf.NTU_BBHC_THUOC_LD == '1'){
						var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
						var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
						if (!rowData.CHANDOANVAOKHOA) {
							DlgUtil.showMsg("Bệnh nhân chưa có chẩn đoán.!");
							return;
						}
						var myVar = {
				                DICHVUKHAMBENHID : 0,
				                TIEPNHANID : $("#hidTIEPNHANID").val(),
				                KHAMBENHID : $("#hidKHAMBENHID").val(),
								BENHNHANID : $("#hidBENHNHANID").val(),
								hosobenhanid : $("#hidHOSOBENHANID").val(),
				                ICD10NAME : rowData.CHANDOANVAOKHOA.split('-')[1].trim(),
				                ICD10CODE : rowData.CHANDOANVAOKHOA.split('-')[0].trim(),
				                THONGTIN : $("#lblMSG_MABENHAN").html()  + '|' + $("#lblMSG_TENBENHNHAN").html()+ '|' + $("#lblMSG_NGAYSINH").html()+ '|' + $("#lblMSG_GIOITINH").html()+ '|' + $("#lblMSG_DIACHI").html(),
				                HoiChan1sao : '0',
				                HoiChanKS : '1',
				                CHUY: ''
				            };
				        DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K069_HoiChanThuoc_LD",myVar,"Tạo biên bản hội chẩn thuốc duyệt lãnh đạo " + (thongtinbn==null?"":"(" + thongtinbn + ")"),900,600).open();
					}
					if(cf.NTU_BBHC_THUOC_LD == '2'){
						var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
						var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
						if (!rowData.CHANDOANVAOKHOA) {
							DlgUtil.showMsg("Bệnh nhân chưa có chẩn đoán.!");
							return;
						}
						var _sql_par = [];
						_sql_par.push({
							"name" : "[0]",
							value : $("#hidTIEPNHANID").val()
						});
						_sql_par.push({
							"name" : "[1]",
							value : $("#hidKHAMBENHID").val()
						});
						var thuocsaoid = jsonrpc.AjaxJson.getOneValue("NTU.GETTHUOCSAO.BN", _sql_par);
						var myVar = {
				                DICHVUKHAMBENHID : 0,
				                TIEPNHANID : $("#hidTIEPNHANID").val(),
				                KHAMBENHID : $("#hidKHAMBENHID").val(),
				                ICD10NAME : rowData.CHANDOANVAOKHOA.split('-')[1].trim(),
				                ICD10CODE : rowData.CHANDOANVAOKHOA.split('-')[0].trim(),
				                //checkKeChiTiet : '1',
				                HoiChan1sao : '0',
				                HoiChanKS : '1',
				                THUOCSAOID : thuocsaoid == 'null' ? '0' : thuocsaoid,
				                CHUY: ''
				            };
				            DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K069_PopupHoiChan",myVar,"Tạo biên bản hội chẩn thuốc duyệt lãnh đạo " + (thongtinbn==null?"":"(" + thongtinbn + ")"),900,600).open();
					}
					
				});
		//START--L2PT-2526
		//L2PT-25832
		$("#toolbarIdtreat_hcthuocsao").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					paramInput = {
						KHAMBENHID : $("#hidKHAMBENHID").val(),
						TIEPNHANID : $("#hidTIEPNHANID").val(),
						BENHNHANID : $("#hidBENHNHANID").val(),
						TIEPNHANID : $("#hidTIEPNHANID").val(),
						maubenhpham_id : ""
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgHoiChanLD", "divDlg", "manager.jsp?func=../noitru/NTU02D220_Hoichan_thuocsao", paramInput, "Tạo biên bản hội chẩn thuốc sao " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
							1000, 550);
					DlgUtil.open("divDlgHoiChanLD");
		});

		$("#toolbarIdtreat_bbhctbvbd").on(
			"click",
			function() {

				if (!checkBeforeClickOnMenu())
					return;
				var par = {
					KHAMBENHID : $("#hidKHAMBENHID").val(),
					TIEPNHANID : $("#hidTIEPNHANID").val(),
					maubenhpham_id : ""
				};
				DlgUtil.buildPopupUrl(
					"NTU02D212_BienBanHoiChanThuocBVBD",
					"NTU02D212_BienBanHoiChanThuocBVBD",
					"manager.jsp?func=../noitru/NTU02D212_BienBanHoiChanThuocBVBD",
					par,
					"Biên bản hội chẩn thuốc BVBĐ " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
					1000,
					700).open();
			});

		$("#toolbarIdtreat_18").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			if (rowData.HINHTHUCRAVIENID != '6' && opt.hospital_id == '10284') { //L2PT-55984
				DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, tạo phiếu chuyển viện chỉ với những bệnh nhân chuyển viện');
				return;
			} else {
				var paramInput = {
						mabenhnhan : $("#hidMABENHNHAN").val(),
						tenbenhnhan : $("#hidTENBENHNHAN").val(),
						nghenghiep : rowData.TENNGHENGHIEP,
						diachi : rowData.DIACHI,
						namsinh : rowData.NAMSINH,
						khambenhid : $('#hidKHAMBENHID').val(),
						benhnhanid : $('#hidBENHNHANID').val(),
						chandoan : rowData.CHANDOANRAVIEN,
						ngaytiepnhan : $("#hidTHOIGIANVAOVIEN").val(),
						capnhat : '1',
						hosobenhanid : $('#hidHOSOBENHANID').val()
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K009_Chuyenvien", paramInput, "Thông tin chuyển viện " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
					DlgUtil.open("dlgXuTri");
			}
		});
		//doanpv_add_start_2019711 L2PT-6720
		$("#toolbarIdtreat_19").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenh_id : $("#hidKHAMBENHID").val(),
				maubenhpham_id : ""
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTuVong", "divDlg", "manager.jsp?func=../noitru/NTU02D119_KiemDiemTuVong", paramInput, "Biên bản kiểm điểm tử vong " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 550);
			DlgUtil.open("divDlgTuVong");
		});
		//doanpv_add_start_20190806 L2PT-7575
		$("#toolbarIdtreat_20").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				hosobenhan_id : $("#hidHOSOBENHANID").val(),
				khambenh_id : $("#hidKHAMBENHID").val(),
				maubenhpham_id : ""
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuSoSinh", "divDlg", "manager.jsp?func=../noitru/NTU02D123_PhieuSoSinh", paramInput, "Phiếu sơ sinh " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 700);
			DlgUtil.open("divDlgPhieuSoSinh");
		});
		//doanpv_add_start_20190820 L2PT-8053
		$("#toolbarIdtreat_21").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				hosobenhan_id : $("#hidHOSOBENHANID").val(),
				khambenh_id : $("#hidKHAMBENHID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuTSDU", "divDlg", "manager.jsp?func=../noitru/NTU02D128_PhieuTienSuDiUng", paramInput, "Phiếu tiền sử dị ứng " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1250, 700);
			DlgUtil.open("divDlgPhieuTSDU");
		});
		// cong van 3730 - phieu kham chi dinh phuc hoi chuc nang
		$("#toolbarIdtreatdt_pkcdphcn").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					var khambenhid = rowData.KHAMBENHID;
					var chucNang = 'PKCDPHCN';
					var name = chucNang + '@' + khambenhid;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=800,width=1450');
					popup.moveTo(0, 0);
					popup.onbeforeunload = function() {
						loadGrid();
					}
				});
		$("#toolbarIdprint_pkcdphcn").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					var khambenhid = rowData.KHAMBENHID;
					var chucNang = 'PKCDPHCN';
					var mode = 1;
					var name = chucNang + '@' + khambenhid + '@' + mode;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
							screen.height + ',width=' + screen.width);
					popup.moveTo(0, 0);
					popup.onbeforeunload = function() {
						loadGrid();
					}
				});
		// cong van 3730 - phieu luong gia hoat dong chuc nang va tham gia
		$("#toolbarIdtreatdt_plghdcntg").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					var khambenhid = rowData.KHAMBENHID;
					var chucNang = 'PLGHDCNTG';
					var name = chucNang + '@' + khambenhid;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=800,width=1450');
					popup.moveTo(0, 0);
					popup.onbeforeunload = function() {
						loadGrid();
					}
				});

		// phiếu khám tiền mê
		$("#toolbarIdtreatdt_pktm").on(
			"click",
			function() {
				if (!checkBeforeClickOnMenu())
					return;
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var khambenhid = rowData.KHAMBENHID;
				var chucNang = 'PKTM';
				var name = chucNang + '@' + khambenhid;
				var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
					screen.height + ',width=' + screen.width);
				popup.moveTo(0, 0);
				popup.onbeforeunload = function() {
					loadGrid();
				}
			});

		//doanpv_add_start_20190820 L2PT-8401
		$("#toolbarIdtreat_22").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				benhnhanid : $("#hidBENHNHANID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $("#hidPHONGID").val(),
				bacsike : $("#labelHidBACSYKE").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgLLM", "divDlg", "manager.jsp?func=../noitru/NTU02D131_LenLichMo", paramInput, "Lên lịch mổ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1330, 650);
			DlgUtil.open("divDlgLLM");
		});
		//START -- L2PT-9108
		$("#toolbarIdtreat_23").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgNDC", "divDlg", "manager.jsp?func=../noitru/NTU02D150_XetNghiemNongDoCon", paramInput, "Phiếu xét nghiệm nồng độ cồn " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 400);
			DlgUtil.open("dlgNDC");
		});
		//START -- L2PT-21577
		$("#toolbarIdtreat_24").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			if( cf.HIS_SHOW_THONGTIN_TUVONG == '1') {
				paramInput = {
					mabenhnhan : rowData.MABENHNHAN,
					tenbenhnhan : rowData.TENBENHNHAN,
					nghenghiep : rowData.TENNGHENGHIEP,
					diachi : rowData.DIACHI,
					namsinh : rowData.NAMSINH,
					khambenhid : rowData.KHAMBENHID,
					benhnhanid : rowData.BENHNHANID,
					chandoan : rowData.CHANDOANRAVIEN,
					ngaytiepnhan : rowData.NGAYTIEPNHAN,
					capnhat : '1',
					hosobenhanid : rowData.HOSOBENHANID
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K010_Tuvong", paramInput, "Thông tin tử vong " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 970, 700);
				DlgUtil.open("dlgXuTri");
			} else {
				paramInput = {
					hosobenhanid : rowData.HOSOBENHANID
				};
				var dlgPopup = DlgUtil.buildPopupUrl("divDlgGBT", "divDlg", "manager.jsp?func=../noitru/NTU02D166_GiayBaoTu", paramInput, "Giấy báo tử " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 900, 500);
				DlgUtil.open("divDlgGBT");
			}
		});
		$("#toolbarIdtreat_29").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var myVar = {
				khambenhId : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				hosobenhanId : $("#hidHOSOBENHANID").val(),
				loaibenhanId : $("#hidLOAIBENHANID").val(),
				phongId : $("#hidPHONGID").val(),
				trangthaikhambenh : $("#hidTRANGTHAIKHAMBENH").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT04K007_PhieuKhamGayMeHoiSuc", myVar, "KHÁM GÂY MÊ TRƯỚC PTTT " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 650);
			//dlgPopup.open();
			DlgUtil.open("dlgKham");
		});
		
		
		//L2PT-33388
		if (getCauHinh("NTU02D170_FORMPHIEU_CSNB") == "1") { 
			$('#toolbarIdbtnTreat').next().append('<li data-external="{}" id="toolbarIdtreatdt_csnb"><a href="#"><i class="glyphicon glyphicon-dieutri"></i>&nbsp;&nbsp;Phiếu theo dõi và thực hiện kế hoạch chăm sóc người bệnh</a></li>')		
		} 
		$("#toolbarIdtreatdt_csnb").click(
				function() { 
					var khambenhid =  $("#hidKHAMBENHID").val();
					var chucNang = 'CHAMSOCNB';
					var name = chucNang + '@' + khambenhid;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU_CSNB&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
							screen.height + ',width=1200');
							
				});
				
		//L2PT-35354
		if (getCauHinh("NTU02D170_FORMPHIEU_TRUYEN_OXY") == "1") { 
			$('#toolbarIdbtnTreat').next().append('<li data-external="{}" id="toolbarIdtreatdt_truyen_oxy"><a href="#"><i class="glyphicon glyphicon-dieutri"></i>&nbsp;&nbsp;Bảng theo dõi truyền Oxytoxin</a></li>')		
		} 
		$("#toolbarIdtreatdt_truyen_oxy").click( function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);  
					 paramInput={ 
						PHIEU: rowData
						 , KHAMBENHID: $("#hidKHAMBENHID").val()
						 , CHUCNANG: 'TRUYEN_OXY'
					 };
					 var name = JSON.stringify(paramInput); 
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU_THEODOI_TRUYEN_OXY&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
							screen.height + ',width=1200'); 
				});
				
		
		
		
		
		// Start ThemPhieu
		$("#toolbarIdtreat_themphieu").click(function() {
			let _loaiphieu = "NTU_DIEUTRI";
			if (type == "3") {
				_loaiphieu = "NGT_DIEUTRI";
			}
			let myVar = {
				KHAMBENHID: $("#hidKHAMBENHID").val(),
				BENHNHANID: $("#hidBENHNHANID").val(),
				TIEPNHANID: $("#hidTIEPNHANID").val(),
				MABENHNHAN: $("#hidMABENHNHAN").val(),
				HOSOBENHANID: $("#hidHOSOBENHANID").val(),
				MAUBENHPHAMID: 1111,
				DICHVUKHAMBENHID: 2222,
				KHOAID: opt._deptId,
				PHONGID: $("#hidPHONGID").val(),
				LOAI_PHIEU :_loaiphieu
			}

			dlgPopup=DlgUtil.buildPopupUrl("divDlgThemPhieu","divDlg","manager.jsp?func=../noitru/NTU02D204_ThemPhieu", myVar,"HIS-Thêm phiếu " + (thongtinbn==null?"":"(" + thongtinbn + ")"),window.innerWidth*0.95,window.innerHeight*0.95);
			DlgUtil.open("divDlgThemPhieu");

		});
		// End ThemPhieu
		
		//L2PT-23979 start
		$("#toolbarIdtreat_dgbd_d155").on("click",function() {
			if (!checkBeforeClickOnMenu())
				return;
			var NTU_PDGBD_VER2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_PDGBD_VER2');
			if (NTU_PDGBD_VER2 == '1') {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var ttKhac = getTTKhacPCSC2();
				var khambenhid = rowData.KHAMBENHID;
				var tenbenhnhan = rowData.TENBENHNHAN;
				var mabenhan = rowData.MABENHAN;
				var tuoi = ttKhac.tuoi;
				var gioi = ttKhac.gioi;
				var giuong = ttKhac.giuong;
				var phong = opt.subdept_name;
				var ngayvaovien = rowData.NGAYVAOKHOA;
				var bmi = ttKhac.bmi;
				var name = khambenhid + '@' + tenbenhnhan + '@' + mabenhan + '@' + tuoi + '@' + gioi + '@' + giuong + '@' + phong + '@' + ngayvaovien + '@' + bmi;
				var popup = window.open('manager.jsp?func=../noitru/NTU02D164_PDGBDBS&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
						screen.height + ',width=' + screen.width);
				popup.moveTo(0, 0);
				popup.onbeforeunload = function() {
					loadGrid();
				}
			} else {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var ttKhac = getTTKhacPCSC2();
				var par = {
					khambenhid : rowData.KHAMBENHID,
					tiepnhanid : rowData.TIEPNHANID,
					hosobenhanid : rowData.HOSOBENHANID,
					benhnhanid : rowData.BENHNHANID,
					mabenhan : rowData.MABENHAN,
					tenbenhnhan : rowData.TENBENHNHAN,
					tuoi : ttKhac.tuoi,
					gioi : ttKhac.gioi,
					giuong : ttKhac.giuong,
					chanDoan : ""
				};
				var dlgPopup = DlgUtil.buildPopupUrl("PhieuDanhGiaBanDau", "PhieuDanhGiaBanDau", "manager.jsp?func=../noitru/NTU02D155_PhieuDanhGiaBanDau", par, "Phiếu đánh giá ban đầu " + (thongtinbn==null?"":"(" + thongtinbn + ")"),1200, 500);
				dlgPopup.open();
			}
		});
		function getTTKhacPCSC2() {
			var obj = {
				tuoi : "",
				gioi : "",
				giuong : "",
				bmi : ""
			}
			obj.tuoi = getTuoiPCSC2();
			obj.gioi = $('#hidGIOITINH').val() == '1' ? 'Nam' : 'Nữ';
			obj.giuong = $('#lblGIUONG').text();
			obj.bmi = getBmi();
			return obj;
		}
		function getTuoiPCSC2() {
			// if ($('#hidNGAYSINH').val().length > 0) {
			// 	var namsinh = $('#hidNGAYSINH').val().substring(6,10);
			// 	var today = new Date();
			// 	var year = today.getFullYear();
			// 	return (year - namsinh) + '';
			// } else {
			// 	return "";
			// }
			var str = $('#lblNGAYSINH').text();
			var index1 = str.indexOf("(");
			var index2 = str.indexOf(")");
			var tuoi = str.substr(index1 + 1, index2 - index1 - 1);
			var index3 = tuoi.indexOf("Tuổi");
			if (index3 != -1) {
				return tuoi.substr(0, index3);
			}
			return tuoi;
		}
		function getBmi() {
			var obj = new Object();
			obj.khambenhid = $("#hidKHAMBENHID").val();
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.08", JSON.stringify(obj));
			if (result.length > 0) {
				var ttKhac = result[0];
				if (ttKhac.CANNANG !== "" && ttKhac.CHIEUCAO !== "" && ttKhac.CHIEUCAO !== "0") {
					var bmi = ttKhac.CANNANG / Math.pow(ttKhac.CHIEUCAO * 0.01, 2);
					return bmi.toFixed(2);
				} else {
					return "";
				}
			} else {
				return "";
			}
		}
		//L2PT-23979 end
		
		//callback cho phieu hoi chan lanh dao
		EventUtil.setEvent("assignSevice_SaveHoiChanLD", function(e) {
			DlgUtil.showMsg(e.msg);
			//DlgUtil.close("divDlgHoiChanLD");
		});
		//tuyennx_add_end
		//Begin_HaNv_23042018: tao phieu hoi chan dinh duong
		$("#toolbarIdtreat_14").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				type : 1
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgHoiChanDD", "divDlg", "manager.jsp?func=../noitru/NTU02D089_HoiChanDinhDuong", paramInput, "Tạo giấy mời hội chẩn, tư vấn dinh dưỡng " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1330, 650);
			DlgUtil.open("divDlgHoiChanDD");
		});
		//End_HaNv_23042018
		//tao phieu truyen dich
		$("#toolbarIdtreat_4").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenh_id : $("#hidKHAMBENHID").val(),
				maubenhpham_id : "",
				isCopy : '0',//L2PT-2085
				userID : opt.user_id
			//L2PT-4346
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgPTruyenDich", "divDlg", "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenDich", paramInput, "Tạo Phiếu truyền dịch " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 850, 300);
			DlgUtil.open("divDlgPTruyenDich");
		});
		//tao phieu truyen mau
		$("#toolbarIdtreat_7").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenh_id : $("#hidKHAMBENHID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				thoigianvaovien : rowData.THOIGIANVAOVIEN
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgPTruyenMau", "divDlg", "manager.jsp?func=../noitru/NTU02D006_PhieuTruyenMau_Insert", paramInput, "Tạo phiếu truyền máu " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 600);
			DlgUtil.open("divDlgPTruyenMau");
		});
		//chuyen mo cap cuu
		$("#toolbarIdhandling_4").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			if (rowData.TRANGTHAIKHAMBENH == '4') {
				paramInput = {
					benhnhanid : $("#hidBENHNHANID").val(),
					khambenhid : $("#hidKHAMBENHID").val(),
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					tiepnhanid : $("#hidTIEPNHANID").val(),
					doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
					hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
					loaibenhanid : $("#hidLOAIBENHANID").val(),
					phongid : $("#hidPHONGID").val(),
					khoaid : _opts._deptId,
					mode : '0' //mo cap cuu
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgMoCapCuu", "divDlg", "manager.jsp?func=../noitru/NTU01H007_ChuyenMoCapCuu", paramInput, "Chuyển mổ cấp cứu " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 600, 100);
				DlgUtil.open("divDlgMoCapCuu");
			} else {
				DlgUtil.showMsg('Không thể chuyển mổ cấp cứu cho bệnh nhân này');
			}
		});
		$("#toolbarIdhandling_3").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					//mo popup nhap benh nhan khi dbclick on row

					//HungND - L2PT-51531
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
					if (rowData != null && rowData.TRANGTHAIKHAMBENH == '9' ) {
						DlgUtil.showMsg("Bệnh án đã đóng");
						return;
					}
					//HungND - L2PT-51531 END

					var paramInput = {
						mode : 2,
						benhnhanid : $("#hidBENHNHANID").val(),
						khambenhid : $("#hidKHAMBENHID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val(),
						tiepnhanid : $("#hidTIEPNHANID").val(),
						type : type
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgNhapBenhNhan", "divDlg", "manager.jsp?func=../noitru/NTU01H002_NhapBenhNhan", paramInput, "HIS - Xử trí " + (thongtinbn==null?"":"(" + thongtinbn + ")"), window.innerWidth * 0.95,
							window.innerHeight * 0.95);
					DlgUtil.open("divDlgNhapBenhNhan");
				});
		//chuyen mo phien

		//tao phieu ngay giuong
		$("#toolbarIdbtnNewBed").on("click", function() {
			if (!kegiuongDs) {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (rowData.TRANGTHAIKHAMBENH != 4) {
					DlgUtil.showMsg('Bệnh nhân không ở trạng thái đang điều trị');
					return;
				}
				if (selRowId != null && selRowId > 0) {
					if (!checkDoiTuongBN())
						return;
					paramInput = {
						chidinhdichvu : '1',
						loaidichvu : '13',
						loaiphieumbp : '12',
						benhnhanid : $("#hidBENHNHANID").val(),
						khambenhid : $("#hidKHAMBENHID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val(),
						tiepnhanid : $("#hidTIEPNHANID").val(),
						doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
						hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
						loaibenhanid : $("#hidLOAIBENHANID").val(),
						loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
						subDeptId : $('#hidPHONGID').val()
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 13, paramInput, "Phiếu ngày giường " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
					DlgUtil.open("divDlgDichVu");
				} else {
					DlgUtil.showMsg('Chưa chọn bệnh nhân');
				}
			} else {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (rowData.TRANGTHAIKHAMBENH != 4) {
					DlgUtil.showMsg('Bệnh nhân không ở trạng thái đang điều trị');
					return;
				}
				paramInput = {
					khambenhid : $("#hidKHAMBENHID").val(),
					tiepnhanid : $("#hidTIEPNHANID").val()
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVuGiuong", "divDlg", "manager.jsp?func=../noitru/NTU01H034_BenhNhanGiuong", paramInput, "Ngày giường " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 600);
				DlgUtil.open("divDlgDichVuGiuong");
			}
		});

		$("#toolbarIdhandling_5").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			if (rowData.TRANGTHAIKHAMBENH == '4') {
				paramInput = {
					benhnhanid : $("#hidBENHNHANID").val(),
					khambenhid : $("#hidKHAMBENHID").val(),
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					tiepnhanid : $("#hidTIEPNHANID").val(),
					doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
					hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
					loaibenhanid : $("#hidLOAIBENHANID").val(),
					phongid : $("#hidPHONGID").val(),
					khoaid : _opts._deptId,
					mode : "1" //mo phien
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgMoPhien", "divDlg", "manager.jsp?func=../noitru/NTU01H007_ChuyenMoCapCuu", paramInput, "Gửi duyệt mổ phiên " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 600, 100);
				DlgUtil.open("divDlgMoPhien");
			} else {
				DlgUtil.showMsg('Không thể chuyển mổ phiên cho bệnh nhân này');
			}
		});
		//điều trị kết hợp
		$("#toolbarIdhandling_7").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			if (_kbdtkhid > 0) {
				DlgUtil.showMsg('Bệnh nhân đang khám chuyên khoa. Không thể chuyển khám tiếp');
				return;
			}
			if (rowData.TRANGTHAIKHAMBENH == '4') {
				paramInput = {
					benhnhanid : $("#hidBENHNHANID").val(),
					khambenhid : $("#hidKHAMBENHID").val(),
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					tiepnhanid : $("#hidTIEPNHANID").val(),
					doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
					hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
					loaibenhanid : $("#hidLOAIBENHANID").val(),
					phongid : $("#hidPHONGID").val(),
					khoaid : _opts._deptId,
					mode : "2" // dieu tri ket hop
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDieutriKetHop", "divDlg", "manager.jsp?func=../noitru/NTU01H007_ChuyenMoCapCuu", paramInput, "Chuyển Khám chuyên khoa " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 500, 200);
				DlgUtil.open("divDlgDieutriKetHop");
			} else {
				DlgUtil.showMsg('Không thể chuyển Khám chuyên khoa cho bệnh nhân này');
			}
		});
		// EDITED BY SONDN - 31/07/2019
		function _openKhamChuyenKhoa(mode, h, w, rowData) {
			paramInput = {
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				mabhyt : mode == "1" ? "" : rowData.MA_BHYT, // che do xem thong tin khong truyen vao rowData
				mode : mode
			};
			var _str = mode == "0" ? "Chuyển Khám chuyên khoa phòng khám" : "Lịch sử khám chuyên khoa";
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDieutriKetHop", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K074_CHUYENPKNOITRU", paramInput, _str + " " + (thongtinbn==null?"":"(" + thongtinbn + ")"), h, w);
			DlgUtil.open("divDlgDieutriKetHop");
		}
		EventUtil.setEvent("ngt02k047_chuyenpknoitru_close", function(e) {
			loadGridData();
			DlgUtil.close("divDlgDieutriKetHop");
		});
		$("#toolbarIdhandling_10").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			if (rowData.TRANGTHAIKHAMBENH == '4') {
				if (opt.hospital_id == 10284) {
					//BVTM-5578
					_openKhamChuyenKhoa("0", 1200, 350, rowData);
				} else
					_openKhamChuyenKhoa("0", 1200, 350, rowData);
			} else {
				DlgUtil.showMsg('Không thể chuyển Khám chuyên khoa cho bệnh nhân này');
			}
		});
		$("#toolbarIdhandling_11").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			_openKhamChuyenKhoa("1", 1200, 500, null);
		});
		// END EDITED BY SONDN - 27/08/2018
		//dich vu dieu tri ket hop
		$("#toolbarIdhandling_9").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					/*if(rowData.TRANGTHAIKHAMBENH != '1' && rowData.TRANGTHAIKHAMBENH != '9'){*/
					paramInput = {
						benhnhanid : $("#hidBENHNHANID").val(),
						khambenhid : $("#hidKHAMBENHID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val(),
						tiepnhanid : $("#hidTIEPNHANID").val(),
						doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
						hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
						loaibenhanid : $("#hidLOAIBENHANID").val(),
						phongid : $("#hidPHONGID").val(),
						khoaid : _opts._deptId,
						mode : "2" // dieu tri ket hop
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVuKetHop", "divDlg", "manager.jsp?func=../noitru/NTU02D071_DichVuKetHop", paramInput, "Tra cứu dịch vụ chuyên khoa " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
							window.innerWidth * 0.95, window.innerHeight * 0.93);
					DlgUtil.open("divDlgDichVuKetHop");
					/*} else {
						DlgUtil.showMsg('Không thể chuyển khám chuyên khoa cho bệnh nhân này');
					}*/
				});
		//ket thuc dieu tri ket hop
		$("#toolbarIdhandling_8").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var _sql_par_dtkh = [];
			_sql_par_dtkh.push({
				"name" : "[0]",
				"value" : $("#hidKHAMBENHID").val()
			});
			var _data_dtkh = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.KT.DT.KETHOP", _sql_par_dtkh);
			var rows_dtkh = JSON.parse(_data_dtkh);
			if (rows_dtkh != null && rows_dtkh.length > 0) {
				var _khambenhchaid = rows_dtkh[0].KHAMBENHID;
				var _khoabandauid = rows_dtkh[0].KHOAID;
				var _par_dtkh = [ $("#hidKHAMBENHID").val(), _khambenhchaid, _khoabandauid ];
				var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H009.KTDT", _par_dtkh.join('$'));
				if (_return == 1) {
					DlgUtil.showMsg("Kết thúc khám chuyên khoa thành công");
					loadGridData();
				} else {
					DlgUtil.showMsg("Kết thúc khám chuyên khoa thất bại");
				}
			} else {
				DlgUtil.showMsg("Bệnh nhân không khám chuyên khoa, nên không thể cập nhật kết thúc khám chuyên khoa!");
			}
		});
		//TRA VE KHOA DIEU TRI
		$("#toolbarIdhandling_6").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var _sql_par_dtkh = [];
			_sql_par_dtkh.push({
				"name" : "[0]",
				"value" : $("#hidKHAMBENHID").val()
			});
			var _data_dtkh = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.KT.DT.TVKDT", _sql_par_dtkh);
			var rows_dtkh = JSON.parse(_data_dtkh);
			if (rows_dtkh != null && rows_dtkh.length > 0) {
				var _khambenhchaid = rows_dtkh[0].KHAMBENHID;
				var _khoabandauid = rows_dtkh[0].KHOAID;
				var _par_dtkh = [ $("#hidKHAMBENHID").val(), _khambenhchaid, _khoabandauid ];
				var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H007.TVKDT", _par_dtkh.join('$'));
				if (_return == 1) {
					DlgUtil.showMsg("Trả về khoa điều trị thành công");
					loadGridData();
				} else {
					DlgUtil.showMsg("Trả về khoa điều trị thất bại");
				}
			} else {
				DlgUtil.showMsg("Bệnh nhân không chuyển mổ, nên không thể cập nhật trả về khoa điều trị!");
			}
		});
		//Tao phieu thuoc
		$("#toolbarIddrug_1").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				opt : "02D010", // tao phieu thuoc
				_doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
		});
		//Tao phieu thuoc từ kho
		//HaNv_051222: Thao tác menu trên mobie - L2PT-29886
		//$("#toolbarIddrug_khothuoc").on("click", function() {
		$("#toolbarIddrug_khothuoc").hammer().bind("tap", function() {
            var loadkho = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "KETHUOC_LOADKHO_KHOTHUOC");
			if (!checkBeforeClickOnMenu())
				return;
			// start jira L2PT-9615
			// neu chua xep giuong thi khong cho chi dinh dich vu, thuoc - HaNv_251022: L2PT-27502
			var cauHinhXepGiuong = getCauHinh('NTU_CHIDINHDVKHIXEPGIUONG');
			if (cauHinhXepGiuong != 0) {
				if (isChuaXepGiuong($("#hidKHAMBENHID").val())) {
					if (cauHinhXepGiuong == '1') {
						DlgUtil.showMsg("Bệnh nhân chưa xếp giường!");
					} else if (cauHinhXepGiuong == '2') {
						DlgUtil.showMsg("Bệnh nhân chưa xếp giường!");
						return;
					} else if (cauHinhXepGiuong == '3' && $("#hidLOAITIEPNHANID").val() == '0') {
						DlgUtil.showMsg("Bệnh nhân chưa xếp giường!");
					} else if (cauHinhXepGiuong == '4' && $("#hidLOAITIEPNHANID").val() == '0') {
						DlgUtil.showMsg("Bệnh nhân chưa xếp giường!");
						return;
					}
				}
			}
			// end jira L2PT-9615
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				opt : "02D010", // tao phieu thuoc
				loadkhotheo : loadkho// 0: kho và tủ trực, 1: kho, 2: tủ trực. - macdinh : 1
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu thuốc từ kho " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
		});
		//Tao phieu thuoc từ kho. Begin_DoanPV_20201201 L2PT-31476 : Tách Menu kê thuốc ngoại trú
		$("#toolbarIddrug_khothuoc1").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				opt : "02D010", // tao phieu thuoc
				loadkhotheo : '1',// 0: kho và tủ trực, 1: kho, 2: tủ trực.
				loaikho : '1'
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu thuốc từ kho " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
		});
		//Tao phieu thuoc từ kho. Begin_DoanPV_20201201 L2PT-31476 : Tách Menu kê thuốc ngoại trú
		$("#toolbarIddrug_khothuoc2").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				opt : "02D010", // tao phieu thuoc
				loadkhotheo : '1',// 0: kho và tủ trực, 1: kho, 2: tủ trực.
				loaikho : '0'
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu thuốc từ kho " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
		});
		//tuyennx_add_start_20190115_L2PT-9849
		//Tao phieu thuoc ra vien
		$("#toolbarIddrug_1rv").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			//L2PT-60299
			if(NTU_THUOC_RAVIEN == '0'){
				paramInput = {
						khambenhid : $("#hidKHAMBENHID").val(),
						maubenhphamid : "",
						loaikedon : _loaikedon,
						dichvuchaid : "",
						opt : "02D010", // tao phieu thuoc
						ravien : "1",
						loadkhotheo : '0'// 0: kho và tủ trực, 1: kho, 2: tủ trực.
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu thuốc ra viện " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
					DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
			}else{
				paramInput = {
						khambenhid : $("#hidKHAMBENHID").val(),
						maubenhphamid : "",
						//L2PT-2841
						loaikedon : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'LOAIKEDON_MUANGOAI'), //tuyennx_edit_L2PT-6845,
						dichvuchaid : "",
						ravien : "1",
						opt : "02D011"
					};
					var chchucnang = [ 'CHUCNANG_KEDON_MUANGOAI_NTU' ];
					var _chucnang = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', chchucnang.join('$'));
					dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D011", "divDlg", "manager.jsp?func=../noitru/" + _chucnang, paramInput, "Tạo đơn thuốc ra viện  " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
					DlgUtil.open("divDlgTaoPhieuThuoc" + "02D011");
			}
		});
		// start jira 21951
		// đơn thuốc đông y ra viện
		$("#toolbarIddrug_1dyrv").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				opt : "02D017", // tao phieu thuoc
				ravien : "1",
				loadkhotheo : '0'// 0: kho và tủ trực, 1: kho, 2: tủ trực.
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D017", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu thuốc ra viện " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D017");
		});
		// end jira 21951
		//Tao phieu vat tu ra vien L2PT-18977
		$("#toolbarIddrug_vtrv").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : 1,
				dichvuchaid : "",
				opt : "02D015", // tao phieu thuoc
				ravien : "1",
				loadkhotheo : '0'// 0: kho và tủ trực, 1: kho, 2: tủ trực.
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D015", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu vật tư ra viện " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D015");
		});
		//tuyennx_add_end_20190115_L2PT-9849
		//tuyennx_add_start_20190115_L2PT-12313
		//Tao phieu thuoc ra vien
		$("#toolbarIddrug_1put").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuPUThuoc", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K076_PhanUngThuocADR", paramInput, "Tạo phản ứng thuốc ADR " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 590);
			DlgUtil.open("divDlgTaoPhieuPUThuoc");
		});
		//tuyennx_add_end_20190115_L2PT-12313
		// click button Thuốc > Tạo phiếu thuốc đông y.
		$("#toolbarIddrug_1dy").on("click", function(e) {
			//tuyennx_add_start
			var _sql_par = [ {
				"name" : "[0]",
				"value" : $("#hidTIEPNHANID").val()
			} ];
			var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK.DATA", $("#hidTIEPNHANID").val());
			if (_result == 1) {
				DlgUtil.showMsg('Bệnh nhân đã duyệt kế toán không thể thao tác');
				return;
			}
			EventUtil.setEvent("assignDrug_fail", function(e) {
				DlgUtil.close("dlgCDT");
			});
			//Begin_HaNv_25032020: Thêm cấu hình để truyền biến loadkhotheo vào form thuốc với BN điều trị ngoại trú - L2PT-18463
			if ($("#hidLOAITIEPNHANID").val() == 3) {
				var loadkho = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "KETHUOC_LOADKHO_DONGY");
				paramInput = {
					khambenhid : $("#hidKHAMBENHID").val(),
					mabenhnhan : $("#hidMABENHNHAN").val(),
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : "",
					loadkhotheo : loadkho, // 0: kho và tủ trực, 1: kho, 2: tủ trực.
					opt : "02D017"
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgCDT", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Chỉ định thuốc YHCT " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
				DlgUtil.open("dlgCDT");
			} else {
				_openDialogThuoc('02D017', 1, "Chỉ định thuốc YHCT");
			}
			//END_HaNv_25032020
		});
		// click button Thuốc > Tạo phiếu trả thuốc đông y.
		$("#toolbarIddrug_2dy").on("click", function(e) {
			//tuyennx_add_start
			var _sql_par = [ {
				"name" : "[0]",
				"value" : $("#hidTIEPNHANID").val()
			} ];
			var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT.CHECK.DATA", $("#hidTIEPNHANID").val());
			if (_result == 1) {
				DlgUtil.showMsg('Bệnh nhân đã duyệt kế toán không thể thao tác');
				return;
			} else if (_result == 2) {
				DlgUtil.showMsg('Bệnh nhân đã nhập viện không thể thao tác');
				return;
			}
			EventUtil.setEvent("assignDrug_fail", function(e) {
				DlgUtil.close("dlgCDT");
			});
			_openDialogThuoc('02D018', 1, "Trả thuốc YHCT");
		});
		function _openDialogThuoc(_opt, _loaikedon, _title) {
			var myVar = {
				khambenhid : $("#hidKHAMBENHID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				maubenhphamid : "",
				opt : _opt,
				loaikedon : _loaikedon,
				dichvuchaid : ''
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgCDT", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", myVar, _title + " " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("dlgCDT");
		}
		//Tao phieu thuoc từ tủ trực
		//HaNv_051222: Thao tác menu trên mobie - L2PT-29886
		//$("#toolbarIddrug_tutruc").on("click", function() {
		$("#toolbarIddrug_tutruc").hammer().bind("tap", function() {
            var loadkho = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "KETHUOC_LOADKHO_TUTRUC");
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				opt : "02D010", // tao phieu thuoc
				loadkhotheo : loadkho// 0: kho và tủ trực, 1: kho, 2: tủ trực. - macdinh :2
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu thuốc từ tủ trực " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
		});
		//Tao phieu thuoc
		$("#toolbarIddrug_9").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				opt : "02D010_1" // tao phieu thuoc
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010_1", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu thuốc lẻ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010_1");
		});
		//tuyennx_add_start_20181023 L2HOTRO-11471
		var loadkho = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "KETHUOC_LOADKHO_HAOPHI");
		//tuyennx_add_end_20181023
		//Tao phieu hao phi
		$("#toolbarIddrug_8").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				opt : "02D010",
				macdinh_hao_phi : 9,
				//tuyennx_add_start_20181023 L2HOTRO-11471
				loadkhotheo : loadkho
			// 0: kho và tủ trực, 1: kho, 2: tủ trực.
			//tuyennx_add_end_20181023
			};
			//tuyennx_edit_start_20181023 L2PT-5325
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu hao phí " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			//tuyennx_edit_end_20181023
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
		});

		//Tao phieu mien phi L2PT-29648
		$("#toolbarIddrug_mp").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				opt : "02D010",
				macdinh_hao_phi : 15,
				//tuyennx_add_start_20181023 L2HOTRO-11471
				loadkhotheo : loadkho
			// 0: kho và tủ trực, 1: kho, 2: tủ trực.
			//tuyennx_add_end_20181023
			};
			//tuyennx_edit_start_20181023 L2PT-5325
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu miễn phí " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			//tuyennx_edit_end_20181023
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
		});

		//Tao phieu hao phi vat tu
		$("#toolbarIddrug_hpvt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var loaikedonvthp = _loaikedonvt;//L2PT-51355

			var loaikedon = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "KEDONTHUOC_VATTU_HAOPHI");
			if(loaikedon != '0') {
				_loaikedon = loaikedon;
				loaikedonvthp =_loaikedon//L2PT-51355
			}


			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : loaikedonvthp,//L2PT-51355
				dichvuchaid : "",
				opt : "02D015",
				macdinh_hao_phi : 9,
				//tuyennx_add_start_20181023  L2HOTRO-11471
				loadkhotheo : loadkho
			// 0: kho và tủ trực, 1: kho, 2: tủ trực.
			//tuyennx_add_end_20181023
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D015", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu hao phí VT " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D015");
		});
		//Tao phieu thuoc tu truc
		$("#toolbarIddrug_7").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : 1,
				dichvuchaid : "",
				opt : "02D010" // tao phieu thuoc
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
		});
		//Tao phieu tra thuoc
		$("#toolbarIddrug_2").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : 1,
				dichvuchaid : "",
				opt : "02D014" // tao phieu thuoc
			};
			//dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D014","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,"Tạo phiếu trả thuốc",1300,590);
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D014", "divDlg", "manager.jsp?func=../noitru/NTU02D100_TraThuoc", paramInput, "Tạo phiếu trả thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D014");
		});
		
		//Tao phieu tra thuoc ko goi sang duoc L2PT-85844
		$("#toolbarIddrug_2_duoc").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : 1,
				TRATHUOC_DUOC : "1", 
				dichvuchaid : "",
				opt : "02D014" // tao phieu thuoc
			};
			//dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D014","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,"Tạo phiếu trả thuốc",1300,590);
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D014", "divDlg", "manager.jsp?func=../noitru/NTU02D100_TraThuoc", paramInput, "Tạo phiếu trả thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D014");
		});
		
		//Tao phieu chi dinh mau ko goi sang duoc L2PT-120710
		$("#toolbarIddrug_chidinhmau").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedon,
				dichvuchaid : "",
				KEMAU : "1", 
				opt : "02D010", // tao phieu thuoc
				loadkhotheo : 1// 0: kho và tủ trực, 1: kho, 2: tủ trực. - macdinh :2
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D010", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Chỉ định máu " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D010");
		});
		
		//Mua thuốc nhà thuốc
		$("#toolbarIddrug_dtnhathuoc").on("click", function(e) {
			EventUtil.setEvent("assignDrug_fail", function(e) {
				DlgUtil.close("dlgCDT");
			});
			_openDialogThuoc('02D019', 0, "Mua thuốc nhà thuốc");
		});
		$("#toolbarIddrug_phieuđinhuong").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				phongkhamdangkyid : "",
				maubenhphamid : "",
				loaikedon : 1,
				dichvuchaid : "",
				opt : "02D011",
				dinhduong : 1
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D011", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Phiếu tư vấn dinh dưỡng " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D011");
		});
		//Tao phieu vat tu
		$("#toolbarIddrug_3").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			//tuyennx_edit_start_20181023 L2PT-42
			var loadkho = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "KETHUOC_LOADKHO_VATTU");
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedonvt, //L2PT-51355
				dichvuchaid : "",
				loadkhotheo : loadkho, // 0: kho và tủ trực, 1: kho, 2: tủ trực.
				opt : "02D015"
			};
			//tuyennx_edit_end
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D015", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu vật tư " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D015");
		});
		//tuyennx_add_start_20181023 L2PT-14596
		$("#toolbarIddrug_3_tt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : _loaikedonvt, //L2PT-51355
				dichvuchaid : "",
				loadkhotheo : 2, // 0: kho và tủ trực, 1: kho, 2: tủ trực.
				opt : "02D015"
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D015", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, "Tạo phiếu vật tư " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D015");
		});
		//tuyennx_add_end_20181023 L2PT-14596
		//Tao phieu tra vat tu
		$("#toolbarIddrug_4").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				loaikedon : 1,
				dichvuchaid : "",
				opt : "02D016"
			};
			//dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+"02D016","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,"Tạo phiếu trả vật tư",1300,590);
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D016", "divDlg", "manager.jsp?func=../noitru/NTU02D100_TraThuoc", paramInput, "Tạo phiếu trả vật tư " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);//L2PT-4695
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D016");
		});
		//Tao phieu don thuoc mua ngoai
		//HaNv_051222: Thao tác menu trên mobie - L2PT-29886
		//$("#toolbarIddrug_6").on("click", function() {
		$("#toolbarIddrug_6").hammer().bind("tap", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				//L2PT-2841
				loaikedon : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'LOAIKEDON_MUANGOAI'), //tuyennx_edit_L2PT-6845,
				dichvuchaid : "",
				opt : "02D011"
			};
			var chchucnang = [ 'CHUCNANG_KEDON_MUANGOAI_NTU' ];
			var _chucnang = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', chchucnang.join('$'));
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D011", "divDlg", "manager.jsp?func=../noitru/" + _chucnang, paramInput, "Tạo đơn thuốc mua ngoài " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D011");
		});
		//L2PT-129939
		$("#toolbarIddrug_phar").hammer().bind("tap", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				maubenhphamid : "",
				//L2PT-2841
				loaikedon : 0,
				dichvuchaid : "",
				opt : "02D011"
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + "02D011", "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc_Pharmacy", paramInput, "Kê đơn PHARMACY " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgTaoPhieuThuoc" + "02D011");
		});
		//tao phieu phan ung thuoc
		$("#toolbarIdtreat_5").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenh_id : $("#hidKHAMBENHID").val(),
				maubenhpham_id : ""
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgPhanUngThuoc", "divDlg", "manager.jsp?func=../noitru/NTU02D007_PhieuThu_PU_Thuoc", paramInput, "Tạo phiếu Phản ứng thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 950, 340);
			DlgUtil.open("divDlgPhanUngThuoc");
		});
		//tao phieu cong kham
		$("#toolbarIdtreat_8").on("click", function() {
			if (!checkDoiTuongBN())
				return;
			paramInput = {
				chidinhdichvu : '1',
				loaidichvu : '2',
				loaiphieumbp : '3',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
				loaibenhanid : $("#hidLOAIBENHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $('#hidPHONGID').val(),
				bacsike : $("#labelHidBACSYKE").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 2, paramInput, "Phiếu công khám " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgDichVu");
		});
		//Dieu tri cat con
		$("#toolbarIdtreat_9").on("click", function() {
			var messageConfirm = "";
			var dieutricatcon = -1;
			var trangthaitiepnhan = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D021.DTCC", $("#hidHOSOBENHANID").val());
			if (trangthaitiepnhan == 1) {
				messageConfirm = "Bạn có muốn HỦY cắt cơn cho bệnh nhân điều trị này ko?";
				dieutricatcon = 0;
			} else {
				messageConfirm = "Bạn có muốn cắt cơn cho bệnh nhân điều trị này ko?";
				dieutricatcon = 1;
			}
			DlgUtil.showConfirm(messageConfirm, function(flag) {
				if (flag) {
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D021.CCUPD", $("#hidHOSOBENHANID").val() + "$" + dieutricatcon);
					if (fl == 1) {
						DlgUtil.showMsg("Cập nhật thành công!");
					} else {
						DlgUtil.showMsg("Xảy ra lỗi !");
					}
					//FormUtil.clearForm('inputForm',"");
				}
			});
		});
		//START -- hongdq --06/03/2018 -- Di ung thuoc
		$("#toolbarIdtreat_10").on("click", function() {
			paramInput = {
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				mabenhan : $("#hidMABENHAN").val(),
				tenbenhnhan : $("#hidTENBENHNHAN").val(),
				mabenhnhan : $("#hidMABENHNHAN").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDiungThuoc", "divDlg", "manager.jsp?func=../noitru/NTU02D083_DiUngThuoc", paramInput, "Dị ứng thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 600);
			DlgUtil.open("divDlgDiungThuoc");
		});
		//END -- hongdq --06/03/2018 -- Di ung thuoc

		//tao phieu van chuyen L2PT-61875
		$("#toolbarIdtreat_vc").on("click", function() {
			if (!checkDoiTuongBN())
				return;
			paramInput = {
				chidinhdichvu : '1',
				loaidichvu : '14',
				loaiphieumbp : '16',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
				loaibenhanid : $("#hidLOAIBENHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $('#hidPHONGID').val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 14, paramInput, "Phiếu vận chuyển " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgDichVu");
		});

		//Begin_HaNv_12042018: Dinh dưỡng
		$("#toolbarIdtreat_13").on("click", function() {
			paramInput = {
				type : 3,
				khambenhid : $("#hidKHAMBENHID").val(),
				modeView : 2
			};

							//L2PT-42569
							var _form = '';
							if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'NTU02D086_SANGLOCDINHDUONG40782') == '1') _form = '40782';

			dlgPopup = DlgUtil.buildPopupUrl("divDlgSangLocNguyCo", "divDlg", "manager.jsp?func=../noitru/NTU02D086_SangLocDinhDuong"
			+ _form //L2PT-42569
			, paramInput, "Sàng lọc và đánh giá dinh dưỡng " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 500);
			DlgUtil.open("divDlgSangLocNguyCo");
		});
		//END_HaNv_12042018

		$("#toolbarIdtreatdt_tdlm").on("click", function () {
			paramInput = {
				maubenhpham_id: "",
				khambenh_id: $("#hidKHAMBENHID").val()
			};
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid: rowData.KHAMBENHID,
				tiepnhanid: rowData.TIEPNHANID,
				hosobenhanid: rowData.HOSOBENHANID,
				benhnhanid: rowData.BENHNHANID,
				mabenhan: rowData.MABENHAN,
				mabenhnhan: rowData.MABENHNHAN,
				tenbenhnhan: rowData.TENBENHNHAN,
				maubenhpham_id: "",
				action: 'add'
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuchamsocTBH", "divDlg", "manager.jsp?func=../noitru/NTU02D092_PhieuChamsoc_Chaythan_BD", paramInput,
				"Tạo phiếu chăm sóc Chi tiết lọc máu " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 580);
			DlgUtil.open("divDlgPhieuchamsocTBH");
			//END -- HISL2TK-536 -- hongdq -- 22052018
		});

		//Tao phieu suat an
		$("#toolbarIdtreat_15").on("click", function() {
			if (!checkDoiTuongBN())
				return;
			paramInput = {
				chidinhdichvu : '1',
				loaidichvu : '12',
				loaiphieumbp : '11',
				benhnhanid : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
				loaibenhanid : $("#hidLOAIBENHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				subDeptId : $('#hidPHONGID').val(),
				bacsike : $("#labelHidBACSYKE").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 12, paramInput, "Phiếu suất ăn " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgDichVu");
		});
		//START--L2K74TW-615
		$("#toolbarIdtreat_16").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				phauthuatvienid : rowData.BACSYDIEUTRIID,
				benhnhanid : rowData.BENHNHANID
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgSoKetDieuTri", "divDlg", "manager.jsp?func=../noitru/NTU02D055_SoKetDieuTri", paramInput, "Sơ kết điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
			DlgUtil.open("divDlgSoKetDieuTri");
		});
		//START--L2K74TW-615
		//Tao phieu dieu tri
		//HaNv_051222: Thao tác menu trên mobie - L2PT-29886
		//$("#toolbarIdtreat_1").on("click", function() {
		$("#toolbarIdtreat_1").hammer().bind("tap", function() {
			if (!checkBeforeClickOnMenu())
				return;
			//START -- HISL2TK-297--hongdq-29052018
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			//END -- HISL2TK-297--hongdq-29052018
			var thoigianNhapvien = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D021.GET_TGVV", $("#hidBENHNHANID").val());
			paramInput = {
				khambenhId : $("#hidKHAMBENHID").val(),
				maubenhphamId : -1,
				subDeptId : $("#hidPHONGID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				hosobenhanId : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanId : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanId : $("#hidLOAITIEPNHANID").val(),
				//START -- HISL2TK-297--hongdq-29052018
				thoigian_vaovien : thoigianNhapvien[0].THOIGIANVAOVIEN,
				bacsydieutriid : rowData.BACSYDIEUTRIID
			//END -- HISL2TK-297--hongdq-29052018
			};
			//L2PT-23635
			if(form_phieudt_lv){
				dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT_LV", paramInput, "Tạo phiếu điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1330, 700);
			}else{
				dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT", paramInput, "Tạo phiếu điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1330, 700);
			}

			EventUtil.setEvent("divDlgPhieuDieuTri_onClose", function(name) {
				$('#tcDieuTri').ntu02d027_dt({
					_grdDieuTri : 'grdDieuTri',
					_khambenhid : $("#hidKHAMBENHID").val(),
					_benhnhanid : $("#hidBENHNHANID").val(),
					_thoigianvaovien : $("#hidTHOIGIANVAOVIEN").val(),
					_bacsidieutri : rowData.BACSYDIEUTRIID,
					_lnmbp : LNMBP_DieuTri,
					_modeView : _flgModeView, // =1 chi view; !=1 la update
					_hosobenhanid : ""
				});
			});
			DlgUtil.open("divDlgPhieuDieuTri");
		});
		//hunglv [HNI-BND]Thêm mới Form Giấy xác nhận bệnh nhân cấp cứu
		$("#toolbarIdtreat_cc").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			if ($("#hidLOAIBENHANID").val() == null || $("#hidLOAIBENHANID").val() == '' || $("#hidLOAIBENHANID").val() == 23) {
				DlgUtil.showMsg('Bệnh nhân chưa nhập viện');
				return;
			}
			var _benhnhanid = $("#hidBENHNHANID").val();
			var _khambenhid = $("#hidKHAMBENHID").val();
			var _hosobenhanid = $("#hidHOSOBENHANID").val();
			paramInput = {
				benhnhanid : _benhnhanid,
				khambenhid : _khambenhid,
				hosobenhanid : _hosobenhanid
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgInCC", "divDlg", "manager.jsp?func=../ngoaitru/NGT03K004_CapCuu", paramInput, "Giấy xác nhận cấp cứu " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 950, 350);
			DlgUtil.open("dlgInCC");
		});
		//end hunglv

		// L2PT-58694 start
		$("#toolbarIdbtnDDKTVHPL").on("click", function() {
			paramInput = {
				khoaid : _opts._deptId
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgTheoDoiNhanLuc", "divDlg", "manager.jsp?func=../noitru/NTU02D227_TheoDoiNhanLuc", paramInput, "Bảng theo dõi tình hình nhân lực điều dưỡng, kỹ thuật viện, hộ lý", 1300, 700);
			DlgUtil.open("dlgTheoDoiNhanLuc");
		});
		// L2PT-58694 end

		//L2PT-23570 start
		$("#toolbarIdtreat_bcyt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var _benhnhanid = $("#hidBENHNHANID").val();
			var _khambenhid = $("#hidKHAMBENHID").val();
			var _hosobenhanid = $("#hidHOSOBENHANID").val();
			paramInput = {
				benhnhanid : _benhnhanid,
				khambenhid : _khambenhid,
				hosobenhanid : _hosobenhanid
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgBaoCaoYTe", "divDlg", "manager.jsp?func=../noitru/NTU02D216_BaoCaoYTe", paramInput, "Tạo báo cáo y tế " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 800, 600);
			DlgUtil.open("dlgBaoCaoYTe");
		});
		//L2PT-23570 end
		//START -- L2PT-7663--DoanPV-19082019 , L2PT-26426
		$("#toolbarIdtreat_ttdd, #toolbarIdprint_ttdd").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var _hosobenhanid = $("#hidHOSOBENHANID").val();
					var _khambenhid = $("#hidKHAMBENHID").val();
					paramInput = {
						hosobenhanid : _hosobenhanid,
						khambenhid : _khambenhid,
						mode: _flgModeView //L2PT-27784
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgTTDD", "divDlg", "manager.jsp?func=../noitru/NTU02D126_PhieuTinhTrangDinhDuong", paramInput,
							"Phiếu đánh giá trình trạng dinh dưỡng (Trưởng thành)" + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1250, 600);
					DlgUtil.open("dlgTTDD");
				});
		//START -- L2PT-7665--DoanPV-22082019
		$("#toolbarIdtreat_ttdd_nhi, #toolbarIdprint_ttdd_nhi").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					var _hosobenhanid = $("#hidHOSOBENHANID").val();
					var _khambenhid = $("#hidKHAMBENHID").val();
					paramInput = {
						hosobenhanid : _hosobenhanid,
						khambenhid : _khambenhid,
						mode: _flgModeView //L2PT-27784
					};
					if (cf.NTU02D129_PHIEUDINHDUONG_NHI == "1") { //L2PT-36202
						dlgPopup = DlgUtil.buildPopupUrl("dlgTTDinhDuong", "divDlg", "manager.jsp?func=../noitru/NTU02D129_PhieuTinhTrangDinhDuong_Nhi_TBH", paramInput,
								"Phiếu đánh giá trình trạng dinh dưỡng - Nhi", $(document).width() * 0.8, $(window).height() * 0.9);
						DlgUtil.open("dlgTTDinhDuong");
					} else if (cf.NTU02D129_PHIEUDINHDUONG_NHI == "2") {//HaNv_180423: L2PT-37198
						dlgPopup = DlgUtil.buildPopupUrl("dlgTTDDNhiVPC", "divDlg", "manager.jsp?func=../noitru/NTU02D129_DinhDuong_NhiSNVPC", paramInput,
								"Phiếu đánh giá trình trạng dinh dưỡng (Nhi)", $(document).width() * 0.8, $(window).height() * 0.9);
						DlgUtil.open("dlgTTDDNhiVPC");
					}
					else{
						dlgPopup = DlgUtil.buildPopupUrl("dlgTTDDNhi", "divDlg", "manager.jsp?func=../noitru/NTU02D129_PhieuTinhTrangDinhDuong_Nhi", paramInput,
								"Phiếu đánh giá trình trạng dinh dưỡng (Nhi)", 1100, 600);
						DlgUtil.open("dlgTTDDNhi");
					}
				});
		// L2PT-11223 duonghn start: chuyen tu phong hanh chinh sang buong dieu tri
		//Begin_DoanPV_20200817 L2PT-25510 : Dinh dưỡng cho phụ nữ mang thai
		$("#toolbarIdtreatdt_18, #toolbarIdprint_pnmt").on(
				"click",
				function() {
					paramInput = {
						type : 2,
						khambenhid : $("#hidKHAMBENHID").val(),
						mode: _flgModeView //L2PT-27784
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgSLDDMT", "divDlg", "manager.jsp?func=../noitru/NTU02D159_SangLocDinhDuongMangThai", paramInput, "Sàng lọc dinh dưỡng cho phụ nữ mang thai " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
							1100, 600);
					DlgUtil.open("divDlgSLDDMT");
				});
		//End_DoanPV_20200817
		// L2PT-11223 duonghn end
		//Begin_HaNv_301220: Đánh giá tình trạng dinh dưỡng 18 tuổi, không mang thai  - L2PT-31735
		$("#toolbarIdtreatdt_18_1, #toolbarIdprint_18_1").on(
				"click",
				function() {
					paramInput = {
						khambenhid : $("#hidKHAMBENHID").val(),
						mode: _flgModeView //L2PT-27784
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgSLDD_BNT", "divDlg", "manager.jsp?func=../noitru/NTU02D086_SangLocDinhDuongBTN", paramInput,
							"Đánh giá tình trạng dinh dưỡng >=18 tuổi, không mang thai " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 600);
					DlgUtil.open("divDlgSLDD_BNT");
				});
		//End_HaNv_301220
		//START -- L2PT-9108
		$("#toolbarIdtreat_bgtpt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var _hosobenhanid = $("#hidHOSOBENHANID").val();
			var _khambenhid = $("#hidKHAMBENHID").val();
			paramInput = {
				hosobenhanid : _hosobenhanid,
				khambenhid : _khambenhid,
				tiepnhanid : $("#hidTIEPNHANID").val(),
				benhnhanid : $("#hidBENHNHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgBBBGPT", "divDlg", "manager.jsp?func=../noitru/NTU02D135_BB_Bangiao_TruocPT", paramInput, "Phiếu bàn giao trước phẫu thuật " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1130, 680);
			DlgUtil.open("dlgBBBGPT");
		});
		//START -- L2PT-9108
		$("#toolbarIdtreat_qlpt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgQLPT", "divDlg", "manager.jsp?func=../noitru/NTU02D137_QuanLyPhoiThai", paramInput, "Thông tin quản lý phôi thai " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 400);
			DlgUtil.open("dlgQLPT");
		});
		//START -- L2PT-9108
		$("#toolbarIdtreat_23").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgNDC", "divDlg", "manager.jsp?func=../noitru/NTU02D150_XetNghiemNongDoCon", paramInput, "Phiếu xét nghiệm nồng độ cồn " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 400);
			DlgUtil.open("dlgNDC");
		});
		//START -- L2PT-21577
		$("#toolbarIdtreat_24").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			if( cf.HIS_SHOW_THONGTIN_TUVONG == '1') {
				paramInput = {
					mabenhnhan : rowData.MABENHNHAN,
					tenbenhnhan : rowData.TENBENHNHAN,
					nghenghiep : rowData.TENNGHENGHIEP,
					diachi : rowData.DIACHI,
					namsinh : rowData.NAMSINH,
					khambenhid : rowData.KHAMBENHID,
					benhnhanid : rowData.BENHNHANID,
					chandoan : rowData.CHANDOANRAVIEN,
					ngaytiepnhan : rowData.NGAYTIEPNHAN,
					capnhat : '1',
					hosobenhanid : rowData.HOSOBENHANID
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K010_Tuvong", paramInput, "Thông tin tử vong " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 970, 700);
				DlgUtil.open("dlgXuTri");
			} else {
				paramInput = {
					hosobenhanid : rowData.HOSOBENHANID
				};
				var dlgPopup = DlgUtil.buildPopupUrl("divDlgGBT", "divDlg", "manager.jsp?func=../noitru/NTU02D166_GiayBaoTu", paramInput, "Giấy báo tử " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 900, 500);
				DlgUtil.open("divDlgGBT");
			}
		});
		// L2PT-21249 ttlinh start
		$("#toolbarIdfunction_32").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				mabenhnhan : rowData.MABENHNHAN,
				tenbenhnhan : rowData.TENBENHNHAN,
				nghenghiep : rowData.TENNGHENGHIEP,
				diachi : rowData.DIACHI,
				namsinh : rowData.NAMSINH,
				khambenhid : rowData.KHAMBENHID,
				benhnhanid : rowData.BENHNHANID,
				chandoan : rowData.CHANDOANRAVIEN,
				ngaytiepnhan : rowData.NGAYTIEPNHAN,
				capnhat : '1',
				hosobenhanid : rowData.HOSOBENHANID
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K010_Tuvong", paramInput, "Thông tin tử vong " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 900, 375);
			DlgUtil.open("dlgXuTri");
		});
		// L2PT-21249 end

		// L2PT-21249 ttlinh start
		$("#toolbarIdfunction_33").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			var paramInput = {
				khambenhid: $("#hidKHAMBENHID").val(),
				tiepnhanid: $("#hidTIEPNHANID").val(),
				hosobenhanid: $("#hidHOSOBENHANID").val(),
				mabenhan: $("#hidMABENHNHAN").val(),
				tenbenhnhan: rowData.TENBENHNHAN,
				tuoi: rowData.TUOI,
				diachi: rowData.DIACHI
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgBA", "divDlg", "manager.jsp?func=../noitru/NTU01H101_DayLaiBenhAn", paramInput, "HIS - Quản lý bệnh án " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
			DlgUtil.open("divDlgBA");
		});

		//Begin_HaNv_01072020: Chuyển đổi săn sóc tích cực - L2PT-22747
		$("#toolbarIdtreat_25").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				ngaytiepnhan : $("#hidTHOIGIANVAOVIEN").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgICU", "dlgICU", "manager.jsp?func=../noitru/NTU02D156_ChuyenDoiICU", paramInput, "Chuyển đổi săn sóc tích cực " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 460);
			DlgUtil.open("dlgICU");
		});
		//End_HaNv_01072020
		//Begin_DoanPV_20210414: BVTM-1012 Phiếu theo dõi chạy thận nhân tạo
		$("#toolbarIdtreat_26").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgTDCT", "dlgICU", "manager.jsp?func=../noitru/NTU02D181_TheoDoiChayThan", paramInput, "Phiếu theo dõi chạy thận nhân tạo " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1150, 550);
			DlgUtil.open("dlgTDCT");
		});
		//End_DoanPV_20210414
		//ladinhtuan BVTM-6402 15102021
		$("#toolbarIdtreat_28").on("click", function() {
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgSLDD_BDHCM", "divDlg", "manager.jsp?func=../noitru/NTU02D086_SangLocDinhDuongBDHCM", paramInput, "Đánh giá sàng lọc dinh dưỡng " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1400, 700);
			DlgUtil.open("divDlgSLDD_BDHCM");
		});
		//ladinhtuan BVTM-6402 15102021
		$("#toolbarIdtreat_29").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var myVar = {
				khambenhId : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				tiepnhanId : $("#hidTIEPNHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
				mabenhnhan : $("#hidMABENHNHAN").val(),
				hosobenhanId : $("#hidHOSOBENHANID").val(),
				loaibenhanId : $("#hidLOAIBENHANID").val(),
				phongId : $("#hidPHONGID").val(),
				trangthaikhambenh : $("#hidTRANGTHAIKHAMBENH").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT04K007_PhieuKhamGayMeHoiSuc", myVar, "KHÁM GÂY MÊ TRƯỚC PTTT " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 650);
			//dlgPopup.open();
			DlgUtil.open("dlgKham");
		});
		//Begin_DoanPV_20210414: BVTM-708 Phiếu hướng dẫn chế độ ăn bệnh lý
		$("#toolbarIdtreat_27").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgHDCDA", "divDlg", "manager.jsp?func=../noitru/NTU02D179_CheDoAnBenhLy", paramInput, "Phiếu hướng dẫn chế độ ăn bệnh lý " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 550);
			DlgUtil.open("dlgHDCDA");
		});
		//End_DoanPV_20210414
		$("#toolbarIdtreat_ttqtcm").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = {
				HOSOBENHANID : $("#hidHOSOBENHANID").val()
			};
			var dlgPopup = DlgUtil.buildPopupUrl("QuyTrinhChuyenMon", "QuyTrinhChuyenMon", "manager.jsp?func=../noitru/NTU02D142_QuyTrinhChuyenMon", par, "TT Quy trình chuyên môn " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 550);
			dlgPopup.open();
		});


		//L2PT-33388
		if (getCauHinh("NTU02D170_FORMPHIEU_CSNB") == "1") {
			$('#toolbarIdbtnTreat').next().append('<li data-external="{}" id="toolbarIdtreatdt_csnb"><a href="#"><i class="glyphicon glyphicon-dieutri"></i>&nbsp;&nbsp;Phiếu theo dõi và thực hiện kế hoạch chăm sóc người bệnh</a></li>')
		}
		$("#toolbarIdtreatdt_csnb").click( function() {
					var khambenhid =  $("#hidKHAMBENHID").val();
					var chucNang = 'CHAMSOCNB';
					var name = chucNang + '@' + khambenhid;
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU_CSNB&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
							screen.height + ',width=1200');
				});

		//L2PT-35354
		if (getCauHinh("NTU02D170_FORMPHIEU_TRUYEN_OXY") == "1") {
			$('#toolbarIdbtnTreat').next().append('<li data-external="{}" id="toolbarIdtreatdt_truyen_oxy"><a href="#"><i class="glyphicon glyphicon-dieutri"></i>&nbsp;&nbsp;Bảng theo dõi truyền Oxytoxin</a></li>')
		}
		$("#toolbarIdtreatdt_truyen_oxy").click( function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					 paramInput={
						PHIEU: rowData
						 , KHAMBENHID: $("#hidKHAMBENHID").val()
						 , CHUCNANG: 'TRUYEN_OXY'
					 };
					 var name = JSON.stringify(paramInput);
					var popup = window.open('manager.jsp?func=../noitru/NTU02D170_FORMPHIEU_THEODOI_TRUYEN_OXY&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
							screen.height + ',width=1200');
				});



		// Start ThemPhieu
		$("#toolbarIdtreat_themphieu").click(
				function() {
					let
					_loaiphieu = "NTU_DIEUTRI";
					if (type == "3") {
						_loaiphieu = "NGT_DIEUTRI";
					}
					let
					myVar = {
						KHAMBENHID : $("#hidKHAMBENHID").val(),
						BENHNHANID : $("#hidBENHNHANID").val(),
						TIEPNHANID : $("#hidTIEPNHANID").val(),
						MABENHNHAN : $("#hidMABENHNHAN").val(),
						HOSOBENHANID : $("#hidHOSOBENHANID").val(),
						MAUBENHPHAMID : 1111,
						DICHVUKHAMBENHID : 2222,
						KHOAID : opt._deptId,
						PHONGID : $("#hidPHONGID").val(),
						LOAI_PHIEU : _loaiphieu
					}
					dlgPopup = DlgUtil.buildPopupUrl("divDlgThemPhieu", "divDlg", "manager.jsp?func=../noitru/NTU02D204_ThemPhieu", myVar, "HIS-Thêm phiếu " + (thongtinbn==null?"":"(" + thongtinbn + ")"), window.innerWidth * 0.95,
							window.innerHeight * 0.95);
					DlgUtil.open("divDlgThemPhieu");
				});
		// End ThemPhieu
		//Beg_HaNv_300924: L2PT-105461
		$("#toolbarIdtreatdt_16").on("click", function() {
			paramInput = {
				maubenhpham_id : "",
				khambenh_id : $("#hidKHAMBENHID").val()
			};
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				tiepnhanid : rowData.TIEPNHANID,
				hosobenhanid : rowData.HOSOBENHANID,
				benhnhanid : rowData.BENHNHANID,
				mabenhan : rowData.MABENHAN,
				mabenhnhan : rowData.MABENHNHAN,
				tenbenhnhan : rowData.TENBENHNHAN,
				maubenhpham_id : "",
				action : 'add'
			};
			if(cf.NTU_CSCT_QTI == '1'){
				dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuchamsoc", "divDlg", "manager.jsp?func=../noitru/NTU01H059_PhieuChamsoc_ChaythanQTI", paramInput, "Tạo phiếu chăm sóc chạy thận " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 580);
			}else{
				dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuchamsoc", "divDlg", "manager.jsp?func=../noitru/NTU01H059_PhieuChamsoc_Chaythan", paramInput, "Tạo phiếu chăm sóc chạy thận " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 580);
			}
			DlgUtil.open("divDlgPhieuchamsoc");
		});
		$("#toolbarIdtreatdt_csct").on("click", function() {
			paramInput = {
				maubenhpham_id : "",
				khambenh_id : $("#hidKHAMBENHID").val()
			};
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				tiepnhanid : rowData.TIEPNHANID,
				hosobenhanid : rowData.HOSOBENHANID,
				benhnhanid : rowData.BENHNHANID,
				mabenhan : rowData.MABENHAN,
				mabenhnhan : rowData.MABENHNHAN,
				tenbenhnhan : rowData.TENBENHNHAN,
				maubenhpham_id : "",
				action : 'add'
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuchamsocTBH", "divDlg", "manager.jsp?func=../noitru/NTU02D092_PhieuChamsoc_chaythan_TBH", paramInput,
					"Tạo phiếu chăm sóc chạy thận BANT " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1300, 580);
			DlgUtil.open("divDlgPhieuchamsocTBH");
			//END -- HISL2TK-536 -- hongdq -- 22052018
		});
		//End_HaNv_300924
		//START -- L2PT-12600
		$("#toolbarIdtreat_dgbd").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgDGBD", "divDlg", "manager.jsp?func=../noitru/NTU02D143_PhieuDanhGiaBanDau", paramInput, "Phiếu đánh giá ban đầu người bệnh nhập viện " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1150, 600);
			DlgUtil.open("dlgDGBD");
		});
		EventUtil.setEvent("CLOSE_QuyTrinhChuyenMon", function(e) {
			DlgUtil.close("QuyTrinhChuyenMon");
		});
		//L2PT-23979 start
		$("#toolbarIdtreat_dgbd_d155").on("click",function() {
			if (!checkBeforeClickOnMenu())
				return;
			var NTU_PDGBD_VER2 = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_PDGBD_VER2');
			if (NTU_PDGBD_VER2 == '1') {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var ttKhac = getTTKhacPCSC2();
				var khambenhid = rowData.KHAMBENHID;
				var tenbenhnhan = rowData.TENBENHNHAN;
				var mabenhan = rowData.MABENHAN;
				var tuoi = ttKhac.tuoi;
				var gioi = ttKhac.gioi;
				var giuong = ttKhac.giuong;
				var phong = opt.subdept_name;
				var ngayvaovien = rowData.NGAYVAOKHOA;
				var bmi = ttKhac.bmi;
				var name = khambenhid + '@' + tenbenhnhan + '@' + mabenhan + '@' + tuoi + '@' + gioi + '@' + giuong + '@' + phong + '@' + ngayvaovien + '@' + bmi;
				var popup = window.open('manager.jsp?func=../noitru/NTU02D164_PDGBDBS&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' +
						screen.height + ',width=' + screen.width);
				popup.moveTo(0, 0);
				popup.onbeforeunload = function() {
					loadGrid();
				}
			} else {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var ttKhac = getTTKhacPCSC2();
				var par = {
					khambenhid : rowData.KHAMBENHID,
					tiepnhanid : rowData.TIEPNHANID,
					hosobenhanid : rowData.HOSOBENHANID,
					benhnhanid : rowData.BENHNHANID,
					mabenhan : rowData.MABENHAN,
					tenbenhnhan : rowData.TENBENHNHAN,
					tuoi : ttKhac.tuoi,
					gioi : ttKhac.gioi,
					giuong : ttKhac.giuong,
					chanDoan : ""
				};
				var dlgPopup = DlgUtil.buildPopupUrl("PhieuDanhGiaBanDau", "PhieuDanhGiaBanDau", "manager.jsp?func=../noitru/NTU02D155_PhieuDanhGiaBanDau", par, "Phiếu đánh giá ban đầu " + (thongtinbn==null?"":"(" + thongtinbn + ")"),1200, 500);
				dlgPopup.open();
			}
		});
		function getTTKhacPCSC2() {
			var obj = {
				tuoi : "",
				gioi : "",
				giuong : "",
				bmi : ""
			}
			obj.tuoi = getTuoiPCSC2();
			obj.gioi = $('#hidGIOITINH').val() == '1' ? 'Nam' : 'Nữ';
			obj.giuong = $('#lblGIUONG').text();
			obj.bmi = getBmi();
			return obj;
		}
		function getTuoiPCSC2() {
			// if ($('#hidNGAYSINH').val().length > 0) {
			// 	var namsinh = $('#hidNGAYSINH').val().substring(6,10);
			// 	var today = new Date();
			// 	var year = today.getFullYear();
			// 	return (year - namsinh) + '';
			// } else {
			// 	return "";
			// }
			var str = $('#lblNGAYSINH').text();
			var index1 = str.indexOf("(");
			var index2 = str.indexOf(")");
			var tuoi = str.substr(index1 + 1, index2 - index1 - 1);
			var index3 = tuoi.indexOf("Tuổi");
			if (index3 != -1) {
				return tuoi.substr(0, index3);
			}
			return tuoi;
		}
		function getBmi() {
			var obj = new Object();
			obj.khambenhid = $("#hidKHAMBENHID").val();
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D125.08", JSON.stringify(obj));
			if (result.length > 0) {
				var ttKhac = result[0];
				if (ttKhac.CANNANG !== "" && ttKhac.CHIEUCAO !== "" && ttKhac.CHIEUCAO !== "0") {
					var bmi = ttKhac.CANNANG / Math.pow(ttKhac.CHIEUCAO * 0.01, 2);
					return bmi.toFixed(2);
				} else {
					return "";
				}
			} else {
				return "";
			}
		}
		//L2PT-23979 end
		//thanh toan vien phi
		$("#toolbarIdhospitalfee_1").on("click", function() {
			if (!checkVPBeforeClickOnMenu())
				return;
			paramInput = {
				tiepnhanid : $("#hidTIEPNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgTTVP", "divDlg", "manager.jsp?func=../vienphi/VPI01T006_thanhtoanvienphi", paramInput, "Thanh toán viện phí " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1250, 600);
			DlgUtil.open("dlgTTVP");
		});
		//thong tin vien phi
		$("#toolbarIdhospitalfee_2").on("click", function() {
			if (!checkVPBeforeClickOnMenu())
				return;
			paramInput = {
				tiepnhanid : $("#hidTIEPNHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgThongTinVP", "divDlg", "manager.jsp?func=../vienphi/VPI01T005_thongtinvienphi", paramInput, "Thông tin viện phí " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1250, 620);
			DlgUtil.open("divDlgThongTinVP");
		});
		//Ra vien lich hen
		$("#toolbarIdfunction_7").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				mabenhnhan : $("#hidMABENHNHAN").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
				tenbenhnhan : rowData.TENBENHNHAN,
				namsinh : rowData.NAMSINH,
				nghenghiep : rowData.NGHENGHIEP,
				diachi : rowData.DIACHI,
				ngaytiepnhan : $("#hidTHOIGIANVAOVIEN").val(),
				//dannd 20210201 L2PT-33810
				benhnhanid : rowData.BENHNHANID,
				hosobenhanid : rowData.HOSOBENHANID,
				//HungND - L2PT-74949 - 240304
				tuyen: rowData.TENLOAIBHYT
			//end
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K007_Thongtin_Ravien", paramInput, "Ra viện, Lịch hẹn " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 380);
			DlgUtil.open("dlgXuTri");
		});
		//Lich su dieu tri
		$("#toolbarIdhistory_2").on("click", function() {
			if (!checkLSBeforeClickOnMenu())
				return;
			paramInput = {
				benhnhanId : $("#hidBENHNHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K025_LichSuDieuTri", paramInput, "Lịch sử điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1320, 610);
			DlgUtil.open("dlgLichSuDieuTri");
		});
		//Lich su benh an
		$("#toolbarIdhistory_1").on("click", function() {
			if (!checkLSBeforeClickOnMenu())
				return;
			paramInput = {
				benhnhanId : $("#hidBENHNHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val(),
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuBenhAn", "divDlg", "manager.jsp?func=../noitru/NTU02D042_LichSuBenhAn", paramInput, "Lịch sử bệnh án " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1320, 610);
			DlgUtil.open("dlgLichSuBenhAn");
		});
		//START TICKET BVTM-983 phiếu xét nghiệm ngoài viện
		$("#toolbarIdfunction_23").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					//START L2PT-1124
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					paramInput = {
						khambenhid : $("#hidKHAMBENHID").val(),
						benhnhanId : $("#hidBENHNHANID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val(),
						ma_bhyt : rowData.MA_BHYT
					//START L2PT-1124
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgPhieuXetNghiemGuiNgoai", "divDlg", "manager.jsp?func=../ngoaitru/NGT04K001_PhieuChiDinh_Ngoai", paramInput,
							"Phiếu chỉ định xét nghiệm ngoài viện " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 630);
					DlgUtil.open("dlgPhieuXetNghiemGuiNgoai");
				});
		//END TICKET BVTM-983
		//BVTM-1851
		$("#toolbarIdfunction_24").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgTVGTTM", "divDlg", "manager.jsp?func=../noitru/NTU02D182_TuVanGiaiThichTrongMo", paramInput, "Phiếu tư vấn - giải thích trong mổ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 550);
			DlgUtil.open("divDlgTVGTTM");
		});
		// cap giay chung sinh
		$("#toolbarIdfunction_9").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			//START L2PT-1124
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			//L2PT-9093
			if (check_ktba_giaycs) {
				var check_par = [];
				check_par.push({
					"name" : "[0]",
					"value" : $("#hidTIEPNHANID").val()
				});
				var checkktdt = jsonrpc.AjaxJson.getOneValue("NTU02D049.CHECKKTBA", check_par);
				if (checkktdt != "0") {
					return DlgUtil.showMsg("Bệnh án đã kết thúc, không thể cập nhật!");;
				}
			}
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				ma_bhyt : rowData.MA_BHYT
			//START L2PT-1124
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgGiayChungSinh", "divDlg", "manager.jsp?func=../noitru/NTU02D036_Giaychungsinh", paramInput, "Giấy chứng sinh " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 550);
			DlgUtil.open("dlgGiayChungSinh");
		});
		//ductx bvtm-7061
		$("#toolbarIdfunction_96").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			//START L2PT-1124
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgKhamSoSinh", "divDlg", "manager.jsp?func=../noitru/NTU007_KhamSoSinh", paramInput, "Khám sơ sinh " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 550);
			DlgUtil.open("dlgKhamSoSinh");
		});
		//end bvtm-7061
		// Theo doi san phu 6 gio sau de
		$("#toolbarIdfunction_10").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
				bacsydieutriid : rowData.BACSYDIEUTRIID,
				benhnhanid : rowData.BENHNHANID,
				thoigianvaovien : $("#hidTHOIGIANVAOVIEN").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgTheoDoiSanPhu", "divDlg", "manager.jsp?func=../noitru/NTU02D064_TheoDoiSanPhu", paramInput, "Theo dõi sản phụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
			DlgUtil.open("dlgTheoDoiSanPhu");
		});
		//Lich su phac do dieu tri
		$("#toolbarIdfunction_11").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			paramInput = {
				khambenhid : rowData.KHAMBENHID,
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgLichsuPhacdoDT", "divDlg", "manager.jsp?func=../noitru/NTU02D079_LichsuPhacdoDT", paramInput, "Lịch sử phác đồ điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 800, 580);
			DlgUtil.open("dlgLichsuPhacdoDT");
		});
		//Beg_HaNv_261023: Dien bien benh nhan - L2PT-57867
		$("#toolbarIdf_dbbenh").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				hosobenhanid : $("#hidHOSOBENHANID").val(),
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgDBBenh", "divDlg", "manager.jsp?func=../noitru/NTU02D229_DienBienBN", paramInput, "Diễn biến bệnh nhân", 1200, 600);
			DlgUtil.open("dlgDBBenh");
		});
		//End_HaNv_261023
		//Beg_DoanPV_20240223: Dien bien benh nhan - L2PT-74416
		$("#toolbarIdf_lichsudieutri").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				benhnhanid : $("#hidBENHNHANID").val(),
				mabenhnhan : $("#hidMABENHAN").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgLSBenh", "divDlg", "manager.jsp?func=../noitru/NTU02D231_LichSuDieuTri", paramInput, "Lịch sử điều trị", 1200, 600);
			DlgUtil.open("dlgLSBenh");
		});
		//End_DoanPV_20240223
		// Danh sach kham
		$("#toolbarIdbtnStart").on(
				"click",
				function(e) {
					var param = "";
					param = "&dtTungay=" + nvl($("#txtTG_NHAPVIEN_TU").val(), '-1') + "&dtDenngay=" + nvl($("#txtTG_NHAPVIEN_DEN").val(), '-1') + "&khoaid=" + opt._deptId + "&phongid=" +
							$('#cboPHONGID').val() + "&loaitiepnhanid=0";
					if (opt.hospital_id == 947) { //L2PT-6606
						window.open('manager.jsp?func=../noitru/ThongBaoBenhNhan_ICD&showMode=dlg' + param, '',
								'width=1000,height=500,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
					}
					//L2PT-41901
					else if (opt.hospital_id == 1133) { // thật Thật  996 QNI-TAMTHAN
						window.open('manager.jsp?func=../noitru/ThongBaoBenhNhan996&showMode=dlg' + param, '',
								'width=1000,height=500,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
					}
					else {
						window.open('manager.jsp?func=../noitru/ThongBaoBenhNhan&showMode=dlg' + param, '',
								'width=1000,height=500,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
					}
				});
		// nut goi kham cho yhct quang ngai (rut gon code, chi lay code tuong ung cau hinh KBH_CHEDO_GOIKHAM = 2) tu man NGT02K001_KB_MHC
		$('#toolbarIdbtnCall').click(function() {
			if (!checkBeforeClickOnMenu())
				return;
			var objGoiKham = getTTGoiKham($("#hidKHAMBENHID").val());
			if (objGoiKham) {
				var sophongkham = objGoiKham.SOPHONG;
				var _texts = "Mời bệnh nhân " + $("#hidTENBENHNHAN").val() + " vào " + sophongkham;
				var timeOut = getCauHinh("HIS_API_TIMEOUT") == 0 ? 5000 : getCauHinh("HIS_API_TIMEOUT");
				var modeGoiKham = getCauHinh("HIS_API_MODE");
				goiKhamGG(_texts, modeGoiKham, timeOut);
			}
		});

		// START L2PT-58694

		// END L2PT-58694
		// thong tin tai nan thuong tich
		$("#toolbarIdfunction_5").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanId : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgTaiNanThuongTich", "divDlg", "manager.jsp?func=../noitru/NTU02D035_Thongtintainanthuongtich", paramInput, "Thông tin tai nạn thương tích " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 460);
			DlgUtil.open("dlgTaiNanThuongTich");
		});
		// Bien ban kiem diem tu vong HaiNM BVTM-1951
		$("#toolbarIdfunction_30").on(
				"click",
				function() {
					if (!checkBeforeClickOnMenu())
						return;
					paramInput = {
						khambenhid : $("#hidKHAMBENHID").val(),
						hosobenhanid : $("#hidHOSOBENHANID").val()
					};
					dlgPopup = DlgUtil.buildPopupUrl("dlgBienBanKiemDiemTuVong", "divDlg", "manager.jsp?func=../ngoaitru/NGT04K002_BienBanKiemDiemTuVong", paramInput, "Biên bản kiểm điểm tử vong " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
							1000, 460);
					DlgUtil.open("dlgBienBanKiemDiemTuVong");
				});
		//report phieu chi dinh CLS
		$("#toolbarIdprint_1").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			if (opt.hospital_id == 922) {
				//mo phieu xet nghiem
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'i_loainhommaubenhpham',
					type : 'String',
					value : 1
				} ];
				CommonUtil.openReportGet('window', "PHIEU_CLSC_922", "pdf", par);
				//mo phieu cdha
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'i_loainhommaubenhpham',
					type : 'String',
					value : 2
				} ];
				CommonUtil.openReportGet('window', "PHIEU_CLSC_922", "pdf", par);
				//mo phieu pttt
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'i_loainhommaubenhpham',
					type : 'String',
					value : 5
				} ];
				CommonUtil.openReportGet('window', "PHIEU_CLSC_922", "pdf", par);
			} else {
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				} ];
				openReport('window', "PHIEU_CLSC", "pdf", par);
			}
		});
		//Sổ bệnh án ngoại trú_YHCT_1941 docx  L2PT-5320 start
		$("#toolbarIdprint_135").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			} ];
			//openReport('window', "BENHAN_NGOAITRUYHOCCOTRUYEN_QD4069_A4", "pdf", par);
			var rpName = "BENHAN_NGOAITRUYHOCCOTRUYEN_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
			CommonUtil.inPhieu('window', "BENHAN_NGOAITRUYHOCCOTRUYEN_QD4069_A4", 'rtf', par, rpName);
		});

		// L2PT-12582
		$("#toolbarIdprint_pkck").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];
			openReport('window', "PHIEU_KHAMCHUYENKHOA_NOIVIEN", "pdf", par);
		});

		// L2PT-12582
		$("#toolbarIdprint_gmhs").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			}];
			var rpName = "PHIEU_GAYMEHOISUC" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
			CommonUtil.inPhieu('window', "PHIEU_GAYMEHOISUC", 'xlsx', par, rpName);
		});

		// L2PT-23661 start
		$("#toolbarIdprint_nntv").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];
			openReport('window', "RPT_CHANDOAN_NGUYENNHANTUVONG", "pdf", par);
		});

		$("#toolbarIdprint_xinve").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];
			openReport('window', "RPT_TOMTAT_THONGTIN_NGUOIBENH_NANGXINVE", "pdf", par);
		});
		// L2PT-23661 end

		//Sổ bệnh án nội trú_YHCT_1941 docx
		$("#toolbarIdprint_136").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			} ];
			//openReport('window', "BENHAN_NOITRUYHOCCOTRUYEN_QD4069_A4", "rtf", par);
			var rpName = "BENHAN_NOITRUYHOCCOTRUYEN_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
			CommonUtil.inPhieu('window', "BENHAN_NOITRUYHOCCOTRUYEN_QD4069_A4", 'rtf', par, rpName);
		});
		$("#toolbarIdprint_999").on("click", function() {
			CommonUtil.sendAllEmr2($("#hidHOSOBENHANID").val(), '1');
		});
		//Sổ bệnh án nội trú nhi_YHCT_1941 docx
		$("#toolbarIdprint_137").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			} ];
			//openReport('window', "BENHAN_NOINHIYHOCCOTRUYEN_QD4069_A4", "rtf", par);
			var rpName = "BENHAN_NOINHIYHOCCOTRUYEN_QD4069_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
			CommonUtil.inPhieu('window', "BENHAN_NOINHIYHOCCOTRUYEN_QD4069_A4", 'rtf', par, rpName);
		});
		// L2PT-5320 end
		// so benh an PHCN_YHCT_3730 L2PT-10960 start
		$("#toolbarIdprint_138").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			}, {
				name : 'loaibenhanid',
				type : 'String',
				value : $("#hidLOAIBENHANID").val()
			} ];
			openReport('window', "BAN_PHUCHOICHUCNANG", "pdf", par);
			// var rpName = "BAN_PHUCHOICHUCNANG" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
			// CommonUtil.inPhieu('window', "BAN_PHUCHOICHUCNANG", 'rtf', par, rpName);
		});
		// L2PT-10960 end
		// so benh an PHCN_YHCT_3730 L2PT-10960 start
		$("#toolbarIdprint_139").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			}, {
				name : 'loaibenhanid',
				type : 'String',
				value : $("#hidLOAIBENHANID").val()
			} ];
			openReport('window', "BAN_SOPHUCHOICHUCNANG", "pdf", par);
			// var rpName = "BAN_SOPHUCHOICHUCNANG" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
			// CommonUtil.inPhieu('window', "BAN_SOPHUCHOICHUCNANG", 'rtf', par, rpName);
		});
		// L2PT-10960 end
		//in bang ke
		$("#toolbarIdprint_4").on("click", function() {
			if (!checkVPBeforeClickOnMenu())
				return;
			if (isPrintBk && $('#hidTRANGTHAITIEPNHAN').val() == '0') {
				DlgUtil.showMsg('Bệnh nhân chưa kết thúc bệnh án nên chưa thể in bảng kê');
				return;
			}
			var _loaitiepnhanid = $("#hidLOAITIEPNHANID").val();
			var dtbnid = $("#hidDOITUONGBENHNHANID").val();
			vienphi_tinhtien.inBangKe($("#hidTIEPNHANID").val(), dtbnid, _loaitiepnhanid);
		});
		//in bang ke bhyt
		$("#toolbarIdprint_bhyt").on("click", function() {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			} ];
			if(cf.HIS_TACHBANGKE_DIEUTRINOITRU == '1') {
				openReport('window', "NTU001_BKCPKCBBHYT_QD6556_DOCMOI_A4", "pdf", par);
			} else {
				openReport('window', "NGT001_BKCPKCBBHYT_QD6556_DOCMOI_A4", "pdf", par);
			}
		});
		//in bang ke vien phí
		$("#toolbarIdprint_vp").on("click", function() {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			} ];
			openReport('window', "NGT001_BKCPKCBVP_QD6556_DOCMOI_A4", "pdf", par);
		});
		//in bang ke dich vu khac
		$("#toolbarIdprint_dvkhac").on("click", function() {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			} ];
			openReport('window', "NGT001_BKCPKCBDICHVUKHAC_QD6556_A4", "pdf", par);
		});
		//ductx - BVTM-963 in phieu su dung tu truc
		$("#toolbarIdprint_99").on("click", function() {
			var sql_par = [];
			if ($('#hidKHAMBENHID').val() == "-1") {
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in!');
				return false;
			}
			sql_par.push({
				"name" : "[0]",
				"value" : $('#hidKHAMBENHID').val()
			});
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
			var rows = $.parseJSON(data);
			if (rows.length == 0) {
				DlgUtil.showMsg('Bệnh nhân này chưa kê thuốc từ tủ trực hôm nay!');
				return false;
			}
			for (var i = 0; i < rows.length; i++) {
				var ngayHienTai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
				if (ngayHienTai == rows[i].NGAYMAUBENHPHAM) {
					var par = [ {
						name : 'maubenhphamid',
						type : 'String',
						value : rows[i].MAUBENHPHAMID
					} ];
					openReport('window', "DUC_PHIEUSD_THUOCTT_BVBD", "pdf", par);
				}
			}
		});
		//in bang ke chi phi dieu tri noi tru mien phi
		$("#toolbarIdprint_ntmp").on("click", function() {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			} ];
			openReport('window', "NGT001_BKCPKCBMIENPHI_QD6556_DOC_A4", "pdf", par);
		});
		//in bang ke chua thanh toan
		$("#toolbarIdprint_33").on("click", function() {
			if (!checkVPBeforeClickOnMenu())
				return;
			var _loaitiepnhanid = $("#hidLOAITIEPNHANID").val();
			var dtbnid = $("#hidDOITUONGBENHNHANID").val();
			vienphi_tinhtien.inBangKeCTT($("#hidTIEPNHANID").val(), dtbnid, _loaitiepnhanid);
		});
		//START L2PT-452
		$("#toolbarIdprint_34").on("click", function() {
			if (!checkVPBeforeClickOnMenu())
				return;
			var _loaitiepnhanid = $("#hidLOAITIEPNHANID").val();
			var dtbnid = $("#hidDOITUONGBENHNHANID").val();
			vienphi_tinhtien.inBangKe3455($("#hidTIEPNHANID").val(), dtbnid, _loaitiepnhanid);
		});
		//END L2PT-452
		$("#toolbarIdprint_bktheokhoa").on("click", function () {
			if (!checkVPBeforeClickOnMenu())
				return;

			var paramInput = {
				mode: '1'
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgChonKhoa", "divDlg", "manager.jsp?func=../noitru/NTU01H121_ChonKhoa", paramInput, "HIS - Chọn khoa " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 550, 200);
			DlgUtil.open("divDlgChonKhoa");
			//xử lý sự kiện event.
			EventUtil.setEvent("ChonKhoa", function (e) {
				DlgUtil.close("divDlgChonKhoa");
				var par = [{
					name: 'tiepnhanid',
					type: 'String',
					value: $("#hidTIEPNHANID").val()
				}, {
					name: 'i_khoaid',
					type: 'String',
					value: e.res
				}];
				openReport('window', "NGT001_BKCPKCBBHYT_QD6556_THEOKHOA_A4", "pdf", par);
			});
		});
		//in bang ke
		$("#toolbarIdprint_10").on("click", function() {
			if (!checkVPBeforeClickOnMenu())
				return;
			var flag = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T004.10", $("#hidTIEPNHANID").val());
			var dtbnid = $("#hidDOITUONGBENHNHANID").val();
			vienphi_tinhtien.inPhoiVP(dtbnid, $("#hidTIEPNHANID").val(), 'NGT001_BKCPKCB_HAOPHI_01BV_QD3455_A4');
		});
		//Phiếu hẹn khám.
		$("#toolbarIdprint_11").on("click", function() {
			if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
				return;
			}
			var obj = new Object();
			obj.KHAMBENHID = $("#hidKHAMBENHID").val();
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.HK", JSON.stringify(obj));
			if (ret > 0) {
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				} ];
				openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
			} else {
				DlgUtil.showMsg("Không có thông tin hẹn khám của bệnh nhân này.");
			}
		});
		//report in don thuoc
		$("#toolbarIdprint_3").on("click", function() {
			var sql_par = [];
			if ($('#hidKHAMBENHID').val() == "-1") {
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
				return false;
			}
			sql_par.push({
				"name" : "[0]",
				"value" : $('#hidKHAMBENHID').val()
			});
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
			var rows = $.parseJSON(data);
			if (rows.length == 1) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rows[0].MAUBENHPHAMID
				} ];
				openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
			} else if (rows.length > 1) {
				paramInput = {
					data : rows,
					khambenhid: $("#hidKHAMBENHID").val()//L2PT-99945
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgInDonThuoc", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc", paramInput, "IN ĐƠN THUỐC " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 420, 400);
				DlgUtil.open("dlgInDonThuoc");
			} else {
				DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
			}
		});
		// L2PT-26520 start
		//In Don thuoc HEN
		$("#toolbarIdprint_hen").on("click", function() {

			var sql_par=[];
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc HEN.');
				return false;
			}
			sql_par.push({"name":"[0]","value":$('#hidKHAMBENHID').val()});

			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
			var rows=$.parseJSON(data);
			if(rows.length == 1){
				var par = [ {
	   				name : 'maubenhphamid',
	   				type : 'String',
	   				value : rows[0].MAUBENHPHAMID
	   			}];
	   			openReport('window', "NGT006_DONTHUOCHEN_17DBV01_TT052016_A5_924", "pdf", par);
			}else if(rows.length > 1){
				paramInput={
					data : rows,
					dthen: '1'
				};
				dlgPopup=DlgUtil.buildPopupUrl("dlgInDonThuoc","divDlg","manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc",paramInput,"IN ĐƠN THUỐC HEN " + (thongtinbn==null?"":"(" + thongtinbn + ")"),420,260);
				DlgUtil.open("dlgInDonThuoc");
			}else{
				DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
			}
		});

		//In Don thuoc COPD.
		$("#toolbarIdprint_copd").on("click", function() {

			var sql_par=[];
			if($('#hidKHAMBENHID').val() == "-1"){
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc COPD.');
				return false;
			}
			sql_par.push({"name":"[0]","value":$('#hidKHAMBENHID').val()});

			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K034.INDT", sql_par);
			var rows=$.parseJSON(data);
			if(rows.length == 1){
				var par = [ {
	   				name : 'maubenhphamid',
	   				type : 'String',
	   				value : rows[0].MAUBENHPHAMID
	   			}];
	   			openReport('window', "NGT006_DONTHUOCCOPD_17DBV01_TT052016_A5_924", "pdf", par);
			}else if(rows.length > 1){
				paramInput={
						data : rows,
						dtcopd: '1'
					};
				dlgPopup=DlgUtil.buildPopupUrl("dlgInDonThuoc","divDlg","manager.jsp?func=../ngoaitru/NGT02K034_InDonThuoc",paramInput,"IN ĐƠN THUỐC COPD " + (thongtinbn==null?"":"(" + thongtinbn + ")"),420,260);
				DlgUtil.open("dlgInDonThuoc");
			}else{
				DlgUtil.showMsg('Bệnh nhân chưa được cấp thuốc.');
			}
		});
		//L2PT-26520 end
		$("#toolbarIdprint_tdsdgb").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			}];
			openReport('window', "BANG_THEODOI_SUDUNG_GIUONGBENH", "pdf", par);
		});
		//L2PT-26520 end
		$("#toolbarIdprint_dmmm").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];
			openReport('window', "PHIEU_XN_DUONGMAU_MAOMACH", "pdf", par);
		});
		//L2PT-26520 end
		$("#toolbarIdprint_ckdtnt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			}];
			openReport('window', "GIAYCAMKET_DIEUTRINOITRU", "pdf", par);
		});
		//L2PT-27520 end
		//L2PT-68634 start
		$("#toolbarIdprint_gcdcnntcl").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [{
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			},
			{
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			}];
			openReport('window', "RPT_GIAYCAMDOAN_NOPTIENCHENH_NTU", "pdf", par);
		});
		//L2PT-68634 end
		//L2PT-68635 start
		$("#toolbarIdprint_gcdtphdtnt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [{
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			},
			{
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			}];
			openReport('window', "GIAYCAMDOAN_TTHVINH", "pdf", par);
		});
		//L2PT-68635 end
		//L2PT-68636 start
		$("#toolbarIdprint_gcdcnptvgm").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [{
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			},
			{
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			}];
			openReport('window', "GIAYCAMDOAN_CHAPNHANPHAUTHUAT_GAYME_TTHVINH", "pdf", par);
		});
		//L2PT-68636 end
		//L2PT-68637 start
		$("#toolbarIdprint_ptvvgtcbn").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [{
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			},
			{
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			}];
			openReport('window', "RPT_TUVAN_GIAITHICH_NTU", "pdf", par);
		});
		//L2PT-68637 end
		//L2PT-115263 start
		$("#toolbarIdprint_phieuchamsoc23").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
				var obj = new Object();
				obj.HOSOBENHANID = rowData.HOSOBENHANID;
				obj.TIEPNHANID = rowData.TIEPNHANID;
				obj.KHAMBENHID = rowData.KHAMBENHID;
				obj.MAHOSOBENHAN = rowData.MABENHAN;
				obj.BENHNHANID = rowData.BENHNHANID;
				obj.KHOAID = opt._deptId;
				obj.PHONGID = $("#hidPHONGID").val();
				obj.LOAI_PHIEU = "NTU_HANHCHINH";
				obj.LOAICHAMSOC = "CHAMSOCCAPHAIBA";
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
			var dlgPopup = DlgUtil.buildPopupUrl("divDlgThemPhieu", "divDlg", "manager.jsp?func=../noitru/NTU02D204_ThemPhieu", obj,
				"HIS-Thêm phiếu " + (thongtinbn == null ? "" : "(" + thongtinbn + ")"), window.innerWidth * 0.95, window.innerHeight * 0.95);
			DlgUtil.open("divDlgThemPhieu");
		});
		//L2PT-115263 end
		
		//L2PT-68638 start
		$("#toolbarIdprint_bvctsddv").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [{
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			},
			{
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			}];
			openReport('window', "RPT_VANBANCHAPTHUAN_SUDUNGDICHVU_NTU", "pdf", par);
		});
		//L2PT-68638 end
		
		//report in don thuoc không toa
		$("#toolbarIdprint_159").on("click", function() {
			if ($('#hidKHAMBENHID').val() == "-1") {
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in đơn thuốc.');
				return false;
			}
			var myVar = {
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				mabenhnhan : $("#hidMABENHAN").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
				maubenhphamid : "",
				action : "Add"
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgCDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K044_CapThuocK", myVar, "Chỉ định thuốc không thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 800, 520);
			DlgUtil.open("dlgCDT");
		});
		//START DoanPV_20200910 L2PT-27257 -- in phiếu công khai y lệnh cho toàn bộ BN trong khoa
		$("#toolbarIdprint_161").on("click", function() {
			var par = [ {
				name : 'tungay',
				type : 'String',
				value : $("#txtTG_NHAPVIEN_TU").val()
			}, {
				name : 'denngay',
				type : 'String',
				value : $("#txtTG_NHAPVIEN_DEN").val()
			}, {
				name : 'bacsyid',
				type : 'String',
				value : $("#cboBACSIID").val()
			}, {
				name : 'khoaid',
				type : 'String',
				value : opt._deptId
			}, {
				name : 'phongid',
				type : 'String',
				value : $("#cboPHONGID").val()
			} ];
			openReport('window', "PHIEU_CONGKHAI_YLENH_A4", "pdf", par);
		});
		//START L2HOTRO-10373 -- in phieu benh nhan lao
		$("#toolbarIdprint_21").on("click", function() {
			var sql_par = [];
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}
			paramInput = {
				tiepnhanid : $("#hidTIEPNHANID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgInThuocBNLao", "divDlg", "manager.jsp?func=../noitru/NTU02D104_In_BN_LAO", paramInput, "Danh sách phiếu in bệnh nhân lao " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 350, 400);
			DlgUtil.open("dlgInThuocBNLao");
		});
		//END L2HOTRO-10373
		//phieu ra vien
		$("#toolbarIdprint_5").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			//L2PT-51878 start
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (opt.hospital_id == '902' && rowData.KETQUADIEUTRIID == 5) {
					DlgUtil.showMsg('Bệnh nhân xử trí tử vong, không cho in giấy ra viện!');
					return;
				}
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'i_hid',
					type : 'String',
					value : opt.hospital_id
				}, {
					name : 'i_sch',
					type : 'String',
					value : opt.db_schema
				} ];
				if (opt.hospital_id == "965" && $("#HINHTHUCRAVIENID").val() == '7') {
					DlgUtil.showMsg("Bệnh nhân xử trí tử vong, không cho in giấy ra viện.");
					return;
				}
				//START L2HOTRO-12615
				var data_CH = '';
				var data_CH_DH = '';
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'NTU_BDHCM_12615_DOCX' + ";" + "NTU_PRINT_DOC_DKTNN");
				if (data_ar != null && data_ar.length > 0) {
					data_CH = data_ar[0].NTU_BDHCM_12615_DOCX;
					data_CH_DH = data_ar[0].NTU_PRINT_DOC_DKTNN;
				}
				if (opt.hospital_id == "965" && data_CH == '1') {
					var rpName = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
					CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", 'docx', par, rpName);
				} else if (opt.hospital_id == "939" && data_CH_DH == '1') { // L2PT-2615 DKDHTNN: XUAT docx
					var rpName = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5_939" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
					CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5_939", 'docx', par, rpName);
				} else {
					if (opt.hospital_id == "919") { // TTKHA: XUAT EXCEL
						var rpName = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'xlsx';
						CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", 'xlsx', par, rpName);
					} else {
						openReport('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", "pdf", par);
					}
				}
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
			//L2PT-51878 end
		});
		//Phiếu ra viện dạng doc
		$("#toolbarIdprint_5_doc").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (opt.hospital_id == '902' && rowData.KETQUADIEUTRIID == 5) {
					DlgUtil.showMsg('Bệnh nhân xử trí tử vong, không cho in giấy ra viện!');
					return;
				}
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'i_hid',
					type : 'String',
					value : opt.hospital_id
				}, {
					name : 'i_sch',
					type : 'String',
					value : opt.db_schema
				} ];
				// L2PT-85558 start
				var rpFileType = 'docx';
				if (cf.NTU_LOAI_GIAYRAVIEN_DOC && cf.NTU_LOAI_GIAYRAVIEN_DOC != '0') {
					rpFileType = cf.NTU_LOAI_GIAYRAVIEN_DOC;
				}
				var rpName = "NTU009_GIAYRAVIEN_01BV01_QD4069_A5" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + rpFileType;
				CommonUtil.inPhieu('window', "NTU009_GIAYRAVIEN_01BV01_QD4069_A5", rpFileType, par, rpName);
				// L2PT-85558 end
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//phieu ra vien da chien L2PT-7970 start
		$("#toolbarIdprint_172").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "NTU009_GIAYRAVIEN_DACHIEN_A5", "pdf", par);
		});
		// L2PT-7970 end
		$("#toolbarIdprint_8").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					khambenhid : _khambenhid
				};
				var url = "manager.jsp?func=../noitru/NTU01H028_InPhieuCongKhaiThuoc";
				var popup = DlgUtil.buildPopupUrl("divDlgPCKT", "divDlg", url, paramInput, "In phiếu công khai thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 268);
				popup.open("divDlgPCKT");
			}
		});

		$("#toolbarIdprintca_8").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					khambenhid : _khambenhid,
					kyca : '1'
				};
				var width = '505';
				var height= '268';
				var _rptCode = 'DUC033_PHIEUCONGKHAI_THUOC_V2';
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
				if (data_ar[0].BNKY > '0') {
					width = _width;
					height = _height;
				}
				var url = "manager.jsp?func=../noitru/NTU01H028_InPhieuCongKhaiThuoc";
				var popup = DlgUtil.buildPopupUrl("divDlgPCKT", "divDlg", url, paramInput, "In phiếu công khai thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), width, height);
				popup.open("divDlgPCKT");
			}
		});

		$("#toolbarIdprint_8_2").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "DUC033_PHIEUCONGKHAI_THUOC_MAU2_1216", "pdf", par);
		});

		$("#toolbarIdprint_156").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					khambenhid : _khambenhid,
					tiepnhanid : $("#hidTIEPNHANID").val()
				};
				var url = "manager.jsp?func=../noitru/NTU02D136_InPhieuTheoDoiDieuTri";
				var popup = DlgUtil.buildPopupUrl("divDlgPTDDTNT", "divDlg", url, paramInput, "In phiếu theo dõi điều trị nội trú " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 268);
				popup.open("divDlgPTDDTNT");
			}
		});
		$("#toolbarIdprint_22").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					khambenhid : _khambenhid
				};
				var url = "manager.jsp?func=../noitru/NTU01H028_InPhieuCongKhaiVatTu";
				var popup = DlgUtil.buildPopupUrl("divDlgPCKVT", "divDlg", url, paramInput, "In phiếu công khai vật tư " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 268);
				popup.open("divDlgPCKVT");
			}
		});
		$("#toolbarIdprint_ckyltn").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var paramInput = {
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				khambenhid : $("#hidKHAMBENHID").val()
			};
			var url = "manager.jsp?func=../noitru/NTU02D202_InCongKhaiYLenhTheoNgay";
			var popup = DlgUtil.buildPopupUrl("divDlgPCKT", "divDlg", url, paramInput, "In phiếu công khai y lệnh theo ngày " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 268);
			popup.open("divDlgPCKT");
		});
		// bien ban kiem diem tu vong - L2PT-10860
		$("#toolbarIdprint_bbkdtv").on("click", function () {
			if (!checkBeforeClickOnMenu())
				return;
			//L2PT-51878
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (opt.hospital_id == '902' && $("#hidKETQUADIEUTRIID").val() != 5) {
					DlgUtil.showMsg('Bệnh nhân xử trí tử vong mới in được giấy tử vong!');
					return;
				} else {
					var par = [{
						name: 'hosobenhanid',
						type: 'String',
						value: $("#hidHOSOBENHANID").val()
					},{
						name : 'i_khambenhid',
						type : 'String',
						value : $("#hidKHAMBENHID").val()
					},{
						name : 'khambenhid',
						type : 'String',
						value : $("#hidKHAMBENHID").val()
					} ];
					if (cf.NTU_IN_KIEMDIEM_TUVONG == '1') {
						openReport('window', "NTU02_PHIEUKIEMDIEMTUVONG_41BV01", "pdf", par);
					} else {
						openReport('window', "BIENBAN_TUVONG", "pdf", par);
					}
				}
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}

		});

		//giấy báo tử
		$("#toolbarIdprint_gbt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [{
				name: 'hosobenhanid',
				type: 'String',
				value: $("#hidHOSOBENHANID").val()
			},{
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			},{
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "NTU_PHIEUIN_GIAYBAOTU", "pdf", par);
		});
		// Giấy cam kết PTTT - L2PT-12081
		$("#toolbarIdprint_CAMKETPTTT").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "NTU027_GIAYCAMDOANCHAPNHANPTTTVAGAYMEHS_03BV01_QD4069_A4", "pdf", par);
		});
		// Phiếu công khai thuốc và thủ thuật - L2PT-12085
		//START L2PT-23856
		$("#toolbarIdprint_201").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (rowData.HINHTHUCRAVIENID == null || rowData.HINHTHUCRAVIENID == '') {
					DlgUtil.showMsg('Bệnh nhân chưa xử trí');
				} else {
					paramInput = {
						khambenhid : rowData.KHAMBENHID,
						tiepnhanid : rowData.TIEPNHANID,
						hosobenhanid : rowData.HOSOBENHANID,
						benhnhanid : rowData.BENHNHANID,
						mabenhan : rowData.MABENHAN,
						mabenhnhan : rowData.MABENHNHAN,
						tenbenhnhan : rowData.TENBENHNHAN,
						namsinh : rowData.NAMSINH,
						nghenghiep : rowData.TENNGHENGHIEP,
						diachi : rowData.DIACHI,
						capnhat : '1',
						xutri : rowData.HINHTHUCRAVIENID,
						ngaytiepnhan : rowData.NGAYTIEPNHAN
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgHenKham", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K008_Thongtin_Lichkham", paramInput, "Thông tin lịch hẹn " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 900, 300);
					DlgUtil.open("divDlgHenKham");
				}
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//END L2PT-23856
		$("#toolbarIdprint_congkhaithuoctt").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val()
			};
			var url = "manager.jsp?func=../noitru/NTU02D210_InPhieuCongKhaiThuocTT";
			var popup = DlgUtil.buildPopupUrl("divDlgPTDDTNT", "divDlg", url, paramInput, "In phiếu công khai thuốc thủ thuật " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 268);
			popup.open("divDlgPTDDTNT");
		});
		$("#toolbarIdprint_179").on("click", function() {
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			CommonUtil.openReportGet('window', "RPT_GIAYCAMKETXN_BETAHCG", "pdf", par);
		});
		$("#toolbarIdprint_180").on("click", function() {
			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			CommonUtil.openReportGet('window', "RPT_GIAYCAMDOANPHUTHU_CTSCANNER", "pdf", par);
		});
		//tuyendv start
		$("#toolbarIdprint_thtt").on("click", function() {
				var paramInput={
						khoaid		: opt._deptId,
						loaitiepnhanid	: type
					};
				var url = "manager.jsp?func=../noitru/NTU01H032_InPhieuVatTuTieuHao";
				var popup =DlgUtil.buildPopupUrl("divDlgVTTH","divDlg",url,paramInput,"In sổ vật tư tiêu hao tủ trực" + (thongtinbn==null?"":"(" + thongtinbn + ")"),505,500);
				popup.open("divDlgVTTH");
		});

		$("#toolbarIdprint_sttt").on("click", function() {
				var paramInput={
						khoaid		: opt._deptId,
						loaitiepnhanid	: type
					};
				var url = "manager.jsp?func=../noitru/NTU01H033_InPhieuThuocTuTruc";
				var popup =DlgUtil.buildPopupUrl("divDlgTHUOC","divDlg",url,paramInput,"In sổ thuốc tủ trực" + (thongtinbn==null?"":"(" + thongtinbn + ")"),505,500);
				popup.open("divDlgTHUOC");
		});
		//tuyendv end
		$("#toolbarIdprintca_153").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var sql_par = [];
				sql_par.push({
					"name" : "[0]",
					value : $("#hidLOAIBENHANID").val()
				});
				var _report_code = jsonrpc.AjaxJson.getOneValue('BACT.GET_REPORT', sql_par);
				var _param = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'loaibenhanid',
					type : 'String',
					value : $("#hidLOAIBENHANID").val()
				}, {
					name : 'benhnhanid',
					type : 'String',
					value : $("#hidBENHNHANID").val()
				}, {
					name : 'RPT_CODE',
					type : 'String',
					value : _report_code
				} ];
				_kyCaRpt(_param);
			}
		});
		$("#toolbarIdprint_16").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					dept_id : opt._deptId,
					khambenhid : _khambenhid
				};
				var url = "manager.jsp?func=../noitru/NTU02D098_ThoiGianDialogTHYL";
				var popup = DlgUtil.buildPopupUrl("divDlgTime", "divDlg", url, paramInput, "Phiếu tổng hợp y lệnh " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 600, 500);
				popup.open("divDlgTime");
			}
		});
		$("#toolbarIdprint_thtvt").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					dept_id : opt._deptId,
					khambenhid : _khambenhid,
					mode : 'th_tvt'
				};
				var url = "manager.jsp?func=../noitru/NTU02D098_ThoiGianDialogTHYL";
				var popup = DlgUtil.buildPopupUrl("divDlgTime", "divDlg", url, paramInput, "Phiếu tổng hợp thuốc vật tư " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 600, 500);
				popup.open("divDlgTime");
			}
		});
		//L2PT-6463 START
		if (inYLTONG) {
			$("#toolbarIdprint_171").on("click", function() {
				var _khambenhid = $("#hidKHAMBENHID").val();
				if (_khambenhid != null && _khambenhid != -1) {
					var paramInput = {
						dept_id : opt._deptId,
						khambenhid : _khambenhid
					};
					var url = "manager.jsp?func=../noitru/NTU02D196_DialogTHYLTONG";
					var popup = DlgUtil.buildPopupUrl("divDlgTime", "divDlg", url, paramInput, "Phiếu tổng hợp y lệnh tổng " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 600, 500);
					popup.open("divDlgTime");
				}
			});
		}

		//HungND - L2PT-63845
		$("#toolbarIdprint_ckdvtdg2").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					khambenhid : _khambenhid,
					tiepnhanid : $("#hidTIEPNHANID").val(),
					rpt_type : '2'
				};
				var url = "manager.jsp?func=../noitru/NTU01H039_InPhieuCongKhaiDV2";
				var popup = DlgUtil.buildPopupUrl("divDlgPCKDV", "divDlg", url, paramInput, "In Phiếu CKDV treo đầu giường " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 268);
				popup.open("divDlgPCKDV");
				return;
			}
		});
		//End HungND - L2PT-63845

		// L2PT-6463 END
		//in phieu cong khai dich vu
		$("#toolbarIdprint_12").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					khambenhid : _khambenhid,
					tiepnhanid : $("#hidTIEPNHANID").val()
				};
				// tu ky hai duong. popup rieng. jira: L2PT-11420
				// update a sy: bv my phuoc
				if ([ "32620", "34380" ].includes(opt.hospital_id + "")) {
					var url = "manager.jsp?func=../noitru/NTU01H039_InPhieuCongKhaiDV2";
					var popup = DlgUtil.buildPopupUrl("divDlgPCKDV", "divDlg", url, paramInput, "In phiếu công khai dịch vụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 400);
					popup.open("divDlgPCKDV");
					return;
				}
				// if (opt.hospital_id==996||opt.hospital_id==902||opt.hospital_id==944||opt.hospital_id==915||opt.hospital_id==932||opt.hospital_id==965||opt.hospital_id==957){
				// HungND - L2PT-68627 - Fix height Popup
				var _isPopup = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "NTU_INPHIEU_CONGKHAI");
				if (_isPopup == '1') {
					var url = "manager.jsp?func=../noitru/NTU01H039_InPhieuCongKhaiDV";
					var popup = DlgUtil.buildPopupUrl("divDlgPCKDV", "divDlg", url, paramInput, "In phiếu công khai dịch vụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 400);
					popup.open("divDlgPCKDV");
				} else if (_isPopup == '2') {
					var url = "manager.jsp?func=../noitru/NTU01H039_InPhieuCongKhaiDV2";
					var popup = DlgUtil.buildPopupUrl("divDlgPCKDV", "divDlg", url, paramInput, "In phiếu công khai dịch vụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 400);
					popup.open("divDlgPCKDV");
					return;
				} else {
					var par = [{
						name: 'khambenhid',
						type: 'String',
						value: _khambenhid
					}];
					openReport('window', "PHIEU_CONGKHAI_DICHVU", "pdf", par);
				}
			}
		});
		//START L2PT-16198
		$("#toolbarIdprint_12_1").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if(_khambenhid != null && _khambenhid != -1){
				var paramInput={
					khambenhid		: _khambenhid,
					tiepnhanid 		: $("#hidTIEPNHANID").val()
				};
				if (opt.hospital_id==902||opt.hospital_id==944||opt.hospital_id==915||opt.hospital_id==932||opt.hospital_id==965||opt.hospital_id==957){
					var url = "manager.jsp?func=../noitru/NTU01H039_InPhieuCongKhaiDV2";
					var popup =DlgUtil.buildPopupUrl("divDlgPCKDV","divDlg",url,paramInput,"In phiếu công khai dịch vụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"),505,268);
					popup.open("divDlgPCKDV");
				}
				else
				{
					var par = [{
						name : 'khambenhid',
						type : 'String',
						value : _khambenhid
					}];
					openReport('window', "PHIEU_CONGKHAI_DICHVU", "pdf", par);
				}
			}
		});

		//START L2PT-47212
		$("#toolbarIdprint_kimboi").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			var _tiepnhanid = $("#hidTIEPNHANID").val();
			if(_khambenhid != null && _khambenhid != -1 && _tiepnhanid != null && _tiepnhanid != -1){
				var par = [{
					name : 'khambenhid',
					type : 'String',
					value : _khambenhid,
				},{
					name : 'tiepnhanid',
					type : 'String',
					value : _tiepnhanid
				}];
				openReport('window', "PHIEU_CONGKHAI_THUTHUAT", "pdf", par);
			}
		});

		$("#toolbarIdprint_157").on("click", function() {
			var param = {
				khambenhid : $('#hidKHAMBENHID').val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgXuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K058_Thongtin_nghiduong", param, 'Thông tin nghỉ hưởng BHXH ' + (thongtinbn==null?"":"(" + thongtinbn + ")"), 800, 380);
			DlgUtil.open("dlgXuTri");
		});
		//END L2PT-16198
		//START L2PT-20552
		$("#toolbarIdprint_158").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (false) {
					DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
					return;
				} else {
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : rowData.KHAMBENHID
					} ];
					//openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
					var rpName = "GIAYHENKHAMLAI_" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
					CommonUtil.inPhieu('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", 'docx', par, rpName);
				}
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//END L2PT-20552
		//START L2PT-25127
		$("#toolbarIdprint_rvbnct").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : rowData.KHAMBENHID
				} ];
				openReport('window', "NTU009_GIAYRAVIEN_CHUYENTUYEN_01BV01_QD4069_A5", "pdf", par);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//END L2PT-25127
		//START -- HISL2TK-245-- hongdq 20180402
		//in phieu kham benh vao vien khi nhap BN truc tiep noi tru
		$("#toolbarIdprint_13").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var par = [{
					name: 'khambenhid',
					type: 'String',
					value: rowData.KHAMBENHID
				}];
				if (inPHieuKBChung) {
					_openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
				} else {
					// start jira L2PT-948
					// mau in cho nam luong son
					if (opt.hospital_id == "1103") {
						_openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
					}
					// end jira L2PT-948
					else {
						if ($("#hidDOITUONGBENHNHANID").val() == 3) {
							_openReport(par, "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'pdf');
						} else {
							_openReport(par, "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'pdf');
						}
					}
				}
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//END -- HISL2TK-245-- hongdq 20180402
		//END -- HISL2TK-245-- hongdq 20180402
		$("#toolbarIdprint_13_2").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : rowData.KHAMBENHID
				} ];
				_openReport(par, "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});

		//Phiếu khám bệnh vào viện ck mat L2PT-27727
		$("#toolbarIdprint_13_mat").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var par = [{
					name: 'khambenhid',
					type: 'String',
					value: rowData.KHAMBENHID
				}];
				_openReport(par, "NGT005_PHIEUKBVAOVIEN_CK_MAT_A4", 'pdf');
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//Phiếu khám bệnh vào viện ck tmh
		$("#toolbarIdprint_13_tmh").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var par = [{
					name: 'khambenhid',
					type: 'String',
					value: rowData.KHAMBENHID
				}];
				_openReport(par, "NGT005_PHIEUKBVAOVIEN_CK_TMH_A4", 'pdf');
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//Phiếu khám bệnh vào viện ck rhm
		$("#toolbarIdprint_13_rhm").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var par = [{
					name: 'khambenhid',
					type: 'String',
					value: rowData.KHAMBENHID
				}];
				_openReport(par, "NGT005_PHIEUKBVAOVIEN_CK_RHM_A4", 'pdf');
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//in phieu kham benh vao vien khi nhap BN truc tiep noi tru
		$("#toolbarIdprint_13_word").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : rowData.KHAMBENHID
				} ];
				if (inPhieuKBChung) {
					CommonUtil.inPhieu('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'docx', par, 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4.docx');
				} else {
					// start jira L2PT-948
					// mau in cho nam luong son
					if (opt.hospital_id == "1103") {
						CommonUtil.inPhieu('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'docx', par, 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4.docx');
					}
					// end jira L2PT-948
					else {
						if ($("#hidDOITUONGBENHNHANID").val() == 3) {
							CommonUtil.inPhieu('window', "NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4", 'docx', par, 'NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4.docx');
						} else {
							CommonUtil.inPhieu('window', "NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4", 'docx', par, 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4.docx');
						}
					}
				}
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//END -- HISL2TK-245-- hongdq 20180402
		//2019 -01-10 - duongnv
		//in bảng tóm tắt hồ sơ bện án quân nhân
		$("#toolbarIdprint_151").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				if (selRowId != null && selRowId != '') {
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					if (opt.hospital_id == 1216) {
						if (rowData.MA_BHYT != null && rowData.MA_BHYT.substr(3, 2) == "97") {
							var i_tiepnhanid = $("#hidTIEPNHANID").val();
							var par = [ {
								name : 'i_tiepnhanid',
								type : 'String',
								value : i_tiepnhanid
							} ];
							openReport('window', "RPT_TOMTATHOSOBENHAN_QUANNHAN", "pdf", par);
						} else {
							return DlgUtil.showMsg('Bệnh nhân ' + rowData.TENBENHNHAN + ' không phải quân nhân.');
						}
					}
				}
			}
		});
		//end
		//START L2PT-1283
		$("#toolbarIdprint_152").on("click", function() {
			var _inType = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "NTU_INPHIEU_TRUYENMAU_BND");
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var i_tiepnhanid = $("#hidTIEPNHANID").val();
				var par = [ {
					name : 'i_tiepnhanid',
					type : 'String',
					value : i_tiepnhanid
				} ];
				if (_inType == '1') {
					openReport('window', "RPT_GIAYCAMKET_TRUYENMAU_INLAI_951", "pdf", par);
				} else {
					openReport('window', "RPT_GIAYCAMKET_TRUYENMAU_951", "pdf", par);
				}
			}
		});
		//START L2PT-1283
		// L2PT-8630 start
		$("#toolbarIdprint_174").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "PHIEUTHEODOINGHIEN_MATUYTONGHOP", "pdf", par);
		});
		$("#toolbarIdprint_175").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "BANGKIEM_TRUOCTIEMCHUNG_SOSINH", "pdf", par);
		});
		$("#toolbarIdprint_176").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "PHIEUKETQUA_NGHIENMATUY", "pdf", par);
		});
		$("#toolbarIdprint_177").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "PHIEUTHEODOINGHIEN_THUOCPHIEN", "pdf", par);
		});
		// L2PT-8630 end
		//START L2PT-1720
		$("#toolbarIdprint_153").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var par = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'loaibenhanid',
					type : 'String',
					value : $("#hidLOAIBENHANID").val()
				}, {
					name : 'benhnhanid',
					type : 'String',
					value : $("#hidBENHNHANID").val()
				} ];
				var sql_par = [];
				sql_par.push({
					"name" : "[0]",
					value : $("#hidLOAIBENHANID").val()
				});
				var _report_code = jsonrpc.AjaxJson.getOneValue('BACT.GET_REPORT', sql_par);

				//openReport('window', _report_code, "pdf", par);
				//L2PT-43732
				var _cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'BAN_IN_DREPORT');
				if(_cauhinh == '1') {
					par.push({
						name: 'RPT_CODE',
						type: 'String',
						value: _report_code
					});
					CommonUtil.openReportEmr(par, false);
				}else{
					openReport('window', _report_code, "pdf", par);
				}

			}
		});
		//START L2PT-1720
		//START L1PT-997
		$("#toolbarIdprint_154").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				} ];
				openReport('window', "REPORT_PHIEU_SANGLOC_DINHDUONG", "pdf", par);
			}
		});
		//START L2PT-5628
		$("#toolbarIdprint_155").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				} ];
				openReport('window', "PHIEU_HUYTHUOC_TAIKHOA", "pdf", par);
			}
		});
		//START L1PT-997
		//In giấy chứng nhận nghỉ việc hưởng BHXH -- L2HOTRO-11892
		$("#toolbarIdprint_14").on(
				"click",
				function() {
					var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
					if (selRowId != null && selRowId != '') {
						var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
						var par = [ {
							name : 'khambenhid',
							type : 'String',
							value : rowData.KHAMBENHID
						} ];
						if (("1216,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227").indexOf(opt.hospital_id) >= 0 && $("#hidDOITUONGBENHNHANID").val() == 1 && rowData.MA_BHYT != null &&
								rowData.MA_BHYT.indexOf("97") == 3) {
							openReport('window', "RPT_GIAYNGHI_QUANNHAN_BHXH", "pdf", par);
						} else {
							openReport('window', "RPT_GIAYNGHI_BHXH", "pdf", par);
						}
					} else {
						DlgUtil.showMsg('Chưa chọn bệnh nhân');
						return;
					}
				});
		//END -- L2HOTRO-11892
		//export tờ điều trị
		$("#toolbarIdprint_9").on("click", function() {
			var _benhnhanid = $("#hidBENHNHANID").val();
			var _khoaid = $("#hidKHOAID").val();
			var par = [ {
				name : 'i_benhnhanid',
				type : 'String',
				value : _benhnhanid
			}, {
				name : 'i_khoaid',
				type : 'String',
				value : _khoaid
			}, {
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			//Beg_HaNv_170823: In tờ điều trị YHCT - L2PT-50089
			sql_par = [];
			sql_par.push({
				"name" : "[0]",
				"value" : _khoaid
			});
			var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D027.LOAIKHOA", sql_par);
			var row2 = JSON.parse(data2)[0];
			var loaikhoa = row2.ORG_TYPE;
			if (cf.HIS_DHYTB_INPDT_KHOAYHCT == '1' && cf.HIS_DHYTB_INPDT_KHOAID == loaikhoa) {
				openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_YHCT_A4_ALL", "pdf", par);
			} else {
				openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ALL", "pdf", par);
			}
			//End_HaNv_170823
		});
		// BVTM-7762
		$("#toolbarIdfunction_29, #toolbarIdprint_bktc").on("click", function() {
			var selRowId =  $('#'+_gridId).jqGrid ('getGridParam', 'selrow');
			if(selRowId != null && selRowId > 0){
				paramInput={
					khambenhid : $("#hidKHAMBENHID").val(),
					tenbenhnhan : $("#hidTENBENHNHAN").val()//L2PT-14794
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgBKTC_TE", "divDlg", "manager.jsp?func=../noitru/NTU02D203_BangKiemTC_TE", paramInput, "Bảng kiểm tiêm chủng đối với trẻ sơ sinh " + (thongtinbn==null?"":"(" + thongtinbn + ")"), window.innerWidth*0.85,window.innerHeight*0.85);
				DlgUtil.open("dlgBKTC_TE");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//START -- HISL2TK-1005
		$("#toolbarIdprint_17").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var _benhnhanid = $("#hidBENHNHANID").val();
				var _khambenhid = $("#hidKHAMBENHID").val();
				var _hosobenhanid = $("#hidHOSOBENHANID").val();
				var _self = this;
				paramInput = {
					benhnhanid : _benhnhanid,
					khambenhid : _khambenhid,
					hosobenhanid : _hosobenhanid,
					lnmbp : LNMBP_XetNghiem,
					ingop : '1'//In gộp cùng phiếu
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In phiếu XN " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 600);
				DlgUtil.open("divDlgDeleteXN");
			}
		});
		//END -- HISL2TK-1005
		//START -- HISL2TK-1005
		$("#toolbarIdprint_182").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var _benhnhanid = $("#hidBENHNHANID").val();
				var _khambenhid = $("#hidKHAMBENHID").val();
				var _hosobenhanid = $("#hidHOSOBENHANID").val();
				paramInput = {
					benhnhanid : _benhnhanid,
					khambenhid : _khambenhid,
					hosobenhanid : _hosobenhanid,
					lnmbp : LNMBP_CDHA,
					ingop : '1'//In gộp cùng phiếu
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In phiếu CĐHA " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 600);
				DlgUtil.open("divDlgDeleteXN");
			}
		});
		//END -- HISL2TK-1005

		//START -- HISL2TK-1085
		$("#toolbarIdprint_18").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var i_tiepnhanid = $("#hidTIEPNHANID").val();
				var i_dtbn_id = $("#hidDOITUONGBENHNHANID").val();
				var i_loaitiepnhanid = $("#hidLOAITIEPNHANID").val();
				inBangKeLPLSO(i_tiepnhanid, i_dtbn_id, i_loaitiepnhanid);
			}
		});
		//START -- HISL2TK-1085
		$("#toolbarIdprint_20").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var i_tiepnhanid = $("#hidTIEPNHANID").val();
				var par = [ {
					name : 'i_tiepnhanid',
					type : 'String',
					value : i_tiepnhanid
				} ];
				openReport('window', "PHIEU_DIEUTRI_LAO", "pdf", par);
			}
		});
		$("#toolbarIdprint_30").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (rowData.HINHTHUCRAVIENID != '6') {
					DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
					return;
				} else {
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : rowData.KHAMBENHID
					} ];
					//START L2HOTRO-12615
					var pars = [ 'NTU_BDHCM_12615_DOCX' ];
					var data_CH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
					if (data_CH == '1') {
						var rpName = "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
						CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'rtf', par, rpName);
					} else if (opt.hospital_id == "1111") { // DK LAN: Ham khac;
						openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4_BHYT_ND146_1111", "pdf", par);
					} else {
						openReport('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", "pdf", par);
					}
				}
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//STRART DoanPV-20200819 L2PT-25763
		$("#toolbarIdprint_160").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				if (rowData.HINHTHUCRAVIENID != '6') {
					DlgUtil.showMsg('Bệnh nhân không phải xử trí chuyển viện, In phiếu chỉ với những bệnh nhân chuyển viện');
					return;
				} else {
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : rowData.KHAMBENHID
					} ];
					var rpName = "NGT003_GIAYCHUYENTUYEN_TT14_A4" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'rtf';
					CommonUtil.inPhieu('window', "NGT003_GIAYCHUYENTUYEN_TT14_A4", 'rtf', par, rpName);
				}
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//END DoanPV-20200819
		//START -- L2PT-29569--DoanPV-20201111
		$("#toolbarIdprint_162").on("click", function() {
			var par = [ {
				name : 'i_hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			} ];
			CommonUtil.openReportGet('window', "PHIEU_LOCMAU", "pdf", par);
		});
		//START -- L2PT-30126--DoanPV-20201118
		$("#toolbarIdprint_163").on("click", function() {
			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			CommonUtil.openReportGet('window', "RPT_CHUNGNHAN_PHAUTHUAT", "pdf", par);
		});
		//L2PT-65617 start
		$("#toolbarIdprint_163_docx").on("click", function() {
			var par = [ {
				name : 'i_khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			var rpName = "RPT_CHUNGNHAN_PHAUTHUAT" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + 'docx';
			CommonUtil.inPhieu('window', "RPT_CHUNGNHAN_PHAUTHUAT", 'docx', par, rpName);
		});
		//L2PT-65617 end
		//START -- L2PT-32115--DoanPV-20201218
		$("#toolbarIdprint_164").on("click", function() {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : $("#hidTIEPNHANID").val()
			} ];
			CommonUtil.openReportGet('window', "PHIEU_THANHTOAN_RAVIEN_A4", "pdf", par);
		});
		//START -- L2PT-32115--DoanPV-20201218
		$("#toolbarIdprint_165").on("click", function() {
			var par = [ {
				name : 'hosobenhanid',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			}, {
				name : 'loaibenhanid',
				type : 'String',
				value : $("#hidLOAIBENHANID").val()
			}, {
				name : 'benhnhanid',
				type : 'String',
				value : $("#hidBENHNHANID").val()
			} ];
			CommonUtil.openReportGet('window', "PHIEU_KHAM_CD_PHCN_10284", "pdf", par);
		});
		//L2PT-10835 start
		$("#toolbarIdprint_178").on("click", function() {
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			var obj = {
				PARAM: par
				,REPORTCODE: "RPT_THUCHIEN_KYTHUATPHCN"
			};
			var dlgPopup = DlgUtil.buildPopupUrl(
				"NTU02D211_PhieuInExcelPDF",
				"NTU02D211_PhieuInExcelPDF",
				"manager.jsp?func=../noitru/NTU02D211_PhieuInExcelPDF",
				obj,
				"Chọn loại in " + (thongtinbn==null?"":"(" + thongtinbn + ")"),
				800,
				120);
			dlgPopup.open();
		});
		// L2PT-10835 end
		//START BVTM-31
		$("#toolbarIdprint_203").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var par = [ {
					name : 'i_khoaid',
					type : 'String',
					value : opt._deptId
				}, {
					name : 'i_khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				} ];
				openReport('window', "REPORT_PHIEUAN_BENHNHAN_THEOKHOA", "pdf", par);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//END L2PT-23856
		//START BVTM-1448
		$("#toolbarIdprint_210").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var par = [ {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				} ];
				openReport('window', "GIAY_CD_CHAY_THANNHANTAO", "pdf", par);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//END BVTM-1448
		//START L2PT-5902
		$("#toolbarIdprint_bkdsdv").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var par = [ {
					name : 'tiepnhanid',
					type : 'String',
					value : $("#hidTIEPNHANID").val()
				} ];
				openReport('window', "BANGKE_DOISOAT_DICHVU", "pdf", par);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//END L2PT-5902
		//START -- L2PT-31571
		$("#toolbarIdprint_166").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				paramInput = {
					benhnhanid : $("#hidBENHNHANID").val(),
					khambenhid : $("#hidKHAMBENHID").val(),
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					lnmbp : 4
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In phiếu ĐT các khoa " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 600);
				DlgUtil.open("divDlgDeleteXN");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		// start jira L2PT-167
		$("#toolbarIdprint_phieuduoccatcom").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId == null) {
				DlgUtil.showMsg("Chưa chọn bệnh nhân");
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "PHIEUDUOCCATCOM", "pdf", par);
		});
		// end jira L2PT-167
		$("#toolbarIdprint_169").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var paramInput = {
					hosobenhanid : $("#hidHOSOBENHANID").val(),
					khambenhid : $("#hidKHAMBENHID").val()
				};
				var url = "manager.jsp?func=../noitru/NTU01H028_InPhieuCongKhaiThuocHTSS";
				var popup = DlgUtil.buildPopupUrl("divDlgPCKT", "divDlg", url, paramInput, "In phiếu công khai thuốc " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 268);
				popup.open("divDlgPCKT");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		// start jira L2PT-167
		$("#toolbarIdprint_170").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId == null) {
				DlgUtil.showMsg("Chưa chọn bệnh nhân");
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "THETHEODOIDIUNG_10284", "pdf", par);
		});
		// end jira L2PT-167
		// start jira L2PT-3573
		$("#toolbarIdprint_gcntt").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId == null) {
				DlgUtil.showMsg("Chưa chọn bệnh nhân");
				return;
			}
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "NTU_GIAYCHUNGNHAN_THUONGTICH", "pdf", par);
		});
		// end jira L2PT-3573
		//START L2PT-30686
		$("#toolbarIdprint_202").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				paramInput = {
					khambenhid : $("#hidKHAMBENHID").val(),
					khoaid : opt._deptId
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgGiayXacNhanNamVien", "divDlg", "manager.jsp?func=../noitru/NTU02D169_GiayXacNhanBNNamVien " + (thongtinbn==null?"":"(" + thongtinbn + ")"), paramInput,
					"In giấy xác nhận bệnh nhân đang nằm viện", 580, 500);
				DlgUtil.open("divDlgGiayXacNhanNamVien");
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//END L2PT-23856
		//START -- L2PT-32273--DoanPV-20201218
		$("#toolbarIdprint_165").on("click", function() {
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			CommonUtil.openReportGet('window', "PHIEU_DANH_GIA_TT_DD_A4", "pdf", par);
		});
		//Begin_NghiaNT_24102018: In tom tat benh an - L2HOTRO-11256
		$("#toolbarIdprint_31").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var par = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : rowData.HOSOBENHANID
				}, {
					name : 'loaibenhanid',
					type : 'String',
					value : rowData.LOAIBENHANID
				}, {
					name : 'khambenhid',
					type : 'String',
					value : rowData.KHAMBENHID
				} ];

				//HungND - L2PT-74972 - 240308
				var format = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'BAN_TTBA_PRINT_FORMAT');
				if(format && format != '0'){
					openReport('window', "TOMTAT_BA", format, par);
				}
				else {
					openReport('window', "TOMTAT_BA", "pdf", par);
				}
				//HungND - L2PT-74972 - 240308 END

			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//End_NghiaNT_24102018
		$("#toolbarIdprint_31_word").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
				var par = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : rowData.HOSOBENHANID
				}, {
					name : 'loaibenhanid',
					type : 'String',
					value : rowData.LOAIBENHANID
				}, {
					name : 'khambenhid',
					type : 'String',
					value : rowData.KHAMBENHID
				} ];
				CommonUtil.inPhieu('window', "TOMTAT_BA", 'docx', par, 'TOMTAT_BA.docx');
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});
		//Begin_HaNv_11092018: In lai to dieu tri voi BN ket thuc kham - L2HOTRO-5885
		$("#toolbarIdprint_19").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				paramInput = {
					khambenhid : $("#hidKHAMBENHID").val(),
					benhnhanid : $("#hidBENHNHANID").val(),
					thoigianvaovien : $("#hidNGAYTIEPNHAN").val(),
					modePrint : '1'
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgSoKetDieuTri", "divDlg", "manager.jsp?func=../noitru/NTU02D055_SoKetDieuTri", paramInput, "Sơ kết điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
				DlgUtil.open("divDlgSoKetDieuTri");
			}
		});
		//End_HaNv_11092018
		//HungND - L2PT-69796
		$("#toolbarIdtreatdt_11").on("click", function() {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1')
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			EventUtil.setEvent("cancelSoDe", function(e) {
				DlgUtil.close("dlgSoDe");
			});
			var myVar = {
				benhnhanId : rowData.BENHNHANID,
				tiepnhanId : rowData.TIEPNHANID
			};
			dlgPopup = DlgUtil.buildPopupUrl("dlgSoDe", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K011_SoDe", myVar, "Sổ đẻ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 880, 650);
			DlgUtil.open("dlgSoDe");
		});
		//HungND - L2PT-69796 END
		//start tuyendv 2905
		$("#toolbarIdprint_15").on("click", function() {
			var _benhnhanid = $("#hidBENHNHANID").val();
			var _khoaid = $("#hidKHOAID").val();
			var par = [ {
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			} ];
			openReport('window', "PHIEU_THEODOI_THUTHUAT", "pdf", par);
		});
		//end tuyendv
		//Beg_HaNv_040823: Theo dõi chức năng sống - L2PT-50068
		$("#toolbarIdtreat_cns").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			var paramInput = {
				khambenhid : rowData.KHAMBENHID,
				tiepnhanid : rowData.TIEPNHANID,
				hosobenhanid : rowData.HOSOBENHANID,
				benhnhanid : rowData.BENHNHANID,
				mabenhan : rowData.MABENHAN,
				mabenhnhan : rowData.MABENHNHAN,
				tenbenhnhan : rowData.TENBENHNHAN,
				namsinh : rowData.NAMSINH,
				modeView : _flgModeView
			};
			var dlgPopup = DlgUtil.buildPopupUrl("divDlgChucNangSong", "divDlg", "manager.jsp?func=../noitru/NTU02D084_ChucNangSong", paramInput, "Theo dõi chức năng sống " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 600);
			DlgUtil.open("divDlgChucNangSong");
		});
		//End_HaNv_040823
		//Beg_HaNv_060225: In bìa hồ sơ BA - L2PT-124992
		$("#toolbarIdprint_222").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var par = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				} ];
				openReport('window', "BIA_BENHAN", "pdf", par);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//End_HaNv_060225
	}
	function checkBeforeClickOnMenu() {
		var i_khambenhid = $("#hidKHAMBENHID").val();
		if (i_khambenhid == '' || i_khambenhid == '-1') {
			DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			return false;
		} else {
			return true;
		}
	}
	function checkVPBeforeClickOnMenu() {
		var i_tiepnhanid = $("#hidTIEPNHANID").val();
		if (i_tiepnhanid == '' || i_tiepnhanid == '-1') {
			DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			return false;
		} else {
			return true;
		}
	}
	function checkLSBeforeClickOnMenu() {
		var i_benhnhanid = $("#hidBENHNHANID").val();
		if (i_benhnhanid == '' || i_benhnhanid == '-1') {
			DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			return false;
		} else {
			return true;
		}
	}
//	function InPhoiVP(_inbangkechuan,_tiepnhanid, _report_code) {
//		var par = [];
//		            par.push({name : 'inbangkechuan',type : 'String',value : _inbangkechuan.toString()});
//		            par.push({name : 'tiepnhanid',type : 'String',value : _tiepnhanid.toString()});
//		var typeExport = "pdf";//$("#sltInBieuMau").val();
//		openReport('window', _report_code, typeExport, par);
//    }
	function loadDataTabs() {
		$("#tabDanhSachBenhNhan").on("click", function(e) {
			height_divMain = $('#hidDocumentHeight').val();
			$('#divMain').css('height', height_divMain);
		});
		$("#tabDanhSachBenhNhan").on("click", function(e) {
			height_divMain = $('#hidDocumentHeight').val();
			$('#divMain').css('height', height_divMain);
		});
		//widget thong tin hanh chinh
		$("#tabHanhChinh").on("click", function(e) {
			$('#tcHanhChinh').ntu02d023_tthc({
				_khambenhid : $("#hidKHAMBENHID").val()
			});
		});
		//widget thong tin benh an
		$("#tabBenhAn").on("click", function(e) {
			$('#tcBenhAn').ntu02d022_ttba({
				_khambenhid : $("#hidKHAMBENHID").val()
			});
		});
		//widget khoi tao grid danh sach xet nghiem
		$("#tabXetNghiem").on("click", function(e) {
			$('#tcXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_XetNghiem,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_formCall: "NTU_BDT"
			});
		});
		//==========================================
		$("#tabPhieuTruyenMau").on("click", function(e) {
			$('#tcPhieuTruyenMau').ntu02d070_ttptm({
				_gridXnId : "grdPhieuTruyenMau",
				_gridXnDetailId : "grdPhieuTruyenMauChiTiet",
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_modeView : _flgModeView
			// =1 chi view; !=1 la update
			//_hosobenhanid: ""
			});
		});
		//===========================================
		// click tab viện phí
		$("#tabVienPhiTab").on("click", function(e) {
			$('#tabVienPhi').vpi01t006_ttvp({
				_tiepnhanid : $("#hidTIEPNHANID").val(),
				_dept_id : opt._deptId
			});
		});
		//Begin_HaNv_18042018: bổ sung widget dinh dưỡng
		$("#idTabDinhDuong").on("click", function(e) {
			$('#tabDinhDuong').ntu02d087_dd({
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		});
		//End_HaNv_18042018
		//widget khoi tao grid danh sach CDHA
		$("#tabCDHA").on("click", function(e) {
			$('#tcCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_CDHA,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_formCall: "NTU_BDT"
			});
		});
		//widget khoi tao grid danh sach chuyen khoa
		$("#tabChuyenKhoa").on("click", function(e) {
			$('#tcChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua : 'grdCKketQua',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_ChuyenKhoa,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_formCall: "NTU_BDT"
			});
		});
		//widget khoi tao grid dieu tri
		$("#tabDieuTri").on("click", function(e) {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			$('#tcDieuTri').ntu02d027_dt({
				_grdDieuTri : 'grdDieuTri',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_thoigianvaovien : $("#hidTHOIGIANVAOVIEN").val(),
				_bacsidieutri : rowData.BACSYDIEUTRIID,
				_lnmbp : LNMBP_DieuTri,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_formCall: "NTU_BDT" //L2PT-20786
			});
		});
		//widget cho tab cham soc
		$("#tabChamSoc").on("click", function(e) {
			$('#tcChamSoc').ntu02d028_pcs({
				_grdChamSoc : 'grdChamSoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_ChamSoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_formCall: "NTU_BDT", //L2PT-20786
				_hosobenhanid : ""
			});
		});
		//widget cho tab truyen dich
		$("#tabPhieuTruyenDich").on("click", function(e) {
			$('#tcPhieuTruyenDich').ntu02d030_td({
				_grdTruyenDich : 'grdPhieuTruyenDich',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_TruyenDich,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_formCall: "NTU_BDT", //L2PT-20786
				_hosobenhanid : ""
			});
		});
		//widget cho tab tao phan ung thuoc
		$("#tabPhieuThuPhanUngThuoc").on("click", function(e) {
			$('#tcPhieuThuPhanUngThuoc').ntu02d031_tput({
				_grdPhanUngThuoc : 'grdPhieuThuPhanUngThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_TaoPhanUngThuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		});
		//widget cho tab tao hoi chan
		$("#tabHoiChuan").on("click", function(e) {
			$('#tcHoiChuan').ntu02d032_phc({
				_grdHoiChan : 'grdPhieuHoiChuan',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_HoiChan,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_formCall: "NTU_BDT" //L2PT-55717
			});
		});
		//DoanPV_20210603 BVTM-3343 widget cho tab dich vu khac
		$("#tabDichVuKhac").on("click", function(e) {
			//widget cho ao vang
			$('#tcDichVuKhac').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : '17',
				_loaidichvu : '1',
				_modeView : _flgModeView
			// =1 chi view; !=1 la update
			});
		});
		//widget cho tab ngay giương
		$("#tabNgayGiuong").on("click", function(e) {
			//widget cho tab ngay giuong
			$('#tcNgayGiuong').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : '12',
				_loaidichvu : '13',
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_formCall: "NTU_BDT"
			});
		});

		// L2PT-15640
		$("#tabPTDCNS").on("click", function(e) {
			if (!checkBeforeClickOnMenu())
				return;
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			var paramInput = {
				khambenhid : rowData.KHAMBENHID,
				tiepnhanid : rowData.TIEPNHANID,
				hosobenhanid : rowData.HOSOBENHANID,
				benhnhanid : rowData.BENHNHANID,
				mabenhan : rowData.MABENHAN,
				mabenhnhan : rowData.MABENHNHAN,
				tenbenhnhan : rowData.TENBENHNHAN,
				namsinh : rowData.NAMSINH,
				onlyView: true
			};
			var dlgPopup = DlgUtil.buildPopupUrl("divDlgChucNangSong", "divDlg", "manager.jsp?func=../noitru/NTU02D084_ChucNangSong", paramInput, "Theo dõi chức năng sống " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1000, 600);
			DlgUtil.open("divDlgChucNangSong");
		});

		//widget cho tab thong tin vat tu
		$("#tabVatTu").on("click", function(e) {
			$('#tcVatTu').ntu02d034_pvt({
				_grdVatTu : 'grdVatTu',
				_gridVatTuDetail : 'grdChiTietVatTu',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuvattu,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		});
		//widget cho tab thong tin thuoc
		$("#tabThuoc").on("click", function(e) {
			$('#tcThuoc').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		});
		//widget cho tab thong tin mau
		$("#tabMau").on("click", function(e) {
			$('#tcMau').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_loaitvt : '4'
			});
		});
		//widget cho tab phieu suat an
		$("#tabSuatAn").on("click", function(e) {
			$('#tcSuatAn').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieusuatan,
				_loaidichvu : "12",
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		});
		//widget cho tab phieu suat an
		$("#tabVanChuyen").on("click", function(e) {
			$('#tcVanChuyen').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuvanchuyen,
				_loaidichvu : "14",
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		});
		//widget cho tab tao phieu so sinh
		$("#tabPhieuSoSinh").on("click", function(e) {
			$('#tcPhieuSoSinh').ntu02d124_pss({
				_grdPhieuSoSinh : 'grdDSPhieu',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_PhieuSoSinh,
				_modeView : '0', // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		});

		//HungND - L2PT-63849
		//widget cho tab phieu cong kham
		$("#tabCongKham").on("click", function(e) {
			$('#divCongKham').ntu02d029_pdv({
				_grdSuatAn : 'grdSuatAn',
				_grdSuatAnChitiet : 'grdSuatAnChitiet',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : '3',
				_loaidichvu : "2",
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		});
		//HungND - L2PT-63849 End

		// Xử lý sự kiện liên quan ký CA => START
		//Phiếu hẹn khám.
		$("#toolbarIdprintca_11").on("click", function() {
			if ($("#hidKHAMBENHID").val() == "" || $("#hidKHAMBENHID").val() == "-1" || $("#hidKHAMBENHID").val() == null) {
				DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in giấy hẹn khám.');
				return;
			}
			var obj = new Object();
			obj.KHAMBENHID = $("#hidKHAMBENHID").val();
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NGT02K001.HK", JSON.stringify(obj));
			if (ret > 0) {
				var _param = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'RPT_CODE',
					type : 'String',
					value : 'NGT014_GIAYHENKHAMLAI_TT402015_A4'
				} ];
				_kyCaRpt(_param);
			} else {
				DlgUtil.showMsg("Không có thông tin hẹn khám của bệnh nhân này.");
			}
		});
		$("#toolbarIdprintca_9").on("click", function() {//HaNv_270325: L2PT-132899
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var _param = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'RPT_CODE',
					type : 'String',
					value : 'NGT003_GIAYCHUYENTUYEN_TT14_A4'
				} ];
				_kyCaRpt(_param);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		$("#toolbarIdprintca_5").on("click", function() {//HaNv_270325: L2PT-132899
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var _param = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'RPT_CODE',
					type : 'String',
					value : 'NTU009_GIAYRAVIEN_01BV01_QD4069_A5'
				} ];
				_kyCaRpt(_param);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		$("#toolbarIdprintca_12").on("click", function() {
			var _khambenhid = $("#hidKHAMBENHID").val();
			if (_khambenhid != null && _khambenhid != -1) {
				var paramInput = {
					khambenhid : _khambenhid,
					tiepnhanid : $("#hidTIEPNHANID").val()
				};
				// if (opt.hospital_id==996||opt.hospital_id==902||opt.hospital_id==944||opt.hospital_id==915||opt.hospital_id==932||opt.hospital_id==965||opt.hospital_id==957){
				var _isPopup = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "NTU_INPHIEU_CONGKHAI");
				if (_isPopup == '1') {
					var url = "manager.jsp?func=../noitru/NTU01H039_InPhieuCongKhaiDV";
					var popup = DlgUtil.buildPopupUrl("divDlgPCKDV", "divDlg", url, paramInput, "In phiếu công khai dịch vụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 505, 268);
					popup.open("divDlgPCKDV");
				} else {
					var _param = [ {
						name : 'hosobenhanid',
						type : 'String',
						value : $("#hidHOSOBENHANID").val()
					}, {
						name : 'khambenhid',
						type : 'String',
						value : _khambenhid
					}, {
						name : 'RPT_CODE',
						type : 'String',
						value : 'PHIEU_CONGKHAI_DICHVU'
					} ];
					_kyCaRpt(_param);
				}
			}
		});

		$("#toolbarIdprintca_13").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId != '') {
				var rpt_code = "";
				if (inPHieuKBChung) {
					rpt_code = 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4';
				} else {
					if ($("#hidDOITUONGBENHNHANID").val() == 3) {
						rpt_code = 'NGT022_GIAYKCBTHEOYEUCAU_05BV01_QD4069_A4';
					} else {
						rpt_code = 'NGT005_PHIEUKBVAOVIENCHUNG_42BV01_QD4069_A4';
					}
				}

				var _param = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'RPT_CODE',
					type : 'String',
					value : rpt_code
				} ];
				_kyCaRpt(_param);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
				return;
			}
		});

		$("#toolbarIdprintca_153").on("click", function() {
			if($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1'){
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			}else{
				var sql_par = [];
				sql_par.push({
					"name": "[0]",
					value: $("#hidLOAIBENHANID").val()
				});
				var _report_code = jsonrpc.AjaxJson.getOneValue('BACT.GET_REPORT', sql_par);

				var _param = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'loaibenhanid',
					type : 'String',
					value : $("#hidLOAIBENHANID").val()
				}, {
					name : 'benhnhanid',
					type : 'String',
					value : $("#hidBENHNHANID").val()
				}, {
					name: 'RPT_CODE',
					type: 'String',
					value: _report_code
				} ];
				_kyCaRpt(_param);
			}
		});

		$("#toolbarIdprintca_169").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var _param = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'i_sch',
					type : 'String',
					value : opt.dbschema
				}, {
					name : 'RPT_CODE',
					type : 'String',
					value : 'REPORT_PHIEU_XACNHAN_HIV'
				} ];
				_kyCaRpt(_param);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});

		$("#toolbarIdprintca_179").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var _param = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'RPT_CODE',
					type : 'String',
					value : 'RPT_GIAYCAMKETXN_BETAHCG'
				} ];
				_kyCaRpt(_param);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		// L2PT-23216, L2PT-23217,L2PT-23218
		$("#tabKythuat_PHCN").on("click", function() {
			if (!checkBeforeClickOnMenu())
				return;
			paramInput = {
				khambenhid : $("#hidKHAMBENHID").val(),
				benhnhanid : $("#hidBENHNHANID").val(),
				trangthaikhambenh : $("#hidTRANGTHAIKHAMBENH").val(),
				//dichvuKhambenhID : rowData.DICHVUKHAMBENHID,
				maubenhphamid : '',
				hosobenhanid : $("#hidHOSOBENHANID").val(),
				tiepnhanid : $("#hidTIEPNHANID").val(),
				callFrom : 'WG'
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgThucHienPHCN", "divDlg", "manager.jsp?func=../noitru/NTU02D199_PhieuThucHienPHCN_QTI", paramInput, "Tạo phiếu thực hiện kỹ thuật PHCN " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1100, 635);
			DlgUtil.open("divDlgThucHienPHCN");

		});

		//HungND - L2PT-68659
		$('#toolbarIdbtnLSKCBGW').on('click', function (e) {
			if ($("#hidKHAMBENHID").val() == '' || $("#hidKHAMBENHID").val() == '-1') {
				return DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
			} else {
				var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
				if (selRowId != null && selRowId != '') {
					var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
					var paramInput={
						MABHYT : rowData.MA_BHYT,
						NGAYKHAM : rowData.NGAYTIEPNHAN,
						MAKCBBD : _opts.hospital_code
					};

					dlgPopup=DlgUtil.buildPopupUrl(
						"divDlgDDT","divDlg","manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB_GW",
						paramInput,"Thông tin lịch sử KCB Trong Tỉnh",window.innerWidth*0.95,window.innerHeight*0.93);

					var parent = DlgUtil.open("divDlgDDT");
				}
			}
		});
		//HungND - L2PT-68659 END

		//HungND - L2PT-74256
		$("#toolbarIdprintca_210").on("click", function() {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			if (selRowId != null && selRowId > 0) {
				var _param = [ {
					name : 'hosobenhanid',
					type : 'String',
					value : $("#hidHOSOBENHANID").val()
				}, {
					name : 'khambenhid',
					type : 'String',
					value : $("#hidKHAMBENHID").val()
				}, {
					name : 'RPT_CODE',
					type : 'String',
					value : 'GIAY_CD_CHAY_THANNHANTAO'
				} ];
				_kyCaRpt(_param);
			} else {
				DlgUtil.showMsg('Chưa chọn bệnh nhân');
			}
		});
		//HungND - L2PT-74256 END
	}
	//calback cho dong man hinh chuyen phong khong chuyen giuong
	EventUtil.setEvent("assignSevice_saveCBNVPKG", function(e) {
		DlgUtil.showMsg(e.msg);
		DlgUtil.close("divDlgCBNVaoPhong");
	});
	//callback cho  giay chung sinh
	EventUtil.setEvent("assignSevice_saveGCS", function(e) {
		DlgUtil.showMsg(e.msg);
		DlgUtil.close("dlgGiayChungSinh");
	});
	//callback cho ho so benh an
	EventUtil.setEvent("assignSevice_saveHSBADetail", function(e) {
		//HaNv_240723: L2PT-47559
		DlgUtil.showMsg(e.msg, undefined, jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_TIMEOUT_THONGBAO'));
		DlgUtil.close("divDlgBenhAnDetail");
	});
	//callback cho tai nan thuong tich
	EventUtil.setEvent("assignSevice_saveTNTT", function(e) {
		DlgUtil.showMsg(e.msg);
		DlgUtil.close("dlgTaiNanThuongTich");
	});
	//callback cho cap nhat thong tin hanh chinh
	EventUtil.setEvent("assignSevice_loadData", function(e) {
		DlgUtil.close("divDlgSuaBenhNhan");
		DlgUtil.showMsg('Cập nhật bệnh nhân thành công');
	});
	//callback cho la phieu di kem
	EventUtil.setEvent("assignSevice_DV_DinhKem", function(e) {
		DlgUtil.showMsg(e.msg); //L2PT-13321
		$('#tcVatTu').ntu02d034_pvt({
			_grdVatTu : 'grdVatTu',
			_gridVatTuDetail : 'grdChiTietVatTu',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_Phieuvattu,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		$('#tcThuoc').ntu02d033_pt({
			_grdPhieuthuoc : 'grdThuoc',
			_gridPhieuthuocDetail : 'grdChiTietThuoc',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_Phieuthuoc,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		$('#tcMau').ntu02d033_pt({
			_grdPhieuthuoc : 'grdThuoc',
			_gridPhieuthuocDetail : 'grdChiTietThuoc',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_Phieuthuoc,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : "",
			_loaitvt : '4'
		});
		DlgUtil.close("divCreateNoteAttach");
	});
	EventUtil.setEvent("assignSevice_savePTTT", function(e) {
		DlgUtil.showMsg(e.msg);
		$('#tcChuyenKhoa').ntu02d026_ck({
			_gridCK : 'grdCK',
			_gridCKDetail : 'grdCKChitiet',
			_grdCKketQua : 'grdCKketQua',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_ChuyenKhoa,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		DlgUtil.close("dlgPTTT");
	});
	//cap nhat thong tin benh nhan
	EventUtil.setEvent("assignSevice_updateNhapBenhNhan", function(e) {
		if (typeof (e) != 'undefined') {
			DlgUtil.showMsg(e.msg);
		}
		DlgUtil.close("divDlgNhapBenhNhan");
		loadGridData();
		FormUtil.clearForm("divThongTinBenhNhan", "");
	});
	//calback cho MAN HINH TAO BAN SAO DON THUOC
	EventUtil.setEvent("assignSevice_SaveCopyMbp", function(e) {
		DlgUtil.showMsg(e.msg);
		if (e.type == '7') {
			//widget cho tab thong tin thuoc
			$('#tcThuoc').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			$('#tcMau').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_loaitvt : '4'
			});
		} else if (e.type == '8') {
			//widget cho tab thong tin thuoc
			$('#tcVatTu').ntu02d034_pvt({
				_grdVatTu : 'grdVatTu',
				_gridVatTuDetail : 'grdChiTietVatTu',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuvattu,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		} else if (e.type == '1') {
			$('#tcXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_XetNghiem,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		} else if (e.type == '2') {
			$('#tcCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_CDHA,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		} else if (e.type == '5') {
			$('#tcChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua : 'grdCKketQua',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_ChuyenKhoa,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		} else if (e.type == '4') {
			var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
			var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
			$('#tcDieuTri').ntu02d027_dt({
				_grdDieuTri : 'grdDieuTri',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_thoigianvaovien : $("#hidTHOIGIANVAOVIEN").val(),
				_bacsidieutri : rowData.BACSYDIEUTRIID,
				_lnmbp : LNMBP_DieuTri,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			$('#tcThuoc').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			$('#tcMau').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_loaitvt : '4'
			});
			$('#tcVatTu').ntu02d034_pvt({
				_grdVatTu : 'grdVatTu',
				_gridVatTuDetail : 'grdChiTietVatTu',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuvattu,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			$('#tcXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_XetNghiem,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			$('#tcCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_CDHA,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			$('#tcChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua : 'grdCKketQua',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_ChuyenKhoa,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			// start laphm: chưc năng tạo bản sao phiếu điều trị: nếu ngày tạo bản sao mà cách ngày Vào khoa số ngày là bội của 15
			// thì ngoài việc tạo bản sao sẽ hiện ra form cho nhập phiếu sơ kết điều trị
			if (e.paramSoKetDieuTri) {
				dlgPopup = DlgUtil.buildPopupUrl("divDlgSoKetDieuTri", "divDlg", "manager.jsp?func=../noitru/NTU02D055_SoKetDieuTri", e.paramSoKetDieuTri, "Sơ kết điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1200, 600);
				DlgUtil.open("divDlgSoKetDieuTri");//
			}
			// end laphm
			//Beg_HaNv_111023: Tự động mở popup cập nhật PĐT sau sao chép - L2PT-51576
			if (cf.PDT_OPEN_UPD_AFTER_COPY == "1") {
				var paramInput = {
					khambenhId : $("#hidKHAMBENHID").val(),
					maubenhphamId : e.phieudieutriid_new,
					benhnhanId : $("#hidBENHNHANID").val(),
					tiepnhanId : $("#hidTIEPNHANID").val(),
					hosobenhanId : $("#hidHOSOBENHANID").val(),
					doituongbenhnhanId : $("#hidDOITUONGBENHNHANID").val(),
					loaitiepnhanId : $("#hidLOAITIEPNHANID").val(),
					loaibenhanid : $("#hidLOAIBENHANID").val(),//L2PT-16629
					hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
					subDeptId : $("#hidPHONGID").val(),
					mode_time : 1
				};
				if (form_phieudt_lv) {
					dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT_LV", paramInput, "Cập nhật phiếu điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1330, 600);
				} else {
					dlgPopup = DlgUtil.buildPopupUrl("divDlgPhieuDieuTri", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K015_PhieuDT", paramInput, "Cập nhật phiếu điều trị " + (thongtinbn==null?"":"(" + thongtinbn + ")"), 1330, 700);
				}
				EventUtil.setEvent("divDlgPhieuDieuTri_onClose", function(name) {
					$('#tcDieuTri').ntu02d027_dt({
						_grdDieuTri : 'grdDieuTri',
						_khambenhid : $("#hidKHAMBENHID").val(),
						_benhnhanid : $("#hidBENHNHANID").val(),
						_thoigianvaovien : $("#hidTHOIGIANVAOVIEN").val(),
						_lnmbp : LNMBP_DieuTri,
						_modeView : _flgModeView, // =1 chi view; !=1 la update
						_hosobenhanid : ""
					});
				});
				DlgUtil.open("divDlgPhieuDieuTri");
			}
			//End_HaNv_111023
		}
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
		DlgUtil.close("divDlgCopyMbp");
	});
	//calback cho MA HINH SUA PHONG THUC HIEN
	EventUtil.setEvent("assignSevice_SavePhongThucHien", function(e) {
		DlgUtil.showMsg(e.msg);
		//reload danh sach xet nghiem
		if (e.loaiPhieu == LNMBP_XetNghiem) {
			$('#tcXetNghiem').ntu02d024_ttxn({
				_gridXnId : "grdXetNghiem",
				_gridXnDetailId : "grdXetNghiemChiTiet",
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_XetNghiem,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		} else if (e.loaiPhieu == LNMBP_CDHA) {
			$('#tcCDHA').ntu02d025_cdha({
				_gridCDHA : "grdCDHA",
				_gridCDHADetail : "grdCDHAChiTiet",
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_CDHA,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		} else if (e.loaiPhieu == LNMBP_ChuyenKhoa) {
			$('#tcChuyenKhoa').ntu02d026_ck({
				_gridCK : 'grdCK',
				_gridCKDetail : 'grdCKChitiet',
				_grdCKketQua : 'grdCKketQua',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_ChuyenKhoa,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		} else if (e.loaiPhieu == LNMBP_Phieuvattu) {
			$('#tcVatTu').ntu02d034_pvt({
				_grdVatTu : 'grdVatTu',
				_gridVatTuDetail : 'grdChiTietVatTu',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuvattu,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		} else if (e.loaiPhieu == LNMBP_Phieuthuoc) {
			//widget cho tab thong tin thuoc
			$('#tcThuoc').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			$('#tcMau').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_loaitvt : '4'
			});
		}
		DlgUtil.close("divDlgEditOrgDone");
	});
	//calback cho dong man hinh chuyen phong khong chuyen giuong
	EventUtil.setEvent("assignSevice_saveCBNVaoBuongPhong", function(e) {
		DlgUtil.showMsg(e.msg);
		loadGridData();
		DlgUtil.close("divDlgCBNVaoBuongPhong");
	});
	//callback cho them moi benh an
	EventUtil.setEvent("assignSevice_saveBenhAn", function(e) {
		//HaNv_240723: L2PT-47559
		DlgUtil.showMsg(e.msg, undefined, jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_TIMEOUT_THONGBAO'));
		$('#tcBenhAn').ntu02d022_ttba({
			_khambenhid : $("#hidKHAMBENHID").val()
		});
		DlgUtil.close("divDlgBenhAn");
	});
	//callback cho them moi benh an
	EventUtil.setEvent("assignSevice_closeBenhAn", function(e) {
		$('#tcBenhAn').ntu02d022_ttba({
			_khambenhid : $("#hidKHAMBENHID").val()
		});
		var NTU_CHECK_CAPNHAT_BACHUNG = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'NTU_CHECK_CAPNHAT_BACHUNG');
		if (NTU_CHECK_CAPNHAT_BACHUNG == '1') {
			var sql_par = [];
			sql_par.push({
				"name" : "[0]",
				value : $("#hidKHAMBENHID").val()
			});
			var _capnhat_bachung = jsonrpc.AjaxJson.getOneValue('NTU02D021.UPD.BA', sql_par);
			if (_capnhat_bachung == '1') {
				$("#toolbarIdbtnService").attr("disabled", false);
				$("#toolbarIdbtndrug").attr("disabled", false);
			} else {
				$("#toolbarIdbtnService").attr("disabled", true);
				$("#toolbarIdbtndrug").attr("disabled", true);
			}
		}
		DlgUtil.close("divDlgBenhAn");
	});
	EventUtil.setEvent("exam_cancel", function(e) {
		DlgUtil.close("dlgKham");
	});
	EventUtil.setEvent("assignTruyenDich_cancel", function(e) {
		DlgUtil.close("divDlgPTruyenDich");
	});
	EventUtil.setEvent("assignTruyenMau_cancel", function(e) {
		DlgUtil.close("divDlgPTruyenMau");
	});
	EventUtil.setEvent("assignTruyenMauChiTiet_cancel", function(e) {
		DlgUtil.close("divDlgPTruyenMauChiTiet");
	});
	EventUtil.setEvent("assignTruyenMauChiTiet2_cancel", function(e) {
		DlgUtil.close("divDlgPTruyenMauChiTiet2");
	});
	EventUtil.setEvent("assignPUThuoc_cancel", function(e) {
		DlgUtil.close("divDlgPhanUngThuoc");
	});
	EventUtil.setEvent("exam_save", function(e) {
		//widget thong tin benh an
		$('#tcBenhAn').ntu02d022_ttba({
			_khambenhid : $("#hidKHAMBENHID").val()
		});
		DlgUtil.close("dlgKham");
	});
	//callback cho phieu cham soc
	EventUtil.setEvent("assignSevice_SaveChamSoc", function(e) {
		DlgUtil.showMsg(e.msg);
		//widget cho tab cham soc
		$('#tcChamSoc').ntu02d028_pcs({
			_grdChamSoc : 'grdChamSoc',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_ChamSoc,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_formCall: "NTU_BDT", //L2PT-20786
			_hosobenhanid : ""
		});
		DlgUtil.close("divDlgChamSoc");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	//callback cho phieu hoi chan
	EventUtil.setEvent("assignSevice_SaveHoiChan", function(e) {
		// DlgUtil.showMsg(e.msg);
		DlgUtil.showMsg(e.msg, undefined, jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_TIMEOUT_THONGBAO')); //L2PT-30583
		//widget cho tab tao hoi chan
		$('#tcHoiChuan').ntu02d032_phc({
			_grdHoiChan : 'grdPhieuHoiChuan',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_HoiChan,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		DlgUtil.close("divDlgHoiChan");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	//callback cho phieu phan ung thuoc
	EventUtil.setEvent("assignSevice_SavePUThuoc", function(e) {
		DlgUtil.showMsg(e.msg);
		//widget cho tab tao phan ung thuoc
		$('#tcPhieuThuPhanUngThuoc').ntu02d031_tput({
			_grdPhanUngThuoc : 'grdPhieuThuPhanUngThuoc',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_TaoPhanUngThuoc,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		DlgUtil.close("divDlgPhanUngThuoc");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	//callback cho phieu truyen dich
	EventUtil.setEvent("assignSevice_SaveTruyenDich", function(e) {
		DlgUtil.showMsg(e.msg);
		//widget cho tab truyen dich
		$('#tcPhieuTruyenDich').ntu02d030_td({
			_grdTruyenDich : 'grdPhieuTruyenDich',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_TruyenDich,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_formCall: "NTU_BDT", //L2PT-20786
			_hosobenhanid : ""
		});
		DlgUtil.close("divDlgPTruyenDich");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	//callback cho phieu truyen mau
	EventUtil.setEvent("assignSevice_SaveTruyenMau", function(e) {
		DlgUtil.showMsg(e.msg);
		//widget cho tab truyen mau	cap nhat phieu
		$('#tcPhieuTruyenMau').ntu02d070_ttptm({
			_gridXnId : "grdPhieuTruyenMau",
			_gridXnDetailId : "grdPhieuTruyenMauChiTiet",
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val()
		});
		DlgUtil.close("divDlgPTruyenMau");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	EventUtil.setEvent("assignSevice_SaveTruyenMauCT", function(e) {
		DlgUtil.showMsg(e.msg);
		//widget cho tab truyen mau	chi tiet sua
		$('#tcPhieuTruyenMau').ntu02d070_ttptm({
			_gridXnId : "grdPhieuTruyenMau",
			_gridXnDetailId : "grdPhieuTruyenMauChiTiet",
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val()
		});
		DlgUtil.close("divDlgPTruyenMauChiTiet");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	EventUtil.setEvent("assignSevice_SaveTruyenMauCT2", function(e) {
		DlgUtil.showMsg(e.msg);
		//widget cho tab truyen mau	chi tiet2 :them
		$('#tcPhieuTruyenMau').ntu02d070_ttptm({
			_gridXnId : "grdPhieuTruyenMau",
			_gridXnDetailId : "grdPhieuTruyenMauChiTiet",
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val()
		});
		DlgUtil.close("divDlgPTruyenMauChiTiet2");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	//calback cho dong man hinh phieu dieu tri
	EventUtil.setEvent("treatment_cancel", function(e) {
		//widget phieu dieu tri
		var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
		var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
		/*$('#tcDieuTri').ntu02d027_dt({
			_grdDieuTri : 'grdDieuTri',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_thoigianvaovien : $("#hidTHOIGIANVAOVIEN").val(),
			_bacsidieutri : rowData.BACSYDIEUTRIID,
			_lnmbp : LNMBP_DieuTri,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});*/
		DlgUtil.close("divDlgPhieuDieuTri");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	//calback cho dong man hinh phieu dieu tri
	EventUtil.setEvent("treatment_print", function(e) {
		//widget phieu dieu tri
		var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
		var rowData = $("#" + _gridId).jqGrid('getRowData', selRowId);
		$('#tcDieuTri').ntu02d027_dt({
			_grdDieuTri : 'grdDieuTri',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_thoigianvaovien : $("#hidTHOIGIANVAOVIEN").val(),
			_bacsidieutri : rowData.BACSYDIEUTRIID,
			_lnmbp : LNMBP_DieuTri,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		DlgUtil.close("divDlgPhieuDieuTri");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
		var _benhnhanid = $("#hidBENHNHANID").val();
		var _khoaid = $("#hidKHOAID").val();
		var _maubenhphamid = e.msg;
		var _data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'HIS_DHYTB_INPDT_KHOAYHCT;HIS_DHYTB_INPDT_KHOAID');
		sql_par = [];
		sql_par.push({
			"name" : "[0]",
			"value" : rowData.KHOAID
		});
		var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D027.LOAIKHOA", sql_par);
		var row2 = JSON.parse(data2)[0];
		var loaikhoa = row2.ORG_TYPE;
		if (_data_ar != null && _data_ar.length > 0) {
			if (_data_ar[0].HIS_DHYTB_INPDT_KHOAYHCT == '1' && _data_ar[0].HIS_DHYTB_INPDT_KHOAID == loaikhoa) {
				var par = [ {
					name : 'i_benhnhanid',
					type : 'String',
					value : _benhnhanid
				}, {
					name : 'i_maubenhphamid',
					type : 'String',
					value : _maubenhphamid
				}, {
					name : 'i_khoaid',
					type : 'String',
					value : _khoaid
				} ];
				openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_YHCT_A4_ONE", "pdf", par);
			} else {
				var par = [ {
					name : 'i_benhnhanid',
					type : 'String',
					value : _benhnhanid
				}, {
					name : 'i_maubenhphamid',
					type : 'String',
					value : _maubenhphamid
				}, {
					name : 'i_khoaid',
					type : 'String',
					value : _khoaid
				} ];
				openReport('window', "NTU020_TODIEUTRI_39BV01_QD4069_A4_ONE", "pdf", par);
			}
		}
	});
	//calback cho man hinh chuyen mo thanh cong
	EventUtil.setEvent("assignSevice_saveChuyenMo", function(e) {
		if (e.msg != null && e.msg != "") {
			DlgUtil.showMsg(e.msg);
		}
		DlgUtil.close(e.divId);
		loadGridData();
	});
	// sự kiện đóng phieu truyen mau
	EventUtil.setEvent("phieu truyen mau", function(e) {
		// DlgUtil.showMsg('parent event e.ncc= '+e.ncc+' id= '+e.id);
		DlgUtil.close("divDlgPTruyenMau");
		// loadGrid();
		// loadGridCT();
		loadGridData();
	});
	//calback cho man hinh ra vien, lich hen
	EventUtil.setEvent("assignSevice_saverv", function(e) {
		var data = e.msg;
		_objData = $.extend({
			khambenhid : $("#hidKHAMBENHID").val(),
			benhnhanid : $("#hidBENHNHANID").val()
		}, data);
		var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT02K007_RAVIEN", JSON.stringify(_objData));
		if ($.isNumeric(ret) && ret != -1 && ret != 0) {
			DlgUtil.showMsg("Cập nhật thông tin ra viện, lịch hẹn thành công");
			if (getCauHinh('NTU_SAVERV_UNRV') == '1') {
				updateNgayRaVienTheoTTRaVien();
			}
			_dayhosoct(); //L2PT-49249
		} else {
			DlgUtil.showMsg(ret); // L2PT-28440
		}
		DlgUtil.close("dlgXuTri");
	});
	//calback cho man hinh tao phieu thuoc, vat tu
	EventUtil.setEvent("assignSevice_saveTaoPhieuThuoc", function(e) {
		DlgUtil.showMsg(e.msg);
		//reload lai widget cho tung man hinh
		if (e.option == '02D010' || e.option == '02D014' || e.option == '02D011' || e.option == '02D017') {
			//widget cho tab thong tin thuoc
			$('#tcThuoc').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
			$('#tcMau').ntu02d033_pt({
				_grdPhieuthuoc : 'grdThuoc',
				_gridPhieuthuocDetail : 'grdChiTietThuoc',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuthuoc,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : "",
				_loaitvt : '4'
			});
		} else if (e.option == '02D015' || e.option == '02D016') {
			//widget cho tab thong tin vat tu
			$('#tcVatTu').ntu02d034_pvt({
				_grdVatTu : 'grdVatTu',
				_gridVatTuDetail : 'grdChiTietVatTu',
				_khambenhid : $("#hidKHAMBENHID").val(),
				_benhnhanid : $("#hidBENHNHANID").val(),
				_lnmbp : LNMBP_Phieuvattu,
				_modeView : _flgModeView, // =1 chi view; !=1 la update
				_hosobenhanid : ""
			});
		}
		DlgUtil.close("divDlgTaoPhieuThuoc" + e.option);
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	//tuyennx_add_start_HISL2TK-554
	EventUtil.setEvent("assignSevice_saveThuocSao", function(e) {
		DlgUtil.showMsg(e.msg);
		$('#tcThuoc').ntu02d033_pt({
			_grdPhieuthuoc : 'grdThuoc',
			_gridPhieuthuocDetail : 'grdChiTietThuoc',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_Phieuthuoc,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		$('#tcMau').ntu02d033_pt({
			_grdPhieuthuoc : 'grdThuoc',
			_gridPhieuthuocDetail : 'grdChiTietThuoc',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_Phieuthuoc,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : "",
			_loaitvt : '4'
		});
		DlgUtil.close("dlgTHUOCSAO");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	//tuyennx_add_end
	//L2PT-107978
	EventUtil.setEvent("assignSevice_saveThuocpt", function(e) {
		DlgUtil.showMsg(e.msg);
		$('#tcThuoc').ntu02d033_pt({
			_grdPhieuthuoc : 'grdThuoc',
			_gridPhieuthuocDetail : 'grdChiTietThuoc',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_Phieuthuoc,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		DlgUtil.close("dlgTHUOCPT");
	});
	EventUtil.setEvent("assignDrug_cancel", function(e) {
		DlgUtil.close("divDlgTaoPhieuThuoc" + e.option);
		DlgUtil.close("dlgCDT" + e.option);
	});
	//calback cho man hinh chi dinh dich vu
	EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function(e) {
		if (e.msg != '' && e.msg != null && opt.hospital_id != 26720) {
			DlgUtil.showMsg(e.msg, undefined, jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'HIS_TIMEOUT_THONGBAO')); //L2PT-30583
		}
		//widget khoi tao grid danh sach xet nghiem
		$('#tcXetNghiem').ntu02d024_ttxn({
			_gridXnId : "grdXetNghiem",
			_gridXnDetailId : "grdXetNghiemChiTiet",
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_XetNghiem,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		//widget khoi tao grid danh sach CDHA
		$('#tcCDHA').ntu02d025_cdha({
			_gridCDHA : "grdCDHA",
			_gridCDHADetail : "grdCDHAChiTiet",
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_CDHA,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		//widget khoi tao grid danh sach chuyen khoa
		$('#tcChuyenKhoa').ntu02d026_ck({
			_gridCK : 'grdCK',
			_gridCKDetail : 'grdCKChitiet',
			_grdCKketQua : 'grdCKketQua',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_ChuyenKhoa,
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		//widget cho tab phieu suat an
		$('#tcSuatAn').ntu02d029_pdv({
			_grdSuatAn : 'grdSuatAn',
			_grdSuatAnChitiet : 'grdSuatAnChitiet',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_Phieusuatan,
			_loaidichvu : "12",
			_modeView : _flgModeView, // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		if (!e.isLuuTiep) {
			DlgUtil.close("divDlgDichVu");
		}
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	EventUtil.setEvent("assignSevice_saveChiDinhDichVuTiepOk", function(e) {
		DlgUtil.close("divDlgDichVu");
		if (!checkDoiTuongBN())
			return;
		paramInput = {
			chidinhdichvu : '1',
			loaidichvu : '12',
			loaiphieumbp : LNMBP_Phieusuatan.toString(),
			benhnhanid : $("#hidBENHNHANID").val(),
			khambenhid : $("#hidKHAMBENHID").val(),
			hosobenhanid : $("#hidHOSOBENHANID").val(),
			tiepnhanid : $("#hidTIEPNHANID").val(),
			doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(),
			hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
			loaibenhanid : $("#hidLOAIBENHANID").val(),
			loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
			subDeptId : $("#hidPHONGID").val(),
			bacsike : $("#labelHidBACSYKE").val(),
			thoigiantiep : e.tgtiep
		};
		dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 12, paramInput, "Phiếu suất ăn " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
		DlgUtil.open("divDlgDichVu");
	});
	//Begin_HaNv_090621: Cho phép tìm kiếm và load lại thông tin BN - L2PT-3632
	EventUtil.setEvent("assignSevice_ChiDinhDichVuReloadBN", function(e) {
		DlgUtil.close("divDlgDichVu");
		var obj = e.obj;
		paramInput = {
			benhnhanid : obj.BENHNHANID,
			khambenhid : obj.KHAMBENHID,
			tiepnhanid : obj.TIEPNHANID,
			hosobenhanid : obj.HOSOBENHANID,
			doituongbenhnhanid : obj.DOITUONGBENHNHANID,
			loaitiepnhanid : obj.LOAITIEPNHANID
		};
		dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, paramInput, "Tạo phiếu chỉ định dịch vụ " + (thongtinbn==null?"":"(" + thongtinbn + ")"), _width, _height);
		DlgUtil.open("divDlgDichVu");
	});
	//End_HaNv_090621
	//callback cho phieu so sinh
	EventUtil.setEvent("assignSevice_SaveSoSinh", function(e) {
		DlgUtil.showMsg(e.msg);
		$('#tcPhieuSoSinh').ntu02d124_pss({
			_grdPhieuSoSinh : 'grdDSPhieu',
			_khambenhid : $("#hidKHAMBENHID").val(),
			_benhnhanid : $("#hidBENHNHANID").val(),
			_lnmbp : LNMBP_PhieuSoSinh,
			_modeView : '0', // =1 chi view; !=1 la update
			_hosobenhanid : ""
		});
		// DlgUtil.close("divDlgPhieuSoSinh");
		setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
	});
	//tim kiem benh nhan
	function searchBenhNhan() {
		$("#btnTIMKIEM").on("click", function(e) {
			loadGridData();
		});
	}
	//khoi tao luoi danh sach benh nhan
	function loadGridData() {
		//chỉ cho tìm kiếm theo ngày vs trạng thái chọn và kết thúc.
		if ($('#cboTRANGTHAIKHAMBENH').val() == '' || $('#cboTRANGTHAIKHAMBENH').val() == '9') {
			if ($("#txtTG_NHAPVIEN_TU").val() == '') {
				DlgUtil.showMsg("Từ ngày chưa được chọn!", function() {
					$('#txtTG_NHAPVIEN_TU').focus();
				});
				return false;
			}
			if ($("#txtTG_NHAPVIEN_DEN").val() == '') {
				DlgUtil.showMsg("Đến ngày chưa được chọn!", function() {
					$('#txtTG_NHAPVIEN_DEN').focus();
				});
				return false;
			}
		}

		FormUtil.clearForm("divContentHC", "");
		var lookup_sql = "";
		lookup_sql = _gridSQL;
		var sql_par = [];
		var objData = new Object();
		// start jira L2PT-6219
		// vào phòng có loại điều trị nội trú thì chỉ thấy bệnh nhân điều trị nội trú
		// vào phòng có loại điều trị ngoại trú thì chỉ thấy bệnh nhân điều trị ngoại trú
		if (getCauHinh("NTU_KHONGSHOWBNPHONGKHAM") == '1') {
			var loaiPhongHienTai = getLoaiPhongHienTai();
			if ((type == 3 && (!["9" ].includes(loaiPhongHienTai + ""))) || (type == 0 && (![ "3" ].includes(loaiPhongHienTai + "")))) {
				$("#grdBenhNhan").jqGrid("clearGridData", true);
				return;
			}
		}
		// end jira L2PT-6219
		var _chkTimtheongay = $("#chkTIMTHEONGAY");
		var _chkTimtheongayra = $("#chkTIMTHEONGAYRA");
		if (_chkTimtheongay.is(":checked") || _chkTimtheongayra.is(":checked")) {
			if (_validateParam()) {
				objData.TG_NHAPVIEN_TU = nvl($("#txtTG_NHAPVIEN_TU").val(), '-1');
				objData.TG_NHAPVIEN_DEN = nvl($("#txtTG_NHAPVIEN_DEN").val(), '-1');
				// sql_par.push({"name":"[0]","value":nvl($("#txtTG_NHAPVIEN_TU").val(),'-1')});
				// sql_par.push({"name":"[1]","value":nvl($("#txtTG_NHAPVIEN_DEN").val(),'-1')});
			} else {
				return;
			}
		} else {
			// sql_par.push({"name":"[0]","value":'-1'});
			// sql_par.push({"name":"[1]","value":'-1'});
			objData.TG_NHAPVIEN_TU = '-1';
			objData.TG_NHAPVIEN_DEN = '-1';
		}
		// sql_par.push({"name":"[2]","value":$("#cboTRANGTHAIKHAMBENH").val()== ''? '-1' : $("#cboTRANGTHAIKHAMBENH").val()});
		// sql_par.push({"name":"[3]","value":opt._deptId});
		// sql_par.push({"name":"[4]","value":isDsKhoa == true ? nvl($('#cboPHONGID').val(),"-1") :opt._subdeptId});
		// sql_par.push({"name":"[5]","value":type});
		objData.TRANGTHAIKHAMBENH = $("#cboTRANGTHAIKHAMBENH").val() == '' ? '-1' : $("#cboTRANGTHAIKHAMBENH").val();
		objData.KHOAID = opt._deptId;
		objData.PHONGID = isDsKhoa == true ? nvl($('#cboPHONGID').val(), "-1") : opt._subdeptId;
		objData.LOAITIEPNHANID = type;
		var chkBACSYDIEUTRI = $("#chkBACSYDIEUTRI");
		if (chkBACSYDIEUTRI.is(":checked")) {
			//sql_par.push({"name":"[6]","value": "1" });
			// sql_par.push({"name":"[6]","value": $('#cboBACSIID').val()});
			objData.BACSYDIEUTRIID = $('#cboBACSIID').val();
		} else {
			// sql_par.push({"name":"[6]","value": "0" });
			objData.BACSYDIEUTRIID = '0';
		}
		// laphm: bo sung tim kiem theo ngay ra vien
		// tham so i_loaingay:
		// 1 : checkbox ngay vao vien
		// 2 : checkbox ngay ra vien
		// -1 : không có checkbox nào
		if (_chkTimtheongay.is(":checked")) {
			objData.LOAINGAY = '1';
			sql_par.push({
				"name" : "[7]",
				"value" : '1'
			});
		} else if (_chkTimtheongayra.is(":checked")) {
			sql_par.push({
				"name" : "[7]",
				"value" : '2'
			});
			objData.LOAINGAY = '2';
		} else {
			sql_par.push({
				"name" : "[7]",
				"value" : '-1'
			});
			objData.LOAINGAY = '-1';
		}
		objData.TRANGTHAITIEPNHAN = $('#cboTRANGTHAITIEPNHAN').val();
		if ($("#chkBNDIEUTRIKETHOP").is(":checked")) {
			objData.DIEUTRIKETHOP = '1';
		} else {
			objData.DIEUTRIKETHOP = '-1';
		}
		if (timTheoDTBN) {
			objData.DOITUONGBENHNHAN = $('#cboDOITUONGBENHNHAN').val();
		}
		objData.NGUOINHA = $('#cboNGUOINHA').val();
		objData.MABENHAN = nvl($('#txtMABENHAN').val().trim(), '-1');
		objData.MABENHNHAN = nvl($('#txtMABENHNHAN').val().trim(), '-1');
		objData.TENBENHNHAN = nvl($('#txtTENBENHNHAN').val().trim(), '-1');
		objData.MABHYT = nvl($('#txtMABHYT').val().trim(), '-1');
		//Beg_HaNv_150523: check phiếu điều trị và đơn thuốc trong ngày - L2PT-40227
		if ($("#chkCHUATAOPDT").is(":checked")) {
			objData.CHUATAOPDT = '1';
		} else {
			objData.CHUATAOPDT = '-1';
		}
		if ($("#chkCHUATAOTHUOC").is(":checked")) {
			objData.CHUATAOTHUOC = '1';
		} else {
			objData.CHUATAOTHUOC = '-1';
		}
		//End_HaNv_150523
		objData.LOAI_DTRI_NTU = $('#cboLOAI_DTRI_NTU').val(); // L2PT-66453
		var _sql_par = [ {
			"name" : "[0]",
			"value" : JSON.stringify(objData)
		} ];
		GridUtil.loadGridBySqlPage(_gridId, lookup_sql, _sql_par);
	}
	function _setButtonToolBar(mode) {
		var NTU_BDT_SHOW_THUOCNHATHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "NTU_BDT_SHOW_THUOCNHATHUOC");
		if (mode == '0') {//disenable tat ca cac submenu
			$("#toolbarIdbtnPrint").attr("disabled", false);
			$("#toolbarIdbtnStart").attr("disabled", false);
			$("#toolbarIdbtnBan").attr("disabled", true);
			$("#toolbarIdbtnTreat").attr("disabled", true);
			$("#toolbarIdtreat_1").attr("disabled", true);
			$("#toolbarIdbtnService").attr("disabled", true);
			$("#toolbarIdbtndrug").attr("disabled", true);
			$("#toolbarIdhandling_3").attr("disabled", true);
			$("#toolbarIdbtnhistory").attr("disabled", true);
			$("#toolbarIdbtnhospitalfee").attr("disabled", true);
			//Begin_HaNv_08052018: chinh lai disable menu khac
			//$("#toolbarIdbtnfunction").attr("disabled", true);
			$("#toolbarIdfunction_8").prop("disabled", true);
			//End_HaNv_08052018
			$("#toolbarIdbtnkh").attr("disabled", true);
		} else if (mode == '1') {//enable tat ca submenu
			$("#toolbarIdbtnPrint").attr("disabled", false);
			$("#toolbarIdbtnStart").attr("disabled", false);
			$("#toolbarIdbtnBan").attr("disabled", false);
			$("#toolbarIdbtnTreat").attr("disabled", false);
			$("#toolbarIdtreat_1").attr("disabled", false);
			$("#toolbarIdbtnService").attr("disabled", false);
			$("#toolbarIdbtndrug").attr("disabled", false);
			$("#toolbarIdhandling_3").attr("disabled", false);
			$("#toolbarIdbtnhistory").attr("disabled", false);
			$("#toolbarIdbtnhospitalfee").attr("disabled", false);
			$("#toolbarIdbtnfunction").attr("disabled", false);
			$("#toolbarIdbtnkh").attr("disabled", false);
			//L2PT-38219
			if (showDonThuoc || NTU_BDT_SHOW_THUOCNHATHUOC == '1') {
				$('[id^=toolbarIddrug_]').show();
				$('[id^=toolbarIdgroup_]').show();
			}
			// duyet mo kham me disable nut Xu tri
			if (isGMHS()) {
				$("#toolbarIdhandling_3").attr("disabled", true);
			}
		} else if (mode == '2') {//xem che do view cho benh nhan da ket thuc kham
			$("#toolbarIdbtnPrint").attr("disabled", false);
			$("#toolbarIdbtnStart").attr("disabled", false);
			$("#toolbarIdbtnBan").attr("disabled", true);
			$("#toolbarIdbtnTreat").attr("disabled", true);
			$("#toolbarIdtreat_1").attr("disabled", true);
			$("#toolbarIdbtnService").attr("disabled", true);
			$("#toolbarIdbtndrug").attr("disabled", true);
			$("#toolbarIdhandling_3").attr("disabled", true);
			$("#toolbarIdbtnhistory").attr("disabled", false);
			$("#toolbarIdbtnhospitalfee").attr("disabled", false);
			//Begin_HaNv_08052018: chinh lai disable menu khac
			//$("#toolbarIdbtnfunction").attr("disabled", true);
			$("#toolbarIdfunction_8").attr("disabled", true);
			//End_HaNv_08052018
			$("#toolbarIdbtnkh").attr("disabled", false);
			if (showBenhanALL != '') {
				$("#toolbarIdbtnBan").attr("disabled", false);
				$("#toolbarIdban_1").hide();
				$("#toolbarIdban_2").hide();
				$("#toolbarIdban_3").hide();
				$("#toolbarIdban_4").hide();
				if(showBenhanALL.indexOf(",")  == -1) {
					$("#toolbarIdban_" + showBenhanALL).show();
				} else {
					showBenhanALL.split(',').forEach(function (el) {
						$("#toolbarIdban_" + el).show();
					});
				}
			}

			//HungND - L2PT-64728
			if (showDonThuoc || NTU_BDT_SHOW_THUOCNHATHUOC == '1') {
				$("#toolbarIdbtndrug").attr("disabled", false);
				$('[id^=toolbarIddrug_]').hide();
				$('[id^=toolbarIdgroup_]').hide();
				if(showDonThuoc){
					$("#toolbarIdgroup_2").show();
					$("#toolbarIddrug_6").show();
				}
				if(NTU_BDT_SHOW_THUOCNHATHUOC == '1'){
					$("#toolbarIdgroup_0").show();
					$("#toolbarIddrug_dtnhathuoc").show();
				}
			}
			//HungND - L2PT-64728 END

			if (cf.NTU02D170_FORMPHIEU_CSNB == '1') {
				$("#toolbarIdbtnTreat").attr("disabled", false);
				$("#toolbarIdtreat_1").attr("disabled", false);
				$('[id^=toolbarIdtreat]').hide();
				$("#toolbarIdtreatdt_csnb").show();
			}
		} else if (mode == '3') {//truong hop khoa nhan chuyen dieu tri ket hop
			$("#toolbarIdbtnPrint").attr("disabled", false);
			$("#toolbarIdbtnStart").attr("disabled", false);
			$("#toolbarIdbtnBan").attr("disabled", true);
			$("#toolbarIdbtnTreat").attr("disabled", true);
			$("#toolbarIdtreat_1").attr("disabled", true);
			$("#toolbarIdbtnService").attr("disabled", true);
			$("#toolbarIdbtndrug").attr("disabled", true);
			$("#toolbarIdhandling_3").attr("disabled", true);
			$("#toolbarIdbtnhistory").attr("disabled", false);
			$("#toolbarIdbtnhospitalfee").attr("disabled", false);
			//Begin_HaNv_08052018: chinh lai disable menu khac
			//$("#toolbarIdbtnfunction").attr("disabled", true);
			$("#toolbarIdfunction_8").attr("disabled", true);
			//End_HaNv_08052018
			$("#toolbarIdbtnkh").attr("disabled", false);
		}
		$("#toolbarIdbtnhandling_8").attr("disabled", true);
		if (_kbdtkhid > 0 && NTU_DOITEN_KCK != '1') {
			$("#toolbarIdhandling_3").attr("disabled", true);
			$("#toolbarIdbtnkh").attr("disabled", true);
		}
		if ($("#hidTRANGTHAIKHAMBENH").val() == '4' && $("#hidHINHTHUCRAVIENID").val() == '5' && $("#hidKHAMBENHID_TIEP").val() != '') {
			$("#toolbarIdhandling_3").attr("disabled", true);
		}

	}
	//view thong tin chi tiet benh nhan
	function viewDetail(rowId) {
		var rowData = $('#' + _gridId).jqGrid('getRowData', rowId);
		if (rowData != null) {
			//START L2HOTRO-13545
			var tamung;
			var tongtien;
			var danop;
			var bhyt_nop;
			//END L2HOTRO-13545
			//L2PT-59003 start ttlinh
			var _par = [];
			_par.push({
				"name" : "[0]",
				"value" : rowData.HOSOBENHANID
			}, {
				"name" : "[1]",
				"value" : rowData.KHAMBENHID
			});
			if (is_tiensu_diungthuoc) {
				var results = jsonrpc.AjaxJson.getOneValue("NTU02D021.TSDIUNG", _par)
				if (results > 0) {
					$.bootstrapGrowl(('Bệnh nhân có tiền sử dị ứng ').fontsize(5) + '<a onclick="openPopupDiUng()">(Mở)</a>', {
						type : 'warning',
						delay : 6000,
						width : 500,
						offset : {
							from : "top",
							amount : 50
						},
					});
				}
			}
			if (is_tt_tenga) {
				var results = jsonrpc.AjaxJson.getOneValue("NTU02D021.TENGA", _par)
				if (results > 0) {
					$.bootstrapGrowl(('Bệnh nhân có thông tin té ngã ').fontsize(5) + '<a onclick="openPopupTeNga()">(Mở)</a>', {
						type : 'warning',
						delay : 6000,
						width : 500,
						offset : {
							from : "top",
							amount : 50
						},
					});
				}
			}
			//L2PT-59003 end
			var sql_par = [ rowData.KHAMBENHID ];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NT.005", sql_par.join('$'));
			if (data_ar != null && data_ar.length > 0) {
				var data = data_ar[0];
				data.NGAYSINH = data.NGAYSINH + tinhTuoi(data.NGAYSINH);
				FormUtil.clearForm("divThongTinBenhNhan", "");
				FormUtil.setObjectToForm("divThongTinBenhNhan", "", data);
				//end:
				$("#spKHOA").html(data.KHOA);
				$("#spPHONG").html(data.PHONG);
				if (data.HTRVID == -1 || data.HTRVID == 5) {
					$('#lblNGAYRAVIEN').html('');
				}
				if (data.BHYT_LOAIID == 4) {
					$("#lblTUYEN").css('color', 'red');
				} else {
					$("#lblTUYEN").css('color', 'black');
				}
				showTTNoThe();
				$('#lblTENLOAIBENHAN_KB').html("Loại bệnh án: " + data.TENLOAIBENHAN_KB); // L2PT-122433
			}
			$('#lblTUYEN').text(data.TUYEN + (data.SUBTUYEN != "" ? (' (' + data.SUBTUYEN + ')') : ''));
			//cap nhat thong tin cho cac input hidden
			$("#hidKHAMBENHID").val(rowData.KHAMBENHID);
			$("#hidBENHNHANID").val(rowData.BENHNHANID);
			$("#hidTIEPNHANID").val(rowData.TIEPNHANID);
			$("#hidHOSOBENHANID").val(rowData.HOSOBENHANID);
			$("#hidDOITUONGBENHNHANID").val(rowData.DOITUONGBENHNHANID);
			$("#hidMABENHNHAN").val(rowData.MABENHNHAN);
			$("#hidHINHTHUCVAOVIENID").val(rowData.HINHTHUCVAOVIENID);
			$("#hidLOAITIEPNHANID").val(rowData.LOAITIEPNHANID);
			$("#hidLOAIBENHANID").val(rowData.LOAIBENHANID);
			$("#hidTHOIGIANVAOVIEN").val(rowData.THOIGIANVAOVIEN);
			$("#hidTRANGTHAIKHAMBENH").val(rowData.TRANGTHAIKHAMBENH);
			$("#hidTRANGTHAITIEPNHAN").val(rowData.TRANGTHAITIEPNHAN);
			$("#hidKHOAID").val(rowData.KHOAID);
			$("#hidPHONGID").val(rowData.PHONGID);
			$("#hidTENPHONG").val(rowData.TENPHONG);
			$("#hidUserID").val(opt.user_id);
			$("#hidHisId").val(opt.hospital_id);
			//START -- hongdq - 05032018
			$("#hidMABENHAN").val(rowData.MABENHAN);
			$("#hidMABENHNHAN").val(rowData.MABENHNHAN);
			$("#hidTENBENHNHAN").val(rowData.TENBENHNHAN);
			//END --  hongdq - 05032018
			$("#hidNAMSINH").val(rowData.NAMSINH);//L2HOTRO-10747
			//Begin_HaNv_16042018: Bổ sung thêm biến hidden cho form sàng lọc
			$("#hidGIOITINH").val(rowData.GIOITINH);
			$("#hidHINHTHUCRAVIENID").val(rowData.HINHTHUCRAVIENID);
			$("#hidKHAMBENHID_TIEP").val(data.KHAMBENHID_TIEP);
			cccd = data.CMND;
			ma_bhyt = data.MABHYT;
			//End_HaNv_16042018
			if(showTTBNWidget) {
				$('#divMsg').show();
				if (data.THUOCTUONGTACID != '') {
					$('#divMsg').css('color', 'blue');
				}
				$('#lblMSG_MABENHAN').html($("#hidMABENHAN").val());
				$('#lblMSG_TENBENHNHAN').html($("#hidTENBENHNHAN").val());
				$('#lblMSG_NGAYSINH').html(rowData.NGAYSINH);
				$('#lblMSG_GIOITINH').html(rowData.GIOITINH);
				$('#lblMSG_DIACHI').html(rowData.DIACHI);
				if(cf.HIS_SHOW_TTBENHAN_NGAYGIUONG == '1') {//Doanpv_240201: L2PT-74404
					var txtbs = ' | Phòng :' + data.PHONG + ' | Giường :' + data.GIUONG;
					$('#lblMSG_GIUONGPHONG').html(txtbs);
				}
				if(cf.HIS_SHOW_TTBENHAN_BS == '1') {//HaNv_220623: L2PT-45650
					var txtbs = ' | ' + data.THOIGIANVAOVIEN;
					if (data.CHANDOANVAOKHOA) {
						txtbs = txtbs + ' | ' + data.CHANDOANVAOKHOA;
					}
					if (data.MABHYT) {
						txtbs = txtbs + ' | ' + data.MABHYT;
					}
					$('#lblMSG_BOSUNG').html(txtbs);
				}
				if(cf.NTU_TTBN_SHOW_CCCD == '1') {//L2PT-114638 ttlinh
					var txtbs = '';
					if(rowData.CCCD) {
						var txtbs = ' | Số CCCD :' + rowData.CCCD;
					}
					if(rowData.NGAYCAP_CCCD) {
						txtbs = txtbs + ' (Ngày cấp :' + rowData.NGAYCAP_CCCD + ')';
					}
					$('#lblMSG_CCCD').html(txtbs);
				}
				if (rowData.DOITUONGBENHNHANID == '1') {
					if (parseInt(rowData.SONGAYBHYT) > 0 && songayCbHanThe >= parseInt(rowData.SONGAYBHYT)) {
						$('#lblCHECK_THEBHYT').html(' | THẺ BẢO HIỂM SẮP HẾT HẠN');
					} else if ((parseInt(rowData.SONGAYBHYT) <= 0)) {
						$('#lblCHECK_THEBHYT').html(' | THẺ BẢO HIỂM HẾT HẠN');
					} else {
						$('#lblCHECK_THEBHYT').html('');
					}
				} else {
					$('#lblCHECK_THEBHYT').html('');
				}
				if(cf.HIS_SHOW_THONGTIN_TONGPHIEU == '1') {
					var fl_ca_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D021.EV002", rowData.HOSOBENHANID);
					if (fl_ca_ar != null && fl_ca_ar.length > 0) {
						$('#lblTHONGTINPHEU').html(' | Tổng phiếu/Đã ký/Tổng loại phiếu: '
							+ fl_ca_ar[0].TONGPHIEU + '/'
							+ fl_ca_ar[0].DAKY + '/'
							+ fl_ca_ar[0].LOAIPHIEU);
					}
				}
			}
			var vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.06", rowData.TIEPNHANID);
			if (vp_ar != null && vp_ar.length > 0) {
				var data = vp_ar[0];
				$('#lblTAMUNG').html(formatNumber(data.TAMUNG) + "đ");
				tamung = Number(data.TAMUNG);//START L2HOTRO-13545
			}
			vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.05", rowData.TIEPNHANID);
			if (vp_ar != null && vp_ar.length > 0) {
				var data = vp_ar[0];
				$('#lblTONGCHIPHI').html(formatNumber(data.TONGTIENDV) + "đ");
				$('#lblBAOHIEMTHANHTOAN').html(formatNumber(data.BHYT_THANHTOAN) + "đ");
				$('#lblBNDANOP').html(formatNumber(data.DANOP) + "đ");
				// L2PT-9324;L2PT-10784 duonghn start
				$('#lblTUCONLAI').html(formatNumber(data.TAMUNG_CONLAI) + "đ");
				// L2PT-9324;L2PT-10784 duonghn end
				tongtien = Number(data.TONGTIENDV);//START L2HOTRO-13545
				danop = Number(data.DANOP);//START L2HOTRO-13545
				bhyt_nop = Number(data.BHYT_THANHTOAN);//START L2HOTRO-13545
			}
			setSLPhieuDVInTab($("#hidKHAMBENHID").val(), "", "1");
			//START -- L2K74TW-663
			$("#hidNGAYTIEPNHAN").val(rowData.NGAYTIEPNHAN);
			var tgVaovien = moment(rowData.NGAYTIEPNHAN, 'DD/MM/YYYY');
			var ngayHientai = "";
			var ngaytaoPhieuSoketDT = "";
			var pars = [ 'NTU_K74VP_SOKETDT_MESS' ];
			var data_CH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
			if (data_CH == '1') {
				var sql_par = [];
				sql_par.push({
					"name" : "[0]",
					"value" : rowData.KHAMBENHID
				}, {
					"name" : "[1]",
					"value" : rowData.BENHNHANID
				});
				var data_ar = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT02K015.SKPDT", sql_par);
				if (data_ar != null && data_ar != "[]") {
					ngayHientai = moment(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'), 'DD/MM/YYYY');
					data_ar = JSON.parse(data_ar);
					ngaytaoPhieuSoketDT = moment(data_ar[0].NGAYTAO, 'DD/MM/YYYY');
					if ((ngayHientai - ngaytaoPhieuSoketDT) / 86400000 >= 15) {
						return DlgUtil.showMsg("Bệnh nhân đã tới ngày cần tạo phiếu sơ kết điều trị");
					}
				} else {
					ngayHientai = moment(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY'), 'DD/MM/YYYY');
					if ((ngayHientai - tgVaovien) / 86400000 >= 15) {
						return DlgUtil.showMsg("Bệnh nhân đã tới ngày cần tạo phiếu sơ kết điều trị");
					}
				}
			}
			//END -- L2K74TW-663
			//START L2HOTRO-13545
			var pars = [ 'NTU_BND_13545_TB' ];
			var data_CH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
			if (opt.hospital_id == "951" && data_CH == '1') {
				var result = 0;
				if (tamung != null && tongtien != null && danop != null) {
					result = tamung + danop + bhyt_nop - tongtien;
				}
				if (result < 1000000) {
					$.bootstrapGrowl('Bệnh nhân sắp hết tiền ký quỹ'.fontsize(5), {
						type : 'warning',
						delay : 3000,
						width : 700,
						offset : {
							from : "top",
							amount : 50
						},
					});
				}
			}
			//END L2HOTRO-13545
			//START L2HOTRO-11975
			if (rowData.MA_BHYT != '') {
				var _par = [];
				_par.push({
					"name" : "[0]",
					"value" : rowData.BENHNHANID
				});
				_par.push({
					"name" : "[1]",
					"value" : rowData.HOSOBENHANID
				});
				var check_duyet_bhyt = jsonrpc.AjaxJson.getOneValue("DUYET_BHYT", _par);
				if (check_duyet_bhyt == "0") {
					$.bootstrapGrowl('Bệnh nhân sắp hết tiền ký quỹ'.fontsize(5), {
						type : 'warning',
						delay : 3000,
						width : 700,
						offset : {
							from : "top",
							amount : 100
						},
					});
				}
			}
			//END L2HOTRO-11975
			// start jira L2PT-6966
			if (nguoiNha) {
				var tenBNNguoiNha = getTenBNNguoiNha(rowData.HOSOBENHANID);
				$('#lblTENBENHNHANNGUOINHA').html(tenBNNguoiNha);
			}
			// end jira L2PT-6966
		}
		$('#hidPHONGDNID').val(opt._subdeptId);
	}
	function setSLPhieuDVInTab(i_khambenhid, i_hosobenhanid, i_type) {
		var _par = [ i_khambenhid, i_hosobenhanid, i_type ];
		if (cf.HIS_SOPHIEU_TOOLBAR_NOITRU == '1') {
			_par = [i_khambenhid, i_hosobenhanid, '2'];
		}
		var dataSL = jsonrpc.AjaxJson.ajaxCALL_SP_O("NT021.TAB.SOPHIEU", _par.join('$'));
		//L2PT-11572 lay so luong skdt
		if (show_btn_skdt) {
			var parDLSKDT = [];
			parDLSKDT.push({
				"name" : "[0]",
				"value" : $("#hidKHAMBENHID").val()
			}, {
				"name" : "[1]",
				"value" : $("#hidBENHNHANID").val()
			});
			var dataSLSKDT = jsonrpc.AjaxJson.getOneValue("NT021.TAB.SLSKDT", parDLSKDT);
			if (parseInt(dataSLSKDT) > 0) {
				$('#toolbarIdbtnSKDT').text("SKĐT(" + dataSLSKDT + ")");
			} else {
				$('#toolbarIdbtnSKDT').text("SKĐT");
			}
		}
		//L2PT-28433
		if(cf.NTU_WIDGET_PCHN_SHOW == '1'){
			var parPHCN=[];
        	parPHCN.push({"name":"[0]","value":$("#hidKHAMBENHID").val()});
      		var dataSLPHCN = jsonrpc.AjaxJson.getOneValue("NT021.TAB.SLPHCN", parPHCN);
      		if(parseInt(dataSLPHCN) > 0){
      			$('#idtabKythuat_PHCN').text("Kỹ thuật PHCN("+dataSLPHCN+")");
				$('#idtabKythuat_PHCN').show();
      		}else{
      			$('#idtabKythuat_PHCN').text("Kỹ thuật PHCN");
				$('#idtabKythuat_PHCN').hide();
      		}
		}
		//L2PT-11572
		if (dataSL != null && dataSL.length > 0) {
			var rowSL = dataSL[0];
			if (rowSL.SLCDHA != 0) {
				$('#idTabCDHA').text("CĐHA(" + rowSL.SLCDHA + ")");
				$('#idTabCDHA').show();
			} else {
				$('#idTabCDHA').text("CĐHA");
				_showTabUx2023 == '1' ? $('#idTabCDHA').hide() : $('#idTabCDHA').show();
			}
			if (rowSL.SLCHUYENKHOA != 0) {
				$('#idTabChuyenKhoa').text("PTTT(" + rowSL.SLCHUYENKHOA + ")");
				$('#idTabChuyenKhoa').show();
			} else {
				$('#idTabChuyenKhoa').text("PTTT");
				_showTabUx2023 == '1' ? $('#idTabChuyenKhoa').hide() : $('#idTabChuyenKhoa').show();
			}
			if (rowSL.SLXN != 0) {
				$('#idTabXetNghiem').text("Xét nghiệm(" + rowSL.SLXN + ")");
				$('#idTabXetNghiem').show();
			} else {
				$('#idTabXetNghiem').text("Xét nghiệm");
				_showTabUx2023 == '1' ? $('#idTabXetNghiem').hide() : $('#idTabXetNghiem').show();
			}
			if (rowSL.SLTRUYENMAU != 0) {
				$('#idTabPhieuTruyenMau').text("Truyền máu(" + rowSL.SLTRUYENMAU + ")");
				$('#idTabPhieuTruyenMau').show();
			} else {
				$('#idTabPhieuTruyenMau').text("Truyền máu");
				_showTabUx2023 == '1' ? $('#idTabPhieuTruyenMau').hide() : $('#idTabPhieuTruyenMau').show();
			}
			if (rowSL.SLTHUOC != 0) {
				$('#idTabThuoc').text("Thuốc(" + rowSL.SLTHUOC + ")");
				$('#idTabThuoc').show();
			} else {
				$('#idTabThuoc').text("Thuốc");
				_showTabUx2023 == '1' ? $('#idTabThuoc').hide() : $('#idTabThuoc').show();
			}
			if (rowSL.SLMAU != 0) {
				$('#idTabMau').text("Máu(" + rowSL.SLMAU + ")");
				$('#idTabMau').show();
			} else {
				$('#idTabMau').text("Máu");
				_showTabUx2023 == '1' ? $('#idTabMau').hide() : $('#idTabMau').show();
			}
			//DoanPV_20210603 BVTM-3343
			if (rowSL.SLDICHVUKHAC != 0) {
				$('#idTabDichVuKhac').text("Dịch vụ khác(" + rowSL.SLDICHVUKHAC + ")");
				$('#idTabDichVuKhac').show();
			} else {
				$('#idTabDichVuKhac').text("Dịch vụ khác");
				_showTabUx2023 == '1' ? $('#idTabDichVuKhac').hide() : $('#idTabDichVuKhac').show();
			}
			if (rowSL.SLVATTU != 0) {
				$('#idTabVatTu').text("Vật tư(" + rowSL.SLVATTU + ")");
				$('#idTabVatTu').show();
			} else {
				$('#idTabVatTu').text("Vật tư");
				_showTabUx2023 == '1' ? $('#idTabVatTu').hide() : $('#idTabVatTu').show();
			}
			if (rowSL.SLVANCHUYEN != 0) {
				$('#idTabVC').text("Vận chuyển(" + rowSL.SLVANCHUYEN + ")");
				$('#idTabVC').show();
			} else {
				$('#idTabVC').text("Vận chuyển");
				_showTabUx2023 == '1' ? $('#idTabVC').hide() : $('#idTabVC').show();
			}
			if (rowSL.SLCHAMSOC != 0) {
				$('#idTabChamSoc').text("Chăm sóc(" + rowSL.SLCHAMSOC + ")");
				$('#idTabChamSoc').show();
			} else {
				$('#idTabChamSoc').text("Chăm sóc");
				_showTabUx2023 == '1' ? $('#idTabChamSoc').hide() : $('#idTabChamSoc').show();
			}
			if (rowSL.SLSUATAN != 0) {
				$('#idTabSuatAn').text("Suất ăn(" + rowSL.SLSUATAN + ")");
				$('#idTabSuatAn').show();
			} else {
				$('#idTabSuatAn').text("Suất ăn");
				_showTabUx2023 == '1' ? $('#idTabSuatAn').hide() : $('#idTabSuatAn').show();
			}
			if (rowSL.SLTRUYENDICH != 0) {
				$('#idTabTruyenDich').text("Truyền dịch(" + rowSL.SLTRUYENDICH + ")");
				$('#idTabTruyenDich').show();
			} else {
				$('#idTabTruyenDich').text("Truyền dịch");
				_showTabUx2023 == '1' ? $('#idTabTruyenDich').hide() : $('#idTabTruyenDich').show();
			}
			if (rowSL.SLPHANUNGTHUOC != 0) {
				$('#idTabPhanUngThuoc').text("Phản ứng thuốc(" + rowSL.SLPHANUNGTHUOC + ")");
				$('#idTabPhanUngThuoc').show();
			} else {
				$('#idTabPhanUngThuoc').text("Phản ứng thuốc");
				_showTabUx2023 == '1' ? $('#idTabPhanUngThuoc').hide() : $('#idTabPhanUngThuoc').show();
			}
			if (rowSL.SLHOICHAN != 0) {
				$('#idTabHoiChan').text("Hội chẩn(" + rowSL.SLHOICHAN + ")");
				$('#idTabHoiChan').show();
			} else {
				$('#idTabHoiChan').text("Hội chẩn");
				_showTabUx2023 == '1' ? $('#idTabHoiChan').hide() : $('#idTabHoiChan').show();
			}
			if (rowSL.SLDIEUTRI != 0) {
				$('#idTabDieuTri').text("Điều trị(" + rowSL.SLDIEUTRI + ")");
				$('#idTabDieuTri').show();
			} else {
				$('#idTabDieuTri').text("Điều trị");
				_showTabUx2023 == '1' ? $('#idTabDieuTri').hide() : $('#idTabDieuTri').show();
			}
			if (rowSL.SLPHIEUSOSINH != 0) {
				$('#idPhieuSoSinh').text("Phiếu Sơ Sinh(" + rowSL.SLPHIEUSOSINH + ")");
				$('#idPhieuSoSinh').show();
			} else {
				$('#idPhieuSoSinh').text("Phiếu Sơ Sinh");
				_showTabUx2023 == '1' ? $('#idPhieuSoSinh').hide() : $('#idPhieuSoSinh').show();
			}
			if (rowSL.SLNGAYGIUONG != 0) {
				$('#idTabNgayGiuong').text("Ngày giường(" + rowSL.SLNGAYGIUONG + ")");
				$('#idTabNgayGiuong').show();
			} else {
				$('#idTabNgayGiuong').text("Ngày giường");
				_showTabUx2023 == '1' ? $('#idTabNgayGiuong').hide() : $('#idTabNgayGiuong').show();
			}
			if (rowSL.SLCHUCNANGSONG != 0) {
				$('#idTabPTDCNS').text('Chức năng sống(' + rowSL.SLCHUCNANGSONG + ")");
				$('#idTabPTDCNS').show();
			} else {
				$('#idTabPTDCNS').text("Chức năng sống");
				_showTabUx2023 == '1' ? $('#idTabPTDCNS').hide() : $('#idTabPTDCNS').show();
			}

			//HungND - L2PT-71749
			if (rowSL.SLCONGKHAM != 0) {
				$('#idTabCk').text("Khám(" + rowSL.SLCONGKHAM + ")");
				$('#idTabCk').show();
			} else {
				$('#idTabCk').text("Khám");
				_showTabUx2023 == '1' ? $('#idTabCk').hide() : $('#idTabCk').show();
			}
			//HungND - L2PT-71749 END
		}
	}
	/**
	 * hàm xét giá trị mặc định khi null
	 * @param giá trị ban đầu
	 * @param giá trị mặc định
	 * @returns
	 */
	function nvl(value1, value2) {
		if (value1 != null && value1 != '')
			return value1;
		return value2.trim();
	}
	//START HISL2TK-1085
	function inBangKeLPLSO(_tiepnhanid, _dtbnid, _loaitiepnhanid) {
		var opt = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "VP_IN_TACH_BANGKE");
		var IN_BK_VP = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "c");
		var IN_GOP_BKNTRU = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "VPI_GOP_BANGKENTRU");
		//check quan y 15 cac tram xa in bang ke rieng
		var flagTramxa = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01.CHECK.TRAMXA", _tiepnhanid);
		if (!IN_BK_VP)
			IN_BK_VP = 0;
		if (opt == 1) {
			var flag = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T004.10", _tiepnhanid);
			if (_loaitiepnhanid == 0) {
				if (IN_GOP_BKNTRU == 1) {
					inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBBHYTNOITRU_02BV_QD3455_A4');
				} else {
					if (_dtbnid == 1) {
						jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.SINH.STT", _tiepnhanid);
						if (flagTramxa != null && flagTramxa == 1) {
							inPhoiVP('1', _tiepnhanid, 'NTU001_BKCPKCBBHYTNOITRU_02BV_QD3455_TRAMXA_A4');
						} else {
							inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBBHYTNOITRU_02BV_QD3455_A4');
						}
						if (IN_BK_VP == 0 && flag == 1)
							inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBTUTUCNOITRU_02BV_QD3455_A4');
					} else {
						if (flag == 1)
							inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NTU001_BKCPKCBTUTUCNOITRU_02BV_QD3455_A4');
					}
				}
			} else {
				if (_dtbnid == 1) {
					jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.SINH.STT", _tiepnhanid);
					if (flagTramxa != null && flagTramxa == 1) {
						inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_TRAMXA_A4');
					} else {
						inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NGT001_BKCPKCBBHYTNGOAITRU_01BV_QD3455_A4');
					}
					if (IN_BK_VP == 0 && flag == 1)
						inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NGT035_BKCPKCBTUTUCNGOAITRU_A4');
				} else {
					if (flag == 1)
						inPhoiVP('1', _tiepnhanid, 'DIEUDUONG_NGT035_BKCPKCBTUTUCNGOAITRU_A4');
				}
			}
		} else {
			jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.SINH.STT", _tiepnhanid);
			if (_loaitiepnhanid == 0) {
				inPhoiVP('1', _tiepnhanid, 'NTU001_BKCPKCBNOITRU_02BV_QD3455_A4');
			} else {
				inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCBNGOAITRU_01BV_QD3455_A4');
			}
		}
		// in bang ke hao phi neu co
		var flag_haophi = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T005.11", _tiepnhanid);
		var opt_haophi = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "VP_IN_BANGKE_HAOPHI");
		if (opt_haophi == 1) {
			if (flag_haophi == 1) {
				inPhoiVP('1', _tiepnhanid, 'NGT001_BKCPKCB_HAOPHI_01BV_QD3455_A4');
			}
		}
	}
	function inPhoiVP(_inbangkechuan, _tiepnhanid, _report_code) {
		var par = [];
		par.push({
			name : 'inbangkechuan',
			type : 'String',
			value : _inbangkechuan.toString()
		});
		par.push({
			name : 'tiepnhanid',
			type : 'String',
			value : _tiepnhanid.toString()
		});
		var typeExport = "pdf";//$("#sltInBieuMau").val();
		CommonUtil.openReportGet('window', _report_code, typeExport, par);
	}
	//END HISL2TK-1085
	function _openReport(param, reportName, format1) {
		var format = format1 != "rtf" && format1 != "docx" ? "pdf" : format1;
		if ($("#hidKHAMBENHID").val() == "-1") {
			DlgUtil.showMsg('Hãy chọn bệnh nhân muốn in.');
			return;
		}
		if (format == "pdf") {
			param.push({
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			});
			openReport('window', reportName, format, param);
		} else {
			param.push({
				name : 'i_hid',
				type : 'String',
				value : opt.hospital_id
			});
			param.push({
				name : 'i_sch',
				type : 'String',
				value : opt.db_schema
			});
			param.push({
				name : 'khambenhid',
				type : 'String',
				value : $("#hidKHAMBENHID").val()
			});
			var rpName = reportName + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') + "." + format;
			CommonUtil.inPhieu('window', reportName, format, param, rpName);
		}
	}
	function checkRolePhieuIn() {
		var report_url = window.location.protocol + "//" + window.location.host;
		var link = window.location.href;
		var linkcat = link.slice(report_url.length, link.length);
		//set hien thi phieu in theo cau hinh
		var _type = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "HIS_HIENTHI_PHIEUIN");
		if (_type == '1') {
			var _parram = [ linkcat, opt._userGroupId ];
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC81S0012.04", _parram.join('$'));
			if (result != null && result.length > 0) {
				for (var i = 0; i < result.length; i++) {
					$('#' + result[i].PHIEUINID).hide();
				}
			}
		} else {
			var _parPQ = linkcat + '$';
			var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC81S001.04", _parPQ);
			for (var i = 0; i < result.length; i++) {
				$('#' + result[i].PHIEUINID).hide();
			}
		}
	};
	function getCauHinhLayTatCaPhong() {
		var res = 0;
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'NTU_BDT_LTCP');
		if (data_ar != null && data_ar.length > 0) {
			if (data_ar[0].NTU_BDT_LTCP == '1') {
				res = 1;
			}
		}
		return res;
	}
	$('#chkBACSYDIEUTRI').change(function() {
		if (this.checked) {
			$('#divCboBacSi').show();
		} else {
			$('#divCboBacSi').hide();
		}
	});
	$('#cboBACSIID').change(function() {
		loadGridData();
	});
	$('#cboTRANGTHAIKHAMBENH').change(function() {
		if ($('#cboTRANGTHAIKHAMBENH').val() == '' || $('#cboTRANGTHAIKHAMBENH').val() == '9') {
			var denngay = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
			$("#txtTG_NHAPVIEN_DEN").val(denngay);
			var _time = formatNumber(cf.HIS_TIMKIEM_BNNOITRU_KETTHUC) == 0 ? 30 : formatNumber(cf.HIS_TIMKIEM_BNNOITRU_KETTHUC);
			var tungay = moment($('#txtTG_NHAPVIEN_DEN').val(), "DD/MM/YYYY").add(-_time, 'days').format('DD/MM/YYYY');
			$("#txtTG_NHAPVIEN_TU").val(tungay);
			$("#chkTIMTHEONGAY").prop('checked', true);
		}
		if(cf.NTU_CHANGE_CBO_TT == '1') {
			if($('#cboTRANGTHAIKHAMBENH').val() == '9' || $('#cboTRANGTHAIKHAMBENH').val() == '15') {
				$("#chkTIMTHEONGAYRA").prop('checked', true);
				$("#chkTIMTHEONGAY").prop('checked', false);
			}
			else {
				$("#chkTIMTHEONGAYRA").prop('checked', false);
				$("#chkTIMTHEONGAY").prop('checked', true);
			}
		}
		loadGridData();
	});
	$('#cboDOITUONGBENHNHAN').change(function() {
		loadGridData();
	});
	$('#cboPHONGID').on('change', function(e) {
		loadGridData();
	});
	$('#cboNGUOINHA').on('change', function(e) {
		loadGridData();
	});
	//Beg_HaNv_150523: check phiếu điều trị và đơn thuốc trong ngày - L2PT-40227
	$('#chkCHUATAOPDT').click(function() {
		loadGridData();
	});
	$('#chkCHUATAOTHUOC').click(function() {
		loadGridData();
	});
	//End_HaNv_150523
	function showTTNoThe() {
		if ($('#lblMABHYT').text().indexOf("NTH") != "-1") {
			$("#divNOTHE").show();
			$("#chkNOTHE").prop("checked", true);
		} else {
			$("#divNOTHE").hide();
			$("#chkNOTHE").prop("checked", false);
		}
	}
	function tinhTuoi(bNgaySinh) {
		var sysDate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		var ngayHT = moment(sysDate, 'DD/MM/YYYY HH:mm:ss').toDate();
		var bNgayHT = ngayHT.getDate();
		var bThangHT = ngayHT.getMonth() + 1; // Thang 1 la 0
		var bNamHT = ngayHT.getFullYear()
		var tuoi;
		var dvTuoi;
		if (bNgaySinh.length == 10) {
			var bNam = bNgaySinh.substr(6, 4);
			var bThang = bNgaySinh.substr(3, 2);
			var bNgay = bNgaySinh.substr(0, 2);
			if (bNamHT > bNam) {
				var birthDate = moment(bNgaySinh, 'DD/MM/YYYY');
				var sysDate = moment(sysDate, 'DD/MM/YYYY HH:mm:ss'); // SONDN - L2PT-14112 - NEW
				var diff = sysDate.diff(birthDate, 'months');
				// SONDN 06/01/2019 L2PT-13842
				var _modeTinhTuoi = Number(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_TINHTUOI'));
				if (diff < _modeTinhTuoi) {
					if (diff == 0) {
						tuoi = sysDate.diff(birthDate, 'days');
						dvTuoi = 'Ngày';
					} else {
						tuoi = diff;
						dvTuoi = 'Tháng';
					}
				} else {
					tuoi = bNamHT - bNam;
					dvTuoi = 'Tuổi';
				}
			} else if (bThangHT > bThang) {
				var ngayHTTemp = moment(ngayHT).format('DD/MM/YYYY');
				var diff = diffDate(ngayHTTemp, bNgaySinh, 'DD/MM/YYYY', 'days');
				var _modeTinhTuoi_ngay = Number(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'NGT_TINHTUOI_NGAY'));
				if (_modeTinhTuoi_ngay == '0') {
					_modeTinhTuoi_ngay = 30;
				}
				if (diff < _modeTinhTuoi_ngay) {
					tuoi = diff;
					dvTuoi = 'Ngày';
				} else {
					tuoi = bThangHT - bThang;
					dvTuoi = 'Tháng';
				}
			} else if (bNgayHT > bNgay) {
				tuoi = bNgayHT - bNgay;
				dvTuoi = 'Ngày';
			} else {
				tuoi = 1;
				dvTuoi = 'Ngày';
			}
		} else if (bNgaySinh.length == 4) {
			tuoi = bNamHT - bNgaySinh;
			dvTuoi = 'Tuổi';
		}
		return ' (' + tuoi + ' ' + dvTuoi + ')';
	}
	function isGMHS() {
		if ($("#hidKHAMBENHID").val() == "-1") {
			return false;
		}
		var obj = {
			khambenhid : $("#hidKHAMBENHID").val(),
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H001.L04", param);
		if (result == '1') {
			return true;
		} else {
			return false;
		}
	}
	//L2PT-49249
	function _dayhosoct() {
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_TUDONG_DAYHSCT_LUU') == "1"){
			var myVar = {
					loaigiay : '1',
					khambenhid : $("#hidKHAMBENHID").val() + "",
					hosobenhanid : $("#hidHOSOBENHANID").val() 
				};
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI02G011.TTHS", JSON.stringify(myVar));
			if(ret == '1'){
				var retHSCT = ajaxSvc.InsrWS.guiHS_CHUNGTU2076(JSON.stringify({
					"MA_LK" : $("#hidTIEPNHANID").val() + "",
					"CBO_GIAY" : "1"
				}));
				var msgHSCT = "";
				if (retHSCT && retHSCT != "") {
					var jretHSCT = $.parseJSON(retHSCT);
					if (jretHSCT.MaKetQua && jretHSCT.MaKetQua == "200") {
						var myVar = {
							kieu : '2',
							loaigiay : '1',
							khambenhid : $("#hidKHAMBENHID").val() + "",
							hosobenhanid : $("#hidHOSOBENHANID").val() 
						};
						var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI02G011.GUIHSCT", JSON.stringify(myVar));
						msgHSCT = "Gửi hồ sơ chứng từ thành công";
					} else if (jretHSCT.maKetQua == "401") {
						msgHSCT = "Lỗi xác thực cổng BHXH khi gửi HSCT";
					} else if (jretHSCT.maKetQua == "500") {
						msgHSCT = "Lỗi từ cổng BHXH khi gửi HSCT";
					} else {
						msgHSCT = "Lỗi gửi hồ sơ chứng từ";
					}
				} else {
					msgHSCT = "Có lỗi xảy ra";
				}
				var msg_hsct = "Mã LK " + $("#hidTIEPNHANID").val() + " " + msgHSCT + "\n"; 
			}
		}
	}
	
	function getCauHinh(tenCauHinh) {
		var giaTriCauHinh = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", tenCauHinh);
		return giaTriCauHinh;
	}
	// Xử lý sự kiện liên quan ký CA => START
	function _kyCaRpt(_params) {
		//check ky cap hay khong
		var _rptCode = _params.find(element => element.name == 'RPT_CODE')['value'];
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
		if (data_ar != null && data_ar.length > 0) {
			var row = data_ar[0];
			var loaiky = row.LOAIKY;
		}
		if (loaiky == '1') {
			CommonUtil.kyCA(_params, '', '', '', '', '1');
		} else {
			CommonUtil.kyCA(_params);
		}
		EventUtil.setEvent("eventKyCA", function(e) {
			DlgUtil.showMsg(e.res);
		});
	}
	// Xử lý sự kiện liên quan ký CA => END
	function updateNgayRaVienTheoTTRaVien() {
		var obj = {
			khambenhid : $("#hidKHAMBENHID").val() + "",
			tiepnhanid : $("#hidTIEPNHANID").val() + ""
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D021.L01", param);
		if (result == '0') {
			console.log("lỗi hàm updateNgayRaVienTheoTTRaVien");
		}
	}
	function getIconCell1(row) {
		// đa khoa quảng trị
		// hiển thị icon mổ ở khoa ban đầu nếu có lịch mổ đã được duyệt
		if (opt.hospital_id == 30360 && row.DADUYETMO == '1') {
			return '<center><img src="../common/image/knife.png" width="15px"></center>';
		}
		// đa khoa quảng trị
		// hiển thị icon xanh dương ở khoa điều trị kết hợp và khoa trước đó (khoa chuyển)
		if (opt.hospital_id == 30360 && (row.KHAMBENHDTKHID != '' || row.ISCHUYENDTKH == '1')) {
			return '<center><img src="../common/image/Pin.png" width="15px"></center>';
		}
		// bưu điện hà nội
		// hiển thị icon xanh dương ở khoa chuyển điều trị kết hợp
		if (cf.HIS_SHOW_DTKH_KHOACHUYENDEN == '1' && row.KHAMBENHDTKHID != '') {
			return '<center><img src="../common/image/Pin.png" width="15px"></center>';
		}
		var trangThaiKhamBenh = parseInt(row.TRANGTHAIKHAMBENH);
		// đang khám
		if (trangThaiKhamBenh == 4) {
			// có xử trí và xử trí khác chuyển khoa
			if (row.HINHTHUCRAVIENID != '' && row.HINHTHUCRAVIENID != '5') {
				return '<center><img src="../common/image/Dollar.png" width="15px"></center>';
			} else {
				return '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
			}
		}
		// kết thúc khám
		if (trangThaiKhamBenh == 9) {
			return '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
		}
		// chờ khám
		if (trangThaiKhamBenh == 1) {
			return '<center><img src="../common/image/metacontact_away.png" width="15px"></center>';
		}
		// được điều trị kết hợp ở khoa khác
		if (trangThaiKhamBenh == 8) {
			return '<center><img src="../common/image/Pin.png" width="15px"></center>';
		}
		// các trường hợp còn lại
		return '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
	}
	function getLoaiPhongHienTai() {
		var obj = {
			phongid : opt._subdeptId + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D021.L03", param);
		if (result !== '0') {
			return result;
		} else {
			return 'error';
		}
	}
	function getTTGoiKham(khambenhid) {
		var obj = {
			khambenhid : khambenhid + "",
			phongid : opt._subdeptId + "",
			khoaid : opt._deptId + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D021.L04", param);
		if (result && result.length > 0) {
			return result[0];
		} else {
			return null;
		}
	}
	function getTenBNNguoiNha(hosobenhanid) {
		var obj = {
			hosobenhanid : hosobenhanid + ""
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU01H001.L13", param);
		if (result && result != 'EXCEPTION_SQL') {
			return result;
		} else {
			return '';
		}
	}
	function isChuaXepGiuong(khambenhid) {
		var obj = {
			khambenhid : khambenhid + "",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D021.L05", param);
		if (result == '1') {
			return true;
		} else {
			return false;
		}
	}
	function checkDoiTuongBN() {
		var par = [];
		par.push({
			"name" : "[0]",
			"value" : $("#hidTIEPNHANID").val()
		});
		var result = jsonrpc.AjaxJson.getOneValue("NTU02D080.L01", par);
		if (result != $("#hidDOITUONGBENHNHANID").val()) {
			DlgUtil.showMsg('Hãy chọn lại bệnh nhân muốn thao tác do BN đã thay đổi đối tượng');
			return false;
		}
		return true;
	}
	// L2PT-11973;L2PT-8408 duonghn start: tạo toolbar
	function getToolBar(result, CONFIG_NAME) {
		//console.log(JSON.stringify(result));
		var NTU_BDT_TOOLBAR = jsonrpc.AjaxJson.ajaxCALL_SP_X('COM.CAUHINH.CLOB', CONFIG_NAME);
		if (NTU_BDT_TOOLBAR && NTU_BDT_TOOLBAR != 0) {
			try {
				var chBDTToolBar = FormUtil.unescape(NTU_BDT_TOOLBAR);
				//console.log(chBDTToolBar);
				var menuBDTToolBar = JSON.parse(chBDTToolBar);
				result = menuBDTToolBar;
				for (var i = 0; i < result.length; i++) {
					if (result[i].CONFIG && result[i].CONFIG.length > 0) {
						var NTU_MENU_BDT = jsonrpc.AjaxJson.ajaxCALL_SP_X('COM.CAUHINH.CLOB', result[i].CONFIG);
						if (NTU_MENU_BDT && NTU_MENU_BDT != 0) {
							var chMenuBDT = FormUtil.unescape(NTU_MENU_BDT);
							var menuBDT = JSON.parse(chMenuBDT);
							result[i].children = menuBDT;
						}
					}
				}
			} catch (err) {
				console.log(err.message);
			}
		}
		return result;
	}
	// L2PT-11973;L2PT-8408 duonghn end

	function alert() {
		$(".bootstrap-growl").remove();
		var alert = $("#hidMABENHAN").val() + ' | ' + $("#hidTENBENHNHAN").val() + ' | ' + $("#hidNAMSINH").val() + ' | ' + $("#hidGIOITINH").val();
		alert = alert + '<br>' + $("#hidDIACHI").val();
		$.bootstrapGrowl(alert.fontsize(2), {
			type : 'info',
			delay : 0,
			width : 370,
			allow_dismiss: true,
			offset : {
				from : "bottom",
				amount : 50
			},
		});
	}
}
//L2PT-59003 start
function openPopupDiUng() {
	paramInput = {
			benhnhanid : $("#hidBENHNHANID").val(),
			khambenhid : $("#hidKHAMBENHID").val(),
			hosobenhanid : $("#hidHOSOBENHANID").val(),
			tiepnhanid : $("#hidTIEPNHANID").val(),
			mabenhan : $("#hidMABENHAN").val(),
			tenbenhnhan : $("#hidTENBENHNHAN").val(),
			mabenhnhan : $("#hidMABENHNHAN").val()
		};
		dlgPopup = DlgUtil.buildPopupUrl("divDlgDiungThuoc", "divDlg", "manager.jsp?func=../noitru/NTU02D083_DiUngThuoc", paramInput, "Dị ứng thuốc " + ($("#hidTHONGTINBN").val()==null?"":"(" + $("#hidTHONGTINBN").val() + ")"), 1000, 600);
		DlgUtil.open("divDlgDiungThuoc");
}
function openPopupTeNga() {
	let
	myVar = {
		KHAMBENHID : $("#hidKHAMBENHID").val(),
		BENHNHANID : $("#hidBENHNHANID").val(),
		TIEPNHANID : $("#hidTIEPNHANID").val(),
		MABENHNHAN : $("#hidMABENHNHAN").val(),
		HOSOBENHANID : $("#hidHOSOBENHANID").val(),
		MAUBENHPHAMID : 1111,
		DICHVUKHAMBENHID : 2222,
		KHOAID : opt._deptId,
		PHONGID : $("#hidPHONGID").val(),
		LOAI_PHIEU : "NTU_DIEUTRI"
	}
	dlgPopup = DlgUtil.buildPopupUrl("divDlgThemPhieu", "divDlg", "manager.jsp?func=../noitru/NTU02D204_ThemPhieu", myVar, "HIS-Thêm phiếu " + ($("#hidTHONGTINBN").val()==null?"":"(" + $("#hidTHONGTINBN").val() + ")"), window.innerWidth * 0.95,
			window.innerHeight * 0.95);
	DlgUtil.open("divDlgThemPhieu");
}
//L2PT-59003 end
//HungND - L2PT-51531
function checkKetThucKham(){
	var selRowId = $('#' + _gridId).jqGrid('getGridParam', 'selrow');
	var rowData = $('#' + _gridId).jqGrid('getRowData', selRowId);
	if (rowData != null && rowData.TRANGTHAIKHAMBENH == '9' ) {
		DlgUtil.showMsg("Bệnh án đã đóng");
		return true;
	}
	return false
}
//HungND - L2PT-51531 END
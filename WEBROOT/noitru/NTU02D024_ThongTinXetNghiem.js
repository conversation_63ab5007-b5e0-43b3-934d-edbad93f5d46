(function($) {
	var _checkKy = '';
	var isKySo = false;
	var sign_type = ''
	var causer = -1;
	var capassword = -1;
	var smartcauser = -1;
	var _rptKyCa = '';
	var capassword = -1;
	var hospital_id = -1;
	var hopital = -1;
	var showAllDVKT = false; //L2PT-20786
	var cfObj = new Object();
	var lstParamSignHub = [];//HaNv_200525: L2PT-133043
	$.widget("ui.ntu02d024_ttxn", {
		//Options to be used as defaults
		options : {
			_gridXnId : 'grdXetNghiem',
			_gridXnDetailId : 'grdXetNghiemChiTiet',
			_khambenhid : "",
			_benhnhanid : "",
			_lnmbp : "",
			_modeView : "0",
			_hosobenhanid : "",
			_deleteDV : "0",
			_gridXnChiDinhId : 'grdXetNghiemChiDinh',
			_modePrint : "0", //Begin_HaNv_28052018: Them bien de hien thi menu in phieu - 1: Show toolbar inPhieu
			_modeTiepDon : "0", //Begin_HaNv_26072018: Nhap benh nhan tu khu tiep don - 1: full quyen - khong can qua ham _checkRoles
			_formCall : "", //Begin_HaNv_26032020: Xác định mã màn hình cha gọi đến widget để tùy biến - L2PT-18728
			checkLoad : false,
			_donviid : ""
		},
		containerId : '',
		_create : function() {
			console.log('_create');
			this.containerId = $(this.element).attr('id');
			this._initWidget();
		},
		_t : function(_id) {
			var newId = '';
			if (_id.indexOf("=") > 0) {
				newId = _id.replace(/\b((txt|cbo|chk|hid|lbl|rad)[a-z,A-Z,0-9,_]+=)\b/gi, _self.containerId + "$1");
			} else if (_id.indexOf("#") == 0) {
				newId = _id.replace(/(#)([a-z,A-Z,0-9,_]+)/gi, "$1" + _self.containerId + "$2");
			} else {
				newId = _self.containerId + _id;
			}
			return newId;
		},
		_initWidget : function() {
			var _self = this;
			_self.options.checkLoad = false;
			$(_self.element).load('../noitru/NTU02D024_ThongTinXetNghiem.tpl?v=1', function() {
				$(_self.element).find("[id]").each(function() {
					if ($(this).attr("href")) {
						$(this).attr("href", '#' + _self.containerId + $(this).attr('href').replace("#", ""));
					}
					if (this.id == "pager_" + _self.options._gridXnId) {
						this.id = "pager_" + _self.containerId + _self.options._gridXnId;
					} else if (this.id == "pager_" + _self.options._gridXnDetailId) {
						this.id = "pager_" + _self.containerId + _self.options._gridXnDetailId;
					} else {
						this.id = _self.containerId + this.id;
					}
				})
				//$("[data-i18n]").i18n();
				_self._initControl();
				_self._loadData();
				_self._bindEvent();
				height_window = $(window).height(); // returns height of browser viewport
			});
		},
		_initControl : function() {
			var _self = this;
			//Beg_HaNv_021021: Điều chỉnh widget - BVTM-5952
			var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "OPT_HOSPITAL_ID;SHOW_COL_BACSITH;PHONG_TUDONG_IN;"
					+ "HIS_SUDUNG_KYSO_KYDIENTU;HIS_SHOW_TOOLBAR_PHUTHU;NTU_ALLOW_HUYDV_TAMUNG;WGXN_SHOW_INCOVID19;HIS_BACSY_YCHUY_DV;"
					+ "CLS_ALLOW_XOA_CHUATHUTIEN;KBH_TATTHONGBAO_KBHB;NTU_XN_CHECKXOA_USER;BM2_NOTALLOW_XOA_CLS;NTU_SAVEOK_NOTMSG;"
					+ "XOA_CAPNHAT_PHIEU_KOCANHUY;HIS_CHECK_DIEUTRI_KISO;CDDV_USING_REPORT_CODE;HIS_FILEEXPORT_TYPE;CDDV_GIAODIEN_KHA;"
					+ "SHOW_COL_GHICHULIS;CLS_COPY_NOCHECK_ROLE;WGXN_SHOW_TTLAYMAU;HIS_SHOWALL_DVKT;NTU_ALL_ACCESS_WIDGET;NTU_POPUP_KQXN_GPB;"
					+ "INCLS_KSK_THEO_DOAN;NTU_BM2_12397_INTD;HIS_CHECKBOX_ALLDVKT;INPHIEU_THEO_DV;WGCLS_SHOW_TOOLBAR_INPHIEU;WGCLS_HIDE_COPY;"
					+ "NTU_HUY_CHECK_DUYET_MP;XN_INPHIEU_THEO_NHOMDV;WGXN_SHOW_INXNLAO;CLS_INBARCODE;WGCLS_SHOW_VIEW_KQ;CDDV_HIDECOL_LOAIMBP;"
					+ "NTU_DTKH_ALLOW_UPDATE_DVKT;HIS_KYSO_WIDGET_XN_ALL;HIS_KYSO_WIDGET_XN_RPT;WGCLS_SAVE_VIEWKQ;HIS_KYCHOT_PHIEUKQ;" 
					+ "EMR_RPT_SIGNTYPE");
			if (config_ar != null && config_ar.length > 0) {
				cfObj = config_ar[0];
			} else {
				DlgUtil.showMsg("Không tải được thông tin cấu hình sử dụng trên màn hình");
				return;
			}
			//End_HaNv_021021
			// L2PT-20786 ttlinh start
			if (_self.options._formCall != '' && cfObj.HIS_SHOWALL_DVKT == '1' && "NTU_BDT,NTU_PHC,KB_MHC,TN_NGT1,TN_NGT2,".includes(_self.options._formCall + ',')) {
				showAllDVKT = true;
				$('#' + _self.containerId + 'divAllKhoa').show();
				//L2PT-25016 start
				if (cfObj.HIS_CHECKBOX_ALLDVKT == '1') {
					$('#' + _self.containerId + 'chkAllKhoa').prop("checked", true);
				}
				//L2PT-25016 end
			}
			// L2PT-20786 end
			hospital_id = cfObj.OPT_HOSPITAL_ID;
			_rptKyCa = cfObj.HIS_KYSO_WIDGET_XN_RPT;
			//khoi tao grid danh sach xet nghiem //L2PT-22465
			var _gridHeader = " ,ICON,30,0,ns,l; ,ICON2,30,0,ns,l;Mức chờ CLS,ICON3,90,0,ns,l;Barcode,BARCODE,55,0,f,l;Số phiếu,SOPHIEU,100,0,f,l;"
					+ "Phiếu điều trị,PHIEU_DTRI,100,0,f,l;Bác sỹ chỉ định,NGUOITAO,135,0,f,l;"
					+ "Bác sỹ thực hiện,NGUOITRAKETQUA,140,0,f,l;Thời gian chỉ đinh,NGAYMAUBENHPHAM,115,0,f,l;P. Thực hiện,PHONGCHIDINH,135,0,f,l;"
					+ "STT thực hiện,SOTHUTUCHIDINH,80,0,f,l;P. Lấy mẫu,PHONGLAYMAU,100,0,f,l;Phòng,PHONGDIEUTRI,150,0,f,l;Khẩn,KHANCAP,75,0,f,l;"
					+ "Trạng thái,TRANGTHAI_PHIEU,75,0,f,l;Người tạo phiếu,NGUOITAO_CLS,120,0,f,l;TIEPNHANID,TIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
					+ "TRANGTHAIMAUBENHPHAM,TRANGTHAIMAUBENHPHAM,0,0,t,l;MABENHNHAN,MABENHNHAN,0,0,t,l;HOSOBENHANID,HOSOBENHANID,0,0,t,l;"
					+ "KHAMBENHID,KHAMBENHID,0,0,t,l;MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;NGUOITAO_ID,NGUOITAO_ID,0,0,t,l;"
					+ "TRANGTHAIDULIEUMAUBENHPHAM,TRANGTHAIDULIEUMAUBENHPHAM,0,0,t,l;PHIEUDIEUTRIID,PHIEUDIEUTRIID,0,0,t,l;NGUOITAOCLS_ID,NGUOITAOCLS_ID,0,0,t,l;FLAG_CA,FLAG_CA,0,0,t,l;"
					+ "GHICHULIS,GHICHULIS,0,0,t,l;KHOADIEUTRI,KHOADIEUTRI,0,0,t,l;TG trả KQ,NGAYMAUBENHPHAM_HOANTHANH,120,0,f,l;LOAI_PHONG_CANHBAO_ID,LOAI_PHONG_CANHBAO_ID,0,0,t,l;"
					+ "XN_NO_RES,XN_NO_RES,0,0,t,l";
			var _gridHeaderDetail = " ,ICON,30,0,ns,l; ,VIEW,30,0,f,l,ES;PARAM_HASHED,PARAM_HASHED,0,0,t,l;MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;DICHVUTHUCHIENID,DICHVUTHUCHIENID,0,0,t,l;"
					+ "KETQUACLSID,KETQUACLSID,0,0,t,l;Tên chỉ định,TENCHIDINH,200,0,f,l;Mã xét nghiệm,MADICHVU,100,0,f,l;Tên xét nghiệm,TENDICHVU,260,0,f,l;"
					+ "Kết quả,GIATRI_KETQUA,160,0,f,l;Trị số bình thường,TRISOBINHTHUONG,130,0,f,l;Trạng thái,TRANGTHAIKETQUA,150,0,f,l;"
					+ "Thực hiện,THOIGIANTRAKETQUA,120,0,f,l;TG trả KQ,THOIGIANTRAKETQUA1,120,0,f,l;Ghi chú CĐ,GHICHUCD,150,0,f,l;BS duyệt,NGUOITRAKETQUA,150,0,f,l;"
					+ "BS thực hiện,BACSITHUCHIEN,150,0,f,l;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;DICHVUID,DICHVUID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;VIEW_CLS,VIEW_CLS,0,0,t,l";
			var _group = {
				groupField : [ 'TENCHIDINH' ],
				groupColumnShow : [ false ],
				groupText : [ '<b>{0}</b>' ]
			};
			var _gridheaderChiDinh = "TRANGTHAI_ID,TRANGTHAI_ID,0,0,t,l;MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;"
					+ "Mã xét nghiệm,MADICHVU,150,0,f,l;Tên xét nghiệm,TENDICHVU,300,0,f,l;Loại MBP,LOAIMBP,120,0,f,l;Loại thanh toán,LOAITT,120,0,f,l;"
					+ "Thuốc đi kèm,THUOC_DIKEM,150,0,f,l;Vật tư đi kèm,VATTU_DIKEM,150,0,f,l;Số lượng,SOLUONG,100,0,f,l;Trạng thái,TENTRANGTHAI,150,0,f,l;"
					+ "YC_HOAN,YC_HOAN,0,0,t,l;TRANGTHAIKETQUA,TRANGTHAIKETQUA,0,0,t,l;Ghi chú,GHICHU,100,0,f,l";
			if (cfObj.CDDV_HIDECOL_LOAIMBP == '1') {
				_gridheaderChiDinh = _gridheaderChiDinh.replace('Loại MBP,LOAIMBP,120,0,f,l;', '');
			}
			if (hospital_id == '10284') {
				_gridheaderChiDinh = _gridheaderChiDinh.replace('Loại MBP,LOAIMBP', 'Mẫu bệnh phẩm,LOAIMBP');
			} else {
				_gridheaderChiDinh = _gridheaderChiDinh.replace('Thuốc đi kèm,THUOC_DIKEM,150,0,f,l;Vật tư đi kèm,VATTU_DIKEM,150,0,f,l;', '');
			}
			//Beg_HaNv_260124: Cấu hình GridHeader - L2PT-66882
			var dtMenu = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', 'NTU02D024_GRDXETNGHIEM');
			if (dtMenu != undefined && dtMenu != 0 && dtMenu.length > 100) {
				_gridHeader = dtMenu;
			}
			var dtMenuCt = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', 'NTU02D024_GRDXNCHITIET');
			if (dtMenuCt != undefined && dtMenuCt != 0 && dtMenuCt.length > 100) {
				_gridHeaderDetail = dtMenuCt;
			}
			//End_HaNv_260124
			//L2PT-20786 ttlinh start
			var _group_khoa = {
				groupField : [ 'KHOADIEUTRI' ],
				groupColumnShow : [ false ],
				groupText : [ '<b>{0}</b>' ]
			};
			if (cfObj.HIS_KYSO_WIDGET_XN_ALL == '1') {
				if (showAllDVKT) {
					GridUtil.initGroup(_self.containerId + _self.options._gridXnId, "100%", "165px", 'Danh sách xét nghiệm', true, _group_khoa, _gridHeader, false, {
						rowNum : 500,
						rowList : [ 100, 200, 500 ]
					//HaNv_120921: L2PT-7213
					});
				} else {
					GridUtil.init(_self.containerId + _self.options._gridXnId, "100%", "165px", 'Danh sách xét nghiệm', true, _gridHeader, false, {
						rowNum : 500,
						rowList : [ 100, 200, 500 ]
					});//HaNv_05072019: L2PT-6559
				}
				// L2PT-20786 end
			} else {
				if (showAllDVKT) {
					GridUtil.initGroup(_self.containerId + _self.options._gridXnId, "100%", "165px", 'Danh sách xét nghiệm', false, _group_khoa, _gridHeader, false, {
						rowNum : 500,
						rowList : [ 100, 200, 500 ]
					//HaNv_120921: L2PT-7213
					});
				} else {
					GridUtil.init(_self.containerId + _self.options._gridXnId, "100%", "165px", 'Danh sách xét nghiệm', false, _gridHeader, false, {
						rowNum : 500,
						rowList : [ 100, 200, 500 ]
					});//HaNv_05072019: L2PT-6559
				}
				// L2PT-20786 end
			}
			//GridUtil.init(_self.containerId+_self.options._gridXnDetailId,"1138","129px",'Danh sách kết quả xét nghiệm',false, _gridHeaderDetail,false,{rowNum: 10,rowList: [10, 20, 30]});
			GridUtil.initGroup(_self.containerId + _self.options._gridXnDetailId, "100%", "250px", 'Danh sách kết quả xét nghiệm', false, _group, _gridHeaderDetail, false, {
				rowNum : 50,
				rowList : [ 50, 100, 150 ]
			//HaNv_120921: L2PT-7213
			});
			GridUtil.init(_self.containerId + _self.options._gridXnChiDinhId, "100%", "250px", 'Danh sách dịch vụ chỉ định', false, _gridheaderChiDinh, true, {
				rowNum : 100,
				rowList : [ 100, 150, 200 ]
			});
			$("#" + _self.containerId + _self.options._gridXnDetailId)[0].toggleToolbar();
			$("#" + _self.containerId + _self.options._gridXnChiDinhId)[0].toggleToolbar();
			//Begin_HaNv_16032018: cấu hình ẩn/hiện cột bác sĩ thực hiện trên danh sách mbp
			var showBacSiTh = cfObj.SHOW_COL_BACSITH;
			if (showBacSiTh == '0') {
				$('#' + _self.containerId + _self.options._gridXnId).jqGrid('hideCol', 'NGUOITRAKETQUA');
			}
			//End_HaNv_16032018
			//Begin_ttlinh_090322: L2PT-15632 cau hinh an/hien tab ghi chu tu LIS 
			if (cfObj.SHOW_COL_GHICHULIS == '0') {
				$('#' + _self.containerId + 'tabGhiChu').remove();
			}
			//End_ttlinh_090322
			//Beg_HaNv_300521: BVTM-3035
			if (hospital_id != '10284') {
				//$('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('hideCol', 'THUOC_DIKEM');
				//$('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('hideCol', 'VATTU_DIKEM');
				$('#' + _self.containerId + 'divNote').remove();
				//Beg_HaNv_110921: Menu thuốc, vật tư đi kèm - BVTM-5601
				$('#' + _self.containerId + 'divDK').remove();
				//End_HaNv_110921
				$('#' + _self.containerId + _self.options._gridXnId).jqGrid('hideCol', 'ICON3'); //L2PT-22465
			}
			//End_HaNv_300521
			//L2PT-22465 start
			if (hospital_id == '10284' && (_self.options._formCall == '' || !"NTU_BDT,KB_MHC,".includes(_self.options._formCall + ','))) {
				$('#' + _self.containerId + _self.options._gridXnId).jqGrid('hideCol', 'ICON3'); //L2PT-22465
			}
			//L2PT-22465 end
			//Beg_DoanPV_20220111: L2PT-12778: Ẩn tạo bản sao với BND, NhiHDG (L2PT-30146)
			if ([ "951", "957" ].includes(hospital_id + "") || cfObj.WGCLS_HIDE_COPY == '1') {
				$('#' + _self.containerId + 'copyNote').remove();
			}
			//L2PT-22424 Check role
			if (_self.options._formCall == 'KSK06D001_TiepNhanKham') {
				var _parPQ = 'KSK06D001_TiepNhanKham' + '$';
				var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("DUC.PQSCREEN.03", _parPQ);
				//Beg_HaNv_290822: Chỉnh sửa tính năng phân quyền xóa phiếu trong chức năng tiếp nhận KSK theo đoàn - L2PT-24662
				if (result && result.length > 0) {
					for (var i = 0; i < result.length; i++) {
						if (result[i].ROLES == '0' || result[i].ROLES == '') {
							$('#' + _self.containerId + result[i].ELEMENT_ID).remove();
						}
					}
				} else {
					if (hospital_id == '10284') {
						$('#' + _self.containerId + 'deleteRequest').remove();
						$('#' + _self.containerId + 'deleteAll').remove();
						$('#' + _self.containerId + 'delete').remove();
					}
				}
				//End_HaNv_290822
			}
			//Beg_HaNv_160123: Chặn in kết quả CLS tại khoa - L2PT-33201
			if (hospital_id == '1111' && (_self.options._formCall != '' && "NTU_BDT,KB_MHC,".includes(_self.options._formCall + ','))) {
				$('#' + _self.containerId + 'printKQ').remove();
			}
			//End_HaNv_160123
			//Beg_HaNv_131023: Chuyển thanh toán VP - L2PT-53262
			if (hospital_id != '10284' || _self.options._formCall != 'NTU02D175') {
				$('#' + _self.containerId + 'changeVP').remove();
			}
			//End_HaNv_131023
			//Beg_HaNv_271023: view kq cls - L2PT-57865
			if (cfObj.WGCLS_SHOW_VIEW_KQ == '0') {
				$('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('hideCol', 'VIEW');
			}
			//End_HaNv_271023
		},
		_loadData : function() {
			//var userInfo=CommonUtil.decode('{userData}');
			//alert(userInfo.user_id);
			var _self = this;
			var _look_sql = "NT.024.DSPHIEUCLS";
			var _sql_par = [];
			// L2PT-20786 ttlinh start
			if (showAllDVKT) {
				if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
					_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, $('#hidHOSOBENHANID').val() ]);
				} else {
					_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, -1 ]);
				}
			} else {
				_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, _self.options._hosobenhanid ]);
			}
			// L2PT-20786 end
			GridUtil.loadGridBySqlPage(_self.containerId + _self.options._gridXnId, _look_sql, _sql_par);
			var _tudongin = '0';
			var _data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH_ND", 'NGT_PHIEUKHAM_TUDONGIN');
			if (_data_ar != null && _data_ar.length > 0) {
				_tudongin = _data_ar[0].NGT_PHIEUKHAM_TUDONGIN;
			}
			var dc_phong = cfObj.PHONG_TUDONG_IN;
			var dc_phongs = dc_phong.split(',');
			if (_tudongin != '1' || !$.inArray($('#hidPHONGID').val(), dc_phongs) >= 0) {
				$('#' + _self.containerId + 'printAuto').remove();
				$('#' + _self.containerId + 'printKQAuto').remove();
				$('#' + _self.containerId + 'printViewAuto').remove();
				$('#' + _self.containerId + 'printKQViewAuto').remove();
			}
			// Xử lý sự kiện liên quan ký CA => START
			_checkKy = cfObj.HIS_SUDUNG_KYSO_KYDIENTU;
			if (_checkKy != "1") {
				$('#' + _self.containerId + 'GROUP4').remove();
				$('#' + _self.containerId + 'kyCA').remove();
				$('#' + _self.containerId + 'printKyCA').remove();
				$('#' + _self.containerId + 'printSelectedCASigned').remove();
				$('#' + _self.containerId + 'printKyCAKQ').remove();
				$('#' + _self.containerId + 'printKyCA2').remove();
				$('#' + _self.containerId + 'printSelectedCASigned2').remove();
				$('#' + _self.containerId + 'printKyCAKQ2').remove();
				$('#' + _self.containerId + 'huyKyCA').remove();
				$('#' + _self.containerId + 'kyCAKQ').remove();
				$("#" + _self.containerId + _self.options._gridXnId).hideCol('ICON2');
				$("#" + _self.containerId + _self.options._gridXnDetailId).hideCol('ICON');
			}
			if (cfObj.HIS_KYCHOT_PHIEUKQ == '0') {
				$('#' + _self.containerId + 'kyCAKQ').remove();
			}
			// Xử lý sự kiện liên quan ký CA => END
			if (cfObj.NTU_BM2_12397_INTD != "1") {
				$('#' + _self.containerId + 'printPCD').remove();
				$('#' + _self.containerId + 'printViewPCD').remove();
			}
			//Begin_HaNv_10052018: Hiển thị toolbar phụ thu theo cấu hình L2DKBD-1230
			var toolbar_ar = [
			//Begin_HaNv_28052018: Hiển thị toolbar in phiếu L2DKHN-777
			{
				type : 'buttongroup',
				id : 'btnInPhieu',
				icon : 'print',
				text : ' In phiếu',
				children : [ {
					id : 'btnPhieuIn27BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm',
					hlink : '#'
				}, {
					id : 'btnPhieuIn27BV01DT',
					icon : 'print',
					text : 'Phiếu xét nghiệm đặc thù',
					hlink : '#'
				}, {
					id : 'btnPhieuIn28BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm huyết học',
					hlink : '#'
				}, {
					id : 'btnPhieuIn29BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm huyết tủy đồ',
					hlink : '#'
				}, {
					id : 'btnPhieuIn30BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm chẩn đoán rối loạn đông cầm máu',
					hlink : '#'
				}, {
					id : 'btnPhieuIn31BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm sinh thiết tủy xương',
					hlink : '#'
				}, {
					id : 'btnPhieuIn32BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm nước dịch',
					hlink : '#'
				}, {
					id : 'btnPhieuIn33BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm hóa sinh máu',
					hlink : '#'
				}, {
					id : 'btnPhieuIn34BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm hóa sinh nước tiểu',
					hlink : '#'
				}, {
					id : 'btnPhieuIn35BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm vi sinh',
					hlink : '#'
				}, {
					id : 'btnPhieuIn36BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm giải phẫu bệnh sinh thiết',
					hlink : '#'
				}, {
					id : 'btnPhieuIn36BV01_',
					icon : 'print',
					text : 'Phiếu xét nghiệm giải phẫu bệnh sinh thiết TH',
					hlink : '#'
				}, {
					id : 'btnPhieuIn37BV01',
					icon : 'print',
					text : 'Phiếu xét nghiệm giải phẫu bệnh khám nghiệm tử thi',
					hlink : '#'
				}, {
					id : 'btnPhieuIn27BV01AFB',
					icon : 'print',
					text : 'Phiếu xét nghiệm AFB',
					hlink : '#'
				}, {
					id : 'btnPhieuIn27BV01NuoiCay',
					icon : 'print',
					text : 'Phiếu xét nghiệm nuôi cấy',
					hlink : '#'
				}, {
					id : 'btnPhieuIn27BV01KhangSinhDo',
					icon : 'print',
					text : 'Phiếu xét nghiệm kháng sinh đồ',
					hlink : '#'
				} ]
			}, {
				type : 'buttongroup',
				id : 'btnhospitalfee',
				icon : 'open',
				text : 'Phụ thu',
				hlink : '#',
				children : [ {
					id : 'hospitalfee_1',
					icon : 'open',
					text : 'Tạo phiếu phụ thu',
					hlink : '#'
				}, {
					id : 'hospitalfee_2',
					icon : 'open',
					text : 'Danh sách phiếu phụ thu',
					hlink : '#'
				} ]
			} ];
			if (_self.options._modePrint == "1" || cfObj.WGCLS_SHOW_TOOLBAR_INPHIEU == '1') {
				toolbar = ToolbarUtil.build(_self.containerId + 'toolbarId', toolbar_ar);
				$("#" + _self.containerId + 'toolbarIdbtnInPhieu').attr("disabled", true);
				if (cfObj.HIS_SHOW_TOOLBAR_PHUTHU != 1) {
					$("#" + _self.containerId + 'toolbarIdbtnhospitalfee').hide();
				}
			} else {
				if (cfObj.HIS_SHOW_TOOLBAR_PHUTHU == 1) {
					toolbar = ToolbarUtil.build(_self.containerId + 'toolbarId', toolbar_ar);
					$("#" + _self.containerId + 'toolbarIdbtnInPhieu').closest("div").removeClass('wd100');
					$("#" + _self.containerId + 'toolbarIdbtnInPhieu').hide();
				} else {
					$("#" + _self.containerId + 'toolbarId').hide();
				}
			}
			//End_HaNv_28052018
			//End_HaNv_10052018
			//Begin_HaNv_05072019: Quy trình hoàn hủy với bệnh nhân đóng tạm ứng - L1PT-1245
			if (cfObj.NTU_ALLOW_HUYDV_TAMUNG == "1") {
				$('#' + _self.containerId + 'deleteRequest').remove();
			}
			//End_HaNv_05072019
			//Begin_HaNv_26032020: Xác định mã màn hình cha gọi đến widget để tùy biến - L2PT-18728
			if (_self.options._formCall != "KB_MHC") {
				$('#' + _self.containerId + 'GROUP2_PRINT').remove();
				$('#' + _self.containerId + 'editPhieuDTPrint').remove();
			}
			//End_HaNv_26032020
			//Beg_HaNv_270521: Hiển thị In Covid-19 - L2PT-3475
			if (cfObj.WGXN_SHOW_INCOVID19 != "1") {
				$('#' + _self.containerId + 'printCovid19').remove();
				$('#' + _self.containerId + 'printCovid19View').remove();
			}
			//End_HaNv_270521
			//Beg_HaNv_021021: Điều chỉnh widget - BVTM-5952
			if (cfObj.HIS_BACSY_YCHUY_DV != "1") {
				$('#' + _self.containerId + 'sendRequestDeleteReject').remove();
				$('#' + _self.containerId + 'undoRequestDeleteReject').remove();
			}
			//End_HaNv_021021
			//Beg_HaNv_300522: Hiển thị thông tin lấy mẫu - L2PT-19262
			if (cfObj.WGXN_SHOW_TTLAYMAU != "1") {
				$('#' + _self.containerId + 'thongtinlaymau').remove();
				$('#' + _self.containerId + 'thongtinlaymauPrint').remove();
			}
			//End_HaNv_300522
			//Beg_HaNv_220723: Hiển thị In Phiếu XN Lao - L2PT-47789
			if (cfObj.WGXN_SHOW_INXNLAO != "1") {
				$('#' + _self.containerId + 'printXNLao').remove();
				$('#' + _self.containerId + 'printXNLaoView').remove();
			}
			//End_HaNv_220723
		},
		_bindEvent : function() {
			var _self = this;
			$('#' + _self.containerId + ' a[data-toggle="pill"] , #' + _self.containerId + ' a[data-toggle="tab"], #' + _self.containerId + ' li[role="presentation"] > a').on('shown.bs.tab',
					function(e) {
						if ($($(e.currentTarget).attr("href") + " .ui-jqgrid").length > 0) {
							$(window).trigger('resize');
						}
					});
			var _grid = $("#" + _self.containerId + _self.options._gridXnId);
			GridUtil.setGridParam(_self.containerId + _self.options._gridXnId, {
				onSelectRow : function(id, selected) {
					if (!selected) {
						return;
					}
					_self._viewXetnghiemDetail(id);
					GridUtil.unmarkAll(_self.containerId + _self.options._gridXnId);
					GridUtil.markRow(_self.containerId + _self.options._gridXnId, id);
					var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', id);
					$("#hidMAUBENHPHAMID").val(rowData.MAUBENHPHAMID);
					$("#hidTIEPNHANID").val(rowData.TIEPNHANID);//START -- L2K74TW-491 -- hongdq --03052018
					//Begin_HaNv_28052018: Hiển thị toolbar in phiếu L2DKHN-777
					var trangthai = rowData.TRANGTHAIMAUBENHPHAM;
					if (trangthai == "2") {
						$("#" + _self.containerId + 'toolbarIdbtnInPhieu').attr("disabled", true);
					} else if (trangthai == "3" || trangthai == "4" || trangthai == "8") {
						$("#" + _self.containerId + 'toolbarIdbtnInPhieu').attr("disabled", false);
					}
					//End_HaNv_28052018
					$("#" + _self.containerId + 'txtGHICHULIS').val(rowData.GHICHULIS);//L2PT-15632 ttlinh
					//Beg_HaNv_040324: Lưu thông tin người xem, ngày xem chỉ số KQCLS - L2PT-74410
					if (cfObj.WGCLS_SAVE_VIEWKQ == '1') {
						if ($("#" + _self.containerId + "ultab li.active")[0].id == _self.containerId + 'tabKetQuaXetNghiem') {
							sql_par = [];
							sql_par.push({
								"name" : "[0]",
								"value" : rowData.MAUBENHPHAMID
							});
							jsonrpc.AjaxJson.execute("NTUD024.02", sql_par);
							$('#' + _self.containerId + _self.options._gridXnDetailId).trigger('reloadGrid');
						}
					}
					//End_HaNv_040324
				},
				ondblClickRow : function(id) {
					if (_self.options._modeView == "0") {
						_self._updatePhieuXetNghiem(id)
					}
				},
				gridComplete : function(id) {
					//hiển thị biểu tượng ký số/điện tử
					var ids = $("#" + _self.containerId + _self.options._gridXnId).getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						var row = $("#" + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', id);
						//set icon trang thai  benh nhan
						var _icon = '';
						// đã ký số
						if (row.FLAG_CA) {
							if (row.FLAG_CA == '1') {
								_icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
							} else if (row.FLAG_CA == '99') {
								_icon = '<center><img src="../common/image/ca-3.png" width="15px"></center>';
							}
						}
						$("#" + _self.containerId + _self.options._gridXnId).jqGrid('setCell', id, 'ICON2', _icon);
					}
					//L2PT-20786 start
					var modeView;
					if (showAllDVKT) {
						if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
							modeView = "1";
						} else {
							modeView = _self.options._modeView;
						}
					} else {
						modeView = _self.options._modeView;
					}
					//L2PT-20786 end
					if (modeView == "0") {
						//build menu
						$(".jqgrow", '#' + _self.containerId + _self.options._gridXnId).contextMenu(_self.containerId + 'contextMenu', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridXnId);
								//grid.setSelection(rowId);
								_setSelectionOnContextMenu(_self.containerId + _self.options._gridXnId, rowId);
								return true;
							},
						});
						//xu ly su kien gui yeu cau
						$('#' + _self.containerId + 'sentRequest').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._sendRequest(rowKey);
						});
						//xu ly su kien xoa yeu cau
						$('#' + _self.containerId + 'deleteRequest').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowKey);
							if (rowData != null) {
								if (_checkKy == '1' && rowData.FLAG_CA == '1') {
									DlgUtil.showMsg("Phiếu đã ký số không được hủy yêu cầu!");
									return;
								}
							}
							_self._deleteRequest(rowKey);
						});
						//xu ly su kien xoa 
						$('#' + _self.containerId + 'delete').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._xoaPhieuDichVu(rowKey);
						});
						//xoa cac dich vu khong co ket qua
						$('#' + _self.containerId + 'deleteAll').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._xoaPhieuDichVuKhongCoKetQua(rowKey);
						});
						//Sua phong thuc hien
						$('#' + _self.containerId + 'editOrg').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._editOrgDone(rowKey);
						});
						//Cập nhật PXN
						$('#' + _self.containerId + 'updatePXN').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._updatePhieuXetNghiem(rowKey)
						});
						//Tao ban sao
						$('#' + _self.containerId + 'copyNote').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._copyPhieuXetNghiem(rowKey);
						});
						//L2K74TW-301 - hongdq
						//cap nhat phieu dieu tri
						$('#' + _self.containerId + 'editPhieuDT').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._editPhieuDT(rowKey);
						});
						//L2K74TW-301 -hongdq
						//Beg_HaNv_300522: Hiển thị thông tin lấy mẫu - L2PT-19262
						$('#' + _self.containerId + 'thongtinlaymau').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._thongtinlaymau(rowKey);
						});
						//End_HaNv_300522
						//Begin_HaNv_06062020: Sửa bác sĩ chỉ định - L2PT-21679
						$('#' + _self.containerId + 'editBacSi').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowKey);
							if (rowData != null) {
								if (_checkKy == '1' && rowData.FLAG_CA == '1') {
									DlgUtil.showMsg("Phiếu đã ký số không được sửa!");
									return;
								}
							}
							_self._editBacSi(rowKey);
						});
						//End_HaNv_06062020
						//Beg_HaNv_270521: Hiển thị In Covid-19 - L2PT-3475
						$('#' + _self.containerId + 'printCovid19').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXNCovid19(rowKey);
						});
						//End_HaNv_270521
						//Beg_HaNv_220723: Hiển thị In Phiếu XN Lao - L2PT-47789
						$('#' + _self.containerId + 'printXNLao').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXN_Lao(rowKey);
						});
						//End_HaNv_220723
						//In phieu chi dinh
						$('#' + _self.containerId + 'print').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXNCD(rowKey);
						});
						//START L2HOTRO-12397
						$('#' + _self.containerId + 'printPCD').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXNCDVIEW(rowKey);
						});
						//END L2HOTRO-12397
						$('#' + _self.containerId + 'printAuto').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXNCDAuto(rowKey);
						});
						//In Ket qua chi dinh
						$('#' + _self.containerId + 'printKQ').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportKetQuaPXNCD(rowKey);
						});
						$('#' + _self.containerId + 'printKQAuto').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportKetQuaPXNCDAuto(rowKey);
						});
						//in giay cam doan pttt va gay me hoi suc
						$('#' + _self.containerId + 'printCamDoanPTTT').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
							_self._exportCamDoanPTTT(rowKey);
						});
						//START L2HOTRO-13031
						$('#' + _self.containerId + 'printDVCSC').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
							_self._exportprintDVCSC(rowKey);
						});
						$('#' + _self.containerId + 'xoaAllDV').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._deleteAllDVXetNghiem(rowKey);
						});
						//In cac phieu da chon
						//START -- HISL2TK-611 --hongdq
						$('#' + _self.containerId + 'printselectedRow').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportSelectedXN(rowKey);
						});
						//Begin_HaNv_11112019: In barcode ở phiếu chỉ định xét nghiệm - L2PT-10523
						$('#' + _self.containerId + 'prinBarcode').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportBarcode(rowKey);
						});
						//End_HaNv_11112019
						// Xử lý sự kiện liên quan ký CA => START
						$('#' + _self.containerId + 'kyCA').unbind("click").bind("click", function() {
							var rowDatas = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selarrrow");
							if (rowDatas.length > '1') {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
								_self._caRpt('1');
							} else {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
								_self._kyCA(rowKey);
							}
						});
						$('#' + _self.containerId + 'huyKyCA').unbind("click").bind("click", function() {
							var rowDatas = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selarrrow");
							if (rowDatas.length > '1') {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
								_self._caRpt('2');
							} else {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
								_self._huyKyCA(rowKey);
							}
						});
						$('#' + _self.containerId + 'printKyCA').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportKyCA(rowKey);
						});
						$('#' + _self.containerId + 'printSelectedCASigned').unbind("click").bind("click", function() {
							var rowDatas = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selarrrow");
							if (rowDatas.length > '1') {
								_self._printSelectedCASigned();
							} else {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
								_self._exportKyCA(rowKey);
							}
						});
						$('#' + _self.containerId + 'printKyCAKQ').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportKyKQ(rowKey);
						});
						$('#' + _self.containerId + 'kyCAKQ').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._kyCAKQ(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => END
					} else if (modeView == "1") {
						//build menu
						$(".jqgrow", '#' + _self.containerId + _self.options._gridXnId).contextMenu(_self.containerId + 'contextMenuPrint', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridXnId);
								//grid.setSelection(rowId);
								_setSelectionOnContextMenu(_self.containerId + _self.options._gridXnId, rowId);
								return true;
							},
						});
						//cap nhat phieu dieu tri
						$('#' + _self.containerId + 'editPhieuDTPrint').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._editPhieuDT(rowKey);
						});
						//Beg_HaNv_300522: Hiển thị thông tin lấy mẫu - L2PT-19262
						$('#' + _self.containerId + 'thongtinlaymauPrint').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._thongtinlaymau(rowKey);
						});
						//End_HaNv_300522
						//Beg_HaNv_270521: Hiển thị In Covid-19 - L2PT-3475
						$('#' + _self.containerId + 'printCovid19View').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXNCovid19(rowKey);
						});
						//End_HaNv_270521
						//Beg_HaNv_220723: Hiển thị In Phiếu XN Lao - L2PT-47789
						$('#' + _self.containerId + 'printXNLaoView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXN_Lao(rowKey);
						});
						//End_HaNv_220723
						//Xem In phieu chi dinh
						$('#' + _self.containerId + 'printView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXNCD(rowKey);
						});
						//START L2HOTRO-12397
						$('#' + _self.containerId + 'printViewPCD').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXNCDVIEW(rowKey);
						});
						//END L2HOTRO-12397
						//START -- HISL2TK-611 --hongdq
						$('#' + _self.containerId + 'printViewSelectedRow').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportSelectedXN(rowKey);
						});
						//END -- HISL2TK-611 --hongdq
						//In phieu chi dinh
						$('#' + _self.containerId + 'printViewAuto').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportPXNCDAuto(rowKey);
						});
						//xem in ket qua chi dinh
						$('#' + _self.containerId + 'printKQView').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportKetQuaPXNCD(rowKey);
						});
						//in ket qua chi dinh
						$('#' + _self.containerId + 'printKQViewAuto').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportKetQuaPXNCDAuto(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => START
						$('#' + _self.containerId + 'printKyCA2').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportKyCA(rowKey);
						});
						$('#' + _self.containerId + 'printSelectedCASigned2').unbind("click").bind("click", function() {
							var rowDatas = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selarrrow");
							if (rowDatas.length > '1') {
								_self._printSelectedCASigned();
							} else {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
								_self._exportKyCA(rowKey);
							}
						});
						$('#' + _self.containerId + 'printKyCAKQ2').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._exportKyKQ(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => END
					} else if (modeView == "2") {
						$(".jqgrow", '#' + _self.containerId + _self.options._gridXnId).contextMenu(_self.containerId + 'contextMenuDtkh', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridXnId);
								//grid.setSelection(rowId);
								_setSelectionOnContextMenu(_self.containerId + _self.options._gridXnId, rowId);
								return true;
							},
						});
						//xu ly su kien gui yeu cau
						$('#' + _self.containerId + 'sentRequestDtkh').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._sendRequest(rowKey);
						});
						//xu ly su kien xoa 
						$('#' + _self.containerId + 'deleteDtkh').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._xoaPhieuDichVu(rowKey);
						});
						$('#' + _self.containerId + 'xoaAllDV').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._deleteAllDVXetNghiem(rowKey);
						});
					}
					//Begin_HaNv_14032018: modeView=3 Gọi từ form XoaDichVuCLS cho phép xóa dịch vụ
					else if (modeView == "3") {
						$(".jqgrow", '#' + _self.containerId + _self.options._gridXnId).contextMenu(_self.containerId + 'contextMenuXoaCls', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridXnId);
								//grid.setSelection(rowId);
								_setSelectionOnContextMenu(_self.containerId + _self.options._gridXnId, rowId);
								return true;
							},
						});
						$('#' + _self.containerId + 'xoaAllDV').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
							_self._deleteAllDVXetNghiem(rowKey);
						});
					}
					//End_HaNv_14032018
					//set anh
					_self._setImageStatus();
					//L2PT-22465 start
					if (hospital_id == '10284' && (_self.options._formCall != '' && "NTU_BDT,KB_MHC,".includes(_self.options._formCall + ','))) {
						_self._setAmountStatus();
					}
					//L2PT-22465 end
					_setFocusMauBenhPham($("#hidMAUBENHPHAMID").val(), _self.containerId + _self.options._gridXnId);
				}
			});
			GridUtil.setGridParam(_self.containerId + _self.options._gridXnChiDinhId, {
				onSelectRow : function(id, selected) {
					GridUtil.unmarkAll(_self.containerId + _self.options._gridXnChiDinhId);
					GridUtil.markRow(_self.containerId + _self.options._gridXnChiDinhId, id);
				},
				gridComplete : function(id) {
					var rowCount = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("reccount");
					if (rowCount == 0) {
						return;
					}
					//Beg_HaNv_270621: In đậm chữ khi có thuốc, vật tư đi kèm - BVTM-3724
					if (hospital_id == '10284') {
						var ids = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getDataIDs();
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var row_dt = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', id);
							if ((row_dt.THUOC_DIKEM != '' && row_dt.THUOC_DIKEM != null) || (row_dt.VATTU_DIKEM != '' && row_dt.VATTU_DIKEM != null)) {
								$('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('setRowData', id, "", 'mybold');
							}
						}
					}
					//End_HaNv_270621
					if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
						var ids = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getDataIDs();
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var rowDataCd = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', id);
							if (rowDataCd.YC_HOAN == '2') {
								$('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('setRowData', id, "", {
									color : 'blue'
								});
								$('#' + _self.containerId + _self.options._gridXnChiDinhId).setCell(id, 'MADICHVU', '', {
									'text-decoration' : 'line-through'
								});
								$('#' + _self.containerId + _self.options._gridXnChiDinhId).setCell(id, 'TENDICHVU', '', {
									'text-decoration' : 'line-through'
								});
							}
							if (rowDataCd.YC_HOAN == '1') {
								$('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('setRowData', id, "", {
									color : 'red'
								});
								$('#' + _self.containerId + _self.options._gridXnChiDinhId).setCell(id, 'MADICHVU', '', {
									'text-decoration' : 'line-through'
								});
								$('#' + _self.containerId + _self.options._gridXnChiDinhId).setCell(id, 'TENDICHVU', '', {
									'text-decoration' : 'line-through'
								});
							}
						}
					}
					//xu ly xoa dich vu da thuc hien
					if (_self.options._modeView == "0") {
						//build menu
						$(".jqgrow", '#' + _self.containerId + _self.options._gridXnChiDinhId).contextMenu(_self.containerId + 'contextMenuDeleteDVXNReject', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridXnChiDinhId);
								grid.setSelection(rowId);
								return true;
							},
						}, _self.containerId + 'contextMenuDVKTTDT', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridXnChiDinhId);
								grid.setSelection(rowId);
								return true;
							},
						}, _self.containerId + 'contextMenuNhapSinhThiet', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridXnChiDinhId);
								grid.setSelection(rowId);
								return true;
							},
						}, _self.containerId + 'contextMenuprintCamDoanPTTT', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._gridXnChiDinhId);
								grid.setSelection(rowId);
								return true;
							},
						});
						if (!_self.options.checkLoad) {
							_self.options.checkLoad = true;
							//xu ly su kien gui yeu cau
							$('#' + _self.containerId + 'deleteDVXNReject').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._deleteDVXetNghiemReject(rowKey);
							});
							$('#' + _self.containerId + 'sendRequestDeleteReject').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._sendRequestDeleteReject(rowKey);
							});
							$('#' + _self.containerId + 'undoRequestDeleteReject').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._undoRequestDeleteReject(rowKey);
							});
							$('#' + _self.containerId + 'DVKTTDT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._updateDVKhongThanhToanDT(rowKey);
							});
							//Beg_HaNv_131023: Chuyển thanh toán VP - L2PT-53262
							$('#' + _self.containerId + 'changeVP').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._changeVP(rowKey);
							});
							//End_HaNv_131023
							$('#' + _self.containerId + 'NhapSinhThiet').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._nhapSinhThiet(rowKey);
//		    	        		 alert(JSON.stringify(rowKey)); 
							});
							//L2PT-109940
							$('#' + _self.containerId + 'NhapGiaiPhau').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._nhapGiaiPhau(rowKey);
							});
							//Beg_HaNv_110921: Menu thuốc, vật tư đi kèm - BVTM-5601
							$('#' + _self.containerId + 'TAOPHIEUTHUOCKEM_HAOPHI').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._taoPhieuThuocKemHaoPhi(rowKey);
							});
							$('#' + _self.containerId + 'TAOPHIEUVATTUKEM_HAOPHI').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._taoPhieuVatTuKemHaoPhi(rowKey);
							});
							$('#' + _self.containerId + 'TAOPHIEUTHUOCKEM').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._taoPhieuThuocKem(rowKey);
							});
							$('#' + _self.containerId + 'TAOPHIEUVATTUKEM').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._taoPhieuVatTuKem(rowKey);
							});
							$('#' + _self.containerId + 'DSPTVT_KEM').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._dsPhieuThuocVatTuDiKem(rowKey);
							});
							$('#' + _self.containerId + 'DSPTVT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._dsPhieuThuocVatTu(rowKey);
							});
							//End_HaNv_110921
							//in giay cam doan pttt
							$('#' + _self.containerId + 'printCamDoanPTTT').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._exportCamDoanPTTT(rowKey);
							});
							//START L2HOTRO-13031
							$('#' + _self.containerId + 'printDVCSC').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._exportprintDVCSC(rowKey);
							});
							//Begin_HaNv_20022020: In Kết quả dịch vụ vi khuẩn nhuộm soi BS - L2PT-16778
							$('#' + _self.containerId + 'printKQNhuomSoiBS').unbind("click").bind("click", function() {
								var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
								_self._printKQNhuomSoiBS(rowKey);
							});
							//End_HaNv_20022020
						}
					}
					//xu ly xoa dich vu da thuc hien
					if (_self.options._modeView == "1" || _self.options._modeView == "3") {
						if (_self.options._deleteDV == "1") {
							//build menu
							$(".jqgrow", '#' + _self.containerId + _self.options._gridXnChiDinhId).contextMenu(_self.containerId + 'contextMenuDeleteDV', {
								onContextMenu : function(event, menu) {
									var rowId = $(event.target).parent("tr").attr("id")
									var grid = $('#' + _self.containerId + _self.options._gridXnChiDinhId);
									grid.setSelection(rowId);
									return true;
								},
							});
							if (!_self.options.checkLoad) {
								_self.options.checkLoad = true;
								//xu ly su kien gui yeu cau
								$('#' + _self.containerId + 'deleteDV').unbind("click").bind("click", function() {
									var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
									_self._deleteDVXetNghiem(rowKey);
								});
							}
						}
					}
				}
			});
			//START -- L2K74TW-491 -- hongdq --03052018
			GridUtil.setGridParam(_self.containerId + _self.options._gridXnDetailId, {
				onSelectRow : function(id, selected) {
					GridUtil.unmarkAll(_self.containerId + _self.options._gridXnDetailId);
					GridUtil.markRow(_self.containerId + _self.options._gridXnDetailId, id);
				},
				gridComplete : function(id) {
					//Begin_HaNv_26122019: fix lỗi Kết quả XN không hiển thị theo màu - L2PT-13912
					var rowCount = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("reccount");
					if (rowCount == 0) {
						return;
					}
					_self._setColorKetQuaXN();
					//End_HaNv_26122019
					$(".jqgrow", '#' + _self.containerId + _self.options._gridXnDetailId).contextMenu(_self.containerId + 'contextMenuViewKQVS', {
						onContextMenu : function(event, menu) {
							var rowId = $(event.target).parent("tr").attr("id")
							var grid = $('#' + _self.containerId + _self.options._gridXnDetailId);
							grid.setSelection(rowId);
							return true;
						},
					});
					//Xem Kq soi dom
					$('#' + _self.containerId + 'viewKQSoiDom').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("selrow");
						_self.viewKQ_Soidom(rowKey);
					});
					//Xem KQ VK Lao
					$('#' + _self.containerId + 'viewKQ_VKLao').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("selrow");
						_self.viewKQ_VKLao(rowKey);
					});
					//Xem KQ Khangsinhdo
					$('#' + _self.containerId + 'viewKQ_Khangsinhdo').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("selrow");
						_self.viewKQ_Khangsinhdo(rowKey);
					});
					//Beg_HaNv_040423: Xem KQ Sinh thiet - L2PT-38656
					$('#' + _self.containerId + 'viewKQ_SinhThiet').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("selrow");
						_self.viewKQ_SinhThiet(rowKey);
					});
					//End_HaNv_040423
					//Begin_HaNv_02072018: Xem KQ Giai phau benh - L2K74TW-575
					$('#' + _self.containerId + 'viewKQ_GiaiPhauBenh').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("selrow");
						_self.viewKQ_GiaiPhauBenh(rowKey);
					});
					//End_HaNv_02072018
					//Begin_HaNv_16072020: Xem KQ XN PAP SMEAR (Tế bào học) - L2PT-23823
					$('#' + _self.containerId + 'viewKQ_TeBaoHoc').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("selrow");
						_self.viewKQ_TeBaoHoc(rowKey);
					});
					//End_HaNv_16072018
					//L2PT-8430 duonghn start : Xem kết quả VS SARS-CoV-2
					$('#' + _self.containerId + 'viewKQ_SARS_CoV_2').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("selrow");
						_self.viewKQ_SARS_CoV_2(rowKey);
					});
					//L2PT-8430 duonghn end
					//L2PT-16877 ttlinh start : Xem kết quả RMP Xpert
					$('#' + _self.containerId + 'viewKQ_RMP_XPERT').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("selrow");
						_self.viewKQ_RMP_XPERT(rowKey);
					});
					//L2PT-16877 ttlinh end
					//Beg_HaNv_111022: Xem KQ nhuộm phiến đồ Papanicolaou - L2PT-27431
					$('#' + _self.containerId + 'viewKQ_Nhuomphiendo').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._gridXnDetailId).getGridParam("selrow");
						_self.viewKQ_Nhuomphiendo(rowKey);
					});
					//End_HaNv_111022
				}
			});
			//END -- L2K74TW-491 -- hongdq --03052018
			//START -- L2DKHN-758 -- hongdq 20180315
			//calback cho man hinh xoa DV CLS
			EventUtil.setEvent("assignSevice_KetquaXoaDVCLS", function(e) {
				if (typeof (e) != 'undefined') {
					//var _self = this;
					_self._initWidget();
					EventUtil.raiseEvent("assignSevice_KetquaXoaDVCLS_TTXN", {
						msg : e.msg
					});
				}
			});
			//END -- L2DKHN-758 -- hongdq 20180315
			//L2PT-20786 start
			$('#' + _self.containerId + 'chkAllKhoa').unbind("click").bind("click", function() {
				_self._loadData();
				_self._bindEvent();
			});
			//L2PT-20786 end
			//Begin_HaNv_10052018: Hiển thị toolbar phụ thu theo cấu hình L2DKBD-1230
			$('#' + _self.containerId + 'toolbarIdhospitalfee_1').unbind("click").bind("click", function() {
				var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
				if (rowKey == null || rowKey == '') {
					DlgUtil.showMsg('Chưa chọn dịch vụ Xét nghiệm');
					return;
				}
				_self._taophieuphuthu(rowKey);
			});
			$('#' + _self.containerId + 'toolbarIdhospitalfee_2').unbind("click").bind("click", function() {
				var rowKey = $('#' + _self.containerId + _self.options._gridXnChiDinhId).getGridParam("selrow");
				if (rowKey == null || rowKey == '') {
					DlgUtil.showMsg('Chưa chọn dịch vụ Xét nghiệm');
					return;
				}
				_self._getDanhsachphieuphuthu(rowKey);
			});
			//End_HaNv_10052018
			//Begin_HaNv_28052018: Hiển thị toolbar in phiếu L2DKHN-777
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn27BV01').unbind("click").bind("click", function() {
				_self._inPhieu27BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn28BV01').unbind("click").bind("click", function() {
				_self._inPhieu28BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn29BV01').unbind("click").bind("click", function() {
				_self._inPhieu29BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn30BV01').unbind("click").bind("click", function() {
				_self._inPhieu30BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn31BV01').unbind("click").bind("click", function() {
				_self._inPhieu31BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn32BV01').unbind("click").bind("click", function() {
				_self._inPhieu32BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn33BV01').unbind("click").bind("click", function() {
				_self._inPhieu33BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn34BV01').unbind("click").bind("click", function() {
				_self._inPhieu34BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn35BV01').unbind("click").bind("click", function() {
				_self._inPhieu35BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn36BV01').unbind("click").bind("click", function() {
				_self._inPhieu36BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn36BV01_').unbind("click").bind("click", function() {
				_self._inPhieu36BV01_();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn37BV01').unbind("click").bind("click", function() {
				_self._inPhieu37BV01();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn27BV01AFB').unbind("click").bind("click", function() {
				_self._inPhieu27BV01AFB();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn27BV01NuoiCay').unbind("click").bind("click", function() {
				_self._inPhieu27BV01NuoiCay();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn27BV01KhangSinhDo').unbind("click").bind("click", function() {
				_self._inPhieu27BV01KhangSinhDo();
			});
			$('#' + _self.containerId + 'toolbarIdbtnPhieuIn27BV01DT').unbind("click").bind("click", function() {
				_self._inPhieu27BV01DT();
			});
			//End_HaNv_28052018
			//Beg_HaNv_040324: Lưu thông tin người xem, ngày xem chỉ số KQCLS - L2PT-74410
			$('#' + _self.containerId + 'tabKetQuaXetNghiem').unbind("click").bind("click", function() {
				if (cfObj.WGCLS_SAVE_VIEWKQ == '1') {
					var rowKey = $('#' + _self.containerId + _self.options._gridXnId).getGridParam("selrow");
					var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowKey);
					sql_par = [];
					sql_par.push({
						"name" : "[0]",
						"value" : rowData.MAUBENHPHAMID
					});
					jsonrpc.AjaxJson.execute("NTUD024.02", sql_par);
					$('#' + _self.containerId + _self.options._gridXnDetailId).trigger('reloadGrid');
				}
			});
			//End_HaNv_040324
		},
		//Begin_HaNv_10052018: Các hàm xử lý phiếu phụ thu L2DKBD-1230
		_taophieuphuthu : function(rowId) {
			var _self = this;
			var selRowId = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getGridParam', 'selrow');
			var rowDataXn = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', selRowId);
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					chidinhdichvu : '1',
					loaidichvu : '8',
					loaiphieumbp : '6',
					benhnhanid : rowDataXn.BENHNHANID,
					khambenhid : rowDataXn.KHAMBENHID,
					hosobenhanid : rowDataXn.HOSOBENHANID,
					tiepnhanid : rowDataXn.TIEPNHANID,
					doituongbenhnhanid : rowDataXn.DOITUONGBENHNHANID,
					hinhthucvaovienid : $("#hidHINHTHUCVAOVIENID").val(),
					loaibenhanid : $("#hidLOAIBENHANID").val(),
					loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),
					maubenhphamchaid : rowData.MAUBENHPHAMID,
					dichvukhambenhid : rowData.DICHVUKHAMBENHID,
					subDeptId : $("#hidPHONGID").val()
				}
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 8, paramInput, "Tạo phiếu phụ thu", 1300, 600);
				DlgUtil.open("divDlgDichVu");
			}
		},
		_getDanhsachphieuphuthu : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					khambenhid : _self.options._khambenhid,
					dichvucha_id : rowData.DICHVUKHAMBENHID
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDSPPT", "divDlg", "manager.jsp?func=../noitru/NTU02D040_DanhSachPhieuPhuThu", paramInput, "Danh sách phiếu phụ thu", 1250, 575);
				DlgUtil.open("divDlgDSPPT");
			}
		},
		//End_HaNv_10052018
		//Begin_HaNv_28052018: Các hàm xử lý in phiếu L2DKHN-777
		_haveTestAtPosition : function(data_arr, order) {
			var _self = this;
			for (var i = 0; i < data_arr.length; i++) {
				if (data_arr[i]["THUTUINMAUBYT"] == order) {
					return true;
				}
			}
			return false;
		},
		_findResultAtPosition : function(data_arr, order) {
			var _self = this;
			var result = "";
			for (var i = 0; i < data_arr.length; i++) {
				if (data_arr[i]["THUTUINMAUBYT"] == order) {
					result = data_arr[i]["GIATRI_KETQUA"];
				}
			}
			return result;
		},
		_inPhieu27BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU015_PHIEUXETNGHIEM_27BV01_QD4069_A5', 'pdf', par);
			}
		},
		_inPhieu28BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'chisohc',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 1) == false ? "" : "x"
				}, {
					name : 'kqhc',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 1)
				}, {
					name : 'chisohsto',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 2) == false ? "" : "x"
				}, {
					name : 'kqhsto',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 2)
				}, {
					name : 'chisohema',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 3) == false ? "" : "x"
				}, {
					name : 'kqhema',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 3)
				}, {
					name : 'chisomcv',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 4) == false ? "" : "x"
				}, {
					name : 'kqmcv',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 4)
				}, {
					name : 'chisomch',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 5) == false ? "" : "x"
				}, {
					name : 'kqmch',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 5)
				}, {
					name : 'chisomchc',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 6) == false ? "" : "x"
				}, {
					name : 'kqmchc',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 6)
				}, {
					name : 'chisohcnhan',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 7) == false ? "" : "x"
				}, {
					name : 'kqhcnhan',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 7)
				}, {
					name : 'chisohcluoi',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 8) == false ? "" : "x"
				}, {
					name : 'kqhcluoi',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 8)
				}, {
					name : 'chisotieucau',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 9) == false ? "" : "x"
				}, {
					name : 'kqtieucau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 9)
				}, {
					name : 'chisosotret',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 10) == false ? "" : "x"
				}, {
					name : 'kqsotret',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 10)
				}, {
					name : 'chisobc',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 11) == false ? "" : "x"
				}, {
					name : 'kqbc',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 11)
				}, {
					name : 'chisobachcau',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 12) == false ? "" : "x"
				}, {
					name : 'kqbachcau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 12)
				}, {
					name : 'kqtrungtinh',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 13)
				}, {
					name : 'kqaxit',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 14)
				}, {
					name : 'kqbazo',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 15)
				}, {
					name : 'kqmono',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 16)
				}, {
					name : 'kqlympho',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 17)
				}, {
					name : 'kqtebaobt',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 18)
				}, {
					name : 'chisomaulang',
					type : 'String',
					value : _self._haveTestAtPosition(data_ar, 19) == false ? "" : "x"
				}, {
					name : 'kqmaulang',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 19)
				}, {
					name : 'kqgio',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 20)
				}, {
					name : 'giomauchay',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 21)
				}, {
					name : 'phutmc',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 22)
				}, {
					name : 'giomaudong',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 23)
				}, {
					name : 'phutmd',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 24)
				}, {
					name : 'heabo',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 25)
				}, {
					name : 'herh',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 26)
				} ];
				console.log(JSON.stringify(par));
				openReport('window', 'NTU016_PHIEUXETNGHIEMHUYETHOC_28BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu29BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU039_PHIEUXETNGHIEMHUYETTUYDO_29BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu30BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'tgmauchay',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 1)
				}, {
					name : 'tgmaudong',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 2)
				}, {
					name : 'cocumau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 3)
				}, {
					name : 'ptgiay',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 4)
				}, {
					name : 'ptphantram',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 5)
				}, {
					name : 'ptinr',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 6)
				}, {
					name : 'tghowell',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 7)
				}, {
					name : 'apttgiay',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 8)
				}, {
					name : 'apttchiso',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 9)
				}, {
					name : 'ttgiay',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 10)
				}, {
					name : 'ttchiso',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 11)
				}, {
					name : 'npruou',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 12)
				}, {
					name : 'npvonkaulla',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 13)
				}, {
					name : 'adp',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 14)
				}, {
					name : 'collagen',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 15)
				}, {
					name : 'ristocetin',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 16)
				}, {
					name : 'yeuto3tieucau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 17)
				}, {
					name : 'yeuto4tieucau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 18)
				}, {
					name : 'dlgyeuto4tieucau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 19)
				}, {
					name : 'fdp',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 20)
				}, {
					name : 'ddiner',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 21)
				}, {
					name : 'xnkhac',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 22)
				} ];
				openReport('window', 'NTU040_PHIEUXETNGHIEMCHANDOANROILOANDONGCAMMAU_30BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu31BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU041_PHIEUXETNGHIEMSINHTHIETTUYXUONG_31BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu32BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'hongcau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 1)
				}, {
					name : 'bachcau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 2)
				}, {
					name : 'truhat1',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 3)
				}, {
					name : 'trutrong',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 4)
				}, {
					name : 'trumo',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 5)
				}, {
					name : 'than',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 6)
				}, {
					name : 'nieu',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 7)
				}, {
					name : 'bangquang',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 8)
				}, {
					name : 'oxalat',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 9)
				}, {
					name : 'cacbonat',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 10)
				}, {
					name : 'sulphat',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 11)
				}, {
					name : 'photphat',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 12)
				}, {
					name : 'urat',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 13)
				}, {
					name : 'truhat2',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 14)
				}, {
					name : 'tinhtrung',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 15)
				}, {
					name : 'slghc',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 16)
				}, {
					name : 'slgtebaoconhan',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 17)
				}, {
					name : 'bcdoantrungtinhtuy',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 18)
				}, {
					name : 'bclymphotuy',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 19)
				}, {
					name : 'bcmonotuy',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 20)
				}, {
					name : 'cactebaokhactuy',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 21)
				}, {
					name : 'hongcaukhac',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 22)
				}, {
					name : 'bcdoantrungtinhkhac',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 23)
				}, {
					name : 'bclymphokhac',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 24)
				}, {
					name : 'bcmonokhac',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 25)
				}, {
					name : 'cactbkhac',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 26)
				} ];
				openReport('window', 'NTU042_PHIEUXETNGHIEMNUOCDICH_32BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu33BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'ure',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 1)
				}, {
					name : 'glucose',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 2)
				}, {
					name : 'creatinin',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 3)
				}, {
					name : 'aciduric',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 4)
				}, {
					name : 'bilirubintp',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 5)
				}, {
					name : 'bilirubintt',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 6)
				}, {
					name : 'bilirubingt',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 7)
				}, {
					name : 'proteintp',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 8)
				}, {
					name : 'albumin',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 9)
				}, {
					name : 'globulin',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 10)
				}, {
					name : 'tyleag',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 11)
				}, {
					name : 'fibrinogen',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 12)
				}, {
					name : 'cholesterol',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 13)
				}, {
					name : 'triglycerid',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 14)
				}, {
					name : 'hdl',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 15)
				}, {
					name : 'ldl',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 16)
				}, {
					name : 'na',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 17)
				}, {
					name : 'sat',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 18)
				}, {
					name : 'magie',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 19)
				}, {
					name : 'ast',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 20)
				}, {
					name : 'alt',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 21)
				}, {
					name : 'amylase',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 22)
				}, {
					name : 'ck',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 23)
				}, {
					name : 'ckmb',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 24)
				}, {
					name : 'ldh',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 25)
				}, {
					name : 'ggt',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 26)
				}, {
					name : 'cholinesterase',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 27)
				}, {
					name : 'phosphatase',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 28)
				}, {
					name : 'phdongmanh',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 29)
				}, {
					name : 'pco2',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 30)
				}, {
					name : 'po2',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 31)
				}, {
					name : 'pco3',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 32)
				}, {
					name : 'kiemdu',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 33)
				} ];
				openReport('window', 'NTU017_PHIEUXETNGHIEMHOASINHMAU_33BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu34BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				}, {
					name : 'titrong',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 1)
				}, {
					name : 'ph',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 2)
				}, {
					name : 'bachcau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 3)
				}, {
					name : 'hongcau',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 4)
				}, {
					name : 'nitrit',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 5)
				}, {
					name : 'protein',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 6)
				}, {
					name : 'glucose',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 7)
				}, {
					name : 'thecetonic',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 8)
				}, {
					name : 'bilirubin',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 9)
				}, {
					name : 'urobilinogen',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 10)
				}, {
					name : 'duongchap',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 11)
				}, {
					name : 'porphyrin',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 12)
				}, {
					name : 'proteinbence',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 13)
				}, {
					name : 'tongthetich',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 14)
				}, {
					name : 'proteintieu',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 15)
				}, {
					name : 'glucosetieu',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 16)
				}, {
					name : 'uretieu',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 17)
				}, {
					name : 'creatinintieu',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 18)
				}, {
					name : 'acidurictieu',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 19)
				}, {
					name : 'amylasetieu',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 20)
				}, {
					name : 'na',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 21)
				}, {
					name : 'k',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 22)
				}, {
					name : 'huyetsacto',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 23)
				}, {
					name : 'stecobilin',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 24)
				}, {
					name : 'stecobilinogen',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 25)
				}, {
					name : 'mautptrongphan',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 26)
				}, {
					name : 'xnkhac',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 27)
				}, {
					name : 'proteintuy',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 28)
				}, {
					name : 'glucosetuy',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 29)
				}, {
					name : 'cloruatuy',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 30)
				}, {
					name : 'pandy',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 31)
				}, {
					name : 'xnkhactuy1',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 32)
				}, {
					name : 'xnkhactuy2',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 33)
				}, {
					name : 'xnkhactuy3',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 34)
				}, {
					name : 'xnkhactuy4',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 35)
				}, {
					name : 'xnkhactuy5',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 36)
				}, {
					name : 'hcltudo',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 37)
				}, {
					name : 'hcltoanphan',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 38)
				}, {
					name : 'xnkhacdichvi',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 39)
				}, {
					name : 'rivalta',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 40)
				}, {
					name : 'proteinkhac',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 41)
				}, {
					name : 'xnkhac1',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 42)
				}, {
					name : 'xnkhac2',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 43)
				}, {
					name : 'xnkhac3',
					type : 'String',
					value : _self._findResultAtPosition(data_ar, 44)
				} ];
				openReport('window', 'NTU018_PHIEUXETNGHIEMHOASINHNUOCTIEUPHANDICHCHOCDO_34BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu35BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU019_PHIEUXETNGHIEMVISINH_35BV01_QD4069_A5', 'pdf', par);
			}
		},
		_inPhieu36BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU043_PHIEUXETNGHIEMGIAIPHAUBENHSINHTHIET_36BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu36BV01_ : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU043_PHIEUXETNGHIEMGIAIPHAUBENHSINHTHIET_36BV01_QD4069_TH_A4', 'pdf', par);
			}
		},
		_inPhieu37BV01 : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NTU044_PHIEUXNGIAIPHAUBENHKHAMNGHIEMTUTHI_37BV01_QD4069_A4', 'pdf', par);
			}
		},
		_inPhieu27BV01AFB : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'AFB_PhieuXetNghiem_944', 'pdf', par);
			}
		},
		_inPhieu27BV01NuoiCay : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NHQNM_PHIEUXETNGHIEM_NUOICAY_27BV01_A4', 'pdf', par);
			}
		},
		_inPhieu27BV01KhangSinhDo : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				openReport('window', 'NHQNM_PHIEUXETNGHIEM_KHANGSINHDO_27BV01_A4', 'pdf', par);
			}
		},
		_inPhieu27BV01DT : function() {
			var _self = this;
			if ($("#hidMAUBENHPHAMID").val() == "") {
				DlgUtil.showMsg("Chưa chọn phiếu cần in", undefined, 3000);
			} else {
				var par = [ {
					name : 'id_maubenhpham',
					type : 'String',
					value : $("#hidMAUBENHPHAMID").val()
				} ];
				var param = [ $("#hidMAUBENHPHAMID").val() ];
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X003.TTBN", param.join('$'));
				var max = (data_ar.length > 4 ? 4 : data_ar.length);
				for (var i = 0; i < data_ar.length; i++) {
					var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
					if (hinh_anh1 != "" && hinh_anh1 != null)
						par.push({
							name : 'hinh_anh' + i,
							type : 'Image',
							value : hinh_anh1
						});
				}
				openReport('window', 'PHIEUXN_DACTHU_944', 'pdf', par);
			}
		},
		//End_HaNv_28052018
		_setColorKetQuaXN : function() {
			var _self = this;
			var rowids = $('#' + _self.containerId + _self.options._gridXnDetailId).getDataIDs();
			if (rowids != "") {
				for (var i = 0; i < rowids.length; i++) {
					var rowid = rowids[i];
					var row = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowid);
					var ket_qua = row.GIATRI_KETQUA;
					var tri_so_binh_thuong = row.TRISOBINHTHUONG;
					var chekKySo = row.PARAM_HASHED;
					var doi_tuong = "";
					if (tri_so_binh_thuong.toUpperCase().indexOf("TE") >= 0) {
						if ($("#hdfTuoi").val() < 6)
							doi_tuong = "TE";
						else
							doi_tuong = "NL";
					} else {
						if ($("#hdfGioiTinh").val() == "0")
							doi_tuong = "Nữ";
						else
							doi_tuong = "Nam";
					}
					var abnormal = CheckAbnormalResult(ket_qua, doi_tuong, tri_so_binh_thuong);
					if (abnormal == "-1") {
						$('#' + _self.containerId + _self.options._gridXnDetailId).find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
							$(element).css("font-weight", "bold");
							$(element).css("color", "blue");
						});
					} else if (abnormal == "1") {
						$('#' + _self.containerId + _self.options._gridXnDetailId).find("tr[id='" + rowid + "']").find('td').each(function(index, element) {
							$(element).css("font-weight", "bold");
							$(element).css("color", "red");
						});
					}
					//hiển thị icon phiếu kết quả ký số
					if (chekKySo != '') {
						var _icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
						$("#" + _self.containerId + _self.options._gridXnDetailId).jqGrid('setCell', rowid, 'ICON', _icon);
					}
					//Beg_HaNv_271023: view kq cls - L2PT-57865
					if (cfObj.WGCLS_SHOW_VIEW_KQ == '1') {
						var checked = row.VIEW_CLS == '1' ? 'checked="true"' : "";
						var chk = '<label class="control-label" for="' + (row.KETQUACLSID) + '_VIEW" style="margin-left:5px; margin-bottom: 0px;">';
						chk = chk + '<input type="checkbox" class="mgb5" ' + checked + ' id="' + (row.KETQUACLSID) + '_VIEW"></label>';
						$("#" + _self.containerId + _self.options._gridXnDetailId).jqGrid('setCell', rowid, 'VIEW', chk);
						$("#" + row.KETQUACLSID + '_VIEW').on("change", function(e) {
							var cslid = e.currentTarget.id;
							var check = e.currentTarget.checked ? '1' : '0';
							if (cslid) {
								cslid = cslid.replace('_VIEW', '');
								sql_par = [];
								sql_par.push({
									"name" : "[0]",
									"value" : check
								}, {
									"name" : "[1]",
									"value" : cslid
								});
								jsonrpc.AjaxJson.execute("NTUD024.01", sql_par);
							}
						});
					}
					//End_HaNv_271023
					//Beg_HaNv_040324: Lưu thông tin người xem, ngày xem chỉ số KQCLS - L2PT-74410
					if (cfObj.WGCLS_SAVE_VIEWKQ == '1' && row.NGUOIXEM_KQ) {
						var _icon = '<center><span class="glyphicon glyphicon-eye-open"></span></center>';
						$("#" + _self.containerId + _self.options._gridXnDetailId).jqGrid('setCell', rowid, 'VIEW_KQ', _icon);
					}
					//End_HaNv_040324
				}
			}
		},
		_copyPhieuXetNghiem : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				//Beg_HaNv_270521: Cho phép bác sĩ tạo bản sao DVKT (XN, CĐHA, PTTT) của bác sĩ khác - L2PT-19264
				if (cfObj.CLS_COPY_NOCHECK_ROLE == '0') {
					var _nguoitaoid = rowData.NGUOITAO_ID;
					var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
					if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
						DlgUtil.showMsg("Bạn không có quyền tạo bản sao cho phiếu này!");
						return false;
					}
				}
				//End_HaNv_270521
				var paramInput = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					khambenhid : rowData.KHAMBENHID,
					type : '1'
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgCopyMbp", "divDlg", "manager.jsp?func=../noitru/NTU02D070_ThoiGianDonThuoc", paramInput, "Tạo bản sao", 600, 360);
				DlgUtil.open("divDlgCopyMbp");
			}
		},
		//L2K74TW-301 - hongdq
		_editPhieuDT : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var dataObj = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D009.EV004", RSUtil.buildParam("", [ rowData.MAUBENHPHAMID ]));
				var data_ar = $.parseJSON(dataObj);
				if (data_ar != null && data_ar.length > 0) {
					data = data_ar[0];
					/*sql_par = RSUtil.buildParam("",
						[ rowData.KHAMBENHID, "4" ]);
					ComboUtil.getComboTag("cboMAUBENHPHAMID",
						"COM.PHIEUDIEUTRI", sql_par,
						data.PHIEUDIEUTRIID == null ? ''
								: data.PHIEUDIEUTRIID, {
							value : '',
							text : 'Chưa có phiếu điều trị'
						}, 'sql', '', function() {

						});*/
					var paramInput = {
						maubenhphamid : rowData.MAUBENHPHAMID,
						khambenhid : rowData.KHAMBENHID,
						phieudieutriid : data.PHIEUDIEUTRIID,
						mode : _self.options._modeView == '1' ? '1' : '0',
						hosobenhanid : rowData.HOSOBENHANID,
						tgchidinh : data.TGCHIDINH
					};
					dlgPopup = DlgUtil.buildPopupUrl("divDlgeditPhieuDT", "divDlg", "manager.jsp?func=../noitru/NTU02D081_Capnhat_PhieuDT", paramInput, "Cập nhật phiếu điều trị", 600, 360);
					DlgUtil.open("divDlgeditPhieuDT");
				}
			}
		},
		//Beg_HaNv_300522: Hiển thị thông tin lấy mẫu - L2PT-19262
		_thongtinlaymau : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				dlgPopup = DlgUtil.buildPopupUrl("divDlgThongTinLayMau", "divDlg", "manager.jsp?func=../canlamsang/CLS01X016_CapNhatNguoiLayMau&maubenhphamid=" + rowData.MAUBENHPHAMID, {},
						"Thông tin lấy mẫu", 500, 500);
				DlgUtil.open("divDlgThongTinLayMau");
				EventUtil.setEvent("CLS01X016_Thoat", function(e) {
					DlgUtil.close("divDlgThongTinLayMau");
				});
			}
		},
		//End_HaNv_300522
		//Begin_HaNv_06062020: Sửa bác sĩ chỉ định - L2PT-21679
		_editBacSi : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var paramInput = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					deptid : $("#hidKHOAID").val()
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgBacSyChiDinh", "divDlg", "manager.jsp?func=../noitru/NTU01H026_DoiBacSy", paramInput, "Đổi bác sĩ chỉ định", 500, 200);
				EventUtil.setEvent("assignSevice_saveChangeBS", function(e) {
					if (typeof (e) != 'undefined') {
						DlgUtil.showMsg(e.msg);
					}
					DlgUtil.close(e.divId);
				});
				DlgUtil.open("divDlgBacSyChiDinh");
			}
		},
		//End_HaNv_06062020
		_setImageStatus : function() {
			var _self = this;
			var ids = $("#" + _self.containerId + _self.options._gridXnId).getDataIDs();
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var row = $("#" + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', id);
				var _icon = '';
				var _trangthai = parseInt(row.TRANGTHAIMAUBENHPHAM);
				//Begin_HaNv_24082020: Check Lis Hủy tiếp nhận - L2PT-26335
				var _trangthaidl = parseInt(row.TRANGTHAIDULIEUMAUBENHPHAM);
				if (_trangthai == 2) {
					if (_trangthaidl == 0) {
						_icon = '<center><img src="../common/image/Close_Box_Red.png" width="15px"></center>';
					} else {
						_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
					}
				} else if (_trangthai == 3) {
					_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
					//Beg_HaNv_220723: check phieu XN khong co ketqua L2PT-47989
					if (row.XN_NO_RES != '0') {
						_icon = '<center><img src="../common/image/Circle_Blue.png" width="15px"></center>';
					}
				} else if (_trangthai == 4) {
					if (_trangthaidl == 0) {
						_icon = '<center><img src="../common/image/Flag_Red_New.png" width="15px"></center>';
					} else {
						_icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
					}
				}
				//End_HaNv_24082020
				$("#" + _self.containerId + _self.options._gridXnId).jqGrid('setCell', id, "ICON", _icon);
			}
		},
		//L2PT-22465 start
		_setAmountStatus : function() {
			var _self = this;
			var ids = $("#" + _self.containerId + _self.options._gridXnId).getDataIDs();
			var _soluong = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.PTH", "1");
			var _canhbaoSL = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'CANHBAO_SL_BN_XN');
			var _slxanh = _canhbaoSL.split(";")[0];
			var _sldo = _canhbaoSL.split(";")[1];
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var row = $("#" + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', id);
				var _icon = '';
				if (row.LOAI_PHONG_CANHBAO_ID == '1') {
					if (parseInt(_soluong) <= parseInt(_slxanh)) {
						_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
					} else if (parseInt(_slxanh) < parseInt(_soluong) && parseInt(_soluong) < parseInt(_sldo)) {
						_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
					} else if (parseInt(_soluong) >= parseInt(_sldo)) {
						_icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
					}
				}
				$("#" + _self.containerId + _self.options._gridXnId).jqGrid('setCell', id, "ICON3", _icon);
			}
		}, // L2PT-22465 end
		_deleteDVXetNghiem : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				DlgUtil.showConfirm("Bạn có chắc chắn xóa dịch vụ xét nghiệm không?", function(flag) {
					if (flag) {
						var par = [];
						obj = new Object();
						obj.DICHVUKHAMBENHID = rowData.DICHVUKHAMBENHID;
						obj.MAUBENHPHAMID = rowData.MAUBENHPHAMID;
						par.push(obj);
						var paramInput = {
							parram : par,
							type : 1
						};
						dlgPopup = DlgUtil.buildPopupUrl("divDlgcapnhatLD", "divDlg", "manager.jsp?func=../noitru/NTU02D085_XoaDVCLS", paramInput, "Cập nhật lý do", 600, 250);
						DlgUtil.open("divDlgcapnhatLD");
					}
				});
			}
		},
		_deleteAllDVXetNghiem : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				DlgUtil.showConfirm("Bạn có chắc chắn xóa HẾT dịch vụ của xét nghiệm không?", function(flag) {
					if (flag) {
						//Begin_HaNv_18032020: Điều chỉnh cách xóa dữ liệu CLS - L2PT-18272
						var par = [];
						obj = new Object();
						obj.MAUBENHPHAMID = rowData.MAUBENHPHAMID;
						par.push(obj);
						/*var length = rowData.length;
						for (var i = 0; i < length; i++) {
							obj = new Object();
							obj.DICHVUKHAMBENHID = rowData[i].DICHVUKHAMBENHID;
							obj.MAUBENHPHAMID = rowData[i].MAUBENHPHAMID;
							par.push(obj);
						}*/
						var paramInput = {
							parram : par,
							type : 2
						};
						dlgPopup = DlgUtil.buildPopupUrl("divDlgcapnhatLD", "divDlg", "manager.jsp?func=../noitru/NTU02D085_XoaDVCLS", paramInput, "Cập nhật lý do", 600, 250);
						DlgUtil.open("divDlgcapnhatLD");
						//End_HaNv_18032020
					}
				});
			}
		},
		//them phieu cam doan pttt tuonglt
		_exportCamDoanPTTT : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'i_khambenhid',
					type : 'String',
					value : _self.options._khambenhid
				}, {
					name : 'i_dichvukhambenhid',
					type : 'String',
					value : rowData.DICHVUKHAMBENHID
				} ];
				openReport('window', "NTU027_GIAYCAMDOANCHAPNHANPTTT_A4_944", "pdf", par);
			}
		},
		//START L2HOTRO-13031
		_exportprintDVCSC : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			paramInput = {
				maubenhphamid : $("#hidMAUBENHPHAMID").val()
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D105_InPhieuCon", paramInput, "In Dịch vụ - chỉ số con", 1100, 600);
			DlgUtil.open("divDlgDeleteXN");
		},
		//Begin_HaNv_20022020: In Kết quả dịch vụ vi khuẩn nhuộm soi BS - L2PT-16778
		_printKQNhuomSoiBS : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'DICHVUKHAMBENHID',
					type : 'String',
					value : rowData.DICHVUKHAMBENHID
				} ];
				openReport('window', "rpt_DK_LSO_KQ_NHUOM_SOI_BS", "pdf", par);
			}
		},
		//End_HaNv_20022020
		_deleteDVXetNghiemReject : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthaiid = rowData.TRANGTHAI_ID;
				if (_trangthaiid == null)
					_trangthaiid = -1;
				_trangthaiid = parseInt(_trangthaiid);
				//Begin_HaNv_05112019: Cho phép xóa DV chưa thu tiền, trạng thái chưa tiếp nhận hoặc đã hủy - DKLAN
				var allowXoaChuaThuTien = cfObj.CLS_ALLOW_XOA_CHUATHUTIEN;
				if (allowXoaChuaThuTien == 1) {
					if (_trangthaiid == 3) {
						DlgUtil.showMsg("Dịch vụ đã thu tiền, không được phép xóa!");
						return false;
					} else if (rowData.TRANGTHAIKETQUA != 1 && rowData.TRANGTHAIKETQUA != 8) {
						DlgUtil.showMsg("Dịch vụ phải chưa tiếp nhận hoặc đã hủy mới được phép xóa!");
						return false;
					}
				} else if (_trangthaiid != 9) {
					DlgUtil.showMsg("Dịch vụ không bị từ chối nên không được phép xóa!");
					return;
				}
				//End_HaNv_05112019
				/*DlgUtil.showConfirm("Bạn có chắc chắn xóa dịch vụ xét nghiệm không?", function(flag) {
					if (flag) {
						var _par = [ rowData.DICHVUKHAMBENHID, rowData.MAUBENHPHAMID, "" ];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL_DVXN_REJECT", _par.join('$'));
						if (_return == 1) {
							DlgUtil.showMsg("Xóa thành công dịch vụ xét nghiệm", undefined, 1500);
							_self._initWidget();
						} else if (_return == 0) {
							DlgUtil.showMsg("Xóa không thành công dịch vụ xét nghiệm");
						} else if (_return == -1) {
							DlgUtil.showMsg("Dịch vụ đã thu tiền nên không được phép xóa");
						} else if (_return == -2) {
							DlgUtil.showMsg("Bệnh nhân đã ra viên nên không được phép xóa dịch vụ, Bạn phải mở lại bệnh án để sửa!");
						}
					}
				});*/
				//Beg_HaNv_201021: BVTM-6612
				DlgUtil.showConfirm("Bạn có chắc chắn xóa dịch vụ xét nghiệm không?", function(flag) {
					if (flag) {
						var par = [];
						obj = new Object();
						obj.DICHVUKHAMBENHID = rowData.DICHVUKHAMBENHID;
						obj.MAUBENHPHAMID = rowData.MAUBENHPHAMID;
						par.push(obj);
						var paramInput = {
							parram : par,
							type : 1
						};
						dlgPopup = DlgUtil.buildPopupUrl("divDlgcapnhatLD", "divDlg", "manager.jsp?func=../noitru/NTU02D085_XoaDVCLS", paramInput, "Cập nhật lý do", 600, 250);
						DlgUtil.open("divDlgcapnhatLD");
					}
				});
				//End_HaNv_201021
			}
		},
		_sendRequestDeleteReject : function(rowId) {
			var _self = this;
			if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
				var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
				if (rowData != null) {
					DlgUtil.showConfirm("Bạn có chắc chắn yêu cầu hủy dịch vụ không?", function(flag) {
						if (flag) {
							var _par = [ rowData.MAUBENHPHAMID, rowData.DICHVUKHAMBENHID, 0 ];
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _par.join('$'));
							if (_return == 1) {
								//tuyennx_edit_start_20190425 L1PT-661
								if (cfObj.KBH_TATTHONGBAO_KBHB == "0") {
									DlgUtil.showMsg("Yêu cầu hủy dịch vụ thành công", undefined, 1500);
								}
								//tuyennx_edit_end_20190425 L1PT-661
								_self._initWidget();
							} else if (_return == 2) {
								DlgUtil.showMsg("Dịch vụ chưa thu tiền");
							} else if (_return == 3) {
								DlgUtil.showMsg("Phiếu đã được tiếp nhận hoặc có kết quả, không thể yêu cầu hủy dịch vụ");
							}
							//Begin_HaNv_05072019: Quy trình hoàn hủy với bệnh nhân đóng tạm ứng - L1PT-1245
							else if (_return == 4) {
								DlgUtil.showMsg("BN chưa đóng tạm ứng. Không thể hủy yêu cầu dịch vụ!");
							}
							//End_HaNv_05072019
							else {
								DlgUtil.showMsg("Yêu cầu hủy dịch vụ thất bại");
							}
						}
					});
				}
			}
		},
		_undoRequestDeleteReject : function(rowId) {
			var _self = this;
			if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
				var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
				if (rowData != null && (rowData.YC_HOAN == '2' || rowData.YC_HOAN == '1')) {
					DlgUtil.showConfirm("Bạn có chắc chắn yêu cầu khôi phục dịch vụ không?", function(flag) {
						if (flag) {
							var _par = [ rowData.MAUBENHPHAMID, rowData.DICHVUKHAMBENHID ];
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.002", _par.join('$'));
							if (_return == 1) {
								DlgUtil.showMsg("Yêu cầu khôi phục dịch vụ thành công");
								_self._initWidget();
							} else if (_return == 2) {
								DlgUtil.showMsg("Dịch vụ chưa thu tiền");
							} else if (_return == 3) {
								DlgUtil.showMsg("Dịch vụ không yêu cầu hủy hoặc đã xác nhận, không thể khôi phục");
							} else {
								DlgUtil.showMsg("Yêu cầu khôi phục dịch vụ thất bại");
							}
						}
					});
				} else {
					DlgUtil.showMsg("Dịch vụ không yêu cầu hủy hoặc đã xác nhận, không thể khôi phục");
				}
			}
		},
		_updateDVKhongThanhToanDT : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthaiid = rowData.TRANGTHAI_ID;
				if (_trangthaiid == null)
					_trangthaiid = -1;
				_trangthaiid = parseInt(_trangthaiid);
				DlgUtil.showConfirm("Bạn có chắc chắn chuyển loại dịch vụ xét nghiệm sang dịch vụ miễn giảm thanh toán đồng thời không?", function(flag) {
					if (flag) {
						var _par = [ rowData.DICHVUKHAMBENHID ];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT026.EV003", _par.join('$'));
						if (_return == 1) {
							DlgUtil.showMsg("Cập nhật thành công dịch vụ miễn giảm thanh toán đồng thời");
							_self._viewCKDetailMauBenhPham(rowData.MAUBENHPHAMID);
						} else if (_return == 0) {
							DlgUtil.showMsg("Cập nhật không thành công dịch vụ miễn giảm thanh toán đồng thời");
						} else if (_return == 2) {
							DlgUtil.showMsg("Bệnh nhân không phải là đối tượng BHYT hoặc không phải dịch vụ bảo hiểm");
						} else if (_return == 3) {
							DlgUtil.showMsg("Không tồn tại dịch vụ chính trong phiếu");
						} else if (_return == 4) {
							DlgUtil.showMsg("Dịch vụ đã thu tiền");
						} else if (_return == 5) {
							DlgUtil.showMsg("Bệnh nhân đã kết thúc điều trị. Không thể cập nhật");
						}
					}
				});
			}
		},
		//Beg_HaNv_131023: Chuyển thanh toán VP - L2PT-53262
		_changeVP : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				DlgUtil.showConfirm("Bạn có chắc chắn chuyển loại thanh toán của dịch vụ sang viện phí không?", function(flag) {
					if (flag) {
						var objDVKB = new Object();
						objDVKB.HOSOBENHANID = $('#hidHOSOBENHANID').val();
						objDVKB.TIEPNHANID = $("#hidTIEPNHANID").val();
						objDVKB.DS_DVKBID = rowData.DICHVUKHAMBENHID;
						objDVKB.LOAIDOITUONG_MOI = '4';
						objDVKB.TENDTMOI = 'VIỆN PHÍ';
						objDVKB.HIS_BHYT_ID = '-1';
						objDVKB.FLAG_CHUYEN = "0";
						var r_json = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI01T004.09', JSON.stringify(objDVKB));
						if (r_json == '-1') {
							DlgUtil.showMsg("Xảy ra lỗi");
						} else if (r_json == '-2') {
							DlgUtil.showMsg("Đã kết thúc bệnh án, không thể chuyển loại thanh toán");
						} else if (r_json == '0') {
							DlgUtil.showMsg("Chuyển sang thẻ khác thành công");
						} else if (r_json == '-19') {
							DlgUtil.showMsg("Không thể chuyển dịch vụ sang loại hợp đồng với loại khám của BN này");
						} else if (r_json == '-191') {
							DlgUtil.showMsg("Tổng tiền các dịch vụ đã chọn vượt quá hạn mức, không thể chuyển sang loại hợp đồng");
						} else if (r_json == '-192') {
							DlgUtil.showMsg("Không thể chuyển đối tượng cho BN gửi thực hiện CLS");
						} else {
							try {
								var result = JSON.parse(r_json);
								if (!result || result.length == 0) {
									DlgUtil.showMsg("Không lấy được kết quả chuyển loại thanh toán");
								} else {
									if (!result.RESULT && result.RESULT == 0) {
										DlgUtil.showMsg("Chuyển loại thanh toán không thành công");
									}
									if (result.MESSAGE && result.MESSAGE.length > 0) {
										DlgUtil.showMsg(result.MESSAGE);
									}
								}
							} catch (e) {
								DlgUtil.showMsg("Có lỗi khi đọc kết quả chuyển loại thanh toán");
								console.log(e);
							}
						}
					}
				});
			}
		}
		//End_HaNv_131023
		//======================================= Added by SONDN
		,
		_nhapSinhThiet : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			var title = "Thông tin sinh thiết";
			if (rowData != null) {
				var myVar = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					dichvukhambenhid : rowData.DICHVUKHAMBENHID,
					tendichvu : rowData.TENDICHVU
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgMauSinhThiet", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K062_MauSinhThiet", myVar, title, 1000, 500);
				DlgUtil.open("dlgMauSinhThiet");
			}
		}
		//======================================= End Added by SONDN
		//L2PT-109940
		,
		_nhapGiaiPhau : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnChiDinhId).jqGrid('getRowData', rowId);
			var title = "Thông tin giải phẫu bệnh";
			if (rowData != null) {
				var myVar = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					dichvukhambenhid : rowData.DICHVUKHAMBENHID,
					tendichvu : rowData.TENDICHVU
				};
				dlgPopup = DlgUtil.buildPopupUrl("dlgGiaiPhau", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K062_GiaiPhauBenh", myVar, title, 1000, 300);
				DlgUtil.open("dlgGiaiPhau");
			}
		},
		_xoaPhieuDichVuKhongCoKetQua : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 3) {
					DlgUtil.showConfirm("Bạn có chắc chắn xóa các dịch vụ không có kết quả không?", function(flag) {
						if (flag) {
							var _par = [ rowData.MAUBENHPHAMID ];
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL.PDV.NO_RLT", _par.join('$'));
							if (_return == 1) {
								DlgUtil.showMsg("Xóa thành công các dịch vụ không có kết quả", undefined, 1500);
								_self._initWidget();
							} else if (_return == 0) {
								DlgUtil.showMsg("Xóa không thành công phiếu xét nghiệm");
							} else if (_return == 2) {
								DlgUtil.showMsg("Phiếu không có dịch vụ không có kết quả");
							}
						}
					});
				} else if (_trangthai < 3) {
					DlgUtil.showMsg("Phiếu này chưa kết thúc!");
				}
			}
		},
		_xoaPhieuDichVu : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				if (_self.options._modeView != "2") {
					var _nguoitaoid = rowData.NGUOITAO_ID;
					var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
					//START L2PT-4775
					if (cfObj.NTU_XN_CHECKXOA_USER == '1') {
						if (_nguoitaoid != $("#hidUserID").val()) {
							DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
							return false;
						}
					} else {
						if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
							DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
							return false;
						}
					}
				}
				//Begin_HaNv_08042019: Không cho phép xóa dịch vụ của BN BHYT - L1PT-428
				if (rowData.DOITUONGBENHNHANID == 1) {
					var isNotAllowDel = cfObj.BM2_NOTALLOW_XOA_CLS;
					if (isNotAllowDel == 1) {
						DlgUtil.showMsg("Không cho phép xóa dịch vụ của bệnh nhân BHYT!");
						return false;
					}
				}
				//End_HaNv_08042019
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				//Begin_HaNv_07012019: Không hiển thị thông báo khi xóa phiếu cls thành công - L2PT-516
				var isSaveNotMsg = cfObj.NTU_SAVEOK_NOTMSG;
				//End_HaNv_07012019
				//Begin_HaNv_23032018: Xoa, cap nhat phieu khong can thao tac huy L2DKBD-1032
				var isCapNhatKoCanHuy = cfObj.XOA_CAPNHAT_PHIEU_KOCANHUY;
				if (_trangthai == 1 || _trangthai == 8 || isCapNhatKoCanHuy == 1) {
					DlgUtil.showConfirm("Bạn có chắc chắn xóa phiếu xét nghiệm không?", function(flag) {
						if (flag) {
							if (_trangthai >= 2 && _trangthai != 8) {
								var rs = _self._deleteRequest(rowId, 0);
								if (rs == 1)
									return;
							}
							//End_HaNv_23032018
							var _par = [ rowData.MAUBENHPHAMID ];
							var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL.PDV.024", _par.join('$'));
							if (_return == 1) {
								if (isSaveNotMsg == 0) {
									DlgUtil.showMsg("Xóa thành công phiếu xét nghiệm", undefined, 1500);
								}
								_self._initWidget();
							} else if (_return == 0) {
								DlgUtil.showMsg("Xóa không thành công phiếu phiếu xét nghiệm");
							} else if (_return == -1) {
								DlgUtil.showMsg("Phiếu xét nghiệm đã thu tiền nên không được phép xóa");
							}
						}
					});
				} else if (_trangthai >= 2 && _trangthai != 8) {
					DlgUtil.showMsg("Phiếu không ở trạng thái sửa phiếu hoặc hủy nên không thể xóa!");
				}
			}
		},
		//Begin_HaNv_23032018: them type cho truong hop xoa sua ko can huy L2DKBD-1032
		_deleteRequest : function(rowId, type) {
			if (typeof (type) == "undefined") {
				type = 1;
			}
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			//Begin_HaNv_05102020: Ràng buộc chỉ định dịch vụ khi tờ điều trị kí số - L2PT-28416
			if (rowData.PHIEUDIEUTRIID > 0 && cfObj.HIS_CHECK_DIEUTRI_KISO == '1') {
				var checkKiSo = jsonrpc.AjaxJson.ajaxCALL_SP_I("PDT.CHECK_KISO", rowData.PHIEUDIEUTRIID);
				if (checkKiSo > 0) {
					DlgUtil.showMsg('Không thể cập nhật thông tin phiếu được gắn với phiếu điều trị đã được kí số!');
					return 1;
				}
			}
			//End_HaNv_05102020
			//Beg_HaNv_080721: Check thu tiền trước khi gọi đến hàm xử lý - L2PT-5243
			var par_tt = [ {
				"name" : "[0]",
				"value" : rowData.MAUBENHPHAMID
			} ];
			var checkThutien = jsonrpc.AjaxJson.getOneValue("CHECK.MBP.THUTIEN", par_tt);
			if (parseInt(checkThutien) > 0) {
				DlgUtil.showMsg("Phiếu xét nghiệm đã thu tiền nên không được hủy yêu cầu");
				return 1;
			}
			//End_HaNv_080721
			// L2PT-88453 start
			var check_duyet_cls = jsonrpc.AjaxJson.ajaxCALL_SP_S('CHECK.DUYET.CLS', rowData.MAUBENHPHAMID);
			if (check_duyet_cls == 'PHIEU_DADUYET_CLS') {
				DlgUtil.showMsg("Phiếu xét nghiệm đã duyệt CLS nên không được hủy yêu cầu");
				return 1;
			}
			// L2PT-88453 end
			if (rowData != null) {
				// check quyen xoa du lieu
				var _nguoitaoid = rowData.NGUOITAO_ID;
				var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
				if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
					DlgUtil.showMsg("Bạn không có quyền hủy yêu cầu phiếu này!");
					return 1;
				}
				//L2PT-91016
				if (cfObj.NTU_HUY_CHECK_DUYET_MP == '1') {
					var _par = [ rowData.MAUBENHPHAMID, rowData.TIEPNHANID ];
					var data_dv_kb = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU.CHECK.DMP", _par.join('$'));
					if (data_dv_kb != '0' && data_dv_kb != '-1') {
						DlgUtil.showMsg("Phiếu đã được duyệt CLS Miễn phí, yêu cầu gỡ duyệt cls trước khi hủy phiếu!");
						return 1;
					}
				}
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 2) {
					// Begin_laphm_13062019: không cho huỷ dịch vụ khi có phiếu phụ thu đi kèm - L2PT-5721
					var _sophieudikem = 0;
					var sql_par_chek = [];
					sql_par_chek = RSUtil.buildParam("", [ rowData.MAUBENHPHAMID ]);
					var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU.024.4", sql_par_chek);
					var rows = JSON.parse(data);
					if (rows != null && rows.length > 0) {
						_sophieudikem = rows[0].SOPHIEUDIKEM;
					}
					if (_sophieudikem == null)
						_sophieudikem = 0;
					if (_sophieudikem > 0) {
						DlgUtil.showMsg("Phiếu này đã có phụ thu đi kèm,\n không được phép hủy yêu cầu");
						return 1;
					}
					// End_laphm_13062019
					if (LIS_CONNECTION_TYPE == "1" && LIS_SERVICE_DOMAIN_NAME != "") {
						var _self = this;
						var request_url = LIS_SERVICE_DOMAIN_NAME + LIS_DELETE_REQUEST;
						console.log("request_url=" + request_url);
						var so_phieu = rowData.SOPHIEU;
						var request = new LabRequestSet();
						if (LIS_AUTHENTICATION_GATE) {
							request = createLabRequest(rowData.MAUBENHPHAMID);
						} else {
							request.SID = so_phieu;
							var param = [ rowData.MAUBENHPHAMID ];
							var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
							for (var i = 0; i < data_ar.length; i++) {
								request.TestCodeList.push(data_ar[i]["MADICHVU"]);
							}
						}
						console.log("request=" + JSON.stringify(request));
						$.ajax({
							type : "POST",
							contentType : "application/json; charset=utf-8",
							headers : {
								'Username' : LIS_USERNAME,
								'Identify-Code' : so_phieu,
								'Lis-Access-Hash' : getHash(so_phieu),
								'Token' : getLabToken(),
								'idToken' : getLabidToken(),
								'password' : getLabSecretKey(),
								'maTinh' : getLabProvinceCode(),
								'maCSKCB' : getLabHospitalCode()
							},
							data : JSON.stringify(request),
							url : request_url,
							success : function(data) {
								var _par = [ rowData.MAUBENHPHAMID, 1, 1 ];
								var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ", _par.join('$'));
								if (_return == 1) {
									if (type == 1) {
										//tuyennx_edit_start_20190425 L1PT-661
										if (cfObj.KBH_TATTHONGBAO_KBHB == "0") {
											DlgUtil.showMsg("Phiếu đã được hủy yêu cầu thành công!", undefined, 1500);
										}
										//tuyennx_edit_end_20190425 L1PT-661
										_self._initWidget();
									} else {
										return 0;
									}
								} else if (_return == 0) {
									DlgUtil.showMsg("Hủy yêu cầu phiếu thất bại!");
									return 1;
								} else if (_return == -1) {
									if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
										if (rowData.TRANGTHAIMAUBENHPHAM != 2) {
											DlgUtil.showMsg('Phiếu đã được tiếp nhận hoặc đã có kết quả. Không thể yêu cầu hủy');
											return;
										} else {
											DlgUtil.showConfirm("Dịch vụ đã thu tiền. Bạn có chắc chắn muốn yêu cầu hủy tất cả dịch vụ của phiếu này?", function(flag) {
												if (flag) {
													var _parTmp = [ rowData.MAUBENHPHAMID, 0, 1 ];
													var _returnTmp = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _parTmp.join('$'));
													if (_returnTmp == 1) {
														DlgUtil.showMsg("Yêu cầu hủy phiếu thành công", undefined, 1500);
														_self._initWidget();
														return 1;
													} else {
														DlgUtil.showMsg("Yêu cầu hủy phiếu thất bại");
														return 1;
													}
												}
											});
										}
									} else {
										DlgUtil.showMsg("Phiếu xét nghiệm đã thu tiền nên không được hủy yêu cầu");
										return 1;
									}
								}
							},
							error : function(xhr) {
								DlgUtil.showMsg('Không thể hủy yêu cầu bên LIS');
								return 1;
							}
						});
					} else {
						var _par = [ rowData.MAUBENHPHAMID, 1, 1 ];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ", _par.join('$'));
						if (_return == 1) {
							if (type == 1) {
								//tuyennx_edit_start_20190425 L1PT-661
								if (cfObj.KBH_TATTHONGBAO_KBHB == "0") {
									DlgUtil.showMsg("Phiếu đã được hủy yêu cầu thành công!", undefined, 1500);
								}
								//tuyennx_edit_end_20190425 L1PT-661   	       			
								_self._initWidget();
							} else {
								return 0;
							}
						} else if (_return == 0) {
							DlgUtil.showMsg("Hủy yêu cầu phiếu thất bại!");
							return 1;
						} else if (_return == -1) {
							if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
								if (rowData.TRANGTHAIMAUBENHPHAM != 2) {
									DlgUtil.showMsg('Phiếu đã được tiếp nhận hoặc đã có kết quả. Không thể yêu cầu hủy');
									return;
								} else {
									DlgUtil.showConfirm("Dịch vụ đã thu tiền. Bạn có chắc chắn muốn yêu cầu hủy tất cả dịch vụ của phiếu này?", function(flag) {
										if (flag) {
											var _parTmp = [ rowData.MAUBENHPHAMID, 0, 1 ];
											var _returnTmp = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _parTmp.join('$'));
											if (_returnTmp == 1) {
												DlgUtil.showMsg("Yêu cầu hủy phiếu thành công", undefined, 1500);
												_self._initWidget();
												return 1;
											} else {
												DlgUtil.showMsg("Yêu cầu hủy phiếu thất bại");
												return 1;
											}
										}
									});
								}
							} else {
								DlgUtil.showMsg("Phiếu xét nghiệm đã thu tiền nên không được hủy yêu cầu");
								return 1;
							}
						}
					}
				} else if (_trangthai == 1) {
					DlgUtil.showMsg("Phiếu đã được hủy yêu cầu!", undefined, 1500);
					return 1;
				} else if (_trangthai == 8) {
					if (cfObj.HIS_BACSY_YCHUY_DV == '1') {
						DlgUtil.showConfirm("Dịch vụ đã thu tiền. Bạn có chắc chắn muốn yêu cầu hủy tất cả dịch vụ của phiếu này?", function(flag) {
							if (flag) {
								var _parTmp = [ rowData.MAUBENHPHAMID, 0, 1 ];
								var _returnTmp = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _parTmp.join('$'));
								if (_returnTmp == 1) {
									DlgUtil.showMsg("Yêu cầu hủy phiếu thành công", undefined, 1500);
									_self._initWidget();
									return 1;
								} else {
									DlgUtil.showMsg("Yêu cầu hủy phiếu thất bại");
									return 1;
								}
							}
						});
					} else {
						DlgUtil.showMsg("Phiếu không ở trạng thái đã gửi nên không thể hủy yêu cầu");
						return 1;
					}
				} else if (_trangthai > 2 && _trangthai != 8) {
					DlgUtil.showMsg("Phiếu không ở trạng thái đã gửi nên không thể hủy yêu cầu");
					return 1;
				}
			}
		},
		//End_HaNv_23032018
		// xóa yêu cầu đã gửi tới LIS
		deleteRequestOnLab : function(rowId) {
			if (LIS_CONNECTION_TYPE == "1" && LIS_SERVICE_DOMAIN_NAME != "") {
				var _self = this;
				var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
				var request_url = LIS_SERVICE_DOMAIN_NAME + LIS_DELETE_REQUEST;
				console.log("request_url=" + request_url);
				var so_phieu = rowData.SOPHIEU;
				var request = new LabRequestSet();
				if (LIS_AUTHENTICATION_GATE) {
					request = createLabRequest(rowData.MAUBENHPHAMID);
				} else {
					request.SID = so_phieu;
					var param = [ rowData.MAUBENHPHAMID ];
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.DSKQ", param.join('$'));
					for (var i = 0; i < data_ar.length; i++) {
						request.TestCodeList.push(data_ar[i]["MADICHVU"]);
					}
				}
				console.log("request=" + JSON.stringify(request));
				$.ajax({
					type : "POST",
					contentType : "application/json; charset=utf-8",
					headers : {
						'Username' : LIS_USERNAME,
						'Identify-Code' : so_phieu,
						'Lis-Access-Hash' : getHash(so_phieu),
						'Token' : getLabToken(),
						'idToken' : getLabidToken(),
						'password' : getLabSecretKey(),
						'maTinh' : getLabProvinceCode(),
						'maCSKCB' : getLabHospitalCode()
					},
					data : JSON.stringify(request),
					url : request_url,
					success : function(data) {
						console.log("response=" + JSON.stringify(data));
					},
					error : function(xhr) {
						console.log("delete request fail: " + JSON.stringify(xhr));
					}
				});
			}
		},
		_sendRequest : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				if (_self.options._modeView != "2") {
					var _nguoitaoid = rowData.NGUOITAO_ID;
					var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
					if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
						DlgUtil.showMsg("Bạn không có quyền gửi yêu cầu phiếu này!");
						return false;
					}
				}
				// Check chuyen khoa dieu tri khong co quyen gui phieu
				if ($("#hidKHOAID").val() && cfObj.NTU_DTKH_ALLOW_UPDATE_DVKT != '1') {//L2PT-51997 cấu hình cho phép gửi lại phiếu đối với điều trị kết hợp
					var check_par = [];
					check_par.push({
						"name" : "[0]",
						"value" : $("#hidKHOAID").val()
					});
					check_par.push({
						"name" : "[1]",
						"value" : rowData.KHAMBENHID
					});
					var checkDtkh = jsonrpc.AjaxJson.getOneValue("CHECK.DTKH", check_par);
					if (checkDtkh !== "0") {
						DlgUtil.showMsg("Chuyên khoa điều trị kết hợp không được phép gửi yêu cầu!");
						return false;
					}
				}
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 1 || _trangthai == 8) {
					var _par = [ rowData.MAUBENHPHAMID, 2, 0 ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ", _par.join('$'));
					if (_return == 1) {
						if (cfObj.KBH_TATTHONGBAO_KBHB == "0") {
							DlgUtil.showMsg("Phiếu đã được gửi yêu cầu thành công!");
						}
						_self._initWidget();
					} else if (_return == 0) {
						DlgUtil.showMsg("Gửi yêu cầu phiếu thất bại!");
					}
				} else {
					DlgUtil.showMsg("Phiếu đã được gửi yêu cầu!");
				}
			}
		},
		//Beg_HaNv_110921: Menu thuốc, vật tư đi kèm - BVTM-5601
		_taoPhieuThuocKemHaoPhi : function(rowId) {
			var _opt = "02D010"
			var _msg = "Tạo phiếu thuốc đi kèm hao phí";
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					khambenhid : _self.options._khambenhid,
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : rowData.DICHVUKHAMBENHID,
					opt : _opt, // tao phieu thuoc
					macdinh_hao_phi : 9
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + _opt, "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1300, 590);
				DlgUtil.open("divDlgTaoPhieuThuoc" + _opt);
			}
		},
		_taoPhieuVatTuKemHaoPhi : function(rowId) {
			var _opt = "02D015";
			var _msg = "Tạo phiếu vật tư đi kèm hao phí";
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					khambenhid : _self.options._khambenhid,
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : rowData.DICHVUKHAMBENHID,
					opt : _opt, // tao phieu thuoc
					macdinh_hao_phi : 9
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + _opt, "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1300, 590);
				DlgUtil.open("divDlgTaoPhieuThuoc" + _opt);
			}
		},
		_taoPhieuThuocKem : function(rowId) {
			var _opt = "02D010"
			var _msg = "Tạo phiếu thuốc đi kèm";
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					khambenhid : _self.options._khambenhid,
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : rowData.DICHVUKHAMBENHID,
					opt : _opt
				// tao phieu thuoc     					
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + _opt, "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1300, 590);
				DlgUtil.open("divDlgTaoPhieuThuoc" + _opt);
			}
		},
		_taoPhieuVatTuKem : function(rowId) {
			var _opt = "02D015";
			var _msg = "Tạo phiếu vật tư đi kèm";
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					khambenhid : _self.options._khambenhid,
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : rowData.DICHVUKHAMBENHID,
					opt : _opt
				// tao phieu thuoc
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + _opt, "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1300, 590);
				DlgUtil.open("divDlgTaoPhieuThuoc" + _opt);
			}
		},
		_dsPhieuThuocVatTuDiKem : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					khambenhid : _self.options._khambenhid,
					dichvucha_id : rowData.DICHVUKHAMBENHID
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDSPTDK", "divDlg", "manager.jsp?func=../noitru/NTU02D043_DanhSachPhieuThuocDiKem", paramInput, "Danh sách phiếu thuốc, vật tư đi kèm", 1300,
						620);
				DlgUtil.open("divDlgDSPTDK");
			}
		},
		_dsPhieuThuocVatTu : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					khambenhid : _self.options._khambenhid,
					benhnhanid : _self.options._benhnhanid,
					trangthaikhambenh : $("#hidTRANGTHAIKHAMBENH").val(),
					dichvuKhambenhID : rowData.DICHVUKHAMBENHID
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDSPTVT", "divDlg", "manager.jsp?func=../noitru/NTU02D078_DanhSachPhieuThuocVatTu", paramInput, "Danh sách phiếu thuốc, vật tư", 1200, 635);
				DlgUtil.open("divDlgDSPTVT");
			}
		},
		//End_HaNv_110921
		//Beg_HaNv_270521: Hiển thị In Covid-19 - L2PT-3475
		_exportPXNCovid19 : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				openReport('window', "COVIDPHIEUXETNGHIEM", "pdf", par);
			}
		},
		//End_HaNv_270521
		//Beg_HaNv_220723: Hiển thị In Phiếu XN Lao - L2PT-47789
		_exportPXN_Lao : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				openReport('window', "PHIEU_XN_LAO", "pdf", par);
			}
		},
		//End_HaNv_220723
		_exportPXNCD : function(rowId) {
			var _self = this;
			hopital = cfObj.OPT_HOSPITAL_ID;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				//Begin_HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
				var usingReportCode = cfObj.CDDV_USING_REPORT_CODE;
				//End_HaNv_05082019
				if (hopital == 965) {
					//lay loai dt bhyt
					doituongbenhnhanid = rowData.DOITUONGBENHNHANID;
					if (doituongbenhnhanid == 1) {
						//lay loai dich vu la bhyt hay thuong
						var _loaidichvu = 0;
						var _par_loai = [ rowData.MAUBENHPHAMID ];
						var arr_loaidichvu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075_LOAIDICHVU", _par_loai.join('$'));
						if (arr_loaidichvu != null && arr_loaidichvu.length > 0) {
							for (var i = 0; i < arr_loaidichvu.length; i++) {
								_loaidichvu = arr_loaidichvu[i].BHYT;
								if (_loaidichvu == 1) {
									// openReport('window', "PHIEU_XETNGHIEM_A4_965", "pdf", par);
									_self._printOrPrintCa('PHIEU_XETNGHIEM_A4_965', par);
								} else
									// openReport('window', "PHIEU_XETNGHIEMDICHVU_A4_965", "pdf", par);
									_self._printOrPrintCa('PHIEU_XETNGHIEMDICHVU_A4_965', par);
							}
						}
					} else {
						// openReport('window', "PHIEU_XETNGHIEMDICHVU_A4_965", "pdf", par);
						_self._printOrPrintCa('PHIEU_XETNGHIEMDICHVU_A4_965', par);
					}
				}
				//Beg_HaNv_110822: In riêng các mẫu phiếu CLS đối với BN khám sức khỏe theo đoàn - L2PT-23549
				else if (cfObj.INCLS_KSK_THEO_DOAN == '1' && $('#hidLOAITIEPNHANID').val() == '5') {
					var par = [ {
						name : 'maubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					} ];
					// openReport('window', "KSK_PHIEU_XETNGHIEM", "pdf", par);
					_self._printOrPrintCa('KSK_PHIEU_XETNGHIEM', par);
				}
				//End_HaNv_110822
				else if (usingReportCode == 1) {//HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
					if (hopital == 923) {
						var par = [ {
							name : 'benhnhanid',
							type : 'String',
							value : rowData.BENHNHANID
						}, {
							name : 'donviid',
							type : 'String',
							value : _self.options._donviid
						}, {
							name : 'maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						}, {
							name : 'report_code',
							type : 'String',
							value : 'KSK_PHIEU_XETNGHIEM'
						} ];
						// openReport('window', "KSK_PHIEU_XETNGHIEM", "pdf", par);
						_self._printOrPrintCa('KSK_PHIEU_XETNGHIEM', par);
					} else {
						var _par_code = [ rowData.MAUBENHPHAMID ];
						var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE", _par_code.join('$'));
						if (i_report_code != null && i_report_code.length > 0) {
							for (var i = 0; i < i_report_code.length; i++) {
								var _report_code = i_report_code[i].REPORT_CODE;
								var par_rpt = [ {
									name : 'maubenhphamid',
									type : 'String',
									value : rowData.MAUBENHPHAMID
								}, {
									name : 'report_code',
									type : 'String',
									value : _report_code
								} ];
								//Begin_HaNv_040321: Tích hợp phiếu in BDHNI - BVTM-10
								if (hopital == 10284) {
									par_rpt.push({
										name : 'khambenhid',
										type : 'String',
										value : _self.options._khambenhid
									});
									par_rpt.push({
										name : 'maubenhphamids',
										type : 'String',
										value : rowData.MAUBENHPHAMID
									});
									par_rpt.push({
										name : 'id_maubenhpham',
										type : 'String',
										value : rowData.MAUBENHPHAMID
									});
									//Beg_HaNv_061021: Tùy biến formCall để in báo cáo - BVTM-5873
									if (_self.options._formCall == 'TN_NGT2') {
										par_rpt.push({
											name : 'ngoaitiepnhan',
											type : 'String',
											value : '1'
										});
									}
									//End_HaNv_061021
								}
								//End_HaNv_040321
								// openReport('window', _report_code, "pdf", par_rpt);
								_self._printOrPrintCa(_report_code, par_rpt);
							}
						} else {
							var par_rpt = [ {
								name : 'maubenhphamid',
								type : 'String',
								value : rowData.MAUBENHPHAMID
							}, {
								name : 'report_code',
								type : 'String',
								value : 'PHIEU_XETNGHIEM_CHUNG'
							} ];
							// openReport('window', "PHIEU_XETNGHIEM_CHUNG", "pdf", par_rpt);
							_self._printOrPrintCa('PHIEU_XETNGHIEM_CHUNG', par_rpt);
						}
					}
					var i_check_hiv = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.CHIDINH.HIV", _par_code.join('$'));
					if (i_check_hiv > 0) {
						openReport('window', "PHIEUDONGY_XETNGHIEMHIV_A4_951", "pdf", par);
					}
				} else if (hopital == 957 || hopital == 38440) {//HaNv_140122: L2PT-13716
					//tuyendv 0406
					var _par_code = [ rowData.MAUBENHPHAMID ];
					var lstDoiTuong = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.DSLOAI.DTCLS", _par_code.join('$'));
					var _loaidoituong = '';
					if (lstDoiTuong != null && lstDoiTuong.length > 0) {
						for (var i1 = 0; i1 < lstDoiTuong.length; i1++) {
							_loaidoituong = _loaidoituong + lstDoiTuong[i1].LOAIDOITUONG;
						}
					}
					//end tuyendv 0406
					var par_rpt = [ {
						name : 'nhommaubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'nhomdoituong',
						type : 'String',
						value : _loaidoituong
					} ];
					// openReport('window', "PHIEU_CLS_ALL", "pdf", par_rpt);
					_self._printOrPrintCa('PHIEU_CLS_ALL', par_rpt);
				} else if (hopital == 1014) {
					var _dtbnid = rowData.DOITUONGBENHNHANID;
					//START L2HOTRO-12397
					if (cfObj.NTU_BM2_12397_INTD == '1') {
						// CommonUtil.inPhieu('window', 'PHIEU_CHIDINH_CLS_BHYT', 'pdf', par, null, true, true);
						_self._printOrPrintCa('PHIEU_CHIDINH_CLS_BHYT', par, '1', null, true, true);
					} else {
						// openReport('window', "PHIEU_CHIDINH_CLS_BHYT", "pdf", par);
						_self._printOrPrintCa('PHIEU_CHIDINH_CLS_BHYT', par);
					}
				}
				//Begin_HaNv_29082019: Tich hop report - nguoiyeucau: BangT - L2PT-8463
				else if (hopital == 939) {
					// openReport('window', "PHIEU_XETNGHIEM", "pdf", par, true, true);
					_self._printOrPrintCa('PHIEU_XETNGHIEM', par);
				}
				//End_HaNv_29082019
				//Begin_HaNv_24092019: Tich hop report CLS - nguoiphoihop: TuyenDv - L2PT-8688
				else if (hopital == 987) {
					if ($('#hidLOAITIEPNHANID').val() == '1') { //khambenh
						// openReport('window', "x", "pdf", par);
						_self._printOrPrintCa('x', par);
					} else {
						// openReport('window', "PHIEU_XETNGHIEM_A4", "pdf", par);
						_self._printOrPrintCa('PHIEU_XETNGHIEM_A4', par);
					}
				}//End_HaNv_24092019
				else {
					// openReport('window', "PHIEU_XETNGHIEM", "pdf", par);
					_self._printOrPrintCa('PHIEU_XETNGHIEM', par);
				}
			}
		},
		//END -- HISL2TK-611 --hongdq
		//START L2HOTRO-12397
		_exportPXNCDVIEW : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				var _dtbnid = rowData.DOITUONGBENHNHANID;
				if (_dtbnid == 1) {
					if (cfObj.NTU_BM2_12397_INTD == '1') {
						CommonUtil.inPhieu('window', 'PHIEU_CHIDINH_CLS_BHYT', 'pdf', par, null, false, true);
					}
				} else {
					if (cfObj.NTU_BM2_12397_INTD == '1') {
						CommonUtil.inPhieu('window', 'PHIEU_CHIDINH_CLS_YEUCAU', 'pdf', par, null, false, true);
					}
				}
			}
		},
		_exportSelectedXN : function(rowId) {
			var _self = this;
			paramInput = {
				benhnhanid : _self.options._benhnhanid,
				khambenhid : _self.options._khambenhid,
				hosobenhanid : _self.options._hosobenhanid,
				lnmbp : _self.options._lnmbp,
				loaitnid : $('#hidLOAITIEPNHANID').val(),//HaNv_110822 - L2PT-23549
				ingop : '0'//In nhiều phiếu
			};
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDeleteXN", "divDlg", "manager.jsp?func=../noitru/NTU02D093_InPhieuXN", paramInput, "In phiếu XN", 1100, 600);
			DlgUtil.open("divDlgDeleteXN");
		},
		//END -- HISL2TK-611 --hongdq
		_exportPXNCDAuto : function(rowId) {
			var _self = this;
			var _type = 'pdf';
			if (cfObj.HIS_FILEEXPORT_TYPE != '0') {
				_type = cfObj.HIS_FILEEXPORT_TYPE;
			}
			var hopital = cfObj.OPT_HOSPITAL_ID;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				//Begin_HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
				var usingReportCode = cfObj.CDDV_USING_REPORT_CODE;
				//End_HaNv_05082019
				if (hopital == 965) {
					var rpName = "VNPTHIS_IN_A4_";
					rpName += rowData.SOPHIEU;
					rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
					rpName += "." + _type;
					//lay loai dt bhyt
					doituongbenhnhanid = rowData.DOITUONGBENHNHANID;
					if (doituongbenhnhanid == 1) {
						//lay loai dich vu la bhyt hay thuong
						var _loaidichvu = 0;
						var _par_loai = [ rowData.MAUBENHPHAMID ];
						var arr_loaidichvu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075_LOAIDICHVU", _par_loai.join('$'));
						if (arr_loaidichvu != null && arr_loaidichvu.length > 0) {
							for (var i = 0; i < arr_loaidichvu.length; i++) {
								_loaidichvu = arr_loaidichvu[i].BHYT;
								if (_loaidichvu == 1) {
									//openReport('window', "PHIEU_XETNGHIEM_A4_965", "pdf", par); 
									CommonUtil.inPhieu('window', 'PHIEU_XETNGHIEM_A4_965', _type, par, rpName);
								} else {
									//openReport('window', "PHIEU_XETNGHIEMDICHVU_A4_965", "pdf", par); 
									CommonUtil.inPhieu('window', 'PHIEU_XETNGHIEMDICHVU_A4_965', _type, par, rpName);
								}
							}
						}
					} else {
						//openReport('window', "PHIEU_XETNGHIEMDICHVU_A4_965", "pdf", par);   
						CommonUtil.inPhieu('window', 'PHIEU_XETNGHIEMDICHVU_A4_965', _type, par, rpName);
					}
				} else if (usingReportCode == 1) {//HaNv_05082019: Dieu chinh mau chi dinh CLS - L2PT-7417
					var _par_code = [ rowData.MAUBENHPHAMID ];
					var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE", _par_code.join('$'));
					if (i_report_code != null && i_report_code.length > 0) {
						for (var i = 0; i < i_report_code.length; i++) {
							var _report_code = i_report_code[i].REPORT_CODE;
							var par_rpt = [ {
								name : 'maubenhphamid',
								type : 'String',
								value : rowData.MAUBENHPHAMID
							}, {
								name : 'report_code',
								type : 'String',
								value : _report_code
							} ];
							//Begin_HaNv_040321: Tích hợp phiếu in BDHNI - BVTM-10
							if (hopital == 10284) {
								par_rpt.push({
									name : 'khambenhid',
									type : 'String',
									value : _self.options._khambenhid
								});
								par_rpt.push({
									name : 'maubenhphamids',
									type : 'String',
									value : rowData.MAUBENHPHAMID
								});
								par_rpt.push({
									name : 'id_maubenhpham',
									type : 'String',
									value : rowData.MAUBENHPHAMID
								});
								//Beg_HaNv_061021: Tùy biến formCall để in báo cáo - BVTM-5873
								if (_self.options._formCall == 'TN_NGT2') {
									par_rpt.push({
										name : 'ngoaitiepnhan',
										type : 'String',
										value : '1'
									});
								}
								//End_HaNv_061021
							}
							//End_HaNv_040321
							openReport('window', _report_code, "pdf", par_rpt);
						}
					} else {
						var par_rpt = [ {
							name : 'maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						}, {
							name : 'report_code',
							type : 'String',
							value : 'PHIEU_XETNGHIEM_CHUNG'
						} ];
						openReport('window', "PHIEU_XETNGHIEM_CHUNG", "pdf", par_rpt);
					}
					var i_check_hiv = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.CHIDINH.HIV", _par_code.join('$'));
					if (i_check_hiv > 0) {
						openReport('window', "PHIEUDONGY_XETNGHIEMHIV_A4_951", "pdf", par);
					}
				} else if (hopital == 957) {
					var _par_code = [ rowData.MAUBENHPHAMID ];
					var lstDoiTuong = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.LOAI.DTCLS", _par_code.join('$'));
					if (lstDoiTuong != null && lstDoiTuong.length > 0) {
						for (var i = 0; i < lstDoiTuong.length; i++) {
							var _loaidoituong = lstDoiTuong[i].LOAIDOITUONG;
							var par_rpt = [ {
								name : 'maubenhphamid',
								type : 'String',
								value : rowData.MAUBENHPHAMID
							}, {
								name : 'i_loaidoituong',
								type : 'String',
								value : _loaidoituong
							} ];
							openReport('window', "PHIEU_XETNGHIEM", "pdf", par_rpt);
						}
					}
				} else if (hopital == 1014) {
					if (cfObj.NTU_BM2_12397_INTD == '1') {
						CommonUtil.inPhieu('window', 'PHIEU_CHIDINH_CLS_BHYT', 'pdf', par, null, true, true);
					} else {
						openReport('window', "PHIEU_CHIDINH_CLS_BHYT", "pdf", par);
					}
				} else {
					var rpName = "VNPTHIS_IN_A5_";
					rpName += rowData.SOPHIEU;
					rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
					rpName += "." + _type;
					//openReport('window', "PHIEU_XETNGHIEM", "pdf", par);  
					CommonUtil.inPhieu('window', 'PHIEU_XETNGHIEM', _type, par, rpName);
				}
			}
		},
		//in ket qua phieu xet nghiem
		_exportKetQuaPXNCD : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 2 || _trangthai == 1) {
					DlgUtil.showMsg("Phiếu chưa thực hiện nên chưa có kết quả để in!");
					return false;
				}
				//Beg_HaNv_140821: Tích hợp report_code - L2PT-5980
				var par = [ {
					name : 'id_maubenhpham',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				}, {
					name : 'maubenhphamids',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				}, {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				if (hospital_id == '951' || cfObj.INPHIEU_THEO_DV == '1') {//HaNv_101022: L2PT-27418
					var _par_code = [ rowData.MAUBENHPHAMID ];
					var i_report_code = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET.REPORT.CODE_KQ", _par_code.join('$'));
					for (var i = 0; i < i_report_code.length; i++) {
						var _report_code = i_report_code[i].REPORT_CODE;
						var k = _report_code.split(';');
						if (k.length > 0) {
							for (var j = 0; j < k.length; j++) {
								openReport('window', k[j], 'pdf', par);
							}
						} else {
							openReport('window', _report_code, 'pdf', par);
						}
					}
				}
				//Beg_HaNv_210623: Tích hợp report_code theo nhóm DV - L2PT-45619
				else if (cfObj.XN_INPHIEU_THEO_NHOMDV == '1') {
					var param = [ rowData.MAUBENHPHAMID ];
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS01X002.NHOMDV", param.join('$'));
					if (data_ar != null && data_ar.length > 0) {
						for (var i = 0; i < data_ar.length; i++) {
							var par = [ {
								name : 'id_maubenhpham',
								type : 'String',
								value : rowData.MAUBENHPHAMID
							}, {
								name : 'id_nhomdichvu',
								type : 'String',
								value : data_ar[i].NHOMDICHVUID
							} ];
							openReport('window', 'CLS_PhieuXetNghiem', 'pdf', par);
						}
					}
				}
				//End_HaNv_140821
				else {
					openReport('window', 'PhieuXetNghiem', 'pdf', par);
				}
				//End_HaNv_140821
			}
		},
		_exportKetQuaPXNCDAuto : function(rowId) {
			var _self = this;
			var _type = 'pdf';
			if (cfObj.HIS_FILEEXPORT_TYPE != '0') {
				_type = cfObj.HIS_FILEEXPORT_TYPE;
			}
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 2 || _trangthai == 1) {
					DlgUtil.showMsg("Phiếu chưa thực hiện nên chưa có kết quả để in!");
					return false;
				}
				var rpName = "VNPTHIS_IN_A5_";
				rpName += rowData.SOPHIEU;
				rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS'));
				rpName += "." + _type;
				var par = [ {
					name : 'id_maubenhpham',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				//openReport('window', 'PhieuXetNghiem', 'pdf', par);   
				CommonUtil.inPhieu('window', 'PhieuXetNghiem', _type, par, rpName);
			}
		},
		_editOrgDone : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				var _nguoitaoid = rowData.NGUOITAO_ID;
				var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
				if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
					DlgUtil.showMsg("Bạn không có quyền sửa phòng thực hiện phiếu này!");
					return false;
				}
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 3) {
					DlgUtil.showMsg("Phiếu đã hoàn thành nên bạn không thể sửa phòng thực hiện");
					return false;
				}
				//mo popup nhap benh nhan khi dbclick on row
				var paramInput = {
					maubenhphamid : rowData.MAUBENHPHAMID,
					loaiPhieu : _self.options._lnmbp,
					org_type : 6
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgEditOrgDone", "divDlg", "manager.jsp?func=../noitru/NTU02D038_SuaPhongThucHien", paramInput, "Chọn phòng thực hiện", 900, 450);
				DlgUtil.open("divDlgEditOrgDone");
			}
		},
		_updatePhieuXetNghiem : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				var _nguoitaoid = rowData.NGUOITAO_ID;
				var _nguoitaoclsid = rowData.NGUOITAOCLS_ID;//HaNv_270121: L2PT-33188
				//START L2PT-4775
				if (cfObj.NTU_XN_CHECKXOA_USER == '1') {
					if (_nguoitaoid != $("#hidUserID").val()) {
						DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
						return false;
					}
				} else {
					if (_checkRoles(_nguoitaoid, $("#hidUserID").val(), _nguoitaoclsid) == false && _self.options._modeTiepDon == "0" && cfObj.NTU_ALL_ACCESS_WIDGET == "0") { //L2PT-19873
						DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
						return false;
					}
				}
				//Beg_HaNv_080721: Check thu tiền trước khi gọi đến hàm xử lý - L2PT-5243
				var par_tt = [ {
					"name" : "[0]",
					"value" : rowData.MAUBENHPHAMID
				} ];
				var checkThutien = jsonrpc.AjaxJson.getOneValue("CHECK.MBP.THUTIEN", par_tt);
				if (parseInt(checkThutien) > 0) {
					DlgUtil.showMsg("Phiếu xét nghiệm đã thu tiền nên không thể cập nhật phiếu!");
					return false;
				}
				//End_HaNv_080721
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				//Begin_HaNv_23032018: Xoa, cap nhat phieu khong can thao tac huy L2DKBD-1032
				var isCapNhatKoCanHuy = cfObj.XOA_CAPNHAT_PHIEU_KOCANHUY;
				if (_trangthai <= 1 || isCapNhatKoCanHuy == 1) {
					if (_trangthai > 1) {
						var rs = _self._deleteRequest(rowId, 0);
						if (rs == 1)
							return;
					}
					//End_HaNv_23032018
					//mo popup nhap benh nhan khi dbclick on row
					var paramInput = {
						benhnhanid : rowData.BENHNHANID,
						mabenhnhan : rowData.MABENHNHAN,
						khambenhid : rowData.KHAMBENHID,
						tiepnhanid : rowData.TIEPNHANID,
						hosobenhanid : rowData.HOSOBENHANID,
						doituongbenhnhanid : rowData.DOITUONGBENHNHANID,
						loaitiepnhanid : $("#hidLOAITIEPNHANID").val(),//HaNv_07052020: Fix lỗi PDT - L2PT-20390
						maubenhphamid : rowData.MAUBENHPHAMID,
						loaiPhieu : _self.options._lnmbp,
						subDeptId : $("#hidPHONGID").val(),
						modeFunction : _self.options._modeView,
						modeTiepDon : _self.options._modeTiepDon
					//Begin_HaNv_26072018
					};
					//Begin_HaNv_30112020: Xây dựng giao diện chỉ định DV mới cho DLKHA - L2PT-30889
					var cddvDLKHA = cfObj.CDDV_GIAODIEN_KHA == '1' ? true : false;
					if (cddvDLKHA) {
						dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV_KHA" + "&loaidichvu=" + 5, paramInput,
								"Cập nhật phiếu xét nghiệm", 1300, 600);
					} else {
						dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5, paramInput, "Cập nhật phiếu xét nghiệm",
								1300, 600);
					}
					//End_HaNv_30112020
					DlgUtil.open("divDlgDichVu");
				} else {
					DlgUtil.showMsg("Không thể sửa phiếu này!\nPhiếu này đã hoặc đang được xử lý");
				}
			}
		},
		_viewXetnghiemDetail : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var sql_par1 = [];
				sql_par1.push({
					"name" : "[0]",
					"value" : rowData.MAUBENHPHAMID
				});
				GridUtil.loadGridBySqlPage(_self.containerId + _self.options._gridXnDetailId, "NT.024.2", sql_par1);
				GridUtil.loadGridBySqlPage(_self.containerId + _self.options._gridXnChiDinhId, "NT024.CLS.CHIDINH", sql_par1);
			}
		},
		//START -- L2K74TW-491 -- hongdq --03052018
		viewKQ_Soidom : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			dichvuid = rowData.DICHVUTHUCHIENID;
			console.log('dichvuid: ' + dichvuid + ', DICHVUKHAMBENHID: ' + rowData.DICHVUKHAMBENHID);
			var url = "";
			url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_AFB_K74&idmaubenhpham=" + rowData.MAUBENHPHAMID + "&iddichvukb=" + rowData.DICHVUKHAMBENHID + "&type=read&q=0" +
					"&showNutSuaNgayTraKQ=false&show=false" + "&tiepnhanid=" + $("#hidTIEPNHANID").val() + "&rolePTH=false&dichvuid=" + dichvuid;
			var titleUrl = "Xem kết quả xét nghiệm cho vi khuẩn Soi đờm";
			EventUtil.setEvent("CLS01X002_LUU", function(e) {
				$("#hdfIDMauBenhPham").val();
			});
			EventUtil.setEvent("CLS01X002_HUY", function(e) {
				DlgUtil.close("dlgNhapKetQua");
			});
			var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua", "divNhapKetQua", url, {}, titleUrl, 1290, 600);
			dlgPopup.open("dlgNhapKetQua");
		},
		viewKQ_VKLao : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			dichvuid = rowData.DICHVUTHUCHIENID;
			console.log('dichvuid: ' + dichvuid + ', DICHVUKHAMBENHID: ' + rowData.DICHVUKHAMBENHID);
			$("#hdfIDDichVuKB").val(rowData.DICHVUKHAMBENHID);
			var url = "";
			url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_VKLM1_K74&idmaubenhpham=" + rowData.MAUBENHPHAMID + "&iddichvukb=" + rowData.DICHVUKHAMBENHID + "&type=read&q=0" +
					"&showNutSuaNgayTraKQ=false&show=false" + "&tiepnhanid=" + $("#hidTIEPNHANID").val() + "&rolePTH=false&dichvuid=" + dichvuid;
			titleUrl = "Xem kết quả xét nghiệm cho vi khuẩn lao";
			EventUtil.setEvent("CLS01X002_LUU", function(e) {
				$("#hdfIDMauBenhPham").val();
				reloadAllGrid();
			});
			EventUtil.setEvent("CLS01X002_HUY", function(e) {
				DlgUtil.close("dlgNhapKetQua");
			});
			var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua", "divNhapKetQua", url, {}, titleUrl, 1290, 600);
			dlgPopup.open("dlgNhapKetQua");
		},
		viewKQ_Khangsinhdo : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			dichvuid = rowData.DICHVUTHUCHIENID;
			console.log('dichvuid: ' + dichvuid + ', DICHVUKHAMBENHID: ' + rowData.DICHVUKHAMBENHID);
			var url = "";
			url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_VKKSD2_K74&idmaubenhpham=" + rowData.MAUBENHPHAMID + "&iddichvukb=" + rowData.DICHVUKHAMBENHID + "&type=read" +
					"&showNutSuaNgayTraKQ=false&show=true" + "&tiepnhanid=" + $("#hidTIEPNHANID").val() + "&rolePTH=false&dichvuid=" + dichvuid;
			var titleUrl = "Xem kết quả xét nghiệm cho vi khuẩn ngoài lao";
			EventUtil.setEvent("CLS01X002_LUU", function(e) {
				$("#hdfIDMauBenhPham").val();
			});
			EventUtil.setEvent("CLS01X002_HUY", function(e) {
				DlgUtil.close("dlgNhapKetQua");
			});
			var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua", "divNhapKetQua", url, {}, titleUrl, 1290, 600);
			dlgPopup.open("dlgNhapKetQua");
		},
		//END -- L2K74TW-491 -- hongdq --03052018
		//Beg_HaNv_040423: Xem KQ Sinh thiet - L2PT-38656
		viewKQ_SinhThiet : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			var paramInput = {
				dichvukhambenhid : rowData.DICHVUKHAMBENHID,
				ketquaclsid : rowData.KETQUACLSID,
				type : "read"
			};
			EventUtil.setEvent("assignSevice_saveKetQuaGPBOk", function(e) {
				DlgUtil.close("divDlgGPB");
			});
			dlgPopup = DlgUtil.buildPopupUrl("divDlgGPB", "divDlg", "manager.jsp?func=../canlamsang/CLS01X017_KetQuaSinhThiet", paramInput, "Kết quả sinh thiết", 1200, 600);
			DlgUtil.open("divDlgGPB");
		},
		//End_HaNv_040423
		//Begin_HaNv_02072018: Xem KQ Giai phau benh - L2K74TW-575
		viewKQ_GiaiPhauBenh : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			//L2PT-21138 start ttlinh
			var paramInput = {
				dichvukhambenhid : rowData.DICHVUKHAMBENHID,
				ketquaclsid : rowData.KETQUACLSID,
				mode : 1
			//L2PT-26719
			};
			if (cfObj.NTU_POPUP_KQXN_GPB == '1') {
				EventUtil.setEvent("assignSevice_saveKetQuaGPBOk", function(e) {
					DlgUtil.close("divDlgGPB");
				});
				var popupGPB = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'CLS_POPUP_KQXN_GPB');
				if (popupGPB == "1") {
					dlgPopup = DlgUtil.buildPopupUrl("divDlgGPB", "divDlg", "manager.jsp?func=../canlamsang/CLS01X013_KetQuaGiaiPhau", paramInput, "Xem kết quả giải phẫu bệnh", 1200, 600);
				} else if (popupGPB == "2") {
					dlgPopup = DlgUtil.buildPopupUrl("divDlgGPB", "divDlg", "manager.jsp?func=../canlamsang/CLS01X017_KetQuaGiaiPhau", paramInput, "Xem kết quả giải phẫu bệnh", 1200, 600);
				} else if (popupGPB == "3") {
					dlgPopup = DlgUtil.buildPopupUrl("divDlgGPB", "divDlg", "manager.jsp?func=../canlamsang/CLS01X019_KetQuaGiaiPhau", paramInput, "Xem kết quả giải phẫu bệnh", 1200, 600);
				} else if (popupGPB == "4") { //L2PT-26719
					dlgPopup = DlgUtil.buildPopupUrl("divDlgGPB", "divDlg", "manager.jsp?func=../canlamsang/CLS01X020_KetQuaGiaiPhau", paramInput, "Xem kết quả giải phẫu bệnh", 1200, 600);
				} else {
					dlgPopup = DlgUtil.buildPopupUrl("divDlgGPB", "divDlg", "manager.jsp?func=../canlamsang/CLS01X011_KetQuaGiaiPhau", paramInput, "Xem kết quả giải phẫu bệnh", 1200, 600);
				}
				DlgUtil.open("divDlgGPB");
			} else { //L2PT-21138 end
				var url = "";
				url = "manager.jsp?func=../canlamsang/CLS02C003_TraKetQuaCDHA&idmaubenhpham=" + rowData.MAUBENHPHAMID + "&idketquacls=" + rowData.KETQUACLSID + "&type=read" +
						"&rolePTH=false&printGPB=true";
				var titleUrl = "Xem kết quả xét nghiệm giải phẫu bệnh";
				var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua", "divNhapKetQua", url, {}, titleUrl, 1290, 600);
				EventUtil.setEvent("CLS02C003_HUY", function(e) {
					DlgUtil.close("dlgNhapKetQua");
				});
				dlgPopup.open("dlgNhapKetQua");
			}
		},
		//End_HaNv_02072018
		//Begin_HaNv_16072020: Xem KQ XN PAP SMEAR (Tế bào học) - L2PT-23823
		viewKQ_TeBaoHoc : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			var mahosobenhan = ($("#hidLOAITIEPNHANID").val() == '1') ? $("#hidMAHOSOBENHAN").val() : $("#hidMABENHAN").val();
			var url = "";
			url = "manager.jsp?func=../canlamsang/CLS01X014_KQ_TeBaoHoc&dichvukhambenhid=" + rowData.DICHVUKHAMBENHID + "&mahosobenhan=" + mahosobenhan + "&maubenhphamid=" + rowData.MAUBENHPHAMID +
					"&ketquaclsid=" + rowData.KETQUACLSID + "&type=read";
			var titleUrl = "Xem kết quả phiếu xét nghiệm PAP SMEAR";
			var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua", "divNhapKetQua", url, {}, titleUrl, 800, 600);
			EventUtil.setEvent("assignPapanicolaou_cancel", function(e) {
				DlgUtil.close("dlgNhapKetQua");
			});
			dlgPopup.open("dlgNhapKetQua");
		},
		//End_HaNv_16072018
		//Beg_HaNv_111022: Xem KQ nhuộm phiến đồ Papanicolaou - L2PT-27431
		viewKQ_Nhuomphiendo : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			var mahosobenhan = $("#hidMAHOSOBENHAN").val();
			var url = "";
			url = "manager.jsp?func=../noitru/NTU02D146_Phieu_KQ_Papanicolaou&dichvukhambenhid=" + rowData.DICHVUKHAMBENHID + "&mahosobenhan=" + mahosobenhan + "&maubenhphamid=" +
					rowData.MAUBENHPHAMID + "&type=read";
			var titleUrl = "Xem kết quả nhuộm phiến đồ tế bào theo Papanicolaou";
			var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua", "divNhapKetQua", url, {}, titleUrl, 800, 650);
			EventUtil.setEvent("assignPapanicolaou_cancel", function(e) {
				DlgUtil.close("dlgNhapKetQua");
			});
			dlgPopup.open("dlgNhapKetQua");
		},
		//End_HaNv_111022
		//L2PT-8430 duonghn start : Xem kết quả VS SARS-CoV-2
		viewKQ_SARS_CoV_2 : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			var url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_SARS_COV&idmaubenhpham=" + rowData.MAUBENHPHAMID + "&iddichvukb=" + rowData.DICHVUKHAMBENHID + "&type=read&tiepnhanid=" +
					rowData.TIEPNHANID + "&dichvuid=" + rowData.DICHVUID;
			var titleUrl = "Xem kết quả VS SARS-CoV-2";
			var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua", "divNhapKetQua", url, {}, titleUrl, 1290, 600);
			EventUtil.setEvent("CLS01X002_HUY", function(e) {
				DlgUtil.close("dlgNhapKetQua");
			});
			dlgPopup.open("dlgNhapKetQua");
		},
		//L2PT-8430 duonghn end
		//L2PT-16877 ttlinh start : Xem kết quả RMP Xpert
		viewKQ_RMP_XPERT : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnDetailId).jqGrid('getRowData', rowId);
			dichvuid = rowData.DICHVUTHUCHIENID;
			console.log('dichvuid: ' + dichvuid + ', DICHVUKHAMBENHID: ' + rowData.DICHVUKHAMBENHID);
			var url = "";
			url = "manager.jsp?func=../canlamsang/CLS01X003_KQXN_RMP_XPERT&idmaubenhpham=" + rowData.MAUBENHPHAMID + "&iddichvukb=" + rowData.DICHVUKHAMBENHID + "&type=read" +
					"&showNutSuaNgayTraKQ=false&show=true" + "&tiepnhanid=" + $("#hidTIEPNHANID").val() + "&rolePTH=false&dichvuid=" + dichvuid;
			var titleUrl = "Xem kết quả RMP Xpert";
			EventUtil.setEvent("CLS01X002_LUU", function(e) {
				$("#hdfIDMauBenhPham").val();
			});
			EventUtil.setEvent("CLS01X002_HUY", function(e) {
				DlgUtil.close("dlgNhapKetQua");
			});
			var dlgPopup = DlgUtil.buildPopupUrl("dlgNhapKetQua", "divNhapKetQua", url, {}, titleUrl, 1290, 600);
			dlgPopup.open("dlgNhapKetQua");
		},
		//L2PT-16877 ttlinh end
		//Begin_HaNv_11112019: In barcode ở phiếu chỉ định xét nghiệm - L2PT-10523
		_exportBarcode : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var par = [ {
					name : 'maubenhphamid',
					type : 'String',
					value : rowData.MAUBENHPHAMID
				} ];
				if (cfObj.CLS_INBARCODE == '1') {
					openReport('window', 'RPT_CLS_BARCODE_A6', 'pdf', par);
				} else {
					openReport('window', 'NGT_BARCODE_A6', 'pdf', par);
				}
			}
		},
		//End_HaNv_02072018
		// Destroy an instantiated plugin and clean up  modifications the widget has made to the DOM
		destroy : function() {
			// this.element.removeStuff();
			// For UI 1.8, destroy must be invoked from the
			// base widget
			$.Widget.prototype.destroy.call(this);
			// For UI 1.9, define _destroy instead and don't
			// worry about
			// calling the base widget
		},
		methodB : function(event) {
			//_trigger dispatches callbacks the plugin user
			// can subscribe to
			// signature: _trigger( "callbackName" , [eventObject],
			// [uiObject] )
			// eg. this._trigger( "hover", e /*where e.type ==
			// "mouseenter"*/, { hovered: $(e.target)});
			console.log("methodB called");
		},
		methodA : function(event) {
			this._trigger("dataChanged", event, {
				key : "someValue"
			});
		},
		// Respond to any changes the user makes to the
		// option method
		_setOption : function(key, value) {
			switch (key) {
				case "someValue":
					//this.options.someValue = doSomethingWith( value );
				break;
				default:
					//this.options[ key ] = value;
				break;
			}
			// For UI 1.8, _setOption must be manually invoked
			// from the base widget
			$.Widget.prototype._setOption.apply(this, arguments);
			if (key == '_benhnhanid') {
				this._initWidget();
			}
			// For UI 1.9 the _super method can be used instead
			// this._super( "_setOption", key, value );
		},
		// Xử lý sự kiện liên quan ký CA => START
		_kyCA : function(rowId) {
			isKySo = true;
			lstParamSignHub = [];
			sign_type = '1';
			var _self = this;
			_self._exportPXNCD(rowId);
			if (cfObj.EMR_RPT_SIGNTYPE == '1') {//HaNv_200525: L2PT-133043
				CommonUtil.kyCAs({
					lstReport : lstParamSignHub,
					handlers : {},
					signAction : sign_type,
					returnCode : false
				});
			}
		},
		_huyKyCA : function(rowId) {
			isKySo = true;
			lstParamSignHub = [];
			sign_type = '2';
			var _self = this;
			_self._exportPXNCD(rowId);
			if (cfObj.EMR_RPT_SIGNTYPE == '1') {//HaNv_200525: L2PT-133043
				CommonUtil.kyCAs({
					lstReport : lstParamSignHub,
					handlers : {},
					signAction : sign_type,
					returnCode : false
				});
			}
		},
		_exportKyCA : function(rowId) {
			isKySo = true;
			sign_type = '0';
			var _self = this;
			_self._exportPXNCD(rowId);
		},
		_exportKyKQ : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			var data_ar2 = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", rowData.MAUBENHPHAMID + "$");
			if (data_ar2.length == '0') {
				DlgUtil.showMsg("Phiếu kết quả chưa thực hiện ký số/điện tử!");
				return;
			}
			for (var i = 0; i < data_ar2.length; i++) {
				var paramHashed = data_ar2[i].PARAM_HASHED;
				if (paramHashed != '') {
					CommonUtil.openReportGetCA3(paramHashed, false);
				} else {
					var par_rpt_KySo = [ {
						name : 'HOSOBENHANID',
						type : 'String',
						value : $('#hidHOSOBENHANID').val()
					}, {
						name : 'MAUBENHPHAMID',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'ID_MAUBENHPHAM',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					}, {
						name : 'RPT_CODE',
						type : 'String',
						value : data_ar2[0].REPORTCODE
					} ];
					if (data_ar2[0].REPORTCODE.indexOf("BM_PHIEU_KETQUA_") == '-1') {
						par_rpt_KySo.push({
							name : 'DICHVUKHAMBENHID',
							type : 'String',
							value : ''
						});
					}
					CommonUtil.openReportGetCA2(par_rpt_KySo, false);
				}
			}
		},
		_kyCAKQ : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("CLS05K001.CT2", rowData.MAUBENHPHAMID + "$");
			if (data_ar.length == '0') {
				DlgUtil.showMsg("Phiếu kết quả chưa thực hiện ký số/điện tử!");
				return;
			}
			for (var i = 0; i < data_ar.length; i++) {
				var paramHashed = data_ar[i].PARAM_HASHED;
				if (data_ar[i].KYCHOT != '1') {
					var signUtils = new SignUtils(_rptKyCa);
					signUtils.requestSigningClosed(
						paramHashed,
						(successResponse) => {
							//update trang thai ky chot
							var obj = new Object();
							obj.PARAM_HASHED = paramHashed;
							jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.CA.KYCHOT", JSON.stringify(obj));
						},
						(errorResponse) => {
							DlgUtil.showMsg("Có lỗi xẩy ra!");
							return;
						})
				}
			}
		},
		_printOrPrintCa : function(_rptCode, _par, _typePrint, _rpName, _isPrintDirect, _isCountPrt) {
			var _self = this;
			if (isKySo) {
				isKySo = false;
				_par.push({
					name : 'hosobenhanid',
					type : 'String',
					value : $('#hidHOSOBENHANID').val()
				});
				_par.push({
					name : 'RPT_CODE',
					type : 'String',
					value : _rptCode
				});
				if (sign_type == '0') {
					CommonUtil.openReportGetCA2(_par, false);
				} else {
					if (cfObj.EMR_RPT_SIGNTYPE == '1') {//HaNv_200525: L2PT-133043
						lstParamSignHub.push(_par);
					} else {
						CommonUtil.kyCA(_par, sign_type, true, undefined, undefined, undefined, {
							khambenhid : _self.options._khambenhid
						});
					}
					EventUtil.setEvent("eventKyCA", function(e) {
						DlgUtil.showMsg(e.res);
						_self._initWidget();
					});
				}
			} else {
				if (hopital == 939) {
					openReport('window', "PHIEU_XETNGHIEM", "pdf", _par, true, true);
				} else {
					if (_typePrint == '1') {
						CommonUtil.inPhieu('window', _rptCode, 'pdf', _par, _rpName, _isPrintDirect, _isCountPrt);
					} else {
						openReport('window', _rptCode, "pdf", _par);
					}
				}
			}
		},
		_caRpt : function(signType) {
			var _self = this;
			let
			userCaConf = CaUtils.getCACachingConfig(_rptKyCa);
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptKyCa);
			if (data_ar != null && data_ar.length > 0) {
				var row = data_ar[0];
				var catype = row.CA_TYPE;
				var kieuky = row.KIEUKY;
				if (cfObj.EMR_RPT_SIGNTYPE == '1') {//HaNv_200525: L2PT-133043
					_self._caRpt2(signType, catype, kieuky);
					return;
				}
				if (catype == '3' || catype == '6') {
					var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
					let
					_paramInput = {
						params : null,
						smartca_method : 0
					};
					EventUtil.setEvent("dlgCaLogin_confirm", function() {
						DlgUtil.close("divCALOGIN");
						let
						_hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
						causer = _hisl2SmartCa.token.refresh_token;
						capassword = _hisl2SmartCa.token.access_token;
						smartcauser = _hisl2SmartCa.user.uid;
						_self._caRpt2(signType, catype, kieuky);
					});
					let
					hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
					if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
						_paramInput.smartca_method = 1;
						if (_self.KYSO_TUDONG_KYDIENTU == '1') {
							let
							_hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
							causer = _hisl2SmartCa.token.refresh_token;
							capassword = _hisl2SmartCa.token.access_token;
							smartcauser = _hisl2SmartCa.user.uid;
							_self._caRpt2(signType, catype, kieuky);
						} else {
							let
							_popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
							_popup.open("divCALOGIN");
							return;
						}
					} else {
						EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function(e) {
							if (e.data && e.data.token && e.data.token.access_token) {
								_paramInput.smartca_method = 1;
							}
							DlgUtil.close("dlgCA_SMARTCA_LOGIN");
							let
							_popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
							_popup.open("divCALOGIN");
							return;
						});
						DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {
							isSignPopup : true
						}, "Smart Ca Login", 500, 650);
						DlgUtil.open("dlgCA_SMARTCA_LOGIN");
						return;
					}
				} else if (catype == '5') {
					_self._caRpt2(signType, catype, kieuky);
				} else if (userCaConf && _self.KYSO_TUDONG_KYDIENTU == '1') {
					causer = userCaConf.USER_NAME;
					capassword = userCaConf.PASS_WORD;
					_self._caRpt2(signType, catype, kieuky);
				} else {
					EventUtil.setEvent("dlgCaLogin_confirm", function(e) {
						causer = e.username;
						capassword = e.password;
						DlgUtil.close("divCALOGIN");
						_self._caRpt2(signType, catype, kieuky);
					});
					EventUtil.setEvent("dlgCaLogin_close", function(e) {
						DlgUtil.close("divCALOGIN");
					});
					var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
					var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
					popup.open("divCALOGIN");
				}
			}
		},
		_caRpt2 : function(signType, catype, kieuky) {
			var _self = this;
			msgKyca = '';
			var rowDatas = $('#' + _self.containerId + _self.options._gridXnId).jqGrid("getGridParam", "selarrrow");
			rowDatas.forEach(function(el) {
				_self._caRpt3(el, signType, catype, kieuky);
			});
			if (cfObj.EMR_RPT_SIGNTYPE == '1') {//HaNv_200525: L2PT-133043
				CommonUtil.kyCAs({
					lstReport : lstParamSignHub,
					handlers : {},
					signAction : signType,
					returnCode : false
				});
				EventUtil.setEvent("eventKyCA", function(e) {
					DlgUtil.showMsg(e.res);
				});
			} else {
				DlgUtil.showMsg(msgKyca);
			}
			_self._initWidget();
		},
		_caRpt3 : function(rowId, signType, catype, kieuky) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowId);
			if (signType == '1' && rowData.FLAG_CA == '1') {
				var _msg = rowData.SOPHIEU + ' - Phiếu đã thực hiện Ký điện tử';
				msgKyca = msgKyca == '' ? _msg : msgKyca + '<br/>' + _msg;
				return;
			}
			var par_rpt_KySo = [ {
				name : 'HOSOBENHANID',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			} ];
			par_rpt_KySo.push({
				name : 'RPT_CODE',
				type : 'String',
				value : _rptKyCa
			});
			par_rpt_KySo.push({
				name : 'maubenhphamid',
				type : 'String',
				value : rowData.MAUBENHPHAMID
			});
			if (cfObj.EMR_RPT_SIGNTYPE == '1') {//HaNv_200525: L2PT-133043
				lstParamSignHub.push(par_rpt_KySo);
				return;
			}
			var oData = {
				sign_type : signType,
				causer : causer,
				capassword : capassword,
				smartcauser : smartcauser,
				params : par_rpt_KySo
			};
			if (catype == '5') {
				CommonUtil.kyCA(par_rpt_KySo, signType, true);
				EventUtil.setEvent("eventKyCA", function(e) {
					DlgUtil.showMsg(e.res);
				});
			} else {
				var msg = CommonUtil.caRpt(oData, _rptKyCa, true, '', true, kieuky, catype);
				var _code = msg.split("|")[0];
				var _msg = rowData.SOPHIEU + ' - ' + msg.split("|")[1];
				var _caid = msg.split("|")[2];
				msgKyca = msgKyca == '' ? _msg : msgKyca + '<br/>' + _msg;
				//update ky chot
				if (catype == '3') {
					if (_code == '0' || _code == '7' || _code == '8') {
						var intervalId = null;
						var smartCaLoaderFunction = function() {
							console.log("smartCaLoaderFunction is running!");
							var _sql_par = [];
							_sql_par.push({
								"name" : "[0]",
								value : _caid
							});
							var fl = jsonrpc.AjaxJson.getOneValue("SMARTCA.GET.STATUS", _sql_par);
							if (fl == 1) {
								// bat phieu in
								CommonUtil.openReportGetCA2(_par, false);
								clearInterval(intervalId);
							}
						};
						intervalId = setInterval(smartCaLoaderFunction, 4000);
					}
				}
			}
		},
		_printSelectedCASigned : function() {
			var _self = this;
			var rowDatas = $('#' + _self.containerId + _self.options._gridXnId).jqGrid("getGridParam", "selarrrow");
			if (rowDatas != null) {
				for (var j = 0; j < rowDatas.length; j++) {
					var rowData = $('#' + _self.containerId + _self.options._gridXnId).jqGrid('getRowData', rowDatas[j]);
					if (rowData.FLAG_CA == '1' || rowData.FLAG_CA == '99') {
						var _par = [ {
							name : 'HOSOBENHANID',
							type : 'String',
							value : $("#hidHOSOBENHANID").val()
						}, {
							name : 'maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						}, {
							name : 'RPT_CODE',
							type : 'String',
							value : _rptKyCa
						} ];
						CommonUtil.openReportGetCA2(_par, false);
					}
				}
			}
		}
	// Xử lý sự kiện liên quan ký CA => END
	});
	//Beg_HaNv_290324: setSelection onContextMenu(chuột trái) với grid checkbox không bỏ chọn với dòng đã được chọn - L2PT-74241
	function _setSelectionOnContextMenu(_gridId, _rowId) {
		var grid = $('#' + _gridId);
		var rowDatas = grid.jqGrid("getGridParam", "selarrrow");
		var check = true;
		if (rowDatas.length > 0) {
			for (var j = 0; j < rowDatas.length; j++) {
				if (rowDatas[j] == _rowId) {
					check = false;
				}
			}
			if (check) {
				grid.setSelection(_rowId);
			}
		} else {
			grid.setSelection(_rowId);
		}
	}
	//End_HaN_290324
})(jQuery);

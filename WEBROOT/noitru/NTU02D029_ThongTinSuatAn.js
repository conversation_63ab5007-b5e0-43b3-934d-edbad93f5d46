(function($) {
	var hospital_id = -1;
	var cf = new Object();
	var showAllDVKT = false;
	var formCall = '';
	$.widget("ui.ntu02d029_pdv", {
		//Options to be used as defaults
		options : {
			_grdSuatAn : 'grdSuatAn',
			_grdSuatAnChitiet : 'grdSuatAnChitiet',
			_khambenhid : "",
			_benhnhanid : "",
			_lnmbp : "",
			_loaidichvu : "",
			_modeView : "0", // =1 chi view; !=1 la update
			_hosobenhanid : "",
			_formCall : "" // <PERSON>ác định mã màn hình cha gọi đến widget để tùy biến
		},
		containerId : '',
		_create : function() {
			this.containerId = $(this.element).attr('id');
			this._initWidget();
		},
		_t : function(_id) {
			var newId = '';
			if (_id.indexOf("=") > 0) {
				newId = _id.replace(/\b((txt|cbo|chk|hid|lbl|rad)[a-z,A-Z,0-9,_]+=)\b/gi, _self.containerId + "$1");
			} else if (_id.indexOf("#") == 0) {
				newId = _id.replace(/(#)([a-z,A-Z,0-9,_]+)/gi, "$1" + _self.containerId + "$2");
			} else {
				newId = _self.containerId + _id;
			}
			return newId;
		},
		_initWidget : function() {
			var _self = this;
			var chkAllKhoa = false;
			if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
				chkAllKhoa = true;
			} else {
				chkAllKhoa = false;
			}
			$(_self.element).load('../noitru/NTU02D029_ThongTinSuatAn.tpl?v=2023', function() {
				$(_self.element).find("[id]").each(function() {
					if (this.id == "pager_" + _self.options._grdSuatAn) {
						this.id = "pager_" + _self.containerId + _self.options._grdSuatAn;
					} else if (this.id == "pager_" + _self.options._grdSuatAnChitiet) {
						this.id = "pager_" + _self.containerId + _self.options._grdSuatAnChitiet;
					} else {
						this.id = _self.containerId + this.id;
					}
				})
				if (chkAllKhoa) {
					$('#' + _self.containerId + 'chkAllKhoa').prop("checked", true);
				} else {
					$('#' + _self.containerId + 'chkAllKhoa').prop("checked", false);
				}
				_self._loadData();
				_self._bindEvent();
				height_window = $(window).height(); // returns height of browser viewport
				height_suatan = $('#' + _self.element.attr('id')).height();
				height_divMain = $('#hidDocumentHeight').val();
				if (height_suatan + 110 < height_window) {
					$('#divMain').css('height', height_suatan + 113);
				} else if (height_window < height_suatan + 110) {
					$("#divMain").css('height', height_suatan + 110);
				} else if (height_suatan + 110 == height_window) {
					$('#divMain').css('height', height_suatan + 110);
				}
			});
		},
		_loadData : function() {
			var _self = this;
			var _look_sql = "NT.024.DSPHIEU";
			hospital_id = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');
			_self.hospital_id = hospital_id;
			_self.lstdvkbid = [];
			_self.lstmadichvu = [];
			_self.lstdvkbid_final = [];
			//khoi tao grid danh sach xet nghiem
			var sa_bnd = '';
			var sa_qti = '';
			var data_ar_CH = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "HIS_NTU_SUATAN_BND;NTU_SUATAN_QTI;HIS_CDDV_DTK_QTI;WGNG_UPDATE_NGUOITAO;"
					+ "HIS_SUDUNG_KYSO_KYDIENTU;NTU_SUATAN_NGUOICAPNHAT;WGNG_GRID_SET;HIS_SHOWALL_DVKT;NTU_RPTCODE_PHIEU_VC"); // L2PT-126503
			if (data_ar_CH != null && data_ar_CH.length > 0) {
				cf = data_ar_CH[0];
				sa_bnd = data_ar_CH[0].HIS_NTU_SUATAN_BND;
				_self.sa_qti = data_ar_CH[0].NTU_SUATAN_QTI;
				_self.HIS_CDDV_DTK_QTI = data_ar_CH[0].HIS_CDDV_DTK_QTI;
				_self.NTU_SUATAN_NGUOICAPNHAT = data_ar_CH[0].NTU_SUATAN_NGUOICAPNHAT;
			}
			//Beg_HaNv_211223: load all khoa - L2PT-63860
			formCall = _self.options._formCall;
			if (cf.HIS_SHOWALL_DVKT == '1' && _self.options._lnmbp == '12' && formCall != '' && "NTU_PHC,NTU_BDT,".includes(formCall + ',')) {
				showAllDVKT = true;
				$('#' + _self.containerId + 'divAllKhoa').show();
			}
			//End_HaNv_211223
			//L2PT-26626
			if (_self.options._loaidichvu == 12 && sa_bnd == '1') {
				//L2PT-6139
				if (_self.sa_qti == '1') {
					var _gridHeader = " ,ICON,35,0,ns,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
							+ "HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;MABENHNHAN,MABENHNHAN,0,0,t,l;"
							+ "MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;"
							+ "Số phiếu,SOPHIEU,100,0,f,l;Người tạo,NGUOITAO,220,0,f,l;Phòng,PHONGDIEUTRI,350,0,f,l;"
							+ "Ngày chỉ định,NGAYMAUBENHPHAM,160,0,f,l;Trạng thái phiếu,TRANGTHAI_PHIEU,100,0,f,l;"
							+ "NGUOITAO_ID,NGUOITAO_ID,0,0,t,l;trangthaimaubenhpham,TRANGTHAIMAUBENHPHAM,0,0,t,l";
				} else {
					_look_sql = "NT.024.DSPHIEU_SA";
					var _gridHeader = " ,ICON,35,0,ns,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
							+ "HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;MABENHNHAN,MABENHNHAN,0,0,t,l;"
							+ "MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;"
							+ "Số phiếu,SOPHIEU,80,0,f,l;Người tạo,NGUOITAO,200,0,f,l;Phòng,PHONGDIEUTRI,200,0,f,l;"
							+ "Ngày tạo,NGAYMAUBENHPHAM,160,0,f,l;Ngày SD,NGAYMAUBENHPHAM_SUDUNG,150,0,f,l;Trạng thái phiếu ăn,TRANGTHAI_SUATAN,100,0,f,l;"
							+ "Trạng thái phiếu THSA,TRANGTHAI_TEXT,120,0,f,l;Phiếu THSA,MAPHIEU,100,0,f,l;TRANGTHAI_PTH_SA,TRANGTHAI_PTH_SA,100,0,t,l;"
							+ "Loại dịch vụ,LOAIDICHVU,200,0,f,l;NGUOITAO_ID,NGUOITAO_ID,0,0,t,l;trangthaimaubenhpham,TRANGTHAIMAUBENHPHAM,0,0,t,l";
				}
			} else if (_self.HIS_CDDV_DTK_QTI == '1' && _self.options._lnmbp == '30' && _self.options._loaidichvu == '20') {
				var _gridHeader = " ,ICON,35,0,ns,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
						+ "HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;MABENHNHAN,MABENHNHAN,0,0,t,l;"
						+ "MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;"
						+ "Số phiếu,SOPHIEU,160,0,f,l;Người tạo,NGUOITAO,220,0,f,l;Phòng,PHONGDIEUTRI,250,0,f,l;Khoa TH,KHOATHUCHIEN,250,0,f,l;"
						+ "TG bắt đầu,NGAYMAUBENHPHAM,160,0,f,l;TG kết thúc,NGAYMAUBENHPHAM_HOANTHANH,160,0,f,l;"
						+ "Loại dịch vụ,LOAIDICHVU,200,0,f,l;NGUOITAO_ID,NGUOITAO_ID,0,0,t,l;trangthaimaubenhpham,TRANGTHAIMAUBENHPHAM,0,0,t,l";
			} else {
				var _gridHeader = " ,ICON2,35,0,ns,l;FLAG_CA,FLAG_CA,0,0,t,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
						+ "HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;MABENHNHAN,MABENHNHAN,0,0,t,l;"
						+ "MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;"
						+ "Số phiếu,SOPHIEU,160,0,f,l;Người tạo,NGUOITAO,220,0,f,l;Phòng,PHONGDIEUTRI,250,0,f,l;"
						+ "TG bắt đầu,NGAYMAUBENHPHAM,160,0,f,l;TG kết thúc,NGAYMAUBENHPHAM_HOANTHANH,160,0,f,l;"
						+ "Loại dịch vụ,LOAIDICHVU,200,0,f,l;NGUOITAO_ID,NGUOITAO_ID,0,0,t,l;trangthaimaubenhpham,TRANGTHAIMAUBENHPHAM,0,0,t,l;"
						+ "PHONGID,PHONGID,0,0,t,l;PHONGKHAMDANGKYID,PHONGKHAMDANGKYID,0,0,t,l;KHOADIEUTRI1,KHOADIEUTRI1,0,0,t,l";
			}
			//HungNd - L2PT-66420: Hien thi thong tin nguoi tao
			if (_self.NTU_SUATAN_NGUOICAPNHAT == '1') {
				var _gridHeader = " ,ICON2,35,0,ns,l;FLAG_CA,FLAG_CA,0,0,t,l;LOAITIEPNHANID,LOAITIEPNHANID,0,0,t,l;DOITUONGBENHNHANID,DOITUONGBENHNHANID,0,0,t,l;"
						+ "HOSOBENHANID,HOSOBENHANID,0,0,t,l;TIEPNHANID,TIEPNHANID,0,0,t,l;MABENHNHAN,MABENHNHAN,0,0,t,l;"
						+ "MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;KHAMBENHID,KHAMBENHID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;"
						+ "Số phiếu,SOPHIEU,160,0,f,l;Người tạo,NGUOITAO,160,0,f,l;Người cập nhật,NGUOICAPNHAT,160,0,f,l;Phòng,PHONGDIEUTRI,250,0,f,l;" + "Ngày tạo,NGAYMAUBENHPHAM,160,0,f,l;"
						+ "Loại dịch vụ,LOAIDICHVU,200,0,f,l;NGUOITAO_ID,NGUOITAO_ID,0,0,t,l;trangthaimaubenhpham,TRANGTHAIMAUBENHPHAM,0,0,t,l;"
						+ "PHONGID,PHONGID,0,0,t,l;PHONGKHAMDANGKYID,PHONGKHAMDANGKYID,0,0,t,l";
			}
			//End HungNd
			//Beg_HaNv_190823: Hiển thị thông tin giường - L2PT-50099
			if (_self.options._lnmbp == '12') {
				if (cf.WGNG_GRID_SET == '1') {
					_gridHeader = _gridHeader.replace('SOPHIEU,160,0,f,l;', 'SOPHIEU,100,0,f,l;Mã giường,MAGIUONG,100,0,f,l;Số lượng,SOLUONG,100,0,f,l;Tỷ lệ DV,TYLENGAYGIUONG,60,0,f,l;');
				} else if (cf.WGNG_GRID_SET == '2') {//HaNv_220624: L2PT-88326
					_gridHeader = _gridHeader.replace('Người tạo,NGUOITAO,220,0,f,l;', 'Bác sĩ,NGUOITAO,120,0,f,l;Người tạo,NGUOITAO_CLS,150,0,f,l;');
				}
			}
			//End_HaNv_190823
			//Beg_HaNv_211223: load all khoa - L2PT-63860
			var _group_khoa = {
				groupField : [ 'KHOADIEUTRI1' ],
				groupColumnShow : [ false ],
				groupText : [ '<b>{0}</b>' ]
			};
			var _sql_par = [];
			if (showAllDVKT) {
				GridUtil.initGroup(_self.containerId + _self.options._grdSuatAn, "100%", "165px", 'Danh sách phiếu dịch vụ', true, _group_khoa, _gridHeader, true, {
					rowNum : 50,
					rowList : [ 50, 100, 150 ]
				});
				if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
					_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, $('#hidHOSOBENHANID').val() ]);
				} else {
					_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, -1 ]);
				}
			} else {
				_sql_par = RSUtil.buildParam("", [ _self.options._khambenhid, _self.options._benhnhanid, _self.options._lnmbp, _self.options._hosobenhanid ]);
				GridUtil.init(_self.containerId + _self.options._grdSuatAn, "100%", "165px", 'Danh sách phiếu dịch vụ', true, _gridHeader, false, {
					rowNum : 50,
					rowList : [ 50, 100, 150 ]
				});
			}
			//End_HaNv_211223
			GridUtil.loadGridBySqlPage(_self.containerId + this.options._grdSuatAn, _look_sql, _sql_par);
			//ductx-5600
			var ctl_nt_ar = [ {
				type : 'button',
				id : 'btnDsTvtDk',
				icon : 'xutri',
				text : 'Thuốc, vật tư đi kèm',
				cssClass : 'wd150'
			} ];
			if (hospital_id == '10284' && _self.options._lnmbp == '12' && _self.options._loaidichvu == '13') {
				toolbar = ToolbarUtil.build(_self.containerId + 'toolbarId', ctl_nt_ar);
			}
			//end bvtm-5600
			if (_self.options._loaidichvu != 13) {
				$("#" + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('hideCol', 'MAGIUONG');
			}
			if (_self.options._loaidichvu == 12 && sa_bnd == '1') {
				//L2PT-6139
				if (_self.sa_qti == '1') {
					var _gridDSCountHeader = "dichvuid,dichvuid,100,0,t,l;Mã dịch vụ,MADICHVU,150,0,f,l;" + "Tên dịch vụ,TENDICHVU,300,0,f,l;Số lượng,SOLUONG,100,0,f,l;Đơn vị tính,TEN_DVT,100,0,f,l;"
							+ "Ghi chú,GHICHU,250,0,f,l";
				} else {
					var _gridDSCountHeader = "dichvuid,dichvuid,100,0,t,l;Mã dịch vụ,MADICHVU,150,0,f,l;"
							+ "Tên dịch vụ,TENDICHVU,300,0,f,l;Số lượng,SOLUONG,100,0,f,l;Đơn vị tính,TEN_DVT,100,0,f,l;Trạng thái,trangthai_text,200,0,f,l;"
							+ ",gio_suatan,200,0,t,l;,trangthai,200,0,t,l;,ngaymaubenhpham_sudung,200,0,t,l;YC_HOAN,YC_HOAN,0,0,t,l;Phiếu THSA,MAPHIEU,150,0,f,l;Ghi chú,GHICHU,250,0,f,l";
				}
			}
			// L2PT-11224 duonghn start
			else if (_self.options._loaidichvu == 13) {
				var _gridDSCountHeader = "DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;dichvuid,dichvuid,100,0,t,l;Mã dịch vụ,MADICHVU,150,0,f,l;DOITUONG,LOAIDOITUONG,0,0,t,l;"
						+ "Tên dịch vụ,TENDICHVU,400,0,f,l;Số lượng,soluong,100,0,f,l;Tỷ lệ DV,TYLENGAYGIUONG,60,0,f,l;Mã giường,MAGIUONG,128,0,f,l;"
						+ "Đơn giá,DONGIA,128,0,f,l;Đơn vị tính,TEN_DVT,200,0,f,l;YC_HOAN,YC_HOAN,0,0,t,l;Thuốc đi kèm,THUOC_DIKEM,120,0,f,l;" + "Vật tư đi kèm,VATTU_DIKEM,120,0,f,l";
			}
			// L2PT-11224 duonghn end
			else {
				var _gridDSCountHeader = "DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;dichvuid,dichvuid,100,0,t,l;Mã dịch vụ,MADICHVU,150,0,f,l;Mã giường,MAGIUONG,100,0,t,l;"
						+ "Tên dịch vụ,TENDICHVU,450,0,f,l;Số lượng,soluong,250,0,f,l;Đơn vị tính,TEN_DVT,200,0,f,l;YC_HOAN,YC_HOAN,0,0,t,l;"
						+ "Thuốc đi kèm,THUOC_DIKEM,120,0,f,l;Vật tư đi kèm,VATTU_DIKEM,120,0,f,l";
			}
			GridUtil.init(_self.containerId + _self.options._grdSuatAnChitiet, "100%", "165px", 'Danh sách chi tiết dịch vụ', false, _gridDSCountHeader, false, {
				rowNum : 50,
				rowList : [ 50, 100, 150 ]
			});
			//Beg_HaNv_300521: BVTM-3035
			//hospital_id = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');
			if (_self.options._loaidichvu != 13 || _self.hospital_id != '10284') {
				$('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('hideCol', 'THUOC_DIKEM');
				$('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('hideCol', 'VATTU_DIKEM');
				$('#' + _self.containerId + 'divNote').remove();
			}
			//End_HaNv_300521
			if (_self.options._loaidichvu == 13) {//HaNv_301121: L2PT-11225
				$('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('showCol', 'MAGIUONG');
			}
			//L2PT-20629
			// Xử lý sự kiện liên quan ký CA => START
			if (cf.HIS_SUDUNG_KYSO_KYDIENTU != "1") {
				$('#' + _self.containerId + 'GROUP2').remove();
				$('#' + _self.containerId + 'kyCAPTK').remove();
				$('#' + _self.containerId + 'printKyCAPTK').remove();
				$('#' + _self.containerId + 'huyKyCAPTK').remove();
				$("#" + _self.containerId + _self.options._grdSuatAn).hideCol('ICON2');
			}
			// Xử lý sự kiện liên quan ký CA => END
		},
		_bindEvent : function() {
			var _self = this;
			//Beg_HaNv_211223: load all khoa - L2PT-63860
			var modeView;
			if (showAllDVKT) {
				if ($('#' + _self.containerId + 'chkAllKhoa').is(':checked')) {
					modeView = "1";
				} else {
					modeView = _self.options._modeView;
				}
			} else {
				modeView = _self.options._modeView;
			}
			$('#' + _self.containerId + 'chkAllKhoa').unbind("click").bind("click", function() {
				_self._initWidget();
			});
			//End_HaNv_211223
			//ductx -bvtm -5600
			if (hospital_id == '10284' && _self.options._lnmbp == '12' && _self.options._loaidichvu == '13') {
				_self._bindEventToolbar();
			}
			//end bvtm -5600
			GridUtil.setGridParam(_self.containerId + this.options._grdSuatAn, {
				onSelectRow : function(id, selected) {
					_self._viewPhieuVatTuDetail(id);
					GridUtil.unmarkAll(_self.containerId + _self.options._grdSuatAn);
					GridUtil.markRow(_self.containerId + _self.options._grdSuatAn, id);
					var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', id);
					$("#hidMAUBENHPHAMID").val(rowData.MAUBENHPHAMID);
					//BVTM-5605
					_self.ngaygiuong_check = selected;
				},
				ondblClickRow : function(id) {
					if (modeView == "0") {
						_self._updatePhieuSuatAn(id)
					}
				},
				onSelectAll : function(id, selected) {
					var rowIds = $("#" + _self.containerId + _self.options._grdSuatAn).jqGrid('getDataIDs');
					if (_self.hospital_id == '10284' && _self.options._lnmbp == "12") {
						if (selected) {
							for (var i = 1; i <= rowIds.length; i++) {
								var rowData = $("#" + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', i);
								var sql_par = [];
								sql_par.push({
									"name" : "[0]",
									"value" : rowData.MAUBENHPHAMID
								});
								var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D029.DV", sql_par);
								var data_ar = JSON.parse(data);
								if (data_ar != null && data_ar.length > 0) {
									for (var j = 0; j < data_ar.length; j++) {
										if (_self.lstdvkbid.indexOf(data_ar[j]["DICHVUKHAMBENHID"]) == -1) { //L2PT-28343
											_self.lstdvkbid.push(data_ar[j]["DICHVUKHAMBENHID"]);
											var obj = new Object();
											obj.DVKBID = data_ar[j]["DICHVUKHAMBENHID"];
											obj.MADV = data_ar[j]["MADICHVU"];
											obj.THUOC_DIKEM = data_ar[j]["THUOC_DIKEM"];
											obj.VATTU_DIKEM = data_ar[j]["VATTU_DIKEM"];
											_self.lstmadichvu.push(obj);
										}
										//_self.lstdvkbid.push(data_ar[j]["DICHVUKHAMBENHID"]);
									}
								}
							}
						} else {
							_self.lstdvkbid = [];
							_self.lstmadichvu = [];
						}
					}
				},
				gridComplete : function(id) {
					if (modeView == "0") {
						var menuName = '';
						//bu ild menu
						if (_self.options._loaidichvu == "12" && _self.options._lnmbp == "11") {
							//var _hospital_id=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');//L1PT-1470
							if (_self.sa_qti == '1') {
								menuName = 'contextMenuQTI'
							} else {
								menuName = 'contextMenuSUATAN';
							}
						} else if (_self.options._loaidichvu == "1" && _self.options._lnmbp == "17") {
							menuName = 'contextMenu';
						} else if (_self.options._lnmbp == "17") {
							menuName = 'contextMenuDOVAI';
						} else {
							if (_self.hospital_id == '10284' && _self.options._lnmbp == "12") {
								menuName = 'contextMenuBDHN';
							} else {
								menuName = 'contextMenu';
							}
						}
						//L2PT-26626
						var ids = $("#" + _self.containerId + _self.options._grdSuatAn).getDataIDs();
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var row = $("#" + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', id);
							//set icon trang thai  benh nhan
							var _icon = '';
							// đã ký số
							if (row.FLAG_CA) {
								if (row.FLAG_CA == '1') {
									_icon = '<center><img src="../common/image/ca.png" width="15px"></center>';
								} else if (row.FLAG_CA == '99') {
									_icon = '<center><img src="../common/image/ca-3.png" width="15px"></center>';
								}
							}
							$("#" + _self.containerId + _self.options._grdSuatAn).jqGrid('setCell', id, 'ICON2', _icon);
							//L2PT-6139
							if (_self.sa_qti == '1') {
								var _trangthai = parseInt(row.TRANGTHAIMAUBENHPHAM);
								if (_trangthai == 2) {//Da gui
									_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
								} else if (_trangthai == 3) {//Da duyet
									_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
								}
							} else {
								var _trangthai = parseInt(row.TRANGTHAI_PTH_SA);
								if (_trangthai == 0) {//Chua duyet
									_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
								} else if (_trangthai == 1) {//Da duyet
									_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
								} else if (_trangthai == 2) {//Khong duyet
									_icon = '<center><img src="../common/image/Cancel.png" width="15px"></center>';
								} else if (_trangthai == 3) {//nha an dang xu ly
									_icon = '<center><img src="../common/image/process.png" width="15px"></center>';
								} else if (_trangthai == 4) {//da phat
									_icon = '<center><img src="../common/image/Flag_Green2.png" width="15px"></center>';
								} else if (_trangthai == 5) {//da tinh tien
									_icon = '<center><img src="../common/image/dollar_sa.png" width="15px"></center>';
								}
							}
							//
							if (_self.HIS_CDDV_DTK_QTI == '1' && _self.options._lnmbp == '30' && _self.options._loaidichvu == '20') {
								var _trangthai = parseInt(row.TRANGTHAIMAUBENHPHAM);
								if (_trangthai == 3) {//da duyet
									_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
								} else if (_trangthai == 2) {//Da gui
									_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
								}
							}
							$("#" + _self.containerId + _self.options._grdSuatAn).jqGrid('setCell', id, 'ICON', _icon);
						}
						$(".jqgrow", '#' + _self.containerId + _self.options._grdSuatAn).contextMenu(_self.containerId + menuName, {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id")
								var grid = $('#' + _self.containerId + _self.options._grdSuatAn);
								//grid.setSelection(rowId);
								var rowDatas = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid("getGridParam", "selarrrow");
								var check = true;
								if (rowDatas.length > 0) {
									for (var j = 0; j < rowDatas.length; j++) {
										if (rowDatas[j] == rowId) {
											check = false;
										}
									}
									if (check) {
										grid.setSelection(rowId);
									}
								} else {
									grid.setSelection(rowId);
								}
								return true;
							},
						});
						if (_self.HIS_CDDV_DTK_QTI == '0') {
							$('#' + _self.containerId + 'sentRequest').remove();
							$('#' + _self.containerId + 'deleteRequest').remove();
						}
						//HaNv_130523: L2PT-40141
						if (cf.WGNG_UPDATE_NGUOITAO == '0' || _self.options._loaidichvu != 13) {
							$('#' + _self.containerId + 'updateNguoiTao').remove();
						}
						//nghiant 08092017
						if (_self.options._loaidichvu == "2" && _self.options._lnmbp == "3") {
							//START L2PT-6158
							var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'PRINT_PTK_DISABLE');
							if (data_ar != null && data_ar.length > 0) {
								if (data_ar[0].PRINT_PTK_DISABLE == '1') {
									$('#' + _self.containerId + 'printPTK').addClass("disabled");
								}
							}
							//END L2PT-6158
						}
						//End nghiant 
						//START L2PT-1357
						if (_self.options._modeXoaphieu != null && _self.options._modeXoaphieu == "1") {
							$('#' + _self.containerId + 'delete').addClass("disabled");
							$('#' + _self.containerId + 'deleteSelect').addClass("disabled");
							$('#' + _self.containerId + 'updatePSA').addClass("disabled");
						}
						//START L2PT-1357
						//CẬP NHẬT PHIẾU XUẤT ĂN
						$('#' + _self.containerId + 'updatePSA').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._updatePhieuSuatAn(rowKey);
						}, 500)
						$('#' + _self.containerId + 'updateDVSA').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._updatePhieuSuatAn(rowKey);
						}, 500)
						//Hủy Dich vụ  SUẤT ĂN
						$('#' + _self.containerId + 'HuySuatAn').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
							_self.HuySuatAn(rowKey);
						}, 500)
						//xu ly su kien xoa
						$('#' + _self.containerId + 'delete').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._deletePhieuSuatAn(rowKey);
						}, 500)
						//START -- HISL2TK-567 --hong 28052018
						$('#' + _self.containerId + 'deleteDVSA').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._deletePhieuSuatAn(rowKey);
						}, 500)
						//xu ly su kien xoa chon
						$('#' + _self.containerId + 'deleteSelect').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._deleteSelectPhieuSuatAn(rowKey);
						}, 500)
						//END -- HISL2TK-567 --hong 28052018
						$('#' + _self.containerId + 'deleteSelectDVSA').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._deleteSelectPhieuSuatAn(rowKey);
						}, 500)
						//nghiant 18082017
						//xu ly in phieu dieu tri printPTK
						$('#' + _self.containerId + 'printPTK').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._exportPTKHAC(rowKey);
						}, 500)
						//end nghiant 18082017 
						//START L2PT-15424 huy phieu tra do
						$('#' + _self.containerId + 'deleteDOVAI').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._deletePhieuDOVAI(rowKey);
						}, 500)
						//START L2PT-15424
						//L2PT-6139
						$('#' + _self.containerId + 'updateQTI').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._updatePhieuSuatAn(rowKey);
						}, 500)
						$('#' + _self.containerId + 'sendQTI').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._sendRequest(rowKey);
						}, 500)
						$('#' + _self.containerId + 'cancelQTI').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._deleteRequest(rowKey);
						}, 500)
						$('#' + _self.containerId + 'deleteQTI').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._deletePhieuSuatAn(rowKey);
						}, 500)
						$('#' + _self.containerId + 'printQTI').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowKey);
							var par = [ {
								name : 'khambenhid',
								type : 'String',
								value : rowData.KHAMBENHID
							}, {
								name : 'maubenhphamid',
								type : 'String',
								value : rowData.MAUBENHPHAMID
							} ];
							openReport('window', "DSPHIEU_SUATAN_QTI", "pdf", par);
						}, 500)
						//END L2PT-6139
						//BVTM-5605
						$('#' + _self.containerId + 'updatePSA_BDHN').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._updatePhieuSuatAn(rowKey);
						}, 500);
						//xu ly su kien xoa
						$('#' + _self.containerId + 'delete_BDHN').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._deletePhieuSuatAn(rowKey);
						}, 500);
						//xu ly su kien xoa chon
						$('#' + _self.containerId + 'deleteSelect_BDHN').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._deleteSelectPhieuSuatAn(rowKey);
						}, 500);
						//xu ly in phieu dieu tri printPTK
						$('#' + _self.containerId + 'printPTK_BDHN').bindOnce("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._exportPTKHAC(rowKey);
						}, 500);
						//DSPTVT_KEM
						$('#' + _self.containerId + 'DSPTVT_KEM_BDHN').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn dịch phiếu');
								return;
							}
							_self._dsPhieuThuocVatTuDiKem(rowKey, '1');
						});
						//Tao ban TAOPHIEUTHUOCKEM
						$('#' + _self.containerId + 'TAOPHIEUTHUOCKEM_BDHN').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn phiếu');
								return;
							}
							_self._taoPhieuThuocDiKem(rowKey, '1');
						});
						//Tao ban TAOPHIEUVATTUKEM 
						$('#' + _self.containerId + 'TAOPHIEUVATTUKEM_BDHN').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn phiếu');
								return;
							}
							_self._taoPhieuVatTuDiKem(rowKey, '1');
						});
						//Tao ban TAOPHIEUTHUOCKEM hao phi
						$('#' + _self.containerId + 'TAOPHIEUTHUOCKEM_HAOPHI_BDHN').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn phiếu');
								return;
							}
							_self._taoPhieuThuocDiKem_haophi(rowKey, '1');
						});
						//Tao ban TAOPHIEUVATTUKEM hao phi
						$('#' + _self.containerId + 'TAOPHIEUVATTUKEM_HAOPHI_BDHN').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn phiếu');
								return;
							}
							_self._taoPhieuVatTuDiKem_haophi(rowKey, '1');
						});
						//BVTM-5605
						//xu ly su kien gui yeu cau
						$('#' + _self.containerId + 'sentRequest').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn phiếu');
								return;
							}
							_self._sendRequest(rowKey);
						});
						//xu ly su kien gui yeu cau
						$('#' + _self.containerId + 'deleteRequest').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn phiếu');
								return;
							}
							_self._deleteRequest(rowKey);
						});
						//HaNv_130523: L2PT-40141
						$('#' + _self.containerId + 'updateNguoiTao').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn phiếu');
								return;
							}
							var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowKey);
							var paramInput = {
								maubenhphamid : rowData.MAUBENHPHAMID
							};
							dlgPopup = DlgUtil.buildPopupUrl("divDlgBacSyChiDinh", "divDlg", "manager.jsp?func=../noitru/NTU01H026_DoiBacSy", paramInput, "Đổi bác sĩ chỉ định", 500, 200);
							DlgUtil.open("divDlgBacSyChiDinh");
							EventUtil.setEvent("assignSevice_saveChangeBS", function(e) {
								if (typeof (e) != 'undefined') {
									DlgUtil.showMsg(e.msg);
								}
								DlgUtil.close(e.divId);
								_self._initWidget();
							});
						});
						// Xử lý sự kiện liên quan ký CA => START
						$('#' + _self.containerId + 'kyCAPTK').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._kyCA(rowKey);
						});
						$('#' + _self.containerId + 'huyKyCAPTK').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._huyKyCA(rowKey);
						});
						$('#' + _self.containerId + 'printKyCAPTK').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
							_self._exportKyCA(rowKey);
						});
						// Xử lý sự kiện liên quan ký CA => END
					}//START L2PT-16670
					else {
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'HIS_NTN_DVK_KTBA_IN');
						if (data_ar != null && data_ar.length > 0) {
							if (data_ar[0].HIS_NTN_DVK_KTBA_IN == '1') {
								$(".jqgrow", '#' + _self.containerId + _self.options._grdSuatAn).contextMenu(_self.containerId + 'contextMenuKTBA_In', {
									onContextMenu : function(event, menu) {
										var rowId = $(event.target).parent("tr").attr("id")
										var grid = $('#' + _self.containerId + _self.options._grdSuatAn);
										//grid.setSelection(rowId);
										var rowDatas = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid("getGridParam", "selarrrow");
										var check = true;
										if (rowDatas.length > 0) {
											for (var j = 0; j < rowDatas.length; j++) {
												if (rowDatas[j] == rowId) {
													check = false;
												}
											}
											if (check) {
												grid.setSelection(rowId);
											}
										} else {
											grid.setSelection(rowId);
										}
										return true;
									},
								});
								$('#' + _self.containerId + 'printPTK_KTBA').bindOnce("click", function() {
									var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
									_self._exportPTKHAC(rowKey);
								}, 500)
							} else {
								//Dannd_050623: L2PT-44760
								$(".jqgrow", '#' + _self.containerId + _self.options._grdSuatAn).contextMenu(_self.containerId + 'contextMenuCONGKHAM', {
									onContextMenu : function(event, menu) {
										var rowId = $(event.target).parent("tr").attr("id")
										var grid = $('#' + _self.containerId + _self.options._grdSuatAn);
										//grid.setSelection(rowId);
										var rowDatas = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid("getGridParam", "selarrrow");
										var check = true;
										if (rowDatas.length > 0) {
											for (var j = 0; j < rowDatas.length; j++) {
												if (rowDatas[j] == rowId) {
													check = false;
												}
											}
											if (check) {
												grid.setSelection(rowId);
											}
										} else {
											grid.setSelection(rowId);
										}
										return true;
									},
								});
								$('#' + _self.containerId + 'printCONGKHAM').unbind("click").bind("click", function() {
									var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
									var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowKey);
									var par = [ {
										name : 'i_khambenhid',
										type : 'String',
										value : rowData.KHAMBENHID
									}, {
										name : 'i_phongids',
										type : 'String',
										value : rowData.PHONGID
									}, {
										name : 'i_pkdkids',
										type : 'String',
										value : rowData.PHONGKHAMDANGKYID
									} ];
									// L2PT-126503 start
									var rpt_code_sa = "RPT_PHIEUKHAMCHUYENKHOA_A4";
									if(cf.NTU_RPTCODE_PHIEU_VC && cf.NTU_RPTCODE_PHIEU_VC != '0' && _self.options._lnmbp == '16') {
										rpt_code_sa = cf.NTU_RPTCODE_PHIEU_VC;
									}
									openReport('window', rpt_code_sa, "pdf", par);
									// L2PT-126503 end
								});
								$('#' + _self.containerId + 'editNGAYYL').unbind("click").bind("click", function() {
									var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
									var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowKey);
									var paramInput = {
										MAUBENHPHAMID : rowData.MAUBENHPHAMID,
										NGAYMAUBENHPHAM : rowData.NGAYMAUBENHPHAM,
										loai : "10"
									};
									dlgPopup = DlgUtil.buildPopupUrl("divDlgEDIT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K089_SUACHITIET", paramInput, "Cập nhật ngày y lệnh công khám", 500,
										300);
									DlgUtil.open("divDlgEDIT");
									EventUtil.setEvent("ev_dongcuaso", function(e) {
										DlgUtil.close("divDlgEDIT");
										_self._loadData();
									});
								});
							}
						}
					}
					//END START L2PT-16670
					_setFocusMauBenhPham($("#hidMAUBENHPHAMID").val(), _self.containerId + _self.options._grdSuatAn);
				}
			});
			GridUtil.setGridParam(_self.containerId + this.options._grdSuatAnChitiet, {
				onSelectRow : function(id, selected) {
					GridUtil.unmarkAll(_self.containerId + _self.options._grdSuatAnChitiet);
					GridUtil.markRow(_self.containerId + _self.options._grdSuatAnChitiet, id);
				},
				//HaNv_20172407: sua dinh dang cot so luong < 1
				gridComplete : function(id) {
					var ids = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var row = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', ids[i]);
						if (row.SOLUONG % 1 !== 0) {
							$('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('setCell', ids[i], 'SOLUONG', parseFloat(row.SOLUONG));
						}
						//Beg_HaNv_270621: In đậm chữ khi có thuốc, vật tư đi kèm - BVTM-3724
						if (hospital_id == '10284' && _self.options._loaidichvu == 13) {
							if ((row.THUOC_DIKEM != '' && row.THUOC_DIKEM != null) || (row.VATTU_DIKEM != '' && row.VATTU_DIKEM != null)) {
								$('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('setRowData', ids[i], "", 'mybold');
							}
						}
						//End_HaNv_270621
					}
					//BVTM-5605
					if (_self.hospital_id == '10284' && _self.options._lnmbp == "12") {
						for (var i = 0; i < ids.length; i++) {
							var row = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', ids[i]);
							//check phieu(maubenhpham) thi add vao list
							if (_self.ngaygiuong_check == true) {
								if (_self.lstdvkbid.indexOf(row.DICHVUKHAMBENHID) == -1) {
									_self.lstdvkbid.push(row.DICHVUKHAMBENHID);
									var obj = new Object();
									obj.DVKBID = row.DICHVUKHAMBENHID;
									obj.MADV = row.MADICHVU;
									obj.THUOC_DIKEM = row.THUOC_DIKEM;
									obj.VATTU_DIKEM = row.VATTU_DIKEM;
									_self.lstmadichvu.push(obj);
								}
								//if (_self.lstmadichvu.map(function(e) { return e.MADV; }).indexOf(row.MADICHVU) == -1 ) {
								//}
							} else {
								//bo check phieu(maubenhpham) thi remove dvkbid khoi list
								if (_self.lstdvkbid.indexOf(row.DICHVUKHAMBENHID) > -1) {
									_self.lstdvkbid = _self.lstdvkbid.filter(function(elem) {
										return elem != row.DICHVUKHAMBENHID;
									});
								}
								if (_self.lstmadichvu.map(function(e) {
									return e.DVKBID;
								}).indexOf(row.DICHVUKHAMBENHID) > -1) {
									_self.lstmadichvu = _self.lstmadichvu.filter(function(obj) {
										return obj.DVKBID !== row.DICHVUKHAMBENHID;
									});
								}
							}
						}
					}
					//END BVTM-5605
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'HIS_BACSY_YCHUY_DV');
					if (data_ar != null && data_ar.length > 0) {
						if (data_ar[0].HIS_BACSY_YCHUY_DV == '1') {
							var ids = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getDataIDs();
							for (var i = 0; i < ids.length; i++) {
								var id = ids[i];
								var rowDataCd = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', id);
								if (rowDataCd.YC_HOAN == '2') {
									$('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('setRowData', id, "", {
										color : 'blue'
									});
									$('#' + _self.containerId + _self.options._grdSuatAnChitiet).setCell(id, 'MADICHVU', '', {
										'text-decoration' : 'line-through'
									});
									$('#' + _self.containerId + _self.options._grdSuatAnChitiet).setCell(id, 'TENDICHVU', '', {
										'text-decoration' : 'line-through'
									});
								}
								if (rowDataCd.YC_HOAN == '1') {
									$('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('setRowData', id, "", {
										color : 'red'
									});
									$('#' + _self.containerId + _self.options._grdSuatAnChitiet).setCell(id, 'MADICHVU', '', {
										'text-decoration' : 'line-through'
									});
									$('#' + _self.containerId + _self.options._grdSuatAnChitiet).setCell(id, 'TENDICHVU', '', {
										'text-decoration' : 'line-through'
									});
								}
							}
						}
					}
					if (modeView == "0") {
						$(".jqgrow", '#' + _self.containerId + _self.options._grdSuatAnChitiet).contextMenu(_self.containerId + 'contextMenuMiengiam', {
							onContextMenu : function(event, menu) {
								var rowId = $(event.target).parent("tr").attr("id");
								var grid = $('#' + _self.containerId + _self.options._grdSuatAnChitiet);
								grid.setSelection(rowId);
								return true;
							},
						});
					}
					$('#' + _self.containerId + 'sendRequestDeleteReject').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
						_self._sendRequestDeleteReject(rowKey);
					});
					$('#' + _self.containerId + 'undoRequestDeleteReject').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
						_self._undoRequestDeleteReject(rowKey);
					});
					$('#' + _self.containerId + 'listActionDown').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
						_self._list_miengiam(rowKey);
					});
					$('#' + _self.containerId + 'actionDown').unbind("click").bind("click", function() {
						var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
						_self._miengiam(rowKey);
					});
					if (_self.options._lnmbp == "12" || _self.options._lnmbp == "16" || _self.options._lnmbp == "17") {
						//Tao ban DSPTVT_KEM
						$('#' + _self.containerId + 'DSPTVT_KEM').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn dịch vụ PTTT');
								return;
							}
							_self._dsPhieuThuocVatTuDiKem(rowKey, '0');
						});
						//Tao ban TAOPHIEUTHUOCKEM
						$('#' + _self.containerId + 'TAOPHIEUTHUOCKEM').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn dịch vụ');
								return;
							}
							_self._taoPhieuThuocDiKem(rowKey, '0');
						});
						//Tao ban TAOPHIEUVATTUKEM 
						$('#' + _self.containerId + 'TAOPHIEUVATTUKEM').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn dịch vụ');
								return;
							}
							_self._taoPhieuVatTuDiKem(rowKey, '0');
						});
						//Tao ban TAOPHIEUTHUOCKEM hao phi
						$('#' + _self.containerId + 'TAOPHIEUTHUOCKEM_HAOPHI').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn dịch vụ');
								return;
							}
							_self._taoPhieuThuocDiKem_haophi(rowKey, '0');
						});
						//Tao ban TAOPHIEUVATTUKEM hao phi
						$('#' + _self.containerId + 'TAOPHIEUVATTUKEM_HAOPHI').unbind("click").bind("click", function() {
							var rowKey = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).getGridParam("selrow");
							if (rowKey == null || rowKey == '') {
								DlgUtil.showMsg('Chưa chọn dịch vụ');
								return;
							}
							_self._taoPhieuVatTuDiKem_haophi(rowKey, '0');
						});
					} else {
						$('#' + _self.containerId + 'DSPTVT_KEM').hide();
						$('#' + _self.containerId + 'TAOPHIEUTHUOCKEM_HAOPHI').hide();
						$('#' + _self.containerId + 'TAOPHIEUVATTUKEM_HAOPHI').hide();
					}
				}
			//HaNv_20172407: End
			});
		},
		//ductx -bvtm=5600
		_bindEventToolbar : function() {
			var _self = this;
			$('#' + _self.containerId + 'toolbarIdbtnDsTvtDk').unbind("click").bind("click", function() {
				var rowKey = $('#' + _self.containerId + _self.options._grdSuatAn).getGridParam("selrow");
				if (rowKey == null || rowKey == '') {
					DlgUtil.showMsg('Chưa chọn dịch vụ!');
					return;
				}
				_self._danhsachTvt(rowKey);
			});
		},
		_danhsachTvt : function(rowId) {
			var _self = this;
			var p1 = [];
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowId);
			var rowDatas = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid("getGridParam", "selarrrow");
			if (rowDatas != null) {
				for (var j = 0; j < rowDatas.length; j++) {
					var rowDataP = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowDatas[j]);
					if (rowDataP != null) {
						var _par = [];
						var objData = new Object();
						objData["MAUBENHPHAMID"] = rowDataP.MAUBENHPHAMID;
						_par.push({
							"name" : "[0]",
							"value" : objData
						});
						var _res = jsonrpc.AjaxJson.ajaxExecuteQueryO("NT.024.3.VS2", _par);
						var datar = JSON.parse(_res);
						if (datar.length > 0) {
							for (var k = 0; k < datar.length; k++) {
								p1.push(datar[k].DICHVUKHAMBENHID);
							}
						}
					}
					/*objj = new Object();
					objj.MAUBENHPHAMID = rowData.MAUBENHPHAMID;
					p1.push(objj);*/
				}
				paramInput = {
					khambenhid : rowData.KHAMBENHID,
					dichvucha_id : p1.toString()
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDSPTDK", "divDlg", "manager.jsp?func=../noitru/NTU02D043_DanhSachPhieuThuocDiKem", paramInput, "Danh sách phiếu thuốc, vật tư đi kèm", 1200,
						615);
				DlgUtil.open("divDlgDSPTDK");
			}
		},
//end bvtm-5600
//        _checkRoles: function(_nguoitaoid){
//        	var _nguoidungid=$("#hidUserID").val();
//        	if(_nguoitaoid==null || _nguoitaoid==''){
//        		return false;
//        	}else{
//        		if(_nguoitaoid==_nguoidungid){
//        			return true;
//        		}else{
//        			return false;
//        		}
//        	}
//        },
		_deletePhieuSuatAn : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				//START L2PT-33305
				var config = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_TT_SUATAN_ALL_UPD");
				if (config != null && config.length > 0) {
					if (config[0].NTU_TT_SUATAN_ALL_UPD == '0') {
						var _nguoitaoid = rowData.NGUOITAO_ID;
						if (_checkRoles(_nguoitaoid, $("#hidUserID").val()) == false) {
							DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
							return false;
						}
					}
				}
				if (hospital_id == '10284' && _self.options._lnmbp == '11' && _nguoitaoid != $("#hidUserID").val()) {
					return DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
				}
				if (_self.HIS_CDDV_DTK_QTI == '1' && _self.options._lnmbp == '30' && _self.options._loaidichvu == '20') {
					var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
					_trangthai = parseInt(_trangthai);
					if (_trangthai == null)
						_trangthai = 0;
					if (_trangthai == 3 || _trangthai == 2) {
						return DlgUtil.showMsg("Không thể xóa phiếu này! Phiếu này đã hoặc đang được xử lý");
					}
				} else {
					//START Suat an
					if (rowData.TRANGTHAIMAUBENHPHAM == '6') {
						return DlgUtil.showMsg("Phiếu " + rowData.SOPHIEU + " đã được duyệt, Không thể xóa.");
					} else if (rowData.TRANGTHAIMAUBENHPHAM == '4') {
						return DlgUtil.showMsg("Phiếu " + rowData.SOPHIEU + " đang chờ duyệt, Không thể xóa.");
					}
					//END Suat an
					//L2PT-6139
					if (_self.options._loaidichvu == 12 && _self.sa_qti == '1' && rowData.TRANGTHAIMAUBENHPHAM != '1') {
						return DlgUtil.showMsg("Chỉ được xóa phiếu ở trạng thái đang sửa");
					}
					//END L2PT-6139
				}
				// Begin_laphm_13062019: không cho huỷ dịch vụ khi có phiếu phụ thu đi kèm - L2PT-5721 - HaNv_110921: BVTM-5602
				var _sophieudikem = 0;
				var sql_par_chek = [];
				sql_par_chek = RSUtil.buildParam("", [ rowData.MAUBENHPHAMID ]);
				var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU.024.4", sql_par_chek);
				var rows = JSON.parse(data);
				if (rows != null && rows.length > 0) {
					_sophieudikem = rows[0].SOPHIEUDIKEM;
				}
				if (_sophieudikem == null)
					_sophieudikem = 0;
				if (_sophieudikem > 0) {
					DlgUtil.showMsg("Phiếu này đã có phụ thu đi kèm,\n không được phép xóa phiếu");
					return 1;
				}
				// End_laphm_13062019
				DlgUtil.showConfirm("Bạn có chắc chắn xóa phiếu dịch vụ không?", function(flag) {
					if (flag) {
						var _par = [ rowData.MAUBENHPHAMID ];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL.PDV.024", _par.join('$'));
						if (_return == 1) {
							DlgUtil.showMsg("Xóa thành công phiếu dịch vụ");
							_self._initWidget();
						} else if (_return == 0) {
							DlgUtil.showMsg("Xóa không thành công phiếu dịch vụ");
						} else if (_return == -1) {
							DlgUtil.showMsg("Phiếu dịch vụ đã thu tiền nên không được phép xóa");
						}
						//Begin_HaNv_09102020: Tao goi vat tu di kem ngay giuong - L2PT-9828 - L2PT-28565
						else if (_return == -2) {
							DlgUtil.showMsg("Xóa dịch vụ không thành công vì có lỗi khi xóa phiếu thuốc, vật tư đi kèm");
						}
						//End_HaNv_09102020
						else if (_return == -3) {
							DlgUtil.showMsg("Có suất ăn đã chốt, không thể xóa");
						}
					}
				});
			}
		},
		//L2PT-6139
		_deleteRequest : function(rowId, type) {
			if (typeof (type) == "undefined") {
				type = 1;
			}
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowId);
			//End_HaNv_080721
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 2) {
					var _par = [ rowData.MAUBENHPHAMID, 1, 1 ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ", _par.join('$'));
					if (_return == 1) {
						if (type == 1) {
							DlgUtil.showMsg("Phiếu đã được hủy yêu cầu thành công!", undefined, 1500);
							_self._initWidget();
						} else {
							return 0;
						}
					} else if (_return == 0) {
						DlgUtil.showMsg("Hủy yêu cầu phiếu thất bại!");
						return 1;
					} else if (_return == -1) {
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'HIS_BACSY_YCHUY_DV');
						if (data_ar != null && data_ar.length > 0) {
							if (data_ar[0].HIS_BACSY_YCHUY_DV == '1') {
								if (rowData.TRANGTHAIMAUBENHPHAM != 2) {
									DlgUtil.showMsg('Phiếu đã được tiếp nhận hoặc đã có kết quả. Không thể yêu cầu hủy');
									return;
								} else {
									DlgUtil.showConfirm("Dịch vụ đã thu tiền. Bạn có chắc chắn muốn yêu cầu hủy tất cả dịch vụ của phiếu này?", function(flag) {
										if (flag) {
											var _parTmp = [ rowData.MAUBENHPHAMID, 0, 1 ];
											var _returnTmp = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _parTmp.join('$'));
											if (_returnTmp == 1) {
												DlgUtil.showMsg("Yêu cầu hủy phiếu thành công", undefined, 1500);
												_self._initWidget();
												return 1;
											} else {
												DlgUtil.showMsg("Yêu cầu hủy phiếu thất bại");
												return 1;
											}
										}
									});
								}
							}
						} else {
							DlgUtil.showMsg("Phiếu xét nghiệm đã thu tiền nên không được hủy yêu cầu");
							return 1;
						}
					}
				} else if (_trangthai == 1) {
					DlgUtil.showMsg("Phiếu đã được hủy yêu cầu!", undefined, 1500);
					return 1;
				} else if (_trangthai > 2 && _trangthai != 8) {
					DlgUtil.showMsg("Phiếu không ở trạng thái đã gửi nên không thể hủy yêu cầu");
					return 1;
				}
			}
		},
		_sendRequest : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				if (_trangthai == null)
					_trangthai = 0;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == 1) {
					var _par = [ rowData.MAUBENHPHAMID, 2, 0 ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ", _par.join('$'));
					if (_return == 1) {
						if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'KBH_TATTHONGBAO_KBHB') == "0") {
							DlgUtil.showMsg("Phiếu đã được gửi yêu cầu thành công!");
						}
						_self._initWidget();
					} else if (_return == 0) {
						DlgUtil.showMsg("Gửi yêu cầu phiếu thất bại!");
					}
				} else {
					DlgUtil.showMsg("Phiếu đã được gửi yêu cầu!");
				}
			}
		},
		//START L2PT-15424
		_deletePhieuDOVAI : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowId);
			if (rowData != null) {
				// check quyen xoa du lieu
				var _nguoitaoid = rowData.NGUOITAO_ID;
				if (_checkRoles(_nguoitaoid, $("#hidUserID").val()) == false) {
					DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
					return false;
				}
				DlgUtil.showConfirm("Bạn có chắc chắn xóa phiếu không?", function(flag) {
					if (flag) {
						var _par = [ rowData.MAUBENHPHAMID ];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL.PDV.024", _par.join('$'));
						if (_return == 1) {
							DlgUtil.showMsg("Xóa thành công phiếu dịch vụ");
							_self._initWidget();
						} else if (_return == 0) {
							DlgUtil.showMsg("Xóa không thành công phiếu dịch vụ");
						} else if (_return == -1) {
							DlgUtil.showMsg("Phiếu dịch vụ đã thu tiền nên không được phép xóa");
						}
					}
				});
			}
		},
		//END L2PT-15424
		//START -- HISL2TK-567 --hong 28052018
		_deleteSelectPhieuSuatAn : function(rowId) {
			var _self = this;
			var rowDatas = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid("getGridParam", "selarrrow");
			if (rowDatas != null) {
				var p1 = [];
				for (var j = 0; j < rowDatas.length; j++) {
					var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowDatas[j]);
					var _nguoitaoid = rowData.NGUOITAO_ID;
					if (_self.HIS_CDDV_DTK_QTI == '1' && _self.options._lnmbp == '30' && _self.options._loaidichvu == '20') {
						var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
						_trangthai = parseInt(_trangthai);
						if (_trangthai == null)
							_trangthai = 0;
						if (_trangthai == 3 || _trangthai == 2) {
							return DlgUtil.showMsg("Không thể xóa phiếu " + rowData.SOPHIEU + " ! Phiếu này đã hoặc đang được xử lý");
						}
					} else {
						//START Suat an
						if (rowData.TRANGTHAIMAUBENHPHAM == '6') {
							return DlgUtil.showMsg("Phiếu " + rowData.SOPHIEU + " đã được duyệt, Không thể xóa.");
						} else if (rowData.TRANGTHAIMAUBENHPHAM == '4') {
							return DlgUtil.showMsg("Phiếu " + rowData.SOPHIEU + " đang chờ duyệt, Không thể xóa.");
						}
						//END Suat an
					}
					if (hospital_id == '10284' && _self.options._lnmbp == '11' && _nguoitaoid != $("#hidUserID").val()) {
						return DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
					}
					// Begin_laphm_13062019: không cho huỷ dịch vụ khi có phiếu phụ thu đi kèm - L2PT-5721 - HaNv_110921: BVTM-5602
					var _sophieudikem = 0;
					var sql_par_chek = [];
					sql_par_chek = RSUtil.buildParam("", [ rowData.MAUBENHPHAMID ]);
					var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU.024.4", sql_par_chek);
					var rows = JSON.parse(data);
					if (rows != null && rows.length > 0) {
						_sophieudikem = rows[0].SOPHIEUDIKEM;
					}
					if (_sophieudikem == null)
						_sophieudikem = 0;
					if (_sophieudikem > 0) {
						DlgUtil.showMsg("Phiếu này đã có phụ thu đi kèm,\n không được phép xóa phiếu");
						return 1;
					}
					// End_laphm_13062019
					objj = new Object();
					objj.MAUBENHPHAMID = rowData.MAUBENHPHAMID;
					p1.push(objj);
				}
				// check quyen xoa du lieu
				/*if(_checkRoles(_nguoitaoid,$("#hidUserID").val())==false){
				 DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
				 return false;
				}*/
				DlgUtil.showConfirm("Bạn có chắc chắn xóa phiếu dịch vụ không?", function(flag) {
					if (flag) {
						var _par = [ JSON.stringify(p1) ];
						var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL.PDV.SL", _par.join('$'));
						if (_return == 1) {
							DlgUtil.showMsg("Xóa thành công phiếu dịch vụ");
							_self._initWidget();
						} else if (_return == 0) {
							DlgUtil.showMsg("Xóa không thành công phiếu dịch vụ");
						} else if (_return == -1) {
							DlgUtil.showMsg("Có Phiếu dịch vụ đã thu tiền nên không được phép xóa");
						}
						//Begin_HaNv_09102020: Tao goi vat tu di kem ngay giuong - L2PT-9828 - L2PT-28565
						else if (_return == -2) {
							DlgUtil.showMsg("Xóa dịch vụ không thành công vì có lỗi khi xóa phiếu thuốc, vật tư đi kèm");
						}
						//End_HaNv_09102020
						else if (_return == -3) {
							DlgUtil.showMsg("Có suất ăn đã chốt, không thể xóa");
						}
					}
				});
			}
		},
		//END -- HISL2TK-567 --hong 28052018
		_sendRequestDeleteReject : function(rowId) {
			var _self = this;
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'HIS_BACSY_YCHUY_DV');
			if (data_ar != null && data_ar.length > 0) {
				if (data_ar[0].HIS_BACSY_YCHUY_DV == '1') {
					var rowData = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
					if (rowData != null) {
						DlgUtil.showConfirm("Bạn có chắc chắn yêu cầu hủy dịch vụ không?", function(flag) {
							if (flag) {
								var _par = [ typeof (rowData.MAUBENHPHAMID) == "undefined" ? "0" : rowData.MAUBENHPHAMID, rowData.DICHVUKHAMBENHID, 0 ];
								var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.001", _par.join('$'));
								if (_return == 1) {
									//tuyennx_edit_start_20190425 L1PT-661
									if (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'KBH_TATTHONGBAO_KBHB') == "0") {
										DlgUtil.showMsg("Yêu cầu hủy dịch vụ thành công");
									}
									//tuyennx_edit_end_20190425 L1PT-661
									_self._initWidget();
								} else if (_return == 2) {
									DlgUtil.showMsg("Dịch vụ chưa thu tiền");
								} else if (_return == 3) {
									DlgUtil.showMsg("Phiếu đã được tiếp nhận hoặc có kết quả, không thể yêu cầu hủy dịch vụ");
								}
								//Begin_HaNv_05072019: Quy trình hoàn hủy với bệnh nhân đóng tạm ứng - L1PT-1245
								else if (_return == 4) {
									DlgUtil.showMsg("BN chưa đóng tạm ứng. Không thể hủy yêu cầu dịch vụ!");
								}
								//End_HaNv_05072019
								else {
									DlgUtil.showMsg("Yêu cầu hủy dịch vụ thất bại");
								}
							}
						});
					}
				}
			}
		},
		_undoRequestDeleteReject : function(rowId) {
			var _self = this;
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'HIS_BACSY_YCHUY_DV');
			if (data_ar != null && data_ar.length > 0) {
				if (data_ar[0].HIS_BACSY_YCHUY_DV == '1') {
					var rowData = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
					if (rowData != null && (rowData.YC_HOAN == '2' || rowData.YC_HOAN == '1')) {
						DlgUtil.showConfirm("Bạn có chắc chắn yêu cầu khôi phục dịch vụ không?", function(flag) {
							if (flag) {
								var _par = [ typeof (rowData.MAUBENHPHAMID) == "undefined" ? "0" : rowData.MAUBENHPHAMID, rowData.DICHVUKHAMBENHID ];
								var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.002", _par.join('$'));
								if (_return == 1) {
									DlgUtil.showMsg("Yêu cầu khôi phục dịch vụ thành công");
									_self._initWidget();
								} else if (_return == 2) {
									DlgUtil.showMsg("Dịch vụ chưa thu tiền");
								} else if (_return == 3) {
									DlgUtil.showMsg("Dịch vụ không yêu cầu hủy hoặc đã xác nhận, không thể khôi phục");
								} else {
									DlgUtil.showMsg("Yêu cầu khôi phục dịch vụ thất bại");
								}
							}
						});
					} else {
						DlgUtil.showMsg("Dịch vụ không yêu cầu hủy hoặc đã xác nhận, không thể khôi phục");
					}
				}
			}
		},
		_list_miengiam : function(rowId) {
			var _self = this;
			if (_self.options._loaidichvu != 13) {
				return;
			}
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
			if (rowData != null) {
				paramInput = {
					dichvukhambenhid : rowData.DICHVUKHAMBENHID
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVuMg", "divDlg", "manager.jsp?func=../noitru/NTU02D077_DanhSachMienGiam", paramInput, "Danh sách miễn giảm", 1100, 500);
				DlgUtil.open("divDlgDichVuMg");
			}
//   	     	var _self=this;
//      		 var rowData = $('#' + _self.containerId+_self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
//      		 if(rowData != null){
//      			var paramInput={
//					       dichvukhambenhid : rowData.DICHVUKHAMBENHID
//					     };   
//					     dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVuChiTiet","divDlg","manager.jsp?func=../noitru/NTU01H033_Miengiam",paramInput,"Miễn giảm",600,250);
//					     DlgUtil.open("divDlgDichVuChiTiet");	 
//      		 }
		},
		_miengiam : function(rowId) {
			var _self = this;
			if (_self.options._loaidichvu != 13) {
				return;
			}
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
			if (rowData.LOAIDOITUONG != 4 && rowData.LOAIDOITUONG != 6) {
				DlgUtil.showMsg('Chỉ miễn giảm cho các dịch vụ yêu cầu');
				return;
			}
			var rowIdParent = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getGridParam', 'selrow');
			var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowIdParent);
			if (rowDataP != null) {
				paramInput = {
					chidinhdichvu : '1',
					loaidichvu : '19',
					loaiphieumbp : '18',
					benhnhanid : rowDataP.BENHNHANID,
					khambenhid : rowDataP.KHAMBENHID,
					hosobenhanid : rowDataP.HOSOBENHANID,
					tiepnhanid : rowDataP.TIEPNHANID,
					doituongbenhnhanid : rowDataP.DOITUONGBENHNHANID,
					loaitiepnhanid : rowDataP.LOAITIEPNHANID,
					dichvukhambenhid : rowData.DICHVUKHAMBENHID
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 18, paramInput, "Phiếu miễn giảm", 1300, 600);
				DlgUtil.open("divDlgDichVu");
			}
//   	     	var _self=this;
//      		 var rowData = $('#' + _self.containerId+_self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
//      		 if(rowData != null){
//      			var paramInput={
//					       dichvukhambenhid : rowData.DICHVUKHAMBENHID
//					     };   
//					     dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVuChiTiet","divDlg","manager.jsp?func=../noitru/NTU01H033_Miengiam",paramInput,"Miễn giảm",600,250);
//					     DlgUtil.open("divDlgDichVuChiTiet");	 
//      		 }
		},
		_dsPhieuThuocVatTuDiKem : function(rowId, mode) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
			var rowIdParent = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getGridParam', 'selrow');
			//BVTM-5605
			if (mode == '0') {
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowIdParent);
			} else {
				//check neu trong list co  cho co 1 ma dv thi cho open form
				var valueArr = _self.lstmadichvu.map(function(item) {
					return item.MADV
				});
				if (valueArr.filter(function(itm, i, a) {
					return i == a.indexOf(itm);
				}).length > 1) {
					return DlgUtil.showMsg("Các phiếu không có chung mã dịch vụ");
				}
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowId);
			}
			if (rowData != null) {
				paramInput = {
					khambenhid : rowDataP.KHAMBENHID,
					dichvucha_id : mode == '0' ? rowData.DICHVUKHAMBENHID : _self.lstdvkbid[0],
					dvkbid : mode == '1' ? _self.lstdvkbid.join(",") : ''//BVTM-5605
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDSPTDK", "divDlg", "manager.jsp?func=../noitru/NTU02D043_DanhSachPhieuThuocDiKem", paramInput, "Danh sách phiếu thuốc, vật tư đi kèm", 1200,
						615);
				DlgUtil.open("divDlgDSPTDK");
			}
		},
		_taoPhieuThuocDiKem : function(rowId, mode) {
			var _opt = "02D010"
			var _msg = "Tạo phiếu thuốc đi kèm";
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
			var rowIdParent = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getGridParam', 'selrow');
			//BVTM-5605
			if (mode == '0') {
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowIdParent);
			} else {
				//check neu trong list co  cho co 1 ma dv thi cho open form
				var valueArr = _self.lstmadichvu.map(function(item) {
					return item.MADV
				});
				if (valueArr.filter(function(itm, i, a) {
					return i == a.indexOf(itm);
				}).length > 1) {
					return DlgUtil.showMsg("Các phiếu không có chung mã dịch vụ");
				}
				//L2PT-28343
				_self.lstdvkbid_final = _self.lstdvkbid;
				for (var i = 0; i < _self.lstmadichvu.length; i++) {
					if (_self.lstmadichvu[i].THUOC_DIKEM != '' && _self.lstmadichvu[i].THUOC_DIKEM != null) {
						_self.lstdvkbid_final = jQuery.grep(_self.lstdvkbid_final, function(value) {
							return value != _self.lstmadichvu[i].DVKBID;
						});
					}
				}
				if (_self.lstdvkbid_final.length == 0) {
					return DlgUtil.showMsg("Tất cả các dịch vụ đã chọn đều có thuốc đi kèm!");
				}
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowId);
			}
			if (rowData != null) {
				paramInput = {
					khambenhid : rowDataP.KHAMBENHID,
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : mode == '0' ? rowData.DICHVUKHAMBENHID : _self.lstdvkbid_final[0],
					loadkhotheo : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'LOAD_KHO_KETHUOC_PTTT'), //tuyennx add L2PT-9050
					opt : _opt,
					dvkbid : mode == '1' ? _self.lstdvkbid_final.join(",") : ''//BVTM-5605
				// tao phieu thuoc     					
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + _opt, "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1300, 590);
				DlgUtil.open("divDlgTaoPhieuThuoc" + _opt);
				EventUtil.setEvent("assignDrug_cancel", function(e) {
					DlgUtil.close("divDlgTaoPhieuThuoc" + _opt);
				});
			}
		},
		_taoPhieuThuocDiKem_haophi : function(rowId, mode) {
			var _opt = "02D010";
			var _msg = "Tạo phiếu thuốc đi kèm hao phí";
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
			var rowIdParent = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getGridParam', 'selrow');
			//BVTM-5605
			if (mode == '0') {
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowIdParent);
			} else {
				//check neu trong list co  cho co 1 ma dv thi cho open form
				var valueArr = _self.lstmadichvu.map(function(item) {
					return item.MADV
				});
				if (valueArr.filter(function(itm, i, a) {
					return i == a.indexOf(itm);
				}).length > 1) {
					return DlgUtil.showMsg("Các phiếu không có chung mã dịch vụ");
				}
				//L2PT-28343
				_self.lstdvkbid_final = _self.lstdvkbid;
				for (var i = 0; i < _self.lstmadichvu.length; i++) {
					if (_self.lstmadichvu[i].THUOC_DIKEM != '' && _self.lstmadichvu[i].THUOC_DIKEM != null) {
						_self.lstdvkbid_final = jQuery.grep(_self.lstdvkbid_final, function(value) {
							return value != _self.lstmadichvu[i].DVKBID;
						});
					}
				}
				if (_self.lstdvkbid_final.length == 0) {
					return DlgUtil.showMsg("Tất cả các dịch vụ đã chọn đều có thuốc đi kèm!");
				}
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowId);
			}
			if (rowData != null) {
				paramInput = {
					khambenhid : rowDataP.KHAMBENHID,
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : mode == '0' ? rowData.DICHVUKHAMBENHID : _self.lstdvkbid_final[0],
					opt : _opt, // tao phieu thuoc
					macdinh_hao_phi : 9,
					dvkbid : mode == '1' ? _self.lstdvkbid_final.join(",") : ''//BVTM-5605
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + _opt, "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1300, 590);
				DlgUtil.open("divDlgTaoPhieuThuoc" + _opt);
				EventUtil.setEvent("assignDrug_cancel", function(e) {
					DlgUtil.close("divDlgTaoPhieuThuoc" + _opt);
				});
			}
		},
		_taoPhieuVatTuDiKem : function(rowId, mode) {
			var _opt = "02D015";
			var _msg = "Tạo phiếu vật tư đi kèm";
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
			var rowIdParent = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getGridParam', 'selrow');
			//BVTM-5605
			if (mode == '0') {
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowIdParent);
			} else {
				//check neu trong list co  cho co 1 ma dv thi cho open form
				var valueArr = _self.lstmadichvu.map(function(item) {
					return item.MADV
				});
				if (valueArr.filter(function(itm, i, a) {
					return i == a.indexOf(itm);
				}).length > 1) {
					return DlgUtil.showMsg("Các phiếu không có chung mã dịch vụ");
				}
				//L2PT-28343
				_self.lstdvkbid_final = _self.lstdvkbid;
				for (var i = 0; i < _self.lstmadichvu.length; i++) {
					if (_self.lstmadichvu[i].VATTU_DIKEM != '' && _self.lstmadichvu[i].VATTU_DIKEM != null) {
						_self.lstdvkbid_final = jQuery.grep(_self.lstdvkbid_final, function(value) {
							return value != _self.lstmadichvu[i].DVKBID;
						});
					}
				}
				if (_self.lstdvkbid_final.length == 0) {
					return DlgUtil.showMsg("Tất cả các dịch vụ đã chọn đều có vật tư đi kèm!");
				}
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowId);
			}
			if (rowData != null) {
				paramInput = {
					khambenhid : rowDataP.KHAMBENHID,
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : mode == '0' ? rowData.DICHVUKHAMBENHID : _self.lstdvkbid_final[0],
					loadkhotheo : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'LOAD_KHO_KETHUOC_PTTT'), //tuyennx add L2PT-9050
					opt : _opt,
					dvkbid : mode == '1' ? _self.lstdvkbid_final.join(",") : ''//BVTM-5605
				// tao phieu thuoc
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + _opt, "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1300, 590);
				DlgUtil.open("divDlgTaoPhieuThuoc" + _opt);
				EventUtil.setEvent("assignDrug_cancel", function(e) {
					DlgUtil.close("divDlgTaoPhieuThuoc" + _opt);
				});
			}
		},
		_taoPhieuVatTuDiKem_haophi : function(rowId, mode) {
			var _opt = "02D015";
			var _msg = "Tạo phiếu vật tư đi kèm hao phí";
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
			var rowIdParent = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getGridParam', 'selrow');
			//BVTM-5605
			if (mode == '0') {
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowIdParent);
			} else {
				//check neu trong list co  cho co 1 ma dv thi cho open form
				var valueArr = _self.lstmadichvu.map(function(item) {
					return item.MADV
				});
				if (valueArr.filter(function(itm, i, a) {
					return i == a.indexOf(itm);
				}).length > 1) {
					return DlgUtil.showMsg("Các phiếu không có chung mã dịch vụ");
				}
				//L2PT-28343
				_self.lstdvkbid_final = _self.lstdvkbid;
				for (var i = 0; i < _self.lstmadichvu.length; i++) {
					if (_self.lstmadichvu[i].VATTU_DIKEM != '' && _self.lstmadichvu[i].VATTU_DIKEM != null) {
						_self.lstdvkbid_final = jQuery.grep(_self.lstdvkbid_final, function(value) {
							return value != _self.lstmadichvu[i].DVKBID;
						});
					}
				}
				if (_self.lstdvkbid_final.length == 0) {
					return DlgUtil.showMsg("Tất cả các dịch vụ đã chọn đều có vật tư đi kèm!");
				}
				var rowDataP = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowId);
			}
			if (rowData != null) {
				paramInput = {
					khambenhid : rowDataP.KHAMBENHID,
					maubenhphamid : "",
					loaikedon : 1,
					dichvuchaid : mode == '0' ? rowData.DICHVUKHAMBENHID : _self.lstdvkbid_final[0],
					opt : _opt, // tao phieu thuoc
					macdinh_hao_phi : 9,
					dvkbid : mode == '1' ? _self.lstdvkbid_final.join(",") : ''//BVTM-5605
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc" + _opt, "divDlg", "manager.jsp?func=../noitru/NTU02D010_CapThuoc", paramInput, _msg, 1300, 590);
				DlgUtil.open("divDlgTaoPhieuThuoc" + _opt);
				EventUtil.setEvent("assignDrug_cancel", function(e) {
					DlgUtil.close("divDlgTaoPhieuThuoc" + _opt);
				});
			}
		},
		_updatePhieuSuatAn : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowId);
			if (rowData != null) {
				//L2PT-27009
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", 'NTU_CAPNHAT_PHIEUSA');
				if (data_ar != null && data_ar.length > 0) {
					if (data_ar[0].NTU_CAPNHAT_PHIEUSA == '1') {
						if (rowData.TRANGTHAI_PTH_SA == 1 || rowData.TRANGTHAI_PTH_SA == 3 || rowData.TRANGTHAI_PTH_SA == 4 || rowData.TRANGTHAI_PTH_SA == 5) {
							return DlgUtil.showMsg("Không thể cập nhật phiếu Đã duyệt/ Nhà ăn đang xử lý/ Đã phát/ Đã thu tiền");;
						}
					}
				}
				// check quyen xoa du lieu
				//START L2PT-33305
				var allowAllUPD = false;
				var config = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_TT_SUATAN_ALL_UPD");
				if (config != null && config.length > 0) {
					if (config[0].NTU_TT_SUATAN_ALL_UPD == '0') {
						var _nguoitaoid = rowData.NGUOITAO_ID;
						if (_checkRoles(_nguoitaoid, $("#hidUserID").val()) == false) {
							DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
							return false;
						}
					}
				}
				if (_self.options._loaidichvu == 13) {
					var _par = [ rowData.MAUBENHPHAMID ];
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D077.EV003", _par.join('$'));
					if (result != 0) {
						DlgUtil.showMsg('Tồn tại dịch vụ miễn giảm trong phiếu,vui lòng xóa dịch vụ miễn giảm trước khi cập nhật phiếu');
						return;
					}
				}
				//Beg_HaNv_080721: Check thu tiền trước khi gọi đến hàm xử lý - L2PT-4265
				var par_tt = [ {
					"name" : "[0]",
					"value" : rowData.MAUBENHPHAMID
				} ];
				var checkThutien = jsonrpc.AjaxJson.getOneValue("CHECK.MBP.THUTIEN", par_tt);
				if (parseInt(checkThutien) > 0) {
					DlgUtil.showMsg("Phiếu đã thu tiền nên không thể cập nhật phiếu!");
					return false;
				}
				//End_HaNv_080721
				//L2PT-6139
				if (_self.options._loaidichvu == 12 && _self.sa_qti == '1' && rowData.TRANGTHAIMAUBENHPHAM != '1') {
					return DlgUtil.showMsg("Chỉ được sửa phiếu ở trạng thái đang sửa");
				}
				//END L2PT-6139
				//mo popup nhap phieu suat an khi dbclick on row
				var paramInput = {
					chidinhdichvu : '1',
					loaidichvu : _self.options._loaidichvu,
					loaiphieumbp : _self.options._lnmbp,
					benhnhanid : rowData.BENHNHANID,
					mabenhnhan : rowData.MABENHNHAN,
					khambenhid : rowData.KHAMBENHID,
					tiepnhanid : rowData.TIEPNHANID,
					hosobenhanid : rowData.HOSOBENHANID,
					doituongbenhnhanid : rowData.DOITUONGBENHNHANID,
					loaitiepnhanid : rowData.LOAITIEPNHANID,
					maubenhphamid : rowData.MAUBENHPHAMID,
					loaiPhieu : _self.options._lnmbp
				};
				dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + _self.options._loaidichvu, paramInput,
						"Cập nhật phiếu dịch vụ", 1300, 600);
				DlgUtil.open("divDlgDichVu");
			}
		},
		HuySuatAn : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + this.options._grdSuatAnChitiet).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var sa_bnd = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_NTU_SUATAN_BND');
				// check quyen xoa du lieu
				if (_self.options._loaidichvu == 12 && sa_bnd == '1') {
					var check_par = [];
					check_par.push({
						"name" : "[0]",
						"value" : "1"
					});
					var _sys_date_add1 = jsonrpc.AjaxJson.getOneValue("COM.SYSDATE_P", check_par);
					var _sys_date_temp = _sys_date_add1.substr(0, 10);
					var _sys_date = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
					var systemHour = _sys_date.substr(11, 2);
					_sys_date = _sys_date.substr(0, 10);
					if (rowData.trangthai == 0) {
						return DlgUtil.showMsg('Không thể thao tác do dịch vụ đã được hủy.');
					}
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_SUATAN_TG_TRUOCAN;NTU_SUATAN_TGCD");
					var tgSD = rowData.ngaymaubenhpham_sudung.substr(0, 10);
					//var tgSD_temp = moment(tgSD.substr(0,10), "DD/MM/YYYY");
					if ((rowData.gio_suatan == '6' || rowData.gio_suatan == '9') && tgSD == _sys_date_temp && (Number(systemHour) >= Number(data_ar[0].NTU_SUATAN_TGCD))) {
						return DlgUtil.showMsg('Không được hủy dich vụ 6H và 9H ngày ' + tgSD);
					}
					if (tgSD == _sys_date) {
						/*var pars = ['NTU_SUATAN_TG_TRUOCAN']; 
						var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));*/
						rowData.ngaymaubenhpham_sudung.substr(0, 10);
						if ((Number(rowData.ngaymaubenhpham_sudung.substr(11, 2)) + Number(data_ar[0].NTU_SUATAN_TG_TRUOCAN)) > Number(rowData.gio_suatan)) {
							return DlgUtil.showMsg("Đã quá giờ cho phép, Không thể hủy dịch vụ " + rowData.madichvu + " . Kiểm tra lại thời gian suất ăn!");
						}
					}
					objData = new Object();
					objData["DICHVUID"] = rowData.dichvuid;
					objData["MAUBENHPHAMID"] = $("#hidMAUBENHPHAMID").val();
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D029.HUY_SA", JSON.stringify(objData));
					if (fl == 1) {
						DlgUtil.showMsg("Đã gửi yêu cầu Hủy dịch vụ.");
						_self._initWidget();
					} else if (fl == 2) {
						DlgUtil.showMsg("Không được hủy dịch vụ.");
					} else if (fl == 3) {
						DlgUtil.showMsg("Chưa tạo phiếu suất ăn cho dịch vụ này.");
					} else if (fl == -1) {
						DlgUtil.showMsg("Có lỗi xảy ra.");
					}
					/*var sql_par = [];
					sql_par.push({"name":"[0]", value:$("#hidMAUBENHPHAMID").val()});
					sql_par.push({"name":"[1]", value:rowData.dichvuid});
						jsonrpc.AjaxJson.ajaxExecuteQuery("NTU02D029.HUY_SA", sql_par);
						DlgUtil.showMsg("Hủy dịch vụ thành công");*/
				}
			}
		},
		//nghiant 18082017
		_exportPTKHAC : function(rowId) {
			var _hospital_id = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');//L1PT-1470
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowId);
			if (rowData != null) {
				//L1PT-1470
				if (_hospital_id == '1077') {
					var par = [ {
						name : 'khambenhid',
						type : 'String',
						value : rowData.KHAMBENHID
					}, {
						name : 'maubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					} ];
					openReport('window', "PHIEU_SUDUNG_DVKT_1077", "pdf", par);
				} else {
					var par = [ {
						name : 'maubenhphamid',
						type : 'String',
						value : rowData.MAUBENHPHAMID
					} ];
					//Begin_HaNv_20092019: Tích hợp js phiếu áo vàng theo yc HuongPv - L2PT-8810
					var isPrintPhieuAoVang = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'IS_PHIEU_AOVANG');
					if (isPrintPhieuAoVang == '1') {
						openReport('window', 'PHIEU_AOVANG_A4', 'pdf', par);
					} else {
						console.log("rowData.MAUBENHPHAMID: " + rowData.MAUBENHPHAMID);
						openReport('window', "DKBD_PCD_THEM_CONG_KHAM_A5", "pdf", par);
					}
					//End_HaNv_20092019
				}
			}
		},
		//end nghiant 18082017
		// Xử lý sự kiện liên quan ký CA => START
		_kyCA : function(rowId) {
			var _self = this;
			_self._caRpt(rowId, '1');
		},
		_huyKyCA : function(rowId) {
			var _self = this;
			_self._caRpt(rowId, '2');
		},
		_exportKyCA : function(rowId) {
			var _self = this;
			_self._caRpt(rowId, '0');
		},
		_caRpt : function(rowId, signType) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowId);
			var _par = [ {
				name : 'HOSOBENHANID',
				type : 'String',
				value : rowData.HOSOBENHANID
			}, {
				name : 'MAUBENHPHAMID',
				type : 'String',
				value : rowData.MAUBENHPHAMID
			}, {
				name : 'RPT_CODE',
				type : 'String',
				value : 'DKBD_PCD_THEM_CONG_KHAM_A5'
			} ];
			if (signType == '0') {
				CommonUtil.openReportGetCA2(_par, false);
			} else {
				CommonUtil.kyCA(_par, signType);
				EventUtil.setEvent("eventKyCA", function(e) {
					DlgUtil.showMsg(e.res);
					_self._initWidget();
				});
			}
		},
		// Xử lý sự kiện liên quan ký CA => END
		_viewPhieuVatTuDetail : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + this.options._grdSuatAn).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var sa_bnd = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_NTU_SUATAN_BND');
				if (_self.options._lnmbp == '11' && sa_bnd == '1') {
					var sql_par = [];
					sql_par = RSUtil.buildParam("", [ rowData.MAUBENHPHAMID ]);
					GridUtil.loadGridBySqlPage(_self.containerId + this.options._grdSuatAnChitiet, "NTU02D029.DSSA_CT", sql_par);
				} else {
					var sql_par1 = [];
					var objData = new Object();
					objData["MAUBENHPHAMID"] = rowData.MAUBENHPHAMID;
					sql_par1.push({
						"name" : "[0]",
						"value" : objData
					});
					GridUtil.loadGridBySqlPage(_self.containerId + this.options._grdSuatAnChitiet, "NT.024.3.VS2", sql_par1);
				}
			}
		},
		_sendRequest : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == null)
					_trangthai = 0;
				if (_trangthai == 3) {
					return DlgUtil.showMsg("Không thể sửa phiếu này! Phiếu này đã hoặc đang được xử lý");
				}
				if (_trangthai == 2) {
					return DlgUtil.showMsg("Phiếu đã được gửi đi");;
				}
				if (_trangthai == 1) {
					var _par = [ rowData.MAUBENHPHAMID, 2, 0 ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ", _par.join('$'));
					if (_return == 1) {
						DlgUtil.showMsg("Phiếu đã được gửi yêu cầu thành công!");
						_self._initWidget();
					} else if (_return == 0) {
						DlgUtil.showMsg("Gửi yêu cầu phiếu thất bại!");
					}
				}
			}
		},
		_deleteRequest : function(rowId) {
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdSuatAn).jqGrid('getRowData', rowId);
			if (rowData != null) {
				var _trangthai = rowData.TRANGTHAIMAUBENHPHAM;
				_trangthai = parseInt(_trangthai);
				if (_trangthai == null)
					_trangthai = 0;
				if (_trangthai == 3) {
					return DlgUtil.showMsg("Không thể sửa phiếu này! Phiếu này đã hoặc đang được xử lý");
				}
				if (_trangthai == 1) {
					return DlgUtil.showMsg("Phiếu chưa được gửi đi");;
				}
				if (_trangthai == 1) {
					var _par = [ rowData.MAUBENHPHAMID, 1, 1 ];
					var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("CLS.DEL.SENT.REQ", _par.join('$'));
					if (_return == 1) {
						DlgUtil.showMsg("Phiếu đã được hủy yêu cầu thành công!");
						_self._initWidget();
					} else if (_return == 0) {
						DlgUtil.showMsg("Hủy yêu cầu phiếu thất bại!");
					}
				}
			}
		},
		// Destroy an instantiated plugin and clean up  modifications the widget has made to the DOM
		destroy : function() {
			// this.element.removeStuff();
			// For UI 1.8, destroy must be invoked from the
			// base widget
			$.Widget.prototype.destroy.call(this);
			// For UI 1.9, define _destroy instead and don't
			// worry about
			// calling the base widget
		},
		methodB : function(event) {
			console.log("methodB called");
		},
		methodA : function(event) {
			this._trigger("dataChanged", event, {
				key : "someValue"
			});
		},
		// Respond to any changes the user makes to the
		// option method
		_setOption : function(key, value) {
			switch (key) {
				case "someValue":
					//this.options.someValue = doSomethingWith( value );
				break;
				default:
					//this.options[ key ] = value;
				break;
			}
			// For UI 1.8, _setOption must be manually invoked
			// from the base widget
			$.Widget.prototype._setOption.apply(this, arguments);
			if (key == '_benhnhanid') {
				this._initWidget();
			}
			// For UI 1.9 the _super method can be used instead
			// this._super( "_setOption", key, value );
		}
	});
})(jQuery);

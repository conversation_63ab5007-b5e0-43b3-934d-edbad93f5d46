	 
(function($) {
	var cfObj = new Object(); //L2PT-16134
	var lstParamKyCa = []; //L2PT-28091
	var checkky = 0; //L2PT-28091
	var causer = -1;
	var capassword = -1;
	var _smartcauser = -1;
	//L2PT-43073
	var lstParamPrintCa = [];
	$.widget( "ui.ntu02d033_pt" , {

        //Options to be used as defaults
        options: {        
        	_grdPhieuthuoc : 'grdThuoc',	
        	_gridPhieuthuocDetail : 'grdChiTietThuoc',	
        	_grdChiTietThuoc1Sao : 'grdChiTietThuoc1Sao',	
        	_grdChiTietThuoc2Sao : 'grdChiTietThuoc2Sao',	
        	_grdChiTietThuocKS : 'grdChiTietThuocKS',	//L2PT-5682
        	_grdChiTietThuocPT : 'grdChiTietThuocPT',	//L2PT-107978
			_khambenhid: "",
        	_benhnhanid: "",
        	_lnmbp:	"",
        	_modeView: "0", // =1 chi view; !=1 la update
        	_hosobenhanid: "",
        	//START -- HISL2NT-865 -- hongdq --20180327
        	_modeViewNT : "" ,
        	_dichvuKhambenhID : "",
        	_indtringttrongnghanh : "",
        	checkLoad : false,
			_loaitvt : ""
        	//END -- HISL2NT-865 -- hongdq --20180327
        },
        containerId: '',
        //Setup widget (eg. element creation, apply theming
        // , bind events etc.)
        _create: function () {
            // _create will automatically run the first time this widget is called. Put the initial widget  setup code here, then you can access the element
            // on which the widget was called via this.element. The options defined above can be accessed via this.options this.element.addStuff();
        	console.log('_create');
        	this.containerId=$(this.element).attr('id');        	
        	this._initWidget();        	
        },
        _t : function(_id) {
        	var newId='';
        	if(_id.indexOf("=")>0) {
        		newId=_id.replace(/\b((txt|cbo|chk|hid|lbl|rad)[a-z,A-Z,0-9,_]+=)\b/gi,_self.containerId+"$1");
        	}
        	else if(_id.indexOf("#")==0) {
        		newId=_id.replace(/(#)([a-z,A-Z,0-9,_]+)/gi,"$1"+_self.containerId+"$2");
        	}
        	else {
        		newId=_self.containerId+_id;
        	}
        	return newId;
        },
        _initWidget: function() {
        	var _self=this;
        	_self.options.checkLoad = false;
        	$(_self.element).load('../noitru/NTU02D033_ThongTinThuoc.tpl?v=20180624', function() {
        		$(_self.element).find("[id]").each(function() {  
        			if($(this).attr("href")){
        				$(this).attr("href",'#' + _self.containerId + $(this).attr('href').replace("#", ""));
        			}
        			if(this.id == "pager_" + _self.options._grdPhieuthuoc){
        				this.id = "pager_" + _self.containerId + _self.options._grdPhieuthuoc;
        			}else if(this.id == "pager_" + _self.options._gridPhieuthuocDetail){
        				this.id = "pager_" + _self.containerId+_self.options._gridPhieuthuocDetail;
        			}
        			else if(this.id == "pager_" + _self.options._grdChiTietThuoc1Sao){
        				this.id = "pager_" + _self.containerId+_self.options._grdChiTietThuoc1Sao;
        			}
        			else if(this.id == "pager_" + _self.options._grdChiTietThuoc2Sao){
        				this.id = "pager_" + _self.containerId+_self.options._grdChiTietThuoc2Sao;
        			}
        			//tuyennx_add_start L2PT-5682
        			else if(this.id == "pager_" + _self.options._grdChiTietThuocKS){
        				this.id = "pager_" + _self.containerId+_self.options._grdChiTietThuocKS;
        			}
        			//tuyennx_add_end L2PT-5682
        			//L2PT-107978
        			else if(this.id == "pager_" + _self.options._grdChiTietThuocPT){
        				this.id = "pager_" + _self.containerId+_self.options._grdChiTietThuocPT;
        			}
        			else{
        				this.id = _self.containerId+this.id;	
        			}           	    
        	    })
        	    
    			//$("[data-i18n]").i18n();
    			_self._loadData();
        		_self._bindEvent();
        		height_window = $(window).height();   // returns height of browser viewport
                height_suatan = $('#' + _self.element.attr('id')).height();
                height_divMain = $('#hidDocumentHeight').val();
	            console.log('height_window1:' + height_window );
	            console.log('height_suatan1:' + height_suatan );
	            console.log('height_divMain1:' + height_divMain );
	            if(height_suatan + 110 < height_window){
	            	$('#divMain').css('height',height_divMain);
	            } else if(height_window < height_suatan + 110){
		             $("#divMain").css('height',height_suatan + 110);  
	            } else if(height_suatan + 110 == height_window) {
	            	$('#divMain').css('height',height_suatan + 110);
	            }
	            
	          //tuyennx_add_end_L2PT-15807
	            var par_ctl = ['CONTEXT_MENU_THUOC']; 
	        	var ctl_par = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', par_ctl.join('$'));
	        	if(ctl_par != '0'){
	        		ctl_par = ctl_par.split("id=\"").join("id=\"" + _self.containerId);
	        		$( "#"+_self.containerId+"contextMenu" ).html(ctl_par);
	        	}
	        	//tuyennx_add_end_L2PT-15807
    		});
        },
        _loadData: function() { 
        	
        	//L2PT-16134 L2PT-20784
        	var config_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "PHONG_TUDONG_IN;TAB_THUOC_GRID_THUOC_CT;TABTHUOC_HIENTHI_CHECKBOX;"
					+ "SUDUNGTHUOCSAO_HC;COLUMN_SET_AUTO;TCBN_GRID_THUOC_CT;KT_SUDUNG_KHANGSINH;SUDUNGTHUOC12SAO;CACH_LAY_DS_THUOCVT;"
					+ "HIS_SUDUNG_KYSO_KYDIENTU;TAB_THUOCVT_HIENTHI_PHIEUBU;SHOW_COPY_PHIEUTHUOC;NTU_CAPTHUOC_PHUTROI;HIS_HOICHAN_THUOCTT30;"
					+ "OPT_HOSPITAL_ID;INDONTHUOC_THEOPHONG;KETHUOC_INTACH_NHATHUOC;IN_TACHDONMUA_NGOAI;IN_TACHDON_MP_TPCN;DT_MUANGOAI;"
					+ "DTDT_DAY_DONTHUOC;"//L2PT-21485
					+ "KETHUOC_TAT_HAOPHI;" //L2PT-21906
					+ "TABTHUOC_ANIN_KHONGTOA;" //L2PT-44160
					+ "TABTHUOC_CHANIN_SUAPHIEU;" //L2PT-22921
					+ "HIS_SHOW_THUOC_MUANGOAI;" //L2PT-23946
					+ "KYSO_TUDONG_KYDIENTU;" //L2PT-30069
					+ "KETHUOC_KYCA;" //L2PT-30263
					+ "KETHUOC_IN_KYSO_CHUNG;" //L2PT-43073
					+ "HIS_SHOW_THUOC_MUANGOAI;" //L2PT-38219
					+ "KETHUOC_INTACHDON_NGT;" //L2PT-33876
					+ "TABTHUOC_THUOCKS_CHECKMAU;" //L2PT-21839
					+ "TABTHUOC_PHIEUTRA_MAU;" //L2PT-53518
					+ "KETHUOC_INPHIEULINH_NGT;NTU_THUOC_CHECKXOA_USER;XOA_CAPNHAT_PHIEU_KOCANHUY;BTKD_DAY_BYT;KETHUOC_NHATHUOC_INGOP1DON;NTU_DTKH_ALLOW_UPDATE_DVKT");//L2PT-51997
			if (config_ar != null && config_ar.length > 0) {
				cfObj = config_ar[0];
			} else {
				DlgUtil.showMsg("Không tải được thông tin cấu hình sử dụng trên màn hình");
				return;
			}
			
        	var _tudongin = '0';
        	var _data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH_ND", 'NGT_PHIEUKHAM_TUDONGIN');
        	if (_data_ar != null && _data_ar.length > 0) {
        		_tudongin = _data_ar[0].NGT_PHIEUKHAM_TUDONGIN;
        	}
        	
			var dc_phong = cfObj.PHONG_TUDONG_IN;
			var dc_phongs = dc_phong.split(',');
			
			if(_tudongin != '1' && !$.inArray( $('#hidPHONGID').val(), dc_phongs) >= 0){
				$('#tabThuocprintPT').remove();
				$('#tabThuocprintPhieu').remove();
			}
			
			//L2PT-44160
			if(cfObj.TABTHUOC_ANIN_KHONGTOA == '1'){
				$('#tabThuocprintPKKT').remove();
				$('#tabThuocprintPKKTView').remove();
			}
			//L2PT-72762
			if(cfObj.TABTHUOC_HIENTHI_CHECKBOX == '0'){
				$('#printPhatThuocV').remove();
				$('#printPhatThuoc').remove();
				$('#printThuocMultiV').remove();
				$('#printThuocMulti').remove();
			}
        	
        	var _self=this;
        	var _look_sql="NT.024.DSTHUOCVT";
        	//var pars = cfObj.COLUMN_SET_AUTO; 
			var _setauto = cfObj.COLUMN_SET_AUTO;
			
			//L2PT-38219
        	if(!_self.options._khambenhid || cfObj.HIS_SHOW_THUOC_MUANGOAI == '0'){      	        			 
     			$("#"+_self.containerId+"editView").remove();  
     			$("#"+_self.containerId+"xoaPhieuView").remove();  
     		}
			
			if(_setauto == '1'){
				var _ma_grid = "'GRD_DSPHIEUTHUOC','GRD_CTDSPHIEUTHUOC'";        	
	        	var data =getGrid(_ma_grid); 
	        	for(var i=0;i< data.length;i++){
	        		if(data[i].MA_GRID == "GRD_DSPHIEUTHUOC"){
	        			GridUtil.init(_self.containerId+this.options._grdPhieuthuoc,data[i].WIDTH,data[i].HEIGHT,data[i].TEN_GRID, false, data[i].THUOCTINH_COT,false,data[i].THUOCTINH);
	        			if(data[i].TK_GRID != "1"){
	        				$("#"+_self.containerId+_self.options._grdPhieuthuoc)[0].toggleToolbar(); 
	        			}
	        		}else if(data[i].MA_GRID == "GRD_CTDSPHIEUTHUOC"){
	        			GridUtil.init(_self.containerId+this.options._gridPhieuthuocDetail,data[i].WIDTH,data[i].HEIGHT,data[i].TEN_GRID,false, data[i].THUOCTINH_COT,false,data[i].THUOCTINH);
	        			if(data[i].TK_GRID != "1"){
	        				$("#"+_self.containerId+_self.options._gridPhieuthuocDetail)[0].toggleToolbar(); 
	        			}
	        		}
				}
			}else{
				var _phieutra = ";Phiếu trả,SOPHIEUTRA,70,0,f,l";
	        	if(_self.options._loaitiepnhanid == '1'){
			        _phieutra = ";Phiếu trả,SOPHIEUTRA,240,0,f,l";
				}
				//khoi tao grid danh sach phieu thuoc        	
	        	var _gridHeader=" ,ICON,30,0,ns,l; ,KYSO,30,0,ns,l;FLAG_CA,FLAG_CA,0,0,t,l;Số phiếu,SOPHIEU,70,0,f,l;Phiếu lĩnh,PHIEU_LINH,70,0,f,l;Phiếu ĐTRI,PHIEU_DTRI,120,0,f,l;Người chỉ định,NGUOICAPNHAT,120,0,f,l;Phòng,PHONGDIEUTRI,160,0,f,l;Ngày chỉ định,NGAYMAUBENHPHAM,115,0,f,l;Ngày sử dụng,NGAYMAUBENHPHAM_SUDUNG,115,0,f,l;Kho,TENKHO,110,0,f,l;" + //L2PT-6043  BVTM-4728
	        			"STT,SOTHUTUCHIDINH,40,0,f,l;Đi kèm,DIKEM1,55,0,ns,l;Loại phiếu,LOAIPHIEU,70,0,f,l;Trạng thái,TRANGTHAI_PHIEU,82,0,f,l;" + //tuyennx L2PT-6474 sua icon -> di kem
	        			"LOAIPHIEUMAUBENHPHAM,LOAIPHIEUMAUBENHPHAM,0,0,t,l;PHONGID,PHONGID,0,0,t,l;TRANGTHAIMAUBENHPHAM,TRANGTHAIMAUBENHPHAM,0,0,t,l;MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l;" +
	        			"KHAMBENHID,KHAMBENHID,0,0,t,l;BENHNHANID,BENHNHANID,0,0,t,l;DIKEM,DIKEM,0,0,t,l;NGUOITAO_ID,NGUOITAO_ID,0,0,t,l;DACODICHVUTHUTIEN,DACODICHVUTHUTIEN,0,0,t,l;" +
	        			//tuyennx_add_start L2K74TW-597 HISL2CORE-1315 + SONDN
	        			"LOAIKEDONTHUOC,LOAIKEDONTHUOC,0,0,t,l;"+
	        			"TRANGTHAI_DUYETDUOC_CLS,TRANGTHAI_DUYETDUOC_CLS,0,0,t,l;"+
	        			"Trạng thái,TRANGTHAIDUYETDUOC,60,0,f,l;"+
	        			//tuyennx_add_end
	        			//tuyennx_add_start L2HOTRO-11841
	        			"DONTHUOCPHU,DONTHUOCPHU,0,0,t,l;"+
	        			"LOAITHANHTOAN,LOAITHANHTOAN,0,0,t,l;"+ //L2PT-35459
	        			//tuyennx_add_end
	        			//tuyennx_add_start L2PT-2864
	        			"DINHDUONG,DINHDUONG,0,0,t,l;"+
	        			"KHOADIEUTRI,KHOADIEUTRI,0,0,t,l;"+ //L2PT-20784
	        			"OPTIONS,OPTIONS,0,0,t,l;"+ //L2PT-13999
	        			"DAYDTDT,DAYDTDT,0,0,t,l;"+ //L2PT-19905
	        			"CHOLANHDAODUYET,CHOLANHDAODUYET,0,0,t,l;"+ //L2PT-21839
	        			"TRANGTHAI_DUYET_NGT,TRANGTHAI_DUYET_NGT,0,0,t,l;"+ //L2PT-31534
	        			//tuyennx_add_end
	        			"LOAIKEDON,LOAIKEDON,0,0,t,l;PHIEUHEN,PHIEUHEN,0,0,t,l;PHIEUTRA_ID,PHIEUTRA_ID,0,0,t,l;SLTHANG,SLTHANG,0,0,t,l;LOAIDONTHUOC,LOAIDONTHUOC,30,0,t,l;LOAIKHO,LOAIKHO,30,0,t,l;KIEUKEDON,KIEUKEDON,0,0,t,l"+_phieutra+";Đơn ra viện,RAVIEN,60,0,f,l;Số phiếu đi kèm,SOPHIEUDIKEM,80,0,f,l;" +
	        			"Người tạo,NGUOITAO,120,0,f,l;"+ //L2PT-29422
	        			"Kiểu kê,KIEUKE,80,0,f,l";    //tuyennx L2PT-9849  L2PT-30351  		
	        	
	    		//tao grid phieu vat tu chi tiet
	        	var _gridHeaderDetail="DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;Mã phiếu bù,MAPHIEUBU,80,0,f,l;Mã thuốc,MADICHVU,80,0,f,l;Ngày SD,SOLAN_SD_KHANGSINH,80,0,f,l;Tên thuốc,TENDICHVU,250,0,f,l;Nồng độ/hàm lượng,NDHL,120,0,f,l;" + //L2PT-6669 them maphieubu
	        			"Số lượng,SOLUONG,65,0,f,l;Phụ trội,PHUTROI,65,0,f,l;SL trả,SOLUONGTRA,70,decimal,e,l;" +
	        			"Đơn giá,TIENCHITRA,75,0,f,l;ĐVT,DVT,50,0,f,l;Số ngày,NGAYDUNG,60,0,f,l;" +
	        			"Đường dùng,DUONGDUNG,100,0,f,l;" +
	        			"Số lô,SOLO,60,0,f,l;" + //L2PT-24714
	        			"Hưỡng dẫn sử dụng,HUONGDANSUDUNG,235,0,f,l;" +
	        			"Trạng thái,TRANGTHAIDUYETDUOC,60,0,f,l;"+
	        			"Lý do,LYDOHUYDUYET,100,0,f,l;"+
	        			"Loại TT,LOAIDOITUONG,60,0,f,l;Liều dùng,LIEUDUNG,235,0,f,l;" +
	        			//tuyennx_add_start_20180803  HISL2TK-910 L2DKBD-1189
	        			"NSD,SOLAN_SD_KHANGSINH,65,0,f,l;" +
	        			"STT kê,STT_KE,65,0,f,l;" + //L2PT-71904
	        			"Số lượng kê,SL_KELE,150,0,f,l;" +
	        			//tuyennx_add_end_20180803 
	        			
	        			//tuyennx_add_start_20190507 L2PT-4556 L2PT-8767
	        			"Số lượng quy đổi,SO_LUONG_KE,120,0,f,l;" +
	        			"Đơn vị quy đổi,DONVI_TINH_QD,120,0,f,l;" +
	        			"Đơn giá quy đổi,DON_GIA_QD,120,0,f,l;" +
	        			"Tên quy đổi,TEN_QD,100,0,f,l;" +
	        			//tuyennx_add_end_20190507 L2PT-4556
	        			
	        	        //tuyennx_add_start_20190418 L1PT-464 
	        			"MAUBENHPHAMID,MAUBENHPHAMID,235,0,t,l;" +
	        			"YC_HOAN,YC_HOAN,150,0,t,l;" +
	        			"CHOLANHDAODUYET,CHOLANHDAODUYET,235,0,t,l;" + //L2PT-21839
	        			"Số lượng hỏng,SOLUONGHONG,100,0,f,l;" + //L2PT-14996
	        			"Lý do hủy,LYDOHUY,100,0,f,l;" + //L2PT-24256
	        			"Ghi chú cảnh báo,GHICHUCANHBAO,100,0,f,l;" + //L2PT-29871
	        			"TRANGTHAI_DUYET_NGT,TRANGTHAI_DUYET_NGT,0,0,t,l;"+ //L2PT-31534
	        			"TRANGTHAI_TT,TRANGTHAI_TT,0,0,t,l;"+ //L2PT-51834
	        			
	        	        //tuyennx_add_end_20190418 L1PT-464 
	        			"KIEUKEDON,KIEUKEDON,0,0,t,l;" +
	        			"Mũi tiêm,MUITIEM,60,0,f,l";
	        	//L2PT-10483
	        	var _headerDetailCH = cfObj.TAB_THUOC_GRID_THUOC_CT;
	        	if( _headerDetailCH!= '0' )
	        		_gridHeaderDetail = _headerDetailCH;
	        	
	        	if(_self.options._khambenhid == "" && cfObj.TCBN_GRID_THUOC_CT == 1 ){
	        		_gridHeaderDetail="DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;Mã phiếu bù,MAPHIEUBU,80,0,f,l;Mã thuốc,MADICHVU,80,0,f,l;Ngày SD,SOLAN_SD_KHANGSINH,80,0,f,l;" +
	        		"Tên thuốc,TENDICHVU,250,0,f,l;Tên thuốc(BHYT),TENKHOAHOC,250,0,f,l;Nồng độ/hàm lượng,NDHL,120,0,f,l;" + //L2PT-6669 them maphieubu
        			"Số lượng,SOLUONG,65,0,f,l;Phụ trội,PHUTROI,65,0,f,l;SL trả,SOLUONGTRA,70,0,f,l;" +
        			"Đơn giá,TIENCHITRA,75,0,f,l;ĐVT,DVT,50,0,f,l;Số ngày,NGAYDUNG,60,0,f,l;" +
        			"Đường dùng,TENDUONGDUNG,100,0,f,l;" +
        			"Mã đường dùng,MADUONGDUNG,100,0,f,l;" +
        			"Số lô,SOLO,60,0,f,l;" + //L2PT-24714
        			"Hưỡng dẫn sử dụng,HUONGDANSUDUNG,235,0,f,l;" +
        			"Trạng thái,TRANGTHAIDUYETDUOC,60,0,f,l;"+
        			"Lý do,LYDOHUYDUYET,100,0,f,l;"+
        			"Loại TT,LOAIDOITUONG,60,0,f,l;Liều dùng,LIEUDUNG,235,0,f,l;" +
        			"Hạn sử dụng,HANSUDUNG,100,0,f,l;" +
        			"Số đăng ký,SODANGKY,100,0,f,l;" +
        			"Quyết định thầu,MATHAU,100,0,f,l;" +
        			"Gói thầu,GOITHAU,100,0,f,l;" +
        			"Nhóm thầu,MANHOMTHAU,100,0,f,l;" +
        			"Tỷ lệ,TYLEBHYT,100,0,f,l;" +
        			"Giá VAT,VAT,100,0,f,l;" +
        			"Giá BHYT,GIABHYT,100,0,f,l;" +
        			"Thành tiền,THANHTIEN,100,0,f,l"; 
	        	}
	        	
	        	//L2PT-106597
	        	var dtMenu = jsonrpc.AjaxJson.ajaxCALL_SP_C('COM.CAUHINH.CLOB', 'NTU02D033_HDGRID_THUOC');
				if (dtMenu != undefined && dtMenu != 0 && dtMenu.length > 100) {
					_gridHeader = dtMenu;
				}
	        	
	        	//tuyennx_edit_ L2PT-31551
	        	//L2PT-20784
	        	var bl_check = false;
	        	if(cfObj.TABTHUOC_HIENTHI_CHECKBOX == 1){
	        		bl_check = true;
	        	}else 
	        		bl_check = false;
	        	var _group = {
    					groupField : [ 'KHOADIEUTRI' ],
    					groupColumnShow : [ false, false ],
    					groupText : [ '<b>{0}</b>', '<b>{0}</b>' ]
    				};
	        	if(cfObj.CACH_LAY_DS_THUOCVT == 1)
	        		GridUtil.initGroup(_self.containerId+this.options._grdPhieuthuoc,"100%","165px",'Danh sách phiếu thuốc',bl_check,_group, _gridHeader,false,{rowNum: 200,rowList: [100, 200, 500, 1000]}); //hunglv L2PT-7647
	        	else
	        		GridUtil.init(_self.containerId+this.options._grdPhieuthuoc,"100%","165px",'Danh sách phiếu thuốc',bl_check, _gridHeader,false,{rowNum: 200,rowList: [100, 200, 500, 1000]}); //hunglv L2PT-7647
	        	
	        	GridUtil.init(_self.containerId+this.options._gridPhieuthuocDetail,"100%","165px",'Danh sách chi tiết phiếu thuốc',false, _gridHeaderDetail,false,{rowNum: 20,rowList: [10, 20, 200]});
	        	//tuyennx_add_start_HISL2TK-544 L2PT-295
	        	var _gridHeader1sao= "";
	        	var _gridHeader2sao= "";
	        	if(cfObj.SUDUNGTHUOCSAO_HC == 1){
	        		_gridHeader1sao="DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;PHIEU_HC,PHIEU_HC,0,0,t,l;Mã thuốc,MADICHVU,80,0,f,l;Tên thuốc,TENDICHVU,250,0,f,l;Ngày hội chẩn,NGAYHOICHAN,120,0,f,l;KHAMBENHID,KHAMBENHID,0,0,t,l;" +
	    			"Ngày hết hiệu lực,NGAYHETHAN,250,0,f,l;Số phiếu 1*,SOPHIEU1SAO,250,0,f,l;LOAIHOICHAN,LOAIHOICHAN,0,0,t,l;HOICHANKS,HOICHANKS,0,0,t,l"; //L2PT-5682
//		        	_gridHeader2sao="DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;PHIEU_HC,PHIEU_HC,0,0,t,l;PHIEU_HCLD,PHIEU_HCLD,0,0,t,l;Mã thuốc,MADICHVU,80,0,f,l;Tên thuốc,TENDICHVU,250,0,f,l;Ngày hội chẩn,NGAYHOICHAN,120,0,f,l;KHAMBENHID,KHAMBENHID,0,0,t,l;" +
//		    			"Ngày hết hiệu lực,NGAYHETHAN,250,0,f,l;Số phiếu 1*,SOPHIEU1SAO,250,0,f,l;Số phiếu 2*,SOPHIEU2SAO,250,0,f,l;LOAIHOICHAN,LOAIHOICHAN,0,0,t,l;HOICHANKS,HOICHANKS,0,0,t,l"; //L2PT-5682
		        	_gridHeader2sao="DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;PHIEU_HC,PHIEU_HC,0,0,t,l;PHIEU_HCLD,PHIEU_HCLD,0,0,t,l;Mã thuốc,MADICHVU,80,0,f,l;Tên thuốc,TENDICHVU,250,0,f,l;Ngày hội chẩn,NGAYHOICHAN,120,0,f,l;KHAMBENHID,KHAMBENHID,0,0,t,l;" +
	    			"Tình trạng lâm sàng khi hội chẩn,TINHTRANG_LS,300,0,f,l;Nhiệt độ,NHIETDO,250,0,f,l;LOAIHOICHAN,LOAIHOICHAN,0,0,t,l;HOICHANKS,HOICHANKS,0,0,t,l"; //L2PT-5682
	        	}
				else{
					 _gridHeader1sao="DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;Mã thuốc,MADICHVU,80,0,f,l;Tên thuốc,TENDICHVU,250,0,f,l;Ngày hội chẩn,NGAYHOICHAN,120,0,f,l;KHAMBENHID,KHAMBENHID,0,0,t,l;" +
	    			"Tình trạng bệnh khi hội chẩn,TINHTRANGHC,250,0,f,l;Thuốc đã điều trị,THUOCDIEUTRI,250,0,f,l;Chẩn đoán sau hội chẩn,CHANDOANSAUHOICHAN,250,0,f,l;Chỉ định dùng thuốc dấu (*),CHIDINHDUNGTHUOCSAO,250,0,f,l;LOAIHOICHAN,LOAIHOICHAN,0,0,t,l;HOICHANKS,HOICHANKS,0,0,t,l"; //L2PT-5682
		        	 _gridHeader2sao="DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;Mã thuốc,MADICHVU,80,0,f,l;Tên thuốc,TENDICHVU,250,0,f,l;Mô tả triệu chứng lâm sàng,TRIEUCHUNGLS,250,0,f,l;KHAMBENHID,KHAMBENHID,0,0,t,l;" +
	    			"Chẩn đoán,CHANDOAN,200,0,f,l;Kháng sinh yêu cầu,KHANGSINHYC,250,0,f,l;Chỉ định,CHIDINH,250,0,f,l;Mẫu xét nghiệm,MAUXETNGHIEM,250,0,f,l;Kết quả nuôi cấy,KQNUOICAY,250,0,f,l";
				}
				//tuyennx_edit_end_20190108
	        	GridUtil.init(_self.containerId+this.options._grdChiTietThuoc1Sao,"100%","165px",'Danh sách chi tiết phiếu thuốc 1 sao',false, _gridHeader1sao,false,{rowNum: 10,rowList: [10, 20, 200]});
	        	GridUtil.init(_self.containerId+this.options._grdChiTietThuoc2Sao,"100%","165px",'Danh sách chi tiết phiếu thuốc 2 sao',false, _gridHeader2sao,false,{rowNum: 10,rowList: [10, 20, 200]});
	        	
	        	//tuyennx_add_start L2PT-5682
	        	var _gridHeaderKS = "DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;Mã thuốc,MADICHVU,80,0,f,l;Tên thuốc,TENDICHVU,250,0,f,l;Mô tả triệu chứng lâm sàng,TRIEUCHUNGLS,250,0,f,l;KHAMBENHID,KHAMBENHID,0,0,t,l;" +
    			"Chẩn đoán,CHANDOAN,200,0,f,l;Kháng sinh yêu cầu,KHANGSINHYC,250,0,f,l;Chỉ định,CHIDINH,250,0,f,l;Mẫu xét nghiệm,MAUXETNGHIEM,250,0,f,l;Kết quả nuôi cấy,KQNUOICAY,250,0,f,l;Liều dùng,LIEUDUNG,250,0,f,l;Ghi chú,GHICHU_LD,250,0,f,l;LOAIHOICHAN,LOAIHOICHAN,0,0,t,l;HOICHANKS,HOICHANKS,0,0,t,l";//L2PT-7599
	        	GridUtil.init(_self.containerId+this.options._grdChiTietThuocKS,"100%","165px",'Danh sách chi tiết phiếu thuốc KS',false, _gridHeaderKS,false,{rowNum: 10,rowList: [10, 20, 200]});
	        	$("#"+_self.containerId+_self.options._grdChiTietThuocKS)[0].toggleToolbar(); 
	        	//L2PT-107978_start
	        	if(jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','KT_SUDUNG_KHANGSINH') ==1){
	        		$("#"+_self.containerId+'tabChiTietThuocKS').css('display','');
	        	}
	        	else{
	        		$("#"+_self.containerId+'tabChiTietThuocKS').css('display','none');
	        	}
	        	//tuyennx_add_end L2PT-5682
	        	
	        	var _gridHeaderPT = "DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,l;Mã thuốc,MADICHVU,80,0,f,l;Tên thuốc,TENDICHVU,250,0,f,l;Ngày đánh giá,NGAYDANHGIA,250,0,f,l;KHAMBENHID,KHAMBENHID,0,0,t,l;PHANTANGID,PHANTANGID,0,0,t,l";
	        	GridUtil.init(_self.containerId+this.options._grdChiTietThuocPT,"100%","165px",'Danh sách chi tiết phiếu thuốc PT',false, _gridHeaderPT,false,{rowNum: 10,rowList: [10, 20, 200]});
	        	$("#"+_self.containerId+_self.options._grdChiTietThuocPT)[0].toggleToolbar(); 
	        	if(jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','KETHUOC_PHANTANG') ==1){
	        		$("#"+_self.containerId+'tabChiTietThuocPT').css('display','');
	        	}
	        	else{
	        		$("#"+_self.containerId+'tabChiTietThuocPT').css('display','none');
	        	}
	        	
	        	$("#"+_self.containerId+_self.options._grdChiTietThuoc1Sao)[0].toggleToolbar(); 
	        	$("#"+_self.containerId+_self.options._grdChiTietThuoc2Sao)[0].toggleToolbar(); 
	        	if(jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','SUDUNGTHUOC12SAO') ==1){
	        		$("#"+_self.containerId+'tabChiTietThuoc1Sao').css('display','');
	        		$("#"+_self.containerId+'tabChiTietThuoc2Sao').css('display','');
	        	}
	        	else{
	        		$("#"+_self.containerId+'tabChiTietThuoc1Sao').css('display','none');
	        		$("#"+_self.containerId+'tabChiTietThuoc2Sao').css('display','none');
	        	}
	        	//L2PT-107978_end
	        	//tuyennx_add_end_HISL2TK-544
	        	//$("#"+_self.containerId+_self.options._grdPhieuthuoc)[0].toggleToolbar(); 
	        	$("#"+_self.containerId+_self.options._gridPhieuthuocDetail)[0].toggleToolbar(); 
			}
        	
    		var _sql_par=[];
    		//START -- HISL2NT-865 -- hongdq --20180327
    		if(_self.options._modeViewNT != null && _self.options._modeViewNT == "1"){
    			_sql_par=RSUtil.buildParam("",[_self.options._khambenhid,_self.options._benhnhanid,_self.options._lnmbp,_self.options._hosobenhanid, _self.options._dichvuKhambenhID]);
    			GridUtil.loadGridBySqlPage(_self.containerId+this.options._grdPhieuthuoc,"NT.024.DSTHUOCVT_NT",_sql_par);
    		}else{
    			var _hsbaid = "";
    			if($("#hidHOSOBENHANID").val())
    				_hsbaid = $("#hidHOSOBENHANID").val()
    			else 
    				_hsbaid = _self.options._hosobenhanid;
    			_sql_par=RSUtil.buildParam("",[_self.options._khambenhid,_self.options._benhnhanid,_self.options._lnmbp+';'+_self.options._loaitvt+';'+_self.options._dichvuKhambenhID,_hsbaid]);//L2PT-20784
    			GridUtil.loadGridBySqlPage(_self.containerId+this.options._grdPhieuthuoc,_look_sql,_sql_par);
    		}
    		//END -- HISL2NT-865 -- hongdq --20180327
    		
    		_self._initPopUpHoanHuy();  //L2PT-24256
    		//BVTM-1487 BVTM-4728 L2PT-30263
    		if(cfObj.KETHUOC_KYCA == '1' || (cfObj.KETHUOC_KYCA == '1' && $("#hidLOAITIEPNHANID").val()=='1') ){
    			$("#"+_self.containerId+'divCa').css('display','');
    			$('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('showCol', "KYSO");
	   		}
    		else{//BVTM-5452
    			$('#'+_self.containerId+'viewCA').remove();
    			$('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('hideCol', "KYSO");
    		}
    			
    		//L2PT-6669
    		if(cfObj.TAB_THUOCVT_HIENTHI_PHIEUBU == 1){
    			$('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('showCol', "MAPHIEUBU");
	   		}
    		else
    			$('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('hideCol', "MAPHIEUBU");
        },
        _bindEvent: function() {
			var _self=this;
//			$('#${_self.containerId} a[data-toggle="pill"] , #${_self.containerId} a[data-toggle="tab"], #${_self.containerId} li[role="presentation"] > a').on('shown.bs.tab', function (e) {
//				if ($($(e.currentTarget).attr("href") + " .ui-jqgrid").length> 0) {
//					$(window).trigger('resize');
//				}
//			});
        	GridUtil.setGridParam(_self.containerId+this.options._grdPhieuthuoc,{ 
    	    	onSelectRow: function(id, selected) {
    	    		//L2PT-15599
    	    		if (!selected) {
						return;
					}
    	    		_self._viewPhieuVatTuDetail(id); 
    	    		GridUtil.unmarkAll(_self.containerId+_self.options._grdPhieuthuoc);
    	    		GridUtil.markRow(_self.containerId+_self.options._grdPhieuthuoc,id);
    	    		
    	    		var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', id);    	    		
    	    		$("#hidMAUBENHPHAMID").val(rowData.MAUBENHPHAMID);
    	    		
    	    	},
    	    	 ondblClickRow: function(id) { 
    	    		 //L2PT-23946
    	         	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', id);
    	    		 if(_self.options._modeView == "0" || ( rowData.LOAIKEDONTHUOC == '19' && cfObj.HIS_SHOW_THUOC_MUANGOAI=='1' 
    	    			 && _self.options._khambenhid)){
    	    			 _self._updatePhieuVatTu(id);  
    	    		 }
      			},
      			 gridComplete: function(id){ 
      				 if(_self.options._modeView == "0"){
         	    	   //build menu
         	        	 $(".jqgrow", '#' + _self.containerId+_self.options._grdPhieuthuoc).contextMenu( _self.containerId+'contextMenu', {    	                
         	                 onContextMenu: function (event, menu) {
         	                     var rowId = $(event.target).parent("tr").attr("id")
         	                     var grid = $('#' + _self.containerId+_self.options._grdPhieuthuoc);
         	                     	//L2PT-16134
	         	                    if (cfObj.TABTHUOC_HIENTHI_CHECKBOX == '1') {
										var rowDatas = $('#' + _self.containerId + _self.options._grdPhieuthuoc).jqGrid("getGridParam", "selarrrow");
										var check = true;
										if (rowDatas.length > 0) {
											for (var j = 0; j < rowDatas.length; j++) {
												if (rowDatas[j] == rowId) {
													check = false;
												}
											}
											if (check) {
												grid.setSelection(rowId);
											}
										} else {
											grid.setSelection(rowId);
										}
									} else {
										grid.setSelection(rowId);
									}
									return true;
         	                 },
         	             });  	        		          	        	 
         	        	if (!_self.options.checkLoad) {
							_self.options.checkLoad = true;
	         	        	 //xu ly su kien gui yeu cau
	        	        	 $('#'+_self.containerId+'sentRequest').bindOnce("click",function(){
	        	        		 //L2PT-31551
	        	        		 if(cfObj.TABTHUOC_HIENTHI_CHECKBOX == 1){
	        	        			 _self._sendRequestCheck();
	        	        		 }
	        	        		 else{
	        	        			 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	            	        		 _self._sendRequest(rowKey);
	        	        		 }
	        	        	 },1500);
	        	        	 
	        	        	 //xu ly su kien trả thuốc tây y.
	        	        	 $('#'+_self.containerId+'tradonthuoc').bindOnce("click",function(){
	        	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	        	        		 _self._phieuTraThuoc(rowKey);
	        	        	 },1500);
	        	        	 
	        	        	 //xu ly su kien xoa yeu cau
	        	        	  $('#'+_self.containerId+'deleteRequest').bindOnce("click",function(){
	        	        		//L2PT-31551
	         	        		 if(cfObj.TABTHUOC_HIENTHI_CHECKBOX == 1){
	         	        			 _self._deleteRequestCheck(rowKey);
	         	        		 }
	         	        		 else{
	         	        			 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	            	        		 _self._deleteRequest(rowKey,1);
	         	        		 }
	        	        	 },1500);
	        	        	  
	         	        	 //xu ly su kien xoa
	         	        	 $('#'+_self.containerId+'delete').bindOnce("click",function(){
	         	        		//L2PT-31551
	         	        		 if(cfObj.TABTHUOC_HIENTHI_CHECKBOX == 1){
	         	        			 _self._deletePhieuThuocVatTuCheck(rowKey);
	         	        		 }
	         	        		 else{
	         	        			 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	 	         	        		_self._deletePhieuThuocVatTu(rowKey);  
	         	        		 }
	         	        		       	        		 
	         	        	 },1500);
	         	        	 
	         	        	  //cập nhật phiếu thuốc
	         	        	 $('#'+_self.containerId+'updatePHIEUTHUOC').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		_self._updatePhieuVatTu(rowKey);
	         	        	 },1500);
	         	        	 
	         	        	 //Tao ban sao
	         	        	 $('#'+_self.containerId+'copyNote').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		_self._copyPhieuThuocVatTu(rowKey);
	         	        	 },1500);
	         	        	 
	         	        	 //Tao chi dinh la pheu di kem
	         	        	 $('#'+_self.containerId+'chidinhLPDK').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		_self._createNoteAttach(rowKey);
	         	        	 },1500);
	         	        	 
	         	        	//cap nhat phong chi dinh
	         	        	 $('#'+_self.containerId+'editOrg').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		_self._editOrgDone(rowKey);
	         	        	 },1500);
	         	        	 //L2K74TW-301 - hongdq
	         	        	//cap nhat phieu dieu tri
	         	        	 $('#'+_self.containerId+'editPhieuDT').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		_self._editPhieuDT(rowKey);
	         	        	 },1500);
	         	        	 //L2K74TW-301 -hongdq
	         	        	 //L2PT-45654
	         	        	 $('#'+_self.containerId+'demSttKs').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		_self._countSttKS(rowKey);
	         	        	 },1500);
	         	        	//In don thuoc
	         	        	 $('#'+_self.containerId+'print').bindOnce("click",function(){
	         	        		var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		if(_self.options._indtringttrongnghanh != '1'){
	         	        			_self._inDonThuoc(rowKey);
	         	        		 }else{
	         	        			_self._inDonThuocTrongNghanh(rowKey);
	         	        		 }
	         	        	 },1500);
	         	        	 
	         	        	//In phieu kham khong toa L2PT-27971
	         	        	 $('#'+_self.containerId+'printPKKT').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		_self._inPKKT(rowKey);
	         	        	 },1500);
	         	        	//In phieu phat thuoc L2PT-11555
	         	        	 $('#'+_self.containerId+'printPhatThuoc').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		_self._inPhieuPhatThuoc(rowKey);
	         	        	 },1500);
	         	        	 
	         	        	//L2PT-72762
		     	        	$('#'+_self.containerId+'printThuocMulti').bindOnce("click",function(){
		    	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
		    	        		_self._indonthuocCheck(rowKey);
		    	        	 },1500);
	         	        	 
	         	        	// CA start
							 $('#'+_self.containerId+'kyCA1').bindOnce("click",function(){
								 if(cfObj.TABTHUOC_HIENTHI_CHECKBOX == 1){
									//var _self = this;
								 	//L2PT-28091
									var _rptCode = 'NGT006_DONTHUOC_17DBV01_TT052016_A5';
									var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
									var catype = '';
									var title = '';
									if (data_ar != null && data_ar.length > 0) {
										var row = data_ar[0];
										catype = row.CA_TYPE;
										title = row.KIEUKY;
									}
									if(catype == '5'){
										var selRows = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getGridParam', 'selarrrow');
							    		if(selRows.length == 0){
							    			DlgUtil.showMsg("Chọn ít nhất 1 phiếu!");
							    			return;
							    		}
							    		var check = 0;
							    		for (var i = 0; i < selRows.length; i++) {
							    			rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
							    			if(rowData.FLAG_CA == '1'){	
							    				DlgUtil.showMsg("Chỉ chọn những phiếu chưa ký số!");
							        			return;
							    			}
							    		}
							    		var sophieu = "";
							    		lstParamKyCa = [];
							    		for (var i = 0; i < selRows.length; i++) {
//							    			rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
//							    			if(rowData != null){
							    				_self._kyCa(selRows[i],0);
//							            	}
							    		}
							    		
										CommonUtil._caRptTocken(lstParamKyCa, '1', null, true,title);
										checkky = 0;
										EventUtil.setEvent("eventKyCA", function (e) {
											var msg = e.res;
											var _code = msg.split("|")[0];
											var _msg = msg.split("|")[1];
											var _caid = msg.split("|")[2];
											DlgUtil.showMsg(_msg);
											if (_code == '0' || _code == '7' || _code == '8') {
												for (var i = 0; i < selRows.length; i++) {
									    			rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
									    			if(rowData != null){
									    				if(rowData.DAYDTDT != '1' && (cfObj.DTDT_DAY_DONTHUOC == '2' || cfObj.DTDT_DAY_DONTHUOC == '3') && checkky == 0)
															_dayDonThuocOnline_new('2',rowData.MAUBENHPHAMID,rowData.OPTIONS,$("#hidLOAITIEPNHANID").val(),"", "Add",'99999999','99999999',catype);
									            	}
									    		}
											}
										});
									}else{
										 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
										 _self._kyCa(rowKey,0);
									}
	        	        		 }
	        	        		 else{
	        	        			 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
									 _self._kyCa(rowKey,0);
									//L2PT-43073
									   if (lstParamPrintCa.length > 0 && cfObj.KETHUOC_IN_KYSO_CHUNG == '1' ) {
										   CommonUtil.openReportGetCA2(lstParamPrintCa, false);
										   lstParamPrintCa = [];
									   }
	        	        		 }
								 
							 },1500);
							 //L2PT-15789
							 $('#'+_self.containerId+'kyCATrongNganh').bindOnce("click",function(){
								 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
								 _self._kyCa(rowKey,1);
							 },1500);
							 $('#'+_self.containerId+'huyKyCA1').bindOnce("click",function(){
								 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
								 _self._huyKyCA(rowKey);
							 },1500);
							 // view ky ca
							 $('#'+_self.containerId+'viewCA1').bindOnce("click",function(){
								 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
								 _self._viewCA(rowKey);
								//L2PT-43073
							   if (lstParamPrintCa.length > 0 && cfObj.KETHUOC_IN_KYSO_CHUNG == '1' ) {
								   CommonUtil.openReportGetCA2(lstParamPrintCa, false);
								   lstParamPrintCa = [];
							   }
							 },1500);
	         	        	 
	         	        	//In don thuoc
	         	        	$('#'+_self.containerId+'printPT').bindOnce("click",function(){
	        	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	        	        		_self._tuDongInDonThuoc(rowKey);
	        	        	 },1500);
	         	        	
	         	        	 
	         	        	//L2PT-13999
	         	        	 $('#'+_self.containerId+'sendCong').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		 var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowKey);
			         	         if(rowData != null){
			         	        	_self._sendCong(rowData.MAUBENHPHAMID,'',rowData.OPTIONS);
			         	          }
	         	        	 },1500);
	         	        	 //L2PT-13999
	         	        	//L2PT-13999
	         	        	 $('#'+_self.containerId+'sendCongCA').bindOnce("click",function(){
	         	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	         	        		 var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowKey);
			         	         if(rowData != null){
			         	        	_self._sendCongCA(rowData.MAUBENHPHAMID,'',rowData.OPTIONS);
			         	          }
	         	        	 },1500);
         	        	}
         	        	 //L2PT-13999
      				 }else  if(_self.options._modeView == "1"){
      					 $(".jqgrow", '#' + _self.containerId+_self.options._grdPhieuthuoc).contextMenu( _self.containerId+'contextMenuPrint', {    	                
         	                 onContextMenu: function (event, menu) {
         	                     var rowId = $(event.target).parent("tr").attr("id")
         	                     var grid = $('#' + _self.containerId+_self.options._grdPhieuthuoc);
         	                     grid.setSelection(rowId);
         	                     return true;
         	                 },
         	             });
      					if (!_self.options.checkLoad) {
							_self.options.checkLoad = true;
		  					//In don thuoc
		     	        	 $('#'+_self.containerId+'printView').bindOnce("click",function(){
		     	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
		     	        		 if(_self.options._indtringttrongnghanh != '1'){
		     	        			_self._inDonThuoc(rowKey);
		     	        		 }else{
		     	        			_self._inDonThuocTrongNghanh(rowKey);
		     	        		 }
		     	        	 },1500);
		     	        	 
		     	        	 //L2PT-27971
		     	        	$('#'+_self.containerId+'printPKKTView').bindOnce("click",function(){
		    	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
		    	        		_self._inPKKT(rowKey);
		    	        	 },1500);
		     	        	
		     	        	 //L2PT-11555
		     	        	$('#'+_self.containerId+'printPhatThuocV').bindOnce("click",function(){
		    	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
		    	        		_self._inPhieuPhatThuoc(rowKey);
		    	        	 },1500);
		     	        	//L2PT-72762
		     	        	$('#'+_self.containerId+'printThuocMultiV').bindOnce("click",function(){
		    	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
		    	        		_self._indonthuocCheck(rowKey);
		    	        	 },1500);
		     	        	 
		     	        	// ky ca
							 $('#'+_self.containerId+'kyCA').bindOnce("click",function(){
								 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
								 _self._kyCa(rowKey);
							 },1500);
							 // view ky ca BVTM-5452
							 $('#'+_self.containerId+'viewCA').bindOnce("click",function(){
								 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
								 _self._viewCA(rowKey);
							 },1500);
							 
							 //L2PT-38219
							 $('#'+_self.containerId+'editView').bindOnce("click",function(){
								 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
								 
								 var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowKey);
								 if(rowData && rowData.OPTIONS == '02D011'){
									 var rs = _self._deleteRequest(rowKey,0); 
		    	       				 if(rs == 1) return;
		    	       				 _self._updatePhieuVatTu(rowKey);
								 }
								 else
									 DlgUtil.showMsg('BN đã kết thúc khám chỉ thao tác với đơn mua ngoài');
							 },1500);
							 $('#'+_self.containerId+'xoaPhieuView').bindOnce("click",function(){
								 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
								 var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowKey);
								 if(rowData && rowData.OPTIONS == '02D011')
									 _self._deletePhieuThuocVatTu(rowKey);  
								 else
									 DlgUtil.showMsg('BN đã kết thúc khám chỉ thao tác với đơn mua ngoài');
							 },1500);
							 
		     	        	 
		     	        	//In don thuoc
		     	        	 $('#'+_self.containerId+'printPhieu').bindOnce("click",function(){
		     	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
		     	        		_self._tuDongInDonThuoc(rowKey);
		     	        	 },1500);
		     	        	//L2PT-13999
		     	        	 $('#'+_self.containerId+'sendCongView').bindOnce("click",function(){
		     	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
		     	        		 var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowKey);
			         	         if(rowData != null){
			         	        	_self._sendCong(rowData.MAUBENHPHAMID,'',rowData.OPTIONS);
			         	          }
		     	        	 },1500);
		     	        	$('#'+_self.containerId+'sendCongCAView').bindOnce("click",function(){
		    	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
		    	        		 var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowKey);
			         	         if(rowData != null){
			         	        	_self._sendCongCA(rowData.MAUBENHPHAMID,'',rowData.OPTIONS);
			         	          }
		    	        	 },1500);
      					}
		     	     //L2PT-13999
      				 } else if(_self.options._modeView == "2"){
      					if (!_self.options.checkLoad) {
							_self.options.checkLoad = true;
	      					$(".jqgrow", '#' + _self.containerId+_self.options._grdPhieuthuoc).contextMenu( _self.containerId+'contextMenuDTKH', {    	                
	        	                 onContextMenu: function (event, menu) {
	        	                     var rowId = $(event.target).parent("tr").attr("id")
	        	                     var grid = $('#' + _self.containerId+_self.options._grdPhieuthuoc);
	        	                     grid.setSelection(rowId);
	        	                     return true;
	        	                 },
	        	             });
		      				//xu ly su kien gui yeu cau
		       	        	 $('#'+_self.containerId+'sentRequestDtkh').bindOnce("click",function(){
		       	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
		       	        		 _self._sendRequest(rowKey);
		       	        	 },500)
	        	        	 //xu ly su kien xoa
	        	        	 $('#'+_self.containerId+'deleteDtkh').bindOnce("click",function(){
	        	        		 var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
	        	        		_self._deletePhieuThuocVatTu(rowKey);         	        		 
	        	        	 },500) 
      					}
	      			}
      				 //set anh	
      				_self._setImageStatus();
      				_setFocusMauBenhPham($("#hidMAUBENHPHAMID").val(),_self.containerId+_self.options._grdPhieuthuoc);
      				var showCopyThuoc = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SHOW_COPY_PHIEUTHUOC');
      			    if($("#hidLOAITIEPNHANID")!=undefined && $("#hidLOAITIEPNHANID")!=null
     	        			 && showCopyThuoc!=undefined && showCopyThuoc!=null){
     	        		 if($("#hidLOAITIEPNHANID").val()=='1' && showCopyThuoc=='0'){      	        			 
     	        			$("#"+_self.containerId+"copyNote").remove();         	        			
     	        		 }
     	        	 }  
         	       }	
            });   
        	GridUtil.setGridParam(_self.containerId+this.options._gridPhieuthuocDetail,{ 
    	    	onSelectRow: function(id, selected) {    	    		
    	    		GridUtil.unmarkAll(_self.containerId+_self.options._gridPhieuthuocDetail);
    	    		GridUtil.markRow(_self.containerId+_self.options._gridPhieuthuocDetail,id);
    	    	}
        	});
        	
			GridUtil.setGridParam(_self.containerId+this.options._gridPhieuthuocDetail,{ 
				ondblClickRow: function(id) { 
					 if(_self.options._modeView == "0"){
						 _self._CapNhatPhuTroiThuoc(id);  
					 };
				 },
				 gridComplete: function(id){ 
					 var _capphutroi = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NTU_CAPTHUOC_PHUTROI');
					 //tuyennx_edit_start_20190418 L1PT-464 
					 if(_self.options._modeView == "0"){
						 $(".jqgrow", '#' + _self.containerId+_self.options._gridPhieuthuocDetail).contextMenu( _self.containerId+'contextPhuTroi', {    	                
							 onContextMenu: function (event, menu) {
							     var rowId = $(event.target).parent("tr").attr("id");
							     var grid = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail);
				                 grid.setSelection(rowId);
				                 return true;
							 },
						 });  	        		          	        	 
					 };
					 if(_capphutroi != 1){
						 $('#'+_self.containerId+'bosungphutroi').css({"pointer-events":"none","opacity":"0.6"});
					 }
					 //L2PT-10776
					 if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_HOICHAN_THUOCTT30')!= 1){
						 $('#'+_self.containerId+'thembbkhangsinh').css({"pointer-events":"none","opacity":"0.6"});
					 }
					 //tuyennx_add_start L2PT-31534
					 var ids = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).getDataIDs();
   		        	 for(var i=0;i<ids.length;i++){
   		        		var id = ids[i];
   		        		var rowDataCd = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('getRowData', id);
   		        		if(rowDataCd.TRANGTHAI_DUYET_NGT == '2'){
   		        			$('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('setRowData', id, "", {
   								color : 'red'
   							});
   		        			
   		        		}

   			       		//L2PT-21839
   			       		if(rowDataCd.CHOLANHDAODUYET != '0' && cfObj.TABTHUOC_THUOCKS_CHECKMAU == '1'){
   			       			$('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('setRowData', id, "", {
   									color : '#1a75ff'
   								});
   			       		}
   			       		//L2PT-51834 chi up test demo
   			       		if(rowDataCd.TRANGTHAI_TT && rowDataCd.TRANGTHAI_TT != '0'){
		        			$('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('setRowData', id, "", {
								color : 'red'
							});
		        			
		        		}
   		        	 }
	        		//tuyennx_add_end L2PT-31534
	        		
					 var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH",'HIS_BACSY_YCHUY_DV');
						if(data_ar != null && data_ar.length > 0){
							if(data_ar[0].HIS_BACSY_YCHUY_DV == '1'){
								var ids = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).getDataIDs();
		   		        	 for(var i=0;i<ids.length;i++){
		   		        		  var id = ids[i];
		   		        		var rowDataCd = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('getRowData', id);
		   		        		if(rowDataCd.YC_HOAN == '1'){
		   		        			$('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('setRowData', id, "", {
		   								color : 'red'
		   							});
		   		        			$('#' + _self.containerId+_self.options._gridPhieuthuocDetail).setCell(id,'MADICHVU','',{'text-decoration': 'line-through'});
		   		        			$('#' + _self.containerId+_self.options._gridPhieuthuocDetail).setCell(id,'TENDICHVU','',{'text-decoration': 'line-through'});
		   		        		}
		   		        	 }
							}
						}
					
				 },
			 });  
			$('#'+_self.containerId+'sendRequestDeleteReject').bindOnce("click",function(){
	       		 var rowKey = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).getGridParam("selrow");
	       		 _self._sendRequestDeleteReject(rowKey);
	       	 },1500);
			$('#'+_self.containerId+'undoRequestDeleteReject').bindOnce("click",function(){
	       		 var rowKey = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).getGridParam("selrow");
	       		 _self._undoRequestDeleteReject(rowKey);
	       	 },1500);
			//tuyennx_edit_end_20190418 L1PT-464 
        	
        	$('#'+_self.containerId+'bosungphutroi').bindOnce("click",function(){
	       		 var rowKey = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).getGridParam("selrow");
	       		 _self._CapNhatPhuTroiThuoc(rowKey);
	       	 },1500);
        	//L2PT-10776
        	$('#'+_self.containerId+'thembbkhangsinh').bindOnce("click",function(){
	       		 var rowKey = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).getGridParam("selrow");
	       		 _self._insertPhieuKS(rowKey);
	       	 },1500);
        	
        	//L2PT-55213
        	$('#'+_self.containerId+'capnhatcachdung').bindOnce("click",function(){
	       		 var rowKey = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).getGridParam("selrow");
	       		 _self._capnhatcachdung(rowKey);
	       	 },1500);
        	
        	//tuyennx_add_start_20200203 L2PT-14996
        	$('#'+_self.containerId+'nhapslhong').bindOnce("click",function(){
	       		 var rowKey = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).getGridParam("selrow");
	       		 _self._capNhatSLHong(rowKey);
	       	 },1500);
        	//tuyennx_add_end_20200203 L2PT-14996
        	//tuyennx_add_start_20200203 L2PT-24256
        	$('#'+_self.containerId+'hoantraxuathuy').bindOnce("click",function(){
	       		 var rowKey = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).getGridParam("selrow");
	       		 _self._hoanHuy(rowKey);
	       	 },1500);
        	//tuyennx_add_end_20200203 L2PT-24256
        	
        	//tuyennx_add_start HISL2TK-544
        	GridUtil.setGridParam(_self.containerId+this.options._grdChiTietThuoc1Sao,{ 
    	    	 ondblClickRow: function(id) { 
    	    		 if(_self.options._modeView == "0"){
    	    			 _self._updatePhieu1Sao(id);  
    	    		 };
      			},
      			 gridComplete: function(id){ 
      				 //if(_self.options._modeView == "0"){ L2PT-12837
         	    	   //build menu
         	        	 $(".jqgrow", '#' + _self.containerId+_self.options._grdChiTietThuoc1Sao).contextMenu( _self.containerId+'contextMenu1Sao', {    	                
         	                 onContextMenu: function (event, menu) {
         	                     var rowId = $(event.target).parent("tr").attr("id");
         	                     var grid = $('#' + _self.containerId+_self.options._grdChiTietThuoc1Sao);
         	                     grid.setSelection(rowId);
         	                     return true;
         	                 },
         	             });  	        		          	        	 
         	       //};
      			 },
            });  
	       	 $('#'+_self.containerId+'inDon1Sao').bindOnce("click",function(){
	       		 var rowKey = $('#' + _self.containerId+_self.options._grdChiTietThuoc1Sao).getGridParam("selrow");
	       		 _self._inDon1Sao(rowKey);
	       	 },1500);
        	GridUtil.setGridParam(_self.containerId+this.options._grdChiTietThuoc2Sao,{ 
   	    	 ondblClickRow: function(id) { 
   	    		 if(_self.options._modeView == "0"){
   	    			 _self._updatePhieu2Sao(id);  
   	    		 };
     			},
     			 gridComplete: function(id){ 
     				 if(_self.options._modeView == "0"){
        	    	   //build menu
        	        	 $(".jqgrow", '#' + _self.containerId+_self.options._grdChiTietThuoc2Sao).contextMenu( _self.containerId+'contextMenu2Sao', {    	                
        	                 onContextMenu: function (event, menu) {
        	                     var rowId = $(event.target).parent("tr").attr("id");
        	                     var grid = $('#' + _self.containerId+_self.options._grdChiTietThuoc2Sao);
        	                     grid.setSelection(rowId);
        	                     return true;
        	                 },
        	             });  	        		          	        	 
        	       };
     			 },
           });   
        	$('#'+_self.containerId+'inDon2Sao').bindOnce("click",function(){
	        		 var rowKey = $('#' + _self.containerId+_self.options._grdChiTietThuoc2Sao).getGridParam("selrow");
	        		 _self._inDon2Sao(rowKey);
	        },1500);
        	
        	//L2PT-107978
        	GridUtil.setGridParam(_self.containerId+this.options._grdChiTietThuocPT,{ 
     	    	 ondblClickRow: function(id) { 
     	    		 if(_self.options._modeView == "0"){
     	    			 _self._updatePhieuPT(id);  
     	    		 };
       			},
       			gridComplete: function(id){ 
    				 //if(_self.options._modeView == "0"){ //L2PT-11135 //L2PT-15516
       	    	   //build menu
       	        	 $(".jqgrow", '#' + _self.containerId+_self.options._grdChiTietThuocPT).contextMenu( _self.containerId+'contextMenuPT', {    	                
       	                 onContextMenu: function (event, menu) {
       	                     var rowId = $(event.target).parent("tr").attr("id");
       	                     var grid = $('#' + _self.containerId+_self.options._grdChiTietThuocPT);
       	                     grid.setSelection(rowId);
       	                     return true;
       	                 },
       	             });  	        		          	        	 
       	       //};
    			 },
             });  
        	$('#'+_self.containerId+'inDonPT').bindOnce("click",function(){
        		//var _self=this;
        		var rowKey = $('#' + _self.containerId+_self.options._grdChiTietThuocPT).getGridParam("selrow");
            	var rowData = $('#' + _self.containerId+_self.options._grdChiTietThuocPT).jqGrid('getRowData', rowKey);
            	 if(rowData != null){      	
      					var par = [ {name:'dichvukhambenhid',
      	        			type:'String',
      	        			value:rowData.DICHVUKHAMBENHID}];
      					openReport('window', 'RPT_PHANTANG_NGUYCO_THUOC', 'pdf', par);   
            	 }
          		 _self._inDonKS(rowKey);
           	},1500);
        	
        	
        	//tuyennx_add_start L2PT-5682
        	GridUtil.setGridParam(_self.containerId+this.options._grdChiTietThuocKS,{ 
      	    	 ondblClickRow: function(id) { 
      	    		 if(_self.options._modeView == "0"){
      	    			 _self._updatePhieuKS(id);  
      	    		 };
        			},
        		 gridComplete: function(id){ 
         				 //if(_self.options._modeView == "0"){ //L2PT-11135 //L2PT-15516
            	    	   //build menu
            	        	 $(".jqgrow", '#' + _self.containerId+_self.options._grdChiTietThuocKS).contextMenu( _self.containerId+'contextMenuKS', {    	                
            	                 onContextMenu: function (event, menu) {
            	                     var rowId = $(event.target).parent("tr").attr("id");
            	                     var grid = $('#' + _self.containerId+_self.options._grdChiTietThuocKS);
            	                     grid.setSelection(rowId);
            	                     return true;
            	                 },
            	             });  	        		          	        	 
            	       //};
         			 },
              });  
        	
        	$('#'+_self.containerId+'inDonKS').bindOnce("click",function(){
       		 var rowKey = $('#' + _self.containerId+_self.options._grdChiTietThuocKS).getGridParam("selrow");
       		 _self._inDonKS(rowKey);
        	},1500);
        	
        	//L2PT-15516
        	$('#'+_self.containerId+'inDonKSKySo').bindOnce("click",function(){
          		 var rowKey = $('#' + _self.containerId+_self.options._grdChiTietThuocKS).getGridParam("selrow");
          		 _self._inDonKSKySo(rowKey);
           	},1500);
        	
        	//tuyennx_add_end
        	
        	//tuyennx_add_end
        	
        },
        _CapNhatPhuTroiThuoc : function(rowId){
        	var _self=this;
        	 var rowData = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('getRowData', rowId);
	       	 if(rowData != null){
				 par={					
						 DICHVUKHAMBENHID : rowData.DICHVUKHAMBENHID,
						 KHOAID			  : _self.options._khoaid,
						 DOITUONGBN		  : _self.options._doituongbn,
						 SOLUONG		  : rowData.SOLUONG
    			 }; 
	       	 }
			dlgPopup=DlgUtil.buildPopupUrl("dlgBoSungPhuTroi","divDlg","manager.jsp?func=../noitru/NTU02D099_BoSungPhuTroi",par,"Bổ sung phụ trội thuốc",500,300);
			DlgUtil.open("dlgBoSungPhuTroi");
			
			EventUtil.setEvent("assignDrug_capphutroi", function(e) {
				DlgUtil.close('dlgBoSungPhuTroi');
				DlgUtil.showMsg('Cập nhật phụ trội thành công');
				var ids = $("#"+_self.containerId+_self.options._grdPhieuthuoc).getDataIDs();
		      	  for(var i=0;i<ids.length;i++){
		      		var id = ids[i];      		  
			          var row = $("#"+ _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData',id);
		      		  if(row.MAUBENHPHAMID == e.option){
		      			$('#'+_self.containerId+_self.options._grdPhieuthuoc).find("tr[id='" + id + "']").find('td').trigger( "click" );
		      		  }
		      	  }
			});
        },
        _setImageStatus: function(){
      	  var _self=this;        	  
      	  var ids = $("#"+_self.containerId+_self.options._grdPhieuthuoc).getDataIDs();
      	  for(var i=0;i<ids.length;i++){
      		  //set trang thai phieu
  		      var id = ids[i];      		  
	          var row = $("#"+ _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData',id);
	          var _icon = '';		          
	          var _trangthai=parseInt(row.TRANGTHAIMAUBENHPHAM);
	        	if(_trangthai == 2){
	        		_icon = '<center><img src="../common/image/Circle_Yellow.png" width="15px"></center>';
	        	}else if(_trangthai == 6){
	        		_icon = '<center><img src="../common/image/Circle_Green.png" width="15px"></center>';
	        	}else if(_trangthai == 4){
	        		_icon = '<center><img src="../common/image/Circle_Red.png" width="15px"></center>';
	        	} 	            
				$("#"+ _self.containerId+_self.options._grdPhieuthuoc).jqGrid ('setCell', id, 'ICON', _icon); //L2PT-31551
			  //set di kem
				var _iconDikem='';
				var _dikem=parseInt(row.DIKEM);					
				if(_dikem==1){
					_iconDikem='<center><img src="../common/image/Pin.png" width="15px"></center>';
				}					
				$("#"+ _self.containerId+_self.options._grdPhieuthuoc).jqGrid ('setCell', id, 'DIKEM1', _iconDikem); //L2PT-6474
				
				 //tuyennx_add_start L2PT-31534
	       		if(row.TRANGTHAI_DUYET_NGT == '2'){
	       			$('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('setRowData', id, "", {
							color : 'red'
						});
	       		}
	       		//tuyennx_add_end L2PT-31534
	       		
	       		//L2PT-21839
	       		if(row.CHOLANHDAODUYET != '0' && cfObj.TABTHUOC_THUOCKS_CHECKMAU == '1'){
	       			$('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('setRowData', id, "", {
							color : '#1a75ff'
						});
	       		}
	       		
	       		//L2PT-53518
	       		if(cfObj.TABTHUOC_PHIEUTRA_MAU == '1'){
	       			if(row.LOAIPHIEUMAUBENHPHAM == '2'){
		       			$('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('setRowData', id, "", {
								color : 'red'
							});
		       		}
		       		if(row.LOAIPHIEUMAUBENHPHAM == '2' && row.TRANGTHAIMAUBENHPHAM == '6'){
		       			$('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('setRowData', id, "", {
								color : 'Lime'
							});
		       		}
	       		}

	       		
	       		//tuyennx_add_start BVTM-4728
	       		if(row.FLAG_CA == '1'){
	       			_iconKySo ='<center><img src="../common/image/ca.png" width="15px"></center>';
	       			$("#"+ _self.containerId+_self.options._grdPhieuthuoc).jqGrid ('setCell', id, 'KYSO', _iconKySo);
	       		}
	       		//tuyennx_add_end BVTM-4728
      	  }	 
      },
      _inDonThuocTrongNghanh:function(rowId){
		  var _self=this;
    	  var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
    	  var _report_code = "NGT006_DONTHUOCGOP_17DBV01_TT052016_A5";
    	  if(rowData.LOAIKHO == '5'){
    		  _report_code = "NGT020_DONTHUOCTHANGNGOAITRU";
    	  } 
	    	var par1=[];
    		par1.push(
				{name : 'maubenhphamid',type : 'String',value : rowData.MAUBENHPHAMID}
			);
			openReport('window', _report_code, "pdf", par1);
			return;	    
      },
      _inDonThuoc:function(rowId){
        	var _self=this;
        	var hopital=$("#hidHisId").val(); 
        	//tuyennx_add_start_20190424 L1PT-664
    		var hopital_id=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID'); 
    		if(hopital_id!= null && hopital_id!=0) {
    			hopital = hopital_id;
    		} 
    		//tuyennx_add_end_20190424 L1PT-664
        	var doituongbenhnhanid = $("#hidDOITUONGBENHNHANID").val(); 
        	 //var his_id_913 = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","IN_THUOC_913");
        	var user= $("#hidUserID").val();
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	 if(rowData != null){
        		//L2PT-21906
     			if(cfObj.KETHUOC_TAT_HAOPHI == '1'){ 
     				 var sql_par1 = [];
     				 sql_par1.push({"name":"[0]", value:rowData.MAUBENHPHAMID});
     				 var check_hp = jsonrpc.AjaxJson.getOneValue('KETHUOC.IN.HAOPHI', sql_par1);
     				 if(check_hp == '0')
     					 return;
     			}
     			
        		 var par = [ {
       				name : 'maubenhphamid',
       				type : 'String',
       				value : rowData.MAUBENHPHAMID
       			 }];   
        		//tuyennx_add_start_20181113 L2HOTRO-12722
        		 if(rowData.PHONGID != $('#hidPHONGID').val() && jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","INDONTHUOC_THEOPHONG") ==1){
        			 DlgUtil.showMsg('Chỉ được in đơn thuốc kê tại phòng khám hiện tại');
        			 return;
        		 }
        		//tuyennx_add_end L2HOTRO-11841
        		 //L2PT-22921
        		 if(rowData.TRANGTHAIMAUBENHPHAM == '1' && cfObj.TABTHUOC_CHANIN_SUAPHIEU == '1'){
        			 DlgUtil.showMsg('Không thể in đơn thuốc có trạng thái đang sửa phiếu');
        			 return;
        		 }
        		 
        		//L2PT-4359 in gop don thuoc nha thuoc
    		    if(rowData.LOAIKEDONTHUOC == '20' && jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","KETHUOC_INTACH_NHATHUOC") ==1){ 
    		    	var par1=[];
    		    	//L2PT-16969
		    		if(cfObj.KETHUOC_NHATHUOC_INGOP1DON == '1'){
		    			par1.push({
							name : 'list_maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						});
		    		}
		    		else{
		    			par1.push({
							name : 'maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						});
		    		}
		    		
    				openReport('window', "NGT006_DONTHUOC_NHATHUOC", "pdf", par1);
    				return;
    		    }
        		    
        		 //tuyennx_add_start_20181113 L2PT-2864
        		 if(rowData.DINHDUONG == '1'){
        			 	var par = [ {
							name : 'maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						}];
        			 	var rpcode = "NGT006_DONTUVAN_17DBV01_TT052016_A5";
        				var hopital=$("#hidHisId").val();  
        	        	//tuyennx_add_start_20190424 L1PT-664
        	    		var hopital_id=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID'); 
        	    		if(hopital_id!= null && hopital_id!=0) {
        	    			hopital = hopital_id;
        	    		} 
        	    		//tuyennx_add_end_20190424 L1PT-664
 					   if(hopital=='951'){
 							rpcode = "PHIEUTUVAN_A5_951";
 						}
 					   openReport('window', rpcode, "pdf", par);
        			 return;
        		 }
        		//tuyennx_add_end L2HOTRO-11841
        		 //tuyennx_add_start_20181113 L2PT-9849
        		 var _loaithuoc=0;
        		 var _par_loai = [rowData.MAUBENHPHAMID];						
				 var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
				 
        		 if(rowData.LOAIKEDONTHUOC == '21'){
        			 	var par = [ {
							name : 'maubenhphamid',
							type : 'String',
							value : rowData.MAUBENHPHAMID
						}];
//        			 	var rpcode = "NGT006_DONRAVIEN_17DBV01_TT052016_A5";
// 					   openReport('window', rpcode, "pdf", par);
 					   
 					  if (arr_loaithuoc != null && arr_loaithuoc.length > 0) { // in don thuoc thuong
 							for(var i=0;i< arr_loaithuoc.length;i++){
 								_loaithuoc=arr_loaithuoc[i].LOAI;
 								   if(_loaithuoc==3){
 										//thuoc dong y --DONTHUOCTHANG_NGOAITRU --L2PT-21948
 									    CommonUtil.openReportGet('window', "NGT020_DONTHUOCTHANG_RAVIEN_NGOAITRU", "pdf", par); 
 									 }else if(_loaithuoc==6){
 										 //thuoc huong than 
 										 CommonUtil.openReportGet('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par); 
 									 }else if(_loaithuoc==7){
 										 //don thuoc gay nghien
 										 CommonUtil.openReportGet('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par);
 									 }
 									 else if(_loaithuoc==-1){ //L2PT-21559
										 //don thuoc chuong trinh
										 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5", "pdf", par);
									 }
 									 else{
 										 CommonUtil.openReportGet('window', "NGT006_DONRAVIEN_17DBV01_TT052016_A5", "pdf", par);
 									 }
 							}
 					  }
        			 return;
        		 }
        		//tuyennx_add_end L2PT-9849
        		//tuyennx_add_start_20181113 L2HOTRO-11841
        		 if(rowData.DONTHUOCPHU == '1'){
        			 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_KP", "pdf", par); 
        			 return;
        		 }
        		//tuyennx_add_end L2HOTRO-11841
        		 
        		//tuyennx_add_start_20181113 L2PT-6425 L2PT-49696
        		if((rowData.LOAIKEDONTHUOC == 19 && rowData.LOAIDONTHUOC != '1' && jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'IN_TACHDONMUA_NGOAI')==1) 
        				|| rowData.LOAITHANHTOAN == '4' ){  //L2PT-35459
        			  CommonUtil.openReportGet('window', "NGT006_DONTHUOC_MUANGOAI_A5", "pdf", par);
     		   		return;
     		    }
     		  //tuyennx_add_end_20181113
        	
        		 //lay loai thuoc
     	    	 
				 if (arr_loaithuoc != null && arr_loaithuoc.length > 0) { // in don thuoc thuong
					for(var i=0;i< arr_loaithuoc.length;i++){
						_loaithuoc=arr_loaithuoc[i].LOAI;
						   if(_loaithuoc==3){
								//thuoc dong y --DONTHUOCTHANG_NGOAITRU
							    CommonUtil.openReportGet('window', "NGT020_DONTHUOCTHANGNGOAITRU", "pdf", par); 
							 }else if(_loaithuoc==6){
								 //thuoc huong than 
								 CommonUtil.openReportGet('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par); 
							 }else if(_loaithuoc==7){
								 //don thuoc gay nghien
								 CommonUtil.openReportGet('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par);
							 }
							 //tuyennx_add_start L2PT-13903
							 else if(_loaithuoc==16 || _loaithuoc==19 ){
								 if(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'IN_TACHDON_MP_TPCN') == 1){ //L2PT-30631
									if(_loaithuoc==16){
										CommonUtil.openReportGet('window', 'NGT013_MYPHAM_TT052016_A5', "pdf", par);
									}
									else{
										CommonUtil.openReportGet('window', 'NGT013_TPCN_TT052016_A5', "pdf", par);
									}
								}else
									 //don my pham
									 CommonUtil.openReportGet('window', "NGT013_DONMYPHAM_TPCN_TT052016_A5", "pdf", par);
							 }
						   	//tuyennx_add_end L2PT-13903
						   //tuyennx_add_start L2PT-21559
								else if(_loaithuoc==-1 ){
									 //don my pham
									CommonUtil.openReportGet('window', 'NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5',"pdf", par);
							
								}
							  //tuyennx_add_end L2PT-21559
							 else{
								 
								 var dtmuangoai = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","DT_MUANGOAI");
								 // don thuoc khac
								 if(dtmuangoai==1){
									 var _khothuoc=0;
									 var arr_khothuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC",_par_loai.join('$'));
									 if (arr_khothuoc != null && arr_khothuoc.length > 0) {
										 for(var i2=0;i2< arr_khothuoc.length;i2++){
											 _khothuoc=arr_khothuoc[i2].KHOTHUOCID;
											 if(_khothuoc==1){
												 CommonUtil.openReportGet('window', "NGT006_DONTHUOC1L_17DBV01_TT052016_A5_944", "pdf", par); 
											 }
											 else CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
										 }
									 }		
								 }// them in đơn thuoc mua ngoai 1 liên cho bvbd
								 else if(hopital==965){
									 var _khothuoc=0;
									 var arr_khothuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC",_par_loai.join('$'));
									 if (arr_khothuoc != null && arr_khothuoc.length > 0) {
										 for(var i2=0;i2< arr_khothuoc.length;i2++){
											 _khothuoc=arr_khothuoc[i2].KHOTHUOCID;
											 if(_khothuoc==1){
												 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_MUANGOAI_A5_965", "pdf", par); 
											 }
											 else CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
										 }
									 }
								 }
								 // them in 1 lien cho benh nhan bhyt KHA
								 else if(hopital==919 && doituongbenhnhanid==1){
									 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_3L17DBV01_TT052016_A5_919", "pdf", par);
								 }
								 else if (hopital==915){
									 var _par_dtngt = [rowData.BENHNHANID];	
									 var _dtngt=0;									 
									 var arr_dtngt= jsonrpc.AjaxJson.ajaxCALL_SP_O("CHECK_BENHAN_NGT",_par_dtngt.join('$'));
									 if (arr_dtngt != null && arr_dtngt.length > 0) {
										 for(var i3=0;i3< arr_dtngt.length;i3++){
											 _dtngt=arr_dtngt[i3].BANGT;
											 if(_dtngt==2){
												 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_3L_TT052016_A5_915", "pdf", par); 
											 }
											 else CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
										 }
									 } 
								 }
								 else if(hopital==1014){
									 var _khothuoc=0;
									 var arr_khothuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC",_par_loai.join('$'));
									 if (arr_khothuoc != null && arr_khothuoc.length > 0) {
										 for(var i2=0;i2< arr_khothuoc.length;i2++){
											 _khothuoc=arr_khothuoc[i2].KHOTHUOCID;
											 if(_khothuoc==1){
												 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_NGT_1014", "pdf", par); 
											 }
											 else CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_1014", "pdf", par);
										 }
									 }
								 }	
								 else if(hopital==1077){
									 var _khothuoc=0;
									 var arr_khothuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC",_par_loai.join('$'));
									 if (arr_khothuoc != null && arr_khothuoc.length > 0) {
										 for(var i2=0;i2< arr_khothuoc.length;i2++){
											 _khothuoc=arr_khothuoc[i2].KHOTHUOCID;
											 if(_khothuoc==1){
												 CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_MN", "pdf", par); 
											 }
											 else CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_BH", "pdf", par);
										 }
									 }
								 }	
								 else if (hopital==951 && _self.options._doituongbenhnhanid != '1'){ //tuyennx L2PT-5527
									 CommonUtil.openReportGet('window', "SUB1_NGT006_DONTHUOC_A5_951", "pdf", par);
								 }
								 else{
									//L2PT-33876
									if(cfObj.KETHUOC_INTACHDON_NGT == '1' && $("#hidLOAITIEPNHANID").val() == '1')
										CommonUtil.openReportGet('window', "NGT006_DONTHUOC_NGOAITRU", "pdf", par);
									else
									 	CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
//									 var KETHUOC_MAKHO_NGUONCT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KETHUOC_MAKHO_NGUONCT');
//										var _ds_kho = KETHUOC_MAKHO_NGUONCT.split(',');
//										var sql_par = [];
//										sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
//										var makho = jsonrpc.AjaxJson.getOneValue('KETHUOC.THUOCCT', sql_par);
//										if($.inArray( makho, _ds_kho) >= 0 )
//											CommonUtil.openReportGet('window', "NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5", "pdf", par);
//										else
//											CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
								 }
									 
							 }						 
					}
					if(rowData.PHIEUHEN!=null && rowData.PHIEUHEN==1){
						if(hopital==902){
							var par = [ {
								name : 'khambenhid',
								type : 'String',
								value : rowData.KHAMBENHID
							 },
							 {
				       				name : 'maubenhphamid',
				       				type : 'String',
				       				value : rowData.MAUBENHPHAMID
				       		}
							];
						     CommonUtil.openReportGet('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
						}
						else{
					     var par = [ {
							name : 'khambenhid',
							type : 'String',
							value : rowData.KHAMBENHID
						 }];
					     CommonUtil.openReportGet('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
						}
				    } 
					
				 }          	 
				 //in don thuoc khong thuoc
				 if(rowData.LOAIDONTHUOC == "1"){
					 CommonUtil.openReportGet('window', "NGT006_DONTHUOCK_17DBV01_TT052016_A5", "pdf", par);					 
					 if(rowData.PHIEUHEN!=null && rowData.PHIEUHEN==1){
						 if(hopital==902){
								var par = [ {
									name : 'khambenhid',
									type : 'String',
									value : rowData.KHAMBENHID
								 },
								 {
					       				name : 'maubenhphamid',
					       				type : 'String',
					       				value : rowData.MAUBENHPHAMID
					       		}
								];
							     CommonUtil.openReportGet('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
							}
						 else{
							 var par = [ {
							name : 'khambenhid',
							type : 'String',
							value : rowData.KHAMBENHID
							 }];
							 CommonUtil.openReportGet('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
						 }
				    }					 
				 }				 
        	  }  
        	 
        	//L2PT-30303	 
 		    if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KETHUOC_INPHIEULINH_NGT') == '1' && _self.options._loaitiepnhanid == '1'){
 		    	var sql_par = [];
 				sql_par.push({"name":"[0]", value:rowData.MAUBENHPHAMID});
 				var _nhapxuatId = jsonrpc.AjaxJson.getOneValue('GET.NHAPXUATID', sql_par);
 				if(_nhapxuatId){
 					var par = [ 
 				   				{
 				   				name : 'nhapxuatid',//nhapxuatid
 				   				type : 'String',
 				   				value : _nhapxuatId
 				   			}];
 				   			openReport('window', 'DUC004_PHIEUCAPTHUOCNGOAITRU',
 				   					'pdf', par);
 				}
 		    }
        },  
      //L2PT-27971
        _inPKKT:function(rowId){
        	var _self=this;
        	var hopital=$("#hidHisId").val(); 
    		var hopital_id=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID'); 
    		if(hopital_id!= null && hopital_id!=0) {
    			hopital = hopital_id;
    		} 
        	var doituongbenhnhanid = $("#hidDOITUONGBENHNHANID").val(); 
        	 //var his_id_913 = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","IN_THUOC_913");
        	var user= $("#hidUserID").val();
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	 if(rowData != null){
        		 var par = [ {
       				name : 'maubenhphamid',
       				type : 'String',
       				value : rowData.MAUBENHPHAMID
       			 }];   
        		 CommonUtil.openReportGet('window', "PHIEUIN_KHAMBENH_KHONGTOA", "pdf", par);
        	  }         	 
        },   
        //L2PT-11555
        _inPhieuPhatThuoc:function(rowId){
        	var _self=this;
        	var hopital=$("#hidHisId").val(); 
    		var hopital_id=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID'); 
    		if(hopital_id!= null && hopital_id!=0) {
    			hopital = hopital_id;
    		} 
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	 if(rowData != null){
        		 var par = [ {
       				name : 'maubenhphamid',
       				type : 'String',
       				value : rowData.MAUBENHPHAMID
       			 }];   
        		 CommonUtil.openReportGet('window', "RPT_PHIEUCAP_PHATTHUOC", "pdf", par);
        	  }         	 
        },   
        //L2PT-15789
 /*       _kyCa: function (rowId) {
        	this._caRpt(rowId, '1','0');
        },
        _kyCaTrongNganh: function (rowId) {
        	this._caRpt(rowId, '1','1');
        },
        _huyKyCA: function (rowId) {
        	this._caRpt(rowId, '2','0');
        	if(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'KETHUOC_KY_TRONGNGANH')=='1')
        		this._caRpt(rowId, '2','1');
        },
        _caRpt: function (rowId, signType, type) {
            var par_rpt_KySo = [];
            var _rpt_code_kyso = "NGT006_DONTHUOC_17DBV01_TT052016_A5";
            var _self = this;
            var rowData = $('#' + _self.containerId + _self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
            if(rowData.LOAIKEDONTHUOC == 19 ){
            	_self._exportKyCA("NGT006_DONTHUOC_MUANGOAI_A5",rowData.MAUBENHPHAMID, signType);
				return;
            }	
            // don thuoc trong nganh BVTM-5017
            if((_self.options._indtringttrongnghanh == '1' || type == '1')  && rowData.LOAIKHO != '5'){
            	_self._exportKyCA("NGT006_DONTHUOCGOP_17DBV01_TT052016_A5",rowData.MAUBENHPHAMID, signType);
				return;
            }
           
            var _par_loai = [rowData.MAUBENHPHAMID];						
			 var _loaithuoc=0;    								
	   		 var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
	   		 if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
	   			for(var i=0;i< arr_loaithuoc.length;i++){
	   				_loaithuoc=arr_loaithuoc[i].LOAI;
	   			   if(_loaithuoc==3){
	   					//thuoc dong y --DONTHUOCTHANG_NGOAITRU
	   				   _self._exportKyCA("NGT020_DONTHUOCTHANGNGOAITRU",rowData.MAUBENHPHAMID, signType);
	   			   }else if(_loaithuoc==6){
	   				   _self._exportKyCA("NGT013_DONTHUOCHUONGTHAN_TT052016_A5",rowData.MAUBENHPHAMID, signType);
	   			   }else if(_loaithuoc==7){
	   				   _self._exportKyCA("NGT013_DONTHUOCGAYNGHIEN_TT052016_A5",rowData.MAUBENHPHAMID, signType);
	   			   }
	//   				   //don my pham thuc pham chuc nang
	//   				   else if(_loaithuoc==16 || _loaithuoc==19 ){   
	//   				   }
	//   				   //don thuoc nguon
	//   				   else if(_loaithuoc==-1 ){
	//   				   }
	   			   //don thuoc thuong
	   			   else{
	   				   _self._exportKyCA("NGT006_DONTHUOC_17DBV01_TT052016_A5",rowData.MAUBENHPHAMID, signType);
	   			   }
	   			}
	   		 }
        },
        
       _exportKyCA: function ( _rpt_code_kyso, maubenhphamId, signType){
    		var par_rpt_KySo = [];
    		par_rpt_KySo = [ {
    			name : 'HOSOBENHANID',
    			type : 'String',
    			value : $("#hidHOSOBENHANID").val()
    		}];
    		par_rpt_KySo.push({
    			name : 'RPT_CODE',
    			type : 'String',
    			value : _rpt_code_kyso
    		});
    		par_rpt_KySo.push({
    			name : 'maubenhphamid',
    			type : 'String',
    			value : maubenhphamId
    		});
    		//ky
//    		var oData = {
//    			sign_type : signType,
//    			causer : causer,
//    			capassword : capassword,
//    			params : par_rpt_KySo
//    		};
//    		var msg = CommonUtil.caRpt(oData, _rpt_code_kyso, true);
//    		DlgUtil.showMsg(msg);
//    		_self._initWidget();
    		var msg = CommonUtil.kyCA(par_rpt_KySo, signType, true);
    		EventUtil.setEvent("eventKyCA",function(e){ 
    			DlgUtil.showMsg(e.res);
    			_self._initWidget(); }
    		);
    	},

        _viewCA: function (rowId) {
            var _self = this;
            var rowData = $('#' + _self.containerId + _self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
            if (rowData) {
                if(rowData.LOAIKEDONTHUOC == 19 ){
                	_self._viewCA_chung(rowData,'NGT006_DONTHUOC_MUANGOAI_A5');
                	return;
                }
             // don thuoc trong nganh BVTM-5017
                if(_self.options._indtringttrongnghanh == '1' && rowData.LOAIKHO != '5'){
                	_self._viewCA_chung(rowData,'NGT006_DONTHUOCGOP_17DBV01_TT052016_A5');
    				return;
                }
                var _par_loai = [rowData.MAUBENHPHAMID];						
   			 	var _loaithuoc=0;    
                var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
	   	   		if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
	   	   			for(var i=0;i< arr_loaithuoc.length;i++){
	   	   				_loaithuoc=arr_loaithuoc[i].LOAI;
	   	   			   if(_loaithuoc==3){
	   	   					//thuoc dong y --DONTHUOCTHANG_NGOAITRU
	   	   				   _self._viewCA_chung(rowData,'NGT020_DONTHUOCTHANGNGOAITRU');
	   	   			   }else if(_loaithuoc==6){
	   	   				   _self._viewCA_chung(rowData,'NGT013_DONTHUOCHUONGTHAN_TT052016_A5');
	   	   			   }else if(_loaithuoc==7){
	   	   				   _self._viewCA_chung(rowData,'NGT013_DONTHUOCGAYNGHIEN_TT052016_A5');
	   	   			   }
	   	//   				   //don my pham thuc pham chuc nang
	   	//   				   else if(_loaithuoc==16 || _loaithuoc==19 ){   
	   	//   				   }
	   	//   				   //don thuoc nguon
	   	//   				   else if(_loaithuoc==-1 ){
	   	//   				   }
	   	   			   //don thuoc thuong
	   	   			   else{
	   	   				   _self._viewCA_chung(rowData,'NGT006_DONTHUOC_17DBV01_TT052016_A5');
	   	   			   }
	   	   			}
	   	   		 }
            }
        },
        
        _viewCA_chung: function (rowData, _rpt_code_kyso) {
            var par_rpt_KySo = [ {
                name : 'HOSOBENHANID',
                type : 'String',
                value : $("#hidHOSOBENHANID").val()
            }];
            par_rpt_KySo.push({
                name : 'RPT_CODE',
                type : 'String',
                value : _rpt_code_kyso
            });
            par_rpt_KySo.push({
                name : 'maubenhphamid',
                type : 'String',
                value : rowData.MAUBENHPHAMID
            });
            var data = CommonUtil.buildDataCA(101, RestInfo.uuid, {}, _rpt_code_kyso,
                par_rpt_KySo);
            var notSign = CommonUtil.isSignCANotYet(data);
            if (notSign) {
                DlgUtil.showMsg('Phiếu chưa được ký CA');
                return false;
            }
			CommonUtil.openReportGetCA2(par_rpt_KySo,false);
        },*/
        
        _kyCa: function (rowId,type) {
			var _self = this;
			//L2PT-17577
			var _rptCode = 'NGT006_DONTHUOC_17DBV01_TT052016_A5';
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
			let userCaConf = CaUtils.getCACachingConfig(_rptCode);
			var catype = '';
			var kieuky = '';
			if (data_ar != null && data_ar.length > 0) {
				var row = data_ar[0];
				var loaiky = row.LOAIKY;
				catype = row.CA_TYPE;
				var loaiphieu = row.LOAIPHIEU;
				var _title = row.KIEUKY;
				kieuky = row.KIEUKY;
			}
			if(catype == '3' || catype == '6') {
				var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
				let _paramInput = {params: null, smartca_method: 0};
				EventUtil.setEvent("dlgCaLogin_confirm", function () {
					DlgUtil.close("divCALOGIN");
					let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
					causer = _hisl2SmartCa.token.refresh_token;
					capassword = _hisl2SmartCa.token.access_token;
					_smartcauser = _hisl2SmartCa.user.uid;
					_self._caRpt(rowId, '1', type, _title, catype);
					//L2PT-21485
					//var _self = this;
					var rowData = $('#' + _self.containerId + _self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
					if (rowData.DAYDTDT != '1' && (cfObj.DTDT_DAY_DONTHUOC == '2' || cfObj.DTDT_DAY_DONTHUOC == '3'))
						_dayDonThuocOnline_new('2', rowData.MAUBENHPHAMID, rowData.OPTIONS, $("#hidLOAITIEPNHANID").val(), "", "Add", causer, capassword, catype);
					//_caRpt(_usn, _pwd, _params, kieuky, catype, _smartcauser);
				});

				let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
				if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
					_paramInput.smartca_method = 1;
					let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
					_popup.open("divCALOGIN");
					return;
				} else {
					EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function (e) {
						if (e.data && e.data.token && e.data.token.access_token) {
							_paramInput.smartca_method = 1;
						}
						DlgUtil.close("dlgCA_SMARTCA_LOGIN");
						let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
						_popup.open("divCALOGIN");
						return;
					});
					DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {isSignPopup: true}, "Smart Ca Login", 500, 650);
					DlgUtil.open("dlgCA_SMARTCA_LOGIN");
					return;
				}
			} else if (catype == '5') {
				_self._caRpt(rowId, '1', type, _title, catype);
			} 
			else if (userCaConf && cfObj.KYSO_TUDONG_KYDIENTU == '1') {
				causer = userCaConf.USER_NAME;
				capassword = userCaConf.PASS_WORD;
				_self._caRpt(rowId, '1', type, _title, catype);
            } else {
				EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
					causer = e.username;
					capassword = e.password;
					_smartcauser = "";
					DlgUtil.close("divCALOGIN");
					_self._caRpt(rowId, '1', type, _title, catype);
					//L2PT-21485
					//var _self = this;
					var rowData = $('#' + _self.containerId + _self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
					if(rowData.DAYDTDT != '1' && (cfObj.DTDT_DAY_DONTHUOC == '2' || cfObj.DTDT_DAY_DONTHUOC == '3'))
						_dayDonThuocOnline_new('2',rowData.MAUBENHPHAMID,rowData.OPTIONS,$("#hidLOAITIEPNHANID").val(),"", "Add",causer,capassword,catype);
				});
				EventUtil.setEvent("dlgCaLogin_close", function (e) {
					DlgUtil.close("divCALOGIN");
				});
				var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
				var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
				popup.open("divCALOGIN");
			}
		},
		_huyKyCA: function (rowId,type) {
			var _self = this;
			//L2PT-17577
			var _rptCode = 'NGT006_DONTHUOC_17DBV01_TT052016_A5';
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
			let userCaConf = CaUtils.getCACachingConfig(_rptCode);
			var catype = '';
			var kieuky = '';
			if (data_ar != null && data_ar.length > 0) {
				var row = data_ar[0];
				var loaiky = row.LOAIKY;
				catype = row.CA_TYPE;
				var loaiphieu = row.LOAIPHIEU;
				var _title = row.KIEUKY;
				kieuky = row.KIEUKY;
			}
			if(catype == '3' || catype == '6') {
				var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
				let _paramInput = {params: null, smartca_method: 0};
				EventUtil.setEvent("dlgCaLogin_confirm", function () {
					DlgUtil.close("divCALOGIN");
					let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
					causer = _hisl2SmartCa.token.refresh_token;
					capassword = _hisl2SmartCa.token.access_token;
					_smartcauser = _hisl2SmartCa.user.uid;
					_self._caRpt(rowId, '2', type, _title, catype);
					//_caRpt(_usn, _pwd, _params, kieuky, catype, _smartcauser);
				});

				let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
				if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
					_paramInput.smartca_method = 1;
					let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
					_popup.open("divCALOGIN");
					return;
				} else {
					EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function(e) {
						if (e.data && e.data.token && e.data.token.access_token) {
							_paramInput.smartca_method = 1;
						}
						DlgUtil.close("dlgCA_SMARTCA_LOGIN");
						let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
						_popup.open("divCALOGIN");
						return;
					});
					DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {isSignPopup: true}, "Smart Ca Login", 500, 650);
					DlgUtil.open("dlgCA_SMARTCA_LOGIN");
					return;
				}
			}
			else if(catype == '5') {
				_self._caRpt(rowId, '2',type, _title, catype);
			}
			else if (userCaConf && cfObj.KYSO_TUDONG_KYDIENTU == '1') {
				causer = userCaConf.USER_NAME;
				capassword = userCaConf.PASS_WORD;
				_self._caRpt(rowId, '2',type, _title, catype);
            }
			else {
				EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
					causer = e.username;
					capassword = e.password;
					_smartcauser = "";
					DlgUtil.close("divCALOGIN");
					_self._caRpt(rowId, '2',type, _title, catype);
				});
				EventUtil.setEvent("dlgCaLogin_close", function (e) {
					DlgUtil.close("divCALOGIN");
				});
				var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
				var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
				popup.open("divCALOGIN");
			}
		},
		_caRpt: function (rowId, signType, type, _title, catype) {
			var par_rpt_KySo = [];
			var _rpt_code_kyso = "NGT006_DONTHUOC_17DBV01_TT052016_A5";
			var _self = this;
			var rowData = $('#' + _self.containerId + _self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
			//L2PT-33429
			if(rowData.TRANGTHAIMAUBENHPHAM == '1' ){
				DlgUtil.showMsg('Không thao tác với phiếu đang sửa!');
				return;
			}
				
			if(rowData.LOAIKEDONTHUOC == 19 ){
				_self._exportKyCA("NGT006_DONTHUOC_MUANGOAI_A5",rowData.MAUBENHPHAMID, signType, _title, catype, rowId);
				return;
			}
			// don thuoc trong nganh BVTM-5017
			if((_self.options._indtringttrongnghanh == '1' || type == '1') && rowData.LOAIKHO != '5'){
				_self._exportKyCA("NGT006_DONTHUOCGOP_17DBV01_TT052016_A5",rowData.MAUBENHPHAMID, signType, _title, catype, rowId);
				return;
			}

			var _par_loai = [rowData.MAUBENHPHAMID];
			var _loaithuoc=0;
			var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));
			if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
				for(var i=0;i< arr_loaithuoc.length;i++){
					_loaithuoc=arr_loaithuoc[i].LOAI;
					if(_loaithuoc==3){
						//thuoc dong y --DONTHUOCTHANG_NGOAITRU
						_self._exportKyCA("NGT020_DONTHUOCTHANGNGOAITRU",rowData.MAUBENHPHAMID, signType, _title, catype, rowId);
					}else if(_loaithuoc==6){
						_self._exportKyCA("NGT013_DONTHUOCHUONGTHAN_TT052016_A5",rowData.MAUBENHPHAMID, signType, _title, catype, rowId);
					}else if(_loaithuoc==7){
						_self._exportKyCA("NGT013_DONTHUOCGAYNGHIEN_TT052016_A5",rowData.MAUBENHPHAMID, signType, _title, catype, rowId);
					}
					//   				   //don my pham thuc pham chuc nang
					//   				   else if(_loaithuoc==16 || _loaithuoc==19 ){
					//   				   }
					//   				   //don thuoc nguon
					//   				   else if(_loaithuoc==-1 ){
					//   				   }
					//don thuoc thuong
					else{
						_self._exportKyCA("NGT006_DONTHUOC_17DBV01_TT052016_A5",rowData.MAUBENHPHAMID, signType, _title, catype, rowId);
					}
				}
			}
		},

		_exportKyCA: function ( _rpt_code_kyso, maubenhphamId, signType, _title, catype, rowId){
			var _self = this;
			var par_rpt_KySo = [];
			par_rpt_KySo = [ {
				name : 'HOSOBENHANID',
				type : 'String',
				value : $("#hidHOSOBENHANID").val()
			}];
			par_rpt_KySo.push({
				name : 'RPT_CODE',
				type : 'String',
				value : _rpt_code_kyso
			});
			par_rpt_KySo.push({
				name : 'maubenhphamid',
				type : 'String',
				value : maubenhphamId
			});
			//L2PT-43073
			if (cfObj.KETHUOC_IN_KYSO_CHUNG == '1') {
				lstParamPrintCa.push(par_rpt_KySo);
			}
			//ky
			if(catype == '5') {
				//L2PT-28091
				if(cfObj.TABTHUOC_HIENTHI_CHECKBOX == 0){ // neu ko chon ky nhieu thi van vao luong ky token 1 don
					CommonUtil.kyCA(par_rpt_KySo, signType, true);
					EventUtil.setEvent("eventKyCA", function (e) {
						var rowData = $('#' + _self.containerId + _self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
						if(signType == '1') {
							if(rowData.DAYDTDT != '1' && (cfObj.DTDT_DAY_DONTHUOC == '2' || cfObj.DTDT_DAY_DONTHUOC == '3'))
								_dayDonThuocOnline_new('2',rowData.MAUBENHPHAMID,rowData.OPTIONS,$("#hidLOAITIEPNHANID").val(),"", "Add",'99999999','99999999',catype);
						}
						DlgUtil.showMsg(e.res);
					});
				}else{
					if(signType == '1') {// neu ky moi thi vao luong ky token nhieu phieu, huy thi vao luong huy cu
						lstParamKyCa.push(par_rpt_KySo);
					}
					else{
						checkky = 1;
						CommonUtil.kyCA(par_rpt_KySo, signType, null, true);
						//CommonUtil._caRptTocken(lstParamKyCa, '1', null, true);
//						EventUtil.setEvent("eventKyCA", function (e) {
//							DlgUtil.showMsg(e.res);
//						});
					}
				}
				
			} else {
				var oData = {
					sign_type : signType,
					causer : causer,
					capassword : capassword,
					params : par_rpt_KySo,
					smartcauser: _smartcauser //L2PT-17577
				};
				//L2PT-43073
				var msg ="";
				if (cfObj.KETHUOC_IN_KYSO_CHUNG == '1' && lstParamPrintCa.length > 0) 
					msg = CommonUtil.caRpt(oData, _rpt_code_kyso, false, '', true, _title, catype);
				else
					msg = CommonUtil.caRpt(oData, _rpt_code_kyso, true, '', true, _title, catype);
				
				var _code = msg.split("|")[0];
				var _msg = msg.split("|")[1];
				var _caid = msg.split("|")[2];
				if (_code == '0' || _code == '7' || _code == '8') {
					$.bootstrapGrowl(_msg,{
		                type: 'success',
		                delay: 2000,
		            });
					if (catype == '3') {
						var intervalId = null;
						var smartCaLoaderFunction = function () {
							console.log("smartCaLoaderFunction is running!")
							var _sql_par = [];
							_sql_par.push({"name": "[0]", value: _caid});
							var fl = jsonrpc.AjaxJson.getOneValue("SMARTCA.GET.STATUS", _sql_par);
							if (fl == 1) {
								// bat phieu in
								CommonUtil.openReportGetCA2(par_rpt_KySo, false);
								clearInterval(intervalId);
							}
						};
						intervalId = setInterval(smartCaLoaderFunction, 4000);
					}
				}
				else
					DlgUtil.showMsg(_msg);
			}
			_self._initWidget();
		},

        _viewCA: function (rowId) {
            var _self = this;
            var rowData = $('#' + _self.containerId + _self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
            if (rowData) {
            	//L2PT-33429
    			if(rowData.TRANGTHAIMAUBENHPHAM == '1' ){
    				DlgUtil.showMsg('Không thao tác với phiếu đang sửa!');
    				return;
    			}
                if(rowData.LOAIKEDONTHUOC == 19 ){
                	_self._viewCA_chung(rowData,'NGT006_DONTHUOC_MUANGOAI_A5');
                	return;
                }
             // don thuoc trong nganh BVTM-5017
                if(_self.options._indtringttrongnghanh == '1' && rowData.LOAIKHO != '5'){
                	_self._viewCA_chung(rowData,'NGT006_DONTHUOCGOP_17DBV01_TT052016_A5');
    				return;
                }
                var _par_loai = [rowData.MAUBENHPHAMID];						
   			 	var _loaithuoc=0;    
                var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
	   	   		if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
	   	   			for(var i=0;i< arr_loaithuoc.length;i++){
	   	   				_loaithuoc=arr_loaithuoc[i].LOAI;
	   	   			   if(_loaithuoc==3){
	   	   					//thuoc dong y --DONTHUOCTHANG_NGOAITRU
	   	   				   _self._viewCA_chung(rowData,'NGT020_DONTHUOCTHANGNGOAITRU');
	   	   			   }else if(_loaithuoc==6){
	   	   				   _self._viewCA_chung(rowData,'NGT013_DONTHUOCHUONGTHAN_TT052016_A5');
	   	   			   }else if(_loaithuoc==7){
	   	   				   _self._viewCA_chung(rowData,'NGT013_DONTHUOCGAYNGHIEN_TT052016_A5');
	   	   			   }
	   	//   				   //don my pham thuc pham chuc nang
	   	//   				   else if(_loaithuoc==16 || _loaithuoc==19 ){   
	   	//   				   }
	   	//   				   //don thuoc nguon
	   	//   				   else if(_loaithuoc==-1 ){
	   	//   				   }
	   	   			   //don thuoc thuong
	   	   			   else{
	   	   				   _self._viewCA_chung(rowData,'NGT006_DONTHUOC_17DBV01_TT052016_A5');
	   	   			   }
	   	   			}
	   	   		 }
            }
        },
        
        _viewCA_chung: function (rowData, _rpt_code_kyso) {
            var par_rpt_KySo = [ {
                name : 'HOSOBENHANID',
                type : 'String',
                value : $("#hidHOSOBENHANID").val()
            }];
            par_rpt_KySo.push({
                name : 'RPT_CODE',
                type : 'String',
                value : _rpt_code_kyso
            });
            par_rpt_KySo.push({
                name : 'maubenhphamid',
                type : 'String',
                value : rowData.MAUBENHPHAMID
            });
          
            var data = CommonUtil.buildDataCA(101, RestInfo.uuid, {}, _rpt_code_kyso,
                par_rpt_KySo);
            var notSign = CommonUtil.isSignCANotYet(data);
            if (notSign) {
                DlgUtil.showMsg('Phiếu chưa được ký CA');
                return false;
            }
          //L2PT-43073
			if (cfObj.KETHUOC_IN_KYSO_CHUNG == '1') {
				lstParamPrintCa.push(par_rpt_KySo);
				return;
			}
			CommonUtil.openReportGetCA2(par_rpt_KySo,false);
        },
        
        _tuDongInDonThuoc:function(rowId){
        	var _type = 'pdf';        	
        	var pars = ['HIS_FILEEXPORT_TYPE']; 
    		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
    		if(data_ar != '-1'){
    			_type = data_ar;
    		}
        	
        	var _self=this;
        	var hopital=$("#hidHisId").val();  
        	//tuyennx_add_start_20190424 L1PT-664
    		var hopital_id=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID'); 
    		if(hopital_id!= null && hopital_id!=0) {
    			hopital = hopital_id;
    		} 
    		//tuyennx_add_end_20190424 L1PT-664
        	//var his_id_913 = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA","IN_THUOC_913");
        	var doituongbenhnhanid = $("#hidDOITUONGBENHNHANID").val(); 
        	var user= $("#hidUserID").val();
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	
        	var pars = ['PHONG_TUDONG_IN']; 
			var dc_phong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
			var dc_phongs = dc_phong.split(',');
			if($.inArray( rowData.PHONGID, dc_phongs) < 0){
				DlgUtil.showMsg('Phòng khám chưa được cấu hình in tự động');
				return;
			}
        	
        	 if(rowData != null){
        		 var par = [ {
       				name : 'maubenhphamid',
       				type : 'String',
       				value : rowData.MAUBENHPHAMID
       			 }];    
        		 
        		var rpName = "VNPTHIS_IN_A5_";
 				rpName += rowData.SOPHIEU; 
 				rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
 				rpName += "."+_type; 
 				if(hopital==913){
 	        		//CommonUtil.openReportGet('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_913", "pdf", par);
 	        		CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5_913', _type, par,rpName);
 	        	} 
 	        	else
 	        	 {
        		 //lay loai thuoc
     	    	 var _loaithuoc=0;
        		 var _par_loai = [rowData.MAUBENHPHAMID];						
				 var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
				 if (arr_loaithuoc != null && arr_loaithuoc.length > 0) { // in don thuoc thuong
					for(var i=0;i< arr_loaithuoc.length;i++){
						_loaithuoc=arr_loaithuoc[i].LOAI;
						   if(_loaithuoc==3){
								//thuoc dong y --DONTHUOCTHANG_NGOAITRU
							    //CommonUtil.openReportGet('window', "NGT020_DONTHUOCTHANGNGOAITRU", "pdf", par); 
							    CommonUtil.inPhieu('window', 'NGT020_DONTHUOCTHANGNGOAITRU', _type, par,rpName);
							 }else if(_loaithuoc==6){
								 //thuoc huong than 
								 //CommonUtil.openReportGet('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par); 
								 CommonUtil.inPhieu('window', 'NGT013_DONTHUOCHUONGTHAN_TT052016_A5', _type, par,rpName);
							 }else if(_loaithuoc==7){
								 //don thuoc gay nghien
								 //CommonUtil.openReportGet('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par);
								 CommonUtil.inPhieu('window', 'NGT013_DONTHUOCGAYNGHIEN_TT052016_A5', _type, par,rpName);
							 }
							 //tuyennx_add_start L2PT-13903
								else if(_loaithuoc==16 || _loaithuoc==19 ){
									if(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'IN_TACHDON_MP_TPCN') == 1){ //L2PT-30631
										if(_loaithuoc==16){
											CommonUtil.inPhieu('window', 'NGT013_MYPHAM_TT052016_A5', _type, par,rpName);
										}
										else{
											CommonUtil.inPhieu('window', 'NGT013_TPCN_TT052016_A5', _type, par,rpName);
										}
									}else
										 //don my pham
										CommonUtil.inPhieu('window', 'NGT013_DONMYPHAM_TPCN_TT052016_A5', _type, par,rpName);
							
								}
							  //tuyennx_add_end L2PT-13903
						   //tuyennx_add_start L2PT-21559
								else if(_loaithuoc==-1 ){
									 //don my pham
									CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_NGUON_17DBV01_TT052016_A5', _type, par,rpName);
							
								}
							  //tuyennx_add_end L2PT-21559
							 else{
								 // don thuoc khac
								// don thuoc khac
								 if(hopital==944){
									 if(doituongbenhnhanid!=1){CommonUtil.inPhieu('window', "NGT006_DONTHUOC1L_17DBV01_TT052016_A5_944", _type, par,rpName);}
									 else CommonUtil.inPhieu('window', "NGT006_DONTHUOC1L_17DBV01_TT052016_A5_944", _type, par,rpName);
							  } else CommonUtil.inPhieu('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", _type, par,rpName);								 
							 }						 
					}
					if(rowData.PHIEUHEN!=null && rowData.PHIEUHEN==1){
						if(hopital==902){
							var par = [ {
								name : 'khambenhid',
								type : 'String',
								value : rowData.KHAMBENHID
							 },
							 {
				       				name : 'maubenhphamid',
				       				type : 'String',
				       				value : rowData.MAUBENHPHAMID
				       		}
							];
							var rpName = "VNPTHIS_IN_A4_";
			 				rpName += rowData.SOPHIEU; 
			 				rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
			 				rpName += "."+_type; 
						     //CommonUtil.openReportGet('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
						     CommonUtil.inPhieu('window', 'NGT014_GIAYHENKHAMLAI_TT402015_A4', _type, par,rpName);
						}
						else{
					     var par = [ {
							name : 'khambenhid',
							type : 'String',
							value : rowData.KHAMBENHID
						 }];
					     var rpName = "VNPTHIS_IN_A4_";
			 				rpName += rowData.SOPHIEU; 
			 				rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
			 				rpName += "."+_type; 
					     //CommonUtil.openReportGet('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
					     CommonUtil.inPhieu('window', 'NGT014_GIAYHENKHAMLAI_TT402015_A4', _type, par,rpName);
						}
				    } 
					
				 }  
				 
				 //in don thuoc khong thuoc
				 if(rowData.LOAIDONTHUOC == "1"){
					 var rpName = "VNPTHIS_IN_A5_";
		 				rpName += rowData.SOPHIEU; 
		 				rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
		 				rpName += "."+_type; 
				     CommonUtil.inPhieu('window', 'NGT006_DONTHUOCK_17DBV01_TT052016_A5', _type, par,rpName);
					 //CommonUtil.openReportGet('window', "NGT006_DONTHUOCK_17DBV01_TT052016_A5", "pdf", par);					 
					 if(rowData.PHIEUHEN!=null && rowData.PHIEUHEN==1){
						 if(hopital==902){
								var par = [ {
									name : 'khambenhid',
									type : 'String',
									value : rowData.KHAMBENHID
								 },
								 {
					       				name : 'maubenhphamid',
					       				type : 'String',
					       				value : rowData.MAUBENHPHAMID
					       		}
								];
								var rpName = "VNPTHIS_IN_A4_";
				 				rpName += rowData.SOPHIEU; 
				 				rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
				 				rpName += "."+_type; 
						        CommonUtil.inPhieu('window', 'NGT014_GIAYHENKHAMLAI_TT402015_A4', _type, par,rpName);
							    //CommonUtil.openReportGet('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
							}
						 else{
							 var par = [ {
							name : 'khambenhid',
							type : 'String',
							value : rowData.KHAMBENHID
							 }];
							 var rpName = "VNPTHIS_IN_A4_";
				 				rpName += rowData.SOPHIEU; 
				 				rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
				 				rpName += "."+_type; 
						     CommonUtil.inPhieu('window', 'NGT014_GIAYHENKHAMLAI_TT402015_A4', _type, par,rpName);
							 //CommonUtil.openReportGet('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par);
						 }
				    }					 
				 }				 
 	        	} 
        	 }
        },
        _createNoteAttach:function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	 if(rowData != null){
        		 var paramInput={							
        				    tiepnhanid : $("#hidTIEPNHANID").val(),
							maubenhphamid : rowData.MAUBENHPHAMID													
					};				
					dlgPopup=DlgUtil.buildPopupUrl("divCreateNoteAttach","divDlg","manager.jsp?func=../noitru/NTU02D003_DichVuDinhKem",paramInput,"Chỉ định là phiếu đi kèm",1200,580);
					DlgUtil.open("divCreateNoteAttach");	
        	 }
        },
        //tuyennx_edit_start them type cho truong hop xoa sua ko can huy L2DKBD-1032
        _deleteRequest:function(rowId, type){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	 if(rowData != null){
        		// check quyen xoa du lieu // L2PT-17531
        		 var _nguoitaoid=rowData.NGUOITAO_ID;
        		 if(_checkRoles(_nguoitaoid,$("#hidUserID").val())==false && jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KEDON_QUYENHUYXOA') == "0"){
        			 DlgUtil.showMsg("Bạn không có quyền xóa yêu cầu phiếu này!");
        			 return 1;
        		 }
        		//L2PT-20784
         		if(rowData.KHAMBENHID != _self.options._khambenhid){
         			DlgUtil.showMsg("Bạn không có quyền thao tác với phiếu này!");
        			return 1;
         		}
        		var _trangthai=rowData.TRANGTHAIMAUBENHPHAM; 
        		if(_trangthai==null) _trangthai=0;
        		_trangthai=parseInt(_trangthai);
        		
        		/*if(rowData.LOAIPHIEUMAUBENHPHAM == '2' && $.inArray(rowData.LOAIKHO, ["8","9","13"]) >= 0 ){
    				DlgUtil.showMsg("Không hủy phiếu trả từ tủ trực!");
    				return 1;
    			}	*/        
        		/*=================
        		if(_trangthai==2){
        			var _checkThuTien=rowData.DACODICHVUTHUTIEN;
        			if(_checkThuTien==1){
        				DlgUtil.showMsg("Phiếu đã có dịch vụ thu tiền nên không được phép hủy yêu cầu!");
        				return 1;
        			}   
        		}else if(_trangthai==1){
        			DlgUtil.showMsg("Phiếu đã được hủy yêu cầu!");
        			return 0;
        		//tuyennx_edit_start L2K74TW-597
        		}else if(_trangthai > 2 && ($.inArray(rowData.LOAIKHO , ['8', '9','13','10','11','15']) < 0  && $.inArray(rowData.LOAIKEDONTHUOC , ['19', '20']) < 0 
        		&& $.inArray(rowData.OPTIONS , ['02D011', '02D019']) < 0 )){
        		//}else if(_trangthai > 2 && (rowData.LOAIKHO != 8 && rowData.LOAIKHO != 13)){
        		//tuyennx_edit_end L2K74TW-597
        			DlgUtil.showMsg("Phiếu đã được xử lý nên không thể hủy yêu cầu");
        			return 1;
        		}
        		//tuyennx_add_start HISL2CORE-1315
        		if(rowData.TRANGTHAI_DUYETDUOC_CLS==6){
    				DlgUtil.showMsg("Thuốc đã được duyệt dược, không được thực hiện hủy phiếu!");
    				return 1;
    			}  
        		//tuyennx_add_end 
        		===================*/
        		// BVTM-3216
        		// check hủy phiếu có cùng ghép cặp hay không
        		if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_CHECK_GHEPCAP') == '1'){
        			var _check = [rowData.MAUBENHPHAMID];					
    				var _returnCHK = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK_GHEPCAP",_check.join('$'));
    				if(parseInt(_returnCHK) == "100"){
    					DlgUtil.showMsg("Hủy phiếu không thành công");
        				return 0;
    				}
    				
    				if(parseInt(_returnCHK) > 1){
    					var myVar={
    						maubenhphamid : rowData.MAUBENHPHAMID
    					};
						dlgPopup=DlgUtil.buildPopupUrl("dlgGhepCap","divDlg","manager.jsp?func=../ngoaitru/NGT04K004_DonThuocGhepCap",myVar,"Danh sách phiếu thuốc cùng cặp",650,300, "");
						DlgUtil.open("dlgGhepCap");
						
						EventUtil.setEvent("ghepcap_success", function(e) {
							if(e.status == '1'){
								_self._initWidget();
							}
						});
						
						return 0;
    				}
        		}
        		
        		//go duyet phieu, chuyen trang thai chua duyet
				var _par = ["1",rowData.MAUBENHPHAMID];					
				var _return = jsonrpc.AjaxJson.ajaxCALL_SP_S("NT.G.DUYETPHIEU",_par.join('$'));
				if(parseInt(_return) > 0){
					if(type == 1){
						//tuyennx_edit_start_20190425 L1PT-661 L2PT-14910
						DlgUtil.showMsg('Phiếu đã được hủy yêu cầu thành công!',undefined,jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_TIMEOUT_THONGBAO'));
						//tuyennx_edit_end_20190425 L1PT-661
						_self._initWidget();
					}
					else
						return 0;
						
				}else{
					//DlgUtil.showMsg("Phiếu hủy yêu cầu thất bại!");
					DlgUtil.showMsg(_return);	
					return 1;
				}	
        	 }
        },
        //tuyennx_edit_end
        _sendRequest:function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	 if(rowData != null){
        		// check quyen xoa du lieu L2PT-17531 rao lai check trong DB
//        		 if(_self.options._modeView != "2"){
//        			 var _nguoitaoid=rowData.NGUOITAO_ID;
//            		 if(_checkRoles(_nguoitaoid,$("#hidUserID").val())==false && jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KEDON_QUYENHUYXOA') == "0"){
//            			 DlgUtil.showMsg("Bạn không có quyền gửi yêu cầu phiếu này!");
//            			 return false;
//            		 }
//        		 }
        		 
        		// Check chuyen khoa dieu tri khong co quyen gui phieu
        		 if ($("#hidKHOAID").val() && cfObj.NTU_DTKH_ALLOW_UPDATE_DVKT != '1') {//L2PT-51997 cấu hình cho phép gửi lại phiếu đối với điều trị kết hợp
        			var check_par=[];
              		check_par.push({"name":"[0]","value":$("#hidKHOAID").val()});
              		check_par.push({"name":"[1]","value":rowData.KHAMBENHID});
              		var checkDtkh = jsonrpc.AjaxJson.getOneValue("CHECK.DTKH", check_par);
              		if (checkDtkh !== "0") {
              			DlgUtil.showMsg("Chuyên khoa điều trị kết hợp không được phép gửi yêu cầu!");
              			return false;
              		}
        		}
        		//L2PT-20784
        		if(rowData.KHAMBENHID != _self.options._khambenhid){
        			DlgUtil.showMsg("Bạn không có quyền thao tác với phiếu này!");
       			 	return false;
        		}
          		
        		var _trangthai=rowData.TRANGTHAIMAUBENHPHAM;
        		if(_trangthai==null) _trangthai=0;
        		_trangthai=parseInt(_trangthai);
        		if(_trangthai==1){        			 
					//gUI duyet phieu, chuyen trang thai chua duyet
					var _par = ['2',rowData.MAUBENHPHAMID];						
					var _returns=jsonrpc.AjaxJson.ajaxCALL_SP_S("NT.C.DUYETPHIEU",_par.join('$'));
					_return = _returns.split(",");
					if(parseInt(_return[0]) > 0){
						DlgUtil.showMsg("Phiếu đã được gửi yêu cầu thành công!");					
						_self._initWidget();    
						//tuyennx_add_20170713_start
						EventUtil.raiseEvent("assignDrug_cancel",{option:''});
						//tuyennx_add_20170713_end
					}else{
						DlgUtil.showMsg("Phiếu gửi yêu cầu thất bại:" + _return[0]); //L2PT-15735
					}
        		}else{
        			DlgUtil.showMsg("Phiếu đã được gửi yêu cầu!");
        		}
        	 }
        },
        
        //L2PT-31551
        _sendRequestCheck:function(){
        	var _self=this;
        	var selRows = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getGridParam', 'selarrrow');
    		if(selRows.length == 0){
    			DlgUtil.showMsg("Chọn ít nhất 1 phiếu!");
    			return;
    		}
    		var check = 0;
    		for (var i = 0; i < selRows.length; i++) {
    			rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
    			if(rowData.TRANGTHAIMAUBENHPHAM != '1'){	
    				DlgUtil.showMsg("Chỉ chọn những phiếu ở trạng thái đang sửa!");
        			return;
    			}
    			if(_checkRoles(rowData.NGUOITAO_ID,$("#hidUserID").val())==false){
	       			 DlgUtil.showMsg("Bạn không có quyền gửi yêu cầu phiếu này: " + rowData.SOPHIEU );
	       			 return false;
       		 	}
    			//L2PT-20784
        		if(rowData.KHAMBENHID != _self.options._khambenhid){
        			DlgUtil.showMsg("Bạn không có quyền thao tác với phiếu này!");
       			 	return false;
        		}
    			// Check chuyen khoa dieu tri khong co quyen gui phieu
        		if ($("#hidKHOAID").val() && cfObj.NTU_DTKH_ALLOW_UPDATE_DVKT != '1') {//L2PT-51997 cấu hình cho phép gửi lại phiếu đối với điều trị kết hợp
        			var check_par=[];
              		check_par.push({"name":"[0]","value":$("#hidKHOAID").val()});
              		check_par.push({"name":"[1]","value":rowData.KHAMBENHID});
              		var checkDtkh = jsonrpc.AjaxJson.getOneValue("CHECK.DTKH", check_par);
              		if (checkDtkh !== "0") {
              			DlgUtil.showMsg("Chuyên khoa điều trị kết hợp không được phép gửi yêu cầu!");
              			return false;
              		}
        		}
    		}
    		var sophieu = "";
    		for (var i = 0; i < selRows.length; i++) {
    			rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
    			if(rowData != null){
            		var _trangthai=rowData.TRANGTHAIMAUBENHPHAM;
            		if(_trangthai==null) _trangthai=0;
            		_trangthai=parseInt(_trangthai);
					var _par = ['2',rowData.MAUBENHPHAMID];						
					var _returns=jsonrpc.AjaxJson.ajaxCALL_SP_S("NT.C.DUYETPHIEU",_par.join('$'));
					_return = _returns.split(",");
					if(parseInt(_return[0]) > 0){
						sophieu = i == (selRows.length -1 )? sophieu + rowData.SOPHIEU : sophieu + rowData.SOPHIEU + ";";
					}else{
						DlgUtil.showMsg("Phiếu " + rowData.SOPHIEU + " gửi yêu cầu thất bại:" + _return[0]); //L2PT-15735
					}
            	 }
    		}
    		if(sophieu){
    			DlgUtil.showMsg("Phiếu đã được gửi yêu cầu thành công: " + sophieu);					
				_self._initWidget();    
				EventUtil.raiseEvent("assignDrug_cancel",{option:''});
    		}
        },
        
        _deleteRequestCheck:function(){
        	var _self=this;
        	var selRows = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getGridParam', 'selarrrow');
    		if(selRows.length == 0){
    			DlgUtil.showMsg("Chọn ít nhất 1 phiếu!");
    			return;
    		}
    		var check = 0;
    		for (var i = 0; i < selRows.length; i++) {
    			rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
    			
    			if(rowData.TRANGTHAIMAUBENHPHAM == '2'){
        			var _checkThuTien=rowData.DACODICHVUTHUTIEN;
        			if(_checkThuTien==1){
        				DlgUtil.showMsg("Phiếu đã có dịch vụ thu tiền nên không được phép hủy yêu cầu: " + rowData.SOPHIEU);
        				return 1;
        			}   
        		}else if(rowData.TRANGTHAIMAUBENHPHAM == '1'){
        			DlgUtil.showMsg("Phiếu đã được hủy yêu cầu: " + rowData.SOPHIEU);
        			return 0;
        		//tuyennx_edit_start L2K74TW-597
        		}else if(parseInt(rowData.TRANGTHAIMAUBENHPHAM) > 2 && ($.inArray(rowData.LOAIKHO , ['8', '9','13','10','11','15']) < 0  
        				&& $.inArray(rowData.LOAIKEDONTHUOC , ['19', '20']) < 0  && $.inArray(rowData.OPTIONS , ['02D011', '02D019']) < 0 )){
        		//}else if(_trangthai > 2 && (rowData.LOAIKHO != 8 && rowData.LOAIKHO != 13)){
        		//tuyennx_edit_end L2K74TW-597
        			DlgUtil.showMsg("Phiếu đã được xử lý nên không thể hủy yêu cầu: " + rowData.SOPHIEU);
        			return 1;
        		}
        		//tuyennx_add_start HISL2CORE-1315
        		if(rowData.TRANGTHAI_DUYETDUOC_CLS==6){
    				DlgUtil.showMsg("Thuốc đã được duyệt dược, không được thực hiện hủy phiếu: " + rowData.SOPHIEU);
    				return 1;
    			}  
    		}
    		
    		var sophieu = "";
    		for (var i = 0; i < selRows.length; i++) {
    			rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
    			if(rowData != null){
    				var _par = ["1",rowData.MAUBENHPHAMID];					
    				var _return = jsonrpc.AjaxJson.ajaxCALL_SP_S("NT.G.DUYETPHIEU",_par.join('$'));
    				if(parseInt(_return) > 0){
						sophieu = i == (selRows.length -1 )? sophieu + rowData.SOPHIEU : sophieu + rowData.SOPHIEU + ";";
					}else{
						DlgUtil.showMsg("Phiếu " + rowData.SOPHIEU + " hủy yêu cầu thất bại:" + _return); //L2PT-15735
					}
            	 }
    		}
    		if(sophieu){
    			DlgUtil.showMsg("Phiếu đã được hủy yêu cầu thành công: " + sophieu);					
				_self._initWidget();    
				//EventUtil.raiseEvent("assignDrug_cancel",{option:''});
    		}
        },
        //L2PT-72762
        _indonthuocCheck:function(){
        	var _self=this;
        	var selRows = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getGridParam', 'selarrrow');
    		if(selRows.length == 0){
    			DlgUtil.showMsg("Chọn ít nhất 1 phiếu!");
    			return;
    		}
    		var maubenhphamidt = '';
    		for (var i = 0; i < selRows.length; i++) {
    			rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
    			maubenhphamidt = maubenhphamidt + rowData.MAUBENHPHAMID + ",";
    		}
    		 maubenhphamidt = maubenhphamidt.substring(0, maubenhphamidt.length - 1);
    		 if (maubenhphamidt.length > 0) {
                 var par2 = [{
                     name: 'maubenhphamid',
                     type: 'String',
                     value: maubenhphamidt
                 }];
                 CommonUtil.openReportGet('window', 'PHIEUIN_DONTHUOC_A5_3LIEN', "pdf", par2);
             }
        },
        
        //L2PT-16134
        _deletePhieuThuocVatTuCheck:function(){
        	var _self=this;
        	var selRows = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getGridParam', 'selarrrow');
    		if(selRows.length == 0){
    			DlgUtil.showMsg("Chọn ít nhất 1 phiếu!");
    			return;
    		}
    		var _jsonDonThuoc = [];
    		for (var i = 0; i < selRows.length; i++) {
    			rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
    			if(rowData.TRANGTHAIMAUBENHPHAM == '2'){
        			var _checkThuTien=rowData.DACODICHVUTHUTIEN;
        			if(_checkThuTien==1){
        				DlgUtil.showMsg("Phiếu đã có dịch vụ thu tiền nên không được phép hủy yêu cầu: " + rowData.SOPHIEU);
        				return 1;
        			}   
        		}else if(parseInt(rowData.TRANGTHAIMAUBENHPHAM) > 2 && ($.inArray(rowData.LOAIKHO , ['8', '9','13','10','11','15']) < 0  
        				&& $.inArray(rowData.LOAIKEDONTHUOC , ['19', '20']) < 0 && $.inArray(rowData.OPTIONS , ['02D011', '02D019']) < 0 )){
        			DlgUtil.showMsg("Phiếu đã được xử lý nên không thể hủy yêu cầu: " + rowData.SOPHIEU);
        			return 1;
        		}

        		if(rowData.TRANGTHAI_DUYETDUOC_CLS==6){
    				DlgUtil.showMsg("Thuốc đã được duyệt dược, không được thực hiện hủy phiếu: " + rowData.SOPHIEU);
    				return 1;
    			}
	   			 if(cfObj.NTU_THUOC_CHECKXOA_USER == '1')
	       		 {
	   				if (_nguoitaoid != $("#hidUserID").val() ) {
	   					DlgUtil.showMsg("Bạn không có quyền xóa phiếu này: " + rowData.SOPHIEU);
	   					return false;
	   				}
	       		 }
	   			 //L2PT-20784
        		if(rowData.KHAMBENHID != _self.options._khambenhid){
        			DlgUtil.showMsg("Bạn không có quyền thao tác với phiếu này!");
       			 	return false;
        		}
	
	   			var par=[];
         		par.push({"name":"[0]","value":rowData.MAUBENHPHAMID});
         		var checkThutien = jsonrpc.AjaxJson.getOneValue("CHECK.THUTIEN", par);
         		if(parseInt(checkThutien) > 0){
         			DlgUtil.showMsg("Đã có thuốc/Vật tư thu tiền: " + rowData.SOPHIEU);
          			return false;
         		}
         		if(parseInt(rowData.TRANGTHAIMAUBENHPHAM) == 2 && cfObj.XOA_CAPNHAT_PHIEU_KOCANHUY != 1){
         			DlgUtil.showMsg("Phiếu chưa hủy yêu cầu: " + rowData.SOPHIEU);
          			return false;
         		}
         		if(rowData != null){
    				_jsonDonThuoc.push(rowData);						
            	}
    		}
    		DlgUtil.showConfirm("Bạn có chắc chắn xóa các phiếu thuốc đã chọn?",function(flag) {
       		   if (flag) {
	       			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_S("NT.G.DUYET.MUL",JSON.stringify(_jsonDonThuoc));
	    			if(parseInt(_return) > 0){
	    				DlgUtil.showMsg("Xóa thành công!");
	    				//tam chua xoa don day cong do khi xoa nhieu data_btkd k olay dc du lieu do da bi xoa
//	    				if(cfObj.BTKD_DAY_BYT == 1){
//	    					for (var i = 0; i < selRows.length; i++) {
//	    						rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', selRows[i]);
//		    					var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD",[rowData.MAUBENHPHAMID].join('$'));
//		    	       			_self._daybtkd(rowData.MAUBENHPHAMID, data_btkd);
//		    				}
//	    				}
	    				_self._initWidget();
	    			}else{
	    				DlgUtil.showMsg( _return); 
	    			}
       		   }
	       	});	   	
        },
        
//        tuyennx_add_start_20190418 L1PT-464 
        _sendRequestDeleteReject:function(rowId){
        	var _self=this;
        	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH",'HIS_BACSY_YCHUY_DV');
			if(data_ar != null && data_ar.length > 0){
				if(data_ar[0].HIS_BACSY_YCHUY_DV == '1'){
					var rowData1 = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);        	
		        	if(rowData1 != null){
			    		// check quyen xoa du lieu
			    		if(_self.options._modeView != "2"){
			    			var _nguoitaoid=rowData1.NGUOITAO_ID;
			          		 if(_checkRoles(_nguoitaoid,$("#hidUserID").val())==false){
			          			 DlgUtil.showMsg("Bạn không đủ quyền thao tác!");
			          			 return false;
			          		 }
			    		}
			    		//L2PT-20784
		        		if(rowData.KHAMBENHID != _self.options._khambenhid){
		        			DlgUtil.showMsg("Bạn không có quyền thao tác với phiếu này!");
		       			 	return false;
		        		}
		        	}
					var rowData = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('getRowData', rowId);
		        	 if(rowData != null){
		        		 DlgUtil.showConfirm("Bạn có chắc chắn yêu cầu hủy dịch vụ không?",function(flag) {
		            		   if (flag) {      
		            			 var _par = [rowData.MAUBENHPHAMID,rowData.DICHVUKHAMBENHID,0];						
								 var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D033.001",_par.join('$'));    		 
				   	       		 if(_return == 1){
				   	       			 //tuyennx_edit_start_20190425 L1PT-661
									 if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KBH_TATTHONGBAO_KBHB') == "0" ){
										 DlgUtil.showMsg("Yêu cầu hủy dịch vụ thành công");    
									 }
									//tuyennx_edit_end_20190425 L1PT-661
				   	       		         		    		   	       			
				   	       			 _self._initWidget();
				   	       		 } else if (_return == 2){
				   	       			 DlgUtil.showMsg("Dịch vụ chưa thu tiền");    
				   	       		 } else if (_return == 3){
				   	       			 DlgUtil.showMsg("Phiếu đã được tiếp nhận hoặc có kết quả, không thể yêu cầu hủy dịch vụ");    
				   	       		 } else {
				   	       			 DlgUtil.showMsg("Yêu cầu hủy dịch vụ thất bại");		 
				   	       		 }			
		            		   } 
		            		 });    		 
		        	 }
				}
			}
        },
        _undoRequestDeleteReject:function(rowId){
        	var _self=this;
        	var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH",'HIS_BACSY_YCHUY_DV');
			if(data_ar != null && data_ar.length > 0){
				if(data_ar[0].HIS_BACSY_YCHUY_DV == '1'){
					var rowData = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('getRowData', rowId);
		        	
		        	 if(rowData != null && rowData.YC_HOAN == '1'){
		        		 DlgUtil.showConfirm("Bạn có chắc chắn yêu cầu khôi phục dịch vụ không?",function(flag) {
		            		   if (flag) {      
		            			 var _par = [rowData.MAUBENHPHAMID,rowData.DICHVUKHAMBENHID];						
								 var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D024.002",_par.join('$'));    		 
				   	       		 if(_return == 1){
				   	       		      DlgUtil.showMsg("Yêu cầu khôi phục dịch vụ thành công");      		    		   	       			
				   	       			 _self._initWidget();
				   	       		 } else if (_return == 2){
				   	       			 DlgUtil.showMsg("Dịch vụ chưa thu tiền");    
				   	       		 } else {
				   	       			 DlgUtil.showMsg("Yêu cầu khôi phục dịch vụ thất bại");		 
				   	       		 }			
		            		   } 
		            		 });    		 
		        	 } else {
		        		 DlgUtil.showMsg("Dịch vụ không yêu cầu hủy nên không thể khôi phục");		
		        	 }
				}
			}
        	
        },
//      tuyennx_add_end_20190418 L1PT-464 
        
        //tuyennx_add_start HISL2TK-544
        _inDon1Sao:function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+_self.options._grdChiTietThuoc1Sao).jqGrid('getRowData', rowId);
        	 if(rowData != null){   
        		//tuyennx_edit_start_20190108 L2PT-295
 				if(cfObj.SUDUNGTHUOCSAO_HC == 1){   
 		        		 var par = [ {
 		      				name : 'maubenhphamid',
 		      				type : 'String',
 		      				value : rowData.PHIEU_HC
 		      			}];
 		      			openReport('window', "NTU045_TRICHBIENBANHOICHAN_40BV01_QD4069_A4", "pdf", par);   
 				}else{
 					var par = [ {name:'dichvukhambenhid',
 	        			type:'String',
 	        			value:rowData.DICHVUKHAMBENHID}];
 					openReport('window', 'BIENBANHOICHAN_THUOC_1SAO_TT40_A4', 'pdf', par);    		
 				}	
 				//tuyennx_edit_end_20190108
        	 }
        },
        _inDon2Sao:function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+_self.options._grdChiTietThuoc2Sao).jqGrid('getRowData', rowId);
        	 if(rowData != null){      	
        		//tuyennx_edit_start_20190108 L2PT-295
//  				if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'SUDUNGTHUOCSAO_HC') == 1){   
//  					var par = [ {name:'dichvukhambenhid',
//  	        			type:'String',
//  	        			value:rowData.PHIEU_HCLD}];
//  						openReport('window', 'BIENBANHOICHAN_THUOC_2SAO_TT40_A4', 'pdf', par);   
////  					var par = [ {
////  		      				name : 'maubenhphamid',
////  		      				type : 'String',
////  		      				value : rowData.PHIEU_HC
////  		      			}];
////  		      		openReport('window', "NTU045_TRICHBIENBANHOICHAN_40BV01_QD4069_A4", "pdf", par);   
//  				}else{
  					var par = [ {name:'dichvukhambenhid',
  	        			type:'String',
  	        			value:rowData.DICHVUKHAMBENHID}];
  					openReport('window', 'BIENBANHOICHAN_THUOC_2SAO_TT40_A4', 'pdf', par);   
  				//}	
  				//tuyennx_edit_end_20190108		
        	 }
        },
        _updatePhieu1Sao: function(rowId){     
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._grdChiTietThuoc1Sao).jqGrid('getRowData', rowId);
    		if(rowData != null){
				//tuyennx_edit_start_20190108 L2PT-295 L2PT-5682
    			var _HoiChan1sao = rowData.LOAIHOICHAN;
				var _HoiChanKS = rowData.HOICHANKS;
				var myVar={
						DICHVUKHAMBENHID : rowData.DICHVUKHAMBENHID,
    					TIEPNHANID : $("#hidTIEPNHANID").val(),
    					KHAMBENHID : rowData.KHAMBENHID,
						HoiChan1sao : _HoiChan1sao,
						HoiChanKS : _HoiChanKS,
						CHUY: ''
					};
				if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KT_GOPCHUNG_CANHBAO') != 1){
					if(cfObj.SUDUNGTHUOCSAO_HC == 1)
						dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K069_ThuocMotSao2",myVar,"HỘI CHẨN THUỐC 1 SAO",1050,600);
					else
						dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K069_ThuocMotSao",myVar,"HỘI CHẨN THUỐC 1 SAO",800,500);
				}else{
					dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K069_PopupHoiChan",myVar
							,"HỘI CHẨN THUỐC",900,90+_HoiChan1sao*240+_HoiChanKS*310);	
				}
				
				
				//tuyennx_edit_end_20190108
				DlgUtil.open("dlgTHUOCSAO");	
    		}
        },
        _updatePhieu2Sao: function(rowId){     
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._grdChiTietThuoc2Sao).jqGrid('getRowData', rowId);
    		if(rowData != null){
    			var myVar={
    					DICHVUKHAMBENHID : rowData.DICHVUKHAMBENHID,
    					TIEPNHANID : $("#hidTIEPNHANID").val(),
    					KHAMBENHID : rowData.KHAMBENHID
					};
    			//tuyennx_edit_start_20190108 L2PT-295
				if(cfObj.SUDUNGTHUOCSAO_HC == 1)
					dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K070_ThuocHaiSao_New",myVar,"HỘI CHẨN THUỐC 2 SAO",1150,550);
					//dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K070_ThuocHaiSao2",myVar,"HỘI CHẨN THUỐC 2 SAO",1050,600);
				else
					dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K070_ThuocHaiSao",myVar,"HỘI CHẨN THUỐC 2 SAO",800,500);
				//tuyennx_edit_end_20190108
    			DlgUtil.open("dlgTHUOCSAO");	
    		}
        },
      //tuyennx_edit_start_20190108 L2PT-5682
        //L2PT-107978
        _updatePhieuPT: function(rowId){     
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._grdChiTietThuocPT).jqGrid('getRowData', rowId);
    		if(rowData != null){
				var myVar={
						DICHVUKHAMBENHID : rowData.DICHVUKHAMBENHID,
    					TIEPNHANID : $("#hidTIEPNHANID").val(),
    					KHAMBENHID : rowData.KHAMBENHID,
    					PHANTANGID : rowData.PHANTANGID,
    					HOSOBENHANID : $("#hidHOSOBENHANID").val(),
						CHUY: ''
					};
				dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCPT","divDlg","manager.jsp?func=../ngoaitru/NGT02K070_ThuocPhanTang",myVar
						,"Phân tầng nguy cơ nhiểm khuẩn",1000,500);		
				
    			DlgUtil.open("dlgTHUOCPT");	
    		}
        },
        _updatePhieuKS: function(rowId){     
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._grdChiTietThuocKS).jqGrid('getRowData', rowId);
    		if(rowData != null){
    			var _HoiChan1sao = rowData.LOAIHOICHAN;
				var _HoiChanKS = rowData.HOICHANKS;
				var myVar={
						DICHVUKHAMBENHID : rowData.DICHVUKHAMBENHID,
    					TIEPNHANID : $("#hidTIEPNHANID").val(),
    					KHAMBENHID : rowData.KHAMBENHID,
						HoiChan1sao : _HoiChan1sao,
						HoiChanKS : _HoiChanKS,
						CHUY: ''
					};
				dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K069_PopupHoiChan",myVar
						,"HỘI CHẨN THUỐC",900,90+_HoiChan1sao*240+_HoiChanKS*470);		//L2PT-15516
				
    			DlgUtil.open("dlgTHUOCSAO");	
    		}
        },
        //L2PT-10776
        _insertPhieuKS: function(rowId){     
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._gridPhieuthuocDetail).jqGrid('getRowData', rowId);
        	var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
        	var rowData1 = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowKey);
        	if(rowData != null){
        		var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD",[$("#hidMAUBENHPHAMID").val()].join('$'));
        		
				var myVar={
						DICHVUKHAMBENHID : rowData.DICHVUKHAMBENHID,
    					TIEPNHANID : $("#hidTIEPNHANID").val(),
    					KHAMBENHID : rowData1.KHAMBENHID,
    					ICD10NAME : data_btkd[0].CHANDOAN,
						ICD10CODE : data_btkd[0].MACHANDOAN,
						//checkKeChiTiet : '1',
						HoiChan1sao : '0',
						HoiChanKS : '1',
						CHUY: ''
					};
				dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K069_PopupHoiChan",myVar
						,"HỘI CHẨN THUỐC",900,600);		
				
    			DlgUtil.open("dlgTHUOCSAO");	
    		}
        },
      //L2PT-55213
        _capnhatcachdung: function(rowId){     
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._gridPhieuthuocDetail).jqGrid('getRowData', rowId);
        	var rowKey = $('#' + _self.containerId+_self.options._grdPhieuthuoc).getGridParam("selrow");
        	var rowData1 = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowKey);
        	if(rowData1.FLAG_CA == '1'){	
				DlgUtil.showMsg("Phiếu đã ký số không thể chỉnh sửa!");
    			return;
			}
        	if(rowData != null){
        		var paramInput={							
						loai : "1", 
	   					maubenhphamid : rowData.MAUBENHPHAMID, 
	   					dichvukhambenhid : rowData.DICHVUKHAMBENHID,  
	   					sophieu : rowData1.SOPHIEU, 
	   					tendichvu : rowData.TENDICHVU, 
	   					lieudung : rowData.LIEUDUNG,
	   					cachdung : rowData.HUONGDANSUDUNG, //L2PT-12852
						songaysudungKS: rowData.SOLAN_SD_KHANGSINH,
						lnmbp : '7', 
						from : 'tabThuoc',
	   					mahosobenhan : $("#hidHOSOBENHANID").val(),
						duongdung : rowData.DUONGDUNG
   					};
   					dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K089_SUACHITIET",paramInput,"Cập nhật liều dùng/mã bệnh",500,300);
   					DlgUtil.open("divDlgDichVu");
   					EventUtil.setEvent("ev_dongcuaso", function(e) {
   						DlgUtil.close("divDlgDichVu");
   						_self._viewPhieuVatTuDetail(rowKey); 
   					});
    		}
        },
        _inDonKS:function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+_self.options._grdChiTietThuocKS).jqGrid('getRowData', rowId);
        	 if(rowData != null){      	
  					var par = [ {name:'dichvukhambenhid',
  	        			type:'String',
  	        			value:rowData.DICHVUKHAMBENHID}];
  					openReport('window', 'PHIEUSUDUNG_KHANGSINH_A4', 'pdf', par);   
        	 }
        },
        //L2PT-15516
        _inDonKSKySo:function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+_self.options._grdChiTietThuocKS).jqGrid('getRowData', rowId);
        	 if(rowData != null){
				 var _params = [
					 {
						 name: 'hosobenhanid',
						 type: 'String',
						 value: $("#hidHOSOBENHANID").val()
					 }, {
						 name: 'dichvukhambenhid',
						 type: 'String',
						 value: rowData.DICHVUKHAMBENHID
					 }, {
						 name: 'RPT_CODE',
						 type: 'String',
						 value: 'BIENBANHOICHAN_THUOC_2SAO_TT40_A4'
					 }
				 ];
	    		CommonUtil.openReportGetCA2(_params, false);
        	 }
        },
        //tuyennx_add_end
        _deletePhieuThuocVatTu: function(rowId){
        	var _self=this;        	
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);        	
        	if(rowData != null){
    		// check quyen xoa du lieu L2PT-17531
//    		if(_self.options._modeView != "2"){
//    			var _nguoitaoid=rowData.NGUOITAO_ID;
//          		 if(_checkRoles(_nguoitaoid,$("#hidUserID").val())==false && jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KEDON_QUYENHUYXOA') == "0"){
//          			 DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
//          			 return false;
//          		 }
//    		}
    		//START L2PT-4775
       		 var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_THUOC_CHECKXOA_USER");
			 if(data_ar[0].NTU_THUOC_CHECKXOA_USER == '1')
    		 {
				if (_nguoitaoid != $("#hidUserID").val() ) {
					DlgUtil.showMsg("Bạn không có quyền xóa phiếu này!");
					return false;
				}
    		 }
			//L2PT-20784
     		if(rowData.KHAMBENHID != _self.options._khambenhid){
     			DlgUtil.showMsg("Bạn không có quyền thao tác với phiếu này!");
    			 	return false;
     		}
        		
        	//tuyennx_add_start_L2PT-25931
			var par=[];
      		par.push({"name":"[0]","value":rowData.MAUBENHPHAMID});
      		var checkThutien = jsonrpc.AjaxJson.getOneValue("CHECK.THUTIEN", par);
      		if(parseInt(checkThutien) > 0){
      			DlgUtil.showMsg("Đã có thuốc/Vật tư thu tiền!");
       			return false;
      		}
      		//tuyennx_add_end_L2PT-25931
      		
        	var _trangthai=rowData.TRANGTHAIMAUBENHPHAM; 
    		if(_trangthai==null) _trangthai=0;
    		_trangthai=parseInt(_trangthai);
    		//tuyennx_edit_start L2DKBD-1032
    		var XOA_CAPNHAT_PHIEU_KOCANHUY = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','XOA_CAPNHAT_PHIEU_KOCANHUY');
	    		if(_trangthai<=1 || XOA_CAPNHAT_PHIEU_KOCANHUY == 1){
	    			DlgUtil.showConfirm("Bạn có chắc chắn xóa phiếu thuốc không?",function(flag) {
    	       		   if (flag) {
    	       			   //huy yeu cau truoc khi xoa
    	       			   if(_trangthai > 1){
    	       				   var rs = _self._deleteRequest(rowId,0); 
    	       				   if(rs == 1) return;
    	       			   }
    	       			   //tuyennx_edit_end
	    	       			var _par = [rowData.MAUBENHPHAMID];
	    	       			//tuyennx_add_start
	    	       			var data_btkd ;
	    	       			if(jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','BTKD_DAY_BYT') ==1)
	    	       				data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD",[rowData.MAUBENHPHAMID].join('$'));
	    	       			//tuyennx_add_start
	    	       			//L2PT-19905
		    	       		 if(rowData.DAYDTDT == 1){
	   	   	       				 _self._daydonthuoconline(rowData.MAUBENHPHAMID);
	   	   	       			 }
		   	   	       		 var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("NT.DEL.034",_par.join('$'));   					
		   	   	       		 if(_return >= 1){
		   	   	       			 
		   	   	       			 //tuyennx_edit_start_20190425 L1PT-661 L2PT-14910
		   	   	       			 DlgUtil.showMsg('Xóa thành công phiếu thuốc!',undefined,jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'HIS_TIMEOUT_THONGBAO'));
								//tuyennx_edit_end_20190425 L1PT-661
		   	   	       			 if(jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','BTKD_DAY_BYT') == 1){
		   	   	       				 _self._daybtkd(rowData.MAUBENHPHAMID, data_btkd);
		   	   	       			 }
		   	   	       			 _self._initWidget();
		   	   	       		 }else if(_return == -1){
		   	   	       			 DlgUtil.showMsg("Xóa không thành công phiếu thuốc");
		   	   	       		 }
		   	   	       		 
		   	   	       		 else //L2PT-17531
		   	   	       			 DlgUtil.showMsg(_return);
    	       		   } 
    	       		 });	    			
	    		}else{
	    			DlgUtil.showMsg("Phiếu đã được xử lý nên không thể hủy yêu cầu");
	    		}
        	}
        },
        _editOrgDone: function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	 if(rowData != null){
        		// check quyen xoa du lieu
        		 var _nguoitaoid=rowData.NGUOITAO_ID;
        		 if(_checkRoles(_nguoitaoid,$("#hidUserID").val())==false){
        			 DlgUtil.showMsg("Bạn không có quyền sửa phòng chỉ định phiếu này!");
        			 return false;
        		 }
        		 
        	    var _trangthai=rowData.TRANGTHAIMAUBENHPHAM; 
           		if(_trangthai==null) _trangthai=0;
           		_trangthai=parseInt(_trangthai);          		
           		if(_trangthai==3){
           			 DlgUtil.showMsg("Phiếu đã hoàn thành nên bạn không thể sửa phòng chỉ định");
         			 return false;
           		} 
           		//L2PT-20784
        		if(rowData.KHAMBENHID != _self.options._khambenhid){
        			DlgUtil.showMsg("Bạn không có quyền thao tác với phiếu này!");
       			 	return false;
        		}
				 //mo popup nhap benh nhan khi dbclick on row
				 var paramInput={							
							maubenhphamid : rowData.MAUBENHPHAMID,
							phongid : rowData.PHONGID,
							loaiphong : $("#hidLOAITIEPNHANID").val() == 0? 3:($("#hidLOAITIEPNHANID").val() == 3?9:2),//L2PT-10588
							loaiPhieu : _self.options._lnmbp							
					};				
					dlgPopup=DlgUtil.buildPopupUrl("divDlgEditOrgDone","divDlg","manager.jsp?func=../noitru/NTU02D039_SuaPhongChiDinh",paramInput,"Chọn phòng chỉ định",900,440);
					DlgUtil.open("divDlgEditOrgDone");	
			   }	
        },
		//L2K74TW-301 - hongdq
        _editPhieuDT: function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+_self.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	 if(rowData != null){
        		 var dataObj = jsonrpc.AjaxJson.ajaxExecuteQueryO(
 						"NTU02D009.EV004", RSUtil.buildParam("",
 								[ rowData.MAUBENHPHAMID ]));
        		 var data_ar = $.parseJSON(dataObj);
 				 if (data_ar != null && data_ar.length > 0) {
 					data = data_ar[0];
 					/*sql_par = RSUtil.buildParam("",
							[ rowData.KHAMBENHID, "4" ]);
 					ComboUtil.getComboTag("cboMAUBENHPHAMID",
							"COM.PHIEUDIEUTRI", sql_par,
							data.PHIEUDIEUTRIID == null ? ''
									: data.PHIEUDIEUTRIID, {
								value : '',
								text : 'Chưa có phiếu điều trị'
							}, 'sql', '', function() {

							});*/
 					
 					var paramInput={							
 							maubenhphamid : rowData.MAUBENHPHAMID,
 							khambenhid : rowData.KHAMBENHID,
 							phieudieutriid : data.PHIEUDIEUTRIID,
							tgchidinh: data.TGCHIDINH
 					};	
 	 				 
 	 				dlgPopup=DlgUtil.buildPopupUrl("divDlgeditPhieuDT","divDlg","manager.jsp?func=../noitru/NTU02D081_Capnhat_PhieuDT",paramInput,"Cập nhật phiếu điều trị",600,360);
 					DlgUtil.open("divDlgeditPhieuDT");
 				 }
			  }	
        },
        //L2PT-45654
        _countSttKS: function(rowId){
        	var _json = new Object(); 
        	_json.TIEPNHANID = $("#hidTIEPNHANID").val();
        	var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.DEMSTT.THUOC", JSON.stringify(_json));
        	if(ret == '1'){
        		DlgUtil.showMsg("Đánh STT dùng thuốc thành công!");
        	}
        	else
        		DlgUtil.showMsg("Đánh STT dùng thuốc thất bại!");
        },
		//L2K74TW-301 - hongdq
        _copyPhieuThuocVatTu: function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
        	if(rowData != null){
        		// check quyen xoa du lieu
	       		 var _nguoitaoid=rowData.NGUOITAO_ID;
	       		 //tuyennx_edit_start_20181023 L2HOTRO-10107
	       		 if(_checkRoles(_nguoitaoid,$("#hidUserID").val())==false && jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','CHECK_QUYEN_COPY_THUOC')!=1){
	       		 //tuyennx_edit_end_20181023 
	       			 DlgUtil.showMsg("Bạn không có quyền tạo bản sao cho phiếu này!");
	       			 return false;
	       		 }
	       		 //L2PT-20784
        		if(rowData.KHAMBENHID != _self.options._khambenhid){
        			DlgUtil.showMsg("Bạn không có quyền thao tác với phiếu này!");
       			 	return false;
        		}
	       		 
	       		 var thoigianvaovien = $('#hidTHOIGIANVAOVIEN').val();	       		 
	       		 var paramInput={							
						maubenhphamid : rowData.MAUBENHPHAMID,
						type:'7',
						thoigianvaovien:thoigianvaovien,
						khambenhid : rowData.KHAMBENHID,
						todieutri : '1'
						
				};				
				dlgPopup=DlgUtil.buildPopupUrl("divDlgCopyMbp","divDlg","manager.jsp?func=../noitru/NTU02D070_ThoiGianDonThuoc",paramInput,"Tạo bản sao",600,360);
				DlgUtil.open("divDlgCopyMbp");
        	}
        },
      //đẩy cổng bộ y tế với đơn mua ngoài, đơn thuốc nhà thuốc
        _daybtkd: function(ret_maubenhpham_id, data_btkd){   	
    		var BTKD_DAY_BYT = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','BTKD_DAY_BYT');
    		if(BTKD_DAY_BYT == 1){
//    			var sql_par = [];
//    			sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
//    	   		jsonrpc.AjaxJson.ajaxExecuteQuery("BTKD.UPDATE.MADT", sql_par);
    	   		var objSend = new Object(); 
    	   		var objData = new Object(); 
    	   		var objHeader = new Object(); 
    	   		var objBody = new Object();
    	   		var objSeccurity = new Object();
    	   		var objDonThuoc = new Object();
    	   		var objThongTinBN = new Object();
    	   		var objDSChiTietThuoc = new Object(); 
    	   		
    	   		var BTKD_WS_URL = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_WS_URL');
    	   		var BTKD_WS_USER = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_WS_USER');
    	   		var BTKD_WS_PASS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_WS_PASS');
    	   		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",[ret_maubenhpham_id].join('$'));
    	   		
    	   		//var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD",[ret_maubenhpham_id].join('$'));
    	   		//tao header
    	   		objHeader.SENDER_CODE = _opts.hospital_code;
    	   		objHeader.SENDER_NAME = data_ar[0].TEN_CSYT;
    	   		objHeader.TRANSACTION_TYPE = "M0002";
    	   		objHeader.TRANSACTION_NAME = "FTP";
    	   		objHeader.TRANSACTION_ID = "";
    	   		objHeader.ACTION_TYPE = "2";
    	   		objHeader.PROVIDER ="12";

    	   		//tao don thuoc
    	   		objDonThuoc.DON_THUOC_ID =data_btkd[0].MAUBENHPHAMID;
    	   		objDonThuoc.MA_DON_THUOC =_opts.hospital_code + "."+ data_btkd[0].MA_DON_THUOC;
    	   		objDonThuoc.MA_LUOT_KHAM =data_btkd[0].HOSOBENHANID;
    	   		objDonThuoc.LOAI_DON_VI ="1";
    	   		objDonThuoc.MA_XAC_THUC ="";
    	   		objDonThuoc.MA_DON_VI =_opts.hospital_code;
    	   		objDonThuoc.MA_DINH_DANH =data_btkd[0].HSSK_MADD;
    	   		objDonThuoc.HO_TEN_BN =data_btkd[0].TENBENHNHAN;
    	   		objDonThuoc.NGAY_SINH =data_btkd[0].NGAYSINH;
    	   		objDonThuoc.GIOI_TINH =data_btkd[0].GIOITINHID;
    	   		objDonThuoc.MA_THE =data_btkd[0].MA_BHYT;
    	   		objDonThuoc.MA_BENH =data_btkd[0].MACHANDOAN;
    	   		objDonThuoc.TEN_BENH =data_btkd[0].CHANDOAN;
    	   		objDonThuoc.SD_TU_NGAY =data_btkd[0].NGAYMAUBENHPHAM_SUDUNG;
    	   		objDonThuoc.SD_DEN_NGAY =data_btkd[0].SD_DEN_NGAY;
    	   		objDonThuoc.HD_SD =data_btkd[0].LOIDANBACSI;
    	   		objDonThuoc.SO_THANG =data_btkd[0].SLTHANG;
    	   		objDonThuoc.NGAY_CAP =data_btkd[0].NGAYMAUBENHPHAM;
    	   		objDonThuoc.MA_BAC_SY =data_btkd[0].MA_BAC_SI;
    	   		objDonThuoc.TEN_BAC_SY =data_btkd[0].OFFICER_NAME;
    	   		objDonThuoc.SO_GPHN ="";
    	   		objDonThuoc.MA_KHOA =data_btkd[0].KHOAID;
    	   		objDonThuoc.NHA_THUOC_ID ="";
    	   		objDonThuoc.MA_NHA_THUOC ="";
    	   		objDonThuoc.TEN_NHA_THUOC ="";
    	   		objDonThuoc.TEN_DUOC_SY ="";
    	   		objDonThuoc.NGAY_BAN ="";
    	   		objDonThuoc.MA_TINH =data_btkd[0].MA_TINH;
    	   		
    	   	    //tao obj benh nhan
    	   		objThongTinBN.MABN = data_btkd[0].MABENHNHAN;
    	   		objThongTinBN.MATINH_KHAISINH = data_btkd[0].MATINH_KHAISINH;
    	   		objThongTinBN.SOCMND = data_btkd[0].SOCMTND;
    	   		objThongTinBN.NGAYCAP = data_btkd[0].NGAYCAPCMND;
    	   		objThongTinBN.NOICAP = "";
    	   		objThongTinBN.DIACHI_THUONGTRU = "";
    	   		objThongTinBN.MATINH_THUONGTRU = "";
    	   		objThongTinBN.MAHUYEN_THUONGTRU = "";
    	   		objThongTinBN.MAXA_THUONGTRU = "";
    	   		objThongTinBN.MATHONXOM_THUONGTRU = "";
    	   		objThongTinBN.DIACHI_HIENTAI = data_btkd[0].DIACHI;
    	   		objThongTinBN.MATINH_HIENTAI = data_btkd[0].MATINH_KHAISINH;
    	   		objThongTinBN.MAHUYEN_HIENTAI = data_btkd[0].MAHUYEN_HIENTAI;
    	   		objThongTinBN.MAXA_HIENTAI = data_btkd[0].MAXA_HIENTAI;
    	   		objThongTinBN.MATHONXOM_HIENTAI = "";
    	   		objThongTinBN.DIENTHOAI_CD = data_btkd[0].SDTBENHNHAN;
    	   		objThongTinBN.DIENTHOAI_DD = "";
    	   		objThongTinBN.EMAIL = "";
    	   		
    	   		
    	   		var objChiTietThuoc = [];
    			for(var i=0;i< data_btkd.length;i++){
    				var objThuoc = new Object();
    				objThuoc.DON_THUOC_ID = data_btkd[i].MAUBENHPHAMID;
    				objThuoc.STT = data_btkd[i].THUTU;
    				objThuoc.MA_NHOM = "";
    				objThuoc.SO_DANG_KY = data_btkd[i].SODANGKY;
    				objThuoc.MA_THUOC = data_btkd[i].MA;
    				objThuoc.TEN_THUOC = data_btkd[i].TEN;
    				objThuoc.DON_VI_TINH = data_btkd[i].TEN_DVT;
    				objThuoc.HAM_LUONG = data_btkd[i].LIEULUONG;
    				objThuoc.DUONG_DUNG = data_btkd[i].DUONGDUNG;
    				objThuoc.LIEU_DUNG = data_btkd[i].LIEUDUNG;
    				objThuoc.DONG_GOI = data_btkd[i].DONGGOI;
    				objThuoc.SO_LUONG = data_btkd[i].SOLUONG;
    				objThuoc.DON_GIA = data_btkd[i].TIEN_CHITRA;
    				objThuoc.TYLE_TT = "100";
    				objThuoc.MUC_HUONG = "0";
    				objThuoc.THANH_TIEN = data_btkd[i].THANH_TIEN;
    				objThuoc.T_NGUON_KHAC = "0";
    				objThuoc.T_BNTT = "0";
    				objThuoc.GHI_CHU = data_btkd[i].GHI_CHU;
    				objChiTietThuoc.push(objThuoc);

    			}
    			objDSChiTietThuoc.CHI_TIET_THUOC = objChiTietThuoc;
    			
    	   		objBody.DON_THUOC = objDonThuoc;
    	   		objBody.THONGTINBENHNHAN = objThongTinBN;
    	   		objBody.DSACH_CHI_TIET_THUOC = objDSChiTietThuoc;
    	   		
    	   		objData.HEADER = objHeader;
    	   		objData.BODY = objBody;
    	   		objData.SECURITY = objSeccurity;
    	   		objData.HEADER = objHeader;
    	   		objSend.DATA = objData;
    	   		
    	   		
    	   	
    			var x2js = new X2JS();
    			objSend = JSON.stringify(objSend);
    			var obj =  x2js.json2xml_str($.parseJSON(objSend));
    			obj = obj.replace(/&lt;/g,'<').replace(/&gt;/g,'>').replace(/&amp;/g,'&');
    			obj = btoa(unescape(encodeURIComponent(obj)));//atob(obj);
    			
    			//gui lay ma dinh danh va MK
    			// 20190926 fix ATBM bo truyen url_ws
    			var _hid=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');
    			var resultCongBTKD = ajaxSvc.CongBTKDWS.tiepNhanDonThuoc_new(_hid
    					, BTKD_WS_USER
    					, BTKD_WS_PASS,
    					obj); 	
    			var rs = resultCongBTKD.split(';');
    			if(rs[0] != 0){
    				DlgUtil.showMsg("Lỗi đẩy dữ liệu bán thuốc kê đơn cổng bộ y tế: " +rs[1]);
    				//return 1;
    			}
    		}
    	},
    	
    	 //xoa don thuoc online L2PT-19905
    	_daydonthuoconline: function(ret_maubenhpham_id){   	
			var objData = {
				    "FUNC" : "xoaDonThuocOnline",
				    "PARAM" : {
				        "i_maubenhphamid" : ret_maubenhpham_id
				    }
			};
			var request_url = '/vnpthis/service/donThuocOnline';
			$.ajax({
				type : "POST",
				contentType : "application/json; charset=utf-8",
				data : JSON.stringify(objData),
				url : request_url,
				beforeSend : function(xhr) {
					xhr.setRequestHeader('Authorization', 'Bearer ' + jsonrpc.AjaxJson.uuid);
				},
				success : function(data) {
					console.log("response success=" + JSON.stringify(data));
					if(data.responseCode != '200'){
						DlgUtil.showMsg("Lỗi xóa dữ liệu cổng đơn thuốc điện tử:" + data.responseMessage);
						setTimeout(function(){
							 }, 2000);
						return 0;
					}
				},
				error : function(xhr) {
					DlgUtil.showMsg("Lỗi xóa dữ liệu cổng đơn thuốc điện tử!");
				}
			});
    	},
    	
    	//đẩy cổng bộ y tế với đơn mua ngoài, đơn thuốc nhà thuốc
    	//L2PT-13999
    	_sendCong: function(ret_maubenhpham_id, r_action, options){	
    		var BTKD_DAY_BYT = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','BTKD_DAY_BYT');
    		var ds_option = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_MA_OPTION_DAY');
    		var ds_loaitiepnhan = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_LOAITIEPNHAN_DAY');
    		var ds_options = ds_option.split(',');

    		if(BTKD_DAY_BYT == 1 && (ds_option == '0' || $.inArray( options, ds_options) >= 0)
    				&& (ds_loaitiepnhan.includes($("#hidLOAITIEPNHANID").val()) )){
//    			var sql_par = [];
//    			sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
//    	   		jsonrpc.AjaxJson.ajaxExecuteQuery("BTKD.UPDATE.MADT", sql_par);
    	   		var objSend = new Object(); 
    	   		var objData = new Object(); 
    	   		var objHeader = new Object(); 
    	   		var objBody = new Object();
    	   		var objSeccurity = new Object();
    	   		var objDonThuoc = new Object();
    	   		var objThongTinBN = new Object();
    	   		var objDSChiTietThuoc = new Object(); 
    	   		
    	   		var BTKD_WS_URL = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_WS_URL');
    	   		var BTKD_WS_USER = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_WS_USER');
    	   		var BTKD_WS_PASS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_WS_PASS');
    	   		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",[ret_maubenhpham_id].join('$'));
    	   		
    	   		var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD",[ret_maubenhpham_id].join('$'));
    	   		//tao header
    	   		objHeader.SENDER_CODE = _opts.hospital_code;
    	   		objHeader.SENDER_NAME = data_ar[0].TEN_CSYT;
    	   		objHeader.TRANSACTION_TYPE = "M0002";
    	   		objHeader.TRANSACTION_NAME = "FTP";
    	   		objHeader.TRANSACTION_ID = "";
    	   		objHeader.PROVIDER ="12";
    	   		if(r_action == "Upd")
    	   			objHeader.ACTION_TYPE = "1";
    	   		else
    	   			objHeader.ACTION_TYPE = "0";
    	   		//tao don thuoc
    	   		objDonThuoc.DON_THUOC_ID =data_btkd[0].MAUBENHPHAMID;
    	   		objDonThuoc.MA_DON_THUOC =_opts.hospital_code+"."+ data_btkd[0].MA_DON_THUOC;
    	   		objDonThuoc.MA_LUOT_KHAM =data_btkd[0].HOSOBENHANID;
    	   		objDonThuoc.LOAI_DON_VI ="1";
    	   		objDonThuoc.MA_XAC_THUC ="";
    	   		objDonThuoc.MA_DON_VI =_opts.hospital_code;
    	   		objDonThuoc.MA_DINH_DANH =data_btkd[0].HSSK_MADD;
    	   		objDonThuoc.HO_TEN_BN =data_btkd[0].TENBENHNHAN;
    	   		objDonThuoc.NGAY_SINH =data_btkd[0].NGAYSINH;
    	   		objDonThuoc.GIOI_TINH =data_btkd[0].GIOITINHID;
    	   		objDonThuoc.MA_THE =data_btkd[0].MA_BHYT;
    	   		objDonThuoc.MA_BENH =data_btkd[0].MACHANDOAN;
    	   		objDonThuoc.TEN_BENH =data_btkd[0].CHANDOAN;
    	   		objDonThuoc.SD_TU_NGAY =data_btkd[0].NGAYMAUBENHPHAM_SUDUNG;
    	   		objDonThuoc.SD_DEN_NGAY =data_btkd[0].SD_DEN_NGAY;
    	   		objDonThuoc.HD_SD =data_btkd[0].LOIDANBACSI;
    	   		objDonThuoc.SO_THANG =data_btkd[0].SLTHANG;
    	   		objDonThuoc.NGAY_CAP =data_btkd[0].NGAYMAUBENHPHAM;
    	   		objDonThuoc.MA_BAC_SY =data_btkd[0].MA_BAC_SI;
    	   		objDonThuoc.TEN_BAC_SY =data_btkd[0].OFFICER_NAME;
    	   		objDonThuoc.SO_GPHN ="";
    	   		objDonThuoc.MA_KHOA =data_btkd[0].KHOAID;
    	   		objDonThuoc.NHA_THUOC_ID ="";
    	   		objDonThuoc.MA_NHA_THUOC ="";
    	   		objDonThuoc.TEN_NHA_THUOC ="";
    	   		objDonThuoc.TEN_DUOC_SY ="";
    	   		objDonThuoc.NGAY_BAN ="";
    	   		objDonThuoc.MA_TINH =data_btkd[0].MA_TINH;
    	   		
    	   	    //tao obj benh nhan
    	   		objThongTinBN.MABN = data_btkd[0].MABENHNHAN;
    	   		objThongTinBN.MATINH_KHAISINH = data_btkd[0].MATINH_KHAISINH;
    	   		objThongTinBN.SOCMND = data_btkd[0].SOCMTND;
    	   		objThongTinBN.NGAYCAP = data_btkd[0].NGAYCAPCMND;
    	   		objThongTinBN.NOICAP = "";
    	   		objThongTinBN.DIACHI_THUONGTRU = "";
    	   		objThongTinBN.MATINH_THUONGTRU = "";
    	   		objThongTinBN.MAHUYEN_THUONGTRU = "";
    	   		objThongTinBN.MAXA_THUONGTRU = "";
    	   		objThongTinBN.MATHONXOM_THUONGTRU = "";
    	   		objThongTinBN.DIACHI_HIENTAI = data_btkd[0].DIACHI;
    	   		objThongTinBN.MATINH_HIENTAI = data_btkd[0].MATINH_KHAISINH;
    	   		objThongTinBN.MAHUYEN_HIENTAI = data_btkd[0].MAHUYEN_HIENTAI;
    	   		objThongTinBN.MAXA_HIENTAI = data_btkd[0].MAXA_HIENTAI;
    	   		objThongTinBN.MATHONXOM_HIENTAI = "";
    	   		objThongTinBN.DIENTHOAI_CD = data_btkd[0].SDTBENHNHAN;
    	   		objThongTinBN.DIENTHOAI_DD = "";
    	   		objThongTinBN.EMAIL = "";
    	   		
    	   		
    	   		var objChiTietThuoc = [];
    			for(var i=0;i< data_btkd.length;i++){
    				var objThuoc = new Object();
    				objThuoc.DON_THUOC_ID = data_btkd[i].MAUBENHPHAMID;
    				objThuoc.STT = data_btkd[i].THUTU;
    				 if(_opts.option=='02D010' || _opts.option=='02D017')
    					 objThuoc.CHI_TIET_ID = data_btkd[i].DICHVUKHAMBENHID;
    				 else
    					 objThuoc.CHI_TIET_ID = "";
    				objThuoc.CHI_TIET_ID_CLIENT = data_btkd[i].DICHVUKHAMBENHID;
    				objThuoc.MA_NHOM = "";
    				objThuoc.SO_DANG_KY = data_btkd[i].SODANGKY;
    				objThuoc.MA_THUOC = data_btkd[i].MA;
    				objThuoc.TEN_THUOC = data_btkd[i].TEN;
    				objThuoc.DON_VI_TINH = data_btkd[i].TEN_DVT;
    				objThuoc.HAM_LUONG = data_btkd[i].LIEULUONG;
    				objThuoc.DUONG_DUNG = data_btkd[i].DUONGDUNG;
    				objThuoc.LIEU_DUNG = data_btkd[i].LIEUDUNG;
    				objThuoc.DONG_GOI = data_btkd[i].DONGGOI;
    				objThuoc.SO_LUONG = data_btkd[i].SOLUONG;
    				objThuoc.DON_GIA = data_btkd[i].TIEN_CHITRA;
    				objThuoc.TYLE_TT = "100";
    				objThuoc.MUC_HUONG = "0";
    				objThuoc.THANH_TIEN = data_btkd[i].THANH_TIEN;
    				objThuoc.T_NGUON_KHAC = "0";
    				objThuoc.T_BNTT = "0";
    				objThuoc.GHI_CHU = data_btkd[i].GHI_CHU;
    				objChiTietThuoc.push(objThuoc);

    			}
    			objDSChiTietThuoc.CHI_TIET_THUOC = objChiTietThuoc;
    			
    	   		objBody.DON_THUOC = objDonThuoc;
    	   		objBody.THONGTINBENHNHAN = objThongTinBN;
    	   		objBody.DSACH_CHI_TIET_THUOC = objDSChiTietThuoc;
    	   		
    	   		objData.HEADER = objHeader;
    	   		objData.BODY = objBody;
    	   		objData.SECURITY = objSeccurity;
    	   		objData.HEADER = objHeader;
    	   		objSend.DATA = objData;
    	   	
    			var x2js = new X2JS();
    			objSend = JSON.stringify(objSend);
    			var obj =  x2js.json2xml_str($.parseJSON(objSend));
    			obj = obj.replace(/&lt;/g,'<').replace(/&gt;/g,'>').replace(/&amp;/g,'&');
    			obj = btoa(unescape(encodeURIComponent(obj)));//atob(obj);
    			
    			//gui lay ma dinh danh va MK
    			var _hid=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');
    			var resultCongBTKD = ajaxSvc.CongBTKDWS.tiepNhanDonThuoc_new(_hid
    					, BTKD_WS_USER
    					, BTKD_WS_PASS,
    					obj); 	
    			var rs = resultCongBTKD.split(';');
    			if(rs[0] != 0){
    				DlgUtil.showMsg("Lỗi đẩy dữ liệu bán thuốc kê đơn cổng bộ y tế: " +rs[1]);
    				setTimeout(function(){
    					 }, 2000);
    				
    				//return 1;
    			}
    			else{//L2PT-16266
    				var sql_par = [];
    				sql_par.push({
    					"name" : "[0]",
    					"value" : ret_maubenhpham_id
    				});				
    				jsonrpc.AjaxJson.execute("NGT.UD.BTKD", sql_par);
    				DlgUtil.showMsg("Đẩy đơn thuốc lên cổng thành công");
    			}
    		}
    		else DlgUtil.showMsg("Loại đơn thuốc không được cấu hình đẩy lên cổng");
    	},
    	
    	//tuyennx_add_end
    	
    	//đẩy cổng bộ y tế với đơn  ký CA mua ngoài, đơn thuốc nhà thuốc
    	//L2PT-19740
    	_sendCongCA: function(ret_maubenhpham_id, r_action, options){	
    		var BTKD_DAY_BYT = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','BTKD_DAY_BYT');
    		var ds_option = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_MA_OPTION_DAY');
    		var ds_loaitiepnhan = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_LOAITIEPNHAN_DAY');
    		var ds_options = ds_option.split(',');

    		if(BTKD_DAY_BYT == 1 && (ds_option == '0' || $.inArray( options, ds_options) >= 0)
    				&& (ds_loaitiepnhan.includes($("#hidLOAITIEPNHANID").val()) )){
    			
    			//gui lay ma dinh danh va MK
    			var _hid=jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');
    			
    			var sql_par = [ ret_maubenhpham_id ];
				var xml = jsonrpc.AjaxJson.ajaxCALL_SP_C("BTKD.GETXMLCA", sql_par.join('$'));
				if(!xml){
					DlgUtil.showMsg("Đơn thuốc chưa ký CA ");
					return;
				}
    	   		var BTKD_WS_USER = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_WS_USER');
    	   		var BTKD_WS_PASS = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_WS_PASS');
    			
    	   		xml = xml.replace(/&lt;/g,'<').replace(/&gt;/g,'>').replace(/&amp;/g,'&');
    	   		xml = btoa(unescape(encodeURIComponent(xml)));//atob(obj);
    			var resultCongBTKD = ajaxSvc.CongBTKDWS.tiepNhanDonThuocHisOnline(_hid
    					, BTKD_WS_USER
    					, BTKD_WS_PASS,
    					xml); 	
    			var rs = resultCongBTKD.split(';');
    			if(rs[0] != '200'){
    				DlgUtil.showMsg("Lỗi đẩy dữ liệu bán thuốc kê đơn cổng bộ y tế: " +rs[1]);
    				setTimeout(function(){
    					 }, 2000);
    				
    				//return 1;
    			}
    			else{//L2PT-16266
    				var sql_par = [];
    				sql_par.push({
    					"name" : "[0]",
    					"value" : ret_maubenhpham_id
    				});				
    				jsonrpc.AjaxJson.execute("NGT.UD.BTKD", sql_par);
    				DlgUtil.showMsg("Đẩy đơn thuốc lên cổng thành công");
    			}
    		}
    		else DlgUtil.showMsg("Loại đơn thuốc không được cấu hình đẩy lên cổng");
    	},
    	
    	//tuyennx_add_end
        _updatePhieuVatTu: function(rowId){     
        	var _loaikedon = 0;
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
    		if(rowData != null){ 
    			// check quyen xoa du lieu
    			
    			//tuyennx_edit_start_20200701_L2PT-23505
    			var par=[];
          		par.push({"name":"[0]","value":rowData.MAUBENHPHAMID});
          		var checkDtkh = jsonrpc.AjaxJson.getOneValue("CHECK.KEDTKH", par);
          		
          		//tuyennx_add_start_L2PT-25931
          		var checkThutien = jsonrpc.AjaxJson.getOneValue("CHECK.THUTIEN", par);
          		if(parseInt(checkThutien) > 0){
          			DlgUtil.showMsg("Đã có thuốc/Vật tư thu tiền!");
	       			return false;
          		}
          		//tuyennx_add_end_L2PT-25931

	       		 var _nguoitaoid=rowData.NGUOITAO_ID;
	       		 //START L2PT-4775
	       		 var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH", "NTU_THUOC_CHECKXOA_USER");
				 if(data_ar[0].NTU_THUOC_CHECKXOA_USER == '1')
	    		 {
					if (_nguoitaoid != $("#hidUserID").val() ) {
						DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
						return false;
					}
	    		 }else{
	    			 if(_checkRoles(_nguoitaoid,$("#hidUserID").val())==false &&  checkDtkh == "0" && jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KEDON_QUYENHUYXOA') == "0"){
	    		       		//tuyennx_edit_end_20200701_L2PT-23505
	    		       			 DlgUtil.showMsg("Bạn không có quyền cập nhật phiếu này!");
	    		       			 return false;
	    		     }
	    		 }
				//L2PT-20784
        		if(rowData.KHAMBENHID != _self.options._khambenhid){
        			DlgUtil.showMsg("Bạn không có quyền thao tác với phiếu này!");
       			 	return false;
        		}
	       		 
    			var _trangthai=rowData.TRANGTHAIMAUBENHPHAM;
	        		if(_trangthai==null) _trangthai=0;
	        		_trangthai=parseInt(_trangthai);
	        		//tuyennx_edit_start L2DKBD-1032
	        		var XOA_CAPNHAT_PHIEU_KOCANHUY = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','XOA_CAPNHAT_PHIEU_KOCANHUY');
		    		if(_trangthai<=1 || XOA_CAPNHAT_PHIEU_KOCANHUY == 1){
		    			//huy yeu cau truoc khi sua
 	       			   if(_trangthai > 1){
 	       				   var rs = _self._deleteRequest(rowId,0); 
 	       				   if(rs == 1) return;
 	       			   }
 	       			   //tuyennx_edit_end
	        			var _tenkho = rowData.TENKHO;
		    			var lPhieuMBP=rowData.LOAIPHIEUMAUBENHPHAM;
		    			var _slThang=rowData.SLTHANG;
		    			var _opt="";
		    			var _title="Cập nhật phiếu thuốc";
		    			if(lPhieuMBP == 1){
		    				_opt = "02D010";
		    			}else if(lPhieuMBP == 2){
		    				_opt = "02D014";
		    			}  
		    			
		    			if(_tenkho==null || _tenkho==''){
		    				_opt = "02D011";
		    			}

		    			if(rowData.LOAIKEDON == 1){
		    				_loaikedon = 0;
		    			}else{
		    				_loaikedon = 1;
		    			}
		    			 //tuyennx_add_start
		    			if(rowData.LOAIKEDONTHUOC == '19'){
		    				_opt = "02D011";
		    			}
		    			if(rowData.LOAIKEDONTHUOC == '20'){
		    				_opt = "02D019";
		    			}
		    			//L2PT-60299
		    			if(rowData.LOAIKEDONTHUOC == '21' && rowData.OPTIONS == '02D011'){
		    				_opt = "02D011";
		    			}
		    			
						 _loaikedon = rowData.DIKEM;// nếu là đi kèm thì kê tổng hợp sáng chưa chiều tối
						 
						 if(rowData.KIEUKEDON != '-1'){ // xac dinh kieu ke don chi tiet hay tong hop
							 _loaikedon = rowData.KIEUKEDON;
						 }
						 //tuyennx_add_end
		    			
//		    			if(_slThang!=null && _slThang>0){
//		    				_opt="02D017";
//		    				_loaikedon=1;
//		    				_title="Chỉ định thuốc YHCT";
//		    			}
		    			
		    			 //lay loai thuoc
		     	    	 var _loaithuoc=0;
		        		 var _par_loai = [rowData.MAUBENHPHAMID];						
						 var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
						 if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {							
							_loaithuoc=arr_loaithuoc[0].LOAI;
						    if(_loaithuoc==3){
						    	_opt="02D017";
			    				_loaikedon=1;
			    				_title="Cập nhật thuốc YHCT"; 
							 }
						 }
						
		    			paramInput={					
	    					khambenhid : rowData.KHAMBENHID,
	    					maubenhphamid : rowData.MAUBENHPHAMID,
	    					loaikedon: _loaikedon,
	    					dichvuchaid: "",
	    					opt : _opt,	    					
	    					tiepnhanid : $("#hidTIEPNHANID").val(),
	    					mabenhnhan : $("#txtMABENHNHAN").val(),
	    					benhnhanid : $("#hidBENHNHANID").val(),
	    					hosobenhanid : $("#hidHOSOBENHANID").val(),
	    					doituongbenhnhanid : $("#hidDOITUONGBENHNHANID").val(), 
	    					ravien : rowData.LOAIKEDONTHUOC == '21' ? '1':'0', //tuyennx_add_start_20200716 L2PT-24536
	    					//tuyennx_add_start_20170112 L2DKBD-880
	    					phongId : rowData.PHONGID,
	    					//tuyennx_add_end_20170112 L2DKBD-880
	    					action : "Upd"
		    			};     		    			
		    			
		    			if(rowData.LOAIDONTHUOC == "1"){
		    				dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../ngoaitru/NGT02K044_CapThuocK",paramInput,"Cập nhật phiếu thuốc không thuốc",800,550);
			    			DlgUtil.open("divDlgTaoPhieuThuoc"+_opt);
		    			}else{		    				
		    				if(_opt == "02D011"){
		    					var chchucnang  = ['CHUCNANG_KEDON_MUANGOAI']; 
			    			    var _chucnang = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', chchucnang.join('$'));			    			    
			    			    dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/"+_chucnang,paramInput,_title,1300,600);
		    				}else{
		    					dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+_opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_title,1300,600);
		    				}
		    				//L2PT-13135
			    			EventUtil.setEvent("reloadTabThuocVT",function(e){
			    				var _sql_par=[];
			    				var _look_sql="NT.024.DSTHUOCVT"; 
			    				if(_self.options._modeViewNT != null && _self.options._modeViewNT == "1"){
			    	    			_sql_par=RSUtil.buildParam("",[_self.options._khambenhid,_self.options._benhnhanid,_self.options._lnmbp,_self.options._hosobenhanid, _self.options._dichvuKhambenhID]);
			    	    			GridUtil.loadGridBySqlPage(_self.containerId+this.options._grdPhieuthuoc,"NT.024.DSTHUOCVT_NT",_sql_par);
			    	    		}else{
			    	    			var _hsbaid = "";
			    	    			if($("#hidHOSOBENHANID").val())
			    	    				_hsbaid = $("#hidHOSOBENHANID").val()
			    	    			else 
			    	    				_hsbaid = _self.options._hosobenhanid;
			    	    			_sql_par=RSUtil.buildParam("",[_self.options._khambenhid,_self.options._benhnhanid,_self.options._lnmbp+';'+_self.options._loaitvt+';'+_self.options._dichvuKhambenhID,_hsbaid]);//L2PT-20784
			    	    			GridUtil.loadGridBySqlPage(_self.containerId+"grdThuoc",_look_sql,_sql_par);
			    	    		}
			    			});
		    				
		    				//tuyennx_add_20170713_start
		    				/*EventUtil.setEvent("divDlgTaoPhieuThuoc"+_opt+"_onClose",function(name){						
		    					EventUtil.raiseEvent("assignDrug_cancel",{option:_opt});
		    				 });*/
		    				//tuyennx_add_20170713_end
		    				DlgUtil.open("divDlgTaoPhieuThuoc"+_opt);
		    			}	    			
		    			
	        		}else{
	        			DlgUtil.showMsg("Không thể sửa phiếu này!\nPhiếu này đã hoặc đang được xử lý");
	        		}
    		}
        },
        _phieuTraThuoc: function(rowId){
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._grdPhieuthuoc).jqGrid('getRowData', rowId);        	
        	if(rowData.TRANGTHAIMAUBENHPHAM != '6' && rowData.LOAIKHO != '8' && rowData.LOAIKHO != '13'){ //8,13 tu truc thuoc
        		DlgUtil.showMsg("Phiếu thuốc không trả được vì trạng thái phiếu không phải là đã nhận");
        		return;
        	}
        	
        	if(rowData.TRANGTHAIMAUBENHPHAM == '1'){
        		DlgUtil.showMsg("Trạng thái phiếu đang sửa nên không trả được");
        		return;
        	}
        	
        	if(rowData.LOAIPHIEUMAUBENHPHAM != '1'){
        		DlgUtil.showMsg("Đây không phải là phiếu nhận nên không trả được.");
        		return;
        	}
        	
    		if(rowData != null){     			
    			var myVar={
					khambenhid : rowData.KHAMBENHID,
					mabenhnhan : '',
					maubenhphamid : rowData.MAUBENHPHAMID,
					opt : "02D014",
					loaikedon : '1',
					kieutra : '1',
					dichvuchaid : ''
				};

//				dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+myVar.opt,"divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",myVar,"Tạo phiếu trả thuốc",1300,600);
    			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc"+myVar.opt,"divDlg","manager.jsp?func=../noitru/NTU02D100_TraThuoc",myVar,"Tạo phiếu trả thuốc",1300,600);
    			DlgUtil.open("divDlgTaoPhieuThuoc"+myVar.opt);  
    		}
        },
        _viewPhieuVatTuDetail: function(rowId){  
        	var _self=this;
        	var rowData = $('#' + _self.containerId+this.options._grdPhieuthuoc).jqGrid('getRowData', rowId);
    		if(rowData != null){    			
    			var sql_par1=[];
    			sql_par1.push({
    				"name" : "[0]",
    				"value" : rowData.MAUBENHPHAMID
    			});
    			GridUtil.loadGridBySqlPage(_self.containerId+this.options._gridPhieuthuocDetail,"NT.034.1",sql_par1);
    			//tuyennx_add_start_HISL2TK-544
    			sql_par1=[];
    			sql_par1.push({
    				"name" : "[0]",
    				"value" : rowData.MAUBENHPHAMID
    			});
    			sql_par1.push({
    				"name" : "[1]",
    				"value" : 1
    			});
    			GridUtil.loadGridBySqlPage(_self.containerId+this.options._grdChiTietThuoc1Sao,"NT.034.THUOCSAO",sql_par1);
    			sql_par1=[];
    			sql_par1.push({
    				"name" : "[0]",
    				"value" : rowData.MAUBENHPHAMID
    			});
    			sql_par1.push({
    				"name" : "[1]",
    				"value" : 2
    			});
    			GridUtil.loadGridBySqlPage(_self.containerId+this.options._grdChiTietThuoc2Sao,"NT.034.THUOCSAO",sql_par1);
    			
    			//tuyennx_add_start_L2PT-5682
    			sql_par1=[];
    			sql_par1.push({
    				"name" : "[0]",
    				"value" : rowData.MAUBENHPHAMID
    			});
    			sql_par1.push({
    				"name" : "[1]",
    				"value" : 4
    			});
    			GridUtil.loadGridBySqlPage(_self.containerId+this.options._grdChiTietThuocKS,"NT.034.THUOCSAO",sql_par1);
    			//tuyennx_add_end_L2PT-5682
    			
    			//tuyennx_add_end_HISL2TK-544
    			
    			//L2PT-107978
    			sql_par1=[];
    			sql_par1.push({
    				"name" : "[0]",
    				"value" : rowData.MAUBENHPHAMID
    			});
    			GridUtil.loadGridBySqlPage(_self.containerId+this.options._grdChiTietThuocPT,"NT.034.THUOCPT",sql_par1);
    			
    		}
        },
        
      //tuyennx_add_start_20200203 L2PT-14996
        // tạo popup nhap so luong hong
        _capNhatSLHong: function(rowId){  
        	var _self=this;
    		var rowData = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('getRowData', rowId);
	       	if(rowData != null){
	       		dlgPopup = DlgUtil.buildPopup("dlgslhong", _self.containerId+"dlgNhapSLHong", "Nhập số lượng hỏng", 500, 110);
	       		DlgUtil.open("dlgslhong");
	       		var input = $("#"+_self.containerId+'txtSOLUONGHONG');
	    		var btnOK = $("#"+_self.containerId+'btn_SLHong_OK');
	    		var btnClose = $("#"+_self.containerId+'btn_SLHong_Close');
	    		input.val(rowData.SOLUONGHONG);
	    		
	    		btnOK.click(function() {
	    			var _slhong = input.val();
	    			if(!_slhong){
	    				DlgUtil.showMsg('Chưa nhập số lượng hỏng');
	    				return;
	    			}
	    			    
	    			if(isNaN(_slhong)){
	    				DlgUtil.showMsg('Số lượng hỏng phải là số!');
	    				return;
	    			}
	    			if(parseFloat(_slhong) > parseFloat(rowData.SOLUONG)){
	    				DlgUtil.showMsg('Số lượng hỏng không được nhập quá số lượng cấp!');
	    				return;
	    			}
	    			var _par = [rowData.DICHVUKHAMBENHID,_slhong];					
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.SLHONG",_par.join('$'));
	
	    			if (fl == 1) {
	    				dlgPopup.close();
	    				DlgUtil.showMsg('Cập nhật số lượng hỏng thành công');
	    				
	    				var sql_par1=[];
	        			sql_par1.push({
	        				"name" : "[0]",
	        				"value" : rowData.MAUBENHPHAMID
	        			});
	        			GridUtil.loadGridBySqlPage(_self.containerId+_self.options._gridPhieuthuocDetail,"NT.034.1",sql_par1);	
	    		
	    			}
	    		});
	    		btnClose.click(function() {
	    			dlgPopup.close();
	    		});
	       	}
    	},
    	//tuyennx_add_end_20200203 L2PT-14996
    	
    	 //tuyennx_add_start_20200203 L2PT-24256
        // tạo popup hoan huy
        _hoanHuy: function(rowId){  
	       	DlgUtil.open("dlgHoanHuy");
    	},
    	
    	 _initPopUpHoanHuy: function(){  
    		 var _self=this;
       		dlgPopup = DlgUtil.buildPopup("dlgHoanHuy", _self.containerId+"dlgHoanHuy", "Hoàn trả xuất hủy", 500, 110);
       		$("#txtLYDOHUY").val("");
       		var input = $("#"+_self.containerId+'txtLYDOHUY');
    		var btnOK = $("#"+_self.containerId+'btn_HoanHuy_OK');
    		var btnClose = $("#"+_self.containerId+'btn_HoanHuy_Close');
    		input.val("");
    		var check = 0;
    		btnOK.click(function() {
            	var rowId =$('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid("getGridParam", "selrow");
        		var rowData = $('#' + _self.containerId+_self.options._gridPhieuthuocDetail).jqGrid('getRowData', rowId);
    			var _lydo = input.val();
    			if(check == 1)
    				return;
    			if(!_lydo){
    				DlgUtil.showMsg('Chưa nhập lý do hủy');
    				return;
    			}
    			var _par = [rowData.DICHVUKHAMBENHID,_lydo];					
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.HOANHUY",_par.join('$'));

    			if (fl == 1) {
    				dlgPopup.close();
    				DlgUtil.showMsg('Hoàn trả xuất hủy thành công!');
    				check == 1;
    				var sql_par1=[];
        			sql_par1.push({
        				"name" : "[0]",
        				"value" : rowData.MAUBENHPHAMID
        			});
        			GridUtil.loadGridBySqlPage(_self.containerId+_self.options._gridPhieuthuocDetail,"NT.034.1",sql_par1);	
    		
    			}
    			else if(fl == -1){
    				DlgUtil.showMsg('Có lỗi xảy ra!');
    			}
    			else {
    				DlgUtil.showMsg(fl);
    			}
    		});
    		btnClose.click(function() {
    			dlgPopup.close();
    		});
     	},
    	//tuyennx_add_end_20200203 L2PT-24256
        // Destroy an instantiated plugin and clean up  modifications the widget has made to the DOM
        destroy: function () {

            // this.element.removeStuff();
            // For UI 1.8, destroy must be invoked from the
            // base widget
            $.Widget.prototype.destroy.call(this);
            // For UI 1.9, define _destroy instead and don't
            // worry about
            // calling the base widget
        },

        methodB: function ( event ) {
            //_trigger dispatches callbacks the plugin user
            // can subscribe to
            // signature: _trigger( "callbackName" , [eventObject],
            // [uiObject] )
            // eg. this._trigger( "hover", e /*where e.type ==
            // "mouseenter"*/, { hovered: $(e.target)});
            console.log("methodB called");
        },

        methodA: function ( event ) {
            this._trigger("dataChanged", event, {
                key: "someValue"
            });
        },

        // Respond to any changes the user makes to the
        // option method
        _setOption: function ( key, value ) {
            switch (key) {
            case "someValue":
                //this.options.someValue = doSomethingWith( value );
                break;
            default:
                //this.options[ key ] = value;
                break;
            }

            // For UI 1.8, _setOption must be manually invoked
            // from the base widget
            $.Widget.prototype._setOption.apply( this, arguments );
            if(key=='_benhnhanid'){
            	this._initWidget();	
            }
            // For UI 1.9 the _super method can be used instead
            // this._super( "_setOption", key, value );
        }
    });
})(jQuery);

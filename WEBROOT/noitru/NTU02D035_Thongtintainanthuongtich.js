function NTU02D035_TNTT(opt) {
	var sql_par=[];
	var _checkUpdate=0;
	var _tainanthuongtichid="";
	var _colDIAPHUONG = "Viết tắt,TENVIETTATDAYDU,30,0,f,l;Địa phương,NAME,70,0,f,l";	
	var _trangthaiSql="NTU035.TRANGTHAI";
	var _colNN =   "Mã nguyên nhân,MA,20,0,f,l;Tên nguyên nhân,MOTA,70,0,f,l";
	var _colBP =   "Mã ,MA,15,0,f,l;Tên bộ phận,MOTA,70,0,f,l";
	var sql_parNN = [];
	var sql_parBP = [];
	var data;
	var rows;
	var dlgBLGD;
	var causer = -1;
	var capassword = -1;
	var _tinhId="";
	var _xaId="";
	var _diaphuongid="";
	var _typeNguyenNhan='';
	
	var that=this;
	this.load = doLoad;
	function doLoad() {
		// khoi tao man hinh
		initControl();	
		this.validator = new DataValidator("divMain");
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		//xu ly nghiep vu
		bindEvent();	

	}
	// ham khoi tao man hinh
	function initControl() {		
		//get thong tin ca nhan cua benh nhan		
		dlgBLGD = DlgUtil.buildPopup("dlgBLGD", "dlgBLGD", "Thông tin bạo lực gia đình", 800, 350, {"zIndex":998});
		FormUtil.clearForm("divMain","");
		sql_par = [];
		sql_par = [opt.hosobenhanid,opt.benhnhanid];       	
    	var data = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU036.GTS.TTBN",sql_par.join('$'));
    	if (data != null && data.length > 0) {
    		FormUtil.setObjectToForm("divMain", "", data[0]);
			if(jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_TNTT_LOADDIAPHUONG') == 1){
				_diaphuongid = data[0].DIAPHUONGID
			}
		}
		//get thong tin tai nan thuong tich
		sql_par = [];			
		sql_par.push({"name" : "[0]","value" : opt.hosobenhanid});
		//data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU035.TNTT.TT1", sql_par);
		data = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU035.TNTT.TT2", [opt.hosobenhanid].join('$'));

		// SONDN TNTT DKKHA 
		if (opt.mode == "TNTT_TIEPNHAN"){
			_obj = new Object(); 						// thiet lap prototype object; 
			_obj.BENHNHANID= "";
			_obj.BLGD_BPDT= "";
			_obj.BLGD_CCNTN= "0";
			_obj.BLGD_DTBL= "";
			_obj.BLGD_DTBL_KHAC= "";
			_obj.BLGD_HONNHAN= "";
			_obj.BLGD_HTBL= "";
			_obj.BLGD_MANGTHAI= "0";
			_obj.BLGD_SOCON= "";
			_obj.BLGD_SONAM= "";
			_obj.BLGD_SONGAY= "";
			_obj.BLGD_THSK= "";
			_obj.BLGD_THSK_KHAC= "";
			_obj.BLGD_TTHV= "";
			_obj.CSYTID= "965";
			_obj.DIAPHUONGID= "4042317302";
			_obj.HC_HUYENID= "4042300000";
			_obj.HC_TINHID= "4000000000";
			_obj.HC_XAID= "4042317302";
			_obj.HOSOBENHANID= "";
			_obj.KHAMBENHID= "";
			_obj.NGAYTAINAN= "15/03/2021 16:16:58";
			_obj.NOIXAYRATAINAN= "";
			_obj.SONHA= "";
			_obj.TAINANTHUONGTICHID= "562";
			_obj.TAINAN_BOPHANBITHUONGID= "";
			_obj.TAINAN_DIADIEMID= "";
			_obj.TAINAN_DIENBIENID= "";
			_obj.TAINAN_DOCONID= "";
			_obj.TAINAN_GIAOTHONGID= "";
			_obj.TAINAN_GIAOTHONGMUID= "";
			_obj.TAINAN_NGODOC= "";
			_obj.TAINAN_NGODOCID= "";
			_obj.TAINAN_NGUYENNHANID= "";
			_obj.TAINAN_XUTRIID= "";
			_obj.THONID= "";
			_obj.TINHTRANG_RA= "";
			_obj.TINHTRANG_VAO= "";
			_obj.TT_DIEUTRI= "";
			_obj.USERID= "";
			
			jQuery.extend(_obj,opt.tntt_data); 					// overwrite opt.tntt_data len _obj; 
			data[0] = _obj;
			$("#dvHanhChinh").hide(); 
		}
		// END SONDN TNTT DKKHA 
		
		sql_par=[];		
		sql_par.push({"name":"[0]","value":47});
		ComboUtil.getComboTag("cboTAINAN_DIENBIENID",_trangthaiSql,sql_par, '', {value:'',text:'--Lựa chọn--'},"sql", "", function () {
			if (opt.hospital_id == 10284) {
				$('#cboTAINAN_DIENBIENID').children().remove().end();
				$('#cboTAINAN_DIENBIENID').append('<option value="-1">---Lựa chọn---</option>');
				$('#cboTAINAN_DIENBIENID').append('<option value="1">Chuyển tuyến</option>');
				$('#cboTAINAN_DIENBIENID').append('<option value="2">Nhập viện</option>');
				$('#cboTAINAN_DIENBIENID').append('<option value="3">Xin về</option>');
				$('#cboTAINAN_DIENBIENID').append('<option value="4">Tử vong</option>');
				$('#cboTAINAN_DIENBIENID').val(-1);
			}
		});
		sql_par=[];		
		sql_par.push({"name":"[0]","value":92});
		ComboUtil.getComboTag("cboTAINAN_XUTRIID",_trangthaiSql,sql_par, '', {value:'',text:'--Lựa chọn--'},"sql", "", "");
		sql_par=[];		
		sql_par.push({"name":"[0]","value":93});
		ComboUtil.getComboTag("cboTAINAN_GIAOTHONGMUID",_trangthaiSql,sql_par, '', {value:'',text:'--Lựa chọn--'},"sql", "", "");
		sql_par=[];		
		sql_par.push({"name":"[0]","value":91});
		ComboUtil.getComboTag("cboTAINAN_DOCONID",_trangthaiSql,sql_par, '', {value:'',text:'--Lựa chọn--'},"sql", "", "");
		sql_par=[];		
		sql_par.push({"name":"[0]","value":48});
		ComboUtil.getComboTag("cboTAINAN_GIAOTHONGID",_trangthaiSql,sql_par, '', {value:'',text:'--Lựa chọn--'},"sql", "", "");
		sql_par=[];		
		sql_par.push({"name":"[0]","value":90});
		ComboUtil.getComboTag("cboTAINAN_NGODOCID",_trangthaiSql,sql_par, '', {value:'',text:'--Lựa chọn--'},"sql", "", "");


		if ( jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_NNBP') == 1 ){
			// sql_parBP.push({"name" : "[0]","value" : 1});
			// ComboUtil.getComboTag("cboTAINAN_BOPHANBITHUONGID", "CG.ICD10NN.BP", sql_parBP, "0", {value:'0',text:'--Lựa chọn--'}, "sql", "","" );
			// sql_parNN.push({"name" : "[0]","value" : 2});
			// ComboUtil.getComboTag("cboTAINAN_NGUYENNHANID",'CG.ICD10NN.BP',sql_parNN, '0', {value:'0',text:'--Lựa chọn--'},"sql", "", "");
			// $("#cboTAINAN_NGUYENNHANID option[value='19']").remove();//L2PT-27682
		}else {
			sql_par=[];
			sql_par.push({"name":"[0]","value":49});
			ComboUtil.getComboTag("cboTAINAN_NGUYENNHANID",_trangthaiSql,sql_par, '', {value:'',text:'--Lựa chọn--'},"sql", "", function(){
				var _themgiatri = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_CBO_TNTT_NGUYENHAN');
				if(_themgiatri == 0){
					$("#cboTAINAN_NGUYENNHANID option[value='11']").remove();
					$("#cboTAINAN_NGUYENNHANID option[value='12']").remove();
					$("#cboTAINAN_NGUYENNHANID option[value='13']").remove();
				}
				$("#cboTAINAN_NGUYENNHANID option[value='19']").remove();//L2PT-27682
				//L2PT-77047
				var TNTT_NGUYENNHAN_BOSUNG = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'TNTT_NGUYENNHAN_BOSUNG');
				if(TNTT_NGUYENNHAN_BOSUNG != '0'){  
					var array = TNTT_NGUYENNHAN_BOSUNG.split(';');
					for(var i=0;i<array.length;i++){
						var tntt_vl =  array[i].split(':');
						var newOption = $('<option value="'+tntt_vl[0]+'">'+tntt_vl[1]+'</option>');
						$('#cboTAINAN_NGUYENNHANID').append(newOption);
					}
				}
				var tntt = '';//L2PT-27682
				if(data!=null && data.length>0){
					$("#cboTAINAN_NGUYENNHANID").val(data[0].TAINAN_NGUYENNHANID);
					tntt = data[0].TAINAN_NGUYENNHANID; //L2PT-27682
					$("#cboTAINAN_NGUYENNHANID").change();
					FormUtil.setObjectToForm("", "", data[0]);
				} 
				//L2PT-27682
				if (opt.cboTNTT != "-1" && tntt != opt.cboTNTT) {
					$("#cboTAINAN_NGUYENNHANID").val(opt.cboTNTT);
					$("#cboTAINAN_NGUYENNHANID").val(opt.cboTNTT);
					$("#cboTAINAN_NGUYENNHANID").change();
				}
				var checkTNTT = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_CC_BB_TNTT');
				if(checkTNTT == 0){
					$("#cboTAINAN_NGUYENNHANID option[value='14']").remove();
				}
				
			});
			
		
			sql_par=[];
			sql_par.push({"name":"[0]","value":89});
			ComboUtil.getComboTag("cboTAINAN_BOPHANBITHUONGID",_trangthaiSql,sql_par, '', {value:'',text:'--Lựa chọn--'},"sql", "", function () {
				//L2PT-46473
				if(data!=null && data.length>0){
					$("#cboTAINAN_BOPHANBITHUONGID").val(data[0].TAINAN_BOPHANBITHUONGID);
				} 
				if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_TNTT_LOADBOPHANICD') == 1) {
					var sql_par1 = [];
					sql_par1.push({"name": "[1]", value: opt.khambenhid});
					var _check = jsonrpc.AjaxJson.getOneValue('TNTT.CHECKICD', sql_par1);
					$("#cboTAINAN_BOPHANBITHUONGID").val(_check);
				}
			});
		}
		sql_par=[];		
		sql_par.push({"name":"[0]","value":88});
		ComboUtil.getComboTag("cboTAINAN_DIADIEMID",_trangthaiSql,sql_par, '', {value:'',text:'--Lựa chọn--'},"sql", "", "");
		
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_TNTT_SHOWBSDT') == 1){
			$("#dvBACSIDIEUTRI").show();
			ComboUtil.getComboTag("cboBACSI_DIEUTRI", "NTU01H001.EV019", [], '', {value: '', text: 'Chọn'}, "sql");
		}
		if(data!=null && data.length>0){
			FormUtil.setObjectToForm("", "", data[0]);
			if(data[0].BLGD_DTBL) $('input[name=radBLGD_DTBL][value=' + data[0].BLGD_DTBL + ']').attr('checked', true);
			if(data[0].BLGD_HTBL) $('input[name=radBLGD_HTBL][value=' + data[0].BLGD_HTBL + ']').attr('checked', true);
			_checkUpdate=1;						
			_tainanthuongtichid=data[0].TAINANTHUONGTICHID;
			_tinhId=data[0].HC_TINHID;
			_diaphuongid=data[0].DIAPHUONGID;
			_xaId=data[0].HC_XAID;
			$("#btnXoa").show();
		}else{
			var dtSys=jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			$("#txtNGAYTAINAN").val(dtSys);
			$("#btnXoa").hide();
		}	
		ComboUtil.getComboTag("cboHC_TINHID", "NGTTI.002",[],_tinhId,{value:'',text:'Chọn'},"sql","", function(){			
			//getDiaChi(_xaId, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID',_diaphuongid);
			getDiaChi(_diaphuongid, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID','','txtTKHC_TINH','txtTKHC_HUYEN','txtTKHC_XA');

		});		
		//DIA PHUONG		
		ComboUtil.initComboGrid("txtTKDIAPHUONG","DMDP.002",[], "600px", _colDIAPHUONG, function(event, ui) {
			  $("#txtTKDIAPHUONG").val(ui.item.TENVIETTATDAYDU);
		      var option = $('<option value="'+ui.item.VALUE+'">'+ui.item.NAME+'</option>');
		      $("#cboDIAPHUONGID").empty();
		      $("#cboDIAPHUONGID").append(option);
		      getDiaChi(ui.item.VALUE, 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID','','txtTKHC_TINH','txtTKHC_HUYEN','txtTKHC_XA');
		      return false;
		});
		
		changeValue();


		
		//active 1 so combobox
		$("#cboTAINAN_NGUYENNHANID").on("change", function(e) {
			var tainan_nguyennhanid=$("#cboTAINAN_NGUYENNHANID").val();			
			 if(tainan_nguyennhanid=="5"){
				 var _check = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_TNTT_BLGD');
				 if(_check == 1){
					 var heading = document.getElementById("cboTAINAN_NGUYENNHANID");
					 heading.style["width"] = "70%";
					 $("#btnBLGD").css("display","");
				 }
			 }
			 else{
				 var heading = document.getElementById("cboTAINAN_NGUYENNHANID");
				 heading.style["width"] = "100%"; 
				 $("#btnBLGD").css("display","none");
			 }
			 
			 $("#cboTAINAN_GIAOTHONGID").prop('disabled', 'disabled');
			 $("#cboTAINAN_DOCONID").prop('disabled', 'disabled');
			 $("#cboTAINAN_GIAOTHONGMUID").prop('disabled', 'disabled');
			 $("#cboTAINAN_NGODOCID").prop('disabled', 'disabled');
			 $("#cboTAINAN_LAODONGID").prop('disabled', 'disabled');
			 $("#cboTAINAN_DOCONID").val("");
			 $("#cboTAINAN_NGODOCID").val("");
			 $("#cboTAINAN_GIAOTHONGMUID").val("");
			 $("#cboTAINAN_GIAOTHONGID").val("");
			$('#dvTAINAN_GIAOTHONG').removeClass("required");
			$("#cboTAINAN_GIAOTHONGID").removeAttr('valrule');
			 
			 if(tainan_nguyennhanid=="1"){
				 $("#cboTAINAN_GIAOTHONGID").removeAttr("disabled");
				 $('#dvTAINAN_GIAOTHONG').addClass("required");
				 $("#cboTAINAN_GIAOTHONGID").attr('valrule', 'Loại tai nạn giao thông,required');
				 $("#cboTAINAN_DOCONID").removeAttr("disabled");
				 $("#cboTAINAN_GIAOTHONGMUID").removeAttr("disabled");
				 $("#cboTAINAN_GIAOTHONGMUID").val("2");
			 }else if(tainan_nguyennhanid=="2"){
				 $("#cboTAINAN_LAODONGID").removeAttr("disabled");				
			}else if(tainan_nguyennhanid=="9"){
				 $("#cboTAINAN_NGODOCID").removeAttr("disabled");
			} else if(tainan_nguyennhanid=="7"){
				$("#cboTAINAN_NGODOCID").removeAttr("disabled");
			}
		});		
		$("#txtNOIXAYRATAINAN").focus();
		if ( jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_NOITAINAN_KHONGNHAP') == 1 ){
			$("#lblNOITAINAN").removeClass("required");
			$("#txtNOIXAYRATAINAN").removeAttr("valrule");
		}

		if ( jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_TNTT_PHUONGTIENCHUYENDEN') == 1 ){
			$("#dvPHUONGTIENCHUYENDEN").show();
		}
		//L2PT-59357
		if ( jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_TNTT_DIAPHUONG_REQ') == 1 ){
			$("#divTinh").removeClass("required");
			$("#divHuyen").removeClass("required");
			$("#divXa").removeClass("required");
			$("#cboHC_TINHID").removeAttr('valrule');
			$("#cboHC_HUYENID").removeAttr('valrule');
			$("#cboHC_XAID").removeAttr('valrule');
		}

		_typeNguyenNhan = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH",'NGT_TNTT_NGUYENNHAN');
		if (_typeNguyenNhan == '1') {
			$("#divNGUYENNHAN").hide();
			$("#divNGUYENNHAN_TEXT").show();
			$("#cboTAINAN_NGUYENNHANID").removeAttr('valrule');
			$("#txtTAINAN_NGUYENNHAN").attr('valrule', 'Nguyên nhân,required');
		}
		// Xử lý sự kiện liên quan ký CA => START
		var _checkKy = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_SUDUNG_KYSO_KYDIENTU');
		if(_checkKy == '1') {
			$("#btnKyCa").show();
			$("#btnHuyCa").show();
			$("#btnInCa").show();
		}
		// Xử lý sự kiện liên quan ký CA => END
	}	
	
	function _clearOption(strId,_defOpt){
		$("#" +  strId).empty();
		if(typeof _defOpt.value !== "undefined"){
			$("#" +  strId).append('<option value="'+_defOpt.value+'">'+_defOpt.text+'</option>');
		}
	}
	
	function _clearTextCode(strId,_value){
		$("#"+strId).val(_value);
	}
	
	function changeValue(){		
		$('#cboDIAPHUONGID').on('change', function (e) {
			getDiaChi($('#cboDIAPHUONGID').val(), 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID',"",'txtTKHC_TINH','txtTKHC_HUYEN','txtTKHC_XA','txtTKDIAPHUONG');
		});
		
		$('#cboHC_TINHID').on('change', function (e) {			
			if($('#cboHC_TINHID').val() != ''){
				getDiaChi($('#cboHC_TINHID').val(), 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID');
				_b_flag_taothe_trem = false;
			} else {
				_clearTextCode('txtTKHC_HUYEN','');
				_clearTextCode('txtTKHC_XA','');	
				_clearTextCode('txtTKDIAPHUONG','');
				_clearOption('cboHC_HUYENID',{value:'',text:'Chọn'});				
				_clearOption('cboHC_XAID',{value:'',text:'Chọn'});				
				_clearOption('cboDIAPHUONGID',{value:'',text:'Chọn'});				
			}
			$('#txtTKHC_TINH').val($('#cboHC_TINHID').val());
		});
		
		$('#cboHC_HUYENID').on('change', function (e) {
			if($('#cboHC_HUYENID').val() != ''){
				getDiaChi($('#cboHC_HUYENID').val(), 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID');
			}
			else {
				_clearOption('cboHC_XAID',{value:'',text:'Chọn'});
				_clearTextCode('txtTKHC_XA','');
				_clearOption('cboDIAPHUONGID',{value:'',text:'Chọn'});
				_clearTextCode('txtTKDIAPHUONG','');
			}
			$('#txtTKHC_HUYEN').val($('#cboHC_HUYENID').val());	
		});	
		
		$('#cboHC_XAID').on('change', function (e) {
			if($('#cboHC_XAID').val() != ''){
				getDiaChi($('#cboHC_XAID').val(), 'cboHC_TINHID', 'cboHC_HUYENID', 'cboHC_XAID', 'cboDIABANID');
			} else {
				_clearOption('cboDIAPHUONGID',{value:'',text:'Chọn'});
				_clearTextCode('txtTKDIAPHUONG','');
			}
			$('#txtTKHC_XA').val($('#cboHC_XAID').val());
		});
		
		$('#cboDIAPHUONGID').on('change', function (e) {
			$('#txtTKDIAPHUONG').val($('#cboDIAPHUONGID').val());
		});
		
		$('#txtTKHC_TINH').on('change', function (e) {
			fillValueDisplay('txtTKHC_TINH','cboHC_TINHID');
		});
		
		$('#txtTKHC_HUYEN').on('change', function (e) {
			fillValueDisplay('txtTKHC_HUYEN','cboHC_HUYENID');
		});
		
		$('#txtTKHC_XA').on('change', function (e) {
			fillValueDisplay('txtTKHC_XA','cboHC_XAID');
		});
		
		$('#txtTKDIAPHUONG').on('change', function (e) {
			fillValueDisplay('txtTKDIAPHUONG','cboDIAPHUONGID');
		});
	}

	function bindEvent(){		
		// SONDN L2PT-34006
		$(document).unbind('keydown').keydown(function (e) {      
			if (e.keyCode == 114) {							// F3
				saveOrUpdate(1);
			}
			if (e.keyCode == 115) {							// F4
				saveOrUpdate(2);			
			}
		});
		
		$(':input').keydown(function (e) {
			var id = $(this).attr('id');
		    if (e.which === 13 || e.which === 9) {				// tab hoac enter
		    	if (id == "txtTKDIAPHUONG"){	
		    		$("#cboTAINAN_DIADIEMID").focus();
		    	}else if (id == "cboDIAPHUONGID"){	
		    		$("#cboTAINAN_DIADIEMID").focus();
		    	}else if (id == "cboHC_XAID"){	
		    		$("#cboTAINAN_DIADIEMID").focus();
		    	}else if (id == "cboTAINAN_NGUYENNHANID"){	
		    		if ($("#cboTAINAN_NGUYENNHANID").val() == "7"){						// Ngộ độc
		    			$("#cboTAINAN_NGODOCID").focus(); 
		    		}else if ($("#cboTAINAN_NGUYENNHANID").val() == "1"){				// TNGT
		    			$("#cboTAINAN_GIAOTHONGID").focus(); 
		    		}else if ($("#cboTAINAN_NGUYENNHANID").val() == "2"){				// TNGT
		    			$("#cboTAINAN_LAODONGID").focus(); 
		    		}else{
		    			$("#cboTAINAN_XUTRIID").focus(); 
		    		}
		    	}else if (id == "cboTAINAN_GIAOTHONGID"){	
		    		$("#cboTAINAN_DOCONID").focus(); 
		    	}else if (id == "cboTAINAN_LAODONGID"){	
		    		$("#cboTAINAN_XUTRIID").focus(); 
		    	}else if (id == "cboTAINAN_DOCONID"){
		    		$("#cboTAINAN_GIAOTHONGMUID").focus();
		    	}else if (id == "cboTAINAN_NGODOCID"){
		    		$("#cboTAINAN_XUTRIID").focus(); 
		    	}else{
					if ($(this).is("textarea") == false){ //L2PT-40580 textarea thì ở lại để xuống dòng.
						var index = $(':input').index(this) + 1;
						$(':input').eq(index).focus();
					}
		    	}
		    }
		    
		    if (e.which === 9) {						// Tab
	    		e.preventDefault(); 					// chan cac event ung voi phim Tab
	    	} 
		    
		    if  (e.shiftKey && e.which === 9) {
		    	if (id == "txtTINHTRANG_RA"){	
		    		$("#txtTINHTRANG_VAO").focus();
		    	}else if (id == "txtTINHTRANG_VAO"){	
		    		$("#txtTT_DIEUTRI").focus();
		    	}else if (id == "txtTT_DIEUTRI"){	
		    		$("#cboTAINAN_DIENBIENID").focus();
		    	}else if (id == "cboTAINAN_DIENBIENID"){	
		    		$("#cboTAINAN_XUTRIID").focus();
		    	}else if (id == "cboTAINAN_XUTRIID"){	
		    		$("#cboTAINAN_NGUYENNHANID").focus();
		    	}else if (id == "cboTAINAN_NGUYENNHANID"){	
		    		$("#cboTAINAN_BOPHANBITHUONGID").focus();
		    	}else if (id == "cboTAINAN_BOPHANBITHUONGID"){	
		    		$("#cboTAINAN_DIADIEMID").focus();
		    	}else if (id == "cboTAINAN_DIADIEMID"){	
		    		$("#txtTKDIAPHUONG").focus();
		    	}else if (id == "txtTKDIAPHUONG"){	
		    		$("#txtNOIXAYRATAINAN").focus();
		    	}
		    }
		}); 	
		// end sondn 
		
		//dong trang
		closePage();
		
		$('#cboDIABANID').on('change', function (e) {
			$('#txtTKDIABAN').val($('#cboDIABANID').val());
		});
		
		$('#txtTKDIABAN').on('change', function (e) {
			fillValueDisplay('txtTKDIABAN','cboDIABANID');
		});
		
		$("#btnIn").bindOnce("click", function() {
			saveOrUpdate(2);			
		},500);
		$("#btnLuu").bindOnce("click", function() {
			saveOrUpdate(1);
		},500);
		$("#btnBLGD").bindOnce("click", function() {
			DlgUtil.open("dlgBLGD");
		},500);
		
		$("#btnXoa").bindOnce("click", function() {
 	       DlgUtil.showConfirm("Ban có chắc chắn xóa thông tin ?",function(flag) {
    		   if (flag) {
    			   _par = [_tainanthuongtichid];
    			   var _result=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU035.TNTT.DEL",_par.join('$'));
    			   if(_result=="1"){
    				   parent.DlgUtil.close("dlgTaiNanThuongTich");
    			   }
    			   else{
    				   DlgUtil.showMsg("Có lỗi xảy ra !"); 
    			   }
    		   }
    		 });  
		},500);

		// Xử lý sự kiện liên quan ký CA => START
		$("#btnKyCa").bindOnce("click", function() {
			saveOrUpdate(3);
		},500);

		$("#btnHuyCa").bindOnce("click", function() {
			_caRpt('2');
		},500);

		$("#btnInCa").bindOnce("click", function() {
			_exportKyCA();
		});
		// Xử lý sự kiện liên quan ký CA => END
	}
	// luu thong tin duyet mo
	function saveOrUpdate(type) {
		// SONDN DKKHA TIEP NHAN TNTT 
		if (opt.mode == "TNTT_TIEPNHAN"){
			var objData = new Object();
			FormUtil.setFormToObject("dvChiTiet","",objData);
			
//			objData = new Object();
//			FormUtil.setFormToObject("dvHanhChinh","",objData);
//			
//			objData = new Object();
//			FormUtil.setFormToObject("","",objData);
			
			EventUtil.raiseEvent("assignSevice_loadTNTT",{
				tntt_data : objData
			});
			return; 
		}
		// END SONDN DKKHA TIEP NHAN TNTT

		if ($('#cboTAINAN_NGUYENNHANID').val() == 1 && $('#cboTAINAN_GIAOTHONGID').val() == ''){
			DlgUtil.showMsg("Loại tai nạn giao thông không được để trống !");
			$('#cboTAINAN_GIAOTHONGID').focus();
			return false;
		}

		var valid = false;
		if (jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH", 'NGT_TNTT_KHONGRANGBUOC_BENH') == 1) {
			if ($('#cboTAINAN_NGUYENNHANID').val() == 14) {
				valid = true;
			} else {
				valid = that.validator.validateForm();
			}
		} else {
			valid = that.validator.validateForm();
		}
		if(valid){	
			dtSys=jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			if(!compareDate($('#txtNGAYTAINAN').val().trim(),dtSys,'DD/MM/YYYY HH:mm:ss')){
				    DlgUtil.showMsg("Thời điểm xảy ra tai nạn phải nhỏ hơn thời điểm hiện tại!");								
					$('#txtNGAYTAINAN').focus();
					return false;	
				}
			var objData = new Object();
			var _par;
			if($("#cboTAINAN_NGUYENNHANID").val() != 5){
				FormUtil.clearForm("dlgBLGD","");
				$('input[name=radBLGD_DTBL]').attr('checked', false);
				$('input[name=radBLGD_HTBL]').attr('checked', false);
			}
			FormUtil.setFormToObject("","",objData);
			_par = [JSON.stringify(objData),_tainanthuongtichid,opt.khambenhid,opt.benhnhanid,opt.hosobenhanid,_checkUpdate];
			var _result=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU035.TNTT",_par.join('$'));				
			
			if(_result!="0"){
				if(_result !="1"){
					_tainanthuongtichid = _result;
					_checkUpdate = "1";
				};
				if(type == 1){
					DlgUtil.showMsg("Cập nhật tai nạn thương tích thành công");
				}else if(type == 2){
					var par = [ {
					    name : 'khambenhid', 
					    type : 'String',
					    value : opt.khambenhid}
					   ];
					openReport('window', "GIAY_CHUNGNHAN_THUONGTICH_965", 'pdf', par);
					
					var evFunc=EventUtil.getEvent("assignSevice_saveTNTT");			
					if(typeof evFunc==='function') {				
						evFunc({msg:"Cập nhật tai nạn thương tích thành công"});
					}
					else {
						console.log('evFunc not a function');
					} 
					
				} else if(type == 3) {
					// Xử lý sự kiện liên quan ký CA => START
					_caRpt('1');
				}
				//xu ly su kien callback
				$("#btnXoa").show();
			}else{
				DlgUtil.showMsg("Cập nhật tai nạn thương tích không thành công");
				return false;
			}	
		}
	}	
	function formatSqlParam(sqlParam){
		for(var i=0;i<sqlParam.length;i++){
			if(sqlParam[i].value==null){
				sqlParam[i].value='';
			}
		}		
	}
	// ham xu ly close trang
	function closePage() {
		$("#btnHuy").on("click", function(e) {
			parent.DlgUtil.close("dlgTaiNanThuongTich");
		});
		$("#btn_BLGD_OK").on("click", function(e) {
			DlgUtil.close("dlgBLGD");
		});
		$("#btn_BLGD_HUY").on("click", function(e) {
				FormUtil.clearForm("dlgBLGD","");
				$('input:radio[name=radBLGD_DTBL]').prop('checked', false);
				$('input:radio[name=radBLGD_HTBL]').prop('checked', false);
			if(data!=null && data.length>0){
				FormUtil.setObjectToForm("dlgBLGD", "", data[0]);
				if(data[0].BLGD_DTBL) $('input[name=radBLGD_DTBL][value=' + data[0].BLGD_DTBL + ']').attr('checked', true);
				if(data[0].BLGD_HTBL) $('input[name=radBLGD_HTBL][value=' + data[0].BLGD_HTBL + ']').attr('checked', true);
			}
			DlgUtil.close("dlgBLGD");
		});
		
		$('input[type=radio][name=radBLGD_DTBL]').change(function() {
		    if (this.value == '5') {
		    	$("#txtBLGD_DTBL_KHAC").prop('disabled', '');
		    }	    
		    else{
		    	$("#txtBLGD_DTBL_KHAC").prop('disabled', 'disabled');
		    	$("#txtBLGD_DTBL_KHAC").val("");
		    }
		});
		
		$('#cboBLGD_THSK').on('change', function (e) {
			var value = $('#cboBLGD_THSK').val();
			if(value == 4){
				$("#txtBLGD_THSK_KHAC").prop('disabled', '');
			}
			else
			{
				$("#txtBLGD_THSK_KHAC").prop('disabled', 'disabled');
				$("#txtBLGD_THSK_KHAC").val("")
			}
		});
	}

	function _caRpt(signType) {
		var rptCode = 'GIAY_CHUNGNHAN_THUONGTICH_965';
		var oData = {
			sign_type: signType,
			causer: causer,
			capassword: capassword,
			params: [
				{
					name: 'HOSOBENHANID',
					type: 'String',
					value: opt.hosobenhanid
				},
				{
					name: 'KHAMBENHID',
					type: 'String',
					value: opt.khambenhid
				},
				{
					name: 'RPT_CODE',
					type: 'String',
					value: rptCode
				}
			]
		};
		
		var par_rpt_KySo = [];
		par_rpt_KySo = [ {
			name : 'HOSOBENHANID',
			type : 'String',
			value : opt.hosobenhanid
		}];
		par_rpt_KySo.push({
			name : 'RPT_CODE',
			type : 'String',
			value : rptCode
		});
		par_rpt_KySo.push({
			name : 'KHAMBENHID',
			type : 'String',
			value : opt.khambenhid
		});
		
		var msg = CommonUtil.kyCA(par_rpt_KySo, signType, true, false);
			EventUtil.setEvent("eventKyCA",function(e)
	
			{ DlgUtil.showMsg(e.res); }
		);
	}

	function _exportKyCA() {
		var _params = [
			{
				name: 'HOSOBENHANID',
				type: 'String',
				value: opt.hosobenhanid
			},
			{
				name: 'KHAMBENHID',
				type: 'String',
				value: opt.khambenhid
			},
			{
				name: 'RPT_CODE',
				type: 'String',
				value: 'GIAY_CHUNGNHAN_THUONGTICH_965'
			}
		];
		CommonUtil.openReportGetCA2(_params, false);
	}
}
 function NTU02D037_PTTT(opt) {
    var _col = "Mã ICD,ICD10CODE,10,0,f,l;Tên VN,ICD10NAME,40,0,f,l;Tên E<PERSON>,ICD10NAME_EN,50,0,f,l";
    var _sql = "CG.ICD10";
    var _colPTTT = "Mã PTTT,MAPTTT,25,0,f,l;Tên PTTT,TENPTTT,50,0,f,l";
    var _sqlPTTT = "PTTT.PHUONGPHAP";
    var _colCTPTTT = "Mã Cách thức PTTT,MADICHVU,25,0,f,l;T<PERSON><PERSON> cách thức PTTT,TENDICHVU,50,0,f,l";
    var _sqlCTPTTT = "PTTT_DICHVU";
    var _sqlPTTT_hang = "PTTT.HANG";
    var _sqlPTTT_vocam = "PTTT.VOCAM";
    var _sqlPTTT_taibien = "PTTT.TAIBIEN";
    var _col_loaduser = "USER_ID,USER_ID,0,0,t,l;<PERSON><PERSON><PERSON>,USERNAME,20,0,f,l;<PERSON><PERSON><PERSON><PERSON>,FULLNAME,30,0,f,l;Ch<PERSON>c danh/Khoa phòng,CHUCDANH,50,0,f,l";
    var sql_par = [];
    var _phauthuatthuthuatid = "";
    var _checkUpdate = 0;
    var khoacdid = opt.khoaid;
    var tyle_pttt = '';
    var istextPttt = false;
    var that=this;
	this.load = doLoad;
	var isShowInfoTuongTrinh = false;
	var isPtvShowAll = false;
	var tinhhinhPTTTmacdinh = false;//START L2PT-962
	var isNhapChandoanchinh = false;//L1PT-1562
	
	var machuandoanvaokhoa, tenchuandoanvaokhoa = '';
	var machuandoantruocpt, tenchuandoantruocpt, machuandoansaupt, tenchuandoansaupt = '';//L2PT-9014
	var isNhapPTTT = false;
	var isGetICD_Bandau = false;//L2PT-7739
	var changeTextEkipNhiHDG = false;//L2PT-8920
	
	//START L2PT-4898
	this.luuAnh=luuAnh;
	this.xoaAnh=xoaAnh;
	this.lietKeAnh=lietKeAnh;
	var video = document.querySelector('video');  
    var canvas = window.canvas = document.querySelector('canvas'); 
    var context = canvas.getContext('2d');
    var width = canvas.width;
    var height = canvas.height;

	var imgIndex = 0;
	//END L2PT-4898
	var checkktBA ;//L2PT-16223
	var check_songuoi_thuchien, sl_nguoi, sl_giupviec = '', sl_nhanlucchinh = '', sl_nhanlucphu = '' ;//L2PT-19388
	var hienthi_ekipduyetmo ;
	//L2PT-20993
	var mau_pttt_theophongcd;
	var mdv_in_caychi_ydgli, madichvu_caychi = '';//L2PT-22996
	var form_pttt_bvnt, ppvc_default;//L2PT-25760
	var pp_pttt_dklsn;//L2PT-26618
	var kt_pttt_dklci;//L2PT-27254
	var get_nth_cdha_snvpc;//L2PT-28421
	var sql_load_user = '';//L2PT-31604
	var show_ekip_pttt;//START L2PT-32306
	var check_trungvitri_pttt;//L2PT-320600
	var ch_macdinhkhoa_cd;//L2PT-34204
	var ptv_chinh;//L2PT-34023
	var show_them_pmome; //L2PT-1404
	
	function doLoad() {
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		// khoi tao man hinh
		initControl();	
		//validate
		this.validator = new DataValidator("divMain");
		//xu ly nghiep vu
		bindEvent();	

	}
	// ham khoi tao man hinh
	function initControl() {	
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","HIS_CAUHINH_NHAPHANG_PTTT;HIS_PTTT_SHOW_INFO_TUONGTRINH;" +
				"NTU_PTTT_NHIHDG;PTTT_PTV_SHOWALL;PTTT_BO_REQ_CHANDOAN_TRUOCSAU;TINHHINH_PTTT_MACDINH;SHOW_HINHANH_PTTT;" +
				"NTU_LDTT_PTTT_BND;NTU_NHAP_CDC;NTU_NHAP_PTTT;GET_ICD_BANDAU;HIS_TENEKIP_NHIHDG;NTU_CHECK_KTBA;" +
				"NTU_PTTT_CHECK_SONGUOI;NTU_PTTT_SHOWEKIP_DUYETMO;NTU_MAU_PTTT_PHONGCD;NTU_IN_CAYCHI_YDGLI;NTU_PTTT_BVNT;" +
				"NTU_PTTT_PPVC_DEFAULT;NTU_PTTT_PPPT_DKLSN;NTU_PTTT_KTPT_DKLCI;NTU_PTTT_BTN_TT_BS;NTU_PTTT_DKLSN_PHUMO;" +
				"NTU_PTTT_NTH_CDHA;NTU_PTTT_SHOW_ICD9CM;NTU_PTTT_SQL_LOADUSER;NTU_SHOW_EKIP_PTTT;NTU_CHECK_TRUNG_VITRI;" +
				"NTU_PTTT_KCD_DEFAULT;NTU_PTTT_LOAD_MOME");//START L2PT-962
		if(data_ar != null && data_ar.length > 0){
			if(data_ar[0].HIS_CAUHINH_NHAPHANG_PTTT == '1'){
				istextPttt = true;
				$('#txtPTTT_Hang').show();
				$('#cboPTTT_HangID').hide();
			}
			if(data_ar[0].HIS_PTTT_SHOW_INFO_TUONGTRINH == '1'){
				isShowInfoTuongTrinh = true;
				$('#divInfoNew').show();
			}else{
				$('#divInfoNew').hide();
			}
			//START L2PT-4898
			if(data_ar[0].SHOW_HINHANH_PTTT != null && data_ar[0].SHOW_HINHANH_PTTT == '1'){
				lietKeAnh();
				$("#divHinhanhPTTT").show();
			}else{
				$("#divHinhanhPTTT").hide();
			}
			//END L2PT-4898
			//L1PT-1562
			if(data_ar[0].NTU_NHAP_CDC == '1'){
				isNhapChandoanchinh = true;
			}
			//START -- HISL2TK-691 --hongdq
			if(data_ar[0].NTU_PTTT_NHIHDG == '1'){
				$('#divLuocdoPTTT').show();
				$('#divTrintuPTTT').show();
			}else{
				$('#divLuocdoPTTT').hide();
				$('#divTrintuPTTT').hide();
			}
			//END -- HISL2TK-691 --hongdq
			
			//START L2PT-6521
			if(data_ar[0].NTU_LDTT_PTTT_BND == '1'){
				$('#btnLuuIn_BND').show();
				$('#divTrinhtuPTTT_BND').show();
				$('#divMota').hide();
			}else{
				$('#btnLuuIn_BND').hide();
				$('#divTrinhtuPTTT_BND').hide();
				$('#divMota').show();
			}
			//END L2PT-6521
			//Begin_HaNv_23102018: Hiển thị tất cả nhân viên ở ô Phẫu thuật viên 1 - L2HOTRO-11688
			if (data_ar[0].PTTT_PTV_SHOWALL == '1') {
				isPtvShowAll = true;
			}
			//End_HaNv_23102018
			//Begin_HaNv_05122018: Cấu hình bỏ required ở phần chẩn đoán trước pttt , sau pttt
			if (data_ar[0].PTTT_BO_REQ_CHANDOAN_TRUOCSAU == '1') {
				$('#divCDTruocPttt').removeClass('required');
				$('#divCDSauPttt').removeClass('required');
				$("#txtCHANDOANTRUOCPHAUTHUAT").removeAttr("valrule");
				$("#txtCHANDOANSAUPHAUTHUAT").removeAttr("valrule");
			}
			//End_HaNv_05122018
			//START L2PT-962
			if (data_ar[0].TINHHINH_PTTT_MACDINH == '1') {
				tinhhinhPTTTmacdinh = true;
			}
			//END L2PT-962
			//START L2PT-8042
			if(data_ar[0].NTU_NHAP_PTTT != null && data_ar[0].NTU_NHAP_PTTT == '1'){
				isNhapPTTT = true;
			}
			//L2PT-7739
			if(data_ar[0].GET_ICD_BANDAU != null && data_ar[0].GET_ICD_BANDAU == '1'){
				isGetICD_Bandau = true;
			}
			//L2PT-8920
			if(data_ar[0].HIS_TENEKIP_NHIHDG != null && data_ar[0].HIS_TENEKIP_NHIHDG == '1'){
				changeTextEkipNhiHDG = true;
			}
			//START L2PT-16223
			if(data_ar[0].NTU_CHECK_KTBA != null && data_ar[0].NTU_CHECK_KTBA == '1'){
				checkktBA = true;
			}
			//L2PT-19388
			if(data_ar[0].NTU_PTTT_CHECK_SONGUOI != null && data_ar[0].NTU_PTTT_CHECK_SONGUOI == '1'){
				check_songuoi_thuchien = true;
			}
			if(data_ar[0].NTU_PTTT_SHOWEKIP_DUYETMO != null && data_ar[0].NTU_PTTT_SHOWEKIP_DUYETMO == '1'){
				hienthi_ekipduyetmo = true;
			}
			// L2PT-20993
			if(data_ar[0].NTU_MAU_PTTT_PHONGCD != null && data_ar[0].NTU_MAU_PTTT_PHONGCD == '1'){
				mau_pttt_theophongcd = true;
			}
			//L2PT-22996
			if(data_ar[0].NTU_IN_CAYCHI_YDGLI != null){
				mdv_in_caychi_ydgli = data_ar[0].NTU_IN_CAYCHI_YDGLI;
			}
			//L2PT-25760
			if(data_ar[0].NTU_PTTT_BVNT != null && data_ar[0].NTU_PTTT_BVNT == '1'){
				form_pttt_bvnt = data_ar[0].NTU_PTTT_BVNT;
			}
			if(!ppvc_default && data_ar[0].NTU_PTTT_PPVC_DEFAULT != null){
				ppvc_default = data_ar[0].NTU_PTTT_PPVC_DEFAULT;
			}
			//L2PT-26618
			if(data_ar[0].NTU_PTTT_PPPT_DKLSN != null && data_ar[0].NTU_PTTT_PPPT_DKLSN == '1'){
				pp_pttt_dklsn = true;;
			}
			//L2PT-27254
			if(data_ar[0].NTU_PTTT_KTPT_DKLCI != null && data_ar[0].NTU_PTTT_KTPT_DKLCI == '1' && isShowInfoTuongTrinh){
				kt_pttt_dklci = true;
				$("#div_KTPT_LCI").show();
				$("#div_KTPT").hide();
				$("#div_KTPT_LBL").show();
			}
			//L2PT-28073
			if(data_ar[0].NTU_PTTT_BTN_TT_BS != null && data_ar[0].NTU_PTTT_BTN_TT_BS == '1' ){
				$("#divTT_BS").show();
			}
			//L2PT-28383
			if(data_ar[0].NTU_PTTT_DKLSN_PHUMO != null && data_ar[0].NTU_PTTT_DKLSN_PHUMO == '1' ){
				$('#cboPHUMO1').attr('style', 'width: 43% !important');
				$("#cboPHUMO2").attr('style', 'width: 43% !important');
				$("#cboTILE_PHUMO1").show();
				$("#cboTILE_PHUMO2").show();
			}
			//L2PT-28421
			if(data_ar[0].NTU_PTTT_NTH_CDHA != null && data_ar[0].NTU_PTTT_NTH_CDHA == '1' ){
				get_nth_cdha_snvpc = true;
			}
			//L2PT-29766
			if(data_ar[0].NTU_PTTT_SHOW_ICD9CM != null && data_ar[0].NTU_PTTT_SHOW_ICD9CM == '1' ){
				$("#divICM9CM").show();
			}
			//L2PT-31604
			if(data_ar[0].NTU_PTTT_SQL_LOADUSER != null && data_ar[0].NTU_PTTT_SQL_LOADUSER != '0'){
				//JSON.parse('{ "name":"John", "age":30, "city":"New York"}');
				sql_load_user = JSON.parse(data_ar[0].NTU_PTTT_SQL_LOADUSER);
			}
			//L2PT-32306
			if(data_ar[0].NTU_SHOW_EKIP_PTTT != null && data_ar[0].NTU_SHOW_EKIP_PTTT == '1'){
				//JSON.parse('{ "name":"John", "age":30, "city":"New York"}');
				show_ekip_pttt = true;
			}
			//L2PT-320600
			if(data_ar[0].NTU_CHECK_TRUNG_VITRI == '1'){
				check_trungvitri_pttt = true;
			}
			//L2PT-34204
			if(data_ar[0].NTU_PTTT_KCD_DEFAULT == '1'){
				ch_macdinhkhoa_cd = true;
			}
			//L2PT-1404
			if(data_ar[0].NTU_PTTT_LOAD_MOME == '1'){
				$("#divPhumo_Me_Them").show();
			}
			
		}
		
		//L2PT-25760
		if(form_pttt_bvnt){
			$("#divPPPT,#divCDCHINH,#divCDPHU,#divBSTHUCHIEN").hide();
			$("#divPPPT_BVNT,#div_CDP_TRCPT,#div_CDP_SAUPT").show();
			$("#cboPHUONGPHAPPTTT_BVNT").attr("valrule","P.Pháp PTTT|required");
			$("#txtCHANDOANVAOKHOA").removeAttr("valrule");
			ComboUtil.init("txtTKMACHANDOANPHU_TRC",_sql,[], "600px",_col, function(event, ui) {
				var str = $("#txtCHANDOANPHU_TRC").val();
				if(str != '')
					str += ";";
		        $("#txtCHANDOANPHU_TRC").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
		        return false;
			});
			ComboUtil.init("txtTKMACHANDOANPHU_SAU",_sql,[], "600px",_col, function(event, ui) {
				var str = $("#txtCHANDOANPHU_SAU").val();
				if(str != '')
					str += ";";
		        $("#txtCHANDOANPHU_SAU").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
		        return false;
			});
			//ComboUtil.initComboGrid("txtTKMACHANDOANPHU_SAU",_sql,[],"600px",_col,"txtTKMACHANDOANPHU_SAU=ICD10CODE,txtCHANDOANPHU_SAU=ICD10NAME");
		}
		
		if(opt.hospital_id == '924'){
			$("#divketluan").show();
			$("#labelMota").html("Kết quả");
		}
		//START L2PT-19030
		if(opt.hospital_id == '957'  && opt.callfrom != null && opt.callfrom == '1'){
			  $("#divLbl_PPPTTT").addClass("required");
			  $("#divCDTruocPttt").addClass("required");
		}
		//LAY DICH VU KHAM BENH
		var dtSys=jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		sql_par = [];			
		sql_par.push({"name" : "[0]","value" : opt.dichvukhambenhid});
		var data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("PTTT.DICHVUKB", sql_par);	
		var row1=JSON.parse(data1)[0];
		var _madichvu=row1.MADICHVU;
		madichvu_caychi = row1.MADICHVU;
		var _tendichvu=row1.TENDICHVU;
		//START L2PT-19388
		if(check_songuoi_thuchien){
			var sql_par_checksl = [];	
			sql_par_checksl.push({
				"name" : "[0]",
				"value" : opt.dichvukhambenhid
			});
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D037.SONGUOI", sql_par_checksl);
			if (data != null) {
				var rows = JSON.parse(data);
				if (rows != null && rows.length > 0) {
					var row = rows[0];
					$('#txtSONGUOI').prop('disabled', true);
					sl_nguoi = row.SONGUOI;
					sl_giupviec = row.SL_GIUPVIEC;
					sl_nhanlucchinh = row.SL_NHANLUCCHINH;
					sl_nhanlucphu = row.SL_NHANLUCPHU;
				}
			}
			
		}else{
			$('#txtSONGUOI').val(row1.SONGUOI);
		}
		$('#txtTYLEPTTT').val(row1.TILE_PTTT);
		
		
		if(changeTextEkipNhiHDG){
			if(row1.PTTTHANGID == 0 || row1.PTTTHANGID == 1 || row1.PTTTHANGID == 2 || row1.PTTTHANGID == 3 || row1.PTTTHANGID == 4){
				$("#ptvchinh").text("Thủ thuật viên chính");
				$("#bsgayme").text("Bác sỹ gây mê/tê");
				$("#pmo1").text("Phụ");
				$("#pme1").text("Phụ mê, tê");
				$("#pmo2").text("Phụ");
				$("#lbldcvien").text("Giúp việc");
				$("#lblgv").text("Giúp việc");
			}
			if(row1.PTTTHANGID == 5 || row1.PTTTHANGID == 6 || row1.PTTTHANGID == 7 || row1.PTTTHANGID == 8 ){
				$("#ptvchinh").text("Phẫu thuật viên chính");
				$("#bsgayme").text("Bác sỹ gây mê/tê");
				$("#pmo1").text("Phụ mổ");
				$("#pme1").text("Phụ mê, tê");
				$("#pmo2").text("Phụ mổ");
				$("#lbldcvien").text("Giúp việc");
				$("#lblgv").text("Giúp việc");
			}
			
		}
		 
		//START L1PT-962
		if( (opt.hospital_id == '1014' || opt.hospital_id == '1077') && row1.EKIP_PTTT != null && row1.EKIP_PTTT == '1'){
			setEnabled([], 
					['txtTKPHAUTHUATVIEN', 'cboPHAUTHUATVIEN', 'txtTKDUNGCUVIEN', 'cboDUNGCUVIEN', 'txtTKBACSIGAYME',
					 'cboBACSIGAYME', 'txtTKPHUME', 'cboPHUME','txtTKPHUME2', 'cboPHUME2' , 'txtTKPHUMO1', 'cboPHUMO1', 
					 'txtTKPHUMO2', 'cboPHUMO2', 'txtTKPHUMO3','cboPHUMO3',  'txtTKBACSITHUCHIEN',  'cboBACSITHUCHIEN','txtTKDIEUDUONG', 'cboDIEUDUONG', 'txtTKCHAYMAYCHINH',
					 'cboCHAYMAYCHINH', 'txtTKCHAYMAYPHU', 'cboCHAYMAYPHU', 'btnCLEARPHAUTHUATVIEN','btnCLEARDUNGCUVIEN',
					 'btnCLEARBACSIGAYME','btnCLEARPHUME', 'btnCLEARPHUMO1', 'btnCLEARPHUMO2', 'btnCLEARDIEUDUONG',
					 'btnCLEARCHAYMAYCHINH', 'btnCLEARCHAYMAYPHU', 'btnCLEARPHUMO3', 'btnCLEARPHUME2','txtTK_EKIPPTTT',
					 'cboEKIPPTTT','btnEDIT_EKIPPTTT','txtTENMAU_EKIP','btnLuuMauEKIP']);//L1PT-1139
		}
		//END L1PT-962
		//START L2PT-4894
		if(opt.hospital_id == '939'){
			$('#divMaMay').show();
		}else{
			$('#divMaMay').hide();
		}
		//END L2PT-4894
		sql_par = [];
		//Begin_HaNv_28062018: Lay thong tin chan doan va chan doan kem theo tu phieu chi dinh - HISL2TK-775
		sql_par.push({"name" : "[0]","value" : opt.maubenhphamid});
		var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO("PTTT.MACHUANDOAN", sql_par);
		var row2=JSON.parse(data2)[0];
		var machandoanravien = row2.MACHANDOAN;
		var chandoanravien = row2.CHANDOAN;
		var chandoankemtheo = row2.CHANDOAN_KEMTHEO;
		//End_HaNv_28062018
		 
		//check xem da co ban ghi phau thuat thu thuat  chua 
		var rowDetail;	
		sql_par = [];			
		sql_par.push({"name" : "[0]","value" : opt.dichvukhambenhid},{"name" : "[1]","value" : opt.khambenhid});		
		var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("PTTT.CHECK", sql_par);		
		var rows = JSON.parse(data);
		if (rows != null && rows.length > 0) {
			//L2PT-34023
			if(opt.hospital_id == '10284'){
				$('#btnThemDV').show();
			}
			//L2PT-34204
			ch_macdinhkhoa_cd = false;
			_phauthuatthuthuatid=rows[0].PHAUTHUATTHUTHUATID;
			_checkUpdate=1;			
			var sql_par1=[_phauthuatthuthuatid];       	
        	var dataDetail = jsonrpc.AjaxJson.ajaxCALL_SP_O("PTTT.GET.INFO",sql_par1.join('$'));				
			rowDetail = dataDetail[0];	
			$('#txtSONGUOI').val(rowDetail.SONGUOI);
			$('#txtTYLEPTTT').val(rowDetail.TILE_PTTT);
			khoacdid = rowDetail.KHOACHIDINH != '-1' ? rowDetail.KHOACHIDINH : opt.khoaid;
			//L2PT-34023
			ptv_chinh = rowDetail.PHAUTHUATVIENID ? rowDetail.PHAUTHUATVIENID : null;
			//START L2PT-17277
			if(rowDetail.HENTRASAU == 1){
				$("#calTG_HENTRASAU").show();
			}else{
				$("#calTG_HENTRASAU").hide();
				$("#txtTG_HENTRASAU").hide();
				$("#txtTG_HENTRASAU").val('01/01/2000 00:00:00');
			}
			//START L2PT-19388
			if(check_songuoi_thuchien){
				sl_nguoi = rowDetail.SONGUOI;
				sl_giupviec = rowDetail.SL_GIUPVIEC;
				sl_nhanlucchinh = rowDetail.SL_NHANLUCCHINH;
				sl_nhanlucphu = rowDetail.SL_NHANLUCPHU;
			}
			
			//Begin_HaNv_29012019: Tinh trang PTTT (binh thuong hoac cap cuu) - L2PT-1567
			if(rowDetail.PTTT_TINHHINHID == '' || rowDetail.PTTT_TINHHINHID == null){
				sql_par = [];			
				sql_par.push({"name" : "[0]","value" : opt.dichvukhambenhid});
				var data1 = jsonrpc.AjaxJson.ajaxExecuteQueryO("PTTT.TINHTRANG_PTTT", sql_par);	
				var row1 = JSON.parse(data1)[0];
				$("#cboPTTT_TINHHINHID").val(row1.TINHTRANG_PTTT);
			}
			//End_HaNv_21062018
			
			//L2PT-27254
			if(kt_pttt_dklci){
				$("#txtKETTHUCPTTT_DKLCI").val(rowDetail.KETTHUCPTTT);
			}

			// Xử lý sự kiện liên quan ký CA => START
			$("#btnKyCa").attr("disabled", false);
			$("#btnHuyCa").attr("disabled", false);
			$("#btnInCa").attr("disabled", false);
		}else{
			//if(opt.hospital_id == '1014' || opt.hospital_id == '1077'){
			//START L2PT-19214
			sql_par = [];			
			sql_par.push({"name" : "[0]","value" : opt.maubenhphamid});		
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D037.KHOACD", sql_par);
			var rows = JSON.parse(data);
			if (rows != null && rows.length > 0) {
				khoacdid = rows[0].KHOAID; 
			}
			//}
			//L2PT-8404
			if(hienthi_ekipduyetmo){
				var sql_par1=[opt.maubenhphamid];       	
	        	var dataDetail = jsonrpc.AjaxJson.ajaxCALL_SP_O("LICHMO.INFO",sql_par1.join('$'));	
				if(dataDetail != null && dataDetail.length > 0){
					var row = dataDetail[0];
					row.BS_CHINH_ID != null ? setValueToCbo('txtTKPHAUTHUATVIEN','cboPHAUTHUATVIEN',row.BS_CHINH_ID,row.BS_CHINH) : 1 == 1;
					row.BS_PHU1_ID != null ? setValueToCbo('txtTKPHUMO1','cboPHUMO1',row.BS_PHU1_ID,row.BS_PHU1) : 1 == 1;
					row.BS_PHU2_ID != null ? setValueToCbo('txtTKPHUMO2','cboPHUMO2',row.BS_PHU2_ID,row.BS_PHU2) : 1 == 1;
					row.BS_PHU3_ID != null ? setValueToCbo('txtTKPHUMO3','cboPHUMO3',row.BS_PHU3_ID,row.BS_PHU3) : 1 == 1;
					row.GAYMECHINH_ID != null ? setValueToCbo('txtTKBACSIGAYME','cboBACSIGAYME',row.GAYMECHINH_ID,row.GAYMECHINH) : 1 == 1;
					row.PHUME_ID != null ? setValueToCbo('txtTKPHUME','cboPHUME',row.PHUME_ID,row.PHUME) : 1 == 1;
					//L2PT-32060
					row.VC_CHINH_ID != null ? setValueToCbo('txtVC_CHINH_ID','cboVC_CHINH',row.VC_CHINH_ID,row.VC_CHINH) : 1 == 1;
					row.VC_PHU_ID != null ? setValueToCbo('txtVC_PHU_ID','cboVC_PHU',row.VC_PHU_ID,row.VC_PHU) : 1 == 1;
					row.XEP_LICH_ID != null ? setValueToCbo('txtXEP_LICH_ID','cboXEP_LICH',row.XEP_LICH_ID,row.XEP_LICH) : 1 == 1;
					row.HUU_TRUNG_ID != null ? setValueToCbo('txtHUU_TRUNG_ID','cboHUU_TRUNG',row.HUU_TRUNG_ID,row.HUU_TRUNG) : 1 == 1;
					row.VO_TRUNG_ID != null ? setValueToCbo('txtVO_TRUNG_ID','cboVO_TRUNG',row.VO_TRUNG_ID,row.VO_TRUNG) : 1 == 1;
					//phuong phap vo cam
					rowDetail=new Object();
					rowDetail.PTTT_PHUONGPHAPVOCAMID = row.PPVCAM;
				}
	        	
			}
			
			if(tinhhinhPTTTmacdinh){
				$("#cboPTTT_TINHHINHID").val('2');
			}
			//L2PT-28421
			if(get_nth_cdha_snvpc){
				var sql_par1=[opt.dichvukhambenhid];       	
	        	var dataNTH= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.GETNTH",sql_par1.join('$'));		
	        	if(dataNTH.length > 0){
	        		var nguoiTH = dataNTH[0];
	        		rowDetail=new Object();
					if(nguoiTH.TKPHAUTHUATVIEN && nguoiTH.TKPHAUTHUATVIEN != ''){
						rowDetail.TKPHAUTHUATVIEN = nguoiTH.TKPHAUTHUATVIEN;
					}
					if(nguoiTH.TKPHUMO1 && nguoiTH.TKPHUMO1 != ''){
						rowDetail.TKPHUMO1 = nguoiTH.TKPHUMO1;
					}
	        	}
			}
		}
		
		if(rowDetail==null){
			rowDetail=new Object();
		}		
		//set gia tri len form phau thuat thu thuat
		sql_par = [];
		sql_par = [opt.hosobenhanid,opt.benhnhanid];       	
    	var data = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU036.GTS.TTBN",sql_par.join('$'));
    	if (data != null && data.length > 0) {
    		FormUtil.setObjectToForm("divMain", "", data[0]);    		
    	}	
    	//L2PT-34204
		var hang_pttt_id = typeof(rowDetail.PTTT_HANGID) == 'undefined' || rowDetail.PTTT_HANGID == '-1' || rowDetail.PTTT_HANGID == '' || rowDetail.PTTT_HANGID == '0' ? (typeof(row1.PTTTHANGID) == 'undefined' || row1.PTTTHANGID == null || row1.PTTTHANGID == '' ? '0':row1.PTTTHANGID) : rowDetail.PTTT_HANGID;
		if(ch_macdinhkhoa_cd && (hang_pttt_id == '5' || hang_pttt_id == '6' || hang_pttt_id == '7' || hang_pttt_id == '8') ){
			khoacdid = '-1';
		}
		
    	ComboUtil.getComboTag("cboKHOACHIDINH",'NTU02D037.EV001', [], khoacdid, {text:"--- Tất cả ---", value:-1}, 'sql');	
    	
		//khoi tao cho chuan doan chinh
		ComboUtil.initComboGrid("txtMA_CHANDOANVAOKHOA",_sql,[],"600px",_col,"txtMA_CHANDOANVAOKHOA=ICD10CODE,txtCHANDOANVAOKHOA=ICD10NAME");		
		//khoi tao chuan doan phu		
		ComboUtil.initComboGrid("txtMA_CHANDOANVAOKHOAKEMTHEO",_sql,[],"600px",_col, function(event, ui){
			$('#txtCHANDOANVAOKHOAKEMTHEO').val($("#txtCHANDOANVAOKHOAKEMTHEO").val() == '' ? ui.item.ICD10CODE +"-"+ ui.item.ICD10NAME : $("#txtCHANDOANVAOKHOAKEMTHEO").val() + ";" + ui.item.ICD10CODE +"-"+ ui.item.ICD10NAME);
			//Begin_HaNv_28062018: Lay thong tin chan doan va chan doan kem theo tu phieu chi dinh - HISL2TK-775
//			var FOMAT_MA_BENHPHU = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','FOMAT_MA_BENHPHU');
//			if(FOMAT_MA_BENHPHU == 1){
//				$('#txtCHANDOANVAOKHOAKEMTHEO').val($("#txtCHANDOANVAOKHOAKEMTHEO").val() == '' ? ui.item.ICD10CODE +"-"+ ui.item.ICD10NAME : $("#txtCHANDOANVAOKHOAKEMTHEO").val() + ";" + ui.item.ICD10CODE +"-"+ ui.item.ICD10NAME);
//			}
//			else{
//				$('#txtCHANDOANVAOKHOAKEMTHEO').val($("#txtCHANDOANVAOKHOAKEMTHEO").val() == '' ? ui.item.ICD10NAME+ "( "+ui.item.ICD10CODE +")" : $("#txtCHANDOANVAOKHOAKEMTHEO").val() + ";" + ui.item.ICD10NAME+ "( "+ui.item.ICD10CODE +")");
//			}
			//End_HaNv_28062018
		});	
		
		//khoi tao chuan doan TRUOC PTTTT
		ComboUtil.initComboGrid("txtMA_CHANDOANTRUOCPHAUTHUAT",_sql,[],"600px",_col,"txtMA_CHANDOANTRUOCPHAUTHUAT=ICD10CODE,txtCHANDOANTRUOCPHAUTHUAT=ICD10NAME");
		//khoi tao chuan doan SAU PTTTT
		ComboUtil.initComboGrid("txtMA_CHANDOANSAUPHAUTHUAT",_sql,[],"600px",_col,"txtMA_CHANDOANSAUPHAUTHUAT=ICD10CODE,txtCHANDOANSAUPHAUTHUAT=ICD10NAME");
		//Phuong phap PTTT
		ComboUtil.initComboGrid("txtPTTTID",_sqlPTTT,[],"600px",_colPTTT,"txtPTTTID=MAPTTT,txtPHUONGPHAPPTTT=TENPTTT");
		//loai PTTT
		sql_par = [];
		
		ComboUtil.getComboTag("cboPTTT_HangID",_sqlPTTT_hang, sql_par, hang_pttt_id ,{value:'-1',text:'--Lựa chọn--'},"sql");
		
		//START L2PT-8042
		if(isNhapPTTT){
			$('#cboPTTT_HangID').attr("disabled", false);
		}else{
			$('#cboPTTT_HangID').attr("disabled", true);
		}
		//phuong phap vo cam
		//L2PT-25760
		var ckhoa = rowDetail.CHUYENKHOA
		ComboUtil.getComboTag("cboCHUYENKHOA","COM.TRANGTHAI",[{"name":"[0]", "value":137}], ckhoa , "","sql");
		if(rowDetail.PTTT_PHUONGPHAPVOCAMID){
			ppvc_default = rowDetail.PTTT_PHUONGPHAPVOCAMID;
		}
		ComboUtil.getComboTag("cboPTTT_PHUONGPHAPVOCAMID",_sqlPTTT_vocam, sql_par,ppvc_default,{value:'-1',text:'--Lựa chọn--'},"sql");
		//tai bien
		ComboUtil.getComboTag("cboPTTT_TAIBIENID",_sqlPTTT_taibien, sql_par,rowDetail.PTTT_TAIBIENID,{value:'-1',text:'--Lựa chọn--'},"sql");
				
		//cach thuc pttt	
		
		$("#txtTENCACHTHUCPTTT").val(_tendichvu);
		$("#txtMACACHTHUCPTTT").val(_madichvu);
		
		//L2PT-26618
		if(pp_pttt_dklsn){
			$("#txtPHUONGPHAPPTTT").val(_tendichvu);
			$("#txtPTTTID").val(_madichvu);
		}
		
		if(_checkUpdate==0){
			$("#txtCHANDOANVAOKHOA").val(chandoanravien);
			$("#txtMA_CHANDOANVAOKHOA").val(machandoanravien);
			$("#txtCHANDOANVAOKHOAKEMTHEO").val(chandoankemtheo);
		}
		
		//Begin_HaNv_19062018: Hien thi ekip thuc hien theo khoa thiet lap - L2HOTRO-2797
		//HaNv_20170731: load doi ngu y, bac si, phau thuat vien bang initComboGrid (cho phep tim kiem)
		//load doi ngu y, bac sy phau thuat vien
		var sql_par=[];
		//phau thuat vien
		//Begin_HaNv_23102018: Hiển thị tất cả nhân viên ở ô Phẫu thuật viên 1 - L2HOTRO-11688
		if(isPtvShowAll){
			sql_par.push({"name":"[0]","value":0},{"name":"[1]","value":opt.dept_id});
		}else{
			sql_par.push({"name":"[0]","value":1},{"name":"[1]","value":opt.dept_id});
		}
		//End_HaNv_23102018
		ComboUtil.initComboGrid("txtTKPHAUTHUATVIEN",sql_load_user.TKPHAUTHUATVIEN,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHAUTHUATVIEN").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHAUTHUATVIEN").empty();
		      $("#cboPHAUTHUATVIEN").append(option);
			  //L2PT-30152
			  kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHAUTHUATVIEN",'1');
		      return false;
		});
		ComboUtil.initComboGrid("txtTKPHAUTHUATVIEN2",sql_load_user.TKPHAUTHUATVIEN2,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHAUTHUATVIEN2").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHAUTHUATVIEN2").empty();
		      $("#cboPHAUTHUATVIEN2").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHAUTHUATVIEN2",'2');
		      return false;
		});
		//dung cu vien
		sql_par=[];
		sql_par.push({"name":"[0]","value":0},{"name":"[1]","value":opt.dept_id});
		ComboUtil.initComboGrid("txtTKDUNGCUVIEN",sql_load_user.TKDUNGCUVIEN,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKDUNGCUVIEN").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboDUNGCUVIEN").empty();
		      $("#cboDUNGCUVIEN").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboDUNGCUVIEN",'3');
		      return false;
		});
		//bac sy gay me		
		ComboUtil.initComboGrid("txtTKBACSIGAYME",sql_load_user.TKBACSIGAYME,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKBACSIGAYME").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboBACSIGAYME").empty();
		      $("#cboBACSIGAYME").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboBACSIGAYME",'4');
		      return false;
		});
		//bac sy phu me
		ComboUtil.initComboGrid("txtTKPHUME",sql_load_user.TKPHUME,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUME").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUME").empty();
		      $("#cboPHUME").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUME",'5');
		      return false;
		});
		ComboUtil.initComboGrid("txtTKPHUME2",sql_load_user.TKPHUME2,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUME2").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUME2").empty();
		      $("#cboPHUME2").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUME2",'6');
		      return false;
		});
		//dieu duong vien
		ComboUtil.initComboGrid("txtTKDIEUDUONG",sql_load_user.TKDIEUDUONG,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKDIEUDUONG").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboDIEUDUONG").empty();
		      $("#cboDIEUDUONG").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboDIEUDUONG",'7');
		      return false;
		});
		//PHỤ Mổ
		ComboUtil.initComboGrid("txtTKPHUMO1",sql_load_user.TKPHUMO1,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUMO1").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUMO1").empty();
		      $("#cboPHUMO1").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUMO1",'8');
		      return false;
		});
		ComboUtil.initComboGrid("txtTKPHUMO2",sql_load_user.TKPHUMO2,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUMO2").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUMO2").empty();
		      $("#cboPHUMO2").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUMO2",'9');
		      return false;
		});
		ComboUtil.initComboGrid("txtTKPHUMO3",sql_load_user.TKPHUMO3,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUMO3").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUMO3").empty();
		      $("#cboPHUMO3").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUMO3",'10');
		      return false;
		});
		ComboUtil.initComboGrid("txtTKBACSITHUCHIEN",sql_load_user.TKBACSITHUCHIEN,sql_par,"600px",_col_loaduser, function(event, ui) {
			$("#txtTKBACSITHUCHIEN").val("");
			var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
			$("#cboBACSITHUCHIEN").empty();
			$("#cboBACSITHUCHIEN").append(option);
			//L2PT-30152
		    kiemtra_trunggio_pttt(ui.item.USER_ID,"cboBACSITHUCHIEN",'11');
			return false;
		});
		ComboUtil.initComboGrid("txtTKPHUMO4",sql_load_user.TKPHUMO4,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUMO4").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUMO4").empty();
		      $("#cboPHUMO4").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUMO4",'12');
		      return false;
		});
		ComboUtil.initComboGrid("txtTKPHUMO5",sql_load_user.TKPHUMO5,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUMO5").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUMO5").empty();
		      $("#cboPHUMO5").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUMO5",'13');
		      return false;
		});
		//End HaNv_20170731
		//End_HaNv_19062018
		
		//START L1PT-680
		ComboUtil.initComboGrid("txtTKCHAYMAYCHINH",sql_load_user.TKCHAYMAYCHINH,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKCHAYMAYCHINH").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboCHAYMAYCHINH").empty();
		      $("#cboCHAYMAYCHINH").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYCHINH",'14');
		      return false;
		});
		
		ComboUtil.initComboGrid("txtTKCHAYMAYPHU",sql_load_user.TKCHAYMAYPHU,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKCHAYMAYPHU").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboCHAYMAYPHU").empty();
		      $("#cboCHAYMAYPHU").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
		      return false;
		});
		//END L1PT-680
		//L2PT-32060
		ComboUtil.initComboGrid("txtVC_CHINH_ID",sql_load_user.VC_CHINH_ID,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtVC_CHINH_ID").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboVC_CHINH").empty();
		      $("#cboVC_CHINH").append(option);
		      //L2PT-30152
		      //kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
		      return false;
		});
		ComboUtil.initComboGrid("txtVC_PHU_ID",sql_load_user.VC_PHU_ID,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtVC_PHU_ID").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboVC_PHU").empty();
		      $("#cboVC_PHU").append(option);
		      //L2PT-30152
		      //kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
		      return false;
		});
		ComboUtil.initComboGrid("txtXEP_LICH_ID",sql_load_user.XEP_LICH_ID,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtXEP_LICH_ID").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboXEP_LICH").empty();
		      $("#cboXEP_LICH").append(option);
		      //L2PT-30152
		      //kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
		      return false;
		});
		ComboUtil.initComboGrid("txtHUU_TRUNG_ID",sql_load_user.HUU_TRUNG_ID,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtHUU_TRUNG_ID").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboHUU_TRUNG").empty();
		      $("#cboHUU_TRUNG").append(option);
		      //L2PT-30152
		      //kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
		      return false;
		});
		
		ComboUtil.initComboGrid("txtVO_TRUNG_ID",sql_load_user.VO_TRUNG_ID,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtVO_TRUNG_ID").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboVO_TRUNG").empty();
		      $("#cboVO_TRUNG").append(option);
		      //L2PT-30152
		      //kiemtra_trunggio_pttt(ui.item.USER_ID,"cboCHAYMAYPHU",'15');
		      return false;
		});
		//END L2PT-32060
		
		//START L2PT-1404
		ComboUtil.initComboGrid("txtTKPHUME3",sql_load_user.TKPHUME,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUME3").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUME3").empty();
		      $("#cboPHUME3").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUME",'5');
		      return false;
		});
		ComboUtil.initComboGrid("txtTKPHUME4",sql_load_user.TKPHUME,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUME4").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUME4").empty();
		      $("#cboPHUME4").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUME",'5');
		      return false;
		});
		ComboUtil.initComboGrid("txtTKPHUME5",sql_load_user.TKPHUME,sql_par,"600px",_col_loaduser, function(event, ui) {
			  $("#txtTKPHUME5").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUME5").empty();
		      $("#cboPHUME5").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUME",'5');
		      return false;
		});
		ComboUtil.initComboGrid("txtTKPHUME6",sql_load_user.TKPHUME,sql_par,"600px",_col_loaduser, function(event, ui) {
			$("#txtTKPHUME6").val("");
		      var option = $('<option value="'+ui.item.USER_ID+'">'+ui.item.FULLNAME+'</option>');
		      $("#cboPHUME6").empty();
		      $("#cboPHUME6").append(option);
		      //L2PT-30152
		      kiemtra_trunggio_pttt(ui.item.USER_ID,"cboPHUME",'5');
		      return false;
		});
		//END L2PT-1404
		
		FormUtil.setObjectToForm("divMain", "", rowDetail);
		//L2PT-7739
		//if(!isGetICD_Bandau){
		//START fix loi ma icd cd vao khoa co trong ten icd => hien thi sai icd, lay icd dau tien co ma icd
		if(rowDetail.MA_CHANDOANVAOKHOA != null && rowDetail.MA_CHANDOANVAOKHOA != ''){
			machuandoanvaokhoa = rowDetail.MA_CHANDOANVAOKHOA;
		}else{
			machuandoanvaokhoa = machandoanravien;
		}
		if(rowDetail.CHANDOANVAOKHOA != null && rowDetail.CHANDOANVAOKHOA != ''){
			tenchuandoanvaokhoa = rowDetail.CHANDOANVAOKHOA;
		}else{
			tenchuandoanvaokhoa = chandoanravien;
		}
		//START L2PT-9014
		if(rowDetail.MA_CHANDOANTRUOCPHAUTHUAT != null && rowDetail.MA_CHANDOANTRUOCPHAUTHUAT != ''){
			machuandoantruocpt = rowDetail.MA_CHANDOANTRUOCPHAUTHUAT;
		}else{
			machuandoantruocpt = machandoanravien;
			if(form_pttt_bvnt){
				machuandoantruocpt = machuandoanvaokhoa;
			}
		}
		if(rowDetail.CHANDOANTRUOCPHAUTHUAT != null && rowDetail.CHANDOANTRUOCPHAUTHUAT != ''){
			tenchuandoantruocpt = rowDetail.CHANDOANTRUOCPHAUTHUAT;
		}else{
			tenchuandoantruocpt = chandoanravien;
			if(form_pttt_bvnt){
				tenchuandoantruocpt = tenchuandoanvaokhoa;
			}
		}
		if(rowDetail.MA_CHANDOANSAUPHAUTHUAT != null && rowDetail.MA_CHANDOANSAUPHAUTHUAT != ''){
			machuandoansaupt = rowDetail.MA_CHANDOANSAUPHAUTHUAT;
		}else{
			machuandoansaupt = machandoanravien;
			if(form_pttt_bvnt){
				machuandoansaupt = machuandoanvaokhoa;
			}
		}
		if(rowDetail.CHANDOANSAUPHAUTHUAT != null && rowDetail.CHANDOANSAUPHAUTHUAT != ''){
			tenchuandoansaupt = rowDetail.CHANDOANSAUPHAUTHUAT;
		}else{
			tenchuandoansaupt = chandoanravien;
			if(form_pttt_bvnt){
				tenchuandoansaupt = tenchuandoanvaokhoa;
			}
		}
		
		//END L2PT-9014
		$(document).ready(function() {
			setTimeout(function () {
				$("#txtMA_CHANDOANVAOKHOA").val(machuandoanvaokhoa);
				$("#txtCHANDOANVAOKHOA").val(tenchuandoanvaokhoa);
				//L2PT-25760
				
				$("#txtMA_CHANDOANTRUOCPHAUTHUAT").val(machuandoantruocpt);
				$("#txtCHANDOANTRUOCPHAUTHUAT").val(tenchuandoantruocpt);
				$("#txtMA_CHANDOANSAUPHAUTHUAT").val(machuandoansaupt);
				$("#txtCHANDOANSAUPHAUTHUAT").val(tenchuandoansaupt);
				
				
				//START L2PT-19388
				if(check_songuoi_thuchien){
					$('#txtSONGUOI').val(sl_nguoi);
				}
		    }, 200);
			
		});		
			//END fix loi ma icd cd vao khoa
		//}
		
		$("#txtTENPHONG").val(opt.tenphong);
		$("#txtVAOVIENLUC").val(opt.thoigianvaovien);		
		$("#txtNGHENGHIEP").val(opt.nghenghiep);	
		
		if($("#txtNGAYPHAUTHUATTHUTHUAT").val()==''){
			//L2PT-34076
			if(opt.hospital_id == '923' && opt.ngaymaubenhpham){
				$("#txtNGAYPHAUTHUATTHUTHUAT").val(opt.ngaymaubenhpham);	
			}else{
				$("#txtNGAYPHAUTHUATTHUTHUAT").val(dtSys);	
			}
			
		}
		
		//Begin_HaNv_19062018: Bổ sung thêm thông tin - L2DKBD-1275
		if(isShowInfoTuongTrinh){
			if($("#txtBATDAUTHUOCME").val()==''){
				$("#txtBATDAUTHUOCME").val(dtSys);	
			}
			if($("#txtDUTTHUOCME").val()==''){
				$("#txtDUTTHUOCME").val(dtSys);	
			}
			//L2PT-27254
			if(kt_pttt_dklci){
				if($("#txtKETTHUCPTTT_DKLCI").val()==''){
					$("#txtKETTHUCPTTT_DKLCI").val(dtSys);	
				}
			}else{
				if($("#txtKETTHUCPTTT").val()==''){
					$("#txtKETTHUCPTTT").val(dtSys);	
				}
			}
			
		}
		//End_HaNv_19062018
		
		$("#txtMA_CHANDOANVAOKHOA").focus();
		
		
		//check ket thuc benh an
		sql_par = [];			
		sql_par.push({"name" : "[0]","value" : opt.tiepnhanid});		
		var thangthaitiepnhan = jsonrpc.AjaxJson.getOneValue("PTTT.CHECK_KTBA",sql_par);
		if(thangthaitiepnhan != '0' && checkktBA){
			$("#calNGAYPHAUTHUATTHUTHUAT").hide();
			$("#txtNGAYPHAUTHUATTHUTHUAT").prop('disabled', true);
		}else{
			$("#calNGAYPHAUTHUATTHUTHUAT").show();
		}	
		//START L2PT-469
		loadCombomau();
		//END L2PT-469
		//START L1PT-680
		if(opt.hospital_id == '1014' || opt.hospital_id == '1077'){
			$("#lbldcvien").text("Dụng cụ vòng trong");
			$("#lblgv").text("Dụng cụ vòng ngoài");
			$("#ptttluc").text("Bắt đầu PTTT");
			$("#ptvchinh").text("Phẫu thuật viên chính");
			$("#divChaymayBM").show();
			$("#divEkipPTTT").show();
		}else{
			$("#divChaymayBM").hide();
			$("#divEkipPTTT").hide();
		}
		//END L1PT-680
		//STARTL2PT-32306--show ekip pttt
		if(show_ekip_pttt){
			$("#divEkipPTTT").show();
		}
		//L2PT-32306
		
		//START L1PT-1139
		_colTEN_MAUEKIP = "Tên mẫu,TENMAU_EKIP,30,0,f,l";
		var _sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			"value" : opt.khoaid
		});
		ComboUtil.initComboGrid("txtTK_EKIPPTTT","TENMAU_EKIP",_sql_par, "300px", _colTEN_MAUEKIP, function(event, ui) {
			var idMau = ui.item.ID;
			var option = $('<option value="'+ui.item.ID+'">'+ui.item.TENMAU_EKIP+'</option>');
		    $("#cboEKIPPTTT").empty();
		    $("#cboEKIPPTTT").append(option);
		    $("#txtTENMAU_EKIP").val(ui.item.TENMAU_EKIP);
			var sql_par1=[idMau];       	
        	var dataDetail = jsonrpc.AjaxJson.ajaxCALL_SP_O("TT_MAUEKIP_PTTT",sql_par1.join('$'));				
			if(dataDetail != null && dataDetail.length > 0){
				rowDetail = dataDetail[0];
				FormUtil.setObjectToForm("divMain", "", rowDetail);  
			}
        	
		});
		//END L1PT-1139
		//L2PT-34023
		if(opt.loaiptttid){
			//BVTM-2458 check loai pttt la thu thuat thi khong load cac loai pttt
			if('1,2,3,4'.indexOf(hang_pttt_id) != -1 ){
				//bv bưu điện tỉ lệ thủ thuật = tỉ lệ PT chính => add value = id của PT chính
				$('#cboLOAIPTTTID').append('<option value="0">Thủ thuật</option>')
			}else{
				ComboUtil.getComboTag("cboLOAIPTTTID",'NTU02D037.LOAIPTTT', [], opt.loaiptttid, {text:"--- chọn ---", value:-1}, 'sql');
			}
		}

		//DoanPV_20210511 check chuyển BN về hậu phẫu BDHNI
		var _chuyenBN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NTU_PTTT_CHUYEN_BN');
		if(_chuyenBN == 1) {
			var objCheck = new Object();
			objCheck.KHAMBENHID = opt.khambenhid;
			objCheck.MAUBENHPHAMID = opt.maubenhphamid;
			objCheck.KHOAID = opt.dept_id;
			var _check = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D049.CHECK.CBN", JSON.stringify(objCheck));
			if (_check == '1') {
				$("#divChuyenBN").show();
				$('#chkCHUYENHAUPHAU').prop('checked', true);
			} else if (_check == '2') {
				DlgUtil.showMsg("Bệnh nhân đã chuyển về hậu phẫu");
			} else if (_check == '3') {
				$("#divChuyenBN").show();
			}
		}

		// Xử lý sự kiện liên quan ký CA => START
		var _cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_SUDUNG_KYSO_KYDIENTU');
		if(_cauhinh != '0' && typeof _cauhinh !== "undefined") {
			$("#btnKyCa").show();
			$("#btnHuyCa").show();
			$("#btnInCa").show();
		}
		// Xử lý sự kiện liên quan ký CA => END
	}	
	//in phieu
	function inPhieu() {
		$("#btnExport").on("click", function(e) {	
			print();
		})
	};
	
	//L2PT-6521
	function print() {
		if(opt.hospital_id == '965'){
			var par = [ {
  				name : 'ptttid',
  				type : 'String',
  				value : _phauthuatthuthuatid
  			}];
			//BVTM-5887 *********
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.LIETKEANH",opt.maubenhphamid + "$");
			var max = (data_ar.length > 6 ? 6 : data_ar.length);
	        for (var i = 0; i < max; i++) {
		          var hinh_anh = data_ar[i]["DUONGDANFILE"];
		          if (hinh_anh != "" && hinh_anh != null) {
		            par.push({
		              name : 'hinh_anh' + i,
		              type : 'Image',
		              value : hinh_anh
		            });
		          }
		     }
            par.push({
              name : 'soluong',
              type : 'Integer',
              value : max
            });	   
			openReport('window', "PHIEU_TUONGTRINHPHAUTHUAT_A4", "pdf", par);
			//BVTM-5887 E17092021
		}else{
			var par = [ {
 				name : 'dichvukhambenhid',
 				type : 'String',
 				value : opt.dichvukhambenhid
 			},{
 				name : 'benhnhanid',
 				type : 'String',
 				value : opt.benhnhanid
 			}];
			//L2PT-22996
			if(opt.hospital_id == '913' && (madichvu_caychi == '08.0007.0227' || mdv_in_caychi_ydgli.indexOf(madichvu_caychi) != -1 ) ){
				openReport('window', "PHIEU_KETQUA_CAYCHI_913", "pdf", par);
			}else{
				
				//start L2PT-5577(L2PT-5654)
	 			var param = [opt.dichvukhambenhid];
	 			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("PTTT.GETIMG", param.join('$'));
	 			if(data_ar != null){
	 				for(var i=0 ; i < data_ar.length ; i++){
	 	 				var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
	 	 				if(hinh_anh1 != null && hinh_anh1 != "") par.push({name:'hinh_anh'+i,type:'Image',value:hinh_anh1});
	 	 			}
	 			}
	 			//end L2PT-5577(L2PT-5654)
		 		openReport('window', "NTU030_PHIEUPHAUTHUATTHUTHUAT_14BV01_QD4069_A4", "pdf", par);
				}
			
		}
	};
	function bindEvent(){
		saveAndClose();
		// luu them
		saveOrUpdate();
		//dong trang
		closePage();
		//in phieu
		inPhieu();
		
		//L2PT-6521
		saveAndPrint();

		//ký số
		saveAndCa();
		huyCa();
		exportKyCA()

		var f2 = 113;
		$(document).unbind('keydown').keydown(function (e) {                             
		 if(e.keyCode == f2){
		  getIcd(e.target);
		 }
		});
		
		EventUtil.setEvent("assignSevice_resultTK", function(e) {
		   if(e.mode == '0'){
		    $('#' + e.ctrId).combogrid("setValue",e.text);
		   } else if(e.mode == '1'){
		    $('#' + e.ctrTargetId).val($('#' + e.ctrTargetId).val() == '' ? "" + e.text : $('#' + e.ctrTargetId).val() + ";" + e.text);
		   }
		   DlgUtil.close(e.popupId);
		});
		
		//L2PT-34023
		EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function(e) {
			DlgUtil.showMsg(e.msg);	
			//loadGrid();		
			DlgUtil.close("divDlgDichVu");
			if(e.result){
				var result = e.result.split('@')[1];
				opt.dichvukhambenhid = result;
				//BVTM-1295
				ppvc_default = $("#cboPTTT_PHUONGPHAPVOCAMID").val();
				initControl();
			}
		});
		
		$('#btnCLEARPHAUTHUATVIEN').on("click",function() {
			$("#txtTKPHAUTHUATVIEN").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHAUTHUATVIEN").empty();
		    $("#cboPHAUTHUATVIEN").append(option);
		});
		$('#btnCLEARPHAUTHUATVIEN2').on("click",function() {
			$("#txtTKPHAUTHUATVIEN2").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHAUTHUATVIEN2").empty();
		    $("#cboPHAUTHUATVIEN2").append(option);
		});
		//START L1PT-680
		$('#btnCLEARCHAYMAYCHINH').on("click",function() {
			$("#txtTKCHAYMAYCHINH").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboCHAYMAYCHINH").empty();
		    $("#cboCHAYMAYCHINH").append(option);
		});
		$('#btnCLEARCHAYMAYPHU').on("click",function() {
			$("#txtTKCHAYMAYPHU").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboCHAYMAYPHU").empty();
		    $("#cboCHAYMAYPHU").append(option);
		});
		//END L1PT-680
		$('#btnCLEARDUNGCUVIEN').on("click",function() {
			$("#txtTKDUNGCUVIEN").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboDUNGCUVIEN").empty();
		    $("#cboDUNGCUVIEN").append(option);
		});
		$('#btnCLEARBACSIGAYME').on("click",function() {
			$("#txtTKBACSIGAYME").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboBACSIGAYME").empty();
		    $("#cboBACSIGAYME").append(option);
		});
		$('#btnCLEARPHUME').on("click",function() {
			$("#txtTKPHUME").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUME").empty();
		    $("#cboPHUME").append(option);
		});
		$('#btnCLEARPHUME2').on("click",function() {
			$("#txtTKPHUME2").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUME2").empty();
		    $("#cboPHUME2").append(option);
		});
		$('#btnCLEARDIEUDUONG').on("click",function() {
			$("#txtTKDIEUDUONG").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboDIEUDUONG").empty();
		    $("#cboDIEUDUONG").append(option);
		});
		$('#btnCLEARPHUMO1').on("click",function() {
			$("#txtTKPHUMO1").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUMO1").empty();
		    $("#cboPHUMO1").append(option);
		});
		$('#btnCLEARPHUMO2').on("click",function() {
			$("#txtTKPHUMO2").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUMO2").empty();
		    $("#cboPHUMO2").append(option);
		});
		$('#btnCLEARPHUMO3').on("click",function() {
			$("#txtTKPHUMO3").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUMO3").empty();
		    $("#cboPHUMO3").append(option);
		});
		$('#btnCLEARBACSITHUCHIEN').on("click",function() {
			$("#txtTKBACSITHUCHIEN").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
			$("#cboBACSITHUCHIEN").empty();
			$("#cboBACSITHUCHIEN").append(option);
		});
		$('#btnCLEARPHUMO4').on("click",function() {
			$("#txtTKPHUMO4").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUMO4").empty();
		    $("#cboPHUMO4").append(option);
		});
		$('#btnCLEARPHUMO5').on("click",function() {
			$("#txtTKPHUMO5").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUMO5").empty();
		    $("#cboPHUMO5").append(option);
		});
		//START L2PT-4898
		
		//Chụp hình lưu base 64
		$('#btnChupHinh').on('click', function (e) {
			if(imgIndex==0)
			{
				if(navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
					// Not adding `{ audio: true }` since we only want video now
					navigator.mediaDevices.getUserMedia({ video: true }).then(function(stream) {
						//video.src = window.URL.createObjectURL(stream);
						video.srcObject = stream;
						video.play();
					});
					imgIndex=imgIndex+1;
				}
			}
			else 
			{
				context.drawImage(video, 0, 0, width, height);
				var dt = new Date();
				var pictureName = "CM"+dt.getFullYear() +""+ (dt.getMonth() + 1) + ""+dt.getDate()+ "" + dt.getHours()+""+ dt.getMinutes()+""+ dt.getSeconds();
				var uri = canvas.toDataURL("image/png");
				var rs = UploadUtil.uploadBase64(uri,'png');
				var param = opt.maubenhphamid + '$' + rs.id + '$' + rs.url +
				'$'+pictureName;
				var kqUpAnh = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUANH", param);
				lietKeAnh();
			}
        });	
		// upload ảnh lên server, sau khi upload thành công thì update vào database
		$('#btnUpload').on("click", function (){
			var filename = $("#fileUpload").val();

	        // Use a regular expression to trim everything before final dot
	        var extension = filename.replace(/^.*\./, '');

	        // Iff there is no dot anywhere in filename, we would have extension == filename,
	        // so we account for this possibility now
	        if (extension == filename) {
	            extension = '';
	        } else {
	            // if there is an extension, we convert to lower case
	            // (N.B. this conversion will not effect the value of the extension
	            // on the file upload.)
	            extension = extension.toLowerCase();
	        }

	        switch (extension) {
	            case 'jpg':
	            case 'jpeg':
	            case 'png':
	            case 'bmp':
	    			UploadUtil.upload("formUpload","", luuAnh);
	    			break;
	            default:
	                alert("Chỉ cho phép upload ảnh định dạng jpeg, png, bmp");
	                submitEvent.preventDefault();
	        }
	    });
		//END BVTM-2902
		//L2PT-32060
		$('#btnCLEARVC_CHINH').on("click", function () {
            $("#txtVC_CHINH_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboVC_CHINH").empty();
            $("#cboVC_CHINH").append(option);
        });

        $('#btnCLEARVC_PHU').on("click", function () {
            $("#txtVC_PHU_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboVC_PHU").empty();
            $("#cboVC_PHU").append(option);
        });

        $('#btnCLEARXEP_LICH').on("click", function () {
            $("#txtXEP_LICH_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboXEP_LICH").empty();
            $("#cboXEP_LICH").append(option);
        });

        $('#btnCLEARHUU_TRUNG').on("click", function () {
            $("#txtHUU_TRUNG_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboHUU_TRUNG").empty();
            $("#cboHUU_TRUNG").append(option);
        });
        $('#btnCLEARVO_TRUNG').on("click", function () {
            $("#txtVO_TRUNG_ID").val("");
            var option = $('<option value="-1">--Lựa chọn--</option>');
            $("#cboVO_TRUNG").empty();
            $("#cboVO_TRUNG").append(option);
        });
        //END L2PT-32060
        //START 1404
        $('#btnCLEARPHUME3').on("click",function() {
			$("#txtTKPHUME3").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUME3").empty();
		    $("#cboPHUME3").append(option);
		});
        $('#btnCLEARPHUME4').on("click",function() {
			$("#txtTKPHUME4").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUME4").empty();
		    $("#cboPHUME4").append(option);
		});
        $('#btnCLEARPHUME5').on("click",function() {
			$("#txtTKPHUME5").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUME5").empty();
		    $("#cboPHUME5").append(option);
		});
        $('#btnCLEARPHUME6').on("click",function() {
			$("#txtTKPHUME6").val("");
			var option = $('<option value="-1">--Lựa chọn--</option>');
		    $("#cboPHUME6").empty();
		    $("#cboPHUME6").append(option);
		});
        //END 1404
		//START -- HISL2TK-691 --hongdq
		loadCboMau();
		
		$("#cboMAULUOCDOPTTT").on("change",function(e){
			setEnabled(['btnXoaMauLUOCDO'],[]);
			var tmp_id = $("#cboMAULUOCDOPTTT").val();
			if(tmp_id != ''){
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.MAUCT",tmp_id);
				if (data_ar != null && data_ar.length > 0) {
					var row=data_ar[0];
					$("#txtLUOCDOPTTT").val(row.NOIDUNG);
				}
			}else{
				$("#txtLUOCDOPTTT").val("");
			}
		});
		$("#cboMAUTRINHTUPTTT").on("change",function(e){
			setEnabled(['btnXoaMauTRINHTU'],[]);
			var tmp_id = $("#cboMAUTRINHTUPTTT").val();
			if(tmp_id != ''){
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.MAUCT",tmp_id);
				if (data_ar != null && data_ar.length > 0) {
					var row=data_ar[0];
					$("#txtTRINHTUPTTT").val(row.NOIDUNG);
				}
			}else{
				$("#txtLUOCDOPTTT").val("");
			}
		});
		$("#btnLuuMauLUOCDO").on("click", function(e) {
			if($("#txtTENMAULUOCDOPTTT").val().trim() == ""){
				$("#txtTENMAULUOCDOPTTT").focus();
				return DlgUtil.showMsg("Chưa nhập tên mẫu.");
			}
			if($("#txtLUOCDOPTTT").val().trim() == ""){
				$("#txtLUOCDOPTTT").focus();
				return DlgUtil.showMsg("Chưa nhập nội dung.");
			}
			var objData = new Object();
			var _par;				 
			objData["LOAI"]	= '1';
			objData["TENMAU"]	= $("#txtTENMAULUOCDOPTTT").val().trim();
			objData["NOIDUNG"]	= $("#txtLUOCDOPTTT").val().trim();
			objData["KHOAID"]	= opt.khoaid;
			
			_par = [JSON.stringify(objData)];
			var _result=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUMAU",_par.join('$'));
			if(_result==1){
				DlgUtil.showMsg("Lưu mẫu thành công");
				$("#txtTENMAULUOCDOPTTT").val("");
				$("#txtLUOCDOPTTT").val("");
				loadCboMau();
			}
			else if(_result==2){
				DlgUtil.showMsg("Tên mẫu bị trùng");
				loadCboMau();
			}
			else{
				DlgUtil.showMsg("Lưu mẫu không thành công");
			}
		})
		$("#btnXoaMauLUOCDO").on("click", function(e) {
			if($("#cboMAULUOCDOPTTT").val() == ""){
				return DlgUtil.showMsg("Chưa chọn mẫu.");
			}
			
			var sql_par=[];
			sql_par.push({"name":"[0]","value":$("#cboMAULUOCDOPTTT").val()});	
			jsonrpc.AjaxJson.execute("NTU02D037.XOAMAU", sql_par); 
			DlgUtil.showMsg("Xóa thành công !");
			$("#txtLUOCDOPTTT").val("");
			loadCboMau();
		})
		$("#btnLuuMauTRINHTU").on("click", function(e) {
			if($("#txtTENMAUTRINHTUPTTT").val().trim() == ""){
				$("#txtTENMAUTRINHTUPTTT").focus();
				return DlgUtil.showMsg("Chưa nhập tên mẫu.");
			}
			if($("#txtTRINHTUPTTT").val().trim() == ""){
				$("#txtTRINHTUPTTT").focus();
				return DlgUtil.showMsg("Chưa nhập nội dung.");
			}
			var objData = new Object();
			var _par;				 
			objData["LOAI"]	= '2';
			objData["TENMAU"]	= $("#txtTENMAUTRINHTUPTTT").val().trim();
			objData["NOIDUNG"]	= $("#txtTRINHTUPTTT").val().trim();
			objData["KHOAID"]	= opt.khoaid;
			_par = [JSON.stringify(objData)];
			var _result=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUMAU",_par.join('$'));
			if(_result==1){
				DlgUtil.showMsg("Lưu mẫu thành công");
				$("#txtTENMAUTRINHTUPTTT").val("");
				$("#txtTRINHTUPTTT").val("");
				loadCboMau();
			}
			else if(_result==2){
				DlgUtil.showMsg("Tên mẫu bị trùng");
				loadCboMau();
			}
			else{
				DlgUtil.showMsg("Lưu mẫu không thành công");
			}
		})
		$("#btnXoaMauTRINHTU").on("click", function(e) {
			if($("#cboMAUTRINHTUPTTT").val() == ""){
				return DlgUtil.showMsg("Chưa chọn mẫu.");
			}
			var sql_par=[];
			sql_par.push({"name":"[0]","value":$("#cboMAUTRINHTUPTTT").val()});	
			jsonrpc.AjaxJson.execute("NTU02D037.XOAMAU", sql_par); 
			DlgUtil.showMsg("Xóa thành công !");
			$("#txtTRINHTUPTTT").val("");
			loadCboMau();
		})
		//END -- HISL2TK-691 --hongdq
		
		//START L2PT-469
		$("#btnLuuMau").on("click", function(e) {
			if($("#txtTEN_MAU").val() == ''){
				return DlgUtil.showMsg("Chưa nhập tên mẫu.");
			}
			var _sql_par = [{"name":"[0]", "value":$("#txtTEN_MAU").val()}];
			var checkP = jsonrpc.AjaxJson.getOneValue("NTU02D037.CHECK_TEN", _sql_par);
			if(checkP == '0'){
				var objData = new Object();
				var _par;				 
				FormUtil.setFormToObject("","",objData);
				//START L2PT-20993
				objData["KHOAID"]	= opt.khoaid;//khoa chi dinh
				objData["PHONGID"]	= opt.phongid;//phong chi dinh
				//L2PT-16204
				var data_arr_Cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","NTU_LDTT_PTTT_BND;NTU_NHAP_CDC");
				  		
				if(data_arr_Cauhinh != null && data_arr_Cauhinh.length > 0){
					  if(data_arr_Cauhinh[0].NTU_LDTT_PTTT_BND == '1'){
						  objData.TRINHTUPTTT = $("#txtTRINHTUPTTT_BND").val();
					  }
				}
				_par = [JSON.stringify(objData)];
				var _result=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.MAU",_par.join('$'));
				if(_result=='1'){				
					   DlgUtil.showMsg("Lưu mẫu thành công!");
					   loadCombomau();
				}else {				
					   DlgUtil.showMsg("Lưu mẫu không thành công!");
				}
			}else{
				return DlgUtil.showMsg("Tên mẫu đã có.");
			}
			
		});
		
		//START L1PT-1139
		$("#btnLuuMauEKIP").on("click", function(e) {
			if($("#txtTENMAU_EKIP").val() == ''){
				return DlgUtil.showMsg("Chưa nhập tên mẫu ekip.");
			}
			if($("#cboPHAUTHUATVIEN").val() == '-1' && $("#cboBACSIGAYME").val() == '-1' && $("#cboDUNGCUVIEN").val() == '-1' &&
					$("#cboPHUMO1").val() == '-1' && $("#cboPHUME").val() == '-1' && $("#cboDIEUDUONG").val() == '-1' 
					&& $("#cboPHUMO2").val() == '-1' && $("#cboCHAYMAYCHINH").val() == '-1' && $("#cboCHAYMAYCHINH").val() == '-1'){
				return DlgUtil.showMsg("Chưa nhập thông tin vị trí thực hiện.");
			}
			var objData = new Object();
			var _par;				 
			objData.KHOAID = opt.khoaid;
			FormUtil.setFormToObject("","",objData);			
			_par = [JSON.stringify(objData)];
			var _result=jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.MAUEKIP",_par.join('$'));
			if(_result=='1'){				
				   DlgUtil.showMsg("Lưu mẫu thành công!");
				   sql_par = [];
			}else if(_result == '3'){ 
				   DlgUtil.showMsg("Tên mẫu đã có!");
			}else {	
				   DlgUtil.showMsg("Lưu mẫu không thành công!");
			}
			
		});
		//END L1PT-1139
		
		$("#btnXoaMau").on("click", function(e) {
			var tmp_id_xoa = $("#cboMAUPTTT").val();
			var ten_mau_xoa = $( "#cboMAUPTTT option:selected" ).text();
			if(tmp_id_xoa == "-1"){
				DlgUtil.showMsg("Chưa chọn mẫu để xóa");
				$('#cboMAUPTTT').focus();
				return false;
			}
			DlgUtil.showConfirm("Bạn có muốn xóa mẫu " +ten_mau_xoa+" không?",function(flag) {
				if(flag){
					var sql_par=[];
					sql_par.push({"name":"[0]","value":tmp_id_xoa});	
					jsonrpc.AjaxJson.execute("NTU02D037.XOAPT", sql_par); 
					DlgUtil.showMsg("Xóa mẫu " +ten_mau_xoa+" thành công");
					loadCombomau();
				}
			});
		});
		
		$("#cboMAUPTTT").on("change",function(e){
			var mauid =$("#cboMAUPTTT").val();
			var mau_dhytb;//L2PT-1437
			sql_par = [];
			sql_par = [mauid];       	
	    	var data = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.MAUCT",sql_par.join('$'));
	    	if (data != null && data.length > 0) {
	    		//L2PT-16204
	    		var data_arr_Cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","NTU_LDTT_PTTT_BND;NTU_NHAP_CDC;" +
	    				"NTU_PTTT_BND_MAUCT;NTU_PTTT_MAU_DHYTB");
	    		if(data_arr_Cauhinh != null && data_arr_Cauhinh.length > 0){
	  			  if(data_arr_Cauhinh[0].NTU_LDTT_PTTT_BND == '1'){
	  				$("#txtTRINHTUPTTT_BND").val(data[0].TRINHTUPTTT);
	  			  }
	  			  //L2PT-21108
	  			  if(data_arr_Cauhinh[0].NTU_PTTT_BND_MAUCT == '1'){
	  				delete data[0].CHANDOANVAOKHOA;
	  				delete data[0].MA_CHANDOANVAOKHOA;
	  			  }
	  			  //L2PT-1437
    	  		  if(data_arr_Cauhinh[0].NTU_PTTT_MAU_DHYTB == '1'){
    	  			mau_dhytb = true
    	  		  }
	  		    }
	    		//L2PT-1437
	    		if(mau_dhytb){
	    			$("#txtMOTA").val(data[0].MOTA);
	    		}else{
	    			FormUtil.setObjectToForm("divMain", "", data[0]);    
	    		}
	    			
	    		//START L2PT-7767
	    		var dtSys=jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
	    		$("#txtNGAYPHAUTHUATTHUTHUAT").val(dtSys);
	    	}
		});
		//END L2PT-469
		
		$('#btnEDIT_EKIPPTTT').on("click",function() {
			var par = {
				thamSo1 : 'hihi',
			};
			dlgPopup = DlgUtil.buildPopupUrl(
				"NTU02D117_DanhMucKipPTTT",
				"divDsKipPTTT",
				"manager.jsp?func=../noitru/NTU02D117_DanhMucKipPTTT",
				par,
				"Danh sách ekip PTTT",
				1000,
				520);
			dlgPopup.open();
		});
		//START L2PT-17277
		$("#chkHENTRASAU").change(function() {
			if(this.checked) {
				$("#calTG_HENTRASAU").show();
				$("#txtTG_HENTRASAU").show();
			}else{
				$("#calTG_HENTRASAU").hide();
				$("#txtTG_HENTRASAU").hide();
				$("#txtTG_HENTRASAU").val("");
			}
		});
		//L2PT-25760
		$("#btnCLEARCHANDOANRAVIEN_TRC").on("click",function(e){
			$('#txtCHANDOANPHU_TRC').val('');
		});
		$("#btnCLEARCHANDOANRAVIEN_SAU").on("click",function(e){
			$('#txtCHANDOANPHU_SAU').val('');
		});
		//L2PT-34023
		$('#btnThemDV').on("click",function() {
			//Chỉ cho phép phẫu thuật viên chính chỉ định thêm dịch vụ phẫu thuật phụ đi kèm
			//BVTM-949
			/*var sql_par_off = [];
	    	sql_par_off.push({
				"name" : "[0]",
				"value" : opt.user_id
			});
			var data2 = jsonrpc.AjaxJson.ajaxExecuteQueryO(
						"NTUD095.OFFICE", sql_par_off);
			var rows = JSON.parse(data2);
			var officer_id = rows[0]["OFFICER_ID"];
			if(ptv_chinh != officer_id){
				return DlgUtil.showMsg("Chỉ phẫu thuật viên chính được thêm dịch vụ phụ đi kèm");
			}*/
			
			boSungDichVu();
			
		});
		//BVTM-2853
		$('#txtNGAYPHAUTHUATTHUTHUAT').on('keypress', function (e) {
	         if(e.which === 13){
	            $('#txtKETTHUCPTTT_DKLCI').focus();
	         }
	   });
	}
	
	// lưu lại đường dẫn ảnh đã upload ứng với kết quả đang nhập
	function luuAnh(data){    	
		var param = opt.maubenhphamid + '$' + data.id + '$' + data.url +
		'$'+document.getElementById('fileUpload').files[0].name.replace(/\.[^/.]+$/, "");
		
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.LUUANH", param);
		
		if(rs==1){
			$("#fileUpload").val('');
			lietKeAnh();
		}
		else{
			DlgUtil.showMsg("Nhập ảnh thất bại: "+rs.error_msg,undefined,3000);
		}
	}
	
	// liệt kê ảnh đã lưu trong cơ sở dữ liệu
	function lietKeAnh(){
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D037.LIETKEANH",opt.maubenhphamid + "$");
		if(data_ar!=null && data_ar.length>0) 
		{
			$("#SoAnhDaChup").html('('+data_ar.length+')');
		}
		else 
		{
			$("#SoAnhDaChup").html('');
		}
		$("#list").html('');
		for(var i=0; i<data_ar.length; i++){
			var img = "<div class='col-md-3' style='text-align:center;'>"+
							"<img src='"+data_ar[i]["DUONGDANFILE"]+"' width='150px' alt='"+data_ar[i]["MOTA"]+"' />"+		
							"<input type='image' src='../common/icon/delete.png' width='18px' onclick='NTU02D037.xoaAnh("+data_ar[i]["FILEID"]+");' />"+
							"</br><i>"+data_ar[i]["MOTA"]+"</i>"+
					  "</div>";
				
			$("#list").append(img);
		}
	}
	
	// xóa ảnh đã upload
	function xoaAnh(idfile){
		var param = opt.maubenhphamid + '$' + idfile;
		
		var rs = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D037.XOAANH", param);
		
		if(rs == 1){
			$("#fileUpload").val('');
			var rt = UploadUtil.deleteMedia(idfile,'');
			if(rt == idfile){
				DlgUtil.showMsg("Kết quả xóa file: "+rt);
			}
			lietKeAnh();
		}
		else{
			DlgUtil.showMsg("Xóa ảnh "+idfile+" thất bại ",undefined,3000);
		}
	}
	//END L2PT-4898
	
	function validForm(){	
		//L1PT-1562
		if(isNhapChandoanchinh){
			if($("#txtCHANDOANVAOKHOA").val()== ''){
				DlgUtil.showMsg("Bạn chưa nhập Chẩn đoán chính.");
				$("#txtCHANDOANVAOKHOA").focus();
				return false;
			}
		}
		if(!istextPttt){
			if(isNhapChandoanchinh){//L1PT-1562
				if($("#cboPTTT_HangID").val()=='-1'){
					DlgUtil.showMsg("Bạn chưa chọn loại PTTT");
					$("#cboPTTT_HangID").focus();
					return false;
				}
			}
		} else {
			if(isNhapChandoanchinh){//L1PT-1562
				if($("#txtPTTT_Hang").val().trim()==''){
					DlgUtil.showMsg("Bạn chưa nhập loại PTTT");
					$("#txtPTTT_Hang").focus();
					return false;
				}
			}
		}
		
		var txtTuNgay = $("#txtNGAYPHAUTHUATTHUTHUAT").val();
		//L2PT-27254
		if(kt_pttt_dklci){
			var txtDenNgay = $("#txtKETTHUCPTTT_DKLCI").val();
		}else{
			var txtDenNgay = $("#txtKETTHUCPTTT").val();
		}
		
		if(!compareDate(txtTuNgay,txtDenNgay,"DD/MM/YYYY HH:mm:ss")){
			$("#txtKETTHUCPTTT").focus();
			DlgUtil.showMsg("PTTT lúc không được lớn hơn Giờ phẫu thuật xong.");	
			return false;
	    }
			
		
		if(!compareDate(opt.thoigianvaovien,$("#txtNGAYPHAUTHUATTHUTHUAT").val().trim(),'DD/MM/YYYY HH:mm:ss')){
		    DlgUtil.showMsg("Thời gian phẫu thuật phải lớn hơn hoặc bằng thời gian vào viện!");								
			$('#txtNGAYPHAUTHUATTHUTHUAT').focus();
			return false;	
		}	
		
		if($('#txtSONGUOI').val() != ''){
			if (!/^[1-9][0-9]*$/i.test($('#txtSONGUOI').val())) {
				DlgUtil.showMsg('Trường số lượng người không hợp lệ',
						function() {
							$('#txtSONGUOI').focus();
						});

				return false;
			}
		}
		
		if($('#cboKHOACHIDINH').val() == null || $('#cboKHOACHIDINH').val() == '' || $('#cboKHOACHIDINH').val() == '-1'){
			DlgUtil.showMsg("Vui lòng chọn khoa chỉ định");
			return false;
		}
		//START L2PT-19388
		if(check_songuoi_thuchien){
			var sl_giupviec_tmp = 0, sl_nhanlucchinh_tmp = 0, sl_nhanlucphu_tmp = 0;
			if( $('#cboPHAUTHUATVIEN').val() != '-1' ) sl_nhanlucchinh_tmp = sl_nhanlucchinh_tmp + 1;
			if( $('#cboBACSIGAYME').val() != '-1' ) sl_nhanlucchinh_tmp = sl_nhanlucchinh_tmp + 1;
			
			if( $('#cboPHUMO1').val() != '-1' ) sl_nhanlucphu_tmp = sl_nhanlucphu_tmp + 1;
			if( $('#cboPHUMO2').val() != '-1' ) sl_nhanlucphu_tmp = sl_nhanlucphu_tmp + 1;
			if( $('#cboPHUME').val() != '-1' ) sl_nhanlucphu_tmp = sl_nhanlucphu_tmp + 1;
			
			if( $('#cboDUNGCUVIEN').val() != '-1' ) sl_giupviec_tmp = sl_giupviec_tmp + 1;
			if( $('#cboDIEUDUONG').val() != '-1' ) sl_giupviec_tmp = sl_giupviec_tmp + 1;
			
			if(sl_nhanlucchinh != null && sl_nhanlucchinh_tmp > sl_nhanlucchinh ){
				DlgUtil.showMsg("Đã nhập số lượng nhân lực chính lớn hơn quy định");
				return false;
			}
			
			if(sl_nhanlucphu != null && sl_nhanlucphu_tmp > sl_nhanlucphu ){
				DlgUtil.showMsg("Đã nhập số lượng nhân lực phụ lớn hơn quy định");
				return false;
			}
			
			if(sl_giupviec != null && sl_giupviec_tmp > sl_giupviec ){
				DlgUtil.showMsg("Đã nhập số lượng giúp việc lớn hơn quy định");
				return false;
			}
			
		}
		//L2PT-32060
		if(check_trungvitri_pttt){
			  var arrVitri = [];
			  if($("#cboPHAUTHUATVIEN").val() != null && $("#cboPHAUTHUATVIEN").val() != '-1') arrVitri.push($("#cboPHAUTHUATVIEN").val());
			  if($("#cboPHUMO1").val()!= null && $("#cboPHUMO1").val() != '-1') arrVitri.push($("#cboPHUMO1").val());
			  if($("#cboPHUMO2").val() != null && $("#cboPHUMO2").val() != '-1') arrVitri.push($("#cboPHUMO2").val());
			  if($("#cboPHUMO3").val() != null && $("#cboPHUMO3").val() != '-1') arrVitri.push($("#cboPHUMO3").val());
			  if($("#cboDUNGCUVIEN").val() != null && $("#cboDUNGCUVIEN").val() != '-1') arrVitri.push($("#cboDUNGCUVIEN").val());
			  if($("#cboVC_CHINH").val() != null && $("#cboVC_CHINH").val() != '-1') arrVitri.push($("#cboVC_CHINH").val());
			  if($("#cboVC_PHU").val() != null && $("#cboVC_PHU").val() != '-1') arrVitri.push($("#cboVC_PHU").val());
			  if($("#cboXEP_LICH").val() != null && $("#cboXEP_LICH").val() != '-1') arrVitri.push($("#cboXEP_LICH").val());
			  if($("#cboHUU_TRUNG").val() != null && $("#cboHUU_TRUNG").val() != '-1') arrVitri.push($("#cboHUU_TRUNG").val());
			  if($("#cboVO_TRUNG").val() != null && $("#cboVO_TRUNG").val() != '-1') arrVitri.push($("#cboVO_TRUNG").val());
			  if($("#cboDIEUDUONG").val() != null && $("#cboDIEUDUONG").val() != '-1') arrVitri.push($("#cboDIEUDUONG").val());
			  
			  var recipientsArray = arrVitri.sort(); 

			  var reportRecipientsDuplicate = [];
			  for (var i = 0; i < recipientsArray.length - 1; i++) {
			      if (recipientsArray[i + 1] == recipientsArray[i]) {
			          reportRecipientsDuplicate.push(recipientsArray[i]);
			      }
			  }
			  if(reportRecipientsDuplicate.length > 0){
				  DlgUtil.showMsg("Đã có vị trí bị trùng trong ekip mổ!",undefined,3000);
				  return false;
			  }
		  }
		//BVTM-949
		if(opt.hospital_id == '10284'){
			if($('#cboPHAUTHUATVIEN').val() == '-1' 
				//|| $('#cboVC_CHINH').val() == '-1'  || $('#cboVC_PHU').val() == '-1' || $('#cboPTTT_PHUONGPHAPVOCAMID').val() == '-1' || $('#cboVO_TRUNG').val() == '-1'
					){
					DlgUtil.showMsg("Nhập tất cả các trường bắt buộc có dấu (*)",undefined,3000);
				  return false;
			}
		}
		
		//BVTM-2458
		if(opt.loaiptttid){
    		if('1,2,3,4'.indexOf($('#cboPTTT_HangID').val()) != -1 && $('#cboLOAIPTTTID').val() != '0'){
    			DlgUtil.showMsg("Chọn sai các loại PTTT khi dịch vụ là thủ thuật",undefined,3000);
    			return false;
    		}
		}
		//BVTM-2625
		if(opt.hospital_id == '10284'){
			//validate 'DD/MM/YYY HH:MM:SS'
			var regEx = /([0-3][0-9])\/([0-1][0-9])\/([0-2][0-9]{3}) ([0-5][0-9]):([0-5][0-9]):([0-5][0-9])(([\-\+]([0-1][0-9])\:00))?/;
			if ($("#txtNGAYPHAUTHUATTHUTHUAT").val().match(regEx) == null) {
				DlgUtil.showMsg("Sai định dạng giờ PTTT lúc",undefined,3000);
				return false;
			}
				
			if ($("#txtKETTHUCPTTT_DKLCI").val().match(regEx) == null) {
				DlgUtil.showMsg("Sai định dạng giờ Kết thúc PTTT",undefined,3000);
				return false;
			}
		}
		
		return true;
	}
	// luu thong tin duyet mo
	function saveOrUpdate(){
		$("#btnLuu").bindOnce("click", function() {		
			var valid = that.validator.validateForm();	
			if(valid){
				//validate rieng tung input
				var _check=validForm();
				if(!_check) return false;
				//START L2PT-17277
				if($('#chkHENTRASAU').is(":checked") && $('#txtTG_HENTRASAU').val() == ''){
				    return DlgUtil.showMsg("Chưa đặt thời gian hẹn trả sau!",undefined,3000);
				}
				  
				//START L2PT-19030
				if(opt.hospital_id == '957'  && opt.callfrom != null && opt.callfrom == '1'){
					  if($('#txtPHUONGPHAPPTTT').val() == ''){
						  return DlgUtil.showMsg("Chưa nhập Phương pháp PTTT!",undefined,3000);
					  }
					  if($('#txtCHANDOANTRUOCPHAUTHUAT').val() == ''){
						  return DlgUtil.showMsg("Chưa nhập Chẩn đoán trước PTTT!",undefined,3000);
					  }
				}
				var _result = save();	
				 
				if(_result=='1'){	
					//L2PT-34023
					if(opt.hospital_id == '10284'){
						$('#btnThemDV').show();
					}
					opt.loaiptttid = $('#cboLOAIPTTTID').val();
					DlgUtil.showMsg("Cập nhật thông tin phẫu thuật thủ thuật thành công!",undefined,3000);
					initControl();
					//L2PT-12601
					var ch_taophieuvattu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NTU_NOITRU_MOKETHUOC');
					if(opt.loaitiepnhan != null && opt.loaitiepnhan == '1' && ch_taophieuvattu == '1'){
						var _msg="Tạo phiếu vật tư đi kèm hao phí";
			     		paramInput={					
			  					khambenhid : opt.khambenhid,
			  					maubenhphamid : "",
			  					loaikedon: 1,
			  					dichvuchaid: opt.dichvukhambenhid,
			  					opt : '02D015', // tao phieu thuoc
			  					loadkhotheo : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LOAD_KHO_KETHUOC_PTTT'), 
			  					macdinh_hao_phi : 9
			  					
			  			};	
				  					
		  				dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc02D015","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1080,550);
			  			DlgUtil.open("divDlgTaoPhieuThuoc02D015"); 
			  			EventUtil.setEvent("assignDrug_cancel", function(e) {
							DlgUtil.close("divDlgTaoPhieuThuoc02D015");	
						});
					}
					
				}else if (_result=='2' || _result=='3'){	//L2PT-1434			
					   DlgUtil.showMsg("Thời gian PTTT phải lớn hơn thời gian chỉ định và nhỏ hơn hoặc bằng ngày hiên tại");
					   initControl();
				}//L2PT-26328
				else if (_result=='4'){				
					   DlgUtil.showMsg("Thời gian kết thúc PTTT phải lớn hơn thời gian quy định");
				}//L2PT-34023
				else if (_result=='5'){				
					   DlgUtil.showMsg("Không thể cập nhật, Phòng hậu phẫu đã kết thúc điều trị kết hợp");
				}
				else{
					DlgUtil.showMsg("Cập nhật thông tin phẫu thuật thủ thuật không thành công!");
				}
				EventUtil.raiseEvent("refresh_gridCKDetail",{});
				
			}
	   },500)
	}
	function saveAndClose() {
		$("#btnLuuDong").bindOnce("click", function() {		
			var valid = that.validator.validateForm();	
			if(valid){
				
				//validate rieng tung input
				var _check=validForm();
				if(!_check) return false;
				//START L2PT-17277
				if($('#chkHENTRASAU').is(":checked") && $('#txtTG_HENTRASAU').val() == ''){
				    return DlgUtil.showMsg("Chưa đặt thời gian hẹn trả sau!",undefined,3000);
				}
				  
				//START L2PT-19030
				if(opt.hospital_id == '957'  && opt.callfrom != null && opt.callfrom == '1'){
					  if($('#txtPHUONGPHAPPTTT').val() == ''){
						  return DlgUtil.showMsg("Chưa nhập Phương pháp PTTT!",undefined,3000);
					  }
					  if($('#txtCHANDOANTRUOCPHAUTHUAT').val() == ''){
						  return DlgUtil.showMsg("Chưa nhập Chẩn đoán trước PTTT!",undefined,3000);
					  }
				}
				
				var _result = save();		
				 
				if(_result=='1'){
					//L2PT-12601
					var ch_taophieuvattu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NTU_NOITRU_MOKETHUOC');
		     		paramInput={					
		  					khambenhid : opt.khambenhid,
		  					maubenhphamid : "",
		  					loaikedon: 1,
		  					dichvuchaid: opt.dichvukhambenhid,
		  					opt : '02D015', // tao phieu thuoc
		  					loadkhotheo : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LOAD_KHO_KETHUOC_PTTT'), 
		  					macdinh_hao_phi : 9,
		  					msg: "Cập nhật phẫu thuật thủ thuật thành công"
		  					
		  			};	
					//xu ly su kien callback
					var evFunc=EventUtil.getEvent("assignSevice_savePTTT");			
					if(typeof evFunc==='function') {
						if(opt.loaitiepnhan != null && opt.loaitiepnhan == '1' && ch_taophieuvattu == '1'){
							evFunc({msg:paramInput});		
						}else{
							evFunc({msg:"Cập nhật phẫu thuật thủ thuật thành công"});		
						}
					}
					else {
						console.log('evFunc not a function');
					} 
				}
				else if (_result=='2' || _result=='3'){	//L2PT-1434				
					   DlgUtil.showMsg("Thời gian PTTT phải lớn hơn thời gian chỉ định và nhỏ hơn hoặc bằng ngày hiên tại");
				}
				//L2PT-26328
				else if (_result=='4'){				
					   DlgUtil.showMsg("Thời gian kết thúc PTTT phải lớn hơn thời gian quy định");
				}
				//L2PT-34023
				else if (_result=='5'){				
					   DlgUtil.showMsg("Không thể cập nhật, Phòng hậu phẫu đã kết thúc điều trị kết hợp");
				}
				EventUtil.raiseEvent("refresh_gridCKDetail",{});				
			}
	   },500)
	}	
	//START L2PT-6521
	function save(){
		  
		  var txtTuNgay = $("#txtNGAYPHAUTHUATTHUTHUAT").val();
		  //L2PT-27254
		  if(kt_pttt_dklci){
			var txtDenNgay = $("#txtKETTHUCPTTT_DKLCI").val();
		  }else{
			var txtDenNgay = $("#txtKETTHUCPTTT").val();
		  }
		  	
		  //L1PT-1562
		  var data_arr_Cauhinh = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH","NTU_LDTT_PTTT_BND;NTU_NHAP_CDC");
		  
		  var objData = new Object();
		  var _par;				 
		  FormUtil.setFormToObject("","",objData);			
		  if(data_arr_Cauhinh != null && data_arr_Cauhinh.length > 0){
			  if(data_arr_Cauhinh[0].NTU_LDTT_PTTT_BND == '1'){
				  objData.TRINHTUPTTT = $("#txtTRINHTUPTTT_BND").val();
			  }
		  }

		  //START L2PT-19388
		  objData.SL_NHANLUCCHINH = sl_nhanlucchinh == null ? '' : sl_nhanlucchinh;
		  objData.SL_NHANLUCPHU = sl_nhanlucphu == null ? '' : sl_nhanlucphu;
		  objData.SL_GIUPVIEC = sl_giupviec == null ? '' : sl_giupviec;
		  
		  //L2PT-27254
		  if(kt_pttt_dklci){
			  objData.KETTHUCPTTT = $("#txtKETTHUCPTTT_DKLCI").val();
		  }
		  
		  _par = [JSON.stringify(objData),_phauthuatthuthuatid,opt.khambenhid,opt.benhnhanid,opt.hosobenhanid,
			        opt.khoaid,opt.phongid,opt.maubenhphamid,opt.tiepnhanid,opt.dichvukhambenhid,_checkUpdate];
		  var _result=jsonrpc.AjaxJson.ajaxCALL_SP_I("PTTT.INS",_par.join('$'));
		  return _result;
	}
	
	//START L2PT-6521
	function saveAndPrint(){
		$("#btnLuuIn_BND").on("click", function(e) {
			var valid = that.validator.validateForm();
			if(valid){
				//validate rieng tung input
				var _check=validForm();
				if(!_check) return false;
				//START L2PT-17277
				if($('#chkHENTRASAU').is(":checked") && $('#txtTG_HENTRASAU').val() == ''){
				    return DlgUtil.showMsg("Chưa đặt thời gian hẹn trả sau!",undefined,3000);
				}
				  
				//START L2PT-19030
				if(opt.hospital_id == '957'  && opt.callfrom != null && opt.callfrom == '1'){
					  if($('#txtPHUONGPHAPPTTT').val() == ''){
						  return DlgUtil.showMsg("Chưa nhập Phương pháp PTTT!",undefined,3000);
					  }
					  if($('#txtCHANDOANTRUOCPHAUTHUAT').val() == ''){
						  return DlgUtil.showMsg("Chưa nhập Chẩn đoán trước PTTT!",undefined,3000);
					  }
				}
				
				var _result = save();
				if(_result=='1'){				
				   DlgUtil.showMsg("Cập nhật thông tin phẫu thuật thủ thuật thành công!",undefined,3000);
				   initControl();
				   print();
				   //L2PT-12601
				   var ch_taophieuvattu = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NTU_NOITRU_MOKETHUOC');
				   if(opt.loaitiepnhan != null && opt.loaitiepnhan == '1' && ch_taophieuvattu == '1'){
					   var _msg="Tạo phiếu vật tư đi kèm hao phí";  
				       paramInput={					
				  					khambenhid : opt.khambenhid,
				  					maubenhphamid : "",
				  					loaikedon: 1,
				  					dichvuchaid: opt.dichvukhambenhid,
				  					opt : '02D015', // tao phieu thuoc
				  					loadkhotheo : jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LOAD_KHO_KETHUOC_PTTT'), 
				  					macdinh_hao_phi : 9
				  					
				  		};	
			  					
			  			dlgPopup=DlgUtil.buildPopupUrl("divDlgTaoPhieuThuoc02D015","divDlg","manager.jsp?func=../noitru/NTU02D010_CapThuoc",paramInput,_msg,1080,550);
			  			DlgUtil.open("divDlgTaoPhieuThuoc02D015");
			  			
			  			EventUtil.setEvent("assignDrug_cancel", function(e) {
							DlgUtil.close("divDlgTaoPhieuThuoc02D015");	
						});
				   }
				   

				}else if (_result=='2' || _result=='3'){		//L2PT-1434			
					   DlgUtil.showMsg("Thời gian PTTT phải lớn hơn thời gian chỉ định và nhỏ hơn hoặc bằng ngày hiên tại");
					   initControl();
				}
				else{
					DlgUtil.showMsg("Cập nhật thông tin phẫu thuật thủ thuật không thành công!");
				}
				
			}
		})
	}
	//END L2PT-6521
	// ham xu ly close trang
	function closePage() {
		$("#btnHuy").on("click", function(e) {
			parent.DlgUtil.close("dlgPTTT");
		})
	}
	
	//START -- HISL2TK-691 --hongdq
	function loadCboMau(){
		var sql_par = []

		sql_par.push({"name":"[0]","value": 1},{"name":"[1]","value":opt.user_id},{"name":"[2]","value":opt.khoaid});
		ComboUtil.getComboTag("cboMAULUOCDOPTTT","NTU02D037.DSMAU", sql_par,"",{extval: true,value:'',text:'--Chọn mẫu--'},"sql");
		
		var sql_par = []
		sql_par.push({"name":"[0]","value": 2},{"name":"[1]","value":opt.user_id},{"name":"[2]","value":opt.khoaid});
		ComboUtil.getComboTag("cboMAUTRINHTUPTTT","NTU02D037.DSMAU", sql_par,"",{extval: true,value:'',text:'--Chọn mẫu--'},"sql");

	}
	function setEnabled(_ena, _dis) {
		for (var i =0; i<_ena.length; i++) {
			$("#"+_ena[i]).attr('disabled', false);
		}
		for (var i =0; i<_dis.length; i++) {
			$("#"+_dis[i]).attr('disabled', true);
		}
	}
	//END -- HISL2TK-691 --hongdq
	function setValueToCbo(element1,element2, id, name) {
		$("#"+element1+"").val("");
	    var option = $('<option value="'+id+'">'+name+'</option>');
	    $("#"+element2+"").empty();
	    $("#"+element2+"").append(option);
	}
	
	function loadCombomau() {
		//START L2PT-20993
		if(mau_pttt_theophongcd){
			sql_par = [{"name":"[0]", "value":opt.phongid}];
			ComboUtil.getComboTag("cboMAUPTTT","NTU02D037.DSMAU_PCD", sql_par,"",{extval: true,value:'-1',text:'--Chọn mẫu pttt--'},"sql");
		}else{
			sql_par = [];
			ComboUtil.getComboTag("cboMAUPTTT","NTU02D037.DSMAU_PT", sql_par,"",{extval: true,value:'-1',text:'--Chọn mẫu pttt--'},"sql");
		}
	}
	//L2PT-30152
	function kiemtra_trunggio_pttt(user_id,element,type) {
		if(user_id){
      		var objData = new Object();
      		var _par;
      		objData.USERID = user_id;
      		objData.NGAYPHAUTHUATTHUTHUAT = $("#txtNGAYPHAUTHUATTHUTHUAT").val();
      		objData.KETTHUCPTTT = $("#txtKETTHUCPTTT_DKLCI").val();
      		objData.TYPE = type;
      		objData.PHAUTHUATTHUTHUATID = _phauthuatthuthuatid;
      		_par = [JSON.stringify(objData)];
      		var checkTrunggio=jsonrpc.AjaxJson.ajaxCALL_SP_I("D037.CHECK_TIME",_par.join('$'));
      		//var checkTrunggio = jsonrpc.AjaxJson.getOneValue("D037.CHECK_TIME", par);
      		if(checkTrunggio >= 1){
      			DlgUtil.showConfirm("Đã trùng giờ làm PTTT, Bạn có muốn thực hiện tiếp không?",function(flag) {
    				if(flag){
    					return true;
    				}else{
    					$("#"+element+"").empty();
    	      			//DlgUtil.showMsg("Đã trùng giờ làm PTTT",undefined,3000);	
    	      			$("#"+element+"").focus();
    	       			return false;
    				}
    			});
      		}else{
      			return true;
      		}
		}
	}
	//L2PT-34023
	function boSungDichVu(){

			var paramInput = { 
				benhnhanid : opt.benhnhanid, 
				mabenhnhan : opt.mabenhnhan, 
				khambenhid : opt.khambenhid, 
				tiepnhanid : opt.tiepnhanid, 
				hosobenhanid : opt.hosobenhanid, 
				doituongbenhnhanid : opt.doituongbenhnhanid, 
				loaitiepnhanid : opt.loaitiepnhan, 
				subDeptId : opt.phongid, 
				deptId : opt.dept_id, 
				mbpid_bosungdv : opt.maubenhphamid,
				loaiPhieu: 5
			};
			
			dlgPopup=DlgUtil.buildPopupUrl("divDlgDichVu","divDlg","manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 5,paramInput,"Tạo phiếu chỉ định dịch vụ",1300,600);
			DlgUtil.open("divDlgDichVu");

	}

	 // Xử lý sự kiện liên quan ký CA => START
	 function saveAndCa(){
		 $("#btnKyCa").on("click", function(e) {
			 _kyCaRpt('1');
		 })
	 };

	 function huyCa(){
		 $("#btnHuyCa").on("click", function(e) {
			 _kyCaRpt('2');
		 })
	 };

	 function exportKyCA(){
		 $("#btnInCa").on("click", function(e) {
			 _caRpt('0');
		 })
	 };

	 function _kyCaRpt(_signType) {
		 EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
			 DlgUtil.close("divCALOGIN");
			 _caRpt(_signType, e.username, e.password);
		 });
		 EventUtil.setEvent("dlgCaLogin_close", function (e) {
			 DlgUtil.close("divCALOGIN");
		 });
		 var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
		 var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", "CA - LOGIN", 505, 268);
		 popup.open("divCALOGIN");
	 };

	 function _caRpt(_signType, _causer, _capassword){
		 var _reportCode = '';
		 if (opt.hospital_id == '965') {
			 var par = [{
				 name: 'hosobenhanid',
				 type: 'String',
				 value: opt.hosobenhanid
			 }, {
				 name: 'ptttid',
				 type: 'String',
				 value: _phauthuatthuthuatid
			 }];
		 } else {
			 var par = [{
				 name: 'hosobenhanid',
				 type: 'String',
				 value: opt.hosobenhanid
			 }, {
				 name: 'dichvukhambenhid',
				 type: 'String',
				 value: opt.dichvukhambenhid
			 }, {
				 name: 'benhnhanid',
				 type: 'String',
				 value: opt.benhnhanid
			 }];
			 //L2PT-22996
			 if (opt.hospital_id == '913' && (madichvu_caychi == '08.0007.0227' || mdv_in_caychi_ydgli.indexOf(madichvu_caychi) != -1)) {
				 _reportCode = 'PHIEU_KETQUA_CAYCHI_913';
			 } else {
				 var param = [opt.dichvukhambenhid];
				 var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("PTTT.GETIMG", param.join('$'));
				 if (data_ar != null) {
					 for (var i = 0; i < data_ar.length; i++) {
						 var hinh_anh1 = data_ar[i]["DUONGDANFILE"];
						 if (hinh_anh1 != null && hinh_anh1 != "") par.push({
							 name: 'hinh_anh' + i,
							 type: 'Image',
							 value: hinh_anh1
						 });
					 }
				 }
				 _reportCode = 'NTU030_PHIEUPHAUTHUATTHUTHUAT_14BV01_QD4069_A4';
			 }

		 }
		 par.push({
			 name: 'RPT_CODE',
			 type: 'String',
			 value: _reportCode
		 });
		 if(_signType != '0') {
			 var oData = {
				 sign_type: _signType,
				 causer: _causer,
				 capassword: _capassword,
				 params: par
			 };
			 //var msg = CommonUtil.caRpt(oData, _reportCode, true);
			 //DlgUtil.showMsg(msg);
			 var msg = CommonUtil.kyCA(par, _signType, true, true);
			 EventUtil.setEvent("eventKyCA",function(e)
			 { DlgUtil.showMsg(e.res); }
			 );
		 } else {
			 CommonUtil.openReportGetCA2(par, false);
		 }
	 }
	 // Xử lý sự kiện liên quan ký CA => END
	
}
	
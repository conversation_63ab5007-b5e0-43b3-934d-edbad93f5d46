<!-- 
  	 <PERSON><PERSON> màn hình  : NTU02D086
	 File mã nguồn : NTU02D086_TonghopSuatan.html
	 M<PERSON><PERSON> đích  : <PERSON><PERSON><PERSON> diện màn hình
	 	+ <PERSON><PERSON><PERSON> phiếu tổng hợp xuất ăn
	 Tham số vào : 
		
	 <PERSON><PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nhật  	<PERSON>hi chú
	 hongdq				 12/04/2018		    Tạo mới
 -->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/RestService.js" ></script>
<script type="text/javascript" src="../common/script/dbForm.js" ></script>
<script type="text/javascript" src="../common/script/dbToolbar.js" ></script>
<script type="text/javascript" src="../common/script/DataValidator.js" ></script>
<script type="text/javascript" src="../common/script/CommonUtil.js" ></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js" ></script>
<script type="text/javascript" src="../common/script/jsdatetime/Scripts/DateTimePicker.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../ngoaitru/custom_ngt.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">       
<link href="../common/script/jqueryui/jquery-ui-redmond.1.9.1.css" rel="stylesheet"/>     
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>   
 
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet"/>           
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>  
<script src="../common/script/jquery/jquery.storageapi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js" ></script>
<script type="text/javascript" src="../common/script/moment.min.js" ></script>
<script src="../plugin/jquery.textboxLookup.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi.js" ></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js" ></script>
<script type="text/javascript" src="../noitru/NTU02D091_GuiDuyetLanhDao.js?v=220713"></script>
<script type="text/javascript" src="../noitru/cominf.js" ></script>
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js" ></script>
<style>
#lean_overlay {
    position: fixed;
    z-index:100;
    top: 0px;
    left: 0px;
    height:100%;
    width:100%;
    background: #000;
    display: none;
}
</style>
	<input type="hidden" id="hidHUONGTHAN" value="" />
	<input type="hidden" id="hidREPORTNAME" value="" />
	<div class="container" id="divMain">
		<ul class="nav nav-tabs mgt5">
		   	<li role="presentation" class="active" ><a href="#divDON_THUOC" id="hrDonThuoc" aria-controls="home" role="tab" data-toggle="pill">Dược lâm sàng</a></li>
		   	<li role="presentation" ><a href="#divDSPHIEU" id="hrDsPhieu" aria-controls="home" role="tab" data-toggle="pill">Danh sách phiếu</a></li>
		</ul>
		<div class="tab-content">
			<div class="tab active" id="divDON_THUOC">
				<div class="row mgt5">
					<div class="col-md-12 low-padding mgt-5">
						<div class="col-md-12">
							<div class="col-md-12 low-padding border-group-1" style="background-color: white!important;">
								<div class="col-md-12 mgt3">
									<div class="col-md-1 low-padding">
										<label>Từ ngày</label>
									</div>
									<div class="col-md-2 low-padding">
										<div class="input-group" style="width: 100%;">	
										  <input class="form-control input-sm" id="txtTU_NGAY" name="txtTU_NGAY" valrule="TG tạo phiếu,required" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy hh:mm:ss" readonly="readonly">
										  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtTU_NGAY','ddMMyyyy','dropdown',false,'24',true)"></span>
										</div>
									</div>
									<div class="col-md-1 ">
										<label class="">Đến ngày</label>
									</div>
									<div class="col-md-2 low-padding">
										<div class="input-group" style="width: 100%;">	
										  <input class="form-control input-sm" id="txtDEN_NGAY" name="txtDEN_NGAY" valrule="TG tạo phiếu,required" title="" data-mask="00/00/0000" placeholder="dd/MM/yyyy hh:mm:ss" readonly="readonly">
										  <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal"  onclick="NewCssCal('txtDEN_NGAY','ddMMyyyy','dropdown',false,'24',true)"></span>							 
										</div>
									</div>
									<div class="col-md-1 ">
										<label class="">Kho</label>
									</div>
									<div class="col-md-2 low-padding">
										<select class="form-control input-sm" id="cboMA_KHO"></select>
									</div>
									<div class="col-md-1 ">
										<label class="">Trạng thái</label>
									</div>
									<div class="col-md-2 low-padding">
										<select class="form-control input-sm" id="cboTRANGTHAIID">
											<option value="3">Chờ LĐ duyệt</option>
										</select>
									</div>
								</div>
								<div class="col-md-12  mgt3 mgb5" style="text-align: center;">
									<button id="btnSearch" class="btn btn-primary">
									<span class="glyphicon glyphicon-search"></span> Tìm kiếm</button>
									<button id="btnSendRequest" class="btn btn-primary">
									<span class="glyphicon glyphicon-pencil"></span> Gửi Duyệt</button>
								</div>
							</div>
							<div class="col-md-12 low-padding" id="divTHUOCDUYET">
								<table id="grdDS_DONTHUOC_VT" class="table table-striped jambo_table bulk_action"></table>
								<div id="pager_grdDS_DONTHUOC_VT"></div>
							</div>
							<div class="col-md-12 low-padding" id="divTHUOCTUCHOI" style="display:none">
								<table id="grdDS_DONTHUOC_VTTC" class="table table-striped jambo_table bulk_action"></table>
								<div id="pager_grdDS_DONTHUOC_VTTC"></div>
							</div>
							
						</div>
					</div>
				</div>
			</div>
			<div class="tab" id="divDSPHIEU">
				<div class="col-md-12 low-padding">
						<div class="col-md-6 low-padding mgt-10">
							<table id="grdDS_PHIEU">
							</table>
							<div id="pager_grdDS_PHIEU"></div>
						</div>
						<div class="col-md-2 low-padding" style="text-align: center;margin-top:30px;">
							<button id="btnDelRequest" class="btn btn-primary">
									<span class="glyphicon glyphicon-pencil"></span> Gỡ Duyệt</button>
						</div>
						<div class="col-md-4 low-padding mgt-10">
<!-- 							<div class="col-md-12 low-padding"> -->
<!-- 								<div class="col-md-5 low-padding mgt-10"> -->
<!-- 									<label>Người tạo</label> -->
<!-- 								</div> -->
<!-- 								<div class="col-md-7 low-padding mgt-10"> -->
<!-- 									<label id="lblNGUOITAO"></label> -->
<!-- 								</div> -->
<!-- 							</div> -->
<!-- 							<div class="col-md-12 low-padding"> -->
<!-- 								<div class="col-md-5 low-padding mgt-10"> -->
<!-- 									<label>Ngày tạo</label> -->
<!-- 								</div> -->
<!-- 								<div class="col-md-7 low-padding mgt-10"> -->
<!-- 									<label id="lblNGAYTAO"></label> -->
<!-- 								</div> -->
<!-- 							</div> -->
							<div class="col-md-12 low-padding">
								<table id="grdDS_MAUBENHPHAM">
								</table>
								<div id="pager_grdDS_MAUBENHPHAM"></div>
							</div>
						</div>
				</div>
			</div>
		</div>
		
	</div>

<script>

	var hospital_id = '{hospital_id}';
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var khoaid = '{dept_id}';
	var uuid = '{uuid}';
	var url ='{url}';
	var _optFunc = '{optFunc}';
	var phongid = '{subdept_id}';

	var session_par=[];
	initRest(uuid);	
	var _opts={
			lang: lang,
			_param:session_par,
			_uuid:uuid,
			user_id:user_id,
			_hospital_id: hospital_id,
			khoaid : khoaid,
			optFunc: _optFunc,
			phongid: phongid
	}
		
    var data;	
    var mode = '{showMode}';    	
	if(mode=='dlg') {		
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		data=EventUtil.getVar("dlgVar");
		_opts.mode = typeof data.mode =='undefined' ? '0' : data.mode;
	}
	
	var dvDk = new NTU02D091(_opts);
	dvDk.load();	
</script>
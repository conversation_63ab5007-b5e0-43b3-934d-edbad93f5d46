

/*
 Mã màn hình  : NTU02D010
 File mã nguồn : NTU02D010_CapThuoc.js
 <PERSON><PERSON><PERSON> đích  : <PERSON><PERSON><PERSON> diện màn hình
 	+ Chỉ định thuốc
 	+ Chỉ định vật tư
 	+ <PERSON><PERSON><PERSON>
 	+ <PERSON>r<PERSON> vật tư
 	....(các chức năng liên quan chỉ định thuốc, vật tư)
 Tham số vào : 
 	khambenhid 		: M<PERSON> khám bệnh ID 
	maubenhphamid 	: Mẫu bệnh phẩm ID, để load thông tin về phiếu đã được chỉ định
	opt 			: Tham số định nghĩa loại màn hình tương ứng
		+ 	opt 	= '02D010' -> M<PERSON>n hình chỉ định thuốc
		+ 	opt 	= '02D014' -> M<PERSON>n hình phiếu trả thuốc
		+ 	opt 	= '02D015' -> <PERSON><PERSON><PERSON> hình phiếu vật tư
		+ 	opt 	= '02D016' -> <PERSON><PERSON><PERSON> hình phiếu trả vật tư
	loaikedon 		: <PERSON><PERSON><PERSON> số định nghĩa loại kê đơn thuốc
		+	loaikedon = 1 -> <PERSON><PERSON> số lượng thuốc tổng hợp
		+	loaikedon = 0 -> Kê đơn chi tiết (sáng, trưa, chiều, tối)
 
 Người lập trình	 Ngày cập nhật  	Ghi chú
 linhvv				 5/9/2016			Sửa bổ xung tính năng
*/
function TraThuoc(_opts) {
	var gridInfo;
	var _gridCaption			= ""; 
	var _company_id 			= _opts.company_id;
	var _khambenhid 			= _opts.khambenh_id;
	var _user_id 				= _opts.user_id;
	var _dept_id 				= _opts.dept_id;
	var _loaikedon 				= _opts.loaikedon;
	var _khoaId 				= _opts.khoaId;
	var _phongId 				= _opts.phongId;
	var _dichvucha_id 			= _opts.dichvuchaid;
	var _maubenhpham_id 		= _opts.maubenhpham_id;
	var _phieudieutri_id		= _opts.phieudieutriid;
	var _macdinh_hao_phi		= _opts.macdinh_hao_phi;
	var _kieutra				= _opts.kieutra;
	var _loadkhotheo			= _opts.loadkhotheo;	
	var _tiepnhanid				= "";
	var _srch_hoatchat			= 0;
	var _maubenhpham_temp_id 	= "";
	var _option 				= _opts.option;
	var i_action 				= "";
	var _first_load 			= 0;
	var _loainhommaubenhpham_id = "";
	var _tyle_bhyt 				= "";
	var _ma_bhyt 				= "";
	var _doituongbenhnhanid 	= "";
	var _ten_doituong_benhnhan 	= "";
	var _icd10_code				= "";
	var _icd10_text				= "";
	var _objDrugTemp 			= [];
	var _objDrug 				= [];
	var oldValue 				= "";
	var _benhnhanid 			= "";
	var _list_thuoc_dake 		= "";
	var _tuongtacthuoc			= "";
	var _loaitiepnhanid			= "";
	var _hinhthuc_kho			= "";
	var _loai_tvt_kho			= "";
	var _grdRowSelect			= "";
	var _nhom_mabhyt_id			= "";
	var _muc_tran_bhyt			= "";
	var _lbl_kho				= "";
	var _lbl_text				= "";
	var _ten_donvitinh			= "";
	var _khoanmucid				= "";
	var _thoigian_vaovien		= "";
	var _loai_doituong_moi		= "";
	var _TRAN_BHYT 				= "";
	var _TYLE_BHYT_TT 			= "";
	var _TONGTIENDV_BH 			= "";
	var _tradu6thangluongcoban	= "";
	var _duoc_van_chuyen		= "";
	var _ngay_bhyt_kt			= "";
	var _GLB_CACH_TINH_TIEN		= 1;//0: Tinh binh thuong; 1: Tinh duoi gia tran
	var _tong_tien_dv			= "";
	var _tyle_bhyt_tt_tvt		= "";
	var _indonthuoc_noitru		= "";
	var _songaykemax     		= "";
	var _soluongthuocmax   		= "";
	var _phieutraid   			= "";
	var _dichvukbcha_id 		= "";
	var _ngaytiepnhan			= "";
	var _indonthuoc_huongthan	= "";
	var _makhomacdinh			= "";
	var _tyle_miengiam			= 0;
	var _thoihanthe				= 0;
	var _chanhoatchat			= 0;
	var _kechungthuocvt			= 0;
	var _objTmpThuoc 			= [];
	var _ngayhanthe				= 0;
	var _luu     				= 0;
	var _cachdung_sangchieu		= 0;
	var _ngaykethuocmax			= 0;
	var _badaingay				= 0;
	var _kieucheck				= 0;
	var _checkdiung				= 1;
	var _botimcachdung			= 1;
	var _canhbaophacdo 			= 0;
	var _sudungphacdo			= 0;
	var _checktrunghoatchat	    = 0;
	var _bacsi_ke				= 0;
	var _format_cd				= 0;
	var _dsthuocvattuid 		= "";
	var _ke_tunhieu_kho 		= 0;
	var _sudung_lieudung 		= 0;
	var _kieucanhbaotientamung  = 0;
	var _hopdongid				= 0;
	var _sudung_dvqd_kethuoc	= 0;
	var _ds_id_loaitvt			= "";
	var _phieudtri_kedon		= "0";
	var _loaikedonthuoc			= "";
	var _phieudtri_trathuoc		= "";
	var _lbllieudung			= "";
	var _checkngaykedonntu		= "1";
	var _ngt_lamtron_kedon		= 1;
	var _an_menu_phai_kedon		= 0;
	var _chonkho_kedon			= 0;
	var _ds_khoid				= "0";
	var _sudungthuoc12sao		= '0';
	var _timkiem_bn_kedon_ntu 	= '0';
	var TRATHUOC_KHONGLAY_SOLUONG = '0';//L2PT-27468
	
	var _jsonThuoc24h;
	var _jsonDonThuoc 			= 
								{
									DS_THUOC:[],
									BENHNHANID:"",
									MA_CHANDOAN:"",
									CHANDOAN:"",
									CHANDOAN_KT : "",
									DUONG_DUNG:"",
									NGUOIDUNG_ID:"",
									CSYT_ID:"",
									KHO_THUOCID:"",
									INS_TYPE:"",
									I_ACTION:"",
									MAUBENHPHAMID:"",
									PHIEUDIEUTRI_ID:"",
									NGAYMAUBENHPHAM:"",
									NGAYMAUBENHPHAM_SUDUNG:"",
									DICHVUCHA_ID:"",
									DOITUONG_BN_ID:"",
									TYLE_BHYT:"",
									NGAY_BHYT_KT:"",
									HINH_THUC_KE:"",
									SONGAY_KE : "",
									MAUBENHPHAMCHA_ID:"",
									KIEUKEDON : ""
								};
	var _url;
	var _kho_id;
	var _loaithuocid;
	
	var _numberFormat = {
		    prefix: '',		    
		    thousandsSeparator: '.',
		    centsLimit: 0
		};
	var _enter = $.Event('keypress');
	_enter.which = 13;
	 var _loai_khothuoc = "";
	 var _loai_don = "";
	var _param=[_company_id];
	var sql_par=[];
	var _tableName="";
	var _keyField="";
	var _gridComboDonThuoc_TRA = "NTU02D010.01";
	var _gridComboMuaNgoai = "NTU02D010.02";
	_gridComboMuaNgoai_HoatChat ="NTU02D010.16";
	var _gridComboKhoThuoc = "NTU02D010.03";
	var _gridComboKhoThuoc_HoatChat = "NTU02D010.15";
	var _gridDonThuoc = "grdDONTHUOC";
	var	_gridDonThuocHeader = "Tên thuốc,TEN_THUOC,100,0,f,l;Hoạt chất,HOATCHAT,60,0,f,l;Đơn vị,TEN_DVT,60,0,f,l,0;SL khả dụng,SLKHADUNG,50,0,f,l,0;Mã thuốc,MA_THUOC,50,0,f,c,0;Giá DV,GIA_BAN,50,0,f,c,0;Biệt dược,BIETDUOC,50,0,f,c,0";
	
	var _gridDonThuocSQL = "NTU02D010.04";
	var _gridDonThuocTemp = "NTU02D010.05";
	var r_dichvu_id_diff = "-1";
	var r_dichvu_id_dtc_diff = "-1";
	var r_checkicd = "0";
	var r_loaicheck = "0";
	var _tudongindt = "0";
	var _kieucheck_hoatchat = "0";
	var _tongtien = 0;
	var _tientu = 0;
	var _danop = 0;
	
	var valid_ar =[{"name" : "txtTENTHUOC","display" : "Tên thuốc","rules" : "trim_required"},					
					{"name" : "txtSOLUONG_TONG","display" : "Số lượng","rules" : "trim_required|min_length[15]"},
					{"name" : "cboDUONG_DUNG","display" : "Giá trị thẻ từ","rules" : "trim_required"}];
	var _col="Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
	var _colloidanbs="Lời dặn bác sỹ,LOIDANBS,100,0,f,l";
	var that=this;
	this.load=doLoad;

	function doLoad() {
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof this.lang !== "undefined") ? this.lang : "vn";
		console.log("-------------_macdinh_hao_phi: "+_macdinh_hao_phi);
		
		$("#txtTHOI_GIAN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		$("#txtTG_DUNG").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		$("#btnAllPhieu").addClass("disabled");
		
		var tkcbo = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','TK_CBO_PHIEUTRATVT');
		if(tkcbo == '1'){
			$("#cboDONTHUOCVT").select2();
		}
		
		_list_thuoc_dake = "";
		if(_option == '02D010'){//Phieu thuoc 
			r_checkicd = "1";
			_loai_khothuoc 			= "2,3,8,10";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			//_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#dvLOAI_THUOC").show();
			$("#divSLTHANG").hide();		
		}else if(_option == '02D010_1'){
			r_checkicd = "1";
			_loai_khothuoc 			= "2,3,8,10";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			//_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#dvLOAI_THUOC").show();
			$("#divSLTHANG").hide();	
		}else if(_option == '02D011'){//Don thuoc mua ngoai 
			_loai_khothuoc 			= "4";
			_loai_don 				= "1";
			_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_loainhommaubenhpham_id = "7";
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			$("#dvHeaderName").text("Đơn thuốc mua ngoài");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#grMenuRight").css("display", 'none');
			$("#divDonThuocVT").hide();
			$("#divPhieuDT").show();
			$("#btnTraAllPhieu").hide();
			$("#divSLTHANG").hide();
			
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#cboDVQUYDOI").hide();
		}
		else if(_option == '02D014'){//Phieu tra thuoc
			_loai_khothuoc 			= "2,3,8,10";
			_loai_don 				= "2";
			_loainhommaubenhpham_id = "7";
			_srch_hoatchat			= 1;
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			//Truong hop phieu tra chi hien thi la ke tong
			//_loaikedon = 1;
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu trả thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu trả thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			$("#grMenuRight").css("display", 'none');
			
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			
			$("#btnDTMau").hide();
			$("#btnDTCu").hide();
			$("#btnTConSD").hide();	
			//tuyennx_add_start_20170816 yc L2DKBD-195
			$('#btnTDiUng').show();
			//tuyennx_add_end_20170816 yc L2DKBD-195
			$("#txtTEXT_TEMP").hide();
			$("#btnSaveTemp").hide();
			$("#lblLoaiTra").text("Đơn thuốc trả");			
			$("#divDonThuocVT").show();
			//$("#divPhieuDT").hide(); //L2PT-22531
			$("#btnTraAllPhieu").show();
			$("#dvLOAI_THUOC").hide();
			$("#divSLTHANG").hide();
			$("#btnXuTri").hide();
			
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#cboDVQUYDOI").hide();
		}else if(_option == '02D015'){//Phieu vat tu 
			 r_checkicd = "1";
			_loai_khothuoc 			= "7, 9, 11";
			_loai_don 				= "3";
			_loainhommaubenhpham_id = "8";
			//_gridCaption			= "Danh sách vật tư";
			_gridCaption			= "";
			_lbl_kho				= "Kho vật tư";
			_lbl_text				= " vật tư";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp vật tư đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp vật tư");
			$("#lbSearchName").text("Tên vật tư");
			$("#lbTenThuoc").text("Tên vật tư");
			setTextToButton("btnAdd", "Thêm vật tư", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 1;
			
			$("#btnDTMau").text("Mẫu VT");
			$("#btnDTCu").text("Mẫu VT Cũ");
			
			$("#btnDTMau").show();
			$("#btnDTCu").show();
			$("#btnTConSD").hide();	
			//tuyennx_add_start_20170816 yc L2DKBD-195
			 $('#btnTDiUng').hide();
			 //tuyennx_add_end_20170816 yc L2DKBD-195
			$("#txtTEXT_TEMP").show();
			$("#btnSaveTemp").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();
			$("#lblloaithuoc").text("Loại vật tư");
			$("#dvLOAI_THUOC").show();
			$("#divSLTHANG").hide();
			$("#btnXuTri").hide();
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			//$("#divPhieuDT").hide();
		}else if(_option == '02D016'){//Phieu tra vat tu
			_loai_khothuoc 			= "7, 9, 11";
			_loai_don 				= "4";
			_loainhommaubenhpham_id = "8";
			//_gridCaption			= "Danh sách vật tư";
			_gridCaption			= "";
			//Truong hop phieu tra chi hien thi la ke tong
			_loaikedon 				= 1;
			_lbl_kho				= "Kho vật tư";
			_lbl_text				= " vật tư";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu trả vật tư đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu trả vật tư");
			$("#lbSearchName").text("Tên vật tư");
			$("#lbTenThuoc").text("Tên vật tư");
			setTextToButton("btnAdd", "Thêm vật tư", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 1;
			$("#grMenuRight").css("display", 'none');
			
			$("#btnDTMau").hide();
			$("#btnDTCu").hide();
			$("#btnTConSD").hide();	
			//tuyennx_add_start_20170816 yc L2DKBD-195
			   $('#btnTDiUng').hide();
			   //tuyennx_add_end_20170816 yc L2DKBD-195
			$("#txtTEXT_TEMP").hide();
			$("#btnSaveTemp").hide();
			$("#lblLoaiTra").text("Phiếu VT trả");
			$("#divDonThuocVT").show();
			//$("#divPhieuDT").hide(); //L2PT-22531
			$("#btnTraAllPhieu").show();
			$("#divSLTHANG").hide();
			$("#btnXuTri").hide();
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#cboDVQUYDOI").hide();
		}else if(_option == '02D017'){//Cấp thuốc đông y
			r_checkicd = "1";
			_loai_khothuoc 			= "5";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			_gridCaption			= "";
			_loaikedon 				= 1;
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " vị thuốc YHCT";
			$("#lbSearchName").text("Tên vị thuốc YHCT");
			
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#lblcachdung").text("Ghi chú");
			$("#dvLOAI_THUOC").show();
			
			$("#lbTenThuoc").hide();
			$("#dvTENTHUOC").css('display','none');
			$("#divSLTHANG").show();
			$("#lblDUONGDUNG").css('display','none');
			$("#cboDUONG_DUNG").css('display','none');
			$("#dvTenThuoc").css('display','none');
			$("#dvDUONG_DUNG").css('display','none');			
			$("#txtTG_HENKHAM").val($("#txtSLTHANG").val());
			$("#cboDVQUYDOI").hide();
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
		}else if(_option == '02D018'){//trả thuốc đông y
			_loai_khothuoc 			= "5";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách vật tư";
			_gridCaption			= "";
			//Truong hop phieu tra chi hien thi la ke tong
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " vị thuốc YHCT";
			$("#lbSearchName").text("Tên vị thuốc YHCT");
			setTextToButton("btnAdd", "Thêm thuốc trả", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#btnDTMau").hide();
			$("#btnDTCu").hide();
			$("#btnTConSD").hide();	
			//tuyennx_add_start_20170816 yc L2DKBD-195
			   $('#btnTDiUng').hide();
			   //tuyennx_add_end_20170816 yc L2DKBD-195
			   $('#btnPdDt').hide();
			$("#txtTEXT_TEMP").hide();
			$("#btnSaveTemp").hide();
			$("#lblLoaiTra").text("Đơn thuốc trả");			
			$("#divDonThuocVT").show();
			$("#divPhieuDT").hide();
			$("#btnTraAllPhieu").show();
			$("#dvLOAI_THUOC").hide();
			$("#divSLTHANG").hide();
			
			$("#lbTenThuoc").hide();
			$("#dvTENTHUOC").css('display','none');
			$("#lblDUONGDUNG").css('display','none');
			$("#cboDUONG_DUNG").css('display','none');
			$("#dvTenThuoc").css('display','none');
			$("#dvDUONG_DUNG").css('display','none');
			$("#btnXuTri").hide();
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#cboDVQUYDOI").hide();
		}else if(_option == '02D019'){//phiếu thuốc mua từ nhà thuốc 
			r_checkicd = "1";
			_loai_khothuoc 			= "4";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			//_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").hide();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#dvLOAI_THUOC").hide();
			$("#divSLTHANG").hide();
			
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
			$("#cboDVQUYDOI").hide();
		}else if(_option == '02D020'){//Phieu thuoc kê BN nội trú ra viện 
			r_checkicd = "1";
			_loai_khothuoc 			= "2,3,8,10";
			_loai_don 				= "1";
			_loainhommaubenhpham_id = "7";
			//_gridCaption			= "Danh sách thuốc";
			_gridCaption			= "";
			_loaikedon 				= 0;
			_srch_hoatchat			= 1;
			_lbl_kho				= "Kho thuốc";
			_lbl_text				= " thuốc";
			if(_dichvucha_id != "")
				$("#dvHeaderName").text("Phiếu cấp thuốc đi kèm dịch vụ");
			else
				$("#dvHeaderName").text("Phiếu cấp thuốc");
			$("#lbSearchName").text("Tên thuốc/ tên hoạt chất");
			$("#lbTenThuoc").text("Tên hoạt chất");
			setTextToButton("btnAdd", "Thêm thuốc", "glyphicon glyphicon-pencil");
			setTextToButton("btnSave", "Lưu", "glyphicon glyphicon-floppy-disk");
			_loai_tvt_kho 			= 0;
			$("#divPhieuDT").show();
			$("#divDonThuocVT").hide();
			$("#btnTraAllPhieu").hide();		
			$("#lblloaithuoc").text("Loại thuốc");
			$("#dvLOAI_THUOC").show();
			$("#divSLTHANG").hide();			
		}
		
		TRATHUOC_KHONGLAY_SOLUONG = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TRATHUOC_KHONGLAY_SOLUONG'); //L2PT-27468
		
		//L2PT-1478 
		if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TRATHUOC_HIENTHI_ICD')=='1'){
			$('#divICD').css('display','');
		}
		
		$("#lblKho").text(_lbl_kho);
		doLoadCombo("txtMACHANDOANICD","txtTENCHANDOANICD");		
		ComboUtil.initComboGrid("txtMATENCHANDOANICD_KT","NT.SEARCH.ICD10",[], "600px", _col, function(event, ui) {			
			var str = $("#txtTENCHANDOANICD_KT").val();
				if(str.indexOf(ui.item.ICD10CODE+'-') > -1){
				DlgUtil.showMsg("Bệnh kèm theo đã được nhập");
				    return false;
		    }
		   
		    if(ui.item.ICD10CODE.indexOf($('#txtMACHANDOANICD').val()) > -1){
		    	DlgUtil.showMsg("Bệnh kèm theo trùng bệnh chính");
		    	return false;
		    }
		   //tuyennx_edit_start_20170731 mở comment check trung ma benh phu
		    var _par=[_khambenhid,_phongId,ui.item.ICD10CODE,"1"];
			var resultCheck = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.ICD.TR",_par.join('$'));
			if(resultCheck == '0'){
				DlgUtil.showMsg("Đã tồn tại mã bệnh kèm theo trùng với phòng khám khác");
				return false;
			}
			//tuyennx_edit_start_20170731	   
			if(str != '')
			str += ";";
//			var FOMAT_MA_BENHPHU = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','FOMAT_MA_BENHPHU');
//			if(FOMAT_MA_BENHPHU == 1){
//				$("#txtTENCHANDOANICD_KT").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
//			}
//			else{
//				$("#txtTENCHANDOANICD_KT").val(str +  ui.item.ICD10NAME+ "("+ ui.item.ICD10CODE + ")" );
//			}
			$("#txtTENCHANDOANICD_KT").val(str + ui.item.ICD10CODE + "-" + ui.item.ICD10NAME);
			$("#txtMATENCHANDOANICD_KT").val('');
			$("#txtMATENCHANDOANICD_KT").trigger("focus");
			return false;
		});
		
		ComboUtil.init("txtTKLOIDANBS",'DMC.LOIDANBS',sql_par,"600px",_colloidanbs,"txtLOIDANBS=LOIDANBS");
		
		// cách dùng
		sql_par = [];
		sql_par.push({"name":"[0]","value":1});
		ComboUtil.initComboGrid("txtSearchCD","DMC.CACHDUNG.THUOC",sql_par, "300px", "Cách dùng,CACHDUNG,100,0,f,l", function(event, ui) {
			$("#txtSearchCD").val(ui.item.CACHDUNG);
			doInputDrugType(_loaikedon, 1);
			return false;
		});
		
		// tim ma BA, Ma BN, TEN
		sql_par = [];
		sql_par.push({"name":"[0]","value":_khoaId});
		s_column = "Mã BN,MABENHNHAN,20,0,f,l;Mã BA,MAHOSOBENHAN,20,0,f,l;Tên,TENBENHNHAN,40,0,f,l;Năm sinh,NGAYSINH,20,0,f,l";
		ComboUtil.initComboGrid("lblPATIENTCODE","DS.BN.DTRI",sql_par, "530px", s_column, function(event, ui) {
			$("#lblPATIENTNAME").val(ui.item.TENBENHNHAN);
			$("#lblPATIENTCODE").val(ui.item.TENBENHNHAN);
			_khambenhid = ui.item.KHAMBENHID;  
			layThongTinBenhNhan();
			loadDSPhieuTra();
			loadDSPhieuDT(); //L2PT-8716
			$("#txtDS_THUOC").focus();			
			return false;
		});
		
		// tg dùng
		sql_par = [];
		sql_par.push({"name":"[0]","value":2});		
		ComboUtil.initComboGrid("txtTGSD","DMC.CACHDUNG.THUOC",sql_par, "300px", "Cách dùng,CACHDUNG,100,0,f,l", function(event, ui) {			
			$("#txtTGSD").val(ui.item.CACHDUNG);
			doInputDrugType(_loaikedon, 1);
			return false;
		});
		
		/*if(_loaitiepnhanid == '0'){
		
	}else if(_loaitiepnhanid == '3'){
		$('#dvlGhiChu').removeClass("col-xs-3");
		$('#dvlGhiChu').addClass("col-xs-2"); 
	}*/			
		
		if(_loaikedon == 1){
			$("#dvKE_TONG_TXT").show();
			if(_option == '02D010' || _option == '02D015'){
				$("#dvSONGAY_KE_TXT").show();
				$("#dvSONGAY_KE_LBL").show();
				$('#txtSONGAY_KE').val('1');
			}else {
				$("#dvSONGAY_KE_TXT").hide();
				$("#dvSONGAY_KE_LBL").hide();
			}			
			$("#dvGhiChu").removeClass("col-xs-3 low-padding").addClass("col-xs-5 low-padding");
			$("#dvKE_TONG_LB").show();
			$("#dvKE_CHITIET_TXT").hide();
			$("#dvKE_CHITIET_LB").hide();
			$("#dvSearchCD").hide();
			
			$('#dvlLIEU_DUNG').removeClass("col-xs-2");
			$('#dvlLIEU_DUNG').addClass("col-xs-3");
			
			$('#dvLIEU_DUNG').removeClass("col-xs-2");
			$('#dvLIEU_DUNG').addClass("col-xs-3");
		}else{
			$("#dvKE_TONG_TXT").hide();
			$("#dvKE_TONG_LB").hide();
			$("#dvSONGAY_KE_TXT").hide();
			$("#dvSONGAY_KE_LBL").hide();
			$("#dvKE_CHITIET_TXT").show();
			$("#dvKE_CHITIET_LB").show();
		}
		if(_srch_hoatchat == 1){
			$("#txtTENTHUOC").prop('readonly', false);
		}else{
			$("#txtTENTHUOC").prop('readonly', true);
		}
		
		//Load thong tin BN
		layThongTinBenhNhan();
    	//0$12$50$2$0$
		if(_option == '02D019'){
			_loai_tvt_kho = '0';
			_hinhthuc_kho = '9';
			_doituongbenhnhanid = '0';			
		}
		
		// 0: kho và tủ trực, 1: kho, 2: tủ trực.
		var _loaikho = "";
		if(_loadkhotheo == '1' || _option == '02D010_1'){ 
			_loaikho = "loaikho NOT IN (8,9,13)";
		}else if(_loadkhotheo == '2'){
			_loaikho = "loaikho IN (8,9,13)";
		}
		
		//tuyennx_add_add_start_20190807 L2PT-14523 L2PT-6527
		if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TRATHUOC_KOLAY_TUTRUC')=='1')
			_loaikho = "(loaikho NOT IN (8,9,13) or (loaikho IN (8,9,13) and upper(TENKHO)  like \'%GHÉP%\'))";
			
		//tuyennx_add_end_start_20190807 L2PT-14523
		
    	var _par_kho = _loai_tvt_kho+"$"+_hinhthuc_kho+"$"+_khoaId+"$"+_doituongbenhnhanid+"$0$"+_loaikho;
    	var _macdinh = {extval: true, value:'0',text:'-- Chọn --'};
		if(_option == '02D011'){ //Don thuoc mua ngoai
			_macdinh = {extval: true, value:'0',text:'--Mua ngoài--'};
		}
			
		ComboUtil.getComboTag("cboMA_KHO", "NTU02D010.17" ,_par_kho, "", _macdinh,"sp","", false);
		ComboUtil.findByExtra("cboMA_KHO",_makhomacdinh,0);
		
		var values = $.map($('#cboMA_KHO option'), function(e) { return e.value; });   
	  	_ds_khoid = values.join(',');
		
		var sql_loaithuoc = "";
		var _kieu = '0';		
		if(_kechungthuocvt == '1'){
			sql_loaithuoc = "LOAITHUOCVATTU.01";
		}else{
			if(_loainhommaubenhpham_id == '7'){
				sql_loaithuoc = "LOAITHUOC.01";
				_kieu = 1;
			}
			if(_loainhommaubenhpham_id == '8'){
				sql_loaithuoc = "LOAIVATTU.01";
			}
		}
		
		ComboUtil.getComboTag("cboLOAITHUOC", sql_loaithuoc ,[], "", {value:'-1',text:'-- Tất cả --'},"sql","", false);	
		//ComboUtil.getComboTag("cboDICHVUID","NGTDV.009",sql_par,_opt.dichvuid,{value:'',text:'Chọn yêu cầu khám'},'sql','', '');
		
		ComboUtil.getComboTag("cboBACSIID","NGT02K016.EV002",[{"name":"[0]", "value":_opts.khoaId}], "", {value:'-1',text:'Chọn'},"sql","",
		function() {
			if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LOAD_BSI_THEO_USER')=='1'){
				$("#cboBACSIID").val(_user_id);
			}
		});	
		//tuyennx_edit_end_20191007 L2PT-9546
		
		loadDSPhieuTra();
		ComboUtil.getComboTag("cboDUONG_DUNG","NTU02D010.07",[], "", {extval: true,value:'0',text:''},"sql");		
		
		loadDSPhieuDT(); //L2PT-8716
    	
    	//L2PT-7412
    	if(_phieudieutri_id && jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TRATHUOC_EDIT_PDT')=='1')
    		$('#cboPHIEU_CD').addClass("disabled");
    	//tuyennx_add_start_20170724 check an hien nut sua ghi chu benh chinh
    	var NGT_GHICHU_BENHCHINH = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','NGT_GHICHU_BENHCHINH');
		if(NGT_GHICHU_BENHCHINH=='1'){
			$("#divBc").removeClass("col-md-8");
			$("#divBc").addClass("col-md-6");
			$('#divSuaBc').css('display','');
		}
		//tuyennx_add_end_20170724 
		
    	
    	//Load thong tin phieu dieu tri
    	if(_phieudieutri_id != ""){
    		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.09", _phieudieutri_id,[]);
        	if(data_ar != null && data_ar.length > 0){
        		var _row = data_ar[0];
        		_icd10_code = _row.MACHANDOAN;
        		_icd10_text = _row.CHANDOAN;
        		
        		$("#lblICD10").val(_icd10_code);
        		$("#lblCHI_DINH").val(_icd10_text);
        		$("#txtTHOI_GIAN").val(_row.NGAYMAUBENHPHAM);
	    		$("#txtTG_DUNG").val(_row.NGAYMAUBENHPHAM);        		
        		$("#lblPHIEU_DT").val(_row.SOPHIEU);
        		if(_phieudieutri_id != ""){
        			$("#txtMACHANDOANICD").val(_row.MACHANDOAN);
            		$("#txtTENCHANDOANICD").val(_row.CHANDOAN);
            		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOAN_KEMTHEO);
        		}
        	}
    	}
    	bindEvent();
		if(_maubenhpham_id != "" && _maubenhpham_id != undefined){
			i_action = "Upd";
			data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.11", _maubenhpham_id,[]);
	    	if(data_ar != null){
	    		var _row = data_ar[0];
	    		$("#txtTHOI_GIAN").val(_row.NGAYMAUBENHPHAM);
	    		//$("#txtTG_DUNG").val(_row.NGAYMAUBENHPHAM_SUDUNG); 	L2PT-6609
	        	$("#txtMACHANDOANICD").val(_row.MACHANDOAN);
	    		$("#txtTENCHANDOANICD").val(_row.CHANDOAN);
	    		$("#cboPHIEU_CD").val(_row.PHIEUDIEUTRIID);
	    		$("#cboMA_KHO").val(_row.KHOTHUOCID);
	    		$('#txtDS_THUOC').trigger("focus");
	    		$('#txtSONGAY_KE').val('1');
	    		
	    		$("#txtLOIDANBS").val(_row.LOIDANBACSI);
	    		$("#txtTG_HENKHAM").val(_row.SONGAYHEN);
	    		if(_row.PHIEUHEN == '1'){
	    			$("#chkCapPhieuHenKham").prop('checked', true);
	    		}
	    		$('#txtSLTHANG').val(_row.SLTHANG);
	    		$("#cboDONTHUOCVT").val(_row.MAUBENHPHAMCHA_ID);	    		
	    		//_loaikedon = _row.LOAIKEDON;
	    		//$('#txtSONGAY_KE').attr("readonly", true);
	    	}else
	    		$('#txtMACHANDOANICD').trigger("focus");
	    	
	    	// kiểu trả = 1 là trả theo tiện ích
	    	if(_kieutra != '1'){
	    		loadGridDonThuoc('DONTHUOC', _maubenhpham_id);
	    	}			
			
			var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			for(var i = 0; i < jsonGridData.length; i ++){
				//doAddDrugToJson(ui, _objDrug, 1);
				 var objRowData = jQuery("#"+_gridDonThuoc).getRowData(jsonGridData[i]);
				 doAddDrugToJson(objRowData, _objDrug, 1);
			}			
			$("#cboMA_KHO").addClass("disabled");
			$("#cboMA_KHO").change();
			$("#cboDONTHUOCVT").addClass("disabled");
//			if(_option == '02D014' || _option == '02D016'){
//				$("#cboDONTHUOCVT").change();
//			}
			
			if(_loaikedon == 1){
				$("#dvKE_TONG_TXT").show();
				if(_option == '02D010' || _option == '02D015'){
					$("#dvSONGAY_KE_TXT").show();
					$("#dvSONGAY_KE_LBL").show();
					$('#txtSONGAY_KE').val('1');
				}else {
					$("#dvSONGAY_KE_TXT").hide();
					$("#dvSONGAY_KE_LBL").hide();
				}			
				$("#dvGhiChu").removeClass("col-xs-3 low-padding").addClass("col-xs-5 low-padding");
				$("#dvKE_TONG_LB").show();
				$("#dvKE_CHITIET_TXT").hide();
				$("#dvKE_CHITIET_LB").hide();
				$("#dvSearchCD").hide();
			}else{
				$("#dvKE_TONG_TXT").hide();
				$("#dvKE_TONG_LB").hide();
				$("#dvSONGAY_KE_TXT").hide();
				$("#dvSONGAY_KE_LBL").hide();
				$("#dvKE_CHITIET_TXT").show();
				$("#dvKE_CHITIET_LB").show();
			}			
		}else{
			i_action = "Add";
			_first_load = 1;
			if($("#txtMACHANDOANICD").val() != "")
				$("#txtDS_THUOC").trigger("focus");
			else
				$('#txtMACHANDOANICD').trigger("focus");
		}
		this.validator = new DataValidator("dvMain");
		//dsThuocKe24h(_khambenhid);
		_jsonThuoc24h = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.19", _khambenhid,[]);
		doLoadPrescription();
		//doCalDrugTicket();
		if(_maubenhpham_id != "" && _maubenhpham_id != undefined){
			var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			tinhTruocTien(_jsonGridData);
			loadAll("","");
			loadComboGrid(1);
			loadComboGrid(0);
		}		
		
		$('#cboMA_KHO').change();
		
		if(_kieutra == '1' && (_option == '02D014' || _option == '02D016') && _maubenhpham_id != "" && _maubenhpham_id != undefined){
			i_action = "Add";
			$("#txtTHOI_GIAN").val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
			$('#cboDONTHUOCVT').val(_maubenhpham_id);					
			$('#cboDONTHUOCVT').change();
		}
		//tuyennx_add_start_20180605
//		$('#jqgh_grdDONTHUOC_cb').css('display','none');
//		GridUtil.setGridParam(_gridDonThuoc, {
//			onSelectRow : function(id, status) {				
//				if (!status) {
//					var valueold = $("#"+_gridDonThuoc).jqGrid ('getCell', id, 'SO_LUONG');
//					$("#"+_gridDonThuoc).jqGrid ('setCell', id, 'SO_LUONG', Math.ceil(valueold));
//					
//					var value = $("#"+_gridDonThuoc).jqGrid ('getCell', id, 'SO_LUONG');
//					var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
//					tinhTruocTien(_jsonGridData);
//					loadAll("","");
//					$('#'+_gridDonThuoc).jqGrid('setCell',id,'OLDVALUE',value);
//				}
//			}
//		});
		//tuyennx_add_end_20180605
		
		if(_dichvucha_id > 0){
			$("#dvlLIEU_DUNG").hide();
			$("#dvLIEU_DUNG").hide();
		}
		//tuyennx_add_start_20180919 L2HOTRO-10337
		if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LOAD_TGDUNG_THEO_TGCD')!='0'){
			$('#spTG_DUNG').prop("onclick", null).off("click");
			$('#spTG_CHIDINH').prop("onclick", null).off("click");
		}
		//tuyennx_add_end_20180919 
	}
	
	function setTextToButton(_btnId, _btnText, _className){
		$("#"+_btnId+"").append("<span class='"+_className+"'></span> " + _btnText);
	}
	
	
	function loadDSPhieuTra(){
		if(_option == '02D014' || _option == '02D016' || _option == '02D024'){
			//L2PT-5527 khi tra thuoc tu menu load don thuoc theo phieu dieu tri, chuot phai tra thuoc ko xu ly
			var _phieudieutri_id = "0";
			if(!_maubenhpham_id && (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TRATHUOC_PDT_LOADPHIEU_HDG')=='0' || !_opts.phieudieutriid))//L2PT-136037
				_phieudieutri_id = $("#cboPHIEU_CD").val();
	    	var _par_phieutra = $('#txtTUNGAYKE').val()+"$"+$('#txtDENNGAYKE').val()+"$"+_loainhommaubenhpham_id+"$"+_khambenhid+"$"+$('#cboMA_KHO').val()+"$"+_phieudieutri_id;
			//ComboUtil.getComboTag("cboDONTHUOCVT", "LAYDS.PHIEUTRA.COND" , _par_phieutra, "0", {extval: true,value:'0',text:'--Chọn--'},"sp","",false);
			ComboUtil.getComboTag("cboDONTHUOCVT", "LAYDS.PHIEUTRA" , _par_phieutra, "0", {extval: true,value:'0',text:'--Chọn--'},"sp","",false);
			_phieutraid = $('#cboDONTHUOCVT').val();			
		}   
	}
	
	//L2PT-8716
	function loadDSPhieuDT(){
		sql_par=[];
    	sql_par.push({"name":"[0]","value":_khambenhid});
    	ComboUtil.getComboTag("cboPHIEU_CD", "NTU02D010.08" , sql_par, _phieudieutri_id, {value:'',text:'-- Chọn --'},"sql",'','');
	}
	
	function layThongTinBenhNhan(){
		_tyle_miengiam = 0;
		var _par_loai = [_khambenhid,_phongId];
		data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.10", _par_loai.join('$'),[]);
		_thoigian_vaovien = "";
    	if(data_ar != null && data_ar.length > 0){
    		var _row = data_ar[0];
    		//mbp.chandoan, mbp.machandoan, mbp.phieudieutriid
    		
    		/*if(_row.TRANGTHAIKHAMBENH == '1'){
    			EventUtil.raiseEvent("assignDrug_loisaibn",{option:_opts.option});
    		}*/
    		
			/*if(_opts._mabenhnhan != undefined &&_row.MABENHNHAN !== _opts._mabenhnhan){
    			EventUtil.raiseEvent("assignDrug_khacbn",{option:_opts.option});
    		}*/
    		
    		_thoigian_vaovien = _row.THOIGIANVAOVIEN;
    		$('#lblPATIENTCODE').val(FormUtil.unescape(_row.MABENHNHAN));
        	$('#lblPATIENTNAME').val(FormUtil.unescape(_row.TENBENHNHAN));
        	$('#lblBIRTHDAY_YEAR').val(FormUtil.unescape(_row.NAMSINH));
        	$('#lblGIOITINHCODE').val(FormUtil.unescape(_row.GIOITINH));
        	$('#txtDIACHI').val(FormUtil.unescape(_row.DIACHI));
        	$('#lblMA_BHYT').val(FormUtil.unescape(_row.MA_BHYT));
        	$('#lblDT_THANHTOAN').val(FormUtil.unescape(_row.TEN_DTBN));
        	$('#lblMUCHUONG_BHYT').val(FormUtil.unescape(_row.TYLE_BHYT));        	
        	$('#hidDIACHI').val(FormUtil.unescape(_row.DIACHI));
        	$('#hidBENHNHANID').val(FormUtil.unescape(_row.BENHNHANID));
        	$('#hidHOSOBENHANID').val(FormUtil.unescape(_row.HOSOBENHANID));
        	// tuyennx day congboyte add _start
        	$('#hidMAHOSOBENHAN').val(FormUtil.unescape(_row.MAHOSOBENHAN));
        	$('#hidGIOITINHID').val(FormUtil.unescape(_row.GIOITINHID));
        	$('#hidNGAYSINH').val(FormUtil.unescape(_row.NGAYSINH));
        	// tuyennx day congboyte add end
        	
        	$('#hidNGHENGHIEP').val(FormUtil.unescape(_row.NGHENGHIEP));        	
        	_ngaytiepnhan = _row.NGAYTIEPNHAN;
        	_indonthuoc_huongthan = _row.INHUONGTHAN;
        	_ngay_bhyt_kt = _row.BHYT_KT;
        	_tradu6thangluongcoban	= _row.TRADU6THANGLUONGCOBAN;
        	_duoc_van_chuyen		= _row.DUOC_VAN_CHUYEN;
        	_tiepnhanid 	= _row.TIEPNHANID;
        	_loaitiepnhanid = _row.LOAITIEPNHANID;
        	_indonthuoc_noitru = _row.INDONTHUOC;
        	_songaykemax = _row.SONGAYKEMAX;
        	_soluongthuocmax = _row.SOLUONGTHUOCMAX;
        	_canhbaophacdo = _row.CANHBAOPHACDO;
        	_hopdongid     = _row.HOPDONGID;        	
        	if(_loaitiepnhanid == 0){
        		_hinhthuc_kho = 12;
        		$("#txtMACHANDOANICD").val(_row.MACHANDOANVAOKHOA);
        		$("#txtTENCHANDOANICD").val(_row.CHANDOANVAOKHOA);
        		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOANVAOKHOA_KEMTHEO);
        	}
        	else if(_loaitiepnhanid == 1){
        		_hinhthuc_kho = 13;
        		$("#txtMACHANDOANICD").val(_row.MACHANDOANRAVIEN);
        		$("#txtTENCHANDOANICD").val(_row.CHANDOANRAVIEN);
        		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOANRAVIEN_KEMTHEO);
        		//tuyennx_add_start_20170724 
        		$("#txtGHICHU_BENHCHINH").val(_row.GHICHU_BENHCHINH);
        		//tuyennx_add_end_20170724 
        	}else if(_loaitiepnhanid == 3){
        		_hinhthuc_kho = 10;
        		$("#txtMACHANDOANICD").val(_row.MACHANDOANVAOKHOA);
        		$("#txtTENCHANDOANICD").val(_row.CHANDOANVAOKHOA);
        		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOANVAOKHOA_KEMTHEO);
        	}
        	if(_option == '02D011')//Nếu là mua ngoài -> băt buộc = 9
        		_hinhthuc_kho = 9;
        	_ma_bhyt = _row.MA_BHYT;
        	_ten_doituong_benhnhan = _row.TEN_DTBN;
        	_tyle_bhyt = _row.TYLE_BHYT;
        	_doituongbenhnhanid = _row.DOITUONGBENHNHANID;
        	_benhnhanid = _row.BENHNHANID;
    		
        	if(_benhnhanid != ""){
        		$("#btnTConSD").prop('disabled', false);
        	}else{
        		$("#btnTConSD").prop('disabled', true);
        	} 
        	
        	// tham sô cho phép kê nhiều ngày ko
        	if(_row.KENHIEUNGAY != '1'){
        		$("#txtSONGAY_KE").prop('disabled', false);
        	}else{
        		$("#txtSONGAY_KE").prop('disabled', true);
        	}  
        	
        	// tham số cho phép kê thuốc lẻ hay ko
        	if(_row.CAPTHUOCLE == '1'){
        		$("#txtSOLUONG_TONG").removeClass('clsnumber');
        		$("#txtSOLUONG_CHITIET").removeClass('clsnumber');
        		$("#txtSANG").removeClass('clsnumber');
        		$("#txtTRUA").removeClass('clsnumber');
        		$("#txtCHIEU").removeClass('clsnumber');
        		$("#txtTOI").removeClass('clsnumber');        		
        		//$("#txtSOLUONG_TONG").addClass('clsfloat');
        		$("#txtSOLUONG_CHITIET").addClass('clsfloat');
        		$("#txtSANG").addClass('clsfloat');
        		$("#txtTRUA").addClass('clsfloat');
        		$("#txtCHIEU").addClass('clsfloat');
        		$("#txtTOI").addClass('clsfloat');
        	} 
        	
        	_makhomacdinh = _row.MAKHO;        	
        	r_loaicheck = _row.LOAICHECK;
        	_cachdung_sangchieu = _row.CACHDUNG;
        	_checkdiung = _row.CHECKDIUNGTHUOC;
        	
        	if(_checkdiung != '1'){
        		$('#btnTDiUng').remove();
        	}
        	
        	_botimcachdung = _row.ANTIMCACHDUNGDT;
        	if(_botimcachdung != '1'){
        		$('#dvSearchCD').remove();
        		$('#dvGhiChu').removeClass('col-xs-2');
        		$('#dvGhiChu').addClass('col-xs-3');
        	}
        	
        	_sudungphacdo = _row.SUDUNGPHACDO;
        	if(_sudungphacdo != '1'){
        		$('#btnPdDt').remove();        		
        	}        	
        	
        	if(_row.LOAITIEPNHANID != '1'){
        		$("#btnXuTri").hide();
        	}
        	
        	if(_row.KTCODONTHUOC == '1' && _row.KOKTKHICODONTHUOC != '1'){
        		$('#btnXuTri').css('display','none');
        	}
        	
        	_tyle_miengiam = _row.TYLE_MIENGIAM;   
        	_chanhoatchat = _row.CHANHOATCHAT;
        	_kechungthuocvt = _row.KECHUNGTHUOCVT;
        	if(_row.DOITUONGBENHNHANID == "1"){
        		_ngayhanthe = _row.HANTHE;
        	}else{
        		_ngayhanthe = 365;
        	}
        	
        	// chỉ check với cấp thuốc - vật tư - đông y
        	if(_option == '02D010' || _option == '02D015' || _option == '02D017') {
        		if(_row.DOITUONGBENHNHANID == "1" && (parseInt(_row.HANTHE) <= parseInt(_row.NGAYTHE))){
            		DlgUtil.showMsg('Thẻ của bệnh nhân sắp hết hạn, còn ' + _row.HANTHE + ' ngày sử dụng');
            	}
        	}       	        	
        	
        	if(_cachdung_sangchieu == '1'){
        		$('#divhenkham').css('display','');
        	}
        	//lay danh sach a hoat chat thuoc da ke trong dot dieu trị ngoai tru
        	_objTmpThuoc = jsonrpc.AjaxJson.ajaxCALL_SP_O("GET_MAHOATCHAT", _tiepnhanid,[]);
        	
        	_ngaykethuocmax = _row.NGAYKEMAX;        	
        	_kieucheck = _row.KIEUCHECK;
        	
        	_checktrunghoatchat = _row.CHECKTRUNGHOATCHAT;
        	
        	if(_row.AN_CBO_LOAITHUOC == '1'){
        		$('#cboLOAITHUOC').addClass("disabled");
        	}else{
        		$('#cboLOAITHUOC').removeClass("disabled");
        	}
        	
        	_bacsi_ke = _row.BACSI_KE;
        	//if(_bacsi_ke == '1' && _loaikedon == '1'){
        	if(_bacsi_ke == '1' && _loaitiepnhanid == '0'){
        		$('#divbske').css('display','');
        	}       
        	
        	_format_cd = _row.FORMAT_CD;
        	_tudongindt  = _row.TUDONGIN;
        	_kieucheck_hoatchat = _row.KIEUCHECK_HOATCHAT;
        	_ke_tunhieu_kho = _row.KE_TUNHIEU_KHO;
        	_sudung_lieudung = _row.SUDUNG_LIEUDUNG;
        	
        	/*if(_loaitiepnhanid == '0'){
        		$('#dvSearchName').removeClass("col-xs-2");
        		$('#dvSearchName').addClass("col-xs-3");
        		
        		$('#dvDS_THUOC').removeClass("col-xs-2");
        		$('#dvDS_THUOC').addClass("col-xs-3");
        		
        		$('#dvGhiChu').removeClass("col-xs-2");
        		$('#dvGhiChu').addClass("col-xs-3");
        		
        		$('#dvlLIEU_DUNG').removeClass("col-xs-2");
        		$('#dvlLIEU_DUNG').addClass("col-xs-3");
        		
        		$('#dvLIEU_DUNG').removeClass("col-xs-2");
        		$('#dvLIEU_DUNG').addClass("col-xs-3");
        	}else if(_loaitiepnhanid == '3'){
        		$('#dvlGhiChu').removeClass("col-xs-3");
        		$('#dvlGhiChu').addClass("col-xs-2");
        	}*/
        	
        	//if(_sudung_lieudung == '1' &&  $.inArray(_option, ['02D010', '02D017']) >= 0) {
        	if(_sudung_lieudung == '1' &&  $.inArray(_option, ['02D010']) >= 0) {
        		$('#dvlLIEU_DUNG').css('display','');
        		$('#dvLIEU_DUNG').css('display','');
        		
        	}
        	/*	_lbllieudung = "250,0,f,c;Liều dùng,LIEUDUNG,130,0,e,c;";
        	}else{
        		_lbllieudung = "350,0,f,c;";
        	}
        	
*/        	// lấy thông tin thanh toán
        	var vp_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.VIENPHI",_tiepnhanid);
    		if(vp_ar != null && vp_ar.length > 0){
    			var data = vp_ar[0];
    			
    			_tongtien = data.TONGTIENDV;
    			_tientu = data.TAMUNG;
    			_danop = data.DANOP;				
    		}
    		
    		_kieucanhbaotientamung = _row.KIEUCANHBAOTIENTAMUNG;
    		_sudung_dvqd_kethuoc =  _row.SUDUNG_DVQD_KETHUOC;
    		if(_sudung_dvqd_kethuoc != '1'){
    			$("#cboDVQUYDOI").hide();
    			$("#dvlDVQD").hide();
    		}
    		
    		_ds_id_loaitvt = _row.DS_ID_LOAITVT.split(',');
    		_phieudtri_kedon = _row.PHIEUDTRI_KEDON;
    		
    		if(_macdinh_hao_phi == '9' || (_dichvucha_id != "" && parseInt(_dichvucha_id) > 0)){
    			_phieudtri_kedon = 0;
    		}   		
    		
    		_phieudtri_trathuoc = _row.PHIEUDTRI_TRATHUOC;
    		_loaikedonthuoc = _row.PHIEUDTRI_LOAIKEDON.split(',');
    		if( $.inArray(_option, _loaikedonthuoc) >= 0 && _loaitiepnhanid == '0' && _phieudtri_kedon == "1"){
    			$('#divPhieuDT').css('display','');
    			if(_option != '02D011'){
    				$('#lblpdtri').addClass('required');
    			}
    		}/*else{
    			$('#divPhieuDT').css('display','');
    			$('#lblpdtri').addClass('required');
    		}*/
    		
    		//_checkngaykedonntu = _row.CHECKNGAYKEDONNTU;

    		_ngt_lamtron_kedon = _row.NGT_LAMTRON_KETHUOC;
    		_an_menu_phai_kedon = _row.AN_MENU_PHAI_KEDON;
    		_chonkho_kedon = _row.KETHUOC_CHONKHO;
    		_sudungthuoc12sao = _row.SUDUNGTHUOC12SAO;
    		
    		_timkiem_bn_kedon_ntu = _row.TIMKIEM_BN_KEDON_NTU;
    		if(_timkiem_bn_kedon_ntu == '1' && _loaitiepnhanid == 0){
    			$("#lblPATIENTCODE").removeAttr('readonly');
    		}else{
    			$("#btnNhapMoi").remove();
    		}
    	}else{    		
    		_thoigian_vaovien = "";
    		$('#lblPATIENTCODE').val(FormUtil.unescape(""));
        	$('#lblPATIENTNAME').val(FormUtil.unescape(""));
        	$('#lblBIRTHDAY_YEAR').val(FormUtil.unescape(""));
        	$('#lblGIOITINHCODE').val(FormUtil.unescape(""));
        	$('#txtDIACHI').val(FormUtil.unescape(""));
        	$('#lblMA_BHYT').val(FormUtil.unescape(""));
        	$('#lblDT_THANHTOAN').val(FormUtil.unescape(""));
        	$('#lblMUCHUONG_BHYT').val(FormUtil.unescape(""));        	
        	$('#hidDIACHI').val(FormUtil.unescape(""));
        	$('#hidBENHNHANID').val(FormUtil.unescape(""));
        	$('#hidHOSOBENHANID').val(FormUtil.unescape(""));
        	// tuyennx day congboyte add _start
        	$('#hidMAHOSOBENHAN').val(FormUtil.unescape(""));
        	$('#hidGIOITINHID').val(FormUtil.unescape(""));
        	$('#hidNGAYSINH').val(FormUtil.unescape(""));
        	// tuyennx day congboyte add end
        	
        	$('#hidNGHENGHIEP').val("");        	
        	_ngaytiepnhan = "";
        	_indonthuoc_huongthan = "";
        	_ngay_bhyt_kt = 0;
        	_tradu6thangluongcoban	= "";
        	_duoc_van_chuyen		= "";
        	_tiepnhanid 	= 0;
        	_loaitiepnhanid = -1;
        	_indonthuoc_noitru = 0;
        	_songaykemax = 0;
        	_soluongthuocmax = 0;
        	_canhbaophacdo = 0;
        	_hopdongid     = 0;       	

    		_hinhthuc_kho = 12;
    		$("#txtMACHANDOANICD").val("");
    		$("#txtTENCHANDOANICD").val("");
    		$("#txtTENCHANDOANICD_KT").val("");
        	
        	_ma_bhyt = "";
        	_ten_doituong_benhnhan = 0;
        	_tyle_bhyt = 0;
        	_doituongbenhnhanid = 0;
        	_benhnhanid = 0      	;
        	_tyle_miengiam = 0;
        	_objTmpThuoc = [];
			_tongtien = 0;
			_tientu = 0;
			_danop = 0;	
			$('#msgCNKQ').text("");
			jQuery("#grdDONTHUOC").jqGrid("clearGridData");
    	}
	}
	
	EventUtil.setEvent("saveHDSD",function(e){
		$('#'+_gridDonThuoc).jqGrid('setCell', e.rowid, 'HUONGDAN_SD', e.text);
		$('#'+_gridDonThuoc).jqGrid('getLocalRow', e.rowid).HUONGDAN_SD = e.text;
		
		$('#'+_gridDonThuoc).jqGrid('setCell', e.rowid, 'DUONG_DUNG', e.duongdung);
		$('#'+_gridDonThuoc).jqGrid('getLocalRow', e.rowid).DUONG_DUNG = e.duongdung;
		
		// SONDN 27/03/2018
		$('#'+_gridDonThuoc).jqGrid('setCell', e.rowid, 'LIEUDUNG', e.lieudung);
		$('#'+_gridDonThuoc).jqGrid('getLocalRow', e.rowid).LIEUDUNG = e.lieudung;
		// END SONDN 27/03/2018
		
		var _paramTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', e.rowid);
		$("#btnTHUOC_" + _paramTmp.THUOCVATTUID).text(splitDd(e.text));
		DlgUtil.close("dlgDuongDung");
	});
	
	function updateJson(jsonObj, _key, _valueComp, _noteUpdate, _value){
		for (var i=0; i<jsonObj.length; i++) {
			if (jsonObj[i][_key] == _valueComp && i == (jsonObj.length -1)) {
				jsonObj[i][_noteUpdate] = _value;
				jsonObj[i][_noteUpdate] = _value;
				if(_noteUpdate == "SO_LUONG"){
					jsonObj[i]['OLDVALUE'] = _value;
					var _so_luong = jsonObj[i].SO_LUONG;
					_don_gia = parseInt(jsonObj[i].DON_GIA.replaceAll(',',''));
				    var ngay_dv = $("#txtTHOI_GIAN").val().trim();
				    //alert(ngay_dv.substring(0, 10));
					var objTinhTien = new Object();
			    	objTinhTien.DOITUONGBENHNHANID 	= _doituongbenhnhanid;
			    	objTinhTien.MUCHUONG 			= parseFloat(_tyle_bhyt);
			    	objTinhTien.GIATRANBH 			= parseFloat(jsonObj[i].GIATRANBHYT);
			    	objTinhTien.GIABHYT 			= parseFloat(_don_gia);
			    	objTinhTien.GIAND 				= parseFloat(_don_gia);
			    	objTinhTien.GIADV 				= parseFloat(_don_gia);
			    	objTinhTien.GIANN 				= parseFloat(_don_gia);
			    	objTinhTien.DOITUONGCHUYEN 		= jsonObj[i].ID_DT_MOI;
			    	objTinhTien.GIADVKTC 			= 0;
			    	objTinhTien.MANHOMBHYT 			= _nhom_mabhyt_id;
			    	objTinhTien.SOLUONG 			= _so_luong;
			    	objTinhTien.CANTRENDVKTC 		= 0;
			    	objTinhTien.THEDUTHOIGIAN 		= _tradu6thangluongcoban;
			    	objTinhTien.DUOCVANCHUYEN 		= _duoc_van_chuyen;
			    	objTinhTien.TYLETHUOCVATTU 		= _tyle_bhyt_tt_tvt;
			    	objTinhTien.NGAYHANTHE			= _ngay_bhyt_kt;
			    	objTinhTien.NGAYDICHVU			= ngay_dv.substring(0, 10);
			    	objTinhTien.TYLE_MIENGIAM 		= _tyle_miengiam;
			    	var r = vienphi.tinhtien_dichvu(objTinhTien);
					jsonObj[i].THANH_TIEN = r.tong_cp;//""+_thanh_tien.formatMoney(0);
					jsonObj[i].BH_TRA = r.bh_tra;//""+_bh_tra.formatMoney(0);
					jsonObj[i].ND_TRA = r.nd_tra;//""+_nd_tra.formatMoney(0);
				}
				break;
		  	}
		}
	}
	
	function _openformhenkham(){
		var _songayhen = parseInt($('#txtTG_HENKHAM').val());
		var _ngayht = jsonrpc.AjaxJson.getSystemDate('DD-MM-YYYY');
		var _ngayhen = moment(_ngayht,"DD-MM-YYYY").add('days', _songayhen).format('DD/MM/YYYY');
		var param = {
			mabenhnhan : $('#lblPATIENTCODE').val(),
			tenbenhnhan : $('#lblPATIENTNAME').val(), 
			nghenghiep : $('#hidNGHENGHIEP').val(),
			diachi : $('#hidDIACHI').val(),
			namsinh : $('#lblBIRTHDAY_YEAR').val(),
			khambenhid : _khambenhid,
			benhnhanid : $('#hidBENHNHANID').val(),
			chandoan : $('#txtTENCHANDOANICD').val(),
			ngaytiepnhan : _ngayhen,
			capnhat : '1',
			hosobenhanid : $('#hidHOSOBENHANID').val()
		};
		
		dlgPopup=DlgUtil.buildPopupUrl("dlgXuTri","divDlg","manager.jsp?func=../ngoaitru/NGT02K008_Thongtin_Lichkham",param,'Thông tin lịch hẹn',900,500);
		DlgUtil.open("dlgXuTri");
	}
	
	function tinhTruocTien(jsonObj){
		var _tong_tien_dv_tt = 0;
		var _data_ar 	= jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.05", _tiepnhanid,[]);
	    if(_data_ar != null){
    		var _row = _data_ar[0];
    		_TRAN_BHYT 		= _row.TRAN_BHYT;
    		_TYLE_BHYT_TT 	= _row.TYLE_BHYT;
    		_TONGTIENDV_BH 	= _row.TONGTIENDV_BH;
	    }
	    _tong_tien_dv_tt = parseFloat(_TONGTIENDV_BH);
	    var _obj_new, _so_luong, _don_gia;
		//_tong_tien_dv
	    //_GLB_CACH_TINH_TIEN
		for (var i=0; i<jsonObj.length; i++) {
			_so_luong = jsonObj[i].SO_LUONG;
			_don_gia = parseInt(jsonObj[i].DON_GIA.replaceAll(',',''));
			_obj_new = jsonObj[i].ID_DT_MOI;
			
			var objTinhTien = new Object();
	    	objTinhTien.DOITUONGBENHNHANID 	= _doituongbenhnhanid;
	    	objTinhTien.MUCHUONG 			= parseFloat(_TYLE_BHYT_TT);
	    	objTinhTien.GIATRANBH 			= parseFloat(jsonObj[i].GIATRANBHYT);
	    	objTinhTien.GIABHYT 			= parseFloat(_don_gia);
	    	objTinhTien.GIAND 				= parseFloat(_don_gia);
	    	objTinhTien.GIADV 				= parseFloat(_don_gia);
	    	objTinhTien.GIANN 				= _don_gia;
	    	objTinhTien.DOITUONGCHUYEN 		= _obj_new;
	    	objTinhTien.GIADVKTC 			= 0;
	    	objTinhTien.MANHOMBHYT 			= _nhom_mabhyt_id;
	    	objTinhTien.SOLUONG 			= _so_luong;
	    	objTinhTien.CANTRENDVKTC 		= 0;
	    	objTinhTien.THEDUTHOIGIAN 		= _tradu6thangluongcoban;
	    	objTinhTien.DUOCVANCHUYEN 		= _duoc_van_chuyen;
	    	objTinhTien.TYLETHUOCVATTU 		= _tyle_bhyt_tt_tvt;
	    	objTinhTien.NGAYHANTHE			= _ngay_bhyt_kt;
	    	objTinhTien.NGAYDICHVU			= moment($("#txtTHOI_GIAN").val().trim()).format('DD/MM/YYYY');
	    	objTinhTien.TYLE_MIENGIAM 		= _tyle_miengiam;
	    	var r = vienphi.tinhtien_dichvu(objTinhTien);
		    row_Price 	= parseFloat(r.tong_cp);
		    row_Insr 	=  parseFloat(r.bh_tra);
		    row_End 	=  parseFloat(r.nd_tra);
		    _tong_tien_dv_tt = parseFloat(_tong_tien_dv_tt) + parseFloat(row_Price);		    
		    
		    var parttt = ['NGT_CHECK_TU']; 
			var _ngt_check_tu = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', parttt.join('$'));
		    
		    // check BN khác BHYT, BN là NGT và BN nằm ngoài HĐ
		    if(_ngt_check_tu == '1' && _doituongbenhnhanid != "1"  && _loaitiepnhanid == '1' && $.inArray( _option, ['02D010','02D015','02D017']) >= 0 && parseInt(_hopdongid) == 0){	
		    	var checktien = parseFloat(_tongtien) + parseFloat(_tong_tien_dv_tt) - parseFloat(_danop);
		    	if( parseFloat(_tientu) <  parseFloat(checktien)){
		    		DlgUtil.showMsg('Tổng tiền thanh toán > tiền tạm ứng');
		    		if(_kieucanhbaotientamung == "1"){
		    			$('#btnSave').prop('disabled', true);
		    		}else{
		    			$('#btnSave').prop('disabled', false);
		    		}
		    	}
		    }
		    
		    if(parseFloat(_TRAN_BHYT) < _tong_tien_dv_tt){
		    	_GLB_CACH_TINH_TIEN = 0;
		    	break;
		    }else{
		    	_GLB_CACH_TINH_TIEN = 1;
		    }				    	
		}
	}
	
	function ktTongSoLuong(jsonObj, _key, _val){
		_tongso = 0;
		for (var i in jsonObj){
			if(jsonObj[i].THUOCVATTUID == _val)
				_tongso += parseFloat(jsonObj[i][_key]);
		}
		console.log("------------------_tongso: "+ _tongso);
		return _tongso;
	}
	//Tim kiem json
	function findObjectJson(jsonObj, _key, _valueComp){
		var _ret = false;
		if(jsonObj.length > 0){
			for (var i=0; i<jsonObj.length; i++) {
				if (jsonObj[i][_key] == _valueComp) {
					_ret = true;
					break;
			  	}else
			  		_ret = false;
			}
		}else
			_ret = false;
		return _ret;
	}
	
	function removeItemJson(jsonObj, _key, _valueComp){
		for (var i=0; i<jsonObj.length; i++) {
			if (jsonObj[i][_key] == _valueComp) {
				delete jsonObj[i][_key];
				break;
		  	}
		}
	}
	
	function bindEvent(){
		$("#cboMA_KHO").on('change', function (e) {
			if(parseInt(_loai_don) == 2 || parseInt(_loai_don) == 4)
				loadComboGrid(1);
			else{
				loadComboGrid(1);
				var e = jQuery.Event("keydown");
				e.which = 50; // # Some key code value
				$("#txtDS_THUOC").trigger(e);
			}
			loadComboGrid(0);//Tim theo hoat chat thuoc
			$("#txtDS_THUOC").focus();
			
			loadDSPhieuTra();
		});
		
		$("#txtTUNGAYKE").on('change', function (e) {
			if(parseInt(_loai_don) == 2 || parseInt(_loai_don) == 4)
				loadComboGrid(1);
			else{
				loadComboGrid(1);
				var e = jQuery.Event("keydown");
				e.which = 50; // # Some key code value
				$("#txtDS_THUOC").trigger(e);
			}
			loadComboGrid(0);//Tim theo hoat chat thuoc
			$("#txtDS_THUOC").focus();
			
			loadDSPhieuTra();
		});
		
		$("#txtDENNGAYKE").on('change', function (e) {
			if(parseInt(_loai_don) == 2 || parseInt(_loai_don) == 4)
				loadComboGrid(1);
			else{
				loadComboGrid(1);
				var e = jQuery.Event("keydown");
				e.which = 50; // # Some key code value
				$("#txtDS_THUOC").trigger(e);
			}
			loadComboGrid(0);//Tim theo hoat chat thuoc
			$("#txtDS_THUOC").focus();
			
			loadDSPhieuTra();
		});
		
		$('#txtMATENCHANDOANICD_KT').keydown(function (e) {
			if (e.keyCode == 9 && $('#txtMATENCHANDOANICD_KT').val() == '') {
				$("#chkCapPhieuHenKham").focus();
			}
		});
		
		$("#cboLOAITHUOC").on('change', function (e) {
			if(parseInt(_loai_don) == 2 || parseInt(_loai_don) == 4)
				loadComboGrid(1);
			else{
				loadComboGrid(1);
				var e = jQuery.Event("keydown");
				e.which = 50; // # Some key code value
				$("#txtDS_THUOC").trigger(e);
			}
			loadComboGrid(0);//Tim theo hoat chat thuoc
			$("#txtDS_THUOC").focus();
		});
		
		$("#cboDONTHUOCVT").on('change', function (e) {
			_phieutraid = $('#cboDONTHUOCVT').val();
			if(parseInt(_loai_don) == 2 || parseInt(_loai_don) == 4)
				loadComboGrid(1);
			else{
				loadComboGrid(1);
				var e = jQuery.Event("keydown");
				e.which = 50; // # Some key code value
				$("#txtDS_THUOC").trigger(e);
			}
			loadComboGrid(0);//Tim theo hoat chat thuoc
			
			$("#txtDS_THUOC").focus();
			if(_phieutraid == '0'){
				$("#btnAllPhieu").addClass("disabled");
			}else{
				$("#btnAllPhieu").removeClass("disabled");
			}			
			if(_maubenhpham_id == "" || _maubenhpham_id == undefined){
				$('#cboMA_KHO').val($('#cboDONTHUOCVT'+ " option:selected").attr('extval0'));
			}
			if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TRATHUOC_TRATUNGTHUOC')=='0'){
				$("#btnAllPhieu").trigger("click");
			}
			//tuyennx_add_start_20180919 L2HOTRO-11045
			var _loadTime = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'LOAD_TGDUNG_THEO_TGCD');
			if(_loadTime=='1'){
				$('#txtTHOI_GIAN').val($('#cboDONTHUOCVT'+ " option:selected").text().split('-')[1]);
				$('#txtTG_DUNG').val($('#cboDONTHUOCVT'+ " option:selected").text().split('-')[1]);
			} else if (_loadTime=='2') {
				$('#txtTHOI_GIAN').val($('#cboDONTHUOCVT'+ " option:selected").text().split('-')[1]);
				$('#txtTG_DUNG').val($('#cboDONTHUOCVT'+ " option:selected").text().split('-')[2]);
			}
//			//tuyennx_add_end__20180919
		});
		
		$("#txtDS_THUOC").on('keypress', function (e) {
			if(_option == '02D014' || _option == '02D016'){
				var phieutraid = $('#cboDONTHUOCVT').val();
				if(phieutraid == '0'){
					$("#cboDONTHUOCVT").focus();
					DlgUtil.showMsg('Hãy chọn đơn thuốc/Vật tư cần trả');
					return;
				}
			}
			
			if(_option == '02D010' || _option == '02D015'){
				var khothuocid = $('#cboMA_KHO').val();
				if(khothuocid == '0' && _chonkho_kedon == '1'){
					$("#cboMA_KHO").focus();
				     setTimeout(
				      function(){ 
				       DlgUtil.showMsg('Hãy chọn kho thuốc/vật tư');
				      }
				     , 500);
				}
			}
		});
		
		$("#txtSONGAY_KE").on('change', function (e) {
			if(parseInt($(this).val()) > parseInt(_songaykemax)){
				$("#txtSONGAY_KE").focus();
				DlgUtil.showMsg('Số ngày kê thuốc không lớn hơn '+_songaykemax+' ngày');
				return;
			}
		});
		
		$("#chkCapPhieuHenKham").on('change', function (e) {
			if($('#chkCapPhieuHenKham').is(':checked')){
				_openformhenkham();
			}
		});
		
		$("#btnEDITBP").on("click",function(e){
			var myVar={
				benhphu : $('#txtTENCHANDOANICD_KT').val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgBPKT","divDlg","manager.jsp?func=../ngoaitru/NGT02K052_ChinhSuaBenhPhu",myVar,"Chỉnh sửa bệnh kèm theo",600,420);
			DlgUtil.open("dlgBPKT");
		});	
		
		EventUtil.setEvent("chinhsua_benhphu",function(e){
			$('#txtTENCHANDOANICD_KT').val(e.benhphu);
			DlgUtil.close("dlgBPKT");
		});
		
		EventUtil.setEvent("pddt_presc_success",function(e){
			_maubenhpham_temp_id = e.id;
			var ret = '';
			var param = [];
			if(_loaitiepnhanid != 0){
				param = [e.id,_khambenhid,$('#txtTG_DUNG').val()];
				ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D075.EV005", param.join('$'));
			} else {
				ret = '';
			}
			
			if(typeof ret != 'undefined' && ret != ''){
				DlgUtil.showConfirm(ret + " đã được chỉ định trong ngày, có đồng ý load mẫu không có chỉ định trùng?",function(flag) {
					if(flag){
						_maubenhpham_temp_id = e.id;
						if(_maubenhpham_temp_id != ""){
							loadGridDonThuoc('TEMP', _maubenhpham_temp_id,'2');
						}
					} else {
						DlgUtil.close("dlgPhacDoMau");
						return;
					}
				});
			} else {
				_maubenhpham_temp_id = e.id;
				if(_maubenhpham_temp_id != ""){
					loadGridDonThuoc('TEMP', _maubenhpham_temp_id,'2');
				}
			}
			
			if(e.khoid != "" && e.khoid != '' && _maubenhpham_temp_id != ''){
				$('#cboMA_KHO').val(e.khoid);
				$("#cboMA_KHO").addClass("disabled");
				$("#cboMA_KHO").change();
			}
				
			DlgUtil.close("dlgPhacDoMau");
		});
		
		$("#txtSLTHANG").focusout(function(){
			$('#txtTG_HENKHAM').val($('#txtSLTHANG').val());
		});
		
		$("#cboPHIEU_CD").on('change', function (e) {
			var _sel = this.value;
			if(_sel != ""){
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.09", _sel,[]);
				if(data_ar != null && data_ar.length > 0){
					var _row = data_ar[0];
					if(_phieudtri_kedon == '1'){
						$("#txtTHOI_GIAN").val(_row.NGAYMAUBENHPHAM);
			    		$("#txtTG_DUNG").val(_row.NGAYMAUBENHPHAM);
					}					
					$("#txtMACHANDOANICD").val(_row.MACHANDOAN);
		    		$("#txtTENCHANDOANICD").val(_row.CHANDOAN);
		    		$("#txtTENCHANDOANICD_KT").val(_row.CHANDOAN_KEMTHEO);
				}
				//load don thuoc tra theo phieu dieu tri //L2PT-59397
				if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TRATHUOC_PDT_LOADPHIEU')=='0')
					loadDSPhieuTra();//L2PT-5527		
			}else{
				layThongTinBenhNhan();
			}
		});
		
		//Trả cả đơn thuốc
		$("#btnAllPhieu").on("click",function(e){
			var _tongtien = 0, _bhtra = 0, _ndtra = 0;
			var sql_par=[];
			_objDrug = [];
			sql_par.push(
				{"name":"[0]","value":$('#cboDONTHUOCVT').val()},
				{"name":"[1]","value":_khambenhid},
				{"name":"[2]","value":_loainhommaubenhpham_id}
			 );			
			var vthuoc = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D010.TRADON", sql_par);
			_objDrug = JSON.parse(vthuoc);		
			jQuery("#grdDONTHUOC").jqGrid("clearGridData");
			for(var i=0;i<_objDrug.length;i++) {
				_objDrug[i].SO_LUONG_KD = _objDrug[i].SO_LUONG; //L2PT-6627
				//tuyennx_add_start_20200909_L2PT-27468
				if(TRATHUOC_KHONGLAY_SOLUONG == '1')
					_objDrug[i].SO_LUONG = ""; 
				//tuyennx_add_end_20200909_L2PT-27468
				jQuery("#grdDONTHUOC").jqGrid('addRowData', i, _objDrug[i]);
				loadAll(i, _objDrug[i].ID_DT_MOI);
				_tongtien += parseFloat(_objDrug[i].THANH_TIEN);
				_bhtra += parseFloat(_objDrug[i].BH_TRA);
				_ndtra += parseFloat(_objDrug[i].ND_TRA);
				
				//tuyennx_add_start_20180810  L2HOTRO-10863
				var html = '<button type="button" class="btn btn-sm btn-primary" style="height: 24px;" id="'+'btnTHUOCLT_'+_objDrug[i].DICHVUKHAMBENHID+'">'+'<span class="glyphicon glyphicon-upload"></span></button>';
				$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'LAMTRON', html);
				$('#btnTHUOCLT_' + _objDrug[i].DICHVUKHAMBENHID).on("click",function(e) {
					if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
						var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
						var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);						
						var valueold = dataTmp.SO_LUONG;
						if(valueold.startsWith(".")){
							valueold = valueold.replace('.','0.');
						}
						$("#"+_gridDonThuoc).jqGrid ('setCell', rowIdTmp, 'SO_LUONG', Math.ceil(valueold));
					}
				});
				//tuyennx_add_end_20181003 L2HOTRO-10863
            }
			
			$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { THANH_TIEN	: _tongtien.formatMoney(0) });
			$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { BH_TRA		: _bhtra.formatMoney(0) });
			$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { ND_TRA		: _ndtra.formatMoney(0) });
			
			//$('#cboDONTHUOCVT').prop('disabled', true);
		});
		
		//Thêm thuốc vào Grid
		//$("#btnAdd").on("click",function(e){
//		function addThuoc(){
//			var valid = that.validator.validateForm();
//			var _soluong_thuoc = 0;
//			var songay = 0;		
//			
//			//if(_ds_id_loaitvt.length > 0 && $.inArray( _option, ['02D010','02D015','02D017']) >= 0 && $.inArray( $("#hidLOAITVTID").val(), _ds_id_loaitvt) >= 0 && $("#txtGHICHU").val() == ''){
//			if(_ds_id_loaitvt.length > 0 && $.inArray( _option, ['02D010','02D015']) >= 0 && $.inArray( $("#hidLOAITVTID").val(), _ds_id_loaitvt) >= 0 && $("#txtGHICHU").val() == '' && _dichvucha_id == ""){
//				$("#txtGHICHU").focus();
//				DlgUtil.showMsg("Hãy nhập cách dùng thuốc");				
//				return;
//			}
//			
//			if(_sudung_dvqd_kethuoc != "0" && $("#cboDVQUYDOI").val() == '0' && $("#hidMABYT").val() == "40.17"){
//				$("#cboDVQUYDOI").focus();
//				DlgUtil.showMsg("Hãy chọn đơn vị quy đổi");				
//				return;
//			}
//			
//			if(_objDrugTemp.length == 0){
//				$("#txtDS_THUOC").trigger("focus");
//				return DlgUtil.showMsg("Bạn phải nhập thuốc để kê đơn!");
//			}
//			if(_loaikedon == 1){
//				if(parseInt($('#txtSONGAY_KE').val()) > parseInt(_songaykemax)){
//					$('#txtSONGAY_KE').select();
//					$('#txtSONGAY_KE').focus();
//					DlgUtil.showMsg("Số ngày kê đơn không được lớn hơn " +_songaykemax);
//					return;
//				}
//				if(r_loaicheck == "0" && (parseInt($('#txtSOLUONG_TONG').val()) > parseInt(_soluongthuocmax))){
//					$('#txtSOLUONG_TONG').select();
//					$('#txtSOLUONG_TONG').focus();
//					DlgUtil.showMsg("Số lượng kê đơn thuốc không được lớn hơn " +_soluongthuocmax);
//					return;
//				}
//				
//				songay = $('#txtSONGAY_KE').val();
//			}else {
//				if(r_loaicheck == "0" && (parseInt($('#txtSOLUONG_CHITIET').val()) > parseInt(_soluongthuocmax))){
//					$('#txtSOLUONG_CHITIET').select();
//					$('#txtSOLUONG_CHITIET').focus();
//					DlgUtil.showMsg("Số lượng kê đơn thuốc  không được lớn hơn " +_soluongthuocmax);
//					return;
//				}	
//				songay = $('#txtSO_NGAY').val();
//			}
//			
//			// bệnh nhân ngoại trú và kê thuốc.
//			if(_loaitiepnhanid == '1' && (_option == '02D010' || _option == '02D017')) {
//				
//				if(_kieucheck == '1' && (parseInt(_ngayhanthe) <= 5 && parseInt(songay) > 7)){
//					DlgUtil.showMsg("Hạn thẻ BHYT <= 5 ngày, số ngày kê thuốc ko được lớn hơn 7 ngày");
//					return;
//				}
//				
//				if(_kieucheck == '2' && (parseInt(_ngayhanthe) < parseInt(songay))){
//					DlgUtil.showMsg("Số ngày kê thuốc không thể lớn hơn số ngày còn hạn thẻ");
//					return;
//				}
//			}
//			
//			if(parseInt($("#txtSO_NGAY").val()) <= 0){
//				DlgUtil.showMsg("Số ngày kê thuốc phải lớn hơn 0");
//				return;
//			}
//			
//			if(_loaikedon == 1){
//				_soluong_thuoc = $("#txtSOLUONG_TONG").val();
//			}else{
//				_soluong_thuoc = $("#txtSOLUONG_CHITIET").val();
//			}	
//			
//			if(r_loaicheck == "1" && parseFloat($("#hidCANHBAOSOLUONG").val()) > 0 && (parseFloat(_soluong_thuoc) > parseFloat($("#hidCANHBAOSOLUONG").val()))){
//				return DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + $("#hidCANHBAOSOLUONG").val());
//			}
//			
//			if(_soluong_thuoc > 0){
//				if(parseFloat(_soluong_thuoc) > parseFloat($("#hdSOLUONGKHADUNG").val())){
//					if(_loaikedon == 1)
//						$("#txtSOLUONG_TONG").trigger("focus");
//					else
//						$("#txtSOLUONG_CHITIET").trigger("focus");
//					return DlgUtil.showMsg("Số lượng kê đơn phải nhỏ hơn hoặc bằng số lượng khả dụng");
//				}
//			}else{
//				if(_loaikedon == 1)
//					$("#txtSOLUONG_TONG").trigger("focus");
//				else
//					$("#txtSOLUONG_CHITIET").trigger("focus");
//				return DlgUtil.showMsg("Số lượng"+_lbl_text+" kê đơn phải lớn hơn 0!");
//			}
//			/*if(_option == '02D014'||_option == '02D016'){
//				if(Number.isInteger(parseFloat(_soluong_thuoc)) == false){
//					$("#txtSOLUONG_TONG").focus();
//					return DlgUtil.showMsg("Số lượng trả"+_lbl_text+" phải là số nguyên dương!");
//				}	
//			}*/
//			
//			objData = new Object();
//			objData.THUOCVATTUID = $('#hidTHUOCVTID').val();
//			objData.TIEPNHANID = _tiepnhanid;
//			
//			var _checkthuocsao = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.THUOCSAO", JSON.stringify(objData));
//			if(_loaitiepnhanid == 0 && _sudungthuoc12sao == '1' && $.inArray($('#hidCHOLANHDAODUYET').val(), ['2','3']) >= 0 && _checkthuocsao == '0'){
//				if($('#hidCHOLANHDAODUYET').val() == '2'){
//					var myVar={
//							MABENHNHANH : $('#lblPATIENTCODE').val()
//						};
//					dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K069_ThuocMotSao",myVar,"HỘI CHẨN THUỐC 1 SAO",800,500);
//					DlgUtil.open("dlgTHUOCSAO");					
//				}
//				
//				EventUtil.setEvent("assignSevice_save1sao",function(e){
//					var _objData = new Object();
//					_objData = $.extend(_objData,e.msg);
//					$('#hidTHUOCSAO').val(JSON.stringify(_objData));
//					_addthuocgrid(valid, _soluong_thuoc);
//					DlgUtil.close("dlgTHUOCSAO");
//				});
//				
//				
//				if($('#hidCHOLANHDAODUYET').val() == '3'){
//					var myVar={
//							ICD10NAME : $('#txtTENCHANDOANICD').val(),
//							ICD10CODE : $('#txtMACHANDOANICD').val()
//						};
//					dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K070_ThuocHaiSao",myVar,"HỘI CHẨN THUỐC 2 SAO",800,500);
//					DlgUtil.open("dlgTHUOCSAO");
//				}
//				
//				EventUtil.setEvent("assignSevice_save2sao",function(e){
//					var _objData = new Object();
//					_objData = $.extend(_objData,e.msg);
//					$('#hidTHUOCSAO').val(JSON.stringify(_objData));
//					_addthuocgrid(valid, _soluong_thuoc);
//					DlgUtil.close("dlgTHUOCSAO");
//				});
//			}else{
//				_addthuocgrid(valid, _soluong_thuoc);
//			}
//			
//			$('#hidTHUOCVTID').val("");
//		};

		$("#"+_gridDonThuoc).bind("jqGridInlineAfterSaveRow", function (e, rowid, orgClickEvent) {
			var value = $("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'SO_LUONG');
			var sl_kd = $("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'SO_LUONG_KD');
			var oldValue = $("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'OLDVALUE');
			if(parseFloat(value) > parseFloat(sl_kd)){
				$('#'+_gridDonThuoc).jqGrid('setCell',rowid,'SO_LUONG',oldValue);
				DlgUtil.showMsg("Số lượng trả không được lớn hơn số lượng khả dụng!");
				return;
			}
			if(_loaitiepnhanid != 0){
				if(/^\d+$/.test(value)){
					var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
					tinhTruocTien(_jsonGridData);
					loadAll("","");
					$('#'+_gridDonThuoc).jqGrid('setCell',rowid,'OLDVALUE',value);
				} else {
					$('#'+_gridDonThuoc).jqGrid('setCell',rowid,'SO_LUONG',oldValue);
					DlgUtil.showMsg("Số lượng thuốc phải là số nguyên dương!");
					return;
				}
			} else {
				var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				tinhTruocTien(_jsonGridData);
				loadAll("","");
				$('#'+_gridDonThuoc).jqGrid('setCell',rowid,'OLDVALUE',value);
			}
			/*}
			else{
				$("#"+_gridDonThuoc).jqGrid('setCell',rowid,7,oldValue);
				DlgUtil.showMsg("Số lượng"+_lbl_text+" phải là số nguyên dương!");
			}*/
			
		});
		//jqGridLoadComplete
		$("#"+_gridDonThuoc).bind("jqGridLoadComplete", function (e, rowid, orgClickEvent) {
			var rowIds = $("#"+_gridDonThuoc).jqGrid('getDataIDs');
			if(rowIds != null && rowIds.length > 0){
				if(_first_load == 0){
					for (var i = 0; i < rowIds.length; i++) { 
					    _param =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIds[i]);
					    doAddItemGridToJson(_param, _objDrug, (i + 1));
					}
					_first_load = 1;
				}
				
				for (var i = 0; i < rowIds.length; i++) { 
					_param =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIds[i]);
					var html = '<button type="button" class="btn btn-link" style="white-space: normal;" id="'+'btnTHUOC_'+_param.THUOCVATTUID+'">'+splitDd(_param.HUONGDAN_SD)+'</span></button>';
					$("#"+_gridDonThuoc).jqGrid ('setCell', rowIds[i], 'DUONGDUNGE', html);
					$('#btnTHUOC_' + _param.THUOCVATTUID).on("click",function(e) {
						if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
							var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
							var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);
							var myVar={
									HDSD:dataTmp.HUONGDAN_SD,
									rowId:rowIdTmp
							};
							dlgPopup=DlgUtil.buildPopupUrl("dlgDuongDung","dlgDuongDung","manager.jsp?func=../ngoaitru/NGT02K043_DUONGDUNG",myVar,"Đường dùng",700,250);
							DlgUtil.open("dlgDuongDung");
						}
					});
					//tuyennx_add_start_20181003 L2HOTRO-10863
					var html = '<button type="button"  class="btn btn-sm btn-primary" style="height: 24px;" id="'+'btnTHUOCLT_'+_param.DICHVUKHAMBENHID+'">'+'<span class="glyphicon glyphicon-upload"></span></button>';
					$('#btnTHUOCLT_' +  _param.DICHVUKHAMBENHID).on("click",function(e) {
						if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
							var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
							var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);						
							var valueold = dataTmp.SO_LUONG;
							if(valueold.startsWith(".")){
								valueold = valueold.replace('.','0.');
							}
							$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'SO_LUONG', Math.ceil(valueold));
						}
					});
					
					$("#"+_gridDonThuoc).jqGrid ('setCell', rowIds[i], 'LAMTRON', html);
					//tuyennx_add_end_20181003 L2HOTRO-10863
				}
			}
			var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			tinhTruocTien(_jsonGridData);
			loadAll("","");
			
		});
		
		$("#"+_gridDonThuoc).bind("jqGridBeforeSelectRow", function (e, rowid, orgClickEvent) {
			if(/^\d+$/.test($("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'SO_LUONG')))
				oldValue = $("#"+_gridDonThuoc).jqGrid ('getCell', rowid, 'SO_LUONG');
			GridUtil.unmarkAll(_gridDonThuoc);
            GridUtil.markRow(_gridDonThuoc,rowid);
		});
		
		$("#"+ _gridDonThuoc).bind("CustomActionCompleted", function(e, rowid){
			var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			_objDrug = jsonGridData;
			_list_thuoc_dake = "";
			if(_objDrug.length > 0){
				for(var i = 0; i < _objDrug.length; i ++){
					if(_list_thuoc_dake == "")
						_list_thuoc_dake = _objDrug[i].THUOCVATTUID;
					else
						_list_thuoc_dake = _list_thuoc_dake +","+ _objDrug[i].THUOCVATTUID;
				}
			}else
				$('#cboMA_KHO').prop('disabled', false);
			//Cap nhat lai thong tin tien chi tra cho phieu thuoc
			
			var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			tinhTruocTien(_jsonGridData);
			
			loadAll("","");
		});
		//Thuoc con su dung
		$("#btnTConSD").on("click",function(e){
			EventUtil.setEvent("rrugusing_cancel",function(e){
				DlgUtil.close("dlgCDDV");
			});
			var myVar={
					benhnhanId : _benhnhanid
				};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTCu","manager.jsp?func=../ngoaitru/NGT02K020_ThuocConSuDung",myVar,"Thuốc còn sử dụng",1200,550);
			DlgUtil.open("dlgCDDV");
		});
		
		//tuyennx_add_start_20170816 yc L2DKBD-195
		//Thuoc di ung
		$("#btnTDiUng").on("click",function(e){
			EventUtil.setEvent("diungthuoc_cancel",function(e){
				DlgUtil.close("dlgCDDV");
			});
			var myVar={
					benhnhanId : _benhnhanid,
					khoaId : _khoaId,
					phongId : _phongId
				};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTCu","manager.jsp?func=../ngoaitru/NGT02K057_ThuocDiUng",myVar,"Dị ứng thuốc",1200,550);
			DlgUtil.open("dlgCDDV");
		});
		//tuyennx_add_end_20170816 yc L2DKBD-195
		
		$("#btnPdDt").on("click",function(e){
			var myVar={
					machandoan:$('#txtMACHANDOANICD').val(),
					loaidv:1,
					khoid: $('#cboMA_KHO').val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgPhacDoMau","divPhieuMauCDDV","manager.jsp?func=../noitru/NTU02D075_PhacDoMau",myVar,"Phác đồ mẫu",1200,550);
			DlgUtil.open("dlgPhacDoMau");
		});
		
		$("#btnSave").bindOnce("click",function(e){
			$('#btnSave').prop('disabled', true);
			doInsDonThuoc(i_action);
		},5000);
		
		$("#btnNhapMoi").bindOnce("click",function(e){
			$('#btnSave').prop('disabled', false);
			//L2PT-7366
			if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TRATHUOC_LOADBN_NHAPMOI') == '1'){
				$('#cboDONTHUOCVT').focus();
				$('#cboDONTHUOCVT').val('0');
				jQuery("#grdDONTHUOC").jqGrid("clearGridData");
			}
			else{
				_khambenhid = -1;
				layThongTinBenhNhan();
				$('#lblPATIENTCODE').focus();
			}
			
		},5000);
		
		$("#btnDTMau").on("click", function(e) {
			EventUtil.setEvent("temp_presc_success",function(e){
				_maubenhpham_temp_id = e.id;
				var ret = '';
				var param = [];
				if(_loaitiepnhanid != 0){
					param = [e.id,_khambenhid,$('#txtTG_DUNG').val()];
					ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.21", param.join('$'));
				} else {
					ret = '';
				}
				
				if(typeof ret != 'undefined' && ret != ''){
					DlgUtil.showConfirm(ret + " đã được chỉ định trong ngày, có đồng ý load mẫu không có chỉ định trùng?",function(flag) {
						if(flag){
//							if(_maubenhpham_temp_id != ""){
//								loadGridDonThuoc('TEMP', _maubenhpham_temp_id);
//							}
							_maubenhpham_temp_id = e.id;
							if(_maubenhpham_temp_id != ""){
								param = [e.id,_khambenhid,$('#txtTG_DUNG').val()];
								ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.23", param.join('$'));
								r_dichvu_id_diff = ret;
								loadGridDonThuoc('TEMP', _maubenhpham_temp_id,'1');
							}
						} else {
							DlgUtil.close("dlgCDDV");
							return;
						}
					});
				} else {
					_maubenhpham_temp_id = e.id;
					if(_maubenhpham_temp_id != ""){
						loadGridDonThuoc('TEMP', _maubenhpham_temp_id);
					}
				}
				
				if(e.khoid != "" && e.khoid != '' && _maubenhpham_temp_id != ''){
					$('#cboMA_KHO').val(e.khoid);
					$("#cboMA_KHO").addClass("disabled");
					$("#cboMA_KHO").change();
				}
					
				DlgUtil.close("dlgCDDV");
			});
			var myVar={
				loainhom : _loainhommaubenhpham_id,
				loaitiepnhanid : _loaitiepnhanid
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTCu","manager.jsp?func=../ngoaitru/NGT02K018_DonThuocMau",myVar,"Đơn thuốc/vật tư mẫu",1200,550);
			DlgUtil.open("dlgCDDV");
		});
		
		$("#btnDTCu").on("click", function(e) {
			EventUtil.setEvent("old_presc_success",function(e){
				_maubenhpham_id = e.id;
				
				var ret = '';
				var param = [];
				if(_loaitiepnhanid != 0 && _option != '02D011'){
					param = [_maubenhpham_id,_tiepnhanid];
					ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.24", param.join('$'));
				} else {
					ret = '';
				}
				
				if(typeof ret != 'undefined' && ret != ''){
					DlgUtil.showConfirm(ret + " đã tồn tại trong các đơn thuốc của bệnh nhân, có đồng ý load đơn không trùng hoạt chất?",function(flag) {
						if(flag){
							if(_maubenhpham_id != ""){
								param = [_maubenhpham_id,_tiepnhanid];
								ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.25", param.join('$'));
								r_dichvu_id_dtc_diff = ret;
								loadGridDonThuoc('', _maubenhpham_id,'1');
							}
						} else {
							DlgUtil.close("dlgCDDV");
							return;
						}
					});
				} else {
					loadGridDonThuoc('', _maubenhpham_id);
				}

				DlgUtil.close("dlgCDDV");
			});
			var myVar={
				benhnhanId : _benhnhanid,
				khothuocId : $("#cboMA_KHO").val()
			};
			dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTCu","manager.jsp?func=../ngoaitru/NGT02K019_DonThuocCu",myVar,"Đơn thuốc cũ",1200,550);
			DlgUtil.open("dlgCDDV");
		});
		$("#btnClose").on("click", function(e) {
			EventUtil.raiseEvent("assignDrug_cancel",{option:_opts.option,type:_luu, badaingay:_badaingay});
		});
		$("#btnXuTri").on("click", function(e) {
			EventUtil.raiseEvent("assignDrug_xutri",{khambenhid:_khambenhid,option:_opts.option});
		});
		$("#btnSaveTemp").on("click", function(e) {
			if($("#txtTEXT_TEMP").val().trim() != "")
				doInsDonThuoc("SAVE_TEMP");
			else
				return DlgUtil.showMsg("Bạn phải nhập tên đơn thuốc mẫu!");
		});
		$('#txtTHOI_GIAN').on('change', function (e) {
			var bstr = $('#txtTHOI_GIAN').val();			
			$('#txtTHOI_GIAN').val(stringToDateFormat(bstr));
		});
		$('#txtTG_DUNG').on('change', function (e) {
			var bstr = $('#txtTG_DUNG').val();			
			$('#txtTG_DUNG').val(stringToDateFormat(bstr));
		});
		
		$("#"+_gridDonThuoc).bind("jqGridAfterInsertRow", function (e, rowid, orgClickEvent) {
			if(_an_menu_phai_kedon != "1"){
				$(".jqgrow", '#'+_gridDonThuoc).contextMenu('contextMenu', {
					bindings: {
	                    'changeBHYT': function (t) {
	                    	loadAll($(t).attr("id"),'1');	
	                    },
	                    'changeVP': function (t) {
	                    	loadAll($(t).attr("id"),'4');	
	                    },
	                    'changeYC': function (t) {
	                    	loadAll($(t).attr("id"),'6');
	                    },
	                    'changeHPK': function (t) {
	                    	loadAll($(t).attr("id"),'9');
	                    }
	                },
	                onContextMenu: function (event, menu) {
	                    var rowId = $(event.target).parent("tr").attr("id");
	                    var grid = $('#'+_gridDonThuoc);
	                    grid.setSelection(rowId);
	                    GridUtil.unmarkAll(_gridDonThuoc);
	                    GridUtil.markRow(_gridDonThuoc,rowId);
	                    return true;
	                },
	            });
			}
		});

		var f6 = 117;
		//tuyennx_edit_start_20170721 chi hien thi thuoc thuong dung khi click vao ten thuoc 
		$("#txtDS_THUOC").unbind('keydown').keydown(function(e) {
			//var _jsonThuoc;
			pressF6(e);
		});
		$("#txtTENTHUOC").unbind('keydown').keydown(function(e) {
			//var _jsonThuoc;
			pressF6(e);
		});
		
		function pressF6(e){
			if (e.keyCode == f6) {
				EventUtil.setEvent("always_presc_success",function(e){
					if(e != null){
						//var _jsonThuoc = e.jsonThuoc;
						//alert(JSON.stringify(e.jsonThuoc));
						var _item = e.jsonThuoc;
						$("#txtDS_THUOC").val(_item.TEN_THUOC);
				        if(_srch_hoatchat == 1)
				        	$("#txtTENTHUOC").val(_item.HOATCHAT);
				        else
				        	$("#txtTENTHUOC").val(_item.TEN_THUOC);
				        $("#cboDUONG_DUNG").val(_item.DUONGDUNGID);
				        _tuongtacthuoc = ','+ _item.TUONGTACTHUOC+',';
				        _nhom_mabhyt_id = _item.NHOM_MABHYT_ID;
				        if(_loai_don =="2" || _loai_don =="4")
				        	$("#cboMA_KHO").val(_item.KHOID);
				        if(_option != '02D011')//Don mua ngoai
				        	$("#hdSOLUONGKHADUNG").val(_item.SLKHADUNG);
				        else
				        	$("#hdSOLUONGKHADUNG").val(100000);
				        $("#txtGHICHU").val("");
				        $("#hdHUONGDANTHUCHIEN").val("");
				        if(_loaikedon == 1){
					        $("#txtSOLUONG_TONG").trigger("focus");
				        }else{
				        	$("#txtSO_NGAY").trigger("focus");
				        }	
				        _objDrugTemp = [];
				        _objDrug = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
						doAddDrugToJson(_item, _objDrug, 1);
						doAddDrugToJson(_item, _objDrugTemp, 1);
						DlgUtil.close("dlgCDDV");
					}
					else{
						$("#txtDS_THUOC").trigger("focus");
						DlgUtil.close("dlgCDDV");
					}
				});
				var myVar={
						option : _option,
						khoid : $("#cboMA_KHO").val()
					};
				dlgPopup=DlgUtil.buildPopupUrl("dlgCDDV","divDTTD","manager.jsp?func=../noitru/NTU02D056_ThuocThuongDung",myVar,"Thuốc thường dùng",1200,550);
				DlgUtil.open("dlgCDDV");
			}
			//tuyennx_edit_end_20170721 
		}
		//tuyennx_add_start_20180605
		$("#"+_gridDonThuoc).bind("jqGridAfterLoadComplete", function (e, rowid, orgClickEvent) {
			var ids=$("#"+_gridDonThuoc).getGridParam("reccount");
				for(var i = 1; i <= ids; i++){
					var _row = $("#"+_gridDonThuoc).jqGrid('getRowData', i);
					if(_row.SO_LUONG.includes(".")){
						$("#"+_gridDonThuoc).jqGrid('setSelection', i, false);
					}
			}
		 });
		//tuyennx_add_end_20180605
		
		//START-- L2K74TW-605 -- hongdq
		$('#txtTGSEARCH').on(
				'change',
				function(e) {
					$('#calSearch').prop('title', $('#txtTGSEARCH').val());
					sql_par = RSUtil.buildParam("", [ _opts.khambenh_id, "4", $('#txtTGSEARCH').val()+ ' 00:00:00', $('#txtTGSEARCH').val()+ ' 23:59:59']);
					ComboUtil.getComboTag("cboPHIEU_CD",
							"COM.PHIEUDIEUTRI_1", sql_par,'', 'sql', '');
		});
		
		$("#btnCancelSearch").click(function(){
			sql_par = RSUtil.buildParam("", [ _opts.khambenh_id, "4" ]);
			ComboUtil.getComboTag("cboPHIEU_CD",
				"COM.PHIEUDIEUTRI", sql_par,
				_opts.phieudieutriid == null ? ''
						:_opts.phieudieutriid, {
					value : '-1',
					text : 'Chưa có phiếu điều trị'
				}, 'sql', '', function() {

				});
		});
		//END-- L2K74TW-605 -- hongdq
	}
	
	function _addthuocgrid(valid, _soluong_thuoc){
		if(valid){				
			var _objTVTTuongtac = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			if(_objTVTTuongtac.length > 0){
				for(var i = 0; i < _objTVTTuongtac.length; i ++){
					if(_dsthuocvattuid != ""){
						_dsthuocvattuid = _dsthuocvattuid +","+ _objTVTTuongtac[i].THUOCVATTUID;
					}else{
						_dsthuocvattuid = _objTVTTuongtac[i].THUOCVATTUID;
					}						
				}
			}
			
			if(_dsthuocvattuid != ""){
				var param = ['',_objDrugTemp[0].THUOCVATTUID, _dsthuocvattuid];
				var _msgTuongtac = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.TUONGTAC.TVT", param.join('$'));
				if(_msgTuongtac != undefined && _msgTuongtac != ""){
					DlgUtil.showMsg(_msgTuongtac+"");
				}					
			}							
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "THUOCSAO", $("#hidTHUOCSAO").val());
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "KHO_THUOCID", $("#hidKHOTHUOCTHEOTHUOC").val());
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "SO_LUONG", _soluong_thuoc);
			var _huongdan_sd = "";
			if(_loaikedon == 1)
				_huongdan_sd = "1@"+ $("#cboDUONG_DUNG option:selected").text() +"@"+$("#txtGHICHU").val()+"@"+_soluong_thuoc+"@@@@";
			else
				_huongdan_sd = $("#hdHUONGDANTHUCHIEN").val().replace("_param_huongdan", $("#txtGHICHU").val());
			
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "HUONGDAN_SD", _huongdan_sd);
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DUONG_DUNG", $("#cboDUONG_DUNG option:selected").text());
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DUONGDUNGID", $("#cboDUONG_DUNG").val());				
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "LOAITVTID", $("#hidLOAITVTID").val());
			
			var r_lieudung = "";
			if(_sudung_lieudung == '1'){
				r_lieudung = $("#txtLIEUDUNG").val();
				if(r_lieudung == "" && $.inArray(_option, ['02D010']) >= 0){
					$("#txtLIEUDUNG").focus();
					DlgUtil.showMsg("Hãy nhập liều dùng");
					return;
				}
			}else{					
				if(_option == '02D017' ){
					r_lieudung = $("#hidLIEUDUNGBD").val();
				}
				var _format = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'FORMAT_LIEUDUNG_4210');
			    if(_format == '1' && _option != '02D017'){
			    	r_lieudung = $("#hidLIEUDUNGBD").val();
			    }else{
			    	r_lieudung = $("#txtGHICHU").val();
			    }
			}
			
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "LIEUDUNG", r_lieudung);
			updateJson(_objDrug, "THUOCVATTUID", _objDrugTemp[0].THUOCVATTUID, "DVQD", $("#cboDVQUYDOI").val());
			
			if(ktTongSoLuong(_objDrug, "SO_LUONG", _objDrugTemp[0].THUOCVATTUID) > parseInt($("#hdSOLUONGKHADUNG").val())){
				if(_loaikedon == 1)
					$("#txtSOLUONG_TONG").trigger("focus");
				else
					$("#txtSOLUONG_CHITIET").trigger("focus");
				return DlgUtil.showMsg("Tổng số lượng kê"+_lbl_text+" lớn hơn số lượng tồn kho khả dụng!");
			}
			jQuery("#grdDONTHUOC").jqGrid("clearGridData");
			for(var i=1;i<=_objDrug.length;i++) {
				jQuery("#grdDONTHUOC").jqGrid('addRowData', i, _objDrug[i-1]);
				
				if(Math.ceil(_objDrug[i-1].SO_LUONG)-_objDrug[i-1].SO_LUONG > 0){
					$("#"+_gridDonThuoc).jqGrid('setSelection', i, false);
				}
				
				var html = '<button type="button" class="btn btn-link" style="white-space: normal;" id="'+'btnTHUOC_'+_objDrug[i-1].THUOCVATTUID+'">'+splitDd(_objDrug[i-1].HUONGDAN_SD)+'</button>';
				$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'DUONGDUNGE', html);
				$('#btnTHUOC_' + _objDrug[i-1].THUOCVATTUID).on("click",function(e) {
					if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
						var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
						var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);
						var myVar={
								HDSD:dataTmp.HUONGDAN_SD,
								rowId:rowIdTmp
						};
						dlgPopup=DlgUtil.buildPopupUrl("dlgDuongDung","dlgDuongDung","manager.jsp?func=../ngoaitru/NGT02K043_DUONGDUNG",myVar,"Đường dùng",700,250);
						DlgUtil.open("dlgDuongDung");
					}
				});
				//tuyennx_add_start_20180810 
				var html = '<button type="button" class="btn btn-sm btn-primary" style="height: 24px;" id="'+'btnTHUOCLT_'+_objDrug[i-1].DICHVUKHAMBENHID+'">'+'<span class="glyphicon glyphicon-upload"></span></button>';
				$("#"+_gridDonThuoc).jqGrid ('setCell', i, 'LAMTRON', html);
				$('#btnTHUOCLT_' + _objDrug[i-1].DICHVUKHAMBENHID).on("click",function(e) {
					if($(e.target.parentElement).is(':button') || $(e.target).is(':button')){
						var rowIdTmp = $(e.target).closest("tr.jqgrow").attr("id");
						var dataTmp =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIdTmp);						
						var valueold = dataTmp.SO_LUONG;
						if(valueold.startsWith(".")){
							valueold = valueold.replace('.','0.');
						}
						$("#"+_gridDonThuoc).jqGrid ('setCell', rowIdTmp, 'SO_LUONG', Math.ceil(valueold));
					}
				});
				//tuyennx_add_end_20180810 
            }
			if(_GLB_CACH_TINH_TIEN == 1)
				tinhTruocTien(_objDrug);
			cleanForm();
			$('#txtDS_THUOC').trigger("focus");
			if(_ke_tunhieu_kho != '1'){
				$('#cboMA_KHO').prop('disabled', true);
			}
			$('#cboDONTHUOCVT').prop('disabled', true);
			loadAll("","");
			
			// đẩy ma hoat chat vao array temp
			_objTmpThuoc.push({
				"THUOCVATTUID" : _objDrugTemp[0].THUOCVATTUID,
				"MAHOATCHAT" : _objDrugTemp[0].MAHOATCHAT,
				"KETRUNGHOATCHAT" : _objDrugTemp[0].KETRUNGHOATCHAT
			});
		}
		$('#txtGHICHU').val('');
		$('#txtSearchCD').val('');
		
		$('#txtSLSOLAN').val('');
		$('#txtSOLANSONGAY').val('');
		$('#txtLIEUDUNG').val('');
		$("#hidLOAITVTID").val('');
		$("#hidTHUOCSAO").val('');		
	}
	
	/*function changeObject(rowId,loaiDt){
		if($('#'+_gridDonThuoc).find("input[id*='SO_LUONG']").length > 0){
    		DlgUtil.showMsg('Tồn tại trường số lượng đang sửa');
			return false;
    	}
		
		var rowData = $("#grdDSCD").jqGrid('getRowData',rowId); 
		var objDataChange = vienphi.tinh_phi_dv(_doituongbenhnhanid, parseFloat(_tyle_bhyt),parseFloat(rowData.DICHVU_BHYT_DINHMUC),parseFloat(rowData.GIABHYT),parseFloat(rowData.GIANHANDAN),parseFloat(rowData.GIADICHVU),'0',loaiDt);
		if(objDataChange.bh_tra != -1 && objDataChange.bh_tra != -1 && objDataChange.nd_tra != -1){
			var _oldBHYTDataRow = rowData.BHYT_TRA;
			var _oldTTDataRow =  rowData.THANH_TIEN;
			
			var _newBHYTDataRow = parseFloat(objDataChange.bh_tra)*parseFloat(rowData.SOLUONG);
			var _newTTDataRow = (parseFloat(objDataChange.nd_tra)+parseFloat(objDataChange.bh_tra))*parseFloat(rowData.SOLUONG);
			
			var _payBHYTChange = _newBHYTDataRow - parseFloat(_oldBHYTDataRow);
			var _payTTChange = _newTTDataRow - parseFloat(_oldTTDataRow) - parseFloat(_oldBHYTDataRow);
			loadPay(_payTTChange, _payBHYTChange);
//			$('#grdDSCD').jqGrid('setCell',rowId,6,parseFloat(objDataChange.tong_cp) + parseFloat(typeof objDataChange.nd_tra_chenh == "undefined" ? '0':objDataChange.nd_tra_chenh));
//			$('#grdDSCD').jqGrid('setCell',rowId,7,parseFloat(objDataChange.bh_tra)*parseFloat(rowData.SOLUONG));
//			$('#grdDSCD').jqGrid('setCell',rowId,8,parseFloat(objDataChange.nd_tra)*parseFloat(rowData.SOLUONG));
//			$('#grdDSCD').jqGrid('setCell',rowId,15,objDataChange.loai_dt);
//			$('#grdDSCD').jqGrid('setCell',rowId,20,typeof objDataChange.nd_tra_chenh == "undefined" ? '':objDataChange.nd_tra_chenh);
//			$('#grdDSCD').jqGrid('setCell',rowId,19,typeof objDataChange.loai_dt_moi == "undefined" ? '':objDataChange.loai_dt_moi);
//			$('#grdDSCD').jqGrid('setCell',rowId,22,typeof objDataChange.ten_loai_tt_moi == "undefined" ? '':objDataChange.ten_loai_tt_moi);
			$('#grdDSCD').jqGrid('setCell',rowId,7,parseFloat(objDataChange.tong_cp) + parseFloat(typeof objDataChange.nd_tra_chenh == "undefined" ? '0':objDataChange.nd_tra_chenh));
			$('#grdDSCD').jqGrid('setCell',rowId,8,parseFloat(objDataChange.bh_tra)*parseFloat(rowData.SOLUONG));
			$('#grdDSCD').jqGrid('setCell',rowId,9,parseFloat(objDataChange.nd_tra)*parseFloat(rowData.SOLUONG));
			$('#grdDSCD').jqGrid('setCell',rowId,16,objDataChange.loai_dt);
			$('#grdDSCD').jqGrid('setCell',rowId,21,typeof objDataChange.nd_tra_chenh == "undefined" ? '':objDataChange.nd_tra_chenh);
			$('#grdDSCD').jqGrid('setCell',rowId,20,typeof objDataChange.loai_dt_moi == "undefined" ? '':objDataChange.loai_dt_moi);
			$('#grdDSCD').jqGrid('setCell',rowId,23,typeof objDataChange.ten_loai_tt_moi == "undefined" ? '':objDataChange.ten_loai_tt_moi);
		} else {
			DlgUtil.showMsg('Không thể chuyển loại thanh toán cho đối tượng bệnh nhân này');
		}
	}*/
	
	function loadAll(_rowId, _loadDTMoi){
		var rowIds = $("#"+_gridDonThuoc).jqGrid('getDataIDs');
		var _obj_new = "";
		var _totalPrice = 0, _totalIns = 0, _totalEnd = 0;
		var row_Price = 0, row_Insr = 0, row_End = 0;
		var data_ar;
		if(rowIds != null && rowIds.length > 0){
			var _param, _number;
			var _don_gia, _tong_tra;
			for (var i = 0; i < rowIds.length; i++) { 
			    _param =  $("#"+_gridDonThuoc).jqGrid('getRowData', rowIds[i]);
			    if(_rowId == rowIds[i]){
			    	_obj_new = _loadDTMoi;
			    }else{
			    	_obj_new = _param.ID_DT_MOI;
			    }
			    _number = parseFloat(_param.SO_LUONG) || 0;
			    //alert("_number :"+ _number +" --_obj_new: "+_obj_new);
			    if(_number > 0 || !_param.SO_LUONG){ //L2PT-27468
			    	_don_gia = _param.DON_GIA.replaceAll(',','');
			    	
			    	var objTinhTien = new Object();
			    	objTinhTien.DOITUONGBENHNHANID 	= _doituongbenhnhanid;
			    	objTinhTien.MUCHUONG 			= parseFloat(_tyle_bhyt);			    	
			    	objTinhTien.GIATRANBH 			= parseFloat(_param.GIATRANBHYT);
			    	objTinhTien.GIABHYT 			= parseFloat(_don_gia);
			    	objTinhTien.GIAND 				= parseFloat(_don_gia);
			    	objTinhTien.GIADV 				= parseFloat(_don_gia);
			    	objTinhTien.GIANN 				= parseFloat(_don_gia);
			    	objTinhTien.DOITUONGCHUYEN 		= _obj_new;
			    	objTinhTien.GIADVKTC 			= 0;
			    	objTinhTien.MANHOMBHYT 			= _nhom_mabhyt_id;
			    	objTinhTien.SOLUONG 			= _number;
			    	objTinhTien.CANTRENDVKTC 		= 0;
			    	objTinhTien.THEDUTHOIGIAN 		= _tradu6thangluongcoban;
			    	objTinhTien.DUOCVANCHUYEN 		= _duoc_van_chuyen;
			    	objTinhTien.TYLETHUOCVATTU 		= _tyle_bhyt_tt_tvt;
			    	objTinhTien.NGAYHANTHE			= _ngay_bhyt_kt;
			    	objTinhTien.NGAYDICHVU			= moment($("#txtTHOI_GIAN").val().trim(),'DD/MM/YYYY');
			    	objTinhTien.TYLE_MIENGIAM 		= _tyle_miengiam;
			    	//L2PT-3550
			    	if(_obj_new == 17)
			    		objTinhTien.DOITUONGCHUYEN = 4
		    		if(_obj_new == 18)
			    		objTinhTien.DOITUONGCHUYEN = 1
			    	console.log("------------------------------objTinhTien :"+ JSON.stringify(objTinhTien));
			    	var r = vienphi.tinhtien_dichvu(objTinhTien);
				    if(parseFloat(r.tong_cp) == - 1){
						if(_loadDTMoi != "")
							DlgUtil.showMsg("Chuyển đối tượng thanh toán không hợp lệ!");
					}else{
						//console.log("------------------------------r.tong_cp:"+ r.tong_cp);
						_loai_doituong_moi = _loadDTMoi;
						row_Price 	= parseFloat(r.tong_cp);
					    row_Insr 	=  parseFloat(r.bh_tra);
					    row_End 	=  parseFloat(r.nd_tra);
					    
			    		if(_GLB_CACH_TINH_TIEN == 1){
			    			row_Insr = (parseFloat(_don_gia)*_number*_TYLE_BHYT_TT)/100;
			    			if(row_Price > 0)
			    				row_End = parseFloat(row_Price) - parseFloat(row_Insr);
			    			else
			    				row_End = 0;
			    		}			    			
					    
					    _totalPrice = _totalPrice + row_Price;	
					    _totalIns = _totalIns + row_Insr;
					    _totalEnd = _totalEnd + row_End;
					    
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],8,row_Price.formatMoney(0));
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],9,row_Insr.formatMoney(0));
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],10,row_End.formatMoney(0));
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],11,_ten_doituong_benhnhan);
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],12,r.ten_loai_tt_moi);
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],13,_doituongbenhnhanid);
//						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],14,_obj_new);
					    $("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'THANH_TIEN',row_Price.formatMoney(0));
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'BH_TRA',row_Insr.formatMoney(0));
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ND_TRA',row_End.formatMoney(0));
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_CU',_ten_doituong_benhnhan);
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_MOI',r.ten_loai_tt_moi);
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ID_DT_CU',_doituongbenhnhanid);
						$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ID_DT_MOI',_obj_new);
						//L2PT-3550
				    	if(_obj_new == 17){
				    		$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_MOI','Corona');
				    		$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ID_DT_MOI',_obj_new);
				    	}	
			    		if(_obj_new == 18){
			    			$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_MOI','BHYT+Corona');
				    		$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ID_DT_MOI',_obj_new);
			    		}
						
						//cap nhat lai doi tuong moi cho tung thuoc
					}
			    }else{
//			    	$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],8, 0);
//					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],9, 0);
//					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],10, 0);
//					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],11, _ten_doituong_benhnhan);
//					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],12, "");
			    	$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'THANH_TIEN', 0);
					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'BH_TRA', 0);
					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'ND_TRA', 0);
					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_CU', _ten_doituong_benhnhan);
					$("#"+_gridDonThuoc).jqGrid('setCell',rowIds[i],'LOAI_DT_MOI', "");
			    	return DlgUtil.showMsg("Số lượng phải là số nguyên dương!");
			    }
			}
		}else{
			_totalStart = 0;
			_totalIns =  0;
			_totalEnd =  0;
			oldValue = "";
		}
		
		var _tt_sothang = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'THANHTIEN_DONGY_SOTHANG');
	    if(_tt_sothang == '1' && _option == '02D017'){
	    	_totalPrice = _totalPrice * parseFloat($('#txtSLTHANG').val()); 
	    }
		
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { THANH_TIEN	: _totalPrice.formatMoney(0) });
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { BH_TRA		: _totalIns.formatMoney(0) });
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { ND_TRA		: _totalEnd.formatMoney(0) });
		//Cap nhat lai tien don thuoc khi sua so luong
		$('#lblMUCHUONG_BHYT').val(_tyle_bhyt +"%");
		$('#lblMA_BHYT').val(_ma_bhyt);
		$('#lblDT_THANHTOAN').val(_ten_doituong_benhnhan);
	}
	
	function doLoadPrescription(){//Load don thuoc
		if(_sudung_lieudung == '1'){
			var	_gridDonThuocHeader="THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,200,0,f,l;Hoạt chất,HOATCHAT,130,0,f,l;Hàm lượng,LIEULUONG,80,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,f,r;SL khả dụng,SO_LUONG_KD,100,0,f,c;SL,SO_LUONG,40,0,e,c;Thành tiền,THANH_TIEN,80,0,f,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,80,0,f,l;Loại TT mới,LOAI_DT_MOI,80,0,f,l;Ghi chú,GHICHUCANHBAO,150,0,e,c,ES;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c; ,ACTION,50,udb,f,l; ,ACTION,30,d,f,l;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l"; //OldValue,OLDVALUE,0,0,f,t,0
		}else{
			var	_gridDonThuocHeader="THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,200,0,f,l;Hoạt chất,HOATCHAT,130,0,f,l;Hàm lượng,LIEULUONG,80,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,f,r;SL khả dụng,SO_LUONG_KD,100,0,f,c;SL,SO_LUONG,40,0,e,c;Thành tiền,THANH_TIEN,80,0,f,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,80,0,f,l;Loại TT mới,LOAI_DT_MOI,80,0,f,l;Ghi chú,GHICHUCANHBAO,150,0,e,c,ES;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c; ,ACTION,50,udb,f,l; ,ACTION,30,d,f,l;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l"; //OldValue,OLDVALUE,0,0,f,t,0
		}		
		
		var opt_ext={footerrow: true, rowNum: 200,rowList: [200]};
		
//		if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TACH_PHIEUTHUOC_KE_SOLUONGLE')==1){
//			GridUtil.init(_gridDonThuoc,"1278","200",_gridCaption,true, _gridDonThuocHeader,false,opt_ext);
//		}else{
//			GridUtil.init(_gridDonThuoc,"1278","200",_gridCaption,false, _gridDonThuocHeader,false,opt_ext);
//		}
		//tuyennx_add_start_20181003 L2HOTRO-10863
		GridUtil.init(_gridDonThuoc,"1278","200",_gridCaption,false, _gridDonThuocHeader,false,opt_ext);
		if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TACH_PHIEUTHUOC_KE_SOLUONGLE')==1)
			$("#grdDONTHUOC").jqGrid('showCol', "LAMTRON");
		else
			$("#grdDONTHUOC").jqGrid('hideCol', "LAMTRON");
		//tuyennx_add_end_20181003 L2HOTRO-10863
		
		$("#"+_gridDonThuoc)[0].toggleToolbar();
		//$("#"+_gridDonThuoc).jqGrid('navGrid',{edit:false,add:false,del:true});
		//Xoa 1 row = keypress
		
		$("#"+_gridDonThuoc).bind("CustomAction", function(e,act,rid){ 
			if(act == 'del'){
				var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rid);
				$("#"+_gridDonThuoc).jqGrid('delRowData',rid);
				var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				_objDrug = jsonGridData;
				_list_thuoc_dake = "";
				if(_objDrug.length > 0){
					for(var i = 0; i < _objDrug.length; i ++){
						if(_list_thuoc_dake == "")
							_list_thuoc_dake = _objDrug[i].THUOCVATTUID;
						else
							_list_thuoc_dake = _list_thuoc_dake +","+ _objDrug[i].THUOCVATTUID;
					}
				}
				$("#"+_gridDonThuoc).jqGrid("clearGridData", true);
				GridUtil.fetchGridData(_gridDonThuoc,jsonGridData);
				loadAll("","");
				var tmp = [];
				for(var k = 0; k <_objTmpThuoc.length; k++){
					if(_objTmpThuoc[k].THUOCVATTUID != row.THUOCVATTUID){
						tmp.push({
							"THUOCVATTUID" : _objTmpThuoc[k].THUOCVATTUID,
							"MAHOATCHAT" : _objTmpThuoc[k].MAHOATCHAT,
							"KETRUNGHOATCHAT" : _objTmpThuoc[k].KETRUNGHOATCHAT
						});
						
						//tmp.push(_objTmpThuoc[k]);
					}
				}
				_objTmpThuoc = tmp;
			}else{
				//console.log("CustomAction act="+act+" rid="+rid);
				var _jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				var rowIds = $("#"+_gridDonThuoc).jqGrid('getDataIDs');
				var _rowUp;
				var _ridDown;
				if(act == "up"){
					_rowUp = rid;
					_rowDown = rid - 1;
					if(rid == 0)
						return false;
				}else{
					if(_jsonGridData.length > 2)
						_rowUp = rid;
					else
						_rowUp = parseInt(rid)+ 1;
					if(_jsonGridData.length > 2)
						_rowDown = parseInt(rid) + 1;
					else
						_rowDown = parseInt(rid);
					if(rid == rowIds.length)
						return false;
				}
				doSwap(_rowUp, _rowDown);
				var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				//jQuery("#"+_gridDonThuoc).jqGrid("clearGridData");
				GridUtil.fetchGridData(_gridDonThuoc, jsonGridData);
			}
			
			return false;
		});
		$("#"+_gridDonThuoc).jqGrid('bindKeys', {"onKeyDelete":function( rowid ) {
			$("#"+_gridDonThuoc).jqGrid('delRowData',rowid);
			var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
			_objDrug = jsonGridData;
			_list_thuoc_dake = "";
			if(_objDrug.length > 0){
				for(var i = 0; i < _objDrug.length; i ++){
					if(_list_thuoc_dake == "")
						_list_thuoc_dake = _objDrug[i].THUOCVATTUID;
					else
						_list_thuoc_dake = _list_thuoc_dake +","+ _objDrug[i].THUOCVATTUID;
				}
			}
			$("#"+_gridDonThuoc).jqGrid("clearGridData", true);
			GridUtil.fetchGridData(_gridDonThuoc,jsonGridData);
			loadAll("","");
		}});
	}
	
	function doSwap(_rowUp, _rowDown){
		var $row1 = $("#"+_rowUp), $row2 = $("#"+_rowDown),
		$next1 = $row1.next(".jqgrow"), $prev1 = $row1.prev(".jqgrow"),
        $next2, $prev2, doOneMove = false, movedOnce = false;

	    if ($row2.is($next1) || $prev1.is($row2)) {
	        doOneMove = true;
	    }
	
	    if ($prev1.length > 0 && !$prev1.is($row2)) {
	        $row2.detach().insertAfter($prev1);
	        movedOnce = true;
	    } else if ($next1.length > 0 && !$next1.is($row2)) {
	        $row2.detach().insertBefore($next1);
	        movedOnce = true;
	    }
	    
	    if (doOneMove && movedOnce) {
	        return;
	    }
	
	    $next2 = $row2.next();
	    $prev2 = $row2.prev();
	    if ($prev2.length > 0 && !$prev2.is($row1)) {
	        $row1.detach().insertAfter($prev2);
	    } else if ($next2.length > 0 && !$next2.is($row1)) {
	        $row1.detach().insertBefore($next2);
	    }
	}
	
	function doAddDrugToJson(_itemDrug, _obj, _opt){
		var _stt;
		if( _opt == 0){
			_stt = 1;
			_obj = []; 
		}
		else{
			_stt = parseInt(_obj.length + 1);
		}
		
		if(_option == '02D014' || _option == '02D016' || _option == '02D018'){
			_macdinh_hao_phi = _itemDrug.ID_DT_MOI;
		}
		
		var r_khothuocid = $("#cboMA_KHO").val();
		if(r_khothuocid = '0' && _option != '02D011' && _chonkho_kedon != '1'){
			r_khothuocid = $("#hidKHOTHUOCTHEOTHUOC").val();
		}
			
		_obj.push({
			"THUOCVATTUID"	: _itemDrug.THUOCVATTUID,
			"STT"			: _stt,
	        "TEN_THUOC" 	: _itemDrug.TEN_THUOC,
	        "HOATCHAT"		: _itemDrug.HOATCHAT,
	        "SO_LUONG"  	: $("#txtSOLUONG_CHITIET").val(),
	        "SO_LUONG_KD"  	: _itemDrug.SLKHADUNG, //L2PT-6627
	        "DONVI_TINH"	: _itemDrug.TEN_DVT,
	        "KHOANMUCID"	: _itemDrug.KHOANMUCID,
	        "DON_GIA"		: _itemDrug.GIA_BAN,
	        "THANH_TIEN"	: "",
	        "BH_TRA"		: "",
	        "ND_TRA"		: "",
	        "DUONG_DUNG"	: _itemDrug.DUONG_DUNG,
	        "DUONGDUNGID" 	: _itemDrug.DUONGDUNGID,
	        "MA_THUOC"		: _itemDrug.MA_THUOC,
	        "NHOM_MABHYT_ID": _itemDrug.NHOM_MABHYT_ID,
	        "GIATRANBHYT"	: _itemDrug.GIATRANBHYT,
	        "ID_DT_CU"		: _doituongbenhnhanid,
	        "ID_DT_MOI"		: _macdinh_hao_phi, 
	        "HUONGDAN_SD"   : $("#hdHUONGDANTHUCHIEN").val(),
	        "TYLEBHYT_TVT"	: _itemDrug.TYLEBHYT_TVT,
	        "DICHVUKHAMBENHID" : _itemDrug.DICHVUKHAMBENHID,
	        "MAHOATCHAT" 	: _itemDrug.MAHOATCHAT,
	        "OLDVALUE"		: $("#txtSOLUONG_CHITIET").val(),
	        "KETRUNGHOATCHAT" : _itemDrug.KETRUNGHOATCHAT,
	        "KHO_THUOCID" : r_khothuocid
	    });
		$("#hdHUONGDANTHUCHIEN").val("");
	}
	
	function doAddItemGridToJson(_item, _obj, i){		
		_obj.push({
			"THUOCVATTUID"	: _item.THUOCVATTUID,
			"STT"			: i,
	        "TEN_THUOC" 	: _item.TEN_THUOC,
	        "HOATCHAT"		: _item.HOATCHAT,
	        "SO_LUONG"  	: _item.SO_LUONG,
	        "SO_LUONG_KD"  	: _item.SLKHADUNG, //L2PT-6627
	        "DONVI_TINH"	: _item.DONVI_TINH,
	        "KHOANMUCID"	: _item.KHOANMUCID,
	        "DON_GIA"		: _item.DON_GIA,
	        "THANH_TIEN"	: _item.THANH_TIEN,
	        "BH_TRA"		: _item.BH_TRA,
	        "ND_TRA"		: _item.ND_TRA,
	        "DUONG_DUNG"	: _item.DUONG_DUNG,
	        "DUONGDUNGID" 	: _item.DUONGDUNGID,
	        "MA_THUOC"		: _item.MA_THUOC,
	        "NHOM_MABHYT_ID": _item.NHOM_MABHYT_ID,
	        "GIATRANBHYT"	: _item.GIATRANBHYT,
	        "ID_DT_CU"		: _doituongbenhnhanid,
	        "ID_DT_MOI"		: _macdinh_hao_phi,
	        "HUONGDAN_SD"   : $("#hdHUONGDANTHUCHIEN").val(),
	        "TYLEBHYT_TVT"	: _item.TYLEBHYT_TVT,
	        "DICHVUKHAMBENHID" : _item.DICHVUKHAMBENHID,
	        "MAHOATCHAT"	: _item.MAHOATCHAT,
	        "OLDVALUE"		: _item.SO_LUONG,
	        "KETRUNGHOATCHAT" : _item.KETRUNGHOATCHAT,
	        "KHO_THUOCID" : _item.KHO_THUOCID
	    });
		$("#hdHUONGDANTHUCHIEN").val("");
	}
	
	function doCalDrugTicket(){
		var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
		//console.log("-----------doCalDrugTicket: "+JSON.stringify(jsonGridData));
		var _totalStart = 0, _totalIns = 0, _totalEnd = 0;
		var _soluong = 0;
		_list_thuoc_dake = "";
		var row_totalStart = 0, row_totalIns = 0, row_totalEnd = 0;
		if(jsonGridData.length > 0){
			for(var i = 0; i < jsonGridData.length; i ++){
				if(_list_thuoc_dake == "")
					_list_thuoc_dake = jsonGridData[i].THUOCVATTUID;
				else
					_list_thuoc_dake = _list_thuoc_dake +","+ jsonGridData[i].THUOCVATTUID;
				var _don_gia = jsonGridData[i].DON_GIA.replace(/,/g,'');
				//console.log(_tyle_bhyt +"--"+ _don_gia+"--"+_doituongbenhnhanid);
				var r = vienphi.tinh_phi_dv(_doituongbenhnhanid,_tyle_bhyt, "0",_don_gia,_don_gia,_don_gia, _don_gia, "0");
				_soluong = parseFloat(jsonGridData[i].SO_LUONG);
				row_totalStart = _soluong * parseInt(r.tong_cp);
				row_totalIns = _soluong*parseInt(r.bh_tra);
				row_totalEnd = _soluong*parseInt(r.nd_tra);
				
				_totalStart = _totalStart + row_totalStart;
				_totalIns =  _totalIns + row_totalIns;
				_totalEnd =  _totalEnd + row_totalEnd;
				
				//console.log(_tyle_bhyt +"--"+ _don_gia)
			}
		}else{
			_totalStart = 0;
			_totalIns =  0;
			_totalEnd =  0;
		}
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { THANH_TIEN	: _totalStart.formatMoney(0) });
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { BH_TRA		: _totalIns.formatMoney(0) });
		$('#'+_gridDonThuoc).jqGrid('footerData', 'set', { ND_TRA		: _totalEnd.formatMoney(0) });

		$('#lblMUCHUONG_BHYT').val(_tyle_bhyt +"%");
		$('#lblMA_BHYT').val(_ma_bhyt);
		$('#lblDT_THANHTOAN').val(_ten_doituong_benhnhan);
	}
	function doInsDonThuoc(r_action){
		if($('#cboMA_KHO').val() == '0' && _option != '02D011' && _chonkho_kedon == '1'){
			DlgUtil.showMsg('Chưa chọn kho thuốc');
			$('#btnSave').prop('disabled', false);
			return;
		}
		if((r_action == "Upd" || r_action == "Add") && $('#txtMACHANDOANICD').val() == '' && r_checkicd == "1"){
			DlgUtil.showMsg('Bạn phải nhập ICD10');			
			$('#btnSave').prop('disabled', false);
			$('#txtMACHANDOANICD').focus();
			return false;
		}
		
		if(_option != '02D011' && _loaitiepnhanid == "0" && _phieudtri_kedon == "1" && $.inArray(_option, _loaikedonthuoc) >= 0 && $('#cboPHIEU_CD').val() == ""){
			DlgUtil.showMsg('Hãy chọn phiếu điều trị');
			$('#btnSave').prop('disabled', false);
			$('#cboPHIEU_CD').focus;
			return false;
		}		
		
		var b_ngaytiepnhan = _ngaytiepnhan.substr(6,4) + _ngaytiepnhan.substr(3,2) + _ngaytiepnhan.substr(0,2) + _ngaytiepnhan.substring(11,13);
		var b_ngaycapthuoc = $('#txtTHOI_GIAN').val().substr(6,4) + $('#txtTHOI_GIAN').val().substr(3,2) + $('#txtTHOI_GIAN').val().substr(0,2) + $('#txtTHOI_GIAN').val().substring(11,13);
		var b_ngaykham_tn = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY').substr(6,4) + jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY').substr(3,2) + jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY').substr(0,2);
		var r_ngaycapthuoc = $('#txtTHOI_GIAN').val().substr(6,4) + $('#txtTHOI_GIAN').val().substr(3,2) + $('#txtTHOI_GIAN').val().substr(0,2);

		// check ngày cấp thuốc với ngày hiện tại
		if(_loaitiepnhanid == 1 && parseInt(r_ngaycapthuoc) > parseInt(b_ngaykham_tn)){
			$('#txtTHOI_GIAN').select();
			$('#txtTHOI_GIAN').focus;
			$('#btnSave').prop('disabled', false);
			DlgUtil.showMsg('Ngày chỉ định không lớn hơn ngày hiện tại');
			return;
		}
		
		if(_loaitiepnhanid == 0 && (parseInt(_ngaykethuocmax.substring(0, 8)) < parseInt(b_ngaycapthuoc.substring(0, 8)))){
			DlgUtil.showMsg('Thời gian chỉ định không được vượt quá số ngày cho phép');
			$('#btnSave').prop('disabled', false);
			return;
		}
		
		//tuyennx_edit_start_20191007 L2PT-9546 L2PT-9545
		if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'KETHUOC_BSKE_DOITUONG').includes(_loaitiepnhanid) &&_bacsi_ke == '1' 
			&& ($('#cboBACSIID').val() == '-1' || $('#cboBACSIID').val() == undefined)){
		//tuyennx_edit_end_20191007 L2PT-9546 L2PT-9545
			DlgUtil.showMsg('Chọn bác sỹ kê đơn');
			$('#cboBACSIID').focus();
			$('#btnSave').prop('disabled', false);
			return;
		}

		// check ngày cấp thuốc với ngày tiep nhan
	    var b_ngaytiepnhan = _ngaytiepnhan.substr(6,4) + _ngaytiepnhan.substr(3,2) + _ngaytiepnhan.substr(0,2) + _ngaytiepnhan.substring(11,13)+ _ngaytiepnhan.substring(14,16)+_ngaytiepnhan.substring(17,19);
	    var b_ngaycapthuoc = $('#txtTHOI_GIAN').val().substr(6,4) + $('#txtTHOI_GIAN').val().substr(3,2) + $('#txtTHOI_GIAN').val().substr(0,2) + $('#txtTHOI_GIAN').val().substring(11,13)+ $('#txtTHOI_GIAN').val().substring(14,16)+$('#txtTHOI_GIAN').val().substring(17,19);
		if(parseInt(b_ngaycapthuoc) < parseInt(b_ngaytiepnhan)){
			$('#txtTHOI_GIAN').select();
			$('#txtTHOI_GIAN').focus;
			DlgUtil.showMsg('Ngày giờ chỉ định không nhỏ hơn ngày giờ tiếp nhận');
			$('#btnSave').prop('disabled', false);
			return;
		}
		
		//tuyennx_add_start_20190702 L2PT-6609
//		if($("#cboDONTHUOCVT").val() != 0 && $("#cboDONTHUOCVT").val() != null){
//			//var _tg_ke = moment($("#txtTHOI_GIAN").val().trim(),'DD/MM/YYYY HH24:MI:SS');
//			var _tg_tra = $('#txtTG_DUNG').val().substr(6,4) + $('#txtTG_DUNG').val().substr(3,2) + $('#txtTG_DUNG').val().substr(0,2) + $('#txtTG_DUNG').val().substring(11,13)+ $('#txtTG_DUNG').val().substring(14,16)+$('#txtTG_DUNG').val().substring(17,19);
//			
//			var sql_par = [];
//			sql_par.push({"name":"[0]", value:$('#cboDONTHUOCVT').val()});
//			_tg_kethuoc = jsonrpc.AjaxJson.getOneValue('GET.THOIGIAN.LINH', sql_par);
//			
////			var _tg_ke = $('#cboDONTHUOCVT'+ " option:selected").text().split('-')[1] ;
//			var i_ngaycapthuoc = $('#txtTHOI_GIAN').val().substr(6,4) + $('#txtTHOI_GIAN').val().substr(3,2) + $('#txtTHOI_GIAN').val().substr(0,2) + $('#txtTHOI_GIAN').val().substring(11,13)+ $('#txtTHOI_GIAN').val().substring(14,16)+$('#txtTHOI_GIAN').val().substring(17,19);
//			if(parseInt(i_ngaycapthuoc) < parseInt(_tg_kethuoc) || parseInt(_tg_tra) < parseInt(_tg_kethuoc)){
//				//$("#txtTHOI_GIAN").focus();
//				DlgUtil.showMsg("Thời gian trả đơn phải lớn hơn thời gian lĩnh thuốc!");
//				$('#btnSave').prop('disabled', false);
//				return false;
//			}
//		}
		//tuyennx_add_end_20190702 L2PT-6609

		if(parseInt($('#txtSONGAY_KE').val()) > parseInt(_songaykemax)){
			$('#txtSONGAY_KE').select();
			$('#txtSONGAY_KE').focus;
			DlgUtil.showMsg('Số ngày kê thuốc không lớn hơn '+_songaykemax+' ngày');
			$('#btnSave').prop('disabled', false);
			return;
		}
		
		var isCheckSL = 0;
		var isCheckLOAIDT = 0;
		var _print = false;
		var jsonGridData = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
		var rowIds = $('#'+_gridDonThuoc).jqGrid('getDataIDs');
		for (var k = 0; k < rowIds.length; k++) {
			var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rowIds[k]);
			
			if($('#'+_gridDonThuoc).find("input[id*='SO_LUONG']").length > 0){
				isCheckSL = 1;
	    	}
			
			if(row.ID_DT_MOI == null || row.ID_DT_MOI == ''){
				isCheckLOAIDT = 1;
			}
			//tuyennx_add_start_20190821 L2PT-27340 //L2PT-27468
			if(parseFloat(row.SO_LUONG)  < 0){
				DlgUtil.showMsg('Tồn tại thuốc trả có số lượng âm!');
				$('#btnSave').prop('disabled', false);
	    		return false;
			}
			if(!row.SO_LUONG){
				DlgUtil.showMsg('Chưa nhập số lượng thuốc trả!');
				$('#btnSave').prop('disabled', false);
	    		return false;
			}
			//tuyennx_add_end_20190821 L2PT-27340
		}
		
		/*for (var k = 0; k < rowIds.length; k++) {
			var row = $("#"+_gridDonThuoc).jqGrid('getRowData',rowIds[k]);			
			if($.inArray(row.LOAITVTID, ["0","3","6","7","8","9"]) >= 0 && _option == '02D010'){
				if(_sudung_lieudung == '1' && row.LIEUDUNG == ""){
					DlgUtil.showMsg('Thuốc kê trong danh sách không có liều dùng');
					$('#btnSave').prop('disabled', false);
		    		return false;
				}else{
					var temp = row.HUONGDAN_SD.split('@');
					if(temp[2] == ""){
						DlgUtil.showMsg('Thuốc kê trong danh sách không có cách dùng');
						$('#btnSave').prop('disabled', false);
			    		return false;
					}
				}
			}
		}*/
		
		if(isCheckSL == '1'){
			DlgUtil.showMsg('Tồn tại trường số lượng đang sửa trong đơn'+_lbl_text+'!');
    		$('#btnSave').prop('disabled', false);
    		return false;
		}
		
		if(isCheckLOAIDT == '1'){
			DlgUtil.showMsg('Thuốc chưa có loại đối tượng thanh toán');
			$('#btnSave').prop('disabled', false);
    		return false;
		}
		//tuyennx_add_start_20180605
		var rowKeys_select = $("#"+_gridDonThuoc).getGridParam('selarrrow');
		for (var k = 0; k < rowKeys_select.length; k++) {
			var rowObject = $("#"+_gridDonThuoc).getRowData(rowKeys_select[k]);
			if(!rowObject.SO_LUONG.includes(".")){
				DlgUtil.showMsg('Tồn tại thuốc được check nhưng số lượng kê chẵn');
				$('#btnSave').prop('disabled', false);
	    		return false;
			}
		}
		//tuyennx_add_end_20180605
		
		if(jsonGridData.length > 0){
			_jsonDonThuoc.DS_THUOC 			= jsonGridData;
			_jsonDonThuoc.KHAMBENHID 		= _khambenhid;
			_jsonDonThuoc.KHOAID 			= _khoaId;
			_jsonDonThuoc.PHONGID 			= _phongId;
			_jsonDonThuoc.MA_CHANDOAN		= $("#txtMACHANDOANICD").val();
			//tuyennx_add_start_20170724 
			_jsonDonThuoc.GHICHU_BENHCHINH		= $("#txtGHICHU_BENHCHINH").val();
			//tuyennx_add_end_20170724 
			_jsonDonThuoc.CHANDOAN 			= $("#txtTENCHANDOANICD").val();
			_jsonDonThuoc.CHANDOAN_KT 		= $("#txtTENCHANDOANICD_KT").val();
			_jsonDonThuoc.DUONG_DUNG 		= $("#cboDUONG_DUNG").val();
			_jsonDonThuoc.KIEUKEDON			= _loaikedon+"";
			
			if(_bacsi_ke == '1' && $("#cboBACSIID").val() != '-1' && $("#cboBACSIID").val() != null){
				_jsonDonThuoc.NGUOIDUNG_ID 		= $("#cboBACSIID").val();
			}else{
				_jsonDonThuoc.NGUOIDUNG_ID 		= _user_id;
			}
			
			_jsonDonThuoc.TEMP_CODE 		= $("#txtTEXT_TEMP").val();
			_jsonDonThuoc.DICHVUCHA_ID 		= _dichvucha_id;
			_jsonDonThuoc.DOITUONG_BN_ID 	= _doituongbenhnhanid;
			_jsonDonThuoc.TYLE_BHYT 		= _tyle_bhyt;
			_jsonDonThuoc.NGAY_BHYT_KT 		= _ngay_bhyt_kt;
			_jsonDonThuoc.DUONGDUNGE 		= '';
			_jsonDonThuoc.OPTION 			= _option;

			//_jsonDonThuoc.HINH_THUC_KE		= _option;
			//_jsonDonThuoc.NHOM_MABHYT_ID 	= _nhom_mabhyt_id;
			if(_option != '02D011') 
				_jsonDonThuoc.KHO_THUOCID = $("#cboMA_KHO").val();
			else
				_jsonDonThuoc.KHO_THUOCID 	= "";
			_jsonDonThuoc.NGAYMAUBENHPHAM 	= $("#txtTHOI_GIAN").val();
			_jsonDonThuoc.NGAYMAUBENHPHAM_SUDUNG = $("#txtTG_DUNG").val();
			if($("#cboPHIEU_CD").val() !== null)
				_jsonDonThuoc.PHIEUDIEUTRI_ID = $("#cboPHIEU_CD").val();
			else
				_jsonDonThuoc.PHIEUDIEUTRI_ID = "0";
			_jsonDonThuoc.INS_TYPE 			= _loai_don;
			_jsonDonThuoc.I_ACTION 			= r_action;
			_jsonDonThuoc.MAUBENHPHAMID 	= _maubenhpham_id;
			_jsonDonThuoc.SONGAY_KE 	= $("#txtSONGAY_KE").val();
			_jsonDonThuoc.YKIENBACSY 	= $("#txtLOIDANBS").val();
			
			if(_option == '02D017' || _option == '02D018'){
				_jsonDonThuoc.SLTHANG 		= $("#txtSLTHANG").val();
			}else{
				_jsonDonThuoc.SLTHANG 		= '1';
			}			
			_jsonDonThuoc.NGAYHEN 		= $("#txtTG_HENKHAM").val();
			
			if($('#chkCapPhieuHenKham').is(':checked')){
				_jsonDonThuoc.PHIEUHEN 		= '1';
			}else{
				_jsonDonThuoc.PHIEUHEN 		= '0';
			}
			
			_jsonDonThuoc.MAUBENHPHAMCHA_ID = _phieutraid;
			
			//_thoigian_vaovien
			var _TG_KEDON = moment($("#txtTHOI_GIAN").val().trim(),'DD/MM/YYYY');
			var _TG_SUDUNG_THUOC = moment($("#txtTG_DUNG").val().trim(),'DD/MM/YYYY');
			var _tg_vaovien = moment(_thoigian_vaovien,'DD/MM/YYYY');
			if(_tg_vaovien > _TG_KEDON){
				$("#txtTHOI_GIAN").focus();
				DlgUtil.showMsg("Thời gian kê đơn phải lớn hơn thời gian vào viện!");
				$('#btnSave').prop('disabled', false);
				return false;
			}
			if(_TG_KEDON > _TG_SUDUNG_THUOC){
				$("#txtTG_DUNG").focus();
				DlgUtil.showMsg("Thời gian kê đơn phải nhỏ hơn thời gian vào viện!");
				$('#btnSave').prop('disabled', false);
				return false;
			}
			// uppercase ma chan doan de duyet ke toan khong bi loi;
			_jsonDonThuoc.MA_CHANDOAN = _jsonDonThuoc.MA_CHANDOAN.toUpperCase(); 
			
			_jsonDonThuoc.TRATHUOC_DUOC = _opts.TRATHUOC_DUOC; //L2PT-85844
			
			var rsldt = checkCongLamDungThuocBYT(jsonGridData);
			if(rsldt ==1)
				return; 
			var rs = dayCongBYT();
			if(rs ==1)
				return; 
			
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D010.12", JSON.stringify(_jsonDonThuoc));
			var rets = ret.split(',');
			_badaingay = rets[1];
			if(rets[0] >= 1){
			   var msg="";
			   if(r_action != "SAVE_TEMP"){
				   if(_opts.option=='02D010'){
					   _print = true;
					   msg= "Tạo phiếu thuốc thành công";
				   }else if(_opts.option=='02D014'){
					   _print = false;
					   msg= "Tạo phiếu trả thuốc thành công";
				   }else if(_opts.option=='02D015'){
					   _print = true;
					   msg= "Tạo phiếu vật tư thành công";
				   }else if(_opts.option=='02D016'){
					   _print = false;
					   msg= "Tạo phiếu trả vật tư thành công";
				   }else if(_opts.option=='02D011'){
					   _print = true;
					   msg= "Tạo đơn thuốc mua ngoài thành công";
					   //tuyennx_add_start
					   _daybtkd(rets[0],r_action);
       		   			//tuyennx_add_end
				   }else if(_opts.option=='02D017'){
					   _print = true;
					   msg= "Tạo đơn thuốc YHCT thành công";
				   }else if(_opts.option=='02D018'){
					   _print = true;
					   msg= "Tạo đơn thuốc trả YHCT thành công";
				   }else if(_opts.option=='02D019'){
					   _print = true;
					   msg= "Tạo đơn thuốc nhà thuốc thành công";
					 //tuyennx_add_start
					   _daybtkd(rets[0],r_action);
       		   			//tuyennx_add_end
				   }
				   
				   var show = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'NGT_MO_POPUP_LUUDONTHUOC');
				   if(show == '1') {
					   $("#btnXuTri").trigger("click");
				   }else if(show == '2'){
					   $("#btnXuTri").trigger("click");
				   }
				   
				   if(_print){
					   if(_loaitiepnhanid == "1" && (_loainhommaubenhpham_id == "7" || _loainhommaubenhpham_id == '-1')){
							   _inDonThuoc(rets[0], _opts.phongId);						   
					   }else if(_indonthuoc_noitru == "1" && (_loainhommaubenhpham_id == "7" || _loainhommaubenhpham_id == '-1')){
						   _inDonThuoc(rets[0],_opts.phongId);
					   }
				   }				   
				   
				 //tuyennx_add_start HISL2TK-296
				   if(_loaitiepnhanid == 0 && _sudungthuoc12sao == '1'){
					   
			        	var dt_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU.LAYDSTHUOCSAO",rets[0]);
			    		for(var i = 0; i< dt_ar.length; i++){
			    			var data = dt_ar[i];
			    			if(data.LOAIHOICHAN == 1){
			    				var par = [ 
								{name:'dichvukhambenhid',type:'String',value:data.DICHVUKHAMBENHID}
								];
								
								openReport('window', 'BIENBANHOICHAN_THUOC_1SAO_TT40_A4', 'pdf', par);
			    			}
			    			else{
			    				var par = [ 
								{name:'dichvukhambenhid',type:'String',value:data.DICHVUKHAMBENHID}
								];
								
								openReport('window', 'BIENBANHOICHAN_THUOC_2SAO_TT40_A4', 'pdf', par);
			    			}
			    		}
				   }
				 //tuyennx_add_end
				   
				   $('#btnSave').prop('disabled', true);
				   $('#btnAdd').prop('disabled', true);
				   $('#btnDTMau').prop('disabled', true);
				   $('#btnDTCu').prop('disabled', true);
				   $('#btnTConSD').prop('disabled', true);
				   //tuyennx_add_start_20170816 yc L2DKBD-195
				   $('#btnTDiUng').prop('disabled', true);
				   //tuyennx_add_end_20170816 yc L2DKBD-195
				 //tuyennx_add_start day hssk
					var HIS_KETTHUCKHAM_KHICODONTHUOC = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','HIS_KETTHUCKHAM_KHICODONTHUOC');
					if(HIS_KETTHUCKHAM_KHICODONTHUOC == 1){
						dayCongHSSK(_khambenhid);
					}
					//tuyennx_add_end
				   _luu = 1;
				   if(_timkiem_bn_kedon_ntu != '1' && _loaitiepnhanid == 0){					   
					   EventUtil.raiseEvent("assignSevice_saveTaoPhieuThuoc",{msg:msg,option:_opts.option});
				   }else{
					   $('#btnSave').prop('disabled', true);
					   $('#btnAdd').prop('disabled', false);
					   $('#btnDTMau').prop('disabled', false);
					   $('#btnDTCu').prop('disabled', false);
					   $('#btnTConSD').prop('disabled', false);
					   //tuyennx_add_start_20170816 yc L2DKBD-195
					   $('#btnTDiUng').prop('disabled', false);
					   $('#msgCNKQ').text("Tạo đơn thuốc thành công mã BA: "+$('#lblPATIENTCODE').val());
					   DlgUtil.showMsg("Tạo phiếu trả thuốc thành công");
				   }
			   }else{
				   DlgUtil.showMsg("Tạo phiếu trả thuốc thành công");
			   }   
			}
			else if (ret == '-1'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lưu không thành công!");
			}
			//tuyennx_add_start_20170727  y/c HISL2BVDKHN-247
			else if (ret == 'ngaydichvu'){
				DlgUtil.showMsg('Bệnh nhân có thời gian chỉ định dịch vụ lớn hơn thời gian ra viện đã cấp thuốc thành công nhưng chưa kết thúc khám');
			}
			//tuyennx_add_end_20170727 
			else if (ret == '-2'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Có lỗi khi chuyển phiếu!");
			}/*else if (ret == -3){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lỗi khi yêu cầu cấp"+_lbl_text+"!");
			}*/else if (ret == '-4'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lỗi khi hủy"+_lbl_text+"!");
			}else if (ret == '-5'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lỗi khi tính giá dịch vụ cao vượt mức trần Bảo hiểm!");
			}else if (ret == '-6'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg("Lỗi khi yêu cầu trả"+_lbl_text+"!");
			}else if (ret == '-7'){
				DlgUtil.showMsg("Lỗi do nhập sai mã ICD10!");
				$('#btnSave').prop('disabled', false);
				$("#txtMACHANDOANICD").trigger("focus");
			}else if (ret == 'cophieudangsua'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg('Bệnh nhân có phiếu CLS/Đơn thuốc đang sửa, không kết thúc khám được.');
			}else if (ret == 'daduyetvp'){
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg('Bệnh nhân đã duyệt thanh toán, không kê đơn được.');
			} 
			else{
				$('#btnSave').prop('disabled', false);
				DlgUtil.showMsg(ret);
			}
		}else{
			$('#btnSave').prop('disabled', false);
			DlgUtil.showMsg("Bạn chưa nhập"+_lbl_text+" cho đơn thuốc!");
		}
	}	
	
	//tuyennx_add_start tích hợp cổng dữ liệu y tế
	function dayCongBYT(){
		var sql_par=[];
		sql_par.push({"name":"[0]","value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')});
		var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT",sql_par);
		vsothutu = JSON.parse(vsothutu);
		if (vsothutu[0].BYTDAYDL == "1"){
			var _parram = ["1"];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",_parram.join('$'));
			var data_bv = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETTT_BV",[]);
			
			var objCHECKIN = new Object(); 
			objCHECKIN.MA_LK = $("#hidHOSOBENHANID").val();
			objCHECKIN.Sender_Code = _opts.hospital_code;
			objCHECKIN.Sender_Name = "";
			objCHECKIN.Action_Type = "1";				// 0: bắt đầu khám, 1: kết thúc khám
			objCHECKIN.Transaction_Type = "M0001";
			objCHECKIN.MABENHVIEN = data_ar[0].I_U1;
			objCHECKIN.MA_THE = $("#lblMA_BHYT").val();
			objCHECKIN.MA_LOAI_KCB = "1";
			objCHECKIN.TEN_BENH = $("#txtTENCHANDOANICD").val();
			objCHECKIN.MA_BENH = $("#txtMACHANDOANICD").val();
			objCHECKIN.HO_TEN = $("#lblPATIENTNAME").val();
			objCHECKIN.NAM_SINH = $("#lblBIRTHDAY_YEAR").val();
//			objCHECKIN.GIOI_TINH ="2";
//			objCHECKIN.DIA_CHI ="Xã Phú Sơn-Huyện Tân Kỳ-Nghệ An";
//			objCHECKIN.NGAYGIOVAO = "201803121530";
//			objCHECKIN.TINHTRANGVAOVIEN = "1";
			objCHECKIN.NGAYHETTHUOC = jsonrpc.AjaxJson.getSystemDate('YYYYMMDD');

			objCHECKIN.NGAYGIORA = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');
			var objHeader = XML_BYT_TaoHeaderKTK(objCHECKIN,"1"); 										// tao doi tuong header; 
			var objIn = XML_BYT_TaoTheCHECKIN(objCHECKIN); 									// tao the 
			var obj3 = XML_BYT_TaoKhung(objHeader, objIn, "1"); 											// tao JSON full => XML

			var resultCongBYT = ajaxSvc.CongDLYTWS.guiTTKTK(
					vsothutu[0].BYTURL
					, data_ar[0].I_U1
					, data_ar[0].I_P1
					, data_ar[0].I_U1						// csytid
					, data_bv[0].MADIAPHUONG							// ma tinh 
					, "1", obj3);		
			var rets = resultCongBYT.split(';');
			if(rets[0] != '0'){
				DlgUtil.showMsg("Lỗi đẩy dữ liệu cổng y tế: "+rets[1]);
				if(vsothutu[0].BYTSTOPCHUCNANG == "1")
					return 1;
			}			
		}
		return 0;
	}
	
	function checkCongLamDungThuocBYT(jsonGridData){
		var sql_par=[];
		sql_par.push({"name":"[0]","value": jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY')});
		var vsothutu = jsonrpc.AjaxJson.ajaxExecuteQueryO("NGT_STT_DT",sql_par);
		vsothutu = JSON.parse(vsothutu);
		if (vsothutu[0].BYTDAYDL == "1"){
			var _parram = ["1"];
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",_parram.join('$'));
			
			var objCHECKIN = new Object(); 
			objCHECKIN.MA_LK = $("#hidHOSOBENHANID").val();
			objCHECKIN.Sender_Code = _opts.hospital_code;
			objCHECKIN.Sender_Name = "";
			objCHECKIN.Action_Type = "1";				// 0: bắt đầu khám, 1: kết thúc khám
			objCHECKIN.Transaction_Type = "M0001";
			objCHECKIN.MABENHVIEN = data_ar[0].I_U1;
			objCHECKIN.MA_THE = $("#lblMA_BHYT").val();
			objCHECKIN.MA_LOAI_KCB = "1";
			objCHECKIN.TEN_BENH = $("#txtTENCHANDOANICD").val();
			objCHECKIN.MA_BENH = $("#txtMACHANDOANICD").val();
			objCHECKIN.NGAYHETTHUOC = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');
			objCHECKIN.NGAYGIORA = jsonrpc.AjaxJson.getSystemDate('YYYYMMDDHh24mi');			
			var objTTChung = new Object();
			objTTChung.MA_LK = $("#hidMAHOSOBENHAN").val();
			objTTChung.MABENHVIEN = data_ar[0].I_U1;
			objTTChung.MA_THE = $("#lblMA_BHYT").val();
			objTTChung.NGAY_SINH = $("#hidNGAYSINH").val();
			objTTChung.GIOI_TINH = $("#hidGIOITINHID").val();
			
			var objDSThuoc = [];
			for(var i=0;i< jsonGridData.length;i++){
				var objThuoc = new Object();
				objThuoc.STT = i+1;
				objThuoc.MA_THUOC = jsonGridData[i].MA_THUOC;
				objThuoc.MA_NHOM = jsonGridData[i].NHOM_MABHYT_ID;
				objDSThuoc.push(objThuoc);
			}

			var objIn = new Object();
			var objThuoc = new Object();
			objThuoc.CHI_TIET_THUOC = objDSThuoc;
			objIn.THONGTINCHUNG = objTTChung;
			objIn.DSACH_CHI_TIET_THUOC = objThuoc;
			var objHeader = XML_BYT_TaoHeader(objCHECKIN); 										// tao doi tuong header; 
			var obj3 = XML_BYT_TaoKhung(objHeader, objIn, "8"); 											// tao JSON full => XML

			var resultCongBYT = ajaxSvc.CongDLYTWS.lamdungthuoc(
					vsothutu[0].BYTURL
					, data_ar[0].I_U1
					, data_ar[0].I_P1
					, obj3);		
			var rets = resultCongBYT.split(';');
			if(rets[0] != '0'){
				DlgUtil.showMsg("Thông tin check cổng y tế: "+rets[1]);
				if(vsothutu[0].BYTSTOPCHUCNANG == "1")
					return 1;
			}			
		}
		return 0;
	}
	//đẩy cổng bộ y tế với đơn mua ngoài, đơn thuốc nhà thuốc
	function _daybtkd(ret_maubenhpham_id, r_action){	
		var BTKD_DAY_BYT = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA','BTKD_DAY_BYT');
		if(BTKD_DAY_BYT == 1){
//			var sql_par = [];
//			sql_par.push({"name":"[0]", value:ret_maubenhpham_id});
//	   		jsonrpc.AjaxJson.ajaxExecuteQuery("BTKD.UPDATE.MADT", sql_par);
	   		var objSend = new Object(); 
	   		var objData = new Object(); 
	   		var objHeader = new Object(); 
	   		var objBody = new Object();
	   		var objSeccurity = new Object();
	   		var objDonThuoc = new Object();
	   		var objThongTinBN = new Object();
	   		var objDSChiTietThuoc = new Object(); 
	   		
	   		var BTKD_WS_URL = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'BTKD_WS_URL');
	   		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT02K047.TTTK",[ret_maubenhpham_id].join('$'));
	   		
	   		var data_btkd = jsonrpc.AjaxJson.ajaxCALL_SP_O("NGT.GETDATA.BTKD",[ret_maubenhpham_id].join('$'));
	   		//tao header
	   		objHeader.SENDER_CODE = _opts.hospital_code;
	   		objHeader.SENDER_NAME = data_ar[0].TEN_CSYT;
	   		objHeader.TRANSACTION_TYPE = "M0002";
	   		objHeader.TRANSACTION_NAME = "FTP";
	   		if(r_action == "Upd")
	   			objHeader.ACTION_TYPE = "1";
	   		else
	   			objHeader.ACTION_TYPE = "0";
	   		//tao don thuoc
	   		objDonThuoc.DON_THUOC_ID =data_btkd[0].MAUBENHPHAMID;
	   		objDonThuoc.MA_DON_THUOC =_opts.hospital_code+ data_btkd[0].MA_DON_THUOC;
	   		objDonThuoc.MA_LUOT_KHAM =data_btkd[0].HOSOBENHANID;
	   		objDonThuoc.LOAI_DON_VI ="1";
	   		objDonThuoc.MA_XAC_THUC ="";
	   		objDonThuoc.MA_DON_VI =_opts.hospital_code;
	   		objDonThuoc.MA_DINH_DANH =data_btkd[0].HSSK_MADD;
	   		objDonThuoc.HO_TEN_BN =data_btkd[0].TENBENHNHAN;
	   		objDonThuoc.NGAY_SINH =data_btkd[0].NGAYSINH;
	   		objDonThuoc.GIOI_TINH =data_btkd[0].GIOITINHID;
	   		objDonThuoc.MA_THE =data_btkd[0].MA_BHYT;
	   		objDonThuoc.MA_BENH =data_btkd[0].MACHANDOAN;
	   		objDonThuoc.TEN_BENH =data_btkd[0].CHANDOAN;
	   		objDonThuoc.SD_TU_NGAY =data_btkd[0].NGAYMAUBENHPHAM_SUDUNG;
	   		objDonThuoc.SD_DEN_NGAY =data_btkd[0].SD_DEN_NGAY;
	   		objDonThuoc.HD_SD =data_btkd[0].LOIDANBACSI;
	   		objDonThuoc.SO_THANG =data_btkd[0].SLTHANG;
	   		objDonThuoc.NGAY_CAP =data_btkd[0].NGAYMAUBENHPHAM;
	   		objDonThuoc.MA_BAC_SY =data_btkd[0].MA_BAC_SI;
	   		objDonThuoc.TEN_BAC_SY =data_btkd[0].OFFICER_NAME;
	   		objDonThuoc.SO_GPHN ="";
	   		objDonThuoc.MA_KHOA =data_btkd[0].KHOAID;
	   		objDonThuoc.NHA_THUOC_ID ="";
	   		objDonThuoc.MA_NHA_THUOC ="";
	   		objDonThuoc.TEN_NHA_THUOC ="";
	   		objDonThuoc.TEN_DUOC_SY ="";
	   		objDonThuoc.NGAY_BAN ="";
	   		objDonThuoc.MA_TINH =data_btkd[0].MA_TINH;
	   		
	   	    //tao obj benh nhan
	   		objThongTinBN.MABN = data_btkd[0].MA_TINH;
	   		objThongTinBN.MATINH_KHAISINH = data_btkd[0].MATINH_KHAISINH;
	   		objThongTinBN.SOCMND = data_btkd[0].SOCMTND;
	   		objThongTinBN.NGAYCAP = data_btkd[0].NGAYCAPCMND;
	   		objThongTinBN.NOICAP = "";
	   		objThongTinBN.DIACHI_THUONGTRU = "";
	   		objThongTinBN.MATINH_THUONGTRU = "";
	   		objThongTinBN.MAHUYEN_THUONGTRU = "";
	   		objThongTinBN.MAXA_THUONGTRU = "";
	   		objThongTinBN.MATHONXOM_THUONGTRU = "";
	   		objThongTinBN.DIACHI_HIENTAI = data_btkd[0].DIACHI;
	   		objThongTinBN.MATINH_HIENTAI = data_btkd[0].MATINH_KHAISINH;
	   		objThongTinBN.MAHUYEN_HIENTAI = data_btkd[0].MAHUYEN_HIENTAI;
	   		objThongTinBN.MAXA_HIENTAI = data_btkd[0].MAXA_HIENTAI;
	   		objThongTinBN.MATHONXOM_HIENTAI = "";
	   		objThongTinBN.DIENTHOAI_CD = data_btkd[0].SDTBENHNHAN;
	   		objThongTinBN.DIENTHOAI_DD = "";
	   		objThongTinBN.EMAIL = "";
	   		
	   		
	   		var objChiTietThuoc = [];
			for(var i=0;i< data_btkd.length;i++){
				var objThuoc = new Object();
				objThuoc.DON_THUOC_ID = data_btkd[i].MAUBENHPHAMID;
				objThuoc.STT = data_btkd[i].THUTU;
				objThuoc.MA_NHOM = "";
				objThuoc.SO_DANG_KY = data_btkd[i].SODANGKY;
				objThuoc.MA_THUOC = data_btkd[i].MA;
				objThuoc.TEN_THUOC = data_btkd[i].TEN;
				objThuoc.DON_VI_TINH = data_btkd[i].TEN_DVT;
				objThuoc.HAM_LUONG = data_btkd[i].LIEULUONG;
				objThuoc.DUONG_DUNG = data_btkd[i].DUONGDUNG;
				objThuoc.LIEU_DUNG = data_btkd[i].LIEUDUNG;
				objThuoc.DONG_GOI = data_btkd[i].DONGGOI;
				objThuoc.SO_LUONG = data_btkd[i].SOLUONG;
				objThuoc.DON_GIA = data_btkd[i].TIEN_CHITRA;
				objThuoc.TYLE_TT = "100";
				objThuoc.MUC_HUONG = "0";
				objThuoc.THANH_TIEN = data_btkd[i].THANH_TIEN;
				objThuoc.T_NGUON_KHAC = "0";
				objThuoc.T_BNTT = "0";
				objThuoc.GHI_CHU = "";
				objChiTietThuoc.push(objThuoc);

			}
			objDSChiTietThuoc.CHI_TIET_THUOC = objChiTietThuoc;
			
	   		objBody.DON_THUOC = objDonThuoc;
	   		objBody.THONGTINBENHNHAN = objThongTinBN;
	   		objBody.DSACH_CHI_TIET_THUOC = objDSChiTietThuoc;
	   		
	   		objData.HEADER = objHeader;
	   		objData.BODY = objBody;
	   		objData.SECURITY = objSeccurity;
	   		objData.HEADER = objHeader;
	   		objSend.DATA = objData;
	   		
	   		
	   	
			var x2js = new X2JS();
			objSend = JSON.stringify(objSend);
			var obj =  x2js.json2xml_str($.parseJSON(objSend));
			obj = obj.replace(/&lt;/g,'<').replace(/&gt;/g,'>').replace(/&amp;/g,'&');
			obj = btoa(unescape(encodeURIComponent(obj)));//atob(obj);
			
			//gui lay ma dinh danh va MK
			// 20190926 fix ATBM bo truyen url_ws
			var resultCongBTKD = ajaxSvc.CongBTKDWS.tiepNhanDonThuoc_new(_company_id
					, data_ar[0].I_U1
					, data_ar[0].I_P1,
					obj); 	
			var rs = resultCongBTKD.split(';');
			if(rs[0] != 0){
				DlgUtil.showMsg("Lỗi đẩy dữ liệu bán thuốc kê đơn cổng bộ y tế: " +rs[1]);
				setTimeout(function(){
					 }, 2000);
				
				//return 1;
			}
		}
	}
	//tuyennx_add_end
	
	function _inDonThuoc(ret_maubenhpham_id, phongid){	
			var _type = 'pdf';        	
	    	var pars = ['HIS_FILEEXPORT_TYPE']; 
			var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
			if(data_ar != '-1'){
				_type = data_ar;
			}
	    	
		    var par = [ {
				name : 'maubenhphamid',
				type : 'String',
				value : ret_maubenhpham_id
			}];
		    if(_opts._hospital_id=='913'){
		    	openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5_913", "pdf", par);
	        } else{
	 		 //lay loai thuoc
	 		 var _par_loai = [ret_maubenhpham_id];						
	 		 var _loaithuoc=0;    								
			 var arr_loaithuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_LOAITHUOC",_par_loai.join('$'));	
			 if (arr_loaithuoc != null && arr_loaithuoc.length > 0) {
				var pars = ['PHONG_TUDONG_IN']; 
				var dc_phong = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', pars.join('$'));
				var dc_phongs = dc_phong.split(',');
				for(var i=0;i< arr_loaithuoc.length;i++){
					_loaithuoc=arr_loaithuoc[i].LOAI;
					   if(_loaithuoc==3){
							//thuoc dong y --DONTHUOCTHANG_NGOAITRU
						   if(_tudongindt == '1' || $.inArray( phongid, dc_phongs) >= 0){
							   var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
								rpName += $('#lblPATIENTCODE').val(); 
								rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
								rpName += "."+_type; 
								CommonUtil.inPhieu('window', 'NGT020_DONTHUOCTHANGNGOAITRU', _type, par,rpName);
						   }else{
							   openReport('window', "NGT020_DONTHUOCTHANGNGOAITRU", "pdf", par);
						   }
						 }else if(_loaithuoc==6){
							 //thuoc huong than 
							 if(_tudongindt == '1' || $.inArray( phongid, dc_phongs) >= 0){
								   var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
									rpName += $('#lblPATIENTCODE').val(); 
									rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
									rpName += "."+_type;  
									CommonUtil.inPhieu('window', 'NGT013_DONTHUOCHUONGTHAN_TT052016_A5', _type, par,rpName);
							   }else{
								   openReport('window', "NGT013_DONTHUOCHUONGTHAN_TT052016_A5", "pdf", par); 
							   }
						 }else if(_loaithuoc==7){
							 //don thuoc gay nghien
							if(_tudongindt == '1' || $.inArray( phongid, dc_phongs) >= 0){
								var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
								rpName += $('#lblPATIENTCODE').val(); 
								rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
								rpName += "."+_type;
								CommonUtil.inPhieu('window', 'NGT013_DONTHUOCGAYNGHIEN_TT052016_A5', _type, par,rpName);
						   }else{
							   openReport('window', "NGT013_DONTHUOCGAYNGHIEN_TT052016_A5", "pdf", par);
						   }
						 }else{
							 // don thuoc khac
							if( _opts._hospital_id=='944'){
								 var _khothuoc=0;
								 var arr_khothuoc= jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D033_KHOTHUOC",_par_loai.join('$'));
								 if (arr_khothuoc != null && arr_khothuoc.length > 0) {
									 for(var i2=0;i2< arr_khothuoc.length;i2++){
										 _khothuoc=arr_khothuoc[i2].KHOTHUOCID;
										 if(_khothuoc==1){
											 if(_tudongindt == '1' || $.inArray( phongid, dc_phongs) >= 0){
													var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
													rpName += $('#lblPATIENTCODE').val(); 
													rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
													rpName += "."+_type; 
													CommonUtil.inPhieu('window', 'NGT006_DONTHUOC1L_17DBV01_TT052016_A5_944', _type, par,rpName);
											   }else{
												   openReport('window', "NGT006_DONTHUOC1L_17DBV01_TT052016_A5_944", "pdf", par);
											   } 
										 }
										 else {
											 if(_tudongindt == '1' || $.inArray( phongid, dc_phongs) >= 0){
													var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
													rpName += $('#lblPATIENTCODE').val(); 
													rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
													rpName += "."+_type; 
													CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5', _type, par,rpName);
											   }else{
												   openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
											   }
										 }
									 }
								 }
							}else{
							 if(_tudongindt == '1' || $.inArray( phongid, dc_phongs) >= 0){
									var rpName = "VNPTHIS_IN_A5_DONTHUOC_";
									rpName += $('#lblPATIENTCODE').val(); 
									rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
									rpName += "."+_type; 
									CommonUtil.inPhieu('window', 'NGT006_DONTHUOC_17DBV01_TT052016_A5', _type, par,rpName);
							   }else{
								   openReport('window', "NGT006_DONTHUOC_17DBV01_TT052016_A5", "pdf", par);
							   }
							}
						 }  
				}
				
				 if(_opts._hospital_id == '902'){
					 if($('#chkCapPhieuHenKham').is(':checked')){
					     var par = [ {
							name : 'khambenhid',
							type : 'String',
							value : _khambenhid
						 }];
					     if(_tudongindt == '1'){
					    	var rpName = "VNPTHIS_IN_A4_DONTHUOC_";
							rpName += $('#lblPATIENTCODE').val(); 
							rpName += "_" + (jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS')); 
							rpName += "."+_type; 
							CommonUtil.inPhieu('window', 'NGT014_GIAYHENKHAMLAI_TT402015_A4', _type, par,rpName);
					     }else{
					    	 openReport('window', "NGT014_GIAYHENKHAMLAI_TT402015_A4", "pdf", par); 
					     }
					 }
				   }
				 }
			 }  			
	    }	

	/*param	: _itype
	 * 		= 1 -> Search theo ten thuoc
	 * 		= 0 -> Search theo hoat chat
	 * */	
	function loadComboGrid(_srchType){
		var _col = "";
		var _sql = "";
		var _sql_par=[];
		//_macdinh_hao_phi
		//console.log("---------------------search_thuoc: "+ _srchType);
		if(_option == '02D011'){//Mua ngoai
			if(_srchType == 1)
				_sql = _gridComboMuaNgoai;
			else
				_sql = _gridComboMuaNgoai_HoatChat;
			_sql_par.push({"name":"[0]","value":_company_id});
			_col="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN_THUOC,25,0,f,l;Hoạt chất,HOATCHAT,15,0,f,l;Liều lượng,LIEULUONG,15,0,f,l;Đơn vị,TEN_DVT,10,0,f,l;" +
					"Mã"+_lbl_text+",MA_THUOC,15,0,f,l;SL khả dụng,SLKHADUNG,10,0,f,l;Giá DV,GIA_BAN,12,0,f,r;Biệt dược,BIETDUOC,13,0,f,c;" +
					"DUONGDUNGID,DUONGDUNGID,0,0,t,c;DUONG_DUNG,DUONG_DUNG,0,0,t,c;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;TENKHO,TENKHO,0,0,t,c;" +
					"KHOID,KHOID,0,0,t,c;TUONGTACTHUOC,TUONGTACTHUOC,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;" +
					"KHOANMUCID,KHOANMUCID,0,0,t,c;TYLEBHYT_TVT,TYLEBHYT_TVT,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;" +
					"KHO_THUOCID,KHO_THUOCID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l";
		}else if(_option == '02D014' || _option == '02D016'){//Phieu tra thuoc
			_sql = _gridComboDonThuoc_TRA;

			_sql_par.push({"name":"[0]","value":_khambenhid},
						{"name":"[1]","value":_company_id},
						{"name":"[2]","value":_loainhommaubenhpham_id},
						{"name":"[3]","value":_phieutraid}
			 );
			_col="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN_THUOC,25,0,f,l;Hoạt chất,HOATCHAT,15,0,f,l;Đơn vị,TEN_DVT,10,0,f,l;" +
					"Mã"+_lbl_text+",MA_THUOC,15,0,f,l;SL khả dụng,SLKHADUNG,10,0,f,l;Giá DV,GIA_BAN,12,0,f,r;Biệt dược,BIETDUOC,13,0,f,c;" +
					"DUONGDUNGID,DUONGDUNGID,0,0,t,c;DUONG_DUNG,DUONG_DUNG,0,0,t,c;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;TENKHO,TENKHO,0,0,t,c;" +
					"KHOID,KHOID,0,0,t,c;TUONGTACTHUOC,TUONGTACTHUOC,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;" +
					"KHOANMUCID,KHOANMUCID,0,0,t,c;TYLEBHYT_TVT,TYLEBHYT_TVT,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;" +
					"KHO_THUOCID,KHO_THUOCID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l";
		}else if(_option == '02D017' || _option == '02D018'){//Phieu tra thuoc
			_col="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên Tên vị thuốc YHCT,TEN_THUOC,25,0,f,l;Đơn vị,TEN_DVT,10,0,f,l;" +
					"Mã"+_lbl_text+",MA_THUOC,15,0,f,l;SL khả dụng,SLKHADUNG,10,0,f,l;Giá DV,GIA_BAN,12,0,f,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;" +
					"TENKHO,TENKHO,0,0,t,c;KHOID,KHOID,0,0,t,c;TUONGTACTHUOC,TUONGTACTHUOC,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;" +
					"GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;TYLEBHYT_TVT,TYLEBHYT_TVT,0,0,t,c;" +
					"DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;KHO_THUOCID,KHO_THUOCID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l";
			if(_srchType == 1)
				_sql = "NTU02D010.13";
			else
				_sql = "NTU02D010.14";
			//_kho_id = $('#cboMA_KHO').val();
			//_ds_khoid = _kho_id;
			_loaithuocid = $('#cboLOAITHUOC').val();
			_sql_par.push(
				//{"name":"[0]","value":_kho_id},				
				{"name":"[0]","value":_ds_khoid},
				{"name":"[1]","value":_company_id},
				{"name":"[2]","value":_loainhommaubenhpham_id},
				{"name":"[3]","value":_loaithuocid}
			);	
		}
		else{
			_col="THUOCVATTUID,THUOCVATTUID,0,0,t,c;Tên"+_lbl_text+",TEN_THUOC,25,0,f,l;Hoạt chất,HOATCHAT,15,0,f,c;Liều lượng,LIEULUONG,10,0,f,l;" +
					"Đơn vị,TEN_DVT,8,0,f,l;Mã"+_lbl_text+",MA_THUOC,15,0,t,c;SL khả dụng,SLKHADUNG,8,0,f,l;Giá DV,GIA_BAN,8,0,f,l;" +
					"Biệt dược,BIETDUOC,10,0,f,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;DUONG_DUNG,DUONG_DUNG,0,0,t,c;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;" +
					"Kho thuốc,TEN_KHO,15,0,f,l;KHOID,KHOID,15,0,t,l;TUONGTACTHUOC,TUONGTACTHUOC,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;" +
					"GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;TYLEBHYT_TVT,TYLEBHYT_TVT,0,0,t,c;" +
					"DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c;" +
					"MAHOATCHAT,MAHOATCHAT,0,0,t,c;OldValue,OLDVALUE,0,0,t,l;KETRUNGHOATCHAT,KETRUNGHOATCHAT,0,0,t,l;" +
					"KHO_THUOCID,KHO_THUOCID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l"; //OldValue,OLDVALUE,0,0,f,t,0			
			//_kho_id = $('#cboMA_KHO').val();
			_loaithuocid = $('#cboLOAITHUOC').val();
			//Ko cho ke thuoc da co trong danh sach
			if(_srchType == 1)
				_sql = "NTU02D010.13";
			else
				_sql = "NTU02D010.14";
			
			if(_kechungthuocvt == '1'){
				_loainhommaubenhpham_id = '-1';
			}
			//_ds_khoid = _kho_id;
			_sql_par.push(
				{"name":"[0]","value":_ds_khoid},
				{"name":"[1]","value":_company_id},
				{"name":"[2]","value":_loainhommaubenhpham_id},
				{"name":"[3]","value":_loaithuocid}
			);			
		}
		//_ten_donvitinh, _khoanmucid
		var _selfnc=function(event, ui) {
			var _time = 2000000;
			var _ui = ui.item;
			if($.inArray( _option, ['02D011','02D019']) >= 0){
	        	_ui.GIATRANBHYT = "0"; 
	        	_ui.GIA_BAN = "0";
	        }
			$("#hidCHOLANHDAODUYET").val("");
			if(_checktrunghoatchat == '1'){
				if(_loaitiepnhanid == '1'){ // check ngoai tru
					for(var i = 0; i <_objTmpThuoc.length; i++){
						if(_option != '02D015' && _ui.KETRUNGHOATCHAT != '1' && _objTmpThuoc[i].KETRUNGHOATCHAT != '1' && _ui.MAHOATCHAT != undefined && _objTmpThuoc[i].MAHOATCHAT != undefined && _objTmpThuoc[i].MAHOATCHAT == _ui.MAHOATCHAT){
							DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] có hoạt chất trùng với thuốc đã kê tại phòng khám hiện tại hoặc phòng khám khác trong ngày");
							if(_chanhoatchat == '1'){
								$('#btnSave').prop('disabled', true);
								return;
							}
						}else{
							$('#btnSave').prop('disabled', false);
						}
					}
				}else{
					if(_kieucheck_hoatchat == '1'){ // check hoat chat va duong dung
						var _jsonthuoc = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
						if(_jsonthuoc.length > 0){
							for(var i = 0; i <_jsonthuoc.length; i++){
								if(_option != '02D015' && _ui.MAHOATCHAT != undefined && _jsonthuoc[i].MAHOATCHAT != undefined && _jsonthuoc[i].MAHOATCHAT == _ui.MAHOATCHAT && _jsonthuoc[i].DUONGDUNGID == _ui.DUONGDUNGID){
									DlgUtil.showMsg("Thuốc ["+ _ui.TEN_THUOC +"] có hoạt chất và đường dùng trùng với thuốc đã kê trong đơn");
									if(_chanhoatchat == '1'){
										$('#btnSave').prop('disabled', true);
										return;
									}
								}else{
									$('#btnSave').prop('disabled', false);
								}
							}
						}
					}
				}
			}			
			
			var _par = [_tiepnhanid, _ui.THUOCVATTUID];					
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_S("CHECK.KE.KHANGSINH",_par.join('$'));
			if(parseInt(_return) > 0){
				var _ischeck
				_showConfirm = DlgUtil.showConfirm("Bệnh nhân đã sử dụng kháng sinh vượt quá ngày quy định, có sử dung tiếp không?", function(_ret){
					if(!_ret){
						return false;
					}
				}, _time);
			}
			
			if(_ui.MABYT == "40.17"){
				var _par_quydoi=[];
				_par_quydoi.push({"name":"[0]","value":_ui.THUOCVATTUID});
				ComboUtil.getComboTag("cboDVQUYDOI","DMC_QUYDOIOXY",_par_quydoi, "", {extval: true,value:'0',text:''},"sql");	
				
				$('#cboDVQUYDOI').prop("disabled", false);
				$("#cboDVQUYDOI").addClass("notnullandlonhon0");
			}else{
				$('#cboDVQUYDOI').prop("disabled", true);
			}
			
			var _ret = findObjectJson(_jsonThuoc24h, "THUOCVATTUID", _ui.THUOCVATTUID);
			if(_ret == true && (_option == '02D010' || _option == '02D011')){//Neu thuoc dc ke -> phai confirm
				var _msgInfo = "Thuốc "+_ui.TEN_THUOC +" đã được kê khoảng 24h trước, bạn có muốn kê đơn với thuốc này nữa không?";
				var _showConfirm = DlgUtil.showConfirm(_msgInfo, function(_ret){
					if(_ret){
						$("#txtDS_THUOC").val(_ui.TEN_THUOC);
				        if(_srch_hoatchat == 1)
				        	$("#txtTENTHUOC").val(_ui.HOATCHAT);
				        else
				        	$("#txtTENTHUOC").val(_ui.TEN_THUOC);
				        
				        $("#txtGHICHU").val(_ui.HUONGDAN_SD);
				        $("#cboDUONG_DUNG").val(_ui.DUONGDUNGID);
				        _tuongtacthuoc 		= ','+ _ui.TUONGTACTHUOC+',';
				        _nhom_mabhyt_id 	= _ui.NHOM_MABHYT_ID;
				        _ten_donvitinh 		= _ui.TEN_DVT;
				        _khoanmucid 		= _ui.KHOANMUCID;
				        _tyle_bhyt_tt_tvt	= _ui.TYLEBHYT_TVT;
				        
				        $("#hidLOAITVTID").val(_ui.LOAI);
				        $("#hidMABYT").val(_ui.MABYT);
				        
				        if(_loai_don =="2" || _loai_don =="4")
				        	$("#cboMA_KHO").val(_ui.KHOID);
				        if(_option != '02D011')//Don mua ngoai
				        	$("#hdSOLUONGKHADUNG").val(_ui.SLKHADUNG);
				        else
				        	$("#hdSOLUONGKHADUNG").val(100000);
				        $("#txtGHICHU").val("");
				        $("#hdHUONGDANTHUCHIEN").val("");
				        $("#hidCANHBAOSOLUONG").val(_ui.CANHBAOSOLUONG);
				        
				        if(_loaikedon == 1)
				        	$("#txtSOLUONG_TONG").trigger("focus");
				        else
				        	$("#txtSO_NGAY").trigger("focus");
				        $("#hdDONVI").val(_ui.TEN_DVT);
				        $("#hidKHOTHUOCTHEOTHUOC").val(_ui.KHOID);
				        $('#hidTHUOCVTID').val(_ui.THUOCVATTUID);
				        
				        _objDrugTemp = [];
				        _objDrug = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
						doAddDrugToJson(_ui, _objDrug, 1);
						doAddDrugToJson(_ui, _objDrugTemp, 1);
				        return false;
					}
					return true;
				}, _time);
			}else {//Neu thuoc chua dc ke -> Cu the ma ke
				// check thuốc tồn tại trong đơn
				var data = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				for(var i = 0; i < data.length; i++){
					if(_ui.THUOCVATTUID == data[i].THUOCVATTUID){
						DlgUtil.showMsg('Thuốc đã có trong đơn');
						$('#txtDS_THUOC').select();
						$('#txtDS_THUOC').focus();
						return;
					}
				}
				
				objData = new Object();
				objData.THUOCVATTUID = _ui.THUOCVATTUID;
				objData.BENHNHANID = $('#hidBENHNHANID').val();
				objData.MAHOATCHAT = _ui.MAHOATCHAT;
				
				if(_canhbaophacdo == '1' && $('#txtMACHANDOANICD').val() != null && $('#txtMACHANDOANICD').val() != ''){
					var paramArrDvPd = [_ui.THUOCVATTUID,$('#txtMACHANDOANICD').val(),'2'];
					var resultDvPd = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D009.EV013",paramArrDvPd.join('$'));
					if(typeof resultDvPd != 'undefined' && resultDvPd != ''){
						DlgUtil.showMsg('Thuốc ' + resultDvPd + ' có hoạt chất không tồn tại trong phác đồ điều trị mã bệnh ' + $('#txtMACHANDOANICD').val());
					}
				}
				
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.DIUNGTHUOC", JSON.stringify(objData)); 
				if(fl > 0 && _checkdiung == '1'){
					DlgUtil.showMsg('Thuốc [' + _ui.TEN_THUOC + '] nằm trong danh sách thuốc dị ứng của bệnh nhân');
					$('#txtDS_THUOC').select();
					$('#txtDS_THUOC').focus();
					//return;
				}			
				$("#hidCHOLANHDAODUYET").val(_ui.CHOLANHDAODUYET);
				
				if(_ui.CHOLANHDAODUYET == '1'){
					DlgUtil.showMsg(_ui.CHUY);
				}
				
				$("#txtDS_THUOC").val(_ui.TEN_THUOC);
		        if(_srch_hoatchat == 1)
		        	$("#txtTENTHUOC").val(_ui.HOATCHAT);
		        else
		        	$("#txtTENTHUOC").val(_ui.TEN_THUOC);
		        
		        $("#hidLOAITVTID").val(_ui.LOAI);
		        $("#hidMABYT").val(_ui.MABYT);
		        $("#txtGHICHU").val(_ui.HUONGDAN_SD);
		        $("#cboDUONG_DUNG").val(_ui.DUONGDUNGID);
		        _tuongtacthuoc 		= ','+ _ui.TUONGTACTHUOC+',';
		        _nhom_mabhyt_id 	= _ui.NHOM_MABHYT_ID;
		        _ten_donvitinh 		= _ui.TEN_DVT;
		        _khoanmucid 		= _ui.KHOANMUCID;
		        _tyle_bhyt_tt_tvt	= _ui.TYLEBHYT_TVT;
		        _dichvukbcha_id		= _ui.DICHVUKHAMBENHID;
		        if(_loai_don =="2" || _loai_don =="4")
		        	$("#cboMA_KHO").val(_ui.KHOID);
		        if(_option != '02D011')//Don mua ngoai
		        	$("#hdSOLUONGKHADUNG").val(_ui.SLKHADUNG);
		        else
		        	$("#hdSOLUONGKHADUNG").val(100000);
		        $("#txtGHICHU").val("");
		        $("#hdHUONGDANTHUCHIEN").val("");
		        $("#hidCANHBAOSOLUONG").val(_ui.CANHBAOSOLUONG);
		        if(_loaikedon == 1){
		        	$("#txtSOLUONG_TONG").trigger("focus");
		        }else{
		        	$("#txtSO_NGAY").trigger("focus");
		        }
		        
		        $("#hdDONVI").val(_ui.TEN_DVT);
		        $("#hidKHOTHUOCTHEOTHUOC").val(_ui.KHOID);
		        $('#hidTHUOCVTID').val(_ui.THUOCVATTUID);
		        
		        _objDrugTemp = [];
		        _objDrug = jQuery("#"+_gridDonThuoc).jqGrid('getRowData');
				doAddDrugToJson(_ui, _objDrug, 1);
				doAddDrugToJson(_ui, _objDrugTemp, 1);
		        return false;
			}
	    };

	    if(_srchType == 1){
	    	ComboUtil.initComboGrid("txtDS_THUOC", _sql, _sql_par,"1200px", _col, _selfnc);
	    }else{
	    	ComboUtil.initComboGrid("txtTENTHUOC", _sql, _sql_par,"1200px", _col, _selfnc);
	    }
	};

	/*
	 _ten_donvitinh = _ui.TEN_DVT;
	    _khoanmucid = _ui.KHOANMUCID;
	 */
	$("#cboMA_KHO" ).change(function () {
		_kho_id = $('#cboMA_KHO :selected').val();
		if(_kho_id != "0"){			
		    _ds_khoid = _kho_id;
		}else{
			var values = $.map($('#cboMA_KHO option'), function(e) { return e.value; });   
		  	_ds_khoid = values.join(',');
		}
	});
	
	$("#btnBP").on("click",function(e){
		$('#txtMATENCHANDOANICD_KT').val('');
		$('#txtTENCHANDOANICD_KT').val('');
	});
	
	$("#cboLOAITHUOC" ).change(function () {
		_loaithuocid = $('#cboLOAITHUOC').val();
	});
	
	function loadGridDonThuoc(iType, _maubenhpham_id,diff) {
		var sql_par=[];
		if(_maubenhpham_id != "" && _maubenhpham_id !="undefined"){
			var _sqlGrid = "";
			if(iType == 'TEMP'){
				sql_par.push(//{"name":"[0]","value":_tyle_bhyt},
							{"name":"[0]","value":_company_id},
							{"name":"[1]","value":_loainhommaubenhpham_id},
							{"name":"[2]","value":_maubenhpham_id});
				
				if(diff == '1'){
					sql_par.push({"name":"[3]","value":r_dichvu_id_diff});
					_gridDonThuocTemp = 'NTU02D010.22';
				} else if(diff == '2'){
					sql_par = [];
					sql_par.push(//{"name":"[0]","value":_tyle_bhyt},
							{"name":"[0]","value":_doituongbenhnhanid},
							{"name":"[1]","value":_maubenhpham_id});
					_gridDonThuocTemp = 'NTU02D075.EV006';
				} else {
					_gridDonThuocTemp = 'NTU02D010.05';
				}
				
				var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D010.20",_maubenhpham_id);
				if(data_ar != null && data_ar.length > 0){
					$('#txtLOIDANBS').val(data_ar[0].LOIDANBS);
				}
				_sqlGrid = _gridDonThuocTemp;
				
				// check trung hoat chat
				if(_loaitiepnhanid == '1'){
					var ischeckhc = '0';
					var vtmp = jsonrpc.AjaxJson.ajaxExecuteQueryO(_sqlGrid, sql_par);
					vtmp = JSON.parse(vtmp);
					if(vtmp.length > 0) {
						for(var k = 0; k <vtmp.length; k++){
							for(var i = 0; i < _objTmpThuoc.length; i++){
								if(vtmp[k].KETRUNGHOATCHAT != "1" && _objTmpThuoc[i].KETRUNGHOATCHAT != "1" && (vtmp[k].MAHOATCHAT == _objTmpThuoc[i].MAHOATCHAT)){
									ischeckhc = '1';
									break;
								}							
							}
							
							if(ischeckhc == '1'){
								DlgUtil.showMsg("Thuốc ["+ vtmp[k].TEN_THUOC +"] có hoạt chất trùng với thuốc đã kê tại phòng khám hiện tại hoặc phòng khám khác trong ngày");
								if(_chanhoatchat == '1'){
									return;
								}
							}else{
								_objTmpThuoc.push({
									"THUOCVATTUID" : vtmp[k].THUOCVATTUID,
									"MAHOATCHAT" : vtmp[k].MAHOATCHAT,
									"KETRUNGHOATCHAT" : vtmp[k].KETRUNGHOATCHAT
								});
							}
						}
					}
				}
			}else{
				sql_par.push({"name":"[0]","value":_loainhommaubenhpham_id},
						 	{"name":"[1]","value":_company_id},
						 	{"name":"[2]","value":_maubenhpham_id});
				if(diff == '1'){
					sql_par.push({"name":"[3]","value":r_dichvu_id_dtc_diff});	
					_sqlGrid = 'NTU02D010.26';
				} else {
					_sqlGrid = _gridDonThuocSQL;
				}
			}
			sql_par=setSysParam(sql_par);
			var _gridDonThuocHeader = "";
			if(_option != '02D017' || _option != '02D018'){
				_gridDonThuocHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,200,0,f,l;Hoạt chất,HOATCHAT,130,0,f,l;Hàm lượng,LIEULUONG,80,0,f,l;ĐVT,DONVI_TINH,50,0,f,l;Đường dùng,DUONG_DUNG,100,0,f,l;Đơn giá,DON_GIA,70,0,f,r;SL,SO_LUONG,40,0,e,c;Thành tiền,THANH_TIEN,80,0,f,r;BH trả,BH_TRA,70,0,t,l;ND trả,ND_TRA,70,0,t,l;Loại TT cũ,LOAI_DT_CU,80,0,f,l;Loại TT mới,LOAI_DT_MOI,80,0,f,l;Ghi chú,GHICHUCANHBAO,150,0,e,c,ES;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c; ,ACTION,50,udb,f,c; ,ACTION,30,d,f,c;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l";
			}else{
				_gridDonThuocHeader = "THUOCVATTUID,THUOCVATTUID,0,0,t,l; ,LAMTRON,42,0,f,c,ES;Tên"+_lbl_text+",TEN_THUOC,200,0,f,l;Đơn vị tính,DONVI_TINH,100,0,f,l;Đơn giá,DON_GIA,100,0,f,r;Số lượng,SO_LUONG,100,0,f,c;Thành tiền,THANH_TIEN,100,0,f,r;BH trả,BH_TRA,100,0,t,l;ND trả,ND_TRA,100,0,t,l;Loại TT cũ,LOAI_DT_CU,100,0,f,l;Loại TT mới,LOAI_DT_MOI,80,0,f,l;Ghi chú,GHICHUCANHBAO,150,0,e,c,ES;ID_DT_CU,ID_DT_CU,0,0,t,l;ID_DT_MOI,ID_DT_MOI,0,0,t,l;ma_thuoc,MA_THUOC,0,0,t,r;HUONGDAN_SD,HUONGDAN_SD,0,0,t,c;DUONGDUNGID,DUONGDUNGID,0,0,t,c;NHOM_MABHYT_ID,NHOM_MABHYT_ID,0,0,t,c;GIATRANBHYT,GIATRANBHYT,0,0,t,c;KHOANMUCID,KHOANMUCID,0,0,t,c;DICHVUKHAMBENHID,DICHVUKHAMBENHID,0,0,t,c; ,ACTION,70,udb,f,c; ,ACTION,50,d,f,c;Mã hoạt chất,MAHOATCHAT,0,0,f,t,0;OldValue,OLDVALUE,0,0,t,l;KHO_THUOCID,KHO_THUOCID,0,0,t,l;DVQD,DVQD,0,0,t,l;LOAITVTID,LOAITVTID,0,0,t,l;THUOCSAO,THUOCSAO,0,0,t,l";	//OldValue,OLDVALUE,0,0,f,t,0
			}
			
			var opt_ext={footerrow: true, rowNum: 200,rowList: [200]};
//			if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TACH_PHIEUTHUOC_KE_SOLUONGLE')==1){
//				GridUtil.init(_gridDonThuoc,"1278","245",_gridCaption,true, _gridDonThuocHeader, false, opt_ext);
//			}else{
//				GridUtil.init(_gridDonThuoc,"1278","245",_gridCaption,false, _gridDonThuocHeader, false, opt_ext);
//			}
//			
			//tuyennx_add_start 20180928 fix
			if(jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH",'TACH_PHIEUTHUOC_KE_SOLUONGLE')==1)
				$("#grdDONTHUOC").jqGrid('showCol', "LAMTRON");
			else
				$("#grdDONTHUOC").jqGrid('hideCol', "LAMTRON");
			GridUtil.init(_gridDonThuoc,"1278","245",_gridCaption,false, _gridDonThuocHeader, false, opt_ext);
			//tuyennx_add_end 20180928 fix
			GridUtil.loadGridBySqlPage(_gridDonThuoc, _sqlGrid, sql_par);
			loadAll("","");
		}
	}
	
	function setSysParam(_par_ar) {
		var v_par=_par_ar;
		for(var i1=0;i1<_param.length;i1++) {
			v_par.push({"name":"[S"+i1+"]","value":_param[i1]});
		}
		return v_par;
	}
	
	function cleanForm(){
		$("#txtDS_THUOC").val("");
		$("#txtTENTHUOC").val("");
		$("#txtSOLUONG_TONG").val("");
		$("#txtGHICHU").val("");
		$("#txtSOLUONG_CHITIET").val("");
		$("#txtTGSD").val("");
		$("#txtSearchCD").val("");
		
		//$("#txtSO_NGAY").val("");
		$("#txtSANG").val("");
		$("#txtTRUA").val("");
		$("#txtCHIEU").val("");
		$("#txtTOI").val("");
		$("#hdHUONGDANTHUCHIEN").val("");
		
		$("#txtSLSOLAN").val("");
		$("#txtSOLANSONGAY").val("");
		$("#txtLIEUDUNG").val("");
		//txtSOLUONG_CHITIET,txtSO_NGAY,txtSANG,txtTRUA,txtCHIEU,txtTOI
	}
	
	$('#txtSOLUONG_TONG').keydown(function (e) {
		if (e.which === 13){
			var num = parseFloat($('#txtSOLUONG_TONG').val()) || 0;
		    if($('#txtSOLUONG_TONG').val() == '' || num == 0){
		    	DlgUtil.showMsg("Số lượng kê đơn phải là số nguyên dương và lớn hơn 0");
		    }	
		    else{
		    	$('#txtGHICHU').trigger("focus");
		    	if($('#txtSOLUONG_TONG').val().indexOf('/') != -1){
		    		var array = $('#txtSOLUONG_TONG').val().split('/');
		    		var value = array[0]/array[1];
		    		$('#txtSOLUONG_TONG').val(value);
		    	}
		    	
		    	if(isNaN($('#txtSOLUONG_TONG').val())){
		    		$('#txtSOLUONG_TONG').trigger("focus");
			    	DlgUtil.showMsg("Số lượng phải là kiểu số");
			    }
		    	
		    	$('#SLSOLAN').trigger("focus");
		    	if(_sudung_lieudung == '1' && (_option == '02D010')){
		    		$('#txtSLSOLAN').trigger("focus");
				}else{
					$('#txtGHICHU').trigger("focus");
				}
		    }	
		}
	});
	
	$('#cboDUONG_DUNG').keydown(function (e) {
		if (e.which == 13)
			$('#txtSO_NGAY').trigger("focus");
	});
	
	$('#txtGHICHU').keydown(function (e) {
		if (e.which === 13){
			addThuoc();
			//$('#btnAdd').trigger("focus");
			/*if(_sudung_lieudung == '1' && (_option == '02D010' || _option == '02D017')){
				$('#txtSLSOLAN').trigger("focus");
			}else{
				$('#btnAdd').trigger("focus");
			}*/
		}	
	});
	
	function addThuoc(){
		var valid = that.validator.validateForm();
		var _soluong_thuoc = 0;
		var songay = 0;		
		
		//if(_ds_id_loaitvt.length > 0 && $.inArray( _option, ['02D010','02D015','02D017']) >= 0 && $.inArray( $("#hidLOAITVTID").val(), _ds_id_loaitvt) >= 0 && $("#txtGHICHU").val() == ''){
		if(_ds_id_loaitvt.length > 0 && $.inArray( _option, ['02D010','02D015']) >= 0 && $.inArray( $("#hidLOAITVTID").val(), _ds_id_loaitvt) >= 0 && $("#txtGHICHU").val() == '' && _dichvucha_id == ""){
			$("#txtGHICHU").focus();
			DlgUtil.showMsg("Hãy nhập cách dùng thuốc");				
			return;
		}
		
		if(_sudung_dvqd_kethuoc != "0" && $("#cboDVQUYDOI").val() == '0' && $("#hidMABYT").val() == "40.17"){
			$("#cboDVQUYDOI").focus();
			DlgUtil.showMsg("Hãy chọn đơn vị quy đổi");				
			return;
		}
		
		if(_objDrugTemp.length == 0){
			$("#txtDS_THUOC").trigger("focus");
			return DlgUtil.showMsg("Bạn phải nhập thuốc để kê đơn!");
		}
		if(_loaikedon == 1){
			if(parseInt($('#txtSONGAY_KE').val()) > parseInt(_songaykemax)){
				$('#txtSONGAY_KE').select();
				$('#txtSONGAY_KE').focus();
				DlgUtil.showMsg("Số ngày kê đơn không được lớn hơn " +_songaykemax);
				return;
			}
			if(r_loaicheck == "0" && (parseInt($('#txtSOLUONG_TONG').val()) > parseInt(_soluongthuocmax))){
				$('#txtSOLUONG_TONG').select();
				$('#txtSOLUONG_TONG').focus();
				DlgUtil.showMsg("Số lượng kê đơn thuốc không được lớn hơn " +_soluongthuocmax);
				return;
			}
			
			songay = $('#txtSONGAY_KE').val();
		}else {
			if(r_loaicheck == "0" && (parseInt($('#txtSOLUONG_CHITIET').val()) > parseInt(_soluongthuocmax))){
				$('#txtSOLUONG_CHITIET').select();
				$('#txtSOLUONG_CHITIET').focus();
				DlgUtil.showMsg("Số lượng kê đơn thuốc  không được lớn hơn " +_soluongthuocmax);
				return;
			}	
			songay = $('#txtSO_NGAY').val();
		}
		
		// bệnh nhân ngoại trú và kê thuốc.
		if(_loaitiepnhanid == '1' && (_option == '02D010' || _option == '02D017')) {
			
			if(_kieucheck == '1' && (parseInt(_ngayhanthe) <= 5 && parseInt(songay) > 7)){
				DlgUtil.showMsg("Hạn thẻ BHYT <= 5 ngày, số ngày kê thuốc ko được lớn hơn 7 ngày");
				return;
			}
			
			if(_kieucheck == '2' && (parseInt(_ngayhanthe) < parseInt(songay))){
				DlgUtil.showMsg("Số ngày kê thuốc không thể lớn hơn số ngày còn hạn thẻ");
				return;
			}
		}
		
		if(parseInt($("#txtSO_NGAY").val()) <= 0){
			DlgUtil.showMsg("Số ngày kê thuốc phải lớn hơn 0");
			return;
		}
		
		if(_loaikedon == 1){
			_soluong_thuoc = $("#txtSOLUONG_TONG").val();
		}else{
			_soluong_thuoc = $("#txtSOLUONG_CHITIET").val();
		}	
		
		if(r_loaicheck == "1" && parseFloat($("#hidCANHBAOSOLUONG").val()) > 0 && (parseFloat(_soluong_thuoc) > parseFloat($("#hidCANHBAOSOLUONG").val()))){
			return DlgUtil.showMsg("Thuốc/Vật tư kê không được kê quá số lượng " + $("#hidCANHBAOSOLUONG").val());
		}
		
		if(_soluong_thuoc > 0){
			if(parseFloat(_soluong_thuoc) > parseFloat($("#hdSOLUONGKHADUNG").val())){
				if(_loaikedon == 1)
					$("#txtSOLUONG_TONG").trigger("focus");
				else
					$("#txtSOLUONG_CHITIET").trigger("focus");
				return DlgUtil.showMsg("Số lượng kê đơn phải nhỏ hơn hoặc bằng số lượng khả dụng");
			}
		}else{
			if(_loaikedon == 1)
				$("#txtSOLUONG_TONG").trigger("focus");
			else
				$("#txtSOLUONG_CHITIET").trigger("focus");
			return DlgUtil.showMsg("Số lượng"+_lbl_text+" kê đơn phải lớn hơn 0!");
		}
		/*if(_option == '02D014'||_option == '02D016'){
			if(Number.isInteger(parseFloat(_soluong_thuoc)) == false){
				$("#txtSOLUONG_TONG").focus();
				return DlgUtil.showMsg("Số lượng trả"+_lbl_text+" phải là số nguyên dương!");
			}	
		}*/
		
		objData = new Object();
		objData.THUOCVATTUID = $('#hidTHUOCVTID').val();
		objData.TIEPNHANID = _tiepnhanid;
		
		var _checkthuocsao = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK.THUOCSAO", JSON.stringify(objData));
		if(_loaitiepnhanid == 0 && _sudungthuoc12sao == '1' && $.inArray($('#hidCHOLANHDAODUYET').val(), ['2','3']) >= 0 && _checkthuocsao == '0'){
			if($('#hidCHOLANHDAODUYET').val() == '2'){
				var myVar={
						MABENHNHANH : $('#lblPATIENTCODE').val()
					};
				dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K069_ThuocMotSao",myVar,"HỘI CHẨN THUỐC 1 SAO",800,500);
				DlgUtil.open("dlgTHUOCSAO");					
			}
			
			EventUtil.setEvent("assignSevice_save1sao",function(e){
				var _objData = new Object();
				_objData = $.extend(_objData,e.msg);
				$('#hidTHUOCSAO').val(JSON.stringify(_objData));
				_addthuocgrid(valid, _soluong_thuoc);
				DlgUtil.close("dlgTHUOCSAO");
			});
			
			
			if($('#hidCHOLANHDAODUYET').val() == '3'){
				var myVar={
						ICD10NAME : $('#txtTENCHANDOANICD').val(),
						ICD10CODE : $('#txtMACHANDOANICD').val()
					};
				dlgPopup=DlgUtil.buildPopupUrl("dlgTHUOCSAO","divDlg","manager.jsp?func=../ngoaitru/NGT02K070_ThuocHaiSao",myVar,"HỘI CHẨN THUỐC 2 SAO",800,500);
				DlgUtil.open("dlgTHUOCSAO");
			}
			
			EventUtil.setEvent("assignSevice_save2sao",function(e){
				var _objData = new Object();
				_objData = $.extend(_objData,e.msg);
				$('#hidTHUOCSAO').val(JSON.stringify(_objData));
				_addthuocgrid(valid, _soluong_thuoc);
				DlgUtil.close("dlgTHUOCSAO");
			});
		}else{
			_addthuocgrid(valid, _soluong_thuoc);
		}
		
		$('#hidTHUOCVTID').val("");
	};
	
	$('#txtSLSOLAN').keydown(function (e) {
		if (e.which === 13){ 
			$('#txtSOLANSONGAY').trigger("focus");
		}	
	});
	
	$('#txtSOLANSONGAY').keydown(function (e) {
		if (e.which === 13){
			var lieudung = $('#txtSLSOLAN').val()+ " " + $('#hdDONVI').val() + "/Lần * " + $('#txtSOLANSONGAY').val() + "lần/Ngày";
			
			$('#txtLIEUDUNG').val(lieudung);
			$('#txtLIEUDUNG').trigger("focus");
		}	
	});
	$('#txtLIEUDUNG').keydown(function (e) {
		if (e.which === 13){
			$('#txtGHICHU').trigger("focus");
			//$('#btnAdd').trigger("focus");
		}	
	});
	
	$('#txtSOLUONG_CHITIET').keydown(function (e) {
		if (e.which === 13){
			var num = parseFloat($('#txtSOLUONG_CHITIET').val()) || 0;
		    if($('#txtSOLUONG_CHITIET').val() == '' || num == 0){
		    	DlgUtil.showMsg("Số lượng kê đơn phải là số nguyên dương và lớn hơn 0");
		    }	
		    else{
		    	//$('#txtGHICHU').trigger("focus");
		    	$('#SLSOLAN').trigger("focus");
		    	if(_sudung_lieudung == '1' && (_option == '02D010' || _option == '02D017')){
		    		$('#txtSLSOLAN').trigger("focus");
				}else{
					$('#txtGHICHU').trigger("focus");
				}
		    }	
		}
		/*if (e.which == 13 && _botimcachdung == '1'){
			//$('#txtSearchCD').trigger("focus");
			$('#txtTGSD').trigger("focus");
		}else if(e.which == 13){
			$('#txtGHICHU').trigger("focus");
		}*/
	});
	
	$('#txtTGSD').keydown(function (e) {
		if (e.which == 13){
			$('#txtSearchCD').trigger("focus");
		}
	});
	
	$('#txtSearchCD').keydown(function (e) {
		if (e.which == 13){
			$('#txtGHICHU').trigger("focus");
		}
	});
	
	$('#txtSO_NGAY').keydown(function (e) {
		if (e.which == 13){
			$('#txtSANG').trigger("focus");
		}
	});
	$('#txtSANG').keydown(function (e) {
		if (e.which == 13){
			$('#txtTRUA').trigger("focus");
		}
	});
	$('#txtTRUA').keydown(function (e) {
		if (e.which == 13){
			var _trua = parseFloat($('#txtTRUA').val()) || 0;
			if(_trua == 0)
				$('#txtTRUA').val("0");
			$('#txtCHIEU').trigger("focus");
		}
	});
	$('#txtCHIEU').keydown(function (e) {
		if (e.which == 13){
			var _chieu = parseFloat($('#txtCHIEU').val()) || 0;
			if(_chieu == 0)
				$('#txtCHIEU').val("0");
			//doInputDrugType(_loaikedon);
			$('#txtTOI').trigger("focus");
		}
	});
	$('#txtTOI').keydown(function (e) {
		if (e.which == 13){
			var _chieu = parseFloat($('#txtTOI').val()) || 0;
			if(_chieu == 0)
				$('#txtTOI').val("0");
			//doInputDrugType(_loaikedon);
			$('#txtSOLUONG_CHITIET').trigger("focus");
		}
	});
	
	$('#txtSONGAY_KE').keydown(function (e) {
		if (e.which == 13){
			$('#txtSOLUONG_TONG').trigger("focus");
		}
	});
	
	$('#txtSANG').focusin(function() { $(this).select(); });
	$('#txtTRUA').focusin(function() { $(this).select(); } );
	$('#txtCHIEU').focusin(function() { $(this).select(); } );
	$('#txtTOI').focusout(function() { doInputDrugType(_loaikedon);});
	//$('#txtSOLUONG_TONG').focusin(function() { $(this).select(); });
	$('#txtSOLUONG_CHITIET').focusout(function() { doInputDrugType(_loaikedon, 1);});
	$('#txtTGSD').focusout(function() { doInputDrugType(_loaikedon, 1);});
	$('#txtSearchCD').focusout(function() { doInputDrugType(_loaikedon, 1);});
	$('#txtSOLUONG_TONG').focusout(function() { doInputDrugType(_loaikedon, 1); });
	
	String.prototype.replaceAll = function(search, replacement) {
	    var target = this;
	    return target.replace(new RegExp(search, 'g'), replacement);
	};
	
	Number.prototype.formatMoney = function(c, d, t){
		var n = this, 
		    c = isNaN(c = Math.abs(c)) ? 2 : c, 
		    d = d == undefined ? "." : d, 
		    t = t == undefined ? "," : t, 
		    s = n < 0 ? "-" : "", 
		    i = parseInt(n = Math.abs(+n || 0).toFixed(c)) + "", 
		    j = (j = i.length) > 3 ? j % 3 : 0;
		   return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
		 };
	function doLoadCombo(_txt,_txtDst){
		var _selfnc=function(event, ui) {
		   var str = $("#txtTENCHANDOANICD_KT").val();
		   if(str.indexOf(ui.item.ICD10CODE) > -1){
		    DlgUtil.showMsg("Bệnh chính trùng bệnh kèm theo");
		    return false;
		   }
			   
	        $("#"+_txt).val(ui.item.ICD10CODE);
	        $("#"+_txtDst).val(ui.item.ICD10NAME);
	        $("#txtMATENCHANDOANICD_KT").trigger("focus");
	        return false;
	    };
	    ComboUtil.initComboGrid(_txt,"NT.SEARCH.ICD10",sql_par,"600px",_col,_selfnc);
	};
	
	function doInputDrugType(_loaikedon, kieu){
		var _total = 0;
		var _text = "";
		var _lan_sang = 0;
		var _lan_trua = 0;
		var _lan_chieu = 0;
		var _lan_toi = 0;
		var _tong_lan = 0;
		var _trung_binh = 0;
		var _soluong = 0;
		var _solan = 0;
		var _lieudung = "";
		var _slmax = 0;
		
		$("#hidLIEUDUNGBD").val("");
		var _text_duongdung = $("#cboDUONG_DUNG option:selected").text();
			var _songay = parseFloat($('#txtSO_NGAY').val()) || 0;
			
			var _sang = parseFloat($('#txtSANG').val()) || 0;
			var _trua = parseFloat($('#txtTRUA').val()) || 0;
			var _chieu = parseFloat($('#txtCHIEU').val()) || 0;
			var _toi = parseFloat($('#txtTOI').val()) || 0;
			
			_slmax = _sang;
			if(_slmax < _trua)
				_slmax = _trua;
			if(_slmax < _chieu)
				_slmax = _chieu;
			if(_slmax < _toi)
				_slmax = _toi;
			
			if(_sang > 0){
				_tong_lan = parseInt(_tong_lan) + 1;
				_soluong = parseFloat(_soluong) + _sang;
				
			}else{
				$('#txtSANG').val("0");
			}
			
			if(_trua > 0){
				_tong_lan = parseInt(_tong_lan) + 1;
				_soluong = parseFloat(_soluong) + _trua;
			}else{
				$('#txtTRUA').val("0");
			}				
			
			if(_chieu > 0){
				_tong_lan = parseInt(_tong_lan) + 1;
				_soluong = parseFloat(_soluong) + _chieu;
			}else{
				$('#txtCHIEU').val("0");
			}
			
			if(_toi > 0){
				_tong_lan = parseInt(_tong_lan) + 1;
				_soluong = parseFloat(_soluong) + _toi;
			}else{
				$('#txtTOI').val("0");
			}

			_trung_binh = (_sang + _trua + _chieu + _toi)/_tong_lan;
			
			if(_songay > 0){				
				_total = Number((_sang + _trua + _chieu + _toi)*_songay);
				if(kieu != '1'){// set lai so luong
					if(_ngt_lamtron_kedon == '1'){
						$("#txtSOLUONG_CHITIET").val(Math.round(_total));
					}else{
						$("#txtSOLUONG_CHITIET").val(_total);
					}
				}				
				// SONDN START 20180111
				var _tgsd1 = typeof $("#txtTGSD").val() === 'undefined' ? "" : $("#txtTGSD").val(); 
				var _searchcd1 = typeof $("#txtSearchCD").val() === 'undefined' ? "" : $("#txtSearchCD").val();
				var _donvi1 = typeof $("#hdDONVI").val() === 'undefined' ? "" : $("#hdDONVI").val();
				
				_text = _songay +"@"+$("#cboDUONG_DUNG option:selected").text()+"@_param_huongdan@"
							+ _total +"@"+ _sang +"@"+ _trua +"@"+ _chieu +"@"+ _toi 
							+ "@" + _tgsd1 + "@" + _searchcd1 + "@" + _donvi1;
				
				// SONDN END 20180111
				$("#hdHUONGDANTHUCHIEN").val(_text);
				$("#txtGHICHU").val(_songay + " ngày, " + "Ngày "+ (_total/_songay) + " " + $('#hdDONVI').val() +" chia "+ _tong_lan);
				
				// tạm check với HCM
				if(_cachdung_sangchieu == '1'){
					$("#txtGHICHU").val("");
					var dvt = "";
					var _ghichu = "";
					var sang = "";
					var trua = "";
					var chieu = "";
					var toi = "";
					var _tgsd = "";
					var _cachdung = "";
					var _duongdung = "";
					dvt = $("#hdDONVI").val();
					
					if(parseFloat(_sang) != 0){
						_ghichu += "Sáng " + _sang + " " + dvt;
						sang = "Sáng " + _sang + " " + dvt;
					}
					
					if(parseFloat(_trua) != 0){						
						if(_ghichu != ""){
							_ghichu += ", Trưa " + _trua + " " + dvt;
							trua = ", Trưa " + _trua + " " + dvt;;
						} else {
							_ghichu += "Trưa " + _trua + " " + dvt;
							trua = "Trưa " + _trua + " " + dvt;
						}
					}
					
					if(parseFloat(_chieu) != 0){
						if(_ghichu != ""){
							_ghichu += ", Chiều " + _chieu + " " + dvt;
							chieu = ", Chiều " + _chieu + " " + dvt;
						} else {
							_ghichu += "Chiều " + _chieu + " " + dvt;
							chieu = "Chiều " + _chieu + " " + dvt;
						}
					}
					
					if(parseFloat(_toi) != 0){						
						if(_ghichu != ""){
							_ghichu += ", Tối " + _toi + " " + dvt;
							toi = ", Tối " + _toi + " " + dvt;
						} else {
							_ghichu += "Tối " + _toi + " " + dvt;
							toi = "Tối " + _toi + " " + dvt;
						}
					}
					
					if(_format_cd == '1'){ // đường dùng + thời gian dùng + cách dùng + sang/trua/chieu/toi. 
						_ghichu = "";
						_tgsd = $("#txtTGSD").val();
						_cachdung = $("#txtSearchCD").val();
						_duongdung = $("#cboDUONG_DUNG option:selected").text();
						
						if(typeof _duongdung != 'undefined'){
							_duongdung += ", ";
						}
						else
							_duongdung ="";
						
						if(typeof _tgsd != 'undefined'){
							_tgsd += ", ";
						}
						else
							_tgsd="";
						
						if(typeof _cachdung != 'undefined'){
							_cachdung += ", ";
						}	
						else
							_cachdung="";
						_ghichu = _duongdung + _tgsd + _cachdung + sang + trua + chieu + toi;
					}else if(_format_cd == '2'){ // đường dùng + ngày + sl + DVT + thời gian dùng + cách dùng + sang/trua/chieu/toi. 
						var _soluong = 0;
						_ghichu = "";
						_tgsd = ($("#txtTGSD").val() == undefined)?'':$("#txtTGSD").val();
						_cachdung = ($("#txtSearchCD").val() == undefined)?'':$("#txtSearchCD").val();
						_duongdung = $("#cboDUONG_DUNG option:selected").text();
						
						if(_duongdung != ""){
							_duongdung += ", ";
						}
						
						if(_tgsd != ""){
							_tgsd += ", ";
						}
						
						if(_cachdung != ""){
							_cachdung += ", ";
						}					
						_soluong = Number((_sang + _trua + _chieu + _toi));
						
						if(parseFloat(_soluong) <= 0){
							_soluong = parseFloat(Number($("#txtSOLUONG_CHITIET").val())/_songay);
							_duongdung = $("#cboDUONG_DUNG option:selected").text();
							if(_duongdung.toUpperCase() != "UỐNG"){
								_ghichu = _duongdung + ", Ngày " + _tgsd + " " + _cachdung;
							}else{
								_ghichu = _duongdung + ", Ngày " + _soluong + " " + dvt + " " + _tgsd + " " + _cachdung;
							}							
						}else{
							_ghichu = _duongdung + "Ngày " + _soluong + " " + dvt + " " + _tgsd + " " + _cachdung + ", " + sang + trua + chieu + toi;
						}						
					}else{
						if(_songay != null && _songay != ''){
							_ghichu = _songay + " ngày, " + _ghichu;
						} 
					}
					$("#txtGHICHU").val(_ghichu);
				}
				$('#txtSOLUONG_CHITIET').focusin(function() { $(this).select(); });
			}else{
				_total = 0;
				// SONDN START 20180111 : bo sung them 2 chu @
				var _donvi1 = typeof $("#hdDONVI").val() === 'undefined' ? "" : $("#hdDONVI").val();
				_text = "@"+$("#cboDUONG_DUNG option:selected").text()+"@_param_huongdan@"+ _total +"@@@@@@"  + "@" + _donvi1;
				// SONDN END 20180111
				
				$("#hdHUONGDANTHUCHIEN").val(_text);
				$("#txtGHICHU").val("");
				$("#txtSOLUONG_CHITIET").val("");
				$('#txtSOLUONG_CHITIET').focusin(function() { $(this).select(); });
			}
			
/*			if(_option == '02D010'){
				_lieudung = _soluong + "/lần * " + _tong_lan + "/ ngày";
			}else */
				
			if(_option == '02D017'){
				_lieudung = $("#txtSOLUONG_TONG").val() + "g * 1 thang * " + $("#txtSLTHANG").val() + " ngày";
			}
			
			if(_option == '02D010'){
				$("#txtSLSOLAN").val(_slmax);
				$("#txtSOLANSONGAY").val(_tong_lan);
				_lieudung = _slmax + " " + $("#hdDONVI").val() + "/lần * " + _tong_lan + "lần/ ngày";
				$("#txtLIEUDUNG").val(_lieudung);
			}
			$("#hidLIEUDUNGBD").val(_lieudung);
			
		return _total;
	}
	
	function isInt(n){
	    return Number(n) === n && n % 1 === 0;
	}

	function isFloat(n){
	    return Number(n) === n && n % 1 !== 0;
	}
	
	function splitDd(n){
		var duongdung = '...';
		if(typeof n !== "undefined" && n != null && n != ''){
			var hdsd = n.split("@");
			var songay = '';
			if(hdsd != null && hdsd.length > 6){
				duongdung = hdsd[2];
			}
			if(duongdung == ''){
				duongdung = '...';
			}
		}
		return duongdung;
	}
	
}
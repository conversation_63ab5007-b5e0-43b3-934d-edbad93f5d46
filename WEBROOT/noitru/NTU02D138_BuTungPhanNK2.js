var _CAUHINH = {};
var _CHUCNANG = "";

function NT02D044(_opts) {

    this.load = doLoad;

    function doLoad() {
        initControl(_opts);
        setInterface();
    }

    function initControl(_options) {
        _CAUHINH = getDSCauHinh('QLLT_BUTUNGPHAN_NHIEUKHO');
        getDSCauHinh();
        ganLaiDinhDanh();

        var taophieu = new taoPhieuThuocVT(_options);
        taophieu.load();

        var plThuocVT = new phieuLinhThuocVT(_options);
        plThuocVT.load();

        var tabTHC = new tab3(_options);
        tabTHC.load();
    }

    function ganLaiDinhDanh(){
        var optFunc = _opts.optFunc;
        if (optFunc == 'PHIEU_TT_THUOC') {
            _CHUCNANG = "5_BU_THUOC";
        } else if (optFunc == 'PHIEU_TT_VATTU') {
            _CHUCNANG = "6_BU_VATTU";
        } else if (optFunc == 'TRA_TT_THUOC') {
            _CHUCNANG = "10_TRA_BU_THUOC";
        } else if (optFunc == 'TRA_TT_VATTU') {
            _CHUCNANG = "11_TRA_BU_VATTU";
        }
    }

    function setInterface() {
        var title = "";
        switch (_CHUCNANG){
            case "5_BU_THUOC":
                title = "Phiếu lĩnh thuốc bù tủ trực";
                break;
            case "6_BU_VATTU":
                title = "Phiếu lĩnh vật tư bù tủ trực";
                break;
            case "10_TRA_BU_THUOC":
                title = "Phiếu trả thuốc bù tủ trực";
                break;
            case "11_TRA_BU_VATTU":
                title = "Phiếu trả vật tư bù tủ trực";
                break;
            default:
                break;
        }

        $('#titleQLLT').text(title + " từng phần");

    }
}

function taoPhieuThuocVT(_opts) {

    var _Loainhommaubenhphamid = "";
    var _loai_khothuoc = "";
    var _Loaiphieumaubenhpham = "";
    var _loai_tvt_kho = "";
    var _alert_text = "";
    var _alert_text_loai = "";
    var _call_report = "";
    var isThLoaiBn = false;

    var _jsonMBP = {
        NGAY_TAO: "",
        KHO_LAP_ID: "",
        KHO_DOIUNG_ID: "",
        KHOA_ID: "",
        DS_MBP: "",
        GHICHU_PL: ""
    };

    this.load = doLoad;

    function doLoad() {
        initControl();
        bindEvent();
        setInterface();
    }

    function initControl() {
        getVariable();

        getDataComboBox();
        generateGridDSDonThuoc();
        generateGridDsThuoc();
    }

    function getVariable() {
        if (_CHUCNANG == "5_BU_THUOC") {
            if (_CAUHINH.HIS_LOAI_BN_TH == '1') {
                isThLoaiBn = true;
                $('#divTHONGTINLOAIBN').show();
            }
            _loai_khothuoc = "8,10,13,15";
            _Loainhommaubenhphamid = 7;
            _Loaiphieumaubenhpham = 1;
            _loai_tvt_kho = 0;
            _alert_text = "thuốc";
            _alert_text_loai = "lĩnh bù tủ trực";
            _call_report = "LINH_THUOC";
        } else if (_CHUCNANG == "6_BU_VATTU") {
            if (_CAUHINH.HIS_LOAI_BN_TH == '1') {
                isThLoaiBn = true;
                $('#divTHONGTINLOAIBN').show();
            }
            _loai_khothuoc = "9,11,13,15";
            _Loainhommaubenhphamid = 8;
            _Loaiphieumaubenhpham = 1;
            _loai_tvt_kho = 1;
            _alert_text = "vật tư";
            _alert_text_loai = "lĩnh bù tủ trực";
            _call_report = "LINH_VATTU";
        } else if (_CHUCNANG == "10_TRA_BU_THUOC") {
            _loai_khothuoc = "8,10,13,15";
            _Loainhommaubenhphamid = 7;
            _Loaiphieumaubenhpham = 2;
            _loai_tvt_kho = 0;
            _alert_text = "thuốc";
            _alert_text_loai = "trả bù tủ trực";
            _call_report = "TRA_TT_THUOC";
        } else if (_CHUCNANG == "11_TRA_BU_VATTU") {
            _loai_khothuoc = "9,11,13,15";
            _Loainhommaubenhphamid = 8;
            _Loaiphieumaubenhpham = 2;
            _loai_tvt_kho = 1;
            _alert_text = "vật tư";
            _alert_text_loai = "trả bù tủ trực";
            _call_report = "TRA_TT_VATTU";
        }

        if (isThLoaiBn) {
            if (_CAUHINH.QLLT_SHOWTHUOCKB == '1') {
                var optionKhamBenh = '<option value="1">Khám bệnh</option>';
                $("#cboLOAIBENHNHAN").append(optionKhamBenh);
            }
        }
    }


    function setInterface() {

        $("#txtTU_NGAY").val(moment().format('DD/MM/YYYY'));
        $("#txtDEN_NGAY").val(moment().format('DD/MM/YYYY'));

        if (_CAUHINH.HIS_QLLT_TIMTHEOGIO == '1') {
            $("#txtTU_NGAY").attr("data-mask", "00/00/0000 00:00:00");
            $("#txtTU_NGAY").attr("placeholder", "dd/MM/yyyy hh:mm:ss");
            $("#txtTU_NGAY").next().attr("onclick", "NewCssCal('txtTU_NGAY','ddMMyyyy','dropdown',true,'24',true)");
            $("#txtTU_NGAY").val(moment().format('DD/MM/YYYY 00:00:00'));

            $("#txtDEN_NGAY").attr("data-mask", "00/00/0000 00:00:00");
            $("#txtDEN_NGAY").attr("placeholder", "dd/MM/yyyy hh:mm:ss");
            $("#txtDEN_NGAY").next().attr("onclick", "NewCssCal('txtDEN_NGAY','ddMMyyyy','dropdown',true,'24',true)");
            $("#txtDEN_NGAY").val(moment().format('DD/MM/YYYY 23:59:59'));
        }

        $("#txtPLTUNGAY").val(moment().format('DD/MM/YYYY 00:00:00'));
        $("#txtPLDENNGAY").val(moment().format('DD/MM/YYYY 23:59:59'));

        setTimeout(searchDSDonThuoc, 1000);
    }

    function getDataComboBoxKhoKe() {
        var _parm_loai_kho = "Kho.LoaiKho IN (" + _loai_khothuoc + ")";
        var _param_kho = _loai_tvt_kho + "$" + 12 + "$" + _opts.khoaid + "$0$0$" + _parm_loai_kho;
        ComboUtil.getComboTag("cboMA_KHO", "NTU02D010.17", _param_kho, "", "", "sp", "", false);
    }

    function getDataComboBoxKhoPL() {
        var _parm_loai_kho = "Kho.LoaiKho IN (" + _loai_khothuoc + ")";
        var _param_kho = _loai_tvt_kho + "$" + 12 + "$" + _opts.khoaid + "$0$0$" + _parm_loai_kho;
        ComboUtil.getComboTag("cboPLKHO", "NTU02D010.17", _param_kho, "" , {
            value: '-1',
            text: '-- Tất cả --'
        }, "sp", "", false);
    }

    function getDataComboBoxKhoBu(khoKe) {
        ComboUtil.getComboTag("cboKHOBU", "NTU02D138.L01", [{
            "name": "[0]",
            "value": khoKe
        }], "", "", "");
    }

    function getDataComboBox() {
        // kho kê
        getDataComboBoxKhoKe();

        // kho bu
        // var firstKhoKe = $('#cboMA_KHO').find("option:first-child").val();
        // getDataComboBoxKhoBu(firstKhoKe);
        $("#cboKHOBU").empty();
        $("#cboKHOBU").append('<option value="-1">-- Tất cả --</option>');
        $("#cboKHOBU").prop("disabled",true);

        // bac si
        getDataComboBoxBacsi();

        // kho de tim kiem tab phieu linh
        getDataComboBoxKhoPL();

    }

    function getDataComboBoxBacsi() {
        var _colUser = "USER_ID,USER_ID,10,0,t,l;Bác sỹ,USER_NAME,90,0,f,l";
        ComboUtil.initComboGrid("txtTKBACSI", "NTU01H046.EV001", [], "600px", _colUser, function (event, ui) {
            $("#cboBACSIID").empty();
            $("#cboBACSIID").append('<option value="' + ui.item.USER_ID + '">' + ui.item.USER_NAME + '</option>');
        });
    }

    function loadDSThuocFromDonThuoc() {
        if (!$('#cboKHOBU').val()) {
            DlgUtil.showMsg("Chưa có kho bù!");
            return;
        }

        var arrMBP = [];
        var ids = $("#grdDS_DONTHUOC_VT").jqGrid('getGridParam', 'selarrrow');
        for (var i = 0; i < ids.length; i++) {
            var row = $("#grdDS_DONTHUOC_VT").jqGrid('getRowData', ids[i]);
            arrMBP.push(row.MAUBENHPHAMID);
        }

        var obj = {
            loainhommbp: _Loainhommaubenhphamid + "",
            dsmbp: arrMBP.join(",") + "",
            khoke: $('#cboMA_KHO').val() + ""
        }
        var param = [{name: "[0]", value: JSON.stringify(obj)}];
        GridUtil.loadGridBySqlPage("grdDS_THUOC_VT", "NTU02D138.NK.L05", param, null, 'POST');
    }

    function loadTTBenhNhanFromMBP(row) {
        var obj = {
            loaiNhomMBP: _Loainhommaubenhphamid + "",
            maubenhphamID: row.MAUBENHPHAMID + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D044NK.NL03", param); // NTU02D044.03
        if (result && result.length > 0) {
            var _row = result[0];
            $("#txtPHONG").val(_row.PHONG);
            $("#txtBENH_AN").val(_row.BENH_AN);
            $("#txtBENH_KEMTHEO").val(_row.CHANDOAN_KEMTHEO);
            $("#txtBENH_NHAN").val(_row.TENBENHNHAN);
            $("#txtBAC_SI").val(_row.BACSI_CHIDINH);
            $("#txtGHI_CHU").val(_row.GHICHU);
        }
    }

    function generateGridDSDonThuoc() {
        var _gridDonThuocVatTuHeader =
            "Mã bệnh án,MAHOSOBENHAN,80,0,f,l" +
            ";Mã bệnh nhân,MABENHNHAN,80,0,f,l" +
            ";Bệnh nhân,TENBENHNHAN,110,0,f,l" +
            ";Ngày sử dụng,NGAYMAUBENHPHAM_SUDUNG,80,0,f,l" +
            ";Số phiếu,SOPHIEU,60,0,f,l" +
            ";Phiếu lĩnh,PHIEU_LINH,60,0,ns,l" +
            ";BS Chỉ định,OFFICER_NAME,113,0,f,l" +
            ";Phòng,ORG_NAME,80,0,f,l" +
            ";Kho,TEN_KHO,0,0,t,l" +
            ";MAUBENHPHAMID,MAUBENHPHAMID,0,0,t,l" +
            ";TRANGTHAIID,TRANGTHAIID,0,0,t,l" +
            ";LOAIKEDON,LOAIKEDON,0,0,t,l"
        ;

        GridUtil.init("grdDS_DONTHUOC_VT", "100%", "325px", "Danh sách đơn " + _alert_text
            , true, _gridDonThuocVatTuHeader, false, {
                rowNum: 300
                , rowList: [300, 500, 1000]
            });
        GridUtil.setGridParam("grdDS_DONTHUOC_VT", {
            onSelectRow: function (id, selected) {
                var row = $("#grdDS_DONTHUOC_VT").jqGrid('getRowData', id);
                GridUtil.unmarkAll("grdDS_DONTHUOC_VT");
                GridUtil.markRow("grdDS_DONTHUOC_VT", id);

                loadDSThuocFromDonThuoc();
                loadTTBenhNhanFromMBP(row);
            },
            onSelectAll: function (aRowids, status) {
                loadDSThuocFromDonThuoc();
            },
            loadComplete: function (id) {

            }
        });
    }

    function generateGridDsThuoc() {
        var _gridDSThuocVatTuHeader =
            "Mã " + _alert_text + ",MA_THUOC,150,0,f,l" +
            ";Tên,TEN_THUOC,150,0,f,l" +
            ";Số lượng,SLKHADUNG,0,0,t,l" +
            ";Số lượng,SLKHADUNG_LAMTRON,125,0,f,l" +
            ";THUOCVATTUID,THUOCVATTUID,0,0,t,l" +
            ";THUOCKHOBU,THUOCKHOBU,0,0,t,l" +
            ";SLKHOBU,SLKHOBU,0,0,t,l" +
            ";LOAI,LOAI,0,0,t,l"
        ;
        GridUtil.init("grdDS_THUOC_VT", "100%", "275px", "Danh sách " + _alert_text, true, _gridDSThuocVatTuHeader, false, {
            rowNum: 100,
            rowList: [100, 150, 200]
        });
        GridUtil.setGridParam("grdDS_THUOC_VT", {
            beforeSelectRow: function (rowid, e) {
                // không cho tích chọn hàng bị disable
                if ($("#jqg_grdDS_THUOC_VT_" + rowid).attr("disabled")) {
                    return false;
                }
                return true;
            },
            rowattr: function (item) {
                // disable thuốc không thuộc kho bù (khi tắt cấu hình HIS_QLLT_ATKTKB)
                if (item.THUOCKHOBU == 'KHONG') {
                    if (_CAUHINH.HIS_QLLT_ATKTKB !== '1') {
                        return {"class": "ui-state-disabled ui-jqgrid-disablePointerEvents"};
                    }
                }

                // disable thuốc bị khoá ở kho bù
                if (item.KHOATRONGKHOBU == 'CO') {
                    return { "class": "ui-state-disabled ui-jqgrid-disablePointerEvents" };
                }
            },
            loadComplete: function (id) {
                var rowIds = $('#grdDS_THUOC_VT').jqGrid('getDataIDs');
                $.each(rowIds, function (index, value) {
                    // check thuoc khong thuoc kho bu thi disable, con lai thi tich chon luon
                    var thuocKhoBu = $('#grdDS_THUOC_VT').jqGrid('getRowData', value).THUOCKHOBU;
                    var slKhoBu = $('#grdDS_THUOC_VT').jqGrid('getRowData', value).SLKHOBU;
                    if (thuocKhoBu == 'CO' && slKhoBu == 'LON_HON') {
                        jQuery('#grdDS_THUOC_VT').jqGrid('setSelection', value);
                    }

                    // UPDATE: nếu bật cấu hình HIS_QLLT_ATKTKB thì sẽ ẩn thuốc không thuộc kho bù đi (thay vì disable)
                    if (_CAUHINH.HIS_QLLT_ATKTKB == '1') {
                        if (thuocKhoBu == 'KHONG') {
                            $('#grdDS_THUOC_VT').jqGrid('delRowData', value);
                        }
                    }
                });
            }
        });

        $("#grdDS_THUOC_VT")[0].toggleToolbar();
        $("#grdDS_THUOC_VT").jqGrid("setColProp", 'SLKHADUNG', {
            formatter: myFormat
        });

        function myFormat(cellvalue, options, rowObject) {
            if (cellvalue.startsWith('.')) {
                return '0' + cellvalue;
            } else {
                return cellvalue;
            }
        }
    }

    function searchDSDonThuoc() {
        var _tu_ngay = moment($("#txtTU_NGAY").val().trim(), 'DD/MM/YYYY HH:mm:ss');
        var _den_ngay = moment($("#txtDEN_NGAY").val().trim(), 'DD/MM/YYYY HH:mm:ss');
        if (_tu_ngay > _den_ngay) {
            $("#txtTU_NGAY").focus();
            return DlgUtil.showMsg("Điều kiện tìm kiếm không hợp lệ, từ ngày phải nhỏ hơn đến ngày");
        }

        $("#grdDS_THUOC_VT").jqGrid("clearGridData", true);
        doClearText();

        loadDataGridDonThuoc();

    }

    function loadDataGridDonThuoc() {
        // hien tai san nhi phu yen, da khoa phu yen, da khoa lang son deu nhay vao case nay cho truong hop bu tu truc theo kho bu
        // ca truong hop thuoc lan vat tu
        // sql_par = [];
        // sql_par.push(
        // 	{"name":"[0]","value": _khoaid},
        // 	{"name":"[1]","value": $("#txtTU_NGAY").val()},
        // 	{"name":"[2]","value": $("#txtDEN_NGAY").val()},
        // 	{"name":"[3]","value": $("#cboMA_KHO").val()},
        // 	{"name":"[4]","value": _Loaiphieumaubenhpham},
        // 	{"name":"[5]","value": _Loainhommaubenhphamid},
        // 	{"name":"[6]","value": nvl($('#cboBACSIID').val(),'-1')},
        // 	{"name":"[7]","value": $('#cboLOAIBENHNHAN').val()}
        // );
        // GridUtil.loadGridBySqlPage("grdDS_DONTHUOC_VT", 'NTU02D044.25.BTKB', sql_par);
        //


        var tuNgay = $("#txtTU_NGAY").val();
        var denNgay = $("#txtDEN_NGAY").val();

        if (_CAUHINH.HIS_QLLT_TIMTHEOGIO == '0') {
            tuNgay = tuNgay + " 00:00:00";
            denNgay = denNgay + " 23:59:59";
        }

        var khoID = $("#cboMA_KHO").val();

        var bacSiID = nvl($('#cboBACSIID').val(), '-1');

        var loaiBN = $('#cboLOAIBENHNHAN').val();
        if (!isThLoaiBn) {
            // nếu không bật cấu hình tìm theo loại  bệnh nhân thì mặc định tìm tất cả
            // (ngoại trừ khám bệnh nếu không bật cấu hình QLLT_SHOWTHUOCKB)
            loaiBN = '-1';
        }

        var obj = {
            khoaid: _opts.khoaid + "",
            loaiPhieuMBP: _Loaiphieumaubenhpham + "",
            loaiNhomMBP: _Loainhommaubenhphamid + "",
            tuNgay: tuNgay + "",
            denNgay: denNgay + "",
            khoID: khoID + "",
            bacSi: bacSiID + "",
            loaiBN: loaiBN + "",
            chucNang: _CHUCNANG
        }
        var param = [{name: "[0]", value: JSON.stringify(obj)}];
        GridUtil.loadGridBySqlPage("grdDS_DONTHUOC_VT", "NTU02D138.L06", param); // NTU02D044.25.BTKB + NTU02D044.01TRA.N
    }

    function doClearText() {
        $("#txtPHONG").val("");
        $("#txtBENH_AN").val("");
        $("#txtBENH_NHAN").val("");
        $("#txtBENH_KEMTHEO").val("");
        $("#txtBAC_SI").val("");
        $("#txtGHI_CHU").val("");
        $("#txtGHICHU_PL").val("");
    }

    function bindEvent() {


        $("#btnSearch").on("click", function (e) {
            searchDSDonThuoc();
        });

        $('#hrDonThuoc').on('click', function (e) {
            searchDSDonThuoc();
        });

        $("#cboLOAITONGHOP").change(function () {
            searchDSDonThuoc();
        });

        $("#cboPHONG").change(function () {
            searchDSDonThuoc();
        });

        $("#cboLOAIPHIEU").change(function () {
            searchDSDonThuoc();
        });

        $('#txtTU_NGAY').on('change', function (e) {
            searchDSDonThuoc();
        });

        $('#txtDEN_NGAY').on('change', function (e) {
            searchDSDonThuoc();
        });

        $("#cboMA_KHO").change(function () {
            searchDSDonThuoc();

            // change ds kho bù theo kho kê
            // getDataComboBoxKhoBu($('#cboMA_KHO').val());

        });

        // thay đổi kho bù thi phải load lại Ds thuốc (vì liên quan đến cái disable, ẩn, hiện, chọn thuốc)
        $("#cboKHOBU").change(function () {
            loadDSThuocFromDonThuoc();
        });

        $('#btnCreateTicket').bindOnce("click",function() {
            taoPhieu();
        }, 2000);
    }

    function taoPhieu() {
        if (validateTaoPhieu() == -1) {
            return;
        }
        saveSuccess();
    }

    function validateTaoPhieu(){
        if (!($('#cboKHOBU').val())) {
            DlgUtil.showMsg("Chưa chon kho bù");
            return -1;
        }

        var loaiKho = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D044.16", [$('#cboMA_KHO').val()].join('$'));
        if (loaiKho == '-1') {
            DlgUtil.showMsg('Lấy loại kho thuốc lỗi. Kiểm tra lại dữ liệu');
            return -1;
        }

        var idsThuoc = $('#grdDS_THUOC_VT').jqGrid("getGridParam", "selarrrow");
        if (idsThuoc.length == 0) {
            DlgUtil.showMsg("Chưa chon thuốc nào");
            return -1;
        }

        // khong cho tao phieu khi co ton tai don gay nghien, huong than, gia tri cao
        if (_CAUHINH.HIS_QLLT_CHAN_THTGNGTC == '1') {
            if (kiemTraThuocHTGNGTC2(idsThuoc) == 1) {
                DlgUtil.showMsg('Tồn tại thuốc hướng thần/ gây nghiện/ giá trị cao. Không thể tạo phiếu!');
                return -1;
            }
        }

        var idsMBP = $('#grdDS_DONTHUOC_VT').jqGrid("getGridParam", "selarrrow");
        if (idsMBP.length == 0) {
            DlgUtil.showMsg("Chưa chon đơn nào");
            return -1;
        }

        var mbpIDArr = [];
        for (var i = 0; i < idsMBP.length; i++) {
            var row = $("#grdDS_DONTHUOC_VT").jqGrid('getRowData', idsMBP[i]);
            mbpIDArr.push(row.MAUBENHPHAMID);
        }

        var check_thuocle = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D044.32", mbpIDArr.join(",")); // cau hinh HIS_NTU_CHAN_THUOC_LE
        if (check_thuocle == 0) {
            DlgUtil.showMsg('Thuốc/ vật tư tổng hợp có số lượng lẻ. Không thể tạo phiếu');
            return -1;
        }


        return 1;

    }

    function kiemTraThuocHTGNGTC2(dsThuoc) {
        for (var i = 0; i < dsThuoc.length; i++) {
            var ret = $("#grdDS_THUOC_VT").jqGrid('getRowData', dsThuoc[i]);
            var loaithuoc = ret.LOAI;
            if (loaithuoc == 6 || loaithuoc == 7 || loaithuoc == 20) {
                return 1;
            }
        }
        return 0;
    }

    function saveSuccess() {

        var idsThuoc = $('#grdDS_THUOC_VT').jqGrid("getGridParam", "selarrrow");
        var idsMBP = $('#grdDS_DONTHUOC_VT').jqGrid("getGridParam", "selarrrow");

        var mbpIDArr = [];
        for (var i = 0; i < idsMBP.length; i++) {
            var row = $("#grdDS_DONTHUOC_VT").jqGrid('getRowData', idsMBP[i]);
            mbpIDArr.push(row.MAUBENHPHAMID);
        }

        var thuocIDArr = [];
        for (var i = 0; i < idsThuoc.length; i++) {
            var thuocId = $('#grdDS_THUOC_VT').getRowData(idsThuoc[i]).THUOCVATTUID;
            thuocIDArr.push(thuocId);
        }

        // goi ham ben duoc
        var i_chucnanggoi = "NTU02D138";
        var i_KhoLapID = $('#cboMA_KHO').val();
        //var i_KhoDoiUngID = $('#cboKHOBU').val();
        var i_DSMauBenhPhamID = mbpIDArr.join(",");
        var i_DSThuocVatTuID = thuocIDArr.join(",");
        var i_ThongTinNhapXuat = "";

        var par = [
            i_chucnanggoi,
            i_KhoLapID,
            //i_KhoDoiUngID,
            i_DSMauBenhPhamID,
            i_DSThuocVatTuID,
            i_ThongTinNhapXuat
        ];
        var kqTaoPhieu = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D138.NK.L03", par.join('$'));
        var ret = kqTaoPhieu.split("@-@");
        if (ret[0] == '0') {
            var obj = JSON.parse(ret[1]);
            DlgUtil.showMsg(obj.MESSAGE);
        } else {
            if (_CAUHINH.HIS_NOPRINT_PL == '0') {
                doPrint(1, ret[0], _call_report);
            }

            DlgUtil.showMsg("Tạo phiếu " + _alert_text_loai + " " + _alert_text + " thành công!");
            searchDSDonThuoc();
        }
    }

}

function phieuLinhThuocVT(_opts) {

    var _company_id = _opts._hospital_id;
    var _khoaid = _opts.khoaid;
    var _loai_khothuoc = "";
    var _kieuPhieu = "";
    var _hinhthucid = "";
    var _Loainhommaubenhphamid = "";
    var _ylenh_id = "";
    var sql_par = [];
    var _alert_text = "";
    var _call_report = "";
    var isInTongHopPdf = false;//HaNv_03112019: In tong hop phieu linh PDF - DKLAN

    this.load = doLoad;

    function doLoad() {
        initControl();
        bindEvent();
        setInterface();
    }

    function initControl() {
        getVariable();

        generateGridDSPhieu();
        generateGridDsThuoc2();
    }

    function setInterface() {
        // start jira 27768
        if (_CAUHINH.QLLT_DIVTKPL == '1') {
            $('#divTIMKIEMPHIEULINH').show();
        }
        // end jira 27768

        // ẩn/ hiện các nút in ở tab THLT
        if (_CAUHINH.QLLTNK_SHOW_INBTNTHLT == '1') {
            $('#divBtnTabTHLT').show();
        } else {
            $('#divBtnTabTHLT').hide();
        }

        // mặc định ẩn nút huỷ phiếu ở tab THLT
        $('#btnHuyPhieu').hide();
    }

    function getVariable() {
        if (_CHUCNANG == "5_BU_THUOC") {
            _loai_khothuoc = "8,10,13";
            _kieuPhieu = 3;
            _hinhthucid = 9;
            _Loainhommaubenhphamid = 7;
            _call_report = "LINH_THUOC";
            _alert_text = "thuốc";
        } else if (_CHUCNANG == "6_BU_VATTU") {
            _loai_khothuoc = "9,11,13";
            _kieuPhieu = 3;
            _hinhthucid = 9;
            _Loainhommaubenhphamid = 8;
            _call_report = "LINH_VATTU";
            _alert_text = "vật tư";
        } else if (_CHUCNANG == "10_TRA_BU_THUOC") {
            _loai_khothuoc = "8,10,13";
            _kieuPhieu = 2;
            _hinhthucid = 9;
            _Loainhommaubenhphamid = 7;
            _alert_text = "thuốc";
            _call_report = "TRA_TT_THUOC";
        } else if (_CHUCNANG == "11_TRA_BU_VATTU") {
            _loai_khothuoc = "9,11,13";
            _kieuPhieu = 2;
            _hinhthucid = 9;
            _Loainhommaubenhphamid = 8;
            _call_report = "TRA_TT_VATTU";
            _alert_text = "vật tư";
        }
    }

    function loadDSThuocFromPhieu(row) {
        var obj = {
            nhapXuatID: row.YLENHLINHTHUOC + ""
        }
        var param = [{
            name: "[0]",
            value: JSON.stringify(obj)
        }];
        GridUtil.loadGridBySqlPage("grdPL_DS_THUOCVT", "NTU02D044.DK.L05", param); // copy from NTU02D044.12.NEW
    }

    function loadTTChiTietPhieu(row) {
        var obj = {
            khoaID: _opts.khoaid + "",
            nhapXuatID: row.YLENHLINHTHUOC + ""
        }
        var param = JSON.stringify(obj);
        var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D044.DK.L06", param);
        if (result && result.length > 0) {
            var _row = result[0];
            $("#txtPL_MAPHIEU").val(_row.MA);
            $("#txtPL_NGUOILAP").val(_row.USER_NAME);
            $("#txtPL_NGAYLAP").val(_row.NGAYTHUCHIEN);
            $("#txtPL_NGUOIDUYET").val(_row.NGUOIDUYET);
            $("#txtPL_KHO").val(_row.TENKHO);
            $("#txtPL_GHICHU").val(_row.GHICHU);
            $("#hidHUONGTHAN").val(_row.HUONGTHAN);
            $("#hidREPORTNAME").val(_row.DANHSACHBC);
        } else {
            clearTTChiTietPhieu();
        }
    }

    function generateGridDSPhieu() {
        var _gridPL_DSPhieuHeader =
            " ,ICON,35,0,ns,l" +
            ";Mã phát,MA,100,0,f,l" +
            ";Mã duyệt,MATHC,100,0,f,l" +
            ";Trạng thái phát,TRANGTHAIPHAT,100,0,f,l" +
            ";Trạng thái duyệt,TRANGTHAIDUYET,100,0,f,l" +
            //";Loại,TEN,120,0,f,l" +
            //";Trạng thái,TEN_TRANGTHAI,100,0,f,l" +
            ";Thời gian,NGAYTHUCHIEN,200,0,f,l" +
            ";YLENHLINHTHUOC,YLENHLINHTHUOC,0,0,t,l" +
            ";TRANGTHAIID,TRANGTHAIID,0,0,t,l" +
            ";Kho nhận,TENKHO,120,0,f,l" +
            ";Người lập phiếu,NGUOITHUCHIEN,150,0,f,l" +
            ";TTXUAT,TTXUAT,0,0,t,l" +
            ";TTNHAP,TTNHAP,0,0,t,l"
        ;
        GridUtil.init("grdDS_PHIEULINH", "480", "427", "Danh sách phiếu", false, _gridPL_DSPhieuHeader, false);
        GridUtil.setGridParam("grdDS_PHIEULINH", {
            onSelectRow: function (id) {
                GridUtil.unmarkAll("grdDS_PHIEULINH");
                GridUtil.markRow("grdDS_PHIEULINH", id);
                var row = $("#grdDS_PHIEULINH").jqGrid('getRowData', id);
                loadDSThuocFromPhieu(row);
                loadTTChiTietPhieu(row);

            },
            loadComplete: function (id) {
                var ids = $("#grdDS_PHIEULINH").getDataIDs();
                for (var i = 0; i < ids.length; i++) {
                    var id = ids[i];
                    var row = $("#grdDS_PHIEULINH").jqGrid('getRowData', id);
                    if (row.TRANGTHAIID == 7) {
                        GridUtil.markRow("grdDS_PHIEULINH", id, 'markedRowWaring');
                    } else {
                        if (row.TTXUAT == 1) {
                            $("#grdDS_PHIEULINH").jqGrid('setRowData', id, "", {
                                color: 'red'
                            });

                            $('#grdDS_PHIEULINH').find("tr[id='" + id + "']").find('td').each(function (index, element) {
                                $(element).css('background-color', '#FF9900');
                            });
                        }
                    }
                }
            }
        });
    }

    function generateGridDsThuoc2() {
        var _gridPL_DSThuocVTHeader =
            "Mã " + _alert_text + ",MA_THUOC,50,0,f,l" +
            ";Tên,TEN_THUOC,100,0,f,l" +
            ";Số lượng,SLKHADUNG,60,0,f,l,0" +
            ";THUOCVATTUID,THUOCVATTUID,0,0,t,l";
        GridUtil.init("grdPL_DS_THUOCVT", "100%", "288", "Danh sách " + _alert_text, false, _gridPL_DSThuocVTHeader, true, {
            rowNum: 200,
            rowList: [200, 250, 300]
        });
        $("#grdPL_DS_THUOCVT")[0].toggleToolbar();

    }

    function clearTTChiTietPhieu() {
        $("#txtPL_MAPHIEU").val("");
        $("#txtPL_NGUOILAP").val("");
        $("#txtPL_NGAYLAP").val("");
        $("#txtPL_KHO").val("");
        $("#txtPL_GHICHU").val("");
        $("#hidHUONGTHAN").val("");
        $("#hidREPORTNAME").val("");
    }

    function searchDSPhieu() {
        clearTTChiTietPhieu();
        $("#grdPL_DS_THUOCVT").jqGrid("clearGridData", true);
        loadDataGridPhieu();
    }

    function loadDataGridPhieu() {
        // var obj = {
        //     khoaID: _opts.khoaid + "",
        //     kieuPhieu: _kieuPhieu + "",
        //     loaiKhoThuoc: _loai_khothuoc + "",
        //     hinhThucID: _hinhthucid + "",
        //     loainhomMBP: _Loainhommaubenhphamid + "",
        //     chucNang: _optFunc,
        //     // start jira 27768
        //     tuNgay: $('#txtPLTUNGAY').val(),
        //     denNgay: $('#txtPLDENNGAY').val(),
        //     khoID: $('#cboPLKHO').val(),
        //     trangThai: $('#cboPLTRANGTHAI').val()
        //     // end jira 27768
        // }
        // var param = [{name: "[0]", value: JSON.stringify(obj)}];
        // GridUtil.loadGridBySqlPage("grdDS_PHIEULINH", "NTU02D044.DK.L02", param); //NTU02D044.07 + NTU02D044.14

        var obj = {
            khoaID:  _opts.khoaid + "",
            kieuPhieu: _kieuPhieu + "",
            loaiKhoThuoc: _loai_khothuoc + "",
            hinhThucID: _hinhthucid + "",
            loainhomMBP: _Loainhommaubenhphamid + ""
        }
        var param = [{name: "[0]", value: JSON.stringify(obj)}];
        GridUtil.loadGridBySqlPage("grdDS_PHIEULINH", "NTU02D044NK.NL07", param); // COPY FROM NTU02D044.07 DAKHOA
    }

    function huyPhieu() {
        if (validateHuyPhieu() == -1) {
            return;
        }

        var id = $("#grdDS_PHIEULINH").jqGrid('getGridParam', 'selrow');
        var row = $("#grdDS_PHIEULINH").jqGrid('getRowData', id);

        if (confirm("Bạn có muốn hủy phiếu " + row.MA + " không?")) {
            saveHuyPhieu(row);
        }
    }

    function validateHuyPhieu() {
        var idx = $("#grdDS_PHIEULINH").jqGrid('getGridParam', 'selrow');
        if (idx == null) {
            DlgUtil.showMsg("Vui lòng chọn 1 phiếu để hủy");
            return -1;
        }

        var data = $("#grdDS_PHIEULINH").jqGrid('getRowData', idx);
        if (data.MA.includes("BULE")) {
            DlgUtil.showMsg("Phiếu tổng hợp lẻ vui lòng hủy ở màn hình quản lý thuốc lẻ");
            return -1;
        }

        return 1;
    }

    function saveHuyPhieu(row) {
        var nhapxuatid = row.YLENHLINHTHUOC;
        var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D044.10", nhapxuatid);
        if (ret == 1) {
            DlgUtil.showMsg("Hủy phiếu thành công!");
            searchDSPhieu();
        } else {
            DlgUtil.showMsg("Lỗi khi hủy phiếu ! " + ret);
        }
    }

    function bindEvent() {

        $('#hrPhieuLinh').on('click', function (e) {
            searchDSPhieu();
        });

        $('#btnHuyPhieu').bindOnce("click",function() {
            huyPhieu();
        }, 2000);

        $("#btnSearchPL").on("click", function (e) {
            searchDSPhieu();
        });

        $('#txtPLTUNGAY').on('change', function (e) {
            searchDSPhieu();
        });

        $('#txtPLDENNGAY').on('change', function (e) {
            searchDSPhieu();
        });

        $('#cboPLKHO').on('change', function (e) {
            searchDSPhieu();
        });

        $('#cboPLTRANGTHAI').on('change', function (e) {
            searchDSPhieu();
        });


        $("#btnInPhieu").on("click", function (e) {
            var idx = $("#grdDS_PHIEULINH").jqGrid('getGridParam', 'selrow');
            if (idx != null && idx.length != '') {
                var data = $("#grdDS_PHIEULINH").jqGrid('getRowData', idx);

                if (_CAUHINH.HIS_PRINT_BYSTATUS_ACCEPT == '1') {
                    var sql_par = [{
                        "name": "[0]",
                        "value": data.YLENHLINHTHUOC
                    }];
                    var result = jsonrpc.AjaxJson.ajaxExecuteQueryO("NTU02D044.33", sql_par);
                    if (result != null) {
                        var jsonResult = JSON.parse(result);
                        if (jsonResult[0].TRANGTHAIID != '6') {
                            DlgUtil.showMsg("Phiếu lĩnh chưa được duyệt, không được phép in");
                            return;
                        }
                    }
                }


                //cau hinh kiem tra neu thuoc da duyet phat thi ko cho in
                if (_CAUHINH.PHARMA_SHOW_INPHIEULINH_DUYETPHAT == '1') {
                    var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("DUC01S002.KT.PTHUOC", data.YLENHLINHTHUOC);
                    if (ret == 4) {
                        return DlgUtil.showMsg("Không in được do phiếu đã được duyệt phát thuốc.");
                    }
                }

                doPrint(1, data.YLENHLINHTHUOC, _call_report);

            } else {
                DlgUtil.showMsg("Bạn phải chọn phiếu để in!");
            }

        });

        $("#btnInTongHopXLXS").on("click", function (e) {
            var idx = $("#grdDS_PHIEULINH").jqGrid('getGridParam', 'selrow');
            if (idx != null && idx.length != '') {
                var data = $("#grdDS_PHIEULINH").jqGrid('getRowData', idx);
                doPrint(2, data.YLENHLINHTHUOC, _call_report);
            } else {
                DlgUtil.showMsg("Bạn phải chọn phiếu để in!");
            }
        });

        $("#btnInTongHopPDF").on("click", function (e) {
            isInTongHopPdf = true;
            var idx = $("#grdDS_PHIEULINH").jqGrid('getGridParam', 'selrow');
            if (idx != null && idx.length != '') {
                var data = $("#grdDS_PHIEULINH").jqGrid('getRowData', idx);
                doPrint(2, data.YLENHLINHTHUOC, _call_report, 'pdf');
            } else {
                DlgUtil.showMsg("Bạn phải chọn phiếu để in!");
            }

        });

    }


}

function tab3(_opts){
    this.load = doLoad;
    var _Loainhommaubenhphamid;
    var kieuPhieu;
    function doLoad() {
        initControl();
        bindEvent();
        setInterface();
    }

    function initControl(){
        if (_optFunc == "PHIEU_TT_THUOC") {
            _Loainhommaubenhphamid = 7;
            kieuPhieu = 3;
        } else if (_optFunc == "PHIEU_TT_VATTU") {
            _Loainhommaubenhphamid = 8;
            kieuPhieu = 3;
        } else if (_optFunc == "TRA_TT_THUOC") {
            _Loainhommaubenhphamid = 7;
            kieuPhieu = 2;
        } else if (_optFunc == "TRA_TT_VATTU") {
            _Loainhommaubenhphamid = 8;
            kieuPhieu = 2;
        }

        generateGridDSPhieuTC();
        generateGridDsThuocTC();
    }

    function bindEvent(){

        $('#tabDSPHIEUCHATab').on('click', function(e) {
            loadPhieuLinhCha();
        });

        $("#btnInPhieuLinhChung").on("click",function(e) {
            var row = $("#grdDS_PHIEULINH_TC").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdDS_PHIEULINH_TC").jqGrid('getRowData', row).NHAPXUATID;
            //var kieu = $("#grdDS_PHIEULINH_TC").jqGrid('getRowData', row).KIEU;
            if (_id == "" || _id == undefined){
                return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);
            }

            if (_CAUHINH.QLLTNK_INKHIDUYETPHAT == '1') {
                // chỉ in khi tất cả các phiếu lĩnh đã duyệt
                if (!isTatCaPhieuLinhDaDuyet()) {
                    DlgUtil.showMsg("Chỉ được phép in khi tất cả các phiếu lĩnh đã duyệt");
                    return;
                }
            } else if (_CAUHINH.QLLTNK_INKHIDUYETPHAT == '2') {
                // chỉ in khi tất cả các phiếu lĩnh đã duyệt và đã phát
                if (!(isTatCaPhieuLinhDaDuyet() && isTatCaPhieuLinhDaPhat())) {
                    DlgUtil.showMsg("Chỉ được phép in khi tất cả các phiếu lĩnh đã duyệt và đã phát");
                    return;
                }
            }


            var ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01.DSPL.THLT", _id);
            if (ret == '0') {
                DlgUtil.showMsg("Có lỗi xảy ra trên hệ thống!");
            } else {
                var arr = ret.split(",");
                arr.forEach(function (value, index, array) {
                    doPrint(1, value);
                })
            }

        });

        $("#btnPrintTotalTicket").on("click",function(e) {
            var row = $("#grdDS_PHIEULINH_TC").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdDS_PHIEULINH_TC").jqGrid('getRowData', row).NHAPXUATID;
            //var kieu = $("#grdDS_PHIEULINH_TC").jqGrid('getRowData', row).KIEU;
            if (_id == "" || _id == undefined)
                return DlgUtil.showMsg("Chưa chọn phiếu!",undefined,2000);

            var par = [
                {
                    name : 'nhapxuatid',//nhapxuatid
                    type : 'String',
                    value : _id
                }];
            var rpName= "NTU008_SOTONGHOPTHUOCHANGNGAY_THC" + jsonrpc.AjaxJson.getSystemDate('DDMMYY-HH24MISS') +"." + 'xls';
            CommonUtil.inPhieu('window', "NTU008_SOTONGHOPTHUOCHANGNGAY_THC", 'xls', par, rpName);

            openReport('window','NTU008_SOTONGHOPTHUOCHANGNGAY_THC',
                'pdf', par);

        });

        $("#btnDelTicket").on("click",function(e){

            var row = $("#grdDS_PHIEULINH_TC").jqGrid('getGridParam', 'selrow');
            var _id = $("#grdDS_PHIEULINH_TC").jqGrid('getRowData', row).NHAPXUATID;
            if(_id == ''){
                DlgUtil.showMsg("Chưa chọn phiếu tổng hợp");
                return;
            }
            var result = jsonrpc.AjaxJson.ajaxCALL_SP_S("DUC01S002.HUYTHC",_id);
            var ret = JSON.parse(result);
            if(ret.SUCCESS>0){
                DlgUtil.showMsg(ret.MESSAGE);
                $("#grdPL_DS_THUOCVT_TC").jqGrid("clearGridData", true);
                loadPhieuLinhCha();
            }
            else{
                DlgUtil.showMsg(ret.MESSAGE);
            }

        });
    }

    function setInterface(){
        // ẩn/ hiện tab THC
        if (_CAUHINH.QLLTNK_SHOW_TABTHC == '1') {
            $('#tabDSPHIEUCHATab').show();
        } else {
            $('#tabDSPHIEUCHATab').hide();
        }
    }

    function generateGridDSPhieuTC (){
        var _gridHeader = "NHAPXUATID,NHAPXUATID,0,0,t,l;" +
            "NGAYDUYET,NGAYDUYET,0,0,t,l;" +
            "NGUOIDUYET,NGUOIDUYET,0,0,t,l;" +
            "NGUOINX,NGUOINX,0,0,t,l;" +
            "Mã phiếu,MA,90,0,f,l;" +
            "Ngày y lệnh,NGAYYLENH,100,0,f,l;" +
            "Ngày lập phiếu,NGAYLAPPHIEU,100,0,f,l;"+
            "Khoa,KHOA,100,0,f,l;" +
            "Ghi chú,GHICHU,200,0,f,l";
        GridUtil.init("grdDS_PHIEULINH_TC","480","370px","Danh sách phiếu",false,_gridHeader,false,{
            rowNum: 300,
            rowList : [ 300 ]
        });
        $('#grdDS_PHIEULINH_TC').jqGrid('setGridParam', {
            onSelectRow: function(id){
                GridUtil.unmarkAll('grdDS_PHIEULINH_TC');
                GridUtil.markRow('grdDS_PHIEULINH_TC',id);
                var ret = $("#grdDS_PHIEULINH_TC").jqGrid('getRowData',id);

                $("#txtPL_MAPHIEU_TC").val(ret.MA);
                $("#txtPL_NGAYLAP_TC").val(ret.NGAYLAPPHIEU);
                $("#txtPL_KHOA_TC").val(ret.KHOA);
                $("#txtPL_NGUOILAP_TC").val(ret.NGUOINX);
                $("#txtPL_NGUOIDUYET_TC").val(ret.NGUOIDUYET);
                $("#txtPL_GHICHU_TC").val(ret.GHICHU);

                if(ret.NHAPXUATID != ''){
                    var sql_par=RSUtil.buildParam("",[ret.NHAPXUATID]);
                    GridUtil.loadGridBySqlPage("grdPL_DS_THUOCVT_TC","DUC01S002.DS_PL01",sql_par);
                }

            }
        });
    }

    function generateGridDsThuocTC(){
        var _gridHeader =
            "NHAPXUATID,NHAPXUATID,0,0,t,l" +
            ";Mã phiếu,MA,100,0,f,l" +
            ";Kho,KHO,100,0,f,l" +
            ";Trạng thái phát,TRANGTHAIPHAT,100,0,f,l" +
            ";Trạng thái duyệt,TRANGTHAIDUYET,100,0,f,l" +
            ";Ngày lập phiếu,NGAYLAPPHIEU,100,0,f,l"+
            ";Ghi chú,GHICHU,220,0,f,l";
        GridUtil.init("grdPL_DS_THUOCVT_TC","100%","270px","Tổng hợp danh sách phiễu lĩnh",false,_gridHeader,false,{
            rowNum: 300,
            rowList : [ 300 ]
        });
    }

    function loadPhieuLinhCha(){
        // var sql_par = RSUtil.buildParam("",[27,_opts.khoaid,_Loainhommaubenhphamid]);
        // GridUtil.loadGridBySqlPage("grdDS_PHIEULINH_TC","DUC01S002.DSTHLT",sql_par,function(){
        // });


        var obj = {
            hinhThucID: 27 + "",
            khoaID: _opts.khoaid + "",
            loaiNhomMBP: _Loainhommaubenhphamid + "",
            kieuPhieu: kieuPhieu + "",
        }

        var param = [{
            name: "[0]",
            value: JSON.stringify(obj)
        }];
        GridUtil.loadGridBySqlPage("grdDS_PHIEULINH_TC", "NTU02D138.NK.L06",param);
    }

    function isTatCaPhieuLinhDaDuyet(){
        var dataIDs = $('#grdPL_DS_THUOCVT_TC').getDataIDs();
        for(var i = 0; i < dataIDs.length; i++) {
            var rowData = $('#grdPL_DS_THUOCVT_TC').jqGrid ('getRowData', dataIDs[i]);
            if (rowData.TRANGTHAIDUYET != 'Đã duyệt') {
                return false;
            }
        }
        return true;
    }

    function isTatCaPhieuLinhDaPhat(){
        var dataIDs = $('#grdPL_DS_THUOCVT_TC').getDataIDs();
        for(var i = 0; i < dataIDs.length; i++) {
            var rowData = $('#grdPL_DS_THUOCVT_TC').jqGrid ('getRowData', dataIDs[i]);
            if (rowData.TRANGTHAIPHAT != 'Đã phát') {
                return false;
            }
        }
        return true;
    }

}

function doPrint(_print_type, _ylenh_id, _call_report, filetype) {
    var _param, _report_name, _report_huongthan, _report_hoachat, _report_thuoctieuhao, _freport;

    var ret;
    if (_CAUHINH.PHARMA_LAY_DSPHIEUIN_MOI == '1') {
        ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D044.15.NEW",
            _ylenh_id);
    } else {
        ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D044.15",
            _ylenh_id);
    }


    _param = [{
        name: 'nhapxuatid',
        type: 'String',
        value: _ylenh_id
    }, {
        name: 'nhomreportcode',
        type: 'String',
        value: ret
    }
    ];

    var typeIn = "";
    if (filetype) {
        typeIn = filetype;
    } else {
        var data_ar = _CAUHINH.NTU_IN_TONGHOP_PHIEULINH;
        if (!data_ar) {
            typeIn = "pdf";
        } else {
            typeIn = data_ar;
        }
    }


    if (ret != null && ret != '' && _print_type == 1) {
        //tuyendv 23/06 in gop phieu linh tren 1 tab
        var kieustr = jsonrpc.AjaxJson.ajaxExecuteQueryO('DUC.000005', [{"name": "[0]", "value": _ylenh_id}]);
        var kieujson = JSON.parse(kieustr);
        if (_CAUHINH.NTU_INPHIEULINH_1TAB == '1' && kieujson[0].KIEU == '3') {
            var _reportCode = 'NTU_IN_TONGHOP_PHIEULINH_ALL';
            CommonUtil.openReportGet('window', _reportCode, "pdf", _param);
        } else {
            _freport = ret.split(";");
            for (var i = 0; i < _freport.length; i++) {
                //					openReport('window', _freport[i].trim(), "pdf", _param);
                CommonUtil.openReportGet('window', _freport[i].trim(), "pdf", _param);
                //DoanPV_20191211 L2PT-12880 in phieu vaccine
                if (_freport[i].trim() == 'DUC_PHIEULINHVACCINE') {
                    CommonUtil.openReportGet('window', 'DUC_PHIEUVACCINE_1111', "pdf", _param);
                    // start L2PT-13588: bo sung them lien 2 luu khoa duoc
                    CommonUtil.openReportGet('window', 'DUC_PHIEUVACCINE_2_1111', "pdf", _param);
                    // end L2PT-13588: bo sung them lien 2 luu khoa duoc
                }
            }
        }
        //end tuyendv 23/06
    } else {
        switch (_call_report) {
            case "LINH_THUOC":
                if (_print_type == 1) {
                    _report_name = "NTU004_PHIEULINHTHUOC_01DBV01_TT23_A4";
                    _report_huongthan = "NTU025_PHIEULINHTHUOCTPGAYNGHIENHUONGTHANTIENCHAT_MS8_TT192014_A4";
                } else {
                    // bu theo kho bu
                    if (_CAUHINH.NTU_QLLT_INTHPLBTKB == '1') {
                        _report_name = "NTU008_SOTONGHOPTHUOCHANGNGAY_14DBV01_LINHBU_TT23_A2";
                    } else {
                        _report_name = "NTU008_SOTONGHOPTHUOCHANGNGAY_14DBV01_TT23_A2";
                    }
                }
                break;
            case "LINH_VATTU":
                if (_print_type == 1) {
                    _report_name = "NTU067_PHIEULINHVATTU_01DBV01_TT23_A4";
                    _report_hoachat = "NTU005_PHIEULINHHOACHAT_02DBV01_TT23_A4";
                } else {
                    // bu theo kho bu
                    if (_CAUHINH.NTU_QLLT_INTHPLBTKB == '1') {
                        _report_name = "NTU008_SOTONGHOPTHUOCHANGNGAY_14DBV01_LINHBU_TT23_A2";
                    } else {
                        _report_name = "NTU008_SOTONGHOPTHUOCHANGNGAY_14DBV01_TT23_A2";
                    }
                }
                break;
            case "TRA_THUOC":
                if (_print_type == 1) {
                    _report_name = "NTU007_PHIEUTRALAITHUOCHOACHATVTYTTIEUHAO_05DBV01_TT23_A4";
                    _report_thuoctieuhao = "NTU007_PHIEUTRALAITHUOCHOACHATVTYTTIEUHAO_05DBV01_TT23_A4";
                } else
                    _report_name = "NTU068_SOTONGHOPTRATHUOCHANGNGAY_A2";
                break;
            case "TRA_VATTU":
                if (_print_type == 1) {
                    _report_name = "NTU007_PHIEUTRALAITHUOCHOACHATVTYTTIEUHAO_05DBV01_TT23_A4";
                } else
                    _report_name = "NTU068_SOTONGHOPTRATHUOCHANGNGAY_A2";
                break;
            case "TRA_TT_THUOC":
                if (_print_type == 2) {
                    _report_name = "NTU068_SOTONGHOPTRATHUOCHANGNGAY_A2";
                }
                break;
            case "TRA_TT_VATTU":
                if (_print_type == 2) {
                    _report_name = "NTU068_SOTONGHOPTRATHUOCHANGNGAY_A2";
                }
                break;
        }

        if ($('#hidREPORTNAME').val() != '' && _print_type == 1) {
            _freport = $('#hidREPORTNAME').val().split(";");
            for (var i = 0; i < _freport.length; i++) {
                CommonUtil.openReportGet('window', _freport[i].trim(), "pdf", _param);
            }
        } else {
            if (typeIn.toUpperCase() == 'XLS' || typeIn.toUpperCase() == 'XLSX') {
                CommonUtil.inPhieu('window', _report_name, typeIn, _param, _report_name + "." + typeIn);
            } else {
                openReport('window', _report_name, typeIn, _param);
            }
        }

    }
}
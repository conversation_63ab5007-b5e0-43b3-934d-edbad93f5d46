function NTU02D165_BTDCSNB(opt) {

	var arrButtonHideShow = [
		{
			idButton: "btndivMACH",
			idElement: "txtMACHROW1"
		}
		,{
			idButton: "btndivHUYETAP",
			idElement: "txtHUYETAPROW1"
		}
		,{
			idButton: "btndivNHIETDO",
			idElement: "txtNHIETDOROW1"
		}
		,{
			idButton: "btndivSPO2",
			idElement: "txtSPO2ROW1"
		}
		,{
			idButton: "btndivNHIPTHO",
			idElement: "txtNHIPTHOROW1"
		}
		,{
			idButton: "btndivGLASGOW",
			idElement: "txtGLASGOWROW1"
		}
		,{
			idButton: "btndivRASS",
			idElement: "txtRASSROW1"
		}
		,{
			idButton: "btndivSLNUOCTIEU",
			idElement: "txtSLNUOCTIEUROW1"
		}
		,{
			idButton: "btndivMAUNUOCTIEU",
			idElement: "txtMAUNUOCTIEUROW1"
		}
		,{
			idButton: "btndivSLPHAN",
			idElement: "txtSLPHANROW1"
		}
		,{
			idButton: "btndivMAUPHAN",
			idElement: "txtMAUPHANROW1"
		}
		,{
			idButton: "btndivVORUNGHUTDOM",
			idElement: "cboVORUNGHUTDOMROW1"
		}
		,{
			idButton: "btndivMAUDOM",
			idElement: "txtMAUDOMROW1"
		}
		,{
			idButton: "btndivTHOMAY",
			idElement: "chkTHOMAYROW1_CHK"
		}
		,{
			idButton: "btndivTHOOXY",
			idElement: "chkTHOOXYROW1_CHK"
		}
		,{
			idButton: "btndivVSRM",
			idElement: "chkVSRMROW1_CHK"
		}
		,{
			idButton: "btndivTAM",
			idElement: "chkTAMROW1_CHK"
		}
		,{
			idButton: "btndivGOIDAU",
			idElement: "chkGOIDAUROW1_CHK"
		}
		,{
			idButton: "btndivVSCNKHAC",
			idElement: "chkVSCNKHACROW2_CHK"
		}
		,{
			idButton: "btndivTHAYBANGNKQ",
			idElement: "chkTHAYBANGNKQROW1_CHK"
		}
		,{
			idButton: "btndivTHAYBANGMKQ",
			idElement: "chkTHAYBANGMKQROW1_CHK"
		}
		,{
			idButton: "btndivTHAYBANGCATHETER",
			idElement: "chkTHAYBANGCATHETERROW1_CHK"
		}
		,{
			idButton: "btndivTHAYBANGVETLOET",
			idElement: "chkTHAYBANGVETLOETROW1_CHK"
		}
		,{
			idButton: "btndivTHAYBANGKHAC",
			idElement: "chkTHAYBANGKHACROW2_CHK"
		}
		,{
			idButton: "btndivDATSONDETIEU",
			idElement: "chkDATSONDETIEUROW1_CHK"
		}
		,{
			idButton: "btndivDATSONDEDADAY",
			idElement: "chkDATSONDEDADAYROW1_CHK"
		}
		,{
			idButton: "btndivDATSONDEHAUMON",
			idElement: "chkDATSONDEHAUMONROW1_CHK"
		}
		,{
			idButton: "btndivDATSONDEKHAC",
			idElement: "chkDATSONDEKHACROW2_CHK"
		}
		,{
			idButton: "btndivBOMRUASONDETIEU",
			idElement: "chkBOMRUASONDETIEUROW1_CHK"
		}
		,{
			idButton: "btndivBOMRUASONDEDADAY",
			idElement: "chkBOMRUASONDEDADAYROW1_CHK"
		}
		,{
			idButton: "btndivBOMRUASONDEKHAC",
			idElement: "chkBOMRUASONDEKHACROW2_CHK"
		}
		,{
			idButton: "btndivSLDICHTONDADAY",
			idElement: "txtSLDICHTONDADAYROW1"
		}
		,{
			idButton: "btndivMAUDICHTONDADAY",
			idElement: "txtMAUDICHTONDADAYROW1"
		}
		,{
			idButton: "btndivCHOANQUASONDEDD",
			idElement: "txtCHOANQUASONDEDDROW1"
		}
		,{
			idButton: "btndivSLDICHDLMANGPHOI",
			idElement: "txtSLDICHDLMANGPHOIROW1"
		}
		,{
			idButton: "btndivMAUDICHDLMP",
			idElement: "txtMAUDICHDLMPROW1"
		}
		,{
			idButton: "btndivSLDICHDLMANGBUNG",
			idElement: "txtSLDICHDLMANGBUNGROW1"
		}
		,{
			idButton: "btndivMAUDICHDLMANGBUNG",
			idElement: "txtMAUDICHDLMANGBUNGROW1"
		}
		,{
			idButton: "btndivSLDICHDLMANGTIM",
			idElement: "txtSLDICHDLMANGTIMROW1"
		}
		,{
			idButton: "btndivMAUDICHDLMANGTIM",
			idElement: "txtMAUDICHDLMANGTIMROW1"
		}
		,{
			idButton: "btndivSLDICHDLOBUNG",
			idElement: "txtSLDICHDLOBUNGROW1"
		}
		,{
			idButton: "btndivMAUDICHDLOBUNG",
			idElement: "txtMAUDICHDLOBUNGROW1"
		}
		,{
			idButton: "btndivSLDICHDLKERTH",
			idElement: "txtSLDICHDLKERTHROW1"
		}
		,{
			idButton: "btndivMAUDICHDLKERTH",
			idElement: "txtMAUDICHDLKERTHROW1"
		}
		,{
			idButton: "btndivSLDICHDLKHAC",
			idElement: "txtSLDICHDLKHACROW2"
		}
		,{
			idButton: "btndivMAUDICHDLKHAC",
			idElement: "txtMAUDICHDLKHACROW2"
		}
		,{
			idButton: "btndivCHUOMMAT",
			idElement: "chkCHUOMMATROW1_CHK"
		}
		,{
			idButton: "btndivLOET",
			idElement: "txtLOETROW1"
		}
		,{
			idButton: "btndivDOLOET",
			idElement: "cboDOLOETROW1"
		}
		,{
			idButton: "btndivDIENTICHLOET",
			idElement: "txtDIENTICHLOETROW1"
		}
		,{
			idButton: "btndivDLTUTHE",
			idElement: "txtDLTUTHEROW1"
		}
		,{
			idButton: "btndivVANDONGTRILIEU",
			idElement: "txtVANDONGTRILIEUROW1"
		}
		,{
			idButton: "btndivPHUGIUPBS",
			idElement: "txtPHUGIUPBSROW1"
		}
		,{
			idButton: "btndivDICHVAODICHRA",
			idElement: "sssssssssss"
		}
		,{
			idButton: "btndivNHANDINH",
			idElement: "sssssssssss"
		}
		,{
			idButton: "btndivYLENHCHAMSOC",
			idElement: "sssssssssss"
		}
	];

	var _CAUHINH = {};

	this.load = doLoad;

	function doLoad(){
		getDSCauHinh();
		initControl();
		bindEvent();
		setInterface();
	}

	function getDSCauHinh(){
		var str = ''
			+ 'CAUHINH1' // ...
			+ ";CAUHINH2" // ...
			+ ";CAUHINH3"
		;
		var data = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH",str);
		if(data != null && data.length > 0){
			_CAUHINH = data[0];
		}
	}

	function initControl(){
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		generateGrid();
		generateComboBox();
		generateComboGrid();
		generateKeyForDivTAB();
	}

	function generateComboBox(){
		loadDsMau();
	}

	function setInterface(){
		disableTTBN();
		loadThongTinBN();

		disablAllDIV();
		$("#btnThem").prop('disabled', false);
		$("#btnDong").prop('disabled', false);

		$('#txtTIMECREATE').mask('00/00/0000 00:00:00');

		loadGrid();

		for (var i = 0; i< arrButtonHideShow.length; i++) {
			$('#' + arrButtonHideShow[i].idButton).click();
		}

		$('#txtDICHVAODICHRAROW1_DICHVAO').attr("type", "number");
		$('#txtDICHVAODICHRAROW1_DICHRA').attr("type", "number");
		$('#txtDICHVAODICHRAROW1_BILANDICH').attr("type", "number");
	}

	function generateGrid(){
		var gridHeader = ""
			+ "IDDATA,IDDATA,0,0,t,l"
			+ ";TIEPNHANID,TIEPNHANID,0,0,t,l"
			+ ";KHAMBENHID,KHAMBENHID,0,0,t,l"
			+ ";HOSOBENHANID,HOSOBENHANID,0,0,t,l"
			+ ";STT,STT,1,0,f,c"
			+ ";Ngày thực hiện,TIMECREATE,2,0,f,c"
			+ ";Người thực hiện,NGUOITHUCHIEN,2,0,f,c"
			// + ";Mã chẩn đoán,MACHANDOAN,4,0,f,c"
			// + ";Tên chẩn đoán,TENCHANDOAN,4,0,f,c"
		;
		GridUtil.init("grdDS", "100%", "150", "Danh sách phiếu", false, gridHeader);

		$("#grdDS").jqGrid('setGridParam',{
			onSelectRow : function(id) {
				opt.rowid = id;
				selectRowGrid(id);
			},
			ondblClickRow : function(id) {
				$('#btnSua').click();
			}
		});
	}

	function generateComboGrid(){
		var hearderICD = "Mã bệnh,ICD10CODE,30,0,f,l;Tên bệnh,ICD10NAME,70,0,f,l";
		ComboUtil.initComboGrid("txtTKMACHANDOAN", "NT.008", [], "600px", hearderICD, function(event, ui) {
			var item = ui.item;
			$("#cboMACHANDOAN").empty();
			$("#cboMACHANDOAN").append('<option value="' + item.ICD10CODE + '">' + item.ICD10NAME + '</option>');
		});
	}

	function generateKeyForDivTAB(){
		// lay ra danh sach key từ danh mục key
		var obj = {
			thamso1: "hihi",
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D165.L04", param);

		// luôn có ROOT
		if (result && result.length >= 2) {
			keyToHTMLDivTAB(result);
			tuning24RowTextBoxTo1Row();
			tuning24RowComboBoxTo1Row();
			tuning24RowCheckBoxTo1Row();
			tuningOther();
		} else {
			DlgUtil.showMsg("Chưa khai báo danh mục key", function(){
			});
			return;
		}
	}

	// xay dung lai giao dien cho bang theo doi cham soc nguoi benh
	function tuning24RowTextBoxTo1Row(){
		var arr1 = ['MACHROW1', 'HUYETAPROW1', 'NHIETDOROW1', 'SPO2ROW1', 'NHIPTHOROW1', 'GLASGOWROW1', 'RASSROW1', 'SLNUOCTIEUROW1', 'MAUNUOCTIEUROW1', 'SLPHANROW1', 'MAUPHANROW1', 'MAUDOMROW1', 'SLDICHTONDADAYROW1', 'MAUDICHTONDADAYROW1', 'CHOANQUASONDEDDROW1', 'SLDICHDLMANGPHOIROW1', 'MAUDICHDLMPROW1', 'SLDICHDLMANGBUNGROW1', 'MAUDICHDLMANGBUNGROW1', 'SLDICHDLMANGTIMROW1', 'MAUDICHDLMANGTIMROW1', 'SLDICHDLOBUNGROW1', 'MAUDICHDLOBUNGROW1', 'SLDICHDLKERTHROW1', 'MAUDICHDLKERTHROW1', 'SLDICHDLKHACROW2', 'MAUDICHDLKHACROW2', 'LOETROW1', 'DIENTICHLOETROW1', 'DLTUTHEROW1', 'VANDONGTRILIEUROW1', 'PHUGIUPBSROW1'];
		for (var i = 0; i< arr1.length; i++) {
			var div1 = '<div class="col-md-6 low-padding">';
			var div2 = div1;
			for (var j = 1; j<= 24; j++) {
				var id = '#txt' + arr1[i] + '_' + j;
				var div = $(id).parent().parent();
				//div.get(0).outerHTML;
				var str = div.html();
				var strOuter = '<div class="col-md-1 low-padding">' + str + '</div>';
				if (j<=12) {
					div1 += strOuter;
				} else {
					div2 += strOuter;
				}

				if (j == 24) {
					div1 += '</div>';
					div2 += '</div>';
					var divRow = '<div class="col-md-12 low-padding">' + div1 + div2 + '</div>';

					div.html(divRow);
				} else {
					div.remove();
				}
			}
		}

	}

	function tuning24RowComboBoxTo1Row(){
		var arr1 = ['VORUNGHUTDOMROW1', 'DOLOETROW1'];
		for (var i = 0; i< arr1.length; i++) {
			var div1 = '<div class="col-md-6 low-padding">';
			var div2 = div1;
			for (var j = 1; j<= 24; j++) {
				var id = '#cbo' + arr1[i] + '_' + j;
				var div = $(id).parent().parent();
				//div.get(0).outerHTML;
				var str = div.html();
				var strOuter = '<div class="col-md-1 low-padding">' + str + '</div>';
				if (j<=12) {
					div1 += strOuter;
				} else {
					div2 += strOuter;
				}

				if (j == 24) {
					div1 += '</div>';
					div2 += '</div>';
					var divRow = '<div class="col-md-12 low-padding">' + div1 + div2 + '</div>';

					div.html(divRow);
				} else {
					div.remove();
				}
			}
		}

	}

	function tuning24RowCheckBoxTo1Row(){
		var arr1 = ['THOMAYROW1_CHK','THOOXYROW1_CHK', 'VSRMROW1_CHK', 'TAMROW1_CHK', 'GOIDAUROW1_CHK', 'VSCNKHACROW2_CHK', 'THAYBANGNKQROW1_CHK', 'THAYBANGMKQROW1_CHK', 'THAYBANGCATHETERROW1_CHK', 'THAYBANGVETLOETROW1_CHK', 'THAYBANGKHACROW2_CHK', 'DATSONDETIEUROW1_CHK', 'DATSONDEDADAYROW1_CHK', 'DATSONDEHAUMONROW1_CHK', 'DATSONDEKHACROW2_CHK', 'BOMRUASONDETIEUROW1_CHK', 'BOMRUASONDEDADAYROW1_CHK', 'BOMRUASONDEKHACROW2_CHK', 'CHUOMMATROW1_CHK'];
		for (var i = 0; i< arr1.length; i++) {
			var div1 = '<div class="col-md-6 low-padding">';
			var div2 = div1;
			for (var j = 1; j<= 24; j++) {
				var id = '#chk' + arr1[i] + '_' + j;
				var div = $(id).parent();
				//div.get(0).outerHTML;
				var str = div.html();
				var strOuter = '<div class="col-md-1 low-padding">' + str + '</div>';
				if (j<=12) {
					div1 += strOuter;
				} else {
					div2 += strOuter;
				}

				if (j == 24) {
					div1 += '</div>';
					div2 += '</div>';
					var divRow = '<div class="col-md-12 low-padding">' + div1 + div2 + '</div>';

					div.html(divRow);
				} else {
					div.remove();
				}
			}
		}
	}

	function tuningOther(){

	}

	function keyToHTMLDivTAB(arr){
		// mỗi tab phải generate 1 html riêng
		var htmlTabA = '';
		var htmlTabB = '';
		var htmlTabC = '';
		var htmlTabD = '';
		var htmlTabE = '';

		for (var i = 0; i< arr.length; i++) {
			var obj = arr[i];
			var html = '';

			if (obj.KEYNAME == 'ROOT' || obj.TYPE == '6') {
				continue;
			}
			// label - parent
			else if (obj.TYPE == '5') {
				// cha
				if (obj.PREFIX != '') {
					html += '' +
						'\t\t<div class="col-md-12 low-padding">\n' +
						'\t\t\t<h3><strong>' + obj.PREFIX + '</strong></h3>\n' +
						'\t\t\t\t\t<button type="button" id="btndiv' + obj.KEYNAME + '" >\n' +
						'\t\t\t\t\t\t<span class="glyphicon glyphicon-minus"></span>\n' +
						'\t\t\t\t\t</button>'
						'\t\t</div>\n';
				} else {
					html += '' +
						'\t\t<div class="col-md-12 low-padding">\n' +
						'\t\t\t<h3><strong>' + obj.PREFIX + '</strong></h3>\n' +
						'\t\t</div>\n';
				}

			} else {
				// cấp nhỏ nhất
				html += '' +
					'\t\t<div class="col-md-12 low-padding">\n' +
					'\t\t\t<div class="col-md-12 low-padding">\n' +
					'\t\t\t\t<label><b>' + obj.PREFIX + '</b></label>\n' +
					'\t\t\t</div>\n';

				// textbox
				if (obj.TYPE == '1') {
					// chỉ textbox mới có suffix
					if (obj.SUFFIX == '') {
						html += '' +
							'\t\t\t<div class="col-md-11 low-padding">\n' +
							'\t\t\t\t<input class="form-control input-sm i-col-99 class1" required type="text" id="txt' + obj.KEYNAME + '" placeholder="' + obj.TITLE + '" style="width: 100%">\n' +
							'\t\t\t</div>\n';
					} else {
						html += '' +
							'\t\t\t<div class="col-md-9 low-padding">\n' +
							'\t\t\t\t<input class="form-control input-sm i-col-99 class1" required type="text"  id="txt' + obj.KEYNAME + '" placeholder="' + obj.TITLE + '" style="width: 100%">\n' +
							'\t\t\t</div>\n' +
							'\t\t\t<div class="col-md-2 low-padding">\n' +
							'\t\t\t\t<label>&nbsp;\n'+ obj.SUFFIX +'</label>\n' +
							'\t\t\t</div>\n';
					}
				}
				// textarea
				else if (obj.TYPE == '2') {
					html += '' +
						'\t\t\t<div class="col-md-11 low-padding">\n' +
						'\t\t\t\t<textarea maxlength="500" type="number" class="class1" required rows="2" maxlength="500" id="txt'+ obj.KEYNAME + '"  placeholder="' + obj.TITLE + '" style="width: 100%"></textarea>\n' +
						'\t\t\t</div>\n';
				}
				// checkbox
				else if (obj.TYPE == '3') {
					html += '' +
						'\t\t\t<div class="col-md-11 low-padding">\n';

					// danh sach con theo ngay sau
					var arrChild = [];
					for (var j = i+1; j <arr.length; j++) {
						var nextChild = arr[j];
						if (nextChild.TYPE != '6') {
							break;
						} else {
							arrChild.push(nextChild);
						}
					}

					for (var j = 0; j< arrChild.length; j++ ) {
						var objChild = arrChild[j];
						html += '' +
							'\t\t\t\t<div class="col-md-12 low-padding">\n' +
							'\t\t\t\t\t<input type="checkbox" id="chk' + objChild.KEYNAME + '"> <label id="label' + objChild.KEYNAME  + '"  for="chk' + objChild.KEYNAME  + '" class="label1">' + objChild.PREFIX + '</label>\n' +
							'\t\t\t\t</div>\n';
					}
					html += '' +
						'\t\t\t</div>\n';

				}
				// combobox: type = 4
				else if (obj.TYPE == '4') {
					html += '' +
						'\t\t\t<div class="col-md-11 low-padding">\n' +
						'\t\t\t\t<select class="form-control input-sm" id="cbo' + obj.KEYNAME + '">\n';

					// danh sach con theo ngay sau
					var arrChild = [];
					for (var j = i+1; j <arr.length; j++) {
						var nextChild = arr[j];
						if (nextChild.TYPE != '6') {
							break;
						} else {
							arrChild.push(nextChild);
						}
					}

					for (var j = 0; j< arrChild.length; j++ ) {
						var objChild = arrChild[j];
						html += '' +
							'\t\t\t\t\t<option value="' + objChild.KEYNAME + '">' + objChild.PREFIX + '</option>\n';
					}
					html += '' +
						'\t\t\t\t</select>\n' +
						'\t\t\t</div>\n';
				}

				html += '' +
					'\t\t</div>\n';
			}

			if (obj.AREA == '1') {
				htmlTabA += html;
			} else if (obj.AREA == '2') {
				htmlTabB += html;
			} else if (obj.AREA == '3') {
				htmlTabC += html;
			} else if (obj.AREA == '4') {
				htmlTabD += html;
			} else if (obj.AREA == '5') {
				htmlTabE += html;
			}
		}

		$('#divTabA').append(htmlTabA);
		$('#divTabB').append(htmlTabB);
		$('#divTabC').append(htmlTabC);
		$('#divTabD').append(htmlTabD);
		$('#divTabE').append(htmlTabE);
	}

	function bindEventDynamic(){

		for (var i = 0; i< arrButtonHideShow.length; i++) {
			bindEventCollapse(arrButtonHideShow[i]); // phải dùng hàm rời chỗ này
		}
	}

	function bindEventCollapse(obj){
		var idBtn = obj.idButton;
		var idEle = obj.idElement;

		$('#' + idBtn).on('click', function(e) {
			if ($('#' + idBtn + " span").hasClass("glyphicon-minus")) {
				$("#" + idBtn + " span").removeClass("glyphicon glyphicon-minus");
				$("#" + idBtn + " span").addClass("glyphicon glyphicon-plus");

				if (idBtn == 'btndivVSCNKHAC') {
					$('#txtVSCNKHACROW1_TXT').hide();
				}

				if (idBtn == 'btndivTHAYBANGKHAC') {
					$('#txtTHAYBANGKHACROW1_TXT').hide();
				}

				if (idBtn == 'btndivDATSONDEKHAC') {
					$('#txtDATSONDEKHACROW1_TXT').hide();
				}

				if (idBtn == 'btndivBOMRUASONDEKHAC') {
					$('#txtBOMRUASONDEKHACROW1_TXT').hide();
				}

				if (idBtn == 'btndivSLDICHDLKHAC') {
					$('#txtSLDICHDLKHACROW1_TXT').hide();
				}

				if (idBtn == 'btndivMAUDICHDLKHAC') {
					$('#txtMAUDICHDLKHACROW1_TXT').hide();
				}

				if (idBtn == 'btndivDICHVAODICHRA') {
					$('#txtDICHVAODICHRAROW1_DICHVAO').hide();
					$('#txtDICHVAODICHRAROW1_DICHRA').hide();
					$('#txtDICHVAODICHRAROW1_BILANDICH').hide();
				}

				if (idBtn == 'btndivNHANDINH') {
					$('#txtNHANDINHROW1_ND1').hide();
					$('#txtNHANDINHROW1_ND2').hide();
					$('#txtNHANDINHROW1_ND3').hide();
					$('#txtNHANDINHROW1_ND4').hide();
				}

				if (idBtn == 'btndivYLENHCHAMSOC') {
					$('#txtYLENHCHAMSOCROW1_YLENH1').hide();
					$('#txtYLENHCHAMSOCROW1_YLENH2').hide();
					$('#txtYLENHCHAMSOCROW1_YLENH3').hide();
					$('#txtYLENHCHAMSOCROW1_YLENH4').hide();
				}

			} else {
				$("#" + idBtn + " span").removeClass("glyphicon glyphicon-plus");
				$("#" + idBtn + " span").addClass("glyphicon glyphicon-minus");

				// xu ly them
				if (idBtn == 'btndivVSCNKHAC') {
					$('#txtVSCNKHACROW1_TXT').show();
				}

				if (idBtn == 'btndivTHAYBANGKHAC') {
					$('#txtTHAYBANGKHACROW1_TXT').show();
				}

				if (idBtn == 'btndivDATSONDEKHAC') {
					$('#txtDATSONDEKHACROW1_TXT').show();
				}

				if (idBtn == 'btndivBOMRUASONDEKHAC') {
					$('#txtBOMRUASONDEKHACROW1_TXT').show();
				}

				if (idBtn == 'btndivSLDICHDLKHAC') {
					$('#txtSLDICHDLKHACROW1_TXT').show();
				}

				if (idBtn == 'btndivMAUDICHDLKHAC') {
					$('#txtMAUDICHDLKHACROW1_TXT').show();
				}

				if (idBtn == 'btndivDICHVAODICHRA') {
					$('#txtDICHVAODICHRAROW1_DICHVAO').show();
					$('#txtDICHVAODICHRAROW1_DICHRA').show();
					$('#txtDICHVAODICHRAROW1_BILANDICH').show();
				}

				if (idBtn == 'btndivNHANDINH') {
					$('#txtNHANDINHROW1_ND1').show();
					$('#txtNHANDINHROW1_ND2').show();
					$('#txtNHANDINHROW1_ND3').show();
					$('#txtNHANDINHROW1_ND4').show();
				}

				if (idBtn == 'btndivYLENHCHAMSOC') {
					$('#txtYLENHCHAMSOCROW1_YLENH1').show();
					$('#txtYLENHCHAMSOCROW1_YLENH2').show();
					$('#txtYLENHCHAMSOCROW1_YLENH3').show();
					$('#txtYLENHCHAMSOCROW1_YLENH4').show();
				}

			}

			for (var i = 0; i<= 24; i++) {
				var idDiv = idEle + "_" + i;
				if ($('#' + idDiv).css('display') == 'none') {
					$('#' + idDiv).show();
				} else {
					$('#' + idDiv).hide();
				}

				// label checkbox
				var index1 = idEle.indexOf("chk");
				if (index1 != -1) {
					var idEle2 = "label" + idEle.substring(index1 + 3, idEle.length);
					var idDiv = idEle2 + "_" + i;
					if ($('#' + idDiv).css('display') == 'none') {
						$('#' + idDiv).show();
					} else {
						$('#' + idDiv).hide();
					}
				}
			}

		});




	}

	function validateE (id, e) {
		var invalidcharacters = /[^0-9]/gi;
		var phn = document.getElementById(id);
		if (invalidcharacters.test(phn.value)) {
			newstring = phn.value.replace(invalidcharacters, "");
			phn.value = newstring
		}
	}

	function tinhBilan(){
		var dichVao = parseFloat($('#txtDICHVAODICHRAROW1_DICHVAO').val());
		var dichRa = parseFloat($('#txtDICHVAODICHRAROW1_DICHRA').val());
		var bilan = dichVao - dichRa;
		$('#txtDICHVAODICHRAROW1_BILANDICH').val(bilan);
	}

	function bindEvent(){

		$("#txtDICHVAODICHRAROW1_DICHVAO").focusout(function(e) {
			tinhBilan();
		});

		$("#txtDICHVAODICHRAROW1_DICHRA").focusout(function(e) {
			tinhBilan();
		});

		bindEventDynamic();

		$("#btnEditBcVk").on("click", function(e) {
			if(!$('#cboMACHANDOAN').val()){
				DlgUtil.showMsg("Vui lòng chọn chẩn đoán chính trước khi muốn sửa tên", function(){
					$('#txtTKMACHANDOAN').focus();
				});
				return;
			}

			$('#cboMACHANDOAN').hide();
			$('#txtBCEDITVK').val($('#cboMACHANDOAN option:selected').text());
			$('#txtBCEDITVK').show();
			$('#btnSaveEditBcVk').show();
			$("#btnEditBcVk").hide();
		});

		$("#btnSaveEditBcVk").on("click", function(e) {
			if ($.trim($('#txtBCEDITVK').val()) != '') {
				var icd = $("#cboMACHANDOAN").val();
				$("#cboMACHANDOAN").empty();
				$("#cboMACHANDOAN").append('<option value="' + icd + '">' + $('#txtBCEDITVK').val().trim() + '</option>');
				$('#txtBCEDITVK').hide();
				$('#btnSaveEditBcVk').hide();
				$('#cboMACHANDOAN').show();
				$("#btnEditBcVk").show();
			} else {
				$('#txtBCEDITVK').focus();
				return;
			}
		});

		$("#txtBCEDITVK").focusout(function() {
			if ($.trim($('#txtBCEDITVK').val()) != '') {
				var icd = $("#cboMACHANDOAN").val();
				$("#cboMACHANDOAN").empty();
				$("#cboMACHANDOAN").append('<option value="' + icd + '">' + $('#txtBCEDITVK').val().trim() + '</option>');
				$('#txtBCEDITVK').hide();
				$('#btnSaveEditBcVk').hide();
				$('#cboMACHANDOAN').show();
				$("#btnEditBcVk").show();
			} else {
				$('#txtBCEDITVK').focus();
				return;
			}
		});

		$("#btnThem").on("click", function(e) {
			GridUtil.unmarkAll('grdDS');

			opt.rowid = undefined;
			opt.IDDATA = undefined;

			enableAllDIV();
			disableTrue(["btnThem","btnSua","btnXoa","btnInPhieu"]);

			clearDivTTCT();

			focusTabA();
			$('#txtTIMECREATE').focus();
			var currentTime = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			$('#txtTIMECREATE').val(currentTime);

			$('#cboMAU').val('');
			$('#txtTENMAU').val('');
			disableFalse(["cboMAU","txtTENMAU","btnLuuThanhMau","btnXoaMau"]);

			function2();

		});

		$("#btnSua").on("click", function(e) {
			if (!opt.IDDATA) {
				DlgUtil.showMsg("Chưa chọn phiếu!");
				return;
			}

			enableAllDIV();
			disableTrue(["btnThem","btnSua","btnXoa","btnInPhieu"]);

			$('#cboMAU').val('');
			$('#txtTENMAU').val('');
			disableFalse(["cboMAU","txtTENMAU","btnLuuThanhMau","btnXoaMau"]);

			function2();
		});

		$("#btnXoa").on("click", function(e) {
			if (!opt.IDDATA) {
				DlgUtil.showMsg("Chưa chọn phiếu!");
				return;
			}
			DlgUtil.showConfirm("Bạn có muốn xóa phiếu này không?", function(flag) {
				if (flag) {
					xoaPhieu();
				}
			});
		});

		$("#btnLuu").on("click", function(e) {
			if (validateBeforeSave() == -1){
				return;
			}
			savePhieu(false);
		});

		$("#btnLuuVaDong").on("click", function(e) {
			if (validateBeforeSave() == -1){
				return;
			}
			savePhieu(true);
		});

		$("#btnInPhieu").on("click", function(e) {
			if (!opt.IDDATA) {
				DlgUtil.showMsg("Chưa chọn phiếu!");
				return;
			}

			var name = opt.IDDATA;
			var popup  = window.open('manager.jsp?func=../noitru/NTU02D165_BTDCSNB_INPHIEU&showMode=dlg',name,'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height='+screen.height+',width='+screen.width);
			popup.moveTo(0,0);


		});

		$("#btnHuy").on("click", function(e) {
			DlgUtil.showConfirm("Bạn có muốn huỷ không?", function(flag) {
				if (flag) {
					if (!opt.IDDATA) {
						$('#btnThem').click();
					} else {
						selectRowGrid(opt.rowid);
					}
				}
			});
		});

		$("#btnDong").on("click", function(e) {
			DlgUtil.showConfirm("Bạn có muốn đóng không?", function(flag) {
				if (flag) {
					window.close();
				}
			});
		});

		$('#cboMAU').change(function() {
			$('#txtTENMAU').val('');
			clearDivTTCT();
			if ($('#cboMAU').val() != ''){
				var dataKeyValue = getDataKeyValue($('#cboMAU').val());
				var obj = getInfoMau($('#cboMAU').val());
				var data = tuningData(dataKeyValue, obj );
				fillDataToDivTTCT(data);
			}
		});

		$("#btnLuuThanhMau").on("click", function(e) {
			if ($('#txtTENMAU').val().trim() == '') {
				DlgUtil.showMsg('Tên mẫu không được để trống', function() {
				});
				return;
			}
			saveMau();
		});

		$("#btnXoaMau").on("click", function(e) {
			if ($('#cboMAU').val() == '') {
					DlgUtil.showMsg('Vui lòng chọn mẫu', function() {
				});
				return;
			}
			DlgUtil.showConfirm("Bạn có muốn xóa mẫu này không?", function(flag) {
				if (flag) {
					var par = [
							$('#cboMAU').val()
					];
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D165.L09",par.join('$'));
					var data = $.parseJSON(result);
					if (data == '-1') {
						DlgUtil.showMsg('Đã có lỗi xảy ra',function() {
					    });
					} else {
						DlgUtil.showMsg('Đã xóa mẫu',function() {
							loadDsMau();
					    });
					}
				}
			});
		});

	}

	function getInfoMau(id){
		var obj = {
			idMau: id + ""
		}
		var param = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D165.L08", param);
		if (result && result.length > 0) {
			return result[0];
		} else {
			return null;
		}
	}

	function loadGrid() {
		var obj = {
			khambenhid: opt.KHAMBENHID
		}
		var param = [{
			name: "[0]",
			value: JSON.stringify(obj)
		}];
		GridUtil.loadGridBySqlPage("grdDS", "NTU02D165.L01",param);
	}

	// fill data vao divTTCT
	function fillDataToDivTTCT(data){
		FormUtil.setObjectToForm('divTTCT',"",data);
		$("#cboMACHANDOAN").empty();
		$("#cboMACHANDOAN").append('<option value="' + data.MACHANDOAN + '">' + data.TENCHANDOAN + '</option>');
		$('#txtGHICHUCHANDOAN').val(data.GHICHUCHANDOAN);

	}


	function saveMau() {
		var objChung = {};
		FormUtil.setFormToObject("divTT1", "", objChung);
		objChung.TENMAU = $('#txtTENMAU').val();

		var objKeyValue = {};
		FormUtil.setFormToObject("divTAB", "", objKeyValue);

		var arr = [];
		for (var prop in objKeyValue) {
			if (Object.prototype.hasOwnProperty.call(objKeyValue, prop)) {
				arr.push({
					"KEYNAME": prop,
					"VALUE": objKeyValue[prop]
				});
			}
		}
		var jsonKeyValue = JSON.stringify(arr);

		var par = [JSON.stringify(objChung), jsonKeyValue];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D165.L06", par.join('$'));
		var data = $.parseJSON(result);
		switch (data) {
			case 1:
				DlgUtil.showMsg('Lưu thành công!', function () {
					loadDsMau();
					$('#txtTENMAU').val('');
				});
				break;
			default:
				DlgUtil.showMsg('Đã có lỗi xảy ra!', function () {
				});
				break;
		}
	}


	// enable tat ca tru vung grid và ttbn
	function enableAllDIV(){
		$('#divBUTTON select, #divBUTTON input, #divBUTTON button').attr('disabled', false);
		$('#divMAU select, #divMAU input, #divMAU button').attr('disabled', false);
		$('#divTTCT select, #divTTCT input, #divTTCT textarea').attr('disabled', false);
	}

	// disable tat ca tru vung grid và ttbn
	function disablAllDIV(){
		$('#divBUTTON select, #divBUTTON input, #divBUTTON button').attr('disabled', true);
		$('#divMAU select, #divMAU input, #divMAU button').attr('disabled', true);
		$('#divTTCT select, #divTTCT input, #divTTCT textarea').attr('disabled', true);
	}

	// disable thong tin benh nhan
	function disableTTBN(){
		$('#divTTBN select, #divTTBN input, #divTTBN button').attr('disabled', true);
	}


	// clear divTTCT
	function clearDivTTCT(){
		$('#divTTCT input[type=text]').val('');
		$('#divTTCT input[type=checkbox]').prop('checked', false);
		$('#divTTCT textarea').val('');
		$('#divTTCT select option:first-child').attr("selected", "selected");

		$('#txtTKMACHANDOAN').val('');
		$("#cboMACHANDOAN").empty();
		$("#cboMACHANDOAN").show();
		$('#txtGHICHUCHANDOAN').val('');
		$('#txtBCEDITVK').val('');
		$('#txtBCEDITVK').hide();
		$('#btnEditBcVk').show();
		$('#btnSaveEditBcVk').hide();

	}

	function loadDsMau(){
		var sqlMau = "NTU02D165.L07";
		ComboUtil.getComboTag("cboMAU", sqlMau, [ {
			"name" : "[0]",
			"value" : 'hihi' // hid
		} ], "", {
			value : '',
			text : '-- Chọn mẫu --'
		}, "");

	}

	function savePhieu(close){
		var objChung = {};
		FormUtil.setFormToObject("divTT1", "", objChung);
		objChung.KHAMBENHID = opt.KHAMBENHID;
		objChung.IDDATA = opt.IDDATA;

		var objKeyValue = {};
		FormUtil.setFormToObject("divTAB", "", objKeyValue);

		var arr = [];
		for (var prop in objKeyValue) {
			if (Object.prototype.hasOwnProperty.call(objKeyValue, prop)) {
				arr.push({
					"KEYNAME": prop,
					"VALUE": objKeyValue[prop]
				});
			}
		}
		var jsonKeyValue = JSON.stringify(arr);

		var par = [JSON.stringify(objChung), jsonKeyValue];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D165.L03", par.join('$'));
		var data = $.parseJSON(result);
		switch (data) {
			case 1:
			case 2:
				DlgUtil.showMsg('Lưu thành công!', function () {
					if (close) {
						window.close();
					} else {
						loadGrid();
						opt.rowid = undefined;
						opt.IDDATA = undefined;
						clearDivTTCT();
						disablAllDIV();
						$("#btnThem").prop('disabled', false);
						$("#btnDong").prop('disabled', false);
					}
				});
				break;
			default:
				DlgUtil.showMsg('Đã có lỗi xảy ra!', function () {
				});
				break;
		}
	}

	function xoaPhieu(){
		var par = [opt.IDDATA];
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D165.L02",par.join('$'));
		var data = $.parseJSON(result);
		if (data == '-1') {
			DlgUtil.showMsg('Đã có lỗi xảy ra',function() {
			});
		} else {
			DlgUtil.showMsg('Đã xóa phiếu',function() {
				loadGrid();
				opt.rowid = undefined;
				opt.IDDATA = undefined;
				clearDivTTCT();
				disablAllDIV();
				$("#btnThem").prop('disabled', false);
				$("#btnDong").prop('disabled', false);
			});
		}
	}

	function validateBeforeSave(){
		var validator = new DataValidator("divTTCT");
		var valid = validator.validateForm();
		if(!valid){
			return -1;
		}

		return 'ok';
	}

	// chuyển về 1 object
	function tuningData(dataKeyValue, obj){
		var data = {};
		for (var i = 0; i< dataKeyValue.length; i++) {
			var keyvalue = dataKeyValue[i];
			data[keyvalue.KEYNAME] = keyvalue.VALUE;
		}
		$.extend( data, obj );
		return data;

	}

	// input is disabled, the browser doesn't look at valid or invalid
	function function1(){
		$("input.class1").each(function() {
			$(this).css({ 'background-color' : '', 'opacity' : '' });
			if ($(this).val() !== '') {
				$(this).css("background-color", '#00ff21');
			}
		});
	}

	function function2(){
		$("input.class1").each(function() {
			$(this).css({ 'background-color' : '', 'opacity' : '' });
		});
	}

	function selectRowGrid(id){
		GridUtil.unmarkAll('grdDS');
		GridUtil.markRow('grdDS', id);

		var obj = $("#grdDS").jqGrid('getRowData', id);
		opt.IDDATA = obj.IDDATA;

		disablAllDIV();
		disableFalse(["btnThem","btnSua","btnXoa","btnInPhieu","btnDong","txtTENMAU","btnLuuThanhMau"]);

		clearDivTTCT();
		var dataKeyValue = getDataKeyValue(obj.IDDATA);
		var data = tuningData(dataKeyValue, obj );
		fillDataToDivTTCT(data);

		$('#cboMAU').val('');
		$('#txtTENMAU').val('');

		function1();

	}

	// lay thong tin chi tiet cac key va value tu bang key_value
	function getDataKeyValue(id){
		var obj = new Object();
		obj.IDDATA = id;
		var par = JSON.stringify(obj);
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D165.L05", par);
		if (result && result.length > 0) {
			return result;
		} else {
			return null;
		}

	}

	function dateRegEx(date){
		var pattern = new RegExp("^(3[01]|[12][0-9]|0[1-9])/(1[0-2]|0[1-9])/[0-9]{4} (2[0-3]|[01]?[0-9]):([0-5]?[0-9]):([0-5]?[0-9])$");
		if (date.search(pattern)===0){
			return true;
		} else {
			return false;
		}
	}

	function loadThongTinBN(){
		FormUtil.setObjectToForm("divTTBN","",opt);
	}

	// focus tab A
	function focusTabA(){
		$("#divTabB").removeClass("active");
		$("#divTabC").removeClass("active");
		$("#divTabD").removeClass("active");
		$("#divTabE").removeClass("active");
		$("#divTabA").addClass("active");

		$("#tabB").removeClass("active");
		$("#tabC").removeClass("active");
		$("#tabD").removeClass("active");
		$("#tabE").removeClass("active");
		$("#tabA").addClass("active");
	}


	function disableTrue(arr){
		for (var i = 0; i< arr.length; i++) {
			$("#" + arr[i]).prop('disabled', true);
		}
	}

	function disableFalse(arr){
		for (var i = 0; i< arr.length; i++) {
			$("#" + arr[i]).prop('disabled', false);
		}
	}


}
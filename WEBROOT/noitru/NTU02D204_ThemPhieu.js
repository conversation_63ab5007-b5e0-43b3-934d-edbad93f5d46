function dSDTList(_opts) {
    this.load = doLoad;
    let objectVar = parent.EventUtil.getVar("dlgVar");
    let _khambenhidVar = objectVar.KHAMBENHID;
    let _hosobenhanid = objectVar.HOSOBENHANID;
    let _paramSession = session_par;
    let _gridId = "grdDanhSach";
    let ctphieuids = [];

    let _gridHeader = "" +
        " ,ICON,25,0,ns,l;" +
        "FLAG_CA,FLAG_CA,0,0,t,l;" +
        "FLAG_CHA,FLAG_CHA,0,0,t,l;" +
        "USERNGUOITAO,USERNGUOITAO,0,0,t,l;" +
        "PRINT_HTML,PRINT_HTML,0,0,t,l;" +
        "LINK_HTML,LINK_HTML,0,0,t,l;" +
        "KHAMBENHID,KHAMBENHID,0,0,t,l;" +
        "<PERSON><PERSON>u ID,PHIEUID,70,0,f,l,1,2;" +
        "FORM_ID,FORM_ID,0,0,t,l,1,2;" +
        "RPT_CODE,RPT_CODE,0,0,t,l,1,2;" +
        "RPT_CODE_DETAIL,RPT_CODE_DETAIL,0,0,t,l,1,2;" +
        "RPT_CODE_MERGE,RPT_CODE_MERGE,0,0,t,l,1,2;" +
        "RPT_ORDER_FIELD,RPT_ORDER_FIELD,0,0,t,l,1,2;" +
        "GOM_PHIEU,GOM_PHIEU,0,0,t,l,1,2;" +
        "PHIEUIDCHA,PHIEUIDCHA,0,0,t,l,1,2;" +
        "Mã phiếu,MA_FORM,120,0,f,l,1,2;" +
        "Tên phiếu,TEN_FORM,320,0,f,l,1,2;" +
        "Khoa tạo phiếu,TENKHOA,300,0,f,l,1,2;" +
        "Mã phiếu cha,MAPHIEUCHA,120,0,f,l,1,2;" +
        "Ngày tạo,NGAYTAO,120,0,f,l,1,2;" +
        "Ngày thực hiện,NGAYTHUCHIEN,120,0,f,l,1,2;" +
        "Người tạo,NGUOITAO,220,0,f,l,1,2;" +
        "Loại phiếu,LOAI_PHIEU,120,0,t,l,1,2;" +
        "KY_TRUCTIEP,KY_TRUCTIEP,120,0,t,l,1,2"
    ;
    let phieuid = -1;
    let maphieu = "";
    let tenphieu = "";
    let formid = -1;
    let rptcode = "";
    let rptcodedetail = "";
    let rptcodemerge = "";
    let gomphieu = 0;
    let phieuidcha = "";
    let ngaythuchien = "";
    let ngaytaophieu = "";
    let tieudeformngay = "";
    let ky_tructiep = "";
    let columnKey = "";
    let columnValue = "";
    let _khambenhid = "";
    let _catype;
    let _msgCa = '';
    let _flagCaAll = false;
    let _par_rpt_kyso = [];
    let _data_config = [];
    let _lstParamHashed = '';
    let _rptOrderField = '';
    let ch = new Object();
    let _jsonString = "";
    let _listPhieuId = [];
    let _listIdRow = [];
    let _indexId = 0;
    let _configure = "";
    let _configshowform = "";
    let _configdatatemp = "";
    let _configdatatempall = "";
    let _configshowtemp = "";
    let _configtimelabel = "";
    let _configctcflagca = "";
    let _configallticketsca = "";
    let _configformbmte = "";
    let _configctccopy = "";
    let _configsosinh = "";
    let _dichvukhambenhid = "";
    let _checkDatePerform = "";
    let _checkDateCreated = "";
    let _checkCauHinh = 0;
    let _checkCaAll = 0;
    let _checkChange = false;
    let _maformcha = "";
    let _flgCha = 0;
    let _flgCa = 0;
    let _printHtml = 0;
    let _linkHtml = "";
    let _nguoitaophieu = "";
    let _permission = "";
    let _rowid = "";
    let causer = -1;
    let capassword = -1;
    let smartcauser = -1;
    let _isPrint = true;
    let _isKyCA = false;
    let _isType = 0;
    let keyCustomerObjectCacher = {}
    let keyValue = objectVar.KEY_VALUE;
    let _exitMaForm = "";
    let _maformphieu = "";
    let _arrayChiTietId = [];
    let _strPhieuChaId = "";
    let _strTenPhieuCha = "";
    let _showTabChamSocMoi = "";
    let _configureTtakeCare = "";
    let _formchamsoc = "";
    let _closeMedical = "";
    let _returnMedical = "";
    let _mutilcheck = "";
    let _hiddenInPhieu = "";
    let _mutilCombobox = "";


    function doLoad() {
        $.i18n().load(i18n_his.err_code);
        $.i18n().load(i18n_his.com_msg);
        $.i18n().load(i18n_ngoaitru.com_msg);
        $.i18n().locale = (typeof _opts.lang !== "undefined") ? _opts.lang : "vn";
        if (jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH', 'NTU02D204_NGAY_TAO') == "1") {
            _gridHeader = _gridHeader.replace('Ngày tạo,NGAYTAO,120,0,f,l,1,2;', 'Ngày tạo,NGAYTAO,120,0,t,l,1,2;');
        }
        _initControl();
        _bindEvent();
    }

    function _initControl() {
        $("#cboDANHSACH").select2();
        $("#cboTEMPLATE").select2();
        let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("COM.DS_CAUHINH",
            "HIS_NTU_THEMPHIEU_LOAIPHIEU;HIS_NTU_THEMPHIEU_DTM_FORMCT;HIS_SUDUNG_KYSO_KYDIENTU;KYSO_TUDONG_KYDIENTU;HIS_THEMPHIEU_CHON;CONFIGURE_TIME_LABELS;SHOW_AND_HIDDEN_THEMPHIEU;" +
            "CONFIGURE_SIGNATURE_PRINT;CONFIG_ON_OFF_CA_DTSS;HIS_SHOW_TAB_CHAMSOC_MOI;CONFIGURE_LIST_TAKE_CARE;HIS_NTU_SET_FUNCTION_USER;CONFIGURATION_SHOW_CTC_COPY;CONFIGURE_ALL_SIGNED_TICKETS;" +
            "CONFIGURATION_CHECK_FORM_BMTE;HIS_NTU_SET_PARAM_DVKB;NTU_HIS_SHOW_TIME_PERFORM;SETUP_PHIEUDIEUTRISOSINH_DATA;SETUP_CLOSE_MEDICAL_RECORD;SETUP_MULTIL_CHECK;SETUP_HIDDEN_PRINT_PCS;" +
            "SETUP_MULTI_COMBOBOX_FORM"
        );
        _dichvukhambenhid = (objectVar.DICHVUKHAMBENHID == Number(data_ar[0].HIS_NTU_SET_PARAM_DVKB) ? "" : objectVar.DICHVUKHAMBENHID);
        _moreClass(['tabHanhChinh']);
        if (data_ar != null && data_ar.length > 0) {
            ch = data_ar[0];
        }
        GridUtil.init(_gridId, "100%", "400", "Danh sách phiếu", true, _gridHeader, false);
        _configshowform = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'CONFIGURATION_DISPLAY_FORM_GENERAL')); // Bật tắt hiển thị biểu mẫu chung phiếu chăm sóc
        _configdatatemp = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'CONFIGURATION_LOADDATA_TEMPLATES')); // Bật tắt lấy dữ liệu mẫu theo các trường dữ liệu
        _configdatatempall = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'CONFIGURATION_LOADDATA_TEMPLATES_ALL')); // Bật tắt lấy dữ liệu mẫu theo các trường dữ liệu
        _configshowtemp = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'CONFIGURATION_DISPLAY_TEMPLATES')); // Bật tắt chức năng lưu mẫu trong màn hình thêm phiếu
        _checkDateCreated = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'CONFIGURATION_SHOW_EXECUTIONTIME_CREATE'); // Cấu hình hiển thị thời gian tạo phiếu
        _configctcflagca = JSON.parse(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'CONFIGURATION_SHOW_CHITIET_FLAGCA')); // Cấu hình hiển thị nút ký trong danh sách phiếu
        _configtimelabel = JSON.parse(data_ar[0].CONFIGURE_TIME_LABELS); // Cấu hình hiển thị thời gian thực hiện theo từng giao diện
        _checkDatePerform = data_ar[0].NTU_HIS_SHOW_TIME_PERFORM; // Cấu hình hiển thị thời gian thực hiện thêm phiếu
        _checkCauHinh = data_ar[0].SHOW_AND_HIDDEN_THEMPHIEU; // Cấu hình bật tắt hiển thị thông tin bệnh nhân thêm phiếu
        _configure = data_ar[0].CONFIGURE_SIGNATURE_PRINT; // Cấu hình cho phép in ký số nhiều theo từng dòng được chọn
        _permission = data_ar[0].HIS_NTU_SET_FUNCTION_USER; // Cấu hình không cho phép xóa, sửa khi không đúng người tạo phiếu
        _configctccopy = data_ar[0].CONFIGURATION_SHOW_CTC_COPY; // Cấu hình hiển thị nút sao chép danh sách con của từng phiếu
        _configallticketsca = JSON.parse(data_ar[0].CONFIGURE_ALL_SIGNED_TICKETS); // Cấu hình hiển thị nút in tất cả các phiếu đã ký số
        _configformbmte = JSON.parse(data_ar[0].CONFIGURATION_CHECK_FORM_BMTE); // Cấu hình kiểm tra các biểu mẫu thông tin bà mẹ trẻ em
        _showTabChamSocMoi = data_ar[0].HIS_SHOW_TAB_CHAMSOC_MOI; // Cấu hình phiếu chăm sóc ở tab thông tin hành chính
        _configureTtakeCare = JSON.parse(data_ar[0].CONFIGURE_LIST_TAKE_CARE); // Cấu hình loại chăm sóc theo từng đơn vị
        _data_config = data_ar[0].CONFIG_ON_OFF_CA_DTSS; // Cấu hình bật tắt nút In nhiều phiếu ký số;
        _configsosinh = data_ar[0].SETUP_PHIEUDIEUTRISOSINH_DATA; // Cấu hình lấy dữ liệu phiếu đầu tiên (Phiếu sơ sinh)
        _closeMedical = data_ar[0].SETUP_CLOSE_MEDICAL_RECORD; // Cấu hình không cho tạo mới khi bệnh án đã đóng
        _mutilcheck = JSON.parse(data_ar[0].SETUP_MULTIL_CHECK); // Cấu hình hiển thị nút chọn nhiều phiếu
        _hiddenInPhieu = data_ar[0].SETUP_HIDDEN_PRINT_PCS; // Cấu hình tắt nút In phiếu ở Phiếu chăm sóc 1,2,3
        _mutilCombobox = data_ar[0].SETUP_MULTI_COMBOBOX_FORM; // Cấu hình thêm gi chú vào Multi Combobox Thêm phiếu

        if (_checkCauHinh == 1) {
            _loadThongTinBenhNhan();
            _removeClass(['divBenhNhanInfo']);
        }
        columnKey = jsonrpc.AjaxJson.getOneValue('NTU02D204.18', [{
            "name": "[0]", value: objectVar.LOAI_PHIEU
        }]).toUpperCase();
        let objKey = Object.keys(objectVar).filter(k => k.toUpperCase() === columnKey);
        if (objKey && objKey.length > 0 && objKey[0] && (objectVar[objKey[0]] || objectVar[objKey[0].toUpperCase()])) {
            columnValue = (objectVar[objKey[0]] | objectVar[objKey[0].toUpperCase()]) + "";
        } else {
            alert("Thông tin cấu hình phiếu không hợp lệ.");
            $("#divMain").html("Thông tin cấu hình phiếu không hợp lệ: " + columnKey);
            return;
        }
        let sql_par = "";
        let sql_name = "";
        if (_showTabChamSocMoi == '1' && objectVar.LOAICHAMSOC != null) {
            let _sql_par = [];
            if (_opts._hospital_id == _configureTtakeCare[0].CSYTID) {
                if (objectVar.LOAICHAMSOC == 'CHAMSOCCAPMOT') {
                    _sql_par.push({
                        "name": "[0]", value: _configureTtakeCare[0].MAFORM
                    });
                    _formchamsoc = jsonrpc.AjaxJson.getOneValue('NTU.GET.FORMID', _sql_par);
                    sql_par = {"CODE": objectVar.LOAI_PHIEU, "FORMID": _formchamsoc};
                    sql_name = "GET.FORM.TABCS";
                } else if (objectVar.LOAICHAMSOC == 'CHAMSOCCAPHAIBA') {
                    _sql_par.push({
                        "name": "[0]", value: _configureTtakeCare[1].MAFORM
                    });
                    _formchamsoc = jsonrpc.AjaxJson.getOneValue('NTU.GET.FORMID', _sql_par);
                    sql_par = {"CODE": objectVar.LOAI_PHIEU, "FORMID": _formchamsoc};
                    sql_name = "GET.FORM.TABCS";
                }
            }
        } else {
            sql_par = objectVar.LOAI_PHIEU + '$';
            sql_name = "NTUD204.GET.FORM";
        }
        ComboUtil.getComboTag("cboDANHSACH", sql_name, sql_par, "", {
            value: '', text: '-- Chọn phiếu --'
        }, "sp", '', function () {
            if (objectVar.LOAI_PHIEU == ch.HIS_THEMPHIEU_CHON) {
                let _sql_par = [];
                _sql_par.push({
                    "name": "[0]", value: objectVar.LOAI_PHIEU
                });
                let formid = jsonrpc.AjaxJson.getOneValue('NTUD204.GET.FORMID', _sql_par);
                $("#cboDANHSACH").val(formid);
            } else {
                _loadThongTinChamSoc();
            }
            loadGridData();
        });
        _setCaEvent();
        _insetThemPhieu();
    }

    function _bindEvent() {
        EventUtil.setEvent("ChinhSuaDuLieuType10", function (e) {
            if (e.TYPE == "DANHSACH_DULIEU") {
                let _ctformid = e.CTFORMID;
                let _dulieu = e.DULIEU;
                $("#txtMSCFULL_" + _ctformid).val(_dulieu);
            }
            DlgUtil.close("divDlgNTU02D207");
        });
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            if (e.target.id == "tcctp" && phieuid != -1) {
                if (maphieu && tenphieu) {
                    $("#tcctp").html('<span class="glyphicon glyphicon-pencil"></span>' + " " + tenphieu + " (" + maphieu + ")" + '</a>');
                    let _maformcha = maphieu.split("-")[0];
                    let _csytid = _configshowtemp.CSYTID;
                    let _giatri = _configshowtemp.GIATRI;
                    let _maform = _checkExistMaForm(_configshowtemp.MAFORM, maphieu);
                    if (_maformcha == _maform && _csytid == _opts._hospital_id && _giatri == '1') {
                        _moreClass(['divTempForm']);
                    } else {
                        _removeClass(['divTempForm']);
                    }
                    if (_flgCha === "1" || _flgCha === 1) {
                        $("#btnLuuKy").hide();
                        $("#btnKySoIn").hide();
                        $("#btnHuyKySo").hide();
                        $("#btnInKySo").hide();
                    }

                    if (_configctcflagca.GIATRI === "1" && maphieu.split("-")[0] === _configctcflagca.MAFORM) {
                        $("#btnLuuKy").hide();
                        $("#btnKySoIn").hide();
                        $("#btnHuyKySo").hide();
                    }
                }
                ctphieuids = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.06", phieuid);
                if (ctphieuids.length > 0) {
                    $(".ctc-over-el").removeClass("ctc-over-locked");
                    $(".ctc-over-el").hide();
                    ctphieuids.forEach(function (el) {
                        let type = el.TYPE;
                        let dulieu = el.DULIEU;
                        let split = el.SPLIT;
                        let ctFormId = el.CT_FORM_ID;
                        let ctType = el.DATA_TYPE;
                        let rpt_param_name = el.RPT_PARAM_NAME;
                        let param_child = el.PARAM_CHILD;
                        let ctFormElement = $('#content_CTPhieu > .ct-form-id[data-ct-form-id="' + ctFormId + '"]');
                        if (type == 1) {
                            if (split && split !== "") {
                                dulieu.split(split).forEach(function (el2, idx2) {
                                    ctFormElement.find("input#textfield_" + ctFormId + "_" + (idx2 + 1)).val(el2);
                                });
                            } else {
                                ctFormElement.find("input#textfield_" + ctFormId).val(dulieu);
                            }
                        }

                        if (type == 2) {
                            let _html = "";
                            let _ct_form_id = ctFormId;
                            let sql_par = {"FORM_ID": formid, "PARAM_CHILD": param_child, "KEY_NAME": "2", "PHIEU_ID": phieuid};
                            let _listObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.29", JSON.stringify(sql_par));
                            if (_listObject.length > 0) {
                                _ct_form_id = "paramChild" + _listObject[0].CT_FORM_ID;
                                if (_listObject[0].LOAI_CHACON === "1") {
                                    if (dulieu === "2" || dulieu === "" || dulieu === null) {
                                        _html = "<div id='" + _ct_form_id + "'></div>";
                                        _setHtmlElement(_html, _ct_form_id);
                                    } else {
                                        let loaidl = _listObject[0].LOAI_DL;
                                        if (loaidl == 2) {
                                            _html = buildRadio(_listObject[0], 0, 0);
                                        } else if (loaidl == 3) {
                                            _html = buildCheckbox(_listObject[0], 0, 10);
                                        }
                                        _setHtmlElement(_html, _ct_form_id);
                                        $("#" + _ct_form_id).removeClass("hidden");
                                    }
                                }
                            }
                            ctFormElement.find('input[value="' + dulieu + '"]').prop("checked", true);
                        }

                        if (type == 3) {
                            dulieu.split("|").forEach(function (el2, idx2) {
                                ctFormElement.find('input#checkbox_' + ctFormId + '[value="' + el2 + '"]').prop("checked", true);
                            });
                        }

                        if (type == 4) {
                            ctFormElement.find("input#datepicker_" + ctFormId).val(dulieu);
                        }

                        if (type == 5) {
                            ctFormElement.find("select#combobox_" + ctFormId).val(dulieu);
                            if (_maformphieu[0] == 'BMTE_THONGTINSINHNO') {
                                let _value = $("#combobox_" + ctFormId).val();
                                let _rpt_param_name = el.RPT_PARAM_NAME;
                                let sql_par = {"FORM_ID": formid, "PARAM_CHILD": _rpt_param_name, "KEY_NAME": "1", "PHIEU_ID": phieuid};
                                let _listObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.29", JSON.stringify(sql_par));
                                if (_listObject.length > 0) {
                                    _listObject.forEach(function (data) {
                                        if (data.LOAI_DL == '1') {
                                            _value == '5' ? $("#textfield_" + data.CT_FORM_ID).attr("disabled", true) : $("#textfield_" + data.CT_FORM_ID).attr("disabled", false)
                                            if (data.REQUIRED == '1') {
                                                _value == '5' ? $("#textfield_" + data.CT_FORM_ID).removeAttr("valrule") : $("#textfield_" + data.CT_FORM_ID).attr("valrule", data.TIEU_DE_DL + ",required");
                                                _value == '5' ? $("#required_" + data.CT_FORM_ID).removeClass("required") : $("#required_" + data.CT_FORM_ID).addClass("required")
                                            }
                                        } else if (data.LOAI_DL == '5') {
                                            _value == '5' ? $("#combobox_" + data.CT_FORM_ID).attr("disabled", true) : $("#combobox_" + data.CT_FORM_ID).attr("disabled", false)
                                            if (data.REQUIRED == '1') {
                                                _value == '5' ? $("#combobox_" + data.CT_FORM_ID).removeAttr("valrule") : $("#combobox_" + data.CT_FORM_ID).attr("valrule", data.TIEU_DE_DL + ",required");
                                                _value == '5' ? $("#required_" + data.CT_FORM_ID).removeClass("required") : $("#required_" + data.CT_FORM_ID).addClass("required")
                                            }
                                        }
                                    });
                                }
                            } else if (_maformphieu[0] == 'BMTE_TTKT' && rpt_param_name == 'P_9') {
                                if (dulieu == '0') {
                                    $("#BMTESLTS_" + _getDataChiTietFormId(ctphieuids, 'P_10')).addClass("hidden");
                                } else {
                                    $("#BMTESLTS_" + _getDataChiTietFormId(ctphieuids, 'P_10')).removeClass("hidden");
                                }
                            }
                        }

                        if (type == 7) {
                            let _html = "";
                            let _kieudulieu = "";
                            let dlar = dulieu.split("|");
                            if (dlar.length > 1) {
                                let option = $('<option value="' + dlar[0] + '">' + dlar[1] + '</option>');
                                ctFormElement.find("select#cboSCOMBOBOX_" + ctFormId).empty();
                                ctFormElement.find("select#cboSCOMBOBOX_" + ctFormId).append(option);
                                let sql_par = {"FORM_ID": formid, "PARAM_CHILD": rpt_param_name, "KEY_NAME": "1", "PHIEU_ID": phieuid};
                                let _listObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.29", JSON.stringify(sql_par));
                                if (_listObject.length > 0) {
                                    let _ct_form_id = "paramChild" + _listObject[0].CT_FORM_ID;
                                    let loaidl = _listObject[0].LOAI_DL;
                                    if (loaidl == 2) {
                                        _html = buildRadio(_listObject[0], _kieudulieu, dlar[0]);
                                    } else if (loaidl == 3) {
                                        _html = buildCheckbox(_listObject[0], _kieudulieu, dlar[0]);
                                    }
                                    _setHtmlElement(_html, _ct_form_id);
                                }
                            }
                        }

                        if (type == 8) {
                            $("#stextfield_" + ctFormId).empty();
                            let dlar = dulieu.split("|");
                            let datatype = (ctType == undefined || ctType == "" ? "1" : ctType)
                            let rq = $("#btnTEXTFIELDADD_" + ctFormId).data("required");
                            let rqText = rq == '1' ? 'valrule="' + $("#btnTEXTFIELDADD_" + ctFormId).data("label") + ',required"' : "";
                            if (dlar.length > 1 || (dlar.length == 1 && dlar[0] != "")) {
                                dlar.forEach(function (dl, idx) {
                                    let stf = $('<div class="col-md-2 low-padding mgb3">\n' + '<input class="form-control input-sm kb-i-col-m" ' + rqText + ' value="' + dl + '" type=' + (datatype == 1 ? "text" : "number") + ' id="txtSTextField_' + ctFormId + idx + '" style="width: 100%; height: 23px">\t\n' + '</div>\n' + '<div class="col-md-1 low-padding">\n' + '<button style="background-color: #fff; height: 23px" type="button" class="btn btn-sm btn-secondary" onclick="ntu02d204_removeSTextField(this)" id="btnSTFCLEAR_' + ctFormId + '" modedisxt="">\n' + '<span class="glyphicon glyphicon-remove"></span>\n' + '</button>\t\n' + '</div>');
                                    $("#stextfield_" + ctFormId).append(stf);
                                });
                            }
                        }

                        if (type == 10) {
                            if (_mutilCombobox == "1") {
                                ctFormElement.find("input#txtMSCFULL_" + ctFormId).val(dulieu.split("&&")[0]);
                                ctFormElement.find("input#txtMSCGC_" + ctFormId).val(dulieu.split("&&")[1]);
                            } else {
                                ctFormElement.find("input#txtMSCFULL_" + ctFormId).val(dulieu);
                            }
                        }

                        if (type == 11) {
                            ctFormElement.find("textarea#textarea_" + ctFormId).val(dulieu);
                        }

                        if (type == 12) {
                            ctFormElement.find("#SubData_" + ctFormId).attr("data-phieuchitietid", el.CHITIETPHIEUID);
                            ctFormElement.find("#SubData_" + ctFormId + " table.ctc-element-grid").attr("data-phieuchitietid", el.CHITIETPHIEUID);
                        }

                        if (type == 13) {
                            ctFormElement.find(".html-content").html(el.DATA_CLOB);
                            ctFormElement.find(".html-content").data("dl", JSON.parse(el.DULIEU));
                        }

                        if (type == 14) {
                            ctFormElement.find("input#date_" + ctFormId).val(dulieu);
                        }
                    })
                    loadDataGridChilds();
                } else {
                    $(".ctc-over-el").addClass("ctc-over-locked");
                    $(".ctc-over-el").show();
                }

                $(".ctc-element-grid").toArray().forEach(grid => {
                    let idG = grid.id;
                    $('#' + idG).jqGrid('setGridWidth', $("#gbox_" + idG).parent().width());
                })
            }
        });

        $("#cboDANHSACH").change(function (event) {
            _maformphieu = jsonrpc.AjaxJson.dbExecuteQuery("", "NTU02D204.30", [{"name": "[0]", value: $("#cboDANHSACH").val()}]);
            _checkChange = false;
            let sql_par = [];
            sql_par.push({
                "name": "[0]", value: $("#cboDANHSACH").val()
            });
            _moreClass(['tabHanhChinh']);
            $("#btnInKySoAllCA").hide();
            let _option = $('<option value="-1">--Tất cả--</option>');
            $("#cboPhieuCha").empty();
            $("#cboPhieuCha").append(_option);
            $("#cboPhieuCha").val("-1").change();
            let _formid = $(event.currentTarget).val();
            if (_formid == '-1') {
                $("#btnTaoMoi").attr("disabled", true);
            } else {
                $("#btnTaoMoi").attr("disabled", false);
            }
            $("#btnXoa").attr("disabled", true);
            $("#btnInPhieuAll").hide();
            $("#btnKySoInAll").hide();
            $("#btnInKySoAll").hide();
            $("#btnHuyKySoAll").hide();
            $("#btnInKySoAllOne").hide();
            $("#btnCopy").attr("disabled", true);
            _setParamDefaultForm();
            loadGridData();
            let _sql_par = [];
            _sql_par.push({
                "name": "[0]", value: $(event.currentTarget).val()
            });
            let _gp = jsonrpc.AjaxJson.getOneValue('NTU02D204.09', _sql_par).split("|");
            gomphieu = _gp.length > 0 ? _gp[0] : "";
            tieudeformngay = _gp.length > 1 ? _gp[1] : "";
            rptcodemerge = _gp.length > 2 ? _gp[2] : "";
            if (rptcodemerge && rptcodemerge != "") {
                $("#btnInPhieuGop").show();
            } else {
                $("#btnInPhieuGop").hide();
            }
            if (gomphieu == 1) {
                $(".phieu-cha").hide();
                if ((_data_config[0].CONFIG_ON_OFF_CA_DTSS == '1') && (_configallticketsca.MAFORM).indexOf(_maformphieu[0]) >= 0 && _configallticketsca.GIATRI === "1" && _configallticketsca.MUTIL === "1") {
                    $("#btnInKySoAllCA").show();
                }
            } else if (gomphieu == 2 || gomphieu == 3) {
                $(".phieu-cha").show();
                _setParentTicketData();
                _maformcha = jsonrpc.AjaxJson.dbExecuteQuery("", "NTU02D204.30", [{"name": "[0]", value: _formid}]);
                let _maform = _checkExistMaForm(_configshowform.MAFORM, _maformcha[0] + "-");
                let _ctl_sql = (_maformcha[0] == _maform) ? "NTU02D204.31" : "NTU02D204.27";
                let sql_par1 = [{"name": "[0]", value: objectVar.KHAMBENHID}, {"name": "[1]", value: "-" + _formid}, {"name": "[2]", value: _formid}];
                let _col = "PHIEUID,PHIEUID,20,0,t,l;TENPHIEU,TENPHIEU,70,0,f,l";
                ComboUtil.initComboGrid('txtPHIEUCHA', _ctl_sql, sql_par1, "600px", _col, function (event, ui) {
                    $("#txtPHIEUCHA").val("");
                    let option = $('<option value="' + ui.item.PHIEUID + '">' + ui.item.TENPHIEU + '</option>');
                    $("#cboPhieuCha").empty();
                    $("#cboPhieuCha").append(option);
                    _checkChange = true;
                    if ((_configallticketsca.MAFORM).indexOf(_maformcha[0].join()) >= 0 && _configallticketsca.GIATRI === "1" && _configallticketsca.MUTIL === "1") {
                        $("#btnInKySoAllCA").show();
                    }
                    loadGridData();
                    return false;
                });

                $("#btnCLEAR_PHIEUCHA").click(function () {
                    $("#cboPhieuCha").empty();
                    $("#cboPhieuCha").append(_option);
                    if ((_configallticketsca.MAFORM).indexOf(_maformcha[0].join()) >= 0 && _configallticketsca.GIATRI === "1" && _configallticketsca.MUTIL === "1") {
                        $("#btnInKySoAllCA").hide();
                    }
                    loadGridData();
                });
            } else if (gomphieu == "null") {
                $(".phieu-cha").hide();
                $("#cboPhieuCha").empty();
                $("#cboPhieuCha").append(_option);
            }
        });

        $("#cboPhieuCha").change(function () {
            $("#btnXoa").attr("disabled", true);
            $("#btnInPhieuAll").hide();
            $("#btnKySoInAll").hide();
            $("#btnInKySoAll").hide();
            $("#btnHuyKySoAll").hide();
            $("#btnInKySoAllOne").hide();
            loadGridData();
        });

        GridUtil.setGridParam(_gridId, {
            onSelectRow: function (id, status) {
                GridUtil.unmarkAll(_gridId);
                GridUtil.markRow(_gridId, id);
                if (id) {
                    let _row = $("#" + _gridId).jqGrid('getRowData', id);
                    // Đưa các danh sách id khi chọn vào một mảng mới
                    _onSelectRowId(id, status, _row);
                    _onClickRowId(id, status, _row);
                    // Set dữ liệu vào từng biến khi chọn dòng dữ liệu
                    _setDataSelectRowId(id, _row);
                    if (rptcode && rptcode != "" && _indexId != undefined) {
                        if (ch.HIS_SUDUNG_KYSO_KYDIENTU == '1') {
                            if (_row.FLAG_CA == '1') {
                                if (_exitMaForm == _maformphieu[0] && _hiddenInPhieu == '1') {
                                    $("#btnInPhieuAll").hide();
                                } else {
                                    $("#btnInPhieuAll").show();
                                }
                                $("#btnKySoInAll").hide();
                                _onClickViewCa(_maformphieu);
                                $("#btnHuyKySoAll").show();
                            } else {
                                if (_exitMaForm == _maformphieu[0] && _hiddenInPhieu == '1') {
                                    $("#btnInPhieuAll").hide();
                                } else {
                                    $("#btnInPhieuAll").show();
                                }
                                $("#btnKySoInAll").show();
                                _onClickViewCa(_maformphieu);
                                $("#btnHuyKySoAll").show();
                            }
                        } else {
                            if (_exitMaForm == _maformphieu[0] && _hiddenInPhieu == '1') {
                                $("#btnInPhieuAll").hide();
                            } else {
                                $("#btnInPhieuAll").show();
                            }
                        }
                    } else {
                        $("#btnInPhieuAll").hide();
                        $("#btnKySoInAll").hide();
                        $("#btnInKySoAll").hide();
                        $("#btnHuyKySoAll").hide();
                        $("#btnInKySoAllOne").hide();
                    }
                    if (rptcodedetail && rptcodedetail != "") {
                        $("#btnInPhieu").show();
                        $("#btnLuuKy").show();
                        $("#btnKySoIn").show();
                        $("#btnHuyKySo").show();
                        $("#btnInKySo").show();
                    } else {
                        $("#btnInPhieu").hide();
                        $("#btnLuuKy").hide();
                        $("#btnKySoIn").hide();
                        $("#btnHuyKySo").hide();
                        $("#btnInKySo").hide();
                    }

                    if (rptcodemerge && rptcodemerge != "") {
                        $("#btnInPhieuGop").show();
                    } else {
                        $("#btnInPhieuGop").hide();
                    }

                    _setHiddenButtonCa(_flgCha);
                    let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.04", _row.FORM_ID);
                    console.log(data_ar);
                    if (data_ar.length > 0) {
                        $("#btnLuu").attr("disabled", false);
                        // START L2PT-77963
                        let congifTime = (_configtimelabel.MAFORM).indexOf((maphieu.split("-")[0])) == 0 && _configtimelabel.GIATRI == 1 ? 1 : 0;
                        // END L2PT-77963
                        let html = "" + '<div class="mgt5">';
                        html = html + '<div ' + (_checkDatePerform === "1" ? "hidden" : "") + ' class="col-md-12 mgt5">' + '<div class="col-md-3 low-padding">' + '<label class="mgl5">' + (congifTime == 1 ? _configtimelabel.TITLE : "Ngày thực hiện") + '</label>' + '</div>' + '<div class="col-md-3 low-padding" style="display: flex;">';
                        html = html + `<div class="input-group" style="width: ` + (congifTime == 1 ? _configtimelabel.WIDTH + "%" : "95" + "%") + `;">
                                    <input ` + (congifTime == 1 ? "" : "disabled") + ` class="form-control input-sm" id="datepicker_TGTH"
                                    data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19"
                                    value="` + ngaythuchien + `"><span
                                    class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                                    type="sCal" id='sp'
                                    onclick="NewCssCal('datepicker_TGTH','ddMMyyyy','dropdown',true,'24',true)"></span>
                                    </div>`;
                        html = html + '</div><div ' + (_checkDateCreated === "1" ? "" : "hidden") + ' class="col-md-6 form-inline low-padding ">' + '<div class="col-md-2 low-padding"><label>Ngày tạo phiếu: </label></div>' + '<div class="col-md-6 low-padding" style="display: flex;">' + '<div class="input-group" style="width: 95%">' + '<input ' + (congifTime == 1 ? "" : "disabled") + ' class="form-control input-sm disabled" id="datepicker_TGTP" data-mask="00/00/0000 00:00:00" placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19" value="' + ngaytaophieu + '">' + '<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" type="sCal" id=\'sp\' onclick="NewCssCal(\'datepicker_TGTP\',\'ddMMyyyy\',\'dropdown\',true,\'24\',true)"></span>' + '</div>' + '</div></div></div></div></div>';
                        let _loaipsc = ((_row.MA_FORM).split("-")[0] == _checkExistMaForm(_configshowform.MAFORM, (_row.MA_FORM).split("-")[0])) ? 1 : 0;
                        data_ar.forEach(function (el) {
                            _arrayChiTietId.push({
                                CT_FORM_ID: el.CT_FORM_ID, RPT_PARAM_NAME: el.RPT_PARAM_NAME
                            });
                            if (el.LOAI_DL == 1) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildTextField(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildTextField(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildTextField(el);
                                }
                            }

                            if (el.LOAI_DL == 2) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildRadio(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildRadio(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildRadio(el);
                                }
                            }

                            if (el.LOAI_DL == 3) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildCheckbox(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildCheckbox(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildCheckbox(el);
                                }
                            }

                            if (el.LOAI_DL == 4) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildDatePicker(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildDatePicker(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildDatePicker(el);
                                }
                            }

                            if (el.LOAI_DL == 5) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildCombobox(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildCombobox(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildCombobox(el);
                                }
                            }

                            if (el.LOAI_DL == 6) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildTabTitle(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildTabTitle(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildTabTitle(el);
                                }
                            }

                            if (el.LOAI_DL == 7) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildSCombobox(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildSCombobox(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildSCombobox(el);
                                }
                            }

                            if (el.LOAI_DL == 8) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildTextFieldDynamic(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildTextFieldDynamic(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildTextFieldDynamic(el);
                                }
                            }

                            if (el.LOAI_DL == 9) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildLabelHeader(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildLabelHeader(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildLabelHeader(el);
                                }
                            }

                            if (el.LOAI_DL == 10) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildMSCombobox(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildMSCombobox(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildMSCombobox(el);
                                }
                            }

                            if (el.LOAI_DL == 11) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildTextArea(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildTextArea(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildTextArea(el);
                                }
                            }

                            if (el.LOAI_DL == 12) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildSubDataArea(el, phieuid);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildSubDataArea(el, phieuid);
                                } else if (_loaipsc == 0) {
                                    html = html + buildSubDataArea(el, phieuid);
                                }
                            }

                            if (el.LOAI_DL == 13) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildHTMLTemplate(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildHTMLTemplate(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildHTMLTemplate(el);
                                }
                            }

                            if (el.LOAI_DL == 14) {
                                if (el.THONGTIN_CHUNG == 1 && _row.FLAG_CHA == 1 && _loaipsc == 1) {
                                    html = html + buildDate(el);
                                } else if (el.THONGTIN_CHUNG == 0 && _row.FLAG_CHA == 0 && _loaipsc == 1) {
                                    html = html + buildDate(el);
                                } else if (_loaipsc == 0) {
                                    html = html + buildDate(el);
                                }
                            }
                        });
                        $("#content_CTPhieu").html(html);
                        $("[id*='txtSCOMBOBOX_']").each(function (index, el2) {
                            let _html = "";
                            let scboid = $(el2).data("scboid");
                            let txtId = $(el2).attr("id");
                            let sqlDl = $(el2).data("sql-dl");
                            let child = $(el2).data("child-param");
                            let _col = "ID,ID,20,0,t,l;Danh sách,NAME,70,0,f,l";
                            let sql_par = [];
                            sql_par.push({"name": "[0]", "value": objectVar.KHOAID}, {"name": "[1]", "value": objectVar.PHONGID}, {"name": "[2]", "value": objectVar.BENHNHANID}, {
                                "name": "[3]",
                                "value": objectVar.TIEPNHANID
                            }, {"name": "[4]", "value": objectVar.HOSOBENHANID}, {"name": "[5]", "value": objectVar.KHAMBENHID}, {"name": "[6]", "value": _opts._hospital_id});
                            ComboUtil.initComboGrid(txtId, sqlDl, sql_par, "650px", _col, function (event, ui) {
                                $("#" + txtId).val("");
                                let option = $('<option value="' + ui.item.ID + '">' + ui.item.NAME + '</option>');
                                $("#" + scboid).empty();
                                $("#" + scboid).append(option);
                                // Lấy thông tin Element con theo Element cha
                                let sql_par = {"FORM_ID": formid, "PARAM_CHILD": child, "KEY_NAME": "1", "PHIEU_ID": phieuid};
                                let _listObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.29", JSON.stringify(sql_par));
                                let _ct_form_id = "";
                                if (_listObject.length > 0) {
                                    _ct_form_id = "paramChild" + _listObject[0].CT_FORM_ID;
                                    let loaidl = _listObject[0].LOAI_DL;
                                    if (loaidl == 2) {
                                        _html = buildRadio(_listObject[0], '', (ui.item.ID));
                                    } else if (loaidl == 3) {
                                        _html = buildCheckbox(_listObject[0], '', (ui.item.ID));
                                    }
                                    _setHtmlElement(_html, _ct_form_id);
                                }
                                return false;
                            });
                        });

                        $("[id*='btnCLEAR_']").each(function (index, el3) {
                            $("#" + $(el3).attr("id")).click(function () {
                                let child = $(el3).data("child-param");
                                let required = $(el3).data("required");
                                let sql_par = {"FORM_ID": formid, "PARAM_CHILD": child, "KEY_NAME": "1", "PHIEU_ID": phieuid};
                                let _listObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.29", JSON.stringify(sql_par));
                                let _ct_form_id = "";
                                let option = $('<option value="' + (required == 1 ? "" : "-1") + '">--Lựa chọn--</option>');
                                $("#cboSCOMBOBOX" + $(el3).attr("id").substring(8)).empty();
                                $("#cboSCOMBOBOX" + $(el3).attr("id").substring(8)).append(option);
                                if (_listObject.length > 0) {
                                    _ct_form_id = "paramChild" + _listObject[0].CT_FORM_ID;
                                    document.getElementById("" + _ct_form_id).innerHTML = "";
                                }
                            });
                        });

                        $("[id*='btnTEXTFIELDADD_']").each(function (index, el4) {
                            let _ctformid = $(el4).attr("id").substring(16);
                            let rqText = $(el4).data("required") == '1' ? 'valrule="' + $(el4).data("label") + ',required"' : "";
                            let _type = $(el4).data("type") == undefined || $(el4).data("type") == "" ? "1" : $(el4).data("type");
                            $("#" + $(el4).attr("id")).click(function () {
                                let stf = $('<div class="col-md-2 low-padding mgb3">\n' + '<input class="form-control input-sm kb-i-col-m" ' + rqText + ' type="' + (_type == 1 ? "text" : "number") + '" id="txtSTextField_' + _ctformid + new Date().getTime() + '" style="width: 100%; height: 23px">\t\n' + '</div>\n' + '<div class="col-md-1 low-padding">\n' + '<button style="background-color: #fff; height: 23px" type="button" class="btn btn-sm btn-secondary" onclick="ntu02d204_removeSTextField(this)" id="btnSTFCLEAR_' + _ctformid + '" modedisxt="">\n' + '<span class="glyphicon glyphicon-remove"></span>\n' + '</button>\t\n' + '</div>');
                                $("#stextfield_" + _ctformid).append(stf);
                            });
                        });

                        $("[id*='txtMSC_']").each(function (index, el5) {
                            let _ctformid = $(el5).attr("id").substring(7);
                            let txtId = $(el5).attr("id");
                            let sqlDl = $(el5).data("sql-dl");
                            let _col = "Mã,ID,20,0,f,l;Danh sách,NAME,80,0,f,l";
                            let sql_par = [];
                            sql_par.push({"name": "[0]", "value": objectVar.KHOAID}, {"name": "[1]", "value": objectVar.PHONGID}, {
                                "name": "[2]", "value": objectVar.BENHNHANID
                            }, {
                                "name": "[3]", "value": objectVar.TIEPNHANID
                            }, {"name": "[4]", "value": objectVar.HOSOBENHANID}, {"name": "[5]", "value": objectVar.KHAMBENHID});
                            ComboUtil.init(txtId, sqlDl, sql_par, "600px", _col, function (event, ui) {
                                let _mscTextFull = $("#txtMSCFULL_" + _ctformid).val();
                                let _mscTextFullSet = new Set(_mscTextFull.split(";").filter(_msc => _msc != ""));
                                _mscTextFullSet.add(ui.item.NAME);
                                let _mscTextFullAfter = "";
                                for (const v of _mscTextFullSet) {
                                    if (_mscTextFullAfter == "") {
                                        _mscTextFullAfter = _mscTextFullAfter + v;
                                    } else {
                                        _mscTextFullAfter = _mscTextFullAfter + ";" + v;
                                    }
                                }
                                $("#txtMSCFULL_" + _ctformid).val(_mscTextFullAfter.replaceAll(/^;/, "").replaceAll(/;$/), "");
                            }, undefined, undefined, {showOn: true});
                        });

                        $("[id*='btnMSCCLEAR_']").each(function (index, el6) {
                            let _ctformid = $(el6).attr("id").substring(12);
                            $(el6).click(function () {
                                $("#txtMSCFULL_" + _ctformid).val("");
                                $("#txtMSCGC_" + _ctformid).val("");
                            });
                        });

                        $("[id*='btnMSCEDIT_']").each(function (index, el6) {
                            let _ctformid = $(el6).attr("id").substring(11);
                            let _ctlsql = $(el6).attr("data-ctl-sql");
                            $(el6).click(function () {
                                console.log(_ctlsql);
                                let textLabel = $("#lblMSC_" + _ctformid).text();
                                let paramInput = {
                                    objData: $("#txtMSCFULL_" + _ctformid).val(), objCtformid: _ctformid, textLabel: textLabel.substring(0, textLabel.length - 1), ctlsql: _ctlsql.toString()
                                };
                                let url = "manager.jsp?func=../noitru/NTU02D207_ChinhSuaDuLieuType10";
                                let popup = DlgUtil.buildPopupUrl("divDlgNTU02D207", "divDlg", url, paramInput, "Chi tiết danh sách " + textLabel.substring(0, textLabel.length - 1), 840, 600);
                                popup.open("divDlgNTU02D207");
                            });
                        });

                        $("[id*='combobox_']").each(function (index, el6) {
                            let _ctformid = $(el6).attr("id").substring(9);
                            $(el6).change(function (e) {
                                let _value = $("#combobox_" + _ctformid).val();
                                let _rpt_param_name = $(el6).attr("father-param-name");
                                let _child_param_name = $(el6).attr("father-param-name");
                                if (_maformphieu[0] == 'BMTE_THONGTINSINHNO') {
                                    let sql_par = {"FORM_ID": formid, "PARAM_CHILD": _rpt_param_name, "KEY_NAME": "1", "PHIEU_ID": phieuid};
                                    let _listObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.29", JSON.stringify(sql_par));
                                    if (_listObject.length > 0) {
                                        _listObject.forEach(function (data) {
                                            if (data.LOAI_DL == '1') {
                                                _value == '5' ? $("#textfield_" + data.CT_FORM_ID).attr("disabled", true) : $("#textfield_" + data.CT_FORM_ID).attr("disabled", false)
                                                if (data.REQUIRED == '1') {
                                                    _value == '5' ? $("#textfield_" + data.CT_FORM_ID).removeAttr("valrule") : $("#textfield_" + data.CT_FORM_ID).attr("valrule", data.TIEU_DE_DL + ",required");
                                                    _value == '5' ? $("#required_" + data.CT_FORM_ID).removeClass("required") : $("#required_" + data.CT_FORM_ID).addClass("required")
                                                }
                                            } else if (data.LOAI_DL == '5') {
                                                _value == '5' ? $("#combobox_" + data.CT_FORM_ID).attr("disabled", true) : $("#combobox_" + data.CT_FORM_ID).attr("disabled", false)
                                                if (data.REQUIRED == '1') {
                                                    _value == '5' ? $("#combobox_" + data.CT_FORM_ID).removeAttr("valrule") : $("#combobox_" + data.CT_FORM_ID).attr("valrule", data.TIEU_DE_DL + ",required");
                                                    _value == '5' ? $("#required_" + data.CT_FORM_ID).removeClass("required") : $("#required_" + data.CT_FORM_ID).addClass("required")
                                                }
                                            }
                                        });
                                    }
                                } else if (_maformphieu[0] == 'BMTE_TTKT' && _rpt_param_name == 'P_9') {
                                    if (_value == '0') {
                                        $("#BMTESLTS_" + _getDataChiTietFormId(data_ar, 'P_10')).addClass("hidden");
                                    } else {
                                        $("#BMTESLTS_" + _getDataChiTietFormId(data_ar, 'P_10')).removeClass("hidden");
                                    }
                                }
                            });
                        });

                        // Xử lý validate Tuần thai trong thông tin khám thai.
                        $("[id*='textfield_']").each(function (index, el6) {
                            let _ctformid = $(el6).attr("id");
                            let _rpt_param_name = $(el6).attr("rpt-param-name");
                            if (_maformphieu[0] == 'BMTE_TTKT' && _rpt_param_name == 'P_11') {
                                $("#" + _ctformid).on("change", function (e) {
                                    let _value = Number(e.target.value) ? Number(e.target.value) : 0;
                                    if (_value < 1 || _value > 42) {
                                        $("#date_" + _getDataChiTietFormId(data_ar, "P_5")).val("");
                                        DlgUtil.showError("Trường (Tuần thai) phải thỏa mãn điều kiện: 1 ≤ Tuần thai ≤ 42", 1500);
                                    } else {
                                        // Tính ngày kinh cuối
                                        let _ngaykham = $("#datepicker_" + _getDataChiTietFormId(data_ar, "P_2")).val();
                                        let _today_ngaydaukinh = new Date((_ngaykham.substring(6, 10) + "-" + _ngaykham.substring(3, 5) + "-" + _ngaykham.substring(0, 2)).toString())
                                        let _ngaydaukinh = new Date(_today_ngaydaukinh.getTime() - (_value * 7) * 24 * 60 * 60 * 1000);
                                        $("#date_" + _getDataChiTietFormId(data_ar, "P_5")).val((_ngaydaukinh.getDate() < 10 ? '0' + _ngaydaukinh.getDate() : _ngaydaukinh.getDate()) + "/" + ((_ngaydaukinh.getMonth() + 1) < 10 ? '0' + (_ngaydaukinh.getMonth() + 1) : (_ngaydaukinh.getMonth() + 1)) + "/" + (_ngaydaukinh.getFullYear()));
                                        // Tính ngày dự kiến sinh
                                        let _ngaydukiensinh = new Date(_ngaydaukinh.getTime() + 277 * 24 * 60 * 60 * 1000);
                                        $("#date_" + _getDataChiTietFormId(data_ar, "P_17")).val((_ngaydukiensinh.getDate() < 10 ? '0' + _ngaydukiensinh.getDate() : _ngaydukiensinh.getDate()) + "/" + ((_ngaydukiensinh.getMonth() + 1) < 10 ? '0' + (_ngaydukiensinh.getMonth() + 1) : (_ngaydukiensinh.getMonth() + 1)) + "/" + (_ngaydukiensinh.getFullYear()));
                                    }
                                })
                            }
                        });

                        $("#timepicker_TGTH").timepicker();
                        $.jMaskGlobals = {
                            maskElements: 'input,td,span,div',
                            dataMaskAttr: '*[data-mask]',
                            dataMask: true,
                            watchInterval: 300,
                            watchInputs: true,
                            watchDataMask: true,
                            byPassKeys: [9, 16, 17, 18, 36, 37, 38, 39, 40, 91],
                            translation: {
                                '0': {pattern: /\d/}, '9': {pattern: /\d/, optional: true}, '#': {pattern: /\d/, recursive: true}, 'A': {pattern: /[a-zA-Z0-9]/}, 'S': {pattern: /[a-zA-Z]/}
                            }
                        };

                        $("#timepicker_TGTH").change(function (event) {
                            let _dategn = $(".date-gom-ngay").val().substring(0, 11);
                            $(".date-gom-ngay").val(_dategn + $(event.currentTarget).val() + ":00");
                        });

                        if (_row.LOAI_PHIEU == ch.HIS_NTU_THEMPHIEU_LOAIPHIEU) {
                            let data_ar;
                            data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D001.05", objectVar.KHAMBENHID, []);
                            if (data_ar != null && data_ar.length > 0) {
                                _row = data_ar[0];
                                let strSTT = ch.HIS_NTU_THEMPHIEU_DTM_FORMCT.split(',');
                                $("#textfield_" + strSTT[0]).val(_row.NHOMMAU);
                                if (_row.RH == '1') {
                                    $("#textfield_" + strSTT[1]).val('+');
                                } else if (_row.RH == '2') {
                                    $("#textfield_" + strSTT[1]).val('-');
                                }
                            }
                        }

                        let data_radio = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.34", (_row.FORM_ID + "|" + phieuid));
                        let _listRadioAll = [];
                        let _listRadio = [];
                        if (data_radio.length > 0) {
                            data_radio.forEach(function (row) {
                                _listRadioAll.push({"CT_FORM_ID": row.CT_FORM_ID, "VALUE_DATA": row.VALUE_DATA});
                            })
                        }
                        $("[id*='radio_']").change(function (e) {
                            let _html = "";
                            let childparam = $(e.currentTarget).attr("child-param");
                            let radio_id = $(e.currentTarget).attr("id");
                            let value = $(e.currentTarget).attr("value");
                            let sql_par = {"FORM_ID": formid, "PARAM_CHILD": childparam, "KEY_NAME": "2", "PHIEU_ID": phieuid};
                            let _listObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.29", JSON.stringify(sql_par));
                            let _ct_form_id = "";
                            if (_listObject.length > 0) {
                                _ct_form_id = "paramChild" + _listObject[0].CT_FORM_ID;
                                if (_listObject[0].LOAI_CHACON === "1") {
                                    if (value === "2" || value === "" || value === null) {
                                        _html = "<div id='" + _ct_form_id + "'></div>";
                                        _setHtmlElement(_html, _ct_form_id);
                                    } else {
                                        let loaidl = _listObject[0].LOAI_DL;
                                        if (loaidl == 2) {
                                            _html = buildRadio(_listObject[0], 0, 0);
                                        } else if (loaidl == 3) {
                                            _html = buildCheckbox(_listObject[0], 0, 0);
                                        }
                                        _setHtmlElement(_html, _ct_form_id);
                                        $("#" + _ct_form_id).removeClass("hidden");
                                    }
                                } else if (_listObject[0].LOAI_CHACON === "2") {
                                    _listRadioAll.push({"CT_FORM_ID": radio_id, "VALUE_DATA": value});
                                }
                            }
                            if (_listObject[0].LOAI_CHACON === "2") {
                                let countRadio = _listRadioAll.filter(_listRadioAll => _listRadioAll.CT_FORM_ID === radio_id).length;
                                if (countRadio === 0 || countRadio === 1) {
                                    _listRadio = _listRadioAll;
                                } else {
                                    let indexToRemove = _listRadioAll.findIndex(function (radio) {
                                        return radio.CT_FORM_ID === radio_id;
                                    });
                                    if (indexToRemove !== -1) {
                                        _listRadioAll.splice(indexToRemove, 1);
                                    }
                                    _listRadio = _listRadioAll;
                                }
                                _setDataBuildChild(_listRadio, _listObject, value);
                            }
                        });
                    }
                    _loadCboMau();
                    _bindEventC();
                }
            },

            onSelectAll: function (id, status) {
                if (id) {
                    let ret = $("#" + _gridId).jqGrid('getDataIDs');
                    // Đưa dữ liệu khi chọn tất cả vào mảng
                    _onSelectAllRowId(id, status, ret);
                    _onClickAllRowId(id, status, ret);
                }
            },

            ondblClickRow: function () {
                $("#tcctp").click();
                _setParamDefaultForm();
                _removeClass(['tabHanhChinh']);
            },

            gridComplete: function (id) {
                let _rowids = $("#" + _gridId).getDataIDs();
                let _phieuchaid = $("#cboPhieuCha").val();
                if (_rowids && _rowids.length > 0) {
                    _rowids.forEach(function (rowid) {
                        let _row = $("#" + _gridId).jqGrid('getRowData', rowid);
                        let _mafromcha = (_row.MA_FORM).split("-")[0];
                        let _maform = _checkExistMaForm(_configshowform.MAFORM, (_row.MA_FORM).split("-")[0]);
                        if (_row.FLAG_CA && _row.FLAG_CA == '1') {
                            let _icon = '<div style="text-align: center"><img src="../common/image/ca.png" width="15px"></div>';
                            $("#" + _gridId).jqGrid('setCell', rowid, 'ICON', _icon);
                        }
                        if (_row.FLAG_CHA == '1' && (_mafromcha == _maform)) {
                            if (_phieuchaid !== "-1") {
                                let _color = "#fbec88";
                                $("#" + _gridId).find("tr[id='" + rowid + "']").find('td').css("background-color", _color);
                                $("#" + _gridId).find("tr[id='" + rowid + "']").find('td').css("color", "red");
                                $("#" + _gridId).find("tr[id='" + rowid + "']").find('td').css("font-weight", "bold");
                                $("#" + _gridId).find("tr[id='" + rowid + "']").find("input[type=checkbox]").prop("checked", true);
                                $("#" + _gridId).find("tr[id='" + rowid + "']").find("input[type=checkbox]").prop("disabled", true);
                                let _icon = '<div style="text-align: center"><img src="../common/image/Flag_Red.png" width="15px"></div>';
                                $("#" + _gridId).jqGrid('setCell', rowid, 'ICON', _icon);
                            } else {
                                let _color = "#FF0000";
                                $("#" + _gridId).find("tr[id='" + rowid + "']").find('td').css("color", _color);
                                $("#" + _gridId).find("tr[id='" + rowid + "']").find('td').css("font-weight", "bold");
                                $("#" + _gridId).find("tr[id='" + rowid + "']").find("input[type=checkbox]").prop("checked", false);
                                $("#" + _gridId).find("tr[id='" + rowid + "']").find("input[type=checkbox]").prop("disabled", false);
                                let _icon = '<div style="text-align: center"><img src="../common/image/Flag_Red.png" width="15px"></div>';
                                $("#" + _gridId).jqGrid('setCell', rowid, 'ICON', _icon);
                            }
                        }
                    })
                }
            },
        });

        $("#btnTaoMoi").click(function () {
            let _maform = _checkExistMaForm(_configshowform.MAFORM, _maformcha[0] + "-");
            if (_closeMedical == '1') {
                let sql_par = [];
                sql_par.push({"name": "[0]", value: _hosobenhanid});
                _returnMedical = jsonrpc.AjaxJson.getOneValue('NTU.MEDICAL.01', sql_par);
                console.log("Trạng thái bệnh án: " + _returnMedical);
            }
            if (Number(_returnMedical) > 0 && _maformcha[0] == _maform) {
                DlgUtil.showMsg("Đã đóng bệnh án không thể tạo thêm được phiếu.");
            } else {
                let formid = $("#cboDANHSACH").val();
                if (formid != '-1') {
                    let objData = Object.assign({}, objectVar);
                    objData.FORM_ID = formid;
                    objData.GOM_PHIEU = gomphieu;
                    objData.COLUMN_KEY = columnKey;
                    objData.COLUMN_VALUE = columnValue;
                    objData.DEPT_ID = _opts._dept_id;
                    objData.SUBDEPT_ID = _opts._subdept_id;
                    if (gomphieu == 1) {
                        let _maphieucha = jsonrpc.AjaxJson.dbExecuteQuery("", "NTU02D204.30", [{"name": "[0]", value: formid}]);
                        let _maphieucon = _checkExistMaForm(_configformbmte.MAFORM, _maphieucha[0] + "-0");
                        if (_maphieucha[0] == _maphieucon && _configformbmte.GIATRI === '1') {
                            try {
                                let _maicd = _jsonString['ICDCHANDOAN'];
                                let _gioitinh = _jsonString['GIOITINHID'];
                                let _sotuoi = Number(_jsonString['SOTUOI']);
                                let _cancuoccongdan = _jsonString['CANCUOCCONGDAN'];
                                let _mathe = _jsonString['MATHE'];
                                if (_maicd !== "1") {
                                    DlgUtil.showMsg("ICD không nằm trong nhóm khám thai.");
                                } else if (_gioitinh !== "2") {
                                    DlgUtil.showMsg("Giới tính phải là nữ.");
                                } else if (_sotuoi <= 10) {
                                    DlgUtil.showMsg("Tuổi phải > 10 và đơn vị phải là tuổi.");
                                } else if (_cancuoccongdan == '' || _cancuoccongdan == null || _cancuoccongdan == undefined) {
                                    DlgUtil.showMsg("Chưa có thông tin căn cước công dân.");
                                } else if (_mathe === "" || _mathe === null || _mathe === undefined) {
                                    DlgUtil.showMsg("Chưa có thông tin mã thẻ BHYT.");
                                } else {
                                    _insetThemPhieuTaoMoi(formid, objData);
                                }
                            } catch (e) {
                                console.log(e);
                            }
                        } else {
                            _insetThemPhieuTaoMoi(formid, objData);
                        }
                    } else if (gomphieu == 2) {
                        EventUtil.setEvent("divDlgNTU02D206_success", function (e) {
                            setThongTinChung(e.id);
                            loadGridData(e.id);
                            DlgUtil.showMsg("Tạo phiếu thành công !", function () {
                            }, 600);
                            _removeClass(['tabHanhChinh']);
                        });
                        let paramInput = {
                            objData: objData, tieudeformngay: tieudeformngay
                        };
                        let url = "manager.jsp?func=../noitru/NTU02D206_ThemPhieuNgay";
                        let popup = DlgUtil.buildPopupUrl("divDlgNTU02D206", "divDlg", url, paramInput, "Tạo phiếu ngày", 505, 400);
                        popup.open("divDlgNTU02D206");
                    } else if (gomphieu == 3) {
                        EventUtil.setEvent("divDlgNTU02D207_success", function (e) {
                            _setParentTicketData();
                            setThongTinChung(e.id);
                            setComboboxPhieuCha(e.id, _maformcha);
                            loadGridData(e.id);
                            DlgUtil.showMsg("Tạo phiếu thành công !", function () {
                            }, 600);
                            _removeClass(['tabHanhChinh']);
                        });
                        let paramInput = {
                            objData: objData, phieuchaid: _strPhieuChaId ? _strPhieuChaId : "-1", tenphieucha: _strTenPhieuCha ? _strTenPhieuCha : "--Tất cả--"
                        };
                        let url = "manager.jsp?func=../noitru/NTU02D207_ThemPhieuCha";
                        let popup = DlgUtil.buildPopupUrl("divDlgNTU02D207", "divDlg", url, paramInput, "Chỉ định phiếu cha", 800, 250);
                        popup.open("divDlgNTU02D207");
                    }
                }
            }
        });

        $("#btnCopy").click(function () {
            if (phieuid == -1) return;
            EventUtil.setEvent("divDlgNTU02D208_success", function (e) {
                loadGridData();
                DlgUtil.showMsg("Copy phiếu thành công !", function () {
                }, 600);
            });
            let paramInput = {
                objData: {
                    NGAYTHUCHIEN: ngaythuchien, PHIEUID: phieuid + "", GOM_PHIEU: gomphieu + "", KHAMBENHID: objectVar.KHAMBENHID, FORM_ID: formid + "", PHIEUIDCHA: phieuidcha
                }
            };

            let url = "manager.jsp?func=../noitru/NTU02D208_ThemPhieuCopy";
            let popup = DlgUtil.buildPopupUrl("divDlgNTU02D208", "divDlg", url, paramInput, "Copy phiếu", 600, 400);
            popup.open("divDlgNTU02D208");
        });

        $("#btnXoa").click(function () {
            let _strTenKhoa = _checkFuctionKhoa(phieuid, objectVar.KHAMBENHID);
            let _checkKhoa = _strTenKhoa ? _strTenKhoa.split("#")[0] : "";
            if (_checkKhoa === "1") {
                DlgUtil.showMsg("Không thể xóa khi khoa tạo phiếu là: " + _strTenKhoa.split("#")[1]);
            } else {
                DlgUtil.showConfirm("Bạn có muốn xóa phiếu này ko?", function (flag) {
                    if (flag) {
                        let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.03", phieuid);
                        if (fl == 1) {
                            DlgUtil.showMsg("Xóa phiếu thành công!");
                            $("#content_CTPhieu").html("");
                            $("#btnLuu").attr("disabled", true);
                            $("#btnInPhieu").hide();
                            $("#btnLuuKy").hide();
                            $("#btnKySoIn").hide();
                            $("#btnHuyKySo").hide();
                            $("#btnInKySo").hide();
                            _moreClass(['tabHanhChinh']);
                            if (Number(phieuid) == (Number(phieuidcha) + 1)) {
                                _setDefaultComboboxPhieuCha('1');
                            }
                            loadGridData();
                        } else if (fl == 2) {
                            DlgUtil.showMsg("Bạn không có quyền xóa phiếu này.");
                        } else if (fl == 3) {
                            DlgUtil.showMsg("Phiếu đã thực hiện ký không thể xóa.");
                        } else if (fl == 4) {
                            DlgUtil.showMsg("Không thể xóa phiếu cha khi còn tồn tại phiếu con.");
                        } else {
                            DlgUtil.showMsg("Xảy ra lỗi, xóa không thành công");
                        }
                    }
                });
            }
        });

        $("#btnLuu").click(function () {
            _setParamDefaultForm();
            let _strTenKhoa = _checkFuctionKhoa(phieuid, objectVar.KHAMBENHID);
            let _checkKhoa = _strTenKhoa ? _strTenKhoa.split("#")[0] : "";
            if (_checkKhoa === "1") {
                DlgUtil.showMsg("Không thể lưu phiếu khi khoa tạo phiếu là: " + _strTenKhoa.split("#")[1]);
            } else if (_maformphieu[0] == 'BMTE_TTKT') {
                let _ct_form_id = "";
                _ct_form_id = _arrayChiTietId.filter(object => object.RPT_PARAM_NAME == 'P_2');
                let _ngaykhamthai = $("#datepicker_" + _ct_form_id[0].CT_FORM_ID).val().substring(0, 10);
                let _sotuan_sinhde = Math.round(_soTuanTheoKhoangThoiGian(_jsonString['NGAY_SINH_DE'], (_ngaykhamthai ? (_ngaykhamthai.substring(6, 10) + "-" + _ngaykhamthai.substring(3, 5) + "-" + _ngaykhamthai.substring(0, 2)) : "")));
                let _sotuan_phathai = Math.round(_soTuanTheoKhoangThoiGian(_jsonString['NGAY_PHA_THAI'], (_ngaykhamthai ? (_ngaykhamthai.substring(6, 10) + "-" + _ngaykhamthai.substring(3, 5) + "-" + _ngaykhamthai.substring(0, 2)) : "")));
                if (_sotuan_sinhde <= 6 && _sotuan_phathai <= 6) {
                    DlgUtil.showMsg("Ngày khám thai so với ngày sinh đẻ và ngày phá thai gần nhất phải lớn hơn 6 tuần.");
                } else if (_sotuan_sinhde <= 6) {
                    DlgUtil.showMsg("Ngày khám thai so với ngày sinh đẻ gần nhất phải lớn hơn 6 tuần.");
                } else if (_sotuan_phathai <= 6) {
                    DlgUtil.showMsg("Ngày khám thai so với ngày phá thai gần nhất phải lớn hơn 6 tuần.");
                } else {
                    save();
                }
            } else {
                _isKyCA = false;
                if (keyValue == 1) {
                    _kiemTraPhieuPTTT();
                } else {
                    save();
                }
            }
        });

        $("#btnViewImageTemplate").click(function () {
            let mainElement = document.querySelector("#content_CTPhieu .html-content");
            let backupHTML = mainElement.innerHTML;
            document.querySelector("#content_CTPhieu .html-content");
            for (let i of mainElement.querySelectorAll("input[type='text']")) {
                let _value = i.value;
                let span = document.createElement("span");
                span.textContent = _value;
                i.replaceWith(span);
            }
            for (let i of mainElement.querySelectorAll("input[type='checkbox'], input[type='radio']")) {
                i.style.width = "18px";
                i.style.height = "18px";
            }
            html2canvas(document.querySelector("#content_CTPhieu .html-content")).then(canvas => {
                let finalBase64Finger = canvas.toDataURL("image/png");
                let a = document.getElementById("imagePrintData");
                a.href = finalBase64Finger;
                a.click();
                mainElement.innerHTML = backupHTML;
            });
        });

        $("#btnInPhieu").click(function () {
            if (rptcodedetail == "") return;
            if (_printHtml == 1) {
                let _phieuchaid = $("#cboPhieuCha").val();
                if (_phieuchaid == '-1') {
                    DlgUtil.showMsg("Chưa chọn phiếu cha. Vui lòng thao tác lại.", function () {
                        $("#tabdsp").tab("show");
                    });
                } else {
                    let name = formid + '@' + phieuid + '@' + Number(_phieuchaid) + 1 + '@' + 2;
                    let popup = window.open('manager.jsp?func=../noitru/' + _linkHtml + '&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' + screen.height + ',width=' + screen.width);
                    popup.moveTo(0, 0);
                }
            } else if (_configctcflagca.GIATRI === "1" && maphieu.split("-")[0] === _configctcflagca.MAFORM) {
                let listRid = [];
                let _grid = "";
                let _ctFormId = "";
                let _rowDatas = "";
                $(".ctc-element-grid").toArray().forEach(grid => {
                    _grid = grid.id;
                    _ctFormId = grid.getAttribute("data-ct-form-id");
                    _rowDatas = $("#" + grid.id).getDataIDs();
                });
                _rowDatas.forEach(function (el) {
                    let _row = $("#" + _grid).jqGrid('getRowData', el);
                    if (_row.RID.length > 0) {
                        listRid.push(_row.RID);
                    }
                });
                let paramRpt = [];
                paramRpt.push({name: "hosobenhanid", type: "String", value: objectVar.HOSOBENHANID});
                paramRpt.push({name: "rid", type: "String", value: listRid + ''});
                paramRpt.push({name: "rpt_code", type: "String", value: rptcodedetail});
                openReport('window', rptcodedetail, 'pdf', paramRpt);
            } else {
                let paramRpt = [];
                paramRpt.push({name: "phieuid", type: "String", value: phieuid});
                paramRpt.push({name: "khambenhid", type: "String", value: _khambenhid});
                paramRpt.push({name: "formid", type: "String", value: formid});
                paramRpt.push({name: "hosobenhanid", type: "String", value: objectVar.HOSOBENHANID});
                if (keyValue === 1 || keyValue === "1") {
                    paramRpt.push({name: "dichvukhambenhid", type: "String", value: _dichvukhambenhid});
                }
                paramRpt.push({name: "RPT_CODE", type: "String", value: rptcodedetail});
                if (ky_tructiep == '1') {
                    CommonUtil.openReportEmr(paramRpt, false);
                } else {
                    openReport('window', rptcodedetail, 'pdf', paramRpt);
                }
            }
        });

        $("#btnClose").on("click", function (e) {
            let objSql = new Object();
            objSql.FORM_ID = formid;
            objSql.PHIEU_ID = phieuid;
            objSql.DICHVUKHAMBENHID = _dichvukhambenhid;
            objSql.KEY_VALUE = "2";
            let result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.25", objSql);
            if (result == 2) {
                console.log("Xóa phiếu không có chi tiết phiếu thành công.");
                parent.DlgUtil.close("dlgThemPhieu");
            }
        });

        $("#btnXoaPhieu").on("click", function (e) {
            if (phieuid != null || phieuid != "" || phieuid != undefined) {
                DlgUtil.showConfirm("Bạn có muốn xóa phiếu này ko?", function (flag) {
                    if (flag) {
                        let objSql = new Object();
                        objSql.FORM_ID = formid;
                        objSql.PHIEU_ID = phieuid;
                        objSql.DICHVUKHAMBENHID = _dichvukhambenhid;
                        objSql.KEY_VALUE = "3";
                        let result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.25", objSql);
                        if (result == 3) {
                            DlgUtil.showMsg("Xóa phiếu thành công.");
                            $("#btnLuu").attr("disabled", true);
                            $("#btnInPhieu").attr("disabled", true);
                            $("#btnXoaPhieu").attr("disabled", true);
                            $("#btnKySoIn").attr("disabled", true);
                            $("#btnHuyKySo").attr("disabled", true);
                            $("#btnInKySo").attr("disabled", true);
                        } else {
                            DlgUtil.showMsg("Xóa phiếu thất bại.");
                        }
                    }
                });
            }
        });

        $("#btnInPhieuAll").click(function () {
            let _formid = $("#cboDANHSACH").val();
            if (rptcode == "") return;
            let paramRpt = [];
            paramRpt.push({name: "khambenhid", type: "String", value: objectVar.KHAMBENHID});
            paramRpt.push({name: "formid", type: "String", value: _formid});
            paramRpt.push({name: "hosobenhanid", type: "String", value: objectVar.HOSOBENHANID});
            paramRpt.push({name: "RPT_CODE", type: "String", value: rptcode});
            paramRpt.push({name: "phieuid", type: "String", value: phieuid});
            if (ky_tructiep == '1') {
                CommonUtil.openReportEmr(paramRpt, false);
            } else {
                openReport('window', rptcode, 'pdf', paramRpt);
            }
        });

        $("#btnKySoIn, #btnHuyKySo, #btnInKySo").click(function (e) {
            _catype = $(e.currentTarget).data("catype") + "";
            _checkCaKhacKhoa(_catype, '1');
        });

        $("#btnLuuKy").click(function (e) {
            _catype = '1';
            _checkCaKhacKhoa(_catype, '2');
        });

        $("#btnKySoInAll, #btnHuyKySoAll, #btnInKySoAll").click(function (e) {
            let _phieuchaid = $("#cboPhieuCha").val();
            let _mafromcha = (maphieu).split("-")[0];
            let _maform = _checkExistMaForm(_configshowform.MAFORM, _mafromcha);
            if (_phieuchaid == '-1' && _mafromcha == _maform) {
                DlgUtil.showMsg("Chưa chọn phiếu cha. Vui lòng thao tác lại.");
            } else {
                _catype = $(e.currentTarget).data("catype") + "";
                if (_configctcflagca.GIATRI === "1" && maphieu.split("-")[0] === _configctcflagca.MAFORM) {
                    _caRptChiTietList(_catype, 2);
                } else {
                    _checkCaAll = 0;
                    _caRptAll(_catype);
                }
            }
        });

        $("#btnInKySoAllOne").click(function (e) {
            _catype = $(e.currentTarget).data("catype") + "";
            _checkCaAll = 0;
            _caRptAll(_catype, 'ONE');
        });

        $("#btnInPhieuGop").click(function () {
            let _row = $("#" + _gridId).jqGrid('getRowData', $("#" + _gridId).getDataIDs().length);
            let _printHtml = _row.PRINT_HTML;
            let _linkHtml = _row.LINK_HTML;
            let _phieuids = $("#" + _gridId).getGridParam('selarrrow').map(el => $('#' + _gridId).getRowData(el).PHIEUID);
            if (_phieuids.length == 0) {
                _phieuids = _row.PHIEUID;
            }
            let _formid = $("#cboDANHSACH").val();
            let _phieuchaid = $("#cboPhieuCha").val();
            let _mafromcha = (_row.MA_FORM).split("-")[0];
            let _maform = _checkExistMaForm(_configshowform.MAFORM, _mafromcha);
            if (_printHtml == 1) {
                if (_phieuchaid == '-1' && _mafromcha == _maform) {
                    DlgUtil.showMsg("Chưa chọn phiếu cha. Vui lòng thao tác lại.");
                } else if (_phieuids <= 0 && _mafromcha == _maform) {
                    DlgUtil.showMsg("Chưa chọn phiếu. Vui lòng thao tác lại.");
                } else {
                    let name = _formid + '@' + _phieuids + '@' + Number(_phieuchaid) + 1 + '@' + 1;
                    let popup = window.open('manager.jsp?func=../noitru/' + _linkHtml + '&showMode=dlg', name, 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' + screen.height + ',width=' + screen.width);
                    popup.moveTo(0, 0);
                }
            } else {
                if (rptcodemerge == "") return;
                if (_phieuids.length < 1) {
                    DlgUtil.showMsg("Chưa chọn phiếu. Vui lòng thao tác lại.");
                    return;
                }
                let paramRpt = [];
                paramRpt.push({name: "hosobenhanid", type: "String", value: objectVar.HOSOBENHANID});
                paramRpt.push({name: "khambenhid", type: "String", value: objectVar.KHAMBENHID});
                paramRpt.push({name: "formid", type: "String", value: _formid});
                paramRpt.push({name: "phieuid", type: "String", value: _phieuids.join(",")});
                openReport('window', rptcodemerge, 'pdf', paramRpt);
            }
        });

        $("#btnLuuMau").click(function () {
            let _tempNameArrays = $("#cboTEMPLATE option").toArray().filter(el => $(el).val() != -1).map(el => $(el).text().trim());
            let _tempName = $("#txtTEMP_NAME").val().trim();
            if (_tempName == "") {
                DlgUtil.showMsg("Tên mẫu không được để trống");
                $('#txtTEMP_NAME').focus();
                return false;
            }
            if (_tempNameArrays.indexOf(_tempName) >= 0) {
                DlgUtil.showMsg("Tên mẫu đã tồn tại");
                $('#txtTEMP_NAME').focus();
                return false;
            }
            let _validator = new DataValidator("content_CTPhieu");
            let valid = _validator.validateForm();
            if (!valid) {
                return false;
            }
            let arrPhieuCTIns = [];
            $("#content_CTPhieu > .ct-form-id:not(.ctc_element)").each(function (idx, el) {
                let type = $(el).data("type");
                let ctformid = $(el).data("ct-form-id");
                let split = $(el).data("split");
                let rptparam = $(el).data("rpt-param-name");
                let val = "";
                if (type == 1) {
                    if (split && split !== "") {
                        val = $(el).find("input").toArray().map(function (el2) {
                            return $(el2).val();
                        }).join(split);
                    } else {
                        val = $(el).find("input").val();
                    }
                }

                if (type == 2) {
                    val = $(el).find("input:checked").val();
                }

                if (type == 3) {
                    val = $(el).find('input:checked').toArray().map(function (el2) {
                        return $(el2).val()
                    }).join("|")
                }

                if (type == 4) {
                    val = $(el).find("input").val();
                }

                if (type == 5) {
                    val = $(el).find("select").val();
                    if (val == -1) val = "";
                }

                if (type == 7) {
                    let _val = $(el).find("select option:selected").val();
                    if (_val) {
                        val = $(el).find("select option:selected").val() + "|" + $(el).find("select option:selected").text();
                    }
                }

                if (type == 8) {
                    val = $(el).find("input").toArray().map(function (el2) {
                        return $(el2).val()
                    }).join("|");
                }

                if (type == 10) {
                    val = $(el).find("input#txtMSCFULL_" + ctformid).val();
                }

                if (type == 11) {
                    val = $(el).find("textarea#textarea_" + ctformid).val();
                }

                if (type == 14) {
                    val = $(el).find("input#date_" + ctformid).val();
                }

                if ((val && val !== "") || type == 8 || type == 1 || type == 10 || type == 11 || type == 14) {
                    arrPhieuCTIns.push({
                        PHIEUID: phieuid + "", FORM_ID: formid + "", CT_FORM_ID: ctformid + "", DULIEU: val + "", TYPE: type + "", SPLIT: split ? split + "" : "", RPT_PARAM_NAME: rptparam + ""
                    });
                }
            });

            let _tempObj = {
                TEMP_NAME: _tempName, FORM_ID: formid + ""
            };

            let _param = [JSON.stringify(arrPhieuCTIns), JSON.stringify(_tempObj)];
            let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.14", _param.join('$'));
            if (fl == 1) {
                DlgUtil.showMsg("Lưu mẫu thành công!");
                _loadCboMau();
                $('#txtTEMP_NAME').val("");
            } else {
                DlgUtil.showMsg("Lưu mẫu lỗi!");
            }
        });

        $("#btnXoaMau").click(function () {
            let _tempId = $("#cboTEMPLATE").val();
            let _tempName = $("#cboTEMPLATE option:selected").text();
            if (_tempId && _tempId != '-1') {
                DlgUtil.showConfirm("Bạn có muốn xóa mẫu " + _tempName + " ko?", function (flag) {
                    if (flag) {
                        let result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.15", _tempId);
                        if (result == 1) {
                            DlgUtil.showMsg("Xóa mẫu " + _tempName + " thành công", function () {
                            }, 600);
                            _loadCboMau();
                        } else DlgUtil.showMsg("Xóa mẫu " + _tempName + " không thành công");
                    }
                });
            } else {
                DlgUtil.showMsg("Chưa chọn mẫu để xóa");
                $('#cboTEMPLATE').focus();
                return false;
            }
        });

        $("#cboTEMPLATE").on("change", function (e) {
            let objParam = "";
            let _tmp_id = $("#cboTEMPLATE").val();
            let _check_ma_form = _configdatatempall.MAFORM.map((data, index) => {
                if (data.MA_FORM === maphieu.split('-')[0]) {
                    return data.MA_FORM;
                }
            }).join("");
            if (_check_ma_form === maphieu.split('-')[0]) {
                let _data_rpt_param = _configdatatempall.PARAMNAME.map((data, index) => {
                    return data.RPT_PARAM_NAME;
                }).join("");
                objParam = {
                    "CSYTID": _configdatatempall.CSYTID, "MAFORM": _check_ma_form, "PARAMNAME": _data_rpt_param, "GIATRI": _configdatatempall.GIATRI, "MAPHIEU": maphieu.split('-')[0], "KEYDATA": "1"
                }
            } else {
                objParam = {
                    "CSYTID": _configdatatemp.CSYTID,
                    "MAFORM": _configdatatemp.MAFORM,
                    "PARAMNAME": _configdatatemp.PARAMNAME,
                    "GIATRI": _configdatatemp.GIATRI,
                    "MAPHIEU": maphieu.split('-')[0],
                    "KEYDATA": "0"
                }
            }
            if (_tmp_id > 0) {
                let _ctphieuids = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.16", (_tmp_id + "@" + JSON.stringify(objParam)));
                if (_ctphieuids.length > 0) {
                    _ctphieuids.forEach(function (el) {
                        let type = el.TYPE;
                        let dulieu = el.DULIEU;
                        let split = el.SPLIT;
                        let ctFormId = el.CT_FORM_ID;
                        let paramChild = el.RPT_PARAM_NAME;
                        let ctFormElement = $('#content_CTPhieu > .ct-form-id:not(.ctc_element)[data-ct-form-id="' + ctFormId + '"]');
                        if (type == 1) {
                            if (split && split !== "") {
                                dulieu.split(split).forEach(function (el2, idx2) {
                                    ctFormElement.find("input#textfield_" + ctFormId + "_" + (idx2 + 1)).val(el2);
                                });
                            } else {
                                ctFormElement.find("input#textfield_" + ctFormId).val(dulieu);
                            }
                        }

                        if (type == 2) {
                            ctFormElement.find('input[value="' + dulieu + '"]').prop("checked", true);
                        }

                        if (type == 3) {
                            ctFormElement.find('input#checkbox_' + ctFormId).prop("checked", false);
                            dulieu.split("|").forEach(function (el2, idx2) {
                                ctFormElement.find('input#checkbox_' + ctFormId + '[value="' + el2 + '"]').prop("checked", true);
                            });
                        }

                        if (type == 4) {
                            ctFormElement.find("input#datepicker_" + ctFormId).val(dulieu);
                        }

                        if (type == 5) {
                            ctFormElement.find("select#combobox_" + ctFormId).val(dulieu);

                        }

                        if (type == 7) {
                            let _html = "";
                            let _kieudulieu = "";
                            let dlar = dulieu.split("|");
                            if (dlar.length > 1) {
                                let option = $('<option value="' + dlar[0] + '">' + dlar[1] + '</option>');
                                ctFormElement.find("select#cboSCOMBOBOX_" + ctFormId).empty();
                                ctFormElement.find("select#cboSCOMBOBOX_" + ctFormId).append(option);
                                let sql_par = {"FORM_ID": formid, "PARAM_CHILD": paramChild, "KEY_NAME": "1", "PHIEU_ID": phieuid};
                                let _listObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.29", JSON.stringify(sql_par));
                                if (_listObject.length > 0) {
                                    let _ct_form_id = "paramChild" + _listObject[0].CT_FORM_ID;
                                    let loaidl = _listObject[0].LOAI_DL;
                                    if (loaidl == 2) {
                                        _html = buildRadio(_listObject[0], _kieudulieu, dlar[0]);
                                    } else if (loaidl == 3) {
                                        _html = buildCheckbox(_listObject[0], _kieudulieu, dlar[0]);
                                    }
                                    _setHtmlElement(_html, _ct_form_id);
                                }
                            }
                        }

                        if (type == 8) {
                            $("#stextfield_" + ctFormId).empty();
                            let dlar = dulieu.split("|");
                            let rq = $("#btnTEXTFIELDADD_" + ctFormId).data("required");
                            let rqText = rq == '1' ? 'valrule="' + $("#btnTEXTFIELDADD_" + ctFormId).data("label") + ',required"' : "";
                            if (dlar.length > 1 || (dlar.length == 1 && dlar[0] != "")) {
                                dlar.forEach(function (dl, idx) {
                                    let stf = $('<div class="col-md-2 low-padding mgb3">\n' + '<input class="form-control input-sm kb-i-col-m" ' + rqText + ' value="' + dl + '" id="txtSTextField_' + ctFormId + idx + '" style="width: 100%; height: 23px">\t\n' + '</div>\n' + '<div class="col-md-1 low-padding">\n' + '<button style="background-color: #fff; height: 23px" type="button" class="btn btn-sm btn-secondary" onclick="ntu02d204_removeSTextField(this)" id="btnSTFCLEAR_' + ctFormId + '" modedisxt="">\n' + '<span class="glyphicon glyphicon-remove"></span>\n' + '</button>\t\n' + '</div>');
                                    $("#stextfield_" + ctFormId).append(stf);
                                });
                            }
                        }

                        if (type == 10) {
                            ctFormElement.find("input#txtMSCFULL_" + ctFormId).val(dulieu);
                        }

                        if (type == 11) {
                            ctFormElement.find("textarea#textarea_" + ctFormId).val(dulieu);
                        }

                        if (type == 14) {
                            ctFormElement.find("input#date_" + ctFormId).val(dulieu);
                        }
                    })
                }
            }
        });

        $("#btnInKySoAllCA").on("click", function (e) {
            _checkCaAll = 1;
            _catype = $(e.currentTarget).data("catype") + "";
            _caRptAll(_catype);
        });
    }

    function loadGridData(fl) {
        let param = Object.assign({}, objectVar);
        param.FORM_ID = $("#cboDANHSACH").val();
        param.PHIEUIDCHA = $("#cboPhieuCha").val();
        GridUtil.loadGridBySqlPage(_gridId, 'NTU02D204.01', [{"name": "[0]", "value": JSON.stringify(param)}], function () {
            if (fl != undefined && fl != null && fl > 0) {
                $('#grdDanhSach tr.jqgrow td[title="' + fl + '"]').parent().click();
                $("#tcctp").tab("show");
                _removeClass(['tabHanhChinh']);
            }
        });
        $("#btnInPhieuAll").hide();
        $("#btnKySoInAll").hide();
        $("#btnInKySoAll").hide();
        $("#btnHuyKySoAll").hide();
        $("#btnInKySoAllOne").hide();
        $("#btnXoa").attr("disabled", true);
        $("#btnCopy").attr("disabled", true);
    }

    function _insetThemPhieuTaoMoi(formid, objData) {
        let currentDate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
        objData.NGAYTHUCHIEN = currentDate;
        let param = [JSON.stringify(objData)];
        let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.02", param.join('$'));
        if (parseInt(fl) > 0) {
            setThongTinChung(fl);
            _setParamDefaultForm();
            DlgUtil.showMsg("Tạo phiếu thành công !");
            loadGridData(parseInt(fl));
            _removeClass(['tabHanhChinh']);
        } else {
            DlgUtil.showMsg("Tạo phiếu lỗi !");
        }
    }

    function buildTextField(object, _kieudulieu) {
        let _html = '';
        let dataValue = "";
        let dataType = "";
        let dataWidth = "";
        let dataDonVi = "";
        let inputType = "";
        let required = object.REQUIRED == 1 ? "required" : "";
        let rqText = object.REQUIRED == 1 ? 'valrule="' + object.TIEU_DE_DL + ',required' : "";
        if (object.VALRULE != '') {
            rqText != '' ? rqText = rqText + '|' + object.VALRULE + '"' : rqText = 'valrule="' + object.TIEU_DE_DL + ',' + object.VALRULE + '"';
        } else {
            rqText != '' ? rqText = rqText + '"' : "";
        }

        try {
            dataValue = object.DATA_DEFAULT == undefined ? undefined : _jsonString[object.DATA_DEFAULT];
            dataType = object.DATA_TYPE == undefined ? '1' : object.DATA_TYPE;
            dataWidth = (object.TEXT_WIDTH) == null || (object.TEXT_WIDTH) == "" ? '48.6' : object.TEXT_WIDTH;
            if (dataType == "1") {
                inputType = "text";
            } else if (dataType == "2") {
                inputType = "number";
            }
            dataDonVi = (object.DON_VI_DL).length > 0 ? '<label style="width: 10%">' + object.DON_VI_DL + '</label>\n' : '';
        } catch (e) {
            console.log("TextField: " + e);
        }

        let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
        if (object.SO_LUONG_DL == null || object.SO_LUONG_DL == "" || object.SO_LUONG_DL == 1) {
            _html = '<div id="BMTESLTS_' + object.CT_FORM_ID + '" class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id" data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-type="' + object.LOAI_DL + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '">\n' + '<div class="col-md-3 low-padding ' + required + '" id="required_' + object.CT_FORM_ID + '">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>\n' + '</div>\n' + '<div class="col-md-9 low-padding" style="display: flex">\n' + '<input class="form-control input-sm kb-i-col-m" ' + rqText + ' type="' + inputType + '" ' + 'rpt-param-name="' + object.RPT_PARAM_NAME + '"' + 'value="' + (dataValue == undefined ? "" : dataValue) + '" ' + 'id="textfield_' + idElement + '" style="width:' + dataWidth + "%;" + '">' + (dataDonVi === "" ? "" : "&nbsp;" + dataDonVi) + '</div>\n' + '</div>';
        }

        if (object.SO_LUONG_DL > 1) {
            _html = '<div id="BMTESLTS_' + object.CT_FORM_ID + '" class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id" data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-type="' + object.LOAI_DL + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-split="' + object.NGAN_CACH_DL + '">\n' + '<div class="col-md-3 low-padding ' + required + '" id="required_' + object.CT_FORM_ID + '">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>\n' + '</div>\n' + '' + '<div class="col-md-9 low-padding" style="display: flex;">';
            for (let i = 1; i <= object.SO_LUONG_DL; i++) {
                _html = _html + '<input ' + rqText + ' class="form-control input-sm kb-i-col-m" type="' + inputType + '" ' + 'rpt-param-name="' + object.RPT_PARAM_NAME + '"' + 'value="' + (dataValue == undefined ? "" : dataValue) + '" ' + 'id="textfield_' + idElement + '_' + i + '" style="width: ' + dataWidth + "%;" + '"><div>&nbsp;</div>' + (i == object.SO_LUONG_DL ? '' : object.NGAN_CACH_DL) + '<div>&nbsp;</div>';
            }
            _html = _html + '</div></div>';
        }
        return _html;
    }

    function buildRadio(object, _kieudulieu, _paramchild) {
        let dataValue = "";
        let dataWidth = "";
        let hidden = (object.HIDDEN_ELM === null || object.HIDDEN_ELM === "" || object.HIDDEN_ELM === "0") ? 0 : 1;
        let _html = '<div id="paramChild' + object.CT_FORM_ID + '" class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' ' + (hidden === 1 ? "hidden" : "") + ' col-md-12 ct-form-id" data-fctcid="' + (object.ID ? object.ID : "") + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-type="' + object.LOAI_DL + '">\n' + '<div class="col-md-3 low-padding">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>\n' + '</div>\n' + '<div class="col-md-9 low-padding"><div class="col-md-12 low-padding">\n';

        try {
            dataValue = (object.VALUE_DEFAULT == '1' ? object.DEFAULT_PARAM : object.DATA_DEFAULT == undefined ? undefined : _jsonString[object.DATA_DEFAULT]);
            dataWidth = (object.DATA_WIDTH) == null || (object.DATA_WIDTH) == "" ? "col-md-3" : "col-md-" + object.DATA_WIDTH;
        } catch (e) {
            console.log("Radio: " + e);
        }
        try {
            let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
            if (object.SQL_DL && object.SQL_DL != "") {
                let sql_par = [];
                sql_par.push({"name": "[0]", "value": objectVar.KHOAID}, {"name": "[1]", "value": objectVar.PHONGID}, {"name": "[2]", "value": objectVar.BENHNHANID}, {
                    "name": "[3]",
                    "value": objectVar.TIEPNHANID
                }, {"name": "[4]", "value": objectVar.HOSOBENHANID}, {"name": "[5]", "value": objectVar.KHAMBENHID}, {"name": "[6]", "value": _opts._dept_id}, {
                    "name": "[20]",
                    "value": _paramchild ? _paramchild : ""
                });
                let _opsArrsSql = jsonrpc.AjaxJson.dbExecuteQuery("", object.SQL_DL, sql_par);
                _opsArrsSql.forEach(function (_ops) {
                    let _text = _ops[1];
                    let _val = _ops[0];
                    _html = _html + '<div class="' + dataWidth + ' low-padding " >' + '<label><input ' + (_val == dataValue ? 'checked' : "") + ' ' + 'type="radio" ' + 'child-param="' + object.PARAM_CHILD + '"' + 'id="radio_' + idElement + '" ' + 'name="radio_' + idElement + '" ' + 'value="' + _val + '">&nbsp;' + _text + '</label></div>'
                });
            } else {
                let _opsArrs = [];
                try {
                    _opsArrs = JSON.parse(object.GIA_TRI_DL.replaceAll("&quot;", '"'));
                } catch (e) {
                    console.error(e);
                }
                _opsArrs.forEach(function (_ops) {
                    let _text = _ops.text;
                    let _val = _ops.value;
                    _html = _html + '<div class="' + dataWidth + ' low-padding ">' + '<label><input ' + (_val == dataValue ? 'checked' : "") + ' ' + 'type="radio" ' + 'child-param="' + object.PARAM_CHILD + '"' + 'id="radio_' + idElement + '" ' + 'name="radio_' + idElement + '" ' + 'value="' + _val + '">&nbsp;' + _text + '</label></div>'
                });
            }
        } catch (e) {
            console.error(e);
        }
        _html = _html + '</div></div></div>';
        return _html;
    }

    function buildCheckbox(object, _kieudulieu, _paramchild, _datachild) {
        let dataValue = "";
        let dataWidth = "";
        let hidden = (object.HIDDEN_ELM === null || object.HIDDEN_ELM === "" || object.HIDDEN_ELM === "0") ? 0 : 1;
        let _html = '<div id="paramChild' + object.CT_FORM_ID + '" class="disabled ' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id" data-fctcid="' + (object.ID ? object.ID : "") + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-type="' + object.LOAI_DL + '">\n' + '<div class="col-md-3 low-padding">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>\n' + '</div>\n' + '<div class="col-md-9 low-padding"><div class="col-md-12 low-padding">\n';
        try {
            dataValue = (object.VALUE_DEFAULT == '1' ? object.DEFAULT_PARAM : object.DATA_DEFAULT == undefined ? undefined : _jsonString[object.DATA_DEFAULT]);
            dataWidth = (object.DATA_WIDTH) == null || (object.DATA_WIDTH) == "" ? "col-md-3" : "col-md-" + object.DATA_WIDTH;
        } catch (e) {
            console.log("Checkbox: " + e);
        }
        try {
            let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
            if (object.SQL_DL && object.SQL_DL != "") {
                let sql_par = [];
                sql_par.push({"name": "[0]", "value": objectVar.KHOAID}, {"name": "[1]", "value": objectVar.PHONGID}, {"name": "[2]", "value": objectVar.BENHNHANID}, {
                    "name": "[3]",
                    "value": objectVar.TIEPNHANID
                }, {"name": "[4]", "value": objectVar.HOSOBENHANID}, {"name": "[5]", "value": objectVar.KHAMBENHID}, {"name": "[6]", "value": _opts._dept_id}, {
                    "name": "[20]",
                    "value": _paramchild ? _paramchild : ""
                });
                let _opsArrsSql = jsonrpc.AjaxJson.dbExecuteQuery("", object.SQL_DL, sql_par);
                _opsArrsSql.forEach(function (_ops) {
                    let _text = _ops[1];
                    let _val = _ops[0];
                    _html = _html + '<div class="' + dataWidth + ' low-padding ">\n' + '<label><input ' + (hidden === 1 ? "disabled" : "") + ' ' + (dataValue == undefined ? '' : (dataValue.indexOf(_val) >= 0 ? 'checked' : '')) + 'type="checkbox" ' + 'id="checkbox_' + idElement + '" ' + 'child-param="' + object.PARAM_CHILD + '"' + 'name="checkbox_' + idElement + '" ' + 'value="' + _val + '">\n' + '&nbsp;' + _text + '</label>\n' + '</div>';
                });
            } else {
                let _opsArrs = [];
                try {
                    let objJson = object.GIA_TRI_DL.replaceAll("&quot;", '"');
                    _opsArrs = JSON.parse(objJson);
                } catch (e) {
                    console.error(e);
                }
                _opsArrs.forEach(function (_ops) {
                    let _text = _ops.text;
                    let _val = _ops.value;
                    _html = _html + '<div class="' + dataWidth + ' low-padding ">\n' + '<label><input ' + (hidden === 1 ? "disabled" : "") + ' ' + (dataValue == undefined ? '' : (dataValue.indexOf(_val) >= 0 ? 'checked' : '')) + ' ' + 'type="checkbox" ' + 'id="checkbox_' + idElement + '" ' + 'child-param="' + object.PARAM_CHILD + '"' + 'name="checkbox_' + idElement + '" ' + 'value="' + _val + '">\n' + '&nbsp;' + _text + '</label>\n' + '</div>';
                });
            }
        } catch (e) {
            console.error(e);
        }
        _html = _html + '</div></div></div>';
        return _html;
    }

    function buildDatePicker(object, _kieudulieu) {
        let required = object.REQUIRED == 1 ? "required" : "";
        let rqText = object.REQUIRED == 1 ? 'valrule="' + object.TIEU_DE_DL + ',required"' : "";
        let hideClass = (object.GOM_NGAY && object.GOM_NGAY == 1) ? "hide" : "";
        let dataValue = "";
        let dataWidth = "";
        let _html = '<div class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id ' + hideClass + '" data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-child-param="' + object.PARAM_CHILD + '" data-type="' + object.LOAI_DL + '">\n' + '<div class="col-md-3 low-padding ' + required + '">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>\n' + '</div>\n' + '<div class="col-md-9 low-padding">\n';
        try {
            dataValue = object.DATA_DEFAULT == undefined ? undefined : _jsonString[object.DATA_DEFAULT];
            dataWidth = (object.TEXT_WIDTH) == null || (object.TEXT_WIDTH) == "" ? "31.6" : object.TEXT_WIDTH;
        } catch (e) {
            console.log("DatePicker: " + e);
        }
        let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
        if (object.GOM_NGAY && object.GOM_NGAY == 1) {
            _html = _html + `<div class="input-group" style="width: ` + dataWidth + "%;" + `"><input value="` + ngaythuchien + `" disabled class="date-gom-ngay form-control input-sm kb-i-col-m" id="datepicker_` + idElement + `"></div>`;
        } else {
            _html = _html + `<div class="input-group" style="width: ` + dataWidth + "%;" + `">
                    <input style="z-index: -1" class="form-control input-sm" id="datepicker_` + idElement + `"
                    data-mask="00/00/0000 00:00:00" ` + rqText + ` placeholder="dd/MM/yyyy hh:mm:ss" maxlength="19"
                    value="` + dataValue + `"
                    >
                    <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                    type="sCal" id='sp` + idElement + `'
                    onclick="NewCssCal('datepicker_` + idElement + `','ddMMyyyy','dropdown',true,'24',true)"></span>
                    </div>`;
        }
        _html = _html + '</div></div>';
        return _html;
    }

    function buildDate(object, _kieudulieu) {
        let required = object.REQUIRED == 1 ? "required" : "";
        let rqText = object.REQUIRED == 1 ? 'valrule="' + object.TIEU_DE_DL + ',required"' : "";
        let hideClass = (object.GOM_NGAY && object.GOM_NGAY == 1) ? "hide" : "";
        let dataValue = "";
        let dataWidth = "";
        let _html = '<div class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id ' + hideClass + '" data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-type="' + object.LOAI_DL + '">\n' + '<div class="col-md-3 low-padding ' + required + '">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>\n' + '</div>\n' + '<div class="col-md-9 low-padding">\n';
        try {
            dataValue = object.DATA_DEFAULT == undefined ? undefined : _jsonString[object.DATA_DEFAULT];
            dataWidth = (object.TEXT_WIDTH) == null || (object.TEXT_WIDTH) == "" ? "31.6" : object.TEXT_WIDTH;
        } catch (e) {
            console.log("Date: " + e);
        }
        let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
        if (idElement != null) {
            _html = _html + `<div class="input-group" style="width: ` + dataWidth + "%;" + `">
                    <input style="z-index: -1" class="form-control input-sm" id="date_` + idElement + `"
                    data-mask="00/00/0000" ` + rqText + ` placeholder="dd/MM/yyyy" maxlength="19"
                    value="` + dataValue + `"
                    >
                    <span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar"
                    type="sCal" id='sp` + idElement + `'
                    onclick="NewCssCal('date_` + idElement + `','ddMMyyyy','dropdown',false,'24',true)"></span>
                    </div>`;
        }
        _html = _html + '</div></div>';
        return _html;
    }

    function buildCombobox(object, _kieudulieu) {
        let required = object.REQUIRED == 1 ? "required" : "";
        let rqText = object.REQUIRED == 1 ? 'valrule="' + object.TIEU_DE_DL + ',required"' : "";
        let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
        let dataValue = "";
        let dataWidth = "";

        try {
            dataValue = object.DATA_DEFAULT == undefined ? undefined : _jsonString[object.DATA_DEFAULT];
            dataWidth = (object.DATA_WIDTH) == null || (object.DATA_WIDTH) == "" ? '48.6' : object.TEXT_WIDTH;
        } catch (e) {
            console.log("Combobox: " + e);
        }

        let _html = '<div class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id" ' + 'data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" ' + 'data-rpt-param-name="' + object.RPT_PARAM_NAME + '" ' + 'data-type="' + object.LOAI_DL + '">\n' + '<div class="col-md-3 low-padding ' + required + '" id="required_' + object.CT_FORM_ID + '">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>\n' + '</div>\n' + '<div class="col-md-9 low-padding">' + '<div style="width: ' + dataWidth + "%;" + '">\n' + '' + '<select ' + rqText + ' class="form-control input-sm" id="combobox_' + idElement + '" child-param-name= "' + object.PARAM_CHILD + '" father-param-name="' + object.RPT_PARAM_NAME + '" inpDisable="">';
        _html = _html + '<option value=""> -- Chọn -- </option>';

        try {
            if (object.SQL_DL && object.SQL_DL != "") {
                let sql_par = [];
                sql_par.push({"name": "[0]", "value": objectVar.KHOAID}, {"name": "[1]", "value": objectVar.PHONGID}, {"name": "[2]", "value": objectVar.BENHNHANID}, {
                    "name": "[3]",
                    "value": objectVar.TIEPNHANID
                }, {"name": "[4]", "value": objectVar.HOSOBENHANID}, {"name": "[5]", "value": objectVar.KHAMBENHID});
                let _opsArrsSql = jsonrpc.AjaxJson.dbExecuteQuery("", object.SQL_DL, sql_par);
                _opsArrsSql.forEach(function (_ops) {
                    let _text = _ops[1];
                    let _val = _ops[0];
                    _html = _html + '<option ' + (_val == dataValue ? 'selected' : null) + ' value="' + _val + '">' + _text + '</option>';
                });
            } else {
                let _opsArrs = [];
                try {
                    _opsArrs = JSON.parse(object.GIA_TRI_DL.replaceAll("&quot;", '"'));
                } catch (e) {
                    console.error(e);
                }
                _opsArrs.forEach(function (_ops) {
                    let _text = _ops.text;
                    let _val = _ops.value;
                    _html = _html + '<option ' + (_val == dataValue ? 'selected' : null) + ' value="' + _val + '">' + _text + '</option>';
                });
            }
        } catch (e) {
            console.log(e);
        }
        _html = _html + '</select></div></div></div>';
        return _html;
    }

    function buildTabTitle(object, _kieudulieu) {
        let _html = '<ul class="nav nav-tabs" style="margin-bottom: 5px;">\n' + '<li class="active" style="width: 100%;">\n' + '<b href="#" style="background-color: white;color: black;">' + object.TIEU_DE_DL + '</b>\n' + '</li>\n' + '</ul>';
        return _html;
    }

    function buildSCombobox(object, _kieudulieu) {
        let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
        let dataValue = "-1";
        let dataText = "--Lựa chọn--";
        let required = object.REQUIRED == 1 ? "required" : "";
        let rqText = object.REQUIRED == 1 ? 'valrule="' + object.TIEU_DE_DL + ',required"' : "";
        try {
            if (object.DATA_DEFAULT != undefined) {
                dataValue = _jsonString[((object.DATA_DEFAULT).split("|"))[0]];
                dataText = _jsonString[((object.DATA_DEFAULT).split("|"))[1]];
            }
        } catch (e) {
            console.log("SCombobox: " + e);
        }

        let _html = '<div class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id" data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-type="' + object.LOAI_DL + '">\n' + '<div class="col-md-3 low-padding ' + required + '">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>\n' + '</div>\n' + '<div class="col-md-9 low-padding">\n' + '<div class="col-xs-7 low-padding"> \n' + '<div class="col-xs-2 low-padding"> \n' + '<input placeholder="" class="form-control input-sm i-col-25" data-sql-dl="' + object.SQL_DL + '" data-scboid="cboSCOMBOBOX_' + idElement + '" data-child-param="' + object.RPT_PARAM_NAME + '" id="txtSCOMBOBOX_' + idElement + '" \n' + 'attrIcd="0" modeDisXT=""> \n' + '</div> \n' + '<div class="col-xs-7 low-padding"> \n' + '<select class="form-control input-sm i-col-60" ' + rqText + ' style="width:100%" \n' + 'id="cboSCOMBOBOX_' + idElement + '" modeDisXT="" attrCl="clear"> \n' + '<option value=' + (dataValue == undefined ? (object.REQUIRED == 1 ? "" : "-1") : dataValue) + '>' + (dataText == undefined ? "--Lựa chọn--" : dataText) + '</option> \n' + '</select> \n' + '</div> \n' + '<div class="col-xs-1 low-padding" style="text-align: center"> \n' + '<button type="button" class="form-control input-sm" data-child-param="' + object.RPT_PARAM_NAME + '" data-required="' + object.REQUIRED + '" id="btnCLEAR_' + idElement + '" modeDisXT=""> \n' + '<span class="glyphicon glyphicon-remove"></span> \n' + '</button> \n' + '</div> \n' + '</div> ';
        _html = _html + '</div></div>';
        return _html;
    }

    function buildTextFieldDynamic(object, _kieudulieu) {
        let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
        let dataType = object.DATA_TYPE == undefined ? '1' : object.DATA_TYPE;
        let required = object.REQUIRED == 1 ? "required" : "";
        let _html = '<div class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id" data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-type="' + object.LOAI_DL + '">\n' + '<div class="col-md-3 low-padding ' + required + '">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>\n' + '</div>\n' + '<div class="col-md-9 low-padding"><div class="col-md-12 low-padding">\n';
        _html = _html + '<div class="col-md-11 low-padding" id="stextfield_' + idElement + '"></div>' + '<div class="col-md-1 low-padding">' + '<button style="width: 62px" type="button" data-label="' + object.TIEU_DE_DL + '" data-required="' + object.REQUIRED + '" data-type="' + (dataType) + '" class="form-control input-sm" id="btnTEXTFIELDADD_' + idElement + '" modedisxt="">\n' + '<span class="glyphicon glyphicon-plus"></span>\n' + '</button></div>';
        _html = _html + '</div></div></div>';
        return _html;
    }

    function buildLabelHeader(object, _kieudulieu) {
        let dataWidth = "";
        let textWidth = "";
        try {
            dataWidth = (object.DATA_WIDTH) == null || (object.DATA_WIDTH) == "" ? "col-md-3" : "col-md";
            textWidth = (object.TEXT_WIDTH) == null || (object.TEXT_WIDTH) == "" ? "48.6" : object.TEXT_WIDTH + "%";
        } catch (e) {
            console.log("LabelHeader: " + e);
        }
        let _html = '<div class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id">\n' + '<div class="' + dataWidth + ' low-padding">\n' + '<div class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</div>\n' + '</div>\n' + '<div class="' + (dataWidth == undefined ? "col-md-9" : "col-md-0") + ' low-padding" style="display: flex;">';
        let gtdl = object.GIA_TRI_DL.split("|");
        for (let i = 0; i < object.SO_LUONG_DL; i++) {
            let _label = gtdl.length > i ? gtdl[i] : "";
            _html = _html + '<div class="text-center form-control input-sm kb-i-col-m mgb3" style="width: ' + (textWidth) + ';background-color: #e7e6e6;">' + _label + '</div><div>&nbsp;</div>' + ((i + 1) == object.SO_LUONG_DL ? '' : '|') + '<div>&nbsp;</div>';
        }
        _html = _html + '</div></div>';
        return _html;
    }

    function buildMSCombobox(object, _kieudulieu) {
        let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
        let _html =
            '<div class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id" data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-type="' + object.LOAI_DL + '">' +
            '<div class="col-md-3 low-padding">\n' + '<label class="mgl5 mgr5" id="lblMSC_' + idElement + '">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>' + '</div>' +
            '<div class="col-md-9 low-padding">' + '<div class="col-xs-2 low-padding">' + '<input class="form-control input-sm" id="txtMSC_' + idElement + '" data-sql-dl="' + object.SQL_DL + '" style="width: 100%;" maxlength="100">' + '</div>' +
            '<div class="' + (_mutilCombobox == '1' ? "col-xs-5" : "col-xs-8") + ' low-padding">' + '<input class="form-control input-sm" id="txtMSCFULL_' + idElement + '" style="width: 100%;" maxlength="1500" disabled="disabled">' + '</div>' +
            (_mutilCombobox == 1 ? '<div class="col-xs-4 low-padding"><input class="form-control input-sm" id="txtMSCGC_' + idElement + '"></div>' : "") +
            '<div class="col-xs-1 low-padding" style="display: inline-flex">' +
            '<button type="button" class="form-control input-sm" id="btnMSCEDIT_' + idElement + '" modeDisXT="" data-ctl-sql="' + object.SQL_DL + '"><span class="glyphicon glyphicon-pencil"></span></button>' +
            '<button type="button" class="form-control input-sm" id="btnMSCCLEAR_' + idElement + '" modeDisXT=""><span class="glyphicon glyphicon-remove"></span></button>' +
            '</div>' + '</div>' + '</div>';
        return _html;
    }

    function buildTextArea(object, _kieudulieu) {
        let idElement = object.G_HIDDEN ? object.CT_FORM_ID + "_" + object.ID : object.CT_FORM_ID;
        let required = object.REQUIRED == 1 ? "required" : "";
        let rqText = object.REQUIRED == 1 ? 'valrule="' + object.TIEU_DE_DL + ',required"' : "";
        let dataValue = "";
        try {
            dataValue = object.DATA_DEFAULT == undefined ? undefined : _jsonString[object.DATA_DEFAULT];
        } catch (e) {
            console.log(e);
        }

        let _html = '<div class="' + (_kieudulieu == 1 ? "pdl0 pdr0" : "") + ' col-md-12 ct-form-id" data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-type="' + object.LOAI_DL + '">' + '<div class="col-md-3 low-padding ' + required + '">\n' + '<label class="mgl5 mgr5">' + (object.TIEU_DE_DL == '-' ? "" : object.TIEU_DE_DL + ":") + '</label>' + '</div>' + '<div class="col-md-9 low-padding mgb3">' + '<textarea class="form-control i-col-full" ' + rqText + ' id="textarea_' + idElement + '" style="padding: 6px">' + (dataValue == undefined ? "" : dataValue) + '</textarea>' + '</div>' + '</div>';
        return _html;
    }

    function buildSubDataArea(object, _phieuid) {
        let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.19", object.CT_FORM_ID);
        let listGridHeader = [];
        listGridHeader.push(["0", `CHITIETPHIEUID,CHITIETPHIEUID,5,0,t,l,1,2`]);
        listGridHeader.push(["0", `RID,RID,5,0,t,l,1,2`]);
        listGridHeader.push(["0", ` ,ICON,20,0,ns,l,ES`]);
        listGridHeader.push(["0", `FLAG_CA,FLAG_CA,5,0,t,l,1,2`]);
        listGridHeader.push(["0", `HDATA,HDATA,5,0,t,l,1,2`]);
        let htmlContent = ""
        data_ar.forEach(function (el, index) {
            if (index == 0) {
                htmlContent = htmlContent + `<input style="display: none;" id="txtRID_${el.CT_FORM_ID}"
                                       name="txtRID_${el.CT_FORM_ID}" title="">`
            }
            if (el.G_HIDDEN == 't') {
                listGridHeader.push([el.G_THUTU, `${el.G_LABEL},${el.G_NAME},${el.G_WIDTH},${el.G_FORMAT},${el.G_HIDDEN},${el.G_ALIGN},1,2`]);
            } else {
                if (["2", "3", "5", "7", "8"].includes(el.LOAI_DL + "")) {
                    listGridHeader.push([el.G_THUTU, `${el.G_LABEL},${el.G_NAME + "_TS"},1,${el.G_FORMAT},t,l,1,2`]);
                    listGridHeader.push([el.G_THUTU, `${el.G_LABEL},${el.G_NAME + "_VIEW"},${el.G_WIDTH},${el.G_FORMAT},f,${el.G_ALIGN},1,2`]);
                    listGridHeader.push([el.G_THUTU, `${el.G_LABEL},${el.G_NAME},1,${el.G_FORMAT},t,l,1,2`]);
                } else {
                    listGridHeader.push([el.G_THUTU, `${el.G_LABEL},${el.G_NAME},${el.G_WIDTH},${el.G_FORMAT},f,${el.G_ALIGN},1,2`]);
                }
            }

            if (el.LOAI_DL == 1) {
                htmlContent = htmlContent + buildTextField(el, 1);
            }

            if (el.LOAI_DL == 2) {
                htmlContent = htmlContent + buildRadio(el, 1);
            }

            if (el.LOAI_DL == 3) {
                htmlContent = htmlContent + buildCheckbox(el, 1);
            }

            if (el.LOAI_DL == 4) {
                htmlContent = htmlContent + buildDatePicker(el, 1);
            }

            if (el.LOAI_DL == 5) {
                htmlContent = htmlContent + buildCombobox(el, 1);
            }

            if (el.LOAI_DL == 6) {
                htmlContent = htmlContent + buildTabTitle(el, 1);
            }

            if (el.LOAI_DL == 7) {
                htmlContent = htmlContent + buildSCombobox(el, 1);
            }

            if (el.LOAI_DL == 8) {
                htmlContent = htmlContent + buildTextFieldDynamic(el, 1);
            }

            if (el.LOAI_DL == 9) {
                htmlContent = htmlContent + buildLabelHeader(el, 1);
            }

            if (el.LOAI_DL == 10) {
                htmlContent = htmlContent + buildMSCombobox(el, 1);
            }

            if (el.LOAI_DL == 11) {
                htmlContent = htmlContent + buildTextArea(el, 1);
            }
        });

        htmlContent = htmlContent.replace(/<input /g, '<input disabled="disabled" ');
        htmlContent = htmlContent.replace(/<select /g, '<select disabled="disabled" ');
        htmlContent = htmlContent.replace(/<textarea /g, '<textarea disabled="disabled" ');
        htmlContent = htmlContent.replace(/<button /g, '<button disabled="disabled" ');

        let _gridHeader = listGridHeader
            .sort(function (a, b) {
                return a[0] - b[0]
            })
            .map(gs => gs[1])
            .join(";");


        let dataTrans = {
            FORM_ID: object.FORM_ID, CT_FORM_ID: object.CT_FORM_ID, PHIEUID: _phieuid
        }
        let configFlagca = ((_configctcflagca.GIATRI === "1" && maphieu.split("-")[0] === _configctcflagca.MAFORM) ? "" : "hidden");
        let configCopy = (_configctccopy === "1" ? "" : "hidden");
        htmlContent = htmlContent + `<div class="col-md-12 low-padding mgt10 ctc-ctl" style="text-align: center;" data-ttp='${JSON.stringify(dataTrans)}'>
                <button type="button" class="btn btn-sm ctc-add-btn" style="color: white;background-color: #1164b4;border-radius: 2px" id="btnThem_${object.CT_FORM_ID}">
                    <span class="glyphicon glyphicon-plus"></span> Thêm
                </button>
                <button type="button" class="btn btn-sm ctc-edit-btn" style="color: white;background-color: #1164b4;border-radius: 2px" disabled id="btnSua_${object.CT_FORM_ID}">
                    <span class="glyphicon glyphicon-edit"></span> Sửa
                </button>
                <button type="button" class="btn btn-sm ctc-copy-btn ` + (configCopy) + `" style="color: white;background-color: #1164b4;border-radius: 2px" disabled id="btnCopy_${object.CT_FORM_ID}">
                    <span class="glyphicon glyphicon-film"></span> Sao chép
                </button>
                <button type="button" class="btn btn-sm ctc-save-btn" style="color: white;background-color: #1164b4;border-radius: 2px" disabled id="btnLuu_${object.CT_FORM_ID}">
                    <span class="glyphicon glyphicon-save"></span> Lưu
                </button>
                <button type="button" class="btn btn-sm ctc-kyso-btn ` + (configFlagca) + `" style="color: white;background-color: #1164b4;border-radius: 2px" disabled id="btnLuuKy_${object.CT_FORM_ID}">
                    <span class="glyphicon glyphicon-pencil"></span> Lưu & Ký
                </button>
                <button type="button" class="btn btn-sm ctc-huykyso-btn ` + (configFlagca) + `" style="color: white;background-color: #1164b4;border-radius: 2px" disabled id="btnHuyKy_${object.CT_FORM_ID}">
                    <span class="glyphicon glyphicon-remove"></span> Hủy ký
                </button>
                <button type="button" class="btn btn-sm ctc-remove-btn" style="color: white;background-color: #1164b4;border-radius: 2px" disabled id="btnXoa_${object.CT_FORM_ID}">
                    <span class="glyphicon glyphicon-trash"></span> Xóa
                </button>
                <button type="button" class="btn btn-sm ctc-cancel-btn" style="color: white;background-color: #1164b4;border-radius: 2px" disabled id="btnHuy_${object.CT_FORM_ID}">
                    <span class="glyphicon glyphicon-eye-close"></span> Hủy
                </button>
            </div>
			<div class="col-md-12 low-padding mgt10">
                <table class="ctc-element-grid" data-hmultiple-select="${object.MULTIPLE_SELECT}" data-ct-form-id="${object.CT_FORM_ID}" data-hgrid-header="${_gridHeader}" id="grdDanhSach_${object.CT_FORM_ID}"></table>
                <div id="pager_grdDanhSach_${object.CT_FORM_ID}"></div>
            </div>
            `

        let _html = `<div class="col-md-12 low-padding ct-form-id ctc_element" data-ct-form-id="${object.CT_FORM_ID}" data-rpt-param-name="${object.RPT_PARAM_NAME}" data-type="${object.LOAI_DL}">
                <div class="loai_dl_12 col-md-12 mgb10" id="SubData_${object.CT_FORM_ID}">
                    <div class="low-padding">${buildTabTitle(object)}</div>
                    <div id="CTC_container_${object.CT_FORM_ID}">${htmlContent}</div>
                    <div class="ctc-over-el" style="display:none;" id="CTC_over_${object.CT_FORM_ID}"></div>
                </div>
            </div>`;
        return _html;
    }

    function buildHTMLTemplate(object) {
        let _htmlContent = object.HTML_TEMPLATE;
        try {
            _htmlContent.match(/{{chk(\w+)}}/g).forEach(el => {
                let key = el.substring(5, el.length - 2)
                _htmlContent = _htmlContent.replace(el, `<input type="checkbox" data-key="${key}" id="chk${key}" name="chk${key}" label/>`)
            })
        } catch (e) {
        }
        try {
            _htmlContent.match(/{{txt(\w+)}}/g).forEach(el => {
                let key = el.substring(5, el.length - 2)
                _htmlContent = _htmlContent.replace(el, `<input class="mgt2 mgb2" style="border: 1px solid black; width: 100px;height: 20px" type="text" data-key="${key}" id="txt${key}" name="txt${key}" label/>`)
            })
        } catch (e) {
        }
        try {
            _htmlContent.match(/{{rad(\w+)\.(\w+)}}/g).forEach((el, index) => {
                let key = el.substring(5, el.length - 2);
                let radName = key.split(".")[1];
                key = key.split(".")[0];
                _htmlContent = _htmlContent.replace(el, `<input class="mgt2 mgb2" style="border: 1px solid black; width: 100px;height: 20px" data-key="${key}" data-name="${radName}" id="rad-${radName}-${key}" type="radio" name="rad${radName}" value="${index}">`)
            })
        } catch (e) {
        }

        let _html = '<div class="col-md-12 ct-form-id mgt5 mgb5" data-fctcid="' + object.ID + '" data-ct-form-id="' + object.CT_FORM_ID + '" data-rpt-param-name="' + object.RPT_PARAM_NAME + '" data-type="' + object.LOAI_DL + '">' + '<div class="col-md-12 html-content" style="padding: 20px; border: 1px solid #bdbdbd; border-radius: 5px;\n">' + _htmlContent + '</div>' + '</div>';
        return _html;
    }

    function _loadThongTinBenhNhan() {
        if (keyValue != 1) {
            _removeClass(['divBenhNhanInfo']);
            let objData = Object.assign({}, objectVar);
            let tthc = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.07", JSON.stringify(objData));
            if (tthc != null && tthc.length > 0) {
                let row = tthc[0];
                FormUtil.clearForm("divBenhNhanInfo", "");
                FormUtil.setObjectToForm("divBenhNhanInfo", "", row);
            }
        }
    }

    function _loadCboMau() {
        ComboUtil.getComboTag("cboTEMPLATE", "NTU02D204.13", formid, "", {value: '-1', text: '-- Chọn mẫu --'}, 'sp');
    }

    function loadDataGridChilds() {
        $(".ctc-element-grid").toArray().forEach(grid => {
            let idG = grid.id;
            let _gridHeaderEl = grid.getAttribute("data-hgrid-header");
            let _ctFormId = grid.getAttribute("data-ct-form-id");
            if (_gridHeaderEl && idG) {
                let multipleSelect = grid.getAttribute("data-hmultiple-select");
                let phieuchitietid = grid.getAttribute("data-phieuchitietid");
                let searchObject = {
                    PHIEUCTID: phieuchitietid.toString(), CT_FORM_ID: _ctFormId.toString(), PHIEUID: phieuid.toString()
                }
                GridUtil.init(idG, "100%", "300", "Danh sách chi tiết phiếu", (multipleSelect == 1), _gridHeaderEl, false);
                loadGridDataElement(searchObject);
            }

            GridUtil.setGridParam(idG, {
                onSelectRow: function (id) {
                    GridUtil.unmarkAll(idG);
                    GridUtil.markRow(idG, id);
                    if (id && keyCustomerObjectCacher[idG]) {
                        let _row = $("#" + idG).jqGrid('getRowData', id);
                        let rId = _row.RID;
                        if (rId) {
                            let _ctphieuids = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.22", rId);
                            if (_ctphieuids && _ctphieuids.length > 0) {
                                _ctphieuids.forEach(function (el, index) {
                                    let type = el.TYPE;
                                    let dulieu = el.DULIEU;
                                    let split = el.SPLIT;
                                    let ctFormId = el.CT_FORM_ID;
                                    let fctcid = el.FCTC_ID;
                                    if (index == 0) {
                                        resetForm("#CTC_container_" + el.CT_FORM_ID);
                                        $(`#txtRID_${el.CT_FORM_ID}`).val(rId);
                                        $("#CTC_container_" + el.CT_FORM_ID + " > .ct-form-id").find("input, select, textarea").attr("disabled", "disabled");
                                    }
                                    let ctFormElement = $(`#CTC_container_${ctFormId} > .ct-form-id[data-fctcid="${fctcid}"]`);
                                    let postIdElement = ctFormId + "_" + fctcid;
                                    if (type == 1) {
                                        if (split && split !== "") {
                                            dulieu.split(split).forEach(function (el2, idx2) {
                                                ctFormElement.find("input#textfield_" + postIdElement + "_" + (idx2 + 1)).val(el2);
                                            });
                                        } else {
                                            ctFormElement.find("input#textfield_" + postIdElement).val(dulieu);
                                        }
                                    }

                                    if (type == 2) {
                                        ctFormElement.find('input[value="' + dulieu + '"]').prop("checked", true);
                                    }

                                    if (type == 3) {
                                        ctFormElement.find(`input#checkbox_${postIdElement}`).prop("checked", false);
                                        dulieu.split("|").forEach(function (el2, idx2) {
                                            ctFormElement.find(`input#checkbox_${postIdElement}[value="${el2}"]`).prop("checked", true);
                                        });
                                    }

                                    if (type == 4) {
                                        ctFormElement.find(`input#datepicker_${postIdElement}`).val(dulieu);
                                    }

                                    if (type == 5) {
                                        ctFormElement.find(`select#combobox_${postIdElement}`).val(dulieu);
                                    }

                                    if (type == 7) {
                                        let dlar = dulieu.split("|");
                                        if (dlar.length > 1) {
                                            let option = $('<option value="' + dlar[0] + '">' + dlar[1] + '</option>');
                                            ctFormElement.find(`select#cboSCOMBOBOX_${postIdElement}`).empty();
                                            ctFormElement.find(`select#cboSCOMBOBOX_${postIdElement}`).append(option);
                                        } else {
                                            ctFormElement.find(`select#cboSCOMBOBOX_${postIdElement}`).empty();
                                        }
                                    }

                                    if (type == 8) {
                                        $(`#stextfield_${postIdElement}`).empty();
                                        let dlar = dulieu.split("|");
                                        let rq = $(`#btnTEXTFIELDADD_${postIdElement}`).data("required");
                                        let rqText = rq == '1' ? 'valrule="' + $(`#btnTEXTFIELDADD_${postIdElement}`).data("label") + ',required"' : "";
                                        if (dlar.length > 1 || (dlar.length == 1 && dlar[0] != "")) {
                                            dlar.forEach(function (dl, idx) {
                                                let stf = $('<div class="col-md-2 low-padding mgb3">\n' + '<input ' + rqText + ' value="' + dl + '" class="form-control input-sm kb-i-col-m" disabled="disabled" id="txtSTextField_' + postIdElement + idx + '" style="width: 100%; height: 23px">\t\n' + '</div>\n' + '<div class="col-md-1 low-padding">\n' + '<button style="height: 23px" type="button" class="btn btn-sm btn-secondary" onclick="ntu02d204_removeSTextField(this)" id="btnSTFCLEAR_' + postIdElement + '" modedisxt="">\n' + '<span class="glyphicon glyphicon-remove"></span>\n' + '</button>\t\n' + '</div>');
                                                $("#stextfield_" + postIdElement).append(stf);
                                            });
                                        }
                                    }

                                    if (type == 10) {
                                        if (_mutilCombobox == "1") {
                                            ctFormElement.find("input#txtMSCFULL_" + postIdElement).val(dulieu.split("&&")[0]);
                                            ctFormElement.find("input#txtMSCGC_" + postIdElement).val(dulieu.split("&&")[1]);
                                        } else {
                                            ctFormElement.find("input#txtMSCFULL_" + postIdElement).val(dulieu);
                                        }
                                    }

                                    if (type == 11) {
                                        ctFormElement.find("textarea#textarea_" + postIdElement).val(dulieu);
                                    }
                                })
                                let ctcCtl = $(`#CTC_container_${$("#" + idG).data("ct-form-id")} .ctc-ctl`);
                                ctcCtl.find("button.ctc-add-btn").prop("disabled", true);
                                ctcCtl.find("button.ctc-edit-btn").prop("disabled", false);
                                ctcCtl.find("button.ctc-save-btn").prop("disabled", true);
                                ctcCtl.find("button.ctc-kyso-btn").prop("disabled", true);
                                ctcCtl.find("button.ctc-huykyso-btn").prop("disabled", true);
                                ctcCtl.find("button.ctc-copy-btn").prop("disabled", false);
                                ctcCtl.find("button.ctc-cancel-btn").prop("disabled", false);
                                ctcCtl.find("button.ctc-remove-btn").prop("disabled", false);
                                $(`#CTC_container_${$("#" + idG).data("ct-form-id")} > .ct-form-id`).find("button").prop("disabled", true);
                            }

                        }
                    }
                }, gridComplete: function () {
                    let _rowids = $("#" + idG).getDataIDs();
                    if (_rowids && _rowids.length > 0) {
                        let firstRow = $("#" + idG).jqGrid('getRowData', "1");
                        if (keyCustomerObjectCacher[idG] && keyCustomerObjectCacher[idG].length > 0) {

                        } else {
                            keyCustomerObjectCacher[idG] = Object.keys(firstRow)
                                .filter(el => {
                                    return el.endsWith("_TS");
                                }).filter(el => {
                                    try {
                                        let type = firstRow[el].split("I_T_I")[0];
                                        let split = firstRow[el].split("I_T_I")[0];
                                        return (["2", "3", "5", "7", "8"].includes(type + "") || (split && type == "1"))
                                    } catch (e) {
                                        return false
                                    }
                                }).map(el => {
                                    let split = "";
                                    let fctcId = "";
                                    let type = firstRow[el].split("I_T_I")[0];
                                    try {
                                        split = firstRow[el].split("I_T_I")[1];
                                    } catch (e) {
                                    }
                                    try {
                                        fctcId = firstRow[el].split("I_T_I")[2];
                                    } catch (e) {
                                    }

                                    let radioValues = {};
                                    if (type == 2) {
                                        $("#grdDanhSach_" + _ctFormId)
                                            .parents("#CTC_container_" + _ctFormId)
                                            .find("input[name='radio_" + _ctFormId + "_" + fctcId + "']")
                                            .toArray()
                                            .forEach(el2 => {
                                                radioValues[el2.value] = $(el2).parent().text().trim()
                                            });
                                    } else if (type == 3) {
                                        $("#grdDanhSach_" + _ctFormId)
                                            .parents("#CTC_container_" + _ctFormId)
                                            .find("[data-fctcid='" + fctcId + "'] input[type='checkbox']")
                                            .toArray()
                                            .forEach(el2 => {
                                                radioValues[el2.value] = $(el2).parent().text().trim()
                                            });
                                    } else if (type == 5) {
                                        $("#grdDanhSach_" + _ctFormId)
                                            .parents("#CTC_container_" + _ctFormId)
                                            .find("[data-fctcid='" + fctcId + "'] #combobox_" + _ctFormId + "_" + fctcId + " option")
                                            .toArray()
                                            .filter(el2 => (el2.value && el2.value != '' && el2.value != '-1'))
                                            .forEach(el2 => {
                                                radioValues[el2.value] = el2.text
                                            });
                                    }
                                    return {
                                        key: el.replace(/_TS$/, ""), type: type, split: split, fctcId: fctcId, radioValues: radioValues
                                    }
                                });
                        }
                        _rowids.forEach(function (rowid) {
                            let row = $("#" + idG).jqGrid('getRowData', rowid);
                            if (row.FLAG_CA && row.FLAG_CA == '1') {
                                let _icon = '<div style="text-align: center"><img src="../common/image/ca.png" width="15px"></div>';
                                $("#grdDanhSach_" + _ctFormId).jqGrid('setCell', rowid, 'ICON', _icon);
                            }
                            keyCustomerObjectCacher[idG].forEach(el => {
                                if (el.type == "2") {
                                    try {
                                        let vlMaper = el.radioValues[row[el.key]];
                                        $("#" + idG).jqGrid('setCell', rowid, el.key + "_VIEW", vlMaper);
                                        $("#" + idG).jqGrid('setCell', rowid, "HDATA", JSON.stringify(el));

                                    } catch (e) {
                                    }
                                } else if (el.type == "3") {
                                    try {
                                        let rawVal = row[el.key].split("|");
                                        if (rawVal.length > 0) {
                                            let vlMaper = rawVal.map(el2 => el.radioValues[el2]).join(";");
                                            $("#" + idG).jqGrid('setCell', rowid, el.key + "_VIEW", vlMaper);
                                            $("#" + idG).jqGrid('setCell', rowid, "HDATA", JSON.stringify(el));
                                        }
                                    } catch (e) {
                                    }
                                } else if (el.type == "5") {
                                    try {
                                        let vlMaper = el.radioValues[row[el.key]];
                                        $("#" + idG).jqGrid('setCell', rowid, el.key + "_VIEW", vlMaper);
                                        $("#" + idG).jqGrid('setCell', rowid, "HDATA", JSON.stringify(el));
                                    } catch (e) {
                                    }
                                } else if (el.type == "7") {
                                    try {
                                        if (row[el.key].split("|")[0] && row[el.key].split("|")[0] != -1) {
                                            let vlMaper = row[el.key].split("|")[1];
                                            $("#" + idG).jqGrid('setCell', rowid, el.key + "_VIEW", vlMaper);
                                        } else {
                                            $("#" + idG).jqGrid('setCell', rowid, el.key + "_VIEW", "");
                                        }
                                        $("#" + idG).jqGrid('setCell', rowid, "HDATA", JSON.stringify(el));
                                    } catch (e) {
                                    }
                                } else if (el.type == "8") {
                                    try {
                                        let vlMaper = row[el.key].replace(/\|/g, ";");
                                        $("#" + idG).jqGrid('setCell', rowid, el.key + "_VIEW", vlMaper);
                                        $("#" + idG).jqGrid('setCell', rowid, "HDATA", JSON.stringify(el));
                                    } catch (e) {
                                    }
                                }
                            })
                        })
                    }
                }
            })
        });
    }

    function _bindEventC() {
        $(".ctc-add-btn").click(function (e) {
            _isType = 1;
            let parent = $(e.currentTarget).parent(".ctc-ctl");
            let objVal = parent.data("ttp");
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("input, select, textarea").removeAttr("disabled");
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("button").prop("disabled", false);
            parent.find("button.ctc-save-btn").prop("disabled", false);
            parent.find("button.ctc-kyso-btn").prop("disabled", false);
            parent.find("button.ctc-huykyso-btn").prop("disabled", false);
            parent.find("button.ctc-copy-btn").prop("disabled", true);
            parent.find("button.ctc-cancel-btn").prop("disabled", false);
            parent.find("button.ctc-remove-btn").prop("disabled", true);
            parent.find("button.ctc-edit-btn").prop("disabled", true);
            $(e.currentTarget).prop("disabled", true);
            resetForm("#CTC_container_" + objVal.CT_FORM_ID);
        });

        $(".ctc-cancel-btn").click(function (e) {
            let parent = $(e.currentTarget).parent(".ctc-ctl");
            let objVal = parent.data("ttp");
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("input, select, textarea").attr("disabled", "disabled");
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("button").prop("disabled", true);
            parent.find("button.ctc-add-btn").prop("disabled", false);
            parent.find("button.ctc-edit-btn").prop("disabled", true);
            parent.find("button.ctc-save-btn").prop("disabled", true);
            parent.find("button.ctc-kyso-btn").prop("disabled", true);
            parent.find("button.ctc-huykyso-btn").prop("disabled", true);
            parent.find("button.ctc-copy-btn").prop("disabled", true);
            parent.find("button.ctc-remove-btn").prop("disabled", true);
            $(e.currentTarget).prop("disabled", true);
            resetForm("#CTC_container_" + objVal.CT_FORM_ID);
        });

        $(".ctc-edit-btn").click(function (e) {
            _isType = 2;
            let parent = $(e.currentTarget).parent(".ctc-ctl");
            let objVal = parent.data("ttp");
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("input, select, textarea").removeAttr("disabled");
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("button").prop("disabled", false);
            parent.find("button.ctc-save-btn").prop("disabled", false);
            parent.find("button.ctc-copy-btn").prop("disabled", true);
            parent.find("button.ctc-kyso-btn").prop("disabled", false);
            parent.find("button.ctc-huykyso-btn").prop("disabled", false);
            parent.find("button.ctc-cancel-btn").prop("disabled", false);
            parent.find("button.ctc-add-btn").prop("disabled", true);
            parent.find("button.ctc-remove-btn").prop("disabled", true);
            $(e.currentTarget).prop("disabled", true);
        });

        $(".ctc-copy-btn").click(function (e) {
            _isType = 3;
            let parent = $(e.currentTarget).parent(".ctc-ctl");
            let objVal = parent.data("ttp");
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("input, select, textarea").removeAttr("disabled");
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("button").prop("disabled", false);
            parent.find("button.ctc-save-btn").prop("disabled", false);
            parent.find("button.ctc-edit-btn").prop("disabled", true);
            parent.find("button.ctc-copy-btn").prop("disabled", true);
            parent.find("button.ctc-kyso-btn").prop("disabled", false);
            parent.find("button.ctc-huykyso-btn").prop("disabled", false);
        });

        $(".ctc-save-btn").click(function (e) {
            let parent = $(e.currentTarget).parent(".ctc-ctl");
            _saveAndCaChitiet(parent, false, _isType);
        });

        $(".ctc-kyso-btn").click(function (e) {
            let parent = $(e.currentTarget).parent(".ctc-ctl");
            _saveAndCaChitiet(parent, true, _isType);
        });

        $(".ctc-huykyso-btn").click(function (e) {
            let parent = $(e.currentTarget).parent(".ctc-ctl");
            _caRptChitiet(parent, '2');
        });

        $(".ctc-remove-btn").click(function (e) {
            let parent = $(e.currentTarget).parent(".ctc-ctl");
            let objVal = parent.data("ttp");
            let _rid = $(`#txtRID_${objVal.CT_FORM_ID}`).val() + "";
            let flagLoading = true;
            DlgUtil.showConfirm("Bạn có muốn xóa bản ghi này ko?", function (flag) {
                if (flag) {
                    let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.23", _rid);
                    if (fl == 1) {
                        DlgUtil.showMsg("Xóa thành công!", function () {
                        }, 600);
                        let searchObject = {
                            PHIEUCTID: $("#SubData_" + objVal.CT_FORM_ID).data("phieuchitietid") + "", CT_FORM_ID: objVal.CT_FORM_ID, PHIEUID: phieuid.toString()
                        }
                        loadGridDataElement(searchObject);
                        resetForm("#CTC_container_" + objVal.CT_FORM_ID);
                        flagLoading = false;
                    } else {
                        DlgUtil.showMsg("Xảy ra lỗi!");
                    }
                    parent.find("button.ctc-save-btn").prop("disabled", true);
                    parent.find("button.ctc-kyso-btn").prop("disabled", true);
                    parent.find("button.ctc-huykyso-btn").prop("disabled", true);
                    parent.find("button.ctc-copy-btn").prop("disabled", true);
                    parent.find("button.ctc-cancel-btn").prop("disabled", true);
                    parent.find("button.ctc-remove-btn").prop("disabled", true);
                    parent.find("button.ctc-edit-btn").prop("disabled", true);
                    parent.find("button.ctc-add-btn").prop("disabled", false);
                }
            });
        });
    }

    function _caRpt(signType) {
        _flagCaAll = false;
        if (rptcodedetail == '') {
            return;
        }
        if (_configctcflagca.GIATRI === "1" && maphieu.split("-")[0] === _configctcflagca.MAFORM) {
            _caRptChiTietList(signType, 1);
        } else {
            _par_rpt_kyso = [];
            _par_rpt_kyso.push({name: "hosobenhanid", type: "String", value: objectVar.HOSOBENHANID});
            _par_rpt_kyso.push({name: "khambenhid", type: "String", value: _khambenhid});
            _par_rpt_kyso.push({name: "formid", type: "String", value: formid});
            _par_rpt_kyso.push({name: "phieuid", type: "String", value: phieuid});
            if (keyValue == 1) {
                _par_rpt_kyso.push({name: "dichvukhambenhid", type: "String", value: _dichvukhambenhid});
            }
            _par_rpt_kyso.push({name: "rpt_code", type: "String", value: rptcodedetail});
            if (signType == '0') {
                CommonUtil.openReportGetCA2(_par_rpt_kyso, false);
            } else {
                CommonUtil.kyCA(_par_rpt_kyso, signType, _isPrint, true);
            }
        }
    }

    function _caRptAll(signType, textValue) {
        if (signType == '0') {
            _caRptAll2(signType, '', '', textValue);
        } else {
            let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", rptcode);
            let userCaConf = CaUtils.getCACachingConfig(rptcode);
            if (data_ar != null && data_ar.length > 0) {
                let row = data_ar[0];
                let catype = row.CA_TYPE;
                let kieuky = row.KIEUKY;
                if (catype == '3' || catype == '6') {
                    let _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                    let _paramInput = {
                        params: null, smartca_method: 0
                    };
                    EventUtil.setEvent("dlgCaLogin_confirm", function () {
                        DlgUtil.close("divCALOGIN");
                        let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                        causer = _hisl2SmartCa.token.refresh_token;
                        capassword = _hisl2SmartCa.token.access_token;
                        smartcauser = _hisl2SmartCa.user.uid;
                        _caRptAll2(signType, catype, kieuky);
                    });
                    let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                    if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
                        _paramInput.smartca_method = 1;
                        if (ch.KYSO_TUDONG_KYDIENTU == '1') {
                            let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                            causer = _hisl2SmartCa.token.refresh_token;
                            capassword = _hisl2SmartCa.token.access_token;
                            smartcauser = _hisl2SmartCa.user.uid;
                            _caRptAll2(signType, catype, kieuky);
                        } else {
                            let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
                            _popup.open("divCALOGIN");
                            return;
                        }
                    } else {
                        EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function (e) {
                            if (e.data && e.data.token && e.data.token.access_token) {
                                _paramInput.smartca_method = 1;
                            }
                            DlgUtil.close("dlgCA_SMARTCA_LOGIN");
                            let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
                            _popup.open("divCALOGIN");
                            return;
                        });
                        DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {isSignPopup: true}, "Smart Ca Login", 500, 650);
                        DlgUtil.open("dlgCA_SMARTCA_LOGIN");
                        return;
                    }
                } else if (catype == '5') {
                    let rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
                    if (rowDatas.length > 1) {
                        DlgUtil.showMsg("Không được phép ký nhiều phiếu với kiểu " + kieuky);
                        return;
                    } else {
                        _caRptAll3(signType, catype, kieuky);
                    }
                } else if (userCaConf && ch.KYSO_TUDONG_KYDIENTU == '1') {
                    causer = userCaConf.USER_NAME;
                    capassword = userCaConf.PASS_WORD;
                    _caRptAll2(signType, catype, kieuky);
                } else {
                    EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                        causer = e.username;
                        capassword = e.password;
                        DlgUtil.close("divCALOGIN");
                        _caRptAll2(signType, catype, kieuky);
                    });
                    EventUtil.setEvent("dlgCaLogin_close", function (e) {
                        DlgUtil.close("divCALOGIN");
                    });
                    let url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                    let popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
                    popup.open("divCALOGIN");
                }
            }
        }
    }

    function _caRptAll2(signType, catype, kieuky, textValue) {
        _lstParamHashed = '';
        _flagCaAll = true;
        let _rowDatas = $("#" + _gridId).jqGrid("getGridParam", "selarrrow");
        _rowDatas.forEach(function (el) {
            if ($("#" + _gridId).jqGrid('getRowData', el).FLAG_CHA != 1) {
                _caRptAll3(el, signType, catype, kieuky);
            }
        });
        if ((signType == '0' || signType == '1') && textValue != 'ONE') {
            let par = [];
            par.push({name: 'HOSOBENHANID', type: 'String', value: objectVar.HOSOBENHANID});
            par.push({name: 'LST_PARAM_HASHED', type: 'String', value: _lstParamHashed.substring(1, _lstParamHashed.length)});
            par.push({name: 'RPT_CODE', type: 'String', value: _par_rpt_kyso[4].value});
            CommonUtil.openReportGetViewCa(par, false, {
                fieldOrder: {
                    key: (_rptOrderField ? _rptOrderField.toUpperCase() : ""), type: "S"
                }
            });
        } else if ((signType == '0' || signType == '1') && textValue == 'ONE') {
            let par = [];
            par.push({name: "hosobenhanid", type: "String", value: objectVar.HOSOBENHANID});
            par.push({name: "khambenhid", type: "String", value: _par_rpt_kyso[1].value});
            par.push({name: "formid", type: "String", value: _par_rpt_kyso[2].value});
            par.push({name: "phieuid", type: "String", value: _par_rpt_kyso[3].value});
            par.push({name: 'rpt_code', type: 'String', value: _par_rpt_kyso[4].value});
            CommonUtil.openReportGetCA2(par, false);
        }

        if (signType != '0') {
            DlgUtil.showMsg(_msgCa);
            loadGridData();
        }
    }

    function _caRptAll3(rowId, signType, catype, kieuky) {
        let _row = $("#" + _gridId).jqGrid('getRowData', rowId);
        if (_row.RPT_CODE == '' || (signType == '1' && _row.FLAG_CA == '1') || (signType == '2' && _row.FLAG_CA != '1')) {
            return;
        }
        _par_rpt_kyso = [];
        _par_rpt_kyso.push({name: "hosobenhanid", type: "String", value: objectVar.HOSOBENHANID});
        _par_rpt_kyso.push({name: "khambenhid", type: "String", value: _row.KHAMBENHID});
        _par_rpt_kyso.push({name: "formid", type: "String", value: _row.FORM_ID});
        _par_rpt_kyso.push({name: "phieuid", type: "String", value: _row.PHIEUID});
        _par_rpt_kyso.push({name: "rpt_code", type: "String", value: _row.RPT_CODE});
        let paramHashed = CryptoJS.MD5(JSON.stringify(_par_rpt_kyso).toUpperCase()).toString().toUpperCase();
        _lstParamHashed = _lstParamHashed + "," + paramHashed;
        _rptOrderField = _row.RPT_ORDER_FIELD;

        if (signType != '0') {
            let oData = {
                sign_type: signType, causer: causer, capassword: capassword, smartcauser: smartcauser, params: _par_rpt_kyso
            };
            if (catype == '5') {
                CommonUtil.kyCA(_par_rpt_kyso, signType, true);
                EventUtil.setEvent("eventKyCA", function (e) {
                    DlgUtil.showMsg(e.res);
                });
            } else {
                let msg = CommonUtil.caRpt(oData, _row.RPT_CODE, false, '', true, kieuky, catype);
                let _code = msg.split("|")[0];
                _msgCa = msg.split("|")[1];
                if (_code == '0' || _code == '7' || _code == '8') {
                    let objCa = new Object();
                    objCa.TABLENAME = 'KBH_PHIEU';
                    objCa.COLUMNAME = 'PHIEUID';
                    objCa.COLUMDATA = _par_rpt_kyso[3].value;
                    objCa.SINGTYPE = _catype;
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.FLAG.CA", JSON.stringify(objCa));
                }
            }
        }
    }

    function setThongTinChung(phieuid) {
        let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.17", phieuid);
    }

    function setComboboxPhieuCha(_phieuid, maformcha) {
        let _jsonObject = new Object();
        let _dataJson = "";
        let _formid = $('#cboDANHSACH').val();
        let _maform = _checkExistMaForm(_configshowform.MAFORM, _maformcha[0] + "-");
        if (_maform == maformcha[0]) {
            let sql_par = new Object();
            sql_par.khambenhid = _khambenhidVar;
            sql_par.formid_1 = _formid;
            sql_par.formid_2 = _formid;
            sql_par.phieuid = _phieuid + "";
            try {
                _jsonObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.37", sql_par);
                _dataJson = JSON.parse(JSON.stringify(_jsonObject[0]));
                let _option = $('<option value="' + _dataJson.PHIEUID + '">' + _dataJson.TENPHIEU + '</option>');
                $("#cboPhieuCha").empty();
                $("#cboPhieuCha").append(_option);
            } catch (e) {
                console.log("Error: " + e);
            }
        } else {
            let _option = $('<option value="-1">--Tất cả--</option>');
            $("#cboPhieuCha").empty();
            $("#cboPhieuCha").append(_option);
        }
    };

    function _setCaEvent() {
        EventUtil.setEvent("eventKyCA", function (e) {
            let _code = e.res.split("|")[0];
            _msgCa = e.res.split("|")[1];
            if (_code == '0' || _code == '7' || _code == '8') {
                let objCa = new Object();
                objCa.TABLENAME = 'KBH_PHIEU';
                objCa.COLUMNAME = 'PHIEUID';
                objCa.COLUMDATA = _par_rpt_kyso[3].value;
                objCa.SINGTYPE = _catype;
                jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.FLAG.CA", JSON.stringify(objCa));
            }
            if (!_flagCaAll) {
                DlgUtil.showMsg(_msgCa);
                loadGridData();
            }
        });
    }

    function loadGridDataElement(searchObject) {
        GridUtil.loadGridBySqlPage("grdDanhSach_" + searchObject.CT_FORM_ID, 'NTU02D204.20', [{"name": "[0]", "value": JSON.stringify(searchObject)}]);
    }

    function resetForm(containerForm) {
        $(containerForm).find(`input, textarea, select`).val("");
        $(containerForm).find(`[name*="txtRID_"]`).val("");
        $(containerForm).find("select").val("");
        $(containerForm).find(`input[type="radio"], input[type="checkbox"]`).each((index, el) => $(el).prop("checked", false))
    }

    function save() {
        let _dataphieu = jsonrpc.AjaxJson.dbExecuteQuery("", "NTU02D204.33", [{"name": "[0]", value: phieuid}]);
        let _dataFlagCa = "";
        _dataphieu.forEach(function (_list) {
            _dataFlagCa = _list[13];
        });
        if (_nguoitaophieu != _opts._user_id && _permission == '1' && _flgCha == '0') {
            DlgUtil.showMsg("Bạn không có quyền thao tác.");
        } else if (_dataFlagCa == 1 && _permission == '1') {
            DlgUtil.showMsg("Phiếu đã thực hiện ký không thể chỉnh sửa.");
        } else {
            if (phieuid == -1) return;
            let _validator = new DataValidator("content_CTPhieu");
            let valid = _validator.validateFormExcludes(".ctc_element");
            if (!valid) {
                return false;
            }
            let arrPhieuCTIns = [];
            let arrDataClob = [];
            $("#content_CTPhieu > .ct-form-id:not(.ctc_element)").each(function (idx, el) {
                let type = $(el).data("type");
                let ctformid = $(el).data("ct-form-id");
                let split = $(el).data("split");
                let val = "";
                let label_v = "";
                let dl_html = "";
                if (type == 1) {
                    if (split && split !== "") {
                        val = $(el).find("input").toArray().map(function (el2) {
                            return $(el2).val();
                        }).join(split);
                    } else {
                        val = $(el).find("input").val();
                    }
                }

                if (type == 2) {
                    val = $(el).find("input:checked").val();
                    if (val) {
                        label_v = $(el).find("input:checked").parent().text().trim();
                    }
                }

                if (type == 3) {
                    let arrCheckedEls = $(el).find('input').toArray();
                    if (arrCheckedEls && arrCheckedEls.length > 0) {
                        arrCheckedEls.forEach(function (el2) {
                            let check = ($(el2).prop("checked")) ? $(el2).val() : "#";
                            val = val + check + "|";
                            label_v = label_v + $(el2).parent().text().trim() + ";";
                        });
                        val = val.replace(/\|$/, "")
                        label_v = label_v.replace(/;$/, "")
                    }
                }

                if (type == 4) {
                    val = $(el).find("input").val();
                }

                if (type == 14) {
                    val = $(el).find("input").val();
                }

                if (type == 5) {
                    val = $(el).find("select").val();
                    label_v = $(el).find(`select option[value="${val}"]`).text().trim();
                }

                if (type == 7) {
                    let _selectedEl = $(el).find("select option:selected");
                    if (_selectedEl.val()) {
                        val = _selectedEl.val() + "|" + _selectedEl.text();
                        label_v = _selectedEl.text();
                    }
                }

                if (type == 8) {
                    let _arrVals = $(el).find("input").toArray().map(function (el2) {
                        return $(el2).val()
                    });
                    val = _arrVals.join("|");
                    label_v = _arrVals.join(";");
                }

                if (type == 10) {
                    if (_mutilCombobox == '1') {
                        val = $(el).find("input#txtMSCFULL_" + ctformid).val() + "&&" + $(el).find("input#txtMSCGC_" + ctformid).val();
                    } else {
                        val = $(el).find("input#txtMSCFULL_" + ctformid).val();
                    }
                }

                if (type == 11) {
                    val = $(el).find("textarea#textarea_" + ctformid).val();
                }

                if (type == 13) {
                    let valHtmlForm = {};
                    $(el).find("input").each((idx, el) => {
                        if (el.type === "checkbox") {
                            valHtmlForm[el.getAttribute("data-key")] = el.checked ? "1" : "0";
                            if (el.checked) {
                                el.setAttribute("checked", "checked");
                            } else {
                                el.removeAttribute("checked");
                            }
                        } else if (el.type === "radio") {
                            valHtmlForm[el.getAttribute("data-key") + "." + el.getAttribute("data-name")] = el.checked ? "1" : "0";
                            if (el.checked) {
                                el.setAttribute("checked", "checked");
                            } else {
                                el.removeAttribute("checked");
                            }
                        } else if (el.type === "text") {
                            valHtmlForm[el.getAttribute("data-key")] = el.value;
                            el.setAttribute("value", el.value);
                        }
                    });
                    val = JSON.stringify(valHtmlForm);
                    dl_html = $(el).find(".html-content").html();
                    dl_html = dl_html.replaceAll("$", "_DO_LAR_");
                }

                if ((val && val !== "") || type == 8 || type == 1 || type == 10 || type == 11 || type == 5 || type == 4 || type == 14) {
                    arrPhieuCTIns.push({
                        PHIEUID: phieuid + "",
                        FORM_ID: formid + "",
                        MA_FROM: _maformphieu[0].toString(),
                        CT_FORM_ID: ctformid + "",
                        DULIEU: val + "",
                        LABEL_V: label_v + "",
                        TYPE: type + "",
                        SPLIT: split ? split + "" : "",
                        KHAMBENHID: objectVar.KHAMBENHID,
                        GOM_PHIEU: gomphieu,
                        NGAYTHUCHIEN: $("#datepicker_TGTH").val(),
                        NGAYTAOPHIEU: $("#datepicker_TGTP").val(),
                        DICHVUKHAMBENHID: _dichvukhambenhid
                    });
                    if (type == 13) {
                        arrDataClob.push(dl_html);
                    }
                }
            });

            $("#content_CTPhieu > .ct-form-id.ctc_element").each(function (idx, el) {
                arrPhieuCTIns.push({
                    PHIEUID: phieuid + "",
                    FORM_ID: formid + "",
                    MA_FROM: _maformphieu[0].toString(),
                    CT_FORM_ID: $(el).data("ct-form-id") + "",
                    DULIEU: "KBH_CHITIET_PHIEU_C Reference",
                    LABEL_V: "",
                    TYPE: "12",
                    SPLIT: "",
                    KHAMBENHID: objectVar.KHAMBENHID,
                    GOM_PHIEU: gomphieu,
                    NGAYTHUCHIEN: $("#datepicker_TGTH").val(),
                    NGAYTAOPHIEU: $("#datepicker_TGTP").val()
                });
            })

            arrDataClob.push("");
            arrDataClob.push("");
            arrDataClob.push("");

            let param = [JSON.stringify(arrPhieuCTIns), ...arrDataClob.slice(0, 3)];
            let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.26", param.join('$'));
            if (fl > 0) {
                let objPhieu = new Object();
                objPhieu.PHIEUID = phieuid;
                objPhieu.NGAYTHUCHIEN = $("#datepicker_TGTH").val();
                objPhieu.NGAYTAOPHIEU = $("#datepicker_TGTP").val();
                if (keyValue == 1 || keyValue == '1') {
                    objPhieu.DICHVUKHAMBENHID = _dichvukhambenhid.toString();
                }
                let f2 = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.11", [JSON.stringify(objPhieu)].join('$'));
                if (f2 == 1) {
                    $(".ctc-over-el").removeClass("ctc-over-locked");
                    $(".ctc-over-el").hide();
                    if (_isKyCA) {
                        _caRpt('1');
                    } else {
                        DlgUtil.showMsg("Lưu phiếu thành công!");
                        loadGridData();
                        _setParamDefaultForm();
                    }
                } else {
                    DlgUtil.showMsg("Lưu ngày thực hiện lỗi!");
                }
            } else {
                DlgUtil.showMsg("Lưu thông tin phiếu lỗi!");
            }
        }
    }

    // Remove Class vào HTML
    function _removeClass(value) {
        if (value.length > 0) {
            for (let i = 0; i < value.length; i++) {
                $("#" + value[i]).removeClass("hidden");
            }
        }
    }

    // Add Class vào HTML
    function _moreClass(value) {
        if (value.length > 0) {
            for (let i = 0; i < value.length; i++) {
                $("#" + value[i]).addClass("hidden");
            }
        }
    }

    // Đưa các danh sách id khi chọn vào một mảng mới
    function _onSelectRowId(id, status, row) {
        if (status && _listIdRow.indexOf(id) == -1 && id != null && id != undefined) {
            _listIdRow.push(id);
            _listPhieuId.push(row.PHIEUID);
        }

        if (!status) {
            let i = _listPhieuId.indexOf(row.PHIEUID);
            if (i != -1) {
                _listPhieuId.splice(i, 1);
            }
            let j = _listIdRow.indexOf(id);
            if (j != -1) {
                _listIdRow.splice(j, 1);
            }
        }
        _setDisabled(_listIdRow);
    }

    // Xử lý khi bỏ chọn phiếu vẫn chọn phiếu cha.
    function _onClickRowId(id, status, row) {
        let _maform = _checkExistMaForm(_configshowform.MAFORM, _maformcha[0] + "-");
        if (_maform == _maformphieu[0]) {
            if (row.FLAG_CHA == '1') {
                $("#" + _gridId).find("tr[id='" + id + "']").find("input[type=checkbox]").prop("checked", true);
                $("#" + _gridId).find("tr[id='" + id + "']").find("input[type=checkbox]").prop("disabled", true);
            }
        }
    }

    // Set dữ liệu vào từng biến khi chọn dòng dữ liệu
    function _setDataSelectRowId(id, _row) {
        _rowid = id;
        phieuid = _row.PHIEUID;
        maphieu = _row.MA_FORM;
        tenphieu = _row.TEN_FORM;
        formid = _row.FORM_ID;
        rptcodedetail = _row.RPT_CODE_DETAIL;
        rptcode = _row.RPT_CODE;
        gomphieu = _row.GOM_PHIEU;
        ngaythuchien = _row.NGAYTHUCHIEN;
        ngaytaophieu = _row.NGAYTAO;
        phieuidcha = _row.PHIEUIDCHA;
        ky_tructiep = _row.KY_TRUCTIEP;
        _rptOrderField = _row.RPT_ORDER_FIELD;
        _indexId = _listIdRow[_listIdRow.length - 1];
        _flgCa = _row.FLAG_CA;
        _flgCha = _row.FLAG_CHA;
        _printHtml = _row.PRINT_HTML;
        _linkHtml = _row.LINK_HTML;
        _nguoitaophieu = _row.USERNGUOITAO;
        _khambenhid = _row.KHAMBENHID;
        _exitMaForm = _checkExistMaForm(_configshowform.MAFORM, _maformphieu[0] + "-");
    }

    // Đưa dữ liệu khi chọn tất cả vào mảng
    function _onSelectAllRowId(id, status, ret) {
        for (let i = 0; i < ret.length; i++) {
            let rowData = $("#" + _gridId).jqGrid('getRowData', ret[i]);
            if (status && _listIdRow.indexOf(ret[i]) == -1 && (ret[i]) != null && (ret[i]) != undefined) {
                _listIdRow.push(ret[i]);
                _listPhieuId.push(rowData.PHIEUID);
            }

            if (!status) {
                let k = _listPhieuId.indexOf(rowData.PHIEUID);
                if (k != -1) {
                    _listPhieuId.splice(k, 1);
                }
                let j = _listIdRow.indexOf(ret[i]);
                if (j != -1) {
                    _listIdRow.splice(j, 1);
                }
            }
            _setDisabled(_listIdRow);
        }
    }

    // Xử lý khi bỏ chọn nhiều phiếu vẫn chọn phiếu cha.
    function _onClickAllRowId(id, status, ret) {
        let _maform = _checkExistMaForm(_configshowform.MAFORM, _maformcha[0] + "-");
        if (_maform == _maformphieu[0]) {
            for (let i = 0; i < ret.length; i++) {
                let _row = $("#" + _gridId).jqGrid('getRowData', ret[i]);
                if (_row.FLAG_CHA == '1') {
                    $("#" + _gridId).find("tr[id='" + ret[i] + "']").find("input[type=checkbox]").prop("checked", true);
                    $("#" + _gridId).find("tr[id='" + ret[i] + "']").find("input[type=checkbox]").prop("disabled", true);
                }
            }
        }
    }

    // Thêm Attr Disabled vào HTML
    function _setDisabled(listRowId) {
        if (listRowId.length == 0) {
            $("#btnXoa").attr("disabled", true);
            $("#btnCopy").attr("disabled", true);
            $("#btnInPhieuGop").attr("disabled", true);
        } else {
            $("#btnXoa").attr("disabled", false);
            $("#btnCopy").attr("disabled", false);
            $("#btnInPhieuGop").attr("disabled", false);
        }
    }

    // Thêm phiếu trình tự PTTT vào cập nhật PTTT
    function _insetThemPhieu() {
        _maformphieu = jsonrpc.AjaxJson.dbExecuteQuery("", "NTU02D204.30", [{"name": "[0]", value: objectVar.FORM_ID}]);
        let _formid = objectVar.FORM_ID;
        let _phieuid = (objectVar.PHIEU_ID == null || objectVar.PHIEU_ID == "" || objectVar.PHIEU_ID == undefined) ? 0 : objectVar.PHIEU_ID;
        if (keyValue == 1) {
            if (_formid != '-1') {
                let currentDate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS')
                let objData = new Object();
                objData.FORM_ID = objectVar.FORM_ID;
                objData.BENHNHANID = objectVar.BENHNHANID;
                objData.HOSOBENHANID = objectVar.HOSOBENHANID;
                objData.TIEPNHANID = objectVar.TIEPNHANID;
                objData.KHAMBENHID = objectVar.KHAMBENHID;
                objData.MAUBENHPHAMID = objectVar.MAUBENHPHAMID;
                objData.DICHVUKHAMBENHID = _dichvukhambenhid;
                objData.GOM_PHIEU = "4";
                objData.NGAYTHUCHIEN = currentDate;
                objData.COLUMN_KEY = columnKey;
                objData.COLUMN_VALUE = columnValue;
                objData.LOAI_PHIEU = objectVar.LOAI_PHIEU;
                let param = [JSON.stringify(objData)];
                if (_phieuid != 0) {
                    loadGridData(parseInt(_phieuid));
                } else {
                    let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.02", param.join('$'));
                    if (parseInt(fl) > 0) {
                        loadGridData(parseInt(fl));
                    }
                }
            }
            _removeClass(['tabsTTPTTT', 'btnXoaPhieu', 'btnClose', 'btnLuuKy', 'btnKySoIn', 'btnHuyKySo', 'btnInKySo']);
            $(parent.document).find(".jBox-title").hide();
        } else {
            _removeClass(['tabsContainer', 'tcDSPhieu', 'btnLuuKy', 'divTempForm', 'btnKySoIn', 'btnHuyKySo', 'btnInKySo']);
        }
    }

    // Thêm giá trị vào Element con
    function _setHtmlElement(html, ct_form_id) {
        try {
            if (html != null || html != undefined || html != "") {
                let tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                let divContent = tempDiv.querySelector("#" + ct_form_id).innerHTML;
                document.getElementById("" + ct_form_id).innerHTML = divContent;
            }
        } catch (e) {
            console.log(e.toString());
        }
    }

    // Kiểm tra MA_FORM theo cấu hình tồn tại không
    function _checkExistMaForm(_list, _maform) {
        let _maformcha = "";
        try {
            if (_list.length > 0) {
                for (let i = 0; i < _list.length; i++) {
                    if (_list[i].KEY == (_maform.split("-")[0])) {
                        _maformcha = _list[i].KEY;
                        break;
                    }
                }
            }
            return _maformcha;
        } catch (e) {
            return _maformcha;
        }
    }

    function _setDataBuildChild(_listRadio, _listObject, _dataValue) {
        let _html = "";
        let _listIndex = [];
        _listRadio.forEach(function (row) {
            _listIndex.push(row.VALUE_DATA);
        });
        let uniqueArray = [...new Set(_listIndex)].join(",");
        let _ct_form_id = "paramChild" + _listObject[0].CT_FORM_ID;
        let loaidl = _listObject[0].LOAI_DL;
        if (loaidl == 2) {
            _html = buildRadio(_listObject[0], "", "");
        } else if (loaidl == 3) {
            _html = buildCheckbox(_listObject[0], "", "");
        }
        if (uniqueArray === "2" && _listIndex.length === 9) {
            _html = _html.replaceAll('value="1"', 'value="1" checked');
            _setHtmlElement(_html, _ct_form_id);
        } else if (uniqueArray === "3" && _listIndex.length === 1) {
            _html = _html.replaceAll('value="2"', 'value="2" checked');
            _setHtmlElement(_html, _ct_form_id);
        } else if ((uniqueArray === "1,2" || uniqueArray === "2,1") && (_listIndex.length === 8 || _listIndex.length === 9)) {
            _html = _html.replaceAll('value="3"', 'value="3" checked');
            _setHtmlElement(_html, _ct_form_id);
        } else if ((uniqueArray === "1,2,3" || uniqueArray === "1,3,2" || uniqueArray === "2,1,3" || uniqueArray === "2,3,1" || uniqueArray === "3,1,2" || uniqueArray === "3,2,1") && _listIndex.length === 9) {
            _html = (_html.replaceAll('value="2"', 'value="2" checked')).replaceAll('value="3"', 'value="3" checked');
            _setHtmlElement(_html, _ct_form_id);
        } else if (uniqueArray === "1,3" || uniqueArray === "2,3" || uniqueArray === "3,1" || uniqueArray === "3,2") {
            _html = _html.replaceAll('value="2"', 'value="2" checked');
            _setHtmlElement(_html, _ct_form_id);
        } else {
            _setHtmlElement(_html, _ct_form_id);
        }
    }

    function _setHiddenButtonCa(_flgCha) {
        if (_flgCha === "1" || _flgCha === 1) {
            $("#btnKySoInAll").hide();
            $("#btnHuyKySoAll").hide();
            $("#btnInKySoAllOne").hide();
            $("#btnInKySoAll").hide();
        }
    }

    function _saveAndCaChitiet(parent, _flagCaCt, _type) {
        let objVal = parent.data("ttp");
        objVal.CHITIETPHIEUID = $("#SubData_" + objVal.CT_FORM_ID).data("phieuchitietid") + "";
        if (phieuid == -1) return;
        if (!objVal.CHITIETPHIEUID || objVal.CHITIETPHIEUID == -1) return;
        if (!objVal.CT_FORM_ID) return;
        if (!objVal.FORM_ID) return;
        if (!objVal.PHIEUID || objVal.CHITIETPHIEUID == -1) return;
        objVal.RID = (_type === 3 ? "" : $(`#txtRID_${objVal.CT_FORM_ID}`).val() + "");
        let _validator = new DataValidator("CTC_container_" + objVal.CT_FORM_ID);
        let valid = _validator.validateForm();
        if (!valid) {
            return false;
        }
        let arrPhieuCTCIns = [];
        $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").each(function (idx, el) {
            let type = $(el).data("type");
            let fctcid = $(el).data("fctcid");
            let split = $(el).data("split");
            let val = "";
            let label_v = "";
            if (type == 1) {
                if (split && split !== "") {
                    val = $(el).find("input").toArray().map(function (el2) {
                        return $(el2).val();
                    }).join(split);
                } else {
                    val = $(el).find("input").val();
                }
            }

            if (type == 2) {
                val = $(el).find("input:checked").val();
                if (val) {
                    label_v = $(el).find("input:checked").parent().text().trim();
                }
            }

            if (type == 3) {
                let arrCheckedEls = $(el).find('input:checked').toArray();
                if (arrCheckedEls && arrCheckedEls.length > 0) {
                    arrCheckedEls.forEach(function (el2) {
                        val = val + $(el2).val() + "|";
                        label_v = label_v + $(el2).parent().text().trim() + ";";
                    });
                    val = val.replace(/\|$/, "")
                    label_v = label_v.replace(/;$/, "")
                }
            }

            if (type == 4) {
                val = $(el).find("input").val();
            }

            if (type == 5) {
                val = $(el).find("select").val();
                if (val == -1) {
                    val = "";
                    label_v = "";
                } else {
                    label_v = $(el).find(`select option[value="${val}"]`).text().trim();
                }
            }

            if (type == 7) {
                let _selectedEl = $(el).find("select option:selected");
                if (_selectedEl.val()) {
                    val = _selectedEl.val() + "|" + _selectedEl.text();
                    label_v = _selectedEl.text();
                }
            }

            if (type == 8) {
                let _arrVals = $(el).find("input").toArray().map(function (el2) {
                    return $(el2).val()
                });
                val = _arrVals.join("|");
                label_v = _arrVals.join(";");
            }

            if (type == 10) {
                val = $(el).find("input#txtMSCFULL_" + objVal.CT_FORM_ID + "_" + fctcid).val();
            }


            if (type == 11) {
                val = $(el).find("textarea#textarea_" + objVal.CT_FORM_ID + "_" + fctcid).val();
            }

            if ((val && val !== "") || type == 8 || type == 1 || type == 10 || type == 11) {
                arrPhieuCTCIns.push({
                    DULIEU: val + "", TYPE: type + "", SPLIT: split ? split + "" : "", FCTC_ID: fctcid + "", LABEL_V: label_v + ""
                });
            }
        });
        let param = [JSON.stringify(objVal), JSON.stringify(arrPhieuCTCIns)];
        let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.21", param.join('$'));
        if (fl > 0) {
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("input, select, textarea").attr("disabled", "disabled");
            $("#CTC_container_" + objVal.CT_FORM_ID + " > .ct-form-id").find("button").prop("disabled", true);
            parent.find("button.ctc-save-btn").prop("disabled", true);
            parent.find("button.ctc-kyso-btn").prop("disabled", true);
            parent.find("button.ctc-huykyso-btn").prop("disabled", true);
            parent.find("button.ctc-copy-btn").prop("disabled", true);
            parent.find("button.ctc-cancel-btn").prop("disabled", true);
            parent.find("button.ctc-remove-btn").prop("disabled", true);
            parent.find("button.ctc-edit-btn").prop("disabled", true);
            parent.find("button.ctc-add-btn").prop("disabled", false);
            let searchObject = {
                PHIEUCTID: objVal.CHITIETPHIEUID, CT_FORM_ID: objVal.CT_FORM_ID, PHIEUID: phieuid.toString()
            }
            if (_flagCaCt) {
                _caRptChitiet(parent, '1', fl);
            } else {
                if (_type === 1) {
                    DlgUtil.showMsg("Thêm mới dữ liệu thành công.");
                    resetForm("#CTC_container_" + objVal.CT_FORM_ID);
                } else if (_type == 2) {
                    DlgUtil.showMsg("Cập nhật dữ liệu thành công.");
                    resetForm("#CTC_container_" + objVal.CT_FORM_ID);
                } else if (_type == 3) {
                    DlgUtil.showMsg("Sao chép dữ liệu thành công.");
                    resetForm("#CTC_container_" + objVal.CT_FORM_ID);
                }
                loadGridDataElement(searchObject);
            }
        } else {
            DlgUtil.showMsg("Lưu thông tin lỗi!");
        }
    }

    function _caRptChitiet(parent, signType, rid) {
        let objVal = parent.data("ttp");
        let _rid = (_isType == 1 || _isType == 3 ? rid : $(`#txtRID_${objVal.CT_FORM_ID}`).val() + "");
        let _par = [];
        _par.push({name: "hosobenhanid", type: "String", value: objectVar.HOSOBENHANID});
        _par.push({name: "rid", type: "String", value: _rid + ''});
        _par.push({name: "rpt_code", type: "String", value: rptcodedetail});
        CommonUtil.kyCA(_par, signType, _isPrint);
        EventUtil.setEvent("eventKyCA", function (e) {
            DlgUtil.showMsg(e.res);
            parent.find("button.ctc-save-btn").prop("disabled", true);
            parent.find("button.ctc-kyso-btn").prop("disabled", true);
            parent.find("button.ctc-huykyso-btn").prop("disabled", true);
            parent.find("button.ctc-copy-btn").prop("disabled", true);
            parent.find("button.ctc-cancel-btn").prop("disabled", true);
            parent.find("button.ctc-remove-btn").prop("disabled", true);
            parent.find("button.ctc-edit-btn").prop("disabled", true);
            parent.find("button.ctc-add-btn").prop("disabled", false);
            _checkStatusFlagCa(e.res, signType, _rid);
            loadDataGridChilds();
        });
    }

    // Kiểm tra ký thành công hoặc hủy ký thì cập nhật FlagCa ký số
    function _checkStatusFlagCa(result, signType, rid) {
        let par = {"PHIEUID": phieuid, "RID": rid, "SIGNTYPE": signType};
        let fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.35", par);
        if (fl > 0) {
            loadGridData();
            console.log("Result: Cập nhật trạng thái phiếu ký số thành công.");
        }
    }

    // Xử lý in nhiều ký số trong phiếu co danh sách con
    function _caRptChiTietList(signType, typePrint) {
        if (signType == '0') {
            _caRptChiTietList1(signType, null, null, typePrint);
        } else {
            let data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", rptcode);
            let userCaConf = CaUtils.getCACachingConfig(rptcode);
            if (data_ar != null && data_ar.length > 0) {
                let row = data_ar[0];
                let catype = row.CA_TYPE;
                let kieuky = row.KIEUKY;
                if (catype == '3' || catype == '6') {
                    let _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                    let _paramInput = {
                        params: null, smartca_method: 0
                    };
                    EventUtil.setEvent("dlgCaLogin_confirm", function () {
                        DlgUtil.close("divCALOGIN");
                        let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                        causer = _hisl2SmartCa.token.refresh_token;
                        capassword = _hisl2SmartCa.token.access_token;
                        smartcauser = _hisl2SmartCa.user.uid;
                        _caRptChiTietList1(signType, catype, kieuky, typePrint);
                    });
                    let hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                    if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
                        _paramInput.smartca_method = 1;
                        if (ch.KYSO_TUDONG_KYDIENTU == '1') {
                            let _hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
                            causer = _hisl2SmartCa.token.refresh_token;
                            capassword = _hisl2SmartCa.token.access_token;
                            smartcauser = _hisl2SmartCa.user.uid;
                            _caRptChiTietList1(signType, catype, kieuky, typePrint);
                        } else {
                            let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
                            _popup.open("divCALOGIN");
                            return;
                        }
                    } else {
                        EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function (e) {
                            if (e.data && e.data.token && e.data.token.access_token) {
                                _paramInput.smartca_method = 1;
                            }
                            DlgUtil.close("dlgCA_SMARTCA_LOGIN");
                            let _popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
                            _popup.open("divCALOGIN");
                            return;
                        });
                        DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {isSignPopup: true}, "Smart Ca Login", 500, 650);
                        DlgUtil.open("dlgCA_SMARTCA_LOGIN");
                        return;
                    }
                } else if (catype == '5') {
                    let rowDatas = $('#' + _self.containerId + _self.options._grdDieuTri).jqGrid("getGridParam", "selarrrow");
                    if (rowDatas.length > 1) {
                        DlgUtil.showMsg("Không được phép ký nhiều phiếu với kiểu " + kieuky);
                        return;
                    } else {
                        _caRptChiTietList2(signType, catype, kieuky, typePrint);
                    }
                } else if (userCaConf && ch.KYSO_TUDONG_KYDIENTU == '1') {
                    causer = userCaConf.USER_NAME;
                    capassword = userCaConf.PASS_WORD;
                    _caRptChiTietList1(signType, catype, kieuky, typePrint);
                } else {
                    EventUtil.setEvent("dlgCaLogin_confirm", function (e) {
                        causer = e.username;
                        capassword = e.password;
                        DlgUtil.close("divCALOGIN");
                        _caRptChiTietList1(signType, catype, kieuky, typePrint);
                    });
                    EventUtil.setEvent("dlgCaLogin_close", function (e) {
                        DlgUtil.close("divCALOGIN");
                    });
                    let url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
                    let popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
                    popup.open("divCALOGIN");
                }
            }
        }
    }

    function _caRptChiTietList1(signType, catype, kieuky, typePrint) {
        _lstParamHashed = "";
        let _grid = "";
        let _ctFormId = "";
        let _rowDatas = "";
        $(".ctc-element-grid").toArray().forEach(grid => {
            _grid = grid.id;
            _ctFormId = grid.getAttribute("data-ct-form-id");
            _rowDatas = $("#" + grid.id).getDataIDs();
        });
        if (typePrint === 2 || typePrint === "2") {
            let _dsPhieuId = [];
            let _row = $("#" + _gridId).jqGrid('getRowData', 1);
            if (((_configure.indexOf((_row.MA_FORM).split("-")[0]) > 0) && (_configure.indexOf(_paramSession[0]) > 0)) === true) {
                $("#" + _gridId).jqGrid("getGridParam", "selarrrow").forEach(function (el) {
                    let _row = $("#" + _gridId).jqGrid('getRowData', el);
                    _dsPhieuId.push(_row.PHIEUID);
                });
                let sql_par = {"FORM_ID": formid, "PHIEU_ID": _dsPhieuId.join(","), "KEY_NAME": "1"};
                jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.36", JSON.stringify(sql_par)).forEach(function (el) {
                    _caRptChiTietList2(el, signType, catype, kieuky, _grid, typePrint, el.FLAG_CA);
                });
            } else {
                $("#" + _gridId).getDataIDs().forEach(function (el) {
                    let _row = $("#" + _gridId).jqGrid('getRowData', el);
                    _dsPhieuId.push(_row.PHIEUID);
                });
                let sql_par = {"FORM_ID": formid, "PHIEU_ID": _dsPhieuId.join(","), "KEY_NAME": "1"};
                jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.36", JSON.stringify(sql_par)).forEach(function (el) {
                    _caRptChiTietList2(el, signType, catype, kieuky, _grid, typePrint, el.FLAG_CA);
                });
            }

        } else {
            _rowDatas.forEach(function (el) {
                _caRptChiTietList2(el, signType, catype, kieuky, _grid, typePrint);
            });
        }

        if (signType == '0' || signType == '1') {
            let par = [];
            par.push({name: 'HOSOBENHANID', type: 'String', value: objectVar.HOSOBENHANID});
            par.push({name: 'LST_PARAM_HASHED', type: 'String', value: _lstParamHashed.substring(1, _lstParamHashed.length)});
            par.push({name: 'RPT_CODE', type: 'String', value: _par_rpt_kyso[2].value});
            CommonUtil.openReportGetViewCa(par, false, {
                fieldOrder: {
                    key: (_rptOrderField ? _rptOrderField.toUpperCase() : ""), type: "S"
                }
            });
        }
        if (signType != '0') {
            DlgUtil.showMsg(_msgCa);
            loadGridData();
        }
    }

    function _caRptChiTietList2(rowId, signType, catype, kieuky, grid, typePrint, flagCa) {
        let _flagCa = "";
        let _rid = "";
        if (typePrint === 2) {
            _flagCa = flagCa
            _rid = rowId.RID;
        } else {
            let _row = $("#" + grid).jqGrid('getRowData', rowId);
            _flagCa = _row.FLAG_CA;
            _rid = _row.RID;
        }
        if (rptcode == '' || (signType == '1' && _flagCa == '1') || (signType == '2' && _flagCa != '1')) {
            return null;
        }
        _par_rpt_kyso = [];
        _par_rpt_kyso.push({name: "hosobenhanid", type: "String", value: objectVar.HOSOBENHANID});
        _par_rpt_kyso.push({name: "rid", type: "String", value: _rid + ''});
        _par_rpt_kyso.push({name: "rpt_code", type: "String", value: rptcode});
        let paramHashed = CryptoJS.MD5(JSON.stringify(_par_rpt_kyso).toUpperCase()).toString().toUpperCase();
        _lstParamHashed = _lstParamHashed + "," + paramHashed;
        if (signType != '0') {
            let oData = {
                sign_type: signType, causer: causer, capassword: capassword, smartcauser: smartcauser, params: _par_rpt_kyso
            };
            if (catype == '5') {
                CommonUtil.kyCA(_par_rpt_kyso, signType, true);
                EventUtil.setEvent("eventKyCA", function (e) {
                    DlgUtil.showMsg(e.res);
                });
            } else {
                let msg = CommonUtil.caRpt(oData, rptcode, false, '', true, kieuky, catype);
                let _code = msg.split("|")[0];
                _msgCa = msg.split("|")[1];
                if (_code == '0' || _code == '7' || _code == '8') {
                    let objCa = new Object();
                    objCa.TABLENAME = 'KBH_PHIEU';
                    objCa.COLUMNAME = 'PHIEUID';
                    objCa.COLUMDATA = rowId.PHIEUID;
                    objCa.SINGTYPE = _catype;
                    jsonrpc.AjaxJson.ajaxCALL_SP_I("UPD.FLAG.CA", JSON.stringify(objCa));
                }
            }
        }
    }

    // Kiểm tra ký ở các khoa khác khoa
    function _checkCaKhacKhoa(_catype, _type) {
        if (_catype != '0') {
            let obj = new Object();
            obj.PHIEUID = phieuid;
            obj.KHAMBENHID = objectVar.KHAMBENHID;
            let fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D204.CHECKKYCA", obj);
            if (fl != '0' || fl === "" || fl === undefined) {
                DlgUtil.showMsg("Không thực hiện ký/hủy ở khoa khác khoa tạo phiếu khoa : " + fl.split("#")[0]);
                return;
            }
        }
        if (_type === '1') {
            _isPrint = true;
            _caRpt(_catype);
        } else if (_type === '2') {
            _isPrint = false;
            _isKyCA = true;
            if (keyValue == 1) {
                _kiemTraPhieuPTTT();
            } else {
                save();
            }
        }
    }

    // Kiểm tra phiếu PTTT đã đựợc cập nhật hay chưa
    function _kiemTraPhieuPTTT() {
        let objParam = new Object();
        objParam.FORM_ID = formid;
        objParam.PHIEU_ID = phieuid;
        objParam.DICHVUKHAMBENHID = _dichvukhambenhid;
        objParam.KEY_VALUE = "1";
        let result = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D204.25", objParam);
        if (result == 0) {
            DlgUtil.showMsg("Phiểu chưa được cập nhật phẫu thuật, thủ thuật.", function (e) {
                $("#btnClose").click();
            });
        } else {
            save();
        }
    }

    // Lấy thông hành chính bệnh nhân và thông tin bà mẹ trẻ em
    function _setParamDefaultForm() {
        let _dataArrayHanhChinh = new Object();
        let _dataArrayBmte = new Object();
        let _dataArraySoSinh = new Object();
        try {
            _dataArrayHanhChinh = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.TTHC", {
                KHOAID: objectVar.KHOAID,
                PHONGID: objectVar.PHONGID,
                BENHNHANID: objectVar.BENHNHANID,
                TIEPNHANID: objectVar.TIEPNHANID,
                HOSOBENHANID: objectVar.HOSOBENHANID,
                KHAMBENHID: objectVar.KHAMBENHID
            });
            // Làm riêng cho các màn hình thông tin bà mẹ trẻ em
            if (_maformphieu == _checkExistMaForm(_configformbmte.MAFORM, _maformphieu + "-0") && _configformbmte.GIATRI === '1') {
                _dataArrayBmte = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.TTBMTE", {
                    HOSOBENHANID: _hosobenhanid.toString(),
                    FORMID: $("#cboDANHSACH").val(),
                    BMTE_TTKT: "BMTE_TTKT",
                    BMTE_THONGTINSINHNO: "BMTE_THONGTINSINHNO",
                    BMTE_TTPT: "BMTE_TTPT"
                });
                if (_dataArrayBmte.length > 0) {
                    Object.assign(_dataArrayHanhChinh[0], {
                        XN_HIV_MT: _dataArrayBmte[0].XN_HIV_MT_SD,
                        XN_GM_MT: _dataArrayBmte[0].XN_GM_MT_SD,
                        XN_VGB_MT: _dataArrayBmte[0].XN_VGB_MT_SD,
                        XN_DUONGHUYET_MT: _dataArrayBmte[0].XN_DH_SD,
                        NGAY_KHAM_THAI: _dataArrayBmte[0].NGAY_KHAM_THAI,
                        NGAY_SINH_DE: _dataArrayBmte[0].NGAY_SINH_DE,
                        NGAY_PHA_THAI: _dataArrayBmte[0].NGAY_PHA_THAI
                    });
                }
            }

            // Làm riêng cho màn hình phiếu điều trị sơ sinh Sản nhi Vĩnh Phúc
            if (_maformphieu == _configsosinh) {
                _dataArraySoSinh = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU.TTSS.SNVPC", {
                    KHOAID: objectVar.KHOAID,
                    PHONGID: objectVar.PHONGID,
                    BENHNHANID: objectVar.BENHNHANID,
                    TIEPNHANID: objectVar.TIEPNHANID,
                    HOSOBENHANID: objectVar.HOSOBENHANID,
                    KHAMBENHID: objectVar.KHAMBENHID
                });
                if (_dataArraySoSinh.length > 0) {
                    Object.assign(_dataArrayHanhChinh[0], {
                        DATA_HOTENBO: _dataArraySoSinh[0].P_HOTENBO,
                        DATA_NAMSINHBO: _dataArraySoSinh[0].P_NAMSINHBO,
                        DATA_NGHENGHIEPBO: _dataArraySoSinh[0].P_NGHENGHIEPBO,
                        DATA_HOTENME: _dataArraySoSinh[0].P_HOTENME,
                        DATA_NAMSINHME: _dataArraySoSinh[0].P_NAMSINHME,
                        DATA_NGHENGHIEPME: _dataArraySoSinh[0].P_NGHENGHIEPME,
                        DATA_XNHBSAG: _dataArraySoSinh[0].P_XNHBSAG,
                        DATA_XNGIANGMAI: _dataArraySoSinh[0].P_XNGIANGMAI,
                        DATA_XNHIV: _dataArraySoSinh[0].P_XNHIV
                    });
                }
            }
            _jsonString = JSON.parse(JSON.stringify(_dataArrayHanhChinh[0]));
            console.log(_jsonString);
        } catch (e) {
            console.log("Lỗi lấy thông tin JsonString:" + e.toString());
        }
    }

    // Khi phiếu chăm sóc chỉ có một phiếu cha thì chọn mặc định
    function _setParentTicketData() {
        let _jsonObject = new Object();
        let _dataJson = "";
        let _formid = $('#cboDANHSACH').val();
        let _maform = _checkExistMaForm(_configshowform.MAFORM, _maformphieu[0] + "-");
        if (_maform == _maformphieu[0]) {
            let sql_par = {
                KHAMBENHID: _khambenhidVar.toString(), FORMID: _formid.toString()
            };
            try {
                _jsonObject = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D204.38", sql_par);
                _dataJson = JSON.parse(JSON.stringify(_jsonObject[0]));
                if (Number(_dataJson.SOLUONG_PHIEUCHA) <= 1 && Number(_dataJson.SOLUONG_PHIEUCHA >= 1)) {
                    _strPhieuChaId = _dataJson.PHIEUID;
                    _strTenPhieuCha = _dataJson.TENPHIEU;
                    let _option = $('<option value="' + _dataJson.PHIEUID + '">' + _dataJson.TENPHIEU + '</option>');
                    $("#cboPhieuCha").empty();
                    $("#cboPhieuCha").append(_option);
                } else {
                    _setDefaultComboboxPhieuCha('1');
                }
            } catch (e) {
                console.log("Lỗi lấy dữ liệu thông tin phiếu cha => " + e.toString());
            }
        } else {
            _setDefaultComboboxPhieuCha('1');
        }
    }

    // Kiểm tra phiếu có thuộc khoa thiết lập vào không
    function _checkFuctionKhoa(i_phieuid, i_khambenhid) {
        let _strTenKhoa = "";
        let obj = new Object();
        obj.PHIEUID = i_phieuid;
        obj.KHAMBENHID = i_khambenhid;
        let fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("NTU02D204.CHECKKYCA", obj);
        if (fl != '0' || fl === "" || fl === undefined) {
            _strTenKhoa = "1#" + fl.split("#")[0];
        }
        return _strTenKhoa;
    }

    // Hàm tính số tuần giữa hai mốc thời gian
    function _soTuanTheoKhoangThoiGian(startDate, endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const differenceInMilliseconds = Math.abs(end - start);
        const millisecondsInAWeek = 7 * 24 * 60 * 60 * 1000;
        const weeks = differenceInMilliseconds / millisecondsInAWeek;
        return weeks;
    }

    // Set default combobox phieu cha
    function _setDefaultComboboxPhieuCha(key_value) {
        _strPhieuChaId = "";
        _strTenPhieuCha = "";
        let _option = $('<option value="-1">--Tất cả--</option>');
        $("#cboPhieuCha").empty();
        $("#cboPhieuCha").append(_option);
    }

    // Hiển thị thông tin chăm sóc ở trên Thông tin hành chính
    function _loadThongTinChamSoc() {
        if (_showTabChamSocMoi == '1') {
            if (objectVar.LOAICHAMSOC == 'CHAMSOCCAPMOT') {
                $("#cboDANHSACH").val(_formchamsoc);
                $("#cboDANHSACH").change();
            } else if (objectVar.LOAICHAMSOC == 'CHAMSOCCAPHAIBA') {
                $("#cboDANHSACH").val(_formchamsoc);
                $("#cboDANHSACH").change();
            }
        } else {
            $("#cboDANHSACH").val(objectVar.FORM_ID);
        }
    }

    // Lấy CT_FORM_ID trong danh sách
    function _getDataChiTietFormId(_list, _value) {
        let _ct_from_id = "";
        if (_list.length > 0) {
            for (let i = 0; _list.length > 0; i++) {
                if (_list[i].RPT_PARAM_NAME == _value) {
                    return _ct_from_id = _list[i].CT_FORM_ID;
                }
            }
        } else {
            _ct_from_id = "";
        }
        return _ct_from_id;
    }

    // Cấu hình View ký số không theo ParamHasmap
    function _onClickViewCa(_maformphieu) {
        if (String(_mutilcheck.MAFORM).indexOf(_maformphieu[0]) >= 0 && _opts._hospital_id == _mutilcheck.CSYTID && _mutilcheck.GIATRI == '1') {
            $("#btnInKySoAllOne").show();
            $("#btnInKySoAll").hide();
        } else {
            $("#btnInKySoAllOne").hide();
            $("#btnInKySoAll").show();
        }
    }
}

function ntu02d204_removeSTextField(el) {
    $(el).parent().prev().detach();
    $(el).parent().detach();
}
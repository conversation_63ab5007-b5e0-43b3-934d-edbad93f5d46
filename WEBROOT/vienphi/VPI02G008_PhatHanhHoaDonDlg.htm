<!--
 * <PERSON><PERSON><PERSON><PERSON> l<PERSON>p tr<PERSON><PERSON> chú
 * HaNv					03/04/2020		Popup show danh sách dịch vụ CLS của BN
-->
<script type="text/javascript" src="../common/script/main.js"></script>
<script type="text/javascript" src="{menuJS}"></script>
<input type="hidden" name="company_id" id="company_id" value="{company_id}" />
<input type="hidden" name="user_id" id="user_id" value="{user_id}" />
<script type="text/javascript" src="../common/script/AjaxService.js"></script>
<script type="text/javascript" src="../common/script/RestService.js"></script>
<script type="text/javascript" src="../common/script/dbForm.js"></script>
<script type="text/javascript" src="../common/script/dbToolbar.js"></script>
<script type="text/javascript" src="../common/script/DataValidator.js"></script>
<script type="text/javascript" src="../common/script/CommonUtil.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.toaster.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/tableExport.js"></script>
<script type="text/javascript" src="../common/script/jquery.tableExport/jquery.base64.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.js"></script>
<script type="text/javascript" src="../common/script/jquery.i18n/jquery.i18n.messagestore.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-his.js"></script>
<script type="text/javascript" src="../common/i18n/i18n-ngoaitru.js"></script>
<script type="text/javascript" src="../common/script/jquery/jquery.mask-plugin.min.js"></script>
<script type="text/javascript" src="../common/script/bootstrap/bootstrap3-typeahead.min.js"></script>
<link rel="stylesheet" href="../common/css/font-awesome.min.css">
<link rel="stylesheet" href="../common/script/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="../common/css/custom.css">
<link rel="stylesheet" href="../noitru/custom_nt.css">
<link rel="stylesheet" href="../common/css/css_style.css">
<link rel="stylesheet" href="../common/script/dialog/jBox.css">
<script type="text/javascript" src="../common/script/dialog/jBox.js"></script>
<script src="../common/script/jqgrid/js/context-menu.js	"></script>
<link href="../common/script/jqgrid/css/ui.jqgrid.css" rel="stylesheet" />
<script src="../common/script/jqgrid/js/i18n/grid.locale-vi.js"></script>
<script src="../common/script/jqgrid/js/jquery.jqGrid.min.js"></script>
<script type="text/javascript" src="../common/vienphi/vienphi_tinhtien.js"></script>
<script type="text/javascript" src="../common/script/UIUtil.js"></script>
<script type="text/javascript" src="../common/script/moment.min.js"></script>
<script src="../common/script/common.js"></script>
<script type="text/javascript" src="../common/script/jquery.priceformat.2.0.min.js"></script>
<script type="text/javascript" src="../noitru/cominf.js?v=20200513"></script>
<script type="text/javascript" src="../vienphi/VPI02G008_PhatHanhHoaDonDlg.js?v=241217_1"></script>
<script type="text/javascript" src="../common/script/xml2json.js"></script>
<style type="text/css">
.money {
	font-weight: bolder;
	font-size: 14px;
	color: red;
}

.money:focus {
	font-weight: bolder;
	font-size: 14px;
	color: green;
}
/* L2PT-119389 start */
.xml-node {
	color: rgb(136, 18, 128);
}
.xml-text {
	color: black;
}
/* L2PT-119389 end */
</style>
<div class="container" id="divInfo">
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-4 low-padding" style="text-align: center;">
			<label class="checkbox-inline mgl5">
				<input type="checkbox" id="chkNHIEU_THUESUAT" style="margin-top: 3px !important;">
				Hóa đơn nhiều thuế suất
			</label>
		</div>
		<!-- <div class="col-md-4 low-padding" style="text-align: center;">
			<label class="checkbox-inline mgl5">
				<input type="checkbox" id="chkGOP_HOADON" style="margin-top: 3px !important;">
				Gộp hóa đơn
			</label>
		</div> -->
		<div class="col-md-4 low-padding" style="text-align: center;">
			<div class="col-md-3 low-padding">
				<label class="">Loại HĐ</label>
			</div>
			<div class="col-md-8 low-padding">
				<select class="form-control input-sm" style="width: 100%;" id="cboGOP_HOADON">
					<option value="0">Theo dịch vụ</option>
					<option value="1">Gộp hóa đơn</option>
					<option value="2">Gom nhóm dịch vụ</option>
				</select>
			</div>
		</div>
		<div class="col-md-4 low-padding" style="text-align: center;">
			<label class="checkbox-inline mgl5">
				<input type="checkbox" id="chkKH_DOANHNGHIEP" style="margin-top: 3px !important;">
				Khách hàng là doanh nghiệp
			</label>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Người lập</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm" style="width: 100%;" id="txtNGUOILAP" name="txtNGUOILAP" disabled>
			</div>
		</div>
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding required">
				<label class="mgl20">Ngày</label>
			</div>
			<div class="col-md-8 low-padding">
				<div class="input-group">
					<input class="form-control input-sm" valrule="Ngày thu,required|datetime" id="txtNGAYTHU" style="position: static;" name="txtNGAYTHU" data-mask="00/00/0000">
					<span class="btn input-group-addon datepicker-sm glyphicon glyphicon-calendar" id="sCalNGAYTHU" type="sCal" onclick="NewCssCal('txtNGAYTHU','ddMMyyyy','dropdown',true,'24',true)"></span>
				</div>
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Mẫu sổ</label>
			</div>
			<div class="col-md-8 low-padding">
				<select class="form-control input-sm" style="width: 100%;" id="cboMAU_SO" hidden>
				</select>
			</div>
		</div>
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Ký hiệu</label>
			</div>
			<div class="col-md-8 low-padding">
				<select class="form-control input-sm" style="width: 100%;" id="cboKY_HIEU">
				</select>
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Mã khách hàng</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm" style="width: 100%;" id="txtMAKHACHHANG">
			</div>
		</div>
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Họ và tên</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm" style="width: 100%;" id="txtTENBENHNHAN">
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Tên đơn vị</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm i-col-30" id="txtTKTENCONGTYBN">
				<input class="form-control input-sm i-col-70" id="txtTENCONGTYBN" style="width: 100%;" />
			</div>
		</div>
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Mã số thuế</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm" style="width: 100%;" id="txtMASOTHUE_CTYBN" name="txtMASOTHUE_CTYBN">
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Địa chỉ</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm" style="width: 100%;" id="txtDIACHI_CTYBN" name="txtDIACHI_CTYBN">
			</div>
		</div>
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Số tài khoản</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm" style="width: 100%;" id="txtSOTAIKHOAN" name="txtSOTAIKHOAN">
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Ngân hàng</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm" style="width: 100%;" id="txtTEN_NGANHANG" name="txtTEN_NGANHANG">
			</div>
		</div>
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Hình thức thanh toán</label>
			</div>
			<div class="col-md-8 low-padding">
				<select class="form-control input-sm" style="width: 100%;" id="cboHINHTHUCTHANHTOAN" hidden>
				</select>
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Email</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm" style="width: 100%;" id="txtEMAIL_CTYBN" name="txtEMAIL_CTYBN">
			</div>
		</div>
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Miễn giảm phiếu thu</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm money clsfloat" style="width: 100%;" id="txtMIENGIAM" valrule="Miễn giảm,trim_required|max_length[20]" disabled>
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Thành tiền</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm money clsfloat" style="width: 100%;" id="txtTHANHTIEN_TRUOC" valrule="Thành tiền,trim_required|max_length[20]">
			</div>
		</div>
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Tổng tiền thuế GTGT</label>
			</div>
			<div class="col-md-8 low-padding">
				<input class="form-control input-sm money clsfloat" style="width: 100%;" id="txtTONGTIENTHUE" valrule="Tổng thuế,trim_required|max_length[20]">
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Thành tiền sau thuế</label>
			</div>
			<div class="col-xs-8 low-padding">
				<input class="form-control input-sm money clsfloat" style="width: 100%;" id="txtTHANHTIEN" valrule="Thành tiền sau thuế,trim_required|max_length[20]">
			</div>
		</div>
		<div class="col-md-6 low-padding">
			<div class="col-md-4 low-padding">
				<label class="mgl20">Số tiền bằng chữ</label>
			</div>
			<div class="col-md-8 low-padding">
				<textarea class="form-control" rows="1" id="txtTIEN_CHU" disabled></textarea>
			</div>
		</div>
	</div>
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-2 low-padding">
			<label class="mgl20">Nội dung thu</label>
		</div>
		<div class="col-md-10 low-padding">
			<textarea class="form-control" rows="2" id="txtNOIDUNGTHU"></textarea>
		</div>
	</div>
	<div class="col-md-12 low-padding">
		<table id="grdDichvu"></table>
		<div id="pager_grdDichvu"></div>
	</div>
	<div class="col-xs-12 low-padding mgb3 mgt3" style="text-align: center;">
		<!-- // L2PT-119389 start -->
		<button type="button" class="btn btn-default btn-primary" id="btnLuu">
			<span class="glyphicon glyphicon-send"></span>
			Lưu
		</button>
		<button type="button" class="btn btn-default btn-primary" id="btnLuuVaPhatHanh">
			<span class="glyphicon glyphicon-send"></span>
			Lưu và Phát hành
		</button>
		<button type="button" class="btn btn-default btn-primary" id="btnPhatHanh" disabled>
			<span class="glyphicon glyphicon-send"></span>
			Phát hành
		</button>
		<!-- // L2PT-119389 start -->
		<button type="button" class="btn btn-sm btn-primary" id="btnXml" disabled>
			<span class="glyphicon glyphicon-search"></span>
			XML Hóa đơn
		</button>
		<!-- IT360-174199 start -->
		<button type="button" class="btn btn-sm btn-primary" id="btnXemHD" disabled>
			<span class="glyphicon glyphicon-search"></span>
			Xem hóa đơn
		</button>
		<button type="button" class="btn btn-default btn-primary" id="btnInHD" disabled>
			<span class="glyphicon glyphicon-print"></span>
			In bảng kê chi tiết
		</button>
		<!-- IT360-174199 end -->
		<button type="button" class="btn btn-sm btn-primary" id="btnClose">
			<span class="glyphicon glyphicon-remove"></span>
			Thoát
		</button>
	</div>
</div>
<!-- // L2PT-119389 start -->
<div id="dlgXML" style="display: none" width="550px">
	<div class="col-md-12 low-padding mgt3">
		<div class="col-md-12 low-padding">
			<div class="" rows="50" style="width: 100%;" id="divXML" disabled></textarea>
		</div>
	</div>
</div>
<!-- // L2PT-119389 end -->
<script>
	var user_id = '{user_id}';
	var user_type = '{user_type}';
	var province_id = '{province_id}';
	var dept_id = '{dept_id}';
	var subdept_id = '{subdept_id}';
	var user_name = '{user_name}';
	var uuid = '{uuid}';
	initRest(uuid, "/vnpthis");
	initAjax("/vnpthis");
	ajaxSvc.register("InvoicesWS");
	var _opts = new Object();
	_opts._uuid = uuid;
	_opts._user_name = user_name;
	_opts._deptId = dept_id;
	_opts.subdept_id = subdept_id;
	var data;
	var khoaid = '{dept_id}';
	var mode = '{showMode}';
	var hospital_id = '{hospital_id}';
	if (mode == 'dlg') {
		parent.DlgUtil.tunnel(DlgUtil.moveEvent);
		data = EventUtil.getVar("dlgVar");
		_opts.list_phieuthuid = data.list_phieuthuid;
		_opts.mabenhnhan = data.mabenhnhan;
		_opts.tenbenhnhan = data.tenbenhnhan;
		_opts.miengiam = data.miengiam;
		_opts.diachi = data.diachi;
		_opts.LOAITIEPNHANID = data.LOAITIEPNHANID;
		_opts.LOAITTDV = data.LOAITTDV;
	}
	var form = new VPI02G008_PhatHanhHoaDonDlg(_opts);
	form.load();
</script>

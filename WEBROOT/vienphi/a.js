/*
 Mã màn hình  : VPI01T001
 File mã nguồn : VPI01T001_thuvienphi.js
 <PERSON><PERSON><PERSON> đích  : Thu tiền dịch vụ
 Tham số vào :
 <PERSON><PERSON><PERSON><PERSON> lập tr<PERSON><PERSON> cập nh<PERSON>				 18/06/2021		comment
*/
var _THUTIEN = 1;
var _TAMUNG = 3;
var _HOANUNG = 2;
var _THUTHEM = 6;
var _HOADONBM = 7;
var _HOANDICHVU = 8;
var _PHIEUCHI = 12; //L2PT-24300
var _HOADONBHBL = 13; //L2PT-74042
var _DT_VIENPHI = 2;
var _SL_VIENPHI = 8;
var _DT_DICHVU = 3;
var _SL_DICHVU = 3;
function VPI01T001(_opts) {
	var _sql_get_xml_checkout = "VPI02G001.03";
	var _gridSQL_BN = "VPI01T001.01.RG"; //L2PT-24613
//	var _gridSQL_DV="VPI01T001.02";	
	var _gridSQL_PT = "VPI01T001.03.RG";// L2PT-32061
	var _sql_sophieuthu = "VPI01T001.13.RG"; // L2PT-34420
	var _sql_dagiaodich = "VPI01T001.06";
	var _sql_duyetvienphi = "VPI01T001.08";
	var _sql_huyphieuthu = "VPI01T001.09";
	var _sql_thuvienphi = "VPI01T001.10";
	var _sql_thongtintiepnhan = "VPI01T001.11";
//   var _sql_tinhtien = "VPI01T001.07";
//	var _sql_ds_maubenhpham ="VPI01T001.16";
//	var _sql_noidungthu = "VPI01T001.17";
//    var _sql_tinhtien_bhyt = "VPI01T001.04";
//	var _sql_tinhtongtien = "VPI01T001.05";	
//	var _sql_ds_dtdv ="VPI01T001.15";
	var _sql_themMST = "VPI01T001.12";
	var _sql_tt_phieuthu = "VPI01T001.14";
	var _gridId_BN = "grdDSBenhNhan";
	var _gridId_DV = "grdDSDichVu";
	var _gridId_DV2 = "grdDSDichVu2";
	var _gridId_DV3 = "grdDSDichVu3";
	var _gridId_DV4 = "grdDSDichVu4";
	var _gridId_PT = "grdDSPhieuThu";
	var _gridId_PT2 = "grdDSPhieuThu2";
	var _ma_timkiem = "Mã bệnh nhân,MABENHNHAN,90,0,f,l;";
	var _focus_element = "#gs_MABENHNHAN";
	var _gridHeader_BN = "";
	var causer = '';//TuyenDV_BVTM-3114
	var capassword = '';//TuyenDV_BVTM-3114
	var smartcauser = '';
	var catype = '';
	var kieuky = '';
	var bnky = '';
	var isKyTocken = false;
	var lstParamKyCa = [];
	var isKySo = false;
	// L2PT-28712 thêm cột đã xóa DVKB
	// L2PT-940 thêm cột TACHHOADON
	// L2PT-13442 thêm cột BNCCT
	// L2PT-13822 thêm cột DON_GIA_BH
	// L2PT-15673: định dạng số cột TIEN_BNCCT
	// L2PT-32078 thêm NHOM_MABHYT_ID
	// L2PT-34420 thêm TYLE_VAT
	// L2PT-42747 chỉnh sửa UX
	// L2PT-74790 L2PT-67262 thêm cột TRANGTHAIKHAMBENH
	// L2PT-103281 thêm cột SOLUONG_NHAN, SOLUONG_TRA
	// L2PT-112926 thêm cột LOAIPHIEUMAUBENHPHAM
	var _gridHeader_DV = "id,DICHVUKHAMBENHID,0,0,t,l;" + "Tiếp nhận ID,TIEPNHANID,0,0,t,l;" + "Phiếu thu id,PHIEUTHUID,0,0,t,l;" + "Khoa,KHOA,0,0,t,l;" + "Khoa id,KHOAID,0,0,t,l;"
			+ "Phòng id,PHONGID,0,0,t,l;" + "Khám bệnh id,KHAMBENHID,0,0,t,l;" + "BHYT dịch vụ,BHYT_DV,0,0,t,l;" + "Đồng chi trả,TIEN_BHYT_BNTT,0,0,t,l;" + "Loại đối tượng id,LOAIDOITUONG,0,0,t,l;"
			+ "Mẫu bệnh phẩm id,MAUBENHPHAMID,0,0,t,l;" + "Loại nhóm mẫu bệnh phẩm id,LOAINHOMMAUBENHPHAM,0,0,t,l;" + "Khoản mục ID,KHOANMUCID,0,0,t,l;" + "Mã khoản mục,MAKHOANMUC,0,0,t,l;"
			+ "Tên khoản mục,TENKHOANMUC,0,0,t,l;" + "DTBNID,DOITUONGBENHNHANID,100,0,t,l;" + "Nhóm thanh toán,NHOMTHANHTOAN,120,0,t,l;" + "Đối tượng dịch vụ,DOITUONGDV,0,0,t,l;"
			+ "Đối tượng,DOITUONG,120,0,t,l;" + "Nhóm bhyt,NHOM_MABHYT,0,0,t,l;" + "Đã thu tiền,DATHUTIEN,90,0,t,l;" + "STT,RN,30,0,f,l;" + "Tên dịch vụ,TENDICHVU,165,0,f,l;"
			+ "Ghi chú,LOAIPHIEUMAUBENHPHAM,60,0,t,l;" + "SL nhận,SOLUONG_NHAN,60,decimal!3,t,l;" + "SL trả,SOLUONG_TRA,60,decimal!3,t,l;" + "SL,SOLUONG,40,decimal!3,e,l;"
			+ "SL_OLD,SOLUONG_OLD,30,decimal!3,t,l;" + "Giá tiền,TIENDICHVU,75,decimal!3,f,r;" + "Giá BH,DONGIA_BHYT,75,decimal!3,f,r;" + "Giá BH,DON_GIA_BH,90,decimal!3,f,r;"
			+ "Thành tiền,THANHTIEN,75,decimal!2,t,r;" + "BHYT trả,TIEN_BHYT_TRA,75,decimal!2,f,r;" + "BNCCT,TIEN_BNCCT,75,decimal!2,f,r;" + "BN trả,THUCTHU,75,decimal!2,f,r;"
			+ "TL %,TYLE_BHYT_TRA,50,0,f,r;" + "% mg,TYLE_MIENGIAM,50,0,e,r;" + "% mg,TYLE_MIENGIAM_OLD,0,0,t,r;" + "Miễn giảm,TIEN_MIENGIAM,75,decimal!2,f,r;" + "Tỷ lệ dịch vụ,TYLE_DV,0,0,t,l;"
			+ "Version,VERSION_OLD,0,0,t,l;" + "Tỷ lệ thẻ - dịch vụ,TYLE,0,0,t,l;" + "Tỷ lệ miễn giảm thẻ,TYLEMIENGIAM,0,0,t,l;" + "Yêu cầu hoàn,YC_HOAN,0,0,t,l;" + "Vật tư 04,VATTU04,0,0,t,l;"
			+ "Đã xóa,DAXOA,0,0,t,l;" + "TACHHOADON,TACHHOADON,0,0,t,l;" + "VAT,TYLE_VAT,50,0,f,l;" + "nhóm mã bhyt id,NHOM_MABHYT_ID,0,0,t,l;" + "DICHVUID_ORG,DICHVUID_ORG,0,0,t,l"
			+ ";Trạng thái KB,TRANGTHAIKHAMBENH,0,0,t,l";
	// L2PT-11665 start: tách riêng header cho popup duyệt CLS
	var _gridHeader_DV_CLS = " ,DUYET_CLS,20,0,ns,l;" + "id,DICHVUKHAMBENHID,0,0,t,l;" + "Loại nhóm mẫu bệnh phẩm id,LOAINHOMMAUBENHPHAM,0,0,t,l;" + "Đã thu tiền,TRANGTHAIDICHVU,90,0,t,l;"
			+ "Số phiếu,SOPHIEU,90,0,f,l;" + "Nhóm BHYT,TEN_NHOM,120,0,f,l;" + "Tên dịch vụ,TENDICHVU,256,0,f,l;" + "Ngày chỉ định,NGAYMAUBENHPHAM,110,0,f,l,0;"
			+ "Loại thanh toán,LOAITHANHTOAN,120,0,f,l;" + "ĐVT,DVT,60,0,f,l;" + "SL,SOLUONG,40,decimal!3,e,l;" + "Giá tiền,TIENDICHVU,90,decimal!3,f,r;" + "Tỷ lệ(%),TYLE_DV_100,60,0,f,r;"
			+ "Thành tiền,THANHTIEN,90,number,f,r;" + "BHYT trả,TIEN_BHYT_TRA,90,number,f,r;" + "BN trả,THUCTHU,90,number,f,r;" + "Tỷ lệ dịch vụ,TYLE_DV,0,0,t,l;"
			+ "Miễn giảm,TIEN_MIENGIAM,80,number,f,r;" + "id,DADUYETTHUCHIENCANLAMSANG,0,0,t,l";
	// L2PT-11665 end
	//tuyennx_edit_start_20171018 yc L2DKBD-103
	//L2PT-18031: thêm cột SYNC_FLAG
	//L2PT-27253: thêm cột người phát hành HDDT
	//L2PT-34526: thêm cột Người chuyển đổi HĐ
	//L2PT-10049: thêm cột LOAIPHIEUTHU Loại phiếu thu
	//L2PT-23697: thêm cột LOAITIEPNHANID
	//BVTM-5728: thêm cột HINHTHUCTHANHTOAN, TEN_HTTT
	//L2PT-53911 : thêm NGAYPHATHANH_HDDT
	// L2PT-68665: lam lai ICON
	// L2PT-84833: thêm TEN_HTTT_HUY
	// L2PT-118724: thêm ICON_KSPT, FLAG_CA
	var _gridHeader_PT = " ,ICON_PT,20,0,ns,l;" + " ,ICON_YCH,20,0,ns,l;" + " ,ICON_KSPT,20,0,ns,l;" + "id,PHIEUTHUID,100,0,t,l,0;" + "id_org,PHIEUTHUID_ORG,100,0,t,l,0;"
			+ "id người dùng,NGUOIDUNGID,100,0,t,l,0;" + "id người hủy,NGUOIDUNGID_HUYPHIEU,100,0,t,l,0;" + "Mặc định,MACDINH,90,0,t,l,0;" + "Đã hủy phiếu,DAHUYPHIEU,90,0,t,l,0;"
			+ "Hủy giữ số,HUYGIUSO,90,0,t,l,0;" + "Số phiếu giữ,SOPHIEU_GIU,90,0,t,l,0;" + "Nhóm phiếu thu id,NHOMPHIEUTHUID,90,0,t,l,0;" + "Mã nhóm phiếu thu,MANHOMPHIEUTHU,90,0,t,l,0;"
			+ "Số phiếu từ,SOPHIEUTO,90,0,t,l,0;" + "Số phiếu đến,SOPHIEUFROM,90,0,t,l,0;" + "Số phiếu khóa từ,KHOASOPHIEUTU,90,0,t,l,0;" + "Tổng tiền,TONGTIEN,80,decimal!2,t,r,0;"
			+ "Tiếp nhận id,TIEPNHANID,100,0,t,l,0;" + "Mã phiếu thu,MAPHIEUTHU,80,0,f,l,0;" + "Số HĐĐT,INVOICES_NUMBER,80,0,f,l,0;" + "Số tiền,DATRA,80,decimal!2,f,r,0;"
			+ "Loại phiếu thu,LOAIPHIEUTHU,90,0,t,l,0;" + "Loại phiếu thu id,LOAIPHIEUTHUID,80,0,t,l,0;" + "Loại phiếu,LOAIPHIEUTHUID_2,80,0,f,l,0;" + "Ngày thanh toán,NGAYTHU,120,0,f,c,0;"
			+ "SYNC_FLAG,SYNC_FLAG,80,0,t,c,0;" + "Trạng thái,TRANGTHAI_HDDT,80,0,f,c,0;" + "Người thu,NGUOITHU,100,0,f,l,0;" + "HTTT,HINHTHUCTHANHTOAN,100,0,t,l,0;"
			+ "Hình thức thanh toán,TEN_HTTT,130,0,f,l,0;" + "Người phát hành,NGUOIDUNGID_GUIHDDT,100,0,t,l,0;" + "Người phát hành HĐ,NGUOIGUIHDDT,140,0,f,l,0;"
			+ "Ngày phát hành HĐ,NGAYPHATHANH_HDDT,120,0,f,l,0;" + "Người chuyển đổi HĐ,NGUOICHUYENDOI,140,0,f,l,0;" + "Ngày hoàn phiếu,NGAYHUY,120,0,f,l,0;"
			+ "Người hoàn phiếu,NGUOIHUY,180,0,f,l,0;" + "Lý do hủy,LYDOHUYPHIEU,100,0,f,l,0;" + "HTTT hủy,TEN_HTTT_HUY,130,0,f,l,0;" + "Log Phiếu thu,PHIEUTHULOG,100,0,t,l,0;"
			+ "TENCONGTYBN,TENCONGTYBN,100,0,t,l,0;" + "DIACHI_CTYBN,DIACHI_CTYBN,100,0,t,l,0;" + "MASOTHUE_CTYBN,MASOTHUE_CTYBN,100,0,t,l,0;" + "TREOPHIEU,TREOPHIEU,100,0,t,l,0;"
			+ "SOTHANG_TREO,SOTHANG_TREO,100,0,t,l,0;" + "DAHOAN,DAHOAN,100,0,t,l,0;" + "YC_HOAN,YC_HOAN,100,0,t,l,0;" + "Loại tiếp nhận,LOAITIEPNHANID,100,0,t,l,0;"//L2PT-10049 // L2PT-23697
			+ "DADUYET,DADUYET,100,0,t,l,0;" + "FLAG_CA,FLAG_CA,100,0,t,l,0";
	var _gridHeader_MG = "LOAI,LOAI,100,0,t,l,0;" + "Tên,TEN,150,0,f,l,0;" + "Số tiền,SOTIEN,50,0,e,r,0";
	// L2PT-24175: thêm cột MADICHVU
	//L2PT-32078
	var _gridHeader_MGNDV = "ID,NHOM_MABHYT_ID,100,0,t,l,0;" + "Mã dịch vụ,MADICHVU,80,0,t,l,0;" + "Tên nhóm,GHICHU,80,0,f,l,0;" + "Tỷ lệ,TYLEMG_CHUNG,40,0,e,r,0;" + "TL BHYT,TYLEMG_BHYT,40,0,e,r,0;"
			+ "TL viện phí,TYLEMG_VIENPHI,40,0,e,r,0;" + "TL dịch vụ,TYLEMG_DICHVU,40,0,e,r,0";// L2PT-2874
	//L2PT-19304 start
	var _gridHeader_HTTT = "LOAI,TRANGTHAI_ID,100,0,t,l,0;" + "Loại HTTT,TEN_TRANGTHAI,100,0,f,l,0;" + "Số tiền,SOTIEN,50,decimal!2,e,r,0;" + "Đối tượng,DOITUONG,128,0,f,l,ES;"
			+ "Loại,LOAI,128,0,t,l,0";
	//L2PT-19304 end
	//tuyennx_edit_end_20171018 
	var _khoa_id = -1;
	var _phong_id = -1;
	var _benhnhan = new Object;
	var flagLoading = false;
	var _fl_tinh = true;
	var _chot = false;
	var _phieuthuid = -1;
	rowIdMarked = -1;
	rowIdMarked_DV = -1;
	rowIdMarked_PT = -1;
	var _tiepnhanid = -1;
	var _loaiphieuthu = _THUTIEN;
	var _vpData = new Object();
	var _phieuInfo = [];
	var _arrMG = [];
	var _DMMG = [];
	//L2PT-19304 start
	var _arrHTTT = [];
	var _DM_HTTT = [];
	//L2PT-19304 end
	var _tien_hoadon = 0;
	var obj = new Object();
	var _ds_nhomtt = [];
	var _dv_sai = [];
	var _khoa = "";
	var _check_congkham = false;
	var _dsSo = [];
	var _dsSo_2 = [];
	var obj_2 = new Object();
	var VP_DUYET_BH_KHI_DUYET_KETOAN = 0;
	var VP_GUI_DULIEU_KHIDUYET = 0;
	var VPI_GUI_BH = 0;
	var HIS_KHONG_TAMUNG_NGOAITRU = 1;
	var VP_TACH_HOADON = 0;
	var VP_THU_BHYT_NGOAITRU_CHUADBA = 0;
	var HIS_IN_HOADONCHITIET = 0;
	var HIS_FOCUS_MABN = -1;
	var HIS_TIMKIEM_VIENPHI = 0;
	var VPI_SOTIEN_TAMUNGNTU = 0;
	var VPI_KHOIPHUCPHIEU = 0;
	var VPI_HOANDICHVU = 0;
	var VPI_DUYETBH_THANHTOANNTU = 0;
	var VPI_SOLAN_INHOADON = 0;
	var VPI_SOLAN_INTAMUNG = 0;
	var VPI_KIEUIN_HOADON = 0;
	var VPI_QUYTRINH_VIENPHI = 0;
	var VPI_DAY_HOADONDT = 0;
	var VPI_KIEMTRA_TYLE = 0;
	var VPI_TAMUNG_CHONHAPKHOA = 0;
	var VPI_TUDONGTACH_HOADON = 0;
	var VP_INPHOI_DONGBA = 0;
	var VPI_PHIEUTHU_DIKEM_HOADON = 0;
	var VPI_HD_KHONGDONG = 0;
	var VPI_IN_PHIEUTHU = 0;
	var VPI_DUYETKT_THUVPDBA = 0;
	var VPI_HOANDV = 0;
	var VPI_LUU_IN = 0;
	var VPI_LYDO_MIEGIAM = 0;
	var VPI_TACHHD_NOITRU = 0;
	var VPI_THUTIEN_NTU = 0;
	var VPI_GDKT_XT_NGT = 0;
	var VPI_CHINH_NGAYTHU = 0;
	var VPI_XACNHAN_THUTIEN = 0;
	var VPI_DEC_FORMAT = 0;
	var VPI_GUI_MA_LOAI_KCB = 0;
	var VPI_GRID_PHIEUTHU = 0;
	var VPI_XACNHAN_TAOPHIEU = 0;
	var VPI_XACNHAN_HOANPHIEU = 0;
	var VPI_QUYEN_GUIHDDT = 0;
	var VPI_CH_INPHOI = 0;
	var VPI_THOIGIAN_DUYETKT = 0;
	var VPI_KTKQ_GUI = 0;
	var VPI_QT_IN_PHIEUTHU = 0;//L2PT-10159
	var VPI_HOANUNG_NGT = 0;
	var VPI_GDVP_VPC = 0;//L2PT-8100
	var VPI_HIEN_IDPOST = 0;//L2PT-7489
	var VPI_SOTIEN_TAMUNGNGT = 0;//L2PT-9097
	var VPI_LUU_HD_THUKHAC = 0;//L2PT-10049
	var VPI_KHOA_HDDT = 0;//L2PT-14008
	var VPI_XACNHAN_GODUYETKT = 0;//L2PT-16007
	var VPI_HIENBANGKE_DUYETKT = 0;//L2PT-16206
	var VPI_DUYET_THUOC = 0;// L2PT-17330
	var VPI_VP_PNT = 0;// L2PT-22759
	var VPI_PAY_PMM_URL = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_PAY_PMM_URL'); //L2PT-31437 
	var VPI_CHANDUYETKT_BNTRONVIEN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_CHANDUYETKT_BNTRONVIEN'); // L2PT-28283
	var VPI_GTMD_GHICHU = 0; // L2PT-28714 
	//tuyennx_add_start
	var INVOICES_URL_IMPORT = 0;
	var INVOICES_URL_VIEW = 0;
	var INVOICES_URL_CANCEL = 0;
	var INVOICES_WS_USER = 0;
	var INVOICES_WS_PWD = 0;
	var INVOICES_WS_USER_ACC = 0;
	var INVOICES_WS_PWD_ACC = 0;
	var INVOICES_WS_PATTERN = 0;
	var INVOICES_WS_SERIAL = 0;
	var INVOICES_IMPORT_AUTO = 0;
	var INVOICES_WS_TAX_CODE = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_WS_TAX_CODE'); // L2PT-5786
	var _invoices_content_type = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_CONTENT_TYPE');
	var VPI_HDDT_VT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_HDDT_VT'); // L2PT-912
	//HUONGPV ADD
	var VPI_IN_HOADON_BANHANG = 0;
	//tuyennx_add_end 
	// SONDN 
	var VPI_HIENTHI_GOIKHAM = 0;
	// END SONDN
	var VPI_CH_IN_INPHOI = 0;
	var VPI_ENABLE_THUKHAC = 0;
	var _hetphieu_2 = false;
	var _hetphieu = false;
	var _co_dv = false;
	var _phieutamungid = null;
	var _arr_DV_CT = [];
	var v_mode = 0;
	var _guihddt = 0;
	var _thu_khac = 0;
	var _thu_nhanh = false; // L2PT-114388
	var _hddt = -1;
//	var that=this;
	var showLyDoMgDv = false;//HaNv_31032020: Bổ sung lý do miễn giảm(bắt buộc) khi nhập miễn giảm theo cấu hình hiển thị - L2PT-18597
	var _allow_thukhac = (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_ALLOW_THUKHAC') == '1') ? true : false;//HaNv_23042020 - L2PT-20080
	var _kySoCH = jsonrpc.AjaxJson.ajaxCALL_SP_I("COM.CAUHINH.THEOMA", "HIS_SUDUNG_KYSO_KYDIENTU");//TuyenDV_BVTM-2828
	var VPI_XACNHAN_GUIHDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_XACNHAN_GUIHDDT');// L2PT-7432
	var VPI_THUTIEN_CHUADBA = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_THUTIEN_CHUADBA');// L2PT-10164
	var VPI_XN_GUILAI_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_XN_GUILAI_HDDT') // BVTM-7213;L2PT-11280;L2PT-11279 
	var VPI_CB_GUILAI_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_CB_GUILAI_HDDT') // L2PT-11280;L2PT-11279
	var VPI_LOAIPHIEU_MACDICH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_LOAIPHIEU_MACDICH'); // BVTM-4638 // L2PT-13637
	var VPI_TT_GODUYETKT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_TT_GODUYETKT'); //L2PT-10597
	var VPI_GDVP_NTI = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_GDVP_NTI'); //L2PT-14685
	var cf_add_ttcongty = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_ADD_TTCONGTY'); //L2PT-18053
	var INVOICE_XML_TYPE = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICE_XML_TYPE'); // L2PT-18916
	var VPI_VIEW_HDDT_DELAY = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_VIEW_HDDT_DELAY'); // L2PT-19351
	var VPI_SINH_HDKD = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_SINH_HDKD'); // L2PT-21870
	var VPI_NHAP_GHICHU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_NHAP_GHICHU'); // L2PT-28406
	var VPI_VIEWHD_FUNC = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_VIEWHD_FUNC'); // L2PT-22840
	var VPI_DAY_HDDT_PH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_DAY_HDDT_PH'); // L2PT-22748
	var VPI_SOLAN_INPHIEUTHU = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_SOLAN_INPHIEUTHU'); // L2PT-24122
	var INVOICES_VT_FMTNBR = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_VT_FMTNBR'); // L2PT-25725
	var VPI_HDDT_VIETTEL_V2 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_HDDT_VIETTEL_V2'); // L2PT-25725
	var VPI_MAU_DSBN_THUVP = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_MAU_DSBN_THUVP'); // L2PT-27740
	var VPI_DUYETKT_CHUYENQT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_DUYETKT_CHUYENQT'); // L2PT-1401 // L2PT-47548
	var VPI_WAIT_MSG = parseInt(jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_WAIT_MSG')); // L2PT-12176 L2PT-54506
	var VPI_HIENTHI_QRCODE = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_HIENTHI_QRCODE'); // L2PT-61290 L2PT-28379
	var INVOICES_MISA_OPTIONUSER = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_MISA_OPTIONUSER'); // L2PT-98874 L2PT-14600
	var INVOICES_MISA_FMTNBR = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_MISA_FMTNBR'); // L2PT-98874 L2PT-14600
	var VPI_POS_ID_MD = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH.ND', 'VPI_POS_ID_MD'); // L2PT-110377
	var fConfig = new Object(); // L2PT-33584
	var _config_hddt = [];// L2PT-16575
	var dsCH = null;// L2PT-16575// L2PT-18357
	var _codv_khongvat = true; // L2PT-34420
	var isLuuVaPhatHanhHDDT = false; // L2PT-33791
	var isKySoTamUng = false; // L2PT-53446
	var QRCODEBASE64STR = ""; // L2PT-71931
	this.load = doLoad;
	function doLoad() {
		$.i18n().load(i18n_his.CtlSql);
		$.i18n().load(i18n_his.err_code);
		$.i18n().load(i18n_his.com_msg);
		$.i18n().load(i18n_ngoaitru.com_msg);
		$.i18n().locale = (typeof opt.lang !== "undefined") ? opt.lang : "vn";
		var _options = $.extend({}, _opts);
//		var uuid = _options._uuid;
		var _param = _options._param;
		_hospital_id = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'OPT_HOSPITAL_ID');
		_user_name = _param[7];
		_user_id = _param[1];
		_sch = _param[4];
		_hospital_code = _param[5];
		_phong_id = _param[6];
		_khoa_id = _param[8];
		loadRISConfig(); // L2PT-88392
		_initControl();
		_bindEvent();
		//_setEvent(); // L2PT-80276 // L2PT-90923
	}
	function _initControl() {
		v_mode = getParameterByName('v_mode', window.location.search.substring(1));
		// L2PT-33584 start
		// lấy cấu hình 
		// L2PT-39523 : VPI_GEN_QRCODE_MERCHANT
		// L2PT-31925 : VPI_TUDONG_GUIHDDT_KTM
		// L2PT-44297 : VPI_LAMTRON_TTKTM
		var str_ch = "VPI_FN_IMPORT_HDDT;VPI_CBDV_YEUCAU;VPI_GEN_QRCODE_MERCHANT;VPI_TUDONG_GUIHDDT_KTM;VPI_LAMTRON_TTKTM";
		str_ch += ';VPI_SERIAL_PATTERN_HDDT';// L2PT-44489: cấu hình lấy serial, pattern
		str_ch += ';VPI_THUVP_DUYETCLS'; // L2PT-46202
		str_ch += ';VPI_HDDT_HIEN_GDIN'; // L2PT-45621
		str_ch += ';VPI_HDDT_VIETTEL_V3'; // L2PT-46267
		str_ch += ';VPI_CHEDO_GOILOA'; // L2PT-43628
		str_ch += ';VPI_INPHIEU_DAHUY'; // L2PT-52868
		str_ch += ';VPI_CBDBHYT_KHACNGAY'; // L2PT-51016 
		str_ch += ';VPI_LOAIHD_TUDONGGUI'; // L2PT-51652
		str_ch += ';VPI_CHOTHUPHI_LCD'; // L2PT-50644 TTLINH
		str_ch += ';VPI_GUIHDDT_KHONGDONG'; // L2PT-54003
		str_ch += ';VPI_BHXH_KT_NGAYGUI'; // L2PT-55208
		str_ch += ';VPI_DUYETKT_LOAD_DSBN'; // L2PT-69024
		str_ch += ';VPI_CANHBAO_MGDV_DKT'; // L2PT-71501
		str_ch += ';VPI_LAMTRON_NDTHU'; // L2PT-69168
		str_ch += ';VPI_TUDONG_KIEMTRA_QRCODE'; // L2PT-71931
		str_ch += ';VPI_THUVP_THEOKHOA'; // L2PT-74790 L2PT-67262
		str_ch += ';VPI_DSVP_TKTP'; // L2PT-73710
		str_ch += ';VPI_CHIHO_QRCODE'; // L2PT-80276
		str_ch += ';VPI_ID_NGANHANG_MD'; // L2PT-98242
		str_ch += ';VPI_ID_DVTT_MD'; // L2PT-131086
		str_ch += ';VPI_HUYHDDT_SINHBIENBAN'; // L2PT-95812
		str_ch += ';INVOICES_TYPE'; // L2PT-104997
		str_ch += ';VPI_SHOW_QRCODE_TITLE;VPI_SHOW_QRCODE_MESSAGE'; // L2PT-102404
		str_ch += ';VPI_SOLUONG_DSDV'; // L2PT-103281
		str_ch += ';VPI_HDDTVT_PATTERN'; // L2PT-109124
		str_ch += ';VPI_PHIEU_KD'; // L2PT-113638
		str_ch += ';VPI_FCAPNHAT_PHIEUTHU'; // L2PT-114387
		str_ch += ';VPI_THUTIEN_NHANH'; // L2PT-114388
		str_ch += ';VPI_KIEMTRA_TTQR'; // L2PT-118765  
		str_ch += ';VPI_HDDT_TOKEN'; // L2PT-119034
		str_ch += ';VPI_DUYET_CHIHO'; // L2PT-118724
		str_ch += ';VPI_LOAD_DV'; // L2PT-128731
		var arrFConfig = jsonrpc.AjaxJson.ajaxCALL_SP_O('COM.DS_CAUHINH', str_ch);
		if (Array.isArray(arrFConfig) && arrFConfig.length > 0) {
			fConfig = arrFConfig[0];
		}
		// L2PT-33584 end
		// L2PT-18615 start: chuyen tu date sang datetime
		$('#txtTU').val(jsonrpc.AjaxJson.ajaxCALL_SP_S('GETSYSDATE', 'DD/MM/YYYY' + '$' + 31) + " 00:00:00");
		$('#txtDEN').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY') + " 23:59:59");
		// L2PT-18615 end
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("LOAIVIENPHI", '$');
		var str = '';
		var ds_loaivienphi = "";
		var loaivienphi;
		var tatca_vp = false;
		if (result.length > 0) {
			for (var i = 0; i < result.length; i++) {
				loaivienphi = result[i].LOAIVIENPHIID;
				if (loaivienphi == 100) {
					tatca_vp = true;
				} else {
					ds_loaivienphi += loaivienphi + ",";
				}
				str = str + "<option value=" + result[i].LOAIVIENPHIID + ">" + result[i].LOAIVIENPHI + "</option>";
			}
			if (!tatca_vp && ds_loaivienphi.length > 0) {
				ds_loaivienphi = ds_loaivienphi.slice(0, -1);
				str = "<option value=" + ds_loaivienphi + ">" + "-- Chọn --" + "</option>" + str;
			}
		}
		$('#cboDOITUONG').html(str);
		var result1 = jsonrpc.AjaxJson.ajaxCALL_SP_O("THUTHEO_DOITUONG", '$');
		var str1 = '';
		var ds_doituongbnid = "";
		var doituongbnid;
		var tatca = false;
		if (result1.length > 0) {
			for (var j = 0; j < result1.length; j++) {
				doituongbnid = result1[j].DTBNID;
				if (doituongbnid == 100) {
					tatca = true;
				} else {
					ds_doituongbnid += doituongbnid + ",";
				}
				str1 = str1 + "<option value=" + result1[j].DTBNID + ">" + result1[j].TEN_DTBN + "</option>";
			}
			if (!tatca && ds_doituongbnid.length > 0) {
				ds_doituongbnid = ds_doituongbnid.slice(0, -1);
				str1 = "<option value=" + ds_doituongbnid + ">" + "-- Chọn --" + "</option>" + str1;
			}
		}
		$('#cboDTBNID').html(str1);
		var result2 = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI_LAY_LOAIQUAY", _phong_id);
		var str2 = '';
		if (result2.length > 0) {
			for (var k = 0; k < result2.length; k++) {
				str2 = str2 + "<option value=" + result2[k].LOAI + ">" + result2[k].TEN + "</option>";
			}
		}
		$('#cboDOITUONGDV').html(str2);
		//L2PT-19304 start
		ComboUtil.getComboTag("cboHINHTHUCTHANHTOAN", "VPI.DM.HTTT", [], "", "", "sp", "", "");
		//L2PT-19304 end
		// L2PT-18615 start
		ComboUtil.getComboTag("cboKHOAKT", 'VPI.KHOA.KCB', [], "", {
			text : "Tất cả",
			value : -1
		}, 'sql', '', '');
		// L2PT-18615 end
		ComboUtil.getComboTag("cboNganHang", 'DMC149.06', "", "", "", 'sp', '', ''); // L2PT-98242
		// L2PT-73710 start
		if (fConfig.VPI_DSVP_TKTP == '1') {
			$('#divPHONGKT').show();
			loadComboPhong();
		}
		// L2PT-73710 end
		var _configArr = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.LAY.CAUHINH", '$');
		if (_configArr && _configArr.length > 0) {
			_config = _configArr[0];
			VP_DUYET_BH_KHI_DUYET_KETOAN = _config.VP_DUYET_BH_KHI_DUYET_KETOAN;
			VP_GUI_DULIEU_KHIDUYET = _config.VP_GUI_DULIEU_KHIDUYET;
			VPI_GUI_BH = _config.VPI_GUI_BH;
			HIS_KHONG_TAMUNG_NGOAITRU = _config.HIS_KHONG_TAMUNG_NGOAITRU;
			VP_TACH_HOADON = _config.VP_TACH_HOADON;
			VP_THU_BHYT_NGOAITRU_CHUADBA = _config.VP_THU_BHYT_NGOAITRU_CHUADBA;
			HIS_IN_HOADONCHITIET = _config.HIS_IN_HOADONCHITIET;
			HIS_THUNGAN_THUKHAC = _config.HIS_THUNGAN_THUKHAC;
			HIS_FOCUS_MABN = _config.HIS_FOCUS_MABN;
			HIS_TIMKIEM_VIENPHI = _config.HIS_TIMKIEM_VIENPHI;
			VPI_SOTIEN_TAMUNGNTU = _config.VPI_SOTIEN_TAMUNGNTU;
			VPI_KHOIPHUCPHIEU = _config.VPI_KHOIPHUCPHIEU;
			VPI_HOANDICHVU = _config.VPI_HOANDICHVU;
			VPI_DUYETBH_THANHTOANNTU = _config.VPI_DUYETBH_THANHTOANNTU;
			VPI_SOLAN_INHOADON = _config.VPI_SOLAN_INHOADON;
			VPI_SOLAN_INTAMUNG = _config.VPI_SOLAN_INTAMUNG;
			VPI_KIEUIN_HOADON = _config.VPI_KIEUIN_HOADON;
			VPI_QUYTRINH_VIENPHI = _config.VPI_QUYTRINH_VIENPHI;
			_SL_VIENPHI = _config.VPI_SL_VIENPHI;
			_SL_DICHVU = _config.VPI_SL_DICHVU;
			VPI_DAY_HOADONDT = _config.VPI_DAY_HOADONDT;
			VPI_KIEMTRA_TYLE = _config.VPI_KIEMTRA_TYLE;
			VPI_TAMUNG_CHONHAPKHOA = _config.VPI_TAMUNG_CHONHAPKHOA;
			VPI_TUDONGTACH_HOADON = _config.VPI_TUDONGTACH_HOADON;
			VP_INPHOI_DONGBA = _config.VP_INPHOI_DONGBA;
			VPI_PHIEUTHU_DIKEM_HOADON = _config.VPI_PHIEUTHU_DIKEM_HOADON;
			VPI_HD_KHONGDONG = _config.VPI_HD_KHONGDONG;
			VPI_IN_PHIEUTHU = _config.VPI_IN_PHIEUTHU;
			VPI_DUYETKT_THUVPDBA = _config.VPI_DUYETKT_THUVPDBA;
			VPI_HOANDV = _config.VPI_HOANDV;
			VPI_LUU_IN = _config.VPI_LUU_IN;
			VPI_LYDO_MIEGIAM = _config.VPI_LYDO_MIEGIAM;
			VPI_TACHHD_NOITRU = _config.VPI_TACHHD_NOITRU;
			VPI_THUTIEN_NTU = _config.VPI_THUTIEN_NTU;
			VPI_GDKT_XT_NGT = _config.VPI_GDKT_XT_NGT;
			VPI_CHINH_NGAYTHU = _config.VPI_CHINH_NGAYTHU;
			VPI_XACNHAN_THUTIEN = _config.VPI_XACNHAN_THUTIEN;
			VPI_DEC_FORMAT = _config.VPI_DEC_FORMAT;
			VPI_GUI_MA_LOAI_KCB = _config.VPI_GUI_MA_LOAI_KCB;
			VPI_GRID_PHIEUTHU = _config.VPI_GRID_PHIEUTHU;
			VPI_XACNHAN_TAOPHIEU = _config.VPI_XACNHAN_TAOPHIEU;
			VPI_XACNHAN_HOANPHIEU = _config.VPI_XACNHAN_HOANPHIEU;
			VPI_QUYEN_GUIHDDT = _config.VPI_QUYEN_GUIHDDT;
			VPI_CH_INPHOI = _config.VPI_CH_INPHOI;
			VPI_THOIGIAN_DUYETKT = _config.VPI_THOIGIAN_DUYETKT;
			VPI_KTKQ_GUI = _config.VPI_KTKQ_GUI;
			VPI_QT_IN_PHIEUTHU = _config.VPI_QT_IN_PHIEUTHU;//L2PT-10159
			VPI_HOANUNG_NGT = _config.VPI_HOANUNG_NGT;
			VPI_GDVP_VPC = _config.VPI_GDVP_VPC;//L2PT-8100
			VPI_HIEN_IDPOST = _config.VPI_HIEN_IDPOST;//L2PT-7489
			VPI_SOTIEN_TAMUNGNGT = _config.VPI_SOTIEN_TAMUNGNGT; //L2PT-9097
			VPI_LUU_HD_THUKHAC = _config.VPI_LUU_HD_THUKHAC;
			VPI_KHOA_HDDT = _config.VPI_KHOA_HDDT;//L2PT-14008
			VPI_XACNHAN_GODUYETKT = _config.VPI_XACNHAN_GODUYETKT;//L2PT-16007
			VPI_HIENBANGKE_DUYETKT = _config.VPI_HIENBANGKE_DUYETKT;//L2PT-16206
			VPI_DUYET_THUOC = _config.VPI_DUYET_THUOC;// L2PT-17330
			VPI_VP_PNT = _config.VPI_VP_PNT;// L2PT-22759
			//tuyennx_add_start
			INVOICES_URL_IMPORT = _config.INVOICES_URL_IMPORT;
			INVOICES_URL_VIEW = _config.INVOICES_URL_VIEW;
			INVOICES_URL_CANCEL = _config.INVOICES_URL_CANCEL;
			INVOICES_WS_USER = _config.INVOICES_WS_USER;
			INVOICES_WS_PWD = _config.INVOICES_WS_PWD;
			INVOICES_WS_USER_ACC = _config.INVOICES_WS_USER_ACC;
			INVOICES_WS_PWD_ACC = _config.INVOICES_WS_PWD_ACC;
			INVOICES_WS_PATTERN = _config.INVOICES_WS_PATTERN;
			INVOICES_WS_SERIAL = _config.INVOICES_WS_SERIAL;
			INVOICES_IMPORT_AUTO = _config.INVOICES_IMPORT_AUTO;
			VPI_IN_HOADON_BANHANG = _config.VPI_IN_HOADON_BANHANG;
			//tuyennx_add_end
			// SONDN 
			VPI_HIENTHI_GOIKHAM = _config.HIENTHIGOIKHAMVPI;
			// END SONDN ;
			VPI_CH_IN_INPHOI = _config.VPI_CH_IN_INPHOI;
			VPI_ENABLE_THUKHAC = _config.VPI_ENABLE_THUKHAC;
			VPI_HIENTHI_NGAYDUYET_VP = _config.VPI_HIENTHI_NGAYDUYET_VP; //L2PT-102868
			VPI_LCD_BNTHANHTOAN = _config.VPI_LCD_BNTHANHTOAN; //L2PT-102282
		}
		//L2PT-102282 start
		if (VPI_LCD_BNTHANHTOAN == '1') {
			$('#divHienThiTT').show();
		}
		//L2PT-102282 end
		//L2PT-8100 start
		if (VPI_GDVP_VPC == 0) {
			$('#divTAMUNG_VPC').hide();
			$('#divDANOP_VPC').hide();
		} else {
			$('#divTAMUNG').hide();
			$('#divDANOP').hide();
		}
		//L2PT-8100 end
		//L2PT-14685 start
		if (VPI_GDVP_NTI == 0) {
			$('#divNOPTHEM_NTI').hide();
			$('#divDANOP_NTI').hide();
		} else {
			$('#divNOPTHEM').hide();
			$('#divDANOP').hide();
		}
		//L2PT-14685 end
		//L2PT-7489 start // L2PT-31437 fix
		if (VPI_HIEN_IDPOST == 1) {
			$('#divIDPOST').show();
		} else {
			$('#divIDPOST').hide();
		}
		//L2PT-7489 end // L2PT-31437 fix
		//L2PT-18053 ttlinh start 
		if (cf_add_ttcongty == 1) {
			$("#divTenCongTy").addClass("required");
			$("#divDiaChiCongTy").addClass("required");
			$("#divMST").addClass("required");
			$("#txtTENCONGTYBN_HDDT").attr('valrule', 'Tên công ty(Đơn vị),required');
			$("#txtDIACHI_CTYBN_HDDT").attr('valrule', 'Địa chỉ công ty,required');
			$("#txtMASOTHUE_CTYBN_HDDT").attr('valrule', 'Mã số thuế,required');
		}
		//L2PT-18053 end
		//L2PT-7410 : thêm cột KHOAMOBA
		// L2PT-21579;L2PT-21838 start: lấy gridheader từ cấu hình menu
		var VPI_GRIDHEADER_DSBN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH.CLOB', 'VPI_GRIDHEADER_DSBN');
		if (VPI_GRIDHEADER_DSBN && VPI_GRIDHEADER_DSBN.length > 0 && VPI_GRIDHEADER_DSBN != 0) {
			_gridHeader_BN = VPI_GRIDHEADER_DSBN;
			// L2PT-30848 start
			if (HIS_TIMKIEM_VIENPHI == 0) {
				_focus_element = "#gs_MABENHNHAN";
			} else if (HIS_TIMKIEM_VIENPHI == 1) {
				_focus_element = "#gs_MAHOSOBENHAN";
			} else if (HIS_TIMKIEM_VIENPHI == 2) {
				_focus_element = "#gs_MATIEPNHAN";
			} else if (HIS_TIMKIEM_VIENPHI == 3) {
				_focus_element = "#gs_MAHOSOBENHAN";
			} else if (HIS_TIMKIEM_VIENPHI == 4) {
				_focus_element = "#gs_MABENHNHAN";
			} else {
				_focus_element = HIS_TIMKIEM_VIENPHI;
			}
			// L2PT-30848 end
		}
		// L2PT-21579;L2PT-21838 end
		// L2PT-42747 chỉnh sửa UX
		// L2PT-68665: lam lai ICON
		else if (HIS_TIMKIEM_VIENPHI == 0) {
			_focus_element = "#gs_MABENHNHAN";
			_gridHeader_BN = " ,ICON,20,0,ns,l;" + " ,ICON,20,0,ns,l;" + " ,ICON_KS,20,0,ns,l;" + "Ký số BK,KYSO_BANGKE,20,0,t,l;" + "id,TIEPNHANID,0,0,t,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;"
					+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "Mã bệnh nhân,MABENHNHAN,90,0,f,l;" + "Họ tên,TENBENHNHAN,120,0,f,l;"
					+ "Địa chỉ,DIACHI,185,0,f,l,ES;" + "Ngày sinh,NGAYSINH,70,0,f,l;" + "Tuổi,TUOI,65,0,f,l;" + "GT,GIOITINH,35,0,f,l;" + "Mã BHYT,MA_BHYT,110,0,f,l;"
					+ "Người duyệt - Ngày duyệt KT,NGUOIDUYET,180,0,f,l;" + "Khoa,KHOA,100,0,f,l;" + "Phòng,PHONG,100,0,f,l;" + "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;"
					+ "Mã viện phí,MATIEPNHAN,85,0,f,l;" + "Vào viện,NGAYTIEPNHAN,120,0,f,l;" + "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "Khóa mở BA,KHOAMOBA,85,0,f,l;" + "Xác nhận BH,XACNHANBH,0,0,t,l;"
					+ "STT,STT,50,0,t,l;" + "NGUOIDUYETID,NGUOIDUYETID,50,0,t,l";
		} else if (HIS_TIMKIEM_VIENPHI == 1) { // L2PT-21579;L2PT-21838
			_focus_element = "#gs_MAHOSOBENHAN";
			_gridHeader_BN = " ,ICON,20,0,ns,l;" + " ,ICON,20,0,ns,l;" + " ,ICON_KS,20,0,ns,l;" + "Ký số BK,KYSO_BANGKE,20,0,t,l;" + "id,TIEPNHANID,0,0,t,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;"
					+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;" + "Họ tên,TENBENHNHAN,120,0,f,l;"
					+ "Địa chỉ,DIACHI,185,0,f,l,ES;" + "Ngày sinh,NGAYSINH,70,0,f,l;" + "Tuổi,TUOI,65,0,f,l;" + "GT,GIOITINH,35,0,f,l;" + "Mã BHYT,MA_BHYT,110,0,f,l;"
					+ "Người duyệt - Ngày duyệt KT,NGUOIDUYET,180,0,f,l;" + "Khoa,KHOA,100,0,f,l;" + "Phòng,PHONG,100,0,f,l;" + "Mã viện phí,MATIEPNHAN,85,0,f,l;"
					+ "Mã bệnh nhân,MABENHNHAN,90,0,f,l;" + "Vào viện,NGAYTIEPNHAN,120,0,f,l;" + "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "Khóa mở BA,KHOAMOBA,85,0,f,l;" + "Xác nhận BH,XACNHANBH,0,0,t,l;"
					+ "STT,STT,50,0,t,l;" + "NGUOIDUYETID,NGUOIDUYETID,50,0,t,l";
		} else if (HIS_TIMKIEM_VIENPHI == 2) {
			_focus_element = "#gs_MATIEPNHAN";
			_gridHeader_BN = " ,ICON,20,0,ns,l;" + " ,ICON,20,0,ns,l;" + " ,ICON_KS,20,0,ns,l;" + "Ký số BK,KYSO_BANGKE,20,0,t,l;" + "id,TIEPNHANID,0,0,t,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;"
					+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "Mã viện phí,MATIEPNHAN,85,0,f,l;" + "Họ tên,TENBENHNHAN,120,0,f,l;"
					+ "Địa chỉ,DIACHI,185,0,f,l,ES;" + "Ngày sinh,NGAYSINH,70,0,f,l;" + "Tuổi,TUOI,65,0,f,l;" + "GT,GIOITINH,35,0,f,l;" + "Mã BHYT,MA_BHYT,110,0,f,l;"
					+ "Người duyệt - Ngày duyệt KT,NGUOIDUYET,180,0,f,l;" + "Khoa,KHOA,100,0,f,l;" + "Phòng,PHONG,100,0,f,l;" + "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;"
					+ "Mã bệnh nhân,MABENHNHAN,90,0,f,l;" + "Vào viện,NGAYTIEPNHAN,120,0,f,l;" + "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "Khóa mở BA,KHOAMOBA,85,0,f,l;" + "Xác nhận BH,XACNHANBH,0,0,t,l;"
					+ "STT,STT,50,0,t,l;" + "NGUOIDUYETID,NGUOIDUYETID,50,0,t,l";
		} else if (HIS_TIMKIEM_VIENPHI == 3) {
			_focus_element = "#gs_MAHOSOBENHAN";
			_gridHeader_BN = " ,ICON,20,0,ns,l;" + " ,ICON,20,0,ns,l;" + " ,ICON_KS,20,0,ns,l;" + "Ký số BK,KYSO_BANGKE,20,0,t,l;" + "id,TIEPNHANID,0,0,t,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;"
					+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;" + "Mã bệnh nhân,MABENHNHAN,90,0,f,l;"
					+ "Họ tên,TENBENHNHAN,130,0,f,l;" + "Địa chỉ,DIACHI,185,0,f,l,ES;" + "Ngày sinh,NGAYSINH,70,0,f,l;" + "Tuổi,TUOI,65,0,f,l;" + "GT,GIOITINH,35,0,f,l;"
					+ "Mã BHYT,MA_BHYT,110,0,f,l;" + "Người duyệt - Ngày duyệt KT,NGUOIDUYET,180,0,f,l;" + "Mã viện phí,MATIEPNHAN,85,0,f,l;" + "Khoa,KHOA,100,0,f,l;" + "Phòng,PHONG,100,0,f,l;"
					+ "Vào viện,NGAYTIEPNHAN,120,0,f,l;" + "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "Khóa mở BA,KHOAMOBA,85,0,f,l;" + "STT,STT,50,0,t,l;" + "NGUOIDUYETID,NGUOIDUYETID,50,0,t,l";
		} else if (HIS_TIMKIEM_VIENPHI == 4) {
			_focus_element = "#gs_MABENHNHAN";
			_gridHeader_BN = " ,ICON,20,0,ns,l;" + " ,ICON,20,0,ns,l;" + " ,ICON_KS,20,0,ns,l;" + "Ký số BK,KYSO_BANGKE,20,0,t,l;" + "id,TIEPNHANID,0,0,t,l;" + "TTTN,TRANGTHAITIEPNHAN,0,0,t,l;"
					+ "TTTN_BH,TRANGTHAITIEPNHAN_BH,0,0,t,l;" + "TTTN_VP,TRANGTHAITIEPNHAN_VP,0,0,t,l;" + "Mã bệnh nhân,MABENHNHAN,90,0,f,l;" + "Họ tên,TENBENHNHAN,120,0,f,l;"
					+ "Khoa,KHOA,100,0,f,l;" + "Phòng,PHONG,100,0,f,l;" + "Mã BHYT,MA_BHYT,110,0,f,l;" + "Người duyệt - Ngày duyệt KT,NGUOIDUYET,180,0,f,l;" + "Địa chỉ,DIACHI,185,0,f,l,ES;"
					+ "Ngày sinh,NGAYSINH,70,0,f,l;" + "Tuổi,TUOI,65,0,f,l;" + "GT,GIOITINH,35,0,f,l;" + "Mã bệnh án,MAHOSOBENHAN,85,0,f,l;" + "Mã viện phí,MATIEPNHAN,85,0,f,l;"
					+ "Vào viện,NGAYTIEPNHAN,120,0,f,l;" + "Ra viện,NGAY_RAVIEN,120,0,f,l;" + "Khóa mở BA,KHOAMOBA,85,0,f,l;" + "STT,STT,50,0,t,l;" + "NGUOIDUYETID,NGUOIDUYETID,50,0,t,l";
		}
		//L2PT-27740 start: thêm cột DOITUONGBENHNHANID
		//L2PT-118724: thêm YC_CHIHO
		_gridHeader_BN = _gridHeader_BN + ";DTBNID,DOITUONGBENHNHANID,0,0,t,l;YC_CHIHO,YC_CHIHO,0,0,t,l"
		//L2PT-27740 end
		// L2PT-68665: lam lai ICON
		_gridHeader_BN = _gridHeader_BN.replace(" ,ICON,20,0,ns,l;" + " ,ICON,20,0,ns,l;", " ,ICON_KT,20,0,ns,l;" + " ,ICON_BH,20,0,ns,l;");
		//_gridHeader_BN = _gridHeader_BN.replace(" ,ICON_KT,20,0,ns,l;" + " ,ICON_BH,20,0,ns,l;", " ,ICON_KT,20,0,ns,l;" + " ,ICON_BH,20,0,ns,l;" + " ,ICON_KS,20,0,ns,l;"
		//		+ "Ký số BK,KYSO_BANGKE,20,0,t,l;");
		// L2PT-44434 start
		// L2PT-25872 start -- L2PT-8044
		var VPI_DINHDANG_SO_GRDDV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_DINHDANG_SO_GRDDV');
		if (VPI_DINHDANG_SO_GRDDV == 1) {
			_gridHeader_DV = _gridHeader_DV.replaceAll("decimal!3", "decimal").replaceAll("decimal!2", "decimal");
		}
		// L2PT-25872 end -- L2PT-8044
		// L2PT-20967 start 
		var VPI_DINHDANG_SL_GRDDV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_DINHDANG_SL_GRDDV');
		if (VPI_DINHDANG_SL_GRDDV != '0') {
			_gridHeader_DV = _gridHeader_DV.replaceAll("SOLUONG,40,decimal!3", "SOLUONG,60,decimal!" + VPI_DINHDANG_SL_GRDDV);
			_gridHeader_DV = _gridHeader_DV.replaceAll("SOLUONG_OLD,40,decimal!3", "SOLUONG_OLD,60,decimal!5" + VPI_DINHDANG_SL_GRDDV);
		}
		// L2PT-20967 end
		if (VPI_HOANDV == 1)
			_focus_element = "#txtTIMKIEM";
		if (HIS_THUNGAN_THUKHAC == 0)
			$("#divThuKhac").hide();
		if (VPI_HOANDICHVU != 1)
			$("#cboLOAIPHIEUTHUID option[value=" + _HOANDICHVU + "]").remove();
		//ComboUtil.getComboTag("cboDOITUONG", "LOAIVIENPHI", [], "", "", "sql", "","");
		// L2PT-6440 start
		// L2PT-30848: rowNum & rowList
		GridUtil.init(_gridId_BN, "100%", "330", "Danh sách bệnh nhân", false, _gridHeader_BN, false, {
			rowNum : 20,
			rowList : [ 20, 50, 100, 200, 500 ]
		});
		GridUtil.addExcelButton(_gridId_BN, 'Xuất excel', true); //L2PT-22266//L2PT-18883
		//$("#"+_gridId_BN)[0].toggleToolbar();
		var _group = {
			groupField : [ "DOITUONG", "NHOM_MABHYT" ],
			groupColumnShow : [ false, false ],
			groupText : [ '<b>{0}</b>' ]
		};
		GridUtil.initGroup(_gridId_DV, "100%", "200", "", true, _group, _gridHeader_DV, false, {
			rowNum : 999999999,
			rowList : [ 999999999 ]
		});
		// L2PT-103281 start
		if (fConfig.VPI_SOLUONG_DSDV == '1') {
			$("#" + _gridId_DV).hideCol('SOLUONG');
			$("#" + _gridId_DV).showCol('SOLUONG_NHAN');
			$("#" + _gridId_DV).showCol('SOLUONG_TRA');
			$("#" + _gridId_DV).showCol('LOAIPHIEUMAUBENHPHAM'); // L2PT-112926
		}
		// L2PT-103281 end
		GridUtil.init(_gridId_PT, "100%", "250", "", false, _gridHeader_PT, false, {
			rowNum : 100,
			rowList : [ 100, 200, 300 ]
		});
		$("#" + _gridId_DV)[0].toggleToolbar();
		$("#" + _gridId_PT)[0].toggleToolbar();
		$("#" + _gridId_DV).hideCol('cb');
		if (VPI_HIENTHI_GOIKHAM == "1") {
			$("#dvCall5").show();
			$("#timMa").show();
		} else {
			$("#dvCall5").hide();
			$("#timMa").hide();
		}
		// L2PT-6440 end
		initPopup_HuyPT();
		// initPopup_CapNhatPT();
		initPopup_MST();
		//tuyennx_edit_start_20171018 yc L2DKBD-587
		initPopup_TTBN();
		initPopup_TTBN_NHIHDG();
		//tuyennx_edit_end_20171018
		//loadGridDataBN(); L2PT-949 
		initPopupLOIDUYET();
		//		toolbar = ToolbarUtil.build('toolbarId', ctl_ar);
//		var _toolbar=ToolbarUtil.getToolbar('toolbarId');
//		setEnabled([], ['toolbarIdBtnLichSuTT', 'toolbarIdBtnMaSoThue', 'toolbarIdBtnLichSuDT', 'toolbarIdBtnXoa']);
		$(_focus_element).focus();
		//tuyennx_add_start
		if (VPI_DAY_HOADONDT == 1) {
			var rs = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T013.GETDATA", []);
			if (rs[0].INVOICES_PASS != "" && rs[0].INVOICES_USER != "") {
				INVOICES_WS_USER_ACC = rs[0].INVOICES_USER;
				INVOICES_WS_PWD_ACC = rs[0].INVOICES_PASS;
			}
		}
		//tuyennx_add_end		
		// L2PT-16663 start: cau hinh chuot phai
		// cau hinh chuot phai phieu thu
		/*if (VPI_GRID_PHIEUTHU && VPI_GRID_PHIEUTHU != 0) {
			try {
				var CHgridPT = FormUtil.unescape(VPI_GRID_PHIEUTHU);
				var arr_ch_menu_pt = JSON.parse('[' + CHgridPT + ']');
				$('#contextMenu_PT ul li.menulevel2').each(function() {
					var idli = $(this).attr('id');
					if (arr_ch_menu_pt.indexOf(idli) == -1) {
						$(this).remove();
					}
				});
			} catch (err) {
				console.log(err.message);
			}
		}*/
		configContextMenus([ {
			idContextMenu : 'contextMenu_DV',
			configCode : 'VPI_GRID_DICHVU'
		}, {
			idContextMenu : 'contextMenu_PT',
			configCode : 'VPI_GRID_PHIEUTHU'
		}, {
			idContextMenu : 'contextMenu_BN',
			configCode : 'VPI_GRID_BENHNHAN'
		} ]);
		// L2PT-16663 end
		// L2PT-98874 L2PT-14600 start
		if (INVOICES_MISA_OPTIONUSER && INVOICES_MISA_OPTIONUSER != 0) {
			try {
				INVOICES_MISA_OPTIONUSER = JSON.parse(INVOICES_MISA_OPTIONUSER);
			} catch (err) {
				console.log(err);
			}
		}
		if (INVOICES_MISA_FMTNBR && INVOICES_MISA_FMTNBR != 0) {
			try {
				INVOICES_MISA_FMTNBR = JSON.parse('[' + FormUtil.unescape(INVOICES_MISA_FMTNBR) + ']');
			} catch (err) {
				console.log(err.message);
			}
		}
		// L2PT-98874 L2PT-14600 end
		// L2PT-25725 start
		if (INVOICES_VT_FMTNBR && INVOICES_VT_FMTNBR != 0) {
			try {
				INVOICES_VT_FMTNBR = JSON.parse('[' + FormUtil.unescape(INVOICES_VT_FMTNBR) + ']');
			} catch (err) {
				console.log(err.message);
			}
		}
		// L2PT-25725 end
		if (VPI_HOANDV == 1) {
			$("#btnHuy").text("Hoàn phiếu");
			$("#rHuyPhieuThu").remove();
		} else if (VPI_HOANDV == 0) {
			$("#rHoanDV").remove();
		}
		// cau hinh ly do mien giam (text or combobox)
		if (VPI_LYDO_MIEGIAM == 1) {
			$("#btnLYDO").show();
			$("#txtLYDO").hide();
		}
		if (VPI_CH_IN_INPHOI == 1) {
			$("#btnIn").hide();
			$("#btnInHDPL").hide(); //L2PT-21449 thêm chức năng in phôi lớn
		}
		//Begin_HaNv_22112019: Cập nhập chức năng miễn giảm % trên dịch vụ - L2PT-11967
		initPopup_MIENGIAMDV();
		//End_HaNv_22112019
		// L2PT-17330 start
		if (VPI_DUYET_THUOC == 1) {
			$("#divDuyetThuoc").show();
			var sql_par = RSUtil.buildParam("", [ "2,4,5,6,12" ]);
			ComboUtil.getComboTag("cboChonKho", "DUC01S002.DSKHO", sql_par, "", "", "sql", "", false);
			var _ck = document.cookie;
			if (_ck) {
				var _arr_ck = _ck.split(";");
				for (var ick = 0; ick < _arr_ck.length; ick++) {
					var _arr_kho = _arr_ck[ick].split("=");
					if (_arr_kho.length > 1) {
						var _key_kho = _arr_kho[0];
						if (_key_kho == "KHOID") {
							var _khoid = _arr_kho[1];
							$("#cboChonKho").val(_khoid);
							break;
						}
					}
				}
			}
		}
		// L2PT-17330 end
		//Begin_HaNv_18062020: Bỏ một số trạng thái trong thu viện phí - L2PT-22526
		var cboHide = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_TRANGTHAI_HIDE_VALUE');
		if (cboHide && cboHide != 0) {
			var arrHide = cboHide.split(',');
			for (var i = 0; i < arrHide.length; i++) {
				$("#cboTRANGTHAI option[value=" + arrHide[i] + "]").remove();
			}
		}
		//End_HaNv_18062020
		//Begin_HaNv_31032020: Bổ sung lý do miễn giảm(bắt buộc) khi nhập miễn giảm theo cấu hình hiển thị - L2PT-18597
		showLyDoMgDv = (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_SHOW_LYDO_MIENGIAM_DV') == '1') ? true : false;
		if (!showLyDoMgDv) {
			$('#dlgLYDOMIENGIAMDV').hide();
		} else {
			$('#dlgLYDOMIENGIAMDV').show();
		}
		//End_HaNv_31032020
		//Begin_HaNv_11052020: Cho phép chọn nhiều dịch vụ để nhập miễn giảm - L2PT-20511
		var _mgNhieuDv = (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_MIENGIAM_NHIEU_DV') == '1') ? true : false;
		if (!_mgNhieuDv) {
			$('#rMGNhieuDichVu').remove();
		}
		//End_HaNv_11052020
		// L2PT-22759 start
		if (VPI_VP_PNT == 1) {
			// xoa loai phieu tam ung va hoan ung
			$("#cboLOAIPHIEUTHUID option[value='2']").remove();
			$("#cboLOAIPHIEUTHUID option[value='3']").remove();
			// xoa chuyen khoan va may pox
			//$("#cboHINHTHUCTHANHTOAN option[value='2']").remove();
			//$("#cboHINHTHUCTHANHTOAN option[value='3']").remove();
			// xoa mien giam va ma bhyt
			$("#divMIENGIAM_PT").hide();
			$("#divMABHYT").hide();
		}
		// L2PT-22759 end
		// L2PT-23723 start
		var NGT_CANBOUUTIEN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'NGT_CANBOUUTIEN');
		if (NGT_CANBOUUTIEN == 1) {
			$("#divCBUT").show();
			$('#cboDTBNID').append($('<option>', {
				value : 99,
				text : 'Cán bộ ưu tiên'
			}));
		} else {
			$("#divCBUT").hide();
		}
		// L2PT-23723 end
		// L2PT-6440 
		var VPI_HIENTHIVP_QTI = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HIENTHIVP_QTI');
		if (VPI_HIENTHIVP_QTI == '1') {
			$(".divQTI").show();
			$("#divNRV").hide();
		} else {
			$(".divQTI").hide();
			$("#divNRV").show();
		}
		// L2PT-6440  end
		// L2PT-24613 start
		var VPI_THUVP_CBOXUTRI = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_THUVP_CBOXUTRI');
		if (VPI_THUVP_CBOXUTRI == 1) {
			$("#divXUTRI").show();
		}
		// L2PT-24613 end
		// L2PT-25034 start
		var VPI_THUVP_NGAYDUYETVP = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_THUVP_NGAYDUYETVP');
		if (VPI_THUVP_NGAYDUYETVP == 1) {
			$("#divNGAYDUYETVP").show();
		}
		// L2PT-25034 end		
		// L2PT-28406 start
		var colGhiChu = "Ghi chú,NOIDUNG,100,0,f,l";
		ComboUtil.initComboGrid("txtGHICHU", "DMC.HOTRO.NHAP", [ {
			"name" : "[0]",
			"value" : "1"
		} ], "256px", colGhiChu, function(event, ui) {
			var uItem = ui.item;
			$("#txtGHICHU").val(uItem.NOIDUNG);
			return false;
		});
		var colLyDo = "Lý do,NOIDUNG,100,0,f,l";
		ComboUtil.initComboGrid("txtLYDO", "DMC.HOTRO.NHAP", [ {
			"name" : "[0]",
			"value" : "2"
		} ], "256px", colLyDo, function(event, ui) {
			var uItem = ui.item;
			$("#txtLYDO").val(uItem.NOIDUNG);
			return false;
		});
		// L2PT-28406 end
		// L2PT-28714  start
		VPI_GTMD_GHICHU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_GTMD_GHICHU');
		// L2PT-28714 end
		// L2PT-29842 start
		var VPI_CHON_HTTT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_CHON_HTTT');
		if (VPI_CHON_HTTT == '1') {
			$("#btnHTTT").hide();
		}
		// L2PT-29842 end
		//L2PT-31437: Start HungND
		if (VPI_HIEN_IDPOST == '2') {
			//Init select POS
			var url = VPI_PAY_PMM_URL + "/api/v1.0/public/test/payment/pos"
			//var url = "http://payment.vncare.vn:8088/api/v1.0/public/test/payment/pos"
			var selIDCache = localStorage.getItem('IDPOST') ? localStorage.getItem('IDPOST') : "";
			// Start HungND
			// Init select POS
			var RESULT = null;
			$.ajax({
				url : url,
				type : 'GET',
				success : function(res) {
					RESULT = res.RESULT;
					var option = ""
					for (var i = 0; i < RESULT.length; i++) {
						if (RESULT[i].posterminalid === selIDCache) {
							option = option + "<option value='" + RESULT[i].posterminalid + "' selected>" + RESULT[i].name + "</option>";
						} else {
							option = option + "<option value='" + RESULT[i].posterminalid + "'>" + RESULT[i].name + "</option>";
						}
					}
					$("#cboPOS").html(option);
				},
				error : function(err) {
					console.log(err)
				}
			});
		}
		//L2PT-31437: End HungND
		//Start TuyenDV BVTM-2440
		if (_kySoCH == '1') {
			$("#div_kyso").show();
		}
		//End TuyenDV BVTM-2440
		//L2PT-50644 ttlinh start
		if (fConfig.VPI_CHOTHUPHI_LCD == '1') {
			$("#divDSThongBao").show();
		}
		//L2PT-50644 ttlinh end
		// L2PT-949 start
		var VPI_DEF_RADIO = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_DEF_RADIO');
		if (VPI_DEF_RADIO == '1') {
			$(".radioOpt").filter('[value=1]').prop('checked', true);
		}
		// L2PT-1401 L2PT-47548 start
		if (VPI_DUYETKT_CHUYENQT == '3') {
			$("#HTH_KYANH_NQT").show();
		} else {
			$("#HTH_KYANH_NQT").hide();
		}
		// L2PT-1401 L2PT-47548 end
		loadGridDataBN();
		// L2PT-949 end
		// L2PT-13822 // L2PT-29354 start
		var VPI_HIENTHI_DONGIA = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_HIENTHI_DONGIA');
		if (VPI_HIENTHI_DONGIA == '1') {
			$("#" + _gridId_DV).hideCol('TIENDICHVU');
			$("#" + _gridId_DV).hideCol('DONGIA_BHYT');
		}
		// BVTM-1166 start
		else if (VPI_HIENTHI_DONGIA == '2') {
			$("#" + _gridId_DV).hideCol('GIA_TIEN');
			$("#" + _gridId_DV).hideCol('DONGIA_BHYT');
		}
		// BVTM-1166 end
		else {
			$("#" + _gridId_DV).hideCol('GIA_TIEN');
			$("#" + _gridId_DV).hideCol('DON_GIA_BH');
		}
		// L2PT-13822 // L2PT-29354 end
		// L2PT-25547 start
		if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_HIENTHI_STT_DUYETBH') == '1') {
			$("#divSTTDuyetBH").show();
		}
		// L2PT-25547 end
		// L2PT-26167 start
		hienIdsTuCauHinh('VPI_DS_CTL_THUVIENPHI', 'VIS', 'SHOW');
		// L2PT-26167 end
		// L2PT-42086 start
		hienIdsTuCauHinh('VPI_DS_CTL_AN_THUVIENPHI', 'VIS', 'HIDE');
		// L2PT-42086 end
		// L2PT-99543 start
		if (typeof _opts.data !== 'object') {
			$("#btnDuyetCLS").hide();
		}
		// L2PT-99543 end
	};
	//Begin_HaNv_22112019: Cập nhập chức năng miễn giảm % trên dịch vụ - L2PT-11967
	function initPopup_MIENGIAMDV() {
		$("#txtTIEN_MG").focusout(function() {
			var grid = $("#" + _gridId_DV);
			var _selRowIds = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
			var rowid = _selRowIds[0];
			if (isNaN(parseFloat($("#txtTIEN_MG").val()))) {
				DlgUtil.showMsg("Giá trị miễn giảm không hợp lệ");
				return false;
			}
			var _tienMG = parseFloat($("#txtTIEN_MG").val());
			var _thucThu = parseFloat(grid.jqGrid('getCell', rowid, 'THANHTIEN')) - parseFloat(grid.jqGrid('getCell', rowid, 'TIEN_BHYT_TRA'));
			if (_tienMG < 0 || _tienMG > _thucThu) {
				DlgUtil.showMsg("Giá trị miễn giảm không được lớn hơn giá bệnh nhân trả");
				return false;
			}
			var _phanTram = parseFloat(_tienMG / _thucThu * 100);
			$('#txtPHANTRAM_MG').val(_phanTram.toFixed(2));
		});
		$("#txtPHANTRAM_MG").focusout(function() {
			var grid = $("#" + _gridId_DV);
			var _selRowIds = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
			var rowid = _selRowIds[0];
			if (isNaN(parseFloat($("#txtPHANTRAM_MG").val()))) {
				DlgUtil.showMsg("Giá trị phần trăm miễn giảm không hợp lệ");
				return false;
			}
			var _thucThu = parseFloat(grid.jqGrid('getCell', rowid, 'THANHTIEN')) - parseFloat(grid.jqGrid('getCell', rowid, 'TIEN_BHYT_TRA'));
			var _phanTram = parseFloat($('#txtPHANTRAM_MG').val());
			if (_phanTram < 0 || _phanTram > 100) {
				DlgUtil.showMsg("Giá trị phần trăm miễn giảm không hợp lệ");
				return false;
			}
			var _tienMG = parseFloat(_phanTram / 100 * _thucThu);
			$('#txtTIEN_MG').val(_tienMG.toFixed(2));
		});
		$('#btnSaveMG').click(function() {
			var validator = new DataValidator("dlgMIENGIAMDV");
			var valid = validator.validateForm();
			if (!valid)
				return false;
			var grid = $("#" + _gridId_DV);
			var _selRowIds = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
			var rowid = _selRowIds[0];
			if (isNaN(parseFloat($("#txtTIEN_MG").val()))) {
				DlgUtil.showMsg("Giá trị miễn giảm không hợp lệ");
				return false;
			}
			var _dichvukhambenhid = grid.jqGrid('getCell', rowid, 'DICHVUKHAMBENHID');
			var _tienMG = parseFloat($("#txtTIEN_MG").val());
			var _lydo = $('#txtLYDOMIENGIAM').val();
			var _thucThu = parseFloat(grid.jqGrid('getCell', rowid, 'THANHTIEN')) - parseFloat(grid.jqGrid('getCell', rowid, 'TIEN_BHYT_TRA'));
			if (_tienMG < 0 || _tienMG > _thucThu) {
				DlgUtil.showMsg("Giá trị miễn giảm không được lớn hơn giá bệnh nhân trả");
				return false;
			}
			//Begin_HaNv_31032020: Bổ sung lý do miễn giảm(bắt buộc) khi nhập miễn giảm theo cấu hình hiển thị - L2PT-18597
			if (showLyDoMgDv && (_lydo == null || _lydo == '')) {
				DlgUtil.showMsg("Bắt buộc nhập thông tin lý do miễn giảm dịch vụ!");
				return false;
			}
			var obj = new Object();
			obj.DICHVUKHAMBENHID = _dichvukhambenhid;
			obj.TIEN_MG = _tienMG + '';
			obj.LYDO_MG = _lydo;
			DlgUtil.showConfirm("Xác nhận sửa miễn giảm dịch vụ?", function(flag) {
				if (flag) {
					var _ret = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.MIENGIAM.DV', JSON.stringify(obj));
					if (_ret == 1) {
						loadGridDataDV(_tiepnhanid);
					} else if (_ret == 0) {
						DlgUtil.showMsg("Dịch vụ đã thu tiền, không thể cập nhật miễn giảm");
					} else if (_ret == '-1') {
						DlgUtil.showMsg("Bệnh nhân đã duyệt BH hoặc duyệt kế toán, không thể cập nhật miễn giảm");
					} else {
						DlgUtil.showMsg("Cập nhật miễn giảm không thành công");
					}
					DlgUtil.close("dlgMIENGIAMDV");
				}
			});
			//End_HaNv_31032020
		});
		$('#btnCloseMG').click(function() {
			$('#txtTIEN_MG').val("");
			$('#txtPHANTRAM_MG').val("");
			DlgUtil.close("dlgMIENGIAMDV");
		});
	}
	//End_HaNv_22112019
	function initPopupLOIDUYET() {
		var dlgMST = DlgUtil.buildPopup("dlgLOI", "dlgLOIDUYET", "Lỗi duyệt bảo hiểm", 800, 500);
		var btnClose = $('#btnDONGLOI');
		textArea = $('#txtNOIDUNGLOI');
		btnClose.click(function() {
			textArea.val("");
			dlgMST.close();
		});
	}
	function _bindEvent() {
		var f2 = 113;
		var view = 86;
		// L2PT-24171 start
		var VPI_PHIMTAT_THUVP = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_PHIMTAT_THUVP');
		var objCHphimTat = 0;
		try {
			var CHphimTat = FormUtil.unescape(VPI_PHIMTAT_THUVP);
			objCHphimTat = JSON.parse(CHphimTat);
		} catch (err) {
			console.log(err.message);
			objCHphimTat = 0;
		}
		// L2PT-26146 start
		//{"btnThem":"118","btnLuu":"119","btnHuyBo":"120","btnDuyet":"121"}
		//{"btnInPhoi":"115","btnThem":"117","btnLuu":"118","btnHuyBo":"119","btnIn":"120","btnDuyet":"121","btnThuKhac":"45","btnInPhoiBH":"123"}
		$(document).unbind('keydown').keydown(function(e) {
			if (e.keyCode == f2) {
				var $focused = $(':focus');
				var $help = $focused.parents("div").find('[help]');
				var attrLink = $help.attr('help');
				window.open(window.location.pathname.substring(0, window.location.pathname.indexOf("/", 2)) + attrLink + ".htm");
			} else if (e.altKey) {
				if (e.keyCode == view) {
					DlgUtil.open("dlgLOI");
				}
			}
			if (objCHphimTat instanceof Object) {
				for ( var idk in objCHphimTat) {
					if (e.keyCode == objCHphimTat[idk]) {
						e.preventDefault();
						console.log(idk);
						$("#" + idk).focus();
						var check = $("#" + idk).is(":enabled");
						if (check) {
							$("#" + idk).click();
						}
					}
				}
			}
		});
		// L2PT-26146 end
		// L2PT-24171 end
		$('#txtTIMKIEM').keyup(function() {});
		$('#txtTIMKIEM').keydown(function() {});
		$('#txtTIMKIEM').keypress(function() {});
		$('#txtTIMKIEM').focusin(function() {
			$("#txtTIMKIEM").select();
		});
		$('#txtTIMKIEM').change(function() {
			var ma = $("#txtTIMKIEM").val();
			if (ma != "") {
				loadGridDataBN();
			}
		});
		/*$('#txtTIMKIEM').focusout(function() {
			var ma = $("#txtTIMKIEM").val();
			if (ma != "") {
				loadGridDataBN();
			}
		});*/
		//
		$('#gs_MAHOSOBENHAN').focusin(function() {
			$("#gs_MAHOSOBENHAN").select();
		});
		$('#gs_MAHOSOBENHAN').scannerDetection(function() {
			$("#gs_MAHOSOBENHAN").select();
		});
		//
		$('#txtTIMKIEM').scannerDetection(function() {
			$("#txtTIMKIEM").select();
			var ma = $("#txtTIMKIEM").val();
			if (ma != "") {
				loadGridDataBN();
			}
		});
//		$('#txtTIMKIEM').bind('scannerDetectionComplete',function(e,data){
//        	console.log(data);
//        });
		$('#btnThuKhac').on('click', function(e) {
			if (flagLoading)
				return false;
			var paramInput = new Object;
			var selRowIds = $("#" + _gridId_DV).jqGrid("getGridParam", "selarrrow");
			var selRowId = selRowIds[0];
			var _khoaid = $("#" + _gridId_DV).jqGrid('getCell', selRowId, 'KHOAID');
			var _phongid = $("#" + _gridId_DV).jqGrid('getCell', selRowId, 'PHONGID');
			var _khambenhid = $("#" + _gridId_DV).jqGrid('getCell', selRowId, 'KHAMBENHID');
			var _dichvukhambenhid = $("#" + _gridId_DV).jqGrid('getCell', selRowId, 'DICHVUKHAMBENHID');
			var _tiepnhanid_dv = $("#" + _gridId_DV).jqGrid('getCell', selRowId, 'TIEPNHANID');
			if (_tiepnhanid_dv != _benhnhan.TIEPNHANID && VPI_ENABLE_THUKHAC == '0') {
				DlgUtil.showMsg("Chọn lại dịch vụ để chỉ định thu khác");
				return false;
			}
			if (typeof _khambenhid == "undefined") {
				var sql_par = [];
				sql_par.push({
					"name" : "[0]",
					"value" : _tiepnhanid
				});
				var _row = jsonrpc.AjaxJson.getFirstRowO("VPI01T001.27", sql_par);
				if (_row != null && _row.length > 0) {
					_khambenhid = _row[0].KHAMBENHID;
				}
			}
			EventUtil.setEvent("assignSevice_saveChiDinhDichVuOk", function(e) {
//		   				DlgUtil.showMsg(e.msg);
				DlgUtil.close("divDlgDichVu");
				loadGridDataDV(_tiepnhanid);
//	 		            loadGridDataPT(_tiepnhanid); 
			});
			paramInput = {
				chidinhdichvu : '1',
				loaidichvu : '1',
				loaiphieumbp : '17',
				benhnhanid : _benhnhan.BENHNHANID,
				khambenhid : _khambenhid,
				hosobenhanid : _benhnhan.HOSOBENHANID,
				tiepnhanid : _benhnhan.TIEPNHANID,
				doituongbenhnhanid : _benhnhan.DOITUONGBENHNHANID,
				loaitiepnhanid : _benhnhan.LOAITIEPNHANID,
				subDeptId : _phongid,
				deptId : _khoaid,
				dichvuidKhac : _dichvukhambenhid,
				formCall : "VPI_TVP"
			};
			var parJson = JSON.stringify(paramInput);
			var checkjson = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU.LOGS", parJson);
			console.log(parJson + "-" + checkjson);
			dlgPopup = DlgUtil.buildPopupUrl("divDlgDichVu", "divThuKhac", "manager.jsp?func=../ngoaitru/NGT02K016_ChiDinhDV" + "&loaidichvu=" + 1, paramInput, "Phiếu thu khác", 1300, 600);
			DlgUtil.open("divDlgDichVu");
		});
		$("#btnTimKiem").on("click", function(e) {
			// $('#txtTIMKIEM').val('**********');
			if (flagLoading)
				return;
			var validator = new DataValidator("divSearch");
			var valid = validator.validateForm();
			if (!valid)
				return false;
			// L2PT-18615 start: chuyen tu date sang datetime
			var _tungay = stringToDateTime($("#txtTU").val());
			var _denngay = stringToDateTime($("#txtDEN").val());
			// L2PT-18615 end
			if (_tungay && _denngay) {
				if (_tungay > _denngay) {
					onFocus([ "txtTU", "txtDEN" ]);
					DlgUtil.showMsg("Trường từ ngày không được lớn hơn trường đến ngày");
					return false;
				}
			}
			loadGridDataBN();
		});
		// xu ly khi chon mot hang trong danh sach vien phi
		GridUtil.setGridParam(_gridId_BN, {
			beforeSelectRow : function(rowid, e) {
				if (flagLoading)
					return false;
				else
					return true;
			},
			onSelectRow : function(id) {
				if (flagLoading)
					return;
				QRCODEBASE64STR = ""; // L2PT-71931
				setEnabled([], [ 'btnThem', 'btnDuyet' ]);
				if (id) {
					FormUtil.clearForm('ttThuTien', "");
					var _ret = $("#" + _gridId_BN).jqGrid('getRowData', id);
					_tiepnhanid = _ret.TIEPNHANID;
					_phieuthuid = -1;
					layTTTiepNhan(_tiepnhanid);
					GridUtil.unmarkRow(_gridId_BN, rowIdMarked);
					GridUtil.markRow(_gridId_BN, id);
					rowIdMarked = id;
					// SONDN 
					if (VPI_HIENTHI_GOIKHAM == "1") {
						$("#txtHOTEN5").val(_ret.TENBENHNHAN);
						$("#txtID5").val(_ret.TIEPNHANID);
						$("#txtSTT5").val("");
					}
					// END SONDN 
					$("#cboLOAIPHIEUTHUID").val(1);
					$("#cboHINHTHUCTHANHTOAN").val(1);
					setEnabled([], [ 'btnHuy', 'btnHTTT' ]); // L2PT-19304 btnHTTT
					//TuyenDV_BVTM-2828
					if (_kySoCH == '1') {
						var _checkKySo = CommonUtil.checkKyCa('NGT001_BKCPKCBBHYT_QD6556_DOC_A4', _ret.TIEPNHANID);
						if (_checkKySo == '1') {
							$("#btnKySo").prop("disabled", true);
							$("#btnHuyKySo").prop("disabled", false);
						} else {
							$("#btnHuyKySo").prop("disabled", true);
							$("#btnKySo").prop("disabled", false);
						}
					}
					//end TuyenDV_BVTM-2828
					// L2PT-118724 start
					/*if (_ret.YC_CHIHO == '-1') {
						setEnabled([ 'btnChiHo' ], []);
					} else {
						setEnabled([], [ 'btnChiHo' ]);
					}*/
					// L2PT-118724 end
				}
			},
			gridComplete : function(id) {}
		});
		// popup canh bao thu tien
		function popupWServiceInit(_loaiphieuthu) {
			dlgPopup = DlgUtil.buildPopupGrid("dlgDVSai", _gridId_DV3, "Danh sách dịch vụ không khớp mức hưởng", 1000, 520);
			GridUtil.init(_gridId_DV3, "990", "350", "", true, _gridHeader_DV);
			var btnClose = $('<div class="col-xs-12 low-padding mgt10" style="text-align: center;"><input class="btn btn-sm btn-primary" id="btn_DVS_CLOSE" type="button" value="Đóng" /></div>');
			$('#dlgDVSai').append(btnClose);
			GridUtil.setGridParam(_gridId_DV3, {
				gridComplete : function() {
					var ids = $("#" + _gridId_DV3).getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						$("#" + _gridId_DV3).jqGrid('setRowData', id, "", {
							color : 'red'
						});
					}
					var _total = $("#" + _gridId_DV3).jqGrid('getGridParam', 'records');
					if (_loaiphieuthu != _TAMUNG && _total > 0) {
						dlgPopup.open();
					} else {
						thutien(_loaiphieuthu);
					}
				}
			});
			GridUtil.fetchGridData(_gridId_DV3, _dv_sai);
			btnClose.click(function() {
				dlgPopup.close();
				if (VPI_KIEMTRA_TYLE == 0) {
					DlgUtil.showMsg("Số tiền bảo hiểm trả không khớp với mức hưởng, hãy kiểm tra lại");
					return;
				}
				DlgUtil.showConfirm("Số tiền bảo hiểm trả không khớp với mức hưởng, bạn có chắc chắn tiếp tục ?", function(flag) {
					if (flag) {
						thutien(_loaiphieuthu);
					}
				});
			});
		}
		// xu ly khi tai xong danh sach dich vu tuong ung voi mot ma vien phi
		$("#" + _gridId_DV).setGridParam({
			// xu ly khi chon/ bo chon dich vu de thanh toan
			onSelectRow : function(id) {
				var grid = $("#" + _gridId_DV);
				if (flagLoading && id) {
//						var _loaidt = grid.jqGrid ('getCell', id, 'LOAI_DOITUONG');
					var _doituongdv = $("#cboDOITUONGDV").val();
					var arr_row = grid.jqGrid("getGridParam", "selarrrow");
					if (_doituongdv == _DT_VIENPHI && _benhnhan.LOAITIEPNHANID == 1 && arr_row.length > _SL_VIENPHI) {
						grid.jqGrid('setSelection', id, false);
						return;
					}
					if (_doituongdv == _DT_DICHVU && arr_row.length > _SL_DICHVU) {
						grid.jqGrid('setSelection', id, false);
						return;
					}
					if (_benhnhan.TRANGTHAITIEPNHAN >= 1) {
						if (_thu_khac == 1) {// check thu khac
							_chot = false;
						} else {
							// var ids = grid.getDataIDs();
							arr_row = grid.jqGrid("getGridParam", "selarrrow");
							var dv_size = _arr_DV_CT.length;
							var sel_dv_size = arr_row.length;
							if (dv_size == sel_dv_size)
								_chot = true;
							else
								_chot = false;
							var _loaiphieuthuid = $("#cboLOAIPHIEUTHUID").val();
							if (VPI_QUYTRINH_VIENPHI != 1 && _loaiphieuthuid == 2 && dv_size != sel_dv_size) {
								$("#cboLOAIPHIEUTHUID").val(_THUTHEM);
								// $("#cboLOAIPHIEUTHUID").change();
							}
							if (VPI_QUYTRINH_VIENPHI != 1 && _loaiphieuthuid == 6 && dv_size == sel_dv_size && parseFloat(_vpData.NOPTHEM) < 0) {
								$("#cboLOAIPHIEUTHUID").val(_HOANUNG);
								// $("#cboLOAIPHIEUTHUID").change();
							}
							// grid.jqGrid('setSelection',id, false);
						}
					}
//						else if(_loaidt == 'BHYT') {
//							for (var i=0, il=ids.length; i < il; i++) {
//								var _loaidt2 = grid.jqGrid ('getCell', ids[i], 'LOAI_DOITUONG');
//								if(_loaidt2 == _loaidt && ids[i] != id){
//									grid.jqGrid('setSelection',ids[i], false); 
//						    	}
//							}
//					    }
//						else { 	
					GridUtil.unmarkAll(_gridId_DV);
					for (var i = 0; i < arr_row.length; i++) {
						GridUtil.markRow(_gridId_DV, arr_row[i]);
					}
//						}
					loadDV();
				} else {
					var selRowIds = grid.jqGrid("getGridParam", "selarrrow");
					for (var i = 0; i < selRowIds.length; i++) {
//   	                    	grid.setSelection(selRowIds[i],false);
					}
					grid.resetSelection();
					grid.setSelection(id, false);
					GridUtil.unmarkRow(_gridId_DV, rowIdMarked_DV);
					GridUtil.markRow(_gridId_DV, id);
					rowIdMarked_DV = id;
					var _dathutien = grid.jqGrid('getCell', id, 'DATHUTIEN');
//		 				var _loaidoituongid = grid.jqGrid('getCell', id, 'LOAIDOITUONG');
//		 				var _bhyt_dv = grid.jqGrid('getCell', id, 'BHYT_DV');
					if (_dathutien == 3) {
						$("#rXoaDichVu").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						$("#rHuyDichVu").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						$("#rThayThe").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
					} else {
						$("#rHuyDichVu").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						$("#rXoaDichVu").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						$("#rThayThe").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					}
					//	       		if(_bhyt_dv == 1 && HIS_THUNGAN_THUKHAC == 1 ){
					if (HIS_THUNGAN_THUKHAC == 1 && _benhnhan.TRANGTHAITIEPNHAN != 2 && _benhnhan.TRANGTHAITIEPNHAN_VP != 1) {
						setEnabled([ 'btnThuKhac' ], []);
					} else {
						setEnabled([], [ 'btnThuKhac' ]);
					}
				}
			},
			onSelectAll : function(ids, status) {
				var grid = $("#" + _gridId_DV);
				if (flagLoading) {
					var _doituongdv = $("#cboDOITUONGDV").val();
					var arr_row = grid.jqGrid('getGridParam', 'selarrrow');
					GridUtil.unmarkAll(_gridId_DV);
					for (var i = 0; i < arr_row.length; i++) {
						if (_doituongdv == _DT_VIENPHI && _benhnhan.LOAITIEPNHANID == 1 && i >= _SL_VIENPHI) {
							grid.jqGrid('setSelection', id, false);
						} else if (_doituongdv == _DT_DICHVU && i >= _SL_DICHVU) {
							grid.jqGrid('setSelection', id, false);
						} else
							GridUtil.markRow(_gridId_DV, arr_row[i]);
					}
					if (_benhnhan.TRANGTHAITIEPNHAN >= 1) {
						if (_thu_khac == 1) {// check thu khac
							_chot = false;
						} else {
							// var ids = grid.getDataIDs();
							var arr_row = grid.jqGrid('getGridParam', 'selarrrow');
							var dv_size = _arr_DV_CT.length;
							var sel_dv_size = arr_row.length;
							if (dv_size == sel_dv_size)
								_chot = true;
							else
								_chot = false;
							var _loaiphieuthuid = $("#cboLOAIPHIEUTHUID").val();
							if (VPI_QUYTRINH_VIENPHI != 1 && _loaiphieuthuid == 2 && dv_size != sel_dv_size)
								$("#cboLOAIPHIEUTHUID").val(6);
							if (VPI_QUYTRINH_VIENPHI != 1 && _loaiphieuthuid == 6 && dv_size == sel_dv_size && _vpData.NOPTHEM < 0)
								$("#cboLOAIPHIEUTHUID").val(2);
							// grid.jqGrid('setSelection',id, false);
						}
					}
					loadDV();
				} else {}
			},
			// xu ly khi tai xong danh sach dichvu
			gridComplete : function() {}
		});
		// xu ly su kien sua so luong
		$("#" + _gridId_DV).bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
			var sl = $("#" + _gridId_DV).jqGrid('getCell', rowid, 'SOLUONG');
			var sl_old = $("#" + _gridId_DV).jqGrid('getCell', rowid, 'SOLUONG_OLD');
			var tien_miengiam = $("#" + _gridId_DV).jqGrid('getCell', rowid, 'TIEN_MIENGIAM');
			var tlmg = $("#" + _gridId_DV).jqGrid('getCell', rowid, 'TYLE_MIENGIAM');
			var tlmg_old = $("#" + _gridId_DV).jqGrid('getCell', rowid, 'TYLE_MIENGIAM_OLD');
			var _dichvukhambenhid = $("#" + _gridId_DV).jqGrid('getCell', rowid, 'DICHVUKHAMBENHID');
			var _loaidoituong = $("#" + _gridId_DV).jqGrid('getCell', rowid, 'LOAIDOITUONG');
			var _dathutien = $("#" + _gridId_DV).jqGrid('getCell', rowid, 'DATHUTIEN');
			if (flagLoading || _dathutien == 3 || _loaidoituong == 1 || _loaidoituong == 2 || _loaidoituong == 3) {
				$("#" + _gridId_DV).jqGrid('setCell', rowid, 'SOLUONG', sl_old);
				$("#" + _gridId_DV).jqGrid('setCell', rowid, 'TYLE_MIENGIAM', tlmg_old);
				return false;
			}
			sl = parseFloat(sl);
			sl_old = parseFloat(sl_old);
			tien_miengiam = parseFloat(tien_miengiam);
			tlmg = parseFloat(tlmg);
			tlmg_old = parseFloat(tlmg_old);
			if (sl != sl_old) {
				if (/^\d+$/i.test(sl) && sl > 0 && sl < sl_old && tien_miengiam == 0) {
					DlgUtil.showConfirm("Xác nhận sửa số lượng dịch vụ", function(flag) {
						if (flag) {
							var _ret = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.TACH.DV', _dichvukhambenhid + "$" + sl + "$" + 0);
							if (_ret == 1) {
								loadGridDataDV(_tiepnhanid);
							} else if (_ret == 0) {
								$("#" + _gridId_DV).jqGrid('setCell', rowid, 'SOLUONG', sl_old);
								DlgUtil.showMsg("Không thể sửa số lượng dịch vụ này");
							} else {
								$("#" + _gridId_DV).jqGrid('setCell', rowid, 'SOLUONG', sl_old);
								DlgUtil.showMsg("Cập nhật không thành công");
							}
						} else {
							$("#" + _gridId_DV).jqGrid('setCell', rowid, 'SOLUONG', sl_old);
						}
					});
				} else {
					$("#" + _gridId_DV).jqGrid('setCell', rowid, 'SOLUONG', sl_old);
				}
			}
			if (tlmg != tlmg_old) {
				if (/^\d+$/i.test(tlmg) && tlmg >= 0 && tlmg <= 100) {
					DlgUtil.showConfirm("Xác nhận sửa tỷ lệ miễn giảm", function(flag) {
						if (flag) {
							var _ret = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.SUA.MIENGIAM', _dichvukhambenhid + "$" + tlmg);
							if (_ret == 1) {
								loadGridDataDV(_tiepnhanid);
							} else if (_ret == 0) {
								$("#" + _gridId_DV).jqGrid('setCell', rowid, 'TYLE_MIENGIAM', tlmg_old);
								DlgUtil.showMsg("Không thể sửa tỷ lệ miễn giảm dịch vụ này");
							} else {
								$("#" + _gridId_DV).jqGrid('setCell', rowid, 'TYLE_MIENGIAM', tlmg_old);
								DlgUtil.showMsg("Cập nhật không thành công");
							}
						} else {
							$("#" + _gridId_DV).jqGrid('setCell', rowid, 'TYLE_MIENGIAM', tlmg_old);
						}
					});
				} else {
					$("#" + _gridId_DV).jqGrid('setCell', rowid, 'TYLE_MIENGIAM', tlmg_old);
				}
			}
		});
		// xu ly su kien chon mot hang trong grid phieu thu
		GridUtil.setGridParam(_gridId_PT, {
			onSelectRow : function(id) {
				if (flagLoading)
					return;
				if (id) {
					var _row = $("#" + _gridId_PT).jqGrid('getRowData', id);
					_phieuthuid = _row.PHIEUTHUID;
					_hddt = _row.INVOICES_NUMBER;
					var sql_par = [];
					sql_par.push({
						"name" : "[0]",
						"value" : _phieuthuid
					});
					_fl_tinh = false;
					GridUtil.loadGridBySqlPage(_gridId_DV, "VPI01T001.22", sql_par);//, function(){});
					var _dahuyphieu = $("#" + _gridId_PT).jqGrid('getCell', id, 'DAHUYPHIEU');
					var _yc_hoan = $("#" + _gridId_PT).jqGrid('getCell', id, 'YC_HOAN');
					var _dahoan = $("#" + _gridId_PT).jqGrid('getCell', id, 'DAHOAN');
					var _treophieu = $("#" + _gridId_PT).jqGrid('getCell', id, 'TREOPHIEU');
					var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', id, 'LOAIPHIEUTHUID');
					//tuyennx_edit_start_20171020 yc L2DKBD-587
					if (_benhnhan.TRANGTHAITIEPNHAN_BH == 1 || _benhnhan.TRANGTHAITIEPNHAN_VP == 1) {
						setEnabled([], [ 'btnHuy' ]);
						// L2PT-17473 start
						/*$("#rCapNhatPhieuThu").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});*/
						// L2PT-17473 end
						$("#rHuyPhieuThu").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						$("#rHuyGiuSo").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						$("#rKhoiPhuc").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						$("#rNhapThongTinTT").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						$("#rHoanDV").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						$("#rTreoPhieu").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						$("#rHuyTreoPhieu").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					} else {
						if (_dahuyphieu == 1) {
							// L2PT-114387 start
							$("#rCapNhatPhieuThu").css({
								"pointer-events" : "auto",
								"opacity" : "1"
							});
							// L2PT-114387 end
							$("#rHuyPhieuThu").css({
								"pointer-events" : "none",
								"opacity" : "0.6"
							});
							$("#rHuyGiuSo").css({
								"pointer-events" : "none",
								"opacity" : "0.6"
							});
							$("#rNhapThongTinTT").css({
								"pointer-events" : "none",
								"opacity" : "0.6"
							});
							$("#rTreoPhieu").css({
								"pointer-events" : "none",
								"opacity" : "0.6"
							});
							$("#rHuyTreoPhieu").css({
								"pointer-events" : "none",
								"opacity" : "0.6"
							});
							if (VPI_KHOIPHUCPHIEU == 1)
								$("#rKhoiPhuc").css({
									"pointer-events" : "auto",
									"opacity" : "1.0"
								});
							else
								$("#rKhoiPhuc").css({
									"pointer-events" : "none",
									"opacity" : "0.6"
								});
							$("#rHoanDV").css({
								"pointer-events" : "none",
								"opacity" : "0.6"
							});
							setEnabled([], [ 'btnHuy' ]); // L2PT-123624
						} else {
							if (VPI_HOANDV == 1 || VPI_HOANDV == 2) {
								if (_dahoan == 0 && (_yc_hoan > 0 || _loaiphieuthuid == 3)) {
									$("#rHoanDV").css({
										"pointer-events" : "auto",
										"opacity" : "1.0"
									});
									setEnabled([ 'btnHuy' ], []);
								} else {
									$("#rHoanDV").css({
										"pointer-events" : "none",
										"opacity" : "0.6"
									});
									setEnabled([], [ 'btnHuy' ]);
								}
							} else {
								$("#rHoanDV").css({
									"pointer-events" : "none",
									"opacity" : "0.6"
								});
								setEnabled([ 'btnHuy' ], []);
							}
							if (_loaiphieuthuid != _TAMUNG) {
								$("#rTreoPhieu").css({
									"pointer-events" : "none",
									"opacity" : "0.6"
								});
								$("#rHuyTreoPhieu").css({
									"pointer-events" : "none",
									"opacity" : "0.6"
								});
							} else if (_treophieu == 1) {
								$("#rTreoPhieu").css({
									"pointer-events" : "none",
									"opacity" : "0.6"
								});
								$("#rHuyTreoPhieu").css({
									"pointer-events" : "auto",
									"opacity" : "1.0"
								});
							} else {
								$("#rTreoPhieu").css({
									"pointer-events" : "auto",
									"opacity" : "1.0"
								});
								$("#rHuyTreoPhieu").css({
									"pointer-events" : "none",
									"opacity" : "0.6"
								});
							}
							$("#rCapNhatPhieuThu").css({
								"pointer-events" : "auto",
								"opacity" : "1.0"
							});
							$("#rHuyPhieuThu").css({
								"pointer-events" : "auto",
								"opacity" : "1.0"
							});
							$("#rHuyGiuSo").css({
								"pointer-events" : "auto",
								"opacity" : "1.0"
							});
							$("#rKhoiPhuc").css({
								"pointer-events" : "none",
								"opacity" : "0.6"
							});
							$("#rNhapThongTinTT").css({
								"pointer-events" : "auto",
								"opacity" : "1.0"
							});
						}
					}
					//tuyennx_edit_end_20171020 yc L2DKBD-587
					var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', id, 'LOAIPHIEUTHUID');
//		               var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.LAYCH", []);
//               			var rows = JSON.parse(data);
//               			var check = null;
//               			if (rows != null && rows.length > 0) {
//               				 check = rows[0]["GIATRI"];
//               			}
					if (_dahuyphieu != 1 && _loaiphieuthuid == 1 && VPI_DAY_HOADONDT == 1) {
						$("#rXemHDDT").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						// L2PT-21660 start
						$("#rXemHDDTCD").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						// L2PT-21660 end
						// L2PT-24260 start
						$("#rInHDDTCD").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						// L2PT-24260 end
						$("#rGuiHDDT").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						// L2PT-26227 start
						// L2PT-22748 start
						$("#rGuiHDDT_KPH").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
						// L2PT-22748 end
						setEnabled([ "btnGuiHDDT", "btnXMLHDDT" ], []); // L2PT-16575
						// L2PT-26227 end
						$("#rInHDDTreview").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
					} else {
						$("#rXemHDDT").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						// L2PT-21660 start 
						$("#rXemHDDTCD").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						// L2PT-21660 end
						// L2PT-24260 start
						$("#rInHDDTCD").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						// L2PT-24260 end
						$("#rGuiHDDT").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						// L2PT-26227 start
						// L2PT-22748 start
						$("#rGuiHDDT_KPH").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
						// L2PT-22748 end
						setEnabled([], [ "btnGuiHDDT", "btnXMLHDDT" ]); // L2PT-16575
						// L2PT-26227 end
						$("#rInHDDTreview").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					}
					if (_dahuyphieu == 1 && _loaiphieuthuid == 1) {
						$("#rInBBHUY").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
					} else {
						$("#rInBBHUY").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					}
					// L2PT-6393 start
					if (_dahuyphieu == 1 && _loaiphieuthuid == 3) {
						$("#rInHuyTU").css({
							"pointer-events" : "auto",
							"opacity" : "1.0"
						});
					} else {
						$("#rInHuyTU").css({
							"pointer-events" : "none",
							"opacity" : "0.6"
						});
					}
					// L2PT-6393 end
					GridUtil.unmarkRow(_gridId_PT, rowIdMarked_PT);
					GridUtil.markRow(_gridId_PT, id, '');
					rowIdMarked_PT = id;
					FormUtil.clearForm('ttThuTien', '');
//					var _sql_par = [ {
//						name : "[0]",
//						value : _phieuthuid
//					} ];
					var _obj_ttpt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', _phieuthuid);
					//var _obj_ttpt = JSON.parse(_tt_phieuthu);
					if (_obj_ttpt.length > 0) {
						var _ttpt = _obj_ttpt[0];
						// L2PT-60354 start
						if (_ttpt.NGUOIDUNGID_YCHUY && _ttpt.NGUOIDUNGID_YCHUY > 0) {
							$("#rYeuCauHuy").css({
								"pointer-events" : "none",
								"opacity" : "0.6"
							});
						} else {
							$("#rYeuCauHuy").css({
								"pointer-events" : "auto",
								"opacity" : "1.0"
							});
						}
						// L2PT-60354 end
						var _ttpt_dis = clone(_ttpt);
						_ttpt_dis.TONGTIEN = vienphi_tinhtien.convertNumToCurrency(_ttpt_dis.TONGTIEN);
						_ttpt_dis.MIENGIAM_PT = vienphi_tinhtien.convertNumToCurrency(_ttpt_dis.MIENGIAM_PT);
						_ttpt_dis.THUCTHU = vienphi_tinhtien.convertNumToCurrency(_ttpt_dis.THUCTHU);
						if (_loaiphieuthuid == _HOANUNG || _loaiphieuthuid == _HOANDICHVU) {
							_ttpt.TLMIENGIAM = 0;
							$("#lTHUCTHU").text("Hoàn tiền");
						} else {
							_ttpt.TLMIENGIAM = (_ttpt.MIENGIAM_PT / _ttpt.TONGTIEN * 100).toFixed(2);
							$("#lTHUCTHU").text("Thực thu");
						}
						FormUtil.setObjectToForm('ttThuTien', "", _ttpt_dis);
						// BVTM-3849 start -- BVTM-5934
						// L2PT-59668 start
						if (_ttpt_dis.HINHTHUCTHANHTOAN == "2") {
							// L2PT-98242
							$("#cboNganHang").val(_ttpt_dis.NGANHANGID);
							$("#divNganHang").show();
						} else {
							$("#cboNganHang").val("");
							$("#divNganHang").show();
						}
						// L2PT-59668 end
						if (_ttpt_dis.HINHTHUCTHANHTOAN == "5" || _ttpt_dis.HINHTHUCTHANHTOAN == "12") { // L2PT-4355 : them loai tt = 5
							ComboUtil.getComboTag("cboDVTT", 'VPI.DVTT.SP', _ttpt_dis.HINHTHUCTHANHTOAN == "12" ? "QRCODE" : "POS", "", "", 'sp', '', '');
							$("#cboDVTT").val(_ttpt_dis.DVTT);
							$("#divDVTT").show();
						} else {
							$("#cboDVTT").val("");
							$("#divDVTT").hide();
						}
						// BVTM-3849 end -- BVTM-5934
						$("#cboMANHOMPHIEUTHU").empty();
						$("#cboMANHOMPHIEUTHU").append($('<option>', {
							value : 0,
							text : _ttpt.MANHOMPHIEUTHU
						}));
						$("#cboMANHOMPHIEUTHU").val(0);
						$("#cboLOAIPHIEUTHUID").val(_ttpt.LOAIPHIEUTHUID == 1 ? 6 : _ttpt.LOAIPHIEUTHUID);
					}
					setEnabled([ 'btnIn', 'btnInHDPL', 'btnInQR', 'btnHTTT' ], []); //L2PT-21449 thêm btn in phôi lớn, BVTM-5936 // L2PT-19304 btnHTTT
					// L2PT-42086 start
					if (_hddt && _hddt.length > 0 && _dahuyphieu != 1) {
						setEnabled([ 'btnInHDDT', 'btnInHDDTCD' ], [])
					} else {
						setEnabled([], [ 'btnInHDDT', 'btnInHDDTCD' ])
					}
					// L2PT-42086 end
				}
			}
		});
		// xu ly su kien khi tai xong grid phieu thu
		GridUtil.setGridParam(_gridId_PT, {
			gridComplete : function() {}
		});
		// xu ly nut "them"
		$("#btnThem").click(function() {
			/*var opts = new Object();
			opts.TIEPNHANID = _tiepnhanid;
			opts.KHOAID_DN = _khoa_id; // khoa đăng nhập
			opts.PHONGID_DN = _phong_id; // phòng đăng nhập
			opts.LOAIPHIEUTHUID = "6"; // tạm thời fix cứng
			opts.HINHTHUCTHANHTOAN = "12"; // tạm thời fix cứng
			opts.TEN_HTTT = "Quét QRCODE"; // tạm thời fix cứng
			opts.DOITUONGDV = "0"; // tạm thời fix cứng
			opts.THUKHAC = "0"; // tạm thời fix cứng
			opts.CHILAPHD = "0"; // tạm thời fix cứng
			opts._loaihd = "0"; // tạm thời fix cứng
			opts._nhomtt_pt = "0"; // tạm thời fix cứng
			opts._id_dvtt = 1; // có thể thêm cấu hình
			opts._sotaikhoan_bv = ""; // để trống
			opts.CashierID = "a"; // người thu id
			opts.CashierName = ":b"; // tên người thu
			qrCode = getQRCode(opts);
			console.log(qrCode);
			return;*/
			QRCODEBASE64STR = ""; // L2PT-71931
			if (_tiepnhanid == -1)
				return false;
			// L2PT-1511 start
			if (checkLichSuPOS() == 1) {
				return;
			}
			// L2PT-1511 end
			flagLoading = true;
			$("#txtMIENGIAM_PT").val(0);
			$("#txtTLMIENGIAM").val(0);
			loadGridDataDV(_tiepnhanid);
		});
//		$("#"+_gridId_DV).click(function(){
//			if(flagLoading) {
//				DlgUtil.showMsg("LOL");
//				loadDV();
//			}
//		});
		// xu ly su kien nhap so tien
		$("#txtTONGTIEN").keyup(function() {
			var n = get_val_m('txtTONGTIEN');
			var before = $("#txtTONGTIEN").val();
			if (!isNaN(n)) {
				var start = this.selectionStart;
				var end = this.selectionEnd;
				val_m('txtTONGTIEN', n);
				var after = $("#txtTONGTIEN").val();
				this.setSelectionRange(start + after.length - before.length, end + after.length - before.length);
			}
			var _mg = get_val_m('txtMIENGIAM_PT');
			var _miengiam = parseFloat(_mg);
			_miengiam = !_miengiam ? 0 : _miengiam;
			var _thucthu_nf = parseFloat(get_val_m('txtTONGTIEN'));
			_thucthu_nf = !_thucthu_nf ? 0 : _thucthu_nf;
			_thucthu_nf = _thucthu_nf - _miengiam;
			var _thucthu = parseFloat(_thucthu_nf.toFixed(2));
			val_m('txtTHUCTHU', _thucthu);//(vienphi_tinhtien.convertNumToCurrency(parseFloat(_thucthu)));
			var _loaiphieuthu = $("#cboLOAIPHIEUTHUID").val();
			var _sotien = _thucthu;
			if (_loaiphieuthu == _THUTIEN) {
				val_m('txtDANOP', (parseFloat(_vpData.DANOP) + _sotien).toFixed(2));
				val_m('txtNOPTHEM', (_vpData.NOPTHEM - _sotien).toFixed(2));
			} else if (_loaiphieuthu == _TAMUNG) {
				val_m('txtTAMUNG', (parseFloat(_vpData.TAMUNG) + _sotien).toFixed(2));
				val_m('txtCON_TAMUNG', (parseFloat(_vpData.CON_TAMUNG) + _sotien).toFixed(2));//L2PT-8100
				val_m('txtNOPTHEM', (parseFloat(_vpData.NOPTHEM) - _sotien).toFixed(2));
			} else if (_loaiphieuthu == _HOANUNG) {
				val_m('txtHOANUNG', (parseFloat(_vpData.HOANUNG) + _sotien).toFixed(2));
				val_m('txtNOPTHEM', (parseFloat(_vpData.NOPTHEM) + _sotien).toFixed(2));
			} else {
				val_m('txtDANOP', (parseFloat(_vpData.DANOP) + _sotien).toFixed(2));
				val_m('txtNOPTHEM', (parseFloat(_vpData.NOPTHEM) - _sotien).toFixed(2));
			}
			objData.TONGTIEN = get_val_m('txtTONGTIEN') + "";
		});
		// L2PT-16670 start: xử lý nhập miễn giảm
		// L2PT-51018 start
		$("#txtMIENGIAM_PT").keyup(function(e) {
			var _miengiam = get_val_m('txtMIENGIAM_PT');
			var before = $("#txtMIENGIAM_PT").val();
			if (!isNaN(_miengiam)) {
				var start = this.selectionStart;
				var end = this.selectionEnd;
				val_m('txtMIENGIAM_PT', _miengiam);
				var after = $("#txtMIENGIAM_PT").val();
				this.setSelectionRange(start + after.length - before.length, end + after.length - before.length);
				_miengiam = parseFloat(_miengiam);
				_miengiam = !_miengiam ? 0 : _miengiam;
				var _sotien = parseFloat(_tien_hoadon);
				var _tlmg = _sotien == 0 ? -1 : _miengiam / _sotien * 100;
				_tlmg = _tlmg.toFixed(2);
				if (parseFloat(_tlmg) > 100 || parseFloat(_tlmg) < 0) {
					_miengiam = 0;
					_tlmg = 0;
					//return false;
				}
				val_m('txtMIENGIAM_PT', _miengiam);
				val_m('txtTLMIENGIAM', _tlmg);
				nhapMG(_miengiam);
			}
		});
		$("#txtMIENGIAM_PT").change(function() {
			var _miengiam = parseFloat(get_val_m('txtMIENGIAM_PT'));
			_miengiam = !_miengiam ? 0 : _miengiam;
			nhapMG(_miengiam);
		});
		$("#txtTLMIENGIAM").keyup(function() {
			nhapTLMIENGIAM();// L2PT-6164
		});
		// L2PT-6164 start 
		function nhapTLMIENGIAM() {
			var _sotien = parseFloat(_tien_hoadon);
			var _tlmg = get_val_m('txtTLMIENGIAM');
			if (!isNaN(_tlmg)) {
				var _tlmg = parseFloat(_tlmg);
				_tlmg = !_tlmg ? 0 : _tlmg;
				// L2PT-113885 start
				var _miengiam = 0;
				if (_tlmg > 100 || _tlmg < 0) {
					_miengiam = 0;
					_tlmg = 0;
					//return false;
				} else {
					_miengiam = _sotien * _tlmg / 100;
					_miengiam = !_miengiam ? 0 : _miengiam;
					_miengiam = parseFloat(_miengiam.toFixed(2));
				}
				// L2PT-113885 end
				val_m('txtMIENGIAM_PT', _miengiam);
				val_m('txtTLMIENGIAM', _tlmg);
				nhapMG(_miengiam);
			}
		}
		// L2PT-6164 end
		// L2PT-51018 end
		function nhapMG(_miengiam) {
			var _loaiphieuthu = $("#cboLOAIPHIEUTHUID").val();
			var _tongtien = parseFloat(get_val_m('txtTONGTIEN'));
			if (_loaiphieuthu == _HOANUNG) {
				var _thucthu = _tongtien + _miengiam;
				_thucthu = _thucthu.toFixed(2);
				_thucthu = parseFloat(_thucthu);
				if (_thucthu <= 0) {
					$("#cboLOAIPHIEUTHUID").val(_THUTHEM);
					$("#cboLOAIPHIEUTHUID").change();
					return;
				}
				val_m('txtTHUCTHU', _thucthu);
				val_m('txtHOANUNG', (parseFloat(_vpData.HOANUNG) + parseFloat(_thucthu)).toFixed(2));
			} else {
				var _thucthu = _tongtien - _miengiam;
				_thucthu = _thucthu.toFixed(2);
				_thucthu = parseFloat(_thucthu);
				if (_thucthu < 0) {
					$("#cboLOAIPHIEUTHUID").val(_HOANUNG);
					$("#cboLOAIPHIEUTHUID").change();
					return;
				}
				val_m('txtTHUCTHU', _thucthu);
				val_m('txtDANOP', (parseFloat(_vpData.DANOP) + _thucthu).toFixed(2));
			}
			val_m('txtMIENGIAM', (parseFloat(_vpData.MIENGIAM) + _miengiam).toFixed(2));
		}
		// L2PT-16670 end
		$("#cboLOAIPHIEUTHUID").change(function() {
			if (flagLoading) {
				var _loaiphieuthu = $("#cboLOAIPHIEUTHUID").val();
				loadGridDataDV(_tiepnhanid, _loaiphieuthu);
//				loadDV();
			}
		});
		$("#cboMANHOMPHIEUTHU").change(function() {
			if (flagLoading) {
				// L2PT-35120 start
				var i = $("#cboMANHOMPHIEUTHU").val();
				var _loaiphieuthu = $("#cboLOAIPHIEUTHUID").val();
				if (_loaiphieuthu == 3) {
					obj.MAPHIEUTHU = _dsSo[i].MAPHIEUTHU;
					obj.SOPHIEUTO = _dsSo[i].SOPHIEUTO;
					obj.KHOASOPHIEUTU = _dsSo[i].KHOASOPHIEUTU;
					obj.SOPHIEUFROM = _dsSo[i].SOPHIEUFROM;
					obj.NHOMPHIEUTHUID = _dsSo[i].NHOMPHIEUTHUID;
					obj.MANHOMPHIEUTHU = _dsSo[i].MANHOMPHIEUTHU;
					obj.LOAIPHIEUTHU = _dsSo[i].LOAIPHIEUTHU;
					FormUtil.setObjectToForm("ttThuTien", "", obj);
					$("#cboMANHOMPHIEUTHU").val(i);
					return;
				}
				// L2PT-35120 end
				var obj_2_dis = new Object();
				obj_2_dis.MAPHIEUTHU = _dsSo_2[i].MAPHIEUTHU;
				obj_2_dis.LOAIPHIEUTHUID = obj.LOAIPHIEUTHUID;
				FormUtil.setObjectToForm("ttThuTien", "", obj_2_dis);
				obj_2.LOAIPHIEUTHUID = _THUTIEN + "";
				$("#cboMANHOMPHIEUTHU").val(i);
				for (var j = 0; j < _phieuInfo.length; j++) {
					_phieuInfo[j].NHOMPHIEUTHUID = _dsSo_2[i].NHOMPHIEUTHUID;
					_phieuInfo[j].MANHOMPHIEUTHU = _dsSo_2[i].MANHOMPHIEUTHU;
					_phieuInfo[j].MAPHIEUTHU = _dsSo_2[i].MAPHIEUTHU;
					_phieuInfo[j].LOAIPHIEUTHU = _dsSo_2[i].LOAIPHIEUTHU;
					_phieuInfo[j].SOPHIEUFROM = _dsSo_2[i].SOPHIEUFROM;
					_phieuInfo[j].SOPHIEUTO = _dsSo_2[i].SOPHIEUTO;
					_phieuInfo[j].KHOASOPHIEUTU = _dsSo_2[0].KHOASOPHIEUTU;
				}
			}
		});
		//L2PT-7489 start 
		// xử lý chọn loại hình thanh toán
		$("#cboHINHTHUCTHANHTOAN").change(function() {
			onCboHtttChange(); // BVTM-5934
		});
		//L2PT-7489 end 
		// xy ly su kien nut "Luu"
		$("#btnLuu").bindOnce("click", function() {
			isLuuVaPhatHanhHDDT = false; // L2PT-33791
			isKySoTamUng = false; // L2PT-53446
			// L2PT-557 start
			if (checkDuyetDLS() >= 1) {
				DlgUtil.showMsg("Có đơn thuốc chưa duyệt dược lâm sàng");
				return;
			}
			// L2PT-557 end
			if (VPI_XACNHAN_TAOPHIEU == 1) {
				DlgUtil.showConfirm("Xác nhận thu tiền", function(flag) {
					if (flag) {
						checkthutien();
					}
				});
			} else {
				checkthutien();
			}
		}, 1000);
		// xy ly su kien nut "Luu"
		$("#btnLuuVaPH").bindOnce("click", function() {
			isLuuVaPhatHanhHDDT = true; // L2PT-33791
			isKySoTamUng = false; // L2PT-53446
			// L2PT-557 start
			if (checkDuyetDLS() >= 1) {
				DlgUtil.showMsg("Có đơn thuốc chưa duyệt dược lâm sàng");
				return;
			}
			// L2PT-557 end
			if (VPI_XACNHAN_TAOPHIEU == 1) {
				DlgUtil.showConfirm("Xác nhận thu tiền", function(flag) {
					if (flag) {
						checkthutien();
					}
				});
			} else {
				checkthutien();
			}
		}, 1000);
		// L2PT-53446 start
		$("#btnLuuVaKySo").bindOnce("click", function() {
			isLuuVaPhatHanhHDDT = false; // L2PT-53446
			isKySoTamUng = true; // L2PT-53446
			// L2PT-557 start
			if (checkDuyetDLS() >= 1) {
				DlgUtil.showMsg("Có đơn thuốc chưa duyệt dược lâm sàng");
				return;
			}
			// L2PT-557 end
			if (VPI_XACNHAN_TAOPHIEU == 1) {
				DlgUtil.showConfirm("Xác nhận thu tiền", function(flag) {
					if (flag) {
						checkthutien();
					}
				});
			} else {
				checkthutien();
			}
		}, 1000);
		// L2PT-114388 start
		$("#btnLuuNhanh").click(function() {
			if (checkDuyetDLS() >= 1) {
				DlgUtil.showMsg("Có đơn thuốc chưa duyệt dược lâm sàng");
				return;
			}
			checkthutien();
		});
		// L2PT-114388 end
		// L2PT-53446 end
		// check thu tien
		function checkthutien() {
			if (_hetphieu_2) {
				DlgUtil.showMsg("Hết phiếu Hóa đơn");
				return;
			}
			if (_hetphieu) {
				DlgUtil.showMsg("Hết phiếu Tạm ứng/Thu tiền/Hoàn ứng");
				return;
			}
			var _loaiphieuthu = $("#cboLOAIPHIEUTHUID").val();
			// kiểm tra nếu tổng tiền BH > 15%TLCB và có hóa đơn không đồng thì không cho thu
			if (_loaiphieuthu != _TAMUNG) {
				var _check_hdkd = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.CHECK.HDKD', _tiepnhanid);
				if (_check_hdkd == 1) {
					DlgUtil.showMsg("Yêu cầu hủy hóa đơn không đồng");
					return;
				}
			}
			// BVTM-5573 start
			/*
			var _tien_chot = _vpData.VIENPHI - _vpData.DANOP - _vpData.MIENGIAM;
			// BVTM-4415 start
			if (HIS_USING_GOIKCB_TYPE == '3' && _benhnhan.GKBNID) {
				_tien_chot = _tien_chot - (parseFloat(get_val_m('txtTAMUNG_BD')) - parseFloat(get_val_m('txtCON_TAMUNG_BD')));
			}
			// BVTM-4415 end
			*/
			var arr_tonghd = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI.TONGHD.BN', _tiepnhanid);
			if (arr_tonghd && arr_tonghd.length > 0) {
				_tien_chot = _vpData.VIENPHI - parseFloat(arr_tonghd[0].THUCTHU_HD) - _vpData.MIENGIAM;
				_tien_chot = _tien_chot.toFixed(2);
			} else {
				DlgUtil.showMsg("Không lấy được thông tin hóa đơn bệnh nhân");
				return;
			}
			// BVTM-5573 end
			if (VPI_QUYTRINH_VIENPHI != 1 && _loaiphieuthu != _TAMUNG && _benhnhan.TRANGTHAITIEPNHAN >= 1 && _chot && _tien_hoadon > 0 && _tien_hoadon != _tien_chot) {
				DlgUtil.showConfirm("Số tiền trên hóa đơn không khớp với các phiếu thu khác, bạn có chắc chắn tiếp tục ?", function(flag) {
					if (flag) {
						popupWServiceInit(_loaiphieuthu);
					}
				});
			} else {
				popupWServiceInit(_loaiphieuthu);
			}
		}
		// Thu tien
		function thutien(_loaipt) {
			objData["THU_NTU"] = "0";
			objData["THUTIEN_THUKHAC"] = "0"; // L2PT-7826_L2PT-7095 
			// check khoa phong
			if (!_khoa_id || !_phong_id || _khoa_id == 0 || _phong_id == 0) {
				DlgUtil.showMsg("Chưa thiết lập khoa phòng");
				return;
			}
			var validator = new DataValidator("valDiv");
			var valid = validator.validateForm();
			if (!valid)
				return false;
			// L2PT-27385 start
			var VPI_TT_CHECK_NGAYRV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_TT_CHECK_NGAYRV');
			if (VPI_TT_CHECK_NGAYRV == '1' && _benhnhan.TRANGTHAITIEPNHAN == 1) {
				var sysdate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
				if (stringToDateTime(_benhnhan.NGAYRAVIEN) > stringToDateTime(sysdate)) {
					DlgUtil.showMsg("Không thể thu tiền cho bệnh nhân có ngày ra viện lớn hơn thời gian hiện tại");
					return;
				}
			}
			// L2PT-27385 end
			// duonghn fix 220115 start
			// L2PT-74790 L2PT-67262 : cấu hình thu theo khoa
			var VPI_THUTIEN_DTNGT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_THUTIEN_DTNGT');
			if (fConfig.VPI_THUVP_THEOKHOA != '1' && VPI_THUTIEN_DTNGT == '1' && _benhnhan.TRANGTHAITIEPNHAN == 0 && _benhnhan.LOAITIEPNHANID == 3 && (_loaipt == _THUTHEM || _loaipt == _HOANUNG)) {
				DlgUtil.showMsg("Không thể thu tiền cho bệnh nhân điều trị ngoại trú chưa đóng bệnh án");
				return;
			}
			// duonghn fix 220115 end
			if (flagLoading && (_loaipt == _TAMUNG || _loaipt == _HOANDICHVU || _loaipt == _HOANUNG || _co_dv)) {
				// L2PT-12562 start
				if (_loaipt != _TAMUNG) {
					var _tv = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.CHECK.TRONVIEN", _benhnhan.BENHNHANID);
					if (_tv >= 1) {
						DlgUtil.showMsg("Cần xóa trốn viện để thu tiền");
						return;
					}
				}
				// L2PT-12562 end
				//Begin_HaNv_06062020: Chặn không thu tiền nội trú với bệnh nhân chưa trả hết đồ vải - L2PT-22011
				if (_loaipt != _TAMUNG) {
					// L2PT-19678 start
					var VPI_KIEMTRA_DOMUON = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_KIEMTRA_DOMUON');
					if (VPI_KIEMTRA_DOMUON == '0') {
						var res = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU01H001.EV026", _benhnhan.HOSOBENHANID);
						if (parseInt(res) > 0) {
							DlgUtil.showMsg(' Bệnh nhân chưa trả hết đồ mượn');
							return;
						} else if (res == '-1') {
							return DlgUtil.showMsg('Có lỗi khi xử lý check đồ mượn');
							return;
						}
					}
					// L2PT-19678 end
				}
				//End_HaNv_06062020
				var _t_tien = get_val_m('txtTONGTIEN');
				if (isNaN(_t_tien)) {
					DlgUtil.showMsg("Số tiền phải là số");
					$("#txtTONGTIEN").focus();
					return;
				}
				var _m_g = get_val_m('txtMIENGIAM_PT');
				if (isNaN(_m_g)) {
					DlgUtil.showMsg("Miễn giảm phải là số");
					$("#txtMIENGIAM_PT").focus();
					return;
				} else {//L2PT-13906 start 
					var _mg_n = parseFloat(_m_g);
					var _lydo_mg = $("#txtLYDO").val();
					if (_mg_n != 0 && _lydo_mg == "") {
						DlgUtil.showMsg("Chưa nhập lý do miễn giảm");
						return;
					}
				}
				var _sotien = get_val_m('txtTHUCTHU');
				_sotien = parseFloat(_sotien); // L2PT-24208
				if (isNaN(_sotien)) {
					DlgUtil.showMsg((_loaipt == _HOANUNG || _loaipt == _HOANDICHVU) ? "Hoàn ứng" : "Thực thu" + " phải là số");
					$("#txtTHUCTHU").focus();
					return;
				}
				if (_sotien < 0 || (_sotien == 0 && !_co_dv)) {
					DlgUtil.showMsg("Hãy nhập số tiền");
					return;
				}
				if (_loaipt == _THUTIEN) {} else if (_loaipt == _THUTHEM) {
					//Begin_HaNv_23042020: Cho phép thu tiền nếu tất cả dịch vụ đã chọn là thu khác - L2PT-20080
					var checkAllTK = false; // duonghn fix 230519
					if (_allow_thukhac) {
						checkAllTK = true; // duonghn fix 230519
						var ids = $("#" + _gridId_DV).jqGrid("getGridParam", "selarrrow");
						for (var i = 0; i < ids.length; i++) {
							var _loainhommbp = $("#" + _gridId_DV).jqGrid('getCell', ids[i], 'LOAINHOMMAUBENHPHAM');
							var _tachhoadon = $("#" + _gridId_DV).jqGrid('getCell', ids[i], 'TACHHOADON');
							// L2PT-940 start
							if (VP_TACH_HOADON == '5') {
								if (_tachhoadon != '1') {
									checkAllTK = false;
									break;
								}
							} else
							// L2PT-940 end
							if (_loainhommbp != '17') {
								checkAllTK = false;
								break;
							}
						}
					}
					//End_HaNv_23042020
					if (_benhnhan.LOAITIEPNHANID == 0) {
						if (_benhnhan.TRANGTHAITIEPNHAN == 0) {
							if (VPI_THUTIEN_NTU == 1 || (VPI_THUTIEN_NTU == 2 && _benhnhan.NHAPNOITRU == 1)) {
								objData["THU_NTU"] = "1";
							} else {
								//Begin_HaNv_23042020: Cho phép thu tiền nếu tất cả dịch vụ là thu khác - L2PT-20080
								// L2PT-74790 L2PT-67262 : cấu hình thu theo khoa
								if (!checkAllTK) {
									if (fConfig.VPI_THUVP_THEOKHOA != '1') {
										DlgUtil.showMsg("Không thể thu tiền cho bệnh nhân nội trú chưa ra viện");
										return;
									}
								} else {
									objData["THUTIEN_THUKHAC"] = "1"; // L2PT-7826_L2PT-7095 
								}
								//End_HaNv_23042020
							}
						}
						if (_benhnhan.DOITUONGBENHNHANID == 1 && _benhnhan.TRANGTHAITIEPNHAN != 2 && VPI_DUYETBH_THANHTOANNTU == 1 && parseFloat(_vpData.BHYT_THANHTOAN) != 0) { //L2PT-17515
							DlgUtil.showMsg("Không thể thanh toán cho bệnh nhân nội trú chưa duyệt bảo hiểm");
							return;
						}
					} else {
						if (VPI_XACNHAN_THUTIEN == 1 && _benhnhan.DOITUONGBENHNHANID == 1) {
							var _str_xacnhanbh = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.CHECK.XNBH", [ {
								name : '[0]',
								value : _tiepnhanid
							} ]);
							var _arr_xacnhanbh = JSON.parse(_str_xacnhanbh);
							if (!_arr_xacnhanbh || _arr_xacnhanbh.length == 0) {
								DlgUtil.showMsg("Lấy thông tin xác nhận bảo hiểm không thành công");
								return;
							}
							var _xacnhanbh = _arr_xacnhanbh[0].XACNHANBH;
							if (_xacnhanbh == 0) {
								DlgUtil.showMsg("Không thể thu tiền cho BN chưa xác nhận bảo hiểm");
								return;
							}
						}
					}
				} else if (_loaipt == _TAMUNG) {
					// kiem tra da dong benh an khong cho tam ung
					if (_benhnhan.TRANGTHAITIEPNHAN >= 1) {
						DlgUtil.showMsg("Đã đóng bệnh án, không thể tạm ứng");
						return;
					}
					// L2PT-82006 start
					else {
						if (VPI_QUYTRINH_VIENPHI == 4 && _benhnhan.DOITUONGBENHNHANID == 1) {
							if (_benhnhan.LYDO_VAOVIEN != 3) {
								DlgUtil.showMsg("Chỉ được tạm ứng với BN BHYT trái tuyến");
								return;
							}
						}
					}
					// L2PT-82006 end
					if ((HIS_KHONG_TAMUNG_NGOAITRU == 1 && _benhnhan.LOAITIEPNHANID != 0) || (HIS_KHONG_TAMUNG_NGOAITRU == 2 && _benhnhan.LOAITIEPNHANID == 1) ||
							(HIS_KHONG_TAMUNG_NGOAITRU == 3 && _benhnhan.LOAITIEPNHANID == 3)) {
						var _TUNGT = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI_LAY_QUYEN', _phong_id + "$" + 'TUNGT');
						if (_TUNGT == 0) {
							DlgUtil.showMsg("Không cho phép bệnh nhân ngoại trú tạm ứng");
							return;
						}
					}
					if (_benhnhan.NHAPNOITRU == 1) {
						if (VPI_TAMUNG_CHONHAPKHOA == 0) {
							DlgUtil.showMsg("Không cho phép bệnh nhân đang chờ nhập khoa tạm ứng");
							return;
						}
					}
				} else if (_loaipt == _HOANUNG) {
					// L2PT-33290 start
					if (_benhnhan.TRANGTHAITIEPNHAN == 0) {
						var VPI_HOANUNG_DBA = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HOANUNG_DBA');
						if (VPI_HOANUNG_DBA == '0' || (VPI_HOANUNG_DBA == '1' && _benhnhan.LOAITIEPNHANID != 1) || (VPI_HOANUNG_DBA == '2' && _benhnhan.LOAITIEPNHANID != 3) ||
								(VPI_HOANUNG_DBA == '3' && _benhnhan.LOAITIEPNHANID != 0) || (VPI_HOANUNG_DBA == '4' && _benhnhan.LOAITIEPNHANID == 0) ||
								(VPI_HOANUNG_DBA == '5' && _benhnhan.LOAITIEPNHANID == 1)) {
							DlgUtil.showMsg("Không thể hoàn ứng cho bệnh nhân chưa ra viện");
							return;
						}
					}
					//L2PT-33290 end
					if (_vpData.TAMUNG == 0) {
						DlgUtil.showMsg("Không thể hoàn ứng cho bệnh nhân không có tạm ứng");
						return;
					}
					if (VPI_HOANUNG_NGT == 1) {
						if (_benhnhan.LOAITIEPNHANID == 1) {
							DlgUtil.showMsg("Không thể hoàn ứng cho bệnh nhân khám bệnh ngoại trú");
							return;
						}
					}
					if (VPI_QUYTRINH_VIENPHI == 2 && _benhnhan.LOAITIEPNHANID != 0 && _benhnhan.TRANGTHAITIEPNHAN == 0) {
						DlgUtil.showMsg("Không thể hoàn ứng cho bệnh nhân ngoại trú chưa ra viện");
						return;
					}
					if (VPI_HOANUNG_NGT == 1 && _benhnhan.LOAITIEPNHANID == 3) {
						if (_benhnhan.TRANGTHAITIEPNHAN == 0) {
							DlgUtil.showMsg("Không thể hoàn ứng cho bệnh nhân điều trị ngoại trú chưa ra viện");
							return;
						}
					}
					if (_benhnhan.LOAITIEPNHANID == 0) {
						if (_benhnhan.TRANGTHAITIEPNHAN == 0) {
							if (_benhnhan.DOITUONGBENHNHANID != 1 && (VPI_THUTIEN_NTU == 1 || (VPI_THUTIEN_NTU == 2 && _benhnhan.NHAPNOITRU == 1))) {
								objData["THU_NTU"] = "1";
							} else {
								DlgUtil.showMsg("Không thể hoàn ứng cho bệnh nhân nội trú chưa ra viện");
								return;
							}
						}
						if (_benhnhan.DOITUONGBENHNHANID == 1 && _benhnhan.TRANGTHAITIEPNHAN != 2 && VPI_DUYETBH_THANHTOANNTU == 1 && parseFloat(_vpData.BHYT_THANHTOAN) != 0) { //L2PT-17515
							DlgUtil.showMsg("Không thể thanh toán cho bệnh nhân nội trú chưa duyệt bảo hiểm");
							return;
						}
					}
					var _tien_conlai = -1 * parseFloat(_vpData.NOPTHEM);
					_tien_conlai = (_tien_conlai + parseFloat(_m_g)).toFixed(2);
					// L2PT-24208 start
					_tien_conlai = parseFloat(_tien_conlai);
					var _chua_thu = _vpData.CHUATHU;
					var VPI_LOG_HU = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_LOG_HU');
					if ((VPI_LOG_HU == '2' || VPI_LOG_HU == '1') && _chua_thu > 0 && _phieuInfo.length == 0) {
						var ids = $("#" + _gridId_DV).getDataIDs();
						var selarrrow = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
						var objLogVP = new Object();
						objLogVP.IDLOG = _tiepnhanid;
						objLogVP.TENTRUONG = 'TIEPNHANID';
						objLogVP.NOIDUNG = "TIEPNHANID:" + _benhnhan.TIEPNHANID + ";VPDATA.NOPTHEM: " + _vpData.NOPTHEM + ";VPDATA.CHUATHU: " + _vpData.CHUATHU + ";DV:" + ids.length + ";DV_CHON:" +
								selarrrow.length;
						objLogVP.GHICHU = 'LOGHoanUng';
						var str_log_vp = JSON.stringify(objLogVP);
						var retLogVP = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.LOG', str_log_vp);
						if (VPI_LOG_HU == '2') {
							DlgUtil.showMsg("Không phát sinh hóa đơn, vui lòng kiểm tra lại");
							return;
						}
					}
					// L2PT-24208 end
					if ((VPI_QUYTRINH_VIENPHI != 1 && _sotien > _tien_conlai) || (VPI_QUYTRINH_VIENPHI == 1 && parseFloat(_sotien) + parseFloat(_vpData.HOANUNG) > parseFloat(_vpData.TAMUNG))) {
						DlgUtil.showMsg("Không thể hoàn ứng số tiền trên");
						return;
					}
				} else if (_loaipt == _HOANDICHVU && VPI_HOANDICHVU != 1) {
					DlgUtil.showMsg("Không cho phép hoàn dịch vụ");
					return;
				}
				var ngay_thu = $("#txtNGAYLAP").val();
				// L2PT-7254 start
				// L2PT-83041 start: BN check in online (VNCARE) thì không chặn
				if (stringToDateTime(ngay_thu) <= stringToDateTime(_benhnhan.NGAYTIEPNHAN) && _benhnhan.CHECKIN_VNCARE != '-1') {
					DlgUtil.showMsg("Ngày thu phải hớn hơn ngày vào viện");
					return;
				}
				// L2PT-83041 end
				// L2PT-7254 end
				var check_notien = $("#chkNoTien").is(':checked') ? "1" : "0";
				objData["NOTIEN"] = check_notien;
				objData["CHOT"] = _chot ? '1' : '0';
				objData["BENHNHANID"] = _benhnhan.BENHNHANID + "";
				objData["HOSOBENHANID"] = _benhnhan.HOSOBENHANID + "";
				objData["NGAYTHU"] = ngay_thu;
				objData["NGUOIDUNGID"] = _user_id; // L2PT-53692
				objData["KHOAID_THU"] = _khoa_id + "";
				objData["PHONGID_THU"] = _phong_id + "";
				objData["KHOAID"] = _loaipt == _TAMUNG ? _benhnhan.KHOAID_NK : _benhnhan.KHOAID;
				objData["PHONGID"] = _loaipt == _TAMUNG ? _benhnhan.PHONGID_NK : _benhnhan.PHONGID;
				objData["CHIETKHAU"] = '0';
				objData["DATRA"] = get_val_m('txtTHUCTHU') + ""; // fix QRCODE
				objData["THUCTHU"] = get_val_m('txtTHUCTHU') + ""; // fix QRCODE
				objData["CONNO"] = '0';
				objData["CONNO_HENTHANHTOAN"] = "01/01/0001 00:00:00";
				objData["MAHOADULIEU"] = '0';
				objData["TENBENHNHAN"] = _benhnhan.TENBENHNHAN;
				objData["DAHUYPHIEU"] = '0';
				objData["DAYEUCAU"] = '0';
				objData["KHAC"] = '0';
				objData["THOIGIANHUYPHIEU"] = "01/01/0001 00:00:00";
				objData["NOIDUNGTHU"] = $("#txtGHICHU").val();
				objData["NOIDUNGIN"] = $("#txtGHICHU").val();
				objData["MIENGIAM"] = get_val_m('txtMIENGIAM_PT');
				objData["LYDOMIENGIAM"] = $("#txtLYDO").val();
				// L2PT-14004 start
				var _hinhthuc_tt = $("#cboHINHTHUCTHANHTOAN").val();
				// L2PT-31437 start
				var _id_post = "";
				if (VPI_HIEN_IDPOST == '1') {
					_id_post = $("#txtIDPOST").val();
				} else if (VPI_HIEN_IDPOST == '2') {
					_id_post = $("#cboPOS").val();
				}
				if (VPI_HIEN_IDPOST != '0' && _hinhthuc_tt == 5 && _id_post == "") {
					DlgUtil.showMsg("Chưa nhập mã thẻ hoặc POS");
					return;
				}
				// BVTM-3849 start
				var _id_dvtt = "";
				if (_hinhthuc_tt == 5 || _hinhthuc_tt == 12) { // L2PT-4355 : them loai tt = 5
					_id_dvtt = $("#cboDVTT").val();
					if (_id_dvtt == "") {
						DlgUtil.showMsg("Chưa chọn đơn vị thanh toán QR Code/POS");
						return;
					}
				}
				objData["IDDVTT"] = _id_dvtt;
				// L2PT-59668 start
				if (_hinhthuc_tt == 2) { // L2PT-4355 : them loai tt = 5
					objData["NGANHANGID"] = $("#cboNganHang").val();
				}
				// L2PT-59668 end
				// BVTM-3849 end
				// L2PT-31437 end
				objData["HINHTHUCTHANHTOAN"] = _hinhthuc_tt;
				objData["IDPOST"] = _id_post;// L2PT-7489
				// L2PT-14004 end
				// objData["LOAIPHIEUTHU"] = _billmode;
				objData["LOAIPHIEUTHUID"] = _loaipt + "";
				objData["PHIEUTAMUNGID"] = _loaipt == _TAMUNG ? _phieutamungid : null + "";
				objData["NGUONHACHTOAN"] = '0';
				objData["KHOACHUYENDEN"] = '0';
				objData["PHONGHACHTOAN"] = '0';
				objData["BENHNHAN_KDV"] = _benhnhan.BENHNHAN_KDV; // L2PT-21633: BN khám DV
				obj.TONGTIEN = get_val_m('txtTONGTIEN') + "";
				obj.THUCTHU = get_val_m('txtTHUCTHU') + "";
				obj.NOIDUNGTHU = $("#txtGHICHU").val();
				obj.NOIDUNGIN = $("#txtGHICHU").val();
				obj.NHOMTHANHTOAN = "";
				if (parseInt(obj.MAPHIEUTHU) > parseInt(obj.SOPHIEUTO)) {
					DlgUtil.showMsg("Số phiếu lớn hơn số phiếu lớn nhất của quyển sổ");
					return;
				}
				//tuyennx_add_start_20171121 yc HISL2CORE-599
				if (parseInt(obj.MAPHIEUTHU) > parseInt(obj.KHOASOPHIEUTU)) {
					DlgUtil.showMsg("Số phiếu đã bị khóa");
					return;
				}
				//tuyennx_add_end_20171121 yc HISL2CORE-599
				if (parseInt(obj.MAPHIEUTHU) < parseInt(obj.SOPHIEUFROM)) {
					DlgUtil.showMsg("Số phiếu nhỏ hơn số phiếu nhỏ nhất của quyển sổ");
					return;
				}
				var _phieu = obj;
				var _dsPhieu = [];
				if (true)//_sotien>0)
					_dsPhieu.push(_phieu);
				var _offset = 0;
				if (_phieuInfo.length > 0) {
					_offset = parseInt($("#txtMAPHIEUTHU").val()) - parseInt(_phieuInfo[_phieuInfo.length - 1].MAPHIEUTHU);
					if (VPI_PHIEUTHU_DIKEM_HOADON == 1 && obj.LOAIPHIEUTHUID == _THUTHEM) {
						var _nhomphieuthuid = _phieuInfo[0].NHOMPHIEUTHUID;
						var _count = 0;
						for (var j = 0; j < _dsSo.length; j++) {
							if (_dsSo[j].NHOMPHIEUTHUID_ORG == _nhomphieuthuid) {
								obj.MAPHIEUTHU = _dsSo[j].MAPHIEUTHU;
								obj.SOPHIEUTO = _dsSo[j].SOPHIEUTO;
								obj.KHOASOPHIEUTU = _dsSo[j].KHOASOPHIEUTU;
								obj.SOPHIEUFROM = _dsSo[j].SOPHIEUFROM;
								obj.NHOMPHIEUTHUID = _dsSo[j].NHOMPHIEUTHUID;
								obj.MANHOMPHIEUTHU = _dsSo[j].MANHOMPHIEUTHU;
								obj.LOAIPHIEUTHU = _dsSo[j].LOAIPHIEUTHU;
								_count = 1;
							}
						}
						if (_count == 0) {
							DlgUtil.showMsg("Chưa có sổ thu tiền đi kèm hóa đơn");
							return;
						}
					}
				}
				var _arr_so_id = [];//khoi tao mang cac so
				for (var i = 0; i < _phieuInfo.length; i++) {
					//bat dau kiem tra neu phieu cung 1 so thi tang so 
					var _so_ht = _phieuInfo[i].NHOMPHIEUTHUID;
					var _tang_so = countElt(_arr_so_id, _so_ht);
					_phieuInfo[i].MAPHIEUTHU = parseInt(_phieuInfo[i].MAPHIEUTHU) + _offset + _tang_so + "";
					_arr_so_id.push(_so_ht);
					//ket thuc kiem tra neu phieu cung 1 so thi tang so
					if (parseInt(_phieuInfo[i].MAPHIEUTHU) > parseInt(_phieuInfo[i].SOPHIEUTO)) {
						DlgUtil.showMsg("Số phiếu lớn hơn số phiếu lớn nhất của quyển sổ");
						return;
					} else if (parseInt(_phieuInfo[i].MAPHIEUTHU) < parseInt(_phieuInfo[i].SOPHIEUFROM)) {
						DlgUtil.showMsg("Số phiếu nhỏ hơn số phiếu nhỏ nhất của quyển sổ");
						return;
						//tuyennx_add_start_20171121 yc HISL2CORE-599
					} else if (parseInt(_phieuInfo[i].MAPHIEUTHU) >= parseInt(_phieuInfo[i].KHOASOPHIEUTU)) {
						DlgUtil.showMsg("Số phiếu bị khóa");
						return;
						//tuyennx_add_end_20171121 yc HISL2CORE-599
					} else {
						// L2PT-21870 start
						if (VPI_SINH_HDKD != '1' || parseFloat(_phieuInfo[i].THUCTHU) != 0) {
							_dsPhieu.push(_phieuInfo[i]);
						}
						// L2PT-21870 end
					}
				}
				if (_dsPhieu.length == 0)
					return;
				objData["DSPHIEU"] = _dsPhieu;
				objData["SL_HOADON"] = (_phieuInfo ? _phieuInfo.length : 0) + "";
				objData["TLMIENGIAM"] = get_val_m('txtTLMIENGIAM') + "";
				objData["TONGTIEN_HD"] = _tien_hoadon + "";
				objData["DS_MIENGIAM"] = _arrMG;
				//L2PT-19304 start
				if (_arrHTTT.length == 0) {
					var _httt_id = $("#cboHINHTHUCTHANHTOAN").val();
					var _ten_httt = $("#cboHINHTHUCTHANHTOAN option:selected").html();
					var objHTTT = new Object();
					objHTTT.HINHTHUCTHANHTOANID = _httt_id + "";
					objHTTT.TENHINHTHUC = _ten_httt;
					objHTTT.SOTIEN = get_val_m('txtTHUCTHU');
					objHTTT.DOITUONG = "";
					objHTTT.NOIDUNG = "";
					objHTTT.IDTHE = "";
					objHTTT.MATHE = "";
					_arrHTTT.push(objHTTT);
				}
				objData["DS_HTTT"] = _arrHTTT;
				//L2PT-19304 end
				console.log("------------------------------------------");
				var input = JSON.stringify(objData);
				console.log(input);
				// L2PT-33791 thêm btnLuuVaPH
				// L2PT-53446 thêm btnLuuVaKySo
				setEnabled([], [ 'btnLuu', 'btnLuuVaPH', 'btnLuuVaKySo' ]);
				// VNPT PAY START
				var rAmount = fConfig.VPI_LAMTRON_TTKTM == '1' ? Math.round(objData.THUCTHU) : Math.ceil(objData.THUCTHU); // L2PT-123128
				var _httt = $("#cboHINHTHUCTHANHTOAN").val();
				if (_httt == 10) {
					var _sql_par = [];
					var _noidung;
					switch (parseInt(_loaipt)) {
						case _THUTHEM:
							_noidung = 'Thu tien vien phi';
						break;
						case _HOANUNG:
							_noidung = 'Hoan tien';
						break;
						case _TAMUNG:
							_noidung = 'Thu tien tam ung';
						break;
						case _HOANDICHVU:
							_noidung = 'Hoan tien dich vu';
						break;
						default:
							_noidung = 'Thu tien';
					}
					_sql_par.push({
						name : 'loaiphieu',
						type : 'String',
						value : _loaipt
					});
					_sql_par.push({
						name : 'tiepnhanid',
						type : 'String',
						value : _tiepnhanid
					});
					_sql_par.push({
						name : 'sotien',
						type : 'String',
						value : rAmount
					// L2PT-123128
					// fix QRCODE
					});
					_sql_par.push({
						name : 'noidung',
						type : 'String',
						value : _noidung
					});
					/*
					_sql_par.push({
						name : 'qrcode',
						type : 'String',
						value : _noidung
					});
					*/
					if (_loaipt == _HOANUNG || _loaipt == _HOANDICHVU) {// HOAN TIEN QUA PAYMENT GATEWAY
						// L2PT-1555 start
						var _loaiPTGoc = _loaipt == _HOANUNG ? _TAMUNG : _THUTHEM;
						var arrHoanUng = [];
						var arrOrder = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.DS.TU.VNPTPAY", _tiepnhanid + "$" + _loaiPTGoc);
						var tienHoanUng = get_val_m('txtTHUCTHU');
						var VPI_HOANTRA_VNPT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HOANTRA_VNPT');
						if (VPI_HOANTRA_VNPT == 0) {
							DlgUtil.showMsg('Không hỗ trợ hoàn trả qua VNPT PAY');
						} else if (VPI_HOANTRA_VNPT == 1) { // tự động hoàn trả
							arrHoanUng = [];
							for (var i = 0; i < arrOrder.length; i++) {
								var conLai = tienHoanUng - arrOrder[i].DATRA;
								var objHoanUng = arrOrder[i];
								var refundType = 1;
								if (conLai <= 0) {
									objHoanUng.HOANTRA = tienHoanUng + "";
									if (conLai < 0) {
										refundType = 2;
									}
									objHoanUng.REFUNDTYPE = refundType;
									arrHoanUng.push(objHoanUng);
									break;
								} else {
									objHoanUng.HOANTRA = arrOrder[i].DATRA;
									tienHoanUng = conLai;
									objHoanUng.REFUNDTYPE = refundType;
									arrHoanUng.push(objHoanUng);
								}
							}
							payRefundArr(objData, arrHoanUng);
						} else { // điền số tiền hoàn trả theo từng phiếu
							dlgPopup = DlgUtil.buildPopupGrid("dlgCHONPT", 'DS_PT_VNPTPAY', "Chọn phiếu", 512, 510);
							var _gridHeader_CHONPT = "ORDERID,ORDERID,100,0,t,l,0;" + "TXNID,TXNID,100,0,t,l,0;" + "QRTXNID,QRTXNID,100,0,t,lr,0;" + "Mã phiếu thu,MAPHIEUTHU,100,0,f,l,0;"
									+ "Số tiền,DATRA,100,0,f,r,0;" + "Hoàn Trả,HOANTRA,100,0,e,r,0";
							GridUtil.init('DS_PT_VNPTPAY', "500", "350", "", false, _gridHeader_CHONPT);
							var grid = $("#" + 'DS_PT_VNPTPAY');
							var arrOrder = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.DS.TU.VNPTPAY", _tiepnhanid + "$" + _loaiPTGoc);
							GridUtil.fetchGridData('DS_PT_VNPTPAY', arrOrder);
							grid.setSelection(1);
							GridUtil.markRow('DS_PT_VNPTPAY', 1);
							dlgPopup.open();
							// xu ly su kien nhap tien hoan tra
							grid.bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
								var _tien_dt = grid.jqGrid('getCell', rowid, 'DATRA');
								_tien_dt = parseFloat(_tien_dt);
								var _tien_ht = grid.jqGrid('getCell', rowid, 'HOANTRA');
								_tien_ht = parseFloat(_tien_ht);
								if (/^\d+$/i.test(_tien_ht) && _tien_ht > 0 && _tien_ht <= _tien_dt) {
									grid.setSelection(rowid);
									GridUtil.unmarkAll('DS_PT_VNPTPAY');
									GridUtil.markRow('DS_PT_VNPTPAY', rowid);
								} else {
									grid.jqGrid('setCell', rowid, 'HOANTRA', null);
								}
							});
							GridUtil.setGridParam('DS_PT_VNPTPAY', {
								onSelectRow : function(id) {
									GridUtil.unmarkAll('DS_PT_VNPTPAY');
									GridUtil.markRow('DS_PT_VNPTPAY', id);
								},
								gridComplete : function(id) {}
							});
							var btnOK = $('<input class="btn btn-sm btn-primary" id="btn_CHONPT_OK" type="button" style="position:absolute;bottom:10px;left:10px" value="Đồng ý" />');
							var btnClose = $('<input class="btn btn-sm btn-primary" id="btn_CHONPT_Close" type="button" style="position:absolute;bottom:10px;right:10px" value="Đóng" />');
							$('#dlgCHONPT').append(btnOK);
							$('#dlgCHONPT').append(btnClose);
							btnOK.click(function() {
								arrHoanUng = [];
								var ids = grid.getDataIDs();
								for (var i = 0; i < ids.length; i++) {
									var objHoanUng = grid.jqGrid('getRowData', ids[i]);
									var _tien_dt = parseFloat(objHoanUng.DATRA);
									var _tien_ht = parseFloat(objHoanUng.HOANTRA);
									if (_tien_ht && _tien_ht > 0) {
										var refundType = 1;
										if (_tien_dt > _tien_ht) {
											refundType = 2;
										} else if (_tien_dt == _tien_ht) {
											refundType = 1;
										} else {
											DlgUtil.showMsg('Tiền hoàn trả không được lớn hơn tiền đã thu');
											return false;
										}
										objHoanUng.REFUNDTYPE = refundType;
										arrHoanUng.push(objHoanUng);
										console.log(arrHoanUng);
										//DlgUtil.showMsg("GUI SANG GATEWAY");
										//DlgUtil.showMsg("GUI SANG GATEWAY 2: OrderID:" + arrHoanUng[0].ORDERID + "; Tien hoan: " + arrHoanUng[0].HOANTRA );
									} else {
										DlgUtil.showMsg('Tiền hoàn trả không hợp lệ');
										return false;
									}
								}
								if (tongHoanTra != tienHoanUng) {
									return false;
									DlgUtil.showMsg('Số tiền hoàn trả không hợp lệ');
								}
								payRefundArr(objData, arrHoanUng);
							});
							btnClose.click(function() {
								dlgPopup.close();
							});
						}
						// L2PT-1555 end
					} else {// THANH TOAN QUA QRCODE
						var rs = paymentGenQrCode(objData);
						if (!rs) {
							DlgUtil.showMsg('ERR: Return NULL');
							return false;
						}
						if (rs.responseCode == '00') {
							_sql_par.push({
								name : 'qrcode',
								type : 'String',
								value : rs.qrCodeImage
							});
							// L2PT-2161 start
							var rpcodeQRCODE = '';
							if (_loaipt == _TAMUNG) {
								rpcodeQRCODE = 'NGT041_PHIEUTAMUNG_QRCODE_A4';
							} else {
								rpcodeQRCODE = 'NGT041_PHIEUTHUTIEN_QRCODE_A4'
							}
							CommonUtil.openReportGet('window', rpcodeQRCODE, 'pdf', _sql_par);
							// L2PT-2161 end
						}
						// Error
						if (_rs.responseCode != 0) {
							DlgUtil.showMsg('Lỗi: ' + _rs.responseMessage);
						}
					}
					$("#btnHuyBo").click();
					return;
				}
				// VNPT PAY END
				// BVTM-1770 start
				else if (_httt == 12) {
					var _sql_par = [];
					var _noidung;
					switch (parseInt(_loaipt)) {
						case _THUTHEM:
							_noidung = 'Thu tien vien phi';
						break;
						case _HOANUNG:
							_noidung = 'Hoan tien';
						break;
						case _TAMUNG:
							_noidung = 'Thu tien tam ung';
						break;
						case _HOANDICHVU:
							_noidung = 'Hoan tien dich vu';
						break;
						default:
							_noidung = 'Thu tien';
					}
					_sql_par.push({
						name : 'tiepnhanid',
						type : 'String',
						value : _tiepnhanid
					});
					_sql_par.push({
						name : 'sotien',
						type : 'String',
						value : rAmount
					// L2PT-123128
					// fix QRCODE
					});
					_sql_par.push({
						name : 'noidung',
						type : 'String',
						value : _noidung
					});
					// L2PT-80276 start
					if (_loaipt == _HOANUNG || _loaipt == _HOANDICHVU) { // case hoan tien
						if (fConfig.VPI_CHIHO_QRCODE == '1') {
							objData.CHIHO = "1";
							// L2PT-90923 start
							hoanTraKTM(_tiepnhanid, "", get_val_m('txtTHUCTHU'), objData);
							// L2PT-90923 end
						} else {
							DlgUtil.showMsg('Không hỗ trợ hoàn trả qua QRCODE');
						}
					}
					// L2PT-80276 end
					else {// case thanh toán
						// BVTM-3849 start
						//var iddvtt = $("#cboDVTT").val();
						var objT = new Object();
						objT.TNAME = 'DMC_DONVITHANHTOAN';
						objT.TMODE = '0';
						objT.TCOL = 'DONVITHANHTOANID-' + _id_dvtt;
						var input = JSON.stringify(objT);
						var tDVTT = jsonrpc.AjaxJson.ajaxCALL_SP_O('T.GET.DATA', input);
						if (!tDVTT || tDVTT.length == 0) {
							DlgUtil.showMsg('Không tồn tại đơn vị thanh toán này'); // BVTM-4061
							return false;
						}
						// L2PT-10089 start: thêm đơn vị thanh toán để in phiếu qrcode
						_sql_par.push({
							name : 'iddvtt',
							type : 'String',
							value : _id_dvtt
						});
						// L2PT-10089 end
						// BVTM-4061 start
						var description = {
							"CashierID" : _opts._param[7],
							"CashierName" : _opts._param[9],
							"MedicalCode" : _benhnhan.MAHOSOBENHAN,
							"PatientCode" : _benhnhan.MABENHNHAN,
							"PatientName" : _benhnhan.TENBENHNHAN
						};
						objData.DescriptionQR = description;
						// BVTM-4061 end
						var rs = genQrCode(objData, tDVTT[0].MALOAI, tDVTT[0].MADONVI);
						// BVTM-3849 end
						console.log(rs);
						// L2PT-39523 fix xử lý kết quả
						if (!rs) {
							DlgUtil.showMsg('Lỗi sinh QRCODE');
						} else if (rs.code == '00') {
							var qrBase64 = rs.data.qrCodeBase64
							QRCODEBASE64STR = qrBase64; // L2PT-71931
							// L2PT-28379 L2PT-61290 start
							if (VPI_HIENTHI_QRCODE == '1' || VPI_HIENTHI_QRCODE == '2') { // L2PT-31763 
								var paramInput = {
									qrCodeBase64 : rs.data.qrCodeBase64,
									// L2PT-100632 start
									thoiGian : rs.thoiGian,
									DONVITHANHTOANID : tDVTT[0].DONVITHANHTOANID,
									// L2PT-100632 end
									LOAIPHIEUTHUID : _loaipt,
									TENBENHNHAN : _benhnhan.TENBENHNHAN,
									MABENHNHAN : _benhnhan.MABENHNHAN,
									MAHOSOBENHAN : _benhnhan.MAHOSOBENHAN,
									SOTIEN : rAmount
								// L2PT-123128
								};
								hienThiQR(paramInput);
							}
							// L2PT-102404 start
							else if (VPI_HIENTHI_QRCODE == '3') {
								var loaiPhieu = vienphi_tinhtien.layLoaiPhieuThu(_loaipt);
								var message = fConfig.VPI_SHOW_QRCODE_MESSAGE.replaceAll('[MA_BA]', _benhnhan.MAHOSOBENHAN).replaceAll('[MA_BN]', _benhnhan.MABENHNHAN).replaceAll('[HO_TEN]',
										_benhnhan.TENBENHNHAN).replaceAll('[LOAI_PHIEU]', loaiPhieu).replaceAll('[SO_TIEN]', rAmount); // L2PT-123128
								var orderId = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.GEN.ORDERID", _hospital_code);
								var posterminalid = $("#cboPOS").val();
								var rs = pmmSale(orderId, '', '', posterminalid, '', '', {
									showQr : 1,
									qr_string : rs.data.qrCode,
									qr_title : fConfig.VPI_SHOW_QRCODE_TITLE,
									qr_message : message
								});
							}
							// L2PT-102404 end	
							// L2PT-108719 start
							else if (VPI_HIENTHI_QRCODE == '4') {
								var loaiPhieu = vienphi_tinhtien.layLoaiPhieuThu(_loaipt);
								var message = fConfig.VPI_SHOW_QRCODE_MESSAGE.replaceAll('[MA_BA]', _benhnhan.MAHOSOBENHAN).replaceAll('[MA_BN]', _benhnhan.MABENHNHAN).replaceAll('[HO_TEN]',
										_benhnhan.TENBENHNHAN).replaceAll('[LOAI_PHIEU]', loaiPhieu).replaceAll('[SO_TIEN]', rAmount); // L2PT-123128
								var orderId = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.GEN.ORDERID", _hospital_code);
								var posterminalid = $("#cboPOS").val();
								var rs = pmmSale(orderId, '', '', posterminalid, '', '', {
									codeApi : 'SEND_IPOS',
									qr_string : rs.data.qrCode,
									qr_title : fConfig.VPI_SHOW_QRCODE_TITLE,
									qr_message : message
								});
							}
							// L2PT-108719 end	
							else {
								_sql_par.push({
									name : 'qrcode',
									type : 'String',
									value : qrBase64
								});
								// L2PT-2161 start
								var rpcodeQRCODE = '';
								if (_loaipt == _TAMUNG) {
									rpcodeQRCODE = 'NGT041_PHIEUTAMUNG_QRCODE_A4';
								} else {
									rpcodeQRCODE = 'NGT041_PHIEUTHUTIEN_QRCODE_A4'
								}
								CommonUtil.openReportGet('window', rpcodeQRCODE, 'pdf', _sql_par);
								// L2PT-2161 end
							}
							// L2PT-28379 L2PT-61290 end
						}
						// Error
						else {
							DlgUtil.showMsg('Lỗi: ' + rs.message);
						}
					}
					$("#btnHuyBo").click();// fix vntpay chuyen tu form 1 sang bi loi
					return;
				}
				// BVTM-1770 end
				// START: Thanh toán qua POS
				else if (_httt == 5 && VPI_HIEN_IDPOST == '2') {
					// L2PT-1511 start
					if (_loaipt == _HOANUNG || _loaipt == _HOANDICHVU) {
						DlgUtil.showMsg("Không thể hoàn ứng qua POS");
						return false;
					}
					// L2PT-1511 end
					//L2PT-31437 start
					//L2PT-31437 start
					var orderId = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.GEN.ORDERID", _hospital_code);
					var hisUser = _user_name;
					var uuid = _opts._uuid;
					var amount = get_val_m('txtTHUCTHU');
					var posterminalid = $("#cboPOS").val();
					// L2PT-1511 start
					//var description = objData.NOIDUNGTHU;
					var description = "Thu tien vien phi " + _benhnhan.MAHOSOBENHAN;
					// L2PT-1511 end
					var f1 = '';
					var f2 = '';
					var f3 = '';
					var f4 = '';
					var f5 = '';
					// Gọi tới POS
					localStorage.setItem('IDPOST', posterminalid);
					// L2PT-1511 start
					var objLPOS = new Object();
					objLPOS.ORDERID = orderId + "";
					objLPOS.HISUSER = hisUser;
					objLPOS.TIEPNHANID = _tiepnhanid + "";
					objLPOS.UUID = uuid;
					objLPOS.POSTERMINALID = posterminalid + "";
					objLPOS.AMOUNT = amount + "";
					objLPOS.DESCRIPTION = description;
					objLPOS.F1 = f1;
					objLPOS.F2 = f2;
					objLPOS.F3 = f3;
					objLPOS.F4 = f4;
					objLPOS.F5 = f5;
					objLPOS.BILL = JSON.stringify(objData);
					luuLogGoiPOS(objLPOS, 'SEND');
					// L2PT-1511 end
					// L2PT-102404 start
					var rs = pmmSale(orderId, hisUser, uuid, posterminalid, amount, description, {
						f1 : f1,
						f2 : f2,
						f3 : f3,
						f4 : f4,
						f5 : f5
					});
					// L2PT-102404 end
					// L2PT-1511 start
					/*
					var rs = {
							CODE : "00",
							RESULT : {
								status : "DONE",
								transInfo : {
									transStatus : 'APPROVE',
									approvalCode : "approvalCode"
								},
								clientId : "clientId"
							}
						};
					*/
					objLPOS.POS_RET = JSON.stringify(rs);
					// L2PT-1511 end
					console.log('POSPayment', rs);
					if (rs.CODE != '00') {
						luuLogGoiPOS(objLPOS, 'GET'); // L2PT-1511
						DlgUtil.showMsg("Lỗi thanh toán POS: " + rs.MESSAGE);
						return false;
					}
					var transReturn = rs.RESULT;
					objLPOS.TRANS_RET_STATUS = transReturn.status; // L2PT-1511
					if (transReturn.status == 'CANCELED') {
						luuLogGoiPOS(objLPOS, 'GET'); // L2PT-1511
						DlgUtil.showMsg('Giao dịch thanh toán đã bị hủy bỏ.');
						return false;
					}
					if (transReturn.status == 'DONE') {
						var transInfo = transReturn.transInfo;
						objLPOS.TRANS_INF_STATUS = transInfo.transStatus; // L2PT-1511
						if (transInfo.transStatus == 'APPROVE') {
							// Nếu thành công thì gọi hàm tạo phieu
							objData.ORDERID = orderId; //L2PT-31437
							objData.MA_GD_CK = transInfo.approvalCode; // L2PT-34078// L2PT-1511
							// L2PT-1511 start
							objData.THANHTOAN_POS = "1";
							objLPOS.BILL = JSON.stringify(objData);
							objLPOS.TRANS_INF_APPROVALCODE = transInfo.approvalCode;
							luuLogGoiPOS(objLPOS, 'GET');
							// L2PT-1511 end
							taoPhieu(objData);
							DlgUtil.showMsg('Giao dịch thanh toán thành công tại ' + transReturn.clientId);
							return true;
						} else {
							DlgUtil.showMsg('Lỗi thanh toán POS. ' + transInfo.transStatus);
							return false;
						}
					}
					luuLogGoiPOS(objLPOS, 'GET'); // L2PT-1511
					DlgUtil.showMsg('Lỗi thanh toán POS. Lỗi khác');
					return false;
				}
				// END: Thanh toán qua POS
				else {
					taoPhieu(objData);
				}
				//L2PT-31437 end
			} else {
				DlgUtil.showMsg("Chưa chọn dịch vụ để thanh toán");
				return;
			}
		}
		// L2PT-1511 start
		function luuLogGoiPOS(objLPOS, LACTION) {
			objLPOS.LACTION = LACTION;
			var retLogPos = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T024.EV004", JSON.stringify(objLPOS));
			if (retLogPos != 1) {
				DlgUtil.showMsg("Lưu log thanh toán qua POS không thành công");
			}
			return retLogPos;
		}
		// L2PT-1511 end
		// L2PT-80276 start
		/*function waitForEvent(eventName) {
			return new Promise(function(resolve) {
				function eventHandler() {
					resolve(); // Resolve the promise when the event occurs
					// You can also pass additional data if needed: resolve(data);
				}
				document.addEventListener(eventName, eventHandler);
				setTimeout(function() {
					reject(new Error('Timeout occurred')); // Reject the promise with an error
					document.removeEventListener(eventName, eventHandler); // Remove the event listener
				}, 60000); // Timeout after 60 seconds
			});
		}*/
		//async
		// L2PT-80276 end
		function taoPhieu(objData) {
			// L2PT-39577: ajaxCALL_SP_I -> ajaxCALL_SP_S
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S(_sql_thuvienphi, JSON.stringify(objData));
			if (fl >= 1) {
				// L2PT-114388 start
				var _loaiphieuthuid = $("#cboLOAIPHIEUTHUID").val();
				if (fConfig.VPI_THUTIEN_NHANH == 1 && _loaiphieuthuid == _HOANUNG && VPI_QUYTRINH_VIENPHI == 1 && _benhnhan.TRANGTHAITIEPNHAN != 0) {
					var arr_dagiaodich = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_dagiaodich, _tiepnhanid);
					if (arr_dagiaodich && arr_dagiaodich.length > 0) {
						var _vp_giaodich = arr_dagiaodich[0];
						if (parseFloat(_vp_giaodich.CHENHLECH) > 0) {
							flagLoading = false;
							_thu_nhanh = true;
							$('#btnThem').trigger('click');
							return;
						}
					}
				}
				_thu_nhanh = false;
				// L2PT-114388 end
				// L2PT-80276 start
				// L2PT-90923 start : Đợi ngân hàng xong mới tạo phiếu thu
				/*if (objData.LOAIPHIEUTHUID == _HOANUNG && fConfig.VPI_CHIHO_QRCODE == '1' && objData.CHIHO == "1") {
					hoanTraKTM(_tiepnhanid, fl, get_val_m('txtTHUCTHU'));
					console.log('wait user action');
					//await waitForEvent('done_hoantraktm');
				}
				console.log('done');*/
				// L2PT-90923 end
				// L2PT-80276 end
				//TUYENNX_ADD_START
				if (VPI_DAY_HOADONDT == 1 && (INVOICES_IMPORT_AUTO == 1 || isLuuVaPhatHanhHDDT)) { // L2PT-33791
					var data_ar_ptid = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.INVOICES.PTID", fl);
					var VPI_SOTIEN_THUVP_GUIHDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_SOTIEN_THUVP_GUIHDDT'); //L2PT-59348 L2PT-1878
					for (var j = 0; j < data_ar_ptid.length; j++) {
						var _phieuthuid_hddt = data_ar_ptid[j].PHIEUTHUID;
						var _datra = data_ar_ptid[j].DATRA;
						// L2PT-51652 start
						var _loaiphieuthu = data_ar_ptid[j].LOAIPHIEUTHU;
						if (fConfig.VPI_LOAIHD_TUDONGGUI == '1' && _loaiphieuthu == '4') {
							console.log("Không tự động gửi loại hóa đơn thu khác");
						} else
						// L2PT-51652 end
						// L2PT-54003: Gửi HDDT không đồng	
						if ((_datra != 0 || fConfig.VPI_GUIHDDT_KHONGDONG == "1") && _datra >= parseFloat(VPI_SOTIEN_THUVP_GUIHDDT)) { // L2PT-59348
							var _sql_par = [];
							_sql_par.push({
								"name" : "[0]",
								value : _phieuthuid_hddt
							});
							var checkhu = jsonrpc.AjaxJson.getOneValue('INVOICE.CHECKHU', _sql_par);
							if (checkhu > 0) {
								guiHDDTTheoPhieu(_phieuthuid_hddt)
							}
						}
					}
				}
				// L2PT-46202 start
				if (fConfig.VPI_THUVP_DUYETCLS == '1' && _benhnhan.LOAITIEPNHANID == 1) {
					var data_ar_ptid = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.INVOICES.PTID", fl);
					for (var j = 0; j < data_ar_ptid.length; j++) {
						var _phieuthuid_hddt = data_ar_ptid[j].PHIEUTHUID;
						var retDuyetCLS_TVP = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI01T001.19.PT', _phieuthuid_hddt + "$" + "1");
						try {
							var objRetDuyetCLS_TVP = JSON.parse(retDuyetCLS_TVP);
							if (objRetDuyetCLS_TVP.CODE == '00') {
								DlgUtil.showMsg(objRetDuyetCLS_TVP.MESSAGE);
							} else {
								DlgUtil.showMsg(objRetDuyetCLS_TVP.MESSAGE + "<br>Lỗi: " + objRetDuyetCLS_TVP.ERROR);
							}
						} catch (err) {
							DlgUtil.showMsg("Có lỗi xảy ra" + "<br>" + err);
						}
					}
				}
				// L2PT-95857 start
				var objRetFastApi = guiDuLieuLenFastApi(fl, _hospital_code);
				// L2PT-95857 end
				// L2PT-53446 start
				// var _loaiphieuthuid = $("#cboLOAIPHIEUTHUID").val();// L2PT-114388
				if (isKySoTamUng && _loaiphieuthuid == _TAMUNG) {
					_caRpt('1');
				}
				// L2PT-53446 end
				// L2PT-46202 end
				// L2PT-24654 start
				/*var VPI_THUTIEN_DUYETPT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_THUTIEN_DUYETPT');
				if(VPI_THUTIEN_DUYETPT) {
					var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI03.KHOA.PHIEUTHU', fl + '$' + 1);
					DlgUtil.showMsg(result);
				}*/
				// L2PT-24654 end
				//TUYENNX_ADD_END
				flagLoading = false;
				// L2PT-39289 start
				guiTinNhanCamOn(); // BVTM-5944
				// L2PT-39289 end
				FormUtil.clearForm('ttThuTien', '');
				checkBN();
				$("#cboLOAIPHIEUTHUID").val(1);
				$("#cboHINHTHUCTHANHTOAN").val(1);
				$("#" + _gridId_DV).hideCol('cb');
				_phieuthuid = fl;
				loadGridDataDV(_tiepnhanid, false, true);
				//loadGridDataPT(_tiepnhanid, true, false);
				//L2PT-21449 thêm chức năng in phôi lớn
				// L2PT-33791 thêm btnLuuVaPH
				// L2PT-53446 thêm btnLuuVaKySo
				setEnabled([ 'btnThem', 'btnIn', 'btnInHDPL', 'btnInQR' ], [ 'chkNoTien', 'btnHuy', 'btnHuyBo', 'btnLuu', 'btnLuuVaPH', 'btnLuuVaKySo', 'txtMABENHNHAN', 'cboHINHTHUCTHANHTOAN',
						'txtIDPOST', 'cboPOS', 'txtNGAYLAP', //L2PT-31437, BVTM-5936
						'calNGAYLAP', 'cboLOAIPHIEUTHUID', 'btnHTTT',//L2PT-7489 // L2PT-19304 btnHTTT
						'txtLYDO', 'txtTLMIENGIAM', 'btnLYDO', 'txtMIENGIAM_PT' ]);
				setEnabled([], [ 'cboMANHOMPHIEUTHU' ]);
				if (HIS_FOCUS_MABN == 3) {
					//$("#"+_gridId_BN)[0].clearToolbar();
					//$(_focus_element).focus();
					$(_focus_element).select();
				}
				// L2PT-21967 chuyen duyet ke toan sau khi load xong dich vu
				/*if (VPI_DUYETKT_THUVPDBA == 1 && _benhnhan.TRANGTHAITIEPNHAN != 0) {
					// check thu tien
					var arr_dagiaodich = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_dagiaodich, _tiepnhanid);
					if (arr_dagiaodich && arr_dagiaodich.length > 0) {
						var _vp_giaodich = arr_dagiaodich[0];
						if (_vp_giaodich.CHENHLECH == 0) {
							$("#btnDuyet").click();
						}
					}
				}*/
				// L2PT-96778 L2PT-37075 duonghn start
				EventUtil.raiseEvent("THU_VIEN_PHI", {
					msg : fl
				});
				parent.DlgUtil.close("dlgThuVienPhi");
				// L2PT-96778 L2PT-37075 duonghn end
				return true;
			} else if (fl == -1) {
				DlgUtil.showMsg("Cập nhật không thành công");
			} else if (fl == -2) {
				DlgUtil.showMsg("Có dịch vụ đã thu tiền, không thể hoàn");
			} else if (fl == -3) {
				DlgUtil.showMsg("Có dịch vụ đã thực hiện, không thể hoàn");
			} else if (fl == -4) {
				DlgUtil.showMsg("Có dịch vụ đã thu tiền hoặc đã bị thay đổi tiền"); // L2PT-31810
			} else if (fl == -5) {
				DlgUtil.showMsg("Dữ liệu đã thay đổi, hãy chọn lại bệnh nhân");
			} else if (fl == -6) {
				DlgUtil.showMsg("Hết phiếu");
			} else if (fl == -7) {
				DlgUtil.showMsg("Mã phiếu đã sử dụng");
			} else if (fl == -8) {
				DlgUtil.showMsg("Số phiếu lớn hơn số phiếu lớn nhất của quyển sổ");
			} else if (fl == -9) {
				DlgUtil.showMsg("Không cho phép thu tiền khi bệnh nhân chưa đóng bệnh án");
			}
			// L2PT-7826_L2PT-7095 start : báo lỗi 
			else if (fl == -91) {
				DlgUtil.showMsg("Không cho phép thu tiền BN nội trú chưa đóng bệnh án");
			} else if (fl == -92) {
				DlgUtil.showMsg("Không cho phép thu tiền BN nội trú đã nhập khoa chưa đóng bệnh án");
			} else if (fl == -93) {
				DlgUtil.showMsg("Không cho phép thu tiền BN Điều trị ngoại trú chưa đóng bệnh án");
			}
			// L2PT-7826_L2PT-7095 end
			// L2PT-6202 start
			else if (fl == -17) {
				DlgUtil.showMsg("Số tiền miễn giảm vượt quá số tiền hóa đơn viện phí");
			}
			// L2PT-6202 end
			// L2PT-9516 start
			else if (fl == -19) {
				DlgUtil.showMsg("Có dịch vụ được tích thu tiền đã hủy hoặc xóa, vui lòng thực hiện lại");
			}
			// L2PT-9516 end
			// L2PT-130437 start
			else if (fl == -20) {
				DlgUtil.showMsg("Số tiền tạm ứng khác thực thu tạm ứng, vui lòng kiểm tra lại");
			}
			// L2PT-130437 end
			$("#btnHuyBo").click();
			return false;
		}
		// xu ly huy bo thu tien
		$("#btnHuyBo").click(function() {
			$("#" + _gridId_DV).hideCol('cb');
			$('#cboMANHOMPHIEUTHU').empty();
			FormUtil.clearForm('ttThuTien', '');
			checkBN();
			$("#cboLOAIPHIEUTHUID").val(1);
			$("#cboHINHTHUCTHANHTOAN").val(1);
			// L2PT-33791 thêm btnLuuVaPH
			// L2PT-53446 thêm btnLuuVaKySo
			setEnabled([ 'btnThem' ], [ 'chkNoTien', 'btnHuyBo', 'btnLuu', 'btnLuuVaPH', 'btnLuuVaKySo', // L2PT-33791 
			'txtMABENHNHAN', 'cboHINHTHUCTHANHTOAN', 'txtIDPOST', 'cboPOS', 'txtNGAYLAP', 'calNGAYLAP', 'cboLOAIPHIEUTHUID', 'txtLYDO', //L2PT-7489 // L2PT-31437
			'txtTLMIENGIAM', 'btnHTTT', 'txtMIENGIAM_PT' ]); // L2PT-19304 btnHTTT
			setEnabled([], [ 'cboMANHOMPHIEUTHU' ]);
			flagLoading = false;
			loadGridDataDV(_tiepnhanid);
//			$("#"+_gridId_BN)[0].clearToolbar();
//			DlgUtil.open("dlgXacNhan"); 
//			$(_focus_element).focus();
		});
		// xu ly in hoa don
		$("#btnIn").click(function() {
			onBtnInClick();
		});
		//Start BVTM-5936		
		$("#btnInQR").click(function() {
			onBtnInQRClick();
		});
		//End BVTM-5936
		// L2PT-21449 start
		$("#btnInHDPL").click(function() {
			var _selRowId = $("#" + _gridId_PT).jqGrid('getGridParam', 'selrow');
			var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'LOAIPHIEUTHUID');
			var _dahuyphieu = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'DAHUYPHIEU');
			var _loaiphieuthu = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'LOAIPHIEUTHU');
			var _loaitiepnhanid = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'LOAITIEPNHANID');
			if (_loaiphieuthuid == _THUTIEN) {
				if (VPI_SOLAN_INHOADON == 1) {
					var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T001.CHECKPT', _phieuthuid);
					if (fl == 1) {
						DlgUtil.showMsg("Hóa đơn đã được in, không thể in lại");
						return false;
					}
				}
				var _rp_code = 'NGT036_HOADONDVTHUKHAC_NOITRU_A4';
				// L2PT-23697 start
				if (_loaitiepnhanid == 0) {
					_rp_code = 'NGT036_HOADONDV_NTRU_PHOILON_A4';
				}
				// L2PT-23697 end
				inhoadon(_rp_code, _dahuyphieu, _phieuthuid, _loaiphieuthu, 1);
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T001.IN', _phieuthuid);
				if (fl == -1) {
					DlgUtil.showMsg("Cập nhật trạng thái phiếu thu không thành công");
				}
			} else {
				DlgUtil.showMsg("Hãy chọn hóa đơn để in");
			}
		});
		// L2PT-21449 end
		// xu ly in hoa don dien tu
		$("#btnInHDDT").click(function() {
			_inHDDT();
		});
		// L2PT-42086 start
		$("#btnInHDDTCD").click(function() {
			_inHDDTCD();
		});
		// L2PT-42086 end
		// L2PT-17867 start xu ly in thanh toan ra vien
		$("#btnIn2Lien").click(function() {
			var _reportCode = '';
			var _sql_par = [];
			_sql_par.push({
				"name" : "[0]",
				value : _tiepnhanid
			});
			var _loaiphieuthuid = jsonrpc.AjaxJson.getOneValue('VPI.LOAIPHIEUTHU', _sql_par);
			if (_loaiphieuthuid == 2) {
				_reportCode = 'NGT038_PHIEUHOANUNG_V2_A4';
			} else {
				_reportCode = 'NGT041_PHIEUTHUTIEN_V2_A4';
			}
			var par = [];
			par.push({
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid.toString()
			});
			CommonUtil.openReportGet('window', _reportCode, 'pdf', par, true, true);
		});
		// L2PT-17867 end xu ly in thanh toan ra vien
		//SONDN 
		//======== START SU KIEN CHO BV BACH MAI 2;
		// L2PT-43628 duonghn start
		$("#btnGOITIEP5").on("click", function() {
			// L2PT-48565 start
			// check khoa phong
			if (!_khoa_id || !_phong_id || _khoa_id == 0 || _phong_id == 0) {
				DlgUtil.showMsg("Chưa thiết lập khoa phòng");
				return false;
			}
			if ($("#txtHOTEN5").val() == "") {
				return false;
			}
			// L2PT-48565 end
			if (fConfig.VPI_CHEDO_GOILOA == '1') {
				var objGoiLoa = new Object();
				objGoiLoa.ORG_ID = _phong_id;
				objGoiLoa.STT = "";
				objGoiLoa.BENHNHANID = _benhnhan.BENHNHANID;
				objGoiLoa.LOAI = "4";
				objGoiLoa.GOIKHAM_TEXT = "Mời Bệnh nhân " + _benhnhan.TENBENHNHAN + " vào " + _opts._param[10] + "";
				ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.GOIKHAM.INS", JSON.stringify(objGoiLoa));
			} else {
				//_goisttbm2("2", "1"); // call from cominf; 
				goiKhamGG("Mời bệnh nhân " + $("#txtHOTEN5").val() + " vào quầy viện phí", "0", "5000");
			}
		});
		$("#btnGOILAI5").on("click", function() {
			if (fConfig.VPI_CHEDO_GOILOA == '1') {
				/*var objGoiLoa = new Object();
				objGoiLoa.ORG_ID = _phong_id;
				objGoiLoa.STT = "";
				objGoiLoa.BENHNHANID = _benhnhan.BENHNHANID;
				objGoiLoa.LOAI = "4";
				objGoiLoa.GOIKHAM_TEXT = "Mời Bệnh nhân " + _benhnhan.TENBENHNHAN + " vào " + _opts._param[10] + "";
				ret = jsonrpc.AjaxJson.ajaxCALL_SP_S("NGT.GOIKHAM.INS", JSON.stringify(objGoiLoa));*/
			} else {
				//_goisttbm2("2", "2");
			}
		});
		$("#btnLCDNHO5").on("click", function() {
			var opt = 'width=' + screen.availWidth * 0.8 + ',height=' + screen.availHeight * 0.8 + 'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no'
			if (fConfig.VPI_CHEDO_GOILOA == '1') {
				window.open('manager.jsp?func=../vienphi/VPI01T032_LCD&showMode=dlg', '', opt);
			} else {
				//var param = "";
				//window.open('manager.jsp?func=../ngoaitru/NGT02K053_VPI_LCDBM55&type=2&showMode=dlg', '', 'width=480,height=360,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
			}
		});
		// L2PT-43628 duonghn end
//	$("#btnDSGOILAI5").on("click", function(){
//        var myVar = {
//        		phongid : _opts.phongid
//        };
//		dlgPopup=DlgUtil.buildPopupUrl("dlgCV","divDlg","manager.jsp?func=../ngoaitru/NGT02K053_BM2_BNLOHEN"
//				,myVar,"Danh sách bệnh nhân gọi lại",750,400);
//		DlgUtil.open("dlgCV");
//	
//	}); 
//	
//	EventUtil.setEvent("evt_kios_bnlohen", function(e) {
//		if(typeof(e) != 'undefined'){
//			DlgUtil.close("dlgCV");
////			DlgUtil.showMsg("Đã gọi lại bệnh nhân " + e.tenbenhnhan);
//			$("#txtID5").val(e.id); 
//			_goisttbm2("2");
//		}
//	});
		//========= END SU KIEN CHO BV BACH MAI 2 
		// END SONDN 
/*	
	 function inNhieuPhieu (_ctn,_code,_type,_par_ar,_idFrm) {
		var isprint = 0;
		var par_data=JSON.stringify(_par_ar);
		var par_str=window.btoa(unescape(encodeURIComponent(par_data))); 
		var _url="../report/directReport.jsp?code="+ _code+"&filetype="+_type+"&reportParam="+ par_str;
		console.log('openReport._url='+_url);
		
		if(CommonUtil.fileURL) {
			console.log('revokeObjectURL='+CommonUtil.fileURL);
  		    URL.revokeObjectURL(CommonUtil.fileURL);
  	    }
		else {
			console.log('NOT revokeObjectURL');
		}
		
		 $.ajax({
		      url: _url,
		      type: "GET",
		      dataType: 'binary',
		      success: function(result) {
		    	//called when successful
			       var fileURL = URL.createObjectURL(result);
			       CommonUtil.fileURL=fileURL;
			       var iframe=document.getElementById(_idFrm);
			       if(!iframe){
			    	   iframe=document.createElement('iframe');
			    	   iframe.id=_idFrm;
			    	   document.body.appendChild(iframe);
			       }
			       iframe.style.display = 'none';
				   iframe.onload = function() {
					      setTimeout(function() {
					        iframe.focus();
					        iframe.contentWindow.print();
					        var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T001.IN', _phieuthuid);
							if(fl == -1) {
								DlgUtil.showMsg("Cập nhật trạng thái phiếu thu không thành công");
							}
							
					      }, 1);
				   };
				   iframe.src = fileURL;
					
		      }
		    });
		return true;
	}
*/
		// xu ly in bang ke (in phoi) -----------------------
		$("#btnInPhoi").click(function() {
			vienphi_tinhtien.inBangKe(_tiepnhanid, _benhnhan.DOITUONGBENHNHANID, _benhnhan.LOAITIEPNHANID, '', 1);
			if (VPI_CH_INPHOI == 1) {
				vienphi_tinhtien.inBangKe3455(_tiepnhanid, _benhnhan.DOITUONGBENHNHANID, _benhnhan.LOAITIEPNHANID);
			}
			if (HIS_FOCUS_MABN == 2) {
				//$("#"+_gridId_BN)[0].clearToolbar();
				//$(_focus_element).focus();
				$(_focus_element).select();
			}
		});
		// xu ly in bang ke bh(in phoi bh) -----------------------
		$("#btnInPhoiBH").click(function() {
			if (_benhnhan.DOITUONGBENHNHANID != 1) {
				DlgUtil.showMsg("Không thể in phơi bảo hiểm cho bệnh nhân viện phí/ dịch vụ");
				return;
			}
			var par = [];
			par.push({
				name : 'inbangkechuan',
				type : 'String',
				value : 1
			});
			par.push({
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid.toString()
			});
			par.push({
				name : 'type',
				type : 'String',
				value : '1'
			});
			CommonUtil.openReportGet('window', 'NGT001_BKCPKCBBHYT_QD6556_DOCMOI_A4', 'pdf', par);
			if (HIS_FOCUS_MABN == 2) {
				//$("#"+_gridId_BN)[0].clearToolbar();
				//$(_focus_element).focus();
				$(_focus_element).select();
			}
		});
		// L2PT-8045 start
		// xu ly in bang ke vp(in phoi vp) -----------------------
		$("#btnInPhoiVP").click(function() {
			var par = [];
			par.push({
				name : 'inbangkechuan',
				type : 'String',
				value : 1
			});
			par.push({
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid.toString()
			});
			par.push({
				name : 'type',
				type : 'String',
				value : '2'
			});
			CommonUtil.openReportGet('window', 'NGT001_BKCPKCBDICHVU_QD6556_A4', 'pdf', par);
			if (HIS_FOCUS_MABN == 2) {
				//$("#"+_gridId_BN)[0].clearToolbar();
				//$(_focus_element).focus();
				$(_focus_element).select();
			}
		});
		// L2PT-8045 end
		// xu ly in bang ke chua thanh toan(in phoi chua thanh toan) -----------------------
		$("#btnInPhoiCTT").click(function() {
			vienphi_tinhtien.inBangKeCTT(_tiepnhanid, _benhnhan.DOITUONGBENHNHANID, _benhnhan.LOAITIEPNHANID);
			if (HIS_FOCUS_MABN == 2) {
				//$("#"+_gridId_BN)[0].clearToolbar();
				//$(_focus_element).focus();
				$(_focus_element).select();
			}
		});
		$("#rInPhoi").click(function() {
			vienphi_tinhtien.inBangKe(_tiepnhanid, _benhnhan.DOITUONGBENHNHANID, _benhnhan.LOAITIEPNHANID, '', 1);
			if (VPI_CH_INPHOI == 1) {
				vienphi_tinhtien.inBangKe3455(_tiepnhanid, _benhnhan.DOITUONGBENHNHANID, _benhnhan.LOAITIEPNHANID);
			}
			if (HIS_FOCUS_MABN == 2) {
				//$("#"+_gridId_BN)[0].clearToolbar();
				//$(_focus_element).focus();
				$(_focus_element).select();
			}
		});
		//START -- L2K74TW-382 --hongdq -- 07032018
		$("#lsCongBHYT").click(
				function() {
					var resultThe = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU01H001.EV021", _tiepnhanid);
					if (resultThe != null && resultThe.length > 0) {
						var paramInput = {
							MABHYT : resultThe[0].MA_BHYT,
							TENBENHNHAN : resultThe[0].TENBENHNHAN,
							NGAYSINH : resultThe[0].NGAYSINH,
							QRCODE : '',
							GIOITINH : resultThe[0].GIOITINHID,
							MAKCBBD : resultThe[0].MA_KCBBD
						//TUNGAY : $('#txtBHYT_BD').val(), 
						//DENNGAY : $('#txtBHYT_KT').val()
						};
						dlgPopup = DlgUtil.buildPopupUrl("divDlgDDT", "divDlg", "manager.jsp?func=../ngoaitru/NGT02K047_LichSuKCB", paramInput, "Thông tin lịch sử điều trị bệnh nhân",
								window.innerWidth * 0.95, window.innerHeight * 0.93);
						var parent = DlgUtil.open("divDlgDDT");
					}
				});
		//END -- L2K74TW-382 --hongdq -- 07032018
		// L2PT-1511 start
		$("#rLSPOS").click(function() {
			var myVar = new Object();
			myVar.TIEPNHANID = _tiepnhanid;
			dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuPOS", "divDlgLSPOS", "manager.jsp?func=../vienphi/VPI01T024_LichSuPOS", myVar, "Lịch sử thanh toán POS", 1150, 650);
			DlgUtil.open("dlgLichSuPOS");
		});
		EventUtil.setEvent("savePhieuThuPOS", function(e) {
			DlgUtil.close("dlgLichSuPOS");
			layTTTiepNhan(_tiepnhanid);
		});
		// L2PT-1511 end
		// huy phieu thu
		$("#btnHuy").bindOnce("click", function() {
			if (flagLoading)
				return;
			if (VPI_HOANDV == 1) {
				hoantien();
			} else {
				DlgUtil.open("dlgXacNhan");
			}
		}, 1000);
		// duyet ke toan vien phi 
		// start thêm xác nhận gỡ duyệt //L2PT-16007
		// start thêm cấu hình in bảng kê //L2PT-16206
		$("#btnDuyet").bindOnce("click", function() {
			layTTTiepNhan(_tiepnhanid, true); // L2PT-23457 
			if (_flag_duyet) {
				// L2PT-71501 start
				if (fConfig.VPI_CANHBAO_MGDV_DKT == '1' && parseFloat(_vpData.MIENGIAMDV) > 0) {
					if (!confirm("BN đang duyệt kế toán, có tiền miễn giảm DV, bạn có đồng ý duyệt")) {
						return;
					}
				}
				// L2PT-71501 end
				// L2PT-23457 start
				var _ch_duyetbh = false;
				var VPI_XACNHAN_DUYETBH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_XACNHAN_DUYETBH');
				if (VP_DUYET_BH_KHI_DUYET_KETOAN == 0 || (VP_DUYET_BH_KHI_DUYET_KETOAN == 2 && _benhnhan.LOAITIEPNHANID != 0) || (VP_DUYET_BH_KHI_DUYET_KETOAN == 3 && _benhnhan.LOAITIEPNHANID == 0)) {
					_ch_duyetbh = true;
				}
				// L2PT-107762 start
				if ((VPI_XACNHAN_DUYETBH == 1 || VPI_XACNHAN_DUYETBH == 2) && _benhnhan.DOITUONGBENHNHANID == 1 && _benhnhan.TRANGTHAITIEPNHAN == 2 && _ch_duyetbh) {
					if (VPI_XACNHAN_DUYETBH == 2) {
						duyetKeToan(false, true);
					} else {
						DlgUtil.showConfirm("Bạn có muốn duyệt lại BHYT không ?", function(flag) {
							if (flag) {
								duyetKeToan();
							} else {
								duyetKeToan(false, true);
							}
						});
					}
				} else {
					duyetKeToan();
				}
				// L2PT-107762 end
				// L2PT-23457 end
			} else {
				if (VPI_XACNHAN_GODUYETKT == 0) {
					duyetKeToan();
				} else {
					DlgUtil.showConfirm("Xác nhận gỡ duyệt kế toán ?", function(flag) {
						if (flag) {
							duyetKeToan();
						}
					});
				}
			}
		}, 1000);
		// end thêm xác nhận gỡ duyệt //L2PT-16007
		// end thêm cấu hình in bảng kê //L2PT-16206
		//L2PT-19304 Xử lý chọn HTTT
		$("#btnHTTT").click(
				function() {
					if (!flagLoading && _phieuthuid > 0) {
						_DM_HTTT = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI.PT.HTTT', _phieuthuid);
					}
					dlgPopup = DlgUtil.buildPopupGrid("dlgHTTT", 'DM_HTTT', "Chọn HTTT", 512, 510);
					GridUtil.init('DM_HTTT', "500", "310", "", false, _gridHeader_HTTT);
					var grid = $("#" + 'DM_HTTT');
					var _thucthu = parseFloat(get_val_m('txtTHUCTHU'));
					/*var _tien_doidiem = parseFloat(get_val_m('txtTIENSUDUNG'));*/
					if (_DM_HTTT.length > 0) {
						GridUtil.fetchGridData('DM_HTTT', _DM_HTTT);
						var ids = grid.getDataIDs();
						var _tongtien_httt = 0;
						var _cell_html
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var _sotien_httt = grid.jqGrid('getCell', id, 'SOTIEN');
							_tongtien_httt += parseFloat(_sotien_httt);
							if (_sotien_httt > 0) {
								grid.jqGrid('setRowData', id, "", {
									color : 'red'
								});
							}
							var _httt_id = grid.jqGrid('getCell', id, 'TRANGTHAI_ID');
							var _doituong_httt = grid.jqGrid('getCell', id, 'DOITUONG');
							var _loai_httt = grid.jqGrid('getCell', id, 'LOAI');
							if (_loai_httt == 1) {
								_cell_html = '<select class="form-control input-sm" ' + 'style="width: 100%;" ' + 'id="cboDOITUONGTT' + _httt_id + '" hidden="">' + '</select>';
							} else {
								_cell_html = '<input class="form-control input-sm" ' + 'id="txtDOITUONGTT' + _httt_id + '" ' + 'name="txtDOITUONGTT' + _httt_id + '">';
							}
							grid.jqGrid('setCell', id, 'DOITUONG', _cell_html);
							if (_loai_httt == 1) {
								var _sql_par_bl = [ {
									name : [ 0 ],
									value : _httt_id
								} ];
								ComboUtil.getComboTag("cboDOITUONGTT" + _httt_id, "VPI.DM.BHBL", _sql_par_bl, "", "", "sql", "", "");
								if (_doituong_httt) {
									$("#cboDOITUONGTT" + _httt_id).val(_doituong_httt);
								}
							} else {
								$("#txtDOITUONGTT" + _httt_id).val(_doituong_httt);
							}
						}
						dlgPopup.open();
					} else {
						var _ds_httt = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.DM.HTTT", "$");
						GridUtil.fetchGridData('DM_HTTT', _ds_httt);
						var id_httt = $("#cboHINHTHUCTHANHTOAN").val();
						var ids = grid.getDataIDs();
						var _cell_html;
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var _httt_id = grid.jqGrid('getCell', id, 'TRANGTHAI_ID');
							var _loai_httt = grid.jqGrid('getCell', id, 'LOAI');
							if (_httt_id == id_httt) {
								grid.jqGrid('setCell', id, 'SOTIEN', _thucthu);
								/*if (_tien_doidiem && _tien_doidiem > 0) {
									//var _doituong_thetd = $("#txtTHETD").val();
									var _tien_conphainop = _thucthu - _tien_doidiem;
									_tien_conphainop = _tien_conphainop.toFixed(2);
									_tien_conphainop = parseFloat(_tien_conphainop);
									grid.jqGrid('setCell', id, 'SOTIEN', _tien_conphainop);
								}*/
							}
							if (_loai_httt == 1) {
								_cell_html = '<select class="form-control input-sm" ' + 'style="width: 100%;" ' + 'id="cboDOITUONGTT' + _httt_id + '" hidden="">' + '</select>';
							} else {
								_cell_html = '<input class="form-control input-sm" ' + 'id="txtDOITUONGTT' + _httt_id + '" ' + 'name="txtDOITUONGTT' + _httt_id + '">';
							}
							grid.jqGrid('setCell', id, 'DOITUONG', _cell_html);
							if (_loai_httt == 1) {
								var _sql_par_bl = [ {
									name : [ 0 ],
									value : _httt_id
								} ];
								ComboUtil.getComboTag("cboDOITUONGTT" + _httt_id, "VPI.DM.BHBL", _sql_par_bl, "", "", "sql", "", "");
							}
							// nếu có thẻ tích điểm 
							/*if (_tien_doidiem && _tien_doidiem > 0 && _httt_id == 8) {
								grid.jqGrid('setCell', _httt_id, 'SOTIEN', _tien_doidiem);
								//$("#txtDOITUONGTT5").val(_doituong_thetd);
								if (id_httt == _httt_id && _tien_doidiem != _thucthu) {
									var _tien_conphainop = _thucthu - _tien_doidiem;
									_tien_conphainop = _tien_conphainop.toFixed(2);
									_tien_conphainop = parseFloat(_tien_conphainop);
									if (id == 1) {
										grid.jqGrid('setCell', 2, 'SOTIEN', _tien_conphainop);
									} else {
										grid.jqGrid('setCell', 1, 'SOTIEN', _tien_conphainop);
									}
								}
							}*/
						}
						dlgPopup.open();
						//GridUtil.loadGridBySqlPage('DM_HTTT', "VPI.DM.HTTT", [], function() {});
					}
					// xu ly su kien nhap tien theo httt
					grid.bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
						var _tien_httt = grid.jqGrid('getCell', rowid, 'SOTIEN');
						_tien_httt = parseFloat(_tien_httt);
						var _thucthu = parseFloat(get_val_m('txtTHUCTHU'));
						if (/^\d+$/i.test(_tien_httt) && _tien_httt > 0) {
							if (_tien_httt <= _thucthu) {
								grid.jqGrid('setRowData', rowid, "", {
									color : 'blue'
								});
							} else {
								grid.jqGrid('setRowData', rowid, "", {
									color : 'black'
								});
							}
							// tính tiền các httt
							var ids = grid.getDataIDs();
							var id_httt = $("#cboHINHTHUCTHANHTOAN").val();
							var _tongtien_httt_khac = 0;
							var id_goc;
							for (var i = 0; i < ids.length; i++) {
								var _httt_id = grid.jqGrid('getCell', ids[i], 'TRANGTHAI_ID');
								if (_httt_id != id_httt) {
									var _tien_httt_i = grid.jqGrid('getCell', ids[i], 'SOTIEN');
									_tien_httt_i = parseFloat(_tien_httt_i);
									_tongtien_httt_khac += _tien_httt_i;
									_tongtien_httt_khac = parseFloat(_tongtien_httt_khac.toFixed(2));
								} else {
									id_goc = ids[i];
								}
							}
							var _tien_httt_conlai = _thucthu - _tongtien_httt_khac;
							_tien_httt_conlai = parseFloat(_tien_httt_conlai.toFixed(2));
							if (_tien_httt_conlai < 0) {
								grid.jqGrid('setCell', rowid, 'SOTIEN', 0);
							} else {
								grid.jqGrid('setCell', id_goc, 'SOTIEN', _tien_httt_conlai);
							}
							// tính tiền các httt
						} else {
							grid.jqGrid('setCell', rowid, 'SOTIEN', 0);
							grid.jqGrid('setRowData', rowid, "", {
								color : 'black'
							});
						}
					});
					GridUtil.setGridParam('DM_HTTT', {
						onSelectRow : function(id) {
							var arr_row = grid.jqGrid('getGridParam', 'selarrrow');
							GridUtil.unmarkAll('DM_HTTT');
							for (var i = 0; i < arr_row.length; i++) {
								GridUtil.markRow('DM_HTTT', arr_row[i]);
							}
						}
					});
					var txtTongTien_HTTT = $('<div class="col-xs-12 low-padding " align="center">' + '<div class="col-xs-5 low-padding">' + '<label class="">Tổng tiền thanh toán</label>' + '</div>'
							+ '<div class="col-xs-5 low-padding">' + '<input class="form-control input-sm money-sm clsfloatcomma"' + 'style="text-align: right; width: 100%;" id="txtTONGTIEN_HTTT"'
							+ 'name="txtTONGTIEN_HTTT" title=""' + 'valrule="Số tiền,trim_required|max_length[20]" disabled>' + '</div>' + '</div>');
					var btnOK = $('<input class="btn btn-sm btn-primary" id="btn_LDMG_OK" type="button" style="position:absolute;bottom:10px;left:10px" value="Đồng ý" />');
					var btnClose = $('<input class="btn btn-sm btn-primary" id="btn_LDMG_Close" type="button" style="position:absolute;bottom:10px;right:10px" value="Đóng" />');
					$('#dlgHTTT').append(btnOK);
					$('#dlgHTTT').append(btnClose);
					$('#dlgHTTT').append(txtTongTien_HTTT);
					val_m('txtTONGTIEN_HTTT', _thucthu);
					btnOK.click(function() {
						_arrHTTT = [];
						_DM_HTTT = [];
						var _tongtien_httt = 0;
						var _ms_httt = "";
						var ids = grid.getDataIDs();
						for (var i = 0; i < ids.length; i++) {
							var id = ids[i];
							var rowData = grid.jqGrid('getRowData', id);
							var _sotien_httt = rowData.SOTIEN;
							_sotien_httt = parseFloat(_sotien_httt)
							var _doituong_httt = "";
							if (_sotien_httt && _sotien_httt > 0) {
								_tongtien_httt += _sotien_httt;
								var _httt_id = rowData.TRANGTHAI_ID;
								var _ten_httt = rowData.TEN_TRANGTHAI;
								var _loai_httt = rowData.LOAI;
								var _id_thetd = "";
								var _ma_thetd = "";
								if (_httt_id == -1) {
									_id_thetd = $("#cboMATHETD").val();
									_ma_thetd = $("#txtTHETD").val();
								}
								if (_loai_httt == 1) {
									_doituong_httt = $("#cboDOITUONGTT" + _httt_id + " option:selected").val();
								} else {
									_doituong_httt = $("#txtDOITUONGTT" + _httt_id).val();
								}
								_ms_httt += _ten_httt + ": " + _sotien_httt + ";";
								var objHTTT = new Object();
								objHTTT.HINHTHUCTHANHTOANID = _httt_id + "";
								objHTTT.TENHINHTHUC = _ten_httt;
								objHTTT.SOTIEN = _sotien_httt + "";
								objHTTT.DOITUONG = _doituong_httt + "";
								objHTTT.NOIDUNG = "";
								objHTTT.IDTHE = _id_thetd + "";
								objHTTT.MATHE = _ma_thetd + "";
								_arrHTTT.push(objHTTT);
							}
							rowData.DOITUONG = _doituong_httt;
							_DM_HTTT.push(rowData);
						}
						var _thucthu = parseFloat(get_val_m('txtTHUCTHU'));
						if (_tongtien_httt != _thucthu) {
							DlgUtil.showMsg("Tổng số tiền của các hình thức thanh toán không được khác số tiền thực thu");
						} else if (_arrHTTT.length > 0) {
							_ms_httt = _ms_httt.slice(0, -1);
							objData.TONGTIEN_HTTT = _tongtien_httt;
							dlgPopup.close();
							/*var _tien_doidiem = parseFloat(get_val_m('txtTIENSUDUNG'));
							if (_tien_doidiem && _tien_doidiem > 0) {
								checkthutien();
							}*/
						} else {
							DlgUtil.showMsg("Chưa nhập hình thức thanh toán");
						}
					});
					btnClose.click(function() {
						dlgPopup.close();
					});
				});
		//L2PT-19304 Xử lý chọn HTTT
		function duyetKeToan(_ngayrv, _fl_duyet_bh) { //L2PT-23457
			if (flagLoading)
				return;
			// L2PT-557 start
			if (_flag_duyet && checkDuyetDLS() >= 1) {
				DlgUtil.showMsg("Có đơn thuốc chưa duyệt dược lâm sàng");
				return;
			}
			// L2PT-557  end
			if (_flag_duyet && _benhnhan.DOITUONGBENHNHANID == 1 && _benhnhan.LOAITIEPNHANID == 0 && _benhnhan.TRANGTHAITIEPNHAN != 2 && VPI_DUYETBH_THANHTOANNTU == 1 &&
					parseFloat(_vpData.BHYT_THANHTOAN) != 0) { //L2PT-17515
				DlgUtil.showMsg("Không thể duyệt kế toán cho bệnh nhân nội trú chưa duyệt bảo hiểm");
				return;
			}
			// L2PT-22774 start
			var VPI_CHECK_CHUATHU = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_CHECK_CHUATHU');
			if (VPI_CHECK_CHUATHU == 1 && _vpData.CHUATHU > 0 && _benhnhan.TRONVIEN != "0") {
				DlgUtil.showMsg("Bệnh nhân còn dịch vụ chưa thu tiền");
				return;
			}
			// L2PT-22774 end
			var objData_DUYET = new Object();
			objData_DUYET["TIEPNHANID"] = _tiepnhanid;
			var ngayduyet_vp = $("#txtNGAYDUYET_VP").val();
			objData_DUYET["NGAY"] = ngayduyet_vp ? ngayduyet_vp : jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
			objData_DUYET["NGAYRAVIEN"] = _benhnhan.NGAYRAVIEN;
			objData_DUYET["DATRONVIEN"] = 0;
			objData_DUYET["SOLUONGQUYETTOAN"] = 0;
			objData_DUYET["LOAIDUYETBHYT"] = 0;
			objData_DUYET["KHOAID"] = _khoa_id; //L2PT-26804 
			objData_DUYET["PHONGID"] = _phong_id; //L2PT-26804 
			objData_DUYET["BENHNHAN_KDV"] = _benhnhan.BENHNHAN_KDV; // L2PT-21633
			// L2PT-27497 L2PT-25547 start : update stt duyet bh khi duyet ke toan
			objData_DUYET.STT_DUYET_BH = $("#txtSTT").val();
			// L2PT-27497 L2PT-25547 end
			var _hcode = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.GET.CSKCB', _benhnhan.KHOAID);
			if (_flag_duyet) {
				objData_DUYET["DUYET"] = 1;
				var _ngayDuyet = stringToDateTime(objData_DUYET.NGAY);
				// L2PT-21633 start: BN KDV không check ngày ra viện
				objData_DUYET["BENHNHAN_KDV"] = _benhnhan.BENHNHAN_KDV;
				var _ngayRaStr = _benhnhan.NGAYRAVIEN;
				if (!_ngayRaStr && _benhnhan.BENHNHAN_KDV == '0') {
					DlgUtil.showMsg("Bệnh nhân chưa có thời gian ra viện");
					return false; // L2PT-21633
				}
				if (!_ngayRaStr) {
					_ngayRaStr = objData_DUYET.NGAY;
				}
				var _ngayRa = stringToDateTime(_ngayRaStr);
				// L2PT-21633 end
				if ((VPI_THOIGIAN_DUYETKT == 1 || (VPI_THOIGIAN_DUYETKT == 2 && _benhnhan.DOITUONGBENHNHANID == 1)) && _ngayDuyet < _ngayRa) { //L2PT-2875
					DlgUtil.showMsg("Không cho phép duyệt kế toán trước thời gian ra viện. Hãy duyệt sau " + _benhnhan.NGAYRAVIEN);
					return false;
				}
				// L2PT-11452 // L2PT-47548 start: ngay quyet toan < ngay ravien
				if (VPI_DUYETKT_CHUYENQT == '3' && _benhnhan.DOITUONGBENHNHANID == 1) {
					var _ngayqt = $("#txtNGAY_QUYET_TOAN").val();
					if (_ngayqt && _ngayqt.length > 0) {
						if (stringToDateTime(_ngayqt) < _ngayRa) {
							DlgUtil.showMsg("Ngày quyết toán không được nhỏ hơn ngày ra viện");
							return false;
						}
					}
					objData_DUYET["NGAYQUYETTOAN"] = $("#txtNGAY_QUYET_TOAN").val(); //L2PT-1401 // L2PT-47548
				}
				// L2PT-11452 // L2PT-47548 end
				if (VPI_KIEMTRA_TYLE == 0) {
					if (_khoa && _khoa.length > 0) {
						DlgUtil.showMsg("Có dịch vụ sai tỷ lệ tại " + _khoa);
						return;
					}
					// L2PT-3083 start: Tuyến 3 trở xuống cho phép trái tuyến
					var VPI_TUYEN_BV = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_TUYEN_BV');
					if (parseInt(VPI_TUYEN_BV) < 3 && _benhnhan.LOAITIEPNHANID != 0 && _benhnhan.LYDO_VAOVIEN == 3 && _benhnhan.MUCHUONG > 0) {
						DlgUtil.showMsg("Bệnh nhân ngoại trú trái tuyến đang có mức hưởng , xem lại thông tin hành chính");
						return;
					}
					// L2PT-3083 end
				}
				//Begin_HaNv_09062020:  Chặn không cho duyệt kế toán, duyệt BH khi BN còn đơn thuốc ngoại trú chưa duyệt - L2PT-22013
				var _ch_duyet_dt_ngt = (jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_CHECK_DUYET_DONTHUOCNGT') == '1') ? true : false;
				if (_ch_duyet_dt_ngt) {
					var _res = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.DONTHUOCNGT", _tiepnhanid);
					if (_res != '0') {
						return DlgUtil.showMsg('Bệnh nhân còn đơn thuốc ngoại trú chưa duyệt. Không được phép duyệt kế toán!');
					}
				}
				//End_HaNv_01042020
				// L2PT-103242 start
				var str_check_thau = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.CHECK.TTHAU', _tiepnhanid);
				var check_thau = JSON.parse(str_check_thau);
				if (check_thau.CODE != '00') {
					if (check_thau.CODE == '02') {
						if (!confirm(check_thau.MESSAGE)) {
							return;
						}
					} else {
						DlgUtil.showMsg(check_thau.MESSAGE);
						return;
					}
				}
				// L2PT-103242 end
			} else {
				//kiem tra benh nhan ngoai tru da xuat thuoc thi ko cho go duyet
				var check = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.CHECKGODUYETKT", _tiepnhanid);
				// L2PT-94924 VPI_GDKT_XT_NGT them gia tri 2
				if (VPI_GDKT_XT_NGT != 1 && _benhnhan.LOAITIEPNHANID == 1 && check > 0) {
					if (VPI_GDKT_XT_NGT == 2) {
						DlgUtil.showMsg("Đơn thuốc đã được Duyệt đơn hoặc Duyệt phát, Người dùng quầy Viện Phí không gỡ duyệt kế toán được");
					} else {
						DlgUtil.showMsg("Bệnh nhân đã xuất thuốc ngoại trú, không thể gỡ duyệt");
					}
					return;
				}
				//kiem tra người gỡ duyệt có là ng duyệt kế toán hay không
				var check_duyet = jsonrpc.AjaxJson.ajaxCALL_SP_I('COM.CAUHINH.THEOMA', 'VP_DUYET_KETOAN');
				if (check_duyet == 1) {
					var selRowId = $("#" + _gridId_BN).jqGrid("getGridParam", "selrow");
					var nguoiduyetid = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'NGUOIDUYETID');
					var nguoiduyet = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'NGUOIDUYET');
					if (nguoiduyetid != _user_id) {
						var arr_nd = nguoiduyet.split("-");
						var name = arr_nd.length > 0 ? arr_nd[0] : nguoiduyet;
						DlgUtil.showMsg("Bạn không có quyền gỡ duyệt, người đã duyệt kế toán : " + name);
						return;
					}
				}
				objData_DUYET["DUYET"] = 0;
			}
			// L2PT-53686 start
			obj_BH = new Object();
			obj_BH.TIEPNHANID = objData_DUYET.TIEPNHANID;
			obj_BH.NGAYDUYET = objData_DUYET.NGAY;
			// L2PT-25547 start
			obj_BH.STT = $("#txtSTT").val();
			// L2PT-25547 end
			var month = objData_DUYET.NGAY.split("/")[1];
			if (month <= 3)
				obj_BH.QUYDUYET = "1";
			else if (month <= 6)
				obj_BH.QUYDUYET = "2";
			else if (month <= 9)
				obj_BH.QUYDUYET = "3";
			else
				obj_BH.QUYDUYET = "4";
			obj_BH.HOSPITAL_CODE = _hcode;
			objData_DUYET.DATA_BH = obj_BH;
			if (_benhnhan.DOITUONGBENHNHANID == 1 &&
					(VP_DUYET_BH_KHI_DUYET_KETOAN == 0 || (VP_DUYET_BH_KHI_DUYET_KETOAN == 2 && _benhnhan.LOAITIEPNHANID != 0) || (VP_DUYET_BH_KHI_DUYET_KETOAN == 3 && _benhnhan.LOAITIEPNHANID == 0)) &&
					!_fl_duyet_bh) { //L2PT-23457
				objData_DUYET.FLAG_DUYET_BH = 0;
			} else {
				objData_DUYET.FLAG_DUYET_BH = 1;
			}
			// L2PT-53686 end
			//console.log(JSON.stringify(objData_DUYET));
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_S(_sql_duyetvienphi, JSON.stringify(objData_DUYET));
			// L2PT-28267 start: lưu log tự động duyệt Kế toán khi thu viện phí BN đã đóng BA
			var ltext = "DUYET_VP1_3:Kết quả duyệt/ gỡ duyệt kế toán" + ";_flag_duyet:" + _flag_duyet + ";fl:" + fl;
			save_log_act_form("VPI01T001_THUVIENPHI_V1", "DUYETKETOAN", ltext, _tiepnhanid.toString());
			// L2PT-28267 end
			// L2PT-38300 start
			if (!fl || fl == "" || fl == -1) {
				if (_flag_duyet) {
					DlgUtil.showMsg("Duyệt không thành công");
				} else {
					DlgUtil.showMsg("Gỡ duyệt không thành công");
				}
			} else if (fl.substr(0, 2) == -1) {
				$('#txtNOIDUNGLOI').val(fl.slice(2));
				DlgUtil.open("dlgLOI");
			}
			// L2PT-38300 end
			// L2PT-24300 start
			else if (fl == -80) {
				DlgUtil.showMsg("Có nhiều hơn 1 phiếu chi");
			} else if (fl == -81) {
				DlgUtil.showMsg("Chưa có sổ phiếu chi");
			} else if (fl == -82) {
				DlgUtil.showMsg("Chưa thiết lập khoa phòng");
			} else if (fl == -83) {
				DlgUtil.showMsg("Lỗi tạo phiếu chi");
			}
			// L2PT-24300 end
			// L2PT-26804 start
			else if (fl == -89) {
				DlgUtil.showMsg("Có nhiều hơn 1 phiếu duyệt kế toán");
			} else if (fl == -90) {
				DlgUtil.showMsg("Chưa thiết lập khoa phòng");
			} else if (fl == -91) {
				DlgUtil.showMsg("Lỗi lưu dữ liệu dịch vụ");
			}
			// L2PT-26804 end
			// L2PT-21881 start: ngày ra viện không được lớn hơn thời gian hiện tại
			else if (fl == -92) {
				DlgUtil.showMsg("Ngày ra viện không được lớn hơn thời gian hiện tại");
			}
			// L2PT-21881 ngày ra viện không được lớn hơn thời gian hiện tại
			// L2PT-11295 start : không được phân quyền gỡ duyệt
			else if (fl == -93) {
				DlgUtil.showMsg("Bạn không có quyền gỡ duyệt kế toán, liên hệ với người quản trị");
			}
			// L2PT-11295 end : không được phân quyền gỡ duyệt
			// L2PT-18615 start : không được phân quyền duyệt
			else if (fl == -930) {
				DlgUtil.showMsg("Bạn không có quyền duyệt kế toán, liên hệ với người quản trị");
			}
			// L2PT-18615 end : không được phân quyền duyệt
			// L2PT-7753 start : hết thời gian gỡ duyệt
			else if (fl == -931) {
				DlgUtil.showMsg("Hết thời gian được phép gỡ duyệt KT hồ sơ này, liên hệ với người quản trị");
			}
			// L2PT-7753 end : hết thời gian gỡ duyệt
			else if (fl == -94) {
				DlgUtil.showMsg("Bệnh nhân chưa gỡ duyệt bảo hiểm");
			} else if (fl == -95) {
				DlgUtil.showMsg("Hồ sơ đã khóa");
			} else if (fl == -96) {
				DlgUtil.showMsg("Còn khoa/phòng chưa kết thúc");
			} else if (fl == -97) {
				DlgUtil.showMsg("Còn phòng khám chưa kết thúc");
			} else if (fl == -98) {
				DlgUtil.showMsg("Bệnh nhân chưa thanh toán viện phí");
			}
			// L2PT-7342 start
			else if (fl == -981) {
				DlgUtil.showMsg("Lỗi khi kiểm tra dịch vụ");
			} else if (fl == -982) {
				DlgUtil.showMsg("Bệnh nhân còn dịch vụ chưa thu tiền");
			}
			// L2PT-7342 end
			// L2PT-3144 start
			else if (fl == -99) {
				DlgUtil.showMsg("Bệnh nhân chưa đóng bệnh án");
			} else if (fl == -88) {
				DlgUtil.showMsg("Bệnh nhân đã được duyệt kế toán trước đó");
			} else if (fl == -87) {
				DlgUtil.showMsg("Bệnh nhân chưa được duyệt kế toán hoặc đã gỡ duyệt");
			}
			// L2PT-3144 end
			// L2PT-3081 start
			else if (fl == -86) {
				DlgUtil.showMsg("Công khám đầu tiên có tỷ lệ khác 100%");
			}
			// L2PT-3081 end
			// L2PT-4522 start
			else if (fl == -85) {
				DlgUtil.showMsg("Hãy gỡ duyệt bảo hiểm trước");
			}
			// L2PT-4522 end
			// L2PT-10411 start
			else if (fl == -84) {
				DlgUtil.showMsg("Hồ sơ đã khóa, không thể gỡ duyệt kế toán");
			}
			// L2PT-10411 end
			else if (fl == 1 || fl == 3) { // L2PT-112148
				var id = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
				if (_flag_duyet) {
					if (_benhnhan.DOITUONGBENHNHANID == 1 &&
							(VP_DUYET_BH_KHI_DUYET_KETOAN == 0 || (VP_DUYET_BH_KHI_DUYET_KETOAN == 2 && _benhnhan.LOAITIEPNHANID != 0) || (VP_DUYET_BH_KHI_DUYET_KETOAN == 3 && _benhnhan.LOAITIEPNHANID == 0)) &&
							!_fl_duyet_bh) { //L2PT-23457
						if (fl == 0) {
							DlgUtil.showMsg("Hồ sơ của bệnh nhân đã khóa, không thể duyệt bảo hiểm");
							return;
						}
						if (fl == 2) {
							DlgUtil.showMsg("Bệnh nhân không có tiền đề nghị thanh toán, không thể duyệt");
							return;
						}
						if (fl == 3) {
							DlgUtil.showMsg("Chú ý: Hồ sơ bệnh nhân đã được duyệt nhiều lần");
						} else if (fl != 1) {
							$('#txtNOIDUNGLOI').val(fl);
							DlgUtil.open("dlgLOI");
							return;
						}
						_benhnhan.TRANGTHAITIEPNHAN = 2;
						_icon = '<center><img src="' + _opts.imgPath[2] + '" width="15px"></center>';
						$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_BH', _icon); // L2PT-68665
						var _ngay_ra = _benhnhan.NGAY_RA_VIEN;
						var _ngayhientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY');
						// L2PT-51016 start
						if (fConfig.VPI_CBDBHYT_KHACNGAY == '0' && _ngay_ra != _ngayhientai) {
							DlgUtil.showMsg("Hồ sơ mã " + _benhnhan.MAHOSOBENHAN + " có ngày ra viện khác ngày thanh toán. Yêu cầu: BỔ SUNG HỒ SƠ VÀO NGÀY " + _ngay_ra);
						}
						// L2PT-55208 start: nếu ngày ra viện lớn hơn thời gian hiện tại thì không gửi
						var sysdate = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
						var diffNGNR = diffDate(_benhnhan.NGAYRAVIEN, sysdate, 'DD/MM/YYYY HH:mm:ss')
						if (fConfig.VPI_BHXH_KT_NGAYGUI == 1 && diffNGNR > 0) {
							console.log("Thời gian ra viện lớn hơn thời gian hiện tại, gửi BHXH sau");
						} else
						// L2PT-55208 end
						if (_vpData.TONGTIENBH === 0) {
							console.log("BN  không có tiền BHYT, không gửi cổng BHXH");
						} else
						// L2PT-51016 end
						if (VPI_GUI_MA_LOAI_KCB == 0 || (VPI_GUI_MA_LOAI_KCB == 2 && _benhnhan.LOAITIEPNHANID == 1) || (VPI_GUI_MA_LOAI_KCB == 3 && _benhnhan.LOAITIEPNHANID == 3) ||
								(VPI_GUI_MA_LOAI_KCB == 4 && _benhnhan.LOAITIEPNHANID != 0) || (VPI_GUI_MA_LOAI_KCB == 5 && _benhnhan.LOAITIEPNHANID == 0)) {
							var data_bh = [];
							var data_byt = [];
							// L2PT-2105 start
							//var _ngayravien = _benhnhan.NGAYRAVIEN;
							var _ngayravien = jsonrpc.AjaxJson.getOneValue("VPI.NGAYHOSO", [ {
								name : "[0]",
								value : _tiepnhanid
							} ]);
							// L2PT-2105 end
							// L2PT-30598 start
							var _flag_gui = true;
							// L2PT-90986 : them cau hinh = 6
							if (VP_GUI_DULIEU_KHIDUYET == 4 || VP_GUI_DULIEU_KHIDUYET == 6) {
								var _ma_bhyt = _benhnhan.MABHYT;
								if (_ma_bhyt) {
									var _loai_the = _ma_bhyt.substr(2, 3);
									if (_loai_the == "297" || _loai_the == "497" || _loai_the == "597") {
										_flag_gui = false;
									}
								}
							}
							if (VP_GUI_DULIEU_KHIDUYET != 0 && _flag_gui) {
								// L2PT-30598 end
								// L2PT-90986 start
								if (parseInt(VP_GUI_DULIEU_KHIDUYET) > 4) {
									// L2PT-93751  start
									// L2PT-84768 start
									var kq_gui_cong_bhxh = gui_cong_bhxh('', _tiepnhanid, _hcode, "130", "1");
									if (kq_gui_cong_bhxh == "1") {
										DlgUtil.showMsg("Gửi hồ sơ( 4750) tới cổng Bảo hiểm xã hội thành công", function() {}, VPI_WAIT_MSG); // L2PT-12176
									} else if (kq_gui_cong_bhxh) {
										DlgUtil.showMsg(kq_gui_cong_bhxh);
									}
									// L2PT-84768 end
									// L2PT-93751  end
								} else
								// L2PT-90986 end
								if (VPI_GUI_BH == 1) {
									// L2PT-4245 start
									var func_name = "";
									var _param = "";
									// L2PT-47331 start
									/*var VPI_KIEU_XML = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_KIEU_XML');
									if (VPI_KIEU_XML == '1') {*/
									// L2PT-47331 end
									func_name = "XUAT.XML.DS";
									var objXuatXML = new Object();
									objXuatXML.LOCTHEO = 1 + "";
									objXuatXML.TU_NGAY = _ngayravien
									objXuatXML.DEN_NGAY = _ngayravien
									objXuatXML.LoaiHS = -1 + "";
									objXuatXML.Tuyen = -1 + "";
									objXuatXML.MaThe = -1 + "";
									objXuatXML.LoaiThe = -1 + "";
									objXuatXML.MAHOA = 1 + "";
									objXuatXML.DS_MALK = _tiepnhanid;
									objXuatXML.HCODE = _hcode + "";
									objXuatXML.MODE = "1";
									objXuatXML.DTBN = "1";
									_param = JSON.stringify(objXuatXML);
									// L2PT-47331 start
									/*} else {
										func_name = "XML.4210";
										_param = 1 + '$' + _ngayravien + '$' + _ngayravien + '$' + 1 + '$' + _tiepnhanid + '$' + _hcode;
									}*/
									// L2PT-47331 end
									// L2PT-4245 end
									data_bh = jsonrpc.AjaxJson.ajaxCALL_SP_O(func_name, _param, []); // L2PT-4245
									data_byt = data_bh;
								} else {
									data_bh = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_get_xml_checkout, _tiepnhanid + '$' + _ngayravien + '$' + _ngayravien, []);
									data_byt = jsonrpc.AjaxJson.ajaxCALL_SP_O('BH01.XML01', _ngayravien + '$' + _ngayravien + '$' + _tiepnhanid, []);
								}
								if (VP_GUI_DULIEU_KHIDUYET == 1) {
									getXmlTo_Insr(data_bh);
									getXmlTo_Medical(data_byt);
								} else if (VP_GUI_DULIEU_KHIDUYET == 2) {
									getXmlTo_Insr(data_bh);
								} else if (VP_GUI_DULIEU_KHIDUYET == 3) {
									getXmlTo_Medical(data_byt);
								}
								// L2PT-30598 start
								else if (VP_GUI_DULIEU_KHIDUYET == 4) {
									getXmlTo_Insr(data_bh);
									getXmlTo_Medical(data_byt);
								}
								// L2PT-30598 end
							}
						}
					} else if (_benhnhan.DOITUONGBENHNHANID != 1) {
						var _ret_byt = "";
						var data_byt = [];
						var _ngayravien = _benhnhan.NGAYRAVIEN;
						if (VP_GUI_DULIEU_KHIDUYET == 1 || VP_GUI_DULIEU_KHIDUYET == 3) {
							data_byt = jsonrpc.AjaxJson.ajaxCALL_SP_O('XML.VP', 1 + '$' + _ngayravien + '$' + _ngayravien + '$' + 1 + '$' + _tiepnhanid, []);
							_ret_byt = getXmlTo_Medical(data_byt);
						}
						if (_ret_byt != "") {
							var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D061.02.VP", _tiepnhanid + "$" + "-1" + "$" + _ret_byt);
							if (ret == -1) {
								DlgUtil.showMsg("Cập nhật trạng thái không thành công");
							}
						}
					}
					// neu duyet kt & bh thanh cong thi cap nhat trang thai
					_flag_duyet = false;
					// L2PT-27497 start: lay lai thong tin vien phi
					$("#btnDuyet").html("<span class='glyphicon glyphicon-ok-sign'></span> Gỡ duyệt KT");
					_benhnhan.TRANGTHAITIEPNHAN_VP = 1;
					_benhnhan.TRANGTHAITIEPNHAN_BH = 1;
					setEnabled([], [ 'calNGAY_QUYET_TOAN', 'txtNGAY_QUYET_TOAN' ]); // L2PT-1401 // L2PT-47548 
					_icon = '<center><img src="' + _opts.imgPath[4] + '" width="15px"></center>';
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_KT', _icon); // L2PT-68665
					setEnabled([ 'btnDuyetThuoc' ], [ 'btnThem', 'btnHuy' ]); // L2PT-17330  
					// L2PT-27497 end
					if (HIS_FOCUS_MABN == 1) {
						//$("#"+_gridId_BN)[0].clearToolbar();
						//$(_focus_element).focus();
						$(_focus_element).select();
					}
					// L2PT-17330 start 
					if (_benhnhan.TRANGTHAITIEPNHAN_VP == 1) {
						var _kieuDuyet1 = $("#chkDuyetKieu1").is(':checked');
						var _kieuDuyet2 = $("#chkDuyetKieu2").is(':checked');
						//L2PT-63512
						if (_kieuDuyet1 || VPI_HIENBANGKE_DUYETKT == 1 || VPI_HIENBANGKE_DUYETKT == 2) {
							if (VPI_HIENBANGKE_DUYETKT == 2) {
								if (_benhnhan.DOITUONGBENHNHANID == 1)
									$("#btnInPhoiBH").click();
							} else
								$("#btnInPhoi").click();
						}
						if (_kieuDuyet2) {
							$("#btnDuyetThuoc").click();
						}
					}
					// L2PT-17330 end
					// L2PT-27473 start: nhan tin cho khach hang
					var VPI_DUYETKT_GUISMS = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_DUYETKT_GUISMS');
					if (VPI_DUYETKT_GUISMS == '1') {
						guiTinNhanKH();
					}
					// L2PT-27473 end
					// L2PT-26717 start: gửi tất cả HDDT chưa gửi
					if (jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_DUYETKT_GUI_HDDT') == '1') {
						guiHDDTTheoLK(_tiepnhanid);
					}
					// L2PT-26717 end
				} else {
					var VP_GODUYETKT_GODUYETBH = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VP_GODUYETKT_GODUYETBH'); // L2PT-4522
					if (_benhnhan.DOITUONGBENHNHANID == 1 &&
							(VP_DUYET_BH_KHI_DUYET_KETOAN == 0 || (VP_DUYET_BH_KHI_DUYET_KETOAN == 2 && _benhnhan.LOAITIEPNHANID != 0) || (VP_DUYET_BH_KHI_DUYET_KETOAN == 3 && _benhnhan.LOAITIEPNHANID == 0)) &&
							!_fl_duyet_bh && VP_GODUYETKT_GODUYETBH == '0') { //L2PT-23457 // L2PT-4522
						if (fl == 0) {
							DlgUtil.showMsg("Hồ sơ của bệnh nhân đã khóa, không thể gỡ duyệt bảo hiểm");
							return;
						} else if (fl == -2) {
							DlgUtil.showMsg("Hết thời gian xử lý bảo hiểm của hồ sơ này, liên hệ với người quản trị");
							return;
						} else if (fl == -3) {
							DlgUtil.showMsg("Bạn không có quyền gỡ duyệt bảo hiểm hồ sơ này");
							return;
						} else if (fl == -4) {
							DlgUtil.showMsg("Hồ sơ chưa được duyệt bảo hiểm, không thể gỡ duyệt bảo hiểm");
							return;
						} else if (fl == -5) {
							DlgUtil.showMsg("Không lưu được log gỡ duyệt");
							return;
						} else if (fl != 1) {
							DlgUtil.showMsg(fl);
							return;
						}
						_benhnhan.TRANGTHAITIEPNHAN = 1;
						$("#" + _gridId_BN).jqGrid('setCell', id, 2, null);
					}
					// neu go duyet kt & bh thanh cong thi cap nhat trang thai
					_flag_duyet = true;
					// L2PT-27497 start: lay lai thong tin vien phi
					$("#btnDuyet").html("<span class='glyphicon glyphicon-ok-sign'></span> Duyệt KT");
					_benhnhan.TRANGTHAITIEPNHAN_VP = 0;
					_benhnhan.TRANGTHAITIEPNHAN_BH = 0;
					setEnabled([ 'calNGAY_QUYET_TOAN', 'txtNGAY_QUYET_TOAN' ], []); // L2PT-1401 // L2PT-47548
					_icon = '<center><img src="' + _opts.imgPath[3] + '" width="15px"></center>';
					$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_KT', _icon); // L2PT-68665
					setEnabled([ 'btnThem' ], [ 'btnDuyetThuoc' ]); // L2PT-17330 
					// L2PT-27497 start: lay lai thong tin vien phi
				}
			}
			// L2PT-112148 start
			else {
				$('#txtNOIDUNGLOI').val(fl);
				DlgUtil.open("dlgLOI");
			}
			// L2PT-112148 end
			// L2PT-27497 start: lay lai thong tin vien phi
			layTTTiepNhan(_tiepnhanid);
			// L2PT-27497 end
			// L2PT-21633 start
			if (_benhnhan.BENHNHAN_KDV != '0') {
				// L2PT-69024 start
				if (fConfig.VPI_DUYETKT_LOAD_DSBN == '0') {
					loadGridDataBN();
				}
				// L2PT-69024 end
			}
			// L2PT-21633 end
		}
		// L2PT-27473 start: nhan tin cho kh
		function guiTinNhanKH() {
			layTTTiepNhan(_tiepnhanid);
			var _sdtbenhnhan = _benhnhan.SDTBENHNHAN;
			var _daguiSMS = parseInt(_benhnhan.DAGUI_SMS);
			if (_sdtbenhnhan && _sdtbenhnhan.length > 0 && isNaN(_daguiSMS)) {
				if (_sdtbenhnhan.substring(0, 2) != '84') {
					_sdtbenhnhan = '84' + _sdtbenhnhan.substring(1);
					var ret = ajaxSvc.PortalWS.guiTinNhanKH(_tiepnhanid, _benhnhan.MAHOSOBENHAN, _sdtbenhnhan);
					try {
						var objRet = JSON.parse(ret);
						var error = objRet.RPLY.ERROR;
						if (error == '0') {
							DlgUtil.showMsg("Đã gửi tin nhắn tới khách hàng");
							var kqCapNhatSMS = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.UPDATE.TTSMS", _tiepnhanid);
							if (kqCapNhatSMS == '-1') {
								DlgUtil.showMsg("Có lỗi khi cập nhật trạng thái gửi SMS");
							}
						} else {
							var error_desc = objRet.RPLY.ERROR_DESC;
							DlgUtil.showMsg(error_desc);
						}
					} catch (err) {
						DlgUtil.showMsg(ret);
					}
				}
			}
		}
		// L2PT-27473 end
//		$("#"+_gridId_PT).bind("jqGridInlineAfterSaveRow", function (e, rowid, orgClickEvent) {
//			var dateRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/](\d{4})(\s)(0?[0-9]|[1][0-9]|[2][0-4])[:](0?[0-9]|[1-5][0-9])[:](0?[0-9]|[1-5][0-9])$/;
//			var _ngaythu = $("#"+_gridId_PT).jqGrid('getCell', rowid, 'NGAYTHU');
//			var _phieuthu_id = $("#"+_gridId_PT).jqGrid('getCell', rowid, 'PHIEUTHUID');
//			if(!dateRegex.test(_ngaythu)){
//				DlgUtil.showMsg("Ngày nhập vào không đúng định dạng");
//				return false;
//			}
//			var _ngaythu_date = stringToDateTime(_ngaythu);
//			var _ngaytiepnhan_date =   stringToDateTime(_benhnhan.NGAYTIEPNHAN);
//			if(_ngaythu_date<_ngaytiepnhan_date) {
//				DlgUtil.showMsg("Ngày thu không được phép trước ngày tiếp nhận");
//				return false;
//			}
//			var _ngayhientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
//			var _ngayhientai_date = stringToDateTime(_ngayhientai);
//			if(_ngaythu_date>_ngayhientai_date) {
//				DlgUtil.showMsg("Ngày thu không được phép sau ngày hiện tại");
//				return false;
//			}
//			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T001.25",_phieuthu_id+'$'+_ngaythu);
//			if(fl==1) {
//				$("#"+_gridId_PT).trigger("reloadGrid");
//			} else if(fl == -1) {
//				DlgUtil.showMsg("Xảy ra lỗi");
//			}
//			
//		});	
		$("#btnHENKHAM").click(
				function() {
					var objTN = {
						TIEPNHANID : _tiepnhanid
					}
					var kqThuVp = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.THUVP", JSON.stringify(objTN));
					DlgUtil.showMsg(kqThuVp);
					return;
					var myVar = {};
					dlgPopup = DlgUtil.buildPopupUrl("dlgCDDV", "divDsDoan", "manager.jsp?func=../ngoaitru/NGT02K061_QLHenKham", myVar, "Lựa chọn bệnh nhân hẹn khám", window.innerWidth * 0.95,
							window.innerHeight * 0.86);
					var parent = DlgUtil.open("dlgCDDV");
				});
		EventUtil.setEvent("assignSevice_getHenKham", function(e) {
			if (typeof (e) != 'undefined') {
//	      $("#gs_MAHOSOBENHAN").val(e.MAHOSOBENHAN).trigger('change');
				DlgUtil.showMsg("Đã hẹn khám bệnh nhân " + e.TENBENHNHAN + ".", function() {
					loadGridDataBN();
				});
			}
		});
		// L2PT-50644 ttlinh Man hinh thong bao BN cho thu phi
		$("#btnDSThongBao").on("click", function(e) {
			window.open('manager.jsp?func=../vienphi/ThongBaoBNChoThuPhi&showMode=dlg', '', 'width=1000,height=500,fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no');
		});
		// L2PT-50644 ttlinh end
// L2PT-11715 start
		$("#rTronVien").on("click", function(e) {
			var par = {
				HOSOBENHANID : _benhnhan.HOSOBENHANID,
				TIEPNHANID : _benhnhan.TIEPNHANID,
				BENHNHANID : _benhnhan.BENHNHANID,
				TONGCONGCP : _vpData.TONGTIENDV,
				BHYTTHANHTOAN : _vpData.BHYT_THANHTOAN,
				TAMUNG : _vpData.TAMUNG,
				DATHU : _vpData.DANOP,
				DUTIEN : _vpData.NOPTHEM >= 0 ? 0 : _vpData.NOPTHEM,
				THIEUTIEN : _vpData.NOPTHEM > 0 ? _vpData.NOPTHEM : 0
			};
			// L2PT-47743: chỉnh kích thước poupup
			var dlgPopup = DlgUtil.buildPopupUrl("ThongTinTronVien", "ThongTinTronVien", "manager.jsp?func=../noitru/NTU02D140_ThongTinTronVien", par, "Thông tin trốn viện", 1000, 650);
			dlgPopup.open();
		});
		EventUtil.setEvent("CLOSE_ThongTinTronVien", function(e) {
			DlgUtil.close("ThongTinTronVien");
		});
		EventUtil.setEvent("OK_ThongTinTronVien", function(e) {
			DlgUtil.close("ThongTinTronVien");
			_benhnhan.TRONVIEN = "0";
			setEnabled([ 'btnDuyet' ], []);
			// L2PT-28283 start
			if (VPI_CHANDUYETKT_BNTRONVIEN == '1') {
				setEnabled([], [ 'btnDuyet' ]);
			}
			// L2PT-28283 end
		});
		EventUtil.setEvent("HUY_ThongTinTronVien", function(e) {
			DlgUtil.close("ThongTinTronVien");
			_benhnhan.TRONVIEN = "1";
			setEnabled([], [ 'btnDuyet' ]);
			layTTTiepNhan(_tiepnhanid); // L2PT-28283
		});
		// L2PT-11715 end
		$("#btnLYDO").click(function() {
			if (!flagLoading)
				return;
			dlgPopup = DlgUtil.buildPopupGrid("dlgLYDO", 'DM_MIENGIAM', "Chọn miễn giảm", 512, 510);
			GridUtil.init('DM_MIENGIAM', "500", "350", "", false, _gridHeader_MG);
			var _sql_par = [];
			var grid = $("#" + 'DM_MIENGIAM');
			if (_DMMG.length > 0) {
				GridUtil.fetchGridData('DM_MIENGIAM', _DMMG);
				var ids = grid.getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var _tien_mg = grid.jqGrid('getCell', id, 'SOTIEN');
					if (_tien_mg && _tien_mg > 0) {
						grid.jqGrid('setRowData', id, "", {
							color : 'red'
						});
					}
				}
				dlgPopup.open();
			} else {
				GridUtil.loadGridBySqlPage('DM_MIENGIAM', "DM.MIENGIAM", _sql_par, function() {
					dlgPopup.open();
				});
			}
			// xu ly su kien nhap tien mien giam
			grid.bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
				var _tien_mg = grid.jqGrid('getCell', rowid, 'SOTIEN');
				if (/^\d+$/i.test(_tien_mg) && _tien_mg > 0) {
					if (_tien_mg && _tien_mg > 0) {
						grid.jqGrid('setRowData', rowid, "", {
							color : 'red'
						});
					} else {
						grid.jqGrid('setRowData', rowid, "", {
							color : 'black'
						});
					}
				} else {
					grid.jqGrid('setCell', rowid, 'SOTIEN', null);
					grid.jqGrid('setRowData', rowid, "", {
						color : 'black'
					});
				}
			});
			GridUtil.setGridParam('DM_MIENGIAM', {
				onSelectRow : function(id) {
					var arr_row = grid.jqGrid('getGridParam', 'selarrrow');
					GridUtil.unmarkAll('DM_MIENGIAM');
					for (var i = 0; i < arr_row.length; i++) {
						GridUtil.markRow('DM_MIENGIAM', arr_row[i]);
					}
				},
				gridComplete : function(id) {}
			});
			var btnOK = $('<input class="btn btn-sm btn-primary" id="btn_LDMG_OK" type="button" style="position:absolute;bottom:10px;left:10px" value="Đồng ý" />');
			var btnClose = $('<input class="btn btn-sm btn-primary" id="btn_LDMG_Close" type="button" style="position:absolute;bottom:10px;right:10px" value="Đóng" />');
			$('#dlgLYDO').append(btnOK);
			$('#dlgLYDO').append(btnClose);
			btnOK.click(function() {
				_arrMG = [];
				_DMMG = [];
				var _tong_mg = 0;
				var _ms_mg = "";
				var ids = grid.getDataIDs();
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var rowData = grid.jqGrid('getRowData', id);
					_DMMG.push(rowData);
					var _sotien_mg = rowData.SOTIEN;
					if (_sotien_mg && _sotien_mg > 0) {
						_tong_mg += parseFloat(_sotien_mg);
						var _loai_mg = rowData.LOAI;
						var _ten_mg = rowData.TEN;
						_ms_mg += _ten_mg + ": " + _sotien_mg + ";";
						var objMG = new Object();
						objMG.LOAI = _loai_mg + "";
						objMG.TEN = _ten_mg;
						objMG.SOTIEN = _sotien_mg + "";
						_arrMG.push(objMG);
					}
				}
				if (_arrMG.length > 0) {
					_ms_mg = _ms_mg.slice(0, -1);
//          		DlgUtil.showMsg(_ms_mg+"");
					$("#txtMIENGIAM_PT").val(_tong_mg);
					$("#txtLYDO").val(_ms_mg);
					$("#txtMIENGIAM_PT").keyup();
					dlgPopup.close();
				} else {
					DlgUtil.showMsg("Chưa nhập miễn giảm");
				}
			});
			btnClose.click(function() {
				dlgPopup.close();
			});
		});
		// L2PT-17330 start 
		$("#btnDuyetThuoc").bindOnce("click", function() {
			var myVar = new Object();
			myVar.MA_BA = _benhnhan.MAHOSOBENHAN;
			myVar.KHOID = $("#cboChonKho").val();
			DlgUtil.buildPopupUrl("dlgDuyetThuoc", "dlgDuyet_Thuoc", "manager.jsp?func=../duoc/DUC47T001_DuyetPhieuQueryCodeTheoMaBA&lk=2,4,5,6,12&cs=1", myVar, "Duyệt thuốc", 1250, 600);
			DlgUtil.open("dlgDuyetThuoc");
		}, 1000);
		$("#cboChonKho").change(function() {
			var _khoid = $("#cboChonKho").val();
			document.cookie = "KHOID=" + _khoid;
		});
		// L2PT-17330 end 
		// L2PT-26227 start
		$("#btnGuiHDDT").bindOnce("click", function() {
			guiHDDT(0); // L2PT-19666
		});
		// L2PT-26227 end
		// L2PT-16575 start
		// L2PT-18357 start
		$("#btnXMLHDDT").bindOnce("click", function() {
			var _selRowId = $("#" + _gridId_PT).jqGrid('getGridParam', 'selrow');
			var dsPhieu = [];
			var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'PHIEUTHUID');
			var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'LOAIPHIEUTHUID');
			var _dahuyphieu = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'DAHUYPHIEU');
			var _datra = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'DATRA');
			var _manhomphieuthu = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'MANHOMPHIEUTHU');
			var _maphieuthu = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'MAPHIEUTHU');
			if (_loaiphieuthuid != 1) {
				DlgUtil.showMsg("Có phiếu không phải là hóa đơn");
				return;
			}
			// L2PT-54003: xuất XML HDDT không đồng
			if (_datra == 0 && fConfig.VPI_GUIHDDT_KHONGDONG == "0") {
				DlgUtil.showMsg("Có hóa đơn không đồng");
				return;
			}
			if (_dahuyphieu == 1) {
				DlgUtil.showMsg("Có phiếu thu đã hủy");
				return;
			}
			var phieu = new Object();
			phieu.PHIEUTHUID = _phieuthuid;
			phieu.MAHOSOBENHAN = _benhnhan.MAHOSOBENHAN;
			phieu.BENHNHANID = _benhnhan.BENHNHANID;
			phieu.MANHOMPHIEUTHU = _manhomphieuthu;
			phieu.MAPHIEUTHU = _maphieuthu;
			dsPhieu.push(phieu);
			if (!dsCH) {
				dsCH = layDsCH(_hospital_id);
			}
			if (!dsCH) {
				return;
			}
			// L2PT-13782 start: XML bán thuốc
			var mode = 1;
			var fileType = 1;
			downloadXML(dsPhieu, dsCH, _config_hddt, mode, fileType);
		});
		// L2PT-18357 end
		// L2PT-16575 end
		//Start TuyenDV BVTM-2440
		$("#btnKySo").on("click", function(e) {
			_caRpt('1');
		});
		/*$("#btnKySo2").on("click", function(e) {
			caDemo('1');
		});

		caDemo = function(){
			// get rptcode
			let _rptCode = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'RPT_CODE_KYSO_BANGKE');
			let HIS_KYSO_BANGKE_KTBA = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_KYSO_BANGKE_KTBA');
			if (_benhnhan.LOAITIEPNHANID != '1' && HIS_KYSO_BANGKE_KTBA == '2') {
				_rptCode = _rptCode + '_NOITRU';
			}
			let _rsExportXML;
			let oData = [{
				name : 'hosobenhanid',
				type : 'String',
				value : _benhnhan.HOSOBENHANID
			},{
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid
			},{
				name : 'phieuthuid',
				type : 'String',
				value : _phieuthuid
			},{
				name : 'rpt_code',
				type : 'String',
				value : _rptCode
			}];
			$.ajax({
				url: '/vnpthis/api/ExportFormXML',
				type: "POST",
				data: JSON.stringify({
					PARAMS: oData,
					FUNCTION: "EXPORT"
				}),
				contentType: 'application/json; charset=utf-8',
				dataType: "json",
				async: false
			}).done(function (_response) {
				_rsExportXML = _response;
			});

			if (_rsExportXML && _rsExportXML.CODE == '0') {

			} else {
				alert("Đã có lỗi xảy ra khi xuất dữ liệu ký.");
				return;
			}

			let _width = $(document).width() - 80;
			let _height = $(window).height() - 80;
			let _html = '';
			_html += `<div id="SIGNHUB" style="width: 100%; display: none">`;
			_html += `<iframe  style="width:1300px;height:650px;" id="SIGNHUB_ifmView"	frameborder="0"></iframe>`;
			_html += `</div>`;
			$('#divHidden').html(_html)
			document.getElementById('SIGNHUB_ifmView').src = "https://test-onehealth.vncare.vn/app/mng/sign-hub/";
			let __opts = {
				title: "Sign Hub",
				theme: 'ModalBorder',
				closeOnEsc: false,
				closeOnClick: false,
				closeButton: 'title',
				overlay: true,
				zIndex: 10000,
				content: $('#SIGNHUB'),
				draggable: 'title',
				width: _width,
				height: _height,
			};

			$("div[id^='jBoxID']:has(#divCALOGIN)").detach();
			let dlgPopup = new jBox('Modal', __opts);
			dlgPopup.open();
			window.top.onmessage = (event) => {
				_rsExportXML = JSON.parse(_rsExportXML["SIGNINFO"]);
				let _parHashed = _rsExportXML["docid"];
				if (event.data.code === 'oh_signhub_ready') {
					let a = document.getElementById('SIGNHUB_ifmView').contentWindow;
					a.postMessage(
						{
							code: "oh_signhub_config",
							data: {
								"documents": [
									{
										"docid": _phieuthuid,
										"doc_type": "XML",
										"doc_data": _rsExportXML["doc_data"],
										"template_code": _rsExportXML["template_code"],
										"minio_bucket": "",
										"his_transid": ""
									},
								],
								"config": {
									"signer_name": _rsExportXML["signer_name"],
									"sign_identify": _rsExportXML["sign_identify"],
									"sign_csyt": _rsExportXML["sign_csyt"],
									"token_smartca": "",
									"token_onehealth": "",
									"form_editable": 1,
									"emr_send": 0,
									"single": 0,
									"show_list": 1,
									"show_step": 0,
									"show_xml": 1,
									"show_download": 1,
									"show_patient": 1,
									"show_sign_image": 0,
									"sign_methods": [
										{
											"label": "Ký người dân",
											"value": "KYNGUOIDAN",
											"default": 1
										},
										{
											"label": "Ký VNPT-SmartCA",
											"value": "SMARTCA",
											"default": 0
										},
										{
											"label": "Ký VNPT-SmartCA theo thông tư 769",
											"value": "SMARTCA769",
											"default": 0
										},
										{
											"label": "Ký eSeal – VNPT-SmartCA nâng cao",
											"value": "ESEAL",
											"default": 0
										},
										{
											"label": "Ký eSeal – VNPT-SmartCA nâng cao theo thông tư 769",
											"value": "ESEAL769",
											"default": 0
										},
										{
											"label": "Ký Điện tử",
											"value": "DIGITALSIGN",
											"default": 0
										},
										{
											"label": "Ký bảng ký",
											"value": "SIGNPAD",
											"default": 0
										},
										{
											"label": "Thiết bị di động",
											"value": "MOBILE",
											"default": 0
										},
										{
											"label": "Vân tay",
											"value": "FINGER",
											"default": 0
										},
										{
											"label": "USB Token",
											"value": "USBTOKEN",
											"default": 0
										}
									]
								}
							}
						},
						'*'
					);
				} else if (event.data.code === 'oh_signhub_return') {
					if (event.data.data && typeof event.data.data === "object") {
						*//**
							 * { "sign_identify":"<Định danh người ký>", "sign_csyt":"<Mã CSYT sở hữu file ký>",
							 * "documents": [ { "docid":"<định danh file phía HIS: số phiếu, phieuid, vv..>", "doc_url":"<URL
							 * file đã ký lưu trên MinIO>", "emr_send": 1 } ] }
							 */
		/*
								$.ajax({
									url: '/vnpthis/api/ExportFormXML',
									type: "POST",
									data: JSON.stringify({
										PARAM_HASHED: _parHashed,
										PARAMS: oData,
										FUNCTION: "CONFIRM",
										DATA: event.data.data.documents[0]
									}),
									contentType: 'application/json; charset=utf-8',
									dataType: "json",
									async: false
								}).done(function (_response) {
									_rsExportXML = _response;
								});
							} else {
								alert("Lỗi xảy ra khi ký.")
							}
						}
					}
				}*/
		$("#btnHuyKySo").on("click", function(e) {
			_caRpt('2');
		});
		$("#btnInKySo").on("click", function(e) {
			_caRpt('0');
		});
		//End TuyenDV BVTM-2440
		// L2PT-7410 start
		$("#rKhoaMoBA").click(function() {
			khoaMoBenhAn("1");
		});
		$("#rMoKhoaBA").click(function() {
			khoaMoBenhAn("0");
		});
		// L2PT-7410
		// L2PT-11569 start
		//in bang ke mien phi
		$("#btnInPhoiMP").on("click", function() {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid
			} ];
			openReport('window', "NGT001_BKCPKCBBHYT_QD6556_DOC_MIENPHI_A4", "pdf", par);
		});
		//in bang ke hao phi
		$("#btnInPhoiHP").on("click", function() {
			vienphi_tinhtien.inPhoiVP(_benhnhan.DOITUONGBENHNHANID, _tiepnhanid, 'NGT001_BKCPKCB_HAOPHI_01BV_QD3455_A4');
		});
		// L2PT-11569 end
		// L2PT-16528 start: in bang ke chenh lech
		$("#btnInPhoiCL").on("click", function() {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid
			} ];
			openReport('window', "NGT001_BKCPKCB_QD6556_DOC_CHENHLECH_A4", "pdf", par);
		});
		// L2PT-16528 end
		// L2PT-38407 start: in bang ke chenh lech
		$("#btnInPhieuMuonDo").on("click", function() {
			var par = [ {
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid
			} ];
			openReport('window', "RPT_PHIEUMUONDO", "pdf", par);
		});
		// L2PT-38407 end
		// L2PT-44185 start
		$('#rChuyenQuyetToan').on("click", function(e) {
			var arrTNID = [];
			var id = $("#" + _gridId_BN).jqGrid('getGridParam', 'selrow');
			var tnid = $("#" + _gridId_BN).jqGrid('getCell', id, 'TIEPNHANID');
			var trangthaitiepnhan = $("#" + _gridId_BN).jqGrid('getCell', id, 'TRANGTHAITIEPNHAN');
			var _dstn = tnid;
			if (!_dstn || _dstn.length == 0) {
				DlgUtil.showMsg("Chưa chọn hồ sơ");
				return;
			}
			if (trangthaitiepnhan == 2) {
				DlgUtil.showMsg("Có hồ sơ đã duyệt bảo hiểm, không thể chuyển quyết toán");
				return;
			}
			var myVar = new Object();
			myVar._dstn = _dstn;
			myVar.dlgName = 'dlgNameNQT';
			dlgPopup = DlgUtil.buildPopupUrl("dlgNameNQT", "dlgIdNQT", "manager.jsp?func=../vienphi/VPI01T031_chuyen_quyettoan", myVar, "Chuyển quyết toán", 600, 450);
			DlgUtil.open("dlgNameNQT");
		});
		// L2PT-73710 start
		$('#cboKHOAKT').on("change", function(e) {
			loadComboPhong();
		});
		// L2PT-73710 end
		// L2PT-102282 start
		$("#btnHienThiTT").bindOnce(
				"click",
				function() {
					var quayso = $("input[name='radQUAY']:checked").val() ? $("input[name='radQUAY']:checked").val() : '';
					if (_benhnhan) {
						var paramInput = _benhnhan.TENBENHNHAN + '@' + quayso + '@' + $("#txtNOPTHEM").val().replace('-', '') + '@' + _benhnhan.NAM_SINH; //L2PT-104999 L2PT-107130
						window.open('manager.jsp?func=../vienphi/VPI01T036_LCD_BNThanhToan&showMode=dlg', paramInput,
								'fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no,height=' + screen.height + ',width=' + screen.width);
					} else {
						DlgUtil.showMsg("Bạn chưa chọn bệnh nhân!");
					}
				}, 1000);
		//L2PT-102282 end
		EventUtil.setEvent("dlgNameNQT_KETQUA", function(e) {
			DlgUtil.showMsg(e.msg);
		});
		// L2PT-44185 end
		// L2PT-31925 start
		if (parseInt(fConfig.VPI_TUDONG_GUIHDDT_KTM) >= 10) {
			var myInterval = setInterval(guiHDDT_KTM, fConfig.VPI_TUDONG_GUIHDDT_KTM * 1000);
		}
		// L2PT-31925 end
		// L2PT-71931 start
		if (parseInt(fConfig.VPI_TUDONG_KIEMTRA_QRCODE) >= 3) {
			var myInterval = setInterval(kiemTraQRCODE, fConfig.VPI_TUDONG_KIEMTRA_QRCODE * 1000);
		}
		// L2PT-71931 end
		// L2PT-82005 start
		$("#btnDuyetCLS").bindOnce("click", function() {
			if (flagLoading) {
				return;
			}
			var dlgName = "dlgDuyetCLS";
			var dlgVar = new Object();
			dlgVar.tiepnhanid = _tiepnhanid;
			dlgVar.dlgName = dlgName;
			DlgUtil.buildPopupUrl(dlgName, dlgName + "Id", "manager.jsp?func=../vienphi/VPI01T035_duyetthuchiencls", dlgVar, "Duyệt thực hiện cận lâm sàng", 1250, 600);
			DlgUtil.open(dlgName);
		}, 1000);
		// L2PT-82005 end
		// L2PT-118724 start
		/*$("#btnChiHo").bindOnce("click", function() {
			if (flagLoading) {
				return;
			}
			hoanTra_KTM(_tiepnhanid);
		}, 1000);*/
		// L2PT-118724 end
		// L2PT-90923 start
		EventUtil.setEvent("assignSevice_hoantraktm_ok", function(data) {
			//loadGridDataPT(data.msg.TIEPNHANID);
			console.log(data.msg);
			// L2PT-118724 start
			var msg = "Chi hộ thành công với số hóa đơn: " + data.msg.soHoaDon;
			$.bootstrapGrowl(msg, {
				type : 'success',
				delay : 5000,
				width : 500,
				offset : {
					from : "top",
					amount : 50
				},
			});
			var selRowId = $("#" + _gridId_BN).jqGrid("getGridParam", "selrow");
			var tiepNhanId = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'TIEPNHANID');
			if (tiepNhanId == data.msg.TIEPNHANID) {
				$("#" + _gridId_BN).jqGrid('setCell', selRowId, 'ICON_CH', getIconPathByName('Dollar'));
			}
			// L2PT-118724 end
			// L2PT-90923 start
			if (data.msg.objData) {
				taoPhieu(data.msg.objData);
			} else if (!data.msg.PHIEUTHUID) {
				data.msg.PHIEUTHUID = _phieuthuid;
				var updateBillRet = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI01T034.01", data.msg);
				var jsonUpdateBillRet = JSON.parse(updateBillRet);
				if (jsonUpdateBillRet.CODE == '00') {
					//closePage('assignSevice_hoantraktm_ok', dataResponse);
				} else {
					DlgUtil.showMsg(jsonUpdateBillRet.MESSAGE);
				}
			}
			// L2PT-90923 end
			//var event = new Event("done_hoantraktm");
			//document.dispatchEvent(event);
		});
		EventUtil.setEvent("assignSevice_hoantraktm_close", function(data) {
			console.log(data.msg);
			//var event = new Event("done_hoantraktm");
			//document.dispatchEvent(event);
		});
		// L2PT-118724 start
		EventUtil.setEvent("assignSevice_hoantraktm_tuchoi", function(data) {
			DlgUtil.showMsg(data.msg.MESSAGE);
			var selRowId = $("#" + _gridId_BN).jqGrid("getGridParam", "selrow");
			var tiepNhanId = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'TIEPNHANID');
			if (tiepNhanId == data.msg.TIEPNHANID) {
				$("#" + _gridId_BN).jqGrid('setCell', selRowId, 'ICON_CH', getIconPathByName('Cancel'));
			}
			//var event = new Event("done_hoantraktm");
			//document.dispatchEvent(event);
		});
		// L2PT-118724 end
		EventUtil.setEvent("ok_dlgIdLyDoHuyPhieu", function(e) {
			try {
				var msg = JSON.parse(e.msg);
				if (typeof msg.LYDOHUYPHIEU == 'undefined' || msg.LYDOHUYPHIEU.trim() == "") {
					DlgUtil.showMsg("Chưa nhập lý do hủy phiếu");
					return;
				}
				huyPhieuThu(msg.LYDOHUYPHIEU, 'CHIHO');
			} catch (err) {
				console.log(err);
			}
		});
		// L2PT-90923 end
		// L2PT-114387 start
		EventUtil.setEvent("ok_popup_capnhatphieuthu", function(e) {
			loadGridDataPT(_tiepnhanid); // L2PT-127059
		});
		/*EventUtil.setEvent("close_popup_capnhatphieuthu", function(e) {
			loadGridDataPT(_tiepnhanid); // L2PT-127059
		});*/
		// L2PT-114387 end
	}
	// L2PT-7410 start
	function khoaMoBenhAn(trangthai) {
		var kq_khoa = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.KHOA.DULIEU", {
			"LOAIKHOA" : "1",
			"GIATRI" : _benhnhan.HOSOBENHANID,
			"TRANGTHAI" : trangthai,
			"GHICHU" : "THUVIENPHI_V2: Không cho phép mở hồ sơ bệnh án"
		});
		if (kq_khoa == "1") {
			DlgUtil.showMsg(trangthai == "1" ? "Khóa thành công" : "Mở khóa thành công");
		} else if (kq_khoa == "2") {
			DlgUtil.showMsg("Bệnh án này đã ở trạng thái " + (trangthai == "1" ? "khóa" : "mở khóa"));
		} else {
			DlgUtil.showMsg(kq_khoa);
		}
		loadGridDataBN();
	}
	// L2PT-7410 end
	//Start TuyenDV BVTM-2440
	// L2PT-87409 start: Đồng bộ với form v2
	function _caRpt(signType, opts) { // L2PT-48254
		var _rptCode = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'RPT_CODE_KYSO_BANGKE');
		var HIS_KYSO_BANGKE_KTBA = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_KYSO_BANGKE_KTBA');
		if (_benhnhan.LOAITIEPNHANID != '1' && HIS_KYSO_BANGKE_KTBA == '2') {
			_rptCode = _rptCode + '_NOITRU';
		}
		if (typeof opts == 'object') {
			_rptCode = opts.RPT_CODE;
		}
		var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", _rptCode);
		if (data_ar != null && data_ar.length > 0) {
			isKySo = true;
			if (data_ar.length > 1) {
				var row = data_ar[1];
			} else {
				var row = data_ar[0];
			}
			catype = row.CA_TYPE;
			kieuky = row.KIEUKY;
			bnky = row.BNKY;
			if (catype == '5') {
				isKyTocken = true;
			} else if (catype == '3' || catype == '6') {
				var _url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
				let
				_paramInput = {
					params : null,
					smartca_method : 0
				};
				EventUtil.setEvent("dlgCaLogin_confirm", function() {
					DlgUtil.close("divCALOGIN");
					let
					_hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
					causer = _hisl2SmartCa.token.refresh_token;
					capassword = _hisl2SmartCa.token.access_token;
					smartcauser = _hisl2SmartCa.user.uid;
					_caRpt2(signType, opts); // L2PT-48254
				});
				let
				hisl2SmartCa = JSON.parse(sessionStorage.getItem("hisl2_smartca"));
				if (hisl2SmartCa && hisl2SmartCa.token && hisl2SmartCa.token.access_token) {
					_paramInput.smartca_method = 1;
					let
					_popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
					_popup.open("divCALOGIN");
					return;
				} else {
					EventUtil.setEvent("dlgCA_SMARTCA_LOGIN_Close2", function(e) {
						if (e.data && e.data.token && e.data.token.access_token) {
							_paramInput.smartca_method = 1;
						}
						DlgUtil.close("dlgCA_SMARTCA_LOGIN");
						let
						_popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", _url, _paramInput, kieuky, 505, 268);
						_popup.open("divCALOGIN");
						return;
					});
					DlgUtil.buildPopupUrl("dlgCA_SMARTCA_LOGIN", "divDlg", "manager.jsp?func=../danhmuc/CA_SMARTCA_LOGIN", {
						isSignPopup : true
					}, "Smart Ca Login", 500, 650);
					DlgUtil.open("dlgCA_SMARTCA_LOGIN");
					return;
				}
			} else {
				EventUtil.setEvent("dlgCaLogin_confirm", function(e) {
					causer = e.username;
					capassword = e.password;
					DlgUtil.close("divCALOGIN");
					_caRpt2(signType, opts); // L2PT-48254
				});
				EventUtil.setEvent("dlgCaLogin_close", function(e) {
					DlgUtil.close("divCALOGIN");
				});
				var url = "manager.jsp?func=../danhmuc/CA_LOGIN_FORM";
				var popup = DlgUtil.buildPopupUrl("divCALOGIN", "divDlg", url, "", kieuky, 505, 268);
				popup.open("divCALOGIN");
			}
		} else {
			DlgUtil.showMsg('Chưa cấu hình phiếu CA theo rpt_code trong cấu hình RPT_CODE_KYSO_BANGKE!');
			return;
		}
	}
	function _caRpt2(signType, opts) { // L2PT-48254
		// L2PT-48254 ký tạm thu
		if (typeof opts == 'undefined') {
			opts = new Object();
		}
		lstParamKyCa = []; // L2PT-65011
		var HIS_KYSO_TAMUNG_VP1 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_KYSO_TAMUNG_VP1');
		if (HIS_KYSO_TAMUNG_VP1 == '1' || opts.RPT_CODE == 'NGT034_PHIEUTAMUNG_A4') {
			var par = [];
			par.push({
				name : 'hosobenhanid',
				type : 'String',
				value : _benhnhan.HOSOBENHANID
			});
			par.push({
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid
			});
			par.push({
				name : 'phieuthuid',
				type : 'String',
				value : _phieuthuid
			});
			par.push({
				name : 'rpt_code',
				type : 'String',
				value : 'NGT034_PHIEUTAMUNG_A4'
			});
			if (signType == '0') {
				CommonUtil.openReportGetCA2(par, false);
			} else if (signType == '1' && bnky != '0') {
				lstParamKyCa.push(par);
			} else {
				var oData = {
					sign_type : signType,
					causer : causer,
					capassword : capassword,
					smartcauser : smartcauser,
					params : par
				};
				var msg = CommonUtil.caRpt(oData, 'NGT034_PHIEUTAMUNG_A4', true, '', false, kieuky, catype);
				DlgUtil.showMsg(msg);
			}
		}
		// L2PT-48254 ký tạm thu
		else {
			// L2PT-28034 start: Ký số cho BDHNI
			var VPI_INPHOI_THEO_HD = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_INPHOI_THEO_HD');
			if (VPI_INPHOI_THEO_HD == '1') { // Ký số cho BDHNI
				var data_ar_ptid = [];
				data_ar_ptid = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.DS.PTID", _tiepnhanid);
				if (data_ar_ptid && data_ar_ptid.length > 0) {
					lstParamKyCa = [];
					var opt_kyCA = new Object;
					opt_kyCA.HOSOBENHANID = _benhnhan.HOSOBENHANID;
					opt_kyCA.inbangkechuan = 1;
					opt_kyCA.tiepnhanid = _tiepnhanid.toString();
					opt_kyCA.signType = signType;;
					// BVTM-843 sửa tên biến
					for (var j = 0; j < data_ar_ptid.length; j++) {
						//khoi tao mang cho moi hoa don (phieuthuid) moi
						var _phieuthuid_cur = data_ar_ptid[j].PHIEUTHUID; // BVTM-843 sửa tên biến
						opt_kyCA.phieuthuid = _phieuthuid_cur.toString();
						// var flagHD_TUTRA = jsonrpc.AjaxJson.ajaxCALL_SP_I("CHECK_HD_TUTRA", _phieuthuid_cur);						
						// L2PT-32533 start
						if (_benhnhan.DOITUONGBENHNHANID == 7) {
							// ký số BK VIP
							opt_kyCA.rpt_code_kyso = 'NGT001_BKCPKCBBHYT_QD6556_VIP_HOADON_A4';
							parKyCA(opt_kyCA);
						} else if (_benhnhan.DOITUONGBENHNHANID == 1) {
							// ký số BK 6556
							opt_kyCA.rpt_code_kyso = 'NGT001_BKCPKCBBHYT_QD6556_A4';
							parKyCA(opt_kyCA);
							// ký số BK BHYT
							opt_kyCA.rpt_code_kyso = 'NGT001_BKCPKCBBHYT_QD6556_XEMTRUOC_A4';
							parKyCA(opt_kyCA);
							// ký số BK tự chi trả cho BN BHYT
							opt_kyCA.rpt_code_kyso = 'NGT001_BKCPKCBTUTRA_QD6556_BNBH_XEMTRUOC_A4';
							parKyCA(opt_kyCA);
							// ký số BK hao phí
							opt_kyCA.rpt_code_kyso = 'NGT001_BKCPKCBDV_QD6556_XEMTRUOC_A4';
							parKyCA(opt_kyCA);
						} else {
							// ký số BK 6556
							opt_kyCA.rpt_code_kyso = 'NGT001_BKCPKCBBHYT_QD6556_A4';
							parKyCA(opt_kyCA);
							// ký số BK tự chi trả cho BN viện phí
							opt_kyCA.rpt_code_kyso = 'NGT001_BKCPKCBTUTRA_QD6556_XEMTRUOC_A4';
							parKyCA(opt_kyCA);
						}
					}
					if (lstParamKyCa.length != '0') {
						var _options = new Object();
						_options.bnky = bnky;
						_options.username = causer;
						_options.password = capassword;
						_options.smartcauser = smartcauser;
						CommonUtil._caRptTocken(lstParamKyCa, signType, true, false, kieuky, '', _options);
						EventUtil.setEvent("eventKyCA", function(e) {
							DlgUtil.showMsg(e.res);
							return;
						});
					}
				} else {
					DlgUtil.showMsg('Bệnh nhân chưa có hóa đơn');
				}
			}
			// L2PT-28034 end
			else { // ký số cho các BV khác
				lstParamKyCa = [];
				var arrReportCode = [];
				vienphi_tinhtien.inBangKe(_tiepnhanid, _benhnhan.DOITUONGBENHNHANID, _benhnhan.LOAITIEPNHANID, arrReportCode, 0, 0);
				console.log(arrReportCode);
				var HIS_KYSO_BANGKE_KTBA = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'HIS_KYSO_BANGKE_KTBA');
				arrReportCode.forEach(function(el) {
					if (_benhnhan.LOAITIEPNHANID != '1' && HIS_KYSO_BANGKE_KTBA == '2') {
						el = el + '_NOITRU';
					}
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("EMR.GET.TTPHIEU", el);
					if (data_ar != null && data_ar.length > 0) {
						var par = [];
						par.push({
							name : 'hosobenhanid',
							type : 'String',
							value : _benhnhan.HOSOBENHANID
						});
						par.push({
							name : 'tiepnhanid',
							type : 'String',
							value : _tiepnhanid
						});
						par.push({
							name : 'rpt_code',
							type : 'String',
							value : el
						});
						if (signType == '0') {
							CommonUtil.openReportGetCA2(par, false);
						} else if (signType == '1' && bnky != '0') {
							lstParamKyCa.push(par);
						} else {
							var oData = {
								sign_type : signType,
								causer : causer,
								capassword : capassword,
								smartcauser : smartcauser,
								params : par
							};
							var msg = CommonUtil.caRpt(oData, el, true, '', false, kieuky, catype);
							DlgUtil.showMsg(msg);
						}
					}
				});
			}
		}
		if (lstParamKyCa.length != '0') {
			var _options = new Object();
			_options.bnky = bnky;
			_options.username = causer;
			_options.password = capassword;
			_options.smartcauser = smartcauser;
			CommonUtil._caRptTocken(lstParamKyCa, signType, true, false, kieuky, '', _options);
			EventUtil.setEvent("eventKyCA", function(e) {
				DlgUtil.showMsg(e.res);
				return;
			});
		}
	}
	// L2PT-87409 end
	//End TuyenDV BVTM-2440
	// lay du lieu cac dich vu da chon
	function loadDV() {
		objData = new Object();
		_phieuInfo = [];
		_arrMG = [];
		_DMMG = [];
		//L2PT-19304 start
		_DM_HTTT = [];
		_arrHTTT = [];
		//L2PT-19304 end
		_tien_hoadon = 0;
		obj = new Object();
		obj_2 = new Object();
		_loaiphieuthu = $("#cboLOAIPHIEUTHUID").val();
		var _loaitt = $("#cboHINHTHUCTHANHTOAN").val();
		var _loaiso = 1;
		var _loaidt = -1;
		var _loaivat = 0; // L2PT-34420
		_co_dv = false;
		setEnabled([], [ 'txtMAPHIEUTHU' ]);
		objData.TIEPNHANID = _benhnhan.TIEPNHANID;
		if (_loaiphieuthu == _TAMUNG) {
			_hetphieu_2 = false;
		} else {
			var grid = $("#" + _gridId_DV);
			var _selRowIds = grid.jqGrid('getGridParam', 'selarrrow');
			_ds_nhomtt = [];
			var _arr_hoadon = [];
			if (VP_TACH_HOADON == 0 || (_benhnhan.LOAITIEPNHANID != 1 && VPI_TACHHD_NOITRU == 1)) {
				_ds_nhomtt.push(0);
				_arr_hoadon.push(_selRowIds);
			} else {
				for (var k = 0; k < _selRowIds.length; k++) {
					var _nhomthanhtoan = $("#" + _gridId_DV).jqGrid('getCell', _selRowIds[k], 'NHOMTHANHTOAN');
					if (_ds_nhomtt.indexOf(_nhomthanhtoan) == -1) {
						_ds_nhomtt.push(_nhomthanhtoan);
					}
				}
				for (var q = 0; q < _ds_nhomtt.length; q++) {
					var _hoadon = [];
					var count = 0;
					for (var p = 0; p < _selRowIds.length; p++) {
						var _nhom_tt = $("#" + _gridId_DV).jqGrid('getCell', _selRowIds[p], 'NHOMTHANHTOAN');
						if ((count >= _SL_DICHVU && _nhom_tt == _DT_DICHVU) || (_benhnhan.LOAITIEPNHANID == 1 && count >= _SL_VIENPHI && _nhom_tt == _DT_VIENPHI)) {
							_arr_hoadon.push(_hoadon);
							count = 0;
							_hoadon = [];
						}
						if (_nhom_tt == _ds_nhomtt[q]) {
							_hoadon.push(_selRowIds[p]);
							count++;
						}
					}
					_arr_hoadon.push(_hoadon);
				}
			}
			for (var j = 0; j < _arr_hoadon.length; j++) {
				var _tongtien = 0;
				var _thucthu = 0;
				var _miengiamdv = 0;
				var _bhyt_tra = 0;
				var _DSDV = [];
				var _ds_maubpid = [];
				var _ds_dtdvid = [];
				var _ds_dvkbid = "";
				var _hoa_don = _arr_hoadon[j];
				var _loai_dichvu = 0;
				for (var i = 0; i < _hoa_don.length; i++) {
					var _dvRow = $("#" + _gridId_DV).jqGrid('getRowData', _hoa_don[i]);
					$("#" + _gridId_DV).jqGrid('setCell', _hoa_don[i], 'RN', i + 1);
					if (true) {
						var _objRow = new Object();
						_ds_dvkbid += _dvRow.DICHVUKHAMBENHID + ",";
						_objRow.THUCTHU = _dvRow.THUCTHU;
						_objRow.TIEN_BHYT_TRA = _dvRow.TIEN_BHYT_TRA;
						_objRow.TIEN_BNCCT = _dvRow.TIEN_BNCCT; // L2PT-13344
						_objRow.TIEN_MIENGIAM = _dvRow.TIEN_MIENGIAM;
						_objRow.DICHVUKHAMBENHID = _dvRow.DICHVUKHAMBENHID;
						_objRow.KHOANMUCID = _dvRow.KHOANMUCID;
						_objRow.TENKHOANMUC = _dvRow.TENKHOANMUC;
						_objRow.MAKHOANMUC = _dvRow.MAKHOANMUC;
						_objRow.VERSION_OLD = _dvRow.VERSION_OLD;
						_objRow.VAT = _dvRow.TYLE_VAT; // L2PT-34420
						_DSDV.push(_objRow);
						_tongtien += parseFloat(_dvRow.TIENDICHVU) * _dvRow.SOLUONG;
						_bhyt_tra += parseFloat(_dvRow.TIEN_BHYT_TRA);
						_thucthu += parseFloat(_dvRow.THUCTHU);
						_miengiamdv += parseFloat(_dvRow.TIEN_MIENGIAM);
						var _mbpid = parseInt(_dvRow.MAUBENHPHAMID);
						var _loaidtdv = parseInt(_dvRow.LOAIDOITUONG);
						_loaidt = _loaidtdv;
						_loai_dichvu = _dvRow.NHOMTHANHTOAN;
						if (_ds_maubpid.indexOf(_mbpid) == -1)
							_ds_maubpid.push(_mbpid);
						if (_ds_dtdvid.indexOf(_loaidtdv) == -1)
							_ds_dtdvid.push(_loaidtdv);
					}
				}
				_tongtien = parseFloat(_tongtien.toFixed(2));
				_bhyt_tra = parseFloat(_bhyt_tra.toFixed(2));
				_thucthu = parseFloat(_thucthu.toFixed(2));
				_miengiamdv = parseFloat(_miengiamdv.toFixed(2));
				_ds_dvkbid = _ds_dvkbid.slice(0, -1);
				var _ds_maubpid_str = JSON.stringify(_ds_maubpid);
				var _ds_dtdvid_str = JSON.stringify(_ds_dtdvid);
				_ds_maubpid_str = _ds_maubpid_str.slice(1, -1);
				_ds_dtdvid_str = _ds_dtdvid_str.slice(1, -1);
//		    console.log(_DSDV);
//				objData.LOAIDOITUONG = _loaidt + "";
//				objData["DS_MAUBENHPHAMID"] = _ds_maubpid_str;
//				objData["DANHSACHDOITUONGDICHVU"] = _ds_dtdvid_str;
//				objData["TONGTIEN"] = _thucthu + "";
//				objData["THUCTHU"] = _thucthu + "";
//				objData["TIEN_BHYT_TRA"] = _bhyt_tra + "";
//				objData["MIENGIAMDV"] = _miengiamdv + "";
//				objData["DOITUONGBENHNHANID"] = _benhnhan.DOITUONGBENHNHANID;
//				objData["DSDVKBID"] = "";
				if (_DSDV.length > 0 && (VPI_HD_KHONGDONG == 1 || _thucthu != 0)) {
					if (_loaiphieuthu == _HOANDICHVU) {
						_tien_hoadon += _thucthu;
						objData["DSDVKBID"] = _ds_dvkbid;
						obj.TONGTIEN = _thucthu + "";
						obj.THUCTHU = _thucthu + "";
						obj.DS_MAUBENHPHAMID = _ds_maubpid_str;
						obj.DANHSACHDOITUONGDICHVU = _ds_dtdvid_str;
						_hetphieu_2 = false;
						co_dv = false;
					} else {
						if (VP_TACH_HOADON == 0 || (_benhnhan.LOAITIEPNHANID != 1 && VPI_TACHHD_NOITRU == 1)) {
							_loaiso = 1;
						} else {
							if (_loai_dichvu == 1) {
								_loaiso = 2;
							} else if (_loai_dichvu == 2) {
								_loaiso = 3;
							} else if (_loai_dichvu == 3) {
								_loaiso = 4;
							} else if (_loai_dichvu == 4) {
								_loaiso = 1;
							} else if (_loai_dichvu == 5) {
								_loaiso = 4;
							} else if (_loai_dichvu == 6) {
								_loaiso = 1;
							} else if (_loai_dichvu == 7) {
								_loaiso = 4;
							} else if (_loai_dichvu == 8) {
								_loaiso = 2;
							} else if (_loai_dichvu == 9) {
								_loaiso = 3;
							} else if (_loai_dichvu == 10) {
								_loaiso = 4;
							} else if (_loai_dichvu == 11) {
								_loaiso = 4;
							}
							// L2PT-23662 start
							else if (_loai_dichvu == 12) {
								_loaiso = 1;
							} else if (_loai_dichvu == 13) {
								_loaiso = 4;
							}
							// L2PT-23662 end
							// L2PT-34672 start
							else if (_loai_dichvu == 14) {
								_loaiso = 2; // L2PT-3896
							} else if (_loai_dichvu == 15) {
								_loaiso = 1;
							}
							// L2PT-34672 end
							// L2PT-34420 start
							else if (_loai_dichvu == 16) {
								_loaiso = 1;
								_loaivat = 0;
							} else if (_loai_dichvu == 17) {
								_loaiso = 1;
								_loaivat = 1;
							} else if (_loai_dichvu == 18) {
								_loaiso = 4;
								_loaivat = 0;
							}
							// L2PT-34420 end
						}
						var _kieuthu_2 = _THUTIEN;
						/*var _sql_par_2 = [];
						_sql_par_2.push({
							name : "[0]",
							value : _loaiso
						});
						_sql_par_2.push({
							name : "[1]",
							value : (_kieuthu_2)
						});
						_sql_par_2.push({
							name : "[2]",
							value : _loaitt
						});
						_sql_par_2.push({
							name : "[3]",
							value : _phong_id
						});*/
						//var _input_2 = _loaiso + "$" + _kieuthu_2 + "$" + _loaitt + "$" + _phong_id;
						// L2PT-34420 start
						var objLaySo = {
							LOAISO : _loaiso + "",
							KIEUTHU : _kieuthu_2 + "",
							HINHTHUCTHANHTOAN : _loaitt + "",
							PHONGID : _phong_id + "",
							VAT : _loaivat + "",
						}
						var _result_2 = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_sophieuthu, JSON.stringify(objLaySo));
						// L2PT-34420 end
						_dsSo_2 = _result_2;
						_co_dv = true;
						if (!_dsSo_2 || _dsSo_2.length == 0) { // kiem tra mang ton tai
							_hetphieu_2 = true;
							obj_2.MAPHIEUTHU = "";
							obj_2.NHOMPHIEUTHUID = "";
							obj_2.MANHOMPHIEUTHU = "";
							obj_2.LOAIPHIEUTHU = "";
							break;
						} else {
							$('#cboMANHOMPHIEUTHU').empty();
							$.each(_dsSo_2, function(i, _so) {
								$('#cboMANHOMPHIEUTHU').append($('<option>', {
									value : i,
									text : _so.MANHOMPHIEUTHU
								}));
							});
							$('#cboMANHOMPHIEUTHU').val(0);
							_hetphieu_2 = false;
							obj_2.MAPHIEUTHU = _dsSo_2[0].MAPHIEUTHU;
							obj_2.SOPHIEUTO = _dsSo_2[0].SOPHIEUTO;
							//tuyennx_add_start_20171121 yc HISL2CORE-599
							obj_2.KHOASOPHIEUTU = _dsSo_2[0].KHOASOPHIEUTU;
							//tuyennx_add_end_20171121 yc HISL2CORE-599
							obj_2.SOPHIEUFROM = _dsSo_2[0].SOPHIEUFROM;
							obj_2.NHOMPHIEUTHUID = _dsSo_2[0].NHOMPHIEUTHUID;
							obj_2.MANHOMPHIEUTHU = _dsSo_2[0].MANHOMPHIEUTHU;
							obj_2.LOAIPHIEUTHU = _dsSo_2[0].LOAIPHIEUTHU;
							obj_2.LOAIPHIEUTHUID = _THUTIEN + "";
							obj_2.LOAIDOITUONG = _loaidt + "";
							obj_2.DS_MAUBENHPHAMID = _ds_maubpid_str;
							obj_2.DANHSACHDOITUONGDICHVU = _ds_dtdvid_str;
							obj_2.TONGTIEN = _thucthu + "";
							obj_2.THUCTHU = _thucthu + "";
							obj_2.TIEN_BHYT_TRA = _bhyt_tra + "";
							obj_2.MIENGIAMDV = _miengiamdv + "";
							obj_2.NHOMTHANHTOAN = _loai_dichvu;
							_tien_hoadon += _thucthu;
							var arr = [];
							var _PTCT = [];
							//					_dskm = objData.PTCT?objData.PTCT:[];
							for (var y = 0; y < _DSDV.length; y++) {
								if (arr.indexOf(_DSDV[y].KHOANMUCID) == -1) {
									arr.push(_DSDV[y].KHOANMUCID);
								}
							}
							for (var m = 0; m < arr.length; m++) {
								var _km = new Object();
								_km.TONGTIEN = 0;
								for (var n = 0; n < _DSDV.length; n++) {
									if (_DSDV[n].KHOANMUCID == arr[m]) {
										_km.KHOANMUCID = _DSDV[n].KHOANMUCID;
										_km.TENKHOANMUC = _DSDV[n].TENKHOANMUC;
										_km.MAKHOANMUC = _DSDV[n].MAKHOANMUC;
										_km.TONGTIEN += parseFloat(_DSDV[n].THUCTHU);
									}
								}
								_km.TONGTIEN += "";
								_PTCT.push(_km);
							}
							//					if(nhomthanhtoan == 1)
							//						objData.PTCT_BH = rs;
							//					else if(nhomthanhtoan == 2){
							//						objData.PTCT_VP = rs;
							//					} else if(nhomthanhtoan == 3){
							//						objData.PTCT_DV = rs;
							//					}
							var _noidungthu = "";
							// L2PT-69168: làm tròn tiền trong các khoản mục(VPI_LAMTRON_NDTHU)
							var _soChuSo = parseFloat(fConfig.VPI_LAMTRON_NDTHU);
							if (!_soChuSo || _soChuSo == "") {
								_soChuSo = 2;
							}
							for (var x = 0; x < _PTCT.length; x++) {
								_noidungthu += _PTCT[x].TENKHOANMUC + ": " + parseFloat(_PTCT[x].TONGTIEN).toFixed(_soChuSo) + ", ";
							}
							_noidungthu = _noidungthu.slice(0, -2);
							objData.NDGHICHU = "Thu tiền viện phí " + "( " + _noidungthu + ")";// L2PT-28714
							if (VP_TACH_HOADON == 1 && (_loaidt == 1 || _loaidt == 2 || _loaidt == 3)) {
								_noidungthu = "Đồng chi trả BHYT( " + _noidungthu + ")";
								obj_2.LOAITHUTIEN = "1";
							} else {
								_noidungthu = "Thu tiền cho viện phí " + _benhnhan.MATIEPNHAN + "( " + _noidungthu + ")";
								obj_2.LOAITHUTIEN = "0";
							}
							// L2PT-28714 start
							if (VPI_GTMD_GHICHU == 1) {
								_noidungthu = objData.NDGHICHU;
							}
							// L2PT-28714 end
							// L2PT-28406 start
							if (VPI_NHAP_GHICHU == 2) {
								_noidungthu = "";
							}
							// L2PT-28406 end
							obj_2.NOIDUNGTHU = _noidungthu;
							obj_2.NOIDUNGIN = _noidungthu;
							obj_2.DSDV = _DSDV;
							obj_2.PTCT = _PTCT;
							var _phieu_2 = clone(obj_2);
							_phieuInfo.push(_phieu_2);
							//			    	if(_ds_nhomtt[j] ==  1) {
							//			    		objData.DSDV_BH = _DSDV;
							//			    	} else if(_ds_nhomtt[j] ==  2) {
							//			    		objData.DSDV_VP = _DSDV;
							//			    	} else if(_ds_nhomtt[j] ==  3) {
							//			    		objData.DSDV_DV = _DSDV;
							//			    	}
						}
						if (VPI_TUDONGTACH_HOADON != 1) {
							setEnabled([ 'txtMAPHIEUTHU' ], []);
						}
					}
				}
				_tien_hoadon = parseFloat(_tien_hoadon.toFixed(2));
			}
			//	        objData.DSDV = _DSDV_arr;
			//	        objData.PTCT = _PTCT_arr;
		}
		if (_loaiphieuthu == _TAMUNG) {
			// L2PT-35120 start
			//setEnabled([], [ 'cboMANHOMPHIEUTHU' ]);
			setEnabled([ 'cboMANHOMPHIEUTHU' ], []);
			// L2PT-35120 end
		} else {
			setEnabled([ 'cboMANHOMPHIEUTHU' ], []);
			var nhomphieuthuid = 0;
			for (var cp = 0; cp < _phieuInfo.length; cp++) {
				var nptid = parseInt(_phieuInfo[cp].NHOMPHIEUTHUID);
				if (nhomphieuthuid != 0 && nhomphieuthuid != nptid) {
					setEnabled([], [ 'cboMANHOMPHIEUTHU' ]);
					break;
				}
				nhomphieuthuid = nptid;
			}
		}
		if (_benhnhan.DOITUONGBENHNHANID == 1)
			_loaiso = 2;
		else {
			_loaiso = 3;
		}
		var _kieuthu = _loaiphieuthu;
/*		var _sql_par = [];
		_sql_par.push({
			name : "[0]",
			value : _loaiso
		});
		_sql_par.push({
			name : "[1]",
			value : (_kieuthu)
		});
		_sql_par.push({
			name : "[2]",
			value : _loaitt
		});
		_sql_par.push({
			name : "[3]",
			value : _phong_id
		});*/
		// L2PT-34420 start
		var objLaySo = {
			LOAISO : _loaiso + "",
			KIEUTHU : _kieuthu + "",
			HINHTHUCTHANHTOAN : _loaitt + "",
			PHONGID : _phong_id + "",
			VAT : _loaivat + "",
		}
		_dsSo = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_sophieuthu, JSON.stringify(objLaySo));
		// L2PT-34420 end
		if (!_dsSo || _dsSo.length == 0) { // kiem tra mang ton tai
			_hetphieu = true;
			obj.MAPHIEUTHU = "";
			obj.NHOMPHIEUTHUID = "";
			obj.MANHOMPHIEUTHU = "";
			obj.LOAIPHIEUTHU = "";
			obj.LOAIPHIEUTHUID = _loaiphieuthu + "";
		}
//	    else if(_size_sopt>1){
//	    	DlgUtil.showMsg("sổ phiếu thu không hợp lệ");
//	    	return;
//	    }
		else {
			_hetphieu = false;
			obj.MAPHIEUTHU = _dsSo[0].MAPHIEUTHU;
			obj.SOPHIEUTO = _dsSo[0].SOPHIEUTO;
			//tuyennx_add_start_20171121 yc HISL2CORE-599
			obj.KHOASOPHIEUTU = _dsSo[0].KHOASOPHIEUTU;
			//tuyennx_add_end_20171121 yc HISL2CORE-599
			obj.SOPHIEUFROM = _dsSo[0].SOPHIEUFROM;
			obj.NHOMPHIEUTHUID = _dsSo[0].NHOMPHIEUTHUID;
			obj.MANHOMPHIEUTHU = _dsSo[0].MANHOMPHIEUTHU;
			obj.LOAIPHIEUTHU = _dsSo[0].LOAIPHIEUTHU;
			obj.LOAIPHIEUTHUID = _loaiphieuthu + "";
			obj.LOAIDOITUONG = "-1";
			obj.LOAITHUTIEN = "-1";
			obj.DS_MAUBENHPHAMID = "";
			obj.DANHSACHDOITUONGDICHVU = "";
			obj.TIEN_BHYT_TRA = "0";
			obj.MIENGIAMDV = "0";
			objData["PHONGID_DANGNHAP"] = _phong_id;
//	    	console.log(JSON.stringify(objData));
		}
		$("#txtNGUOILAP").val(_user_name);
		$('#txtNGAYLAP').val(jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS'));
		if (_tien_hoadon && _tien_hoadon > 0 && _phieuInfo.length == 1) {
			var grid_pt = $("#" + _gridId_PT);
			var ids = grid_pt.getDataIDs();
			for (var z = 0; z < ids.length; z++) {
				var id = ids[z];
				var _huygiuso = grid_pt.jqGrid('getCell', id, 'HUYGIUSO');
				var _tongtien_pttl = grid_pt.jqGrid('getCell', id, 'TONGTIEN');
				var loaiphieuthuid = grid_pt.jqGrid('getCell', id, 'LOAIPHIEUTHUID');
				var _phieuthuid_thulai = grid_pt.jqGrid('getCell', id, 'PHIEUTHUID');
				if (loaiphieuthuid == 1 && _huygiuso == 1 && _tongtien_pttl == _tien_hoadon) {
					var _sophieu_giu = grid_pt.jqGrid('getCell', id, 'SOPHIEU_GIU');
					var _nptid_giu = grid_pt.jqGrid('getCell', id, 'NHOMPHIEUTHUID');
					var _mnpt_giu = grid_pt.jqGrid('getCell', id, 'MANHOMPHIEUTHU');
					var _spt_giu = grid_pt.jqGrid('getCell', id, 'SOPHIEUTO');
					var _spf_giu = grid_pt.jqGrid('getCell', id, 'SOPHIEUFROM');
					var _kspt_giu = grid_pt.jqGrid('getCell', id, 'KHOASOPHIEUTU');
					obj_2.MAPHIEUTHU = _sophieu_giu;
					obj_2.NHOMPHIEUTHUID = _nptid_giu;
					obj_2.MANHOMPHIEUTHU = _mnpt_giu;
					obj_2.SOPHIEUTO = _spt_giu;
					obj_2.SOPHIEUFROM = _spf_giu;
					obj_2.KHOASOPHIEUTU = _kspt_giu;
					_phieu_2.MAPHIEUTHU = _sophieu_giu;
					_phieu_2.NHOMPHIEUTHUID = _nptid_giu;
					_phieu_2.MANHOMPHIEUTHU = _mnpt_giu;
					_phieu_2.SOPHIEUTO = _spt_giu;
					_phieu_2.SOPHIEUFROM = _spf_giu;
					_phieu_2.KHOASOPHIEUTU = _kspt_giu;
					objData.PHIEUTHUID_THULAI = _phieuthuid_thulai + "";
				}
			}
		}
		if (_co_dv && _dsSo_2 && _dsSo_2.length > 0) {
			var obj_2_dis = new Object();
			obj_2_dis.LOAIPHIEUTHUID = obj.LOAIPHIEUTHUID;
//			obj_2_dis.TONGTIEN = vienphi_tinhtien.convertNumToCurrency(obj_2.TONGTIEN, true);
//			obj_2_dis.THUCTHU = vienphi_tinhtien.convertNumToCurrency(obj_2.THUCTHU, true);
			obj_2_dis.MAPHIEUTHU = obj_2.MAPHIEUTHU;
//			obj_2_dis.MANHOMPHIEUTHU = obj_2.MANHOMPHIEUTHU;
			FormUtil.setObjectToForm("ttThuTien", "", obj_2_dis);
			$("#cboMANHOMPHIEUTHU").val(0);
		} else if (_dsSo && _dsSo.length > 0 && _dsSo[0].SOAO != 1) {
			var obj_dis = new Object();
			obj_dis.LOAIPHIEUTHUID = obj.LOAIPHIEUTHUID;
//			obj_dis.TONGTIEN = vienphi_tinhtien.convertNumToCurrency(obj.TONGTIEN, true);
//			obj_dis.THUCTHU = vienphi_tinhtien.convertNumToCurrency(obj.THUCTHU, true);
			obj_dis.MAPHIEUTHU = obj.MAPHIEUTHU;
			FormUtil.setObjectToForm("ttThuTien", "", obj_dis);
			$('#cboMANHOMPHIEUTHU').empty();
			$.each(_dsSo, function(i, _so) {
				$('#cboMANHOMPHIEUTHU').append($('<option>', {
					value : i,
					text : _so.MANHOMPHIEUTHU
				}));
			});
			$('#cboMANHOMPHIEUTHU').val(0);
		}
		cal(_loaiphieuthu);
		// L2PT-33791 thêm btnLuuVaPH
		// L2PT-53446 thêm btnLuuVaKySo
		setEnabled([ 'btnLuu', 'btnLuuVaPH', 'btnLuuVaKySo', 'btnHuyBo', 'chkNoTien' ], [ 'btnThem' ]);
		// L2PT-114388 start
		if (fConfig.VPI_THUTIEN_NHANH == 1 && _thu_nhanh && flagLoading) {
			_thu_nhanh = false;
			setTimeout(function() {
				$("#btnLuuNhanh").trigger('click');
			}, 1000);
		}
		// L2PT-114388 end
	}
	// tinh toan va hien thi du lieu vien phi
	function cal(_loaiphieuthu) {
		_phieutamungid = null;
		if (_loaiphieuthu == _THUTIEN) {
			$("#divMain").attr("help", "help_VIII_2_Thanhtoanvienphi");
		} else if (_loaiphieuthu == _TAMUNG) {
			$("#divMain").attr("help", "help_VIII_1_Tamung");
		} else if (_loaiphieuthu == _HOANUNG) {
			$("#divMain").attr("help", "help_VIII_3_Hoantra");
		}
		var _sotien = 0;
		var objTien = new Object();
		var objTien_dis = new Object();
		var _miengiam = parseFloat(get_val_m('txtMIENGIAM_PT'));
		_miengiam = !_miengiam ? 0 : _miengiam;
		if (_loaiphieuthu == _HOANUNG || _loaiphieuthu == _HOANDICHVU) {
			$("#lTHUCTHU").text('Hoàn tiền');
		} else {
			$("#lTHUCTHU").text('Thực thu');
		}
		if (_loaiphieuthu == _THUTIEN) {
			setEnabled([ 'btnLYDO', 'txtMIENGIAM_PT', 'txtLYDO', 'txtTLMIENGIAM' ], [ 'txtTONGTIEN' ]);
			val_m('txtTHUCTHU', get_val_m('txtTONGTIEN') - _miengiam);
			_sotien = !parseFloat(get_val_m('txtTONGTIEN')) ? 0 : parseFloat(get_val_m('txtTONGTIEN'));
			_sotien = (_sotien - _miengiam).toFixed(2);
			objTien.DANOP = (parseFloat(_vpData.DANOP) + _sotien).toFixed(2);
			objTien.DATHU = (parseFloat(_vpData.DATHU) + _tien_hoadon).toFixed(2);//L2PT-8100
			objTien.CHUATHU = (parseFloat(_vpData.CHUATHU) - _tien_hoadon).toFixed(2);//L2PT-8100
			objTien.TAMUNG = parseFloat(_vpData.TAMUNG);
			objTien.HOANUNG = parseFloat(_vpData.HOANUNG);
			objTien.NOPTHEM = (parseFloat(_vpData.NOPTHEM) - _sotien).toFixed(2);
			objTien.MIENGIAM = (parseFloat(_vpData.MIENGIAM) + _miengiam).toFixed(2);
			objTien_dis = vienphi_tinhtien.convertObjToCurrency(objTien);
//			console.log(objTien_dis);
			FormUtil.setObjectToForm('tongVP', '', objTien_dis);
//			if (_co_dv) {
//				if (objData.LOAIDOITUONG == 1 || objData.LOAIDOITUONG == 2 || objData.LOAIDOITUONG == 3) {
//					_noidungthu = "Đồng chi trả BHYT";
//					objData.LOAITHUTIEN = 1;
//				} else {
//					_noidungthu = "Thu tiền cho viện phí " + _benhnhan.MATIEPNHAN;
//					objData.LOAITHUTIEN = 0;
//				}
//				$("#txtGHICHU").val(_noidungthu);
//			}
		} else if (_loaiphieuthu == _THUTHEM) {
//			$("#txtMIENGIAM_PT").val(0);
//			$("#txtTLMIENGIAM").val(0);
			setEnabled([ 'btnLYDO', 'txtMIENGIAM_PT', 'txtLYDO', 'txtTLMIENGIAM' ], [ 'txtTONGTIEN' ]);
			var _tongtien = parseFloat(_tien_hoadon);
			if (_benhnhan.TRANGTHAITIEPNHAN == 1 || _benhnhan.TRANGTHAITIEPNHAN == 2) {
				if (VPI_QUYTRINH_VIENPHI != 1 && _chot)
					_tongtien = parseFloat(_vpData.NOPTHEM);
			}
			_sotien = parseFloat((_tongtien - _miengiam).toFixed(2));
			_sotien = _sotien > 0 ? _sotien : 0;
			val_m('txtTONGTIEN', _tongtien);
			val_m('txtTHUCTHU', _sotien);
			objTien.DANOP = (parseFloat(_vpData.DANOP) + _sotien).toFixed(2);
			objTien.DATHU = (parseFloat(_vpData.DATHU) + _tien_hoadon).toFixed(2);//L2PT-8100
			objTien.CHUATHU = (parseFloat(_vpData.CHUATHU) - _tien_hoadon).toFixed(2);//L2PT-8100
			objTien.TAMUNG = parseFloat(_vpData.TAMUNG);
			objTien.HOANUNG = parseFloat(_vpData.HOANUNG);
			objTien.NOPTHEM = (parseFloat(_vpData.NOPTHEM) - _sotien).toFixed(2);
			objTien.MIENGIAM = (parseFloat(_vpData.MIENGIAM) + _miengiam).toFixed(2);
			objTien_dis = vienphi_tinhtien.convertObjToCurrency(objTien);
			FormUtil.setObjectToForm('tongVP', '', objTien_dis);
			$("#txtGHICHU").val("Thu tiền cho viện phí " + _benhnhan.MATIEPNHAN);
			// L2PT-28714 start
			if (VPI_GTMD_GHICHU == 1) {
				$("#txtGHICHU").val(objData.NDGHICHU);
			}
			// L2PT-28714 end
			objData.TONGTIEN = _sotien + "";
		} else if (_loaiphieuthu == _TAMUNG) {
			$("#txtMIENGIAM_PT").val(0);
			$("#txtTLMIENGIAM").val(0);
			var _arr_yctu = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.YCTU', _tiepnhanid);
			if (_arr_yctu.length > 0) {
				var _yctu = _arr_yctu[0];
				var _sotien_yc = _yctu.SOTIEN;
				_phieutamungid = _yctu.PHIEUTAMUNGID;
				if (_sotien_yc && _sotien_yc > 0) {
					val_m('txtTONGTIEN', _sotien_yc);
				}
			} else if (_benhnhan.LOAITIEPNHANID == 0) {
				val_m('txtTONGTIEN', VPI_SOTIEN_TAMUNGNTU);
			} else if (VPI_SOTIEN_TAMUNGNGT > 0) {
				val_m('txtTONGTIEN', VPI_SOTIEN_TAMUNGNGT);
			} else {
				val_m('txtTONGTIEN', _vpData.NOPTHEM > 0 ? _vpData.NOPTHEM : 0);
			}
			setEnabled([ 'txtTONGTIEN' ], [ 'btnLYDO', 'txtMIENGIAM_PT', 'txtLYDO', 'txtTLMIENGIAM' ]);
			$("#txtTHUCTHU").val($('#txtTONGTIEN').val());
			_sotien = !parseFloat(get_val_m('txtTHUCTHU')) ? 0 : parseFloat(get_val_m('txtTHUCTHU'));
			objTien.DANOP = parseFloat(_vpData.DANOP);
			objTien.TAMUNG = (parseFloat(_vpData.TAMUNG) + _sotien).toFixed(2);
			objTien.CON_TAMUNG = (parseFloat(_vpData.CON_TAMUNG) + _sotien).toFixed(2);//L2PT-8100
			objTien.HOANUNG = parseFloat(_vpData.HOANUNG);
			objTien.NOPTHEM = (parseFloat(_vpData.NOPTHEM) - _sotien).toFixed(2);
			objTien.MIENGIAM = parseFloat(_vpData.MIENGIAM);
			objTien_dis = vienphi_tinhtien.convertObjToCurrency(objTien);
			FormUtil.setObjectToForm('tongVP', '', objTien_dis);
			// L2PT-36342 start: thêm lần tạm ứng ghi chú phiếu thu
			var ghichu = "Tạm ứng cho viện phí " + _benhnhan.MATIEPNHAN;
			// L2PT-28714 start
			if (VPI_GTMD_GHICHU == 1) {
				ghichu = "Tạm ứng viện phí cho bệnh nhân " + _benhnhan.TENBENHNHAN;
			}
			// L2PT-28714 end
			ghichu += " - lần " + (demPhieuThu(3, -1) + 1);
			$("#txtGHICHU").val(ghichu);
			// L2PT-36342 end
			objData.TONGTIEN = _sotien;
			$("#txtTONGTIEN").focus();
		} else if (_loaiphieuthu == _HOANUNG) {
//			$("#txtMIENGIAM_PT").val(0);
//			$("#txtTLMIENGIAM").val(0);
			setEnabled([ 'btnLYDO', 'txtMIENGIAM_PT', 'txtLYDO', 'txtTLMIENGIAM' ], [ 'txtTONGTIEN' ]);
			var _nopthem = parseFloat(_vpData.NOPTHEM);
			var _hoanung = _nopthem - _miengiam;
			_hoanung = _hoanung.toFixed(2);
			_hoanung = parseFloat(_hoanung);
			_hoanung = _hoanung < 0 ? _hoanung * (-1) : 0;
			if (VPI_QUYTRINH_VIENPHI == 1) {
				var _chenh_lech = parseFloat(_vpData.TAMUNG) - parseFloat(_vpData.HOANUNG);
				_chenh_lech = _chenh_lech > 0 ? _chenh_lech : 0;
				_chenh_lech = _chenh_lech.toFixed(2);
				_hoanung = parseFloat(_chenh_lech);
			}
			val_m('txtTONGTIEN', -1 * _nopthem);
			val_m('txtTHUCTHU', _hoanung);
			_sotien = _hoanung;
			objTien.DANOP = parseFloat(_vpData.DANOP);
			objTien.DATHU = (parseFloat(_vpData.DATHU) + _tien_hoadon).toFixed(2);//L2PT-8100
			objTien.CHUATHU = (parseFloat(_vpData.CHUATHU) - _tien_hoadon).toFixed(2);//L2PT-8100
			objTien.TAMUNG = parseFloat(_vpData.TAMUNG);
			objTien.HOANUNG = (parseFloat(_vpData.HOANUNG) + _sotien).toFixed(2);
			objTien.NOPTHEM = 0;
			objTien.MIENGIAM = (parseFloat(_vpData.MIENGIAM) + _miengiam).toFixed(2);
			objTien_dis = vienphi_tinhtien.convertObjToCurrency(objTien);
			FormUtil.setObjectToForm('tongVP', '', objTien_dis);
			$("#txtGHICHU").val("Hoàn ứng cho viện phí " + _benhnhan.MATIEPNHAN);
			objData.TONGTIEN = get_val_m('txtTONGTIEN') + "";
		} else if (_loaiphieuthu == _HOANDICHVU) {
			$("#txtMIENGIAM_PT").val(0);
			$("#txtTLMIENGIAM").val(0);
			setEnabled([], [ 'txtTONGTIEN', 'txtMIENGIAM_PT' ]);
			_sotien = parseFloat(_tien_hoadon);
			_sotien = _sotien > 0 ? _sotien : 0;
			val_m('txtTONGTIEN', _sotien);
			val_m('txtTHUCTHU', _sotien);
			objTien.DANOP = (parseFloat(_vpData.DANOP) - _sotien).toFixed(2);
			objTien.TAMUNG = parseFloat(_vpData.TAMUNG);
			objTien.HOANUNG = parseFloat(_vpData.HOANUNG);
			objTien.NOPTHEM = (parseFloat(_vpData.NOPTHEM) + _sotien).toFixed(2);
			objTien.MIENGIAM = parseFloat(_vpData.MIENGIAM);
			objTien_dis = vienphi_tinhtien.convertObjToCurrency(objTien);
			FormUtil.setObjectToForm('tongVP', '', objTien_dis);
			$("#txtGHICHU").val("Hoàn tiền dịch vụ không làm cho bệnh nhân " + _benhnhan.MATIEPNHAN);
			objData.TONGTIEN = _sotien + "";
		} else {}
		// L2PT-28406 start
		if (VPI_NHAP_GHICHU == 1 || VPI_NHAP_GHICHU == 2) {
			$("#txtGHICHU").val("");
		}
		// L2PT-28406 end
		// BVTM-7147 start: Chặn với BN BHYT
		if (VPI_LYDO_MIEGIAM == 5 && _benhnhan.DOITUONGBENHNHANID == 1) {
			setEnabled([], [ 'txtMIENGIAM_PT', 'txtTLMIENGIAM' ]);
		}
		// BVTM-7147 end
	}
	// lay du lieu cho grid vien phi
	function loadGridDataBN() {
		if (flagLoading)
			return;
		// L2PT-131223 start
		if (!gioihan_thoigian("VPI_SONGAY_TIMKIEM", "txtTU", "txtDEN")) {
			return false;
		}
		// L2PT-131223 end
		// L2PT-2036 start
		/*
		var loai_vien_phi = $("#cboDOITUONG").val();
		var doituongbn = $("#cboDTBNID").val();
		if (!doituongbn || !loai_vien_phi) {
			return false;
		}
		// L2PT-24613 start
		var sql_par = [];
		var trang_thai = $("#cboTRANGTHAI").val();
		var loai_timkiem = $('input[name=optradio]:checked').val();
		var tu_ngay = $("#txtTU").val();
		var den_ngay = $("#txtDEN").val();
		var ma = $("#txtTIMKIEM").val();
		var xu_tri = $("#cboXUTRI").val();
		var objTimKiem = new Object();
		objTimKiem.DOITUONGBENHNHAN = doituongbn + "";
		objTimKiem.LOAIVIENPHI = loai_vien_phi + "";
		objTimKiem.TRANGTHAI = trang_thai + "";
		objTimKiem.LOAITIMKIEM = loai_timkiem + "";
		objTimKiem.TUNGAY = tu_ngay + "";
		objTimKiem.DENGNAY = den_ngay + "";
		objTimKiem.MA = ma + "";
		objTimKiem.MODE = v_mode ? v_mode : 0 + "";
		objTimKiem.XUTRI = xu_tri + "";
		*/
		var sql_par = [];
		// L2PT-40547
		//var loai_timkiem = $('input[name=optradio]:checked').val();
		var ma = $("#txtTIMKIEM").val();
		var objTimKiem = new Object();
		FormUtil.setFormToObject('divSearch', '', objTimKiem);
		if (!objTimKiem.DOITUONG || !objTimKiem.DTBNID) {
			return false;
		}
		objTimKiem.MODE = v_mode ? v_mode : 0 + "";
		// L2PT-40547
		//objTimKiem.LOAITIMKIEM = loai_timkiem + "";
		objTimKiem.TIMKIEM = ma + "";
		objTimKiem.KHOADN = _khoa_id + ""; // L2PT-12313
		// L2PT-73710 start
		if (fConfig.VPI_DSVP_TKTP == '1') {
			objTimKiem.CHONPHONG = "1";
			objTimKiem.PHONGKT = $("#cboPHONGKT").val().join();
		} else {
			objTimKiem.CHONPHONG = "0";
			objTimKiem.PHONGKT = "-1";
		}
		// L2PT-73710 end
		// L2PT-96778 L2PT-37075 duonghn start
		if (typeof _opts.data == 'object') {
			$("#txtTIMKIEM").val(_opts.data.mahosobenhan);
			objTimKiem.TIMKIEM = _opts.data.mahosobenhan;
			objTimKiem.TIEPNHANID = _opts.data.tiepnhanid;
		}
		// L2PT-96778 L2PT-37075 duonghn end
		sql_par.push({
			"name" : "[0]",
			"value" : JSON.stringify(objTimKiem)
		});
		// L2PT-24613 end
		// L2PT-2036 end
		//console.log(ctr);
		//sql_par=RSUtil.setSysParam(sql_par, _param);
		GridUtil.loadGridBySqlPage(_gridId_BN, _gridSQL_BN, sql_par, function() {
			//build menu DS Viện phí
			$(".jqgrow", '#' + _gridId_BN).contextMenu(
					'contextMenu_BN', // L2PT-16663 contextMenu-> contextMenu_BN
					{
						onContextMenu : function(event, menu) {
							var rowId = $(event.target).parent("tr").attr("id");
							var grid = $('#' + _gridId_BN);
							grid.setSelection(rowId);
							var selRowId = grid.jqGrid("getGridParam", "selrow");
							console.log('SELROW_BN: ' + selRowId);
							return true;
						},
						bindings : {
							// xu ly su kien chon giu thue
							'rGiuThe' : function(t) {
								if (_benhnhan.DOITUONGBENHNHANID != 1) {
									DlgUtil.showMsg("Bệnh nhân không phải đối tượng bảo hiểm");
									return;
								}
								var _mode = _benhnhan.DAGIUTHEBHYT == 1 ? 0 : 1;
								var _result = jsonrpc.AjaxJson.ajaxCALL_SP_I('NGT01K001.GIUTHEBHYT', _tiepnhanid + "$" + _mode);
								if (_result == -1) {
									DlgUtil.showMsg("Cập nhật giữ thẻ không thành công");
								} else {
									_benhnhan.DAGIUTHEBHYT = _mode;
									if (_benhnhan.DAGIUTHEBHYT == 1) {
										$("#rGiuTheS").text("Bỏ giữ thẻ");
										$("#chkDaThuTien").prop('checked', true);
									} else {
										$("#chkDaThuTien").prop('checked', false);
										$("#rGiuTheS").text("Giữ thẻ");
									}
								}
							},
							// xu ly su kien chon xem Lịch sử TT
							'rLichSuThanhToan' : function(t) {
								var myVar = new Object();
								myVar.tiepnhanid = _tiepnhanid;
								DlgUtil.buildPopupUrl("dlgLichSuTT", "dlgLichSuThanhToan", "manager.jsp?func=../vienphi/VPI01T004_lichsuthanhtoan", myVar, "Lịch sử thanh toán", 1250, 600);
								DlgUtil.open("dlgLichSuTT");
							},
							//Begin_HaNv_11052020: Cho phép chọn nhiều dịch vụ để nhập miễn giảm - L2PT-20511
							'rMGNhieuDichVu' : function(t) {
								if (flagLoading)
									return;
								var myVar = new Object();
								myVar.tiepnhanid = _tiepnhanid;
								DlgUtil.buildPopupUrl("dlgMGNhieuDv", "dlgMGNhieuDv", "manager.jsp?func=../vienphi/VPI01T021_MGNhieuDichVu", myVar, "Nhập miễn giảm dịch vụ", 1250, 500);
								DlgUtil.open("dlgMGNhieuDv");
							},
							//End_HaNv_11052020
							//L2PT-7488 start
							//xu ly su kien chon miễn giảm theo nhóm dịch vụ
							'rMGNhomDichVu' : function(t) {
								// L2PT-32078 L2PT-10249 start
								nhapMienGiamNhomDV();
							},
							//xu ly su kien chon miễn giảm chênh lệch
							'rMGChenhLech' : function(t) {
								var ds_mg = "0";
								var _mode = 1;
								var _result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.MG.NDV.CL', _tiepnhanid + "$" + ds_mg + "$" + _mode);
								DlgUtil.showMsg(_result);
							},
							//L2PT-7488 end
							//xu ly su kien chon xem Lịch sử điều trị
							'rLichSuDieuTri' : function(e) {
								EventUtil.setEvent("assignSevice_cancel", function(e) {
//	             					console.log(e.msg);
									DlgUtil.close("dlgLSDT");
								});
								var myVar = new Object();
								myVar.benhnhanId = _benhnhan.BENHNHANID;
								DlgUtil.buildPopupUrl("dlgLSDT", "dlgLichSuDieuTri", "manager.jsp?func=../ngoaitru/NGT02K025_LichSuDieuTri", myVar, "Lịch sử điều trị", 1320, 610);
								DlgUtil.open("dlgLSDT");
							},
							//xu ly su kien chon xem Lịch sử TT
							//xu ly su kiem nhap thong tin thanh toan benh nhan NHIHDG
							'rNhapThongTinTT_NHIHDG' : function(t) {
								if (flagLoading)
									return;
								// L2PT-28833 start sửa fn lấy ttbn
								//var selRowId = $("#" + _gridId_BN).jqGrid("getGridParam", "selrow");
								//var i_benhnhanid = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'BENHNHANID');
								var _par_benhnhanid = [ _benhnhan.BENHNHANID ];
								var data_bn_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.TTBN.HDDT", _par_benhnhanid.join('$'));
								//$("#txtMAPHIEUTHUVIEW").val(_mapthu);
								// lay ma benh nhan -- viet ham lay thong tin bn -- chuyen du lieu bn vao popup -- xu ly xu kien popup
								$("#txtMABENHNHAN_HDDT").val(data_bn_ar[0].MABENHNHAN);
								// L2PT-28833 end sửa fn lấy ttbn
								$("#txtTENCONGTYBN_HDDT").val(data_bn_ar[0].TENCONGTYBN);
								$("#txtDIACHI_CTYBN_HDDT").val(data_bn_ar[0].DIACHI_CTYBN);
								$("#txtMASOTHUE_CTYBN_HDDT").val(data_bn_ar[0].MASOTHUE_CTYBN);
								$("#txtEMAIL_CTYBN_HDDT").val(data_bn_ar[0].EMAIL_CTYBN);
								$("#txtSOTK_CTYBN_HDDT").val(data_bn_ar[0].SOTAIKHOAN);
								$("#txtTENNH_CTYBN_HDDT").val(data_bn_ar[0].TEN_NGANHANG);
								DlgUtil.open("dlgNhapThongTinTT_NHIHDG");
							},
							'rThemMaSoThue' : function(e) {
								DlgUtil.open("dlgMST");
							},
							//xu ly su kiem nhap thong tin thanh toan benh nhan NHIHDG
							'rNhapThongTinTT_NHIHDG' : function(t) {
								if (flagLoading)
									return;
								// L2PT-28833 start sửa fn lấy ttbn
								//var selRowId = $("#" + _gridId_BN).jqGrid("getGridParam", "selrow");
								//var i_benhnhanid = $("#" + _gridId_BN).jqGrid('getCell', selRowId, 'BENHNHANID');
								var _par_benhnhanid = [ _benhnhan.BENHNHANID ];
								var data_bn_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.TTBN.HDDT", _par_benhnhanid.join('$'));
								//$("#txtMAPHIEUTHUVIEW").val(_mapthu);
								// lay ma benh nhan -- viet ham lay thong tin bn -- chuyen du lieu bn vao popup -- xu ly xu kien popup
								$("#txtMABENHNHAN_HDDT").val(data_bn_ar[0].MABENHNHAN);
								// L2PT-28833 end sửa fn lấy ttbn
								$("#txtTENCONGTYBN_HDDT").val(data_bn_ar[0].TENCONGTYBN);
								$("#txtDIACHI_CTYBN_HDDT").val(data_bn_ar[0].DIACHI_CTYBN);
								$("#txtMASOTHUE_CTYBN_HDDT").val(data_bn_ar[0].MASOTHUE_CTYBN);
								$("#txtEMAIL_CTYBN_HDDT").val(data_bn_ar[0].EMAIL_CTYBN);
								$("#txtSOTK_CTYBN_HDDT").val(data_bn_ar[0].SOTAIKHOAN);
								$("#txtTENNH_CTYBN_HDDT").val(data_bn_ar[0].TEN_NGANHANG);
								DlgUtil.open("dlgNhapThongTinTT_NHIHDG");
							},
							'rTaoHD' : function(t) {
								if (flagLoading)
									return;
								dlgPopup = DlgUtil.buildPopupGrid("dlgTaoHD", _gridId_PT2, "Tạo hóa đơn", 810, 510);
								GridUtil.init(_gridId_PT2, "800", "350", "", true, _gridHeader_PT);
								var _sql_par = [ {
									name : "[0]",
									value : _tiepnhanid
								} ];
								GridUtil.loadGridBySqlPage(_gridId_PT2, "VPI01T001.DS.THD", _sql_par, function() {
									var _total = $("#" + _gridId_PT2).jqGrid('getGridParam', 'records');
									if (_total > 0) {
										dlgPopup.open();
									} else {
										DlgUtil.showMsg("Bệnh nhân chưa có phiếu thu nào");
									}
								});
								GridUtil.setGridParam(_gridId_PT2, {
									onSelectRow : function(id) {
										var grid = $("#" + _gridId_PT2);
										var arr_row = grid.jqGrid('getGridParam', 'selarrrow');
										GridUtil.unmarkAll(_gridId_PT2);
										for (var i = 0; i < arr_row.length; i++) {
											GridUtil.markRow(_gridId_PT2, arr_row[i]);
										}
									},
									gridComplete : function(id) {}
								});
								var btnOK = $('<input class="btn btn-sm btn-primary" id="btn_THD_OK" type="button" style="position:absolute;bottom:10px;left:10px" value="Đăng ký" />');
								var btnClose = $('<input class="btn btn-sm btn-primary" id="btn_THD_Close" type="button" style="position:absolute;bottom:10px;right:10px" value="Đóng" />');
								$('#dlgTaoHD').append(btnOK);
								$('#dlgTaoHD').append(btnClose);
								btnOK.click(function() {
									var _dsphieuthuid = "";
									var grid = $("#" + _gridId_PT2);
									var ids = grid.jqGrid("getGridParam", "selarrrow");
									if (ids.length == 0) {
										DlgUtil.showMsg("Không có phiếu thu nào được chọn");
										return;
									}
									for (var i = 0; i < ids.length; i++) {
										var id = ids[i];
										var _ptid = grid.jqGrid('getCell', id, 'PHIEUTHUID');
										_dsphieuthuid += _ptid + ",";
									}
									_dsphieuthuid = _dsphieuthuid.slice(0, -1);
									var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T001.TAODV', _dsphieuthuid + "$" + _khoa_id + "$" + _phong_id);
									if (ret > 0) {
										DlgUtil.showMsg("Tạo hóa đơn thành công");
										_phieuthuid = ret;
										loadGridDataPT(_tiepnhanid);
									} else if (ret == -2) {
										DlgUtil.showMsg("Hết số hóa đơn");
									} else
										DlgUtil.showMsg("Cập nhật không thành công");
								});
								btnClose.click(function() {
									dlgPopup.close();
								});
								GridUtil.setGridParam(_gridId_DV2, {
									gridComplete : function(id) {}
								});
							},
							//xu ly su kien chon Xóa
							'rXoa' : function(e) {
//	                 		 	DlgUtil.showMsg("");
							},
							// L2PT-11665 start: đồng bộ với form 2
							//xu ly su kien chon Duyệt CLS
							'rDuyetCLS' : function(e) {
								duyetthuchienCLS();
							},
							// L2PT-11665 end
							'r_HenKham' : function() {
								var myVar = {};
								dlgPopup = DlgUtil.buildPopupUrl("dlgCDDV", "divDsDoan", "manager.jsp?func=../ngoaitru/NGT02K061_QLHenKham", myVar, "Lựa chọn bệnh nhân hẹn khám",
										window.innerWidth * 0.95, window.innerHeight * 0.86);
								var parent = DlgUtil.open("dlgCDDV");
							}
						}
					});
			var ids = $("#" + _gridId_BN).getDataIDs();
			if (ids.length == 0) {
				$("#" + _gridId_DV).jqGrid("clearGridData");
				$("#" + _gridId_PT).jqGrid("clearGridData");
				FormUtil.clearForm('tongVP', "");
				FormUtil.clearForm('ttVienphi', "");
				FormUtil.clearForm('ttThuTien', "");
				setEnabled([], [ 'btnThem' ]);
				_benhnhan = null;
				_tiepnhanid = -1;
			} else {
				// select first row
				$("#" + _gridId_BN).setSelection(1);
				// set icon
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var row = $("#" + _gridId_BN).jqGrid('getRowData', id);
					var _icon = '';
					if (!row.TRANGTHAITIEPNHAN || row.TRANGTHAITIEPNHAN == 0) {} else if (row.TRANGTHAITIEPNHAN == 1 || row.TRANGTHAITIEPNHAN == 2) {
						_icon = '<center><img src="' + _opts.imgPath[3] + '" width="15px"></center>';
						if (row.TRANGTHAITIEPNHAN_VP == 1 || row.TRANGTHAITIEPNHAN_BH == 1) {
							_icon = '<center><img src="' + _opts.imgPath[4] + '" width="15px"></center>';
						}
						$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_KT', _icon); // L2PT-68665
					}
					if (row.TRANGTHAITIEPNHAN == 2) {
						_icon2 = '<center><img src="' + _opts.imgPath[2] + '" width="15px"></center>';
						$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_BH', _icon2); // L2PT-68665
					}
					// L2PT-68665 start
					if (parseInt(row.KYSO_BANGKE) > 0) {
						$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_KS', getIconPathByName('ca'));
					}
					// L2PT-68665 end
					// L2PT-118724 start
					if (row.YC_CHIHO == '-1') {
						$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_CH', getIconPathByName('process'));
					} else if (row.YC_CHIHO == '1') {
						$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_CH', getIconPathByName('Dollar', '.png', "../common/img/"));
					} else if (row.YC_CHIHO == '0') {
						$("#" + _gridId_BN).jqGrid('setCell', id, 'ICON_CH', getIconPathByName('Cancel'));
					}
					// L2PT-118724 end
					// danh dau ho so da xac nhan bao hiem hay chua
					if (row.XACNHANBH == 1) {
						$("#" + _gridId_BN).jqGrid('setRowData', id, "", {
							color : 'blue'
						});
					}
					// L2PT-27740 start: bôi màu cho BN viện phí
					if (VPI_MAU_DSBN_THUVP == '1' && row.DOITUONGBENHNHANID == 2) {
						$("#" + _gridId_BN).jqGrid('setRowData', id, "", {
							color : '#0797f7'
						});
					}
					// L2PT-27740 end
				}
			}
			// nếu có mã click nút thêm
			var ma = $("#txtTIMKIEM").val();
			if (ma != "") {
				var _total = $("#" + _gridId_BN).jqGrid('getGridParam', 'records');
				if (_total && _total > 0 && _benhnhan && _benhnhan.TRANGTHAITIEPNHAN_VP != 1) {
					var arr_dagiaodich = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_dagiaodich, _tiepnhanid);
					if (arr_dagiaodich && arr_dagiaodich.length > 0) {
						var _vp_giaodich = arr_dagiaodich[0];
						if (_vp_giaodich.CHENHLECH != 0) {
							$('#btnThem').click();
						}
					}
				}
			}
		});
	}
	// lay du lieu cho grid dich vu
	function loadGridDataDV(_tiepnhanid, _loaiphieuthu, _luu) {
//		if (flagLoading)
//			return;
		_khoa = "";
		var sql_par = [];
		var _dvData = [];
		sql_par.push({
			"name" : "[0]",
			"value" : _tiepnhanid
		});
		sql_par.push({
			"name" : "[1]",
			"value" : "0"
		});
//		sql_par=RSUtil.setSysParam(sql_par, _param);
		_fl_tinh = true;
		_ds_nhomtt = [];
		GridUtil.loadGridBySqlPage(_gridId_DV, "VPI01T001.21", sql_par, function() {
			_chot = false;
			_dvData = $("#" + _gridId_DV).jqGrid('getRowData');
			if (_fl_tinh) {
				tinhTongTien(_dvData);
			}
			if (!flagLoading && _benhnhan) {
				$("#" + _gridId_DV).jqGrid('setColProp', 'SOLUONG', {
					editable : true
				});
				$("#" + _gridId_DV).jqGrid('setColProp', 'TYLE_MIENGIAM', {
					editable : true
				});
				if (_benhnhan.TRANGTHAITIEPNHAN_VP != 1 && _benhnhan.TRANGTHAITIEPNHAN != 2) {
					$(".jqgrow", '#' + _gridId_DV).contextMenu(
							'contextMenu_DV',
							{
								// xu ly su kiem xoa dich vu thu khac
								bindings : {
									'rXoaDichVu' : function(t) {
										var grid = $("#" + _gridId_DV);
										var selRowId = grid.jqGrid("getGridParam", "selrow");
										//console.log('SELROW: '+selRowId);
										var _dichvukhambenhid = grid.jqGrid('getCell', selRowId, 'DICHVUKHAMBENHID');
										var _tendichvu = grid.jqGrid('getCell', selRowId, 'TENDICHVU');
										DlgUtil.showConfirm("Bạn có chắc chắn muốn xóa dịch vụ '" + _tendichvu + "' ?", function(flag) {
											if (flag) {
												var _result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.XOADICHVU', _dichvukhambenhid);
												DlgUtil.showMsg(_result);
												loadGridDataDV(_tiepnhanid);
											}
										});
									},
									'rHuyDichVu' : function(t) {
										var grid = $("#" + _gridId_DV);
										var selRowId = grid.jqGrid("getGridParam", "selrow");
										//console.log('SELROW: '+selRowId);
										var _dichvukhambenhid = grid.jqGrid('getCell', selRowId, 'DICHVUKHAMBENHID');
										var _tendichvu = grid.jqGrid('getCell', selRowId, 'TENDICHVU');
										DlgUtil.showConfirm("Bạn có chắc chắn muốn hủy dịch vụ '" + _tendichvu + "' ?", function(flag) {
											if (flag) {
												var _result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.HUYDICHVU', _dichvukhambenhid);
												DlgUtil.showMsg(_result);
												loadGridDataDV(_tiepnhanid);
											}
										});
									},
									'rThayThe' : function(t) {
										var selRowId_pt = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
										var _maphieu = $("#" + _gridId_PT).jqGrid('getCell', selRowId_pt, 'MAPHIEUTHU');
										var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId_pt, 'LOAIPHIEUTHUID');
										if (!selRowId_pt || _loaiphieuthuid != 1) {
											DlgUtil.showMsg("Chưa chọn hóa đơn");
											return false;
										}
										var grid = $("#" + _gridId_DV);
										var selRowId = grid.jqGrid("getGridParam", "selrow");
										//console.log('SELROW: '+selRowId);
										var _dichvukhambenhid = grid.jqGrid('getCell', selRowId, 'DICHVUKHAMBENHID');
										var _tendichvu = grid.jqGrid('getCell', selRowId, 'TENDICHVU');
										dlgPopup = DlgUtil.buildPopupGrid("dlgDVSai", _gridId_DV4, "Danh sách dịch vụ cùng giá tiền", 1000, 520);
										GridUtil.init(_gridId_DV4, "990", "350", "", true, _gridHeader_DV);
										var btnClose = $('<div class="col-xs-12 low-padding mgt10" style="text-align: center;">'
												+ '<input class="btn btn-sm btn-primary" id="btn_DVS_CLOSE" type="button" value="Đóng" /></div>');
										$('#dlgDVSai').append(btnClose);
										btnClose.click(function() {
											dlgPopup.close();
										});
										GridUtil.setGridParam(_gridId_DV4, {
											gridComplete : function() {},
											onSelectRow : function(id) {
												GridUtil.unmarkAll(_gridId_DV4);
												GridUtil.markRow(_gridId_DV4, id);
												var _dvkbid_tt = $("#" + _gridId_DV4).jqGrid('getCell', id, 'DICHVUKHAMBENHID');
												var _tdv_tt = $("#" + _gridId_DV4).jqGrid('getCell', id, 'TENDICHVU');
												DlgUtil.showConfirm("Bạn có chắc chắn muốn thay thế dịch vụ '" + _tendichvu + "' bằng dịch vụ '" + _tdv_tt + "' trên phiếu '" + _maphieu + "' ?",
														function(flag) {
															if (flag) {
																var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.THAYTHE.DV', _dichvukhambenhid + "$" + _dvkbid_tt + "$1");
																DlgUtil.showMsg(result);
																var sql_par = [];
																sql_par.push({
																	"name" : "[0]",
																	"value" : _phieuthuid
																});
																_fl_tinh = false;
																GridUtil.loadGridBySqlPage(_gridId_DV, "VPI01T001.22", sql_par);
																dlgPopup.close();
															} else {
																$("#" + _gridId_DV4).jqGrid('resetSelection');
																GridUtil.unmarkAll(_gridId_DV4);
															}
														});
											}
										});
										var sql_par = [];
										sql_par.push({
											"sql_par" : "[0]",
											"value" : _dichvukhambenhid
										});
										dlgPopup.open();
										GridUtil.loadGridBySqlPage(_gridId_DV4, "VPI01T001.DSDV.TT", sql_par);
									},
									//Begin_HaNv_22112019: Cập nhập chức năng miễn giảm % trên dịch vụ - L2PT-11967
									'r_MIENGIAM_DV' : function(t) {
										var dlgMG = DlgUtil.buildPopup("dlgMIENGIAMDV", "dlgMIENGIAMDV", "Nhập miễn giảm dịch vụ", 500, 150);
										var grid = $("#" + _gridId_DV);
										var _selRowIds = $("#" + _gridId_DV).jqGrid('getGridParam', 'selarrrow');
										if (_selRowIds.length == 0) {
											DlgUtil.showMsg("Chưa chọn dịch vụ");
											return false;
										}
										if (_selRowIds.length > 1) {
											DlgUtil.showMsg("Không thể nhập miễn giảm cho nhiều dịch vụ trong 1 lần");
											return false;
										}
										var rowid = _selRowIds[0];
										var _tienMG = parseFloat(grid.jqGrid('getCell', rowid, 'TIEN_MIENGIAM'));
										var _thucThu = parseFloat(grid.jqGrid('getCell', rowid, 'THANHTIEN')) - parseFloat(grid.jqGrid('getCell', rowid, 'TIEN_BHYT_TRA'));
										if (_thucThu == 0) {
											DlgUtil.showMsg("Dịch vụ có tiền bệnh nhân trả = 0, không được chọn miễn giảm!");
											return false;
										}
										var _phanTram = parseFloat(_tienMG / _thucThu * 100);
										$('#txtTIEN_MG').val(_tienMG.toFixed(2));
										$('#txtPHANTRAM_MG').val(_phanTram.toFixed(2));
										DlgUtil.open("dlgMIENGIAMDV");
									}
								//End_HaNv_22112019
								},
								onContextMenu : function(event, menu) {
									var rowId = $(event.target).parent("tr").attr("id");
									var grid = $("#" + _gridId_DV);
									grid.setSelection(rowId);
									return true;
								}
							});
				}
				var grid = $("#" + _gridId_DV);
				var ids = grid.getDataIDs();
				_dv_sai = [];
				_check_congkham = false;
				var _dv_ck = []; //L2PT-27474
				var _tyledv_ck100 = false; //L2PT-27474
				var VPI_CHECK_CK100 = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_CHECK_CK100'); //L2PT-27474
				_codv_khongvat = false; // L2PT-34420 start
				var _codv_yeucau = false;//HaNv_030323: L2PT-36215
				for (var i = 0; i < ids.length; i++) {
					var id = ids[i];
					var _dathutien = grid.jqGrid('getCell', id, 'DATHUTIEN');
					var _soluong = grid.jqGrid('getCell', id, 'SOLUONG');
					var _tyle_bhyt_tra = grid.jqGrid('getCell', id, 'TYLE_BHYT_TRA');
					var _tyle_the = grid.jqGrid('getCell', id, 'TYLE');
					var _loaidoituong = grid.jqGrid('getCell', id, 'LOAIDOITUONG');
					var _tyle_dv = grid.jqGrid('getCell', id, 'TYLE_DV');
					var _loainhommbp = grid.jqGrid('getCell', id, 'LOAINHOMMAUBENHPHAM');
					var _tien_dv = grid.jqGrid('getCell', id, 'TIENDICHVU');
					var _vattu04 = grid.jqGrid('getCell', id, 'VATTU04');
					var _yc_hoan = grid.jqGrid('getCell', id, 'YC_HOAN');
					//Beg_HaNv_030323: cảnh báo BN sử dụng dịch vụ yêu cầu - L2PT-36215
					if (fConfig.VPI_CBDV_YEUCAU == 1 && !_codv_yeucau && _loaidoituong == 6) {
						_codv_yeucau = true;
					}
					//End_HaNv_030323
					// L2PT-34420 start: check có dịch vụ không vat cần thu tiền hay không
					var _thucthu = grid.jqGrid('getCell', id, 'THUCTHU');
					var _tyle_vat = grid.jqGrid('getCell', id, 'TYLE_VAT');
					if (_tyle_vat == 0 && _thucthu != 0 && _dathutien != 3) {
						_codv_khongvat = true;
					}
					// L2PT-34420 end
					// kiem tra da thu tien
					if (_dathutien == 3) {
						// GridUtil.markRow(_gridId_DV,id);
						$("#" + _gridId_DV).jqGrid('setRowData', id, "", {
							color : 'blue'
						});
					}
					// kiem tra ty le
					if ((_loaidoituong == 1 || _loaidoituong == 2 || _loaidoituong == 3) && _loainhommbp != 16 && _tyle_the != _tyle_bhyt_tra && _tyle_dv != 0 && _soluong != 0 &&
							(_loainhommbp != 3 || _tien_dv != 0) && _vattu04 == 0) {
						$("#" + _gridId_DV).jqGrid('setRowData', id, "", {
							color : 'red'
						});
						var _khoadv = grid.jqGrid('getCell', id, 'KHOA');
						if (!_khoa.includes(_khoadv))
							_khoa += _khoadv + ", ";
						var _row = grid.jqGrid('getRowData', id);
						_dv_sai.push(_row);
					}
					// L2PT-27474 kiểm tra tyle_dv công khám 
					if (VPI_CHECK_CK100 == '1' && _loainhommbp == 3) {
						if (_tyle_dv == 1) {
							_tyledv_ck100 = true;
							_dv_ck = [];
						} else if (!_tyledv_ck100 && _tyle_dv != 1) {
							var _row = grid.jqGrid('getRowData', id);
							_row.ID = id;
							_dv_ck.push(_row);
						}
					}
					// L2PT-27474 kiểm tra tyle_dv công khám
					// kiem tra dich vu hoan
					if (VPI_HOANDV != 0 && _yc_hoan == 1) {
						$("#" + _gridId_DV).jqGrid('setRowData', id, "", {
							color : 'green'
						});
						$("#" + _gridId_DV).setCell(id, 'RN', '', {
							'font-weight' : 'bold'
						});
						$("#" + _gridId_DV).setCell(id, 'TENDICHVU', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'SOLUONG', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'TIENDICHVU', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'DONGIA_BHYT', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'TYLE_DV_100', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'THANHTIEN', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'TIEN_BHYT_TRA', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'THUCTHU', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'TYLE_BHYT_TRA2', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'TIEN_MIENGIAM', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
						$("#" + _gridId_DV).setCell(id, 'TYLE_MIENGIAM', '', {
							'font-weight' : 'bold',
							'font-style' : 'italic',
							'text-decoration' : 'line-through'
						});
					}
					// L2PT-28712 start 
					var _daxoa = grid.jqGrid('getCell', id, 'DAXOA');
					if (_daxoa == 1) {
						$("#" + _gridId_DV).jqGrid('setRowData', id, "", {
							color : 'violet'
						});
					}
					// L2PT-28712 end
					// kiem tra loai dich vu
					if (_loainhommbp != 3) {
						_check_congkham = true;
					}
				}
				//Beg_HaNv_030323: cảnh báo BN sử dụng dịch vụ yêu cầu - L2PT-36215
				if (_codv_yeucau) {
					$('#divCBDVYC').show();
				} else {
					$('#divCBDVYC').hide();
				}
				//End_HaNv_030323
				// L2PT-27474 kiểm tra tyle_dv công khám 
				for (var ick = 0; ick < _dv_ck.length; ick++) {
					var idck = _dv_ck[ick].ID;
					$("#" + _gridId_DV).jqGrid('setRowData', idck, "", {
						color : 'red'
					});
					var _khoadv = grid.jqGrid('getCell', idck, 'KHOA');
					if (!_khoa.includes(_khoadv))
						_khoa += _khoadv + ", ";
					_dv_sai.push(_dv_ck[ick]);
				}
				// L2PT-27474 kiểm tra tyle_dv công khám 
				if (_benhnhan.TRANGTHAITIEPNHAN_VP == 1)
					setEnabled([], [ 'btnThem' ]);
				else
					setEnabled([ 'btnThem' ], []);
				if (VPI_ENABLE_THUKHAC == 1 && ids.length == 0) {
					setEnabled([ 'btnThuKhac' ], []);
				}
			} else {
				$("#" + _gridId_DV).jqGrid('setColProp', 'SOLUONG', {
					editable : false
				});
				$("#" + _gridId_DV).jqGrid('setColProp', 'TYLE_MIENGIAM', {
					editable : false
				});
				//FormUtil.clearForm('ttThuTien', "");
				checkBN();
				$("#" + _gridId_DV).showCol('cb');
				$("#cboHINHTHUCTHANHTOAN").val(1);
				onCboHtttChange(); // BVTM-5934
				$("#cboLOAIPHIEUTHUID").val(1);
				//$("#txtMIENGIAM_PT").val(0);
				//$("#txtTLMIENGIAM").val(0);
				// check thu khac start
				_thu_khac = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.CHECK.THUKHAC', _tiepnhanid);
				//if(_thu_khac == -1) {}
				// check thu khac end
				if (_loaiphieuthu) {}
				// L2PT-22759 start
				else if (VPI_VP_PNT == 1) {
					_loaiphieuthu = _THUTHEM;
				}
				// L2PT-22759 end
				// L2PT-13637 start			
				else if (VPI_LOAIPHIEU_MACDICH == '2' && _benhnhan.TRANGTHAITIEPNHAN == 0 && _benhnhan.DOITUONGBENHNHANID == 1) {
					_loaiphieuthu = _TAMUNG;
				}
				// L2PT-13637 end
				// L2PT-82006 start
				else if (VPI_QUYTRINH_VIENPHI == 4 && _benhnhan.TRANGTHAITIEPNHAN == 0 && _benhnhan.LOAITIEPNHANID == 1) {
					if (_benhnhan.DOITUONGBENHNHANID == 1) {
						if (_benhnhan.LYDO_VAOVIEN == 3) {
							_loaiphieuthu = _TAMUNG;
						} else {
							_loaiphieuthu = _THUTHEM;
						}
					} else {
						_loaiphieuthu = _THUTHEM;
					}
				}
				// L2PT-82006 end
				// L2PT-21633: BN khám DV
				else if (VPI_QUYTRINH_VIENPHI == 2 && _benhnhan.TRANGTHAITIEPNHAN == 0 && _benhnhan.BENHNHAN_KDV == 0) {
					_loaiphieuthu = _TAMUNG;
				}
				// L2PT-78589: không mặc định vào tạm ứng
				else if (fConfig.VPI_THUVP_THEOKHOA != '1' && _benhnhan.TRANGTHAITIEPNHAN == 0 && (_vpData.NOPTHEM == 0 || _benhnhan.LOAITIEPNHANID == 0)) {
					_loaiphieuthu = _TAMUNG;
				} else if (VPI_QUYTRINH_VIENPHI == 1 && _benhnhan.TRANGTHAITIEPNHAN != 0 && parseFloat(_vpData.TAMUNG) - parseFloat(_vpData.HOANUNG) > 0) { // L2PT-34871
					_loaiphieuthu = _HOANUNG;
				}
				// L2PT-113638 L2PT-10884 start: nếu BN ra viện có chi phí cần nộp = 0 thì mặc định ra phiếu hoàn ứng
				else if (fConfig.VPI_PHIEU_KD == '1' && _benhnhan.TRANGTHAITIEPNHAN != 0 && _vpData.NOPTHEM == 0) {
					_loaiphieuthu = _HOANUNG;
				}
				// L2PT-113638 L2PT-10884
				else if (_thu_khac == 1 || _vpData.NOPTHEM >= 0) {// check thu khac
					_loaiphieuthu = _THUTHEM;
				} else {
					_loaiphieuthu = _HOANUNG;
				}
				$("#cboLOAIPHIEUTHUID").val(_loaiphieuthu);
				var grid = $("#" + _gridId_DV);
				var ids = grid.getDataIDs();
				grid.jqGrid('resetSelection');
				// L2PT-128731 start
				if (fConfig.VPI_LOAD_DV == '1' && _loaiphieuthu == _TAMUNG) {
					grid.jqGrid('clearGridData');
				} else {
					var _nhomtt_pt = 0;
					var _nhom_tyle_vat = -1; // L2PT-34420
					// L2PT-36512 start : kiểm tra nhóm thanh toán của dịch vụ cần thu tiền nếu chưa có trong mảng thì đẩy vào
					if (VP_TACH_HOADON == 8) {
						var arrLoaiDV = [];
						for (var i = 0; i < ids.length; i++) {
							var _thucthu = grid.jqGrid('getCell', ids[i], 'THUCTHU');
							var _dathutien = grid.jqGrid('getCell', ids[i], 'DATHUTIEN');
							var _nhomthanhtoan = grid.jqGrid('getCell', ids[i], 'NHOMTHANHTOAN');
							if (_thucthu != 0 && _dathutien != 3) {
								if (arrLoaiDV.indexOf(_nhomthanhtoan) == -1) {
									arrLoaiDV.push(_nhomthanhtoan);
								}
							}
						}
						if (arrLoaiDV.indexOf("17") != -1) {
							_nhomtt_pt = "17";
						} else if (arrLoaiDV.indexOf("18") != -1) {
							_nhomtt_pt = "18";
						} else {
							_nhomtt_pt = "16";
						}
					}
					// L2PT-36512 end
					_arr_DV_CT = [];
					// L2PT-128731 start
					if (fConfig.VPI_LOAD_DV == '1') {
						grid.hide();
					}
					// L2PT-128731 end
					for (var i = 0, il = ids.length; i < il; i++) {
						var _dathutien = grid.jqGrid('getCell', ids[i], 'DATHUTIEN');
						//	    	var _loaidoituong = grid.jqGrid ('getCell', ids[i],'LOAI_DOITUONG');
						var _nhomthanhtoan = grid.jqGrid('getCell', ids[i], 'NHOMTHANHTOAN');
						var _doituong_dv = grid.jqGrid('getCell', ids[i], 'DOITUONGDV');
						var _ptid_tt = grid.jqGrid('getCell', ids[i], 'PHIEUTHUID');
						//if(_ds_nhomtt.indexOf(_nhomthanhtoan) ==  -1){
						//_ds_nhomtt.push(_nhomthanhtoan);
						//}			    	
						var _doituongdv = $("#cboDOITUONGDV").val();
						var _thucthu = grid.jqGrid('getCell', ids[i], 'THUCTHU');
						if (VPI_QUYTRINH_VIENPHI == 1 && _loaiphieuthu == 2) {
							grid.jqGrid('delRowData', ids[i]);
							// L2PT-10164 thu tiền chưa đóng bệnh án với trường hợp cấu hình VPI_QUYTRINH_VIENPHI = 2
							// L2PT-21633 BN khám DV
						} else if ((VPI_QUYTRINH_VIENPHI == 2 && VPI_THUTIEN_CHUADBA != '1' && _benhnhan.BENHNHAN_KDV == 0) &&
								!(VPI_THUTIEN_NTU == 1 || (VPI_THUTIEN_NTU == 2 && _benhnhan.NHAPNOITRU == 1)) && _benhnhan.TRANGTHAITIEPNHAN == 0 && _nhomthanhtoan != 7) {
							grid.jqGrid('delRowData', ids[i]);
						} else if (_loaiphieuthu == 3) {
							grid.jqGrid('delRowData', ids[i]);
						} else if (_loaiphieuthu == 8) {
							if (_dathutien != 3 || _thucthu == 0)
								grid.jqGrid('delRowData', ids[i]);
						} else if (_dathutien == 3 || (_thucthu == 0 && VPI_HD_KHONGDONG == 0)) {
							grid.jqGrid('delRowData', ids[i]);
						} else if ((_doituongdv != 4 && _doituongdv != 0 && _doituongdv != _doituong_dv) || (_doituongdv == 4 && _doituong_dv != 1 && _doituong_dv != 2)) {
							_arr_DV_CT.push(ids[i]);
							grid.jqGrid('delRowData', ids[i]);
						} else {
							_arr_DV_CT.push(ids[i]);
							var arr_row = grid.jqGrid("getGridParam", "selarrrow");
							// check thu khac 
							if (_thu_khac == 1) {
								//Begin_HaNv_23042020: Tách tiêng hóa đơn thu khác theo cấu hình - L2PT-20200
								if (_nhomthanhtoan == 5 || _nhomthanhtoan == 7 || _nhomthanhtoan == 11 || _nhomthanhtoan == 13) {//L2PT-940
									_nhomtt_pt = _nhomthanhtoan;
									grid.jqGrid('setSelection', ids[i], false);
									GridUtil.markRow(_gridId_DV, ids[i]);
								}
								// L2PT-940 start
								else {
									grid.jqGrid('delRowData', ids[i]);
								}
								// L2PT-940 end
								//End_HaNv_23042020
							} else if (_doituongdv == _DT_VIENPHI && _benhnhan.LOAITIEPNHANID == 1 && arr_row.length >= _SL_VIENPHI) {
								continue;
							} else if (_doituongdv == _DT_DICHVU && arr_row.length >= _SL_DICHVU) {
								continue;
							} else if (_benhnhan.TRANGTHAITIEPNHAN != 0 && VP_TACH_HOADON != 8) { // L2PT-34420
								grid.jqGrid('setSelection', ids[i], false);
								GridUtil.markRow(_gridId_DV, ids[i]);
							} else {
								// L2PT-74790 L2PT-67262 start
								var duocThu = true;
								var trangThaiKhamBenh = grid.jqGrid('getCell', ids[i], 'TRANGTHAIKHAMBENH');
								// nếu đã đóng bệnh án thì cho thu
								if (_benhnhan.TRANGTHAITIEPNHAN != 0) {
									duocThu = true;
								}
								// L2PT-82006 start
								else if (VPI_QUYTRINH_VIENPHI == 4 && _benhnhan.LOAITIEPNHANID == 1) {
									if (_benhnhan.DOITUONGBENHNHANID == 1) {
										if (_benhnhan.LYDO_VAOVIEN == 3) {
											duocThu = false;
										} else {
											if (_doituong_dv == 1 && VP_THU_BHYT_NGOAITRU_CHUADBA == 0) {
												duocThu = false;
											} else {
												duocThu = true;
											}
										}
									} else {
										duocThu = true;
									}
								}
								// L2PT-82006 end
								// nếu cấu hình thu theo khoa và khoa đó đã kết thúc thì cho thu
								// L2PT-78589: luôn cho thu tiền
								else if (_benhnhan.LOAITIEPNHANID != 1 && fConfig.VPI_THUVP_THEOKHOA == '1') { // && trangThaiKhamBenh == 9) {
									duocThu = true;
								} else if (_benhnhan.TRANGTHAITIEPNHAN == 0 && _doituong_dv == 1 && (VP_THU_BHYT_NGOAITRU_CHUADBA == 0 || _benhnhan.LOAITIEPNHANID != 1)) { // L2PT-34420
									duocThu = false;
									//grid.jqGrid('delRowData', ids[i]);
								}
								if (!duocThu) {
									grid.jqGrid('delRowData', ids[i]);
								}
								// L2PT-74790 L2PT-67262 end
								else {
									if (VP_TACH_HOADON == 0 || VPI_TUDONGTACH_HOADON == 1 || (_benhnhan.LOAITIEPNHANID != 1 && VPI_TACHHD_NOITRU == 1)) {
										grid.jqGrid('setSelection', ids[i], false);
										GridUtil.markRow(_gridId_DV, ids[i]);
									} else {
										// L2PT-36512 start
										// L2PT-34420 start
										if (VP_TACH_HOADON == 8) {
											if (_nhomthanhtoan == _nhomtt_pt || _nhomtt_pt == 0) {
												var _tyle_vat = grid.jqGrid('getCell', ids[i], 'TYLE_VAT');
												if (_tyle_vat == _nhom_tyle_vat || _nhom_tyle_vat == -1) {
													_nhomtt_pt = _nhomthanhtoan;
													_nhom_tyle_vat = _tyle_vat;
													grid.jqGrid('setSelection', ids[i], false);
													GridUtil.markRow(_gridId_DV, ids[i]);
												} else {
													grid.jqGrid('delRowData', ids[i]);
												}
											} else {
												grid.jqGrid('delRowData', ids[i]);
											}
										} else
										// L2PT-34420 end
										// L2PT-36512 end
										if (_nhomthanhtoan == _nhomtt_pt || _nhomtt_pt == 0) {
											_nhomtt_pt = _nhomthanhtoan;
											grid.jqGrid('setSelection', ids[i], false);
											GridUtil.markRow(_gridId_DV, ids[i]);
										} else {
											//grid.jqGrid('delRowData',ids[i]);
										}
									}
								}
							}
						}
					}
					// L2PT-128731 start
					if (fConfig.VPI_LOAD_DV == '1') {
						grid.show();
					}
					// L2PT-128731 end
					if (_benhnhan.TRANGTHAITIEPNHAN >= 1) {
						if (_thu_khac == 1) {// check thu khac
							_chot = false;
						} else {
							//var arr_id = grid.getDataIDs();
							var arr_row = grid.jqGrid("getGridParam", "selarrrow");
							var dv_size = _arr_DV_CT.length;
							var sel_dv_size = arr_row.length;
							if (dv_size == sel_dv_size)
								_chot = true;
							else
								_chot = false;
							var _loaiphieuthuid = $("#cboLOAIPHIEUTHUID").val();
							if (VPI_QUYTRINH_VIENPHI != 1 && _loaiphieuthuid == 2 && dv_size != sel_dv_size)
								$("#cboLOAIPHIEUTHUID").val(6);
							if (VPI_QUYTRINH_VIENPHI != 1 && _loaiphieuthuid == 6 && dv_size == sel_dv_size && parseFloat(_vpData.NOPTHEM) < 0)
								$("#cboLOAIPHIEUTHUID").val(2);
							//grid.jqGrid('setSelection',id, false); 
						}
					}
				}
				// L2PT-128731 end
				loadDV();
				var _loaitt = $("#cboHINHTHUCTHANHTOAN").val();//L2PT-7489
				setEnabled([ 'txtMABENHNHAN', 'cboHINHTHUCTHANHTOAN', 'btnHTTT', 'cboLOAIPHIEUTHUID' ], [ 'btnIn' ]); // L2PT-19304 btnHTTT
				if (_loaitt == 2 || _loaitt == 5) {
					setEnabled([ 'txtIDPOST' ], []);
				}
				// L2PT-7489 cau hinh chinh nguoi thu
				if (VPI_CHINH_NGAYTHU == 0) {
					setEnabled([ 'txtNGAYLAP', 'calNGAYLAP' ], []);
				}
			}
			if (_luu) {
				loadGridDataPT(_tiepnhanid, true, false);
				// L2PT-21967 click duyet ke toan sau khi load xong dich vu
				// L2PT-31249 chinh sua cau hinh VPI_DUYETKT_THUVPDBA = 2 
				// L2PT-21633: BN khám DV
				// L2PT-28267 start: lưu log tự động duyệt Kế toán khi thu viện phí BN đã đóng BA
				var ltext = "DUYET_VP1_1:Duyệt kế toán khi thu viện phí BN đã đóng BA" + ";VP_DUYET_BH_KHI_DUYET_KETOAN:" + VP_DUYET_BH_KHI_DUYET_KETOAN + ";TRANGTHAITIEPNHAN:" +
						_benhnhan.TRANGTHAITIEPNHAN;
				save_log_act_form("VPI01T001_THUVIENPHI_V1", "DUYETKETOAN", ltext, _tiepnhanid.toString());
				// L2PT-28267 end
				if (_benhnhan.BENHNHAN_KDV != '0' || ((VPI_DUYETKT_THUVPDBA == 1 || (VPI_DUYETKT_THUVPDBA == 2 && _benhnhan.LOAITIEPNHANID == 0)) && _benhnhan.TRANGTHAITIEPNHAN != 0)) {
					// check thu tien
					var arr_dagiaodich = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_dagiaodich, _tiepnhanid);
					if (arr_dagiaodich && arr_dagiaodich.length > 0) {
						var _vp_giaodich = arr_dagiaodich[0];
						// L2PT-28267 start: lưu log tự động duyệt Kế toán khi thu viện phí BN đã đóng BA
						var ltext = "DUYET_VP1_2:Duyệt kế toán khi thu viện phí BN đã đóng BA" + ";CHENHLECH:" + _vp_giaodich.CHENHLECH;
						save_log_act_form("VPI01T001_THUVIENPHI_V1", "DUYETKETOAN", ltext, _tiepnhanid.toString());
						// L2PT-28267 end
						if (_vp_giaodich.CHENHLECH == 0) {
							$("#btnDuyet").click();
						}
					}
				}
				// L2PT-21967 end
				_luu = false;
			}
		});
		// L2PT-33791 thêm btnLuuVaPH
		// L2PT-53446 thêm btnLuuVaKySo
		setEnabled([], [ 'btnIn', 'btnInHDPL', 'btnLuu', 'btnLuuVaPH', 'btnLuuVaKySo', 'btnHuyBo' ]); //L2PT-21449 thêm chức năng in phôi lớn
	}
	function tinhTongTien(_dvData) {
		var _tran_bhyt = _benhnhan.BHYT_GIOIHANBHYTTRAHOANTOAN;
		var _tyle_tuyen = _benhnhan.TYLE_TUYEN;
		var _tyle_bhyt = _benhnhan.TYLE_THE;
//    	var _du5nam = _benhnhan.THAMGIABHYTDU5NAM;
//    	var _tradu6thang = _benhnhan.TRADU6THANGLUONGCOBAN;
		var _vp_giaodich = new Object();
		var arr_dagiaodich = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_dagiaodich, _tiepnhanid);
		if (arr_dagiaodich && arr_dagiaodich.length > 0)
			_vp_giaodich = arr_dagiaodich[0];
		_vpData = vienphi_tinhtien.tinhtien_dv(_dvData, _vp_giaodich, _tran_bhyt, _tyle_tuyen, _tyle_bhyt);
//    	console.log(_vpData);
		// L2PT-13442 start
		var arr_vienphi = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.05", _tiepnhanid);
		if (arr_vienphi && arr_vienphi.length > 0) {
			_vpData.T_BNCCT = arr_vienphi[0].T_BNCCT_BH;
			_vpData.DANOP_NTU = arr_vienphi[0].DANOP_NTU; // L2PT-15705
		}
		// L2PT-13442 end
		var _vpData_hienthi = vienphi_tinhtien.convertObjToCurrency(_vpData, VPI_DEC_FORMAT == 1 ? true : false);
		FormUtil.setObjectToForm('tongVP', '', _vpData_hienthi);
		// L2PT-10597 start
		if (VPI_TT_GODUYETKT == '1' && _benhnhan.TRANGTHAITIEPNHAN_VP == 1) {
			setEnabled([ 'btnDuyet' ], []);
		}
		// L2PT-10597 end
		else if ((_benhnhan.TRONVIEN == 0 || _benhnhan.DT_QUANNHAN != 0 || _vpData.NOPTHEM == 0) && (_benhnhan.TRANGTHAITIEPNHAN == 1 || _benhnhan.TRANGTHAITIEPNHAN == 2)) {
			setEnabled([ 'btnDuyet' ], []);
			// L2PT-1401 // L2PT-47548 start
			if (_benhnhan.TRANGTHAITIEPNHAN_VP != 1) {
				setEnabled([ 'calNGAY_QUYET_TOAN', 'txtNGAY_QUYET_TOAN' ], []);
			} else {
				setEnabled([], [ 'calNGAY_QUYET_TOAN', 'txtNGAY_QUYET_TOAN' ]);
			}
			// L2PT-1401 // L2PT-47548 end
		} else {
			setEnabled([], [ 'btnDuyet' ]);
			setEnabled([], [ 'calNGAY_QUYET_TOAN', 'txtNGAY_QUYET_TOAN' ]); // L2PT-1401 // L2PT-47548
		}
		// L2PT-28283 start 
		if (VPI_CHANDUYETKT_BNTRONVIEN == '1') {
			if (_benhnhan.TRONVIEN == '0') {
				setEnabled([], [ 'btnDuyet' ]);
			}
		}
		// L2PT-28283 end 
		// L2PT-11715 start
		if ((_vpData.NOPTHEM != 0 || _vpData.VIENPHI == 0) && _benhnhan.TRANGTHAITIEPNHAN_VP == 0)
			$("#rTronVien").css({
				"pointer-events" : "auto",
				"opacity" : "1.0"
			});
		else
			$("#rTronVien").css({
				"pointer-events" : "none",
				"opacity" : "0.6"
			});
		// L2PT-11715 end
		// L2PT-17330 start 
		if (_benhnhan.TRANGTHAITIEPNHAN == 0) {
			$('#lblTRANGTHAI').html("<strong>CHƯA ĐÓNG BỆNH ÁN</strong>");
		} else if (_benhnhan.TRANGTHAITIEPNHAN_VP == 1) {
			$('#lblTRANGTHAI').html("<strong>ĐÃ DUYỆT KẾ TOÁN</strong>");
		} else if (_vpData.NOPTHEM != 0) {
			$('#lblTRANGTHAI').html("<strong>CHƯA THANH TOÁN</strong>");
		} else {
			$('#lblTRANGTHAI').html("<strong>CHƯA DUYỆT KẾ TOÁN</strong>");
		}
		// L2PT-17330 end
	}
	// lay du lieu cho grid phieu thu
	function loadGridDataPT(_tiepnhanid, _luu, _ht) {
		if (flagLoading)
			return;
		var lookup_sqlPT = "";
		lookup_sqlPT = _gridSQL_PT;
		var sql_parPT = [];
		// L2PT-32061 start
		var _tungay_pt = -1;
		var _denngay_pt = -1;
		var _key = -1;
		var _mode = 0;
		var _loaiphieu = -1;
		var objLoadDSPT = new Object();
		objLoadDSPT.TIEPNHANID = _tiepnhanid + "";
		objLoadDSPT.LOAIPHIEU = _loaiphieu + "";
		objLoadDSPT.TUNGAY = _tungay_pt + "";
		objLoadDSPT.DENNGAY = _denngay_pt + "";
		objLoadDSPT.KEY = _key + "";
		objLoadDSPT.MODE = _mode + "";
		sql_parPT.push({
			"name" : "[0]",
			"value" : JSON.stringify(objLoadDSPT)
		});
		// L2PT-32061 end
//		sql_parPT= RSUtil.setSysParam(sql_parPT, _param);
		GridUtil.loadGridBySqlPage(_gridId_PT, lookup_sqlPT, sql_parPT, function() {
			//build menu DS phiếu thu
			$(".jqgrow", '#' + _gridId_PT).contextMenu('contextMenu_PT', {
				bindings : {
					'rXemHDDT' : function(t) {
						_inHDDT();
					},
					//L2PT-21660 start 
					'rXemHDDTCD' : function(t) {
						if (flagLoading)
							return;
						// L2PT-6086
						var kt_quyen_hddt = kiemTraQuyen("HDDT", "VPI_QUYEN_HDDT", "0");
						if (kt_quyen_hddt == '0') {
							DlgUtil.showMsg("Bạn không có quyền thao tác với HĐĐT");
							return;
						} else if (kt_quyen_hddt == '-1') {
							DlgUtil.showMsg("Có lỗi xảy ra khi kiểm tra quyền thao tác với HĐĐT");
							return;
						}
						// L2PT-6086
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						// L2PT-37815 start: chuyển sang file inv.js
						if (!dsCH) {
							dsCH = layDsCH(_hospital_id);
						}
						if (!dsCH) {
							return;
						}
						var resultConvert = convertForVerifyFkey(_phieuthuId, dsCH)
						if (resultConvert == "OK:") {
							loadGridDataPT(_tiepnhanid);
						} else {
							DlgUtil.showMsg(resultConvert);
						}
						// L2PT-37815 end
					},
					//L2PT-21660 end
					//L2PT-24260 start 
					'rInHDDTCD' : function(t) {
						// L2PT-42086 : tạo func riêng
						_inHDDTCD();
					},
					// L2PT-24260 end
					'rGuiHDDT' : function(t) {
						// L2PT-7432 start: thêm cấu hình xác nhận gửi hddt
						if (VPI_XACNHAN_GUIHDDT == '1') {
							DlgUtil.showConfirm("Xác nhận gửi HĐĐT ?", function(flag) {
								if (flag) {
									guiHDDT(0); // L2PT-19666 
								} else {
									return;
								}
							});
						} else {
							guiHDDT(0); // L2PT-19666 
						}
						// L2PT-7432 end
					},
					// L2PT-22748 start
					'rGuiHDDT_KPH' : function(t) {
						if (flagLoading)
							return;
						selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuid_hddt = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var _nguoilapphieu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'NGUOIDUNGID');
						var _datra = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'DATRA');
						var _tiepnhanid_pt = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'TIEPNHANID');
						if (_tiepnhanid_pt != _tiepnhanid) {
							DlgUtil.showMsg("Chưa chọn đúng bệnh nhân có hóa đơn này");
							return;
						}
						/*
						var VPI_SOTIEN_THUVP_GUIHDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_SOTIEN_THUVP_GUIHDDT'); //L2PT-1878
						if (_datra != 0 && parseFloat(_datra) >= parseFloat(VPI_SOTIEN_THUVP_GUIHDDT)) { //L2PT-1878
						*/
						_datra = parseFloat(_datra); //L2PT-1878
						// L2PT-54003: Gửi HDDT không đồng
						if (_datra != 0 || fConfig.VPI_GUIHDDT_KHONGDONG == "1") {
							if (!_user_id || _user_id == 0) {
								DlgUtil.showMsg("Chưa đăng nhập");
								return;
							}
							if (VPI_QUYEN_GUIHDDT == 1 && _user_id != _nguoilapphieu) {
								DlgUtil.showMsg("Chỉ người lập phiếu mới có thể gửi HĐĐT");
								return;
							}
							var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.CHECK", [ {
								"name" : "[0]",
								"value" : _phieuthuid_hddt
							} ]);
							var rows = JSON.parse(data);
							if (rows != null && rows.length > 0) {
								var check = rows[0]["SYNC_FLAG"];
								if (check == 1) {
									DlgUtil.showMsg("Hóa đơn đã được gửi");
								} else {
									var kq_gui_hddt = guiHDDTTheoPhieu(_phieuthuid_hddt, 2);
									if (kq_gui_hddt == 1 || kq_gui_hddt == 2) {
										loadGridDataPT(_tiepnhanid, false, 0); // BVTM-676
									}
								}
							}
						}
					},
					// L2PT-22748 end
					// L2PT-19666 start
					'rGuiHDDT_KHDN' : function(t) {
						// L2PT-7432 start: thêm cấu hình xác nhận gửi hddt
						if (VPI_XACNHAN_GUIHDDT == '1') {
							DlgUtil.showConfirm("Xác nhận gửi HĐĐT ?", function(flag) {
								if (flag) {
									guiHDDT(1);
								} else {
									return;
								}
							});
						} else {
							guiHDDT(1);
						}
						// L2PT-7432 end
					},
					// L2PT-19666 end
					'rInHDDTreview' : function(t) {
						var par = [];
						par.push({
							name : 'phieuthuid',
							type : 'String',
							value : _phieuthuid.toString()
						});
						var typeExport = "pdf";//$("#sltInBieuMau").val();
						CommonUtil.openReportGet('window', 'PHIEUIN_HDDT_REVIEW', typeExport, par, true, true);
					},
					'rInBBHUY' : function(t) {
						var par = [];
						par.push({
							name : 'phieuthuid',
							type : 'String',
							value : _phieuthuid.toString()
						});
						var typeExport = "pdf";//$("#sltInBieuMau").val();
						CommonUtil.openReportGet('window', 'BIENBAN_HUY_HDDT_A4', typeExport, par, true, true);
					},
					// Cập nhật phiếu thu(ngày
					// thanh toán)
					// L2PT-32438 start: In biên
					// lai DVC
					'rInBienLaiDVC' : function(t) {
						if (flagLoading)
							return;
						selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var urlBienLai = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.DVC.GETURLBL', _phieuthuid);
						if (urlBienLai == "-1") {
							DlgUtil.showMsg("Lỗi lấy URL biên lai");
							return;
						} else if (!urlBienLai) {
							DlgUtil.showMsg("Phiếu thu không có biên lai cổng DVC");
							return;
						}
						urlBienLai = encodeURIComponent(urlBienLai);
						var type = '.pdf';
						var uuid = jsonrpc.AjaxJson.getUUID();
						var _url = "../billDVC?urlBill=" + urlBienLai + "&fileType=" + type + "&uuid=" + uuid;
						window.open(_url, '_blank');
					},
					// L2PT-32438 end
					'rCapNhatPhieuThu' : function() {
						if (flagLoading)
							return;
						// L2PT-114387 start
						if (fConfig.VPI_FCAPNHAT_PHIEUTHU == "0") {
							var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
							var phieuThuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
							var paramInput = new Object();
							paramInput.PHIEUTHUID = phieuThuId;
							paramInput.dlgName = "dlgCapNhatPhieuThu";
							var url = "manager.jsp?func=../vienphi/VPI01T018_capnhatphieuthu_v2";
							var popup = DlgUtil.buildPopupUrl(paramInput.dlgName, "divDlg", url, paramInput, "Cập nhật phiếu thu", 800, 600);
							popup.open();
							return;
						}
						// L2PT-114387 end
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthu_id = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var _mapthu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'MAPHIEUTHU');
						var _nhom_pthu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'MANHOMPHIEUTHU');
						var _ngaythu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'NGAYTHU');
						var _nguoithu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'NGUOITHU');
						var _nguoithuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'NGUOIDUNGID');
						var _nguoihuy = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'NGUOIHUY');
						var _nguoihuyid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'NGUOIDUNGID_HUYPHIEU');
						var _ngayhuy = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'NGAYHUY');
						var _log = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHULOG');
						var paramInput = {
							phieuthu_id : _phieuthu_id,
							mapthu : _mapthu,
							nhom_pthu : _nhom_pthu,
							ngaythu : _ngaythu,
							nguoithu : _nguoithu,
							ngayhuy : _ngayhuy,
							nguoihuy : _nguoihuy,
							nguoithuid : _nguoithuid,
							nguoihuyid : _nguoihuyid,
							log : _log
						};
						// thiết lập sự kiện khi nhấn nút ở popup con
						EventUtil.setEvent("upd_phieuthu", function(e) {
							$("#" + _gridId_PT).trigger("reloadGrid");
							DlgUtil.close("dlgCapNhatPhieuThu");
						});
						var url = "manager.jsp?func=../vienphi/VPI01T018_capnhatphieuthu";
						var popup = DlgUtil.buildPopupUrl("dlgCapNhatPhieuThu", "divDlg", url, paramInput, "Cập nhật phiếu thu", 700, 320);
						popup.open("dlgCapNhatPhieuThu");
					},
					// Cập nhật phiếu thu(ngày thanh toán)
					'rNhapThongTinTT' : function(t) {
						if (flagLoading)
							return;
						selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _mapthu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'MAPHIEUTHU');
						$("#txtMAPHIEUTHUVIEW").val(_mapthu);
						$("#txtTENCONGTYBN").val($("#" + _gridId_PT).jqGrid('getCell', selRowId, 'TENCONGTYBN'));
						$("#txtDIACHI_CTYBN").val($("#" + _gridId_PT).jqGrid('getCell', selRowId, 'DIACHI_CTYBN'));
						$("#txtMASOTHUE_CTYBN").val($("#" + _gridId_PT).jqGrid('getCell', selRowId, 'MASOTHUE_CTYBN'));
						DlgUtil.open("dlgNhapThongTinTT");
					},
					// L2PT-44384 start: in phiếu yêu cầu hủy
					'rYeuCauHuy' : function(t) {
						if (flagLoading)
							return;
						var par = [];
						par.push({
							name : 'phieuthuid',
							type : 'String',
							value : _phieuthuid.toString()
						});
						var typeExport = "pdf";//$("#sltInBieuMau").val();
						CommonUtil.openReportGet('window', 'NGT038_PHIEUYEUCAUHUY_A5', typeExport, par, true, true);
						// L2PT-60354 start: lưu người in phiếu yêu cầu hủy
						var str_result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.TT.YCHUY', _phieuthuid);
						var result = $.parseJSON(str_result);
						if (result.CODE != '00') {
							DlgUtil.showMsg(result.MESSAGE);
						}
						// L2PT-60354 end
					},
					// L2PT-44384 end
					// Hủy phiếu thu trên grid
					'rHuyPhieuThu' : function(t) {
						if (flagLoading)
							return;
						DlgUtil.open("dlgXacNhan");
					},
					// Hủy giữ số
					'rHuyGiuSo' : function(t) {
						if (flagLoading)
							return;
						huyPhieuThu("Giữ số", '1');
					},
					// treo phiếu
					'rTreoPhieu' : function(t) {
						if (flagLoading)
							return;
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.TREOPHIEU', _phieuthuid + '$' + '0');
						//DlgUtil.showMsg(result);
						loadGridDataPT(_tiepnhanid);
						loadGridDataDV(_tiepnhanid);
					},
					//hủy treo phiếu
					'rHuyTreoPhieu' : function(t) {
						if (flagLoading)
							return;
						// kiem tra thoi gian treo phieu	 
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _sothang_treo = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'SOTHANG_TREO');
						var _msg = _sothang_treo > 12 ? 12 : (_sothang_treo > 9 ? 9 : (_sothang_treo > 6 ? 6 : (_sothang_treo > 3 ? 3 : 1)));
						if (_sothang_treo > 1) {
							DlgUtil.showConfirm("Phiếu đã bị treo quá " + _msg + " tháng", function(flag) {
								if (!flag)
									return;
								var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
								var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.TREOPHIEU', _phieuthuid + '$' + '1');
								//DlgUtil.showMsg(result);
								loadGridDataPT(_tiepnhanid);
								loadGridDataDV(_tiepnhanid);
							});
						} else {
							var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
							var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.TREOPHIEU', _phieuthuid + '$' + '1');
							//DlgUtil.showMsg(result);
							loadGridDataPT(_tiepnhanid);
							loadGridDataDV(_tiepnhanid);
						}
					},
					'rKhoiPhuc' : function(t) {
						if (flagLoading && VPI_KHOIPHUCPHIEU == 1)
							return;
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.RESTORE', _phieuthuid);
						DlgUtil.showMsg(result);
						loadGridDataPT(_tiepnhanid);
						loadGridDataDV(_tiepnhanid);
						setEnabled([ 'btnHuy' ], []);
					},
					'rHoanDV' : function(t) {
						hoantien();
					},
					//L2PT-10049 start
					'rXemTruoc' : function(t) {
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'LOAIPHIEUTHUID');
						var _dahuyphieu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'DAHUYPHIEU');
						InPhieuThu(_phieuthuid, _loaiphieuthuid, _dahuyphieu, 0);
					},
					//L2PT-10049 end
					// L2PT-6393 start
					'rInHuyTU' : function() {
						var par = [];
						par.push({
							name : 'phieuthuid',
							type : 'String',
							value : _phieuthuid.toString()
						});
						var typeExport = "pdf";//$("#sltInBieuMau").val();
						CommonUtil.openReportGet('window', 'NGT038_PHIEUHOANUNG_A4', typeExport, par, true, true);
					}
					// L2PT-6393 end
					// L2PT-6393 start
					,
					'rInHuyTU' : function() {
						var par = [];
						par.push({
							name : 'phieuthuid',
							type : 'String',
							value : _phieuthuid.toString()
						});
						var typeExport = "pdf";//$("#sltInBieuMau").val();
						CommonUtil.openReportGet('window', 'NGT038_PHIEUHOANUNG_A4', typeExport, par, true, true);
					}
					// L2PT-34615 start
					,
					'rInPTCongKham' : function() {
						inPTTheoLoai(1);
					},
					'rInPTDongCT' : function() {
						inPTTheoLoai(2);
					},
					'rInPTTuNguyen' : function() {
						inPTTheoLoai(3);
					},
					'rInPTTongHop' : function() {
						inPTTheoLoai(4);
					}
					// L2PT-34615 end
					// L2PT-80276 start
					,
					'rHoanTraKTM' : function() {
						var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
						var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
						var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'LOAIPHIEUTHUID');
						var _dahuyphieu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'DAHUYPHIEU');
						var _datra = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'DATRA');
						if (_loaiphieuthuid != 1 && _loaiphieuthuid != 3 && _loaiphieuthuid != 6) {
							DlgUtil.showMsg("Chỉ được hoàn trả cho phiếu thu hoặc phiếu tạm ứng");
						} else if (_dahuyphieu == 1) {
							DlgUtil.showMsg("Phiếu đã hủy, không thể hoàn trả");
						} else if (!_datra || parseFloat(_datra) <= 0) {
							DlgUtil.showMsg("số tiền hoàn trả phải lớn hơn 0");
						} else {
							if (flagLoading)
								return;
							initPopupInput({
								dlgId : 'dlgIdLyDoHuyPhieu',
								dlgName : 'dlgNameLyDoHuyPhieu',
								dlgTitle : 'Nhập lý do hủy phiếu',
								dlgEles : [ {
									cla : ' required',
									label : 'Lý do hủy phiếu',
									type : 'input',
									prefix : 'txt',
									id : 'LYDOHUYPHIEU',
									free : 'valrule="Lý do hủy phiếu,trim_required"'
								} ]
							});
						}
					}
					// L2PT-80276 end
					// L2PT-53446 start
					,
					'kyCA' : function() {
						_caRpt('1');
					},
					'printKyCA' : function() {
						_caRpt('2');
					},
					'huyKyCA' : function() {
						_caRpt('0');
					}
				// L2PT-53446 end
				},
				onContextMenu : function(event, menu) {
					var rowId = $(event.target).parent("tr").attr("id");
					var grid = $('#' + _gridId_PT);
					grid.setSelection(rowId);
					var selRowId = grid.jqGrid("getGridParam", "selrow");
					console.log('SELROW_PT: ' + selRowId);
					return true;
				},
			});
			// duyet danh sach phieuthu
			var grid = $("#" + _gridId_PT);
			var ids = grid.getDataIDs();
			var VPI_HIEN_YCHOAN = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HIEN_YCHOAN'); // BVTM-2959
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var _dahuyphieu = grid.jqGrid('getCell', id, 'DAHUYPHIEU');
				var _yc_hoan = grid.jqGrid('getCell', id, 'YC_HOAN');
				var _dahoan = grid.jqGrid('getCell', id, 'DAHOAN');
				var _treophieu = grid.jqGrid('getCell', id, 'TREOPHIEU');
				var _huygiuso = grid.jqGrid('getCell', id, 'HUYGIUSO');
				//L2PT-18031 start 
				var _SYNC_FLAG = grid.jqGrid('getCell', id, 'SYNC_FLAG');
				if (_SYNC_FLAG == 1) {
					grid.jqGrid('setRowData', id, "", {
						color : 'green'
					});
					_icon = '<center><img src="' + _opts.imgPath[4] + '" width="15px"></center>';
					$("#" + _gridId_PT).jqGrid('setCell', id, 'ICON_PT', _icon); // L2PT-68665
					grid.jqGrid('setRowData', id, "", {
						color : 'green'
					});
				}
				//L2PT-18031 End
//		    	var row =  grid.jqGrid('getRowData', id);
//				console.log(make_class(row,"DSPhieuThu"));
				if (VPI_HIEN_YCHOAN == '1' && _yc_hoan > 0) { // BVTM-2959
					_icon = '<center><img src="' + _opts.imgPath[3] + '" width="15px"></center>';
					$("#" + _gridId_PT).jqGrid('setCell', id, 'ICON_YCH', _icon); // L2PT-68665
					grid.jqGrid('setRowData', id, "", {
						color : 'red'
					});
				}
				// L2PT-118724 start
				var _kysopt = grid.jqGrid('getCell', id, 'FLAG_CA');
				if (_kysopt == 1) {
					$("#" + _gridId_PT).jqGrid('setCell', id, 'ICON_KSPT', getIconPathByName('ca'));
				}
				// L2PT-118724 end
				if (_treophieu == 1) {
					_icon = '<center><img src="' + _opts.imgPath[6] + '" width="15px"></center>';
					$("#" + _gridId_PT).jqGrid('setCell', id, 'ICON_PT', _icon); // L2PT-68665
					grid.jqGrid('setRowData', id, "", {
						color : 'maroon'
					});
					grid.setCell(id, 'MAPHIEUTHU', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'LOAIPHIEUTHUID_2', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'NGAYTHU', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'NGUOITHU', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'DATRA', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'TRANGTHAI_HDDT', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'NGAYHUY', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'NGUOIHUY', '', {
						'text-decoration' : 'line-through'
					});
				}
				if (_dahoan == 1) {
					_icon = '<center><img src="' + _opts.imgPath[4] + '" width="15px"></center>';
					$("#" + _gridId_PT).jqGrid('setCell', id, 'ICON_PT', _icon); // L2PT-68665
					grid.jqGrid('setRowData', id, "", {
						color : 'blue'
					});
					grid.setCell(id, 'MAPHIEUTHU', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'LOAIPHIEUTHUID_2', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'NGAYTHU', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'NGUOITHU', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'DATRA', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'TRANGTHAI_HDDT', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'NGAYHUY', '', {
						'text-decoration' : 'line-through'
					});
					grid.setCell(id, 'NGUOIHUY', '', {
						'text-decoration' : 'line-through'
					});
				}
				if (_dahuyphieu == 1) {
					_icon = '<center><img src="' + _opts.imgPath[5] + '" width="15px"></center>';
					$("#" + _gridId_PT).jqGrid('setCell', id, 'ICON_PT', _icon); // L2PT-68665
					grid.jqGrid('setRowData', id, "", {
						color : 'gray'
					});
				}
				if (_huygiuso == 1) {
					_icon = '<center><img src="' + _opts.imgPath[0] + '" width="15px"></center>';
					$("#" + _gridId_PT).jqGrid('setCell', id, 'ICON_PT', _icon); // L2PT-68665
					grid.jqGrid('setRowData', id, "", {
						color : 'gray'
					});
				}
				// L2PT-110765 start
				var _hinhThucThanhToan = grid.jqGrid('getCell', id, 'HINHTHUCTHANHTOAN');
				if (_hinhThucThanhToan == 12) {
					GridUtil.markRow(_gridId_PT, id, 'maunen_pt_qrcode');
				}
				// L2PT-110765 end
				var phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', ids[i], 'PHIEUTHUID');
				if (phieuthuid == _phieuthuid && !_ht) {
					$("#" + _gridId_PT).jqGrid('setSelection', ids[i], true);
					/*GridUtil.unmarkAll(_gridId_PT);
					GridUtil.markRow(_gridId_PT, ids[i], '');
					rowIdMarked_PT = ids[i];*/
				}
			}
			// kiem tra cau hinh in khi luu xong
			if (VPI_LUU_IN == 1 && _luu) {
				onBtnInClick(_luu);
			}
			// click nut them khi hoan tien xong
			if (_ht) {
				var arr_dagiaodich = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_dagiaodich, _tiepnhanid);
				if (arr_dagiaodich && arr_dagiaodich.length > 0) {
					var _vp_giaodich = arr_dagiaodich[0];
					if (_vp_giaodich.CHENHLECH != 0) {
						$('#btnThem').click();
						return;
					}
				}
				loadGridDataDV(_tiepnhanid);
			}
		});
	}
	function onBtnInClick(_saveEvent) {
		var _selRowId = $("#" + _gridId_PT).jqGrid('getGridParam', 'selrow');
		var _nguoithu = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'NGUOIDUNGID');
		if (VPI_IN_PHIEUTHU == 1) {
			if (_user_id != _nguoithu) {
				DlgUtil.showMsg("Bạn không có quyền in phiếu này");
				return false;
			}
		}
		var _phieuthuId_org = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'PHIEUTHUID_ORG');
		var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'LOAIPHIEUTHUID');
		var _dahuyphieu = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'DAHUYPHIEU');
		var _loaiphieuthu = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'LOAIPHIEUTHU');
		// L2PT-52868 start
		if (fConfig.VPI_INPHIEU_DAHUY == '1' && _dahuyphieu == 1) {
			DlgUtil.showMsg('Không được in phiếu đã hủy');
			return false;
		}
		// L2PT-52868 end
		if (VPI_QT_IN_PHIEUTHU == 0) {
			InPhieuThu(_phieuthuid, _loaiphieuthuid, _dahuyphieu, _loaiphieuthu);
		} else {
			var ids = $("#" + _gridId_PT).getDataIDs();
			var _print = false;
			var _set = false;
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var _phieuthuId_org_cur = $("#" + _gridId_PT).jqGrid('getCell', id, 'PHIEUTHUID_ORG');
				_loaiphieuthu = $("#" + _gridId_PT).jqGrid('getCell', id, 'LOAIPHIEUTHU');
				if (_phieuthuId_org_cur == _phieuthuId_org) {
					var _phieuthuId_cur = $("#" + _gridId_PT).jqGrid('getCell', id, 'PHIEUTHUID');
					var _loaiphieuthuid_cur = $("#" + _gridId_PT).jqGrid('getCell', id, 'LOAIPHIEUTHUID');
					var _dahuyphieu_cur = $("#" + _gridId_PT).jqGrid('getCell', id, 'DAHUYPHIEU');
					if (_saveEvent) {
						if (VPI_QT_IN_PHIEUTHU == 1) {
							if (_loaiphieuthuid_cur != _THUTIEN) {
								_print = true;
								InPhieuThu(_phieuthuId_cur, _loaiphieuthuid_cur, _dahuyphieu_cur, _loaiphieuthu);
							} else {
								_set = true;
								_phieuthuid = _phieuthuId_cur;
							}
							if (_set && _print) {
								break;
							}
						} else if (VPI_QT_IN_PHIEUTHU == 2) {
							if (_loaiphieuthuid_cur == _THUTIEN) {
								InPhieuThu(_phieuthuId_cur, _loaiphieuthuid_cur, _dahuyphieu_cur, _loaiphieuthu);
							}
							// L2PT-21601 start
						} else if (VPI_QT_IN_PHIEUTHU == 3) {
							if (_loaiphieuthuid_cur != _THUTIEN) {
								InPhieuThu(_phieuthuId_cur, _loaiphieuthuid_cur, _dahuyphieu_cur, _loaiphieuthu);
							} else {
								// check có dv bh hay không
								var _sql_par = [];
								_sql_par.push({
									"name" : "[0]",
									value : _phieuthuId_cur
								});
								_sql_par.push({
									"name" : "[1]",
									value : _phieuthuId_cur
								});
								var _is_dct = jsonrpc.AjaxJson.getOneValue("VPI01T001.18", _sql_par);
								if (_is_dct != 1) {// check có dv bh hay không
									_report_code = 'NGT036_HOADONGTGT_A4';
									var par = [];
									par.push({
										name : 'phieuthuid',
										type : 'String',
										value : _phieuthuId_cur.toString()
									});
									if (VPI_KIEUIN_HOADON == 1) {
										CommonUtil.openReportGet('window', _report_code, 'pdf', par, true, true);
									} else {
										CommonUtil.openReportGet('window', _report_code, 'pdf', par);
									}
								}
							}
						}
						// L2PT-21601 end
						// L2PT-26720 start
						else if (VPI_QT_IN_PHIEUTHU == 4) {
							if (_loaiphieuthuid_cur == _TAMUNG) {
								InPhieuThu(_phieuthuId_cur, _loaiphieuthuid_cur, _dahuyphieu_cur, _loaiphieuthu);
							}
						}
						// L2PT-26720 end
						// L2PT-29171 start
						else if (VPI_QT_IN_PHIEUTHU == 5) {
							InPhieuThu(_phieuthuId_cur, _loaiphieuthuid_cur, _dahuyphieu_cur, _loaiphieuthu);
						} else if (VPI_QT_IN_PHIEUTHU == 6) {
							if (_loaiphieuthuid_cur != _THUTIEN) {
								InPhieuThu(_phieuthuId_cur, _loaiphieuthuid_cur, _dahuyphieu_cur, _loaiphieuthu);
							}
						}
						// L2PT-29171 end
					} else {
						if (_loaiphieuthuid != _THUTIEN) {
							InPhieuThu(_phieuthuid, _loaiphieuthuid, _dahuyphieu, _loaiphieuthu);
							break;
						} else {
							if (_loaiphieuthuid_cur == _THUTIEN) {
								InPhieuThu(_phieuthuId_cur, _loaiphieuthuid_cur, _dahuyphieu_cur, _loaiphieuthu);
							}
						}
					}
				}
			}
		}
		if (HIS_FOCUS_MABN == 0) {
			//$("#"+_gridId_BN)[0].clearToolbar();
			//$(_focus_element).focus();
			$(_focus_element).select();
		}
	}
	//Start BVTM-5936
	function onBtnInQRClick() {
		var _selRowId = $("#" + _gridId_PT).jqGrid('getGridParam', 'selrow');
		var _httt = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'HINHTHUCTHANHTOAN');
		var _loaipt = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'LOAIPHIEUTHUID');
		if (_httt == 10) {
			var _noidung;
			switch (parseInt(_loaipt)) {
				case _THUTHEM:
					_noidung = 'Thu tien vien phi';
				break;
				case _HOANUNG:
					_noidung = 'Hoan tien';
				break;
				case _TAMUNG:
					_noidung = 'Thu tien tam ung';
				break;
				case _HOANDICHVU:
					_noidung = 'Hoan tien dich vu';
				break;
				default:
					_noidung = 'Thu tien';
			}
			var _sql_par = [];
			_sql_par.push({
				name : 'loaiphieu',
				type : 'String',
				value : _loaipt
			});
			_sql_par.push({
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid
			});
			_sql_par.push({
				name : 'sotien',
				type : 'String',
				value : get_val_m('txtTHUCTHU')
			});
			_sql_par.push({
				name : 'noidung',
				type : 'String',
				value : _noidung
			});
			var qrCodeImage = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.GET.QRCODE.PT', _phieuthuid);
			_sql_par.push({
				name : 'qrcode',
				type : 'String',
				value : qrCodeImage
			});
			// L2PT-2161 start
			var rpcodeQRCODE = '';
			if (_loaipt == _TAMUNG) {
				rpcodeQRCODE = 'NGT041_PHIEUTAMUNG_QRCODE_A4';
			} else {
				rpcodeQRCODE = 'NGT041_PHIEUTHUTIEN_QRCODE_A4'
			}
			CommonUtil.openReportGet('window', rpcodeQRCODE, 'pdf', _sql_par);
			// L2PT-2161 end
		} else
			DlgUtil.showMsg("Phiếu này không có mã QR!");
	}
	//End BVTM-5936
	//L2PT-10049: them _loaiphieuthu
	function InPhieuThu(_phieuthuid, loaiphieuthu, _dahuyphieu, _loaiphieuthu) {
		// L2PT-2161 start
		var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
		var hinhThucThanhToan = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'HINHTHUCTHANHTOAN');
		// L2PT-2161 end
//		var _dahoan = $("#"+_gridId_PT).jqGrid('getCell', _selRowId, 'DAHOAN');
//		if(_dahoan == 1 ) {
//			DlgUtil.showMsg("Không cho phép in phiếu thu đã hoàn tiền");
//			return false;
//		}
		_sql_par = [];
		_sql_par.push({
			"name" : "[0]",
			value : _phieuthuid
		});
		_sql_par.push({
			"name" : "[1]",
			value : _phieuthuid
		});
		var _is_dct = jsonrpc.AjaxJson.getOneValue("VPI01T001.18", _sql_par);
		//var _macdinh = $("#"+_gridId_PT).jqGrid('getCell', _selRowId, 'MACDINH');
		// L2PT-74042 start: in loại BHBL giống hóa đơn
		if (loaiphieuthu == _THUTIEN || loaiphieuthu == _HOADONBM || loaiphieuthu == _HOADONBHBL) {
			if (VPI_SOLAN_INHOADON == 1) {
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T001.CHECKPT', _phieuthuid);
				if (fl == 1) {
					DlgUtil.showMsg("Hóa đơn đã được in, không thể in lại");
					return false;
				}
			}
			var _rp_code = 'NGT036_HOADONGTGT_A4';
			if (VP_TACH_HOADON == 1 && _is_dct == 1) {
				_rp_code = 'NGT039_HOADONDCT_A4';
			}
			inhoadon(_rp_code, _dahuyphieu, _phieuthuid, _loaiphieuthu);
		} else if (loaiphieuthu == _THUTHEM) {
			// L2PT-24122 start
			if (VPI_SOLAN_INPHIEUTHU == 1) {
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T001.CHECKPT', _phieuthuid);
				if (fl == 1) {
					DlgUtil.showMsg("Phiếu đã được in, không thể in lại");
					return false;
				}
			}
			// L2PT-24122 end
			// L2PT-2161 start
			var rpCodeTHUTHEM = 'NGT041_PHIEUTHUTIEN_A4';
			if (hinhThucThanhToan == 10 || hinhThucThanhToan == 12) {
				rpCodeTHUTHEM = 'NGT041_PHIEUTHUTIEN_INQRCODE_A4';
			}
			InHoadonVP(_phieuthuid, rpCodeTHUTHEM);
			// L2PT-2161 end
		} else if (loaiphieuthu == _TAMUNG) {
			if (VPI_SOLAN_INTAMUNG == 1) {
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T001.CHECKPT', _phieuthuid);
				if (fl == 1) {
					DlgUtil.showMsg("Phiếu thu đã được in, không thể in lại");
					return false;
				}
			}
			// L2PT-2161 start
			var rpCodeTAMUNG = 'NGT034_PHIEUTAMUNG_A4';
			if (hinhThucThanhToan == 10 || hinhThucThanhToan == 12) {
				rpCodeTAMUNG = 'NGT034_PHIEUTAMUNG_INQRCODE_A4';
			}
			InHoadonVP(_phieuthuid, rpCodeTAMUNG);
			// L2PT-2161 end
		} else if (loaiphieuthu == _HOANUNG) {
			InHoadonVP(_phieuthuid, 'NGT038_PHIEUHOANUNG_A4');
		}
		// L2PT-24300 start
		else if (loaiphieuthu == _PHIEUCHI) {
			InHoadonVP(_phieuthuid, 'NGT034_PHIEUCHI_HOANTAMUNG_A4');
		}
		// L2PT-24300 end
		// L2PT-73965 start
		else if (loaiphieuthu == _HOANDICHVU) {
			InHoadonVP(_phieuthuid, 'NGT038_PHIEUHOANDICHVU_A4');
		}
		// L2PT-73965 end
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T001.IN', _phieuthuid);
		if (fl == -1) {
			DlgUtil.showMsg("Cập nhật trạng thái phiếu thu không thành công");
		}
	}
	// in hoa don vien phi - L2PT-10049: them _loaiphieuthu
	function InHoadonVP(_phieuthuid, _report_code, _report_code_chitiet, _dahuyphieu, _loaiphieuthu) {
		var par = [];
		par.push({
			name : 'phieuthuid',
			type : 'String',
			value : _phieuthuid.toString()
		});
		// L2PT-26710 start
		var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
		var _loaiPhieuThuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'LOAIPHIEUTHUID');
		// L2PT-26710 end
		var typeExport = "pdf";//$("#sltInBieuMau").val();
		// L2PT-8295: thêm giá trị cấu hình HIS_IN_HOADONCHITIET = 2 không in hóa đơn bán hàng với loại phiếu HĐ
		if (HIS_IN_HOADONCHITIET == '0' || HIS_IN_HOADONCHITIET == '1' || _loaiPhieuThuId != 1) {
			if (VPI_LUU_HD_THUKHAC == 1 && _loaiphieuthu == 4) {
				var sysdate = jsonrpc.AjaxJson.getSystemDate('yyyyMMddHH24miSS');
				CommonUtil.inPhieu('window', _report_code, typeExport, par, "VNPTHIS_IN_A4" + "_" + _report_code + "_" + sysdate, true, true);
			} else if (_hospital_id == 951) {
				if (VPI_IN_HOADON_BANHANG == 1) {
					if (VPI_KIEUIN_HOADON == 1)
						CommonUtil.openReportGet('window', _report_code, typeExport, par, true, true);
					else
						CommonUtil.openReportGet('window', _report_code, typeExport, par);
				}
			} else if (_hospital_id == 1014 && _loaiPhieuThuId != _TAMUNG && _loaiPhieuThuId != _HOANUNG) { //L2PT-26710
				if (_benhnhan.DOITUONGBENHNHANID == 1) {
					vienphi_tinhtien.inBangKe(_tiepnhanid, _benhnhan.DOITUONGBENHNHANID, _benhnhan.LOAITIEPNHANID);
					CommonUtil.openReportGet('window', _report_code_chitiet, typeExport, par, true, true);
				} else {
					var _dahoan = jsonrpc.AjaxJson.getOneValue("VPI.CHECK.HOAN", [ {
						"name" : "[0]",
						"value" : _phieuthuid
					} ]);
					if (_dahoan == 0) {// neu phieu thu chua hoan
						if (_check_congkham) {// neu co dich vu ngoai cong kham thi in ca 2 phieu
							CommonUtil.directReportV1([ "NGT041_DVPHIEUTHUTIEN_A4_1014", "PHIEU_THONGKE_CHIPHI_1014" ], [ typeExport ], [ par, par ], true, true);
						} else {// chi in phieu thu tien kiem bang ke
							CommonUtil.openReportGet('window', "NGT041_DVPHIEUTHUTIEN_A4_1014", typeExport, par, true, true);
						}
					} else {// L2PT-16426 - neu phieu da hoan
						if (_check_congkham) {// neu co dich vu ngoai cong kham in "THONG KE CHI PHI"
							CommonUtil.openReportGet('window', "PHIEU_THONGKE_CHIPHI_1014", typeExport, par, true, true);
						} else {// Khong cho phep in
							DlgUtil.showMsg("Phiếu đã hủy không được in lại");
						}
					}
				}
				return;
			} else if (_hospital_id == 1077) {
				if (VPI_KIEUIN_HOADON == 1)
					CommonUtil.openReportGet('window', _report_code, typeExport, par, true, true);
				else
					CommonUtil.openReportGet('window', _report_code, typeExport, par);
				//vienphi_tinhtien.inBangKe(_tiepnhanid, _benhnhan.DOITUONGBENHNHANID, _benhnhan.LOAITIEPNHANID);
				return;
			} else {
				if (VPI_KIEUIN_HOADON == 1)
					CommonUtil.openReportGet('window', _report_code, typeExport, par, true, true);
				else
					CommonUtil.openReportGet('window', _report_code, typeExport, par);
			}
		}
		if (HIS_IN_HOADONCHITIET == 0 || !_report_code_chitiet || (_dahuyphieu && _dahuyphieu == 1))
			return;
		if (VPI_KIEUIN_HOADON == 1)
			CommonUtil.openReportGet('window', _report_code_chitiet, typeExport, par, true, true);
		else
			CommonUtil.openReportGet('window', _report_code_chitiet, typeExport, par);
	}
	//function in hoa don tuonglt them 20/10/2017 - L2PT-10049: them _loaiphieuthu
	function inhoadon(BHD, BHP, _phieuthuid, _loaiphieuthu, INPHOILON) {
		//Them mau khac khi in doi tuong benh nhan bhyt cho bvnt (tuonglt 02082017)
		if (INPHOILON && INPHOILON == 1) {
			InHoadonVP(_phieuthuid, BHD, 'NGT037_BANGKEKEMHDGTGT_A4', BHP);
		} else if (_hospital_id == 902) {
			//them loai dich vu cua tung benh nhan benh vien buu dien
			var _loaidichvu = 0;
			var _loaidichvutru = 0;
			var _par_loai = [ _phieuthuid ];
			var arr_loaidichvu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075.EV01", _par_loai.join('$'));
			var _loaidvbv = 0;
			//var _par_loaint = [_phieuthuid];						    							
			var arr_loaidichvunt = jsonrpc.AjaxJson.ajaxCALL_SP_O("HOADONDVBV", _par_loai.join('$'));
			var arr_dvbvntru = jsonrpc.AjaxJson.ajaxCALL_SP_O("HOADONDVNTRU", _par_loai.join('$'));
			if ($('#txtMABHYT').val() != "") {
				if (arr_loaidichvu != null && arr_loaidichvu.length > 0) {
					for (var i = 0; i < arr_loaidichvu.length; i++) {
						_loaidichvu = arr_loaidichvu[i].BHYT;
						if (_loaidichvu == 1) {
							if (_benhnhan.LOAITIEPNHANID == 0) {
								InHoadonVP(_phieuthuid, "NGT036_HOADON_A4_NTRU_BHYT_902", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
							} else
								InHoadonVP(_phieuthuid, "NGT036_HOADONGTGT_BHYT_902", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
						} else {
							if (_benhnhan.LOAITIEPNHANID == 1) {
								if (arr_loaidichvunt != null && arr_loaidichvunt.length > 0) {
									for (var i1 = 0; i1 < arr_loaidichvunt.length; i1++) {
										_loaidvbv = arr_loaidichvunt[i1].DVBV;
										if (_loaidvbv == 1) {
											InHoadonVP(_phieuthuid, "NGT036_HOADONDVTHUKHAC_A4_902", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
										} else
											InHoadonVP(_phieuthuid, 'NGT036_HOADONGTGT_A4_902', 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
									}
								}
							} else if (_benhnhan.LOAITIEPNHANID == 0) {
								if (arr_dvbvntru != null && arr_dvbvntru.length > 0) {
									for (var i2 = 0; i2 < arr_dvbvntru.length; i2++) {
										_loaidichvutru = arr_dvbvntru[i2].DVBV;
										if (_loaidichvutru == 1) {
											InHoadonVP(_phieuthuid, "NGT036_HOADONDV_NTRU_A4_902", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
										} else
											InHoadonVP(_phieuthuid, 'NGT036_HOADON_A4_NTRU_VP_902', 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
									}
								}
							}
						}
					}
				}
			} else {
				if (_benhnhan.LOAITIEPNHANID == 1) {
					if (arr_loaidichvunt != null && arr_loaidichvunt.length > 0) {
						for (var i1 = 0; i1 < arr_loaidichvunt.length; i1++) {
							_loaidvbv = arr_loaidichvunt[i1].DVBV;
							if (_loaidvbv == 1) {
								InHoadonVP(_phieuthuid, "NGT036_HOADONDVTHUKHAC_A4_902", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
							} else
								InHoadonVP(_phieuthuid, 'NGT036_HOADONGTGT_A4_902', 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
						}
					}
				} else if (_benhnhan.LOAITIEPNHANID == 0) {
					if (arr_dvbvntru != null && arr_dvbvntru.length > 0) {
						for (var i2 = 0; i2 < arr_dvbvntru.length; i2++) {
							_loaidichvutru = arr_dvbvntru[i2].DVBV;
							if (_loaidichvutru == 1) {
								InHoadonVP(_phieuthuid, "NGT036_HOADONDV_NTRU_A4_902", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
							} else
								InHoadonVP(_phieuthuid, 'NGT036_HOADON_A4_NTRU_VP_902', 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
						}
					}
				}
			}
		} else if (_hospital_id == 965) {
			if (_benhnhan.LOAITIEPNHANID == 0) {
				if ($('#txtMABHYT').val() != "") {
					InHoadonVP(_phieuthuid, "NGT036_HOADON_BHYT_NTRU_A4_965", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
				} else
					InHoadonVP(_phieuthuid, "NGT036_HOADON_VP_NTRU_A4_965", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
			} else {
				//tuonglt them in hoa don bn cap cau, dtri ngoai tru 8/11/2018				
				var par_tn = [ _benhnhan.TIEPNHANID ];
				var arr_tn = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01_HTVV", par_tn.join('$'));
				var kthtvv = arr_tn[0].HTVAOVIEN;
				if (_benhnhan.LOAITIEPNHANID == 3 || kthtvv > 0) {
					InHoadonVP(_phieuthuid, "NGT036_HOADON_VP_NTRU_A4_965", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
				} else {
					var _loaidichvu = 0;
					var _par_loai = [ _phieuthuid ];
					var arr_loaidichvu = jsonrpc.AjaxJson.ajaxCALL_SP_O("NTU02D075.EV01", _par_loai.join('$'));
					if (arr_loaidichvu != null && arr_loaidichvu.length > 0) {
						for (var i = 0; i < arr_loaidichvu.length; i++) {
							_loaidichvu = arr_loaidichvu[i].BHYT;
							if (_loaidichvu == 1) {
								InHoadonVP(_phieuthuid, "NGT036_HOADONGTGT_BHYT_A4_965", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
							} else
								InHoadonVP(_phieuthuid, "NGT036_HOADONGTGT_A4_965", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
						}
					}
				}
			}
		} else if (_hospital_id == 1041) {
			if (compareDate('30/08/2018 00:00:00', _benhnhan.NGAYTIEPNHAN, 'DD/MM/YYYY HH:mm:ss')) {
				InHoadonVP(_phieuthuid, "NGT036_HOADONGTGT_A4_MOI_1041", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
			} else
				InHoadonVP(_phieuthuid, "NGT036_HOADONGTGT_A4_1041", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
		} else if (_hospital_id == 915 || _hospital_id == 1041 || _hospital_id == 978) {
			if (compareDate('14/11/2018 00:00:00', _benhnhan.NGAYTIEPNHAN, 'DD/MM/YYYY HH:mm:ss')) {
				InHoadonVP(_phieuthuid, "NGT036_HOADONGTGT_A4_MOI_915", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
			} else
				InHoadonVP(_phieuthuid, "NGT036_HOADONGTGT_A4_915", 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
		} else if (_hospital_id == 1045) {
			InHoadonVP(_phieuthuid, "VPI_THUVIENPHI_HOADON_1045", '', BHP, _loaiphieuthu);
		} else if (_hospital_id == 951) {
			if (_benhnhan.LOAITIEPNHANID == 0) {
				InHoadonVP(_phieuthuid, BHD, 'NGT037_BANGKEKEMHDGTGT_NOITRU_A4', BHP, _loaiphieuthu);
			} else {
				InHoadonVP(_phieuthuid, BHD, 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
			}
		} else if (_hospital_id == 960) {
			var par = [];
			par.push({
				name : 'tiepnhanid',
				type : 'String',
				value : _tiepnhanid.toString()
			});
			CommonUtil.openReportGet('window', "NGT036_HOADONGTGT_A4_960", "pdf", par, true, true);
		} else
			InHoadonVP(_phieuthuid, BHD, 'NGT037_BANGKEKEMHDGTGT_A4', BHP, _loaiphieuthu);
	}
	// lay thong tin , ho so cua benh nhan tuong ung voi ma vien phi
	function layTTTiepNhan(_tiepnhanid, _fl_kt) { // L2PT-23457 
		// L2PT-23457 start
		if (!_fl_kt) {
			$("#" + _gridId_DV).jqGrid("clearGridData");
			$("#" + _gridId_PT).jqGrid("clearGridData");
		}
		// //L2PT-23457 end
		setEnabled([], [ 'btnThuKhac' ]);
		var _benhnhan_arr = jsonrpc.AjaxJson.ajaxCALL_SP_O(_sql_thongtintiepnhan, _tiepnhanid);
//      console.log(_benhnhan_arr);
//    	FormUtil.clearForm('ttVienphi', "");
		if (_benhnhan_arr && _benhnhan_arr.length > 0) {
			_benhnhan = _benhnhan_arr[0];
//	    	_benhnhan.DIACHI = getDiaChi(_benhnhan.TENXA,_benhnhan.TENHUYEN, _benhnhan.TENTINH);	        
			FormUtil.setObjectToForm('ttVienphi', "", _benhnhan);
			if (_benhnhan.TRANGTHAITIEPNHAN_BH == 1 || _benhnhan.TRANGTHAITIEPNHAN_VP == 1) {
				$('#btnDuyet').html("<span class='glyphicon glyphicon-ok-sign'></span> Gỡ duyệt KT");
				_flag_duyet = false;
				setEnabled([], [ 'btnThem' ]);
				setEnabled([ 'btnDuyetThuoc' ], []);// L2PT-17330 
			} else {
				$('#btnDuyet').html("<span class='glyphicon glyphicon-ok-sign'></span> Duyệt KT");
				_flag_duyet = true;
				setEnabled([], [ 'btnDuyetThuoc' ]);// L2PT-17330 
			}
			checkBN();
//    		if(_benhnhan.DOITUONGBENHNHANID==1 && _benhnhan.TRANGTHAITIEPNHAN ==1 ) {
//    			var ok = jsonrpc.AjaxJson.ajaxCALL_SP_I(_sql_tinhtien_bhyt,_tiepnhanid);
//    	   		if(ok==1) {
//    	   			loadGridDataDV(_tiepnhanid);
//    	   		} else {
//    	   			DlgUtil.showMsg("có lỗi xảy ra");
//    	   			return false;
//    	   		}
//    		}
			//L2PT-23457 start
			if (_fl_kt) {
				return;
			}
			// L2PT-38300;L2PT-1401 // L2PT-47548 start
			if (VPI_DUYETKT_CHUYENQT == '3') { // bỏ fl_kt
				$('#txtNGAY_QUYET_TOAN').val(_benhnhan.NGAY_QUYET_TOAN);
			}
			// L2PT-38300;L2PT-1401 // L2PT-47548 end
			// L2PT-23457  end
			// L2PT-27497 L2PT-25547 start
			//L2PT-102868 start
			if (VPI_HIENTHI_NGAYDUYET_VP == "1") {
				var ngayduyet_vp = _benhnhan.TRANGTHAITIEPNHAN_VP == 1 ? _benhnhan.NGAYDUYET_VP : _benhnhan.NGAYRAVIEN;
				console.log(ngayduyet_vp);
				$("#txtNGAYDUYET_VP").val(ngayduyet_vp);
			}
			//L2PT-102868 end
			$("#txtSTT").val(_benhnhan.STT);
			// L2PT-27497 L2PT-25547 end
			loadGridDataDV(_tiepnhanid);
			loadGridDataPT(_tiepnhanid);
		}
	}
	function checkBN() {
		if (!_benhnhan)
			return false;
		if (_benhnhan.DATRONVIEN == 1) {
			$("#chkTronVien").prop('checked', true);
		} else {
			$("#chkTronVien").prop('checked', false);
		}
		if (_benhnhan.DOITUONGBENHNHANID == 1) {
			$('#lblDaThuTien').html("Đã giữ thẻ BH");
			if (_benhnhan.DAGIUTHEBHYT == 1) {
				$("#rGiuTheS").text("Bỏ giữ thẻ");
				$("#chkDaThuTien").prop('checked', true);
			} else {
				$("#chkDaThuTien").prop('checked', false);
				$("#rGiuTheS").text("Giữ thẻ");
			}
		} else {
			$('#lblDaThuTien').html("Đã thu tiền khám");
			if (_benhnhan.DATHUTIENKHAM == 1) {
				$("#chkDaThuTien").prop('checked', true);
			} else
				$("#chkDaThuTien").prop('checked', false);
		}
		// L2PT-107313 start
		if (_benhnhan.KHONGKHAM == 1) {
			$("#chkKhongKham").prop('checked', true);
			$("#lblBNKhongKham").html(_benhnhan.BNKHONGKHAM);
		} else {
			$("#chkKhongKham").prop('checked', false);
			$("#lblBNKhongKham").html("BN không khám");
		}
		// L2PT-107313 end
		if (_benhnhan.TRANGTHAITIEPNHAN == 0 && VP_INPHOI_DONGBA == 0) {
			// L2PT-8045: thêm nút in phơi VP
			setEnabled([], [ 'btnInPhoi', 'btnInPhoiBH', 'btnInPhoiVP', 'btnInPhoiCTT' ]);
			$("#rInPhoi").css({
				"pointer-events" : "none",
				"opacity" : "0.6"
			});
		} else {
			// L2PT-8045: thêm nút in phơi VP
			setEnabled([ 'btnInPhoi', 'btnInPhoiBH', 'btnInPhoiVP', 'btnInPhoiCTT' ], []);
			$("#rInPhoi").css({
				"pointer-events" : "auto",
				"opacity" : "1.0"
			});
		}
		// L2PT-17867 start
		if (_benhnhan.TRANGTHAITIEPNHAN == 0) {
			setEnabled([], [ 'btnIn2Lien' ]);
		} else {
			setEnabled([ 'btnIn2Lien' ], []);
		}
		// L2PT-17867 end
		//L2PT-7488 start 
		if (_benhnhan.TRANGTHAITIEPNHAN == 2 || _benhnhan.TRANGTHAITIEPNHAN_VP == 1 || _benhnhan.TRANGTHAITIEPNHAN_BH == 1) {
			$("#rMGNhomDichVu").css({
				"pointer-events" : "none",
				"opacity" : "0.6"
			});
			$("#rMGChenhLech").css({
				"pointer-events" : "none",
				"opacity" : "0.6"
			});
		} else {
			$("#rMGNhomDichVu").css({
				"pointer-events" : "auto",
				"opacity" : "1.0"
			});
			$("#rMGChenhLech").css({
				"pointer-events" : "auto",
				"opacity" : "1.0"
			});
		}
		//L2PT-7488 end 
	}
	// tao popup them mst
	function initPopup_MST() {
		var dlgMST = DlgUtil.buildPopup("dlgMST", "dlgThemMST", "Thêm mã số thuế", 500, 110);
		var btnOK = $('#btn_MST_OK');
		var btnClose = $('#btn_MST_CLOSE');
		var textArea = $('#txtMASOTHUE');
		textArea.keyup(function() {
			if (textArea.val().length == 0) {
				btnOK.attr('disabled', true);
			} else {
				btnOK.attr('disabled', false);
			}
		});
		btnOK.click(function() {
			objMST = new Object();
			var _mst = textArea.val();
			_mst = FormUtil.escape(_mst);
			objMST.TIEPNHANID = _tiepnhanid;
			objMST.MST = _mst;
			var rt = jsonrpc.AjaxJson.ajaxCALL_SP_I(_sql_themMST, JSON.stringify(objMST));
			if (rt == -1)
				DlgUtil.showMsg("Cập nhật không thành công!");
			else {
				textArea.val("");
				btnOK.attr('disabled', true);
				dlgMST.close();
			}
		});
		btnClose.click(function() {
			textArea.val("");
			btnOK.attr('disabled', true);
			dlgMST.close();
		});
	}
	// tạo popup hủy phiếu thu
	function initPopup_HuyPT() {
		// L2PT-84833 start: load HTTT hủy
		ComboUtil.getComboTag("cboHTTT_HUY", "VPI.DM.HTTT", [], "", "", "sp", "", "");
		dlgHuyPT = DlgUtil.buildPopup("dlgXacNhan", "dlgConfirm", "Xác nhận hủy phiếu", 500, 180);
		// L2PT-84833 end
		var textArea = $('#txtLYDOHUYPHIEU');
		var btnOK = $('#btn_HuyPT_OK');
		var btnClose = $('#btn_huyPT_Close');
		textArea.keyup(function() {
			if (textArea.val().length == 0) {
				btnOK.attr('disabled', true);
			} else {
				btnOK.attr('disabled', false);
			}
		});
		btnOK.click(function() {
			var _lydohuyphieu = textArea.val();
			_lydohuyphieu = FormUtil.escape(_lydohuyphieu);
			//L2PT-15552 start
			var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
			var _phieuthuId_org = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID_ORG');
			var _check_huy_clsdth = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.HUYPT.DTHCLS", _tiepnhanid + '$' + _phieuthuId_org);
			var fl = false;
			if (_check_huy_clsdth == 2) {
				DlgUtil.showMsg("Có dịch vụ CLS đã tiếp nhận hoặc đã hoàn thành không thể hủy");
			} else if (_check_huy_clsdth == 1) {
				DlgUtil.showConfirm("Có dịch vụ CLS đã tiếp nhận hoặc đã hoàn thành, tiếp tục hủy ?", function(flag) {
					if (flag) {
						fl = huyPhieuThu(_lydohuyphieu);
					}
				});
			} else {
				fl = huyPhieuThu(_lydohuyphieu);
			}
			//L2PT-15552 end
			if (fl) {
				textArea.val("");
				btnOK.attr('disabled', true);
				dlgHuyPT.close();
			}
		});
		btnClose.click(function() {
			textArea.val("");
			btnOK.attr('disabled', true);
			dlgHuyPT.close();
		});
	}
// 	// tạo popup cập nhật phiếu thu
// 	//tuyennx_edit_start_20171018 yc L2DKBD-103
// 	function initPopup_CapNhatPT() {
// 		dlgCapNhatPT= DlgUtil.buildPopup ("dlgCapNhatPhieuThu","dlgCapNhatPT","Cập nhật phiếu thu",550,200);
// 		var btnOK= $('#btn_CapNhat_OK');
// 		var btnClose= $('#btn_CapNhat_Close');
// 		$("#txtMAPHIEUTHUUP").change(function(){
// 			var _mapthu = $("#txtMAPHIEUTHUUP").val();
// 			var selRowId = $("#"+_gridId_PT).jqGrid("getGridParam","selrow");
// 			var _mapthu_cu  = $("#"+_gridId_PT).jqGrid('getCell', selRowId, 'MAPHIEUTHU');
// 			if(!_mapthu || _mapthu.length == 0 || _mapthu == _mapthu_cu) {
// 				btnOK.attr('disabled', true);
// 			} else {
// 				btnOK.attr('disabled', false);
// 			}
// 		});
// 	    btnOK.click(function(){
// //	    	var _ngaythu = $("#txtNGAYTHANHTOAN").val().trim();
// //			var dateRegex = /^(0?[1-9]|[12][0-9]|3[01])[\/](0?[1-9]|1[012])[\/](\d{4})(\s)(0?[0-9]|[1][0-9]|[2][0-4])[:](0?[0-9]|[1-5][0-9])[:](0?[0-9]|[1-5][0-9])$/;
// //			if(!dateRegex.test(_ngaythu)){
// //				DlgUtil.showMsg("Ngày nhập vào không đúng định dạng");
// //				return false;
// //			}
// //			var selRowId = $("#"+_gridId_PT).jqGrid("getGridParam","selrow");
// //			var _phieuthu_id = $("#"+_gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
// //			var _ngaythu_date = stringToDateTime(_ngaythu);
// //			var _ngaytiepnhan_date =   stringToDateTime(_benhnhan.NGAYTIEPNHAN);
// //			if(_ngaythu_date<_ngaytiepnhan_date) {
// //				DlgUtil.showMsg("Ngày thu không được phép trước ngày tiếp nhận");
// //				return false;
// //			}
// //			var _ngayhientai = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
// //			var _ngayhientai_date = stringToDateTime(_ngayhientai);
// //			if(_ngaythu_date>_ngayhientai_date) {
// //				DlgUtil.showMsg("Ngày thu không được phép sau ngày hiện tại");
// //				return false;
// //			}
// //			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T001.25",_phieuthu_id+'$'+_ngaythu);
// //			if(fl==1) {
// //				$("#"+_gridId_PT).trigger("reloadGrid");
// //				btnOK.attr('disabled', true);
// //				dlgCapNhatPT.close();
// //			} else if(fl == -1) {
// //				DlgUtil.showMsg("Xảy ra lỗi");
// //			}
// 	    	var _mapthu = $("#txtMAPHIEUTHUUP").val().trim();
// 			if(isNaN(_mapthu)){
// 				DlgUtil.showMsg("Mã phiếu thu phải là số");
// 				return false;
// 			}
// 			var selRowId = $("#"+_gridId_PT).jqGrid("getGridParam","selrow");
// 			var _phieuthu_id = $("#"+_gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
// 			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T001.PT_UPD",_phieuthu_id+'$'+_mapthu);
// 			if(fl==1) {
// 				$("#"+_gridId_PT).trigger("reloadGrid");
// 				btnOK.attr('disabled', true);
// 				dlgCapNhatPT.close();
// 			} else if(fl == -1) {
// 				DlgUtil.showMsg("Xảy ra lỗi");
// 			}else if(fl == 2) {
// 				DlgUtil.showMsg("Mã phiếu thu nằm ngoài khoảng số của sổ!");
// 			}
// 			else if(fl == 3) {
// 				DlgUtil.showMsg("Sổ phiếu thu bị trùng!");
// 			}else if(fl == 0) {
// 				DlgUtil.showMsg("Bạn không có quyền sửa phiếu thu này!");
// 			}
//
//
// 	    });
// 		btnClose.click(function(){
// 			$("#txtMAPHIEUTHUUP").val("");
// 			btnOK.attr('disabled', true);
// 			dlgCapNhatPT.close();
// 		});
// 	}
// 	//tuyennx_edit_end_20171018
	// tạo popup cập nhật thong tin thanh toan BN
	//tuyennx_edit_start_20171018 yc L2DKBD-587
	function initPopup_TTBN() {
		dlgTTBN = DlgUtil.buildPopup("dlgNhapThongTinTT", "dlgTTBN", "Nhập thông tin bệnh nhân", 550, 250);
		var btnOK = $('#btn_CapNhatBN_OK');
		var btnClose = $('#btn_CapNhatBN_Close');
		btnOK.click(function() {
			var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
			var _phieuthu_id = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
			var _tencty = $("#txtTENCONGTYBN").val().trim();
			var _dc_cty = $("#txtDIACHI_CTYBN").val().trim();
			var _masothue = $("#txtMASOTHUE_CTYBN").val().trim();
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T001.PT_UPDBN", _phieuthu_id + '$' + _tencty + '$' + _dc_cty + '$' + _masothue);
			if (fl == 1) {
				$("#" + _gridId_PT).trigger("reloadGrid");
				dlgTTBN.close();
				DlgUtil.showMsg("Cập nhật thành công");
			} else if (fl == -1) {
				DlgUtil.showMsg("Xảy ra lỗi");
			}
		});
		btnClose.click(function() {
			$("#txtMAPHIEUTHUVIEW").val("");
			$("#txtTENCONGTYBN").val("");
			$("#txtDIACHI_CTYBN").val("");
			$("#txtMASOTHUE_CTYBN").val("");
			dlgTTBN.close();
		});
	}
	// bo sung ham nhap thong tin BN tren grid danh sach benh nhan
	function initPopup_TTBN_NHIHDG() {
		dlgTTBN_NHIHDG = DlgUtil.buildPopup("dlgNhapThongTinTT_NHIHDG", "dlgTTBN_NHIHDG", "Nhập thông tin bệnh nhân", 550, 300);
		var btnOK = $('#btn_CapNhatBN_OK_NHIHDG');
		var btnClose = $('#btn_CapNhatBN_Close_NHIHDG');
		btnOK.click(function() {
			var _mabenhnhan = $("#txtMABENHNHAN_HDDT").val().trim();
			var _tencty = $("#txtTENCONGTYBN_HDDT").val().trim();
			var _dc_cty = $("#txtDIACHI_CTYBN_HDDT").val().trim();
			var _masothue = $("#txtMASOTHUE_CTYBN_HDDT").val().trim();
			var _email_ctybn = $("#txtEMAIL_CTYBN_HDDT").val().trim();
			var _sotk_ctybn = $("#txtSOTK_CTYBN_HDDT").val().trim();
			var _tennh_ctybn = $("#txtTENNH_CTYBN_HDDT").val().trim();
			//L2PT-18053 ttlinh start
			var validator = new DataValidator("dlgTTBN_NHIHDG");
			var valid = validator.validateForm();
			if (!valid)
				return false;
			//L2PT-18053 end
			//var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T001.PT_UPDBN",_mabenhnhan+'$'+_tencty+'$'+_dc_cty+'$'+_masothue);
			var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.UPDATE.BNHDDT", _mabenhnhan + '$' + _tencty + '$' + _dc_cty + '$' + _masothue + '$' + _email_ctybn + '$' + _sotk_ctybn + '$' + _tennh_ctybn);
			if (fl == 1) {
				//$("#"+_gridId_PT).trigger("reloadGrid");
				dlgTTBN_NHIHDG.close();
				DlgUtil.showMsg("Cập nhật thành công");
			} else if (fl == -1) {
				DlgUtil.showMsg("Xảy ra lỗi");
			}
		});
		btnClose.click(function() {
			$("#txtMABENHNHAN_HDDT").val("");
			$("#txtTENCONGTYBN_HDDT").val("");
			$("#txtDIACHI_CTYBN_HDDT").val("");
			$("#txtMASOTHUE_CTYBN_HDDT").val("");
			$("#txtEMAIL_CTYBN_HDDT").val("");
			$("#txtSOTK_CTYBN_HDDT").val("");
			$("#txtTENNH_CTYBN_HDDT").val("");
			dlgTTBN_NHIHDG.close();
		});
	}
	//tuyennx_edit_end_20171018
	//tuyennx_add_start_ day thong tin bn len he thong HDDT
	function daythongtinbn(_phieuthu_cushd_id) {
		var objCusData = new Object();
		var objCustomers = new Object();
		var objCustomer = new Object();
		objCustomer.Name = _benhnhan.TENBENHNHAN;
		// L2PT-28864 start
		var VPI_HDDT_MA_KH = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_HDDT_MA_KH');
		if (VPI_HDDT_MA_KH == '0') {
			objCustomer.Code = _benhnhan.MABENHNHAN;
		} else if (VPI_HDDT_MA_KH == '1') {
			objCustomer.Code = _benhnhan.MAHOSOBENHAN;
		} else {
			//var _phieuthu_cushd_id = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
			var _cuscode = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.CUSCODE.HD', _phieuthu_cushd_id);
			objCustomer.Code = _cuscode;
		}
		// L2PT-28864 end
		// start anhkn them thong tin email
		// L2PT-28833 start sửa fn lấy ttbn
		var _par_benhnhanid = [ _benhnhan.BENHNHANID ];
		var data_bn_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.TTBN.HDDT", _par_benhnhanid.join('$'));
		var _email_info = data_bn_ar[0].EMAIL_CTYBN;
		// L2PT-28833 start sửa fn lấy ttbn
		// end anhkn them thong tin email
		objCustomer.TaxCode = "";
		objCustomer.Address = _benhnhan.DIACHI;
		objCustomer.BankAccountName = "";
		objCustomer.BankName = "";//them cho token site ACATHA
		objCustomer.BankNumber = "";
		objCustomer.Email = _email_info;
		objCustomer.Fax = "";
		objCustomer.Phone = _benhnhan.SDTBENHNHAN;
		objCustomer.ContactPerson = _benhnhan.TEN_NGUOITHAN;
		if (_hospital_id != 1059 && _hospital_id != 996) {
			objCustomer.RepresentPerson = "";
			objCustomer.CusType = '0';
			objCustomer.ContactPerson = _benhnhan.TEN_NGUOITHAN;
		}
		objCustomers.Customer = objCustomer;
		objCusData.Customers = objCustomers;
		var str = JSON.stringify(objCusData);
		str = convertJSon2XML(str);
		var result = ajaxSvc.InvoicesWS.updateCus(str, INVOICES_URL_IMPORT, INVOICES_WS_USER, INVOICES_WS_PWD);
		// L2PT-42086 start
		if (result != "1") {
			initPopupMsg("Lỗi đẩy thông tin BN lên hệ thống HĐĐT: " + result, {
				dlgId : "dlgIdDayTTBN",
				dlgName : "dlgNameDayTTBN",
				dlgTitle : "Lỗi đẩy thông tin BN lên hệ thống HDDT"
			});
		};
		// L2PT-42086 end
	}
	//tuyennx_add_end_ day thong tin bn len he thong HDDT
	// huy phieu thu
	function huyPhieuThu(_lydohuyphieu, _loai) {
		_guihddt = 0;
		if (!_lydohuyphieu || _lydohuyphieu.length == 0) {
			DlgUtil.showMsg("Chưa nhập lý do");
			return false;
		}
		// kiem tra cau hinh day HDDT va loai phieu thu khong phai hoa don
		// va co hoa don kem theo thi khong cho huy
		var grid = $("#" + _gridId_PT);
		var ids = grid.getDataIDs();
		var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
		var _loaiPhieuThuId = grid.jqGrid('getCell', selRowId, 'LOAIPHIEUTHUID');
		// L2PT-23674 start
		//kiem tra Khi đã thực hiện hoàn ứng cho bệnh nhân thì không thể hủy được tạm ứng.
		var VPI_HUY_TAMUNG = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_HUY_TAMUNG');
		if (VPI_HUY_TAMUNG == 1 && _loaiPhieuThuId == 3) {
			var _ret = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.CHECK.HOANUNG', _tiepnhanid);
			if (_ret == 1) {
				DlgUtil.showMsg("Đã hoàn ứng không thể hủy!");
				return false;
			}
		}
		// L2PT-23674 end
		if (VPI_DAY_HOADONDT == 1 && _loaiPhieuThuId != 1) {
			var _phieuthuId_org = grid.jqGrid('getCell', selRowId, 'PHIEUTHUID_ORG');
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var _phieuthuId_org_cur = grid.jqGrid('getCell', id, 'PHIEUTHUID_ORG');
				var _phieuthuId_cur = grid.jqGrid('getCell', id, 'PHIEUTHUID');
				if (_phieuthuId_cur != _phieuthuid && _phieuthuId_org_cur == _phieuthuId_org) {
					DlgUtil.showMsg("Hãy chọn hóa đơn để hủy");
					return false;
				}
			}
		}
		if (VPI_DAY_HOADONDT == 1) {
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.CHECK", [ {
				"name" : "[0]",
				"value" : _phieuthuid
			} ]);
			var rows = JSON.parse(data);
			if (rows != null && rows.length > 0) {
				var check = rows[0]["SYNC_FLAG"];
				if (check && check == 1) {
					// L2PT-23830 start
					var _ch_huyhd_daph = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HUYHD_DAPH');
					if (_ch_huyhd_daph == 1) {
						DlgUtil.showMsg("Hóa đơn đã phát hành, không thể hủy");
						return false;
					}
					// L2PT-50473 start
					else if (_ch_huyhd_daph == 2) {
						var kt_quyen = kiemTraQuyen("HDDT", "", "0");
						if (kt_quyen == 0) {
							DlgUtil.showMsg("Hóa đơn đã phát hành, không có quyền hủy");
							return false;
						}
					}
					// L2PT-50473 end
					// L2PT-23830 end
					_guihddt = 1;
					// L2PT-14068 start: khong dung HDDT VNPT thi khong can check trang thai phat hanh
					if (VPI_HDDT_VT != 0) {
						return checkhuyphieu(_lydohuyphieu, _loai);
					}
					// L2PT-14068 end
					var ret1 = ajaxSvc.InvoicesWS.listInvByCusFkey(_phieuthuid, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
					if (ret1 == "" || ret1.toUpperCase().includes("ERR") || ret1 == "<Data></Data>") {
						DlgUtil.showConfirm("Không lấy được thông tin HDDT, bạn có chắc chắn muốn hủy phiếu ?", function(flag) {
							if (flag) {
								return checkhuyphieu(_lydohuyphieu, _loai);
							} else {
								return false;
							}
						});
					} else {
						var obj = convertXml2JSon(ret1);
						obj = JSON.parse(obj);
						var statusInv = obj.Data.Item.status;
						if (statusInv && statusInv == 1) {
							DlgUtil.showConfirm("Đã phát hành HDDT, bạn có chắc chắn muốn hủy phiếu ?", function(flag) {
								if (flag) {
									return checkhuyphieu(_lydohuyphieu, _loai);
								} else {
									return false;
								}
							});
						} else {
							return checkhuyphieu(_lydohuyphieu, _loai);
						}
					}
				} else {
					return checkhuyphieu(_lydohuyphieu, _loai);
				}
			} else {
				DlgUtil.showConfirm("Không lấy được thông tin phiếu thu, bạn có chắc chắn muốn hủy phiếu ?", function(flag) {
					if (flag) {
						return checkhuyphieu(_lydohuyphieu, _loai);
					} else {
						return false;
					}
				});
			}
		} else {
			return checkhuyphieu(_lydohuyphieu, _loai);
		}
	}
	function checkhuyphieu(_lydohuyphieu, _loai) {
		var _cancelHoaDon = true;
		var obj = new Object();
		obj["PHIEUTHUID"] = _phieuthuid;
		obj["THOIGIANHUYPHIEU"] = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI:SS');
		// L2PT-84833 start: kiểm tra HTTT hủy
		var _httt_huy = $("#cboHTTT_HUY").val();
		if (!_httt_huy || _httt_huy == "") {
			DlgUtil.showMsg("Chưa chọn hình thức thanh toán hủy");
			return;
		}
		obj["HINHTHUCTHANHTOANHUY"] = _httt_huy;
		// L2PT-84833 end
		obj["LYDOHUYPHIEU"] = _lydohuyphieu;
		// L2PT-80276 start
		var _chiho = false;
		if (_loai == 'CHIHO') {
			_chiho = true;
			_loai = "0";
		}
		// L2PT-80276 end
		obj["LOAIHUYPHIEU"] = _loai ? _loai : "0";
//				console.log("____________"+ _phieuthuid);
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I(_sql_huyphieuthu, JSON.stringify(obj));
		if (fl == 1) {
			$("#" + _gridId_DV).hideCol('cb');
			FormUtil.clearForm('ttThuTien', '');
			checkBN();
			$("#cboLOAIPHIEUTHUID").val(1);
			$("#cboHINHTHUCTHANHTOAN").val(1);
			// L2PT-33791 thêm btnLuuVaPH
			// L2PT-53446 thêm btnLuuVaKySo
			setEnabled([ 'btnThem' ], [ 'btnHuyBo', 'btnLuu', 'btnLuuVaPH', 'btnLuuVaKySo', 'txtMABENHNHAN', 'cboHINHTHUCTHANHTOAN', 'txtNGAYLAP', 'calNGAYLAP', 'cboLOAIPHIEUTHUID', 'txtLYDO',
					'txtTLMIENGIAM', 'btnLYDO', 'txtMIENGIAM_PT', 'btnHTTT' ]); // L2PT-19304 btnHTTT
			setEnabled([], [ 'btnHuy' ]);
			//selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
			//var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
			// L2PT-80276 start
			if (fConfig.VPI_CHIHO_QRCODE == '1' && _chiho) {
				// L2PT-18357 start
				var objTPhieuThu = {
					"PHIEUTHUID" : _phieuthuid,
					"KIEU" : "0"
				};
				var tPhieuThu = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.TT.LT', JSON.stringify(objTPhieuThu));
				// L2PT-18357 end
				if (tPhieuThu && tPhieuThu.length > 0) {
					var rPhieuThu = tPhieuThu[0];
					// L2PT-80276: chỉ hoàn qrcode với phiếu tạm ứng và phiếu thu tiền
					if (rPhieuThu.LOAIPHIEUTHUID == '3' || rPhieuThu.LOAIPHIEUTHUID == '6') {
						hoanTraKTM(rPhieuThu.TIEPNHANID, rPhieuThu.PHIEUTHUID, rPhieuThu.DATRA);
					}
				} else {
					DlgUtil.showMsg("Không tìm thấy phiếu thu này");
					return;
				}
			}
			// L2PT-80276 end
			// L2PT-88392 start: goi huy RIS sau khi huy phieu thu
			huyRIS('HUY_PHIEU_THU', "", _user_id, {
				_hospital_id : _hospital_id,
				PHIEUTHUID : _phieuthuid
			});
			// L2PT-88392 end
			// L2PT-51411 goi huy hddt sau khi huy phieu thu thanh cong
			if (VPI_DAY_HOADONDT == 1) {
				if (_guihddt == 1) {
					// L2PT-34670 start: đồng bộ phần hủy hddt theo lần thu
					var grid = $("#" + _gridId_PT);
					var ids = grid.getDataIDs();
					var selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
					var _phieuthuId_org = grid.jqGrid('getCell', selRowId, 'PHIEUTHUID_ORG');
					for (var i = 0; i < ids.length; i++) {
						var id = ids[i];
						var _phieuthuId_org_cur = grid.jqGrid('getCell', id, 'PHIEUTHUID_ORG');
						var _loaiphieuthuId_cur = grid.jqGrid('getCell', id, 'LOAIPHIEUTHUID');
						var _phieuthuId_cur = grid.jqGrid('getCell', id, 'PHIEUTHUID');
						// check co phieu nao trong lan thu da duyet hay khong
						if (_phieuthuId_org_cur == _phieuthuId_org) {
							var _da_duyet = grid.jqGrid('getCell', id, 'DADUYET');
							if (_da_duyet != 0) {
								DlgUtil.showMsg("Đã chốt hóa đơn, không thể hủy phiếu");
								return false;
							}
						}
						// check co phieu nao trong lan thu da duyet hay khong
						if (_phieuthuId_org_cur == _phieuthuId_org && _loaiphieuthuId_cur == 1) {
							// L2PT-23519 L2PT-34673 start
							var VPI_HUYPT_HUY_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HUYPT_HUY_HDDT');
							if (VPI_HUYPT_HUY_HDDT == 0) {
								// L2PT-912 start
								_cancelHoaDon = cancelInv(_phieuthuId_cur, _lydohuyphieu); // L2PT-128915
								// L2PT-912 end
							}
							// L2PT-23519 L2PT-34673 end
						}
					}
					// L2PT-34670 end
					if (_cancelHoaDon) {
						// BVTM-3849 start
						var VPI_HUYPT_HOANQR = jsonrpc.AjaxJson.ajaxCALL_SP_S("COM.CAUHINH", 'VPI_HUYPT_HOANQR');
						if (VPI_HUYPT_HOANQR == '1') {
							hoanTraThanhToanQRCODE(obj, _phieuthuid); // L2PT-51411 tach ham hoan tra qrcode
						}
						// BVTM-3849 end
					} else {
						DlgUtil.showMsg("Hủy hóa đơn điện tử thất bại");
						return false;
					}
				}
			}
			// L2PT-51411 end
			// L2PT-95857 start
			var objRetFastApi = guiDuLieuLenFastApi(_phieuthuid, _hospital_code, true);
			// L2PT-95857 end
			loadGridDataPT(_tiepnhanid);
			loadGridDataDV(_tiepnhanid);
			/*if (VPI_DUYETKT_THUVPDBA == 1 && _benhnhan.TRANGTHAITIEPNHAN_VP == 1) {
				$("#btnDuyet").click();
			}*/
			return true;
		} else if (fl == 0) {
			DlgUtil.showMsg("Bạn không có quyển hủy phiếu thu này");
			return false;
		} else if (fl == -1) {
			DlgUtil.showMsg("Cập nhật không thành công");
			return false;
		} else if (fl == -2) {
			DlgUtil.showMsg("Có dịch vụ viện phí đã thực hiện, không thể hủy phiếu");
			return false;
		} else if (fl == -3) {
			DlgUtil.showMsg("Đã duyệt kế toán, không thể hủy phiếu");
			return false;
		} else if (fl == -4) {
			DlgUtil.showMsg("Đã chốt hóa đơn, không thể hủy phiếu");
			return false;
		} else if (fl == -5) {
			DlgUtil.showMsg("Đã có dịch vụ thu tiền, không thể hủy phiếu hoàn dịch vụ");
			return false;
		} else if (fl == -6) {
			DlgUtil.showMsg("Đã có phiếu hoàn, không thể hủy phiếu");
			return false;
		}
		//tuyennx_add_start_20171221 yc HISL2CORE-654 khong cho huy hoa don khi da gui sang hcsn
		else if (fl == -7) {
			DlgUtil.showMsg("Hóa đơn đã gửi sang phần mềm kế toán không thể hủy phiếu");
			return false;
		}
		//tuyennx_add_end_20171221
		//L2PT-10067 start
		else if (fl == -8) {
			DlgUtil.showMsg("Đã khám không thể hủy công khám");
			return false;
		}
		//L2PT-10067 end
		//L2PT-23674 start
		else if (fl == -9) {
			DlgUtil.showMsg("Đã hoàn tiền vào phiếu thu/ phiếu hoàn ứng, không thể hủy phiếu");
			return false;
		}
		//L2PT-23674 end
		//L2PT-972 start
		else if (fl == -10) {
			DlgUtil.showMsg("Đã chuyển đổi HDDT, không thể hủy phiếu");
		}
		//L2PT-972 end
		//L2PT-11951 start
		else if (fl == -13) {
			DlgUtil.showMsg("Đã duyệt phát hoàn ứng, không thể hủy phiếu");
		}
		//L2PT-11951 end
		//L2PT-127059 start
		else if (fl == -14) {
			DlgUtil.showMsg("Không thể hủy phiếu đã duyệt chi hộ");
		}
		//L2PT-127059 end
		//L2PT-132414 start
		else if (fl == -15) {
			DlgUtil.showMsg("Phiếu đã được hủy, không thể hủy phiếu");
		}
		//L2PT-132414 end
	}
	// L2PT-51411 start
	function hoanTraThanhToanQRCODE(objData, _phieuthuid) {
		// L2PT-18357 start
		var objTPhieuThu = {
			"PHIEUTHUID" : _phieuthuid,
			"KIEU" : "0"
		};
		var tPhieuThu = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.TT.LT', JSON.stringify(objTPhieuThu));
		// L2PT-18357 end
		if (tPhieuThu && tPhieuThu.length > 0) {
			var rPhieuThu = tPhieuThu[0];
			console.log(rPhieuThu.ORDERID);
			if (rPhieuThu.HINHTHUCTHANHTOAN == 12) {
				if (rPhieuThu.ORDERID) {
					console.log(rPhieuThu.MADONVI);
					if (!rPhieuThu.MADONVI) {
						DlgUtil.showMsg("Đơn vị thanh toán không tồn tại");
						return;
					}
					// BVTM-6997 start: hủy vpnt Pay
					if (rPhieuThu.MADONVI == 'VnptPayWallet') {
						huyQRCODE(objData, rPhieuThu.PHIEUTHUID);
						return;
					}
					// BVTM-6997 end	
					var result = refundQrCode(rPhieuThu.PHIEUTHUID, rPhieuThu.ORDERID, rPhieuThu.TONGTIEN, rPhieuThu.MALOAI, rPhieuThu.MADONVI);
					console.log(result);
					if (result.code == "00") {
						DlgUtil.showMsg(result.message);
					} else {
						DlgUtil.showMsg("code: " + result.code + ", message: " + result.message);
						return;
					}
				} else {
					DlgUtil.showMsg("Phiếu thu không có ID giao dịch");
					return;
				}
			}
			// BVTM-6997 start: hủy vpnt Pay
			else if (rPhieuThu.HINHTHUCTHANHTOAN == 10) {
				huyQRCODE(objData, rPhieuThu.PHIEUTHUID);
				return;
			}
			// BVTM-6997 end
		} else {
			DlgUtil.showMsg("Không tìm thấy phiếu thu này");
			return;
		}
	}
// L2PT-51411 end
// BVTM-6997 start: hủy vpnt Pay
	function huyQRCODE(objData, phieuThuId) {
		var arrOrder = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.LAYTTPT.VNPTPAY", phieuThuId + "$" + 0);
		if (arrOrder && arrOrder.length > 0 && parseInt(arrOrder[0].DATRA) > 0) {
			objData.LOAIGIAODICH = "1";
			payRefund(objData, arrOrder[0]);
		} else {
			DlgUtil.showMsg("Không lấy được thông tin phiếu thu");
		}
	}
// BVTM-6997 end
// L2PT-912 start
	function cancelInv(_phieuthuid_hddt, reasonDelete) { // L2PT-128915
		// L2PT-6086
		var kt_quyen_hddt = kiemTraQuyen("HDDT", "VPI_QUYEN_HDDT", "0");
		if (kt_quyen_hddt == '0') {
			DlgUtil.showMsg("Bạn không có quyền thao tác với HĐĐT");
			return;
		} else if (kt_quyen_hddt == '-1') {
			DlgUtil.showMsg("Có lỗi xảy ra khi kiểm tra quyền thao tác với HĐĐT");
			return;
		}
		// L2PT-6086
		if (VPI_HDDT_VT == 1) {
			return cancelInvVT(_phieuthuid_hddt, reasonDelete); // L2PT-128915
		}
		// L2PT-98874 L2PT-14600 start
		if (VPI_HDDT_VT == 2) {
			cancelInvMISA(_phieuthuid_hddt)
			return;
		}
		// L2PT-98874 L2PT-14600 end
		// duonghn chuyen tu snhnm 070322
		if (_hospital_id == 915 || _hospital_id == 978) {
			var ret_huy_thanhtoan = ajaxSvc.InvoicesWS.UnConfirmPaymentFkey(_phieuthuid, INVOICES_URL_CANCEL, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD);
		}
		// L2PT-95812 start
		if (fConfig.VPI_HUYHDDT_SINHBIENBAN == '1') {
			ret = ajaxSvc.InvoicesWS.cancelInvSignFileNoPay(_phieuthuid_hddt, INVOICES_URL_CANCEL, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD);
		} else {// duonghn chuyen tu snhnm 070322
			ret = ajaxSvc.InvoicesWS.cancelHoaDon(_phieuthuid_hddt, INVOICES_URL_CANCEL, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD);
		}
		// L2PT-95812 end
		// L2PT-18932 start
		if (ret == "OK:") {
			var _par = [ "0", "", "", "", _phieuthuid_hddt ];
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
			if (_return == '-1') {
				DlgUtil.showMsg("Cập nhật trạng thái hủy HDDT không thành công");
				return false;
			}
			return true;
		} else {
			// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
			DlgUtil.showMsg(thongbao_loi('HDDT_HUY_PH', ret));
			// L2PT-37815 end
			return false;
		}
		// L2PT-18932 end
	}
	function cancelInvVT(_phieuthuid_hddt, reasonDelete) { // L2PT-128915
		var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', _phieuthuid_hddt);
		if (arr_pt && arr_pt.length > 0) {
			var rowPT = arr_pt[0];
			var supplierTaxCode = INVOICES_WS_TAX_CODE; // L2PT-5786
			var invoiceNo = rowPT.INVOICENO;
			var strIssueDate = rowPT.STRISSUEDATE;
			var additionalReferenceDesc = "huy";
			var additionalReferenceDate = rowPT.ADDITIONALREFERENCEDATE;
			// L2PT-25725 start
			if (VPI_HDDT_VIETTEL_V2 == '1') {
				strIssueDate = new Date(strIssueDate.substring(0, 4), strIssueDate.substring(4, 6) - 1, strIssueDate.substring(6, 8), strIssueDate.substring(8, 10), strIssueDate.substring(10, 12),
						strIssueDate.substring(12, 14)).getTime();
				additionalReferenceDate = new Date(additionalReferenceDate.substring(0, 4), additionalReferenceDate.substring(4, 6) - 1, additionalReferenceDate.substring(6, 8),
						additionalReferenceDate.substring(8, 10), additionalReferenceDate.substring(10, 12), additionalReferenceDate.substring(12, 14)).getTime();
			}
			// L2PT-43054 start: Lấy strIssueDate từ site hóa đơn
			// L2PT-46267 start: thêm cấu hình
			if (fConfig.VPI_HDDT_VIETTEL_V3 == '1') {
				//var transactionUuid = rowPT.TRANSACTIONID_VT;
				var transactionUuid = _hospital_code + _phieuthuid_hddt.padStart(36 - _hospital_code.length, '0');
				var requestParameter = 'supplierTaxCode=' + supplierTaxCode + '&transactionUuid=' + transactionUuid;
				var retSearchInvoiceByTrans = ajaxSvc.InvoicesWS.searchInvoiceByTransactionUuid(requestParameter);
				try {
					var objSearchInvoiceByTrans = JSON.parse(retSearchInvoiceByTrans);
					if (objSearchInvoiceByTrans.errorCode == null) {
						strIssueDate = objSearchInvoiceByTrans.result[0].issueDate;
					} else {
						DlgUtil.showMsg('Hủy HDDT - không tìm thấy hóa đơn này trên site HDDT');
						return false;
					}
				} catch (err) {
					DlgUtil.showMsg('Hủy HDDT - có lỗi xảy ra: ' + err);
					return false;
				}
			}
			// L2PT-46267 end
			// L2PT-43054 end
			// L2PT-25725 end
			var paramTrans = "supplierTaxCode=" + supplierTaxCode + "&invoiceNo=" + invoiceNo + "&strIssueDate=" + strIssueDate + "&additionalReferenceDesc=" + additionalReferenceDesc +
					"&additionalReferenceDate=" + additionalReferenceDate;
			// L2PT-128915 start
			if (reasonDelete) {
				paramTrans += "&reasonDelete=" + reasonDelete;
			}
			// L2PT-128915 end
			var retCancelHDDTviettel = ajaxSvc.InvoicesWS.cancelTransactionInvoice(paramTrans);
			if (retCancelHDDTviettel.includes("ERR_CANCEL_REQ")) {
				DlgUtil.showMsg('Lỗi hủy hóa đơn điện tử');
			} else {
				var objRetCancelHDDTviettel = JSON.parse(retCancelHDDTviettel);
				if (objRetCancelHDDTviettel.errorCode == null) {
					var _par = [ "0", "", "", "", _phieuthuid_hddt ];
					var _returnCancelHDDTviettel = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
					DlgUtil.showMsg("Hủy hóa đơn điện tử thành công");
					return true;
				} else {
					DlgUtil.showMsg(objRetCancelHDDTviettel.description + " ( " + objRetCancelHDDTviettel.errorCode + " )");
				}
			}
		} else {
			DlgUtil.showMsg("Không tìm thấy hóa đơn này");
		}
		return false;
	}
	// L2PT-912 end
	// L2PT-98874 L2PT-14600 end
	function cancelInvMISA(_phieuthuid_hddt) {
		var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', _phieuthuid_hddt);
		if (arr_pt && arr_pt.length > 0) {
			var param = new Object();
			// L2PT-17896 start
			if (INVOICE_XML_TYPE == '1') {
				param = [ {
					"TransactionID" : arr_pt[0].TRANSACTIONID_VT,
					"RefDate" : arr_pt[0].ISSUED_DATE,
					"CancelReason" : "HUY HD"
				} ];
			} else {
				param = {
					"TransactionID" : arr_pt[0].TRANSACTIONID_VT,
					"RefDate" : arr_pt[0].ISSUED_DATE,
					"RefNo" : arr_pt[0].REFNO,
					"DeletedReason" : "HUY HD"
				};
			}
			// L2PT-17896 end
			var retCancelMISA = ajaxSvc.InvoicesWS.deleteInvoices(param);
			try {
				var retCancelMISAObj = JSON.parse(retCancelMISA);
				console.log(retCancelMISAObj);
				if (retCancelMISAObj.errorCode == "0") {
					var _par = [ "0", "", "", "", _phieuthuid_hddt ];
					var _retUdpCancelHDDTMISA = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
					DlgUtil.showMsg("Hủy hóa đơn điện tử thành công");
				} else {
					DlgUtil.showMsg("Mã lỗi " + retCancelMISAObj.errorCode + ": " + retCancelMISAObj.message);
				}
			} catch (e) {
				console.log(e);
			}
		} else {
			DlgUtil.showMsg("Không tìm thấy hóa đơn này");
		}
	}
	// L2PT-98874 L2PT-14600 end
	function hoantien() {
		if (flagLoading)
			return;
		if (VPI_XACNHAN_HOANPHIEU == 1) {
			DlgUtil.showConfirm("Xác nhận hoàn phiếu", function(flag) {
				if (flag) {
					checkhoantien();
				}
			});
		} else {
			checkhoantien();
		}
	}
	function checkhoantien() {
		// check khoa phong
		if (!_khoa_id || !_phong_id || _khoa_id == 0 || _phong_id == 0) {
			DlgUtil.showMsg("Chưa thiết lập khoa phòng");
			return;
		}
		var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI01T001.HOANDV', _phieuthuid + "$" + _khoa_id + "$" + _phong_id);
		if (fl > 0) {
			DlgUtil.showMsg("Cập nhật thành công");
			_phieuthuid = fl;
			loadGridDataPT(_tiepnhanid, false, true);
		} else if (fl == 0) {
			DlgUtil.showMsg("Bạn không có quyển hoàn phiếu thu này");
			return false;
		} else if (fl == -1) {
			DlgUtil.showMsg("Cập nhật không thành công");
			return false;
		} else if (fl == -2) {
			DlgUtil.showMsg("Có dịch vụ viện phí đã thực hiện, không thể hủy phiếu");
			return false;
		} else if (fl == -3) {
			DlgUtil.showMsg("Đã duyệt kế toán, không thể hoàn phiếu");
			return false;
		} else if (fl == -4) {
			DlgUtil.showMsg("Đã chốt hóa đơn, không thể hoàn phiếu");
			return false;
		} else if (fl == -5) {
			DlgUtil.showMsg("Đã có dịch vụ thu tiền, không thể hủy phiếu hoàn dịch vụ");
			return false;
		} else if (fl == -6) {
			DlgUtil.showMsg("Đã có phiếu hoàn dịch vụ, không thể hoàn phiếu");
			return false;
		}
		//tuyennx_add_start_20171221 yc HISL2CORE-654 khong cho huy hoa don khi da gui sang hcsn
		else if (fl == -7) {
			DlgUtil.showMsg("Hóa đơn đã gửi sang phần mềm kế toán không thể hoàn ");
			return false;
		}
		//tuyennx_add_end_20171221 
		else if (fl == -8) {
			DlgUtil.showMsg("Không có yêu cầu hoàn phiếu hoặc công khám chưa bắt đầu khám");
			return false;
		} else if (fl == -9) {
			DlgUtil.showMsg("Đã duyệt kế toán, không thể hoàn phiếu");
			return false;
		} else if (fl == -10) {
			DlgUtil.showMsg("Có dịch vụ chưa thu tiền, không thể hoàn phiếu");
			return false;
		} else {
			DlgUtil.showMsg("Có lỗi xảy ra");
			return false;
		}
	}
	function onFocus(fields) {
		var type = typeof fields;
		if (type.toLowerCase() == "string") {
			$("#" + id).focus();
			$("#" + id).css('background-color', 'yellow');
		} else if (type.toLowerCase() == "object") {
			var id = "";
			for (var i = 0; i < fields.length; i++) {
				id = fields[i];
				$("#" + id).css('background-color', 'yellow');
			}
			$("#" + id).focus();
		}
	}
	function setEnabled(_ena, _dis) {
		for (var i = 0; i < _ena.length; i++) {
			$("#" + _ena[i]).attr('disabled', false);
		}
		for (var i = 0; i < _dis.length; i++) {
			$("#" + _dis[i]).attr('disabled', true);
		}
	}
	function stringToDate(date) {
		var parts = date.split("/");
		return new Date(parts[2], parts[1] - 1, parts[0]);
	}
	function stringToDateTime(date) {
		var parts = date.split("/");
		var tails = parts[2].split(" ");
		var times = tails[1].split(":");
		var ret = new Date(tails[0], parts[1] - 1, parts[0], times[0], times[1], times[2]);
		return ret;
	}
	function noiDungThu(obj, nhomthanhtoan) {}
	function clone(obj) {
		if (null == obj || "object" != typeof obj)
			return obj;
		var copy = obj.constructor();
		for ( var attr in obj) {
			if (obj.hasOwnProperty(attr))
				copy[attr] = obj[attr];
		}
		return copy;
	}
/*func		: 
 * */
	function getXmlTo_Medical(data_ar) {
		if (data_ar && data_ar.length > 0) {
			var _row = data_ar[0];
			var ret = ajaxSvc.PortalWS.WS_call("guiHSXV", _row.HIS_USER_CSYT, _row.HIS_PASS_CSYT, unescape(_row.XMLBN));
			var resultText = $(ret).find("Error > Error_Message").text();
			var result = parseInt($(ret).find("Error > Error_Number").text());
			var trans_id = $(ret).find("Transaction_ID").text();
			if (result !== 0) {
				DlgUtil.showMsg(resultText);
			}
			var objCDLYT = new Object();
			objCDLYT.MA_LK = _tiepnhanid + "";
			objCDLYT.CONG_DLYT = result === 0 ? "1" : "2";
			objCDLYT.BHXH = "";
			objCDLYT.KETQUA = ret;
			objCDLYT.MA_KET_QUA = result + "";
			objCDLYT.MA_GIAO_DICH = trans_id;
			var ret = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D061.02", JSON.stringify(objCDLYT));
			if (ret == -1) {
				DlgUtil.showMsg("Cập nhật trạng thái gửi cổng DLYT không thành công");
			}
			return result === 0 ? "1" : "0";
		} else {
			DlgUtil.showMsg("Không tìm thấy hồ sơ, gửi cổng BYT không thành công");
			return null;
		}
	}
	function getXmlTo_Insr(data_ar) {
		var objBHXH = new Object();
		objBHXH.MA_LK = _tiepnhanid + "";
		objBHXH.CONG_DLYT = "";
		objBHXH.CONG_BHXH = "";
		objBHXH.KETQUA = "";
		objBHXH.MA_KET_QUA = "";
		objBHXH.MA_GIAO_DICH = "";
		if (data_ar && data_ar.length > 0) {
			var ret = "";
			try {
				var _row = data_ar[0];
				/*
				objBHXH.KETQUA = _row.XMLBN;
				var retLogGui = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D061.02", JSON.stringify(objBHXH));
				if (retLogGui == -1) {
					DlgUtil.showMsg("Cập nhật trạng thái gửi cổng BHXH không thành công");
				}
				*/
				if (VPI_GUI_BH == 1) {
					ret = ajaxSvc.InsrWS.sendInsr4210("3", unescape(_row.XMLBN), _row.HIS_USER_INSR, _row.HIS_PASS_INSR, _row.TINH_ID, _row.MA_CSKCB, _row.HUYEN_ID, _row.LOAI_KY_GD);
				} else {
					ret = ajaxSvc.InsrWS.sendInsr("3", unescape(_row.XML_DATA), _row.HIS_USER_INSR, _row.HIS_PASS_INSR, _row.TINH_ID, _row.TEN_CSYT, _row.HUYEN_ID, _row.LOAI_KY_GD);
				}
				var result = $.parseJSON(ret);
				if (result.maKetQua == "200") {
					if (VPI_KTKQ_GUI == 0 || VPI_GUI_BH != 1) {
						DlgUtil.showMsg("Gửi hồ sơ tới cổng Bảo hiểm xã hội thành công. Mã giao dịch để tra cứu trên cổng là : " + result.maGiaoDich, function() {}, VPI_WAIT_MSG); // L2PT-12176 L2PT-54506
					} else {
						var retKQG = ajaxSvc.InsrWS.layKQGui(_row.HIS_USER_INSR, _row.HIS_PASS_INSR, _row.HIS_USER_CSYT, result.maGiaoDich);
//        			console.log(ret);
						if (retKQG && retKQG != "") {
							var jret = $.parseJSON(retKQG);
							var trangThaiHS = 0;
							var msgLoi = "";
							if (jret.maKetQua && jret.maKetQua == "200") {
								var dsLoi = jret.dsLoi;
								if (dsLoi.length == 0) {
									trangThaiHS = 1;
									DlgUtil.showMsg("Gửi hồ sơ tới cổng Bảo hiểm xã hội thành công. Mã giao dịch để tra cứu trên cổng là : " + result.maGiaoDich, function() {}, VPI_WAIT_MSG); // L2PT-12176 L2PT-54506
								} else {
									trangThaiHS = 2;
									for (var i = 0; i < dsLoi.length; i++) {
										var loi = dsLoi[i];
										msgLoi += msgLoi + "Mã lỗi: " + loi.maLoi + " - " + loi.moTaLoi + ".&#13;&#10;";
									}
									$('#txtNOIDUNGLOI').html(msgLoi);
									DlgUtil.open("dlgLOI");
								}
								var retLogTraKQG = jsonrpc.AjaxJson.ajaxCALL_SP_I("UPDATE_KQ_GUI", _tiepnhanid + "$" + trangThaiHS + "$" + msgLoi + "$" + result.maGiaoDich);
								if (retLogTraKQG == -1) {
									DlgUtil.showMsg("Cập nhật trạng thái không thành công");
								}
							} else if (jret.maKetQua == "401") {
								DlgUtil.showMsg("Lỗi xác thực cổng BHXH");
							} else if (jret.maKetQua == "500") {
								DlgUtil.showMsg("Lỗi từ cổng BHXH");
							} else {
								DlgUtil.showMsg("Kiểm tra giao dịch không thành công");
							}
						}
					}
				} else if (result.maKetQua == "401") {
					DlgUtil.showMsg("Lỗi xác thực cổng BHXH");
				} else if (result.maKetQua == "500") {
					DlgUtil.showMsg("Lỗi từ cổng BHXH");
				} else {
					DlgUtil.showMsg("Gửi BHXH không thành công, mã kết quả: " + result.maKetQua);
				}
				objBHXH.MA_LK = _tiepnhanid + "";
				objBHXH.CONG_DLYT = "";
				objBHXH.CONG_BHXH = result.maKetQua == "200" ? "1" : "2";
				objBHXH.KETQUA = ret;
				objBHXH.MA_KET_QUA = result.maKetQua;
				objBHXH.MA_GIAO_DICH = result.maGiaoDich;
			} catch (err) {
				objBHXH.KETQUA = ret + ' - ' + err.message;
				DlgUtil.showMsg("Có lỗi xảy ra, gửi cổng BHXH không thành công");
			}
		} else {
			objBHXH.KETQUA = "Không tìm thấy hồ sơ, gửi cổng BHXH không thành công";
			DlgUtil.showMsg("Không tìm thấy hồ sơ, gửi cổng BHXH không thành công");
		}
		var retLogKQG = jsonrpc.AjaxJson.ajaxCALL_SP_I("NTU02D061.02", JSON.stringify(objBHXH));
		if (retLogKQG == -1) {
			DlgUtil.showMsg("Cập nhật trạng thái gửi cổng BHXH không thành công");
		}
	}
	function val_m(ctl, val) {
		if (!val && val != 0)
			return;
		var jctl = $("#" + ctl);
		jctl.val(vienphi_tinhtien.convertNumToCurrency(val, true));
	}
	function get_val_m(ctl) {
		var jctl = $("#" + ctl);
		var val = jctl.val();
		if (!val && val != 0)
			return null;
		var ret = val.replace(/\./g, '');
		ret = ret.replace(',', '.');
		if (!isNaN(ret) && ret.indexOf('.') != ret.length - 1)
			val = ret;
		return val;
	}
	function make_class(obj, name) {
		var keys = Object.keys(obj);
		var ret = "public class " + name + "\n	{\n";
		for (var i = 0; i < keys.length; i++) {
			var key = keys[i];
			ret += "		public string " + key + "{ get; set; }\n";
		}
		return ret + "	}";
	}
//dem so phan tu co gia tri cho truoc cua mang
	function countElt(arr, val) {
		var ret = 0;
		for (var i = 0; i < arr.length; i++) {
			if (val == arr[i])
				ret++;
		}
		return ret;
	}
// L2PT-11665 start: đồng bộ với form 2
	function duyetthuchienCLS() {
		dlgPopup = DlgUtil.buildPopupGrid("dlgDuyetCSL", _gridId_DV2, "Duyệt thực hiện CLS", 1024, 512);
		GridUtil.init(_gridId_DV2, "1000", "350", "", true, _gridHeader_DV_CLS, false);
		var _sql_par = [ {
			name : "[0]",
			value : _tiepnhanid
		} ];
		_fl_tinh = false;
		GridUtil.loadGridBySqlPage(_gridId_DV2, "VPI01T001.20", _sql_par, function() {
			var ids = $("#" + _gridId_DV2).getDataIDs();
			for (var i = 0; i < ids.length; i++) {
				var id = ids[i];
				var row = $("#" + _gridId_DV2).jqGrid('getRowData', id);
				if (row.DADUYETTHUCHIENCANLAMSANG == 1) {
					// L2PT-11665 start: thêm icon
					var _icon = '<center><img src="' + _opts.imgPath[4] + '" width="15px"></center>';
					$("#" + _gridId_DV2).jqGrid('setCell', id, 'DUYET_CLS', _icon);
					// L2PT-11665 end
					$("#" + _gridId_DV2).jqGrid('setRowData', id, "", {
						color : 'blue'
					});
				}
			}
			var _total = $("#" + _gridId_DV2).jqGrid('getGridParam', 'records');
			if (_total > 0) {
				dlgPopup.open();
			} else {
				DlgUtil.showMsg("Không có dịch vụ nào cần duyệt CLS");
			}
		});
		var btnOK = $('<input class="btn btn-sm btn-primary" id="btn_DSS_OK" type="button" style="position:absolute;bottom:10px;left:10px" value="Duyệt CLS" />');
		var btnGoDuyet = $('<input class="btn btn-sm btn-primary" id="btn_DSS_GODUYET" type="button" style="position:absolute;bottom:10px;left:450px" value="Gỡ duyệt CLS" />');
		var btnClose = $('<input class="btn btn-sm btn-primary" id="btn_DSS_Close" type="button" style="position:absolute;bottom:10px;right:10px" value="Đóng" />');
		$('#dlgDuyetCSL').append(btnOK);
		$('#dlgDuyetCSL').append(btnGoDuyet);
		$('#dlgDuyetCSL').append(btnClose);
		btnOK.click(function() {
			duyetcls(1);
		});
		btnGoDuyet.click(function() {
			duyetcls(0);
		});
		function duyetcls(mode) {
			var _selRowIds = $("#" + _gridId_DV2).jqGrid('getGridParam', 'selarrrow');
			if (_selRowIds.length > 0) {
				var _dsdvId = "";
				for (var i = 0; i < _selRowIds.length; i++) {
					var _selRowId = _selRowIds[i];
					var _rowData = $("#" + _gridId_DV2).jqGrid('getRowData', _selRowId);
					_dsdvId += _rowData.DICHVUKHAMBENHID + ",";
				}
				_dsdvId = _dsdvId.slice(0, -1);
				var fl = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI01T001.19", _dsdvId + "$" + mode);
				if (fl == 1) {
					DlgUtil.showMsg((mode == 1 ? "Duyệt " : "Gỡ duyệt ") + "thực hiện CLS thành công");
					GridUtil.loadGridBySqlPage(_gridId_DV2, "VPI01T001.20", _sql_par);
				} else {
					DlgUtil.showMsg("Có lỗi xảy ra");
				}
				$("#" + _gridId_DV2).jqGrid('trigger', 'reloadGrid');
			} else {
				DlgUtil.showMsg("Chưa chọn dịch vụ");
			}
		};
		btnClose.click(function() {
			dlgPopup.close();
		});
		GridUtil.setGridParam(_gridId_DV2, {
			onSelectRow : function(id) {
				GridUtil.unmarkAll(_gridId_DV2);
				var _selRowIds = $("#" + _gridId_DV2).jqGrid('getGridParam', 'selarrrow');
				for (var i = 0; i < _selRowIds.length; i++) {
					GridUtil.markRow(_gridId_DV2, _selRowIds[i]);
				}
			}
		});
	}
// L2PT-11665 end
// L2PT-1555 start
	function payRefundArr(objData, arrHoanUng) {
		for (var i = 0; i < arrHoanUng.length; i++) {
			var rs = payRefund(objData, arrHoanUng[i]);
			if (!rs) {
				DlgUtil.showMsg('ERR: Return NULL');
				return false;
			}
			// Error
			if (rs.responseCode != '00') {
				DlgUtil.showMsg('Lỗi [' + rs.responseCode + ']: ' + rs.responseMessage);
			}
			return false;
		}
	}
	function payRefund(objData, objHoanUng) {
		console.log('paymentRefund >>', objData);
		try {
			var oData = {
				orderId : objHoanUng.ORDERID,
				amount : objHoanUng.HOANTRA,
				refundType : objHoanUng.REFUNDTYPE,
				description : 'Hoan tra VNPT Pay',
				txnId : objHoanUng.TXNID,
				qrTxnId : objHoanUng.QRTXNID,
				benhnhanId : objHoanUng.BENHNHANID,
				data : JSON.stringify(objData)
			};
			_rs = null;
			var url = '/vnpthis/api/payment/vnptpay/refund?uuid=' + RestInfo.uuid;
			var request = $.ajax({
				url : url,
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				beforeSend : function(xhr) {}
			});
			request.done(function(_response) {
				_rs = _response;
				console.log('_response', _response);
			});
			request.fail(function(jqXHR, textStatus) {
				DlgUtil.showMsg("ERR: " + textStatus);
				return false;
			});
			console.log('Payment.paymentRefund >> ', _rs);
			// Response Null
			if (!_rs) {
				DlgUtil.showMsg('ERR: Return NULL');
				return false;
			}
			// JWT expired 
			if (_rs.responseCode == 0) {
				// Thành công
				return _rs;
			}
			// Error
			if (_rs.responseCode != 0) {
				DlgUtil.showMsg('Lỗi: ' + _rs.responseMessage);
			}
			return _rs;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
// L2PT-1555 end
// HieuBD PaymentGateway VNPT PAY
	function paymentGenQrCode(objData) {
		console.log('paymentGenQrCode >>', objData);
		try {
			var rAmount = fConfig.VPI_LAMTRON_TTKTM == '1' ? Math.round(objData.THUCTHU) : Math.ceil(objData.THUCTHU); // L2PT-123128
			var oData = {
				orderID : '',
				amount : rAmount, // L2PT-123128
				expDate : '',
				purpose : 'Thanh toan VNPT Pay',
				items : JSON.stringify(objData.DSPHIEU),
				checksum : '',
				data : JSON.stringify(objData)
			};
			_rs = null;
			var url = '/vnpthis/api/payment/genqrcode?uuid=' + RestInfo.uuid + '&benhnhanid=' + objData.BENHNHANID;
			var request = $.ajax({
				url : url,
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				beforeSend : function(xhr) {}
			});
			request.done(function(_response) {
				_rs = _response;
				console.log('_response', _response);
			});
			request.fail(function(jqXHR, textStatus) {
				DlgUtil.showMsg("ERR: " + textStatus);
				return false;
			});
			console.log('Payment.paymentGenQrCode >> ', _rs);
			// Response Null
			if (!_rs) {
				DlgUtil.showMsg('ERR: Return NULL');
				return false;
			}
			// JWT expired 
			if (_rs.responseCode == 0) {
				// Thành công
				return _rs;
			}
			// Error
			if (_rs.responseCode != 0) {
				DlgUtil.showMsg('Lỗi: ' + _rs.responseMessage);
			}
			return _rs;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
// End HieuBD PaymentGateway VNPT PAY
// HieuBD PaymentGateway VNPT PAY
	function genQrCode(objData, _hinhThuc, _donViThanhToan) {
		// L2PT-39523 start
		if (fConfig.VPI_GEN_QRCODE_MERCHANT == 1) {
			initPayment();
			var objGetMerchant = {
				MALOAI : _hinhThuc,
				MADONVI : _donViThanhToan
			}
			var arrMerchant = jsonrpc.AjaxJson.ajaxCALL_SP_O('MERCHANT.CODE', JSON.stringify(objGetMerchant));
			if (!arrMerchant) {
				return {
					code : "999",
					message : "Có lỗi khi lấy thông tin merchant"
				}
			} else if (arrMerchant.length == 0) {
				return {
					code : "998",
					message : "Chưa khai báo merchant code"
				}
			} else if (arrMerchant.length > 1) {
				return {
					code : "997",
					message : "Có nhiều hơn 1 merchant coden của ngân hàng đang cho được khai báo cho thu ngân này"
				}
			} else {
				return PaymentSvc.genQRCodeWithMerchant(_benhnhan, objData, _hinhThuc, _donViThanhToan, arrMerchant[0].MERCHANTID, atob(arrMerchant[0].MERCHANTPASS));
			}
			return;
		}
		// L2PT-39523 end
		try {
			var rAmount = fConfig.VPI_LAMTRON_TTKTM == '1' ? Math.round(objData.THUCTHU) : Math.ceil(objData.THUCTHU); // L2PT-123128
			var oData = {
				orderID : '',
				amount : rAmount, // L2PT-123128
				expDate : '',
				purpose : 'Thanh toan',
				items : objData.DSPHIEU,
				checksum : '',
				diachi : _benhnhan.DIACHI, // L2PT-31941
				tenkhachhang : removeVietnameseTones(_benhnhan.TENBENHNHAN), // L2PT-31941
				benhnhanid : _benhnhan.BENHNHANID,
				donvithanhtoan : _donViThanhToan,
				hinhthuc : _hinhThuc,
				data : objData
			};
			console.log(oData);
			var result = '';
			$.ajax({
				url : '/vnpthis/api/payment/qrcode',
				beforeSend : function(xhr) {
					xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
				},
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				timeout : 3 * 60 * 1000,
				success : function(res) {
					result = res;
				},
				error : function() {}
			});
			return result;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
	function refundQrCode(phieuThuId, orderId, soTien, _loaiGiaoDich, _donViThanhToan) {
		try {
			var rAmount = fConfig.VPI_LAMTRON_TTKTM == '1' ? Math.round(soTien) : Math.ceil(soTien); // L2PT-123128
			var oData = {
				orderID : orderId,
				donvithanhtoan : _donViThanhToan,
				loaigiaodich : _loaiGiaoDich,
				benhnhanid : _benhnhan.BENHNHANID,
				phieuthuid : phieuThuId,
				sotien : rAmount
			// L2PT-123128
			// fix QRCODE
			};
			console.log(oData);
			var result = '';
			$.ajax({
				url : '/vnpthis/api/payment/refund',
				beforeSend : function(xhr) {
					xhr.setRequestHeader('Authorization', jsonrpc.AjaxJson.uuid);
				},
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				timeout : 3 * 60 * 1000,
				success : function(res) {
					result = res;
				},
				error : function() {}
			});
			return result;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
// End HieuBD PaymentGateway VNPT PAY
// Start HungND BIDVPayment
	function pmmSale(orderId, hisUser, uuid, posterminalid, amount, description, opt) { // L2PT-102404
		localStorage.setItem('IDPOST', posterminalid);
		var url = VPI_PAY_PMM_URL + '/api/v1.0/public/test/payment/sale';
		//var url = 'http://payment.vncare.vn:8088/api/v1.0/public/test/payment/sale';
		try {
			// L2PT-108719 start
			if (opt.codeApi == 'SEND_IPOS') {
				var oData = {
					orderId : orderId,
					posterminalid : posterminalid,
					qr_string : opt.qr_string,
					qr_title : opt.qr_title,
					qr_message : opt.qr_message
				}
			} else if (opt.codeApi == 'NOTIFY_IPOS') {
				url = VPI_PAY_PMM_URL + '/api/v1.0/public/test/payment/complete-ipos';
				var oData = {
					posterminalid : posterminalid,
					qr_string : "",
					qr_title : "",
					qr_message : ""
				}
			} else if (opt.codeApi == 'CANCEL_IPOS') {
				url = VPI_PAY_PMM_URL + '/api/v1.0/public/test/payment/cancel-ipos';
				var oData = {
					posterminalid : posterminalid,
					qr_string : "",
					qr_title : "",
					qr_message : ""
				}
			} else
			// L2PT-108719 end
			// L2PT-102404 start
			if (opt.showQr == 1) {
				var oData = {
					orderId : orderId,
					posterminalid : posterminalid,
					showQr : 1,
					cmd : "show_qr",
					qr_string : opt.qr_string,
					qr_title : opt.qr_title,
					qr_message : opt.qr_message
				}
			} else {
				var rAmount = fConfig.VPI_LAMTRON_TTKTM == '1' ? Math.round(amount) : Math.ceil(amount); // L2PT-44297
				var oData = {
					orderId : orderId,
					hisUser : hisUser,
					uuid : uuid,
					posterminalid : posterminalid,
					amount : rAmount, // L2PT-44297
					description : description,
					f1 : description,
					f2 : hisUser,
					f3 : opt.f3,
					f4 : opt.f4,
					f5 : opt.f5
				};
			}
			// L2PT-102404 end
			_rs = null;
			var request = $.ajax({
				url : url,
				type : "POST",
				data : JSON.stringify(oData),
				contentType : 'application/json; charset=utf-8',
				dataType : "json",
				async : false,
				timeout : 3 * 60 * 1000,
				beforeSend : function(xhr) {}
			});
			request.done(function(_response) {
				_rs = _response;
				console.log('BIDVPayment|response', _response);
			});
			request.fail(function(jqXHR, textStatus) {
				DlgUtil.showMsg("ERR: " + textStatus);
				return false;
			});
			console.log('Payment.paymentGenQrCode >> ', _rs);
			// Response Null
			if (!_rs) {
				DlgUtil.showMsg('ERR: Return NULL');
				return false;
			}
			// JWT expired
			if (_rs.responseCode == 0) {
				// Thành công
				return _rs;
			}
			return _rs;
		} catch (error) {
			DlgUtil.showMsg('Lỗi! Đã có lỗi xảy ra tại server ' + error);
		}
	}
// End HungND Agribank Payment
	function guiHDDT(KH_DN) { // L2PT-19666 : KH_DN
		if (flagLoading)
			return;
		selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
		var _phieuthuid_hddt = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
		var _nguoilapphieu = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'NGUOIDUNGID');
		var _datra = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'DATRA');
		// L2PT-54003: Gửi HDDT không đồng
		if (_datra != 0 || fConfig.VPI_GUIHDDT_KHONGDONG == "1") {
			if (!_user_id || _user_id == 0) {
				DlgUtil.showMsg("Chưa đăng nhập");
				return;
			}
			if (VPI_QUYEN_GUIHDDT == 1 && _user_id != _nguoilapphieu) {
				DlgUtil.showMsg("Chỉ người lập phiếu mới có thể gửi HĐĐT");
				return;
			}
			var data = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.CHECK", [ {
				"name" : "[0]",
				"value" : _phieuthuid_hddt
			} ]);
			var rows = JSON.parse(data);
			if (rows != null && rows.length > 0) {
				var check = rows[0]["SYNC_FLAG"];
				if (check == 1) {
					DlgUtil.showMsg("Hóa đơn đã được gửi");
				}
				// L2PT-11280;L2PT-11279 start
				else if (VPI_CB_GUILAI_HDDT == '1' && check == 2) {
					DlgUtil.showConfirm("Trạng thái HĐĐT chưa được cập nhật. Xác nhận phát hành lại HĐĐT ?", function(flag) {
						if (flag) {
							var kq_gui_hddt = guiHDDTTheoPhieu(_phieuthuid_hddt, "", "", KH_DN); // L2PT-19666 : KH_DN
							if (kq_gui_hddt == 1 || kq_gui_hddt == 2) {
								loadGridDataPT(_tiepnhanid);
							}
						}
					});
				}
				// L2PT-11280;L2PT-11279 end
				else {
					var kq_gui_hddt = guiHDDTTheoPhieu(_phieuthuid_hddt, "", "", KH_DN); // L2PT-19666 : KH_DN
					if (kq_gui_hddt == 1 || kq_gui_hddt == 2) {
						loadGridDataPT(_tiepnhanid);
					}
				}
			}
		}
	}
// L2PT-28880 start
	function guiHDDTTheoPhieu(phieuthuid_hddt, mode, isCheckedHDDTChitiet, KH_DN) { // L2PT-19666 : KH_DN
		// L2PT-6086 start
		var kt_quyen_hddt = kiemTraQuyen("HDDT", "VPI_QUYEN_HDDT", "0");
		if (kt_quyen_hddt == '0') {
			DlgUtil.showMsg("Bạn không có quyền thao tác với HĐĐT");
			return;
		} else if (kt_quyen_hddt == '-1') {
			DlgUtil.showMsg("Có lỗi xảy ra khi kiểm tra quyền thao tác với HĐĐT");
			return;
		}
		// L2PT-6086 end
		// L2PT-912 start
		if (VPI_HDDT_VT == 1) {
			return guiHDDTTheoPhieuVT(phieuthuid_hddt, mode); // L2PT-22748: them bien mode
		}
		// L2PT-912 end
		// L2PT-98874 L2PT-14600 start
		if (VPI_HDDT_VT == 2) {
			return guiHDDTTheoPhieuMISA(phieuthuid_hddt);
		}
		// L2PT-98874 L2PT-14600 end
		daythongtinbn(phieuthuid_hddt);
		var sql_par = [ phieuthuid_hddt ];
		var fl = "";
		// L2PT-119019 start
		if (fConfig.VPI_FN_IMPORT_HDDT == 5) {
			fl = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.INVOICES.MTT.VP", sql_par.join('$'));
		} else
		// L2PT-119019 end
		if (_invoices_content_type == 1) {
			// lay XML noi dung gom: tien dong chi tra BHYT va Tien tung nhom dich vu BN tra
			fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI_IP_INVOITYPE1", sql_par.join('$'));
		} else if (_invoices_content_type == 2) {
			// lay XML noi dung gom 2 dong tien BH va tien ngoai BH chi tra hoac cau hinh theo tien tung nhom ke toan BN tra
			fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI_IP_INVOITYPE2", sql_par.join('$'));
		} else if (_invoices_content_type == 3) {
			// gộp tien chi tra thanh 1 dong: thanh toan chi phi KCB --DKLSN
			fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI_IP_INVOITYPE3", sql_par.join('$'));
		} else {
			// L2PT-18916 start
			if (INVOICE_XML_TYPE == '1') {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IMPORT.INVOICES", sql_par.join('$'));
			} else
			// L2PT-18916 end
			if (_hospital_id == 957) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOINHI957", sql_par.join('$'));
			} else if (_hospital_id == 965) {
				// L2PT-19666 : KH_DN
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES965", {
					PHIEUTHUID : phieuthuid_hddt + "",
					KH_DN : KH_DN + ""
				});
				// L2PT-19666 : KH_DN
			} else if (_hospital_id == 902) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES902", sql_par.join('$'));
			} else if (_hospital_id == 1007) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1007", sql_par.join('$'));
			} else if (_hospital_id == 919) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES919", sql_par.join('$'));
			} else if (_hospital_id == 944) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES944", sql_par.join('$'));
			}
			// L2PT-17809 start
			/*
			else if (_hospital_id == 951) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES951", sql_par.join('$'));
			} 
			*/
			// L2PT-17809 end
			else if (_hospital_id == 1059 || _hospital_id == 996) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1059", sql_par.join('$'));
			} else if (_hospital_id == 1001) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1001", sql_par.join('$'));
			} else if (_hospital_id == 1108) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1108", sql_par.join('$'));
			} else if (_hospital_id == 982) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES982", sql_par.join('$'));
			} else if (_hospital_id == 1091) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES1091", sql_par.join('$'));
			} else if (_hospital_id == 9364) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES9364", sql_par.join('$'));
			} else if (_hospital_id == 993) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES993", sql_par.join('$'));
			} else if (_hospital_id == 915 || _hospital_id == 1041 || _hospital_id == 978) {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IP.INVOICES915", sql_par.join('$'));
				var ap = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T013.GETDATA", []);
				INVOICES_WS_USER_ACC = ap[0].INVOICES_USER;
				INVOICES_WS_PWD_ACC = ap[0].INVOICES_PASS;
				if (ap[0].INVOICES_PASS == "" || ap[0].INVOICES_USER == "") {
					DlgUtil.showMsg("chưa có acc/pass hóa đơn điện tử!");
					return 0;
				}
			} else {
				fl = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI.IMPORT.INVOICES", sql_par.join('$'));
			}
		}
		var INVOICES_HAI_QUYENHD = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_HAI_QUYENHD');
		// them ghi logs day HDDT: bo sung trang thai 2 la da gui tu HIS
		var _par_flag_sendhd = [ "2", "", "", "", phieuthuid_hddt ];
		var _return_flag_sendhd = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par_flag_sendhd.join('$'));
		var ret = "";
		// L2PT-44489 start
		if (fConfig.VPI_SERIAL_PATTERN_HDDT == '1') {
			var objTtPt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', phieuthuid_hddt);
			if (objTtPt && objTtPt.length > 0) {
				var mauSo = objTtPt[0].MAU_SO;
				var kyHieu = objTtPt[0].KY_HIEU;
				if (typeof mauSo == 'undefined' || typeof kyHieu == 'undefined' || mauSo == "" || kyHieu == "") {
					DlgUtil.showMsg("Sổ hóa đơn chưa có mẫu số hoặc ký hiệu");
					return;
				}
				INVOICES_WS_PATTERN = mauSo;
				INVOICES_WS_SERIAL = kyHieu;
			} else {
				DlgUtil.showMsg("Lỗi lấy thông tin hóa đơn");
				return;
			}
		}
		// L2PT-44489 end
		// L2PT-33584 : thêm giá trị của cấu hình VPI_FN_IMPORT_HDDT
		// L2PT-24350 start
		// var VPI_FN_IMPORT_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'VPI_FN_IMPORT_HDDT');
		if (fConfig.VPI_FN_IMPORT_HDDT == 1) {
			ret = ajaxSvc.InvoicesWS.importInvWithPattern(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER, INVOICES_WS_PWD, INVOICES_WS_PATTERN, INVOICES_WS_SERIAL);
		} else
		// L2PT-24350 end
		// L2PT-17809 start
		if (fConfig.VPI_FN_IMPORT_HDDT == 2) {
			ret = ajaxSvc.InvoicesWS.importInvByPattern(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD, INVOICES_WS_PATTERN, INVOICES_WS_SERIAL);
		} else
		// L2PT-17809 end
		// L2PT-33584 : thêm cấu hình gửi HDDT không phát hành
		if (_hospital_id == 951 || _hospital_id == 9364 || fConfig.VPI_FN_IMPORT_HDDT == 3) {
			ret = ajaxSvc.InvoicesWS.importHoaDon_notPH(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD, INVOICES_WS_PATTERN, INVOICES_WS_SERIAL);
		} else
		// L2PT-32146 : thêm cấu hình gửi HDDT token
		// L2PT-119034 : thêm cấu hình
		if (fConfig.VPI_HDDT_TOKEN == '1' || _hospital_id == 1059 || _hospital_id == 996 || fConfig.VPI_FN_IMPORT_HDDT == 4) { // BV su dung token
			ret = ajaxSvc.InvoicesWS.importHoaDon_token(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER, INVOICES_WS_PWD);
		}
		// L2PT-119019 start
		else if (fConfig.VPI_FN_IMPORT_HDDT == 5) {
			ret = ajaxSvc.InvoicesWS.importHoaDonMTT(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD, INVOICES_WS_PATTERN, INVOICES_WS_SERIAL);
		}
		// L2PT-119019 end
		else if (INVOICES_HAI_QUYENHD == 1) {
			// case cho BV SNPYN
			var INVOICES_BH_PATTERN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_BH_PATTERN');
			var INVOICES_BH_SERIAL = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_BH_SERIAL');
			var INVOICES_DV_PATTERN = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_DV_PATTERN');
			var INVOICES_DV_SERIAL = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_DV_SERIAL');
			var _flag_phieuhd = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.INVOI.FLAGHD", sql_par.join('$')); // check phieu co phai la hoa don hay ko: 1 la hoa don, khac 1 ko phai hoa don
			if (_flag_phieuhd != 1) {
				DlgUtil.showMsg("Phiếu không phải là hóa đơn!");
				return -3;
			}
			var _flag_phieubh_invoi = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.INVOI.FLAGBH1", sql_par.join('$')); // check phieu la hoa don thu khac: 4 la thu khac, khac 4 ko phai thu khac
			// L2PT-28880 start: sua serial va pattern voi truong hop dung 2 quyen hddt
			if (_flag_phieubh_invoi == 4) {
				INVOICES_WS_PATTERN = INVOICES_DV_PATTERN;
				INVOICES_WS_SERIAL = INVOICES_DV_SERIAL;
			} else {
				INVOICES_WS_PATTERN = INVOICES_BH_PATTERN;
				INVOICES_WS_SERIAL = INVOICES_BH_SERIAL;
			}
			ret = ajaxSvc.InvoicesWS.importHoaDon(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD, INVOICES_WS_PATTERN, INVOICES_WS_SERIAL);
			// L2PT-28880 end: sua serial va pattern voi truong hop dung 2 quyen hddt
		} else {
			ret = ajaxSvc.InvoicesWS.importHoaDon(fl, INVOICES_URL_IMPORT, INVOICES_WS_USER_ACC, INVOICES_WS_PWD_ACC, INVOICES_WS_USER, INVOICES_WS_PWD, INVOICES_WS_PATTERN, INVOICES_WS_SERIAL);
		}
		if (ret == "ERR:13") {
			var ret1 = ajaxSvc.InvoicesWS.listInvByCusFkey(phieuthuid_hddt, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
			if (ret1 == "" || ret1.toUpperCase().includes("ERR")) {
				DlgUtil.showMsg("ERR:13 - Lỗi trong quá trình xử lý(listInvByCusFkey)"); // 220422
				return -2;
			}
			var obj = convertXml2JSon(ret1);
			obj = JSON.parse(obj);
			var invoi_nb = obj.Data.Item.invNum;
			var _par = [ "1", INVOICES_WS_PATTERN, INVOICES_WS_SERIAL, parseInt(invoi_nb), phieuthuid_hddt ];
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
			DlgUtil.showMsg("Hóa đơn đã phát hành cập nhật số hóa đơn thành công");
			return 2;
		}
		if (ret.substring(0, 3) != "OK:") {
			// L2PT-31616 start
			// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi
			DlgUtil.showMsg(thongbao_loi('HDDT_PHAT_HANH', ret));
			// L2PT-37815 end
			// L2PT-31616 end
			return -1;
		} else {
			var _par = [ "1", INVOICES_WS_PATTERN, INVOICES_WS_SERIAL, ret.split("_")[1], phieuthuid_hddt ];
			var _return = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par.join('$'));
			// L2PT-14008 start duyệt phiếu L2PT-24654 fix
			if (VPI_KHOA_HDDT == 1 && _return == 1) {
				var _result_duyet = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI03.KHOA.PHIEUTHU', phieuthuid_hddt + '$' + 1);
				DlgUtil.showMsg(_result_duyet);
			}
			// L2PT-14008 end L2PT-24654 fix
			// _hospital_id == 957 || _hospital_id == 1007
			DlgUtil.showMsg("Gửi hóa đơn điện tử thành công", function() {}, VPI_WAIT_MSG); // L2PT-12176 L2PT-54506
			var VPI_VIEW_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_VIEW_HDDT');
			if (VPI_VIEW_HDDT == 1 || VPI_VIEW_HDDT == 2) { // L2PT-24178 them gia tri cấu hình
				// L2PT-19351 start
				// 220422 start
				setTimeout(function() {
					_inHDDTTheoPhieu(phieuthuid_hddt);
				}, VPI_VIEW_HDDT_DELAY * 1000);
				// 220422 end
				// L2PT-19351 end
			}
		}
		//var _par = [phieuthuid_hddt,ret];	
		//var check = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.LOG",_par.join('$'));
		return 1
	}
// L2PT-912 start
	function guiHDDTTheoPhieuVT(phieuthuid_hddt, mode) {
		var xmlInv = jsonrpc.AjaxJson.ajaxCALL_SP_C("VPI_DAY_HDDT_29180", phieuthuid_hddt);
		if (xmlInv && xmlInv.length > 0) {
			var x2js = new X2JS({
				arrayAccessFormPaths : [ "root.itemInfo", "root.metadata", "root.payments", "root.taxBreakdowns" ]
			});
			var objInv = x2js.xml_str2json(xmlInv);
			// L2PT-25725 start
			if (VPI_HDDT_VIETTEL_V2 == '1') {
				formatObj(objInv, INVOICES_VT_FMTNBR);
			}
			var objSenInv = objInv.root;
			var jsonSendInv = JSON.stringify(objSenInv);
			// L2PT-25725 end
			// them ghi logs day HDDT: bo sung trang thai 2 la da gui tu HIS
			var _par_flag_sendhd_vt = [ "2", "", "", "", phieuthuid_hddt ];
			var _return_flag_sendhd_vt = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.DAYHDDT.UPDATE", _par_flag_sendhd_vt.join('$'));
			// L2PT-22748 start
			var retHDDTviettel = "";
			if (mode == 2 || VPI_DAY_HDDT_PH == '1') {
				retHDDTviettel = ajaxSvc.InvoicesWS.createOrUpdateInvoiceDraft(jsonSendInv);
			} else {
				retHDDTviettel = ajaxSvc.InvoicesWS.createInvoice(jsonSendInv);
			}
			// L2PT-22748 end
			if (retHDDTviettel.includes("ERR_CREATTE_REQ")) {
				DlgUtil.showMsg('Lỗi gửi hóa đơn điện tử');
				return -1;
			} else {
				var objRetHDDTviettel = JSON.parse(retHDDTviettel);
				if (objRetHDDTviettel.errorCode == null) {
					var resultHDDTviettel = objRetHDDTviettel.result;
					var objUdpHDDTviettel = new Object();
					// L2PT-22748 start
					objUdpHDDTviettel.SYNC_FLAG = "1";
					objUdpHDDTviettel.PHIEUTHUID = phieuthuid_hddt;
					if (mode == 2 || VPI_DAY_HDDT_PH == '1') {
						objUdpHDDTviettel.PATTERN = "";
						objUdpHDDTviettel.SERIAL = "";
						objUdpHDDTviettel.NUMBER = "";
						objUdpHDDTviettel.TRANSACTIONID_VT = "";
						objUdpHDDTviettel.RESERVATIONCODE_VT = "";
					} else {
						objUdpHDDTviettel.PATTERN = INVOICES_WS_PATTERN;
						objUdpHDDTviettel.SERIAL = resultHDDTviettel.invoiceNo.substr(0, 6);
						objUdpHDDTviettel.NUMBER = resultHDDTviettel.invoiceNo.substr(6);
						objUdpHDDTviettel.TRANSACTIONID_VT = resultHDDTviettel.transactionID;
						objUdpHDDTviettel.RESERVATIONCODE_VT = resultHDDTviettel.reservationCode;
					}
					// L2PT-22748  end
					var retUdpHDDTviettel = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.HDDT.UPDATE.UGR", JSON.stringify(objUdpHDDTviettel));
					if (retUdpHDDTviettel != 1) {
						DlgUtil.showMsg('Cập nhật trạng thái gửi không thành công');
					}
					// L2PT-5786 start
					var VPI_VIEW_HDDT = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'INVOICES_VIEW_HDDT');
					if (VPI_VIEW_HDDT == 1) {
						setTimeout(function() {
							_inHDDTTheoPhieuVT(phieuthuid_hddt);
						}, 2000);
					} else {
						DlgUtil.showMsg('Gửi hóa đơn điện tử thành công');
					}
					// L2PT-5786 end
					return 1;
				} else {
					DlgUtil.showMsg(objRetHDDTviettel.description + " ( " + objRetHDDTviettel.errorCode + " ) ");
					return -2;
				}
			}
		} else {
			DlgUtil.showMsg('Không tìm thấy dữ liệu hóa đơn này');
			return -3;
		}
	}
	// L2PT-912 end
	// L2PT-28880 end
	// L2PT-98874 L2PT-14600 start
	function guiHDDTTheoPhieuMISA(phieuthuid_hddt) {
		var arrInv = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.MISA.HDDT", phieuthuid_hddt);
		if (arrInv && arrInv.length > 0) {
			var objInv = arrInv[0];
			// L2PT-17896 start
			if (INVOICE_XML_TYPE == '1') {
				objInv.TaxRateInfo = JSON.parse(objInv.TaxRateInfo);
			}
			// L2PT-17896 end
			var arrInvDet = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T001.MISA.ITEM", phieuthuid_hddt);
			if (arrInvDet && arrInvDet.length > 0) {
				objInv.OriginalInvoiceDetail = arrInvDet;
				if (INVOICES_MISA_OPTIONUSER !== null && typeof INVOICES_MISA_OPTIONUSER === 'object' && !Array.isArray(INVOICES_MISA_OPTIONUSER)) {
					objInv.OptionUserDefined = INVOICES_MISA_OPTIONUSER;
					var data = [ objInv ];
					parseObj(data, INVOICES_MISA_FMTNBR);
					// L2PT-104997 L2PT-98874 start
					if (fConfig.INVOICES_TYPE == "MISA_HSM") {
						param = {
							"type" : "0",
							"data" : [ {
								RefID : data[0].RefID,
								OriginalInvoiceData : data[0]
							} ]
						};
					} else {
						param = {
							"type" : "0",
							"data" : data
						};
					}
					// L2PT-104997 L2PT-98874 end
					var retInvMISA = ajaxSvc.InvoicesWS.issueInvoices(param);
					try {
						var retObj = JSON.parse(retInvMISA);
						console.log(retObj);
						if (retObj.errorCode == "0") {
							var arrInvObj = JSON.parse(retObj.data);
							console.log(arrInvObj);
							// L2PT-17896 start
							if (INVOICE_XML_TYPE == '1') {
								DlgUtil.showMsg('Phát hành hóa đơn điện tử MISA thành công');
								return 1;
							} else
							// L2PT-17896 end	
							if (arrInvObj && arrInvObj.length > 0) {
								var dataInvObj = arrInvObj[0];
								updateHDDT("1", dataInvObj.TemplateCode, dataInvObj.InvoiceSeries, dataInvObj.InvoiceNumber, phieuthuid_hddt, dataInvObj.TransactionID, dataInvObj.RefID);
								// L2PT-5786 start
								// L2PT-25887 : lấy cấu hình khi load form
								if (INVOICES_VIEW_HDDT == 1) {
									setTimeout(function() {
										_inHDDTTheoPhieuMISA(phieuthuid_hddt);
									}, 1000);
								} else {
									DlgUtil.showMsg('Phát hành hóa đơn điện tử MISA thành công');
								}
								// L2PT-5786 end
								return 1;
							} else {
								DlgUtil.showMsg('Không lấy được dữ liệu hddt để cập nhật');
							}
						} else {
							DlgUtil.showMsg("Mã lỗi " + retObj.errorCode + ": " + retObj.message);
						}
					} catch (e) {
						console.log(e);
					}
				} else {
					DlgUtil.showMsg('Không lấy được cấu hình gửi HDDT MISA');
				}
			} else {
				DlgUtil.showMsg('Không lấy được dữ liệu chi tiết hóa đơn này');
				return -3;
			}
		} else {
			DlgUtil.showMsg('Không tìm thấy dữ liệu hóa đơn này');
			return -3;
		}
	}
	function parseObj(arrObj, arrAtt) {
		for (var i = 0; i < arrObj.length; i++) {
			var keys = Object.keys(arrObj[i]);
			for (var j = 0; j < keys.length; j++) {
				var key = keys[j];
				if (arrObj[i][key] == null || arrObj[i][key] == "") {
					continue;
				} else if (typeof arrObj[i][key] === 'object' && Array.isArray(arrObj[i][key])) {
					parseObj(arrObj[i][key], arrAtt);
				} else if (arrAtt.indexOf(key) != -1) {
					var newVal = parseFloat(arrObj[i][key]);
					if (isNaN(arrObj[i][key])) {
						newVal = (/true/i).test(arrObj[i][key]);
					}
					arrObj[i][key] = newVal;
				}
			}
		}
	}
	function updateHDDT(status, invPattern, invSerial, invNumber, phieuthuid_hddt, transactionID, refID) {
		var objUdpHDDT = new Object();
		objUdpHDDT.SYNC_FLAG = "1";
		objUdpHDDT.PATTERN = invPattern;
		objUdpHDDT.SERIAL = invSerial;
		objUdpHDDT.NUMBER = invNumber;
		objUdpHDDT.PHIEUTHUID = phieuthuid_hddt;
		objUdpHDDT.TRANSACTIONID_VT = transactionID;
		objUdpHDDT.RESERVATIONCODE_VT = refID;
		var retUdpHDDT = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.HDDT.UPDATE.UGR", JSON.stringify(objUdpHDDT));
		if (retUdpHDDT != 1) {
			DlgUtil.showMsg('Cập nhật trạng thái gửi không thành công');
		}
	}
	// L2PT-98874 L2PT-14600 end
	// L2PT-25725 start
	function formatObj(obj, arrAtt) {
		if (typeof obj === 'object') {
			if (Array.isArray(obj)) {
				for (var i = 0; i < obj.length; i++) {
					formatObj(obj[i], arrAtt);
				}
			} else {
				formatObjNaA(obj, arrAtt);
			}
		}
	}
	function formatObjNaA(obj, arrAtt) {
		var keys = Object.keys(obj);
		for (var j = 0; j < keys.length; j++) {
			var key = keys[j];
			if (obj[key] == null || obj[key] == "") {
				continue;
			} else if (typeof obj[key] === 'object') {
				formatObj(obj[key], arrAtt);
			} else if (arrAtt.indexOf(key) != -1) {
				var newVal = obj[key];
				if (isNaN(obj[key])) {
					newVal = (/true/i).test(obj[key]);
				} else {
					newVal = parseFloat(obj[key]); // L2PT-33849
				}
				obj[key] = newVal;
			}
		}
	}
// L2PT-25725 end
	function _inHDDT() {
		if (flagLoading)
			return;
		selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
		var phieuthuid_hddt = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
		_inHDDTTheoPhieu(phieuthuid_hddt);
	}
	function _inHDDTTheoPhieu(phieuthuid_hddt) {
		// BVTM-7213;L2PT-11280;L2PT-11279 start
		if (VPI_XN_GUILAI_HDDT == '1') {
			var checkSyncInv = jsonrpc.AjaxJson.ajaxExecuteQueryO("VPI.DAYHDDT.CHECK", [ {
				"name" : "[0]",
				"value" : phieuthuid_hddt
			} ]);
			var rows = [];
			try {
				rows = JSON.parse(checkSyncInv);
			} catch (err) {
				console.log(err);
			}
			if (rows && rows.length > 0) {
				var check = rows[0]["SYNC_FLAG"];
				if (check == 2) {
					DlgUtil.showConfirm("Trạng thái HĐĐT chưa được cập nhật. Xác nhận phát hành lại HĐĐT ?", function(flag) {
						if (flag) {
							guiHDDTTheoPhieu(phieuthuid_hddt);
						}
					});
					return;
				}
			}
		}
		// BVTM-7213;L2PT-11280;L2PT-11279 end
		// L2PT-912 start
		if (VPI_HDDT_VT == 1) {
			_inHDDTTheoPhieuVT(phieuthuid_hddt);
			return;
		}
		// L2PT-912 end
		// L2PT-98874 L2PT-14600 start
		if (VPI_HDDT_VT == 2) {
			_inHDDTTheoPhieuMISA(phieuthuid_hddt);
			return;
		}
		// L2PT-98874 L2PT-14600 end
		var result = '';
		// L2PT-33584 : thêm cấu hình gửi HDDT không phát hành
		if (_hospital_id == 951 || _hospital_id == 9364 || fConfig.VPI_FN_IMPORT_HDDT == 3) {
			result = ajaxSvc.InvoicesWS.viewHoaDon_notPH(phieuthuid_hddt, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
		}
		// L2PT-22840 start
		else if (VPI_VIEWHD_FUNC == '1') {
			result = ajaxSvc.InvoicesWS.getInvErrorViewFkey(phieuthuid_hddt, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
		}
		// L2PT-22840 end
		else {
			result = ajaxSvc.InvoicesWS.viewHoaDon(phieuthuid_hddt, INVOICES_URL_VIEW, INVOICES_WS_USER, INVOICES_WS_PWD);
		}
		// 220422 start
		if (result == "" || result.includes("ERR:")) {
			// L2PT-37815 start: so sánh bằng thay vì có chứa chuỗi mã lỗi 
			DlgUtil.showMsg(thongbao_loi('HDDT_TAI_VE', result));
			// L2PT-37815 end
		}
		// 220422 end
		else {
			// L2PT-22840 start
			if (VPI_VIEWHD_FUNC == '1') {
				result = atob(result);
			}
			// L2PT-22840 end
			// L2PT-45621 start
			var win = window.open('', '_blank', 'scrollbars=1,menubar=0,resizable=1,width=' + screen.availWidth * 0.8 + ',height=' + screen.availHeight * 0.8 + ",left=" + screen.availWidth * 0.1 +
					",top=" + screen.availHeight * 0.1); // BVTM-4343
			win.document.write(result);
			if (fConfig.VPI_HDDT_HIEN_GDIN == '1') {
				win.document.close();
				win.print();
			}
			// L2PT-45621 end
		}
	}
// L2PT-912 start
	function _inHDDTTheoPhieuVT(phieuthuid_hddt) {
		var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', phieuthuid_hddt);
		if (arr_pt && arr_pt.length > 0) {
			var rowPT = arr_pt[0];
			/*
			var supplierTaxCode = INVOICES_WS_USER;
			var invoiceNo = rowPT.INVOICENO;
			var strIssueDate = rowPT.STRISSUEDATE;
			var additionalReferenceDesc = "huy";
			var additionalReferenceDate = rowPT.ADDITIONALREFERENCEDATE;
			*/
			var objGetFile = new Object;
			objGetFile.supplierTaxCode = INVOICES_WS_TAX_CODE; // L2PT-5786
			objGetFile.invoiceNo = rowPT.INVOICENO;
			objGetFile.templateCode = fConfig.VPI_HDDTVT_PATTERN == '0' ? INVOICES_WS_PATTERN : rowPT.MAU_SO; // L2PT-109124
			// L2PT-25725 start
			if (VPI_HDDT_VIETTEL_V2 == '1') {
				objGetFile.transactionID = rowPT.TRANSACTIONID_VT;
			} else {
				objGetFile.transactionUuid = rowPT.TRANSACTIONID_VT;
			}
			// L2PT-25725 end
			objGetFile.fileType = "PDF";
			var retRFHDDTviettel = ajaxSvc.InvoicesWS.getInvoiceRepresentationFile(JSON.stringify(objGetFile));
			if (retRFHDDTviettel.includes("ERR_GETINVOICE_REQ")) {
				DlgUtil.showMsg('Lỗi xem hóa đơn điện tử');
				return -1;
			} else {
				var objRFHDDTviettel = JSON.parse(retRFHDDTviettel);
				if (objRFHDDTviettel.errorCode == null || objRFHDDTviettel.errorCode == '200') { // L2PT-25725 errorCode = 200
					var arrrayBuffer = base64ToArrayBuffer(objRFHDDTviettel.fileToBytes); //data is the base64 encoded string
					function base64ToArrayBuffer(base64) {
						var binaryString = window.atob(base64);
						var binaryLen = binaryString.length;
						var bytes = new Uint8Array(binaryLen);
						for (var i = 0; i < binaryLen; i++) {
							var ascii = binaryString.charCodeAt(i);
							bytes[i] = ascii;
						}
						return bytes;
					}
					var blob = new Blob([ arrrayBuffer ], {
						type : "application/pdf"
					});
					var link = window.URL.createObjectURL(blob);
					window.open(link, '', 'height=500,width=850');
				} else {
					DlgUtil.showMsg(objRFHDDTviettel.description + " ( " + objRFHDDTviettel.errorCode + " )");
				}
			}
		} else {
			DlgUtil.showMsg("Không tìm thấy hóa đơn này");
		}
	}
	// L2PT-912 end
	// L2PT-98874 L2PT-14600 start
	function _inHDDTTheoPhieuMISA(phieuthuid_hddt) {
		var arr_pt = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI01T001.14.1', phieuthuid_hddt);
		if (arr_pt && arr_pt.length > 0) {
			var retViewHDDTMISA = ajaxSvc.InvoicesWS.getIssuedInvoice(arr_pt[0].TRANSACTIONID_VT);
			console.log(retViewHDDTMISA);
			try {
				var arrViewHDDTMISA = JSON.parse(retViewHDDTMISA);
				if (arrViewHDDTMISA && arrViewHDDTMISA.length > 0) {
					window.open(arrViewHDDTMISA[0]);
				}
			} catch (err) {
				DlgUtil.showMsg("có lỗi xảy ra:" + err);
			}
		} else {
			DlgUtil.showMsg("Không tìm thấy hóa đơn này");
		}
	}
	// L2PT-98874 L2PT-14600 end
	// L2PT-42086 start
	function _inHDDTCD() {
		if (flagLoading)
			return;
		// L2PT-6086 start
		var kt_quyen_hddt = kiemTraQuyen("HDDT", "VPI_QUYEN_HDDT", "0");
		if (kt_quyen_hddt == '0') {
			DlgUtil.showMsg("Bạn không có quyền thao tác với HĐĐT");
			return;
		} else if (kt_quyen_hddt == '-1') {
			DlgUtil.showMsg("Có lỗi xảy ra khi kiểm tra quyền thao tác với HĐĐT");
			return;
		}
		// L2PT-6086 end
		selRowId = $("#" + _gridId_PT).jqGrid("getGridParam", "selrow");
		var _phieuthuId = $("#" + _gridId_PT).jqGrid('getCell', selRowId, 'PHIEUTHUID');
		// L2PT-37815 start: chuyển sang file inv.js
		if (!dsCH) {
			dsCH = layDsCH(_hospital_id);
		}
		if (!dsCH) {
			return;
		}
		dsCH._hospital_code = _hospital_code; // L2PT-83683
		convertForStoreFkey(_phieuthuid, dsCH);
		// L2PT-37815 end
	}
// L2PT-42086 end
// L2PT-1511 start
	function checkLichSuPOS() {
		if (VPI_HIEN_IDPOST != '2') {
			return 0;
		}
		var objTimKiem = new Object();
		objTimKiem.TIEPNHANID = _tiepnhanid + "";
		objTimKiem.LACTION = "GET";
		objTimKiem.TRANS_INF_STATUS = "APPROVE";
		objTimKiem.BILL_STATUS = "0";
		var result = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI01T024.EV002", JSON.stringify(objTimKiem));
		if (result && result.length > 0) {
			var myVar = new Object();
			myVar.TIEPNHANID = _tiepnhanid;
			dlgPopup = DlgUtil.buildPopupUrl("dlgLichSuPOS", "divDlgLSPOS", "manager.jsp?func=../vienphi/VPI01T024_LichSuPOS", myVar, "Lịch sử thanh toán POS", 1150, 650);
			DlgUtil.open("dlgLichSuPOS");
			return 1;
		}
		return 0;
	}
// L2PT-1511 end
// L2PT-557 start
	function checkDuyetDLS() {
		var checkDLS = 0;
		if (_benhnhan.LOAITIEPNHANID == '1' && _benhnhan.TRANGTHAITIEPNHAN != 0) {
			checkDLS = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.CHECK.DUYETDLS", _tiepnhanid);
		}
		return checkDLS;
	}
// L2PT-557 end
// L2PT-6086
	function kiemTraQuyen(maQuyen, maCauHinh, cauHinhMacDinh) {
		var kt_quyen_hddt = jsonrpc.AjaxJson.ajaxCALL_SP_I('VPI.CHECK.QUYEN', maQuyen + "$" + maCauHinh + "$" + cauHinhMacDinh);
		return kt_quyen_hddt;
	}
// L2PT-6086
// BVTM-5934 start
	function onCboHtttChange() {
		// L2PT-31437 start
		$('#txtIDPOST').val("");
		var _loaitt = $("#cboHINHTHUCTHANHTOAN").val();
		if (_loaitt == 5 || (_loaitt == 12 && (VPI_HIENTHI_QRCODE == '3' || VPI_HIENTHI_QRCODE == '4'))) { // L2PT-102404 // L2PT-108719
			if (VPI_HIEN_IDPOST == '2') {
				// L2PT-110377 start
				var selIDCache = '';
				if (VPI_POS_ID_MD && VPI_POS_ID_MD != '-1') {
					selIDCache = VPI_POS_ID_MD;
				} else {
					selIDCache = localStorage.getItem('IDPOST') ? localStorage.getItem('IDPOST') : "";
				}
				// L2PT-110377 end
				$("#cboPOS").val(selIDCache);
				$("#divPOS").show();
			}
			setEnabled([ 'txtIDPOST', 'cboPOS' ], []);
		} else if (_loaitt == 2) {
			setEnabled([ 'txtIDPOST' ], []);
		} else {
			$("#cboPOS").val("");
			setEnabled([], [ 'txtIDPOST', 'cboPOS' ]);
			$("#divPOS").hide();
		}
		// L2PT-31437 end
		// L2PT-59668 start
		if (_loaitt == 2) { // L2PT-4355 : them loai tt = 5
			// L2PT-98242 start
			if (fConfig.VPI_ID_NGANHANG_MD && fConfig.VPI_ID_NGANHANG_MD != 0) {
				$("#cboNganHang").val(fConfig.VPI_ID_NGANHANG_MD);
			} else {
				$("#cboNganHang").prop("selectedIndex", 0);
			}
			// L2PT-98242 end
			$("#divNganHang").show();
		} else
		// L2PT-59668 end
		// BVTM-3849 start
		if (_loaitt == 5 || _loaitt == 12) { // L2PT-4355 : them loai tt = 5
			ComboUtil.getComboTag("cboDVTT", 'VPI.DVTT.SP', _loaitt == 12 ? "QRCODE" : "POS", "", "", 'sp', '', '');
			// L2PT-131086 start
			if (fConfig.VPI_ID_DVTT_MD && fConfig.VPI_ID_DVTT_MD != 0) {
				$("#cboDVTT").val(fConfig.VPI_ID_DVTT_MD);
			} else {
				$("#cboDVTT").prop("selectedIndex", 0);
			}
			// L2PT-131086 end
			$("#divDVTT").show();
		} else {
			$("#cboDVTT").val("");
			$("#divDVTT").hide();
		}
		// BVTM-3849 end
		//L2PT-19304 start
		_arrHTTT = [];
		//L2PT-19304 end
	}
// BVTM-5934 end
// L2PT-26717 start
	function guiHDDTTheoLK(tiepnhanID) {
		var data_ar_ptid = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.INVOICES.TNID", JSON.stringify({
			"TIEPNHANID" : tiepnhanID
		}));
		for (var j = 0; j < data_ar_ptid.length; j++) {
			var _phieuthuid_hddt = data_ar_ptid[j].PHIEUTHUID;
			guiHDDTTheoPhieu(_phieuthuid_hddt)
		}
	}
// L2PT-26717 end
// L2PT-28267 BVTM-3237 start: luu log 
	function save_log_act_form(lcode, lfunc, ltext, lkey) {
		var objLogActForm = new Object();
		objLogActForm.LCODE = lcode;
		objLogActForm.LFUNC = lfunc;
		objLogActForm.LTEXT = ltext;
		objLogActForm.LKEY = lkey;
		var _result_log = jsonrpc.AjaxJson.ajaxCALL_SP_S('LOG.ACT.FORM', objLogActForm);
		if (_result_log != '1' && _result_log != '2') {
			DlgUtil.showMsg("Cập nhật log thao tác không thành công: (" + _result_log + ")");
		}
	}
// L2PT-28267 BVTM-3237 end: luu log 
// L2PT-31941 start
	function removeVietnameseTones(str) {
		str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
		str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
		str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
		str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
		str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
		str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
		str = str.replace(/đ/g, "d");
		str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
		str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
		str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
		str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
		str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
		str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
		str = str.replace(/Đ/g, "D");
		// Some system encode vietnamese combining accent as individual utf-8 characters
		// Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
		str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
		str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
		// Remove extra spaces
		// Bỏ các khoảng trắng liền nhau
		str = str.replace(/ + /g, " ");
		str = str.trim();
		// Remove punctuations
		// Bỏ dấu câu, kí tự đặc biệt
		// str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g," ");
		return str;
	}
// L2PT-31941 end
// L2PT-32078 L2PT-10249 start
	function nhapMienGiamNhomDV() {
		if (flagLoading)
			return;
		// L2PT-3994 start
		dlgPopup = DlgUtil.buildPopupGrid("dlgMGNDV", 'MG_NHOMDV', "Chọn nhóm dịch vụ miễn giảm", 1250, 800);
		// L2PT-19368 start
		GridUtil.init('MG_NHOMDV', "1200", "250", "", false, _gridHeader_MGNDV, true, {
			rowNum : 100,
			rowList : [ 100, 200, 300 ]
		});
		// L2PT-19368 end
		// L2PT-7197 start: them danh sach dich vu 
		//$("#gbox_MG_NHOMDV").addClass("col-xs-5");
		_grid_mg_dv_ndv = '<table id="grdMG_DV_NDV"></table>'
		$('#dlgMGNDV').append(_grid_mg_dv_ndv);
		var _group = {
			groupField : [ "DOITUONG", "NHOM_MABHYT" ],
			groupColumnShow : [ false, false ],
			groupText : [ '<b>{0}</b>', '<b>{0}</b>' ]
		};
		GridUtil.initGroup('grdMG_DV_NDV', "1200", "250", "Danh sách dịch vụ", false, _group, _gridHeader_DV, true, {
			rowNum : 999999999,
			rowList : [ 999999999 ]
		});
		var _sql_par_dv_ndv = [];
		_sql_par_dv_ndv.push({
			"name" : "[0]",
			"value" : _tiepnhanid
		});
		_sql_par_dv_ndv.push({
			"name" : "[1]",
			"value" : "0"
		});
		GridUtil.loadGridBySqlPage('grdMG_DV_NDV', "VPI01T001.21", _sql_par_dv_ndv, function() {});
		var _sql_par = [ {
			"name" : "[0]",
			"value" : _tiepnhanid
		} ];
		// L2PT-7197 end
		var grid = $("#" + 'MG_NHOMDV');
		var edit_row = [];
		GridUtil.loadGridBySqlPage('MG_NHOMDV', "VPI01T001.NDV.SP", _sql_par, function() { // thay sql(VPI01T004.05.NDV) bang sp 
			var ids = grid.getDataIDs();
			// L2PT-7197 start: nhập tỷ lệ miễn giảm theo từng loại thanh toán
			/*for (var i = 0; i < ids.length; i++) {
				var _id_nhom = grid.jqGrid('getCell', ids[i], 'NHOM_MABHYT_ID');
				_cell_html_bh = '<input type="checkbox" ' + 'id="chkLOAI_TT_BH' + _id_nhom + '" checked>';
				_cell_html_vp = '<input type="checkbox" ' + 'id="chkLOAI_TT_VP' + _id_nhom + '" checked>';
				_cell_html_dv = '<input type="checkbox" ' + 'id="chkLOAI_TT_DV' + _id_nhom + '" checked>';
				grid.jqGrid('setCell', ids[i], 'LOAI_BHYT', _cell_html_bh);
				grid.jqGrid('setCell', ids[i], 'LOAI_VIENPHI', _cell_html_vp);
				grid.jqGrid('setCell', ids[i], 'LOAI_DICHVU', _cell_html_dv);
			}*/
			// L2PT-7197 end
			dlgPopup.open();
		});
		// L2PT-7197 start: load dv theo nhom
		GridUtil.setGridParam('MG_NHOMDV', {
			onSelectRow : function(id) {
				GridUtil.loadGridBySqlPage('grdMG_DV_NDV', "VPI01T001.21", _sql_par_dv_ndv, function() {
					var _nhom_mabhyt_id = grid.jqGrid('getCell', id, 'NHOM_MABHYT_ID');
					var ids = $("#" + 'grdMG_DV_NDV').getDataIDs();
					for (var i = 0; i < ids.length; i++) {
						var _nhom_mabhyt_id_dv = $("#" + 'grdMG_DV_NDV').jqGrid('getCell', ids[i], 'NHOM_MABHYT_ID');
						// L2PT-24175 start: miễn giảm gói kcb
						if (_nhom_mabhyt_id_dv == -1) {
							var maDichVu = $("#" + 'grdMG_DV_NDV').jqGrid('getCell', ids[i], 'MADICHVU');
							if (maDichVu != 'GOIKCB.DN') {
								$("#" + 'grdMG_DV_NDV').jqGrid('delRowData', ids[i]);
							}
						} else if (_nhom_mabhyt_id_dv != _nhom_mabhyt_id) {
							$("#" + 'grdMG_DV_NDV').jqGrid('delRowData', ids[i]);
						}
						// L2PT-24175 end
					}
				});
			}
		// L2PT-7197 end
		});
		grid.bind("jqGridInlineAfterSaveRow", function(e, rowid, orgClickEvent) {
			var _tylemg_chung = grid.jqGrid('getCell', rowid, 'TYLEMG_CHUNG');
			var _tylemg_bhyt = grid.jqGrid('getCell', rowid, 'TYLEMG_BHYT');
			var _tylemg_vienphi = grid.jqGrid('getCell', rowid, 'TYLEMG_VIENPHI');
			var _tyleng_dichvu = grid.jqGrid('getCell', rowid, 'TYLEMG_DICHVU');
			var ind = edit_row.indexOf(rowid);
			// L2PT-7197 start: nhập tỷ lệ miễn giảm theo từng loại thanh toán
			// L2PT-3994 cho phep tyle == 0 
			// kiem tra ty le nhap vao phai la so trong pham vi 0 den 100
			var check_tylemg_chung = false;
			var check_tylemg_bhyt = false;
			var check_tylemg_vienphi = false;
			var check_tyleng_dichvu = false;
			if (/^\d+$/i.test(_tylemg_chung) && _tylemg_chung >= 0 && _tylemg_chung <= 100) {
				check_tylemg_chung = true;
			} else {
				grid.jqGrid('setCell', rowid, 'TYLEMG_CHUNG', null);
			}
			if (/^\d+$/i.test(_tylemg_bhyt) && _tylemg_bhyt >= 0 && _tylemg_bhyt <= 100) {
				check_tylemg_bhyt = true;
			} else {
				grid.jqGrid('setCell', rowid, 'TYLEMG_BHYT', null);
			}
			if (/^\d+$/i.test(_tylemg_vienphi) && _tylemg_vienphi >= 0 && _tylemg_vienphi <= 100) {
				check_tylemg_vienphi = true;
			} else {
				grid.jqGrid('setCell', rowid, 'TYLEMG_VIENPHI', null);
			}
			if (/^\d+$/i.test(_tyleng_dichvu) && _tyleng_dichvu >= 0 && _tyleng_dichvu <= 100) {
				check_tyleng_dichvu = true;
			} else {
				grid.jqGrid('setCell', rowid, 'TYLEMG_DICHVU', null);
			}
			if (check_tylemg_chung || check_tylemg_bhyt || check_tylemg_vienphi || check_tyleng_dichvu) {
				GridUtil.markRow('MG_NHOMDV', rowid);
				if (ind == -1) {
					edit_row.push(rowid);
				}
			} else {
				GridUtil.unmarkRow('MG_NHOMDV', rowid);
				if (ind != -1) {
					edit_row.splice(ind, 1);
				}
			}
			// L2PT-7197 end
		});
		// duonghn 220824 start: mod id
		var btnOK = $('<input class="btn btn-sm btn-primary" id="btn_MGNDV_OK" type="button" style="position:absolute;bottom:10px;left:10px" value="Đồng ý" />');
		var btnClose = $('<input class="btn btn-sm btn-primary" id="btn_MGNDV_Close" type="button" style="position:absolute;bottom:10px;right:10px" value="Đóng" />');
		// duonghn 220824 end
		$('#dlgMGNDV').append(btnOK);
		$('#dlgMGNDV').append(btnClose);
		btnOK.click(function() {
			var arr_row = grid.getDataIDs();
			var ds_mgndv = [];
			for (var i = 0; i < arr_row.length; i++) {
				var obj_mg_ndv = new Object();
				var _nhom_mabhyt_id = grid.jqGrid('getCell', arr_row[i], 'NHOM_MABHYT_ID');
				var _tylemg_chung = grid.jqGrid('getCell', arr_row[i], 'TYLEMG_CHUNG');
				var _tylemg_bhyt = grid.jqGrid('getCell', arr_row[i], 'TYLEMG_BHYT');
				var _tylemg_vienphi = grid.jqGrid('getCell', arr_row[i], 'TYLEMG_VIENPHI');
				var _tyleng_dichvu = grid.jqGrid('getCell', arr_row[i], 'TYLEMG_DICHVU');
				// L2PT-7197 start: nhập tỷ lệ miễn giảm theo từng loại thanh toán
				// L2PT-3994 start(cho phép tyle = 0)
				if (edit_row.indexOf(arr_row[i]) != -1) {
					obj_mg_ndv.NHOM_MABHYT_ID = _nhom_mabhyt_id;
					obj_mg_ndv.TYLEMG_CHUNG = _tylemg_chung;
					obj_mg_ndv.TYLEMG_BHYT = _tylemg_bhyt;
					obj_mg_ndv.TYLEMG_VIENPHI = _tylemg_vienphi;
					obj_mg_ndv.TYLEMG_DICHVU = _tyleng_dichvu;
					ds_mgndv.push(obj_mg_ndv);
				}
				// L2PT-3994 end
				// L2PT-7197 end
			}
			var _mode = 0;
			if (ds_mgndv.length > 0) {
				ds_mg = JSON.stringify(ds_mgndv);
				var _result = jsonrpc.AjaxJson.ajaxCALL_SP_S('VPI.MG.NDV.CL', _tiepnhanid + "$" + ds_mg + "$" + _mode);
				DlgUtil.showMsg(_result);
				GridUtil.loadGridBySqlPage('grdMG_DV_NDV', "VPI01T001.21", _sql_par_dv_ndv, function() {}); // L2PT-7197: load lại ds dv
			} else {
				DlgUtil.showMsg("Chưa chọn nhóm dịch vụ");
			}
		});
		btnClose.click(function() {
			dlgPopup.close();
		});
	}
// L2PT-32078 L2PT-10249 end
// L2PT-34615 start
	function inPTTheoLoai(loai) {
		var _selRowId = $("#" + _gridId_PT).jqGrid('getGridParam', 'selrow');
		var _phieuthuid = $("#" + _gridId_PT).jqGrid('getCell', _selRowId, 'PHIEUTHUID');
		var par = [];
		par.push({
			name : 'phieuthuid',
			type : 'String',
			value : _phieuthuid + ""
		});
		par.push({
			name : 'loaiphieuthu',
			type : 'String',
			value : loai + ""
		});
		CommonUtil.openReportGet('window', 'NGT041_PHIEUTHUTIEN_TONGHOP', 'pdf', par, true, true);
	}
// L2PT-34615 end
// L2PT-36342 start: đếm số phiếu thu hiện có trên danh sách theo loại phiếu thu id và trạng thái
	function demPhieuThu(loaiPhieuThuId, trangThai) {
		var count = 0;
		var ids = $("#" + _gridId_PT).getDataIDs();
		for (var i = 0; i < ids.length; i++) {
			var _loaiphieuthuid = $("#" + _gridId_PT).jqGrid('getCell', ids[i], 'LOAIPHIEUTHUID');
			var _dahuyphieu = $("#" + _gridId_PT).jqGrid('getCell', ids[i], 'DAHUYPHIEU');
			if (_dahuyphieu != '1') {
				_dahuyphieu = '0';
			}
			if (_loaiphieuthuid == loaiPhieuThuId) {
				if (trangThai == -1 || _dahuyphieu == trangThai) {
					count++;
				}
			}
		}
		return count;
	}
// L2PT-36342 end
// L2PT-39289 start
// BVTM-5944 start
	function guiTinNhanCamOn() {
		if (_benhnhan.LOAITIEPNHANID == 0 && _benhnhan.TRANGTHAITIEPNHAN != 0 && _benhnhan.TINH_TRANG_RV == 1 && (_benhnhan.KET_QUA_DTRI == 1 || _benhnhan.KET_QUA_DTRI == 2)) {
			//gửi SMS khi kết thúc BA
			var _guiTin = jsonrpc.AjaxJson.ajaxCALL_SP_S('COM.CAUHINH', 'BVBD_SMS_CAMON');
			if (_guiTin == '1') {
				var tt_bn_sms_arr = jsonrpc.AjaxJson.ajaxCALL_SP_O("VPI.TT.SMS", _tiepnhanid);
				if (tt_bn_sms_arr && tt_bn_sms_arr.length > 0) {
					var _tinnhan = '';
					var _time = jsonrpc.AjaxJson.getSystemDate('DD/MM/YYYY HH24:MI');
					var _date = _time.split(" ")[0];
					if (compareDate(_date + " 21:00:00", _time + ":00", 'DD/MM/YYYY HH:mm:ss')) {
						_time = moment(_date, "DD/MM/YYYY").add(1, "days").format("DD/MM/YYYY") + " 08:00";
					} else {
						_time = "";
					}
					var _loai = "10";
					// BVTM-6490 start: bỏ lấy tin nhắn theo loại
					/*
					var data_ar = jsonrpc.AjaxJson.ajaxCALL_SP_O("HIS.GET.SMS", _loai);
					if (data_ar && data_ar.length > 0) {
						_tinnhan = data_ar[0].SMS_NOIDUNG;
						_tinnhan = _tinnhan.replaceAll('XXX', tt_bn_sms_arr[0].KHOA_KT);
						_tinnhan = _tinnhan.replaceAll('YYY', tt_bn_sms_arr[0].SDTKHOA);
						_tinnhan = removeVietnameseTones(_tinnhan);
					*/
					// BVTM-6490 end
					if (tt_bn_sms_arr[0].SDTBN !== '' && tt_bn_sms_arr[0].DAGUI_SMS != '1') {
						try {
							var sms = new SmsSend();
							// BVTM-6490 sửa gửi tin nhắn theo template mới
							//var kq = sms.sendPost(tt_bn_sms_arr[0].SDTBN, _tinnhan, _time);
							// L2PT-27059 start: thêm thông tin khoa phòng
							var thongtinkhoaphong = {
								ma_khoa : tt_bn_sms_arr[0].MA_KHOA_KT,
								ten_khoa : tt_bn_sms_arr[0].KHOA_KT,
								ma_phong : tt_bn_sms_arr[0].MA_PHONG_KC,
								ten_phong : tt_bn_sms_arr[0].PHONG_KC
							};
							var kq = sms.sendPostTemplate(tt_bn_sms_arr[0].SDTBN, 'CAMON_DIEUTRI', [ removeVietnameseTones(tt_bn_sms_arr[0].KHOA_KT), tt_bn_sms_arr[0].SDTKHOA ], _time,
									_benhnhan.KHAMBENHID_KT, _loai, thongtinkhoaphong);
							// L2PT-27059 end
							if (kq !== null && typeof kq === 'object' && !Array.isArray(kq)) {
								/*
								var objDataLogSMS = new Object();
								objDataLogSMS['req_id'] = kq.REQ_ID;
								objDataLogSMS['resp_code'] = kq.RESP_CODE;
								objDataLogSMS['sdtbenhnhan'] = tt_bn_sms_arr[0].SDTBN;
								objDataLogSMS['schedule_sms'] = _time;
								objDataLogSMS['loai'] = _loai;
								objDataLogSMS['noidung_sms'] = _tinnhan;
								objDataLogSMS['kq_send_sms'] = kq;
								objDataLogSMS['khambenhid'] = _benhnhan.KHAMBENHID_KT;
								var retLogSMS = jsonrpc.AjaxJson.ajaxCALL_SP_I("ADD_NHAC_SMS", JSON.stringify(objDataLogSMS));
								*/
								if (kq.RESP_CODE == '00') {
									var kqCapNhatSMS = jsonrpc.AjaxJson.ajaxCALL_SP_I("VPI.UPDATE.TTSMS", _tiepnhanid);
									if (kqCapNhatSMS == '-1') {
										DlgUtil.showMsg("Có lỗi khi cập nhật trạng thái gửi tin nhắn cảm ơn");
									}
								} else {
									DlgUtil.showMsg("Gửi tin nhắn cảm ơn BN không thành công, mã lỗi: " + kq.RESP_CODE);
								}
							} else {
								DlgUtil.showMsg("Có lỗi khi gửi tin nhắn cảm ơn BN");
							}
						} catch (err) {
							console.log(err)
						}
					}
					// BVTM-6490 start
					/*
					} else {
						DlgUtil.showMsg("Chưa thiết lập tin nhắn");
					}
					*/
					// BVTM-6490 end
				} else {
					DlgUtil.showMsg("Không lấy được thông tin bệnh nhân để gửi tin nhắn");
				}
			}
		}
	}
// BVTM-5944 end
// L2PT-39289 end
// L2PT-31925 start
	function guiHDDT_KTM() {
		var objDsHDDT = {
			TIEPNHANID : _tiepnhanid + ""
		}
		var input = JSON.stringify(objDsHDDT);
		var dsHD = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI.LAY.DSHDDT', input);
		console.log(dsHD);
		for (var i = 0; i < dsHD.length; i++) {
			_phieuthuid_hddt = dsHD[i].PHIEUTHUID;
			guiHDDTTheoPhieu(_phieuthuid_hddt, "", "", {
				luuPhieu : 1
			});
		}
	}
// L2PT-31925 end
// L2PT-71931 start
	function kiemTraQRCODE() {
		// L2PT-118765  start
		if (fConfig.VPI_KIEMTRA_TTQR == 'THUNGAN') {
			var objDsHDDT = {
				ACTION : "DS_QRCODE"
			}
			var input = JSON.stringify(objDsHDDT);
			var dsHD = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI.LAY.DSHDDT', input);
			for (var i = 0; i < dsHD.length; i++) {
				var msg = "Thanh toán QRCODE thành công cho " + dsHD[i].MAHOSOBENHAN + " " + dsHD[i].TENBENHNHAN + " số hóa đơn " + dsHD[i].REQ_SOHOADON + " số tiền " + dsHD[i].DATRA;
				$.bootstrapGrowl(msg, {
					type : 'success',
					delay : 5000,
					width : 500,
					offset : {
						from : "top",
						amount : 50
					},
				});
				if (dsHD[i].TIEPNHANID == _tiepnhanid) {
					DlgUtil.close("divDlgdivShowQR");
					_phieuthuid = dsHD[i].PHIEUTHUID;
					loadGridDataPT(_tiepnhanid);
				}
			}
			var objUpdSta = {
				ACTION : "CAPNHAT_DSPT",
				DSPHIEU : dsHD
			}
			var dsHD = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI.LAY.DSHDDT', JSON.stringify(objUpdSta));
		}
		// L2PT-118765  end
		else {
			if (QRCODEBASE64STR && QRCODEBASE64STR != "") {
				var objDsHDDT = {
					TIEPNHANID : _tiepnhanid + "",
					ACTION : "CHECK_QRCODE",
					QRCODEBASE64 : QRCODEBASE64STR
				}
				var input = JSON.stringify(objDsHDDT);
				var dsHD = jsonrpc.AjaxJson.ajaxCALL_SP_O('VPI.LAY.DSHDDT', input);
				if (dsHD && dsHD.length > 0) {
					QRCODEBASE64STR = "";
					DlgUtil.close("divDlgdivShowQR");
					_phieuthuid = dsHD[0].PHIEUTHUID;
					loadGridDataPT(_tiepnhanid);
				}
			}
		}
	}
// L2PT-71931 end
// L2PT-28379 start
	function hienThiQR(opt) {
		// L2PT-31763 start
		if (VPI_HIENTHI_QRCODE == '1') {
			var dlgName = "divDlgdivShowQR";
			opt.dlgName = dlgName;
			var dlgPopup = DlgUtil.buildPopupUrl(dlgName, "divShowQR", "manager.jsp?func=../vienphi/VPI01T029_qrcode", opt, "QR CODE thanh toán", 512, 512);
			DlgUtil.open(dlgName);
		} else if (VPI_HIENTHI_QRCODE == '2') {
			window.qrcode = opt;
			var qrWidth = screen.width;
			var qrHeight = screen.height;
			var qrLeft = screen.width;
			if ('getScreenDetails' in window) {
				window.getScreenDetails().then(function(screenDetails) {
					qrWidth = screenDetails.screens[0].availWidth;
					qrHeight = screenDetails.screens[0].availHeight;
					qrLeft = 0;
					if (screen.isExtended && screenDetails.screens.length > 1) {
						qrWidth = screenDetails.screens[1].availWidth
						qrHeight = screenDetails.screens[1].availHeight
						qrLeft = screenDetails.screens[0].width
					}
					var param = 'width=' + qrWidth + ',height=' + qrHeight + ',left=' + qrLeft + ',fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no';
					qrWindow = window.open('manager.jsp?func=../vienphi/VPI01T029_qrcode&showMode=dlg', 'QRCODE', param);
				}, function(getScreenDetailsError) {
					console.log(getScreenDetailsError);
					//DlgUtil.showMsg("Lỗi lấy thông tin màn hình");
					var param = 'width=' + qrWidth + ',height=' + qrHeight + ',left=' + qrLeft + ',fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no';
					qrWindow = window.open('manager.jsp?func=../vienphi/VPI01T029_qrcode&showMode=dlg', 'QRCODE', param);
				});
			} else {
				var param = 'width=' + qrWidth + ',height=' + qrHeight + ',left=' + qrLeft + ',fullscreen=yes,resizable=no,toolbar=no,menubar=no,location=no,status=no';
				qrWindow = window.open('manager.jsp?func=../vienphi/VPI01T029_qrcode&showMode=dlg', 'QRCODE', param);
			}
			//qrWindow.postMessage(paramInput, '*');
		}
		// L2PT-31763 end
	}
	// L2PT-28379 end
	// L2PT-73710 start
	function loadComboPhong() {
		var _khoa_dachon = $("#cboKHOAKT").val();
		var sql_par = RSUtil.buildParam("", [ _khoa_dachon ]);
		ComboUtil.getComboTag("cboPHONGKT", 'VPI.PHONG.PQ', sql_par, "", {}, 'sql', '', '');
		$('#cboPHONGKT').SumoSelect({
			search : true,
			searchText : 'Tìm kiếm',
			okCancelInMulti : true,
			selectAll : true
		});
		$('#cboPHONGKT')[0].sumo.reload();
		$('#cboPHONGKT')[0].sumo.selectAll();
	}
	// L2PT-73710 end
	// L2PT-80276 start
	function hoanTraKTM(tnid, ptid, sotien, objDataPT) { // L2PT-90923
		if (fConfig.VPI_CHIHO_QRCODE != '1') {
			DlgUtil.showMsg("Không hỗ trợ hoàn trả thanh toán QRCODE");
			return;
		}
		// L2PT-90923 start
		var ma_tra_cuu = jsonrpc.AjaxJson.ajaxCALL_SP_S("VPI.GEN.ORDERID", _hospital_code);
		objDataPT.MA_GD_CK = ma_tra_cuu;
		// L2PT-90923 end
		var opt = new Object;
		opt.TIEPNHANID = tnid;
		opt.PHIEUTHUID = ptid;
		opt.SOTIEN = sotien;
		opt.MATRACUU = ma_tra_cuu; // L2PT-90923
		opt.objData = objDataPT; // L2PT-90923
		var dlgName = "";
		// L2PT-118724 start
		if (fConfig.VPI_DUYET_CHIHO == '1') {
			opt.ACTION = "DUYET";
			dlgName = "divDlgdivRepay";
			opt.dlgName = dlgName;
			var dlgPopup = DlgUtil.buildPopupUrl(dlgName, "divRepay", "manager.jsp?func=../vienphi/VPI01T038_hoantra_ktm", opt, "Hoàn trả không tiền mặt", 1000, 365);
		} else {
			dlgName = "divDlgdivRefundQR";
			opt.dlgName = dlgName;
			var dlgPopup = DlgUtil.buildPopupUrl(dlgName, "divRefundQR", "manager.jsp?func=../vienphi/VPI01T034_hoantra_qrocde", opt, "Hoàn trả thanh toán QRCODE", 1000, 365);
		}
		// L2PT-118724 end
		DlgUtil.open(dlgName);
	}
	// L2PT-80276 end
}

-- =====================================================
-- Stored Procedures for NTU01H104 Digital Signature Workflow
-- Level-based Signature Cancellation and Return Signature
-- =====================================================

-- Procedure: NTU01H104.07 - Hủy ký theo level
-- Purpose: Cancel signature by level, restore previous state
CREATE OR REPLACE PROCEDURE NTU01H104_07_HuyKyTheoLevel(
    i_uid VARCHAR2,
    i_hid VARCHAR2, 
    i_sch VARCHAR2,
    i_ip VARCHAR2,
    i_json_param CLOB,
    o_result OUT NUMBER
) IS
    v_param_hashed VARCHAR2(500);
    v_levelky NUMBER;
    v_loaiphieu NUMBER;
    v_kycapid NUMBER;
    v_signtype VARCHAR2(10);
    v_current_level NUMBER;
    v_higher_level_exists NUMBER := 0;
    v_ca_detail_count NUMBER := 0;
    v_function VARCHAR2(100) := 'NTU01H104.07';
    
    -- Parse JSON parameters
    v_json JSON_OBJECT_T;
BEGIN
    -- Initialize result
    o_result := 0;
    
    -- Parse JSON input
    v_json := JSON_OBJECT_T.parse(i_json_param);
    v_param_hashed := v_json.get_string('PARAM_HASHED');
    v_levelky := v_json.get_number('LEVELKY');
    v_loaiphieu := v_json.get_number('LOAIPHIEU');
    v_kycapid := v_json.get_number('KYCAPID');
    v_signtype := v_json.get_string('SIGNTYPE');
    
    -- Check if signature exists at current level
    SELECT COUNT(*)
    INTO v_ca_detail_count
    FROM his_data.emr_ca_detail
    WHERE param_hashed = v_param_hashed
    AND levelky = v_levelky
    AND trangthai = '2'; -- Signed status
    
    IF v_ca_detail_count = 0 THEN
        o_result := 3; -- Not signed at this level
        RETURN;
    END IF;
    
    -- Check if higher level signatures exist
    SELECT COUNT(*)
    INTO v_higher_level_exists
    FROM his_data.emr_ca_detail
    WHERE param_hashed = v_param_hashed
    AND levelky > v_levelky
    AND trangthai = '2'; -- Signed status
    
    IF v_higher_level_exists > 0 THEN
        o_result := 2; -- Cannot cancel - higher level already signed
        RETURN;
    END IF;
    
    -- Perform level-based cancellation
    -- 1. Update current level signature status to cancelled
    UPDATE his_data.emr_ca_detail
    SET trangthai = '3', -- Cancelled status
        ngayhuy = SYSDATE,
        nguoihuy = i_uid,
        lydohuy = 'Hủy ký theo level'
    WHERE param_hashed = v_param_hashed
    AND levelky = v_levelky;
    
    -- 2. Restore document to unsigned state for this level
    UPDATE his_data.emr_phieu_kycap
    SET trangthai = '1', -- Unsigned status
        ngaycapnhat = SYSDATE,
        nguoicapnhat = i_uid
    WHERE phieukycapid = v_kycapid
    AND levelky = v_levelky;
    
    -- 3. Remove XML signature data for this level
    DELETE FROM his_data.emr_ca_xml_backup
    WHERE param_hashed = v_param_hashed
    AND levelky = v_levelky;
    
    -- 4. Log the cancellation action
    INSERT INTO his_data.emr_ca_log (
        param_hashed,
        levelky,
        action_type,
        action_date,
        action_user,
        description
    ) VALUES (
        v_param_hashed,
        v_levelky,
        'CANCEL_LEVEL',
        SYSDATE,
        i_uid,
        'Hủy ký theo level - Level: ' || v_levelky
    );
    
    COMMIT;
    o_result := 1; -- Success
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        o_result := 0; -- Error
        -- Log error
        INSERT INTO his_data.error_log (
            function_name,
            error_message,
            error_date,
            user_id
        ) VALUES (
            v_function,
            SQLERRM,
            SYSDATE,
            i_uid
        );
        COMMIT;
END;
/

-- Procedure: NTU01H104.08 - Chuyển trả ký theo level
-- Purpose: Return signature to lower level for re-signing
CREATE OR REPLACE PROCEDURE NTU01H104_08_ChuyenTraKyTheoLevel(
    i_uid VARCHAR2,
    i_hid VARCHAR2,
    i_sch VARCHAR2, 
    i_ip VARCHAR2,
    i_json_param CLOB,
    o_result OUT NUMBER
) IS
    v_param_hashed VARCHAR2(500);
    v_levelky NUMBER;
    v_loaiphieu NUMBER;
    v_kycapid NUMBER;
    v_lydo VARCHAR2(1000);
    v_nguoichuyentra VARCHAR2(50);
    v_lower_level NUMBER;
    v_lower_level_exists NUMBER := 0;
    v_current_signed NUMBER := 0;
    v_function VARCHAR2(100) := 'NTU01H104.08';
    
    -- Parse JSON parameters
    v_json JSON_OBJECT_T;
BEGIN
    -- Initialize result
    o_result := 0;
    
    -- Parse JSON input
    v_json := JSON_OBJECT_T.parse(i_json_param);
    v_param_hashed := v_json.get_string('PARAM_HASHED');
    v_levelky := v_json.get_number('LEVELKY');
    v_loaiphieu := v_json.get_number('LOAIPHIEU');
    v_kycapid := v_json.get_number('KYCAPID');
    v_lydo := v_json.get_string('LYDO');
    v_nguoichuyentra := v_json.get_string('NGUOICHUYENTRA');
    
    -- Check if current level is already signed (should not be for return signature)
    SELECT COUNT(*)
    INTO v_current_signed
    FROM his_data.emr_ca_detail
    WHERE param_hashed = v_param_hashed
    AND levelky = v_levelky
    AND trangthai = '2'; -- Signed status
    
    IF v_current_signed > 0 THEN
        o_result := 4; -- Already signed at current level
        RETURN;
    END IF;
    
    -- Find the immediate lower level
    SELECT MAX(levelky)
    INTO v_lower_level
    FROM his_data.emr_phieu_kycap
    WHERE param_hashed = v_param_hashed
    AND levelky < v_levelky;
    
    IF v_lower_level IS NULL THEN
        o_result := 3; -- Already at lowest level
        RETURN;
    END IF;
    
    -- Check if lower level exists in signature workflow
    SELECT COUNT(*)
    INTO v_lower_level_exists
    FROM his_data.emr_phieu_kycap
    WHERE param_hashed = v_param_hashed
    AND levelky = v_lower_level;
    
    IF v_lower_level_exists = 0 THEN
        o_result := 2; -- Lower level not found
        RETURN;
    END IF;
    
    -- Perform return signature workflow
    -- 1. Update lower level status to return signature
    UPDATE his_data.emr_phieu_kycap
    SET trangthai = '4', -- Return signature status
        ngaycapnhat = SYSDATE,
        nguoicapnhat = i_uid,
        ghichu = 'Chuyển trả ký từ level ' || v_levelky || ': ' || v_lydo
    WHERE param_hashed = v_param_hashed
    AND levelky = v_lower_level;
    
    -- 2. Cancel any existing signature at lower level
    UPDATE his_data.emr_ca_detail
    SET trangthai = '4', -- Return signature status
        ngayhuy = SYSDATE,
        nguoihuy = i_uid,
        lydohuy = 'Chuyển trả ký: ' || v_lydo
    WHERE param_hashed = v_param_hashed
    AND levelky = v_lower_level;
    
    -- 3. Create return signature record
    INSERT INTO his_data.emr_chuyen_tra_ky (
        param_hashed,
        level_from,
        level_to,
        lydo_chuyen_tra,
        nguoi_chuyen_tra,
        ngay_chuyen_tra,
        trangthai
    ) VALUES (
        v_param_hashed,
        v_levelky,
        v_lower_level,
        v_lydo,
        v_nguoichuyentra,
        SYSDATE,
        '1' -- Active return signature
    );
    
    -- 4. Log the return signature action
    INSERT INTO his_data.emr_ca_log (
        param_hashed,
        levelky,
        action_type,
        action_date,
        action_user,
        description
    ) VALUES (
        v_param_hashed,
        v_levelky,
        'RETURN_SIGNATURE',
        SYSDATE,
        i_uid,
        'Chuyển trả ký từ level ' || v_levelky || ' về level ' || v_lower_level || ': ' || v_lydo
    );
    
    COMMIT;
    o_result := 1; -- Success
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        o_result := 0; -- Error
        -- Log error
        INSERT INTO his_data.error_log (
            function_name,
            error_message,
            error_date,
            user_id
        ) VALUES (
            v_function,
            SQLERRM,
            SYSDATE,
            i_uid
        );
        COMMIT;
END;
/

-- Create supporting tables if they don't exist

-- Table for return signature tracking
CREATE TABLE his_data.emr_chuyen_tra_ky (
    id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    param_hashed VARCHAR2(500) NOT NULL,
    level_from NUMBER NOT NULL,
    level_to NUMBER NOT NULL,
    lydo_chuyen_tra VARCHAR2(1000),
    nguoi_chuyen_tra VARCHAR2(50),
    ngay_chuyen_tra DATE DEFAULT SYSDATE,
    trangthai VARCHAR2(10) DEFAULT '1',
    created_date DATE DEFAULT SYSDATE
);

-- Table for CA action logging
CREATE TABLE his_data.emr_ca_log (
    id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    param_hashed VARCHAR2(500) NOT NULL,
    levelky NUMBER,
    action_type VARCHAR2(50) NOT NULL,
    action_date DATE DEFAULT SYSDATE,
    action_user VARCHAR2(50),
    description VARCHAR2(2000),
    created_date DATE DEFAULT SYSDATE
);

-- Table for XML backup during signature process
CREATE TABLE his_data.emr_ca_xml_backup (
    id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    param_hashed VARCHAR2(500) NOT NULL,
    levelky NUMBER NOT NULL,
    xml_content CLOB,
    signature_data CLOB,
    backup_date DATE DEFAULT SYSDATE,
    created_by VARCHAR2(50)
);

-- Add indexes for performance
CREATE INDEX idx_emr_chuyen_tra_ky_param ON his_data.emr_chuyen_tra_ky(param_hashed);
CREATE INDEX idx_emr_ca_log_param ON his_data.emr_ca_log(param_hashed);
CREATE INDEX idx_emr_ca_xml_backup_param ON his_data.emr_ca_xml_backup(param_hashed, levelky);

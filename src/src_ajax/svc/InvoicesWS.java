package svc;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.StringReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.xml.parsers.DocumentBuilder;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.log4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import com.google.gson.Gson;
import com.vnpthis.einvoice.EInvoice;
import com.vnpthis.einvoice.EInvoiceFactory;
import com.vnptit.cache.UserInfoCache;
import com.vnptit.common.SysObject;
import com.vnptit.jwt.JsonWebTokenRestApiFactory;
import com.vnptit.util.CheckObjectUtils;
import com.vnptit.util.CommonUtility;
import com.vnptit.util.SysConstants;
import com.vnptit.util.XmlDocumentBuilder;
import com.vsc.util.CryptoUtil;
import com.vsc.util.HttpUtil;

import AjaxUtil.AjaxJson;
import intellsoft.db.DBUtility;
import jsonutil.JSONArray;
import jsonutil.JSONObject;

public class InvoicesWS {

	// private String url_view =
	// "https://webservicedemo.vnpt-invoice.com.vn/PortalService.asmx?wsdl";
	// private String url_import =
	// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
	// private String url_cancel =
	// "https://webservicedemo.vnpt-invoice.com.vn/BusinessService.asmx?wsdl";
	//
	// private String ws_usr = "hisservice";
	// private String ws_pwd = "123456aA@";
	// private String ws_usr_acc = "hisadmin";
	// private String ws_pwd_acc = "123456aA@";
	private static final int CONNECT_TIMEOUT = 10000;
	private static final int SOCKET_TIMEOUT = 15000;

	private int connectTimeout = CONNECT_TIMEOUT;
	private int socketTimout = SOCKET_TIMEOUT;
	private int connectionRequestTimeout = SOCKET_TIMEOUT;
	private static final Logger logsWS = Logger.getLogger("logCallWS");
	private static final String USER_AGENT = "Mozilla/5.0";

	private static String INVOICES_URL_VIEW_CONF = "INVOICES_URL_VIEW";
	private static String INVOICES_URL_IMPORT_CONF = "INVOICES_URL_IMPORT";
	private static String INVOICES_URL_CANCEL_CONF = "INVOICES_URL_CANCEL";
	private static String VPI_N_TKHD_CONF = "VPI_N_TKHD";

	private String dbName;
	private String mediaSchema;
	private String uuid;

	public InvoicesWS(String dbName, String mediaSchema, String uuid) {
		this.dbName = dbName;
		this.mediaSchema = mediaSchema;
		this.uuid = uuid;
	}

	public InvoicesWS() throws Exception {
		System.out.println("\n>>> INIT");
	}

	// private static String CallWebService(String Request, String url, String
	// SOAPAction) {
	//
	// long startTimeLogin = System.currentTimeMillis();
	//
	// StringBuilder buf = new StringBuilder();
	// try {
	// URL url_ws = new URL(url);
	// URLConnection urlc = url_ws.openConnection();
	// urlc.setConnectTimeout(15000);
	// urlc.setReadTimeout(30000);
	// urlc.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
	// urlc.setRequestProperty("SOAPAction", SOAPAction);
	// urlc.setRequestProperty("Accept-Charset", "UTF-8");
	// urlc.setDoOutput(true);
	// urlc.setDoInput(true);
	//
	// OutputStreamWriter writer = new
	// OutputStreamWriter(urlc.getOutputStream(), "UTF-8");
	// writer.write(Request);
	// writer.flush();
	// BufferedReader in = new BufferedReader(new
	// InputStreamReader(urlc.getInputStream(), "UTF-8"));
	// String res_line;
	// while ((res_line = in.readLine()) != null) {
	// buf.append(res_line);
	// }
	// in.close();
	// String respone = buf.toString();
	// logsWS.info("HISL2_InvoicesWS|url:" + url + "|SOAPAction:"+ SOAPAction +
	// "|execution_time(ms):" + (System.currentTimeMillis() - startTimeLogin)
	// +"|Request=" + Request + "|respone=" + respone);
	//
	// return respone;
	//
	// } catch (Exception e) {
	// logsWS.error("InvoicesWS|CallWebService|Request: " + Request + "|url: " +
	// url + "|SOAPAction: " + "|Exception: "
	// + ExceptionUtils.getStackTrace(e));
	// return "";
	// }
	// }
	private static String CallWebService(String Request, String url, String SOAPAction) {

		long startTimeLogin = System.currentTimeMillis();

		StringBuilder buf = new StringBuilder();
		try {

			URL url_ws = new URL(url);
			HttpURLConnection urlc = (HttpURLConnection) url_ws.openConnection();
			urlc.setConnectTimeout(15000);
			urlc.setReadTimeout(30000);
			urlc.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
			urlc.setRequestProperty("SOAPAction", SOAPAction);
			urlc.setRequestProperty("Accept-Charset", "UTF-8");
			urlc.setDoOutput(true);
			urlc.setDoInput(true);
			String respone = "";
			OutputStreamWriter writer = new OutputStreamWriter(urlc.getOutputStream(), "UTF-8");
			writer.write(Request);
			writer.flush();
			if (urlc.getResponseCode() == 200) {
				BufferedReader in = new BufferedReader(new InputStreamReader(urlc.getInputStream(), "UTF-8"));
				String res_line;
				while ((res_line = in.readLine()) != null) {
					buf.append(res_line);
				}
				in.close();
				respone = buf.toString();
			} else {
				respone = "ERR:-100 " + urlc.getResponseCode() + ": " + urlc.getResponseMessage();
			}
			urlc.disconnect();

			logsWS.info("HISL2_InvoicesWS|url:" + url + "|SOAPAction:" + SOAPAction + "|execution_time(ms):"
					+ (System.currentTimeMillis() - startTimeLogin) + "|Request=" + Request + "|respone=" + respone);

			return respone;

		} catch (Exception e) {
			logsWS.error("InvoicesWS|CallWebService|Request: " + Request + "|url: " + url + "|SOAPAction: "
					+ "|Exception: " + ExceptionUtils.getStackTrace(e));
			return "ERR:-100 " + e.getMessage();
		}
	}

	public String viewHoaDon(String key, String url_id, String ws_usr, String ws_pwd) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PortalService.asmx?wsdl";
		ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
		String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
				+ "	   <soapenv:Header/>" + "			   <soapenv:Body>" + "		      <tem:getInvViewFkeyNoPay>"
				+ "	         <!--Optional:-->" + "         <tem:fkey>" + key + "</tem:fkey>" + "      <!--Optional:-->"
				+ "   		<tem:userName>" + ws_usr + "</tem:userName>" + "         <tem:userPass>" + ws_pwd
				+ "</tem:userPass>" + "     </tem:getInvViewFkeyNoPay>" + "  </soapenv:Body>" + "</soapenv:Envelope>";
		String SOAPAction = "http://tempuri.org/getInvViewFkeyNoPay";
		String hid = CommonUtility.getHID(this.uuid);

		String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
		String INVOICES_URL_VIEW = "";
		if ("1".equals(VPI_N_TKHD)) {
			INVOICES_URL_VIEW = getUrl(url_id, "INVOICES_URL_VIEW");
		} else {
			INVOICES_URL_VIEW = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_VIEW_CONF);
		}

		String value = CallWebService(Request, INVOICES_URL_VIEW, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value + ";URL:" + INVOICES_URL_VIEW;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("getInvViewFkeyNoPayResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("getInvViewFkeyNoPayResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String getInvErrorViewFkey(String key, String url_id, String ws_usr, String ws_pwd) throws Exception {
		try {
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "	   <soapenv:Header/>" + "			   <soapenv:Body>" + "		      <tem:GetInvErrorViewFkey>"
					+ "	         <!--Optional:-->" + "         <tem:fkey>" + key + "</tem:fkey>"
					+ "      <!--Optional:-->" + "   		<tem:userName>" + ws_usr + "</tem:userName>"
					+ "         <tem:userPass>" + ws_pwd + "</tem:userPass>" + "     </tem:GetInvErrorViewFkey>"
					+ "  </soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/GetInvErrorViewFkey";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_VIEW = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_VIEW = getUrl(url_id, "INVOICES_URL_VIEW");
			} else {
				INVOICES_URL_VIEW = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_VIEW_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_VIEW, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("GetInvErrorViewFkeyResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("GetInvErrorViewFkeyResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String viewHoaDonPattern(String key, String url_id, String ws_usr, String ws_pwd, String pattern)
			throws Exception {
		try {
			// String url =
			// "https://webservicedemo.vnpt-invoice.com.vn/PortalService.asmx?wsdl";
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "	   <soapenv:Header/>" + "			   <soapenv:Body>"
					+ "		      <tem:getInvViewFkeyNoPayPattern>" + "	         <!--Optional:-->"
					+ "         <tem:fkey>" + key + "</tem:fkey>" + "      <!--Optional:-->" + "   		<tem:userName>"
					+ ws_usr + "</tem:userName>" + "         <tem:userPass>" + ws_pwd + "</tem:userPass>"
					+ "         <tem:pattern>" + pattern + "</tem:pattern>" + "     </tem:getInvViewFkeyNoPayPattern>"
					+ "  </soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/getInvViewFkeyNoPayPattern";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_VIEW = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_VIEW = getUrl(url_id, "INVOICES_URL_VIEW");
			} else {
				INVOICES_URL_VIEW = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_VIEW_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_VIEW, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("getInvViewFkeyNoPayPatternResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("getInvViewFkeyNoPayPatternResult").item(0)
							.getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String convertForStore(String invToken, String ws_usr, String ws_pwd) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PortalService.asmx?wsdl";
		ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
		String Request = "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">"
				+ "<soap:Body>" + "<convertForStore xmlns=\"http://tempuri.org/\">" + "<invToken>" + invToken
				+ "</invToken>" + "<userName>" + ws_usr + "</userName>" + "<userPass>" + ws_pwd + "</userPass>"
				+ "</convertForStore>" + "</soap:Body>" + "</soap:Envelope>";
		String SOAPAction = "http://tempuri.org/convertForStore";
		String hid = CommonUtility.getHID(this.uuid);

		final String INVOICES_URL_VIEW = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_VIEW_CONF);

		String value = CallWebService(Request, INVOICES_URL_VIEW, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("convertForStoreResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("convertForStoreResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String convertForStoreFkey(String fkey, String url_id, String ws_usr, String ws_pwd) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PortalService.asmx?wsdl";
		try {
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "   <soapenv:Header/>" + "   <soapenv:Body>" + "      <tem:convertForStoreFkey>"
					+ "         <!--Optional:-->" + "         <tem:fkey>" + fkey + "</tem:fkey>"
					+ "         <!--Optional:-->" + "         <tem:userName>" + ws_usr + "</tem:userName>"
					+ "         <!--Optional:-->" + "         <tem:userPass>" + ws_pwd + "</tem:userPass>"
					+ "      </tem:convertForStoreFkey>" + "   </soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/convertForStoreFkey";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_VIEW = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_VIEW = getUrl(url_id, "INVOICES_URL_VIEW");
			} else {
				INVOICES_URL_VIEW = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_VIEW_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_VIEW, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("convertForStoreFkeyResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("convertForStoreFkeyResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "";
		}
	}

	public String convertForVerifyFkey(String fkey, String url_id, String ws_usr, String ws_pwd) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PortalService.asmx?wsdl";
		ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
		String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
				+ "   <soapenv:Header/>" + "   <soapenv:Body>" + "      <tem:convertForVerifyFkey>"
				+ "         <!--Optional:-->" + "         <tem:fkey>" + fkey + "</tem:fkey>"
				+ "         <!--Optional:-->" + "         <tem:userName>" + ws_usr + "</tem:userName>"
				+ "         <!--Optional:-->" + "         <tem:userPass>" + ws_pwd + "</tem:userPass>"
				+ "      </tem:convertForVerifyFkey>" + "   </soapenv:Body>" + "</soapenv:Envelope>";
		String SOAPAction = "http://tempuri.org/convertForVerifyFkey";
		String hid = CommonUtility.getHID(this.uuid);

		String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
		String INVOICES_URL_VIEW = "";
		if ("1".equals(VPI_N_TKHD)) {
			INVOICES_URL_VIEW = getUrl(url_id, "INVOICES_URL_VIEW");
		} else {
			INVOICES_URL_VIEW = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_VIEW_CONF);
		}

		String value = CallWebService(Request, INVOICES_URL_VIEW, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("convertForVerifyFkeyResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("convertForVerifyFkeyResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String viewHoaDon_notPH(String key, String url_id, String ws_usr, String ws_pwd) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PortalService.asmx?wsdl";
		ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
		String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
				+ "	   <soapenv:Header/>" + "			   <soapenv:Body>" + "		      <tem:getInvViewFkey_NewInv>"
				+ "	         <!--Optional:-->" + "         <tem:fkey>" + key + "</tem:fkey>" + "      <!--Optional:-->"
				+ "   		<tem:userName>" + ws_usr + "</tem:userName>" + "         <tem:userPass>" + ws_pwd
				+ "</tem:userPass>" + "     </tem:getInvViewFkey_NewInv>" + "  </soapenv:Body>" + "</soapenv:Envelope>";
		String SOAPAction = "http://tempuri.org/getInvViewFkey_NewInv";
		String hid = CommonUtility.getHID(this.uuid);

		String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
		String INVOICES_URL_VIEW = "";
		if ("1".equals(VPI_N_TKHD)) {
			INVOICES_URL_VIEW = getUrl(url_id, "INVOICES_URL_VIEW");
		} else {
			INVOICES_URL_VIEW = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_VIEW_CONF);
		}

		String value = CallWebService(Request, INVOICES_URL_VIEW, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("getInvViewFkey_NewInvResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("getInvViewFkey_NewInvResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String importHoaDon(String xmlInvData, String url_id, String ws_usr_acc, String ws_pwd_acc, String ws_usr,
			String ws_pwd, String pattern, String serial) throws Exception {
		try {
			// String url =
			// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:ImportAndPublishInv>" + "       <tem:Account>"
					+ ws_usr_acc + "</tem:Account>" + "      <tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>"
					+ "      <tem:xmlInvData><![CDATA[" + xmlInvData + "]]></tem:xmlInvData>" + "      <tem:username>"
					+ ws_usr + "</tem:username>" + "      <tem:password>" + ws_pwd + "</tem:password>"
					+ "     <tem:pattern>" + pattern + "</tem:pattern>" + "     <tem:serial>" + serial + "</tem:serial>"
					+ "     <tem:convert>0</tem:convert>" + "  </tem:ImportAndPublishInv>" + " </soapenv:Body>"
					+ "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/ImportAndPublishInv";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_IMPORT = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_IMPORT = getUrl(url_id, "INVOICES_URL_IMPORT");
			} else {
				INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_IMPORT_CONF);
				// final String INVOICES_URL_IMPORT =
				// CommonUtility.getComConf(this.dbName, hid,
				// INVOICES_URL_IMPORT_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_IMPORT, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("ImportAndPublishInvResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("ImportAndPublishInvResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String importInvoiceByFkey(String fkey, String url_id, String ws_usr_acc, String ws_pwd_acc, String ws_usr,
			String ws_pwd, String pattern, String serial) throws Exception {
		try {
			String user_id;
			String db_schema;
			String provide_id;
			String hospital_id;

			String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
					.getJsonWebTokenByType().getID(uuid);
			JSONObject userInfo = UserInfoCache.get(uuidCache);
			if (userInfo != null) {
				user_id = userInfo.getString(SysConstants.USER_ID);
				db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
				provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
				hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
			} else {
				String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
						.getJsonWebTokenByType().getLoginUserData(uuid);
				SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
				user_id = _userInfo.getUserId();
				db_schema = _userInfo.getDbSchema();
				provide_id = _userInfo.getProvinceId();
				hospital_id = _userInfo.getHospitalId();
			}
			AjaxJson ajaxJson = new AjaxJson(this.dbName);
			String xmlInvData = ajaxJson.ajaxCALL_SP_S("{?=call vpi_day_hddt(?2S,?3S,?4S,?5S,?6S)}",
					user_id + "$" + hospital_id + "$" + db_schema + "$" + provide_id + "$" + fkey, new Object[] {});
			String VPI_FN_IMPORT_HDDT = CommonUtility.getComConf(this.dbName, hospital_id, "VPI_FN_IMPORT_HDDT");
			String VPI_HDDT_TOKEN = CommonUtility.getComConf(this.dbName, hospital_id, "VPI_HDDT_TOKEN");
			String INVOICES_HAI_QUYENHD = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_HAI_QUYENHD");
			String INVOICES_ACC = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_ACC");
			String VPI_SERIAL_PATTERN_HDDT = CommonUtility.getComConf(this.dbName, hospital_id,
					"VPI_SERIAL_PATTERN_HDDT");
			if (url_id.equals("-1")) {
				url_id = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_URL_IMPORT");
				ws_usr_acc = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_WS_USER_ACC");
				ws_pwd_acc = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_WS_PWD_ACC");
				ws_usr = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_WS_USER");
				ws_pwd = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_WS_PWD");
				pattern = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_WS_PATTERN");
				serial = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_WS_SERIAL");
			}
			if (INVOICES_ACC.equals("1")) {
				String strAccWs = ajaxJson.ajaxCALL_SP_O("{?=call vpi01t013_getdata(?2S,?3S,?4S,?5S)}",
						user_id + "$" + hospital_id + "$" + db_schema + "$" + provide_id, new Object[] {});
				JSONArray arrAcc = new JSONArray(strAccWs);
				JSONObject jsonAcc = arrAcc.getJSONObject(0);
				String _usr_acc = jsonAcc.getString("INVOICES_USER");
				String _pwd_acc = jsonAcc.getString("INVOICES_PASS");
				if (_usr_acc == null || _pwd_acc == null || _usr_acc.trim().isEmpty() || _pwd_acc.trim().isEmpty()) {
					return "ERR:Chưa có acc/pass hóa đơn điện tử";
				}
				ws_usr_acc = _usr_acc;
				ws_pwd_acc = _pwd_acc;
			}

			if (VPI_SERIAL_PATTERN_HDDT.equals("1")) {
				String strBillInfo = ajaxJson.ajaxCALL_SP_O("{?=call vpi01t001_tt_pt(?2S,?3S,?4S,?5S,?6S)}",
						user_id + "$" + hospital_id + "$" + db_schema + "$" + provide_id + "$" + fkey, new Object[] {});
				JSONArray arrBillInfo = new JSONArray(strBillInfo);
				JSONObject jsonBillInfo = arrBillInfo.getJSONObject(0);
				if (jsonBillInfo != null) {
					String mauSo = jsonBillInfo.getString("MAU_SO");
					String kyHieu = jsonBillInfo.getString("KY_HIEU");
					;
					if (mauSo == null || kyHieu == null || mauSo.trim().isEmpty() || kyHieu.trim().isEmpty()) {
						return "ERR:Sổ hóa đơn chưa có mẫu số hoặc ký hiệu";
					}
					pattern = mauSo;
					serial = kyHieu;
				} else {
					return "ERR:Không lấy được thông tin hóa đơn";
				}
			}

			String resultImportInvoice = "";
			if (VPI_FN_IMPORT_HDDT.equals("1")) {
				resultImportInvoice = importInvWithPattern(xmlInvData, url_id, ws_usr, ws_pwd, pattern, serial);
			} else if (VPI_FN_IMPORT_HDDT.equals("2")) {
				resultImportInvoice = importInvByPattern(xmlInvData, url_id, ws_usr_acc, ws_pwd_acc, ws_usr, ws_pwd,
						pattern, serial);
			} else if (VPI_FN_IMPORT_HDDT.equals("3")) {
				resultImportInvoice = importHoaDon_notPH(xmlInvData, url_id, ws_usr_acc, ws_pwd_acc, ws_usr, ws_pwd,
						pattern, serial);
			} else if (VPI_HDDT_TOKEN.equals("1")) {
				resultImportInvoice = importHoaDon_notPH(xmlInvData, url_id, ws_usr_acc, ws_pwd_acc, ws_usr, ws_pwd,
						pattern, serial);
			} else if (INVOICES_HAI_QUYENHD.equals("1")) {
				// case cho BV SNPYN
				String INVOICES_BH_PATTERN = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_BH_PATTERN");
				String INVOICES_BH_SERIAL = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_BH_SERIAL");
				String INVOICES_DV_PATTERN = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_DV_PATTERN");
				String INVOICES_DV_SERIAL = CommonUtility.getComConf(this.dbName, hospital_id, "INVOICES_DV_SERIAL");
				// check phieu la hoa don thu khac: 4 la thu khac, khac 4 ko
				// phai thu khac
				int _flag_phieubh_invoi = ajaxJson.ajaxCALL_SP_I("{?=call vpi_invoi_flagbh(?2S,?3S,?4S,?5S,?6S)}",
						user_id + "$" + db_schema + "$" + hospital_id + "$" + provide_id + "$" + fkey, new Object[] {});
				// L2PT-28880 start: sua serial va pattern voi truong hop dung 2
				// quyen hddt
				if (_flag_phieubh_invoi == 4) {
					pattern = INVOICES_DV_PATTERN;
					serial = INVOICES_DV_SERIAL;
				} else {
					pattern = INVOICES_BH_PATTERN;
					serial = INVOICES_BH_SERIAL;
				}
				resultImportInvoice = importHoaDon(xmlInvData, url_id, ws_usr_acc, ws_pwd_acc, ws_usr, ws_pwd, pattern,
						serial);
				// L2PT-28880 end: sua serial va pattern voi truong hop dung 2
				// quyen hddt

			} else {
				resultImportInvoice = importHoaDon(xmlInvData, url_id, ws_usr_acc, ws_pwd_acc, ws_usr, ws_pwd, pattern,
						serial);
			}
			if (resultImportInvoice.substring(0, 3).equals("OK:")) {
				String invoiceNumber = resultImportInvoice.split(":")[1];
				int resultUpdateInvoice = ajaxJson.ajaxCALL_SP_I(
						"{?=call vpi_update_hddt(?2S,?3S,?4S,?5S,?6S,?7s,?8s,?9s,?10S)}",
						user_id + "$" + db_schema + "$" + hospital_id + "$" + provide_id + "$" + "1" + "$" + pattern
								+ "$" + serial + "$" + invoiceNumber + "$" + fkey,
						new Object[] {});
				System.out.println(resultUpdateInvoice);
			}

			return resultImportInvoice;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String importHoaDonMTT(String xmlInvData, String url_id, String ws_usr_acc, String ws_pwd_acc, String ws_usr,
			String ws_pwd, String pattern, String serial) throws Exception {
		try {
			// String url =
			// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:ImportAndPublishInvMTT>"
					+ "       <tem:Account>" + ws_usr_acc + "</tem:Account>" + "      <tem:ACpass>" + ws_pwd_acc
					+ "</tem:ACpass>" + "      <tem:xmlInvData><![CDATA[" + xmlInvData + "]]></tem:xmlInvData>"
					+ "      <tem:username>" + ws_usr + "</tem:username>" + "      <tem:password>" + ws_pwd
					+ "</tem:password>" + "     <tem:pattern>" + pattern + "</tem:pattern>" + "     <tem:serial>"
					+ serial + "</tem:serial>" + "     <tem:convert>0</tem:convert>" + "  </tem:ImportAndPublishInvMTT>"
					+ " </soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/ImportAndPublishInvMTT";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_IMPORT = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_IMPORT = getUrl(url_id, "INVOICES_URL_IMPORT");
			} else {
				INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_IMPORT_CONF);
				// final String INVOICES_URL_IMPORT =
				// CommonUtility.getComConf(this.dbName, hid,
				// INVOICES_URL_IMPORT_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_IMPORT, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("ImportAndPublishInvMTTResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("ImportAndPublishInvMTTResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String AdjustInvoiceAction(String xmlInvData, String url_id, String ws_usr_acc, String ws_pwd_acc,
			String ws_usr, String ws_pwd, String pattern, String serial, String fkey) throws Exception {
		try {
			// String url =
			// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:AdjustInvoiceAction>" + "       <tem:Account>"
					+ ws_usr_acc + "</tem:Account>" + "      <tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>"
					+ "      <tem:xmlInvData><![CDATA[" + xmlInvData + "]]></tem:xmlInvData>" + "      <tem:username>"
					+ ws_usr + "</tem:username>" + "      <tem:pass>" + ws_pwd + "</tem:pass>" + "      <tem:fkey>"
					+ fkey + "</tem:fkey>" + "     <tem:AttachFile></tem:AttachFile>" + "     <tem:pattern>" + pattern
					+ "</tem:pattern>" + "     <tem:serial>" + serial + "</tem:serial>"
					+ "     <tem:convert>0</tem:convert>" + "  </tem:AdjustInvoiceAction>" + " </soapenv:Body>"
					+ "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/AdjustInvoiceAction";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_CANCEL = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_CANCEL = getUrl(url_id, "INVOICES_URL_CANCEL");
			} else {
				INVOICES_URL_CANCEL = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_CANCEL_CONF);
				// final String INVOICES_URL_IMPORT =
				// CommonUtility.getComConf(this.dbName, hid,
				// INVOICES_URL_IMPORT_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_CANCEL, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("AdjustInvoiceActionResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("AdjustInvoiceActionResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String ReplaceInvoiceAction(String xmlInvData, String url_id, String ws_usr_acc, String ws_pwd_acc,
			String ws_usr, String ws_pwd, String pattern, String serial, String fkey) throws Exception {
		try {
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:ReplaceInvoiceAction>" + "       <tem:Account>"
					+ ws_usr_acc + "</tem:Account>" + "      <tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>"
					+ "      <tem:xmlInvData><![CDATA[" + xmlInvData + "]]></tem:xmlInvData>" + "      <tem:username>"
					+ ws_usr + "</tem:username>" + "      <tem:pass>" + ws_pwd + "</tem:pass>" + "      <tem:fkey>"
					+ fkey + "</tem:fkey>" + "     <tem:AttachFile></tem:AttachFile>" + "     <tem:pattern>" + pattern
					+ "</tem:pattern>" + "     <tem:serial>" + serial + "</tem:serial>"
					+ "     <tem:convert>0</tem:convert>" + "  </tem:ReplaceInvoiceAction>" + " </soapenv:Body>"
					+ "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/ReplaceInvoiceAction";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_CANCEL = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_CANCEL = getUrl(url_id, "INVOICES_URL_CANCEL");
			} else {
				INVOICES_URL_CANCEL = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_CANCEL_CONF);
				// final String INVOICES_URL_IMPORT =
				// CommonUtility.getComConf(this.dbName, hid,
				// INVOICES_URL_IMPORT_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_CANCEL, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("ReplaceInvoiceActionResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("ReplaceInvoiceActionResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String importInvByPattern(String xmlInvData, String url_id, String ws_usr_acc, String ws_pwd_acc,
			String ws_usr, String ws_pwd, String pattern, String serial) throws Exception {
		try {
			// String url =
			// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:ImportInvByPattern>" + "       <tem:Account>"
					+ ws_usr_acc + "</tem:Account>" + "      <tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>"
					+ "      <tem:xmlInvData><![CDATA[" + xmlInvData + "]]></tem:xmlInvData>" + "      <tem:username>"
					+ ws_usr + "</tem:username>" + "      <tem:password>" + ws_pwd + "</tem:password>"
					+ "     <tem:pattern>" + pattern + "</tem:pattern>" + "     <tem:serial>" + serial + "</tem:serial>"
					+ "     <tem:convert>0</tem:convert>" + "  </tem:ImportInvByPattern>" + " </soapenv:Body>"
					+ "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/ImportInvByPattern";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_IMPORT = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_IMPORT = getUrl(url_id, "INVOICES_URL_IMPORT");
			} else {
				INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_IMPORT_CONF);
				// final String INVOICES_URL_IMPORT =
				// CommonUtility.getComConf(this.dbName, hid,
				// INVOICES_URL_IMPORT_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_IMPORT, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("ImportInvByPatternResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("ImportInvByPatternResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String importHoaDon_token(String xmlInvData, String url_id, String ws_usr, String ws_pwd) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
		ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
		String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
				+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:ImportInv>" + "      <tem:xmlInvData><![CDATA["
				+ xmlInvData + "]]></tem:xmlInvData>" + "      <tem:username>" + ws_usr + "</tem:username>"
				+ "      <tem:password>" + ws_pwd + "</tem:password>" + "     <tem:convert>0</tem:convert>"
				+ "  </tem:ImportInv>" + " </soapenv:Body>" + "</soapenv:Envelope>";

		String SOAPAction = "http://tempuri.org/ImportInv";

		String hid = CommonUtility.getHID(this.uuid);

		String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
		String INVOICES_URL_IMPORT = "";
		if ("1".equals(VPI_N_TKHD)) {
			INVOICES_URL_IMPORT = getUrl(url_id, "INVOICES_URL_IMPORT");
		} else {
			INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_IMPORT_CONF);
		}

		String value = CallWebService(Request, INVOICES_URL_IMPORT, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("ImportInvResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("ImportInvResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String importInvWithPattern(String xmlInvData, String url_id, String ws_usr, String ws_pwd, String pattern,
			String serial) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
		ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
		String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
				+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:ImportInvWithPattern>"
				+ "      <tem:xmlInvData><![CDATA[" + xmlInvData + "]]></tem:xmlInvData>" + "      <tem:username>"
				+ ws_usr + "</tem:username>" + "      <tem:password>" + ws_pwd + "</tem:password>"
				+ "      <tem:pattern>" + pattern + "</tem:pattern>" + "      <tem:serial>" + serial + "</tem:serial>"
				+ "     <tem:convert>0</tem:convert>" + "  </tem:ImportInvWithPattern>" + " </soapenv:Body>"
				+ "</soapenv:Envelope>";

		String SOAPAction = "http://tempuri.org/ImportInvWithPattern";

		String hid = CommonUtility.getHID(this.uuid);

		String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
		String INVOICES_URL_IMPORT = "";
		if ("1".equals(VPI_N_TKHD)) {
			INVOICES_URL_IMPORT = getUrl(url_id, "INVOICES_URL_IMPORT");
		} else {
			INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_IMPORT_CONF);
		}

		String value = CallWebService(Request, INVOICES_URL_IMPORT, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("ImportInvWithPatternResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("ImportInvWithPatternResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String importHoaDon_notPH(String xmlInvData, String url_id, String ws_usr_acc, String ws_pwd_acc,
			String ws_usr, String ws_pwd, String pattern, String serial) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
		ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
		ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
		String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
				+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:ImportInvByPatternSerial>"
				+ "      <tem:xmlInvData><![CDATA[" + xmlInvData + "]]></tem:xmlInvData>" + "      <tem:username>"
				+ ws_usr + "</tem:username>" + "      <tem:pass>" + ws_pwd + "</tem:pass>" + "     <tem:pattern>"
				+ pattern + "</tem:pattern>" + "     <tem:serial>" + serial + "</tem:serial>"
				+ "     <tem:convert>0</tem:convert>" + "  </tem:ImportInvByPatternSerial>" + " </soapenv:Body>"
				+ "</soapenv:Envelope>";
		String SOAPAction = "http://tempuri.org/ImportInvByPatternSerial";
		String hid = CommonUtility.getHID(this.uuid);

		String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
		String INVOICES_URL_IMPORT = "";
		if ("1".equals(VPI_N_TKHD)) {
			INVOICES_URL_IMPORT = getUrl(url_id, "INVOICES_URL_IMPORT");
		} else {
			INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_IMPORT_CONF);
		}

		String value = CallWebService(Request, INVOICES_URL_IMPORT, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("ImportInvByPatternSerialResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("ImportInvByPatternSerialResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String sendInvNoticeErrors(String xmlData, String url_id, String ws_usr_acc, String ws_pwd_acc,
			String ws_usr, String ws_pwd, String pattern) {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
		try {
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:SendInvNoticeErrors>" + "      <tem:Account>"
					+ ws_usr_acc + "</tem:Account>" + "      <tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>"
					+ "      <tem:xml><![CDATA[" + xmlData + "]]></tem:xml>" + "      <tem:username>" + ws_usr
					+ "</tem:username>" + "      <tem:password>" + ws_pwd + "</tem:password>" + "     <tem:pattern>"
					+ pattern + "</tem:pattern>" + "     <tem:convert>0</tem:convert>" + "  </tem:SendInvNoticeErrors>"
					+ " </soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/SendInvNoticeErrors";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_IMPORT = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_IMPORT = getUrl(url_id, "INVOICES_URL_IMPORT");
			} else {
				INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_IMPORT_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_IMPORT, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("SendInvNoticeErrorsResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("SendInvNoticeErrorsResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String getMCCQThueByFkeys(String url_id, String ws_usr_acc, String ws_pwd_acc, String ws_usr, String ws_pwd,
			String pattern, String fkey) {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/PublishService.asmx?wsdl";
		try {
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:GetMCCQThueByFkeys>" + "      <tem:Account>"
					+ ws_usr_acc + "</tem:Account>" + "      <tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>"
					+ "      <tem:username>" + ws_usr + "</tem:username>" + "      <tem:password>" + ws_pwd
					+ "</tem:password>" + "     <tem:pattern>" + pattern + "</tem:pattern>" + "     <tem:fkeys>" + fkey
					+ "</tem:fkeys>" + "  </tem:GetMCCQThueByFkeys>" + " </soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/GetMCCQThueByFkeys";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_IMPORT = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_IMPORT = getUrl(url_id, "INVOICES_URL_IMPORT");
			} else {
				INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_IMPORT_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_IMPORT, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("GetMCCQThueByFkeysResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("GetMCCQThueByFkeysResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String cancelHoaDon(String key, String url_id, String ws_usr_acc, String ws_pwd_acc, String ws_usr,
			String ws_pwd) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/BusinessService.asmx?wsdl";
		ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
		ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
		String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
				+ "<soapenv:Header/>" + "<soapenv:Body>" + "<tem:cancelInv>" + "<tem:Account>" + ws_usr_acc
				+ "</tem:Account>" + "<tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>" + "<tem:fkey>" + key + "</tem:fkey>"
				+ "<tem:userName>" + ws_usr + "</tem:userName>" + "<tem:userPass>" + ws_pwd + "</tem:userPass>"
				+ "</tem:cancelInv>" + "</soapenv:Body>" + "</soapenv:Envelope>";
		String SOAPAction = "http://tempuri.org/cancelInv";
		String hid = CommonUtility.getHID(this.uuid);

		String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
		String INVOICES_URL_CANCEL = "";
		if ("1".equals(VPI_N_TKHD)) {
			INVOICES_URL_CANCEL = getUrl(url_id, "INVOICES_URL_CANCEL");
		} else {
			INVOICES_URL_CANCEL = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_CANCEL_CONF);
			// final String INVOICES_URL_CANCEL =
			// CommonUtility.getComConf(this.dbName, hid,
			// INVOICES_URL_CANCEL_CONF);
		}

		String value = CallWebService(Request, INVOICES_URL_CANCEL, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("cancelInvResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("cancelInvResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String cancelInvNoPay(String key, String url_id, String ws_usr_acc, String ws_pwd_acc, String ws_usr,
			String ws_pwd) {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/BusinessService.asmx?wsdl";
		try {
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "<tem:cancelInvNoPay>" + "<tem:Account>" + ws_usr_acc
					+ "</tem:Account>" + "<tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>" + "<tem:fkey>" + key
					+ "</tem:fkey>" + "<tem:userName>" + ws_usr + "</tem:userName>" + "<tem:userPass>" + ws_pwd
					+ "</tem:userPass>" + "</tem:cancelInvNoPay>" + "</soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/cancelInvNoPay";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_CANCEL = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_CANCEL = getUrl(url_id, "INVOICES_URL_CANCEL");
			} else {
				INVOICES_URL_CANCEL = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_CANCEL_CONF);
				// final String INVOICES_URL_CANCEL =
				// CommonUtility.getComConf(this.dbName, hid,
				// INVOICES_URL_CANCEL_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_CANCEL, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("cancelInvResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("cancelInvResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String cancelInvSignFileNoPay(String key, String url_id, String ws_usr_acc, String ws_pwd_acc, String ws_usr,
			String ws_pwd) {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/BusinessService.asmx?wsdl";
		try {
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "<tem:cancelInvSignFileNoPay>" + "<tem:Account>"
					+ ws_usr_acc + "</tem:Account>" + "<tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>" + "<tem:fkey>" + key
					+ "</tem:fkey>" + "<tem:userName>" + ws_usr + "</tem:userName>" + "<tem:userPass>" + ws_pwd
					+ "</tem:userPass>" + "</tem:cancelInvSignFileNoPay>" + "</soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/cancelInvSignFileNoPay";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_CANCEL = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_CANCEL = getUrl(url_id, "INVOICES_URL_CANCEL");
			} else {
				INVOICES_URL_CANCEL = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_CANCEL_CONF);
				// final String INVOICES_URL_CANCEL =
				// CommonUtility.getComConf(this.dbName, hid,
				// INVOICES_URL_CANCEL_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_CANCEL, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("cancelInvSignFileNoPayResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("cancelInvSignFileNoPayResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String deleteInvoiceByFkey(String key, String url_id, String ws_usr_acc, String ws_pwd_acc, String ws_usr,
			String ws_pwd) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/BusinessService.asmx?wsdl";
		try {
			ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "<tem:deleteInvoiceByFkey>" + "<tem:Account>"
					+ ws_usr_acc + "</tem:Account>" + "<tem:ACpass>" + ws_pwd_acc + "</tem:ACpass>" + "<tem:lstFkey>"
					+ key + "</tem:lstFkey>" + "<tem:userName>" + ws_usr + "</tem:userName>" + "<tem:userPass>" + ws_pwd
					+ "</tem:userPass>" + "</tem:deleteInvoiceByFkey>" + "</soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/deleteInvoiceByFkey";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_CANCEL = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_CANCEL = getUrl(url_id, "INVOICES_URL_CANCEL");
			} else {
				INVOICES_URL_CANCEL = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_CANCEL_CONF);
				// final String INVOICES_URL_CANCEL =
				// CommonUtility.getComConf(this.dbName, hid,
				// INVOICES_URL_CANCEL_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_CANCEL, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("cancelInvResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("cancelInvResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR:" + e.getMessage();
		}
	}

	public String UnConfirmPaymentFkey(String key, String url_id, String ws_usr_acc, String ws_pwd_acc, String ws_usr,
			String ws_pwd) throws Exception {
		// String url =
		// "https://webservicedemo.vnpt-invoice.com.vn/BusinessService.asmx?wsdl";
		ws_pwd = CryptoUtil.BASE64_decode(ws_pwd);
		ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
		String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
				+ "<soapenv:Header/>" + "<soapenv:Body>" + "<tem:UnConfirmPaymentFkey>" + "<tem:lstFkey>" + key
				+ "</tem:lstFkey>" + "<tem:userName>" + ws_usr + "</tem:userName>" + "<tem:userPass>" + ws_pwd
				+ "</tem:userPass>" + "</tem:UnConfirmPaymentFkey>" + "</soapenv:Body>" + "</soapenv:Envelope>";
		String SOAPAction = "http://tempuri.org/UnConfirmPaymentFkey";
		String hid = CommonUtility.getHID(this.uuid);
		String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
		String INVOICES_URL_CANCEL = "";
		if ("1".equals(VPI_N_TKHD)) {
			INVOICES_URL_CANCEL = getUrl(url_id, "INVOICES_URL_CANCEL");
		} else {
			INVOICES_URL_CANCEL = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_CANCEL_CONF);
		}
		// final String INVOICES_URL_CANCEL =
		// CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_CANCEL_CONF);
		String value = CallWebService(Request, INVOICES_URL_CANCEL, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("UnConfirmPaymentFkeyResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("UnConfirmPaymentFkeyResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String updateCus(String xmlInvData, String url_id, String ws_usr_acc, String ws_pwd_acc) throws Exception {
		String value = "";
		try {
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:UpdateCus>" + "      <tem:XMLCusData><![CDATA["
					+ xmlInvData + "]]></tem:XMLCusData>" + "      <tem:username>" + ws_usr_acc + "</tem:username>"
					+ "      <tem:pass>" + ws_pwd_acc + "</tem:pass>" + "     <tem:convert>0</tem:convert>"
					+ "  </tem:UpdateCus>" + " </soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/UpdateCus";
			String hid = CommonUtility.getHID(this.uuid);
	
			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_IMPORT = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_IMPORT = getUrl(url_id, "INVOICES_URL_IMPORT");
			} else {
				INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_IMPORT_CONF);
			}
	
			value = CallWebService(Request, INVOICES_URL_IMPORT, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("UpdateCusResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("UpdateCusResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		}
		catch(Exception e) {
			return "ERR:" + value + ";" + e.getMessage();
		}
	}

	public String listInvByCusFkey(String key, String url_id, String ws_usr_acc, String ws_pwd_acc) throws Exception {
		ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
		String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
				+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:listInvByCusFkey>" + "      <tem:key>" + key
				+ "</tem:key>" + "      <tem:fromDate></tem:fromDate>" + "      <tem:toDate></tem:toDate>"
				+ "      <tem:userName>" + ws_usr_acc + "</tem:userName>" + "      <tem:userPass>" + ws_pwd_acc
				+ "</tem:userPass>" + "  </tem:listInvByCusFkey>" + " </soapenv:Body>" + "</soapenv:Envelope>";
		String SOAPAction = "http://tempuri.org/listInvByCusFkey";
		String hid = CommonUtility.getHID(this.uuid);

		String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
		String INVOICES_URL_VIEW = "";
		if ("1".equals(VPI_N_TKHD)) {
			INVOICES_URL_VIEW = getUrl(url_id, "INVOICES_URL_VIEW");
		} else {
			INVOICES_URL_VIEW = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_VIEW_CONF);
		}

		String value = CallWebService(Request, INVOICES_URL_VIEW, SOAPAction);
		if (value.startsWith("ERR:-100")) {
			return value;
		}
		String strMsgCode = "";
		InputSource is = new InputSource(new StringReader(value));
		DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
			factory.isIgnoringElementContentWhitespace();
		});
		Document doc = dBuilder.parse(is);
		doc.getDocumentElement().normalize();
		NodeList nList = doc.getElementsByTagName("listInvByCusFkeyResponse");
		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node nNode = nList.item(temp);
			if (nNode.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) nNode;
				strMsgCode = eElement.getElementsByTagName("listInvByCusFkeyResult").item(0).getTextContent();
			}
		}
		return strMsgCode;
	}

	public String unConfirmPaymentFkey(String fkey, String url_id, String ws_usr_acc, String ws_pwd_acc)
			throws Exception {
		try {
			ws_pwd_acc = CryptoUtil.BASE64_decode(ws_pwd_acc);
			String Request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">"
					+ "<soapenv:Header/>" + "<soapenv:Body>" + "    <tem:UnConfirmPaymentFkey>" + "      <tem:lstFkey>"
					+ fkey + "</tem:lstFkey>" + "      <tem:userName>" + ws_usr_acc + "</tem:userName>"
					+ "      <tem:userPass>" + ws_pwd_acc + "</tem:userPass>" + "  </tem:UnConfirmPaymentFkey>"
					+ " </soapenv:Body>" + "</soapenv:Envelope>";
			String SOAPAction = "http://tempuri.org/UnConfirmPaymentFkey";
			String hid = CommonUtility.getHID(this.uuid);

			String VPI_N_TKHD = CommonUtility.getComConf(this.dbName, hid, VPI_N_TKHD_CONF);
			String INVOICES_URL_CANCEL = "";
			if ("1".equals(VPI_N_TKHD)) {
				INVOICES_URL_CANCEL = getUrl(url_id, "INVOICES_URL_CANCEL");
			} else {
				INVOICES_URL_CANCEL = CommonUtility.getComConf(this.dbName, hid, INVOICES_URL_CANCEL_CONF);
				// final String INVOICES_URL_CANCEL =
				// CommonUtility.getComConf(this.dbName, hid,
				// INVOICES_URL_CANCEL_CONF);
			}

			String value = CallWebService(Request, INVOICES_URL_CANCEL, SOAPAction);
			if (value.startsWith("ERR:-100")) {
				return value;
			}
			String strMsgCode = "";
			InputSource is = new InputSource(new StringReader(value));
			DocumentBuilder dBuilder = XmlDocumentBuilder.createSaferDocumentBuilder(factory -> {
				factory.isIgnoringElementContentWhitespace();
			});
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("UnConfirmPaymentFkeyResponse");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					strMsgCode = eElement.getElementsByTagName("UnConfirmPaymentFkeyResult").item(0).getTextContent();
				}
			}
			return strMsgCode;
		} catch (Exception e) {
			e.printStackTrace();
			return "ERR_unConfirmPaymentFkey";
		}
	}

	public String getUrl(String id, String url) {
		try {
			String user_id;
			String db_schema;
			String provide_id;
			String hospital_id;

			String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
					.getJsonWebTokenByType().getID(uuid);
			JSONObject userInfo = UserInfoCache.get(uuidCache);
			if (userInfo != null) {
				user_id = userInfo.getString(SysConstants.USER_ID);
				db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
				provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
				hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
			} else {
				String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
						.getJsonWebTokenByType().getLoginUserData(uuid);
				SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
				user_id = _userInfo.getUserId();
				db_schema = _userInfo.getDbSchema();
				provide_id = _userInfo.getProvinceId();
				hospital_id = _userInfo.getHospitalId();
			}

			String rs = DBUtility.CALL_SP_O(this.dbName, "{?=call invoices_get_url(?2S,?3S,?4S,?5S,?6S)}",
					user_id + "$" + db_schema + "$" + hospital_id + "$" + provide_id + "$" + id + "$", new Object[] {});
			JSONArray arr = new JSONArray(rs);
			JSONObject ob = arr.getJSONObject(0);
			return ob.getString(url);
		} catch (Exception e) {
			e.printStackTrace();
			return "";
		}
	}

	/**
	 * Ham xu ly chuc nang huy hoa don dien tu viettel
	 *
	 * @param xmlData
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 */
	public String cancelTransactionInvoice(String xmlData) {
		long startTimeXmlrequest = System.currentTimeMillis();
		String rs = "";
		String INVOICES_URL_IMPORT = "";
		try {
			String hid = CommonUtility.getHID(this.uuid);
			INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, "INVOICES_URL_IMPORT")
					+ "/InvoiceWS/cancelTransactionInvoice";
			String INVOICES_WS_USER = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_USER");
			String INVOICES_WS_PWD = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_PWD");
			String invoiceType = CommonUtility.getComConf(this.dbName, hid, "INVOICES_TYPE");
			Map<String, String> header = new HashMap<String, String>();
			header.put(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded");
			String INVOICES_VT_PHATHANH_TK = CommonUtility.getComConf(this.dbName, hid, "INVOICES_VT_PHATHANH_TK");
			if ("1".equals(INVOICES_VT_PHATHANH_TK)) {
				String user_id;
				String db_schema;
				String provide_id;
				String hospital_id;

				String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
						.getJsonWebTokenByType().getID(uuid);
				JSONObject userInfo = UserInfoCache.get(uuidCache);
				if (userInfo != null) {
					user_id = userInfo.getString(SysConstants.USER_ID);
					db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
					provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
					hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
				} else {
					String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
							.getJsonWebTokenByType().getLoginUserData(uuid);
					SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
					user_id = _userInfo.getUserId();
					db_schema = _userInfo.getDbSchema();
					provide_id = _userInfo.getProvinceId();
					hospital_id = _userInfo.getHospitalId();
				}
				String rstk = DBUtility.CALL_SP_O(this.dbName, "{?=call vpi01t013_getdata(?2S,?3S,?4S,?5S)}",
						user_id + "$" + db_schema + "$" + hospital_id + "$" + provide_id + "$", new Object[] {});
				JSONArray arr = new JSONArray(rstk);
				JSONObject ob = arr.getJSONObject(0);
				if (ob.getString("INVOICES_USER") != null && !ob.getString("INVOICES_USER").trim().isEmpty()
						&& ob.getString("INVOICES_PASS") != null && !ob.getString("INVOICES_PASS").trim().isEmpty()) {
					INVOICES_WS_USER = ob.getString("INVOICES_USER");
					INVOICES_WS_PWD = CryptoUtil.BASE64_decode(ob.getString("INVOICES_PASS"));
				}
			}

			setViettelInvoiceAuthorizationHeader(invoiceType, INVOICES_WS_USER, INVOICES_WS_PWD, header);
			// setViettelInvoiceAuthorizationHeader(invoiceType,
			// INVOICES_WS_USER, INVOICES_WS_PWD, header);

			rs = sendPostRequest(INVOICES_URL_IMPORT, header, xmlData);
		} catch (Exception e) {
			logsWS.error("InvoicesWSVT|Exception: " + ExceptionUtils.getStackTrace(e));
			rs = "ERR_CANCEL_REQ;" + e.toString();
		}
		logsWS.info("InvoicesWSVT|url:" + INVOICES_URL_IMPORT + "|execution_time(ms):"
				+ (System.currentTimeMillis() - startTimeXmlrequest));
		return rs;
	}

	public String createExchangeInvoiceFile(String xmlData) {
		// String xml =
		// "supplierTaxCode=0800207726&invoiceNo=K24TVP5313&templateCode=2/001&strIssueDate=1713325452000&exchangeUser=test123";
		long startTimeXmlrequest = System.currentTimeMillis();
		String rs = "";
		String INVOICES_URL_IMPORT = "";
		try {
			String hid = CommonUtility.getHID(this.uuid);
			INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, "INVOICES_URL_IMPORT")
					+ "/InvoiceWS/createExchangeInvoiceFile";
			String INVOICES_WS_USER = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_USER");
			String INVOICES_WS_PWD = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_PWD");
			String invoiceType = CommonUtility.getComConf(this.dbName, hid, "INVOICES_TYPE");

			Map<String, String> header = new HashMap<String, String>();
			header.put(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded");
			setViettelInvoiceAuthorizationHeader(invoiceType, INVOICES_WS_USER, INVOICES_WS_PWD, header);

			rs = sendPostRequest(INVOICES_URL_IMPORT, header, xmlData);

		} catch (Exception e) {
			logsWS.error("InvoicesWSVT|Exception: " + ExceptionUtils.getStackTrace(e));
			rs = "ERR_SEARCH_REQ;" + e.toString();
		}
		logsWS.info("InvoicesWSVT|url:" + INVOICES_URL_IMPORT + "|execution_time(ms):"
				+ (System.currentTimeMillis() - startTimeXmlrequest));
		return rs;
	}

	/**
	 * Ham xu ly lay thong tin hoa don
	 *
	 * @param xmlData
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 */
	public String searchInvoiceByTransactionUuid(String xmlData) {
		// xml supplierTaxCode=4600537442&transactionUuid=132132
		long startTimeXmlrequest = System.currentTimeMillis();
		String rs = "";
		String INVOICES_URL_IMPORT = "";
		try {
			String hid = CommonUtility.getHID(this.uuid);
			INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, "INVOICES_URL_IMPORT")
					+ "/InvoiceWS/searchInvoiceByTransactionUuid";
			String INVOICES_WS_USER = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_USER");
			String INVOICES_WS_PWD = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_PWD");
			String invoiceType = CommonUtility.getComConf(this.dbName, hid, "INVOICES_TYPE");
			Map<String, String> header = new HashMap<String, String>();
			header.put(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded");
			setViettelInvoiceAuthorizationHeader(invoiceType, INVOICES_WS_USER, INVOICES_WS_PWD, header);

			rs = sendPostRequest(INVOICES_URL_IMPORT, header, xmlData);
		} catch (Exception e) {
			logsWS.error("InvoicesWSVT|Exception: " + ExceptionUtils.getStackTrace(e));
			rs = "ERR_SEARCH_REQ;" + e.toString();
		}
		logsWS.info("InvoicesWSVT|url:" + INVOICES_URL_IMPORT + "|execution_time(ms):"
				+ (System.currentTimeMillis() - startTimeXmlrequest));
		return rs;
	}

	/**
	 * Ham xu ly chuc nang tao hoa don dien tu viettel
	 *
	 * @param oJson
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 */
	// L2PT-129800 : them cau hinh tax_code nha thuoc
	public String createInvoice(String oJson, String type) {
		long startTimeXmlrequest = System.currentTimeMillis();
		String rs = "";
		String INVOICES_URL_IMPORT = "";
		String cfcode_tax_code = "";
		if (type == null || type.length() == 0 || type.equals("0")) {
			cfcode_tax_code = "INVOICES_WS_TAX_CODE";
		} else {
			cfcode_tax_code = type;
		}
		try {
			String hid = CommonUtility.getHID(this.uuid);

			String INVOICES_WS_USER = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_USER");
			String INVOICES_WS_TAX_CODE = CommonUtility.getComConf(this.dbName, hid, cfcode_tax_code);
			INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, "INVOICES_URL_IMPORT")
					+ "/InvoiceWS/createInvoice/" + INVOICES_WS_TAX_CODE;
			String INVOICES_WS_PWD = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_PWD");
			String invoiceType = CommonUtility.getComConf(this.dbName, hid, "INVOICES_TYPE");
			Map<String, String> header = new HashMap<String, String>();
			header.put(HttpHeaders.CONTENT_TYPE, "application/json");
			// L2PT-54995
			String INVOICES_VT_PHATHANH_TK = CommonUtility.getComConf(this.dbName, hid, "INVOICES_VT_PHATHANH_TK");
			if ("1".equals(INVOICES_VT_PHATHANH_TK)) {
				String user_id;
				String db_schema;
				String provide_id;
				String hospital_id;

				String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
						.getJsonWebTokenByType().getID(uuid);
				JSONObject userInfo = UserInfoCache.get(uuidCache);
				if (userInfo != null) {
					user_id = userInfo.getString(SysConstants.USER_ID);
					db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
					provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
					hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
				} else {
					String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
							.getJsonWebTokenByType().getLoginUserData(uuid);
					SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
					user_id = _userInfo.getUserId();
					db_schema = _userInfo.getDbSchema();
					provide_id = _userInfo.getProvinceId();
					hospital_id = _userInfo.getHospitalId();
				}
				String rstk = DBUtility.CALL_SP_O(this.dbName, "{?=call vpi01t013_getdata(?2S,?3S,?4S,?5S)}",
						user_id + "$" + db_schema + "$" + hospital_id + "$" + provide_id + "$", new Object[] {});
				JSONArray arr = new JSONArray(rstk);
				JSONObject ob = arr.getJSONObject(0);
				if (ob.getString("INVOICES_USER") != null && !ob.getString("INVOICES_USER").trim().isEmpty()
						&& ob.getString("INVOICES_PASS") != null && !ob.getString("INVOICES_PASS").trim().isEmpty()) {
					INVOICES_WS_USER = ob.getString("INVOICES_USER");
					INVOICES_WS_PWD = CryptoUtil.BASE64_decode(ob.getString("INVOICES_PASS"));
				}
			}

			setViettelInvoiceAuthorizationHeader(invoiceType, INVOICES_WS_USER, INVOICES_WS_PWD, header);

			rs = sendPostRequest(INVOICES_URL_IMPORT, header, oJson);
		} catch (Exception e) {
			logsWS.error("InvoicesWSVT|data: " + oJson + "|Exception: " + ExceptionUtils.getStackTrace(e));
			rs = "ERR_CREATTE_REQ;" + e.toString();
		}
		logsWS.info("InvoicesWSVT|url:" + INVOICES_URL_IMPORT + "|execution_time(ms):"
				+ (System.currentTimeMillis() - startTimeXmlrequest));
		return rs;
	}

	public String createInvoice(String oJson) {
		return createInvoice(oJson, "0");
	}

	/**
	 * Ham xu ly chuc nang tao hoa don nhap viettel
	 *
	 * @param oJson
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 */
	public String createOrUpdateInvoiceDraft(String oJson) {
		long startTimeXmlrequest = System.currentTimeMillis();
		String rs = "";
		String INVOICES_URL_IMPORT = "";
		try {
			String hid = CommonUtility.getHID(this.uuid);
			String INVOICES_WS_USER = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_USER");
			String INVOICES_WS_TAX_CODE = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_TAX_CODE");
			INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, "INVOICES_URL_IMPORT")
					+ "/InvoiceWS/createOrUpdateInvoiceDraft/" + INVOICES_WS_TAX_CODE;
			String INVOICES_WS_PWD = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_PWD");
			String invoiceType = CommonUtility.getComConf(this.dbName, hid, "INVOICES_TYPE");
			Map<String, String> header = new HashMap<String, String>();
			header.put(HttpHeaders.CONTENT_TYPE, "application/json");
			setViettelInvoiceAuthorizationHeader(invoiceType, INVOICES_WS_USER, INVOICES_WS_PWD, header);

			rs = sendPostRequest(INVOICES_URL_IMPORT, header, oJson);
		} catch (Exception e) {
			logsWS.error("InvoicesWSVT|Exception: " + ExceptionUtils.getStackTrace(e));
			rs = "ERR_CREATTE_REQ;" + e.toString();
		}
		logsWS.info("InvoicesWSVT|url:" + INVOICES_URL_IMPORT + "|execution_time(ms):"
				+ (System.currentTimeMillis() - startTimeXmlrequest));

		return rs;
	}

	/**
	 * Ham xu ly chuc nang xen hoa don dien tu viettel
	 *
	 * @param oJson
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 */
	public String getInvoiceRepresentationFile(String oJson) {
		long startTimeXmlrequest = System.currentTimeMillis();
		String rs = "";
		String INVOICES_URL_IMPORT = "";
		try {
			String hid = CommonUtility.getHID(this.uuid);
			String INVOICES_WS_USER = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_USER");
			INVOICES_URL_IMPORT = CommonUtility.getComConf(this.dbName, hid, "INVOICES_URL_IMPORT")
					+ "/InvoiceUtilsWS/getInvoiceRepresentationFile";
			String INVOICES_WS_PWD = CommonUtility.getComConf(this.dbName, hid, "INVOICES_WS_PWD");
			String invoiceType = CommonUtility.getComConf(this.dbName, hid, "INVOICES_TYPE");
			Map<String, String> header = new HashMap<String, String>();
			header.put(HttpHeaders.CONTENT_TYPE, "application/json");
			String INVOICES_VT_PHATHANH_TK = CommonUtility.getComConf(this.dbName, hid, "INVOICES_VT_PHATHANH_TK");
			if ("1".equals(INVOICES_VT_PHATHANH_TK)) {
				String user_id;
				String db_schema;
				String provide_id;
				String hospital_id;

				String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
						.getJsonWebTokenByType().getID(uuid);
				JSONObject userInfo = UserInfoCache.get(uuidCache);
				if (userInfo != null) {
					user_id = userInfo.getString(SysConstants.USER_ID);
					db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
					provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
					hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
				} else {
					String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
							.getJsonWebTokenByType().getLoginUserData(uuid);
					SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
					user_id = _userInfo.getUserId();
					db_schema = _userInfo.getDbSchema();
					provide_id = _userInfo.getProvinceId();
					hospital_id = _userInfo.getHospitalId();
				}
				String rstk = DBUtility.CALL_SP_O(this.dbName, "{?=call vpi01t013_getdata(?2S,?3S,?4S,?5S)}",
						user_id + "$" + db_schema + "$" + hospital_id + "$" + provide_id + "$", new Object[] {});
				JSONArray arr = new JSONArray(rstk);
				JSONObject ob = arr.getJSONObject(0);
				if (ob.getString("INVOICES_USER") != null && !ob.getString("INVOICES_USER").trim().isEmpty()
						&& ob.getString("INVOICES_PASS") != null && !ob.getString("INVOICES_PASS").trim().isEmpty()) {
					INVOICES_WS_USER = ob.getString("INVOICES_USER");
					INVOICES_WS_PWD = CryptoUtil.BASE64_decode(ob.getString("INVOICES_PASS"));
				}
			}

			setViettelInvoiceAuthorizationHeader(invoiceType, INVOICES_WS_USER, INVOICES_WS_PWD, header);
			// setViettelInvoiceAuthorizationHeader(invoiceType,
			// INVOICES_WS_USER, INVOICES_WS_PWD, header);

			rs = sendPostRequest(INVOICES_URL_IMPORT, header, oJson);
		} catch (Exception e) {
			logsWS.error("InvoicesWSVT|Exception: " + ExceptionUtils.getStackTrace(e));
			rs = "ERR_GETINVOICE_REQ;" + e.toString();
		}
		logsWS.info("InvoicesWSVT|url:" + INVOICES_URL_IMPORT + "|execution_time(ms):"
				+ (System.currentTimeMillis() - startTimeXmlrequest));
		return rs;
	}

	/**
	 * Ham xu ly tao authorization header cho hddt viettel
	 *
	 * @param invoiceType
	 *            version api
	 * @param username
	 *            tai khoan dang nhap
	 * @param password
	 *            mat khau dang nhap
	 * @param header
	 *            authorization header can tao
	 * <AUTHOR>
	 */
	private void setViettelInvoiceAuthorizationHeader(String invoiceType, String username, String password,
			Map<String, String> header) {
		try {
			final Map<String, String> VIETTEL_URL_SERVICE = new JSONObject(
					CommonUtility.getComConf(dbName, CommonUtility.getHID(this.uuid), "INVOICES_VIETTEL_URL_SERVICE"))
							.getHashMap();
			if ("VIETTELV2".equals(invoiceType)) {

				Map<String, String> headerToken = new HashMap<String, String>();
				headerToken.put(HttpHeaders.CONTENT_TYPE, "application/json");
				JSONObject body = new JSONObject();
				body.put("username", username);
				body.put("password", password);
				final String rs = sendPostRequest(
						VIETTEL_URL_SERVICE.get("URLV2_BASE") + VIETTEL_URL_SERVICE.get("URLV2_AUTH"), headerToken,
						body.toString());
				body = new JSONObject(rs);
				header.put("Cookie", "access_token=" + body.getString("access_token"));
			} else {
				String userpass = username + ":" + password;
				header.put(HttpHeaders.AUTHORIZATION,
						"Basic " + new String(Base64.getEncoder().encode(userpass.getBytes())));
			}
		} catch (Exception e) {
			logsWS.error(
					"InvoicesWSVT|setViettelInvoiceAuthorizationHeader|Exception: " + ExceptionUtils.getStackTrace(e));
		}
	}

	/**
	 * Ham xu ly chuc nang phat hanh hoa don
	 *
	 * @param data
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 * <AUTHOR>
	 */
	public String issueInvoices(String data) {
		long startTime = System.currentTimeMillis();
		String result = "";
		String invoiceType = CommonUtility.getComConf(this.dbName, CommonUtility.getHID(this.uuid), "INVOICES_TYPE");
		EInvoice invoice = EInvoiceFactory.getEInvoice(invoiceType, this.dbName, this.uuid);
		if (CheckObjectUtils.isNullOrEmpty(invoice)) {
			logsWS.info("InvoicesWS|InvoiceWS instance null");
		} else {
			result = (String) invoice.issueInvoices(data);
		}
		logsWS.info("InvoicesWS|" + invoiceType + "|Action:Issue Invoice|execution_time(ms):"
				+ (System.currentTimeMillis() - startTime));
		return result;
	}

	/**
	 * Ham xu ly chuc nang tao hoa don
	 *
	 * @param data
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 * <AUTHOR>
	 */
	public String createInvoices(String data) {
		long startTime = System.currentTimeMillis();
		String result = "";
		String invoiceType = CommonUtility.getComConf(this.dbName, CommonUtility.getHID(this.uuid), "INVOICES_TYPE");
		EInvoice invoice = EInvoiceFactory.getEInvoice(invoiceType, this.dbName, this.uuid);
		if (CheckObjectUtils.isNullOrEmpty(invoice)) {
			logsWS.info("InvoicesWS|InvoiceWS instance null");
		} else {
			result = (String) invoice.createInvoices(data);
		}
		logsWS.info("InvoicesWS|" + invoiceType + "|Action:Create Invoice|execution_time(ms):"
				+ (System.currentTimeMillis() - startTime));
		return result;
	}

	/**
	 * Ham xu ly chuc nang ky so hoa don
	 *
	 * @param data
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 * <AUTHOR>
	 */
	public String signInvoices(String data) {
		long startTime = System.currentTimeMillis();
		String result = "";
		String invoiceType = CommonUtility.getComConf(this.dbName, CommonUtility.getHID(this.uuid), "INVOICES_TYPE");
		EInvoice invoice = EInvoiceFactory.getEInvoice(invoiceType, this.dbName, this.uuid);
		if (CheckObjectUtils.isNullOrEmpty(invoice)) {
			logsWS.info("InvoicesWS|InvoiceWS instance null");
		} else {
			result = (String) invoice.signInvoices(data);
		}
		logsWS.info("InvoicesWS|" + invoiceType + "|Action:Sign Invoice|execution_time(ms):"
				+ (System.currentTimeMillis() - startTime));
		return result;
	}

	/**
	 * Ham xu ly chuc nang lay hoa don da phat hanh
	 *
	 * @param data
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 * <AUTHOR>
	 */
	public String getIssuedInvoice(String data) {
		long startTime = System.currentTimeMillis();
		String result = "";
		String invoiceType = CommonUtility.getComConf(this.dbName, CommonUtility.getHID(this.uuid), "INVOICES_TYPE");
		EInvoice invoice = EInvoiceFactory.getEInvoice(invoiceType, this.dbName, this.uuid);
		if (CheckObjectUtils.isNullOrEmpty(invoice)) {
			logsWS.info("InvoicesWS|InvoiceWS instance null");
		} else {
			result = (String) invoice.getIssuedInvoices(data);
		}
		logsWS.info("InvoicesWS|" + invoiceType + "|Action:Get Invoice|execution_time(ms):"
				+ (System.currentTimeMillis() - startTime));
		return result;
	}

	/**
	 * Ham xu ly chuc nang xoa hoa don
	 *
	 * @param data
	 *            Du lieu hoa don
	 * @return Ket qua thục hien
	 * <AUTHOR>
	 */
	public String deleteInvoices(String data) {
		long startTime = System.currentTimeMillis();
		String result = "";
		String invoiceType = CommonUtility.getComConf(this.dbName, CommonUtility.getHID(this.uuid), "INVOICES_TYPE");
		EInvoice invoice = EInvoiceFactory.getEInvoice(invoiceType, this.dbName, this.uuid);
		if (CheckObjectUtils.isNullOrEmpty(invoice)) {
			logsWS.info("InvoicesWS|InvoiceWS instance null");
		} else {
			result = (String) invoice.deleteInvoices(data);
		}
		logsWS.info("InvoicesWS|" + invoiceType + "|Action:Delete Invoice|execution_time(ms):"
				+ (System.currentTimeMillis() - startTime));
		return result;
	}

	public String sendPostRequest(String url, Map<String, String> header, String body) throws Exception {
		HttpPost post = new HttpPost(url);
		// add header
		post.setHeader(HttpHeaders.USER_AGENT, USER_AGENT);
		header.forEach(post::setHeader);
		StringEntity entity = new StringEntity(body, StandardCharsets.UTF_8.name());
		post.setEntity(entity);
		SSLContext sslcontext = createSSLContext();
		SSLConnectionSocketFactory factory = new SSLConnectionSocketFactory(sslcontext, NoopHostnameVerifier.INSTANCE);
		try (CloseableHttpClient client = HttpClients.custom()
				.setDefaultRequestConfig(RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT)
						.setSocketTimeout(SOCKET_TIMEOUT).setConnectionRequestTimeout(SOCKET_TIMEOUT).build())
				.setSSLSocketFactory(factory).build()) {

			try (CloseableHttpResponse response = client.execute(post)) {
				int responseCode = response.getStatusLine().getStatusCode();
				String responseContent = "";
				try (InputStreamReader isr = new InputStreamReader(response.getEntity().getContent(),
						StandardCharsets.UTF_8); BufferedReader rd = new BufferedReader(isr)) {
					responseContent = rd.lines().collect(Collectors.joining());
				}
				if (responseCode != 200 && responseCode != 201) {
					logsWS.error("sendPostRequest|" + url + "|body: " + body + "|" + responseCode + "|response: " + responseContent);
					throw new IllegalStateException(String.valueOf(responseCode + ":" + responseContent));
				}
				logsWS.error("sendPostRequest|" + url + "|body: " + body + "|" + responseCode + "|response: " + responseContent);
				return responseContent;
			} finally {
				try {
					client.close();
				} catch (Exception ignored) {
				}
				try {
					post.releaseConnection();
				} catch (Exception ignored) {
				}
			}
		}
	}

	private SSLContext createSSLContext() throws NoSuchAlgorithmException, KeyManagementException {
		SSLContext sslcontext = SSLContext.getInstance("TLSv1.2");
		sslcontext.init(null, new TrustManager[] { new X509TrustManager() {
			@Override
			public X509Certificate[] getAcceptedIssuers() {
				return new X509Certificate[0];
			}

			@Override
			public void checkClientTrusted(X509Certificate[] certs, String authType) {
			}

			@Override
			public void checkServerTrusted(X509Certificate[] certs, String authType) {
			}
		} }, new SecureRandom());
		return sslcontext;
	}

	public static void main(String[] args) throws Exception {
		// viewHoaDon();
	}
}

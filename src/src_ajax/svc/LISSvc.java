package svc;

import AjaxUtil.AjaxJson;
import com.google.gson.*;
import com.idg.IdgStorage;
import com.vnpt.util.EmrCaDetail;
import com.vnpt.util.HospitalUtils;
import com.vnpt.util.consts.Constants;
import com.vnpthis.CaHelper.ApiCaResponse;
import com.vnpthis.serv.CaApiCancelReport;
import com.vnpthis.serv.bo.SmsResponse;
import com.vnpthis.serv.utils.HttpEmrUtilsNew;
import com.vnpthis.sms.SmsTemplateBusiness;
import com.vnptit.cache.UserInfoCache;
import com.vnptit.common.SysObject;
import com.vnptit.jwt.JsonWebTokenRestApiFactory;
import com.vnptit.util.*;
import com.vsc.util.DateUtil;
import com.vsc.util.HttpUtil;
import intellsoft.db.DBUtil;
import intellsoft.db.DBUtility;
import jsonutil.JSONArray;
import jsonutil.JSONObject;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import otp.sms_foder.SmsSendBusiness;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LISSvc {

    private static final Logger logger = Logger.getLogger(LISSvc.class);
    private final static Pattern REPORT_CODE_XML_REGEX = Pattern.compile("(?<=\\<RPT_CODE\\>)(((?!(\\<\\/RPT_CODE\\>))[\\w\\W])*)");
    public static String token_notify;
    private final String dbName;
    private final String mediaSchema;
    private final String uuid;

    public LISSvc(String dbName, String mediaSchema, String uuid) {
        this.dbName = dbName;
        this.mediaSchema = mediaSchema;
        this.uuid = uuid;
    }

    public String LIS_Get_DM_LoaiMBP() {

        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_LoaiMBP(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();

        } catch (Exception e) {
            logger.error("LIS_Get_DM_LoaiMBP exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_KhoaPhong() {

        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_KhoaPhong(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();

        } catch (Exception e) {
            logger.error("LIS_Get_DM_KhoaPhong exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_Buong() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {


            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_Buong(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_Buong exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_Giuong() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {


            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_Giuong(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code =
                    b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_Giuong exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_BacSi() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_BacSi(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_BacSi exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_DoiTuong() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_DoiTuong(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_DoiTuong exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_ChiDinh() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_ChiDinh(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_ChiDinh exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_XetNghiem() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_XetNghiem(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_XetNghiem exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_NhomViKhuan() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {
            String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT).getJsonWebTokenByType().getLoginUserData(uuid);
            SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
            String user_id = _userInfo.getUserId();
            String db_schema = _userInfo.getDbSchema();
            String provide_id = _userInfo.getProvinceId();
            String hospital_id = _userInfo.getHospitalId();

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_NhomViKhuan(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_NhomViKhuan exception uuid=" + uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_ViKhuan() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {
            String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT).getJsonWebTokenByType().getLoginUserData(uuid);
            SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
            String user_id = _userInfo.getUserId();
            String db_schema = _userInfo.getDbSchema();
            String provide_id = _userInfo.getProvinceId();
            String hospital_id = _userInfo.getHospitalId();

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_ViKhuan(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_ViKhuan exception uuid=" + uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_NhomKhangSinh() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {
            String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT).getJsonWebTokenByType().getLoginUserData(uuid);
            SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
            String user_id = _userInfo.getUserId();
            String db_schema = _userInfo.getDbSchema();
            String provide_id = _userInfo.getProvinceId();
            String hospital_id = _userInfo.getHospitalId();

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_NhomKhangSinh(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_NhomKhangSinh exception uuid=" + uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_KhangSinh() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {
            String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT).getJsonWebTokenByType().getLoginUserData(uuid);
            SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
            String user_id = _userInfo.getUserId();
            String db_schema = _userInfo.getDbSchema();
            String provide_id = _userInfo.getProvinceId();
            String hospital_id = _userInfo.getHospitalId();

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_KhangSinh(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_KhangSinh exception uuid=" + uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_PatientList(String tu_ngay, String den_ngay, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName,
                    "{?=call LIS_Get_PatientList(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}", user_id + "$" + db_schema + "$"
                            + provide_id + "$" + hospital_id + "$" + tu_ngay + "$" + den_ngay + "$" + trang_thai + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_PatientList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            //obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("error_msg", e.toString());
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_PatientList_BA(String mabenhan, String trang_thai) {
        String func = Thread.currentThread().getStackTrace()[1].getMethodName();
        return callFunc(func, 1, mabenhan, trang_thai);
    }

    public String LIS_Get_PatientInfoBA(String mabenhan) {
        String func = Thread.currentThread().getStackTrace()[1].getMethodName();
        return callFunc(func, 1, mabenhan);
    }

    public String LIS_Receive_Test_v2(String sophieu, String nguoinhanmau, String ngaynhanmau) {
        String func = Thread.currentThread().getStackTrace()[1].getMethodName();
        return callFunc(func, 0, sophieu, nguoinhanmau, ngaynhanmau);
    }

    public String LIS_Get_Results_By_BA(String mabenhan, String matkhau) {
        String func = Thread.currentThread().getStackTrace()[1].getMethodName();
        return callFunc(func, 1, mabenhan, matkhau);
    }

    public String LIS_Get_PatientList(String tu_ngay, String den_ngay, String trang_thai, String nhom_xet_nghiem) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName,
                    "{?=call LIS_Get_PatientList2(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?o10S,?o11S)}", user_id + "$" + db_schema + "$"
                            + provide_id + "$" + hospital_id + "$" + tu_ngay + "$" + den_ngay + "$" + trang_thai + "$" + nhom_xet_nghiem + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_PatientList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_OrderList(String so_phieu, String ngay_chi_dinh) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_OrderList(?2S,?3S,?4S,?5S,?6S,?7S,?o8S,?o9S)}", user_id
                            + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + so_phieu + "$" + ngay_chi_dinh + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_OrderList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_OrderList(String so_phieu, String ngay_chi_dinh, String nhom_xet_nghiem) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_OrderList2(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}", user_id
                            + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + so_phieu + "$" + ngay_chi_dinh + "$" + nhom_xet_nghiem + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_OrderList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_ServiceList(String so_phieu, String ngay_chi_dinh) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility
                    .CALL_SP_O(this.dbName,
                            "{?=call LIS_Get_ServiceList(?2S,?3S,?4S,?5S,?6S,?7S,?o8S,?o9S)}", user_id + "$" + db_schema
                                    + "$" + provide_id + "$" + hospital_id + "$" + so_phieu + "$" + ngay_chi_dinh + "$$",
                            b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_ServiceList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_ServiceList(String so_phieu, String ngay_chi_dinh, String nhom_xet_nghiem) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility
                    .CALL_SP_O(this.dbName,
                            "{?=call LIS_Get_ServiceList2(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}", user_id + "$" + db_schema
                                    + "$" + provide_id + "$" + hospital_id + "$" + so_phieu + "$" + ngay_chi_dinh + "$" + nhom_xet_nghiem + "$$",
                            b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_ServiceList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_ServiceListBN(String ma_benh_nhan) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_ServiceListBN(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + ma_benh_nhan + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_ServiceListBN exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_ServiceListSP(final String ma_phieu) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = {"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_ServiceListSP(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}", user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + ma_phieu + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);
            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_ServiceListSP exception uuid=" + this.uuid + ":", e);
            JSONObject obj2 = new JSONObject();
            obj2.put("error_code", "1");
            obj2.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj2.put("result", result);
            return obj2.toString();
        }
    }

    public String LIS_Receive_Sample(String so_phieu, String ngay_chi_dinh, String barcode, String ngay_tiep_nhan,
                                     String gio_tiep_nhan, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName, "{?=call LIS_Receive_Sample(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S,?11S,?o12S,?o13S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, ngay_chi_dinh, barcode,
                            ngay_tiep_nhan, gio_tiep_nhan, trang_thai, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Receive_Sample exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Receive_Service(String so_phieu, String barcode, String ngay_chi_dinh, String service_code,
                                      String ngay_tiep_nhan, String gio_tiep_nhan, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName, "{?=call LIS_Receive_Service(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S,?11S,?12S,?o13S,?o14S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, barcode, ngay_chi_dinh, service_code,
                            ngay_tiep_nhan, gio_tiep_nhan, trang_thai, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Receive_Service exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Receive_Test(String so_phieu, String barcode, String ngay_chi_dinh, String test_code,
                                   String ngay_tiep_nhan, String gio_tiep_nhan, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName, "{?=call LIS_Receive_Test(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S,?11S,?12S,?o13S,?o14S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, barcode, ngay_chi_dinh, test_code,
                            ngay_tiep_nhan, gio_tiep_nhan, trang_thai, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Receive_Test exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Receive_TestSP(String so_phieu_chung, String barcode, String ngay_chi_dinh, String test_code,
                                     String ngay_tiep_nhan, String gio_tiep_nhan, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName, "{?=call LIS_Receive_TestSP(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S,?11S,?12S,?o13S,?o14S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu_chung, barcode, ngay_chi_dinh, test_code,
                            ngay_tiep_nhan, gio_tiep_nhan, trang_thai, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Receive_TestSP exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Update_Appointment(String so_phieu, String ngay_chi_dinh, String thong_tin_hen) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName,
                    "{?=call LIS_Update_Appointment(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, ngay_chi_dinh, thong_tin_hen, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Update_Appointment exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Update_Result(String so_phieu, String ngay_chi_dinh, String tap_ket_qua, String ket_luan, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {
            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            String userData = "";
            SysObject _userInfo = null;
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();

                userInfo = new JSONObject();
                userInfo.put("USER_ID", user_id);
                userInfo.put("DB_SCHEMA", db_schema);
                userInfo.put("HOSPITAL_ID", hospital_id);
                userInfo.put("DB_NAME", "jdbc/HISL2DS");
            }
            String hospitalCode = DBUtil.getOneValueString(this.dbName, "SELECT hospital_code FROM org_organization WHERE org_id= ?1L", new Object[]{hospital_id});
            userInfo.put(SysConstants.HOSPITAL_CODE, hospitalCode);
            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S("jdbc/HISL2DS",
                    "{?=call LIS_Update_Result(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S,?o11S,?o12S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, ngay_chi_dinh, tap_ket_qua,
                            ket_luan, trang_thai, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            // begin of sms
            if (result.contains("@sms") && error_code.equals("0") && trang_thai.equals("2")) {
                SmsSendBusiness sms = new SmsSendBusiness();

                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.MINUTE, 1);
                Date addMinutes = calendar.getTime();
                SimpleDateFormat sd = new SimpleDateFormat("dd/MM/yyyy HH:mm");
                String thoi_gian_gui = sd.format(addMinutes);

                String[] smsarr = res.split("@");

                String smsinfo = smsarr[1];
                String[] infoarr = smsinfo.split("\\|", 4);
                String khambenhid = infoarr[1];
                String sdtbn = infoarr[2];
                String noidung = infoarr[3];

                if (sdtbn.length() > 5) {
                    if (sdtbn.startsWith("+")) {
                        logger.info("LIS_Update_Result|Phone number start with [+] text.");
                    } else if (sdtbn.startsWith("0")) {
                        sdtbn = "84" + sdtbn.substring(1);
                    } else {
                        sdtbn = "84" + sdtbn;
                    }
                    try {
                        SmsResponse resp = sms.sendSMS(this.dbName, sdtbn, noidung, thoi_gian_gui, this.uuid);

                        JSONObject smsobj = new JSONObject(resp.toString());
                        String REQ_ID = smsobj.getString("REQ_ID");
                        String RESP_CODE = smsobj.getString("RESP_CODE");

                        JSONObject datasaveobj = new JSONObject();
                        datasaveobj.put("req_id", REQ_ID);
                        datasaveobj.put("resp_code", RESP_CODE);
                        datasaveobj.put("sdtbenhnhan", sdtbn);
                        datasaveobj.put("schedule_sms", thoi_gian_gui);
                        datasaveobj.put("loai", 3);
                        datasaveobj.put("noidung_sms", noidung);
                        datasaveobj.put("kq_send_sms", resp.toString());
                        datasaveobj.put("khambenhid", khambenhid);

                        String datasave = datasaveobj.toString();
                        Integer res2 = 0;
                        try {
                            res2 = DBUtility.CALL_SP_I(this.dbName,
                                    "{?=call them_lichnhac_sms(?2S,?3S,?4S,?5S,?6S)}",
                                    new Object[]{user_id, hospital_id, db_schema, provide_id, datasave},
                                    b_out);
                            result = result + "|err:" + res2;
                        } catch (Exception ex) {
                            result = result + "|err:" + res2;
                        }
                    } catch (Exception ex) {
                        result = smsarr[0] + "@sms|" + ex.getMessage();
                    }
                } else {
                    result = smsarr[0];
                }
            } else if (result.contains("@tsms") && error_code.equals("0") && trang_thai.equals("2")) {

                String[] smsarr = res.split("@");

                String smsinfo = smsarr[1];
                String[] infoarr = smsinfo.split("\\|", 7);
                String khambenhid = infoarr[1];
                String hotenbn = infoarr[2];
                String sdtbn = infoarr[3];
                String mahsba = infoarr[4];
                String portalkq = infoarr[5];
                String passbn = DBUtil.getOneValueString(this.dbName, "SELECT matkhau FROM " + db_schema + ".ban_hosobenhan WHERE mahosobenhan = ?1S and csytid = ?2L",
                        new Object[]{mahsba, hospital_id});
                String thoi_gian_gui = infoarr[6];

                if (getCauHinh(this.dbName, _userInfo, 3).length() > 1) {
                    String[] SMS_CH_KQXN = getCauHinh(this.dbName, _userInfo, 3).split(",");
                    if (SMS_CH_KQXN[0].equals("0")) {
                        hotenbn = "";
                    }
                    if (SMS_CH_KQXN[1].equals("0")) {
                        portalkq = "";
                    }
                    if (SMS_CH_KQXN[2].equals("0")) {
                        mahsba = "";
                    }
                    if (SMS_CH_KQXN[3].equals("0")) {
                        passbn = "";
                    }
                }
                if (!sdtbn.contentEquals("")) {
                    String templatesms = "KQXN";
                    String urlCallback = _userInfo.getUrlReport().replace("//baocao", "//").replace("/dreport/", "");

                    JSONArray parr = new JSONArray();
                    parr.put(hotenbn);
                    parr.put(portalkq + "?maba=" + mahsba + "&mk=" + passbn);
                    parr.put(mahsba);
                    parr.put(passbn);

                    String sql = "select o.org_code || '|' || o.org_name || '|' || p.org_name || '|' || p.org_name\n" +
                            "from " + db_schema + ".kbh_maubenhpham km,\n" +
                            "    org_organization o,\n" +
                            "    org_organization p\n" +
                            "where sophieu = ?1S\n" +
                            "    and km.khoachuyendenid = o.org_id\n" +
                            "    and km.phongchuyendenid = p.org_id\n" +
                            "    AND ROWNUM <=1";
                    String ttkhoa = DBUtil.getOneValueString(this.dbName, sql, new Object[]{so_phieu});
                    JSONObject ttkp = new JSONObject();
                    if (!CheckObjectUtils.isNullOrEmpty(ttkhoa) && ttkhoa.split("\\|").length == 4) {
                        String[] resultSplit = ttkhoa.split("\\|");
                        ttkp.put("ma_khoa", resultSplit[0]);
                        ttkp.put("ten_khoa", resultSplit[1]);
                        ttkp.put("ma_phong", resultSplit[2]);
                        ttkp.put("ten_phong", resultSplit[3]);
                    }

                    SmsTemplateBusiness sms = new SmsTemplateBusiness(userInfo);
                    SmsResponse resp = sms.sendTemplateInternalOH(sdtbn, templatesms, parr, thoi_gian_gui, urlCallback + "/vnpthis/sms/send-callback-template", khambenhid, "3", ttkp);
                    logger.info("LIS_Update_Result|Send SMS to " + sdtbn + " " + thoi_gian_gui + " " + resp.toString());
                    JSONObject smsobj = new JSONObject(resp.toString());
                    String REQ_ID = smsobj.getString("REQ_ID");
                    String RESP_CODE = smsobj.getString("RESP_CODE");

                    result = result + "|" + REQ_ID + "|" + RESP_CODE;
                } else {
                    result = smsarr[0];
                }


            }
            // end of sms

            // begin of notify
            if (error_code.equals("0") && trang_thai.equals("2")) {
                String notify = CommonUtility.getComConf(this.dbName, hospital_id, "NOTIFY_APP_VNCARE");
                if (notify.equals("1")) {
                    try {
                        String access_token = getTokenNotify();

                        if (access_token != null & !"".equals(access_token)) {
                            String ctbn = DBUtility.CALL_SP_O(this.dbName, "{?=call FCLS07N001_CTTB(?2S,?3S,?4S,?5S,?6S)}",
                                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + so_phieu, b_out);
                            if (ctbn != null && !"".equals(ctbn)) {
                                JSONArray obja = new JSONArray(ctbn.replace("\\", ""));
                                if (obja.length() > 0) {
                                    JSONObject objo = new JSONObject(obja.getString(0));
                                    JSONObject content = new JSONObject();
                                    content.put("maCSYT", objo.getString("HOSPITAL_CODE"));
                                    content.put("tenCSYT", objo.getString("ORG_NAME"));
                                    content.put("maBN", objo.getString("MABENHNHAN"));
                                    content.put("tenBN", objo.getString("TENBENHNHAN"));
                                    content.put("phone", objo.getString("TK_LIENKET"));
                                    content.put("soPhong", objo.getString("MAPHONG"));
                                    content.put("tenPhong", objo.getString("TENPHONG"));
                                    content.put("loaiDichVu", "1");
                                    content.put("mauBenhPhamID", objo.getString("MAUBENHPHAMID"));
                                    content.put("soPhieu", objo.getString("SOPHIEU"));
                                    content.put("tenBS", objo.getString("FULL_NAME"));
                                    content.put("maLuotKham", objo.getString("MAUBENHPHAMID"));
                                    content.put("tenPhongNhanKQ", objo.getString("PHONGTHUCHIEN"));

                                    PortalWS pw = new PortalWS(this.dbName, this.mediaSchema, this.uuid);
                                    String kq = pw.notify_vncare("3", content.toString(), access_token);

                                    result = result + "@notify|" + kq;
                                    logger.info("LIS_Update_Result|" + result);
                                }
                            }
                        }
                    } catch (Exception ex) {
                        result = result + "@notify|" + ex.getMessage();
                        logger.error("LIS_Update_Result|Error|" + result);
                    }
                }
            }
            // end of notify

            // send pc covid
            if (result.contains("@pcc") && error_code.equals("0") && trang_thai.equals("2")) {

                HttpClient client = HttpClientBuilder.create().build();

                String ttbn = DBUtility.CALL_SP_O(this.dbName,
                        "{?=call lis_pcc_patientinfo(?2S,?3S,?4S,?5S,?6S)}",
                        user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + so_phieu, b_out);
                JSONArray obja = new JSONArray(ttbn.replace("\\", ""));
                if (obja.length() > 0) {
                    JSONObject obj = obja.getJSONObject(0);

                    String ttch = DBUtility.CALL_SP_O(this.dbName, "{?=call lis_pcc_getconfig(?2S)}", hospital_id, b_out);
                    JSONArray objb = new JSONArray(ttch.replace("\\", ""));
                    JSONObject chinh = objb.getJSONObject(0);
                    String postURL = "https://xetnghiem.aita.gov.vn/services/AddTiemChung.ashx";
                    postURL = chinh.getString("URL_PCCOVID");
                    HttpPost post = new HttpPost(postURL);

                    List<NameValuePair> params = new ArrayList<NameValuePair>();
                    params.add(new BasicNameValuePair("HoVaTen", obj.getString("TENBENHNHAN")));
                    params.add(new BasicNameValuePair("NamSinh", obj.getString("NAMSINH")));
                    params.add(new BasicNameValuePair("Gioitinh", obj.getString("GIOITINHID")));
                    params.add(new BasicNameValuePair("SDT", obj.getString("SODIENTHOAI")));
                    params.add(new BasicNameValuePair("CMTORCC", obj.getString("CMT")));
                    params.add(new BasicNameValuePair("DiaChi", obj.getString("DIACHI")));
                    params.add(new BasicNameValuePair("XaPhuong", obj.getString("XAPHUONG")));
                    params.add(new BasicNameValuePair("QuanHuyen", obj.getString("QUANHUYEN")));
                    params.add(new BasicNameValuePair("TinhThanhPho", obj.getString("TINHTHANHPHO")));
                    params.add(new BasicNameValuePair("LoaiDoiTuong", obj.getString("LOAIDOITUONG")));
                    params.add(new BasicNameValuePair("NgayLayMau", obj.getString("NGAYLAYMAU")));
                    params.add(new BasicNameValuePair("NgayXetNghiem", obj.getString("NGAYXETNGHIEM")));
                    params.add(new BasicNameValuePair("PhuongPhapXetNghiem", obj.getString("PHUONGPHAPXETNGHIEM")));
                    params.add(new BasicNameValuePair("KetQuaXetNghiem", obj.getString("KETQUA")));
                    params.add(new BasicNameValuePair("GhiChu", obj.getString("GHICHU")));
                    params.add(new BasicNameValuePair("PhongXetNghiemID", chinh.getString("PXN_PCCOVID")));
                    params.add(new BasicNameValuePair("Authorization", chinh.getString("AUTH_PCCOVID")));
                    params.add(new BasicNameValuePair("DiaChiXetNghiem", obj.getString("DIACHIXETNGHIEM")));
                    params.add(new BasicNameValuePair("BHYT", obj.getString("BHYT")));

                    UrlEncodedFormEntity ent = new UrlEncodedFormEntity(params, "UTF-8");
                    post.setEntity(ent);
                    HttpResponse response = client.execute(post);
                    String responseString = new BasicResponseHandler().handleResponse(response);

                    int responseCode = response.getStatusLine().getStatusCode();

                    String kqpcc = DBUtility.CALL_SP_S(this.dbName, "{?=call lis_pcc_request(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S)}",
                            new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, obj.toString(), "0", "", responseString}, b_out);
                    logger.info("LIS_Update_Result|Send kq to pccovid " + so_phieu + ":" + kqpcc);
                }

            }
            // end of send pc covid

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);
            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Update_Result exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String sendToPCCovid(String so_phieu) {
        try {
            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;
            String[] b_out = new String[]{"", ""};
            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            String userData = "";
            SysObject _userInfo = null;
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();

                userInfo = new JSONObject();
                userInfo.put("USER_ID", user_id);
                userInfo.put("DB_SCHEMA", db_schema);
                userInfo.put("HOSPITAL_ID", hospital_id);
                userInfo.put("DB_NAME", "jdbc/HISL2DS");
            }

            try {
                HttpClient client = HttpClientBuilder.create().build();

                String ttbn = DBUtility.CALL_SP_O(this.dbName,
                        "{?=call lis_pcc_patientinfo(?2S,?3S,?4S,?5S,?6S)}",
                        user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + so_phieu, b_out);
                JSONArray obja = new JSONArray(ttbn.replace("\\", ""));
                if (obja.length() > 0) {
                    JSONObject obj = obja.getJSONObject(0);

                    String ttch = DBUtility.CALL_SP_O(this.dbName, "{?=call lis_pcc_getconfig(?2S)}", hospital_id, b_out);
                    JSONArray objb = new JSONArray(ttch.replace("\\", ""));
                    JSONObject chinh = objb.getJSONObject(0);
                    String postURL = "https://xetnghiem.aita.gov.vn/services/AddTiemChung.ashx";
                    postURL = chinh.getString("URL_PCCOVID");
                    HttpPost post = new HttpPost(postURL);

                    List<NameValuePair> params = new ArrayList<NameValuePair>();
                    params.add(new BasicNameValuePair("HoVaTen", obj.getString("TENBENHNHAN")));
                    params.add(new BasicNameValuePair("NamSinh", obj.getString("NAMSINH")));
                    params.add(new BasicNameValuePair("Gioitinh", obj.getString("GIOITINHID")));
                    params.add(new BasicNameValuePair("SDT", obj.getString("SODIENTHOAI")));
                    params.add(new BasicNameValuePair("CMTORCC", obj.getString("CMT")));
                    params.add(new BasicNameValuePair("DiaChi", obj.getString("DIACHI")));
                    params.add(new BasicNameValuePair("XaPhuong", obj.getString("XAPHUONG")));
                    params.add(new BasicNameValuePair("QuanHuyen", obj.getString("QUANHUYEN")));
                    params.add(new BasicNameValuePair("TinhThanhPho", obj.getString("TINHTHANHPHO")));
                    params.add(new BasicNameValuePair("LoaiDoiTuong", obj.getString("LOAIDOITUONG")));
                    params.add(new BasicNameValuePair("NgayLayMau", obj.getString("NGAYLAYMAU")));
                    params.add(new BasicNameValuePair("NgayXetNghiem", obj.getString("NGAYXETNGHIEM")));
                    params.add(new BasicNameValuePair("PhuongPhapXetNghiem", obj.getString("PHUONGPHAPXETNGHIEM")));
                    params.add(new BasicNameValuePair("KetQuaXetNghiem", obj.getString("KETQUA")));
                    params.add(new BasicNameValuePair("GhiChu", obj.getString("GHICHU")));
                    params.add(new BasicNameValuePair("PhongXetNghiemID", chinh.getString("PXN_PCCOVID")));
                    params.add(new BasicNameValuePair("Authorization", chinh.getString("AUTH_PCCOVID")));
                    params.add(new BasicNameValuePair("DiaChiXetNghiem", obj.getString("DIACHIXETNGHIEM")));
                    params.add(new BasicNameValuePair("BHYT", obj.getString("BHYT")));

                    UrlEncodedFormEntity ent = new UrlEncodedFormEntity(params, "UTF-8");
                    post.setEntity(ent);
                    HttpResponse response = client.execute(post);
                    String responseString = new BasicResponseHandler().handleResponse(response);

                    int responseCode = response.getStatusLine().getStatusCode();

                    String kqpcc = DBUtility.CALL_SP_S(this.dbName, "{?=call lis_pcc_request(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S)}",
                            new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, obj.toString(), "0", "Send kq to pccovid " + responseCode, responseString}, b_out);
                    logger.info("sendToPCCovid|Send kq to pccovid " + so_phieu + ":" + responseCode);
                    return "Send kq to pccovid " + so_phieu + ":" + responseCode;
                }
            } catch (Exception ex) {
                String kqpcc = DBUtility.CALL_SP_S(this.dbName, "{?=call lis_pcc_request(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S)}",
                        new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, so_phieu, "9", "", ex.toString()}, b_out);
                logger.info("sendToPCCovid|Send kq to pccovid " + so_phieu + ":" + ex);
            }
        } catch (Exception exx) {
            logger.error("sendToPCCovid|Send kq to pccovid " + so_phieu + ":" + exx);
        }
        return "";
    }

    public String getTokenNotify() {
        try {
            if (token_notify == null || token_notify.equals("")) {
                PortalWS pw = new PortalWS(this.dbName, this.mediaSchema, this.uuid);
                token_notify = login_notify_vncare();

                JSONObject tobj = new JSONObject(token_notify);
                SimpleDateFormat sd = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                Date t = new Date();
                t = DateUtils.addHours(t, 1);
                String expires_date = sd.format(t);
                tobj.put("expires_date", expires_date);
                token_notify = tobj.toString();

                JSONObject re = new JSONObject(tobj.getString("result"));
                String access_token = re.getString("access_token");
                return access_token;
            } else {
                JSONObject tobj = new JSONObject(token_notify);
                String expires_date = tobj.getString("expires_date");
                Date d = DateUtil.toDate(expires_date, "dd/MM/yyyy HH:mm:ss");
                Date now = new Date();
                if (now.after(d)) {
                    token_notify = "";
                    return getTokenNotify();
                } else {
                    JSONObject re = new JSONObject(tobj.getString("result"));
                    String access_token = re.getString("access_token");
                    return access_token;
                }
            }
        } catch (Exception e) {
            logger.error("getTokenNotify=" + e);
        }
        return "";
    }

    public String login_notify_vncare() throws Exception {
        String token = "";
        String hid = CommonUtility.getHID(this.uuid);
        String url_login = CommonUtility.getComConf(this.dbName, hid, "URL_LOGIN_NOTIFY");
        String userLogin = CommonUtility.getComConf(this.dbName, hid, "USER_LOGIN_NOTIFY");
        String passLogin = CommonUtility.getComConf(this.dbName, hid, "PASS_LOGIN_NOTIFY");

        String userData =
                new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
        SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
        String user_id = _userInfo.getUserId();
        String db_schema = _userInfo.getDbSchema();
        String provide_id = _userInfo.getProvinceId();
        String hospital_id = _userInfo.getHospitalId();
        String hospitalCode = DBUtil.getOneValueString(this.dbName, "SELECT hospital_code FROM org_organization WHERE org_id = ?1L", new Object[]{hospital_id});

        String value = "{\"HOSPITALID\": \"" + hospitalCode + "\",\"USERNAME\": \""
                + userLogin + "\",\"PASSWORD\":\"" + passLogin + "\"}";
        HttpUtil a = new HttpUtil();
        org.json.JSONObject jsonObj = new org.json.JSONObject();
        jsonObj = a.lienket_tk_vncare_login(url_login, value);
        if (jsonObj.getString("errorCode").equals("0")) {
            String resultToken = jsonObj.getString("result");
            JSONObject jsonUuid = new JSONObject(resultToken);
            if (jsonUuid.getString("access_token").equals("")) {
                return "-1";
            } else {
                return jsonObj.toString();
            }
        } else {
            return "-1";
        }
    }

    public String LISLS_Get_PatientList(String tu_ngay, String den_ngay, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LISLS_Get_PatientList(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + tu_ngay + "$" + den_ngay + "$"
                            + trang_thai + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LISLS_Get_PatientList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LISLS_Get_ServiceList(String ma_benh_nhan, String ngay_chi_dinh, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LISLS_Get_ServiceList(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + ma_benh_nhan + "$"
                            + ngay_chi_dinh + "$" + trang_thai + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LISLS_Get_ServiceList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String GetListOrderByTime(String tu_ngay, String den_ngay) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(
                    this.dbName, "{?=call LISMT_GetListOrderByTime(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}", user_id + "$"
                            + db_schema + "$" + provide_id + "$" + hospital_id + "$" + tu_ngay + "$" + den_ngay + "$0$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("GetListOrderByTime exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String GetOrder(String so_phieu) {
        String orderResult = "";
        String testResult = "";
        String subResult = "";
        String error_code = "";
        String error_msg = "";

        try {

            Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").create();
            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            JSONObject obj = new JSONObject();

            // lấy thông tin bệnh nhân
            String[] b_out = new String[]{"", ""};
            orderResult = DBUtility.CALL_SP_O(this.dbName, "{?=call LISMT_GetOrder(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + so_phieu + "$$", b_out);
            error_code = b_out[0];
            error_msg = b_out[1];

            if (error_code.equals("0")) {
                JsonParser parser = new JsonParser();
                JsonElement elemOrder = parser.parse(orderResult);
                JsonArray elemArrOrder = elemOrder.getAsJsonArray();

                PatientInfo patient = gson.fromJson(elemArrOrder.get(0).toString(), PatientInfo.class);

                // lấy thông tin các xét nghiệm
                testResult = DBUtility.CALL_SP_O(this.dbName, "{?=call LISMT_GetService(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                        user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + so_phieu + "$$", b_out);
                error_code = b_out[0];
                error_msg = b_out[1];

                if (error_code.equals("0")) {
                    JsonElement elemTest = parser.parse(testResult);
                    JsonArray elemArrTest = elemTest.getAsJsonArray();
                    List<TestResult> testlist = new ArrayList<TestResult>();
                    for (int i = 0; i < elemArrTest.size(); i++) {
                        TestResult test = gson.fromJson(elemArrTest.get(i).toString(), TestResult.class);

                        // lấy thông tin các xét nghiệm con nếu có
                        String madichvu = test.MaDV;
                        subResult = DBUtility.CALL_SP_O(this.dbName, "{?=call LISMT_GetTest(?2S,?3S,?4S,?5S,?6S,?7S,?o8S,?o9S)}",
                                user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$" + so_phieu + "$" + madichvu + "$$", b_out);
                        String suberrcode = b_out[0];

                        if (suberrcode.equals("0")) {
                            JsonElement elemSub = parser.parse(subResult);
                            JsonArray elemArrSub = elemSub.getAsJsonArray();
                            if (elemArrSub.size() > 0) {
                                List<SubTestResult> subtestlist = new ArrayList<SubTestResult>();
                                for (int j = 0; j < elemArrSub.size(); j++) {
                                    SubTestResult sub = gson.fromJson(elemArrSub.get(j).toString(), SubTestResult.class);
                                    subtestlist.add(sub);
                                }
                                test.setListSubTestResult(subtestlist);
                            }
                        }
                        testlist.add(test);
                    }
                    patient.setListTestResult(testlist);
                }

                obj.put("error_code", error_code);
                obj.put("error_msg", error_msg);
                obj.put("result", gson.toJson(patient));
            } else {
                obj.put("error_code", error_code);
                obj.put("error_msg", error_msg);
                obj.put("result", orderResult);
            }
            return obj.toString();
        } catch (Exception e) {
            logger.error("GetOrder exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", "");
            return obj.toString();
        }
    }

    public String UpdateResult(String PatientInfo) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName, "{?=call LISMT_UpdateResult(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, PatientInfo, "", ""}, b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("UpdateResult exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_GiaiPhau() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_GiaiPhau(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_XetNghiem exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_DM_ThietBi() {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_Get_DM_ThietBi(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_DM_BacSi exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LISGP_Get_PatientList(String tu_ngay, String den_ngay, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName,
                    "{?=call LISGP_Get_PatientList(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}", user_id + "$" + db_schema + "$"
                            + provide_id + "$" + hospital_id + "$" + tu_ngay + "$" + den_ngay + "$" + trang_thai + "$$",
                    b_out);
            error_code = b_out[0] + "";
            JSONArray obja = new JSONArray(result.replace("\\", ""));
            int sophieu = obja.length();
            error_msg = "Tong so phieu: " + sophieu;

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_PatientList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LISGP_Update_Result(String so_phieu, String ngay_chi_dinh, String tap_ket_qua, String ket_luan,
                                      String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName,
                    "{?=call LISGP_Update_Result(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S,?o11S,?o12S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, ngay_chi_dinh, tap_ket_qua,
                            ket_luan, trang_thai, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Update_Result exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_PatientInfo(String ma_tim_kiem) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName,
                    "{?=call LIS_Get_PatientInfo(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}", user_id + "$" + db_schema + "$"
                            + provide_id + "$" + hospital_id + "$" + ma_tim_kiem + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_PatientInfo exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_ChiDinh_Mau(String ma_tim_kiem) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName,
                    "{?=call LIS_GET_CHIDINH_MAU(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}", user_id + "$" + db_schema + "$"
                            + provide_id + "$" + hospital_id + "$" + ma_tim_kiem + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_GET_CHIDINH_MAU exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Update_ServiceInfo(String so_phieu, String ngay_chi_dinh, String service_info) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName,
                    "{?=call LIS_Update_ServiceInfo(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, ngay_chi_dinh, service_info, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Update_ServiceInfo exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Update_ChiDinh_Mau(String so_phieu, String ngay_chi_dinh, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName,
                    "{?=call API_UPD_TRANGTHAI_THUOC(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, ngay_chi_dinh, trang_thai, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Update_ServiceInfo exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_PatientReport(String so_phieu, String malienket) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName,
                    "{?=call LIS_Get_PatientReport(?2S,?3S,?4S,?5S,?6S,?7S,?o8S,?o9S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, malienket, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_PatientReport exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    // Bổ sung phần ký số
    public String LIS_GET_LOAI_PHIEU() {

        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName, "{?=call LIS_GET_LOAI_PHIEU(?2S,?3S,?4S,?5S,?o6S,?o7S)}",
                    user_id + "$" + db_schema + "$" + provide_id + "$" + hospital_id + "$$", b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();

        } catch (Exception e) {
            logger.error("LIS_GET_LOAI_PHIEU exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_PatientList_SignedCA(String tu_ngay, String den_ngay, String trang_thai) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {
            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName,
                    "{?=call LIS_Get_PatientList_SignedCA(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}", user_id + "$" + db_schema + "$"
                            + provide_id + "$" + hospital_id + "$" + tu_ngay + "$" + den_ngay + "$" + trang_thai + "$$",
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_PatientList_SignedCA exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            //obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("error_msg", e.toString());
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Get_PatientList_SignedCA(String tu_ngay, String den_ngay, String trang_thai, String nhom_xet_nghiem) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName,
                    "{?=call LIS_Get_PatientList2_SignedCA(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?o10S,?o11S)}", user_id + "$" + db_schema + "$"
                            + provide_id + "$" + hospital_id + "$" + tu_ngay + "$" + den_ngay + "$" + trang_thai + "$" + nhom_xet_nghiem + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_PatientList exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Update_Result_SignedCA(String so_phieu, String ngay_chi_dinh, String dsdichvu, String xmlcontent, String reportCode, String trang_thai, String userca, String passca) {
        String result = "";
        String error_code = "";
        String error_msg = "";
        Long startLIS = System.currentTimeMillis();
        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            SysObject _userInfo = new SysObject();
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }
            String hospitalCode = DBUtil.getOneValueString(this.dbName, "SELECT hospital_code FROM org_organization WHERE org_id = ?1L", new Object[]{hospital_id});
            JSONObject userInfox = new JSONObject();
            userInfox.put(SysConstants.USER_ID, user_id);
            userInfox.put(SysConstants.DB_SCHEMA, db_schema);
            userInfox.put(SysConstants.PROVINCE_ID, provide_id);
            userInfox.put(SysConstants.HOSPITAL_ID, hospital_id);
            userInfox.put(SysConstants.COMPANY_ID, hospital_id);
            userInfox.put(SysConstants.HOSPITAL_CODE, hospitalCode);
            HttpEmrUtilsNew httpEmrUtilsNew = new HttpEmrUtilsNew("jdbc/HISL2DS", userInfox);

            ApiCaResponse apiCaResponse = new ApiCaResponse(0, "OK", "{}");

            String templateCode = "";
            if ("1".equals(new AjaxJson(this.dbName).ajaxCALL_SP_S("{?=call com_lay_cauhinh('" + _userInfo.getHospitalId() + "',?2S)}", "LIS_EMR_TEMPLATE_XML_REFER" + "$", new Object[]{}))) {
                Matcher templateCodeMatcher = REPORT_CODE_XML_REGEX.matcher(xmlcontent);
                if (templateCodeMatcher.find()) {
                    templateCode = templateCodeMatcher.group();
                }
            }

            // không ký, ký số , ký điện tử, signserver, smartca
            if ("0".equals(trang_thai) || "1".equals(trang_thai) || "2".equals(trang_thai) || "5".equals(trang_thai) || "6".equals(trang_thai)) {
                // day du lieu vao idg
                Long timeStartIDG = System.currentTimeMillis();
                Long caid = EmrCaDetail.getCaId(this.dbName, db_schema);
                if (CheckObjectUtils.isNullOrEmpty(caid)) {
                    caid = (long) 0;
                }

                if (CheckObjectUtils.isNullOrEmpty(hospitalCode)) {
                    caid = (long) 0;
                }

                int isStorageObject = Integer.valueOf(PropertiesUtility.getConfBundle("com.media.emr.storage.object"));
                String bucketName = PropertiesUtility.getConfBundle("com.media.emr.storage.bucketname");

                Calendar nowDate = Calendar.getInstance();
                nowDate.get(Calendar.YEAR);
                String objectName = "xml/" + hospitalCode + "/" + nowDate.get(Calendar.YEAR) + "/" + (nowDate.get(Calendar.MONTH) + 1) + "/" + caid + ".xml";

                String lisCAOffUplIdg = getConfig(hospital_id, "LIS_CA_OFF_UPL_IDG");
                if (isStorageObject == 1 && "0".equals(lisCAOffUplIdg)) {
                    String serviceUrl = PropertiesUtility.getConfBundle("idg.storage.emr.service.url");
                    String accessKey = PropertiesUtility.getConfBundle("idg.storage.emr.access.key");
                    String secretKey = PropertiesUtility.getConfBundle("idg.storage.emr.secret.key");
                    byte[] xmlData = xmlcontent.getBytes(StandardCharsets.UTF_8);
                    InputStream is = new ByteArrayInputStream(xmlData);
                    boolean idgUpload = IdgStorage.putObject(is, bucketName, "xml/" + hospitalCode + "/" + nowDate.get(Calendar.YEAR) + "/" + (nowDate.get(Calendar.MONTH) + 1), caid + ".xml", xmlData.length, "text/xml",
                            serviceUrl, accessKey, secretKey);
                    if (!idgUpload) {
                        caid = (long) 0;
                    }
                    logger.info("LIS_Update_Result_SignedCA|sendIDG sophieu: " + so_phieu + " | " + objectName + " | execution_time(ms): " + (System.currentTimeMillis() - timeStartIDG));
                }

                // luu tren his
                Long timeStartHIS = System.currentTimeMillis();
                String[] b_out = new String[]{"", ""};
                String res = DBUtility.CALL_SP_S(this.dbName,
                        "{?=call lis_update_result_signedca2(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S,?11S,?12S,?13S,?14S,?15S,?16S,?o17S,?o18S)}",
                        new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, ngay_chi_dinh, dsdichvu, xmlcontent, reportCode, templateCode, trang_thai, bucketName, objectName, userca, passca, "", ""},
                        b_out);
                result = res + "";
                error_code = b_out[0] + "";
                error_msg = b_out[1] + "";

                String[] resultarr = CheckObjectUtils.isNullOrEmpty(res) ? new String[]{} : res.split("@", 3);
                if (resultarr.length > 1) {
                    String resulttime = resultarr[0];
                    String resultinfo = resultarr[1];

                    logger.info("LIS_Update_Result_SignedCA|saveHIS sophieu: " + so_phieu + " | " + resulttime + " | execution_time(ms): " + (System.currentTimeMillis() - timeStartHIS));
                    Map<String, Object> mapParam = new HashMap<>();
                    String[] mappa = resultinfo.split("\\|");
                    String keyCAs = mappa[0];
                    String paramHashed = mappa[1];
                    JSONArray keyCA = new JSONArray(keyCAs);
                    for (int i = 0; i < keyCA.length(); i++) {
                        JSONObject objx = keyCA.getJSONObject(i);
                        mapParam.put(objx.get("NAME").toString().toLowerCase(), objx.get("VALUE"));
                    }
                    mapParam.put("causer", userca);
                    mapParam.put("capassword", passca);
                    mapParam.put("type", "17");
                    mapParam.put("dichVus", dsdichvu);
                    // thong bao toi emr
                    if ("0".equals(error_code)) {
                        String accountLogin = getCauHinh(this.dbName, _userInfo, 1);
                        if (!CheckObjectUtils.isNullOrEmpty(accountLogin) && !"0".equals(accountLogin) && accountLogin.split("/").length == 2) {
                            String us;
                            String pw;
                            String strToken = "";
                            final String sp = DBActUtil.setDefaultInfo("{?=call emr_get_token('[UID]','[HID]','[SCH]','[IP]',?2S)}", user_id, hospital_id, provide_id, db_schema);
                            List<Map<String, Object>> obj = AjaxJson.callFunction(this.dbName, sp, "$", new Object[]{});
                            String url = getCauHinh(this.dbName, _userInfo, 2);
                            String urlLogin = url + "api/auth/public/login";
                            String urlReceive = url + "api/nosql/receive/v2";
                            if (CheckObjectUtils.isNullOrEmpty(obj)) {

                                if (!CheckObjectUtils.isNullOrEmpty(accountLogin)) {
                                    String[] parts = accountLogin.split("/");
                                    us = parts[0];
                                    pw = parts[1];
                                    strToken = httpEmrUtilsNew.getToken(us, pw, urlLogin);
                                    addLogin(this.dbName, _userInfo, us, pw, strToken);
                                }
                            } else {
                                String timeLogin = obj.get(0).get("NGAYDANGNHAP").toString();
                                DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                                Date loginDate = DateUtils.addHours(dateFormat.parse(timeLogin), 1);
                                Date currentDate = new Date();
                                if (currentDate.compareTo(loginDate) > 0) {
                                    if (!CheckObjectUtils.isNullOrEmpty(accountLogin)) {
                                        String[] parts = accountLogin.split("/");
                                        us = parts[0];
                                        pw = parts[1];
                                        strToken = httpEmrUtilsNew.getToken(us, pw, urlLogin);
                                        addLogin(this.dbName, _userInfo, us, pw, strToken);
                                    }
                                } else {
                                    strToken = obj.get(0).get("TOKEN").toString();
                                }
                            }
                            ApiCaResponse apiCaResponseT = new ApiCaResponse(0, "OK", "{}");
                            if (caid == 0) {
                                apiCaResponse = new ApiCaResponse(2, "Error", "Save file to idg failed: " + objectName);
                            } else {
                                // đẩy xml phiếu xn
                                Long startEmr = System.currentTimeMillis();
                                int responseCode = httpEmrUtilsNew.sendEmr(strToken, urlReceive, objectName);
                                logger.info("LIS_Update_Result_SignedCA|sendEmr hosobenhanid: " + mapParam.get("hosobenhanid") + " | " + so_phieu + " | " + objectName + " | " + responseCode + " | execution_time(ms): " + (System.currentTimeMillis() - startEmr));
                            }
                            result = res + "|" + caid;
                            logger.info("LIS_Update_Result_SignedCA|LIS SignedCA " + so_phieu + " | execution_time(ms): " + (System.currentTimeMillis() - startLIS));
                        }
                    }
                }
            } else if ("3".equals(trang_thai) || "4".equals(trang_thai)) {
                // huy tren his
                Long saveHISstart = System.currentTimeMillis();
                String[] b_out = new String[]{"", ""};
                String res = DBUtility.CALL_SP_S(this.dbName,
                        "{?=call lis_update_result_signedca2(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S,?11S,?12S,?13S,?14S,?15S,?16S,?o17S,?o18S)}",
                        new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, ngay_chi_dinh, dsdichvu, xmlcontent, reportCode, templateCode, trang_thai, "", "", userca, passca, "", ""},
                        b_out);
                result = res + "";
                error_code = b_out[0] + "";
                error_msg = b_out[1] + "";

                String[] resultarr = CheckObjectUtils.isNullOrEmpty(res) ? new String[]{} : res.split("@", 3);
                if (resultarr.length > 1) {
                    String resulttime = resultarr[0];
                    String resultinfo = resultarr[1];
                    logger.info("LIS_Update_Result_SignedCA|saveHIS sophieu: " + so_phieu + " | " + resulttime + " | execution_time(ms): " + (System.currentTimeMillis() - saveHISstart));

                    // huy tren emr
                    if ("0".equals(error_code)) {
                        Long startEMR = System.currentTimeMillis();
                        Map<String, Object> mapParam = new HashMap<>();
                        String[] mappa = resultinfo.split("\\|");
                        String keyCAs = mappa[0];
                        String paramHashed = mappa[1];
                        JSONArray keyCA = new JSONArray(keyCAs);
                        for (int i = 0; i < keyCA.length(); i++) {
                            JSONObject objx = keyCA.getJSONObject(i);
                            mapParam.put(objx.get("NAME").toString().toLowerCase(), objx.get("VALUE"));
                        }
                        mapParam.put("causer", userca);
                        mapParam.put("capassword", passca);
                        mapParam.put("type", "17");
                        mapParam.put("dichVus", dsdichvu);

//						System.out.println("keyCAs="+keyCAs);
//						System.out.println("paramHashed="+paramHashed);
//						System.out.println("userInfo="+userInfox.toString());

                        int signedcheck = EmrCaDetail.checkCaDetail(this.dbName, userInfox, paramHashed);
                        if (signedcheck > 0) {
                            String accountLogin = getCauHinh(this.dbName, _userInfo, 1);
                            if (!CheckObjectUtils.isNullOrEmpty(accountLogin) && !"0".equals(accountLogin) && accountLogin.split("/").length == 2) {
                                Long startloginEMR = System.currentTimeMillis();
                                String us;
                                String pw;
                                String strToken = "";
                                final String sp = DBActUtil.setDefaultInfo("{?=call emr_get_token('[UID]','[HID]','[SCH]','[IP]',?2S)}", user_id, hospital_id, provide_id, db_schema);
                                List<Map<String, Object>> obj = AjaxJson.callFunction(this.dbName, sp, "$", new Object[]{});
                                String url = getCauHinh(this.dbName, _userInfo, 2);
                                String urlLogin = url + "api/auth/public/login";
                                if (CheckObjectUtils.isNullOrEmpty(obj)) {
                                    if (!CheckObjectUtils.isNullOrEmpty(accountLogin)) {
                                        String[] parts = accountLogin.split("/");
                                        us = parts[0];
                                        pw = parts[1];
                                        strToken = httpEmrUtilsNew.getToken(us, pw, urlLogin);
                                        addLogin(this.dbName, _userInfo, us, pw, strToken);
                                    }
                                } else {
                                    String timeLogin = obj.get(0).get("NGAYDANGNHAP").toString();
                                    DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                                    Date loginDate = DateUtils.addHours(dateFormat.parse(timeLogin), 1);
                                    Date currentDate = new Date();
                                    if (currentDate.compareTo(loginDate) > 0) {
                                        if (!CheckObjectUtils.isNullOrEmpty(accountLogin)) {
                                            String[] parts = accountLogin.split("/");
                                            us = parts[0];
                                            pw = parts[1];
                                            strToken = httpEmrUtilsNew.getToken(us, pw, urlLogin);
                                            addLogin(this.dbName, _userInfo, us, pw, strToken);
                                        }
                                    } else {
                                        strToken = obj.get(0).get("TOKEN").toString();
                                    }
                                }
                                logger.info("LIS_Update_Result_SignedCA|Login EMR sophieu: " + so_phieu + " | " + strToken + " | execution_time(ms): " + (System.currentTimeMillis() - startloginEMR));
                                startloginEMR = System.currentTimeMillis();
                                apiCaResponse = CaApiCancelReport.cancelReport2(this.dbName, userInfox, mapParam, paramHashed, strToken);
                                logger.info("LIS_Update_Result_SignedCA|Cancel EMR sophieu: " + so_phieu + " | " + strToken + " | execution_time(ms): " + (System.currentTimeMillis() - startloginEMR));
                                result += "|" + apiCaResponse.getMESSAGE() + ":" + apiCaResponse.getDATA();
                            }
                        } else {
                            result += "|Unsigned";
                        }
                        logger.info("LIS_Update_Result_SignedCA|UnSignedCA sophieu: " + so_phieu + " | execution_time(ms): " + (System.currentTimeMillis() - startEMR));
                    }
                }
            } else {
                String[] b_out = new String[]{"", ""};
                String res = DBUtility.CALL_SP_S(this.dbName,
                        "{?=call lis_update_result_signedca2(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?10S,?11S,?12S,?13S,?14S,?15S,?16S,?o17S,?o18S)}",
                        new Object[]{user_id, db_schema, provide_id, hospital_id, so_phieu, ngay_chi_dinh, dsdichvu, xmlcontent, reportCode, templateCode, trang_thai, "", "", userca, passca, "", ""},
                        b_out);
                result = res + "";
                error_code = b_out[0] + "";
                error_msg = b_out[1] + "";
            }


            JSONObject ret = new JSONObject();
            ret.put("error_code", error_code);
            ret.put("error_msg", error_msg);
            ret.put("result", result);

            return ret.toString();
        } catch (Exception e) {
            logger.error("LIS_Update_Result_SignedCA exception uuid=" + this.uuid + ":" + CommonUtility.getStringException(e, "\n"));
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", e.toString());
            obj.put("result", result);
            return obj.toString();
        }
    }

    private int addLogin(String dbInfo, SysObject _userInfo, String us, String pw, String token) {
        String uid = _userInfo.getUserId();
        String hid = _userInfo.getHospitalId();
        String schema = _userInfo.getDbSchema();

        Object[] sp_out = new Object[]{};
        String sp = DBActUtil.setDefaultInfo("{?=call emr_upd_token('[UID]','[HID]','[SCH]','[IP]',?2S,?3S,?4S)}", uid, hid, SysConstants.EMPTY_STRING, schema);

        String strParam = us + Constants.DOLAR_CHARACTERS +
                pw + Constants.DOLAR_CHARACTERS +
                token + Constants.DOLAR_CHARACTERS;
        return new AjaxJson(dbInfo).ajaxCALL_SP_I(sp, strParam, sp_out);
    }

    private String getCauHinh(String dbInfo, SysObject _userInfo, int type) {
        String giaTri = "";
        AjaxJson ajaxJson = new AjaxJson(dbInfo);
        if (type == 1) {
            giaTri = ajaxJson.ajaxCALL_SP_S("{?=call com_lay_cauhinh('" + _userInfo.getHospitalId() + "',?2S)}", "EMR_ACCOUNT_LOGIN" + "$", new Object[]{});
        } else if (type == 2) {
            giaTri = ajaxJson.ajaxCALL_SP_S("{?=call com_lay_cauhinh('" + _userInfo.getHospitalId() + "',?2S)}", "EMR_URL_SERVICE" + "$", new Object[]{});
        } else if (type == 3) {
            giaTri = ajaxJson.ajaxCALL_SP_S("{?=call com_lay_cauhinh('" + _userInfo.getHospitalId() + "',?2S)}", "PARRAM_SMS_KQXN" + "$", new Object[]{});
        }
        return giaTri;
    }
    // hết phần ký số

    // tiếp nhận theo lô
    public String LIS_Get_PatientList_ByCode(String tu_ngay, String den_ngay, String trang_thai, String code) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            result = DBUtility.CALL_SP_O(this.dbName,
                    "{?=call LIS_Get_PatientList_ByCode(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?o10S,?o11S)}", user_id + "$" + db_schema + "$"
                            + provide_id + "$" + hospital_id + "$" + tu_ngay + "$" + den_ngay + "$" + trang_thai + "$" + code + "$$",
                    b_out);
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Get_PatientList_ByCode exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String LIS_Receive_List(String danhsach) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_S(this.dbName, "{?=call lis_receive_list(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, danhsach, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("LIS_Receive_List exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }
    // hết phần tiếp nhận theo lô

    // kết nối cho ivf
    public String IVF_DM_NHOM_XN(String manhom) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName, "{?=call IVF_DM_NHOM_XN(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, manhom, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("IVF_DM_NHOM_XN exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String IVF_DM_DICHVU(String maxn) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName, "{?=call IVF_DM_DICHVU(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, maxn, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("IVF_DM_DICHVU exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String IVF_DM_CHISO_XN(String manhom) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName, "{?=call IVF_DM_CHISO_XN(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, manhom, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("IVF_DM_CHISO_XN exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String IVF_THONGTIN_BENHNHAN(String pid, String fromdate, String todate) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName, "{?=call IVF_THONGTIN_BENHNHAN(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?o9S,?o10S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, pid, fromdate, todate, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("IVF_THONGTIN_BENHNHAN exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String IVF_THONGTIN_BACSY(String mabacsy) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName, "{?=call IVF_THONGTIN_BACSY(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, mabacsy, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("IVF_THONGTIN_BACSY exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String IVF_THONGTIN_CHIDINH(String machidinh, String mabenhnhan, String fromdate, String todate) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName, "{?=call IVF_THONGTIN_CHIDINH(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?o10S,?o11S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, machidinh, mabenhnhan, fromdate, todate, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("IVF_THONGTIN_CHIDINH exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String IVF_THONGTIN_XETNGHIEM(String machidinh, String mabenhnhan, String fromdate, String todate) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName, "{?=call IVF_THONGTIN_XETNGHIEM(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?o10S,?o11S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, machidinh, mabenhnhan, fromdate, todate, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("IVF_THONGTIN_XETNGHIEM exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String IVF_DM_THUOC(String mathuoc) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName, "{?=call IVF_DM_THUOC(?2S,?3S,?4S,?5S,?6S,?o7S,?o8S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, mathuoc, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("IVF_DM_THUOC exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    public String IVF_THONGTIN_TOATHUOC(String madonthuoc, String mabenhnhan, String fromdate, String todate) {
        String result = "";
        String error_code = "";
        String error_msg = "";

        try {

            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(this.uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                        .getJsonWebTokenByType().getLoginUserData(this.uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            String res = DBUtility.CALL_SP_O(this.dbName, "{?=call IVF_THONGTIN_TOATHUOC(?2S,?3S,?4S,?5S,?6S,?7S,?8S,?9S,?o10S,?o11S)}",
                    new Object[]{user_id, db_schema, provide_id, hospital_id, madonthuoc, mabenhnhan, fromdate, todate, "", ""},
                    b_out);
            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error("IVF_THONGTIN_TOATHUOC exception uuid=" + this.uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }
    // hết phần kết nối cho ivf

    private String callFunc(String funcName, int returnType, String... params) {
        String result = "";
        String error_code;
        String error_msg;
        try {
            String user_id;
            String db_schema;
            String provide_id;
            String hospital_id;

            String uuidCache = new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                    .getJsonWebTokenByType().getID(uuid);
            JSONObject userInfo = UserInfoCache.get(uuidCache);
            if (userInfo != null) {
                user_id = userInfo.getString(SysConstants.USER_ID);
                db_schema = userInfo.getString(SysConstants.DB_SCHEMA);
                provide_id = userInfo.getString(SysConstants.PROVINCE_ID);
                hospital_id = userInfo.getString(SysConstants.HOSPITAL_ID);
            } else {
                String userData =
                        new JsonWebTokenRestApiFactory(JsonWebTokenRestApiFactory.JWT_JJWT)
                                .getJsonWebTokenByType().getLoginUserData(uuid);
                SysObject _userInfo = new Gson().fromJson(userData, SysObject.class);
                user_id = _userInfo.getUserId();
                db_schema = _userInfo.getDbSchema();
                provide_id = _userInfo.getProvinceId();
                hospital_id = _userInfo.getHospitalId();
            }

            String[] b_out = new String[]{"", ""};
            StringBuilder sql = new StringBuilder();
            sql
                    .append("{?=call ")
                    .append(funcName)
                    .append("(?2S,?3S,?4S,?5S,");
            LinkedList paramList = new LinkedList(Arrays.asList(user_id, db_schema, provide_id, hospital_id));
            for (int i = 0; i < params.length; i++) {
                sql.append("?").append(i + 6).append("S,");
                paramList.add(params[i]);
            }
            sql.append("?o" + (params.length + 6) + "S,");
            sql.append("?o" + (params.length + 7) + "S)}");
            paramList.add("");
            paramList.add("");

            String res = 1 == returnType ?
                    DBUtility.CALL_SP_O(this.dbName, sql.toString(), paramList.toArray(), b_out)
                    : DBUtility.CALL_SP_S(this.dbName, sql.toString(), paramList.toArray(), b_out);

            result = res + "";
            error_code = b_out[0] + "";
            error_msg = b_out[1] + "";

            JSONObject obj = new JSONObject();
            obj.put("error_code", error_code);
            obj.put("error_msg", error_msg);
            obj.put("result", result);

            return obj.toString();
        } catch (Exception e) {
            logger.error(funcName + " exception uuid=" + uuid + ":", e);
            JSONObject obj = new JSONObject();
            obj.put("error_code", Constants.RESPONSE_ERR);
            obj.put("error_msg", PropertiesUtility.getMsgBundle("msg.exception.comm"));
            obj.put("result", result);
            return obj.toString();
        }
    }

    private String getConfig(String hospitalId, String configCode) {
        return CommonUtility.getComConf(this.dbName, hospitalId, configCode);
    }
}
